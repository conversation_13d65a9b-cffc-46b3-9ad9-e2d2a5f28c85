{"common.view.modals.txtCopy": "複製到剪貼板", "common.view.modals.txtEmbed": "嵌入", "common.view.modals.txtHeight": "\n高度", "common.view.modals.txtShare": "分享連結", "common.view.modals.txtWidth": "寬度", "common.view.SearchBar.textFind": "尋找", "PE.ApplicationController.convertationErrorText": "轉換失敗。", "PE.ApplicationController.convertationTimeoutText": "轉換逾時。", "PE.ApplicationController.criticalErrorTitle": "錯誤", "PE.ApplicationController.downloadErrorText": "下載失敗", "PE.ApplicationController.downloadTextText": "下載簡報中...", "PE.ApplicationController.errorAccessDeny": "您嘗試進行未被授權的動作。<br> 請聯繫您的文件伺服器主機的管理者。", "PE.ApplicationController.errorDefaultMessage": "錯誤編號：%1", "PE.ApplicationController.errorFilePassProtect": "該文件受密碼保護，無法打開。", "PE.ApplicationController.errorFileSizeExceed": "此檔案超過這一主機限制的大小<br> 進一步資訊，請聯絡您的文件服務主機的管理者。", "PE.ApplicationController.errorForceSave": "保存文件時發生錯誤。請使用“下載為”選項將文件保存到電腦機硬碟中，或稍後再試。", "PE.ApplicationController.errorLoadingFont": "字體未加載。<br>請聯繫文件服務器管理員。", "PE.ApplicationController.errorTokenExpire": "此文件安全憑證(Security Token)已過期。<br>請與您的Document Server管理員聯繫。", "PE.ApplicationController.errorUpdateVersionOnDisconnect": "Internet連接已恢復，文件版本已更改。<br>在繼續工作之前，您需要下載文件或複制其內容以確保沒有丟失，然後重新加載此頁面。", "PE.ApplicationController.errorUserDrop": "目前無法存取該文件。", "PE.ApplicationController.notcriticalErrorTitle": "警告", "PE.ApplicationController.openErrorText": "開啟檔案時發生錯誤", "PE.ApplicationController.scriptLoadError": "連接速度太慢，某些組件無法加載。請重新加載頁面。", "PE.ApplicationController.textAnonymous": "匿名", "PE.ApplicationController.textGuest": "來賓帳戶", "PE.ApplicationController.textLoadingDocument": "正在載入簡報", "PE.ApplicationController.textOf": "於", "PE.ApplicationController.txtClose": "關閉", "PE.ApplicationController.unknownErrorText": "未知錯誤。", "PE.ApplicationController.unsupportedBrowserErrorText": "不支援您的瀏覽器", "PE.ApplicationController.waitText": "請耐心等待...", "PE.ApplicationView.txtDownload": "下載", "PE.ApplicationView.txtEmbed": "嵌入", "PE.ApplicationView.txtFileLocation": "打開文件所在位置", "PE.ApplicationView.txtFullScreen": "全螢幕", "PE.ApplicationView.txtPrint": "列印", "PE.ApplicationView.txtSearch": "搜索", "PE.ApplicationView.txtShare": "分享"}