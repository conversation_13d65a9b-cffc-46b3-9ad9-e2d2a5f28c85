{"common.view.modals.txtCopy": "Copiar al portapapeles", "common.view.modals.txtEmbed": "Insertar", "common.view.modals.txtHeight": "Altura", "common.view.modals.txtShare": "Compartir enlace", "common.view.modals.txtWidth": "<PERSON><PERSON>", "common.view.SearchBar.textFind": "Buscar", "PE.ApplicationController.convertationErrorText": "Se ha producido un error en la conversión.", "PE.ApplicationController.convertationTimeoutText": "Se ha superado el tiempo de conversión.", "PE.ApplicationController.criticalErrorTitle": "Error", "PE.ApplicationController.downloadErrorText": "Se ha producido un error en la descarga", "PE.ApplicationController.downloadTextText": "Descargando presentación...", "PE.ApplicationController.errorAccessDeny": "Está intentando realizar una acción para la que no tiene permiso. <br>Contacte con el administrador del servidor de documentos.", "PE.ApplicationController.errorDefaultMessage": "Código de error: %1", "PE.ApplicationController.errorFilePassProtect": "El archivo está protegido por una contraseña y no se puede abrir.", "PE.ApplicationController.errorFileSizeExceed": "El tamaño del archivo supera el límiete establecido por su servidor. <br>Contacte con el administrador del servidor de documentos para obtener más detalles.", "PE.ApplicationController.errorForceSave": "Se ha producido un error al guardar el archivo. Utilice la opción \"Descargar como\" para guardar el archivo en el disco duro de su ordenador o inténtelo más tarde.", "PE.ApplicationController.errorInconsistentExt": "Se ha producido un error al abrir el archivo.<br>El contenido del archivo no coincide con la extensión del mismo.", "PE.ApplicationController.errorInconsistentExtDocx": "Se ha producido un error al abrir el archivo.<br>El contenido del archivo corresponde a documentos de texto (por ejemplo, docx), pero el archivo tiene extensión inconsistente: %1.", "PE.ApplicationController.errorInconsistentExtPdf": "Se ha producido un error al abrir el archivo.<br>El contenido del archivo corresponde a uno de los siguientes formatos: pdf/djvu/xps/oxps, pero el archivo tiene extensión inconsistente: %1.", "PE.ApplicationController.errorInconsistentExtPptx": "Se ha producido un error al abrir el archivo.<br>El contenido del archivo corresponde a presentaciones (por ejemplo, pptx), pero el archivo tiene extensión inconsistente: %1.", "PE.ApplicationController.errorInconsistentExtXlsx": "Se ha producido un error al abrir el archivo.<br>El contenido del archivo corresponde a hojas de cálculo (por ejemplo, xlsx), pero el archivo tiene extensión inconsistente: %1.", "PE.ApplicationController.errorLoadingFont": "Los tipos de letra no están cargados. <br>Contacte con el administrador del servidor de documentos.", "PE.ApplicationController.errorTokenExpire": "El token de seguridad del documento ha expirado. <br>Contacte con el administrador del servidor de documentos", "PE.ApplicationController.errorUpdateVersionOnDisconnect": "Se ha restablecido la conexión a Internet y se ha cambiado la versión del archivo. <br>Para poder seguir trabajando, es necesario descargar el archivo o copiar su contenido para asegurarse de que no se ha perdido nada, y luego volver a cargar esta página.", "PE.ApplicationController.errorUserDrop": "No se puede acceder al archivo.", "PE.ApplicationController.notcriticalErrorTitle": "Advertencia", "PE.ApplicationController.openErrorText": "Se ha producido un error al abrir el archivo. ", "PE.ApplicationController.scriptLoadError": "La conexión a Internet es demasiado lenta, algunos de los componentes no se han podido cargar. Recargue la página.", "PE.ApplicationController.textAnonymous": "<PERSON><PERSON><PERSON>", "PE.ApplicationController.textGuest": "<PERSON><PERSON><PERSON><PERSON>", "PE.ApplicationController.textLoadingDocument": "Cargando presentación", "PE.ApplicationController.textOf": "de", "PE.ApplicationController.txtClose": "<PERSON><PERSON><PERSON>", "PE.ApplicationController.unknownErrorText": "Error des<PERSON>.", "PE.ApplicationController.unsupportedBrowserErrorText": "Su navegador no es compatible.", "PE.ApplicationController.waitText": "Espere...", "PE.ApplicationView.txtDownload": "<PERSON><PERSON><PERSON>", "PE.ApplicationView.txtEmbed": "Insertar", "PE.ApplicationView.txtFileLocation": "Abrir ubicación del archivo", "PE.ApplicationView.txtFullScreen": "Pantalla completa", "PE.ApplicationView.txtPrint": "Imprimir", "PE.ApplicationView.txtSearch": "Búsqueda", "PE.ApplicationView.txtShare": "Compartir"}