{"common.view.modals.txtCopy": "<PERSON><PERSON>in ke papan klip", "common.view.modals.txtEmbed": "Melekatkan", "common.view.modals.txtHeight": "Ketinggian", "common.view.modals.txtShare": "<PERSON><PERSON>", "common.view.modals.txtWidth": "<PERSON><PERSON>", "common.view.SearchBar.textFind": "Temukan", "PE.ApplicationController.convertationErrorText": "<PERSON><PERSON><PERSON><PERSON> gagal.", "PE.ApplicationController.convertationTimeoutText": "<PERSON><PERSON>tu konversi habis.", "PE.ApplicationController.criticalErrorTitle": "<PERSON><PERSON><PERSON>", "PE.ApplicationController.downloadErrorText": "<PERSON><PERSON><PERSON> gagal.", "PE.ApplicationController.downloadTextText": "<PERSON><PERSON><PERSON><PERSON>", "PE.ApplicationController.errorAccessDeny": "<PERSON><PERSON> mencoba melakukan sebuah", "PE.ApplicationController.errorDefaultMessage": "Kode kesalahan %1", "PE.ApplicationController.errorFilePassProtect": "File diproteksi kata sandi", "PE.ApplicationController.errorFileSizeExceed": "Ukuran file melebihi", "PE.ApplicationController.errorForceSave": "<PERSON> kesalahan saat menyimpan file. <PERSON><PERSON><PERSON> gunakan opsi 'Download sebagai' untuk menyimpan file ke komputer Anda dan coba lagi.", "PE.ApplicationController.errorInconsistentExt": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat membuka file.<br>Isi file tidak cocok dengan ekstensi file.", "PE.ApplicationController.errorInconsistentExtDocx": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat membuka file.<br>Isi file berhubungan dengan dokumen teks (mis. docx), tapi file memiliki ekstensi yang tidak konsisten: %1.", "PE.ApplicationController.errorInconsistentExtPdf": "<PERSON><PERSON><PERSON> kesalahan terjadi ketika membuka file.<br>Isi file berhubungan dengan satu dari format berikut: pdf/djvu/xps/oxps, tapi file memiliki ekstensi yang tidak konsisten: %1.", "PE.ApplicationController.errorInconsistentExtPptx": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat membuka file.<br>Isi file berhubungan dengan presentasi (mis. pptx), tapi file memiliki ekstensi yang tidak konsisten: %1.", "PE.ApplicationController.errorInconsistentExtXlsx": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat membuka file.<br>Isi file berhubungan dengan spreadsheet (mis. xlsx), tapi file memiliki ekstensi yang tidak konsisten: %1.", "PE.ApplicationController.errorLoadingFont": "Font tidak bisa dimuat.<br><PERSON><PERSON><PERSON> kontak admin Server <PERSON><PERSON><PERSON>.", "PE.ApplicationController.errorTokenExpire": "Token keamanan dokumen sudah kadaluwarsa.<br><PERSON><PERSON><PERSON> hubungi admin Server <PERSON><PERSON><PERSON>.", "PE.ApplicationController.errorUpdateVersionOnDisconnect": "Koneksi internet sudah kembali dan versi file sudah diganti.<br>Sebelum Anda bisa melanjutkan kerja, Anda perlu mengunduh file atau salin konten untuk memastikan tidak ada yang hilang, lalu muat ulang halaman ini.", "PE.ApplicationController.errorUserDrop": "File tidak dapat di akses", "PE.ApplicationController.notcriticalErrorTitle": "Peringatan", "PE.ApplicationController.openErrorText": "Kesalahan terjadi ketika membuka file.", "PE.ApplicationController.scriptLoadError": "<PERSON>ne<PERSON><PERSON> terl<PERSON> la<PERSON>,", "PE.ApplicationController.textAnonymous": "<PERSON><PERSON><PERSON>", "PE.ApplicationController.textGuest": "<PERSON><PERSON>", "PE.ApplicationController.textLoadingDocument": "<PERSON><PERSON><PERSON>", "PE.ApplicationController.textOf": "<PERSON><PERSON>", "PE.ApplicationController.txtClose": "<PERSON><PERSON><PERSON>", "PE.ApplicationController.unknownErrorText": "Kesalahan tidak diketahui", "PE.ApplicationController.unsupportedBrowserErrorText": "<PERSON><PERSON><PERSON> kamu tidak didukung", "PE.ApplicationController.waitText": "<PERSON><PERSON>", "PE.ApplicationView.txtDownload": "<PERSON><PERSON><PERSON>", "PE.ApplicationView.txtEmbed": "Melekatkan", "PE.ApplicationView.txtFileLocation": "<PERSON><PERSON> Dokumen", "PE.ApplicationView.txtFullScreen": "<PERSON><PERSON> penuh", "PE.ApplicationView.txtPrint": "Cetak", "PE.ApplicationView.txtSearch": "<PERSON><PERSON>", "PE.ApplicationView.txtShare": "Bagikan"}