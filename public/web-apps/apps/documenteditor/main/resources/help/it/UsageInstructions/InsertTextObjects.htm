﻿<!DOCTYPE html>
<html>
	<head>
		<title>Insert text objects</title>
		<meta charset="utf-8" />
        <meta name="description" content="Insert text objects such as text boxes and Text Art to make your text more impressive" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Insert text objects</h1>
            <p>To make your text more emphatic and draw attention to a specific part of the document, you can insert a text box (a rectangular frame that allows to enter text within it) or a Text Art object (a text box with a predefined font style and color that allows to apply some text effects).</p>
			<h3>Add a text object</h3>
            <p>You can add a text object anywhere on the page. To do that:</p>
            <ol>
                <li>switch to the <b>Insert</b> tab of the top toolbar,</li>
                <li>select the necessary text object type:
                <ul>
                    <li>to add a text box, click the <div class = "icon icon-inserttexticon"></div> <b>Text Box</b> icon at the top toolbar, then click where you want to insert the text box, hold the mouse button and drag the text box border to specify its size. When you release the mouse button, the insertion point will appear in the added text box, allowing you to enter your text.
                    <p class="note"><b>Note</b>: it's also possible to insert a text box by clicking the <span class = "icon icon-insertautoshape"></span> <b>Shape</b> icon at the top toolbar and selecting the <span class = "icon icon-text_autoshape"></span> shape from the <b>Basic Shapes</b> group.</p>
                    </li>
                    <li>to add a Text Art object, click the <div class = "icon icon-inserttextarticon"></div> <b>Text Art</b> icon at the top toolbar, then click on the desired style template – the Text Art object will be added at the current cursor position. Select the default text within the text box with the mouse and replace it with your own text.</li>
                </ul>
                </li>
                <li>click outside of the text object to apply the changes and return to the document.</li>
            </ol>
            <p>The text within the text object is a part of the latter (when you move or rotate the text object, the text moves or rotates with it).</p>
            <p>As an inserted text object represents a rectangular frame with text in it (Text Art objects have invisible text box borders by default) and this frame is a common autoshape, you can change both the shape and text properties.</p>
            <p>To delete the added text object, click on the text box border and press the <b>Delete</b> key on the keyboard. The text within the text box will also be deleted.</p>
            <h3>Format a text box</h3>
            <p>Select the text box clicking on its border to be able to change its properties. When the text box is selected, its borders are displayed as solid (not dashed) lines.</p>
            <p><img alt="Text box selected" src="../images/textbox_boxselected.png" /></p>
            <ul>
                <li>to <a href="../UsageInstructions/InsertAutoshapes.htm#shape_resize" onclick="onhyperlinkclick(this)">resize, move, rotate</a> the text box use the special handles on the edges of the shape.</li>
                <li>to edit the text box <a href="../UsageInstructions/InsertAutoshapes.htm#shape_fill" onclick="onhyperlinkclick(this)">fill</a>, <a href="../UsageInstructions/InsertAutoshapes.htm#shape_stroke" onclick="onhyperlinkclick(this)">stroke</a>, <a href="../UsageInstructions/InsertAutoshapes.htm#shape_wrapping" onclick="onhyperlinkclick(this)">wrapping style</a> or <b>replace</b> the rectangular box with a different shape, click the <b>Shape settings</b> <div class = "icon icon-shape_settings_icon"></div> icon on the right sidebar and use the corresponding options.</li>                
                <li>to <b>align</b> the text box on the page, <b>arrange</b> text boxes as related to other objects, <b>rotate or flip</b> a text box, change a <b>wrapping style</b> or access the shape <b>advanced settings</b>, right-click on the text box border and use the <a href="../UsageInstructions/InsertAutoshapes.htm#shape_rightclickmenu" onclick="onhyperlinkclick(this)">contextual menu options</a>. To learn more on how to arrange and align objects you can refer to <a href="../UsageInstructions/AlignArrangeObjects.htm" onclick="onhyperlinkclick(this)">this page</a>.</li>
            </ul>
            <h3>Format the text within the text box</h3>
            <p>Click the text within the text box to be able to change its properties. When the text is selected, the text box borders are displayed as dashed lines.</p>
            <p><img alt="Text selected" src="../images/textbox_textselected.png" /></p>
            <p class="note"><b>Note</b>: it's also possible to change text formatting when the text box (not the text itself) is selected. In such a case, any changes will be applied to all the text within the text box. Some font formatting options (font type, size, color and decoration styles) can be applied to a previously selected portion of the text separately.</p>
            <p>To <b>rotate</b> the text within the text box, right-click the text, select the <b>Text Direction</b> option and then choose one of the available options: <b>Horizontal</b> (is selected by default), <b>Rotate Text Down</b> (sets a vertical direction, from top to bottom) or <b>Rotate Text Up</b> (sets a vertical direction, from bottom to top).</p>
            <p>To <b>align the text vertically</b> within the text box, right-click the text, select the <b>Vertical Alignment</b> option and then choose one of the available options: <b>Align Top</b>, <b>Align Center</b> or <b>Align Bottom</b>.</p>
            <p>Other formatting options that you can apply are the same as the ones for regular text. Please refer to the corresponding help sections to learn more about the necessary operation. You can:</p>
            <ul>
                <li><a href="../UsageInstructions/AlignText.htm" onclick="onhyperlinkclick(this)">align the text horizontally</a> within the text box</li>
                <li>adjust the <a href="../UsageInstructions/FontTypeSizeColor.htm" onclick="onhyperlinkclick(this)">font type, size, color</a>, apply <a href="../UsageInstructions/DecorationStyles.htm" onclick="onhyperlinkclick(this)">decoration styles</a> and <a href="../UsageInstructions/FormattingPresets.htm" onclick="onhyperlinkclick(this)">formatting presets</a></li>
                <li>set <a href="../UsageInstructions/LineSpacing.htm" onclick="onhyperlinkclick(this)">line spacing</a>, change <a href="../UsageInstructions/ParagraphIndents.htm" onclick="onhyperlinkclick(this)">paragraph indents</a>, adjust <a href="../UsageInstructions/SetTabStops.htm" onclick="onhyperlinkclick(this)">tab stops</a> for the multi-line text within the text box</li>
                <li>insert a <a href="../UsageInstructions/AddHyperlinks.htm" onclick="onhyperlinkclick(this)">hyperlink</a></li>
            </ul>
            <p>You can also click the <b>Text Art settings</b> <span class = "icon icon-textart_settings_icon"></span> icon on the right sidebar and change some style parameters.</p>
            <h3>Edit a Text Art style</h3>
            <p>Select a text object and click the <b>Text Art settings</b> <span class = "icon icon-textart_settings_icon"></span> icon on the right sidebar.</p>
            <p><img alt="Text Art setting tab" src="../images/right_textart.png" /></p>
            <p>Change the applied text style selecting a new <b>Template</b> from the gallery. You can also change the basic style additionally by selecting a different font type, size etc.</p>
            <p>Change the font <b>Fill</b>. You can choose the following options:</p>
            <ul>
                <li>
                    <b>Color Fill</b> - select this option to specify the solid color you want to fill the inner space of letters with.
                    <p><img alt="Color Fill" src="../images/fill_color.png" /></p>
                    <p id="color">Click the colored box below and select the necessary color from the available <a href="../UsageInstructions/ChangeColorScheme.htm" onclick="onhyperlinkclick(this)">color sets</a> or specify any color you like:</p>
                </li>
                <li>
                    <b>Gradient Fill</b> - select this option to fill the letters with two colors which smoothly change from one to another.
                    <p><img alt="Gradient Fill" src="../images/fill_gradient.png" /></p>
                    <ul>
                        <li><b>Style</b> - choose one of the available options: <b>Linear</b> (colors change in a straight line i.e. along a horizontal/vertical axis or diagonally at a 45 degree angle) or <b>Radial</b> (colors change in a circular path from the center to the edges).</li>
                        <li><b>Direction</b> - choose a template from the menu. If the <b>Linear</b> gradient is selected, the following directions are available: top-left to bottom-right, top to bottom, top-right to bottom-left, right to left, bottom-right to top-left, bottom to top, bottom-left to top-right, left to right. If the <b>Radial</b> gradient is selected, only one template is available.</li>
                        <li><b>Gradient</b> - click on the left slider <div class = "icon icon-gradientslider"></div> under the gradient bar to activate the color box which corresponds to the first color. Click on the color box on the right to choose the first color in the palette. Drag the slider to set the gradient stop i.e. the point where one color changes into another. Use the right slider under the gradient bar to specify the second color and set the gradient stop.</li>
                    </ul>
                    <p class="note"><b>Note</b>: if one of these two options is selected, you can also set an <b>Opacity</b> level dragging the slider or entering the percent value manually. The default value is <b>100%</b>. It corresponds to the full opacity. The <b>0%</b> value corresponds to the full transparency.</p>
                </li>
                <li><b>No Fill</b> - select this option if you don't want to use any fill.</li>
            </ul>
            <p>Adjust the font <b>Stroke</b> width, color and type.</p>
            <ul>
                <li>To change the stroke width, select one of the available options from the <b>Size</b> dropdown list. The available options are: 0.5 pt, 1 pt, 1.5 pt, 2.25 pt, 3 pt, 4.5 pt, 6 pt. Alternatively, select the <b>No Line</b> option if you don't want to use any stroke.</li>
                <li>To change the stroke <b>color</b>, click on the colored box below and <a href="../UsageInstructions/ChangeColorScheme.htm" onclick="onhyperlinkclick(this)">select the necessary color</a>.</li>
                <li>To change the stroke <b>type</b>, select the necessary option from the corresponding dropdown list (a solid line is applied by default, you can change it to one of the available dashed lines).</li>
            </ul>
            <p>Apply a text effect by selecting the necessary text transformation type from the <b>Transform</b> gallery. You can adjust the degree of the text distortion by dragging the pink diamond-shaped handle.</p>
            <p><img alt="Text Art Transformation" src="../images/textart_transformation.png" /></p>
		</div>
	</body>
</html>