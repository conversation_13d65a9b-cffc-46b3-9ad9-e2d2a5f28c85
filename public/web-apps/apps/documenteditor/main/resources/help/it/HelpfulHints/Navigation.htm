﻿<!DOCTYPE html>
<html>
	<head>
		<title>View Settings and Navigation Tools</title>
		<meta charset="utf-8" />
		<meta name="description" content="The description of view settings and navigation tools such as zoom, previous/next page buttons" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
		<h1>View Settings and Navigation Tools</h1>
			<p><b>Document Editor</b> offers several tools to help you view and navigate through your document: zoom, page number indicator etc.</p>
			<h3>Adjust the View Settings</h3>
			<p>To adjust default view settings and set the most convenient mode to work with the document, click the <b>View settings</b> <span class = "icon icon-viewsettingsicon"></span> icon on the right side of the editor header and select which interface elements you want to be hidden or shown.
			You can select the following options from the <b>View settings</b> drop-down list:
			</p>
			<ul>
                <li>
                    <b>Hide Toolbar</b> -  hides the top toolbar that contains commands while tabs remain visible. When this option is enabled, you can click any tab to display the toolbar. The toolbar is displayed until you click anywhere outside it.<br />To disable this mode, click the <b>View settings</b> <div class = "icon icon-viewsettingsicon"></div> icon and click the <b>Hide Toolbar</b> option once again. The top toolbar will be displayed all the time.
                    <p class="note"><b>Note</b>: alternatively, you can just double-click any tab to hide the top toolbar or display it again.</p>
                </li>
			<li><b>Hide Status Bar</b> - hides the bottommost bar where the <b>Page Number Indicator</b> and <b>Zoom</b> buttons are situated. To show the hidden <b>Status Bar</b> click this option once again.</li>
			<li><b>Hide Rulers</b> - hides rulers which are used to align text, graphics, tables, and other elements in a document, set up margins, tab stops, and paragraph indents. To show the hidden <b>Rulers</b> click this option once again.</li>
			</ul>
			<p>The right sidebar is minimized by default. To expand it, select any object (e.g. image, chart, shape) or text passage and click the icon of the currently activated tab on the right. To minimize the right sidebar, click the icon once again.</p>
			<p>When the <b>Comments</b> <span class="onlineDocumentFeatures">or <b>Chat</b></span> panel is opened, the left sidebar width is adjusted by simple drag-and-drop: 
			move the mouse cursor over the left sidebar border so that it turns into the bidirectional arrow and drag the border to the right to extend the sidebar width. To restore its original width move the border to the left.</p>
			<h3>Use the Navigation Tools</h3>
			<p>To navigate through your document, use the following tools:</p>
			<p>The <b>Zoom</b> buttons are situated in the right lower corner and are used to zoom in and out the current document. 
			To change the currently selected zoom value that is displayed in percent, click it and select one of the available zoom options from the list 
			or use the <b>Zoom in</b> <span class = "icon icon-zoomin"></span> or <b>Zoom out</b> <span class = "icon icon-zoomout"></span> buttons. 
			Click the <b>Fit width</b> <span class = "icon icon-fitwidth"></span> icon to fit the document page width to the visible part of the working area.
			To fit the whole document page to the visible part of the working area, click the <b>Fit page</b> <span class = "icon icon-fitpage"></span> icon.
			Zoom settings are also available in the <b>View settings</b> <span class = "icon icon-viewsettingsicon"></span> drop-down list that can be useful if you decide to hide the <b>Status Bar</b>.</p>
			<p>The <b>Page Number Indicator</b> shows the current page as a part of all the pages in the current document (page 'n' of 'nn'). 
			Click this caption to open the window where you can enter the page number and quickly go to it.</p>
		</div>
	</body>
</html>