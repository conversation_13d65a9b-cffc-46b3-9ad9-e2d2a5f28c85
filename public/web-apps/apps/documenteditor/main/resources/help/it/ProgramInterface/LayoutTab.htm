﻿<!DOCTYPE html>
<html>
	<head>
		<title>Scheda Layout di Pagina</title>
		<meta charset="utf-8" />
        <meta name="description" content="Presentazione dell'interfaccia utente dell'Editor di Documenti - Scheda Layout di Pagina" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Scheda Layout di Pagina</h1>
            <p>La scheda <b>Layout di Pagina</b> consente di modificare l'aspetto del documento: impostare i parametri della pagina e definire la disposizione degli elementi visivi.</p>
            <div class="onlineDocumentFeatures">
                <p>Finestra <a href="https://www.onlyoffice.com/it/document-editor.aspx">dell’Editor di Documenti Online</a>:</p>
                <p><img alt="Scheda Layout di Pagina" src="../images/interface/layouttab.png" /></p>
            </div>
            <div class="desktopDocumentFeatures">
                <p>Finestra dell’Editor di Documenti Desktop:</p>
                <p><img alt="Scheda Layout di Pagina" src="../images/interface/desktop_layouttab.png" /></p>
            </div>
            <p>Usando questa scheda, puoi:</p>
            <ul>
                <li>regolare <a href="../UsageInstructions/SetPageParameters.htm#margins" onclick="onhyperlinkclick(this)">i margini</a>, <a href="../UsageInstructions/SetPageParameters.htm#orientation" onclick="onhyperlinkclick(this)">l’orientatamento</a>, <a href="../UsageInstructions/SetPageParameters.htm#size" onclick="onhyperlinkclick(this)">la dimensione</a> della pagina,</li>
                <li>aggiungere <a href="../UsageInstructions/SetPageParameters.htm#columns" onclick="onhyperlinkclick(this)">colonne</a>,</li>
                <li>inserire <a href="../UsageInstructions/PageBreaks.htm" onclick="onhyperlinkclick(this)">interruzioni di pagina</a>, <a href="../UsageInstructions/SectionBreaks.htm" onclick="onhyperlinkclick(this)">interruzioni di sezione</a> e <a href="../UsageInstructions/SetPageParameters.htm#columns" onclick="onhyperlinkclick(this)">interruzioni di colonna</a>,</li>
                <li>allineare e disporre gli oggetti (<a href="../UsageInstructions/InsertTables.htm" onclick="onhyperlinkclick(this)">tabelle</a>, <a href="../UsageInstructions/InsertImages.htm" onclick="onhyperlinkclick(this)">immagini</a>, <a href="../UsageInstructions/InsertCharts.htm" onclick="onhyperlinkclick(this)">grafici</a>, <a href="../UsageInstructions/InsertAutoshapes.htm" onclick="onhyperlinkclick(this)">forme</a>),</li>
                <li>cambiare lo <a href="../UsageInstructions/ChangeWrappingStyle.htm" onclick="onhyperlinkclick(this)">stile di disposizione testo</a>,</li>
                <li>aggiungere una <a href="../UsageInstructions/AddWatermark.htm" onclick="onhyperlinkclick(this)">filigrana</a>.</li>
            </ul>
		</div>
	</body>
</html>