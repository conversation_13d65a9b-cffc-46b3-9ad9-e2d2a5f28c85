﻿<!DOCTYPE html>
<html>
	<head>
		<title>Scheda Riferimenti</title>
		<meta charset="utf-8" />
        <meta name="description" content="Presentazione dell'interfaccia utente dell'Editor di Documenti - Scheda Riferimentib" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Scheda Riferimenti</h1>
            <p>La scheda <b>Riferimenti</b> consente di gestire diversi tipi di riferimenti: aggiungere e aggiornare un sommario, creare e modificare note a piè di pagina, inserire collegamenti ipertestuali.</p>
            <div class="onlineDocumentFeatures">
                <p>Finestra <a href="https://www.onlyoffice.com/it/document-editor.aspx">dell’Editor di Documenti Online</a>:</p>
                <p><img alt="Scheda Riferimenti" src="../images/interface/referencestab.png" /></p>
            </div>
            <div class="desktopDocumentFeatures">
                <p>Finestra dell’Editor di Documenti Desktop:</p>
                <p><img alt="Scheda Riferimenti" src="../images/interface/desktop_referencestab.png" /></p>
            </div>
            <p>Usando questa scheda, puoi:</p>
            <ul>
                <li>creare e aggiornare automaticamente un <a href="../UsageInstructions/CreateTableOfContents.htm" onclick="onhyperlinkclick(this)">sommario</a>,</li>
                <li>inserire <a href="../UsageInstructions/InsertFootnotes.htm" onclick="onhyperlinkclick(this)">note a piè di pagina</a>,</li>
                <li>inserire <a href="../UsageInstructions/AddHyperlinks.htm" onclick="onhyperlinkclick(this)">collegamenti ipertestuali</a>,</li>
                <li>aggiungere <a href="../UsageInstructions/InsertBookmarks.htm" onclick="onhyperlinkclick(this)">segnalibri</a>.</li>
                <li>aggiungere <a href="../UsageInstructions/Addcaption.htm" onclick="onhyperlinkclick(this)">didascalie</a>.</li>
            </ul>
		</div>
	</body>
</html>