﻿<!DOCTYPE html>
<html>
	<head>
		<title>Supported Formats of Electronic Documents</title>
		<meta charset="utf-8" />
		<meta name="description" content="The list of document formats supported by Document Editor" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Supported Formats of Electronic Documents</h1>
        <p>
            An electronic document is one of the most commonly used computer.
            Due to the highly developed modern computer network, it's more convenient to distribute electronic documents than printed ones.
            Nowadays, a lot of devices are used for document presentation, so there are plenty of proprietary and open file formats.
            The <a href="https://www.onlyoffice.com/en/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Document Editor</b></a> handles the most popular of them.
        </p>
        <p class="note">While uploading or opening the file for editing, it will be converted to the Office Open XML (DOCX) format. It's done to speed up the file processing and increase the interoperability.</p>
        <p>The following table contains the formats which can be opened for viewing and/or editing.</p>
        <table>
            <tr>
                <td><b>Formats</b></td>
                <td><b>Description</b></td>
                <td>View natively</td>
                <td>View after conversion to OOXML</td>
                <td>Edit natively</td>
                <td>Edit after conversion to OOXML</td>
            </tr>
            <tr>
                <td>DjVu</td>
                <td>File format designed primarily to store scanned documents, especially those containing a combination of text, line drawings, and photographs</td>
                <td></td>
                <td>+</td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td>DOC</td>
                <td>Filename extension for word processing documents created with Microsoft Word</td>
                <td></td>
                <td>+</td>
                <td></td>
                <td>+</td>
            </tr>
            <tr>
                <td>DOCM</td>
                <td>Macro-Enabled Microsoft Word Document<br /> Filename extension for Microsoft Word 2007 or higher generated documents with the ability to run macros</td>
                <td></td>
                <td>+</td>
                <td></td>
                <td>+</td>
            </tr>
            <tr>
                <td>DOCX</td>
                <td>Office Open XML<br />Zipped, XML-based file format developed by Microsoft for representing spreadsheets, charts, presentations, and word processing documents</td>
                <td>+</td>
                <td></td>
                <td>+</td>
                <td></td>
            </tr>
            <tr>
                <td>DOCXF</td>
                <td>A format to create, edit and collaborate on a Form Template.</td>
                <td>+</td>
                <td></td>
                <td>+</td>
                <td></td>
            </tr>
            <tr>
                <td>DOTX</td>
                <td>Word Open XML Document Template<br />Zipped, XML-based file format developed by Microsoft for text document templates. A DOTX template contains formatting settings, styles etc. and can be used to create multiple documents with the same formatting</td>
                <td></td>
                <td>+</td>
                <td></td>
                <td>+</td>
            </tr>
            <tr>
                <td>EPUB</td>
                <td>Electronic Publication<br />Free and open e-book standard created by the International Digital Publishing Forum</td>
                <td></td>
                <td>+</td>
                <td></td>
                <td>+</td>
            </tr>
            <tr>
                <td>FB2</td>
                <td>An ebook extension that lets you read books on your computer or mobile devices</td>
                <td></td>
                <td>+</td>
                <td></td>
                <td>+</td>
            </tr>
            <tr>
                <td>HTML</td>
                <td>HyperText Markup Language<br />The main markup language for web pages</td>
                <td></td>
                <td>+</td>
                <td></td>
                <td>+</td>
            </tr>
            <tr>
                <td>ODT</td>
                <td>Word processing file format of OpenDocument, an open standard for electronic documents</td>
                <td></td>
                <td>+</td>
                <td></td>
                <td>+</td>
            </tr>
            <tr>
                <td>OFORM</td>
                <td>A format to fill out a Form. Form fields are fillable but users cannot change the formatting or parameters of the form elements*.</td>
                <td>+</td>
                <td></td>
                <td>+</td>
                <td></td>
            </tr>
            <tr>
                <td>OTT</td>
                <td>OpenDocument Document Template<br />OpenDocument file format for text document templates. An OTT template contains formatting settings, styles etc. and can be used to create multiple documents with the same formatting</td>
                <td></td>
                <td>+</td>
                <td></td>
                <td>+</td>
            </tr>
            <tr>
                <td>PDF</td>
                <td>Portable Document Format<br />File format used to represent documents regardless of the used software, hardware, and operating systems</td>
                <td></td>
                <td>+</td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td>PDF/A</td>
                <td>Portable Document Format / A<br />An ISO-standardized version of the Portable Document Format (PDF) specialized for use in the archiving and long-term preservation of electronic documents. </td>
                <td></td>
                <td>+</td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td>RTF</td>
                <td>Rich Text Format<br />Document file format developed by Microsoft for cross-platform document interchange</td>
                <td></td>
                <td>+</td>
                <td></td>
                <td>+</td>
            </tr>
            <tr>
                <td>TXT</td>
                <td>Filename extension for text files usually containing very little formatting</td>
                <td></td>
                <td>+</td>
                <td></td>
                <td>+</td>
            </tr>
            <tr>
                <td>XML</td>
                <td>Extensible Markup Language (XML).<br />A simple and flexible markup language that derived from SGML (ISO 8879) and is designed to store and transport data.</td>
                <td></td>
                <td>+</td>
                <td></td>
                <td>+</td>
            </tr>
            <tr>
                <td>XPS</td>
                <td>Open XML Paper Specification<br />Open royalty-free fixed-layout document format developed by Microsoft</td>
                <td></td>
                <td>+</td>
                <td></td>
                <td></td>
            </tr>
        </table>
        <p class="note"><b>*Note</b>: the OFORM format is a format for filling out a form. Therefore, only form fields are editable.</p>
        <p>The following table contains the formats in which you can download a document from the <b>File</b> -> <b>Download as</b> menu.</p>
        <table>
            <tr>
                <td><b>Input format</b></td>
                <td><b>Can be downloaded as</b></td>
            </tr>
            <tr>
                <td>DjVu</td>
                <td>DjVu, PDF</td>
            </tr>
            <tr>
                <td>DOC</td>
                <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
            </tr>
            <tr>
                <td>DOCM</td>
                <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
            </tr>
            <tr>
                <td>DOCX</td>
                <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
            </tr>
            <tr>
                <td>DOCXF</td>
                <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
            </tr>
            <tr>
                <td>DOTX</td>
                <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
            </tr>
            <tr>
                <td>EPUB</td>
                <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
            </tr>
            <tr>
                <td>FB2</td>
                <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
            </tr>
            <tr>
                <td>HTML</td>
                <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
            </tr>
            <tr>
                <td>ODT</td>
                <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
            </tr>
            <tr>
                <td>OFORM</td>
                <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
            </tr>
            <tr>
                <td>OTT</td>
                <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
            </tr>
            <tr>
                <td>PDF</td>
                <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, OFORM, PDF, RTF, TXT</td>
            </tr>
            <tr>
                <td>PDF/A</td>
                <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, OFORM, PDF, RTF, TXT</td>
            </tr>
            <tr>
                <td>RTF</td>
                <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
            </tr>
            <tr>
                <td>TXT</td>
                <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
            </tr>
            <tr>
                <td>XML</td>
                <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
            </tr>
            <tr>
                <td>XPS</td>
                <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT, XPS</td>
            </tr>
        </table>
        <p>You can also refer to the conversion matrix on <a href="https://api.onlyoffice.com/editors/conversionapi#text-matrix" target="_blank" onclick="onhyperlinkclick(this)"><b>api.onlyoffice.com</b></a> to see possibility of conversion your documents into the most known file formats.</p>
    </div>
</body>
</html>