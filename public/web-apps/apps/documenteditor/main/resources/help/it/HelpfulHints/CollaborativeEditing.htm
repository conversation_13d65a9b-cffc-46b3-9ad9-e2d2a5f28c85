﻿<!DOCTYPE html>
<html>
<head>
    <title>Collaborative Document Editing</title>
    <meta charset="utf-8" />
    <meta name="description" content="Tips on collaborative editing" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Collaborative Document Editing</h1>
        <p><b>Document Editor</b> offers you the possibility to work at a document collaboratively with other users. This feature includes:</p>
        <ul>
            <li class="onlineDocumentFeatures">simultaneous multi-user access to the edited document</li>
            <li class="onlineDocumentFeatures">visual indication of passages that are being edited by other users</li>
            <li class="onlineDocumentFeatures">real-time changes display or synchronization of changes with one button click</li>
            <li class="onlineDocumentFeatures">chat to share ideas concerning particular document parts</li>
            <li>comments containing the description of a task or problem that should be solved (it's also possible to work with comments in the offline mode, without connecting to the <em>online version</em>)</li>
        </ul>
        <div class="desktopDocumentFeatures">
            <h3>Connecting to the online version</h3>
            <p>In the <em>desktop editor</em>, open the <b>Connect to cloud</b> option of the left-side menu in the main program window. Connect to your cloud office specifying your account login and password. <!--В окне ввода логина и пароля также доступна ссылка для регистрации в Облаке.--></p>
        </div>
        <div class="onlineDocumentFeatures">
        <h3>Co-editing</h3>
        <p><b>Document Editor</b> allows to select one of the two available co-editing modes:</p>
        <ul>
            <li><b>Fast</b> is used by default and shows the changes made by other users in real time.</li>
            <li><b>Strict</b> is selected to hide other user changes until you click the <b>Save</b> <div class = "icon icon-saveupdate"></div> icon to save your own changes and accept the changes made by others.</li>
        </ul>
            <p>The mode can be selected in the <a href="../HelpfulHints/AdvancedSettings.htm" onclick="onhyperlinkclick(this)">Advanced Settings</a>. It's also possible to choose the necessary mode using the <span class = "icon icon-coeditingmode"></span> <b>Co-editing Mode</b> icon at the <b>Collaboration</b> tab of the top toolbar:</p>
            <p><img alt="Co-editing Mode menu" src="../../../../../../common/main/resources/help/it/images/coeditingmodemenu.png" /></p>
            <p class="note">
                <b>Note</b>: when you co-edit a document in the <b>Fast</b> mode, the possibility to <b>Redo</b> the last undone operation is not available.
            </p>
        <p>When a document is being edited by several users simultaneously in the <b>Strict</b> mode, the edited text passages are marked with dashed lines of different colors. By hovering the mouse cursor over one of the edited passages, the name of the user who is editing it at the moment is displayed. The <b>Fast</b> mode will show the actions and the names of the co-editors once they are editing the text.</p>
        <p>The number of users who are working at the current document is specified on the right side of the editor header - <span class = "icon icon-usersnumber"></span>. If you want to see who exactly are editing the file now, you can click this icon or open the <b>Chat</b> panel with the full list of the users.</p>
        <p>When no users are viewing or editing the file, the icon in the editor header will look like <span class = "icon icon-access_rights"></span> allowing you to manage the users who have access to the file right from the document: invite new users giving them permissions to <em>edit</em>, <em>read</em>, <em>comment</em>, <em>fill forms</em> or <em>review</em> the document, or <em>deny</em> some users access rights to the file. Click this icon to manage the access to the file; this can be done both when there are no other users who view or co-edit the document at the moment and when there are other users and the icon looks like <span class = "icon icon-usersnumber"></span>. It's also possible to set access rights using the <span class = "icon icon-sharingicon"></span> <b>Sharing</b> icon at the <b>Collaboration</b> tab of the top toolbar.</p>
        <p>As soon as one of the users saves his/her changes by clicking the <span class = "icon icon-savewhilecoediting"></span> icon, the others will see a note within the status bar stating that they have updates. To save the changes you made, so that other users can view them, and get the updates saved by your co-editors, click the <span class = "icon icon-saveupdate"></span> icon in the left upper corner of the top toolbar. The updates will be highlighted for you to check what exactly has been changed.</p>
        <p>You can specify what changes you want to be highlighted during co-editing if you click the <b>File</b> tab at the top toolbar, select the <b>Advanced Settings...</b> option and choose between <b>none</b>, <b>all</b> and <b>last</b> real-time collaboration changes. Selecting <b>View all</b> changes, all the changes made during the current session will be highlighted. Selecting <b>View last</b> changes, only the changes made since you last time clicked the <span class = "icon icon-saveupdate"></span> icon will be highlighted. Selecting <b>View None</b> changes, changes made during the current session will not be highlighted.</p>
        <h3 id="chat">Chat</h3>
        <p>You can use this tool to coordinate the co-editing process on-the-fly, for example, to arrange with your collaborators about who is doing what, which paragraph you are going to edit now etc.</p>
        <p>The chat messages are stored during one session only. To discuss the document content it is better to use comments which are stored until you decide to delete them.</p>
        <p>To access the chat and leave a message for other users,</p>
        <ol>
            <li>click the <div class = "icon icon-chaticon"></div> icon at the left sidebar, or <br />
                switch to the <b>Collaboration</b> tab of the top toolbar and click the <div class = "icon icon-chat_toptoolbar"></div> <b>Chat</b> button,
            </li>
            <li>enter your text into the corresponding field below,</li>
            <li>press the <b>Send</b> button.</li>
        </ol>
        <p>All the messages left by users will be displayed on the panel on the left. If there are new messages you haven't read yet, the chat icon will look like this - <span class = "icon icon-chaticon_new"></span>.</p>
        <p>To close the panel with chat messages, click the <span class = "icon icon-chaticon"></span> icon at the left sidebar or the <span class = "icon icon-chat_toptoolbar"></span> <b>Chat</b> button at the top toolbar once again.</p>
        </div>
                <h3 id="comments">Comments</h3>
        <p>It's possible to work with comments in the offline mode, without connecting to the <em>online version</em>.</p>
        <p>To leave a comment,</p>
        <ol>
            <li>select a text passage where you think there is an error or problem,</li>
            <li>
                switch to the <b>Insert</b> or <b>Collaboration</b> tab of the top toolbar and click the <div class = "icon icon-comment_toptoolbar"></div> <b>Comment</b> button, or<br />
                use the <div class = "icon icon-commentsicon"></div> icon at the left sidebar to open the <b>Comments</b> panel and click the <b>Add Comment to Document</b> link, or<br />
                right-click the selected text passage and select the <b>Add Сomment</b> option from the contextual menu,
            </li>
            <li>enter the needed text,</li>
            <li>click the <b>Add Comment/Add</b> button.</li>
        </ol>
        <p>The comment will be seen on the <b>Comments</b> panel on the left. Any other user can answer to the added comment asking questions or reporting on the work he/she has done. For this purpose, click the <b>Add Reply</b> link situated under the comment, type in your reply text in the entry field and press the <b>Reply</b> button.</p>
        <p>If you are using the <b>Strict</b> co-editing mode, new comments added by other users will become visible only after you click the <span class = "icon icon-saveupdate"></span> icon in the left upper corner of the top toolbar.</p>
        <p>The text passage you commented will be highlighted in the document. To view the comment, just click within the passage. If you need to disable this feature, click the <b>File</b> tab at the top toolbar, select the <b>Advanced Settings...</b> option and uncheck the <b>Turn on display of the comments</b> box. In this case the commented passages will be highlighted only if you click the <span class = "icon icon-commentsicon"></span> icon.</p>
        <p>You can manage the added comments using the icons in the comment balloon or at the <b>Comments</b> panel on the left:</p>
        <ul>
            <li>edit the currently selected comment by clicking the <div class = "icon icon-editcommenticon"></div> icon,</li>
            <li>delete the currently selected comment by clicking the <div class = "icon icon-review_delete"></div> icon,</li>
            <li>close the currently selected discussion by clicking the <div class = "icon icon-resolveicon"></div> icon if the task or problem you stated in your comment was solved, after that the discussion you opened with your comment gets the resolved status. To open it again, click the <div class = "icon icon-resolvedicon"></div> icon. If you want to hide resolved comments, click the <b>File</b> tab at the top toolbar, select the <b>Advanced Settings...</b> option, uncheck the <b>Turn on display of the resolved comments</b> box and click <b>Apply</b>. In this case the resolved comments will be highlighted only if you click the <div class = "icon icon-commentsicon"></div> icon.</li>
        </ul>
        <h4>Adding mentions</h4>
        <p>When entering comments, you can use the <b>mentions</b> feature that allows to attract somebody's attention to the comment and send a notification to the mentioned user via email and <b>Talk</b>.</p>
        <p>To add a mention enter the "+" or "@" sign anywhere in the comment text - a list of the portal users will open. To simplify the search process, you can start typing a name in the comment field - the user list will change as you type. Select the necessary person from the list. If the file has not yet been shared with the mentioned user, the <b>Sharing Settings</b> window will open. <b>Read only</b> access type is selected by default. Change it if necessary and click <b>OK</b>.</p>
        <p>The mentioned user will receive an email notification that he/she has been mentioned in a comment. If the file has been shared, the user will also receive a corresponding notification.</p>
        <p>To remove comments,</p>
        <ol>
            <li>click the <div class = "icon icon-removecomment_toptoolbar"></div> <b>Remove</b> button at the <b>Collaboration</b> tab of the top toolbar,</li>
            <li>select the necessary option from the menu:
            <ul>
                <li><b>Remove Current Comments</b> - to remove the currently selected comment. If some replies have beed added to the comment, all its replies will be removed as well.</li>
                <li><b>Remove My Comments</b> - to remove comments you added without removing comments added by other users. If some replies have beed added to your comment, all its replies will be removed as well.</li>
                <li><b>Remove All Comments</b> - to remove all the comments in the document that you and other users added.</li>
            </ul>
            </li>
        </ol>
        <p>To close the panel with comments, click the <span class = "icon icon-commentsicon"></span> icon at the left sidebar once again.</p>        
    </div>
</body>
</html>