var indexes = 
[
   {
        "id": "HelpfulHints/About.htm", 
        "title": "About Document Editor", 
        "body": "Document Editor is an online application that lets you look through and edit documents directly in your browser . Using Document Editor, you can perform various editing operations like in any desktop editor, print the edited documents keeping all the formatting details or download them onto your computer hard disk drive as DOCX, PDF, ODT, TXT, DOTX, PDF/A, OTT, RTF, HTML files. To view the current software version and licensor details in the online version, click the icon at the left sidebar. To view the current software version and licensor details in the desktop version, select the About menu item at the left sidebar of the main program window."
    },
   {
        "id": "HelpfulHints/AdvancedSettings.htm", 
        "title": "Advanced Settings of Document Editor", 
        "body": "Document Editor lets you change its advanced settings. To access them, open the File tab at the top toolbar and select the Advanced Settings... option. You can also click the View settings icon on the right side of the editor header and select the Advanced settings option. The advanced settings are: Commenting Display is used to turn on/off the live commenting option: Turn on display of the comments - if you disable this feature, the commented passages will be highlighted only if you click the Comments icon at the left sidebar. Turn on display of the resolved comments - this feature is disabled by default so that the resolved comments were hidden in the document text. You can view such comments only if you click the Comments icon at the left sidebar. Enable this option if you want to display resolved comments in the document text. Spell Checking is used to turn on/off the spell checking option. Alternate Input is used to turn on/off hieroglyphs. Alignment Guides is used to turn on/off alignment guides that appear when you move objects and allow you to position them on the page precisely. Compatibility is used to make the files compatible with older MS Word versions when saved as DOCX. Autosave is used in the online version to turn on/off automatic saving of changes you make while editing. Autorecover - is used in the desktop version to turn on/off the option that allows to automatically recover documents in case of the unexpected program closing. Co-editing Mode is used to select the display of the changes made during the co-editing: By default the Fast mode is selected, the users who take part in the document co-editing will see the changes in real time once they are made by other users. If you prefer not to see other user changes (so that they do not disturb you, or for some other reason), select the Strict mode and all the changes will be shown only after you click the Save icon notifying you that there are changes from other users. Real-time Collaboration Changes is used to specify what changes you want to be highlighted during co-editing: Selecting the View None option, changes made during the current session will not be highlighted. Selecting the View All option, all the changes made during the current session will be highlighted. Selecting the View Last option, only the changes made since you last time clicked the Save icon will be highlighted. This option is only available when the Strict co-editing mode is selected. Default Zoom Value is used to set the default zoom value selecting it in the list of available options from 50% to 200%. You can also choose the Fit to Page or Fit to Width option. Font Hinting is used to select the type a font is displayed in Document Editor: Choose As Windows if you like the way fonts are usually displayed on Windows, i.e. using Windows font hinting. Choose As OS X if you like the way fonts are usually displayed on a Mac, i.e. without any font hinting at all. Choose Native if you want your text to be displayed with the hinting embedded into font files. Default cache mode - used to select the cache mode for the font characters. It’s not recommended to switch it without any reason. It can be helpful in some cases only, for example, when an issue in the Google Chrome browser with the enabled hardware acceleration occurs. Document Editor has two cache modes: In the first cache mode, each letter is cached as a separate picture. In the second cache mode, a picture of a certain size is selected where letters are placed dynamically and a mechanism of allocating/removing memory in this picture is also implemented. If there is not enough memory, a second picture is created, etc. The Default cache mode setting applies two above mentioned cache modes separately for different browsers: When the Default cache mode setting is enabled, Internet Explorer (v. 9, 10, 11) uses the second cache mode, other browsers use the first cache mode. When the Default cache mode setting is disabled, Internet Explorer (v. 9, 10, 11) uses the first cache mode, other browsers use the second cache mode. Unit of Measurement is used to specify what units are used on the rulers and in properties windows for measuring elements parameters such as width, height, spacing, margins etc. You can select the Centimeter, Point, or Inch option. To save the changes you made, click the Apply button."
    },
   {
        "id": "HelpfulHints/CollaborativeEditing.htm", 
        "title": "Collaborative Document Editing", 
        "body": "Document Editor offers you the possibility to work at a document collaboratively with other users. This feature includes: simultaneous multi-user access to the edited document visual indication of passages that are being edited by other users real-time changes display or synchronization of changes with one button click chat to share ideas concerning particular document parts comments containing the description of a task or problem that should be solved (it's also possible to work with comments in the offline mode, without connecting to the online version) Connecting to the online version In the desktop editor, open the Connect to cloud option of the left-side menu in the main program window. Connect to your cloud office specifying your account login and password. Co-editing Document Editor allows to select one of the two available co-editing modes: Fast is used by default and shows the changes made by other users in real time. Strict is selected to hide other user changes until you click the Save icon to save your own changes and accept the changes made by others. The mode can be selected in the Advanced Settings. It's also possible to choose the necessary mode using the Co-editing Mode icon at the Collaboration tab of the top toolbar: Note: when you co-edit a document in the Fast mode, the possibility to Redo the last undone operation is not available. When a document is being edited by several users simultaneously in the Strict mode, the edited text passages are marked with dashed lines of different colors. By hovering the mouse cursor over one of the edited passages, the name of the user who is editing it at the moment is displayed. The Fast mode will show the actions and the names of the co-editors once they are editing the text. The number of users who are working at the current document is specified on the right side of the editor header - . If you want to see who exactly are editing the file now, you can click this icon or open the Chat panel with the full list of the users. When no users are viewing or editing the file, the icon in the editor header will look like allowing you to manage the users who have access to the file right from the document: invite new users giving them permissions to edit, read, comment, fill forms or review the document, or deny some users access rights to the file. Click this icon to manage the access to the file; this can be done both when there are no other users who view or co-edit the document at the moment and when there are other users and the icon looks like . It's also possible to set access rights using the Sharing icon at the Collaboration tab of the top toolbar. As soon as one of the users saves his/her changes by clicking the icon, the others will see a note within the status bar stating that they have updates. To save the changes you made, so that other users can view them, and get the updates saved by your co-editors, click the icon in the left upper corner of the top toolbar. The updates will be highlighted for you to check what exactly has been changed. You can specify what changes you want to be highlighted during co-editing if you click the File tab at the top toolbar, select the Advanced Settings... option and choose between none, all and last real-time collaboration changes. Selecting View all changes, all the changes made during the current session will be highlighted. Selecting View last changes, only the changes made since you last time clicked the icon will be highlighted. Selecting View None changes, changes made during the current session will not be highlighted. Chat You can use this tool to coordinate the co-editing process on-the-fly, for example, to arrange with your collaborators about who is doing what, which paragraph you are going to edit now etc. The chat messages are stored during one session only. To discuss the document content it is better to use comments which are stored until you decide to delete them. To access the chat and leave a message for other users, click the icon at the left sidebar, or switch to the Collaboration tab of the top toolbar and click the Chat button, enter your text into the corresponding field below, press the Send button. All the messages left by users will be displayed on the panel on the left. If there are new messages you haven't read yet, the chat icon will look like this - . To close the panel with chat messages, click the icon at the left sidebar or the Chat button at the top toolbar once again. Comments It's possible to work with comments in the offline mode, without connecting to the online version. To leave a comment, select a text passage where you think there is an error or problem, switch to the Insert or Collaboration tab of the top toolbar and click the Comment button, or use the icon at the left sidebar to open the Comments panel and click the Add Comment to Document link, or right-click the selected text passage and select the Add Сomment option from the contextual menu, enter the needed text, click the Add Comment/Add button. The comment will be seen on the Comments panel on the left. Any other user can answer to the added comment asking questions or reporting on the work he/she has done. For this purpose, click the Add Reply link situated under the comment, type in your reply text in the entry field and press the Reply button. If you are using the Strict co-editing mode, new comments added by other users will become visible only after you click the icon in the left upper corner of the top toolbar. The text passage you commented will be highlighted in the document. To view the comment, just click within the passage. If you need to disable this feature, click the File tab at the top toolbar, select the Advanced Settings... option and uncheck the Turn on display of the comments box. In this case the commented passages will be highlighted only if you click the icon. You can manage the added comments using the icons in the comment balloon or at the Comments panel on the left: edit the currently selected comment by clicking the icon, delete the currently selected comment by clicking the icon, close the currently selected discussion by clicking the icon if the task or problem you stated in your comment was solved, after that the discussion you opened with your comment gets the resolved status. To open it again, click the icon. If you want to hide resolved comments, click the File tab at the top toolbar, select the Advanced Settings... option, uncheck the Turn on display of the resolved comments box and click Apply. In this case the resolved comments will be highlighted only if you click the icon. Adding mentions When entering comments, you can use the mentions feature that allows to attract somebody's attention to the comment and send a notification to the mentioned user via email and Talk. To add a mention enter the \"+\" or \"@\" sign anywhere in the comment text - a list of the portal users will open. To simplify the search process, you can start typing a name in the comment field - the user list will change as you type. Select the necessary person from the list. If the file has not yet been shared with the mentioned user, the Sharing Settings window will open. Read only access type is selected by default. Change it if necessary and click OK. The mentioned user will receive an email notification that he/she has been mentioned in a comment. If the file has been shared, the user will also receive a corresponding notification. To remove comments, click the Remove button at the Collaboration tab of the top toolbar, select the necessary option from the menu: Remove Current Comments - to remove the currently selected comment. If some replies have beed added to the comment, all its replies will be removed as well. Remove My Comments - to remove comments you added without removing comments added by other users. If some replies have beed added to your comment, all its replies will be removed as well. Remove All Comments - to remove all the comments in the document that you and other users added. To close the panel with comments, click the icon at the left sidebar once again."
    },
   {
        "id": "HelpfulHints/Comparison.htm", 
        "title": "Compare documents", 
        "body": "Note: this option is available in the paid online version only starting from Document Server v. 5.5. If you need to compare and merge two documents, you can use the document Compare feature. It allows to display the differences between two documents and merge the documents by accepting the changes one by one or all at once. After comparing and merging two documents, the result will be stored on the portal as a new version of the original file. If you do not need to merge documents which are being compared, you can reject all the changes so that the original document remains unchanged. Choose a document for comparison To compare two documents, open the original document that you need to compare and select the second document for comparison: switch to the Collaboration tab at the top toolbar and press the Compare button, select one of the following options to load the document: the Document from File option will open the standard dialog window for file selection. Browse your computer hard disk drive for the necessary .docx file and click the Open button. the Document from URL option will open the window where you can enter a link to the file stored in a third-party web storage (for example, Nextcloud) if you have corresponding access rights to it. The link must be a direct link for downloading the file. When the link is specified, click the OK button. Note: The direct link allows to download the file directly without opening it in a web browser. For example, to get a direct link in Nextcloud, find the necessary document in the file list, select the Details option from the file menu. Click the Copy direct link (only works for users who have access to this file/folder) icon to the right of the file name at the details panel. To find out how to get a direct link for downloading the file in a different third-party web storage, please refer to the corresponding third-party service documentation. the Document from Storage option will open the Select Data Source window. It displays the list of all the .docx documents stored on your portal you have corresponding access rights to. To navigate between the Documents module sections use the menu in the left part of the window. Select the necessary .docx document and click the OK button. When the second document for comparison is selected, the comparison process will start and the document will look as if it was opened in the Review mode. All the changes are highlighted with a color, and you can view the changes, navigate between them, accept or reject them one by one or all the changes at once. It's also possible to change the display mode and see how the document looks before comparison, in the process of comparison, or how it will look after comparison if you accept all changes. Choose the changes display mode Click the Display Mode button at the top toolbar and select one of the available modes from the list: Markup - this option is selected by default. It is used to display the document in the process of comparison. This mode allows both to view the changes and edit the document. Final - this mode is used to display the document after comparison as if all the changes were accepted. This option does not actually accept all changes, it only allows you to see how the document will look like after you accept all the changes. In this mode, you cannot edit the document. Original - this mode is used to display the document before comparison as if all the changes were rejected. This option does not actually reject all changes, it only allows you to view the document without changes. In this mode, you cannot edit the document. Accept or reject changes Use the Previous and the Next buttons at the top toolbar to navigate among the changes. To accept the currently selected change you can: click the Accept button at the top toolbar, or click the downward arrow below the Accept button and select the Accept Current Change option (in this case, the change will be accepted and you will proceed to the next change), or click the Accept button of the change notification. To quickly accept all the changes, click the downward arrow below the Accept button and select the Accept All Changes option. To reject the current change you can: click the Reject button at the top toolbar, or click the downward arrow below the Reject button and select the Reject Current Change option (in this case, the change will be rejected and you will move on to the next available change), or click the Reject button of the change notification. To quickly reject all the changes, click the downward arrow below the Reject button and select the Reject All Changes option. Additional info on the comparison feature Method of the comparison Documents are compared by words. If a word contains a change of at least one character (e.g. if a character was removed or replaced), in the result, the difference will be displayed as the change of the entire word, not the character. The image below illustrates the case when the original file contains the word 'Characters' and the document for comparison contains the word 'Character'. Authorship of the document When the comparison process is launched, the second document for comparison is being loaded and compared to the current one. If the loaded document contains some data which is not represented in the original document, the data will be marked as added by a reviewer. If the original document contains some data which is not represented in the loaded document, the data will be marked as deleted by a reviewer. If the authors of the original and loaded documents are the same person, the reviewer is the same user. His/her name is displayed in the change balloon. If the authors of two files are different users, then the author of the second file loaded for comparison is the author of the added/removed changes. Presence of the tracked changes in the compared document If the original document contains some changes made in the review mode, they will be accepted in the comparison process. When you choose the second file for comparison, you'll see the corresponding warning message. In this case, when you choose the Original display mode, the document will not contain any changes."
    },
   {
        "id": "HelpfulHints/KeyboardShortcuts.htm", 
        "title": "Keyboard Shortcuts", 
        "body": "Windows/LinuxMac OS Working with Document Open 'File' panel Alt+F ⌥ Option+F Open the File panel to save, download, print the current document, view its info, create a new document or open an existing one, access Document Editor help or advanced settings. Open 'Find and Replace' dialog box Ctrl+F ^ Ctrl+F, &#8984; Cmd+F Open the Find and Replace dialog box to start searching for a character/word/phrase in the currently edited document. Open 'Find and Replace' dialog box with replacement field Ctrl+H ^ Ctrl+H Open the Find and Replace dialog box with the replacement field to replace one or more occurrences of the found characters. Repeat the last 'Find' action ⇧ Shift+F4 ⇧ Shift+F4, &#8984; Cmd+G, &#8984; Cmd+⇧ Shift+F4 Repeat the Find action which has been performed before the key combination press. Open 'Comments' panel Ctrl+⇧ Shift+H ^ Ctrl+⇧ Shift+H, &#8984; Cmd+⇧ Shift+H Open the Comments panel to add your own comment or reply to other users' comments. Open comment field Alt+H ⌥ Option+H Open a data entry field where you can add the text of your comment. Open 'Chat' panel Alt+Q ⌥ Option+Q Open the Chat panel and send a message. Save document Ctrl+S ^ Ctrl+S, &#8984; Cmd+S Save all the changes to the document currently edited with Document Editor. The active file will be saved with its current file name, location, and file format. Print document Ctrl+P ^ Ctrl+P, &#8984; Cmd+P Print the document with one of the available printers or save it to a file. Download As... Ctrl+⇧ Shift+S ^ Ctrl+⇧ Shift+S, &#8984; Cmd+⇧ Shift+S Open the Download as... panel to save the currently edited document to the computer hard disk drive in one of the supported formats: DOCX, PDF, ODT, TXT, DOTX, PDF/A, OTT, RTF, HTML. Full screen F11 Switch to the full screen view to fit Document Editor into your screen. Help menu F1 F1 Open Document Editor Help menu. Open existing file (Desktop Editors) Ctrl+O On the Open local file tab in Desktop Editors, opens the standard dialog box that allows to select an existing file. Close file (Desktop Editors) Ctrl+W, Ctrl+F4 ^ Ctrl+W, &#8984; Cmd+W Close the current document window in Desktop Editors. Element contextual menu ⇧ Shift+F10 ⇧ Shift+F10 Open the selected element contextual menu. Navigation Jump to the beginning of the line Home Home Put the cursor to the beginning of the currently edited line. Jump to the beginning of the document Ctrl+Home ^ Ctrl+Home Put the cursor to the very beginning of the currently edited document. Jump to the end of the line End End Put the cursor to the end of the currently edited line. Jump to the end of the document Ctrl+End ^ Ctrl+End Put the cursor to the very end of the currently edited document. Jump to the beginning of the previous page Alt+Ctrl+Page Up Put the cursor to the very beginning of the page which preceeds the currently edited one. Jump to the beginning of the next page Alt+Ctrl+Page Down ⌥ Option+&#8984; Cmd+⇧ Shift+Page Down Put the cursor to the very beginning of the page which follows the currently edited one. Scroll down Page Down Page Down, ⌥ Option+Fn+↑ Scroll the document approximately one visible page down. Scroll up Page Up Page Up, ⌥ Option+Fn+↓ Scroll the document approximately one visible page up. Next page Alt+Page Down ⌥ Option+Page Down Go to the next page in the currently edited document. Previous page Alt+Page Up ⌥ Option+Page Up Go to the previous page in the currently edited document. Zoom In Ctrl++ ^ Ctrl+=, &#8984; Cmd+= Zoom in the currently edited document. Zoom Out Ctrl+- ^ Ctrl+-, &#8984; Cmd+- Zoom out the currently edited document. Move one character to the left ← ← Move the cursor one character to the left. Move one character to the right → → Move the cursor one character to the right. Move to the beginning of a word or one word to the left Ctrl+← ^ Ctrl+←, &#8984; Cmd+← Move the cursor to the beginning of a word or one word to the left. Move one word to the right Ctrl+→ ^ Ctrl+→, &#8984; Cmd+→ Move the cursor one word to the right. Move one line up ↑ ↑ Move the cursor one line up. Move one line down ↓ ↓ Move the cursor one line down. Writing End paragraph ↵ Enter ↵ Return End the current paragraph and start a new one. Add line break ⇧ Shift+↵ Enter ⇧ Shift+↵ Return Add a line break without starting a new paragraph. Delete ← Backspace, Delete ← Backspace, Delete Delete one character to the left (← Backspace) or to the right (Delete) of the cursor. Delete word to the left of cursor Ctrl+← Backspace ^ Ctrl+← Backspace, &#8984; Cmd+← Backspace Delete one word to the left of the cursor. Delete word to the right of cursor Ctrl+Delete ^ Ctrl+Delete, &#8984; Cmd+Delete Delete one word to the right of the cursor. Create nonbreaking space Ctrl+⇧ Shift+␣ Spacebar ^ Ctrl+⇧ Shift+␣ Spacebar Create a space between characters which cannot be used to start a new line. Create nonbreaking hyphen Ctrl+⇧ Shift+Hyphen ^ Ctrl+⇧ Shift+Hyphen Create a hyphen between characters which cannot be used to start a new line. Undo and Redo Undo Ctrl+Z ^ Ctrl+Z, &#8984; Cmd+Z Reverse the latest performed action. Redo Ctrl+Y ^ Ctrl+Y, &#8984; Cmd+Y, &#8984; Cmd+⇧ Shift+Z Repeat the latest undone action. Cut, Copy, and Paste Cut Ctrl+X, ⇧ Shift+Delete &#8984; Cmd+X, ⇧ Shift+Delete Delete the selected text fragment and send it to the computer clipboard memory. The copied text can be later inserted to another place in the same document, into another document, or into some other program. Copy Ctrl+C, Ctrl+Insert &#8984; Cmd+C Send the selected text fragment to the computer clipboard memory. The copied text can be later inserted to another place in the same document, into another document, or into some other program. Paste Ctrl+V, ⇧ Shift+Insert &#8984; Cmd+V Insert the previously copied text fragment from the computer clipboard memory to the current cursor position. The text can be previously copied from the same document, from another document, or from some other program. Insert hyperlink Ctrl+K &#8984; Cmd+K Insert a hyperlink which can be used to go to a web address. Copy style Ctrl+⇧ Shift+C &#8984; Cmd+⇧ Shift+C Copy the formatting from the selected fragment of the currently edited text. The copied formatting can be later applied to another text fragment in the same document. Apply style Ctrl+⇧ Shift+V &#8984; Cmd+⇧ Shift+V Apply the previously copied formatting to the text in the currently edited document. Text Selection Select all Ctrl+A &#8984; Cmd+A Select all the document text with tables and images. Select fragment ⇧ Shift+→ ← ⇧ Shift+→ ← Select the text character by character. Select from cursor to beginning of line ⇧ Shift+Home ⇧ Shift+Home Select a text fragment from the cursor to the beginning of the current line. Select from cursor to end of line ⇧ Shift+End ⇧ Shift+End Select a text fragment from the cursor to the end of the current line. Select one character to the right ⇧ Shift+→ ⇧ Shift+→ Select one character to the right of the cursor position. Select one character to the left ⇧ Shift+← ⇧ Shift+← Select one character to the left of the cursor position. Select to the end of a word Ctrl+⇧ Shift+→ Select a text fragment from the cursor to the end of a word. Select to the beginning of a word Ctrl+⇧ Shift+← Select a text fragment from the cursor to the beginning of a word. Select one line up ⇧ Shift+↑ ⇧ Shift+↑ Select one line up (with the cursor at the beginning of a line). Select one line down ⇧ Shift+↓ ⇧ Shift+↓ Select one line down (with the cursor at the beginning of a line). Select the page up ⇧ Shift+Page Up ⇧ Shift+Page Up Select the page part from the cursor position to the upper part of the screen. Select the page down ⇧ Shift+Page Down ⇧ Shift+Page Down Select the page part from the cursor position to the lower part of the screen. Text Styling Bold Ctrl+B ^ Ctrl+B, &#8984; Cmd+B Make the font of the selected text fragment bold giving it more weight. Italic Ctrl+I ^ Ctrl+I, &#8984; Cmd+I Make the font of the selected text fragment italicized giving it some right side tilt. Underline Ctrl+U ^ Ctrl+U, &#8984; Cmd+U Make the selected text fragment underlined with the line going under the letters. Strikeout Ctrl+5 ^ Ctrl+5, &#8984; Cmd+5 Make the selected text fragment struck out with the line going through the letters. Subscript Ctrl+. ^ Ctrl+⇧ Shift+&gt;, &#8984; Cmd+⇧ Shift+&gt; Make the selected text fragment smaller and place it to the lower part of the text line, e.g. as in chemical formulas. Superscript Ctrl+, ^ Ctrl+⇧ Shift+&lt;, &#8984; Cmd+⇧ Shift+&lt; Make the selected text fragment smaller and place it to the upper part of the text line, e.g. as in fractions. Heading 1 style Alt+1 ⌥ Option+^ Ctrl+1 Apply the style of the heading 1 to the selected text fragment. Heading 2 style Alt+2 ⌥ Option+^ Ctrl+2 Apply the style of the heading 2 to the selected text fragment. Heading 3 style Alt+3 ⌥ Option+^ Ctrl+3 Apply the style of the heading 3 to the selected text fragment. Bulleted list Ctrl+⇧ Shift+L ^ Ctrl+⇧ Shift+L, &#8984; Cmd+⇧ Shift+L Create an unordered bulleted list from the selected text fragment or start a new one. Remove formatting Ctrl+␣ Spacebar Remove formatting from the selected text fragment. Increase font Ctrl+] &#8984; Cmd+] Increase the size of the font for the selected text fragment 1 point. Decrease font Ctrl+[ &#8984; Cmd+[ Decrease the size of the font for the selected text fragment 1 point. Align center/left Ctrl+E ^ Ctrl+E, &#8984; Cmd+E Switch a paragraph between centered and left-aligned. Align justified/left Ctrl+J, Ctrl+L ^ Ctrl+J, &#8984; Cmd+J Switch a paragraph between justified and left-aligned. Align right/left Ctrl+R ^ Ctrl+R Switch a paragraph between right-aligned and left-aligned. Apply subscript formatting (automatic spacing) Ctrl+= Apply subscript formatting to the selected text fragment. Apply superscript formatting (automatic spacing) Ctrl+⇧ Shift++ Apply superscript formatting to the selected text fragment. Insert page break Ctrl+↵ Enter ^ Ctrl+↵ Return Insert a page break at the current cursor position. Increase indent Ctrl+M ^ Ctrl+M Indent a paragraph from the left incrementally. Decrease indent Ctrl+⇧ Shift+M ^ Ctrl+⇧ Shift+M Remove a paragraph indent from the left incrementally. Add page number Ctrl+⇧ Shift+P ^ Ctrl+⇧ Shift+P Add the current page number at the current cursor position. Nonprinting characters Ctrl+⇧ Shift+Num8 Show or hide the display of nonprinting characters. Delete one character to the left ← Backspace ← Backspace Delete one character to the left of the cursor. Delete one character to the right Delete Delete Delete one character to the right of the cursor. Modifying Objects Constrain movement ⇧ Shift + drag ⇧ Shift + drag Constrain the movement of the selected object horizontally or vertically. Set 15-degree rotation ⇧ Shift + drag (when rotating) ⇧ Shift + drag (when rotating) Constrain the rotation angle to 15-degree increments. Maintain proportions ⇧ Shift + drag (when resizing) ⇧ Shift + drag (when resizing) Maintain the proportions of the selected object when resizing. Draw straight line or arrow ⇧ Shift + drag (when drawing lines/arrows) ⇧ Shift + drag (when drawing lines/arrows) Draw a straight vertical/horizontal/45-degree line or arrow. Movement by one-pixel increments Ctrl+← → ↑ ↓ Hold down the Ctrl key and use the keybord arrows to move the selected object by one pixel at a time. Working with Tables Move to the next cell in a row ↹ Tab ↹ Tab Go to the next cell in a table row. Move to the previous cell in a row ⇧ Shift+↹ Tab ⇧ Shift+↹ Tab Go to the previous cell in a table row. Move to the next row ↓ ↓ Go to the next row in a table. Move to the previous row ↑ ↑ Go to the previous row in a table. Start new paragraph ↵ Enter ↵ Return Start a new paragraph within a cell. Add new row ↹ Tab in the lower right table cell. ↹ Tab in the lower right table cell. Add a new row at the bottom of the table. Inserting special characters Insert formula Alt+= Insert a formula at the current cursor position."
    },
   {
        "id": "HelpfulHints/Navigation.htm", 
        "title": "View Settings and Navigation Tools", 
        "body": "Document Editor offers several tools to help you view and navigate through your document: zoom, page number indicator etc. Adjust the View Settings To adjust default view settings and set the most convenient mode to work with the document, click the View settings icon on the right side of the editor header and select which interface elements you want to be hidden or shown. You can select the following options from the View settings drop-down list: Hide Toolbar - hides the top toolbar that contains commands while tabs remain visible. When this option is enabled, you can click any tab to display the toolbar. The toolbar is displayed until you click anywhere outside it. To disable this mode, click the View settings icon and click the Hide Toolbar option once again. The top toolbar will be displayed all the time. Note: alternatively, you can just double-click any tab to hide the top toolbar or display it again. Hide Status Bar - hides the bottommost bar where the Page Number Indicator and Zoom buttons are situated. To show the hidden Status Bar click this option once again. Hide Rulers - hides rulers which are used to align text, graphics, tables, and other elements in a document, set up margins, tab stops, and paragraph indents. To show the hidden Rulers click this option once again. The right sidebar is minimized by default. To expand it, select any object (e.g. image, chart, shape) or text passage and click the icon of the currently activated tab on the right. To minimize the right sidebar, click the icon once again. When the Comments or Chat panel is opened, the left sidebar width is adjusted by simple drag-and-drop: move the mouse cursor over the left sidebar border so that it turns into the bidirectional arrow and drag the border to the right to extend the sidebar width. To restore its original width move the border to the left. Use the Navigation Tools To navigate through your document, use the following tools: The Zoom buttons are situated in the right lower corner and are used to zoom in and out the current document. To change the currently selected zoom value that is displayed in percent, click it and select one of the available zoom options from the list or use the Zoom in or Zoom out buttons. Click the Fit width icon to fit the document page width to the visible part of the working area. To fit the whole document page to the visible part of the working area, click the Fit page icon. Zoom settings are also available in the View settings drop-down list that can be useful if you decide to hide the Status Bar. The Page Number Indicator shows the current page as a part of all the pages in the current document (page 'n' of 'nn'). Click this caption to open the window where you can enter the page number and quickly go to it."
    },
   {
        "id": "HelpfulHints/Review.htm", 
        "title": "Document Review", 
        "body": "When somebody shares a file with you that has review permissions, you need to use the document Review feature. If you are the reviewer, then you can use the Review option to review the document, change the sentences, phrases and other page elements, correct spelling, and do other things to the document without actually editing it. All your changes will be recorded and shown to the person who sent the document to you. If you are the person who sends the file for the review, you will need to display all the changes which were made to it, view and either accept or reject them. Enable the Track Changes feature To see changes suggested by a reviewer, enable the Track Changes option in one of the following ways: click the button in the right lower corner at the status bar, or switch to the Collaboration tab at the top toolbar and press the Track Changes button. Note: it is not necessary for the reviewer to enable the Track Changes option. It is enabled by default and cannot be disabled when the document is shared with review only access rights. Choose the changes display mode Click the Display Mode button at the top toolbar and select one of the available modes from the list: Markup - this option is selected by default. It allows both to view suggested changes and edit the document. Final - this mode is used to display all the changes as if they were accepted. This option does not actually accept all changes, it only allows you to see how the document will look like after you accept all the changes. In this mode, you cannot edit the document. Original - this mode is used to display all the changes as if they were rejected. This option does not actually reject all changes, it only allows you to view the document without changes. In this mode, you cannot edit the document. Accept or reject changes Use the Previous and the Next buttons at the top toolbar to navigate among the changes. To accept the currently selected change you can: click the Accept button at the top toolbar, or click the downward arrow below the Accept button and select the Accept Current Change option (in this case, the change will be accepted and you will proceed to the next change), or click the Accept button of the change notification. To quickly accept all the changes, click the downward arrow below the Accept button and select the Accept All Changes option. To reject the current change you can: click the Reject button at the top toolbar, or click the downward arrow below the Reject button and select the Reject Current Change option (in this case, the change will be rejected and you will move on to the next available change), or click the Reject button of the change notification. To quickly reject all the changes, click the downward arrow below the Reject button and select the Reject All Changes option. Note: if you review the document the Accept and Reject options are not available for you. You can delete your changes using the icon within the change balloon."
    },
   {
        "id": "HelpfulHints/Search.htm", 
        "title": "Search and Replace Function", 
        "body": "To search for the needed characters, words or phrases used in the currently edited document, click the icon situated at the left sidebar or use the Ctrl+F key combination. The Find and Replace window will open: Type in your inquiry into the corresponding data entry field. Specify search parameters by clicking the icon and checking the necessary options: Case sensitive - is used to find only the occurrences typed in the same case as your inquiry (e.g. if your inquiry is 'Editor' and this option is selected, such words as 'editor' or 'EDITOR' etc. will not be found). To disable this option click it once again. Highlight results - is used to highlight all found occurrences at once. To disable this option and remove the highlight click the option once again. Click one of the arrow buttons at the bottom right corner of the window. The search will be performed either towards the beginning of the document (if you click the button) or towards the end of the document (if you click the button) from the current position. Note: when the Highlight results option is enabled, use these buttons to navigate through the highlighted results. The first occurrence of the required characters in the selected direction will be highlighted on the page. If it is not the word you are looking for, click the selected button again to find the next occurrence of the characters you entered. To replace one or more occurrences of the found characters click the Replace link below the data entry field or use the Ctrl+H key combination. The Find and Replace window will change: Type in the replacement text into the bottom data entry field. Click the Replace button to replace the currently selected occurrence or the Replace All button to replace all the found occurrences. To hide the replace field, click the Hide Replace link."
    },
   {
        "id": "HelpfulHints/SpellChecking.htm", 
        "title": "Spell-checking", 
        "body": "Document Editor allows you to check the spelling of your text in a certain language and correct mistakes while editing. In the desktop version, it's also possible to add words into a custom dictionary which is common for all three editors. First of all, choose a language for your document. Click the Set Document Language icon at the status bar. In the window that appears, select the necessary language and click OK. The selected language will be applied to the whole document. To choose a different language for any piece of text within the document, select the necessary text passage with the mouse and use the menu at the status bar. To enable the spell checking option, you can: click the Spell checking icon at the status bar, or open the File tab of the top toolbar, select the Advanced Settings... option, check the Turn on spell checking option box and click the Apply button. Incorrectly spelled words will be underlined by a red line. Right click on the necessary word to activate the menu and: choose one of the suggested similar words spelled correctly to replace the misspelled word with the suggested one. If too many variants are found, the More variants... option appears in the menu; use the Ignore option to skip just that word and remove underlining or Ignore All to skip all the identical words repeated in the text; if the current word is missed in the dictionary, you can add it to the custom dictionary. This word will not be treated as a mistake next time. This option is available in the desktop version. select a different language for this word. To disable the spell checking option, you can: click the Spell checking icon at the status bar, or open the File tab of the top toolbar, select the Advanced Settings... option, uncheck the Turn on spell checking option box and click the Apply button."
    },
   {
        "id": "HelpfulHints/SupportedFormats.htm", 
        "title": "Supported Formats of Electronic Documents", 
        "body": "Electronic documents represent one of the most commonly used computer files. Thanks to the computer network highly developed nowadays, it's possible and more convenient to distribute electronic documents than printed ones. Due to the variety of devices used for document presentation, there are a lot of proprietary and open file formats. Document Editor handles the most popular of them. Formats Description View Edit Download DOC Filename extension for word processing documents created with Microsoft Word + + DOCX Office Open XML Zipped, XML-based file format developed by Microsoft for representing spreadsheets, charts, presentations, and word processing documents + + + DOTX Word Open XML Document Template Zipped, XML-based file format developed by Microsoft for text document templates. A DOTX template contains formatting settings, styles etc. and can be used to create multiple documents with the same formatting + + + ODT Word processing file format of OpenDocument, an open standard for electronic documents + + + OTT OpenDocument Document Template OpenDocument file format for text document templates. An OTT template contains formatting settings, styles etc. and can be used to create multiple documents with the same formatting + + + RTF Rich Text Format Document file format developed by Microsoft for cross-platform document interchange + + + TXT Filename extension for text files usually containing very little formatting + + + PDF Portable Document Format File format used to represent documents in a manner independent of application software, hardware, and operating systems + + PDF/A Portable Document Format / A An ISO-standardized version of the Portable Document Format (PDF) specialized for use in the archiving and long-term preservation of electronic documents. + + HTML HyperText Markup Language The main markup language for web pages + + in the online version EPUB Electronic Publication Free and open e-book standard created by the International Digital Publishing Forum + XPS Open XML Paper Specification Open royalty-free fixed-layout document format developed by Microsoft + DjVu File format designed primarily to store scanned documents, especially those containing a combination of text, line drawings, and photographs +"
    },
   {
        "id": "ProgramInterface/FileTab.htm", 
        "title": "Scheda File", 
        "body": "La scheda File consente di eseguire alcune operazioni di base sul file corrente. Finestra dell’Editor di Documenti Online: Finestra dell’Editor di Documenti Desktop: Usando questa scheda, puoi: nella versione online, salvare il file corrente (nel caso in cui l’opzione di Salvataggio automatico sia disabilitata), scaricare in (salva il documento nel formato selezionato sul disco fisso del computer), salvare copia come (salva una copia del documento nel formato selezionato nel portale I miei documenti), stamparlo o rinominarlo, nella versione desktop, salvare il file corrente mantenendo il formato e la posizione correnti utilizzando l’opzione Salva o salvare il file corrente con un nome, una posizione o un formato diversi usando l’opzione Salva con Nome, stampare il file. proteggere il file utilizzando una password, modificare o rimuovere la password (disponibile solo nella versione desktop); creare un nuovo documento o aprirne uno modificato di recente (disponibile solo nella versione online), visualizzare le Informazioni documento o modificare alcune proprietà del file, gestire i Diritti di accesso (disponibile solo nella versione online), tracciare la Cronologia delle versioni (disponibile solo nella versione online), accedere all’editor Impostazioni avanzate, nella versione desktop, aprire la cartella in cui è archiviato il file nella finestra Apri percorso file. Nella versione online, aprire la cartella del modulo I miei documenti in cui è archiviato il file in una nuova scheda del browser."
    },
   {
        "id": "ProgramInterface/HomeTab.htm", 
        "title": "Scheda Home", 
        "body": "La scheda Home si apre per impostazione predefinita quando si apre un documento. Permette di formattare caratteri e paragrafi. Qui sono anche disponibili alcune altre opzioni, come Stampa unione e Cambia combinazione colori. Finestra dell’Editor di Documenti Online: Finestra dell’editor di Documenti Desktop: Usando questa scheda, puoi: regolare il tipo, la dimensione, il colore del carattere, applicare stili di decorazione del carattere, selezionare un colore di sfondo per un paragrafo, creare elenchi puntati e numerati, cmodificare i rientri di un paragrafo, impostare l’interlinea del paragrafo, allineare il testo in un paragrafo, mostrare/nascondere caratteri non stampabili, copiare/cancellare la formattazione del testo, cambiare la combinazione colori, usare Stampa unione (disponibile solo nella versione online), gestire gli stili."
    },
   {
        "id": "ProgramInterface/InsertTab.htm", 
        "title": "Scheda Inserisci", 
        "body": "La scheda Inserisci consente di aggiungere alcuni elementi di formattazione della pagina, nonché oggetti visivi e commenti. Finestra dell’Editor di Documenti Online: Finestra dell’Editor di Documenti Desktop: Usando questa scheda, puoi: inserire una pagina vuota, inserire interruzioni di pagina,  interruzioni di sezione e interruzioni di colonna, inserire intestazioni e piè di pagina e numeri di pagina, inserire tabelle, immagini, grafici, forme, inserire collegamenti ipertestuali, commenti, inserire caselle di testo ed oggetti Text Art, equazioni, simboli, capilettera, controlli del contenuto."
    },
   {
        "id": "ProgramInterface/LayoutTab.htm", 
        "title": "Scheda Layout di Pagina", 
        "body": "La scheda Layout di Pagina consente di modificare l'aspetto del documento: impostare i parametri della pagina e definire la disposizione degli elementi visivi. Finestra dell’Editor di Documenti Online: Finestra dell’Editor di Documenti Desktop: Usando questa scheda, puoi: regolare i margini, l’orientatamento, la dimensione della pagina, aggiungere colonne, inserire interruzioni di pagina, interruzioni di sezione e interruzioni di colonna, allineare e disporre gli oggetti (tabelle, immagini, grafici, forme), cambiare lo stile di disposizione testo, aggiungere una filigrana."
    },
   {
        "id": "ProgramInterface/PluginsTab.htm", 
        "title": "Scheda Plugin", 
        "body": "La scheda Plugin consente di accedere a funzionalità di modifica avanzate utilizzando i componenti di terze parti disponibili. Qui puoi anche usare le macro per semplificare le operazioni di routine. Finestra dell’Editor di Documenti Online: Finestra dell’Editor di Documenti Desktop: Il pulsante Impostazioni consente di aprire la finestra in cui è possibile visualizzare e gestire tutti i plugin installati e aggiungerne di propri. Il pulsante Macro consente di aprire la finestra in cui è possibile creare le proprie macro ed eseguirle. Per saperne di più sulle macro puoi fare riferimento alla nostra Documentazione API. Attualmente, i seguenti plugin sono disponibili per impostazione predefinita: Send consente d’inviare il documento via e-mail utilizzando il cliente di posta desktop predefinito (disponibile solo nella versione desktop), Highlight code consente di evidenziare la sintassi del codice selezionando la lingua, lo stile, il colore di sfondo necessari, OCR consente di riconoscere il testo incluso in un'immagine e d’inserirlo nel testo del documento, PhotoEditor consente di modificare le immagini: ritagliare, capovolgere, ruotare, disegnare linee e forme, aggiungere icone e testo, caricare una maschera e applicare filtri come Scala di grigi, Inverti, Seppia, Sfocatura, Precisione, Rilievo, ecc., Speech consente di convertire il testo selezionato in voce (disponibile solo nella versione online), Thesaurus consente di cercare sinonimi e contrari di una parola e sostituirla con quella selezionata, Translator consente di tradurre il testo selezionato in altre lingue, YouTube consente d’incorporare video di YouTube nel tuo documento. I plugin Wordpress ed EasyBib possono essere utilizzati se si collegano i servizi corrispondenti nelle impostazioni del portale. È possibile utilizzare le seguenti istruzioni per la versione server o per la versione SaaS. Per saperne di più sui plugin puoi fare riferimento alla nostra Documentazione API. Tutti gli esempi di plugin open source attualmente esistenti sono disponibili su GitHub."
    },
   {
        "id": "ProgramInterface/ProgramInterface.htm", 
        "title": "Presentazione dell'interfaccia utente dell'Editor di Documenti", 
        "body": "L’editor di Documenti utilizza un'interfaccia a schede in cui i comandi di modifica sono raggruppati in schede in base alla funzionalità. Finestra dell’Editor di Documenti Online: Finestra dell’Editor di Documenti Desktop: L'interfaccia dell'editor è composta dai seguenti elementi principali: L’intestazione dell’Editor mostra il logo, le schede dei documenti aperti, il nome del documento e le schede dei menu. Nella parte sinistra dell’intestazione dell’Editor ci sono i pulsanti Salva, Stampa file, Annulla e Ripristina. Nella parte destra dell'intestazione dell'Editor vengono visualizzati il nome utente e le seguenti icone: Apri percorso file - nella versione desktop, consente di aprire la cartella in cui è archiviato il file nella finestra Espola file. Nella versione online, consente di aprire la cartella del modulo Documenti in cui è archiviato il file in una nuova scheda del browser. - consente di regolare le Impostazioni di visualizzazione e accedere all'editor Impostazioni avanzate. Gestisci i diritti di accesso al documento - (disponibile solo nella versione online) consente d’impostare i diritti di accesso per i documenti archiviati nel cloud. La barra degli strumenti superiore visualizza una serie di comandi di modifica in base alla scheda del menu selezionata. Attualmente sono disponibili le seguenti schede: File, Home, Inserisci, Layout di Pagina, Referimenti, Collaborazione, Protezione, Plugins. Le opzioni Copia e Incolla sono sempre disponibili nella parte sinistra della barra degli strumenti superiore, indipendentemente dalla scheda selezionata. La barra di stato nella parte inferiore della finestra dell'editor contiene l'indicatore del numero di pagina, visualizza alcune notifiche (come \"Tutte le modifiche salvate\" ecc.), consente d’impostare la lingua del testo, abilitare il controllo ortografico, attivare la modalità traccia cambiamenti, regolare lo zoom. La barra laterale sinistra contiene le seguenti icone: - consente di usare lo strumento Trova e sostituisci, - consente di aprire il pannello dei Commenti, - consente di accedere al pannello di Navigazione e gestire le intestazioni, - (disponibile solo nella versione online) consente di aprire il pannello Chat, - (disponibile solo nella versione online) consente di contattare il nostro team di supporto, - (disponibile solo nella versione online) consente di visualizzare le informazioni sul programma. La barra laterale destra consente di regolare parametri aggiuntivi di oggetti diversi. Quando selezioni un oggetto particolare nel testo, l'icona corrispondente viene attivata nella barra laterale destra. Fare clic su questa icona per espandere la barra laterale destra. I righelli orizzontali e verticali consentono di allineare testo e altri elementi in un documento, impostare margini, tabulazioni e rientri di paragrafo. L'area di lavoro consente di visualizzare il contenuto del documento, inserire e modificare i dati. La barra di scorrimento a destra consente di scorrere su e giù i documenti di più pagine. Per comodità, è possibile nascondere alcuni componenti e visualizzarli di nuovo quando è necessario. Per ulteriori informazioni su come regolare le impostazioni di visualizzazione, fare riferimento a questa pagina."
    },
   {
        "id": "ProgramInterface/ReferencesTab.htm", 
        "title": "Scheda Riferimenti", 
        "body": "La scheda Riferimenti consente di gestire diversi tipi di riferimenti: aggiungere e aggiornare un sommario, creare e modificare note a piè di pagina, inserire collegamenti ipertestuali. Finestra dell’Editor di Documenti Online: Finestra dell’Editor di Documenti Desktop: Usando questa scheda, puoi: creare e aggiornare automaticamente un sommario, inserire note a piè di pagina, inserire collegamenti ipertestuali, aggiungere segnalibri. aggiungere didascalie."
    },
   {
        "id": "ProgramInterface/ReviewTab.htm", 
        "title": "Scheda Collaborazione", 
        "body": "La scheda Collaborazione consente di organizzare il lavoro collaborativo sul documento. Nella versione online è possibile condividere il file, selezionare una modalità di co-editing, gestire i commenti, tenere traccia delle modifiche apportate da un revisore, visualizzare tutte le versioni e le revisioni. Nella modalità di commento, è possibile aggiungere e rimuovere commenti, navigare tra le modifiche rilevate, utilizzare la chat e visualizzare la cronologia delle versioni. Nella versione desktop è possibile gestire i commenti e utilizzare la funzione Traccia cambiamenti. . Finestra dell’Editor di Documenti Online: Finestra dell’Editor di Documenti Desktop: Usando questa scheda, puoi: specificare le impostazioni di condivisione (disponibile solo nella versione online), passare tra le modalità di co-editing Rigorosa e Rapida (disponibile solo nella versione online), aggiungere o rimuovere commenti al documento, abilitare la funzione Traccia cambiamenti, scegliere la Modalità Visualizzazione Modifiche, gestire le modifiche suggerite, caricare un documento per il confronto (disponibile solo nella versione online), aprire il pannello Chat (disponibile solo nella versione online), tracciare la Cronologia delle versioni (disponibile solo nella versione online)."
    },
   {
        "id": "UsageInstructions/AddBorders.htm", 
        "title": "Aggiungere bordi", 
        "body": "Per aggiungere bordi a un paragrafo, una pagina o l'intero documento, posizionare il cursore all'interno del paragrafo che interessa o selezionare diversi paragrafi con il mouse o tutto il testo nel documento premendo la combinazione di tasti Ctrl+A, fare clic con il pulsante destro del mouse e selezionare l'opzione Impostazioni avanzate del paragrafo dal menu o utilizzare il link Mostra impostazioni avanzate nella barra laterale destra, passare alla scheda Bordi e riempimento nella finestra Paragrafo - Impostazioni avanzate aperta, impostare il valore necessario per Dimensione bordo e selezionare un Colore bordo, fare clic all'interno del diagramma disponibile o utilizzare i pulsanti per selezionare i bordi e applicarvi lo stile scelto, fare clic sul pulsante OK. Dopo aver aggiunto i bordi, è anche possibile impostare le spaziature interne , ovvero le distanze tra i bordi destro, sinistro, superiore e inferiore e il testo del paragrafo al loro interno. Per impostare i valori necessari, passare alla scheda Spaziatura interna della finestra Paragrafo - Impostazioni avanzate:"
    },
   {
        "id": "UsageInstructions/AddCaption.htm", 
        "title": "Aggiungere una didascalia", 
        "body": "La Didascalia è un’etichetta numerata che puoi applicare ad oggetti, come equazioni, tabelle, figure e immagini all'interno dei tuoi documenti. Ciò semplifica il riferimento all'interno del testo in quanto è presente un'etichetta facilmente riconoscibile sull'oggetto. Per aggiungere la didascalia ad un oggetto: seleziona l'oggetto a cui applicare una didascalia; passa alla scheda Riferimenti nella barra degli strumenti in alto; fai clic sull’icona Didascalia nella barra degli strumenti in alto o fai clic con il pulsante destro sull’oggetto e seleziona l’opzione Inserisci didascalia per aprire la finestra di dialogo Inserisci didascalia scegli l'etichetta da utilizzare per la didascalia facendo clic sul menù a discesa Etichetta e selezionando l'oggetto; o crea una nuova etichetta facendo clic sul pulsante Aggiungi per aprire la finestra di dialogo Etichetta Inserisci un nome per l’etichetta nella casella di testo, quindi fai clic sul pulsante OK per aggiungere una nova etichetta all’elenco etichette; seleziona la casella di controllo Includi il numero del capitolo per modificare la numerazione della didascalia; nel menu a discesa Inserisci, seleziona Prima per posizionare l’etichetta sopra l’oggetto o Dopo per posizionarla sotto l’oggetto; seleziona la casella di controllo Escudere l’etichetta dalla didascalia per lasciare solo un numero per questa particolare didascalia in conformità con un numero progressivo; puoi quindi scegliere come numerare la didascalia assegnando uno stile specifico alla didascalia e aggiungendo un separatore; per applicare la didascalia fare clic sul pulsante OK. Eliminare un’etichetta Per eliminare un’etichetta creata, seleziona l’etichetta dall’elenco Etichetta nella finestra di dialogo Inserisci didascalia, quindi fai clic sul pulsante Elimina. L'etichetta creata verrà immediatamente eliminata. Nota: è possibile eliminare le etichette create ma non è possibile eliminare le etichette predefinite. Formattazione delle didascalie Non appena aggiungi una didascalia, un nuovo stile per le didascalie viene automaticamente aggiunto alla sezione stili. Per modificare lo stile di tutte le didascalie in tutto il documento, è necessario seguire questi passaggi: seleziona il testo da cui verrà copiato un nuovo stile di Didascalia; cerca lo stile Didascalia (evidenziato in blu per impostazione predefinita) nella galleria degli stili che puoi trovare nella scheda Home nella barra degli struemnti in alto; fai clic con il tatso destro e scegli l’opzione Aggiorna da selezione. Raggruppare le didascalie Se si desidera poter spostare l'oggetto e la didascalia come un'unica unità, è necessario raggruppare l’oggetto e la didascalia. seleziona l’oggetto; seleziona uno degli Stili di disposizione testo usando la barra laterale destra; aggiungi la didascalia come menzionato sopra; tieni premuto il tasto Shift e seleziona gli elementi che desideri raggruppare; fai clic con il tatso destro su uno degli elementi e seleziona Disponi > Ragruppa. Ora entrambi gli elementi si sposteranno simultaneamente se li trascini da qualche altra parte nel documento. Per separare gli oggetti fai clic rispettivamente su Disponi > Separa."
    },
   {
        "id": "UsageInstructions/AddFormulasInTables.htm", 
        "title": "Usare formule nelle tabelle", 
        "body": "Inserire una formula È possibile eseguire semplici calcoli sui dati nelle celle della tabella aggiungendo formule. Per inserire una formula in una cella di una tabella, posizionare il cursore all'interno della cella in cui si desidera visualizzare il risultato, fare clic sul pulsante Aggiungi formula nella barra laterale destra, nella finestra Impostazioni formula visualizzata immettere la formula necessaria nel campo Formula. È possibile immettere manualmente una formula necessaria utilizzando gli operatori matematici comuni, (+, -, *, /), ad esempio =A1*B2 o utilizzare l'elenco a discesa Incolla funzione per selezionare una delle funzioni incorporate, ad esempio =PRODUCT(A1,B2). specificare manualmente gli argomenti necessari tra parentesi nel campo Formula. Se la funzione richiede diversi argomenti, devono essere separati da virgole. utilizzare l'elenco a discesa Formato numero se si desidera visualizzare il risultato in un determinato formato numerico, fare clic su OK. Il risultato verrà visualizzato nella cella selezionata. Per modificare la formula aggiunta, seleziona il risultato nella cella e fai clic sul pulsante Aggiungi formula nella barra laterale destra, apporta le modifiche necessarie nella finestra Impostazioni formula e fai clic su OK. Aggiungere riferimenti alle celle È possibile utilizzare i seguenti argomenti per aggiungere rapidamente riferimenti agli intervalli di celle: SOPRA - un riferimento a tutte le celle nella colonna sopra la cella selezionata SINISTRA - un riferimento a tutte le celle nella riga a sinistra della cella selezionata SOTTO - un riferimento a tutte le celle nella colonna sotto la cella selezionata DESTRA - un riferimento a tutte le celle nella riga a destra della cella selezionata Questi argomenti possono essere utilizzati con le funzioni AVERAGE, COUNT, MAX, MIN, PRODUCT, SUM. È inoltre possibile immettere manualmente i riferimenti a una determinata cella (ad esempio, A1) o a un intervallo di celle (ad esempio, A1:B3). Utilizzare i segnalibri Se sono stati aggiunti alcuni segnalibri a determinate celle all'interno della tabella, è possibile utilizzare questi segnalibri come argomenti quando si immettono formule. Nella finestra Impostazioni formula, posizionare il cursore tra parentesi nel campo di immissione Formula in cui si desidera aggiungere l'argomento e utilizzare l'elenco a discesa Incolla segnalibro per selezionare uno dei segnalibri aggiunti in precedenza. Aggiornare i risultati delle formule Se vengono modificati alcuni valori nelle celle della tabella, sarà necessario aggiornare manualmente i risultati delle formule: Per aggiornare un singolo risultato della formula, selezionare il risultato necessario e premere F9 o fare clic con il pulsante destro del mouse sul risultato e utilizzare l'opzione Aggiorna campo dal menu. Per aggiornare diversi risultati di formule, seleziona le celle necessarie o l'intera tabella e premi F9. Funzioni incorporate È possibile utilizzare le seguenti funzioni matematiche, statistiche e logiche standard: Categoria Funzione Descrizione Esempio Matematica ABS(x) La funzione viene utilizzata per restituire il valore assoluto di un numero. =ABS(-10) Restituisce 10 Logica AND(Logica1, Logica2, ...) La funzione viene utilizzata per verificare se il valore logico immesso è VERO o FALSO. La funzione restituisce 1 (VERO) se tutti gli argomenti sono VERI. =AND(1&gt;0,1&gt;3) Restituisce 0 Statistica AVERAGE(argument-list) La funzione viene utilizzata per analizzare l'intervallo di dati e trovare il valore medio. =AVERAGE(4,10) Restituisce 7 Statistica COUNT(argument-list) La funzione viene utilizzata per contare il numero delle celle selezionate che contengono numeri ignorando le celle vuote o quelle che contengono testo. =COUNT(A1:B3) Restituisce 6 Logica DEFINED() La funzione valuta se è definito un valore nella cella. La funzione restituisce 1 se il valore è definito e calcolato senza errori e restituisce 0 se il valore non è definito o calcolato con un errore. =DEFINED(A1) Logica FALSE() La funzione restituisce 0 (FALSO) e non richiede alcun argomento. =FALSE Restituisce 0 Matematica INT(x) La funzione viene utilizzata per analizzare e restituire la parte intera del numero specificato. =INT(2.5) Restituisce 2 Statistica MAX(number1, number2, ...) La funzione viene utilizzata per analizzare l'intervallo di dati e trovare il numero più grande. =MAX(15,18,6) Restituisce 18 Statistica MIN(number1, number2, ...) La funzione viene utilizzata per analizzare l'intervallo di dati e trovare il numero più piccolo. =MIN(15,18,6) Restituisce 6 Matematica MOD(x, y) La funzione viene utilizzata per restituire il resto dopo la divisione di un numero per il divisore specificato. =MOD(6,3) Restituisce 0 Logica NOT(Logical) La funzione viene utilizzata per verificare se il valore logico immesso è VERO o FALSO. La funzione restituisce 1 (VERO) se l'argomento è FALSO e 0 (FALSO) se l'argomento è VERO. =NOT(2&lt;5) Restituisce 0 Logica OR(Logical1, Logical2, ...) La funzione viene utilizzata per verificare se il valore logico immesso è VERO o FALSO. La funzione restituisce 0 (FALSE) se tutti gli argomenti sono FALSI. =OR(1&gt;0,1&gt;3) Restituisce 1 Matematica PRODUCT(argument-list) La funzione viene utilizzata per moltiplicare tutti i numeri nell'intervallo di celle selezionato e restituire il prodotto. =PRODUCT(2,5) Restituisce 10 Matematica ROUND(x, num_digits) La funzione viene utilizzata per arrotondare il numero al numero di cifre desiderato. =ROUND(2.25,1) Restituisce 2.3 Matematica SIGN(x) La funzione viene utilizzata per restituire il segno di un numero. Se il numero è positivo, la funzione restituisce 1. Se il numero è negativo, la funzione restituisce -1. Se il numero è 0, la funzione restituisce 0. =SIGN(-12) Restituisce -1 Matematica SUM(argument-list) La funzione viene utilizzata per sommare tutti i numeri nell'intervallo di celle selezionato e restituire il risultato. =SUM(5,3,2) Restituisce 10 Logica TRUE() La funzione restituisce 1 (VERO) e non richiede alcun argomento. =TRUE Restituisce 1"
    },
   {
        "id": "UsageInstructions/AddHyperlinks.htm", 
        "title": "Aggiungere collegamenti ipertestuali", 
        "body": "Per aggiungere un collegamento ipertestuale, posizionare il cursore nella posizione in cui verrà aggiunto un collegamento ipertestuale, passare alla scheda Inserisci o Riferimenti della barra degli strumenti superiore, fare clic sull'icona Collegamento ipertestuale nella barra degli strumenti superiore, dopo di che apparirà la finestra Impostazioni collegamento ipertestuale in cui è possibile specificare i parametri del collegamento ipertestuale: Selezionare il tipo di link che si desidera inserire: Utilizzare l'opzione Collegamento esterno e immettere un URL nel formato http://www.example.com nel campo sottostante Collega a se è necessario aggiungere un collegamento ipertestuale che conduce a un sito Web esterno. Utilizzare l'opzione Inserisci nel documento e selezionare una delle intestazioni esistenti nel testo del documento o uno dei segnalibri aggiunti in precedenza se è necessario aggiungere un collegamento ipertestuale che porta a una determinata posizione nello stesso documento. Visualizza - inserire un testo che sarà cliccabile e porterà all'indirizzo specificato nel campo superiore. Testo del Seggerimento - enter a text that will become visible in a small pop-up window that provides a brief note or label pertaining to the hyperlink being pointed to. Fare clic sul pulsante OK. Per aggiungere un collegamento ipertestuale, è anche possibile utilizzare la combinazione di tasti Ctrl+K o fare clic con il pulsante destro del mouse nella posizione in cui verrà aggiunto un collegamento ipertestuale e selezionare l'opzione Collegamento ipertestuale nel menu di scelta rapida. Nota: è anche possibile selezionare un carattere, una parola, una combinazione di parole, un passaggio di testo con il mouse o utilizzando la tastiera e quindi aprire la finestra Impostazioni collegamento ipertestuale come descritto sopra.  In questo caso, il campo Visualizza verrà compilato con il frammento di testo selezionato. Passando il cursore sul collegamento ipertestuale aggiunto, verrà visualizzato il suggerimento contenente il testo specificato. È possibile seguire il collegamento premendo il tasto CTRL e facendo clic sul collegamento nel documento. Per modificare o eliminare il collegamento ipertestuale aggiunto, fare clic con il pulsante destro del mouse, selezionare l'opzione Collegamento ipertestuale e quindi l'azione che si desidera eseguire - Modifica collegamento ipertestuale o Elimina collegamento ipertestuale."
    },
   {
        "id": "UsageInstructions/AddWatermark.htm", 
        "title": "Aggiungere una filigrana", 
        "body": "Una filigrana è un testo o un’immagine inserita sotto il livello del testo principale. Le filigrane di testo permettono d’indicare lo stato del tuo documento (per esempio, riservato, bozza etc.), le filigrane d’immagine permetto di aggiungere un’immagine, ad esempio il logo delle tua azienda. Per aggiungere una filigrana all’interno di un documento: Passa alla scheda Layout di Pagina nella barra degli strumenti in alto. Fai clic sull’icona Filigrana nella barra degli strumenti in alto e scegli l’opzione Filigrana personalizzata dal menù. Successivamente verrà visualizzata la finestra Impostazioni Filigrana. Seleziona un tipo di filigrana che desideri inserire: Utilizza l’opzione Testo filigrana e rogola i parametri disponibili: Lingua - selezionare una delle lingue disponibili dalla lista, Testo - selezionare uno degli esempi di testo disponibili nella lingua selezionata. Per l'inglese sono disponibili i seguenti testi di filigrana: ASAP, CONFIDENTIAL, COPY, DO NOT COPY, DRAFT, ORIGINAL, PERSONAL, SAMPLE, TOP SECRET, URGENT. Carattere - seleziona il nome e la dimensione del carattere dagli elenchi a discesa corrispondenti. Utilizzare le icone sulla destra per impostare il colore del carattere o applicare uno degli stili di decorazione del carattere: Grassetto, Corsivo, Sotttolineato, Barrato, Semitrasparente - seleziona questa casella se desideri applicare la trasparenza, Layout - seleziona l’opzione Diagonale od Orizzonatale. Utilizza l’opzione Immagine filigrana e regola i parametri disponibili: Scegli l'origine del file immagine utilizzando uno dei pulsanti: Da file o Da URL - l'immagine verrà visualizzata nella finestra di anteprima a destra, Ridimensiona - seleziona il valore di scala necessario tra quelli disponibili: Auto, 500%, 200%, 150%, 100%, 50%. Fai clic sul pulsante OK. Per modificare la filigrana aggiunta, apri la finestra Impostazioni Filigrana come descritto sopra, modifica i parametri necessari e fai clic su OK. Per eleminare la filigrana aggiunta, fai clic sull’icona Filigrana nella scheda Layout di Pagina della barra degli strumenti in alto e scegli l’opzione Rimuovi filigrana dal menù. È anche possibile utilizzare l'opzione Nessuno nella finestra Impostazioni Filigrana."
    },
   {
        "id": "UsageInstructions/AlignArrangeObjects.htm", 
        "title": "Align and arrange objects on a page", 
        "body": "The added autoshapes, images, charts or text boxes can be aligned, grouped and ordered on a page. To perform any of these actions, first select a separate object or several objects on the page. To select several objects, hold down the Ctrl key and left-click the necessary objects. To select a text box, click on its border, not the text within it. After that you can use either the icons at the Layout tab of the top toolbar described below or the analogous options from the right-click menu. Align objects To align two or more selected objects, Click the Align icon at the Layout tab of the top toolbar and select one of the following options: Align to Page to align objects relative to the edges of the page, Align to Margin to align objects relative to the page margins, Align Selected Objects (this option is selected by default) to align objects relative to each other, Click the Align icon once again and select the necessary alignment type from the list: Align Left - to line up the objects horizontally by the left edge of the leftmost object/left edge of the page/left page margin, Align Center - to line up the objects horizontally by their centers/center of the page/center of the space between the left and right page margins, Align Right - to line up the objects horizontally by the right edge of the rightmost object/right edge of the page/right page margin, Align Top - to line up the objects vertically by the top edge of the topmost object/top edge of the page/top page margin, Align Middle - to line up the objects vertically by their middles/middle of the page/middle of the space between the top and bottom page margins, Align Bottom - to line up the objects vertically by the bottom edge of the bottommost object/bottom edge of the page/bottom page margin. Alternatively, you can right-click the selected objects, choose the Align option from the contextual menu and then use one of the available alignment options. If you want to align a single object, it can be aligned relative to the edges of the page or to the page margins. The Align to Margin option is selected by default in this case. Distribute objects To distribute three or more selected objects horizontally or vertically so that the equal distance appears between them, Click the Align icon at the Layout tab of the top toolbar and select one of the following options: Align to Page to distribute objects between the edges of the page, Align to Margin to distribute objects between the page margins, Align Selected Objects (this option is selected by default) to distribute objects between two outermost selected objects, Click the Align icon once again and select the necessary distribution type from the list: Distribute Horizontally - to distribute objects evenly between the leftmost and rightmost selected objects/left and right edges of the page/left and right page margins. Distribute Vertically - to distribute objects evenly between the topmost and bottommost selected objects/top and bottom edges of the page/top and bottom page margins. Alternatively, you can right-click the selected objects, choose the Align option from the contextual menu and then use one of the available distribution options. Note: the distribution options are disabled if you select less than three objects. Group objects To group two or more selected objects or ungroup them, click the arrow next to the Group icon at the Layout tab of the top toolbar and select the necessary option from the list: Group - to join several objects into a group so that they can be simultaneously rotated, moved, resized, aligned, arranged, copied, pasted, formatted like a single object. Ungroup - to ungroup the selected group of the previously joined objects. Alternatively, you can right-click the selected objects, choose the Arrange option from the contextual menu and then use the Group or Ungroup option. Note: the Group option is disabled if you select less than two objects. The Ungroup option is available only when a group of the previously joined objects is selected. Arrange objects To arrange objects (i.e. to change their order when several objects overlap each other), you can use the Bring Forward and Send Backward icons at the Layout tab of the top toolbar and select the necessary arrangement type from the list. To move the selected object(s) forward, click the arrow next to the Bring Forward icon at the Layout tab of the top toolbar and select the necessary arrangement type from the list: Bring To Foreground - to move the object(s) in front of all other objects, Bring Forward - to move the selected object(s) by one level forward as related to other objects. To move the selected object(s) backward, click the arrow next to the Send Backward icon at the Layout tab of the top toolbar and select the necessary arrangement type from the list: Send To Background - to move the object(s) behind all other objects, Send Backward - to move the selected object(s) by one level backward as related to other objects. Alternatively, you can right-click the selected object(s), choose the Arrange option from the contextual menu and then use one of the available arrangement options."
    },
   {
        "id": "UsageInstructions/AlignText.htm", 
        "title": "Allineare il testo in un paragrafo", 
        "body": "Il testo è comunemente allineato in quattro modi: sinistra, destra, centro o giustificato. Per farlo posizionare il cursore nella posizione in cui si desidera applicare l'allineamento (può trattarsi di una nuova riga o di testo già immesso), passare alla scheda Home della barra degli strumenti superiore, selezionare il tipo di allineamento che si desidera applicare: L’allineamento a sinistra con il testo allineato dal lato sinistro della pagina (il lato destro rimane non allineato) viene eseguito con l'icona Allinea a sinistra situata nella barra degli strumenti superiore. L'allineamento al centro con il testo allineato al centro della pagina (il lato destro e il lato sinistro rimane non allineato) viene eseguito con l'icona Allinea al centro situata nella barra degli strumenti superiore. L'allineamento a destra con il testo allineato dal lato destro della pagina (il lato sinistro rimane non allineato) viene eseguito con l'icona Allinea a destra situata nella barra degli strumenti superiore. L'allineamento giustificato con il testo allineato sia dal lato sinistro che da quello destro della pagina (la spaziatura aggiuntiva viene aggiunta se necessario per mantenere l'allineamento) viene eseguita con l'icona Giustificato situata nella barra degli strumenti superiore. I parametri di allineamento sono disponibili anche nella finestra Paragrafo - Impostazioni avanzate. fare clic con il pulsante destro del mouse sul testo e scegliere l'opzione Impostazioni avanzate del paragrafo dal menu contestuale o utilizzare l'opzione Mostra impostazioni avanzate nella barra laterale destra, aprire la finestra Paragrafo - Impostazioni avanzate, passare alla scheda Rientri e spaziatura selezionare uno dei tipi di allineamento dall'elenco Allineamento: A sinistra, Al centro, A destra, Giustificato, fare clic sul pulsante OK per applicare le modifiche."
    },
   {
        "id": "UsageInstructions/BackgroundColor.htm", 
        "title": "Selezionare il colore di sfondo per un paragrafo", 
        "body": "Il colore di sfondo viene applicato all'intero paragrafo e riempie completamente tutto lo spazio del paragrafo dal margine sinistro della pagina al margine destro della pagina. Per applicare un colore di sfondo a un determinato paragrafo o modificare quello corrente, selezionare una combinazione di colori per il documento da quelle disponibili facendo clic sull'icona Cambia combinazione colori nella scheda Home della barra degli strumenti superiore posiziona il cursore all'interno del paragrafo che ti interessa o seleziona diversi paragrafi con il mouse o l'intero testo usando la combinazione di tasti Ctrl+A aprire la finestra delle tavolozze dei colori. È possibile accedervi in uno dei seguenti modi: fare clic sulla freccia verso il basso accanto all'icona nella scheda Home della barra degli strumenti superiore, oppure clicca sul campo del colore accanto alla didascalia Colore sfondo nella barra laterale destra, oppure fare clic sul link \"Mostra impostazioni avanzate\" nella barra laterale destra o selezionare l'opzione \"Impostazioni avanzate del paragrafo\" nel menu di scelta rapida, quindi passa alla scheda 'Bordi e riempimento' nella finestra \"Paragrafo - Impostazioni avanzate\" e fare clic sul campo del colore accanto alla didascalia Colore sfondo. selezionare qualsiasi colore nelle tavolloze disponibili Dopo aver selezionato il colore necessario utilizzando l'icona , sarai in grado di applicare questo colore a qualsiasi paragrafo selezionato semplicemente facendo clic sull'icona (visualizza il colore selezionato), senza la necessità di scegliere nuovamente questo colore sulla tavolozza. Se utilizzi l'opzione Colore sfondo nella barra laterale destra o all'interno della finestra 'Paragrafo - Impostazioni avanzate' window, ricorda che il colore selezionato non viene mantenuto per un accesso rapido. (Queste opzioni possono essere utili se si desidera selezionare un colore di sfondo diverso per un paragrafo specifico, mentre si utilizza anche un colore generale selezionato con l'aiuto dell'icona icon). Per cancellare il colore di sfondo di un determinato paragrafo, posiziona il cursore all'interno del paragrafo che ti interessa o seleziona diversi paragrafi con il mouse o l'intero testo usando la combinazione di tasti Ctrl+A aprire la finestra delle tavolozze dei colori facendo clic sul campo colore accanto alla didascalia Colore sfondo nella barra laterale destra selezionare l'icona ."
    },
   {
        "id": "UsageInstructions/ChangeColorScheme.htm", 
        "title": "Change color scheme", 
        "body": "Le combinazioni di colori vengono applicate all'intero documento. Vengono utilizzati per modificare rapidamente l'aspetto del documento, poiché definiscono la tavolozza Temi Colori per gli elementi del documento font, sfondo, tabelle, forme automatiche, grafici). Se hai applicato alcuni Tema Colori agli elementi del documento e poi hai selezionato una combinazione di colori, diversa, i colori applicati nel documento cambieranno di conseguenza. Per modificare una combinazione di colori, fai clic sulla freccia verso il basso accanto all'icona Cambia combinazione di colori nella scheda Home della barra degli strumenti in alto e seleziona la combinazione di colori necessaria tra quelle disponibili: Office, Scala di grigi, Apice, Aspetto, Civico, Concorso, Equità, Flow, Fonderia, Mediana, Metro, Modulo, Odulent, Oriel, Origine, Carta, Solstizio, Technic, Trek, Urbana, Verve. La combinazione di colori selezionata verrà evidenziata nell'elenco. Dopo aver selezionato la combinazione di colori preferita, è possibile selezionare i colori in una finestra tavolozze colori che corrisponde all'elemento del documento a cui si desidera applicare il colore. Per la maggior parte degli elementi del documento, è possibile accedere alla finestra delle tavolozze dei colori facendo clic sulla casella colorata nella barra laterale destra quando viene selezionato l'elemento necessario. Per il carattere, questa finestra può essere aperta usando la freccia verso il basso accanto all'icona Colore carattere nella scheda Home della barra degli strumenti in alto. Sono disponibili le seguenti tavolozze: Tema Colori - i colori che corrispondono alla combinazione di colori selezionata del documento. Colori Standard - i colori predefiniti impostati. La combinazione di colori selezionata non li influenza. Colori personalizzati - fai clic su questa voce se non è disponibile il colore desiderato nelle tavolozze a disposizione. Seleziona la gamma dei colori desiderata spostando il cursore verticale del colore e poi imposta il colore specifico trascinando il selettore colore all'interno del grande campo quadrato del colore. Dopo aver selezionato un colore con il selettore colori, i valori di colore RGB e sRGB appropriati verranno visualizzati nei campi a destra. È inoltre possibile specificare un colore sulla base del modello di colore RGB inserendo i valori numerici necessari nei campi R, G, B (rosso, verde, blu) o immettendo il codice esadecimale sRGB nel campo contrassegnato dal segno #. Il colore selezionato apparirà in anteprima nella casella Nuova. Se l'oggetto è stato precedentemente riempito con un qualsiasi colore personalizzato, questo colore verrà visualizzato nella casella Corrente , in modo da poter confrontare i colori originali con quelli modificati. Quando hai definito il colore, fai clic sul pulsante Aggiungi: Il colore personalizzato verrà applicato all'elemento selezionato e aggiunto alla tavolozza Colori personalizzati."
    },
   {
        "id": "UsageInstructions/ChangeWrappingStyle.htm", 
        "title": "Change text wrapping", 
        "body": "The Wrapping Style option determines the way the object is positioned relative to the text. You can change the text wrapping style for inserted objects, such as shapes, images, charts, text boxes or tables. Change text wrapping for shapes, images, charts, text boxes To change the currently selected wrapping style: select a separate object on the page left-clicking it. To select a text box, click on its border, not the text within it. open the text wrapping settings: switch to the the Layout tab of the top toolbar and click the arrow next to the Wrapping icon, or right-click the object and select the Wrapping Style option from the contextual menu, or right-click the object, select the Advanced Settings option and switch to the Text Wrapping tab of the object Advanced Settings window. select the necessary wrapping style: Inline - the object is considered to be a part of the text, like a character, so when the text moves, the object moves as well. In this case the positioning options are inaccessible. If one of the following styles is selected, the object can be moved independently of the text and positioned on the page exactly: Square - the text wraps the rectangular box that bounds the object. Tight - the text wraps the actual object edges. Through - the text wraps around the object edges and fills in the open white space within the object. So that the effect can appear, use the Edit Wrap Boundary option from the right-click menu. Top and bottom - the text is only above and below the object. In front - the object overlaps the text. Behind - the text overlaps the object. If you select the Square, Tight, Through, or Top and bottom style, you will be able to set up some additional parameters - Distance from Text at all sides (top, bottom, left, right). To access these parameters, right-click the object, select the Advanced Settings option and switch to the Text Wrapping tab of the object Advanced Settings window. Set the necessary values and click OK. If you select a wrapping style other than Inline, the Position tab is also available in the object Advanced Settings window. To learn more on these parameters, please refer to the corresponding pages with the instructions on how to work with shapes, images or charts. If you select a wrapping style other than Inline, you can also edit the wrap boundary for images or shapes. Right-click the object, select the Wrapping Style option from the contextual menu and click the Edit Wrap Boundary option. Drag wrap points to customize the boundary. To create a new wrap point, click anywhere on the red line and drag it to the necessary position. Change text wrapping for tables For tables, the following two wrapping styles are available: Inline table and Flow table. To change the currently selected wrapping style: right-click the table and select the Table Advanced Settings option, switch to the Text Wrapping tab of the Table - Advanced Settings window, select one of the following options: Inline table is used to select the wrapping style when the text is broken by the table as well as to set the alignment: left, center, right. Flow table is used to select the wrapping style when the text is wrapped around the table. Using the Text Wrapping tab of the Table - Advanced Settings window you can also set up the following additional parameters: For inline tables, you can set the table Alignment type (left, center or right) and Indent from left. For floating tables, you can set the Distance from text and the table position at the Table Position tab."
    },
   {
        "id": "UsageInstructions/CopyClearFormatting.htm", 
        "title": "Copiare/cancellare la formattazione del testo", 
        "body": "Per copiare una determinata formattazione del testo, selezionare il passaggio di testo la cui formattazione è necessario copiare con il mouse o utilizzando la tastiera, fare clic sull'icona Copia stile nella scheda Home della barra degli strumenti superiore (il puntatore del mouse sarà simile al seguente ), selezionare il passaggio di testo a cui si desidera applicare la stessa formattazione. Per applicare la formattazione copiata a più passaggi di testo, selezionare il passaggio di testo la cui formattazione è necessario copiare con il mouse o utilizzando la tastiera, fare doppio clic sull'icona Copia stile nella scheda Home della barra degli strumenti superiore (il puntatore del mouse sarà simile al seguente e l'icona Copia stile rimarrà selezionata: ), selezionare i passaggi di testo necessari uno ad uno per applicare la stessa formattazione a ciascuno di essi, per uscire da questa modalità, fare di nuovo clic sull'icona Copia stile o premere il tasto Esc sulla tastiera. Per rimuovere rapidamente la formattazione applicata dal testo, selezionare il passaggio di testo di cui si desidera rimuovere la formattazione, fare clic sull'icona Cancella stile nella scheda Home della barra degli strumenti superiore."
    },
   {
        "id": "UsageInstructions/CopyPasteUndoRedo.htm", 
        "title": "Copy/paste text passages, undo/redo your actions", 
        "body": "Utilizza le operazioni di base negli Appunti Per tagliare, copiare e incollare passaggi di testo ed oggetti inseriti (forme, immagini, grafici) all'interno del documento corrente, utilizzate le opzioni corrispondenti dal menù del tasto destro del mouse o le icone disponibili in qualsiasi scheda della barra superiore degli strumenti: Taglia – seleziona un frammento di testo o un oggetto ed utilizza l'opzione Taglia dal menu di scelta rapida col tasto destro del mouse per tagliare la selezione ed inviarla alla memoria degli appunti del computer. I dati tagliati potranno essere successivamente inseriti in un'altra posizione nello stesso documento.</span></li> Copia – seleziona un frammento di testo o un oggetto ed utilizza l'opzione Copia dal menu di scelta rapida del tasto destro del mouse o l'icona Copia nella barra superiore degli strumenti per copiare la selezione nella memoria degli appunti del computer. I dati copiati potranno essere successivamente inseriti in un'altra posizione nello stesso documento. Incolla – trova la posizione nel documento in cui è necessario incollare il frammento / l’oggetto di testo precedentemente copiato ed utilizza l'opzione Incolla dal menu di scelta rapida del tasto destro del mouse o l'icona Incolla nella barra superiore degli strumenti. Il testo / l’oggetto verrà inserito nella posizione corrente del cursore. I dati potranno essere precedentemente copiati dallo stesso documento. Nella versione online, le seguenti combinazioni di tasti vengono utilizzate solo per copiare o incollare dati da / in un altro documento o qualche altro programma, nella versione desktop, è possibile utilizzare entrambe le corrispondenti opzioni di pulsanti / menu e combinazioni di tasti per qualsiasi operazione di copia / incolla: Ctrl+X combinazione di tasti per tagliare; Ctrl+C combinazione di tasti per copiare; Ctrl+V combinazione di tasti per incollare. Nota: invece di tagliare ed incollare il testo all'interno dello stesso documento, è sufficiente selezionare il passaggio di testo desiderato e trascinarlo nella posizione voluta. Usa la funzione Incolla speciale Una volta che il testo incollato viene copiato, il pulsante Incolla speciale apparirà accanto al passaggio di testo inserito. Fa’ clic su questo pulsante per selezionare l'opzione Incolla desiderata: Quando si incolla il testo del paragrafo o del testo all'interno delle forme automatiche, sono disponibili le seguenti opzioni: Incolla - consente di incollare il testo copiato mantenendo la formattazione originale. Mantieni solo testo - consente di incollare il testo senza la sua formattazione originale. Se si incolla una tabella copiata in una tabella esistente, si renderanno disponibili le seguenti opzioni: Sovrascrivi celle - consente di sostituire il contenuto della tabella esistente con i dati incollati. Questa opzione è selezionata come impostazione predefinita. Annida Tabella  - consente di incollare la tabella copiata come tabella nidificata nella cella selezionata della tabella esistente.. Mantieni solo testo - consente di incollare il testo senza la sua formattazione originale. Annulla / ripristina le tue azioni Per eseguire le operazioni di annullamento / ripristino , utilizzate le icone corrispondenti nell'intestazione dell'editor o le scorciatoie da tastiera: Annulla – usa l’icona Annulla nella parte sinistra dell'intestazione dell'editor o la combinazione di tasti Ctrl+Z per annullare l'ultima operazione eseguita. Ripristina – usa l’icona Ripristina nella parte sinistra dell'intestazione dell'editor o la combinazione di tasti Ctrl+Y per ripristinare l'ultima operazione annullata Nota: quando si co-modifica un documento in modalità Veloce, la possibilità di ripristinare l'ultima operazione annullata non è disponibile."
    },
   {
        "id": "UsageInstructions/CreateLists.htm", 
        "title": "Creare elenchi", 
        "body": "Per creare un elenco nel documento, posiziona il cursore sulla posizione in cui verrà avviato un elenco (può trattarsi di una nuova riga o del testo già inserito), passare alla scheda Home della barra degli strumenti superiore, selezionare il tipo di elenco che si desidera avviare: Elenco non ordinato con marcatori, viene creato utilizzando l'icona Elenchi puntati situata nella barra degli strumenti superiore Elenco ordinato con cifre o lettere viene creato utilizzando l'icona Elenchi numerati situata nella barra degli strumenti superiore Nota: fare clic sulla freccia verso il basso accanto all'icona Elenchi puntati o Elenchi numerati per selezionare l'aspetto dell'elenco. ora ogni volta che si preme il tasto Invio alla fine della riga apparirà una nuova voce di elenco ordinata o non ordinata. Per evitare questo, premere il tasto Backspace e continuare con il paragrafo di testo comune. Il programma crea automaticamente elenchi numerati quando si immette la cifra 1 con un punto o una parentesi e uno spazio dopo di essa: 1., 1). Gli elenchi puntati possono essere creati automaticamente quando si immettono i caratteri -, * e uno spazio dopo di essi. È inoltre possibile modificare il rientro del testo negli elenchi e nella relativa nidificazione utilizzando le icone Elenco a più livelli , Riduci rientro , Aumenta rientro nella scheda Home della barra degli strumenti superiore. Nota: i parametri di rientro e spaziatura aggiuntivi possono essere modificati nella barra laterale destra e nella finestra delle impostazioni avanzate. Per ulteriori informazioni, leggere la sezione Modificare i rientri di paragrafo e Impostare l’interliane del paragrafo. Unire e separare elenchi Per unire un elenco a quello precedente: fare clic sulla prima voce del secondo elenco con il tasto destro del mouse, utilizzare l'opzione Unisci all'elenco precedente dal menu contestuale. Gli elenchi saranno uniti e la numerazione continuerà in conformità con la prima numerazione. Per separare un elenco: fare clic sulla voce dell'elenco in cui si desidera iniziare un nuovo elenco con il tasto destro del mouse, utilizzare l'opzione Inizia nuovo elenco dal menu contestuale. L'elenco verrà separato e la numerazione nel secondo elenco inizierà di nuovo. Modificare la numerazione Per continuare la numerazione sequenziale nel secondo elenco in base alla numerazione dell'elenco precedente: fare clic sulla prima voce del secondo elenco con il tasto destro del mouse, utilizzare l'opzione Continua la numerazione dal menu contestuale. La numerazione continuerà in conformità con la prima numerazione dell'elenco. Per impostare un determinato valore iniziale di numerazione: fare clic sulla voce dell'elenco in cui si desidera applicare un nuovo valore di numerazione con il pulsante destro del mouse, utilizzare l'opzione Imposta valore di numerazione dal menu contestuale, nella nuova finestra che si apre, impostare il valore numerico necessario e fare clic sul pulsante OK. Modificare le impostazioni dell'elenco Per modificare le impostazioni dell'elenco puntato o numerato, ad esempio un tipo di punto elenco/numero, l'allineamento, le dimensioni e il colore: fare clic su una voce di elenco esistente o selezionare il testo che si desidera formattare come elenco, fare click sull’icona Elenchi puntati o Elenchi numerati inella scheda Home della barra degli strumenti superiore, selezionare l'opzione Impostazioni elenco, si aprirà la finestra Impostazioni elenco. La finestra delle impostazioni dell'elenco puntato è simile alla seguente: La finestra delle impostazioni dell'elenco numerato è simile alla seguente: Per l'elenco puntato, è possibile scegliere un carattere utilizzato come punto elenco, mentre per l'elenco numerato è possibile scegliere il tipo di numerazione. Le opzioni Allineamento, Dimensione e Colore sono le stesse sia per gli elenchi puntati che per quelli numerati. Punto elenco - consente di selezionare il carattere necessario utilizzato per l'elenco puntato. Quando si fa clic sul campo Carattere e simbolo, viene visualizzata la finestra Simbolo che consente di scegliere uno dei caratteri disponibili. Per ulteriori informazioni su come lavorare con i simboli, è possibile fare riferimento a questo articolo. Tipo - consente di selezionare il tipo di numerazione necessaria utilizzata per l'elenco numerato. Sono disponibili le seguenti opzioni: Nessuno, 1, 2, 3,..., a, b, c,..., A, B, C,..., i, ii, iii,..., I, II, III,.... Allineamento - consente di selezionare il tipo di allineamento del punto elenco/numero necessario, utilizzato per allineare i punti elenco/numeri orizzontalmente all'interno dello spazio a loro designato. I tipi di allineamento disponibili sono i seguenti: A sinistra, Al centro, A destra. Dimensione - consente di selezionare la dimensione necessaria del punto elenco/numero. L'opzione Come un testo è selezionata per impostazione predefinita. Quando questa opzione è selezionata, la dimensione del punto elenco o del numero corrisponde alla dimensione del testo. È possibile scegliere una delle dimensioni predefinite da 8 a 96. Colore - permette di selezionare il colore necessario del punto elenco/numero. L'opzione Come un testo è selezionata per impostazione predefinita. Quando questa opzione è selezionata, il colore del punto elenco o del numero corrisponde al colore del testo. È possibile scegliere l'opzione Automatico per applicare il colore automatico oppure selezionare uno dei colori del tema o i colori standard nella tavolozza oppure specificare un colore personalizzato. Tutte le modifiche vengono visualizzate nel campo Anteprima. fare clic su OK per applicare le modifiche e chiudere la finestra delle impostazioni. Per modificare le impostazioni dell'elenco a più livelli, fare clic su una voce dell’elenco, fare clic sull'icona Elenco a più livelli nella scheda Home della barra degli strumenti superiore, selezionare l'opzione Impostazioni elenco, si aprirà la finestra Impostazioni elenco. La finestra delle impostazioni Elenco a più livelli è simile alla seguente: Scegliere il livello desiderato dell'elenco nel campo Livello a sinistra, quindi utilizzare i pulsanti in alto per regolare l'aspetto del punto elenco o del numero per il livello selezionato: Tipo - consente di selezionare il tipo di numerazione necessario utilizzato per l'elenco numerato o il carattere necessario utilizzato per l'elenco puntato. Le seguenti opzioni sono disponibili per l'elenco numerato: Nessuno, 1, 2, 3,..., a, b, c,..., A, B, C,..., i, ii, iii,..., I, II, III,.... Per l'elenco puntato, è possibile scegliere uno dei simboli predefiniti o utilizzare l'opzione Nuovo punto elenco. Quando si fa clic su questa opzione, viene visualizzata la finestra Simbolo che consente di scegliere uno dei caratteri disponibili. Per ulteriori informazioni su come lavorare con i simboli, è possibile fare riferimento a questo articolo. Allineamento - consente di selezionare il tipo di allineamento di punti elenco/numeri necessario, utilizzato per allineare i punti elenco/numeri orizzontalmente all'interno dello spazio a loro designato all'inizio del paragrafo. I tipi di allineamento disponibili sono i seguenti: A sinistra, Al centro, A destra. Dimensione - consente di selezionare la dimensione necessaria del punto elenco/numero. L'opzione Come un testo è selezionata per impostazione predefinita. È possibile scegliere una delle dimensioni predefinite da 8 a 96. Colore - consente di selezionare il colore del punto elenco/numero necessario. L'opzione Come un testo è selezionata per impostazione predefinita. Quando questa opzione è selezionata, il colore del punto elenco o del numero corrisponde al colore del testo. È possibile scegliere l'opzione Automatico per applicare il colore automatico, oppure selezionare uno dei colori del tema o colori standard nella tavolozza o specificare un colore personalizzato. Tutte le modifiche vengono visualizzate nel campo Anteprima. fare clic su OK per applicare le modifiche e chiudere la finestra delle impostazioni."
    },
   {
        "id": "UsageInstructions/CreateTableOfContents.htm", 
        "title": "Create a Table of Contents", 
        "body": "A table of contents contains a list of all chapters (sections etc.) in a document and displays the numbers of the pages where each chapter is started. This allows to easily navigate through a multi-page document quickly switching to the necessary part of the text. The table of contents is generated automatically on the base of the document headings formatted using built-in styles. This makes it easy to update the created table of contents without the necessity to edit headings and change page numbers manually if the document text has been changed. Define the heading structure Format headings First of all, format headings in you document using one of the predefined styles. To do that, Select the text you want to include into the table of contents. Open the style menu on the right side of the Home tab at the top toolbar. Click the style you want to apply. By default, you can use the Heading 1 - Heading 9 styles. Note: if you want to use other styles (e.g. Title, Subtitle etc.) to format headings that will be included into the table of contents, you will need to adjust the table of contents settings first (see the corresponding section below). To learn more about available formatting styles, you can refer to this page. Manage headings Once the headings are formatted, you can click the Navigation icon at the left sidebar to open the panel that displays the list of all headings with corresponding nesting levels. This panel allows to easily navigate between headings in the document text as well as manage the heading structure. Right-click on a heading in the list and use one of the available options from the menu: Promote - to move the currently selected heading up to the higher level in the hierarchical structure, e.g. change it from Heading 2 to Heading 1. Demote - to move the currently selected heading down to the lower level in the hierarchical structure, e.g. change it from Heading 1 to Heading 2. New heading before - to add a new empty heading of the same level before the currently selected one. New heading after - to add a new empty heading of the same level after the currently selected one. New subheading - to add a new empty subheading (i.e. a heading with lower level) after the currently selected heading. When the heading or subheading is added, click on the added empty heading in the list and type in your own text. This can be done both in the document text and on the Navigation panel itself. Select content - to select the text below the current heading in the document (including the text related to all subheadings of this heading). Expand all - to expand all levels of headings at the Navigation panel. Collapse all - to collapse all levels of headings, excepting level 1, at the Navigation panel. Expand to level - to expand the heading structure to the selected level. E.g. if you select level 3, then levels 1, 2 and 3 will be expanded, while level 4 and all lower levels will be collapsed. To manually expand or collapse separate heading levels, use the arrows to the left of the headings. To close the Navigation panel, click the Navigation icon at the left sidebar once again. Insert a Table of Contents into the document To insert a table of contents into your document: Position the insertion point where you want to add the table of contents. Switch to the References tab of the top toolbar. Click the Table of Contents icon at the top toolbar, or click the arrow next to this icon and select the necessary layout option from the menu. You can select the table of contents that displays headings, page numbers and leaders, or headings only. Note: the table of content appearance can be adjusted later via the table of contents settings. The table of contents will be added at the current cursor position. To change the position of the table of contents, you can select the table of contents field (content control) and simply drag it to the desired place. To do that, click the button in the upper left corner of the table of contents field and drag it without releasing the mouse button to another position in the document text. To navigate between headings, press the Ctrl key and click the necessary heading within the table of contents field. You will go to the corresponding page. Adjust the created Table of Contents Refresh the Table of Contents After the table of contents is created, you may continue editing your text by adding new chapters, changing their order, removing some paragraphs, or expanding the text related to a heading so that the page numbers that correspond to the preceding or subsequent section may change. In this case, use the Refresh option to automatically apply all changes to the table of contents. Click the arrow next to the Refresh icon at the References tab of the top toolbar and select the necessary option from the menu: Refresh entire table - to add the headings that you added to the document, remove the ones you deleted from the document, update the edited (renamed) headings as well as update page numbers. Refresh page numbers only - to update page numbers without applying changes to the headings. Alternatively, you can select the table of contents in the document text and click the Refresh icon at the top of the table of contents field to display the above mentioned options. It's also possible to right-click anywhere within the table of contents and use the corresponding options from the contextual menu. Adjust the Table of Contents settings To open the table of contents settings, you can proceed in the following ways: Click the arrow next to the Table of Contents icon at the top toolbar and select the Settings option from the menu. Select the table of contents in the document text, click the arrow next to the table of contents field title and select the Settings option from the menu. Right-click anywhere within the table of contents and use the Table of contents settings option from the contextual menu. A new window will open where you can adjust the following settings: Show page numbers - this option allows to choose if you want to display page numbers or not. Right align page numbers - this option allows to choose if you want to align page numbers by the right side of the page or not. Leader - this option allows to choose the leader type you want to use. A leader is a line of characters (dots or hyphens) that fills the space between a heading and a corresponding page number. It's also possible to select the None option if you do not want to use leaders. Format Table of Contents as links - this option is checked by default. If you uncheck it, you will not be able to switch to the necessary chapter by pressing Ctrl and clicking the corresponding heading. Build table of contents from - this section allows to specify the necessary number of outline levels as well as the default styles that will be used to create the table of contents. Check the necessary radio button: Outline levels - when this option is selected, you will be able to adjust the number of hierarchical levels used in the table of contents. Click the arrows in the Levels field to decrease or increase the number of levels (the values from 1 to 9 are available). E.g., if you select the value of 3, headings that have levels 4 - 9 will not be included into the table of contents. Selected styles - when this option is selected, you can specify additional styles that can be used to build the table of contents and assign a corresponding outline level to each of them. Specify the desired level value in the field to the right of the style. Once you save the settings, you will be able to use this style when creating the table of contents. Styles - this options allows to select the desired appearance of the table of contents. Select the necessary style from the drop-down list. The preview field above displays how the table of contents should look like. The following four default styles are available: Simple, Standard, Modern, Classic. The Current option is used if you customize the table of contents style. Click the OK button within the settings window to apply the changes. Customize the Table of Contents style After you apply one of the default table of contents styles within the Table of Contents settings window, you can additionally modify this style so that the text within the table of contents field looks like you need. Select the text within the table of contents field, e.g. pressing the button in the upper left corner of the table of contents content control. Format table of contents items changing their font type, size, color or applying the font decoration styles. Consequently update styles for items of each level. To update the style, right-click the formatted item, select the Formatting as Style option from the contextual menu and click the Update toc N style option (toc 2 style corresponds to items that have level 2, toc 3 style corresponds to items with level 3 and so on). Refresh the table of contents. Remove the Table of Contents To remove the table of contents from the document: click the arrow next to the Table of Contents icon at the top toolbar and use the Remove table of contents option, or click the arrow next to the table of contents content control title and use the Remove table of contents option."
    },
   {
        "id": "UsageInstructions/DecorationStyles.htm", 
        "title": "Applicare stili di decorazione dei caratteri", 
        "body": "È possibile applicare vari stili di decorazione dei caratteri utilizzando le icone corrispondenti situate nella scheda Home della barra degli strumenti superiore. Nota: nel caso in cui si desidera applicare la formattazione al testo già presente nel documento, selezionarlo con il mouse o utilizzando la tastiera e applicare la formattazione. Grassetto Viene utilizzato per rendere grassetto il carattere dandogli più peso. Corsivo Viene utilizzato per rendere il carattere in corsivo dandogli un po' d’inclinazione sul lato destro. Sottolineato Viene utilizzato per rendere il testo sottolineato con la linea che va sotto le lettere. Barrato Viene utilizzato per rendere il testo barrato con la linea che attraversa le lettere. Apice Viene utilizzato per rendere il testo più piccolo e posizionarlo nella parte superiore della riga di testo, ad esempio come nelle frazioni. Pedice Viene utilizzato per rendere il testo più piccolo e posizionarlo nella parte inferiore della riga di testo, ad esempio come nelle formule chimiche. Per accedere alle impostazioni avanzate del carattere, fai clic con il pulsante destro del mouse e seleziona l'opzione Impostazioni avanzate del paragrafo dal menu o utilizza il collegamento Mostra impostazioni avanzate nella barra laterale destra. Quindi si aprirà la finestra Paragrafo - Impostazioni avanzate in cui è necessario passare alla scheda Carattere. Qui è possibile utilizzare i seguenti stili e impostazioni di decorazione dei caratteri: Barrato viene utilizzato per rendere il testo barrato con la linea che attraversa le lettere. Barrato doppio viene utilizzata per rendere il testo barrato con la doppia linea che attraversa le lettere. Apice viene utilizzato per rendere il testo più piccolo e posizionarlo nella parte superiore della riga di testo, ad esempio come nelle frazioni. Pedice viene utilizzato per rendere il testo più piccolo e posizionarlo nella parte inferiore della riga di testo, ad esempio come nelle formule chimiche. Minuscole viene utilizzato per rendere tutte le lettere minuscole. Maiuscole viene utilizzato per rendere tutte le lettere maiuscole. Spaziatura viene utilizzata per impostare lo spazio tra i caratteri. Aumentare il valore predefinito per applicare la spaziatura Estesa o diminuire il valore predefinito per applicare la spaziatura Condensata. Utilizzare i pulsanti freccia o immettere il valore necessario nella casella. Posizione viene utilizzata per impostare la posizione dei caratteri (offset verticale) nella riga. Aumentare il valore predefinito per spostare i caratteri verso l'alto o diminuire il valore predefinito per spostare i caratteri verso il basso. Utilizzare i pulsanti freccia o immettere il valore necessario nella casella. Tutte le modifiche verranno visualizzate nel campo di anteprima sottostante."
    },
   {
        "id": "UsageInstructions/FontTypeSizeColor.htm", 
        "title": "Impostare il tipo, la dimensione e il colore del carattere", 
        "body": "È possibile selezionare il tipo di carattere, la dimensione e il colore utilizzando le icone corrispondenti situate nella scheda Home della barra degli strumenti superiore. Nota: nel caso in cui si desidera applicare la formattazione al testo già presente nel documento, selezionarlo con il mouse o utilizzando la tastiera e applicare la formattazione. Font Viene utilizzato per selezionare uno dei font dall'elenco di quelli disponibili. Se un font richiesto non è disponibile nell'elenco, puoi scaricarlo e installarlo sul tuo sistema operativo, dopodiché il font sarà disponibile per l'uso nella versione desktop. Dimensione carattere Viene utilizzato per selezionare tra i valori di dimensione del carattere preimpostati dall'elenco a discesa (i valori predefiniti sono: 8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 36, 48, 72 and 96). È anche possibile inserire manualmente un valore personalizzato nel campo della dimensione del carattere e quindi premere Invio. Aumenta dimensione carattere Viene utilizzato per modificare la dimensione del carattere rendendolo più grande di un punto ogni volta che si preme il pulsante. Riduci dimensione carattere Viene utilizzato per modificare la dimensione del carattere rendendolo più piccolo di un punto ogni volta che si preme il pulsante. Colore di evidenziazione Viene utilizzato per contrassegnare frasi, locuzioni, parole o persino caratteri separati aggiungendo una banda colorata che imita l'effetto dell’evidenziatore attorno al testo. È possibile selezionare la parte necessaria del testo e quindi fare clic sulla freccia verso il basso accanto all'icona per selezionare un colore sulla tavolozza (questo set di colori non dipende dalla Combinazione di colori selezionata e include 16 colori) - il colore verrà applicato alla selezione del testo. In alternativa, è possibile scegliere prima un colore di evidenziazione e quindi iniziare a selezionare il testo con il mouse - il puntatore del mouse sarà simile a questo e sarà possibile evidenziare diverse parti del testo in sequenza. Per interrompere l'evidenziazione basta fare di nuovo clic sull'icona. Per cancellare il colore di evidenziazione, scegliere l'opzione Nessun riempimento. Il colore di evidenziazione è diverso dal Colore di sfondo poiché quest'ultimo viene applicato all'intero paragrafo e riempie completamente tutto lo spazio del paragrafo dal margine sinistro della pagina al margine destro della pagina. Colore carattere Viene utilizzato per modificare il colore delle lettere/caratteri nel testo. Per impostazione predefinita, il colore automatico del carattere è impostato in un nuovo documento vuoto. Viene visualizzato come carattere nero sullo sfondo bianco. Se si modifica il colore di sfondo in nero, il colore del carattere si trasformerà automaticamente in bianco per mantenere il testo chiaramente visibile. Per scegliere un colore diverso, fare clic sulla freccia verso il basso accanto all'icona e selezionare un colore tra le tavolozze disponibili (i colori nella tavolozza Colori tema dipendono dalla combinazione di colori). Dopo aver modificato il colore del carattere predefinito, è possibile utilizzare l'opzione Automatico nella finestra delle tavolozze dei colori per ripristinare rapidamente il colore automatico per il passaggio di testo selezionato. Nota: per saperne di più sul lavoro con le tavolozze dei colori, fare riferimento a questa pagina."
    },
   {
        "id": "UsageInstructions/FormattingPresets.htm", 
        "title": "Applicare stili di formattazione", 
        "body": "Ogni stile di formattazione è un insieme di opzioni di formattazione predefinite: (dimensione del carattere, colore, interlinea, allineamento, ecc.). Gli stili consentono di formattare rapidamente diverse parti del documento (intestazioni, sottotitoli, elenchi, testo normale, citazioni) invece di applicare diverse opzioni di formattazione singolarmente ogni volta. Ciò garantisce anche un aspetto coerente in tutto il documento. L'applicazione dello stile dipende dal fatto che uno stile sia uno stile di paragrafo (normale, senza spaziatura, intestazioni, elenco paragrafo ecc.), o dallo stile del testo (in base al tipo di carattere, alla dimensione, al colore), nonché dal fatto che sia selezionato un passaggio di testo o che il cursore del mouse sia posizionato all'interno di una parola. In alcuni casi potrebbe essere necessario selezionare due volte lo stile necessario dalla libreria di stili in modo che possa essere applicato correttamente: quando si fa clic per la prima volta sullo stile nel pannello degli stili, vengono applicate le proprietà dello stile di paragrafo. Quando si fa clic per la seconda volta, vengono applicate le proprietà del testo. Utilizzare gli stili predefiniti Per applicare uno degli stili di formattazione del testo disponibili, posizionare il cursore all'interno del paragrafo che interessa o selezionare diversi paragrafi a cui si desidera applicare uno degli stili di formattazione, selezionare lo stile necessario dalla raccolta stili a destra nella scheda Home della barra degli strumenti superiore. Sono disponibili i seguenti stili di formattazione: normale, senza spaziatura, titolo 1-9, titolo, sottotitolo, citazione, citazione profonda, elenco paragrafo, piè di pagina, intestazione, testo della nota a piè di pagina. Modificare gli stili esistenti e crearne di nuovi Per modificare uno stile esistente: Applicare lo stile necessario a un paragrafo. Selezionare il testo del paragrafo e modificare tutti i parametri di formattazione di cui si ha bisogno. Salvare le modifiche apportate: selezionare l'opzione Formatta come stile e quindi scegliere l'opzione Aggiorna stile 'NomeStile' ('NomeStile' corrisponde allo stile applicato al passaggio 1), oppure selezionare il passaggio di testo modificato con il mouse, fare scorrere la galleria di stili, fare clic con il pulsante destro del mouse sullo stile che si desidera modificare e selezionare l'opzione Aggiorna da selezione. Una volta modificato lo stile, tutti i paragrafi all'interno del documento formattati utilizzando questo stile cambieranno di conseguenza. Per creare uno stile completamente nuovo: Formattare un passaggio di testo di cui si ha bisogno. Selezionare un modo appropriato per salvare lo stile: Fare clic con il pulsante destro del mouse sul testo modificato, selezionare l'opzione Formatta come stile, quindi scegliere l'opzione Crea nuovo stile, oppure seleziona il passaggio di testo modificato con il mouse, fai scorrere la galleria di stili e fai clic sull'opzione Nuovo stile da selezione. Impostare i nuovi parametri dello stile nella finestra Crea nuovo stile che si apre: Specificare il nome del nuovo stile nel campo di immissione testo. Selezionare lo stile desiderato per il paragrafo successivo dall'elenco Stile paragrafo successivo. È anche possibile scegliere l'opzione Come il nuovo stile creato. Fare clic sul pulsante OK. Lo stile creato verrà aggiunto alla galleria degli stili. Gestire gli stili personalizzati: Per ripristinare le impostazioni predefinite di un determinato stile modificato, fare clic con il pulsante destro del mouse sullo stile da ripristinare e selezionare l'opzione Ripristina predefinito. Per ripristinare le impostazioni predefinite di tutti gli stili modificati, fare clic con il pulsante destro del mouse su uno stile predefinito nella raccolta stili e selezionare l'opzione Ripristina tutti gli stili predefiniti. Per eliminare uno dei nuovi stili creati, fare clic con il pulsante destro del mouse sullo stile da eliminare e selezionare l'opzione Elimina stile. Per eliminare tutti i nuovi stili creati, fare clic con il pulsante destro del mouse su qualsiasi nuovo stile creato e selezionare l'opzione Elimina tutti gli stili personalizzati."
    },
   {
        "id": "UsageInstructions/InsertAutoshapes.htm", 
        "title": "Insert autoshapes", 
        "body": "Insert an autoshape To add an autoshape to your document, switch to the Insert tab of the top toolbar, click the Shape icon at the top toolbar, select one of the available autoshape groups: basic shapes, figured arrows, math, charts, stars &amp; ribbons, callouts, buttons, rectangles, lines, click the necessary autoshape within the selected group, place the mouse cursor where you want the shape to be put, once the autoshape is added you can change its size, position and properties. Note: to add a caption within the autoshape make sure the shape is selected on the page and start typing your text. The text you add in this way becomes a part of the autoshape (when you move or rotate the shape, the text moves or rotates with it). It's also possible to add a caption to the autoshape. To learn more on how to work with captions for autoshapes, you can refer to this article. Move and resize autoshapes To change the autoshape size, drag small squares situated on the shape edges. To maintain the original proportions of the selected autoshape while resizing, hold down the Shift key and drag one of the corner icons.</pid> When modifying some shapes, for example figured arrows or callouts, the yellow diamond-shaped icon is also available. It allows you to adjust some aspects of the shape, for example, the length of the head of an arrow. To alter the autoshape position, use the icon that appears after hovering your mouse cursor over the autoshape. Drag the autoshape to the necessary position without releasing the mouse button. When you move the autoshape, guide lines are displayed to help you position the object on the page precisely (if a wrapping style other than inline is selected). To move the autoshape by one-pixel increments, hold down the Ctrl key and use the keybord arrows. To move the autoshape strictly horizontally/vertically and prevent it from moving in a perpendicular direction, hold down the Shift key when dragging. To rotate the autoshape, hover the mouse cursor over the rotation handle and drag it clockwise or counterclockwise. To constrain the rotation angle to 15 degree increments, hold down the Shift key while rotating. Note: the list of keyboard shortcuts that can be used when working with objects is available here. Adjust autoshape settings To align and arrange autoshapes, use the right-click menu. The menu options are: Cut, Copy, Paste - standard options which are used to cut or copy a selected text/object and paste a previously cut/copied text passage or object to the current cursor position. Arrange is used to bring the selected autoshape to foreground, send to background, move forward or backward as well as group or ungroup shapes to perform operations with several of them at once. To learn more on how to arrange objects you can refer to this page. Align is used to align the shape left, center, right, top, middle, bottom. To learn more on how to align objects you can refer to this page. Wrapping Style is used to select a text wrapping style from the available ones - inline, square, tight, through, top and bottom, in front, behind - or edit the wrap boundary. The Edit Wrap Boundary option is available only if you select a wrapping style other than Inline. Drag wrap points to customize the boundary. To create a new wrap point, click anywhere on the red line and drag it to the necessary position. Rotate is used to rotate the shape by 90 degrees clockwise or counterclockwise as well as to flip the shape horizontally or vertically. Shape Advanced Settings is used to open the 'Shape - Advanced Settings' window. Some of the autoshape settings can be altered using the Shape settings tab of the right sidebar. To activate it click the shape and choose the Shape settings icon on the right. Here you can change the following properties: Fill - use this section to select the autoshape fill. You can choose the following options: Color Fill - select this option to specify the solid color you want to fill the inner space of the selected autoshape with. Click the colored box below and select the necessary color from the available color sets or specify any color you like: Gradient Fill - select this option to fill the shape with two colors which smoothly change from one to another. Style - choose one of the available options: Linear (colors change in a straight line i.e. along a horizontal/vertical axis or diagonally at a 45 degree angle) or Radial (colors change in a circular path from the center to the edges). Direction - choose a template from the menu. If the Linear gradient is selected, the following directions are available: top-left to bottom-right, top to bottom, top-right to bottom-left, right to left, bottom-right to top-left, bottom to top, bottom-left to top-right, left to right. If the Radial gradient is selected, only one template is available. Gradient - click on the left slider under the gradient bar to activate the color box which corresponds to the first color. Click on the color box on the right to choose the first color in the palette. Drag the slider to set the gradient stop i.e. the point where one color changes into another. Use the right slider under the gradient bar to specify the second color and set the gradient stop. Picture or Texture - select this option to use an image or a predefined texture as the shape background. If you wish to use an image as a background for the shape, you can add an image From File selecting it on your computer HDD or From URL inserting the appropriate URL address into the opened window. If you wish to use a texture as a background for the shape, open the From Texture menu and select the necessary texture preset. Currently, the following textures are available: canvas, carton, dark fabric, grain, granite, grey paper, knit, leather, brown paper, papyrus, wood. In case the selected Picture has less or more dimensions than the autoshape has, you can choose the Stretch or Tile setting from the dropdown list. The Stretch option allows you to adjust the image size to fit the autoshape size so that it could fill the space completely. The Tile option allows you to display only a part of the bigger image keeping its original dimensions or repeat the smaller image keeping its original dimensions over the autoshape surface so that it could fill the space completely. Note: any selected Texture preset fills the space completely, but you can apply the Stretch effect if necessary. Pattern - select this option to fill the shape with a two-colored design composed of regularly repeated elements. Pattern - select one of the predefined designs from the menu. Foreground color - click this color box to change the color of the pattern elements. Background color - click this color box to change the color of the pattern background. No Fill - select this option if you don't want to use any fill. Opacity - use this section to set an Opacity level dragging the slider or entering the percent value manually. The default value is 100%. It corresponds to the full opacity. The 0% value corresponds to the full transparency. Stroke - use this section to change the autoshape stroke width, color or type. To change the stroke width, select one of the available options from the Size dropdown list. The available options are: 0.5 pt, 1 pt, 1.5 pt, 2.25 pt, 3 pt, 4.5 pt, 6 pt. Alternatively, select the No Line option if you don't want to use any stroke. To change the stroke color, click on the colored box below and select the necessary color. To change the stroke type, select the necessary option from the corresponding dropdown list (a solid line is applied by default, you can change it to one of the available dashed lines). Rotation is used to rotate the shape by 90 degrees clockwise or counterclockwise as well as to flip the shape horizontally or vertically. Click one of the buttons: to rotate the shape by 90 degrees counterclockwise to rotate the shape by 90 degrees clockwise to flip the shape horizontally (left to right) to flip the shape vertically (upside down) Wrapping Style - use this section to select a text wrapping style from the available ones - inline, square, tight, through, top and bottom, in front, behind (for more information see the advanced settings description below). Change Autoshape - use this section to replace the current autoshape with another one selected from the dropdown list. Show shadow - check this option to display shape with shadow. Adjust autoshape advanced settings To change the advanced settings of the autoshape, right-click it and select the Advanced Settings option in the menu or use the Show advanced settings link at the right sidebar. The 'Shape - Advanced Settings' window will open: The Size tab contains the following parameters: Width - use one of these options to change the autoshape width. Absolute - specify an exact value measured in absolute units i.e. Centimeters/Points/Inches (depending on the option specified at the File -> Advanced Settings... tab). Relative - specify a percentage relative to the left margin width, the margin (i.e. the distance between the left and right margins), the page width, or the right margin width. Height - use one of these options to change the autoshape height. Absolute - specify an exact value measured in absolute units i.e. Centimeters/Points/Inches (depending on the option specified at the File -> Advanced Settings... tab). Relative - specify a percentage relative to the margin (i.e. the distance between the top and bottom margins), the bottom margin height, the page height, or the top margin height. If the Lock aspect ratio option is checked, the width and height will be changed together preserving the original shape aspect ratio. The Rotation tab contains the following parameters: Angle - use this option to rotate the shape by an exactly specified angle. Enter the necessary value measured in degrees into the field or adjust it using the arrows on the right. Flipped - check the Horizontally box to flip the shape horizontally (left to right) or check the Vertically box to flip the shape vertically (upside down). The Text Wrapping tab contains the following parameters: Wrapping Style - use this option to change the way the shape is positioned relative to the text: it will either be a part of the text (in case you select the inline style) or bypassed by it from all sides (if you select one of the other styles). Inline - the shape is considered to be a part of the text, like a character, so when the text moves, the shape moves as well. In this case the positioning options are inaccessible. If one of the following styles is selected, the shape can be moved independently of the text and positioned on the page exactly: Square - the text wraps the rectangular box that bounds the shape. Tight - the text wraps the actual shape edges. Through - the text wraps around the shape edges and fills in the open white space within the shape. So that the effect can appear, use the Edit Wrap Boundary option from the right-click menu. Top and bottom - the text is only above and below the shape. In front - the shape overlaps the text. Behind - the text overlaps the shape. If you select the square, tight, through, or top and bottom style you will be able to set up some additional parameters - distance from text at all sides (top, bottom, left, right). The Position tab is available only if you select a wrapping style other than inline. This tab contains the following parameters that vary depending on the selected wrapping style: The Horizontal section allows you to select one of the following three autoshape positioning types: Alignment (left, center, right) relative to character, column, left margin, margin, page or right margin, Absolute Position measured in absolute units i.e. Centimeters/Points/Inches (depending on the option specified at the File -> Advanced Settings... tab) to the right of character, column, left margin, margin, page or right margin, Relative position measured in percent relative to the left margin, margin, page or right margin. The Vertical section allows you to select one of the following three autoshape positioning types: Alignment (top, center, bottom) relative to line, margin, bottom margin, paragraph, page or top margin, Absolute Position measured in absolute units i.e. Centimeters/Points/Inches (depending on the option specified at the File -> Advanced Settings... tab) below line, margin, bottom margin, paragraph, page or top margin, Relative position measured in percent relative to the margin, bottom margin, page or top margin. Move object with text controls whether the autoshape moves as the text to which it is anchored moves. Allow overlap controls whether two autoshapes overlap or not if you drag them near each other on the page. The Weights &amp; Arrows tab contains the following parameters: Line Style - this option group allows to specify the following parameters: Cap Type - this option allows to set the style for the end of the line, therefore it can be applied only to the shapes with the open outline, such as lines, polylines etc.: Flat - the end points will be flat. Round - the end points will be rounded. Square - the end points will be square. Join Type - this option allows to set the style for the intersection of two lines, for example, it can affect a polyline or the corners of the triangle or rectangle outline: Round - the corner will be rounded. Bevel - the corner will be cut off angularly. Miter - the corner will be pointed. It goes well to shapes with sharp angles. Note: the effect will be more noticeable if you use a large outline width. Arrows - this option group is available if a shape from the Lines shape group is selected. It allows to set the arrow Start and End Style and Size by selecting the appropriate option from the dropdown lists. The Text Padding tab allows to change the autoshape Top, Bottom, Left and Right internal margins (i.e. the distance between the text within the shape and the autoshape borders). Note: this tab is only available if text is added within the autoshape, otherwise the tab is disabled. The Alternative Text tab allows to specify a Title and Description which will be read to the people with vision or cognitive impairments to help them better understand what information there is in the shape."
    },
   {
        "id": "UsageInstructions/InsertBookmarks.htm", 
        "title": "Aggiungere segnalibri", 
        "body": "I segnalibri consentono di passare rapidamente a una determinata posizione nel documento corrente o di aggiungere un collegamento a questa posizione all'interno del documento. Per aggiungere un segnalibro all'interno di un documento: specificare il punto in cui si desidera aggiungere il segnalibro: posizionare il cursore del mouse all'inizio del passaggio di testo necessario, o selezionare il passaggio di testo necessario, passare alla scheda Riferimenti della barra degli strumenti superiore, fare clic sull'icona Segnalibro nella barra degli strumenti superiore, nella finestra Segnalibri che si apre, inserire il Nome segnalibro e fare clic sul pulsante Aggiungi - verrà aggiunto un segnalibro all'elenco dei segnalibri visualizzato di seguito, Nota: il nome del segnalibro dovrebbe iniziare preferibilmente con una lettera, ma può anche contenere numeri. Il nome del segnalibro non può contenere spazi, ma può includere il carattere di sottolineatura \"_\". Per passare a uno dei segnalibri aggiunti all'interno del testo del documento: fare click sull’icona Segnalibro nella scheda Riferimenti della barra degli strumenti superiore, nella finestra Segnalibri che si apre, selezionare il segnalibro a cui si desidera passare. Per trovare facilmente il segnalibro necessario nell'elenco è possibile ordinare l'elenco per Nome o per Posizione di un segnalibro all'interno del testo del documento, selezionare l'opzione Segnalibri nascosti per visualizzare i segnalibri nascosti nell'elenco (cioè i segnalibri creati automaticamente dal programma quando si aggiungono riferimenti a una determinata parte del documento. Ad esempio, se si crea un collegamento ipertestuale a un determinato titolo all'interno del documento, l'editor di documenti crea automaticamente un segnalibro nascosto alla destinazione di questo collegamento). clicca sul pulsante Vai a - il cursore sarà posizionato nella posizione all'interno del documento in cui è stato aggiunto il segnalibro selezionato, o verrà selezionato il passaggio di testo corrispondente, fare clic sul pulsante Ottieni collegamento - si aprirà una nuova finestra in cui è possibile premere il pulsante Copia per copiare il collegamento al file che specifica la posizione del segnalibro nel documento. Quando si incolla questo collegamento in una barra degli indirizzi del browser e si preme INVIO, il documento verrà aperto nella posizione in cui è stato aggiunto il segnalibro selezionato. Nota: Se si desidera condividere questo collegamento con altri utenti, è inoltre necessario fornire i diritti di accesso corrispondenti al file per determinati utenti utilizzando l'opzione Condivisione nella scheda Collaborazione. fare clic sul pulsante Chiudi per chiudere la finestra. Per eliminare un segnalibro, selezionarlo nell'elenco dei segnalibri e utilizzare il pulsante Elimina. Per scoprire come utilizzare i segnalibri durante la creazione di collegamenti, fare riferimento alla sezione Aggiungere collegamenti ipertestuali."
    },
   {
        "id": "UsageInstructions/InsertCharts.htm", 
        "title": "Insert charts", 
        "body": "Insert a chart To insert a chart into your document, put the cursor at the place where you want to add a chart, switch to the Insert tab of the top toolbar, click the Chart icon at the top toolbar, select the needed chart type from the available ones - Column, Line, Pie, Bar, Area, XY (Scatter), or Stock, Note: for Column, Line, Pie, or Bar charts, a 3D format is also available. after that the Chart Editor window will appear where you can enter the necessary data into the cells using the following controls: and for copying and pasting the copied data and for undoing and redoing actions for inserting a function and for decreasing and increasing decimal places for changing the number format, i.e. the way the numbers you enter appear in cells change the chart settings clicking the Edit Chart button situated in the Chart Editor window. The Chart - Advanced Settings window will open. The Type &amp Data tab allows you to change the chart type as well as the data you wish to use to create a chart. Select a chart Type you wish to apply: Column, Line, Pie, Bar, Area, XY (Scatter), or Stock. Check the selected Data Range and modify it, if necessary, clicking the Select Data button and entering the desired data range in the following format: Sheet1!A1:B4. Choose the way to arrange the data. You can either select the Data series to be used on the X axis: in rows or in columns. The Layout tab allows you to change the layout of chart elements. Specify the Chart Title position in regard to your chart selecting the necessary option from the drop-down list: None to not display a chart title, Overlay to overlay and center a title on the plot area, No Overlay to display the title above the plot area. Specify the Legend position in regard to your chart selecting the necessary option from the drop-down list: None to not display a legend, Bottom to display the legend and align it to the bottom of the plot area, Top to display the legend and align it to the top of the plot area, Right to display the legend and align it to the right of the plot area, Left to display the legend and align it to the left of the plot area, Left Overlay to overlay and center the legend to the left on the plot area, Right Overlay to overlay and center the legend to the right on the plot area. Specify the Data Labels (i.e. text labels that represent exact values of data points) parameters: specify the Data Labels position relative to the data points selecting the necessary option from the drop-down list. The available options vary depending on the selected chart type. For Column/Bar charts, you can choose the following options: None, Center, Inner Bottom, Inner Top, Outer Top. For Line/XY (Scatter)/Stock charts, you can choose the following options: None, Center, Left, Right, Top, Bottom. For Pie charts, you can choose the following options: None, Center, Fit to Width, Inner Top, Outer Top. For Area charts as well as for 3D Column, Line and Bar charts, you can choose the following options: None, Center. select the data you wish to include into your labels checking the corresponding boxes: Series Name, Category Name, Value, enter a character (comma, semicolon etc.) you wish to use for separating several labels into the Data Labels Separator entry field. Lines - is used to choose a line style for Line/XY (Scatter) charts. You can choose one of the following options: Straight to use straight lines between data points, Smooth to use smooth curves between data points, or None to not display lines. Markers - is used to specify whether the markers should be displayed (if the box is checked) or not (if the box is unchecked) for Line/XY (Scatter) charts. Note: the Lines and Markers options are available for Line charts and XY (Scatter) charts only. The Axis Settings section allows to specify if you wish to display Horizontal/Vertical Axis or not selecting the Show or Hide option from the drop-down list. You can also specify Horizontal/Vertical Axis Title parameters: Specify if you wish to display the Horizontal Axis Title or not selecting the necessary option from the drop-down list: None to not display a horizontal axis title, No Overlay to display the title below the horizontal axis. Specify the Vertical Axis Title orientation selecting the necessary option from the drop-down list: None to not display a vertical axis title, Rotated to display the title from bottom to top to the left of the vertical axis, Horizontal to display the title horizontally to the left of the vertical axis. The Gridlines section allows to specify which of the Horizontal/Vertical Gridlines you wish to display selecting the necessary option from the drop-down list: Major, Minor, or Major and Minor. You can hide the gridlines at all using the None option. Note: the Axis Settings and Gridlines sections will be disabled for Pie charts since charts of this type have no axes and gridlines. Note: the Vertical/Horizontal Axis tabs will be disabled for Pie charts since charts of this type have no axes. The Vertical Axis tab allows you to change the parameters of the vertical axis also referred to as the values axis or y-axis which displays numeric values. Note that the vertical axis will be the category axis which displays text labels for the Bar charts, therefore in this case the Vertical Axis tab options will correspond to the ones described in the next section. For the XY (Scatter) charts, both axes are value axes. The Axis Options section allows to set the following parameters: Minimum Value - is used to specify a lowest value displayed at the vertical axis start. The Auto option is selected by default, in this case the minimum value is calculated automatically depending on the selected data range. You can select the Fixed option from the drop-down list and specify a different value in the entry field on the right. Maximum Value - is used to specify a highest value displayed at the vertical axis end. The Auto option is selected by default, in this case the maximum value is calculated automatically depending on the selected data range. You can select the Fixed option from the drop-down list and specify a different value in the entry field on the right. Axis Crosses - is used to specify a point on the vertical axis where the horizontal axis should cross it. The Auto option is selected by default, in this case the axes intersection point value is calculated automatically depending on the selected data range. You can select the Value option from the drop-down list and specify a different value in the entry field on the right, or set the axes intersection point at the Minimum/Maximum Value on the vertical axis. Display Units - is used to determine a representation of the numeric values along the vertical axis. This option can be useful if you're working with great numbers and wish the values on the axis to be displayed in more compact and readable way (e.g. you can represent 50 000 as 50 by using the Thousands display units). Select desired units from the drop-down list: Hundreds, Thousands, 10 000, 100 000, Millions, 10 000 000, 100 000 000, Billions, Trillions, or choose the None option to return to the default units. Values in reverse order - is used to display values in an opposite direction. When the box is unchecked, the lowest value is at the bottom and the highest value is at the top of the axis. When the box is checked, the values are ordered from top to bottom. The Tick Options section allows to adjust the appearance of tick marks on the vertical scale. Major tick marks are the larger scale divisions which can have labels displaying numeric values. Minor tick marks are the scale subdivisions which are placed between the major tick marks and have no labels. Tick marks also define where gridlines can be displayed, if the corresponding option is set at the Layout tab. The Major/Minor Type drop-down lists contain the following placement options: None to not display major/minor tick marks, Cross to display major/minor tick marks on both sides of the axis, In to display major/minor tick marks inside the axis, Out to display major/minor tick marks outside the axis. The Label Options section allows to adjust the appearance of major tick mark labels which display values. To specify a Label Position in regard to the vertical axis, select the necessary option from the drop-down list: None to not display tick mark labels, Low to display tick mark labels to the left of the plot area, High to display tick mark labels to the right of the plot area, Next to axis to display tick mark labels next to the axis. The Horizontal Axis tab allows you to change the parameters of the horizontal axis also referred to as the categories axis or x-axis which displays text labels. Note that the horizontal axis will be the value axis which displays numeric values for the Bar charts, therefore in this case the Horizontal Axis tab options will correspond to the ones described in the previous section. For the XY (Scatter) charts, both axes are value axes. The Axis Options section allows to set the following parameters: Axis Crosses - is used to specify a point on the horizontal axis where the vertical axis should cross it. The Auto option is selected by default, in this case the axes intersection point value is calculated automatically depending on the selected data range. You can select the Value option from the drop-down list and specify a different value in the entry field on the right, or set the axes intersection point at the Minimum/Maximum Value (that corresponds to the first and last category) on the horizontal axis. Axis Position - is used to specify where the axis text labels should be placed: On Tick Marks or Between Tick Marks. Values in reverse order - is used to display categories in an opposite direction. When the box is unchecked, categories are displayed from left to right. When the box is checked, the categories are ordered from right to left. The Tick Options section allows to adjust the appearance of tick marks on the horizontal scale. Major tick marks are the larger divisions which can have labels displaying category values. Minor tick marks are the smaller divisions which are placed between the major tick marks and have no labels. Tick marks also define where gridlines can be displayed, if the corresponding option is set at the Layout tab. You can adjust the following tick mark parameters: Major/Minor Type - is used to specify the following placement options: None to not display major/minor tick marks, Cross to display major/minor tick marks on both sides of the axis, In to display major/minor tick marks inside the axis, Out to display major/minor tick marks outside the axis. Interval between Marks - is used to specify how many categories should be displayed between two adjacent tick marks. The Label Options section allows to adjust the appearance of labels which display categories. Label Position - is used to specify where the labels should be placed in regard to the horizontal axis. Select the necessary option from the drop-down list: None to not display category labels, Low to display category labels at the bottom of the plot area, High to display category labels at the top of the plot area, Next to axis to display category labels next to the axis. Axis Label Distance - is used to specify how closely the labels should be placed to the axis. You can specify the necessary value in the entry field. The more the value you set, the more the distance between the axis and labels is. Interval between Labels - is used to specify how often the labels should be displayed. The Auto option is selected by default, in this case labels are displayed for every category. You can select the Manual option from the drop-down list and specify the necessary value in the entry field on the right. For example, enter 2 to display labels for every other category etc. The Alternative Text tab allows to specify a Title and Description which will be read to the people with vision or cognitive impairments to help them better understand what information there is in the chart. Move and resize charts Once the chart is added, you can change its size and position. To change the chart size, drag small squares situated on its edges. To maintain the original proportions of the selected chart while resizing, hold down the Shift key and drag one of the corner icons. To alter the chart position, use the icon that appears after hovering your mouse cursor over the chart. Drag the chart to the necessary position without releasing the mouse button. When you move the chart, guide lines are displayed to help you position the object on the page precisely (if a wrapping style other than inline is selected). Note: the list of keyboard shortcuts that can be used when working with objects is available here. Edit chart elements To edit the chart Title, select the default text with the mouse and type in your own one instead. To change the font formatting within text elements, such as the chart title, axes titles, legend entries, data labels etc., select the necessary text element by left-clicking it. Then use icons at the Home tab of the top toolbar to change the font type, size, color or its decoration style. When the chart is selected, the Shape settings icon is also available on the right, since a shape is used as a background for the chart. You can click this icon to open the Shape settings tab at the right sidebar and adjust the shape Fill, Stroke and Wrapping Style. Note that you cannot change the shape type. Using the Shape Settings tab at the right panel you can not only adjust the chart area itself, but also change the chart elements, such as plot area, data series, chart title, legend etc and apply different fill types to them. Select the chart element clicking it with the left mouse button and choose the preferred fill type: solid color, gradient, texture or picture, pattern. Specify the fill parameters and set the Opacity level if necessary. When you select a vertical or horizontal axis or gridlines, the stroke settings are only available at the Shape Settings tab: color, width and type. For more details on how to work with shape colors, fills and stroke, you can refer to this page. Note: the Show shadow option is also available at the Shape settings tab, but it is disabled for chart elements. To delete a chart element, select it by left-clicking and press the Delete key on the keyboard. You can also rotate 3D charts using the mouse. Left-click within the plot area and hold the mouse button. Drag the cursor without releasing the mouse button to change the 3D chart orientation. Adjust chart settings Some of the chart settings can be altered using the Chart settings tab of the right sidebar. To activate it click the chart and choose the Chart settings icon on the right. Here you can change the following properties: Size is used to view the current chart Width and Height. Wrapping Style is used to select a text wrapping style from the available ones - inline, square, tight, through, top and bottom, in front, behind (for more information see the advanced settings description below). Change Chart Type is used to change the selected chart type and/or style. To select the necessary chart Style, use the second drop-down menu in the Change Chart Type section. Edit Data is used to open the 'Chart Editor' window. Note: to quickly open the 'Chart Editor' window you can also double-click the chart in the document. Some of these options you can also find in the right-click menu. The menu options are: Cut, Copy, Paste - standard options which are used to cut or copy a selected text/object and paste a previously cut/copied text passage or object to the current cursor position. Arrange is used to bring the selected chart to foreground, send to background, move forward or backward as well as group or ungroup charts to perform operations with several of them at once. To learn more on how to arrange objects you can refer to this page. Align is used to align the chart left, center, right, top, middle, bottom. To learn more on how to align objects you can refer to this page. Wrapping Style is used to select a text wrapping style from the available ones - inline, square, tight, through, top and bottom, in front, behind. The Edit Wrap Boundary option is unavailable for charts. Edit Data is used to open the 'Chart Editor' window. Chart Advanced Settings is used to open the 'Chart - Advanced Settings' window. To change the chart advanced settings, click the needed chart with the right mouse button and select Chart Advanced Settings from the right-click menu or just click the Show advanced settings link at the right sidebar. The chart properties window will open: The Size tab contains the following parameters: Width and Height - use these options to change the chart width and/or height. If the Constant Proportions button is clicked (in this case it looks like this ), the width and height will be changed together preserving the original chart aspect ratio. The Text Wrapping tab contains the following parameters: Wrapping Style - use this option to change the way the chart is positioned relative to the text: it will either be a part of the text (in case you select the inline style) or bypassed by it from all sides (if you select one of the other styles). Inline - the chart is considered to be a part of the text, like a character, so when the text moves, the chart moves as well. In this case the positioning options are inaccessible. If one of the following styles is selected, the chart can be moved independently of the text and positioned on the page exactly: Square - the text wraps the rectangular box that bounds the chart. Tight - the text wraps the actual chart edges. Through - the text wraps around the chart edges and fills in the open white space within the chart. Top and bottom - the text is only above and below the chart. In front - the chart overlaps the text. Behind - the text overlaps the chart. If you select the square, tight, through, or top and bottom style you will be able to set up some additional parameters - distance from text at all sides (top, bottom, left, right). The Position tab is available only if you select a wrapping style other than inline. This tab contains the following parameters that vary depending on the selected wrapping style: The Horizontal section allows you to select one of the following three chart positioning types: Alignment (left, center, right) relative to character, column, left margin, margin, page or right margin, Absolute Position measured in absolute units i.e. Centimeters/Points/Inches (depending on the option specified at the File -> Advanced Settings... tab) to the right of character, column, left margin, margin, page or right margin, Relative position measured in percent relative to the left margin, margin, page or right margin. The Vertical section allows you to select one of the following three chart positioning types: Alignment (top, center, bottom) relative to line, margin, bottom margin, paragraph, page or top margin, Absolute Position measured in absolute units i.e. Centimeters/Points/Inches (depending on the option specified at the File -> Advanced Settings... tab) below line, margin, bottom margin, paragraph, page or top margin, Relative position measured in percent relative to the margin, bottom margin, page or top margin. Move object with text controls whether the chart moves as the text to which it is anchored moves. Allow overlap controls whether two charts overlap or not if you drag them near each other on the page. The Alternative Text tab allows to specify a Title and Description which will be read to the people with vision or cognitive impairments to help them better understand what information there is in the chart."
    },
   {
        "id": "UsageInstructions/InsertContentControls.htm", 
        "title": "Insert content controls", 
        "body": "Content controls are objects containing different types of contents, such as text, objects etc. Depending on the selected content control type, you can create a form with input fields that can be filled in by other users, or protect some parts of the document from being edited or deleted etc. Note: the possibility to add new content controls is available in the paid version only. In the open source version, you can edit existing content controls, as well as copy and paste them. Currently, you can add the following types of content controls: Plain Text, Rich Text, Picture, Combo box, Drop-down list, Date, Check box. Plain Text is an object containing text that can be formatted. Plain text content controls cannot contain more than one paragraph. Rich Text is an object containing text that can be formatted. Rich text content controls can contain several paragraphs, lists, and objects (images, shapes, tables etc.). Picture is an object containing a single image. Combo box is an object containing a drop-down list with a set of choices. It allows to choose one of the predefined values from the list and edit the selected value if necessary. Drop-down list is an object containing a drop-down list with a set of choices. It allows to choose one of the predefined values from the list. The selected value cannot be edited. Date is an object containing a calendar that allows to choose a date. Check box is an object that allows to display two states: check box is selected and check box is cleared. Adding content controls Create a new Plain Text content control position the insertion point within a line of the text where you want the control to be added, or select a text passage you want to become the control contents. switch to the Insert tab of the top toolbar. click the arrow next to the Content Controls icon. choose the Plain Text option from the menu. The control will be inserted at the insertion point within a line of the existing text. Replace the default text within the control (\"Your text here\") with your own one: select the default text, and type in a new text or copy a text passage from anywhere and paste it into the content control. Plain text content controls do not allow adding line breaks and cannot contain other objects such as images, tables etc. Create a new Rich Text content control position the insertion point at the end of a paragraph after which you want the control to be added, or select one or more of the existing paragraphs you want to become the control contents. switch to the Insert tab of the top toolbar. click the arrow next to the Content Controls icon. choose the Rich Text option from the menu. The control will be inserted in a new paragraph. Replace the default text within the control (\"Your text here\") with your own one: select the default text, and type in a new text or copy a text passage from anywhere and paste it into the content control. Rich text content controls allow adding line breaks, i.e. can contain multiple paragraphs as well as some objects, such as images, tables, other content controls etc. Create a new Picture content control position the insertion point within a line of the text where you want the control to be added. switch to the Insert tab of the top toolbar. click the arrow next to the Content Controls icon. choose the Picture option from the menu - the control will be inserted at the insertion point. click the image icon in the button above the content control border - a standard file selection window will open. Choose an image stored on your computer and click Open. The selected image will be displayed within the content control. To replace the image, click the image icon in the button above the content control border and select another image. Create a new Combo box or Drop-down list content control The Combo box and Drop-down list content controls contain a drop-down list with a set of choices. They can be created in nearly the same way. The main difference between them is that the selected value in the drop-down list cannot be edited, while the selected value in the combo box can be replaced with your own one. position the insertion point within a line of the text where you want the control to be added. switch to the Insert tab of the top toolbar. click the arrow next to the Content Controls icon. choose the Combo box or Drop-down list option from the menu - the control will be inserted at the insertion point. right-click the added control and choose the Content control settings option from the contextual menu. in the the Content Control Settings window that opens switch to the Combo box or Drop-down list tab, depending on the selected content control type. to add a new list item, click the Add button and fill in the available fields in the window that opens: specify the necessary text in the Display name field, e.g. Yes, No, Other. This text will be displayed in the content control within the document. by default, the text in the Value field corresponds to the one entered in the Display name field. If you want to edit the text in the Value field, note that the entered value must be unique for each item. click the OK button. you can edit or delete the list items by using the Edit or Delete buttons on the right or change the item order using the Up and Down button. when all the necessary choices are set, click the OK button to save the settings and close the window. You can click the arrow button in the right part of the added Combo box or Drop-down list content control to open the item list and choose the necessary one. Once the necessary item is selected from the Combo box, you can edit the displayed text replacing it with your own one entirely or partially. The Drop-down list does not allow to edit the selected item. Create a new Date content control position the insertion point within a line of the text where you want the control to be added. switch to the Insert tab of the top toolbar. click the arrow next to the Content Controls icon. choose the Date option from the menu - the control with the current date will be inserted at the insertion point. right-click the added control and choose the Content control settings option from the contextual menu. in the the Content Control Settings window that opens switch to the Date format tab. choose the necessary Language and select the necessary date format in the Display the date like this list. click the OK button to save the settings and close the window. You can click the arrow button in the right part of the added Date content control to open the calendar and choose the necessary date. Create a new Check box content control position the insertion point within a line of the text where you want the control to be added. switch to the Insert tab of the top toolbar. click the arrow next to the Content Controls icon. choose the Check box option from the menu - the control will be inserted at the insertion point. right-click the added control and choose the Content control settings option from the contextual menu. in the the Content Control Settings window that opens switch to the Check box tab. click the Checked symbol button to specify the necessary symbol for the selected check box or the Unchecked symbol to select how the cleared check box should look like. The Symbol window will open. To learn more on how to work with symbols, you can refer to this article. when the symbols are specified, click the OK button to save the settings and close the window. The added check box is displayed in the unchecked mode. If you click the added check box it will be checked with the symbol selected in the Checked symbol list. Note: The content control border is visible when the control is selected only. The borders do not appear on a printed version. Moving content controls Controls can be moved to another place in the document: click the button to the left of the control border to select the control and drag it without releasing the mouse button to another position in the document text. You can also copy and paste content controls: select the necessary control and use the Ctrl+C/Ctrl+V key combinations. Editing plain text and rich text content controls Text within the plain text and rich text content controls can be formatted using the icons on the top toolbar: you can adjust the font type, size, color, apply decoration styles and formatting presets. It's also possible to use the Paragraph - Advanced settings window accessible from the contextual menu or from the right sidebar to change the text properties. Text within rich text content controls can be formatted like a regular text of the document, i.e. you can set line spacing, change paragraph indents, adjust tab stops. Changing content control settings No matter which type of content controls is selected, you can change the content control settings in the General and Locking sections of the Content Control Settings window. To open the content control settings, you can proceed in the following ways: Select the necessary content control, click the arrow next to the Content Controls icon at the top toolbar and select the Control Settings option from the menu. Right-click anywhere within the content control and use the Content control settings option from the contextual menu. A new window will open. At the General tab, you can adjust the following settings: Specify the content control Title or Tag in the corresponding fields. The title will be displayed when the control is selected in the document. Tags are used to identify content controls so that you can make reference to them in your code. Choose if you want to display the content control with a Bounding box or not. Use the None option to display the control without the bounding box. If you select the Bounding box option, you can choose this box Color using the field below. Click the Apply to All button to apply the specified Appearance settings to all the content controls in the document. At the Locking tab, you can protect the content control from being deleted or edited using the following settings: Content control cannot be deleted - check this box to protect the content control from being deleted. Contents cannot be edited - check this box to protect the contents of the content control from being edited. For certain types of content controls, the third tab is also available that contains the settings specific for the selected content control type only: Combo box, Drop-down list, Date, Check box. These settings are described above in the sections about adding the corresponding content controls. Click the OK button within the settings window to apply the changes. It's also possible to highlight content controls with a certain color. To highlight controls with a color: Click the button to the left of the control border to select the control, Click the arrow next to the Content Controls icon at the top toolbar, Select the Highlight Settings option from the menu, Select the necessary color on the available palettes: Theme Colors, Standard Colors or specify a new Custom Color. To remove previously applied color highlighting, use the No highlighting option. The selected highlight options will be applied to all the content controls in the document. Removing content controls To remove a control and leave all its contents, click the content control to select it, then proceed in one of the following ways: Click the arrow next to the Content Controls icon at the top toolbar and select the Remove content control option from the menu. Right-click the content control and use the Remove content control option from the contextual menu. To remove a control and all its contents, select the necessary control and press the Delete key on the keyboard."
    },
   {
        "id": "UsageInstructions/InsertDropCap.htm", 
        "title": "Inserire un capolettera", 
        "body": "Un Capolettera è la prima lettera di un paragrafo che è molto più grande di altre e occupa diverse righe di altezza. Per aggiungere un capolettera, posizionare il cursore all'interno del paragrafo necessario, passare alla scheda Inserisci della barra degli strumenti superiore, fare clic sull'icona Capolettera nella barra degli strumenti superiore, nell'elenco a discesa aperto selezionare l'opzione desiderata: Nel Testo - per posizionare il capolettera all'interno del paragrafo. Nel Margine - per posizionare il capolettera nel margine sinistro. Il primo carattere del paragrafo selezionato verrà trasformato in un capolettera. Se si ha bisogno che il capolettera includa altri caratteri, aggiungerli manualmente: selezionare il capolettera e digitare le altre lettere di cui si ha bisogno. Per regolare l'aspetto del capolettera (ad esempio la dimensione del carattere, il tipo, lo stile della decorazione o il colore), selezionate la lettera e utilizzate le icone corrispondenti nella scheda Home della barra degli strumenti superiore. Quando il capolettera è selezionato, è circondato da una cornice (un contenitore utilizzato per posizionare il capolettera sulla pagina). È possibile modificare rapidamente le dimensioni della cornice trascinandone i bordi o la posizione utilizzando l'icona visualizzata dopo aver posizionato il cursore del mouse sulla cornice. Per eliminare il capolettera aggiunto, selezionarlo, fare clic sull'icona Capolettera nella scheda Inserisci della barra degli strumenti superiore e scegliere l'opzione Nessuno dall'elenco a discesa. Per regolare i parametri del capolettera aggiunto, selezionarlo, fare clic sull'icona Capolettera nella scheda Inserisci della barra degli strumenti superiore e scegliere l'opzione Impostazioni capolettera dall'elenco a discesa. Si aprirà la finestra Capolettera - Impostazioni avanzate: La scheda Capolettera consente di impostare i seguenti parametri: Posizione - viene utilizzato per modificare il posizionamento del capolettera. Selezionare l'opzione Nel testo o Nel margine oppure fare clic su Nessuno per eliminare il capolettera. Font - consente di selezionare uno dei font dall'elenco di quelli disponibili. Altezza in righe - viene utilizzato per specificare il numero di righe su cui il capolettera deve estendersi. È possibile selezionare un valore compreso tra 1 e 10. Distanza dal testo - viene utilizzato per specificare la quantità di spazio tra il testo del paragrafo e il bordo destro della cornice che circonda il capolettera. La scheda Bordi e riempimento consente di aggiungere un bordo intorno al capolettera e di regolarne i parametri. Essi sono i seguenti: Parametri del bordo (dimensioni, colore e presenza o assenza) - impostare le dimensioni del bordo, selezionare il colore e scegliere i bordi (superiore, inferiore, sinistra, destra o la loro combinazione) a cui si desidera applicare queste impostazioni. Colore sfondo - scegliere il colore per lo sfondo del capolettera. La scheda Margini consente di impostare la distanza tra il capolettera e i bordi Superiore, Inferiore, Sinistra e Destra intorno ad esso (se i bordi sono stati aggiunti in precedenza). Una volta aggiunto il capolettera, puoi anche modificare i parametri della cornice. Per accedervi, fare clic con il tasto destro all'interno della cornice e selezionare Impostazioni avanzate della cornice dal menu. Si aprirà la finestra Cornice - Impostazioni avanzate: La scheda Cornice permette di impostare i seguenti parametri: Posizione - consente di selezionare lo stile In linea o Dinamica. In alternativa, è possibile fare clic su Nessuno per eliminare la cornice. Larghezza e Altezza - vengono utilizzati per modificare le dimensioni della cornice. L'opzione Auto consente di regolare automaticamente le dimensioni della cornice per adattarle al capolettera. L'opzione Esatta consente di specificare valori fissi. L'opzione Minima viene utilizzata per impostare il valore di altezza minima (se si modifica la dimensione del capolettera, l'altezza del riquadro cambia di conseguenza, ma non può essere inferiore al valore specificato). I parametri orizzontali vengono utilizzati per impostare la posizione esatta della cornice nelle unità di misura selezionate rispetto a un margine, pagina o colonna, oppure per allineare la cornice (a sinistra, al centro o a destra) rispetto a uno di questi punti di riferimento. È inoltre possibile impostare la distanza orizzontale dal testo, ovvero la quantità di spazio tra i bordi della cornice verticale e il testo del paragrafo. I parametri verticali vengono utilizzati per impostare la posizione esatta della cornice nelle unità di misura selezionate rispetto a un margine, pagina o paragrafo, oppure per allineare la cornice (in alto, al centro o in basso) rispetto a uno di questi punti di riferimento. È inoltre possibile impostare la distanza verticale dal testo, ovvero la quantità di spazio tra i bordi della cornice orizzontale e il testo del paragrafo. Sposta col testo - controlla che la cornice si sposti mentre il paragrafo a cui è ancorata si sposta. Le schede Bordi e Riempimento e Margini consentono di impostare solo gli stessi parametri delle schede con lo stesso nome nella finestra Capolettera - Impostazioni avanzate."
    },
   {
        "id": "UsageInstructions/InsertEquation.htm", 
        "title": "Insert equations", 
        "body": "Document Editor allows you to build equations using the built-in templates, edit them, insert special characters (including mathematical operators, Greek letters, accents etc.). Add a new equation To insert an equation from the gallery, put the cursor within the necessary line , switch to the Insert tab of the top toolbar, click the arrow next to the Equation icon at the top toolbar, in the opened drop-down list select the equation category you need. The following categories are currently available: Symbols, Fractions, Scripts, Radicals, Integrals, Large Operators, Brackets, Functions, Accents, Limits and Logarithms, Operators, Matrices, click the certain symbol/equation in the corresponding set of templates. The selected symbol/equation box will be inserted at the cursor position. If the selected line is empty, the equation will be centered. To align such an equation left or right, click on the equation box and use the or icon at the Home tab of the top toolbar. Each equation template represents a set of slots. Slot is a position for each element that makes up the equation. An empty slot (also called as a placeholder) has a dotted outline . You need to fill in all the placeholders specifying the necessary values. Note: to start creating an equation, you can also use the Alt + = keyboard shortcut. It's also possible to add a caption to the equation. To learn more on how to work with captions for equations, you can refer to this article. Enter values The insertion point specifies where the next character you enter will appear. To position the insertion point precisely, click within a placeholder and use the keyboard arrows to move the insertion point by one character left/right or one line up/down. If you need to create a new placeholder below the slot with the insertion point within the selected template, press Enter. Once the insertion point is positioned, you can fill in the placeholder: enter the desired numeric/literal value using the keyboard, insert a special character using the Symbols palette from the Equation menu at the Insert tab of the top toolbar, add another equation template from the palette to create a complex nested equation. The size of the primary equation will be automatically adjusted to fit its content. The size of the nested equation elements depends on the primary equation placeholder size, but it cannot be smaller than the sub-subscript size. To add some new equation elements you can also use the right-click menu options: To add a new argument that goes before or after the existing one within Brackets, you can right-click on the existing argument and select the Insert argument before/after option from the menu. To add a new equation within Cases with several conditions from the Brackets group (or equations of other types, if you've previously added new placeholders by pressing Enter), you can right-click on an empty placeholder or entered equation within it and select the Insert equation before/after option from the menu. To add a new row or a column in a Matrix, you can right-click on a placeholder within it, select the Insert option from the menu, then select Row Above/Below or Column Left/Right. Note: currently, equations cannot be entered using the linear format, i.e. \\sqrt(4&x^3). When entering the values of the mathematical expressions, you do not need to use Spacebar as the spaces between the characters and signs of operations are set automatically. If the equation is too long and does not fit to a single line, automatic line breaking occurs as you type. You can also insert a line break in a specific position by right-clicking on a mathematical operator and selecting the Insert manual break option from the menu. The selected operator will start a new line. Once the manual line break is added, you can press the Tab key to align the new line to any math operator of the previous line. To delete the added manual line break, right-click on the mathematical operator that starts a new line and select the Delete manual break option. Format equations To increase or decrease the equation font size, click anywhere within the equation box and use the and buttons at the Home tab of the top toolbar or select the necessary font size from the list. All the equation elements will change correspondingly. The letters within the equation are italicized by default. If necessary, you can change the font style (bold, italic, strikeout) or color for a whole equation or its part. The underlined style can be applied to the entire equation only, not to individual characters. Select the necessary part of the equation by clicking and dragging. The selected part will be highlighted blue. Then use the necessary buttons at the Home tab of the top toolbar to format the selection. For example, you can remove the italic format for ordinary words that are not variables or constants. To modify some equation elements you can also use the right-click menu options: To change the Fractions format, you can right-click on a fraction and select the Change to skewed/linear/stacked fraction option from the menu (the available options differ depending on the selected fraction type). To change the Scripts position relating to text, you can right-click on the equation that includes scripts and select the Scripts before/after text option from the menu. To change the argument size for Scripts, Radicals, Integrals, Large Operators, Limits and Logarithms, Operators as well as for overbraces/underbraces and templates with grouping characters from the Accents group, you can right-click on the argument you want to change and select the Increase/Decrease argument size option from the menu. To specify whether an empty degree placeholder should be displayed or not for a Radical, you can right-click on the radical and select the Hide/Show degree option from the menu. To specify whether an empty limit placeholder should be displayed or not for an Integral or Large Operator, you can right-click on the equation and select the Hide/Show top/bottom limit option from the menu. To change the limits position relating to the integral or operator sign for Integrals or Large Operators, you can right-click on the equation and select the Change limits location option from the menu. The limits can be displayed to the right of the operator sign (as subscripts and superscripts) or directly above and below the operator sign. To change the limits position relating to text for Limits and Logarithms and templates with grouping characters from the Accents group, you can right-click on the equation and select the Limit over/under text option from the menu. To choose which of the Brackets should be displayed, you can right-click on the expression within them and select the Hide/Show opening/closing bracket option from the menu. To control the Brackets size, you can right-click on the expression within them. The Stretch brackets option is selected by default so that the brackets can grow according to the expression within them, but you can deselect this option to prevent brackets from stretching. When this option is activated, you can also use the Match brackets to argument height option. To change the character position relating to text for overbraces/underbraces or overbars/underbars from the Accents group, you can right-click on the template and select the Char/Bar over/under text option from the menu. To choose which borders should be displayed for a Boxed formula from the Accents group, you can right-click on the equation and select the Border properties option from the menu, then select Hide/Show top/bottom/left/right border or Add/Hide horizontal/vertical/diagonal line. To specify whether empty placeholders should be displayed or not for a Matrix, you can right-click on it and select the Hide/Show placeholder option from the menu. To align some equation elements you can use the right-click menu options: To align equations within Cases with several conditions from the Brackets group (or equations of other types, if you've previously added new placeholders by pressing Enter), you can right-click on an equation, select the Alignment option from the menu, then select the alignment type: Top, Center, or Bottom. To align a Matrix vertically, you can right-click on the matrix, select the Matrix Alignment option from the menu, then select the alignment type: Top, Center, or Bottom. To align elements within a Matrix column horizontally, you can right-click on a placeholder within the column, select the Column Alignment option from the menu, then select the alignment type: Left, Center, or Right. Delete equation elements To delete a part of the equation, select the part you want to delete by dragging the mouse or holding down the Shift key and using the arrow buttons, then press the Delete key on the keyboard. A slot can only be deleted together with the template it belongs to. To delete the entire equation, select it completely by dragging the mouse or double-clicking on the equation box and press the Delete key on the keyboard. To delete some equation elements you can also use the right-click menu options: To delete a Radical, you can right-click on it and select the Delete radical option from the menu. To delete a Subscript and/or Superscript, you can right-click on the expression that contains them and select the Remove subscript/superscript option from the menu. If the expression contains scripts that go before text, the Remove scripts option is available. To delete Brackets, you can right-click on the expression within them and select the Delete enclosing characters or Delete enclosing characters and separators option from the menu. If the expression within Brackets inclides more than one argument, you can right-click on the argument you want to delete and select the Delete argument option from the menu. If Brackets enclose more than one equation (i.e. Cases with several conditions), you can right-click on the equation you want to delete and select the Delete equation option from the menu. This option is also available for equations of other types if you've previously added new placeholders by pressing Enter. To delete a Limit, you can right-click on it and select the Remove limit option from the menu. To delete an Accent, you can right-click on it and select the Remove accent character, Delete char or Remove bar option from the menu (the available options differ depending on the selected accent). To delete a row or a column of a Matrix, you can right-click on the placeholder within the row/column you need to delete, select the Delete option from the menu, then select Delete Row/Column."
    },
   {
        "id": "UsageInstructions/InsertFootnotes.htm", 
        "title": "Inserire note a piè di pagina", 
        "body": "È possibile aggiungere note a piè di pagina per fornire spiegazioni o commenti per determinate frasi o termini utilizzati nel testo, fare riferimenti alle fonti, ecc. Per inserire una nota a piè di pagina nel documento, posizionare il punto di inserimento alla fine del passaggio di testo a cui si desidera aggiungere una nota a piè di pagina, passare alla scheda Riferimenti della barra degli strumenti superiore, Fare click sull’icona Nota a piè di pagina nella barra degli strumenti superiore oppure fare clic sulla freccia accanto all'icona Nota a piè di pagina e selezionare l'opzione Inserisci nota a piè di pagina dal menu, Il segno di nota a piè di pagina (ovvero il carattere in apice che indica una nota a piè di pagina) viene visualizzato nel testo del documento e il punto di inserimento si sposta nella parte inferiore della pagina corrente. digitare il testo della nota a piè di pagina. Ripetere le operazioni di cui sopra per aggiungere note a piè di pagina successive per altri passaggi di testo nel documento. Le note a piè di pagina vengono numerate automaticamente. Se si posiziona il puntatore del mouse sul segno della nota a piè di pagina nel testo del documento, viene visualizzata una piccola finestra popup con il testo della nota a piè di pagina. Per navigare facilmente tra le note a piè di pagina aggiunte all'interno del testo del documento, fare clic sulla freccia accanto all'icona Nota a piè di pagina nella scheda Riferimenti della barra degli strumenti superiore, nella sezione Passa alle note a piè di pagina , utilizzare la freccia per passare alla nota a piè di pagina precedente o la freccia per passare alla nota a piè di pagina successiva. Per modificare le impostazioni delle note a piè di pagina, fare clic sulla freccia accanto all'icona Nota a piè di pagina nella scheda Riferimenti della barra degli strumenti superiore, selezionare l'opzione Impostazioni delle note dal menu, modificare i parametri correnti nella finestra Impostazioni delle note che si apre: Impostare la Posizione delle note a piè di pagina nella pagina selezionando una delle opzioni disponibili: Fondo pagina - per posizionare le note a piè di pagina nella parte inferiore della pagina (questa opzione è selezionata per impostazione predefinita). Sotto al testo - per posizionare le note a piè di pagina più vicino al testo. Questa opzione può essere utile nei casi in cui la pagina contiene un breve testo. Regolare il Formato delle note a piè di pagina: Formato numero - selezionare il formato numerico necessario tra quelli disponibili: 1, 2, 3,..., a, b, c,..., A, B, C,..., i, ii, iii,..., I, II, III,.... Inizia da  - utilizzare le frecce per impostare il numero o la lettera con cui si desidera iniziare la numerazione. Numerazione - selezionare un modo per numerare le note a piè di pagina: Continuo - numerare le note a piè di pagina in sequenza in tutto il documento, Ricomincia a ogni sezione - per iniziare la numerazione delle note a piè di pagina con il numero 1 (o qualche altro carattere specificato) all'inizio di ogni sezione, Ricomincia a ogni pagina - per iniziare la numerazione delle note a piè di pagina con il numero 1 (o un altro carattere specificato) all'inizio di ogni pagina. Simbolo personalizzato - consente di impostare un carattere speciale o una parola che si desidera utilizzare come segno di nota a piè di pagina (ad es. * or Nota1). Immettere il carattere/parola necessari nel campo di immissione testo e fare clic sul pulsante Inserisci nella parte inferiore della finestra Impostazioni note. Utilizzare l'elenco a discesa Applica modifiche a per selezionare se si desidera applicare le impostazioni delle note specificate solo a Tutto il documento o alla Sezione attuale. Nota: per utilizzare la formattazione di note a piè di pagina diverse in parti separate del documento, è necessario aggiungere prima delle Interruzioni di sezione. Quando si è pronti, fare clic sul pulsante Applica. Per rimuovere una singola nota a piè di pagina, posizionare il punto di inserimento direttamente prima del segno della nota a piè di pagina nel testo del documento e premere Elimina. Le altre note a piè di pagina verranno rinumerate automaticamente. Per eliminare tutte le note a piè di pagina del documento, fare clic sulla freccia accanto all'icona Nota a piè di pagina nella scheda Riferimenti della barra degli strumenti superiore, selezionare l'opzione Elimina tutte le note a piè di pagina dal menu."
    },
   {
        "id": "UsageInstructions/InsertHeadersFooters.htm", 
        "title": "Inserire intestazioni e piè di pagina", 
        "body": "Per aggiungere un'intestazione o un piè di pagina al documento o modificare quello esistente, passare alla scheda Inserisci della barra degli strumenti superiore, fare clic sull'icona Intestazioni/Piè di pagina nella barra degli strumenti superiore, selezionare una delle seguenti opzioni: Modifica intestazione per inserire o modificare il testo dell'intestazione. Modifica piè di pagina per inserire o modificare il testo del piè di pagina. modificare i parametri correnti per intestazioni o piè di pagina nella barra laterale destra: Impostare la Posizione del testo rispetto all'inizio (per le intestazioni) o alla fine (per i piè di pagina) della pagina. Selezionare la casella Diversi per la prima pagina per applicare un'intestazione o un piè di pagina diversi alla prima pagina o nel caso in cui non desideri aggiungervi alcuna intestazione/piè di pagina. Utilizzare la casella Diversi per pagine pari e dispari per aggiungere diverse intestazioni / piè di pagina per pagine pari e dispari. L'opzione Collega a precedente è disponibile nel caso in cui tu abbia precedentemente aggiunto <a href=\"../UsageInstructions/SectionBreaks.htm\" </a> onclick=\"onhyperlinkclick(this)\">sezioni</a> al tuo documento. In caso contrario, verrà visualizzato in grigio. Inoltre, questa opzione non è disponibile per la prima sezione (ovvero quando viene selezionata un'intestazione o piè di pagina che appartiene alla prima sezione). Per impostazione predefinita, questa casella è selezionata, in modo che le stesse intestazioni / piè di pagina vengano applicate a tutte le sezioni. Se selezioni un'area di intestazione o piè di pagina, vedrai che l'area è contrassegnata con l'etichetta Uguale a precedente. Deseleziona la casella Collega a precedente per utilizzare intestazioni / piè di pagina diversi per ogni sezione del documento. L'etichetta Uguale a precedente non verrà più visualizzata. Per inserire un testo o modificare il testo già inserito e regolare le impostazioni dell'intestazione o del piè di pagina, puoi anche fare doppio clic all'interno della parte superiore o inferiore di una pagina o fare clic con il tasto destro del mouse e selezionare l'unica opzione di menu - Modifica intestazione o Modifica piè di pagina. Per passare al corpo del documento, fare doppio clic all'interno dell'area di lavoro. Il testo utilizzato come intestazione o piè di pagina verrà visualizzato in grigio. Nota: si prega di fare riferimento alla sezione Inserire numeri di pagina per imparare ad aggiungere numeri di pagina al documento."
    },
   {
        "id": "UsageInstructions/InsertImages.htm", 
        "title": "Inserire immagini", 
        "body": "Nell'Editor di documenti è possibile inserire nel documento immagini nei formati più diffusi. Sono supportati i seguenti formati di immagine: BMP, GIF, JPEG, JPG, PNG. Inserire un'immagine Per inserire un'immagine nel testo del documento, posizionare il cursore nel punto in cui si desidera inserire l'immagine, passare alla scheda Inserisci della barra degli strumenti superiore, fare clic sull'icona Immagine nella barra degli strumenti in alto, selezionare una delle seguenti opzioni per caricare l'immagine: l'opzione Immagine da file aprirà la finestra di dialogo standard per la selezione del file. Sfogliare l'unità disco rigido del computer per il file necessario e fare clic sul pulsante Apri l'opzione Immagine da URL aprirà la finestra in cui è possibile inserire l'indirizzo web dell'immagine necessario e fare clic sul pulsante OK l'opzione Immagine da archiviazione aprirà la finestra Seleziona origine dati. Selezionare un'immagine memorizzata sul tuo portale e fai clic sul pulsante OK una volta aggiunta l'immagine, è possibile modificarne le dimensioni, le proprietà e la posizione. È anche possibile aggiungere una didascalia all'immagine. Per saperne di più su come lavorare con le didascalie per le immagini, puoi fare riferimento a questo articolo. Spostare e ridimensionare le immagini Per modificare le dimensioni dell'immagine, trascinare i quadratini situati sui bordi. Per mantenere le proporzioni originali dell'immagine selezionata durante il ridimensionamento, tenere premuto il tasto Maiusc e trascinare una delle icone agli angoli. Per modificare la posizione dell'immagine, utilizzare l'icona che appare dopo aver posizionato il cursore del mouse sull'immagine. Trascinare l'immagine nella posizione desiderata senza rilasciare il pulsante del mouse. Quando si sposta l'immagine, vengono visualizzate linee guida che consentono di posizionare con precisione l'oggetto sulla pagina (se è selezionato uno stile di disposizione diverso da quello in linea). Per ruotare l'immagine, posizionare il cursore del mouse sulla maniglia di rotazione e trascinala in senso orario o antiorario. Per vincolare l'angolo di rotazione a incrementi di 15 gradi, tenete premuto il tasto Maiusc durante la rotazione. Nota: l'elenco delle scorciatoie da tastiera che possono essere utilizzate quando si lavora con gli oggetti è disponibile qui. Regolare le impostazioni dell'immagine Alcune delle impostazioni dell'immagine possono essere modificate utilizzando la scheda Impostazioni immagine della barra laterale destra. Per attivarla clicca sull'immagine e scegli l'icona Impostazioni immagine a destra. Qui è possibile modificare le seguenti proprietà: Dimensione viene utilizzata per visualizzare la Larghezza e l'Altezza dell'immagine corrente. Se necessario, è possibile ripristinare le dimensioni effettive dell'immagine facendo clic sul pulsante Dimensioni effettive. Il pulsante Adatta al margine consente di ridimensionare l'immagine, in modo da occupare tutto lo spazio tra il margine sinistro e quello destro della pagina. Il pulsante Ritaglia viene utilizzato per ritagliare l'immagine. Fare clic sul pulsante Ritaglia per attivare le maniglie di ritaglio che appaiono agli angoli dell'immagine e al centro di ciascun lato. Trascinare manualmente le maniglie per impostare l'area di ritaglio. È possibile spostare il cursore del mouse sul bordo dell'area di ritaglio in modo che si trasformi nell'icona e trascinare l'area. Per ritagliare un solo lato, trascinare la maniglia situata al centro di questo lato. Per ritagliare contemporaneamente due lati adiacenti, trascinare una delle maniglie d'angolo. Per ritagliare equamente due lati opposti dell'immagine, tenere premuto il tasto Ctrl mentre si trascina la maniglia al centro di uno di questi lati. Per ritagliare equamente tutti i lati dell'immagine, tenere premuto il tasto Ctrl quando si trascina una delle maniglie d'angolo. Quando l'area di ritaglio è specificata, fare di nuovo clic sul pulsante Ritaglia o premere il tasto Esc oppure fare clic in un punto qualsiasi al di fuori dell'area di ritaglio per applicare le modifiche. Dopo aver selezionato l'area di ritaglio, è anche possibile utilizzare le opzioni Riempimento e Adatta disponibili dal menu a discesa Ritaglia. Fare di nuovo clic sul pulsante Ritaglia e selezionare l'opzione desiderata: Se si seleziona l'opzione Riempimento , la parte centrale dell'immagine originale verrà mantenuta e utilizzata per riempire l'area di ritaglio selezionata, mentre altre parti dell'immagine verranno rimosse. Se si seleziona l'opzione Adatta, , l'immagine verrà ridimensionata in modo da adattarla all'altezza o alla larghezza dell'area di ritaglio. Nessuna parte dell'immagine originale verrà rimossa, ma potrebbero apparire spazi vuoti all'interno dell'area di ritaglio selezionata. Rotazione viene utilizzata per ruotare l'immagine di 90 gradi in senso orario o antiorario, nonché per capovolgere l'immagine orizzontalmente o verticalmente. Fare clic su uno dei pulsanti: per ruotare l'immagine di 90 gradi in senso antiorario per ruotare l'immagine di 90 gradi in senso orario per capovolgere l'immagine orizzontalmente (da sinistra a destra) per capovolgere l'immagine verticalmente (sottosopra) Stile di siposizione viene utilizzato per selezionare uno stile di disposizione del testo tra quelli disponibili - in linea, quadrato, ravvicinato, all’interno, sopra e sotto, davantial testo, dietro al testo (per ulteriori informazioni vedere la descrizione delle impostazioni avanzate di seguito). Sostituisci immagine viene utilizzata per sostituire l'immagine corrente caricandone un'altra Da file o Da URL. Alcune di queste opzioni si possono trovare anche nel menu di scelta rapida. Le opzioni del menu sono: Taglia, Copia, Incolla - opzioni standard utilizzate per tagliare o copiare un testo/oggetto selezionato e incollare un passaggio di testo o un oggetto precedentemente tagliato/copiato nella posizione corrente del cursore. Disponi viene utilizzata per portare l'immagine selezionata in primo piano, inviarla sullo sfondo, spostarsi avanti o indietro, nonché raggruppare o separare le immagini per eseguire operazioni con più di esse contemporaneamente. Per ulteriori informazioni su come disporre gli oggetti è possibile fare riferimento a questa pagina. Allinea viene utilizzata per allineare l'immagine a sinistra, al centro, a destra, in alto, al centro, in basso. Per ulteriori informazioni su come allineare gli oggetti è possibile fare riferimento a questa pagina. Stile di siposizione viene utilizzato per selezionare uno stile di disposizione del testo tra quelli disponibili - in linea, quadrato, ravvicinato, all’interno, sopra e sotto, davantial testo, dietro al testo - modificare il contorno di avvolgimento. L'opzione Modifica contorno avvolgimento è disponibile solo se si seleziona uno stile di disposizione diverso da In linea . Trascinare i punti di ritorno a capo per personalizzare il contorno. Per creare un nuovo punto di avvolgimento, fare clic in un punto qualsiasi della linea rossa e trascinarlo nella posizione desiderata. Ruota viene utilizzata per ruotare l'immagine di 90 gradi in senso orario o antiorario, nonché per capovolgere l'immagine orizzontalmente o verticalmente. Ritaglia viene utilizzata per applicare una delle opzioni di ritaglio: Ritaglia, Riempimento o Adatta. Selezionare l'opzione Ritaglia dal sottomenu, quindi trascinare le maniglie di ritaglio per impostare l'area di ritaglio e fare nuovamente clic su una di queste tre opzioni dal sottomenu per applicare le modifiche. Dimensione reale viene utilizzato per modificare le dimensioni correnti dell'immagine in quella effettiva. Sostituisci immagine viene utilizzata per sostituire l'immagine corrente caricandone un'altra Da file o Da URL. Impostazioni avanzate dell'immagine viene utilizzata per aprire la finestra \"Immagine - Impostazioni avanzate\". Quando l'immagine è selezionata, l'icona Impostazioni forma è disponibile anche sulla destra. È possibile fare clic su questa icona per aprire la scheda Impostazioni forma nella barra laterale destra e regolare il tipo di tratto, , le dimensioni e il colore della forma, nonché modificare il tipo di forma selezionando un'altra forma dal menu Cambia forma automatica. La forma dell'immagine cambierà di conseguenza. Nella scheda Impostazioni forma, è inoltre possibile utilizzare l'opzione Mostra ombreggiatura per aggiungere un'ombreggiatura all'immagine. Regolare le impostazioni avanzate dell'immagine Per modificare le impostazioni avanzate dell'immagine, fare clic sull'immagine con il pulsante destro del mouse e selezionare l'opzione Impostazioni avanzate dell’immagine dal menu di scelta rapida o fare semplicemente clic sul collegamento Mostra impostazioni avanzate nella barra laterale destra. Si aprirà la finestra delle proprietà dell'immagine: La scheda Dimensione contiene i seguenti parametri: Larghezza e Altezza - utilizzare queste opzioni per modificare la larghezza e/o l'altezza dell'immagine. Se si fa clic sul pulsante Proporzioni costanti (in questo caso ha questo aspetto ), la larghezza e l'altezza verranno modificate insieme mantenendo le proporzioni originali dell'immagine. Per ripristinare le dimensioni effettive dell'immagine aggiunta, fare clic sul pulsante Dimensioni effettive. La scheda Rotazione contiene i seguenti parametri: Angolo - utilizzare questa opzione per ruotare l'immagine di un angolo esattamente specificato. Immettere il valore necessario misurato in gradi nel campo o regolarlo utilizzando le frecce a destra. Capovolto - selezionare la casella Orizzontalmente per capovolgere l'immagine orizzontalmente (da sinistra a destra) o selezionare la casella Verticalmente per capovolgere l'immagine verticalmente (sottosopra). La scheda Disposizione testo contiene i seguenti parametri: Stile di disposizione - utilizzare questa opzione per modificare il modo in cui l'immagine viene posizionata rispetto al testo: sarà una parte del testo (nel caso in cui si seleziona lo stile In linea) o bypassata da esso da tutti i lati (se si seleziona uno degli altri stili). In linea - l'immagine è considerata una parte del testo, come un carattere, quindi quando il testo si sposta, si sposta anche l'immagine. In questo caso le opzioni di posizionamento sono inaccessibili. Se si seleziona uno dei seguenti stili, l'immagine può essere spostata indipendentemente dal testo e posizionata esattamente sulla pagina: Quadrato - il testo avvolge il riquadro rettangolare che delimita l'immagine. Ravvicinato - il testo avvolge i bordi dell'immagine reale. All’intero - il testo va a capo attorno ai bordi dell'immagine e riempie lo spazio bianco aperto all'interno dell'immagine. Per visualizzare l'effetto, utilizzare l'opzione Modifica contorno avvolgimento dal menu di scelta rapida. Sopra e sotto - il testo è solo sopra e sotto l'immagine. Davanti al testo - l'immagine si sovrappone al testo. Dietro al testo - il testo si sovrappone all'immagine. Se si seleziona lo stile quadrato, ravvicinato, all’interno o sopra e sotto, sarà possibile impostare alcuni parametri aggiuntivi: distanza dal testo su tutti i lati (in alto, in basso, a sinistra, a destra). La scheda Posizione è disponibile solo se si seleziona uno stile di disposizione diverso da quello in linea. Questa scheda contiene i seguenti parametri che variano a seconda dello stile di disposizione selezionato: La sezione Orizzontale consente di selezionare uno dei seguenti tre tipi di posizionamento dell'immagine: Allineamento (a sinistra, al centro, a destra) rispetto a carattere, colonna, margine sinistro, margini, pagina o margine destro, Posizione assoluta misurata in unità assolute, ad esempio Centimetri/Punti/Pollici (a seconda dell'opzione specificata nella scheda File -> Impostazioni avanzate...) a destra del carattere, colonna, margine sinistro, margini, pagina o margine destro, Posizione relativa misurata in percentuale rispetto al margine sinistro, ai margini, alla pagina o al margine destro. La sezione Verticale consente di selezionare uno dei seguenti tre tipi di posizionamento dell'immagine: Allineamento (in alto, al centro, in basso) rispetto a riga, margini, margine inferiore, paragrafo, pagina o margine superiore, Posizione assoluta misurata in unità assolute, ad esempio Centimetri/Punti/Pollici (a seconda dell'opzione specificata nella scheda File -> Impostazioni avanzate...) sotto la linea, i margini, il margine inferiore, il paragrafo, la pagina o il margine superiore, Posizione relativa misurata in percentuale rispetto a margini, al margine inferiore, alla pagina o al margine superiore. Sposta oggetto con testo controlla se l'immagine si sposta mentre il testo a cui è ancorata si sposta. Consenti sovrapposizione controlla se due immagini si sovrappongono o meno se trascinate una accanto all'altra sulla pagina. La scheda Testo alternativo consente di specificare un Titolo e una Descrizione che verranno letti alle persone con disabilità visive o cognitive per aiutarle a capire meglio quali informazioni ci sono nell'immagine."
    },
   {
        "id": "UsageInstructions/InsertPageNumbers.htm", 
        "title": "Insert page numbers", 
        "body": "To insert page numbers into your document, switch to the Insert tab of the top toolbar, click the Header/Footer icon at the top toolbar, choose the Insert Page Number submenu, select one of the following options: To put a page number to each page of your document, select the page number position on the page. To insert a page number at the current cursor position, select the To Current Position option. Note: to insert a current page number at the current cursor position you can also use the Ctrl+Shift+P key combination. To insert the total number of pages in your document (e.g. if you want to create the Page X of Y entry): put the cursor where you want to insert the total number of pages, click the Header/Footer icon at the top toolbar, select the Insert number of pages option. To edit the page number settings, double-click the page number added, change the current parameters at the right sidebar: Set the Position of page numbers on the page as well as relative to the top and bottom of the page. Check the Different first page box to apply a different page number to the very first page or in case you don't want to add any number to it at all. Use the Different odd and even pages box to insert different page numbers for odd and even pages. The Link to Previous option is available in case you've previously added sections into your document. If not, it will be grayed out. Moreover, this option is also unavailable for the very first section (i.e. when a header or footer that belongs to the first section is selected). By default, this box is checked, so that unified numbering is applied to all the sections. If you select a header or footer area, you will see that the area is marked with the Same as Previous label. Uncheck the Link to Previous box to use different page numbering for each section of the document. The Same as Previous label will no longer be displayed. The Page Numbering section allows to adjust page numbering options across different sections of the document. The Continue from previous section option is selected by default and allows to keep continuous page numbering after a section break. If you want to start page numbering with a specific number in the current section of the document, select the Start at radio button and enter the necessary starting value in the field on the right. To return to the document editing, double-click within the working area."
    },
   {
        "id": "UsageInstructions/InsertSymbols.htm", 
        "title": "Inserire simboli e caratteri", 
        "body": "Durante il processo di lavoro potrebbe essere necessario inserire un simbolo che non si trova sulla tastiera. Per inserire tali simboli nel tuo documento, usa l’opzione Inserisci simbolo e segui questi semplici passaggi: posiziona il cursore nella posizione in cui deve essere inserito un simbolo speciale, passa alla scheda Inserisci della barra degli strumenti in alto, fai clic sull’icona Simbolo, viene visualizzata la scheda di dialogo Simbolo da cui è possibile selezionare il simbolo appropriato, utilizza la sezione Intervallo per trovare rapidamente il simbolo necessario. Tutti i simboli sono divisi in gruppi specifici, ad esempio seleziona \"Simboli di valuta” se desideri inserire un carattere di valuta. se questo carattere non è nel set, seleziona un carattere diverso. Molti di loro hanno anche caratteri diversi dal set standard. in alternativa, immetti il valore esadecimale Unicode del simbolo desiderato nel campo valore Unicode HEX. Questo codice si trova nella Mappa caratteri. i simboli utilizzati in precedenza vengono visualizzati anche nel campo Simboli usati di recente, fai clic su Inserisci. Il carattere selezionato verrà aggiunto al documento. Inserire simboli ASCII La tabella ASCII viene anche utilizzata per aggiungere caratteri. Per fare ciò, tieni premuto il tasto ALT e usa il tastierino numerico per inserire il codice carattere. Nota: assicurarsi di utilizzare il tastierino numerico, non i numeri sulla tastiera principale. Per abilitare il tastierino numerico, premere il tasto Bloc Num. Ad esempio, per aggiungere ad un paragrafo il carattere (§), premere e tenere premuto il tasto ALT mentre si digita 789 e quindi rilasciare il tasto ALT. Inserire simboli usando la tabella Unicode Ulteriori caratteri e simboli possono essere trovati anche nella tabella dei simboli di Windows. Per aprire questa tabella, effettuate una delle seguenti operazioni: nel campo Ricerca scrivi 'Tabella caratteri' e aprila, in alternativa premi contemporaneamente Win + R, quindi nella seguente finestra digita charmap.exe e fai clic su OK. Nella Mappa caratteri aperta, selezionare uno dei Set di caratteri, Gruppi e Caratteri. Quindi, fai clic sui caratteri necessari, copiali negli appunti e incollali nella posizione corretta del documento."
    },
   {
        "id": "UsageInstructions/InsertTables.htm", 
        "title": "Inserire tabelle", 
        "body": "Inserire una tabella Per inserire una tabella nel testo del documento, posizionare il cursore nel punto in cui si desidera inserire la tabella, passare alla scheda Inserisci della barra degli strumenti superiore, fare clic sull'icona Tabella nella barra degli strumenti superiore, selezionare l'opzione per creare una tabella: una tabella con un numero predefinito di celle (massimo 10 per 8 celle) Se si desidera aggiungere rapidamente una tabella, è sufficiente selezionare il numero di righe (8 massimo) e colonne (10 massimo). o una tabella personalizzata Nel caso in cui sia necessaria una tabella con più di 10 per 8 celle, selezionare l'opzione Inserisci tabella personalizzata che aprirà la finestra in cui è possibile inserire rispettivamente il numero necessario di righe e colonne, quindi fare clic sul pulsante OK. Se si desidera disegnare una tabella con il mouse, selezionare l'opzione Disegna tabella. Ciò può essere utile se si desidera creare una tabella con righe e colonne di dimensioni diverse. Il cursore del mouse si trasformerà nella matita . Disegnare una forma rettangolare in cui si desidera aggiungere una tabella, quindi aggiungere righe disegnando linee e colonne orizzontali disegnando linee verticali all'interno del contorno della tabella. una volta aggiunta la tabella, è possibile modificarne le proprietà, le dimensioni e la posizione. Per ridimensionare una tabella, posizionare il cursore del mouse sul quadratino nell'angolo inferiore destro e trascinarlo fino a quando la tabella non raggiunge le dimensioni necessarie. È inoltre possibile modificare manualmente la larghezza di una determinata colonna o l'altezza di una riga. Spostare il cursore del mouse sul bordo destro della colonna in modo che il cursore si trasformi nella freccia bidirezionale e trascinare il bordo verso sinistra o verso destra per impostare la larghezza necessaria. Per modificare manualmente l'altezza di una singola riga, spostare il cursore del mouse sul bordo inferiore della riga in modo che il cursore si trasformi nella freccia bidirezionale e trascini il bordo verso l'alto o verso il basso. Per spostare una tabella, tenere premuto il quadratino nell'angolo superiore sinistro e trascinarla nella posizione necessaria nel documento. È anche possibile aggiungere una didascalia alla tabella. Per saperne di più su come lavorare con le didascalie per le tabelle, puoi fare riferimento a questo articolo. Selezionare una tabella o una sua parte Per selezionare un'intera tabella, fare clic sul quadratino nell'angolo superiore sinistro. Per selezionare una determinata cella, spostare il cursore del mouse sul lato sinistro della cella necessaria in modo che il cursore si trasformi nella freccia nera , quindi fare clic con il pulsante sinistro del mouse. Per selezionare una determinata riga, spostare il cursore del mouse sul bordo sinistro della tabella accanto alla riga necessaria in modo che il cursore si trasformi nella freccia nera orizzontale , quindi fare clic con il pulsante sinistro del mouse. Per selezionare una determinata colonna, spostare il cursore del mouse sul bordo superiore della colonna necessaria in modo che il cursore si trasformi nella freccia nera verso il basso , quindi fare clic con il pulsante sinistro del mouse. È anche possibile selezionare una cella, una riga, una colonna o una tabella utilizzando le opzioni del menu contestuale o dalla sezione Righe e colonne nella barra laterale destra. Nota: per spostarsi in una tabella è possibile utilizzare le scelte rapide da tastiera. Regolare le impostazioni della tabella Alcune delle proprietà della tabella e la sua struttura possono essere modificate utilizzando il menu di scelta rapida. Le opzioni del menu sono: Taglia, Copia, Incolla - opzioni standard utilizzate per tagliare o copiare un testo / oggetto selezionato e incollare un passaggio di testo o un oggetto precedentemente tagliato / copiato nella posizione corrente del cursore. Seleziona viene utilizzata per selezionare una riga, una colonna, una cella o una tabella. Inserisci viene utilizzata per inserire una riga sopra o sotto la riga in cui è posizionato il cursore, nonché per inserire una colonna a sinistra oa destra dalla colonna in cui è posizionato il cursore. È anche possibile inserire più righe o colonne. Se si seleziona l'opzione Più righe/colonne, viene visualizzata la finestra Inserisci più righe/colonne. Selezionare l'opzione Righe o Colonne dall'elenco, specificare il numero di righe/colonne da aggiungere, scegliere dove aggiungerle: Sopra il cursore o Sotto il cursore e fare clic su OK. Elimina viene utilizzato per eliminare una riga, una colonna, una tabella o celle. Se si seleziona l'opzione Celle, si aprirà la finestra Elimina celle, in cui è possibile selezionare se si desidera Spostare le celle a sinistra, Elimina l'intera riga o Elimina l'intera colonna. Unisci celle è disponibile se sono selezionate due o più celle e viene utilizzata per unirle. È anche possibile unire le celle cancellando una linea tra di loro utilizzando lo strumento gomma. Per fare ciò, fare clic sull'icona Tabella nella barra degli strumenti superiore, scegliere l'opzione Cancella tabella. Il cursore del mouse si trasformerà nella gomma . Spostare il cursore del mouse sul bordo tra le celle da unire e cancellarlo. Dividi cella... viene utilizzata per aprire una finestra in cui è possibile selezionare il numero necessario di colonne e righe in cui verrà divisa la cella. È anche possibile dividere una cella disegnando righe o colonne utilizzando lo strumento matita. Per fare ciò, fare clic sull'icona Tabella nella barra degli strumenti superiore, scegliere l'opzione Disegna tabella. Il cursore del mouse si trasformerà nella matita . Disegnare una linea orizzontale per creare una riga o una linea verticale per creare una colonna. Distribuisci righe viene utilizzata per regolare le celle selezionate in modo che abbiano la stessa altezza senza modificare l'altezza complessiva della tabella. Distribuisci colonne viene utilizzata per regolare le celle selezionate in modo che abbiano la stessa larghezza senza modificare la larghezza complessiva della tabella. Allineamento verticale cella viene utilizzata per allineare il testo in alto, al centro o in basso nella cella selezionata. Direzione testo - viene utilizzata per modificare l'orientamento del testo in una cella. È possibile posizionare il testo orizzontalmente, verticalmente dall'alto verso il basso (Ruota testo in basso), o verticalmente dal basso verso l'alto (Ruota testo in alto). Tabella Impostazioni avanzate viene utilizzata per aprire la finestra 'Tabella - Impostazioni avanzate'. Collegamento ipertestuale viene utilizzato per inserire un collegamento ipertestuale. Impostazioni avanzate del paragrafo viene utilizzata per aprire la finestra 'Paragrafo - Impostazioni avanzate'. È possibile modificare le proprietà della tabella nella barra laterale destra: Righe e Colonne vengono utilizzate per selezionare le parti della tabella che si desidera evidenziare. Per le righe: Intestazione - per evidenziare la prima riga Totale - per evidenziare l'ultima riga A strisce - per evidenziare ogni altra riga Per le colonne: Prima - per evidenziare la prima colonna Ultima - per evidenziare l'ultima colonna A strisce - per evidenziare ogni altra colonna Seleziona da modello viene utilizzato per scegliere un modello di tabella tra quelli disponibili. Stile bordi viene utilizzato per selezionare la dimensione, il colore, lo stile e il colore di sfondo del bordo. Righe e colonne viene utilizzato per eseguire alcune operazioni con la tabella: selezionare, eliminare, inserire righe e colonne, unire celle, dividere una cella. Dimensione di righe e colonne viene utilizzato per regolare la larghezza e l'altezza della cella attualmente selezionata. In questa sezione, è anche possibile Distribuire righe in modo che tutte le celle selezionate abbiano la stessa altezza o Distribuire colonne in modo che tutte le celle selezionate abbiano la stessa larghezza. Aggiungi formula viene utilizzato per inserire una formula nella cella della tabella selezionata. Ripeti come riga di intestazione nella parte superiore di ogni pagina viene utilizzata per inserire la stessa riga di intestazione nella parte superiore di ogni pagina in tabelle lunghe. Mostra impostazioni avanzate viene utilizzato per aprire la finestra 'Tabella - Impostazioni avanzate'. Regolare le impostazioni avanzate della tabella Per modificare le proprietà avanzate della tabella, fare clic sulla tabella con il pulsante destro del mouse e selezionare l'opzione Impostazioni avanzate tabella dal menu di scelta rapida o utilizzare il collegamento Mostra impostazioni avanzate nella barra laterale destra. Si aprirà la finestra delle proprietà della tabella: La scheda Tabella consente di modificare le proprietà dell'intera tabella. La sezione Dimensioni tabella contiene i seguenti parametri: Larghezza - per impostazione predefinita, la larghezza della tabella viene regolata automaticamente per adattarsi alla larghezza della pagina, ovvero la tabella occupa tutto lo spazio tra il margine sinistro e destro della pagina. È possibile selezionare questa casella e specificare manualmente la larghezza della tabella necessaria. Misura in - consente di specificare se si desidera impostare la larghezza della tabella in unità assolute, ovvero Centimetri/Punti/Pollici (a seconda dell'opzione specificata nella scheda File -> Impostazioni avanzate...) o in Percentuale della larghezza complessiva della pagina. Nota: è anche possibile regolare le dimensioni della tabella modificando manualmente l'altezza della riga e la larghezza della colonna. Spostare il cursore del mouse sul bordo di una riga/colonna finché non si trasforma nella freccia bidirezionale e trascinare il bordo. È inoltre possibile utilizzare gli indicatori sul righello orizzontale per modificare la larghezza della colonna e gli indicatori sul righello verticale per modificare l'altezza della riga. Adatta automaticamente al contenuto - consente la modifica automatica della larghezza di ogni colonna in base al testo all'interno delle sue celle. La sezione Margini predefiniti delle celle consente di modificare lo spazio tra il testo all'interno delle celle e il bordo della cella utilizzato per impostazione predefinita. La sezione Opzioni consente di modificare il seguente parametro: Consenti spaziatura tra le celle - la spaziatura delle celle che verrà riempita con il colore di sfondo della tabella. La scheda Cella consente di modificare le proprietà delle singole celle. In primo luogo è necessario selezionare la cella a cui si desidera applicare le modifiche o selezionare l'intera tabella per modificare le proprietà di tutte le sue celle. La sezione Dimensioni cella contiene i seguenti parametri: Larghezza preferita - permette di impostare una larghezza di cella preferita. Questa è la dimensione a cui una cellula si sforza di adattarsi, ma in alcuni casi potrebbe non essere possibile adattarsi a questo valore esatto. Ad esempio, se il testo all'interno di una cella supera la larghezza specificata, verrà suddiviso nella riga successiva in modo che la larghezza della cella preferita rimanga invariata, ma se si inserisce una nuova colonna, la larghezza preferita verrà ridotta. Misura in - consente di specificare se si desidera impostare la larghezza della cella in unità assolute, ovvero Centimetri/Punti/Pollici (a seconda dell'opzione specificata nella scheda File -> Impostazioni avanzate...) o in Percentuale della larghezza complessiva della tabella. Nota: è anche possibile regolare manualmente la larghezza della cella. Per rendere una singola cella in una colonna più larga o più stretta della larghezza complessiva della colonna, selezionare la cella necessaria e spostare il cursore del mouse sul bordo destro fino a quando non si trasforma nella freccia bidirezionale, quindi trascinare il bordo. Per modificare la larghezza di tutte le celle in una colonna, utilizzare gli indicatori sul righello orizzontale per modificare la larghezza della colonna. La sezione Margini cella consente di regolare lo spazio tra il testo all'interno delle celle e il bordo della cella. Per impostazione predefinita, vengono utilizzati valori standard (i valori predefiniti possono essere modificati anche nella scheda Tabella), ma è possibile deselezionare la casella Utilizzaa margini predefiniti e immettere manualmente i valori necessari. La sezione Opzioni cella consente di modificare il seguente parametro: L’opzione Disponi testo è abilitata per impostazione predefinita. Consente di disporre il testo all'interno di una cella che supera la sua larghezza sulla riga successiva espandendo l'altezza della riga e mantenendo invariata la larghezza della colonna. La scheda Bordi e sfondo contiene i seguenti parametri: Parametri del bordo (dimensione, colore e presenza o assenza) - impostare le dimensioni del bordo, selezionare il colore e scegliere il modo in cui verrà visualizzato nelle celle. Nota: inel caso in cui si seleziona di non mostrare i bordi della tabella facendo clic sul pulsante o deselezionando manualmente tutti i bordi sul diagramma, saranno indicati da una linea tratteggiata nel documento. Per farli scomparire, fare clic sull'icona Caratteri non stampabili nella scheda Home della barra degli strumenti superiore e selezionare l'opzione Bordi tabella nascosti. Sfondo cella - il colore dello sfondo all'interno delle celle (disponibile solo se una o più celle sono selezionate o l'opzione Consenti spaziatura tra le celle è selezionata nella scheda Tabella). Sfondo tabella - il colore per lo sfondo della tabella o lo sfondo dello spazio tra le celle nel caso in cui l'opzione Consenti spaziatura tra le celle sia selezionata nella scheda Tabella. La scheda Posizione tabella è disponibile solo se è selezionata l'opzione Tabella dinamica nella scheda Disposizione testo e contiene i seguenti parametri: I parametri orizzontali includono l'allineamento della tabella (a sinistra, al centro, a destra) rispetto al margine, alla pagina o al testo, nonché la posizione della tabella a destra del margine, della pagina o del testo. I parametri verticali includono l'allineamento della tabella (in alto, al centro, in basso) rispetto al margine, alla pagina o al testo, nonché la posizione della tabella sotto il margine, la pagina o il testo. La sezione Opzioni consente di modificare i seguenti parametri: Sposta l'oggetto con il testo controlla se la tabella si sposta quando il testo in cui è inserita si sposta. Consenti sovrapposizione controlla se due tabelle vengono unite in una tabella di grandi dimensioni o sovrapposte se vengono trascinate l'una vicino all'altra nella pagina. La scheda Disposizione testo contiene i seguenti parametri: Stile di disposizione del testo - Tabella in linea o Tabella dinamica. Utilizzare l'opzione necessaria per modificare il modo in cui la tabella viene posizionata rispetto al testo: sarà una parte del testo (nel caso in cui si seleziona la tabella in linea) o bypassata da tutti i lati (se si seleziona la tabella dinamica). Dopo aver selezionato lo stile di disposizione, è possibile impostare parametri di disposizione aggiuntivi sia per le tabelle in linea che per le tabelle dinamica: Per la tabella in linea, è possibile specificare l'allineamento e il rientro da sinistra della tabella. Per la tabella dinamica, è possibile specificare la distanza dal testo e la posizione della tabella nella scheda Posizione tabella. La scheda Testo alternativo consente di specificare un Titolo e una Descrizione che verranno letti alle persone con disabilità visive o cognitive per aiutarle a capire meglio quali informazioni ci sono nella tabella."
    },
   {
        "id": "UsageInstructions/InsertTextObjects.htm", 
        "title": "Insert text objects", 
        "body": "To make your text more emphatic and draw attention to a specific part of the document, you can insert a text box (a rectangular frame that allows to enter text within it) or a Text Art object (a text box with a predefined font style and color that allows to apply some text effects). Add a text object You can add a text object anywhere on the page. To do that: switch to the Insert tab of the top toolbar, select the necessary text object type: to add a text box, click the Text Box icon at the top toolbar, then click where you want to insert the text box, hold the mouse button and drag the text box border to specify its size. When you release the mouse button, the insertion point will appear in the added text box, allowing you to enter your text. Note: it's also possible to insert a text box by clicking the Shape icon at the top toolbar and selecting the shape from the Basic Shapes group. to add a Text Art object, click the Text Art icon at the top toolbar, then click on the desired style template – the Text Art object will be added at the current cursor position. Select the default text within the text box with the mouse and replace it with your own text. click outside of the text object to apply the changes and return to the document. The text within the text object is a part of the latter (when you move or rotate the text object, the text moves or rotates with it). As an inserted text object represents a rectangular frame with text in it (Text Art objects have invisible text box borders by default) and this frame is a common autoshape, you can change both the shape and text properties. To delete the added text object, click on the text box border and press the Delete key on the keyboard. The text within the text box will also be deleted. Format a text box Select the text box clicking on its border to be able to change its properties. When the text box is selected, its borders are displayed as solid (not dashed) lines. to resize, move, rotate the text box use the special handles on the edges of the shape. to edit the text box fill, stroke, wrapping style or replace the rectangular box with a different shape, click the Shape settings icon on the right sidebar and use the corresponding options. to align the text box on the page, arrange text boxes as related to other objects, rotate or flip a text box, change a wrapping style or access the shape advanced settings, right-click on the text box border and use the contextual menu options. To learn more on how to arrange and align objects you can refer to this page. Format the text within the text box Click the text within the text box to be able to change its properties. When the text is selected, the text box borders are displayed as dashed lines. Note: it's also possible to change text formatting when the text box (not the text itself) is selected. In such a case, any changes will be applied to all the text within the text box. Some font formatting options (font type, size, color and decoration styles) can be applied to a previously selected portion of the text separately. To rotate the text within the text box, right-click the text, select the Text Direction option and then choose one of the available options: Horizontal (is selected by default), Rotate Text Down (sets a vertical direction, from top to bottom) or Rotate Text Up (sets a vertical direction, from bottom to top). To align the text vertically within the text box, right-click the text, select the Vertical Alignment option and then choose one of the available options: Align Top, Align Center or Align Bottom. Other formatting options that you can apply are the same as the ones for regular text. Please refer to the corresponding help sections to learn more about the necessary operation. You can: align the text horizontally within the text box adjust the font type, size, color, apply decoration styles and formatting presets set line spacing, change paragraph indents, adjust tab stops for the multi-line text within the text box insert a hyperlink You can also click the Text Art settings icon on the right sidebar and change some style parameters. Edit a Text Art style Select a text object and click the Text Art settings icon on the right sidebar. Change the applied text style selecting a new Template from the gallery. You can also change the basic style additionally by selecting a different font type, size etc. Change the font Fill. You can choose the following options: Color Fill - select this option to specify the solid color you want to fill the inner space of letters with. Click the colored box below and select the necessary color from the available color sets or specify any color you like: Gradient Fill - select this option to fill the letters with two colors which smoothly change from one to another. Style - choose one of the available options: Linear (colors change in a straight line i.e. along a horizontal/vertical axis or diagonally at a 45 degree angle) or Radial (colors change in a circular path from the center to the edges). Direction - choose a template from the menu. If the Linear gradient is selected, the following directions are available: top-left to bottom-right, top to bottom, top-right to bottom-left, right to left, bottom-right to top-left, bottom to top, bottom-left to top-right, left to right. If the Radial gradient is selected, only one template is available. Gradient - click on the left slider under the gradient bar to activate the color box which corresponds to the first color. Click on the color box on the right to choose the first color in the palette. Drag the slider to set the gradient stop i.e. the point where one color changes into another. Use the right slider under the gradient bar to specify the second color and set the gradient stop. Note: if one of these two options is selected, you can also set an Opacity level dragging the slider or entering the percent value manually. The default value is 100%. It corresponds to the full opacity. The 0% value corresponds to the full transparency. No Fill - select this option if you don't want to use any fill. Adjust the font Stroke width, color and type. To change the stroke width, select one of the available options from the Size dropdown list. The available options are: 0.5 pt, 1 pt, 1.5 pt, 2.25 pt, 3 pt, 4.5 pt, 6 pt. Alternatively, select the No Line option if you don't want to use any stroke. To change the stroke color, click on the colored box below and select the necessary color. To change the stroke type, select the necessary option from the corresponding dropdown list (a solid line is applied by default, you can change it to one of the available dashed lines). Apply a text effect by selecting the necessary text transformation type from the Transform gallery. You can adjust the degree of the text distortion by dragging the pink diamond-shaped handle."
    },
   {
        "id": "UsageInstructions/LineSpacing.htm", 
        "title": "Impostare l'interlinea del paragrafo", 
        "body": "Nell'Editor di documenti, è possibile impostare l'altezza della riga per le righe di testo all'interno del paragrafo, nonché i margini tra il paragrafo corrente e quello precedente o quello successivo. Per farlo, posizionare il cursore all'interno del paragrafo che interessa o selezionare diversi paragrafi con il mouse o tutto il testo nel documento premendo la combinazione di tasti Ctrl+A, utilizzare i campi corrispondenti nella barra laterale destra per ottenere i risultati desiderati: Interlinea - imposta l'altezza della riga per le righe di testo all'interno del paragrafo. È possibile scegliere tra tre opzioni: minima (imposta la spaziatura minima necessaria per adattarsi al carattere o all'immagine più grande sulla riga), multipla ((imposta l'interlinea che può essere espressa in numeri maggiori di 1), esatta (imposta l'interlinea fissa). È possibile specificare il valore necessario nel campo a destra. Spaziatura del paragrafo - impostare la quantità di spazio tra i paragrafi. Prima - impostare la quantità di spazio prima del paragrafo. Dopo - impostare la quantità di spazio dopo il paragrafo. Non aggiungere intervallo tra paragrafi dello stesso stile - seleziona questa casella nel caso in cui non sia necessario alcuno spazio tra i paragrafi dello stesso stile. Questi parametri sono disponibili anche nella finestra Paragrafo - Impostazioni avanzate. Per aprire la finestra Paragrafo - Impostazioni avanzate , fare clic con il pulsante destro del mouse sul testo e sceglere l'opzione Impostazioni avanzate del paragrafo dal menu o utilizzare l'opzione Mostra impostazioni avanzate nella barra laterale destra. Passare quindi alla scheda Rientri e spaziatura e passare alla sezione Spaziatura. Per modificare rapidamente l'interlinea del paragrafo corrente, è anche possibile utilizzare l'icona Interlinea paragrafo nella scheda Home della barra degli strumenti superiore selezionando il valore desiderato dall'elenco: 1.0, 1.15, 1.5, 2.0, 2.5 o 3.0 righe."
    },
   {
        "id": "UsageInstructions/NonprintingCharacters.htm", 
        "title": "Show/hide nonprinting characters", 
        "body": "Nonprinting characters help you edit a document. They indicate the presence of various types of formatting, but they do not print with the document, even when they are displayed on the screen. To show or hide nonprinting characters, click the Nonprinting characters icon at the Home tab of the top toolbar. Alternatively, you can use the Ctrl+Shift+Num8 key combination. Nonprinting characters include: Spaces Inserted when you press the Spacebar on the keyboard. It creates a space between characters. Tabs Inserted when you press the Tab key. It's used to advance the cursor to the next tab stop. Paragraph marks (i.e. hard returns) Inserted when you press the Enter key. It ends a paragraph and adds a bit of space after it. It contains information about the paragraph formatting. Line breaks (i.e. soft returns) Inserted when you use the Shift+Enter key combination. It breaks the current line and puts lines of text close together. Soft return is primarily used in titles and headings. Nonbreaking spaces Inserted when you use the Ctrl+Shift+Spacebar key combination. It creates a space between characters which can't be used to start a new line. Page breaks Inserted when you use the Breaks icon at the Insert or Layout tab of the top toolbar and then select the Insert Page Break option, or select the Page break before option in the right-click menu or advanced settings window. Section breaks Inserted when you use the Breaks icon at the Insert or Layout tab of the top toolbar and then select one of the Insert Section Break submenu options (the section break indicator differs depending on which option is selected: Next Page, Continuous Page, Even Page or Odd Page). Column breaks Inserted when you use the Breaks icon at the Insert or Layout tab of the top toolbar and then select the Insert Column Break option. End-of-cell and end-of row markers in tables These markers contain formatting codes for the individual cell and row, respectively. Small black square in the margin to the left of a paragraph It indicates that at least one of the paragraph options was applied, e.g. Keep lines together, Page break before. Anchor symbols They indicate the position of floating objects (those with a wrapping style other than Inline), e.g. images, autoshapes, charts. You should select an object to make its anchor visible."
    },
   {
        "id": "UsageInstructions/OpenCreateNew.htm", 
        "title": "Create a new document or open an existing one", 
        "body": "Per creare un nuovo documento Nell’Editor di Documenti Online clicca su File nella barra degli strumenti superiore, seleziona l’opzione Crea nuovo. Nell’Editor di Documenti Desktop Nella finestra principale del programma, dalla sezione Crea nuovo che trovi sulla barra degli strumenti a sinistra, seleziona la voce Documento. Un nuovo file verrà aperto in una nuova sheda. Quando tutte le modifiche sono state effettuate, clicca sull’icona Salva nell’angolo superiore sinistro, oppure clicca sulla voce di menù File e poi scegli Salva come dal menu a tendina. Nella finestra di gestione dei file, scegli la locazione, specifica il nome, scegli il formato desiderato in cui desideri salvare il tuo documento (DOCX, Modello di documento (DOTX), ODT, OTT, RTF, TXT, PDF or PDFA) e poi clicca su Salva. Per aprire un documento esistente Nell’Editor di Documenti Desktop Nella finestra principale del programma, seleziona nella barra a sinistra la voce di menù Apri file locale, Scegli il documento desiderato dalla finestra di gestione dei file e clicca su Apri. Puoi anche cliccare col tasto destro sul documento desiderato all’interno della finestra di gestione dei file e scegliere l’opzione Apri con per poi scegliere l’applicazione preferita dal menù. Se il tipo di file documento é già stato associato all’applicazione, puoi anche aprire il documento con un doppio click nella finestra di gestione dei file. Tutte le directory a cui hai accesso usando l’editor per desktop verranno visualizzate nell’elenco delle Cartelle recenti in modo da poter avere un rapido accesso in seguito. Cliccando sulla cartella, verranno visualizzati i file in essa contenuti. Per aprire un documento modificato recentemente Nell’Editor di Documenti Online clicca sulla voce File nella barra superiore dei menú seleziona l’opzione Apri recenti... scegli il documento desiderato dalla lista dei documenti modificati recentemente. Nell’Editor di Documenti Desktop Nella finestra principale del programma seleziona la voce File recenti nella barra a sinistra, Scegli il documento di cui hai bisogno dall’elenco dei file modificati recentemente. Per aprire la cartella dove sono locati i file in una nuova sheda del browser nella versione online , clicca sulla voce Apri cartella, , che nella versione per desktop si trova immediatamente a destra della testata dell’editor. Alternativamente puoi cliccare sulla sheda File nella barra superiore e selezionare la voce Apri cartella."
    },
   {
        "id": "UsageInstructions/PageBreaks.htm", 
        "title": "Inserire interruzioni di pagina", 
        "body": "Nell'Editor di documenti, è possibile aggiungere un'interruzione di pagina per iniziare una nuova pagina, inserire una pagina vuota e regolare le opzioni di impaginazione. Per inserire un'interruzione di pagina nella posizione corrente del cursore, fare clic sull'icona Interruzione di pagina o di sezione nella scheda Inserisci o Layout della barra degli strumenti superiore oppure fare clic sulla freccia accanto a questa icona e selezionare l'opzione Inserisci interruzione di pagina dal menu. È inoltre possibile utilizzare la combinazione di tasti Ctrl+Invio. Per inserire una pagina vuota nella posizione corrente del cursore, fare clic sull'icona Pagina vuota nella scheda Inserisci della barra degli strumenti superiore. In questo modo vengono inserite due interruzioni di pagina che creano una pagina vuota. Per inserire un'interruzione di pagina prima del paragrafo selezionato, ad esempio per iniziare questo paragrafo nella parte superiore di una nuova pagina: fare clic con il pulsante destro del mouse e selezionare l'opzione Anteponi Interruzione di pagina nel menu, oppure fare clic con il pulsante destro del mouse, seleziona l'opzione Impostazioni avanzate del paragrafo nel menu o utilizzare il link Mostra impostazioni avanzate nella barra laterale destra e selezionare la casella Anteponi interruzione di pagina nella scheda Interruzioni di riga e di pagina della finestra Paragrafo - Impostazioni avanzate aperta. Per mantenere le righe unite in modo che solo interi paragrafi vengano spostati nella nuova pagina (cioè non ci sarà alcuna interruzione di pagina tra le righe all'interno di un singolo paragrafo), fare clic con il pulsante destro del mouse e selezionare l'opzione Mantieni assieme le righe nel menu, oppure fare clic con il pulsante destro del mouse, selezionare l'opzione Impostazioni avanzate del paragrafo nel menu o utilizzare il link Mostra impostazioni avanzate nella barra laterale destra e selezionare la casella Mantieni assieme le righe nella scheda Interruzioni di riga e di pagina della finestra Paragrafo - Impostazioni avanzate aperta. La scheda Interruzioni di riga e di pagina della finestra Paragrafo - Impostazioni avanzate consente di impostare altre due opzioni di impaginazione: Mantieni con il successivo - viene utilizzato per evitare un'interruzione di pagina tra il paragrafo selezionato e quello successivo. Controllo righe isolate - è selezionato per impostazione predefinita e utilizzato per impedire la visualizzazione di una singola riga del paragrafo (la prima o l'ultima) nella parte superiore o inferiore della pagina."
    },
   {
        "id": "UsageInstructions/ParagraphIndents.htm", 
        "title": "Modificare i rientri del paragrafo", 
        "body": "Nell'Editor documenti, è possibile modificare l'offset della prima riga dalla parte sinistra della pagina e l'offset del paragrafo dai lati sinistro e destro della pagina. Per farlo, posiziona il cursore all'interno del paragrafo che ti interessa o seleziona diversi paragrafi con il mouse o tutto il testo nel documento premendo la combinazione di tasti Ctrl+A, fai clic con il pulsante destro del mouse e seleziona l'opzione Impostazioni avanzate del paragrafo dal menu o utilizza il collegamento Mostra impostazioni avanzate nella barra laterale destra, nella finestra Paragrafo - Impostazioni avanzate aperta, passare alla scheda Rientri e spaziatura e impostare i parametri necessari nella sezione Rientri: A sinistra - imposta l'offset del paragrafo dal lato sinistro della pagina specificando il valore numerico necessario, A destra - imposta l'offset del paragrafo dal lato destro della pagina specificando il valore numerico necessario, Speciale - imposta un rientro per la prima riga del paragrafo: seleziona la voce di menu corrispondente ((nssuno), Prima riga, Sporgente) e modifica il valore numerico predefinito specificato per Prima riga o Sospensione, fare clic sul pulsante OK. Per modificare rapidamente l'offset del paragrafo dal lato sinistro della pagina, è anche possibile utilizzare le rispettive icone nella scheda Home della barra degli strumenti superiore: Riduci rientro e Aumenta rientro . È inoltre possibile utilizzare il righello orizzontale per impostare i rientri. Selezionate i paragrafi necessari e trascinate gli indicatori di rientro lungo il righello. L'indicatore Rientro prima riga viene utilizzato per impostare l'offset dal lato sinistro della pagina per la prima riga del paragrafo. L'indicatore Rientro sporgente viene utilizzato per impostare l'offset dal lato sinistro della pagina per la seconda riga e tutte le righe successive del paragrafo. L'indicatore Rientro sinistro viene utilizzato per impostare l'intero offset del paragrafo dal lato sinistro della pagina. L'indicatore Rientro destro viene utilizzato per impostare l'offset del paragrafo dal lato destro della pagina."
    },
   {
        "id": "UsageInstructions/SavePrintDownload.htm", 
        "title": "Save/download/print your document", 
        "body": "Save/download/ print your document Saving By default, online Document Editor automatically saves your file each 2 seconds when you work on it preventing your data loss in case of the unexpected program closing. If you co-edit the file in the Fast mode, the timer requests for updates 25 times a second and saves the changes if they have been made. When the file is being co-edited in the Strict mode, changes are automatically saved at 10-minute intervals. If you need, you can easily select the preferred co-editing mode or disable the Autosave feature on the Advanced Settings page. To save your current document manually in the current format and location, press the Save icon in the left part of the editor header, or use the Ctrl+S key combination, or click the File tab of the top toolbar and select the Save option. Note: in the desktop version, to prevent data loss in case of the unexpected program closing you can turn on the Autorecover option at the Advanced Settings page. In the desktop version, you can save the document with another name, in a new location or format, click the File tab of the top toolbar, select the Save as... option, choose one of the available formats depending on your needs: DOCX, ODT, RTF, TXT, PDF, PDFA. You can also choose the Document template (DOTX or OTT) option. Downloading In the online version, you can download the resulting document onto your computer hard disk drive, click the File tab of the top toolbar, select the Download as... option, choose one of the available formats depending on your needs: DOCX, PDF, ODT, TXT, DOTX, PDF/A, OTT, RTF, HTML. Saving a copy In the online version, you can save a copy of the file on your portal, click the File tab of the top toolbar, select the Save Copy as... option, choose one of the available formats depending on your needs: DOCX, PDF, ODT, TXT, DOTX, PDF/A, OTT, RTF, HTML, select a location of the file on the portal and press Save. Printing To print out the current document, click the Print icon in the left part of the editor header, or use the Ctrl+P key combination, or click the File tab of the top toolbar and select the Print option. It's also possible to print a selected text passage using the Print Selection option from the contextual menu. In the desktop version, the file will be printed directly. In the online version, a PDF file will be generated on the basis of the document. You can open and print it out, or save onto your computer hard disk drive or removable medium to print it out later. Some browsers (e.g. Chrome and Opera) support direct printing."
    },
   {
        "id": "UsageInstructions/SectionBreaks.htm", 
        "title": "Insert section breaks", 
        "body": "Section breaks allow you to apply a different layout or formatting for the certain parts of your document. For example, you can use individual headers and footers, page numbering, footnotes format, margins, size, orientation, or column number for each separate section. Note: an inserted section break defines formatting of the preceding part of the document. To insert a section break at the current cursor position: click the Breaks icon at the Insert or Layout tab of the top toolbar, select the Insert Section Break submenu select the necessary section break type: Next Page - to start a new section from the next page Continuous Page - to start a new section at the current page Even Page - to start a new section from the next even page Odd Page - to start a new section from the next odd page Added section breaks are indicated in your document by a double dotted line: If you do not see the inserted section breaks, click the icon at the Home tab of the top toolbar to display them. To remove a section break select it with the mouse and press the Delete key. Since a section break defines formatting of the preceding section, when you remove a section break, this section formatting will also be deleted. The document part that preceded the removed section break acquires the formatting of the part that followed it."
    },
   {
        "id": "UsageInstructions/SetOutlineLevel.htm", 
        "title": "Impostare un livello di struttura del paragarfo", 
        "body": "Il livello di struttura indica il livello del paragrafo nella struttura del documento. Sono disponibili i seguenti livelli: Testo Base, Livello 1 - Livello 9. Il livello di struttura può essere specificato in diversi modi, ad esempio utilizzando gli stili d’intestazione: una volta assegnato uno stile di titolo (Titolo 1 - Titolo 9) ad un paragrafo, esso acquisisce un livello di struttura corrispondente. Se assegni un livello a un paragrafo utilizzando le impostazioni avanzate del paragrafo, il paragrafo acquisisce solo il livello della struttura mentre il suo stile rimane invariato. Il livello di struttura può anche essere modificato nel pannello di Navigazione a sinistra usando le opzioni del menu contestuale. Per modificare il livello di struttura di un paragrafo utilizzando le impostazioni avanzate del paragrafo, fai clic con il pulsante destro e seleziona l’opzione Impostazioni avanzate del paragrafo dal menu contestuale o utilizza l’opzione Mostra impostazioni avanzate nella barra laterale destra, apri la scheda Paragrafo - Impostazioni avanzate, passa alla scheda Rientri e spaziatura, seleziona il livello di struttura necessario dall’elenco Livelli di struttura. fai click sul pulsante OK per applicare le modifiche."
    },
   {
        "id": "UsageInstructions/SetPageParameters.htm", 
        "title": "Set page parameters", 
        "body": "To change page layout, i.e. set page orientation and size, adjust margins and insert columns, use the corresponding icons at the Layout tab of the top toolbar. Note: all these parameters are applied to the entire document. If you need to set different page margins, orientation, size, or column number for the separate parts of your document, please refer to this page. Page Orientation Change the current orientation type clicking the Orientation icon. The default orientation type is Portrait that can be switched to Album. Page Size Change the default A4 format clicking the Size icon and selecting the needed one from the list. The available preset sizes are: US Letter (21,59cm x 27,94cm) US Legal (21,59cm x 35,56cm) A4 (21cm x 29,7cm) A5 (14,81cm x 20,99cm) B5 (17,6cm x 25,01cm) Envelope #10 (10,48cm x 24,13cm) Envelope DL (11,01cm x 22,01cm) Tabloid (27,94cm x 43,17cm) AЗ (29,7cm x 42,01cm) Tabloid Oversize (30,48cm x 45,71cm) ROC 16K (19,68cm x 27,3cm) Envelope Choukei 3 (11,99cm x 23,49cm) Super B/A3 (33,02cm x 48,25cm) You can also set a special page size by selecting the Custom Page Size option from the list. The Page Size window will open where you'll be able to select the necessary Preset (US Letter, US Legal, A4, A5, B5, Envelope #10, Envelope DL, Tabloid, AЗ, Tabloid Oversize, ROC 16K, Envelope Choukei 3, Super B/A3, A0, A1, A2, A6) or set custom Width and Height values. Enter your new values into the entry fields or adjust the existing values using arrow buttons. When ready, click OK to apply the changes. Page Margins Change default margins, i.e. the blank space between the left, right, top and bottom page edges and the paragraph text, clicking the Margins icon and selecting one of the available presets: Normal, US Normal, Narrow, Moderate, Wide. You can also use the Custom Margins option to set your own values in the Margins window that opens. Enter the necessary Top, Bottom, Left and Right page margin values into the entry fields or adjust the existing values using arrow buttons. Gutter position is used to set up additional space on the left or top of the document. Gutter option might come in handy to make sure bookbinding does not cover text. In Margins window enter the necessary gutter position into the entry fields and choose where it should be placed in. Note: Gutter position function cannot be used when Mirror margins option is checked. In Multiple pages drop-down menu choose Mirror margins option to to set up facing pages for double-sided documents. With this option checked, Left and Right margins turn into Inside and Outside margins respectively. In Orientation drop-down menu choose from Portrait and Landscape options. All applied changes to the document will be displayed in the Preview window. When ready, click OK. The custom margins will be applied to the current document and the Last Custom option with the specified parameters will appear in the Margins list so that you can apply them to some other documents.</p> You can also change the margins manually by dragging the border between the grey and white areas on the rulers (the grey areas of the rulers indicate page margins): Columns Apply a multi-column layout clicking the Columns icon and selecting the necessary column type from the drop-down list. The following options are available: Two - to add two columns of the same width, Three - to add three columns of the same width, Left - to add two columns: a narrow column on the left and a wide column on the right, Right - to add two columns: a narrow column on the right and a wide column on the left. If you want to adjust column settings, select the Custom Columns option from the list. The Columns window will open where you'll be able to set necessary Number of columns (it's possible to add up to 12 columns) and Spacing between columns. Enter your new values into the entry fields or adjust the existing values using arrow buttons. Check the Column divider box to add a vertical line between the columns. When ready, click OK to apply the changes. To exactly specify where a new column should start, place the cursor before the text that you want to move into the new column, click the Breaks icon at the top toolbar and then select the Insert Column Break option. The text will be moved to the next column. Added column breaks are indicated in your document by a dotted line: . If you do not see the inserted column breaks, click the icon at the Home tab of the top toolbar to display them. To remove a column break select it with the mouse and press the Delete key. To manually change the column width and spacing, you can use the horizontal ruler. To cancel columns and return to a regular single-column layout, click the Columns icon at the top toolbar and select the One option from the list."
    },
   {
        "id": "UsageInstructions/SetTabStops.htm", 
        "title": "Impostare le tabulazioni", 
        "body": "Nell'Editor documenti è possibile modificare le tabulazioni, ovvero la posizione in cui il cursore avanza quando si preme il tasto Tab sulla tastiera. Per impostare le tabulazioni è possibile utilizzare il righello orizzontale: Selezionare il tipo di tabulazione necessario facendo clic sul pulsante nell'angolo superiore sinistro dell'area di lavoro. Sono disponibili i seguenti tre tipi di tabulazioni: Sinistra - allinea il testo sul lato sinistro nella posizione di tabulazione; il testo si sposta a destra dalla tabulazione durante la digitazione. Tale punto di tabulazione sarà indicato sul righello orizzontale dal marcatore . Centrata - centra il testo nella posizione di tabulazione. Tale tabulazione sarà indicata sul righello orizzontale dal marcatore . Destra - allinea il testo dal lato destro nella posizione di tabulazione; il testo si sposta a sinistra dalla tabulazione durante la digitazione. Tale punto di tabulazione sarà indicato sul righello orizzontale dal marcatore . Fare clic sul bordo inferiore del righello in cui si desidera posizionare la tabulazione. Trascinarlo lungo il righello per modificarne la posizione. Per rimuovere la tabulazione aggiunta, trascinarla fuori dal righello. È inoltre possibile utilizzare la finestra delle proprietà del paragrafo per regolare le tabulazioni. Fare clic con il tasto destro del mouse, selezionare l'opzione Impostazioni avanzate del paragrafo nel menu o utilizzare il collegamento Mostra impostazioni avanzate nella barra laterale destra e passare alla scheda Tabulazioni nella finestra Paragrafo - Impostazioni avanzate aperta. È possibile impostare i seguenti parametri: Tabulazione predefinita è impostata su 1,25 cm. È possibile ridurre o aumentare questo valore utilizzando i pulsanti freccia o immettere quello necessario nella casella. Posizione tabulazione - consente di impostare tabulazioni personalizzate. Immettere il valore necessario in questa casella, regolarlo in modo più preciso utilizzando i pulsanti freccia e premere il pulsante Specifica. La posizione di tabulazione personalizzata verrà aggiunta all'elenco nel campo sottostante. Se in precedenza sono state aggiunte alcune tabulazioni utilizzando il righello, tutte queste posizioni verranno visualizzate anche nell'elenco. Allineamento - consente di impostare il tipo di allineamento necessario per ciascuna delle posizioni di tabulazione nell'elenco precedente. Selezionare la posizione di tabulazione desiderata nell'elenco, scegliere l'opzione Sinistra, Centrata o Destra dall'elenco a discesa e premere il pulsante Specifica. Leader - consente di scegliere un carattere utilizzato per creare una direttrice per ciascuna delle posizioni di tabulazione. Una direttrice è una riga di caratteri (punti o trattini) che riempie lo spazio tra le tabulazioni. Selezionare la posizione di tabulazione desiderata nell'elenco, scegliere il tipo di direttrice dall'elenco a discesa e premere il pulsante Specifica. Per eliminare i punti di tabulazione dall'elenco selezionare un punto di tabulazione e premere il pulsante Elimina o Elimina tutto."
    },
   {
        "id": "UsageInstructions/UseMailMerge.htm", 
        "title": "Use Mail Merge", 
        "body": "Note: this option is available in the online version only. The Mail Merge feature is used to create a set of documents combining a common content which is taken from a text document and some individual components (variables, such as names, greetings etc.) taken from a spreadsheet (for example, a customer list). It can be useful if you need to create a lot of personalized letters and send them to recipients. To start working with the Mail Merge feature, Prepare a data source and load it to the main document A data source used for the mail merge must be an .xlsx spreadsheet stored on your portal. Open an existing spreadsheet or create a new one and make sure that it meets the following requirements. The spreadsheet should have a header row with the column titles, as values in the first cell of each column will designate merge fields (i.e. variables that you can insert into the text). Each column should contain a set of actual values for a variable. Each row in the spreadsheet should correspond to a separate record (i.e. a set of values that belongs to a certain recipient). During the merge process, a copy of the main document will be created for each record and each merge field inserted into the main text will be replaced with an actual value from the corresponding column. If you are goung to send results by email, the spreadsheet must also include a column with the recipients' email addresses. Open an existing text document or create a new one. It must contain the main text which will be the same for each version of the merged document. Click the Mail Merge icon at the Home tab of the top toolbar. The Select Data Source window will open. It displays the list of all your .xlsx spreadsheets stored in the My Documents section. To navigate between other Documents module sections use the menu in the left part of the window. Select the file you need and click OK. Once the data source is loaded, the Mail Merge setting tab will be available on the right sidebar. Verify or change the recipients list Click the Edit recipients list button on the top of the right sidebar to open the Mail Merge Recipients window, where the content of the selected data source is displayed. Here you can add new information, edit or delete the existing data, if necessary. To simplify working with data, you can use the icons on the top of the window: and - to copy and paste the copied data and - to undo and redo undone actions and - to sort your data within a selected range of cells in ascending or descending order - to enable the filter for the previously selected range of cells or to remove the applied filter - to clear all the applied filter parameters Note: to learn more on how to use the filter you can refer to the Sort and filter data section of the Spreadsheet Editor help. - to search for a certain value and replace it with another one, if necessary Note: to learn more on how to use the Find and Replace tool you can refer to the Search and Replace Functions section of the Spreadsheet Editor help. After all the necessary changes are made, click the Save & Exit button. To discard the changes, click the Close button. Insert merge fields and check the results Place the mouse cursor in the text of the main document where you want a merge field to be inserted, click the Insert Merge Field button at the right sidebar and select the necessary field from the list. The available fields correspond to the data in the first cell of each column of the selected data source. Add all the fields you need anywhere in the document. Turn on the Highlight merge fields switcher at the right sidebar to make the inserted fields more noticeable in the document text. Turn on the Preview results switcher at the right sidebar to view the document text with the merge fields replaced with actual values from the data source. Use the arrow buttons to preview versions of the merged document for each record. To delete an inserted field, disable the Preview results mode, select the field with the mouse and press the Delete key on the keyboard. To replace an inserted field, disable the Preview results mode, select the field with the mouse, click the Insert Merge Field button at the right sidebar and choose a new field from the list. Specify the merge parameters Select the merge type. You can start mass mailing or save the result as a file in the PDF or Docx format to be able to print or edit it later. Select the necessary option from the Merge to list: PDF - to create a single document in the PDF format that includes all the merged copies so that you can print them later Docx - to create a single document in the Docx format that includes all the merged copies so that you can edit individual copies later Email - to send the results to recipients by email Note: the recipients' email addresses must be specified in the loaded data source and you need to have at least one email account connected in the Mail module on your portal. Choose the records you want to apply the merge to: All records (this option is selected by default) - to create merged documents for all records from the loaded data source Current record - to create a merged document for the record that is currently displayed From ... To - to create merged documents for a range of records (in this case you need to specify two values: the number of the first record and the last record in the desired range) Note: the maximum allowed quantity of recipients is 100. If you have more than 100 recipients in your data source, please, perform the mail merge by stages: specify the values from 1 to 100, wait until the mail merge process is over, then repeat the operation specifying the values from 101 to N etc. Complete the merge If you've decided to save the merge results as a file, click the Download button to store the file anywhere on your PC. You'll find the downloaded file in your default Downloads folder. click the Save button to save the file on your portal. In the Folder for save window that opens, you can change the file name and specify the folder where you want to save the file. You can also check the Open merged document in new tab box to check the result once the merge process is finished. Finally, click Save in the Folder for save window. If you've selected the Email option, the Merge button will be available on the right sidebar. After you click it, the Send to Email window will open: In the From list, select the mail account you want to use for sending mail, if you have several accounts connected in the Mail module. In the To list, select the merge field corresponding to email addresses of the recipients, if it was not selected automatically. Enter your message subject in the Subject Line field. Select the mail format from the list: HTML, Attach as DOCX or Attach as PDF. When one of the two latter options is selected, you also need to specify the File name for attachments and enter the Message (the text of your letter that will be sent to recipients). Click the Send button. Once the mailing is over you'll receive a notification to your email specified in the From field."
    },
   {
        "id": "UsageInstructions/ViewDocInfo.htm", 
        "title": "View document information", 
        "body": "To access the detailed information about the currently edited document, click the File tab of the top toolbar and select the Document Info... option. General Information The document information includes a number of the file properties which describe the document. Some of these properties are updated automatically, and some of them can be edited. Location - the folder in the Documents module where the file is stored. Owner - the name of the user who have created the file. Uploaded - the date and time when the file has been created. These properties are available in the online version only. Statistics - the number of pages, paragraphs, words, symbols, symbols with spaces. Title, Subject, Comment - these properties allow to simplify your documents classification. You can specify the necessary text in the properties fields. Last Modified - the date and time when the file was last modified. Last Modified By - the name of the user who have made the latest change in the document if a document has been shared and it can be edited by several users. Application - the application the document was created with. Author - the person who have created the file. You can enter the necessary name in this field. Press Enter to add a new field that allows to specify one more author. If you changed the file properties, click the Apply button to apply the changes. Note: Online Editors allow you to change the document name directly from the editor interface. To do that, click the File tab of the top toolbar and select the Rename... option, then enter the necessary File name in a new window that opens and click OK. Permission Information In the online version, you can view the information about permissions to the files stored in the cloud. Note: this option is not available for users with the Read Only permissions. To find out, who have rights to view or edit the document, select the Access Rights... option at the left sidebar. You can also change currently selected access rights by pressing the Change access rights button in the Persons who have rights section. Version History In the online version, you can view the version history for the files stored in the cloud. Note: this option is not available for users with the Read Only permissions. To view all the changes made to this document, select the Version History option at the left sidebar. It's also possible to open the history of versions using the Version History icon at the Collaboration tab of the top toolbar. You'll see the list of this document versions (major changes) and revisions (minor changes) with the indication of each version/revision author and creation date and time. For document versions, the version number is also specified (e.g. ver. 2). To know exactly which changes have been made in each separate version/revision, you can view the one you need by clicking it at the left sidebar. The changes made by the version/revision author are marked with the color which is displayed next to the author name on the left sidebar. You can use the Restore link below the selected version/revision to restore it. To return to the document current version, use the Close History option on the top of the version list. To close the File panel and return to document editing, select the Close Menu option."
    }
]