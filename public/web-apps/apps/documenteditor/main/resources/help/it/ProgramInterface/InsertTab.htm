﻿<!DOCTYPE html>
<html>
	<head>
		<title>Scheda Inserisci</title>
		<meta charset="utf-8" />
        <meta name="description" content="Presentazione dell'interfaccia utente dell'Editor di Documenti - Scheda Inserisci" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Scheda Inserisci</h1>
            <p>La scheda <b>Inser<PERSON>ci</b> consente di aggiungere alcuni elementi di formattazione della pagina, nonché oggetti visivi e commenti.</p>
            <div class="onlineDocumentFeatures">
                <p>Finestra <a href="https://www.onlyoffice.com/it/document-editor.aspx">dell’Editor di Documenti Online</a>:</p>
                <p><img alt="Scheda Inserisci" src="../images/interface/inserttab.png" /></p>
            </div>
            <div class="desktopDocumentFeatures">
                <p>Finestra dell’Editor di Documenti Desktop:</p>
                <p><img alt="Scheda Inserisci" src="../images/interface/desktop_inserttab.png" /></p>
            </div>
            <p>Usando questa scheda, puoi:</p>
            <ul>
                <li>inserire una <a href="../UsageInstructions/PageBreaks.htm" onclick="onhyperlinkclick(this)">pagina vuota</a>,</li>
                <li>inserire <a href="../UsageInstructions/PageBreaks.htm" onclick="onhyperlinkclick(this)">interruzioni di pagina</a>, <a href="../UsageInstructions/SectionBreaks.htm" onclick="onhyperlinkclick(this)"> interruzioni di sezione</a> e <a href="../UsageInstructions/SetPageParameters.htm#columns" onclick="onhyperlinkclick(this)">interruzioni di colonna</a>,</li>
                <li>inserire <a href="../UsageInstructions/InsertHeadersFooters.htm" onclick="onhyperlinkclick(this)">intestazioni e piè di pagina</a> e <a href="../UsageInstructions/InsertPageNumbers.htm" onclick="onhyperlinkclick(this)">numeri di pagina</a>,</li>
                <li>inserire <a href="../UsageInstructions/InsertTables.htm" onclick="onhyperlinkclick(this)">tabelle</a>, <a href="../UsageInstructions/InsertImages.htm" onclick="onhyperlinkclick(this)">immagini</a>, <a href="../UsageInstructions/InsertCharts.htm" onclick="onhyperlinkclick(this)">grafici</a>, <a href="../UsageInstructions/InsertAutoshapes.htm" onclick="onhyperlinkclick(this)">forme</a>,</li>
                <li>inserire <a href="../UsageInstructions/AddHyperlinks.htm" onclick="onhyperlinkclick(this)">collegamenti ipertestuali</a>, <a href="../HelpfulHints/CollaborativeEditing.htm#comments" onclick="onhyperlinkclick(this)">commenti</a>,</li>
                <li>inserire <a href="../UsageInstructions/InsertTextObjects.htm" onclick="onhyperlinkclick(this)">caselle di testo ed oggetti Text Art</a>, <a href="../UsageInstructions/InsertEquation.htm" onclick="onhyperlinkclick(this)">equazioni</a>, <a href="../UsageInstructions/InsertSymbols.htm" onclick="onhyperlinkclick(this)">simboli</a>, <a href="../UsageInstructions/InsertDropCap.htm" onclick="onhyperlinkclick(this)">capilettera</a>, <a href="../UsageInstructions/InsertContentControls.htm" onclick="onhyperlinkclick(this)">controlli del contenuto</a>.</li>
            </ul>
		</div>
	</body>
</html>