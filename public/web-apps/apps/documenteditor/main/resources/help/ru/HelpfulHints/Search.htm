<!DOCTYPE html>
<html>
	<head>
		<title>Функция поиска и замены</title>
		<meta charset="utf-8" />
        <meta name="description" content="Описание функции поиска по документу и замены в редакторе документов" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
			<div class="search-field">
				<input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
			</div>
			<h1>Функция поиска и замены</h1>
			<p>Чтобы найти нужные символы, слова или фразы, использованные в документе, который вы в данный момент редактируете, нажмите на значок <span class="icon icon-searchicon"></span>, расположенный на левой боковой панели, или значок <span class="icon icon-search_icon_header"></span>, расположенный в правом верхнем углу. Вы также можете использовать сочетание клавиш <em>Ctrl+F</em> (<em>Command+F</em> для MacOS), чтобы открыть маленькую панель поиска, или сочетание клавиш <em>Ctrl+H</em>, чтобы открыть расширенную панель поиска.</p>
            <p>В правом верхнем углу рабочей области откроется маленькая панель <b>Поиск</b>.</p>
            <p><img alt="Маленькая панель Поиск" src="../../../../../../common/main/resources/help/ru/images/find_small.png" /></p>
            <p>Чтобы открыть дополнительные параметры, нажмите значок <span class="icon icon-search_advanced"></span> или используйте сочетание клавиш <em>Ctrl+H</em>.</p>
            <p>Откроется панель <b>Поиск и замена</b>:</p>
			<p><img alt="Панель Поиск и замена" src="../images/search_window.png" /></p>
			<ol>
				<li>Введите запрос в соответствующее поле ввода данных <b>Поиск</b>.</li>
                <li>Если требуется <b>заменить</b> одно или более вхождений найденных символов, введите текст для замены в соответствующее поле ввода данных <b>Заменить на</b>. Вы можете заменить одно выделенное в данный момент вхождение или заменить все вхождения, нажав соответствующие кнопки <b>Заменить</b> или <b>Заменить все</b>.</li>
                <li>Для навигации по найденным вхождениям нажмите одну из кнопок со стрелками. Кнопка <div class="icon icon-searchdownbutton"></div> показывает следующее вхождение, а кнопка <div class="icon icon-searchupbutton"></div> показывает предыдущее.</li>
				<li>
					Задайте параметры поиска, отметив нужные опции, расположенные под полями ввода:
					<ul>
						<li><b>С учетом регистра</b> - используется для поиска только тех вхождений, которые набраны в таком же регистре, что и ваш запрос, (например, если Вы ввели запрос 'Редактор', такие слова, как 'редактор' или 'РЕДАКТОР' и т.д. не будут найдены).</li>
						<li><b>Только слово целиком</b> - используется для подсветки только слов целиком.</li>
					</ul>
				</li>
			</ol>
			<p>Все вхождения будут подсвечены в файле и показаны в виде списка на панели <b>Поиск</b> слева. Используйте список для перехода к нужному вхождению или используйте кнопки навигации <span class="icon icon-searchupbutton"></span> и <span class="icon icon-searchdownbutton"></span>.</p>			
			<p><b>Редактор документов</b> поддерживает поиск специальных символов. Чтобы найти специальный символ, введите его в поле поиска.</p>
			<details class="details-example">
				<summary>Список специальных символов, которые можно использовать в поиске</summary>
				<table>
					<tr>
						<td><b>Специальный символ</b></td>
						<td><b>Описание</b></td>
					</tr>
					<tr>
						<td>^l</td>
						<td>Разрыв строки</td>
					</tr>
					<tr>
						<td>^t</td>
						<td>Позиция табуляции</td>
					</tr>
					<tr>
						<td>^?</td>
						<td>Любой символ</td>
					</tr>
					<tr>
						<td>^#</td>
						<td>Любая цифра</td>
					</tr>
					<tr>
						<td>^$</td>
						<td>Любая буква</td>
					</tr>
					<tr>
						<td>^n</td>
						<td>Разрыв колонки</td>
					</tr>
					<tr>
						<td>^e</td>
						<td>Концевая сноска</td>
					</tr>
					<tr>
						<td>^f</td>
						<td>Сноска</td>
					</tr>
					<tr>
						<td>^g</td>
						<td>Графический элемент</td>
					</tr>
					<tr>
						<td>^m</td>
						<td>Разрыв страницы</td>
					</tr>
					<tr>
						<td>^~</td>
						<td>Неразрывный дефис</td>
					</tr>
					<tr>
						<td>^s</td>
						<td>Неразрывный пробел</td>
					</tr>
					<tr>
						<td>^^</td>
						<td>Символ Крышечка</td>
					</tr>
					<tr>
						<td>^w</td>
						<td>Любой пробел</td>
					</tr>
					<tr>
						<td>^+</td>
						<td>Тире</td>
					</tr>
					<tr>
						<td>^=</td>
						<td>Дефис</td>
					</tr>
					<tr>
						<td>^y</td>
						<td>Любая черточка</td>
					</tr>
				</table>
			</details>
			<details class="details-example">
				<summary>Специальные символы, которые также можно использовать для замены:</summary>
				<table>
					<tr>
						<td><b>Специальный символ</b></td>
						<td><b>Описание</b></td>
					</tr>
					<tr>
						<td>^l</td>
						<td>Разрыв строки</td>
					</tr>
					<tr>
						<td>^t</td>
						<td>Позиция табуляции</td>
					</tr>
					<tr>
						<td>^n</td>
						<td>Разрыв колонки</td>
					</tr>
					<tr>
						<td>^m</td>
						<td>Разрыв страницы</td>
					</tr>
					<tr>
						<td>^~</td>
						<td>Неразрывный дефис</td>
					</tr>
					<tr>
						<td>^s</td>
						<td>Неразрывный пробел</td>
					</tr>
					<tr>
						<td>^+</td>
						<td>Тире</td>
					</tr>
					<tr>
						<td>^=</td>
						<td>Дефис</td>
					</tr>
				</table>
			</details>
		</div>
	</body>
</html>