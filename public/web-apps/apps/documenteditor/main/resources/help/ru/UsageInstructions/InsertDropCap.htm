<!DOCTYPE html>
<html>
	<head>
		<title>Вставка буквицы</title>
		<meta charset="utf-8" />
		<meta name="description" content="Вставьте буквицу и настройте свойства ее рамки, чтобы сделать документ более выразительным." />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Вставка буквицы</h1>
			<p><b>Буквица</b> - это первая буква абзаца, которая намного больше всех остальных и занимает в высоту несколько строк.</p>
			<p>Для добавления буквицы:</p>
			<ol>
				<li>установите курсор в пределах нужного абзаца,</li>
                <li>перейдите на вкладку <b>Вставка</b> верхней панели инструментов,</li>
                <li>нажмите на значок <div class = "icon icon-insert_dropcap_icon"></div> <b>Буквица</b> на верхней панели инструментов,</li>
				<li>в открывшемся выпадающем списке выберите нужную опцию:
				    <ul>
				    <li><b>В тексте</b> <div class = "icon icon-dropcap_text"></div> - чтобы поместить буквицу внутри абзаца.</li>
				    <li><b>На поле</b> <div class = "icon icon-dropcap_margin"></div> - чтобы поместить буквицу на левом поле.</li>
				    </ul>
				</li>
			</ol>
			<p><img class="floatleft" alt="Пример буквицы" src="../images/dropcap_example.png" />Первый символ выбранного абзаца будет преобразован в буквицу. Если требуется, чтобы буквица включала в себя еще несколько символов, добавьте их вручную: выделите буквицу и впишите нужные буквы.</p>
			<p>Чтобы изменить вид буквицы (то есть размер, тип, стиль оформления или цвет шрифта), выделите букву и используйте соответствующие значки на вкладке <b>Главная</b> верхней панели инструментов.</p>
			<p>Когда буквица выделена, она окружена <b>рамкой</b> (рамка - это контейнер, который используется для позиционирования буквицы на странице). Можно быстро изменить размер рамки путем перетаскивания ее границ или изменить ее положение с помощью значка <span class = "icon icon-arrow"></span>, который появляется после наведения курсора мыши на рамку.</p>
			<p>Чтобы удалить добавленную буквицу, выделите ее, нажмите на значок <span class = "icon icon-insert_dropcap_icon"></span> <b>Буквица</b> на вкладке <b>Вставка</b> верхней панели инструментов и выберите из выпадающего списка опцию <b>Нет</b> <span class = "icon icon-dropcap_none"></span>.</p>
			<hr />
			<p>Чтобы настроить параметры добавленной буквицы, выделите ее, нажмите на значок <span class = "icon icon-insert_dropcap_icon"></span> <b>Буквица</b> на вкладке <b>Вставка</b> верхней панели инструментов и выберите из выпадающего списка опцию <b>Параметры буквицы</b>. Откроется окно <b>Буквица - дополнительные параметры</b>:</p>
            <p><img alt="Буквица - дополнительные параметры" src="../images/dropcap_properties_1.png" /></p>
			<p>На вкладке <b>Буквица</b> можно задать следующие параметры:</p>
			    <ul>
			    <li><b>Положение</b> - используется, чтобы изменить расположение буквицы. Выберите опцию <b>В тексте</b> или <b>На поле</b> или нажмите на значок <b>Нет</b>, чтобы удалить буквицу.</li>
			    <li><b>Шрифт</b> - используется, чтобы выбрать шрифт из списка доступных.</li>
			    <li><b>Высота в строках</b> - используется, чтобы указать, сколько строк должна занимать буквица. Можно выбрать значение от 1 до 10.</li>
			    <li><b>Расстояние до текста</b> - используется, чтобы указать величину свободного пространства между текстом абзаца и правым краем рамки, окружающей буквицу.</li>
			    </ul>
            <p><img alt="Буквица - дополнительные параметры" src="../images/dropcap_properties_2.png" /></p>
			<p>На вкладке <b>Границы и заливка</b> можно добавить вокруг буквицы границы и настроить их параметры. Это следующие параметры:</p>
			<ul>
				<li>Параметры <b>Границ</b> (ширина, цвет и наличие и отсутствие) - задайте толщину границ, выберите их цвет и укажите, к каким границам (верхней, нижней, левой, правой или их сочетанию) надо применить эти параметры.</li>
				<li><b>Цвет фона</b> - выберите цвет фона буквицы.</li>
			</ul>
            <p><img alt="Буквица - дополнительные параметры" src="../images/dropcap_properties_3.png" /></p>
			<p>На вкладке <b>Поля</b> можно задать расстояние между буквицей и границами <b>сверху</b>, <b>снизу</b>, <b>слева</b> и <b>справа</b> вокруг нее (если границы были предварительно добавлены).</p>
			<hr />
			<p>После добавления буквицы можно также изменить параметры <b>Рамки</b>. Чтобы получить к ним доступ, щелкните правой кнопкой мыши внутри рамки и выберите в контекстном меню пункт <b>Дополнительные параметры рамки</b>. Откроется окно <b>Рамка - дополнительные параметры</b>:</p> 
            <p><img alt="Рамка - дополнительные параметры" src="../images/frame_properties_1.png" /></p>
			<p>На вкладке <b>Рамка</b> можно задать следующие параметры:</p>
			<ul>
				<li><b>Положение</b> -  используется, чтобы выбрать <b>Встроенный</b> или <b>Плавающий</b> стиль обтекания текстом. Или можно нажать на значок <b>Нет</b>, чтобы удалить рамку.</li>
				<li><b>Ширина</b> и <b>Высота</b> - используются, чтобы изменить размеры рамки. Параметр <b>Авто</b> позволяет автоматически корректировать размер рамки в соответствии с размером буквицы в ней. Параметр <b>Точно</b> позволяет задать фиксированные значения. Параметр <b>Минимум</b> используется, чтобы задать минимальное значение высоты (при изменении размера буквицы высота рамки изменяется соответственно, но не может быть меньше указанного значения).</li>
				<li>Параметры <b>По горизонтали</b> используются или для того, чтобы задать точное <b>положение</b> рамки в выбранных единицах измерения <b>относительно</b> поля, страницы или столбца, или для того, чтобы выровнять рамку (слева, по центру или справа) <b>относительно</b> одной из этих опорных точек. Можно также задать <b>Расстояние до текста</b> по горизонтали, то есть величину свободного пространства между вертикальными границами рамки и текстом абзаца.</li>
				<li>Параметры <b>По вертикали</b> используются или для того, чтобы задать точное <b>положение</b> рамки в выбранных единицах измерения <b>относительно</b> поля, страницы или абзаца, или для того, чтобы выровнять рамку (сверху, по центру или снизу) <b>относительно</b> одной из этих опорных точек. Можно также задать <b>Расстояние до текста</b> по вертикали, то есть величину свободного пространства между горизонтальными границами рамки и текстом абзаца.</li>
				<li><b>Перемещать с текстом</b> - определяет, будет ли перемещаться рамка при перемещении абзаца, к которому она привязана.</li>
			</ul>
			<!--<img alt="Frame - Advanced Settings" src="../images/Frame_properties_2.png" />-->
			<p>На вкладках <b>Границы и заливка</b> и <b>Поля</b> можно задать те же самые параметры, что и на одноименных вкладках в окне <b>Буквица - дополнительные параметры</b>.</p>
			<!--<img alt="Frame - Advanced Settings" src="../images/Frame_properties_3.png" />
			<p>The <b>Margins</b> tab allows to set just the same parameters as at the tab of the same name in the <b>Drop Cap - Advanced Settings</b> window.</p>-->
		</div>
	</body>
</html>