<!DOCTYPE html>
<html>
<head>
    <title>Добавление названия</title>
    <meta charset="utf-8" />
    <meta name="description" content=">Название - это пронумерованная метка, которую можно применять к объектам, таким как уравнения, таблицы, рисунки и изображения" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
       <div class="mainpart">
           <div class="search-field">
               <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
           </div>
           <h1>Добавление названия</h1>
           <p><b>Название</b> - это пронумерованная метка, которую можно применять к объектам, таким как уравнения, таблицы, фигуры и изображения.</p>
           <p>Данная функция позволяет легко ссылаться на какой-либо объект в вашем тексте, так как на на нем есть легко узнаваемая подпись.</p>
           <p>Чтобы добавить <b>Название</b> к объекту, выполните следующие действия:</p>
           <ul>
               <li>выберите объект, к которому нужно добавить название;</li>
               <li>перейдите на вкладку <b>Ссылки</b> верхней панели инструментов;</li>
               <li>
                   щелкните на иконку <div class = "icon icon-caption_icon"></div> <b>Название</b> на верхней панели инструментов или щелкнув правой кнопкой по объекту, выберите функцию <b>Вставить название</b>. Откроется диалоговое окно <b>Вставить название</b>
                   <ul>
                       <li>выберите подпись для названия, щелкнув на раскрывающийся список и выбрав один из предложенных вариантов, или</li>
                       <li>создайте новую подпись, нажав кнопку <b>Добавить</b>. В диалоговом окне введите новую подпись в текстовое поле. Затем нажмите кнопку <b>ОК</b>, чтобы добавить новую <b>Подпись</b> в список подписей;</li>
                   </ul>
               <li>в выпадающем списке <b>Вставить</b> выберите <b>Перед</b>, чтобы разместить название над объектом, или <b>После</b>, чтобы разместить его под объектом;</li>
               <li>установите флажок напротив <b>Исключить подпись из названия</b>, чтобы оставить только номер для этой конкретной подписи в соответствии с порядковым номером;</li>
               <li>установите флажок напротив опции <b>Включить номер главы</b>, чтобы добавить выбранный тип нумерации объектов к названию;</li>
               <li>настройте нумерацию подписи, добавив к ней определенный стиль и разделитель;</li>
               <li>чтобы применить название, нажмите кнопку <b>ОК</b>.</li>
           </ul>
           <p><img alt="Content Control settings window" src="../images/insertcaptionbox.png" /></p>
           <h2>Удаление подписи</h2>
           <p>Чтобы удалить созданную вами подпись, выберите подпись из списка, затем нажмите кнопку <b>Удалить</b>. Данная подпись будет немедленно удалена.</p>
           <p class="note"><b>Примечание:</b> вы не можете удалить созданные по умолчанию подписи.</p>
           <h2>Форматирование названий</h2>
           <p>Как только вы добавляете название, в раздел стилей автоматически добавляется новый стиль для подписей. Чтобы изменить стиль для всех названий в документе, выполните следующие действия:</p>
           <ul>
               <li>выделите текст из которого будет скопирован новый стиль для названий;</li>
               <li>найдите стиль <b>Название</b> (по умолчанию выделен синим цветом) в галерее стилей, которую вы можете найти на вкладке <b>Главная</b> верхней панели инструментов;</li>
               <li>щелкните по нему <b>правой кнопкой</b> мыши и выберите пункт <b>Обновить из выделенного фрагмента</b>.</li>
           </ul>
           <p><img alt="Content Control settings window" src="../images/updatefromseleciton.png" /></p>
           <h2>Объединение названий в группы</h2>
           <p>Если вы хотите иметь возможность перемещать объект и подпись как одно целое, вам нужно  <a href="../UsageInstructions/AlignArrangeObjects.htm" onclick="onhyperlinkclick(this)">сгруппировать</a> объект и подпись вместе. Для этого</p>
           <ul>
               <li>выберите объект;</li>
               <li>выберите один из <b>Стилей обтекания</b>, используя правую боковую панель;</li>
               <li>добавьте подпись, как указано выше;</li>
               <li>зажмите Shift и выберите элементы, которые вы хотите сгруппировать;</li>
               <li>щелкните <b>правой кнопкой</b> мыши любой элемент и выберите <b>Порядок</b> > <b>Сгруппировать</b>.</li>
           </ul>
           <p><img alt="Content Control settings window" src="../images/groupup.png" /></p>
           <p>Теперь если вы захотите перетащить объекты в другое место документа, они будут перемещаться одновременно.</p>
           <p>Чтобы отменить привязку объектов, в контекстном меню выберите пункт <b>Порядок</b>, а затем <b>Разгруппировать</b>.</p>
       </div>
   </body>
</html>