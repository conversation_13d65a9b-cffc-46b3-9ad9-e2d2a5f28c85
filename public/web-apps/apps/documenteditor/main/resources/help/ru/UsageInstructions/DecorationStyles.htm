<!DOCTYPE html>
<html>
	<head>
		<title>Применение стилей оформления шрифта</title>
		<meta charset="utf-8" />
		<meta name="description" content="Применяйте стили оформления шрифта: увеличение или уменьшение размера, жирный шрифт, курсив, подчеркнутый, зачеркнутый, надстрочные и подстрочные знаки" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Применение стилей оформления шрифта</h1>
			<p>Вы можете применять различные стили оформления шрифта, используя соответствующие значки, расположенные на вкладке <b>Главная</b> верхней панели инструментов.</p>
			<p class="note"><b>Примечание</b>: если требуется отформатировать текст, который уже есть в документе, выделите его мышью или <a href="../HelpfulHints/KeyboardShortcuts.htm#textselection" onclick="onhyperlinkclick(this)">с помощью клавиатуры</a>, а затем примените форматирование.</p>
			<table>
				<tr>
					<td>Полужирный</td>
					<td><div class = "icon icon-bold"></div></td>
					<td>Используется для придания шрифту большей насыщенности.</td>
				</tr>
				<tr>
					<td>Курсив</td>
					<td><div class = "icon icon-italic"></div></td>
					<td>Используется для придания шрифту наклона вправо.</td>
				</tr>
				<tr>
					<td>Подчеркнутый</td>
					<td><div class = "icon icon-underline"></div></td>
					<td>Используется для подчеркивания текста чертой, проведенной под буквами.</td>
				</tr>
				<tr>
					<td>Зачеркнутый</td>
					<td><div class = "icon icon-strike"></div></td>
					<td>Используется для зачеркивания текста чертой, проведенной по буквам.</td>
				</tr>
				<tr>
					<td>Надстрочные знаки</td>
					<td><div class = "icon icon-sup"></div></td>
					<td>Используется, чтобы сделать текст мельче и поместить его в верхней части строки, например, как в дробях.</td>
				</tr>
				<tr>
					<td>Подстрочные знаки</td>
					<td><div class = "icon icon-sub"></div></td>
					<td>Используется, чтобы сделать текст мельче и поместить его в нижней части строки, например, как в химических формулах.</td>
				</tr>
			</table>
			<p>Для получения доступа к дополнительным настройкам шрифта щелкните правой кнопкой мыши и выберите в меню пункт <b>Дополнительные параметры абзаца</b> или используйте ссылку <b>Дополнительные параметры</b> на правой боковой панели. Откроется окно <b>Абзац - дополнительные параметры</b>, в котором необходимо переключиться на вкладку <b>Шрифт</b>.</p>
			<p>Здесь можно применить следующие стили оформления и настройки шрифта:</p>
			<ul>
				<li><b>Зачёркивание</b> - используется для зачеркивания текста чертой, проведенной по буквам.</li>
				<li><b>Двойное зачёркивание</b> - используется для зачеркивания текста двойной чертой, проведенной по буквам.</li>
				<li><b>Надстрочные</b> - используется, чтобы сделать текст мельче и поместить его в верхней части строки, например, как в дробях.</li>
				<li><b>Подстрочные</b> - используется, чтобы сделать текст мельче и поместить его в нижней части строки, например, как в химических формулах.</li>
				<li><b>Малые прописные</b> - используется, чтобы сделать все буквы строчными.</li>
				<li><b>Все прописные</b> - используется, чтобы сделать все буквы прописными.</li>
				<li><b>Интервал</b> - используется, чтобы задать расстояние между символами. Увеличьте значение, заданное по умолчанию, чтобы применить <b>Разреженный</b> интервал, или уменьшите значение, заданное по умолчанию, чтобы применить <b>Уплотненный</b> интервал. Используйте кнопки со стрелками или введите нужное значение в поле ввода.</li>
                <li><b>Положение</b> - используется, чтобы задать положение символов в строке (смещение по вертикали). Увеличьте значение, заданное по умолчанию, чтобы сместить символы вверх, или уменьшите значение, заданное по умолчанию, чтобы сместить символы вниз. Используйте кнопки со стрелками или введите нужное значение в поле ввода.</li>
                <li>
                    <b>Лигатуры</b> - это объединенные буквы слова, набранного любым из шрифтов OpenType. Обратите внимание, что использование лигатур может нарушить межсимвольный интервал. Доступны следующие параметры лигатур:
                    <ul>
                        <li><em>Нет</em></li>
                        <li><em>Только стандартные</em> (включает “fi”, “fl”, “ff”; повышает удобочитаемость)</li>
                        <li><em>Контекстные</em> (лигатуры применяются на основе окружающих букв; повышает удобочитаемость)</li>
                        <li><em>Исторические</em> (лигатуры с большим числом перепадов и кривых линий; снижает удобочитаемость)</li>
                        <li><em>Дискреционные</em> (декоративные лигатуры; снижает удобочитаемость)</li>
                        <li><em>Стандартные и контекстные</em></li>
                        <li><em>Стандартные и исторические</em></li>
                        <li><em>Контекстные и исторические</em></li>
                        <li><em>Стандартные и дискреционные</em></li>
                        <li><em>Контекстные и дискреционные</em></li>
                        <li><em>Исторические и дискреционные</em></li>
                        <li><em>Стандартные, контекстные и исторические</em></li>
                        <li><em>Стандартные, контекстные и дискреционные</em></li>
                        <li><em>Стандартные, исторические и дискреционные</em></li>
                        <li><em>Контекстные, исторические и дискреционные</em></li>
                        <li><em>Все</em></li>
                    </ul>
                    <p>Все изменения будут отображены в расположенном ниже поле предварительного просмотра.</p>
                </li>
			</ul>
			<p><img alt="Дополнительные параметры абзаца - Шрифт" src="../images/paradvsettings_font.png" /></p>
		</div>
	</body>
</html>