<!DOCTYPE html>
<html>
	<head>
		<title>Вставка уравнений</title>
		<meta charset="utf-8" />
		<meta name="description" content="Вставляйте уравнения и математические символы." />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Вставка уравнений</h1>
            <p>В <b>редакторе документов</b> вы можете создавать уравнения, используя встроенные шаблоны, редактировать их, вставлять специальные символы (в том числе математические знаки, греческие буквы, диакритические знаки и т.д.).</p>
            <h3>Добавление нового уравнения</h3>
            <p>Чтобы вставить уравнение из коллекции,</p>
            <ol>
                <li>установите курсор на нужной строке,</li>
                <li>перейдите на вкладку <b>Вставка</b> верхней панели инструментов,</li>
                <li>нажмите на стрелку рядом со значком <div class="icon icon-insertequationicon"></div> <b>Уравнение</b> на верхней панели инструментов,</li>
                <li>
                    выберите нужную категорию уравнений на панели инструментов над вставленным уравнением,
                    <p>или</p>
                    <p>выберите нужную категорию уравнений в открывшемся выпадающем списке.</p>
                    <p>В настоящее время доступны следующие категории: <em>Символы</em>, <em>Дроби</em>, <em>Индексы</em>, <em>Радикалы</em>, <em>Интегралы</em>, <em>Крупные операторы</em>, <em>Скобки</em>, <em>Функции</em>, <em>Диакритические знаки</em>, <em>Пределы и логарифмы</em>, <em>Операторы</em>, <em>Матрицы</em>,</p>
                </li>
                <li>нажмите на значок <b>Параметры уравнений</b> на панели инструментов над вставленным уравнением, чтобы открыть дополнительные настройки: <em>Юникод</em> или <em>LaTeX</em>, <em>Профессиональный</em> или <em>Линейный</em> и <em>Изменить на встроенный</em>,</li>
                <li>щелкните по определенному символу/уравнению в соответствующем наборе шаблонов.</li>
            </ol>
            <p><img alt="Панель инструментов уравнения" src="../images/equationtoolbar.png" /></p>
            <p>Выбранный символ или уравнение будут вставлены в позиции курсора. Если выбранная строка пуста, уравнение будет выровнено по центру. Чтобы выровнять такое уравнение по левому или правому краю, щелкните по рамке уравнения и используйте значки <span class="icon icon-alignleft"></span> или <span class="icon icon-alignright"></span> на вкладке <b>Главная</b> верхней панели инструментов.</p>
            <div class="icon icon-insertedequation"></div>
            <p>Каждый шаблон уравнения представляет собой совокупность слотов. Слот - это позиция для каждого элемента, образующего уравнение. Пустой слот, также называемый полем для заполнения, имеет пунктирный контур <span class="icon icon-equationplaceholder"></span>. Необходимо заполнить все поля, указав нужные значения.</p>
            <p class="note"><b>Примечание</b>: чтобы начать создание уравнения, можно также использовать сочетание клавиш <b>Alt + =</b>.</p>
            <p>К уравнению также можно добавить подпись. Для получения дополнительной информации о работе с подписями к уравнениям вы можете обратиться к <a href="../UsageInstructions/AddCaption.htm" onclick="onhyperlinkclick(this)">этой статье</a>.</p>
            <h3>Ввод значений</h3>
            <p><b>Курсор</b> определяет, где появится следующий символ, который вы введете. Чтобы точно установить курсор, щелкните внутри поля для заполнения и используйте клавиши со стрелками на клавиатуре для перемещения курсора на один символ влево/вправо или на одну строку вверх/вниз.</p>
            <p>Если в выбранном шаблоне требуется добавить новое поле для заполнения под слотом, в котором находится курсор, нажмите клавишу <b>Enter</b>.</p>
            <div class="big big-newslot"></div>
            <p>
                Когда курсор будет установлен в нужную позицию, можно заполнить поле:
                <ul>
                    <li>введите требуемое цифровое или буквенное значение с помощью клавиатуры,</li>
                    <li>вставьте специальный символ, используя палитру <b>Символы</b> из меню <div class="icon icon-insertequationicon"></div> <b>Уравнение</b> на вкладке <b>Вставка</b> верхней панели инструментов или вводя их с клавиатуры (см. описание функции <a href="../UsageInstructions/MathAutoCorrect.htm" onclick="onhyperlinkclick(this)"><b>Автозамена математическими символами</b></a>),</li>
                    <li>добавьте шаблон другого уравнения с палитры, чтобы создать сложное вложенное уравнение. Размер начального уравнения будет автоматически изменен в соответствии с содержимым. Размер элементов вложенного уравнения зависит от размера поля начального уравнения, но не может быть меньше, чем размер мелкого индекса.</li>
                </ul>
            </p>
            <p><div class="big big-nestedfraction"></div></p>
            <p>Для добавления некоторых новых элементов уравнений можно также использовать <b>пункты контекстного меню</b>:</p>
            <ul>
                <li>Чтобы добавить новый аргумент, идущий до или после имеющегося аргумента в <em>Скобках</em>, можно щелкнуть правой кнопкой мыши по существующему аргументу и выбрать из контекстного меню пункт <b>Вставить аргумент перед/после</b>.</li>
                <li>Чтобы добавить новое уравнение в <em>Наборах условий</em> из группы <em>Скобки</em> (или в уравнениях других типов, в которых вы ранее добавили новые поля для заполнения путем нажатия на <b>Enter</b>), можно щелкнуть правой кнопкой мыши по пустому полю для заполнения или по введенному в него уравнению и выбрать из контекстного меню пункт <b>Вставить уравнение перед/после</b>.</li>
                <li>Чтобы добавить новую строку или новый столбец в <em>Матрице</em>, можно щелкнуть правой кнопкой мыши по полю для заполнения внутри нее, выбрать из контекстного меню пункт <b>Добавить</b>, а затем - опцию <b>Строку выше/ниже</b> или <b>Столбец слева/справа</b>.</li>
            </ul>
            <p class="note"><b>Примечание</b>: в настоящее время не поддерживается ввод уравнений в линейном формате, то есть в виде <b>\sqrt(4&x^3)</b>.</p>
            <p>При вводе значений математических выражений не требуется использовать клавишу <b>Пробел</b>, так как пробелы между символами и знаками действий устанавливаются автоматически.</p>
            <p>Если уравнение слишком длинное и не помещается на одной строке, перенос на другую строку в процессе ввода осуществляется автоматически. Можно также вставить перенос строки в строго определенном месте, щелкнув правой кнопкой мыши по математическому оператору и выбрав из контекстного меню пункт <b>Вставить принудительный разрыв</b>. Выбранный оператор будет перенесен на новую строку. После добавления принудительного разрыва строки можно использовать клавишу <b>Tab</b>, чтобы выровнять новую строку по какому-либо математическому знаку из предыдущей строки. Чтобы удалить добавленный принудительный разрыв строки, щелкните правой кнопкой мыши по математическому оператору в начале новой строки и выберите пункт меню <b>Удалить принудительный разрыв</b>.</p>
            <h3>Форматирование уравнений</h3>
            <p>Чтобы увеличить или уменьшить <b>размер шрифта</b> в уравнении, щелкните мышью внутри рамки уравнения и используйте кнопки <span class="icon icon-larger"></span> и <span class="icon icon-smaller"></span> на вкладке <b>Главная</b> верхней панели инструментов или выберите нужный размер шрифта из списка. Все элементы уравнения изменятся соответственно.</p>
            <p>По умолчанию буквы в уравнении форматируются курсивом. В случае необходимости можно изменить <b>стиль шрифта</b> (<em>выделение полужирным, курсив, зачеркивание</em>) или <b>цвет</b> для всего уравнения или его части. <em>Подчеркивание</em> можно применить только ко всему уравнению, а не к отдельным символам. Выделите нужную часть уравнения путем перетаскивания. Выделенная часть будет подсвечена голубым цветом. Затем используйте нужные кнопки на вкладке <b>Главная</b> верхней панели инструментов, чтобы отформатировать выделенный фрагмент. Например, можно убрать форматирование курсивом для обычных слов, которые не являются переменными или константами.</p>
            <div class="big big-formatastext"></div>
            <p>Для изменения некоторых элементов уравнений можно также использовать <b>пункты контекстного меню</b>:</p>
            <ul>
                <li>Чтобы изменить формат <em>Дробей</em>, можно щелкнуть правой кнопкой мыши по дроби и выбрать из контекстного меню пункт <b>Изменить на диагональную/горизонтальную/вертикальную простую дробь</b> (доступные опции отличаются в зависимости от типа выбранной дроби). <!--Для вертикальных простых дробей также доступна опция <b>Удалить/Добавить дробную черту</b>.--></li>
                <li>Чтобы изменить положение <em>Индексов</em> относительно текста, можно щелкнуть правой кнопкой мыши по уравнению, содержащему индексы, и выбрать из контекстного меню пункт <b>Индексы перед текстом/после текста</b>.</li>
                <li>Чтобы изменить размер аргумента для уравнений из групп <em>Индексы, Радикалы, Интегралы, Крупные операторы, Пределы и логарифмы, Операторы</em>, а также для горизонтальных фигурных скобок и шаблонов с группирующим знаком из группы <em>Диакритические знаки</em>, можно щелкнуть правой кнопкой мыши по аргументу, который требуется изменить, и выбрать из контекстного меню пункт <b>Увеличить/Уменьшить размер аргумента</b>.</li>
                <li>Чтобы указать, надо ли отображать пустое поле для ввода степени в уравнении из группы <em>Радикалы</em>, можно щелкнуть правой кнопкой мыши по радикалу и выбрать из контекстного меню пункт <b>Скрыть/Показать степень</b>.</li>
                <li>Чтобы указать, надо ли отображать пустое поле для ввода предела в уравнение из группы <em>Интегралы</em> или <em>Крупные операторы</em>, можно щелкнуть правой кнопкой мыши по уравнению и выбрать из контекстного меню пункт <b>Скрыть/Показать верхний/нижний предел</b>.</li>
                <li>Чтобы изменить положение пределов относительно знака интеграла или оператора в уравнениях из группы <em>Интегралы</em> или <em>Крупные операторы</em>, можно щелкнуть правой кнопкой мыши по уравнению и выбрать из контекстного меню пункт <b>Изменить положение пределов</b>. Пределы могут отображаться справа от знака оператора (как верхние и нижние индексы) или непосредственно над и под знаком оператора.</li>
                <li>Чтобы изменить положение пределов относительно текста в уравнениях из группы <em>Пределы и логарифмы</em> и в шаблонах с группирующим знаком из группы <em>Диакритические знаки</em>, можно щелкнуть правой кнопкой мыши по уравнению и выбрать из контекстного меню пункт <b>Предел над текстом/под текстом</b>.</li>
                <li>Чтобы выбрать, какие из <em>Скобок</em> надо отображать, можно щелкнуть правой кнопкой мыши по выражению в скобках и выбрать из контекстного меню пункт <b>Скрыть/Показать открывающую/закрывающую скобку</b>.</li>
                <li>Чтобы управлять размером <em>Скобок</em>, можно щелкнуть правой кнопкой мыши по выражению в скобках. Пункт меню <b>Растянуть скобки</b> выбран по умолчанию, так что скобки могут увеличиваться в соответствии с размером выражения, заключенного в них, но вы можете снять выделение с этой опции, чтобы запретить растяжение скобок. Когда эта опция активирована, можно также использовать пункт меню <b>Изменить размер скобок в соответствии с высотой аргумента</b>.</li>
                <li>Чтобы изменить положение символа относительно текста для горизонтальных фигурных скобок или горизонтальной черты над/под уравнением из группы <em>Диакритические знаки</em>, можно щелкнуть правой кнопкой мыши по шаблону и и выбрать из контекстного меню пункт <b>Символ/Черта над/под текстом</b>.</li>
                <li>Чтобы выбрать, какие границы надо отображать для <em>Уравнения в рамке</em> из группы <em>Диакритические знаки</em>, можно щелкнуть правой кнопкой мыши по уравнению и выбрать из контекстного меню пункт <b>Свойства границ</b>, а затем - <b>Скрыть/Показать верхнюю/нижнюю/левую/правую границу</b> или <b>Добавить/Скрыть горизонтальную/вертикальную/диагональную линию</b>.</li>
                <li>Чтобы указать, надо ли отображать пустые поля для заполнения в <em>Матрице</em>, можно щелкнуть по ней правой кнопкой мыши и выбрать из контекстного меню пункт <b>Скрыть/Показать поля для заполнения</b>.</li>
            </ul>
            <p>Для выравнивания некоторых элементов уравнений можно использовать <b>пункты контекстного меню</b>:</p>
            <ul>
                <li>Чтобы выровнять уровнения в <em>Наборах условий</em> из группы <em>Скобки</em> (или в уравнениях других типов, в которых вы ранее добавили новые поля для заполнения путем нажатия на <b>Enter</b>), можно щелкнуть правой кнопкой мыши по уравнению, выбрать из контекстного меню пункт <b>Выравнивание</b>, а затем выбрать тип выравнивания: <b>По верхнему краю</b>, <b>По центру</b> или <b>По нижнему краю</b>.</li>
                <li>Чтобы выровнять <em>Матрицу</em> по вертикали, можно щелкнуть правой кнопкой мыши по матрице, выбрать из контекстного меню пункт <b>Выравнивание матрицы</b>, а затем выбрать тип выравнивания: <b>По верхнему краю</b>, <b>По центру</b> или <b>По нижнему краю</b>.</li>
                <li>Чтобы выровнять по горизонтали элементы внутри отдельного столбца <em>Матрицы</em>, можно щелкнуть правой кнопкой мыши по полю для заполнения внутри столбца, выбрать из контекстного меню пункт <b>Выравнивание столбца</b>, а затем выбрать тип выравнивания: <b>По левому краю</b>, <b>По центру</b> или <b>По правому краю</b>.</li>
            </ul>
            <h3>Удаление элементов уравнения</h3>
            <p>Чтобы удалить часть уравнения, выделите фрагмент, который требуется удалить, путем перетаскивания или удерживая клавишу <b>Shift</b> и используя клавиши со стрелками, затем нажмите на клавиатуре клавишу <b>Delete</b>.</p>
            <p>Слот можно удалить только вместе с шаблоном, к которому он относится.</p>
            <p>Чтобы удалить всё уравнение, выделите его полностью путем перетаскивания или с помощью двойного щелчка по рамке уравнения и нажмите на клавиатуре клавишу <b>Delete</b>.</p>
            <div class="icon icon-deleteequation"></div>
            <p>Для удаления некоторых элементов уравнений можно также использовать <b>пункты контекстного меню</b>:</p>
            <ul>
                <li>Чтобы удалить <em>Радикал</em>, можно щелкнуть по нему правой кнопкой мыши и выбрать из контекстного меню пункт <b>Удалить радикал</b>.</li>
                <li>Чтобы удалить <em>Нижний индекс</em> и/или <em>Верхний индекс</em>, можно щелкнуть правой кнопкой мыши по содержащему их выражению и выбрать из контекстного меню пункт <b>Удалить верхний индекс/нижний индекс</b>. Если выражение содержит индексы, расположенные перед текстом, доступна опция <b>Удалить индексы</b>.</li>
                <li>Чтобы удалить <em>Скобки</em>, можно щелкнуть правой кнопкой мыши по выражению в скобках и выбрать из контекстного меню пункт <b>Удалить вложенные знаки</b> или <b>Удалить вложенные знаки и разделители</b>.</li>
                <li>Если выражение в <em>Скобках</em> содержит несколько аргументов, можно щелкнуть правой кнопкой мыши по аргументу, который требуется удалить, и выбрать из контекстного меню пункт <b>Удалить аргумент</b>.</li>
                <li>Если в <em>Скобках</em> заключено несколько уравнений (а именно, в <em>Наборах условий</em>), можно щелкнуть правой кнопкой мыши по уравнению, которое требуется удалить, и выбрать из контекстного меню пункт <b>Удалить уравнение</b>. Эта опция также доступна для уравнений других типов, в которых вы ранее добавили новые поля для заполнения путем нажатия на <b>Enter</b>.</li>
                <li>Чтобы удалить <em>Предел</em>, можно щелкнуть по нему правой кнопкой мыши и выбрать из контекстного меню пункт <b>Удалить предел</b>.</li>
                <li>Чтобы удалить <em>Диакритический знак</em>, можно щелкнуть по нему правой кнопкой мыши и выбрать из контекстного меню пункт <b>Удалить диакритический знак</b>, <b>Удалить символ</b> или <b>Удалить черту</b> (доступные опции отличаются в зависимости от выбранного диакритического знака).</li>
                <li>Чтобы удалить строку или столбец <em>Матрицы</em>, можно щелкнуть правой кнопкой мыши по полю для заполнения внутри строки/столбца, который требуется удалить, выбрать из контекстного меню пункт <b>Удалить</b>, а затем - <b>Удалить строку/столбец</b>.</li>
            </ul>
            <h3 id="convertequation">Преобразование уравнений</h3>
            <p>Если вы открываете существующий документ с уравнениями, которые были созданы с помощью старой версии редактора уравнений (например, в версиях, предшествующих MS Office 2007), эти уравнения необходимо преобразовать в формат Office Math ML, чтобы иметь возможность их редактировать.</p>
            <p>Чтобы преобразовать уравнение, дважды щелкните по нему. Откроется окно с предупреждением:</p>
            <p><img alt="Преобразование уравнений" src="../images/convertequation.png" /></p>
            <p>Чтобы преобразовать только выбранное уравнение, нажмите кнопку <b>Да</b> в окне предупреждения. Чтобы преобразовать все уравнения в документе, поставьте галочку <b>Применить ко всем уравнениям</b> и нажмите кнопку <b>Да</b>.</p>
            <p>После преобразования уравнения вы сможете его редактировать.</p>
        </div>
	</body>
</html>