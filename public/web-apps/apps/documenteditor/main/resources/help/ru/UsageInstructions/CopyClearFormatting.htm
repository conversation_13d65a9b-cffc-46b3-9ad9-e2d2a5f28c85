<!DOCTYPE html>
<html>
	<head>
		<title>Копирование/очистка форматирования текста</title>
		<meta charset="utf-8" />
		<meta name="description" content="Копируйте/очищайте форматирование текста в документе" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Копирование/очистка форматирования текста</h1>
			<p>Чтобы скопировать определенное форматирование текста,</p>
			<ol>
				<li>с помощью мыши или <a href="../HelpfulHints/KeyboardShortcuts.htm#textselection" onclick="onhyperlinkclick(this)">клавиатуры</a> выделите фрагмент текста, форматирование которого надо скопировать,</li>
				<li>нажмите значок <b>Копировать стиль</b> <div class = "icon icon-copystyle"></div> на вкладке <b>Главная</b> верхней панели инструментов (указатель мыши будет при этом выглядеть так: <div class = "icon icon-paste_style"></div>),</li>
				<li>выделите фрагмент текста, к которому требуется применить то же самое форматирование.</li>
			</ol>
            <p>Чтобы применить скопированное форматирование ко множеству фрагментов текста,</p>
            <ol>
                <li>с помощью мыши или <a href="../HelpfulHints/KeyboardShortcuts.htm#textselection" onclick="onhyperlinkclick(this)">клавиатуры</a> выделите фрагмент текста, форматирование которого надо скопировать,</li>
                <li>дважды нажмите значок <b>Копировать стиль</b> <div class = "icon icon-copystyle"></div> на вкладке <b>Главная</b> верхней панели инструментов (указатель мыши будет при этом выглядеть так: <div class = "icon icon-paste_style"></div>, а значок <b>Копировать стиль</b> будет оставаться нажатым: <div class = "icon icon-copystyle_selected"></div>),</li>
                <li>поочередно выделяйте нужные фрагменты текста, чтобы применить одинаковое форматирование к каждому из них,</li>
                <li>для выхода из этого режима еще раз нажмите значок <b>Копировать стиль</b> <div class = "icon icon-copystyle_selected"></div> или нажмите клавишу <b>Esc</b> на клавиатуре.</li>
            </ol>
			<p>Чтобы быстро убрать из текста примененное форматирование,</p>
			<ol>
				<li>выделите фрагмент текста, форматирование которого надо убрать,</li>
				<li>нажмите значок <b>Очистить стиль</b> <div class = "icon icon-clearstyle"></div> на вкладке <b>Главная</b> верхней панели инструментов.</li>
			</ol>
		</div>
	</body>
</html>