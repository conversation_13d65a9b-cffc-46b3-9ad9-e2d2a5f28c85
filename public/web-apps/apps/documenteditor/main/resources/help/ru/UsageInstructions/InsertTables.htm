<!DOCTYPE html>
<html>
	<head>
		<title>Вставка таблиц</title>
		<meta charset="utf-8" />
		<meta name="description" content="Добавьте в документ таблицу и настройте ее свойства" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Вставка таблиц</h1>
            <h3>Вставка таблицы</h3>
			<p>Для вставки таблицы в текст документа:</p>
			<ol>
				<li>установите курсор там, где надо разместить таблицу,</li>
                <li>перейдите на вкладку <b>Вставка</b> верхней панели инструментов,</li>
                <li>нажмите значок <div class = "icon icon-table"></div> <b>Таблица</b> на верхней панели инструментов,</li>
				<li>выберите опцию для создания таблицы:
					<ul>
						<li><p>или таблица со стандартным количеством ячеек (максимум 10 на 8 ячеек)</p>
						<p>Если требуется быстро добавить таблицу, просто выделите мышью нужное количество строк (максимум 8) и столбцов (максимум 10).</p></li>
						<li><p>или пользовательская таблица</p>
						<p>Если Вам нужна таблица больше, чем 10 на 8 ячеек, выберите опцию <b>Вставить пользовательскую таблицу</b>, после чего откроется окно, в котором можно вручную ввести нужное количество строк и столбцов соответственно, затем нажмите кнопку <b>OK</b>.</p>
						<p><img alt="Пользовательская таблица" src="../images/customtable.png" /></p>
						</li>
                        <li>Если вы хотите нарисовать таблицу с помощью мыши, выберите опцию <b>Нарисовать таблицу</b>. Это может быть полезно, если требуется создать таблицу со строками и столбцами разного размера. Курсор мыши превратится в карандаш <div class = "icon icon-pencil_tool"></div>. Нарисуйте прямоугольную фигуру там, где требуется добавить таблицу, затем добавьте строки, рисуя горизонтальные линии, и столбцы, рисуя вертикальные линии, внутри контура таблицы.</li>
                        <li>
                            Если вы хотите преобразовать существующий текст в таблицу, выберите функцию <b>Преобразовать текст в таблицу</b>. Эта функция может оказаться полезной, если у вас уже есть текст, который вы решили преобразовать в таблицу. Окно <b>Преобразовать текст в таблицу</b> содержит 3 раздела:
                            <p><img alt="Преобразовать текст в таблицу" src="../images/converttotable.png" /></p>
                            <ul>
                                <li><b>Размер таблицы</b>. Выберите необходимое количество столбцов/строк, в которые вы хотите поместить текст. Для этого вы можете использовать кнопки со стрелками вверх/вниз или ввести их количество вручную с клавиатуры.</li>
                                <li><b>Автоподбор ширины столбцов</b>. Выберите нужную опцию, чтобы настроить поведение подгонки текста: <b>Фиксированная ширина столбца</b> (по умолчанию установлено значение <em>Авто</em>. Вы можете использовать кнопки со стрелками вверх/вниз или ввести значение вручную с клавиатуры), <b>Автоподбор по содержимому</b> (ширина столбца соответствует длине текста), <b>Автоподбор по ширине окна</b> (ширина столбца соответствует ширине страницы).</li>
                                <li><b>Разделитель текста</b>. Выберите нужную опцию, чтобы установить тип разделителя для текста: <b>Абзацы</b>, <b>Табуляция</b>, <b>Точки с запятыми</b> и <b>Другое</b> (введите желаемый разделитель вручную).</li>
                                <li>Нажмите <b>ОК</b>, чтобы преобразовать текст в таблицу.</li>
                            </ul>
                        </li>
                        <li>
                            если вы хотите вставить таблицу как OLE-объект:
                            <ol>
                                <li>Выберите опцию <b>Вставить таблицу</b> в меню <b>Таблица</b> на вкладке <b>Вставка</b>.</li>
                                <li>
                                    Появится соответствующее окно, в котором вы можете ввести нужные данные и отформатировать их, используя инструменты форматирования Редактора электронных таблиц, такие как <a href="../../../../../../spreadsheeteditor/main/resources/help/ru/UsageInstructions/FontTypeSizeStyle.htm">выбор шрифта, типа и стиля</a>, <a href="../../../../../../spreadsheeteditor/main/resources/help/ru/UsageInstructions/ChangeNumberFormat.htm">настройка числового формата</a>, <a href="../../../../../../spreadsheeteditor/main/resources/help/ru/UsageInstructions/InsertFunction.htm">вставка функций</a>, <a href="../../../../../../spreadsheeteditor/main/resources/help/ru/UsageInstructions/FormattedTables.htm">форматированные таблицы</a> и так далее.
                                    <p><img alt="OLE-таблица" src="../../../../../../common/main/resources/help/ru/images/ole_table.png" /></p>
                                </li>
                                <li>В шапке в правом верхнем углу окна находится кнопка <span class="icon icon-visible_area"></span> <b>Видимая область</b>. Выберите опцию <b>Редактировать видимую область</b>, чтобы выбрать область, которая будет отображаться при вставке объекта в документ; другие данные не будут потеряны, а просто будут скрыты. Когда область будет выделена, нажмите кнопку <b>Готово</b>.</li>
                                <li>Выберите опцию <b>Показать видимую область</b>, чтобы увидеть выбранную область, у которой будет голубая граница.</li>
                                <li>Когда все будет готово, нажмите кнопку <b>Сохранить и выйти</b>.</li>
                            </ol>
                        </li>
					</ul>
				</li>
				<li>после того, как таблица будет добавлена, Вы сможете изменить ее свойства и положение.</li>
			</ol>
            <p>Чтобы изменить размер таблицы, наведите курсор мыши на маркер <span class = "icon icon-resizetable_handle"></span> в правом нижнем углу и перетаскивайте его, пока таблица не достигнет нужного размера.</p>
            <p><span class = "big big-resizetable"></span></p>
            <p>Вы также можете вручную изменить ширину определенного столбца или высоту строки. Наведите курсор мыши на правую границу столбца, чтобы курсор превратился в двунаправленную стрелку <span class = "icon icon-changecolumnwidth"></span>, и перетащите границу влево или вправо, чтобы задать нужную ширину. Чтобы вручную изменить высоту отдельной строки, наведите курсор мыши на нижнюю границу строки, чтобы курсор превратился в двунаправленную стрелку <span class = "icon icon-changerowheight"></span>, и перетащите границу вверх или вниз.</p>
            <p>Чтобы переместить таблицу, удерживайте маркер <span class = "icon icon-movetable_handle"></span> в левом верхнем углу и перетащите его на нужное место в документе.</p>
            <p>К таблице также можно добавить подпись. Для получения дополнительной информации о работе с подписями к таблицам вы можете обратиться к <a href="../UsageInstructions/AddCaption.htm" onclick="onhyperlinkclick(this)">этой статье</a>.</p>
            <hr />
            <h3>Выделение таблицы или ее части</h3>
            <p>Чтобы выделить всю таблицу, нажмите на маркер <span class = "icon icon-movetable_handle"></span> в левом верхнем углу.</p>
            <p>Чтобы выделить определенную ячейку, подведите курсор мыши к левой части нужной ячейки, чтобы курсор превратился в черную стрелку <span class = "icon icon-selectcellpointer"></span>, затем щелкните левой кнопкой мыши.</p>
            <p>Чтобы выделить определенную строку, подведите курсор мыши к левой границе таблицы рядом с нужной строкой, чтобы курсор превратился в горизонтальную черную стрелку <span class = "icon icon-selectrowpointer"></span>, затем щелкните левой кнопкой мыши.</p>
            <p>Чтобы выделить определенный столбец, подведите курсор мыши к верхней границе нужного столбца, чтобы курсор превратился в направленную вниз черную стрелку <span class = "icon icon-selectcolumnpointer"></span>, затем щелкните левой кнопкой мыши.</p>
            <p>Можно также выделить ячейку, строку, столбец или таблицу с помощью опций контекстного меню или раздела <b>Строки и столбцы</b> на правой боковой панели.</p>
            <p class="note">
                <b>Примечание</b>: для перемещения по таблице можно использовать <a href="../HelpfulHints/KeyboardShortcuts.htm#workwithtables" onclick="onhyperlinkclick(this)">сочетания клавиш</a>.
            </p>
			<hr />
            <h3>Изменение параметров таблицы</h3>
			<p>Некоторые свойства таблицы, а также ее структуру можно изменить с помощью контекстного меню. Меню содержит следующие пункты:</p>
			<ul>
        <li><b>Вырезать, копировать, вставить</b> - стандартные опции, которые используются для вырезания или копирования выделенного текста/объекта и вставки ранее вырезанного/скопированного фрагмента текста или объекта в то место, где находится курсор.</li>
        <li><b>Выделить</b> - используется для выделения строки, столбца, ячейки или таблицы.</li>
				<li><b>Добавить</b> - используется для вставки строки выше или ниже той строки, в которой находится курсор, а также для вставки столбца слева или справа от того столбца, в котором находится курсор.
                <p>Также можно вставить несколько строк или столбцов. При выборе опции <b>Несколько строк/столбцов</b> откроется окно <b>Вставить несколько</b>. Выберите из списка опцию <b>Строки</b> или <b>Столбцы</b>, укажите количество строк или столбцов, которые требуется добавить, выберите, где их требуется добавить: <b>Над курсором</b> или <b>Под курсором</b> и нажмите кнопку <b>OK</b>.</p>
                </li>
				<li><b>Удалить</b> - используется для удаления строки, столбца, таблицы или ячеек. При выборе опции <b>Ячейки</b> откроется окно <b>Удалить ячейки</b>, в котором можно выбрать, требуется ли удалить <b>Ячейки со сдвигом влево</b>, <b>Удалить всю строку</b> или <b>Удалить весь столбец</b>.</li>
				<li><b>Объединить ячейки</b> - этот пункт доступен при выделении двух или более ячеек и используется для их объединения.
                    <p>
                       Также можно объединить ячейки, очистив границу между ними с помощью инструмента "Ластик". Для этого нажмите значок <span class = "icon icon-table"></span> <b>Таблица</b> на верхней панели инструментов, выберите пункт меню <b>Очистить таблицу</b>. Курсор мыши превратится в ластик <span class = "icon icon-pencil_tool"></span>. Наведите курсор мыши на границу между ячейками, которые требуется объединить, и очистите ее.
                    </p>
                </li>
				<li><b>Разделить ячейку...</b> - используется для вызова окна, в котором можно выбрать нужное количество столбцов и строк, на которое будет разделена ячейка.
                    <p>
                       Также можно разделить ячейку, нарисовав строки или столбцы с помощью инструмента "Карандаш". Для этого нажмите значок <span class = "icon icon-table"></span> <b>Таблица</b> на верхней панели инструментов, выберите пункт меню <b>Нарисовать таблицу</b>. Курсор мыши превратится в карандаш <span class = "icon icon-pencil_tool"></span>. Нарисуйте горизонтальную линию для создания строки или вертикальную линию для создания столбца.
                    </p>
                </li>
                <li><b>Выровнять высоту строк</b> - используется для изменения выделенных ячеек таким образом, чтобы все они имели одинаковую высоту, без изменения общей высоты таблицы.</li>
                <li><b>Выровнять ширину столбцов</b> - используется для изменения выделенных ячеек таким образом, чтобы все они имели одинаковую ширину, без изменения общей ширины таблицы.</li>
				<li><b>Вертикальное выравнивание в ячейках</b> - используется для выравнивания текста в выделенной ячейке по верхнему краю, центру или нижнему краю.</li>
                <li><b>Направление текста</b> - используется для изменения ориентации текста в ячейке. Текст можно расположить по горизонтали, по вертикали сверху вниз (<b>Повернуть текст вниз</b>), или по вертикали снизу вверх (<b>Повернуть текст вверх</b>).</li>
				<li><b>Дополнительные параметры таблицы</b> - используется для вызова окна 'Таблица - дополнительные параметры'.</li>
				<li><b>Гиперссылка</b> - используется для вставки гиперссылки.</li>
				<li><b>Дополнительные параметры абзаца</b> - используется для вызова окна 'Абзац - дополнительные параметры'.</li>
			</ul>
			<hr />
			<p><img class="floatleft" alt="Правая боковая панель - Настройки таблицы" src="../images/right_table.png" /></p>
			<p>Свойства таблицы можно также изменить на правой боковой панели:</p>
			<ul style="margin-left: 280px;">
				<li><p><b>Строки</b> и <b>Столбцы</b> - используются для выбора тех частей таблицы, которые необходимо выделить.</p>
					<p>Для строк:</p>
					<ul>
						<li><i>Заголовок</i> - для выделения первой строки</li>
						<li><i>Итоговая</i> - для выделения последней строки</li>
						<li><i>Чередовать</i> - для выделения строк через одну</li>
					</ul>
					<p>Для столбцов:</p>
					<ul>
						<li><i>Первый</i> - для выделения первого столбца</li>
						<li><i>Последний</i> - для выделения последнего столбца</li>
						<li><i>Чередовать</i> - для выделения столбцов через один</li>
					</ul>
				</li>
				<li><p><b>По шаблону</b> - используется для выбора одного из доступных шаблонов таблиц.</p></li>
				<li><p><b>Стиль границ</b> - используется для выбора толщины, цвета и стиля границ, а также цвета фона.</p></li>
				<li><p><b>Строки и столбцы</b> - используется для выполнения некоторых операций с таблицей: выделения, удаления, вставки строк и столбцов, объединения ячеек, разделения ячейки.</p></li>
                <li><p><b>Размеры строк и столбцов</b> - используется для изменения ширины и высоты выделенной ячейки. В этом разделе можно также <b>Выровнять высоту строк</b>, чтобы все выделенные ячейки имели одинаковую высоту, или <b>Выровнять ширину столбцов</b>, чтобы все выделенные ячейки имели одинаковую ширину.</p></li>
                <li><p><b>Добавить формулу</b> - используется для <a href="../UsageInstructions/AddFormulasInTables.htm" onclick="onhyperlinkclick(this)">вставки формулы</a> в выбранную ячейку таблицы.</p></li>
                <li><p><b>Повторять как заголовок на каждой странице</b> - в длинных таблицах используется для вставки одной и той же строки заголовка наверху каждой страницы.</p></li>
                <li><p><b>Преобразовать таблицу в текст</b> используется для преобразования таблицы в текстовую форму. В окне <b>Преобразовать таблицу в текст</b> устанавливается тип разделителя для преобразования: <b>Знаки абзаца</b>, <b>Табуляция</b>, <b>Точки с запятыми</b> и <b>Другоq</b> (введите предпочтительный разделитель вручную). Текст в каждой ячейке таблицы считается отдельным элементом будущего текста.</p></li>
				<li><p><b>Дополнительные параметры</b> - используется для вызова окна 'Таблица - дополнительные параметры'.</p></li>
			</ul>
			<hr />
            <h3>Изменение дополнительных параметров таблицы</h3>
			<p>Чтобы изменить дополнительные параметры таблицы, щелкните по таблице правой кнопкой мыши и выберите из контекстного меню пункт <b>Дополнительные параметры таблицы</b>. Или нажмите ссылку <b>Дополнительные параметры</b> на правой боковой панели. Откроется окно свойств таблицы:</p>
            <p><img alt="Таблица - дополнительные параметры" src="../images/table_properties_1.png" /></p>
            <p>На вкладке <b>Таблица</b> можно изменить свойства всей таблицы.</p>
            <ul>
                <li>
                    Раздел <b>Размер таблицы</b> содержит следующие параметры:
                    <ul>
                        <li>
                            <b>Ширина</b> - по умолчанию ширина таблицы автоматически подгоняется по ширине страницы, то есть таблица занимает все пространство между левым и правым полями страницы. Можно установить этот флажок и указать нужную ширину таблицы вручную.
                        </li>
                        <li>
                            Опция <b>Единицы</b> позволяет указать, надо ли задавать ширину таблицы в абсолютных единицах, то есть <b>Сантиметрах</b>/<b>Пунктах</b>/<b>Дюймах</b> (в зависимости от того, какой параметр указан на вкладке <b>Файл</b> -> <b>Дополнительные параметры...</b>) или в <b>Процентах</b> от общей ширины страницы.
                            <p class="note"><b>Примечание</b>: можно также регулировать размеры таблицы, изменяя высоту строк и ширину столбцов вручную. Наведите указатель мыши на границу строки/столбца, чтобы он превратился в двустороннюю стрелку, и перетащите границу. Кроме того, можно использовать маркеры <span class = "icon icon-rowheightmarker"></span> на горизонтальной линейке для изменения ширины столбцов и маркеры <span class = "icon icon-rowheightmarker"></span> на вертикальной линейке для изменения высоты строк.</p>
                        </li>
                        <li><b>Автоподбор размеров по содержимому</b> - разрешает автоматически изменять ширину каждого столбца в соответствии с текстом внутри его ячеек.</li>
                    </ul>
                </li>
                <li>Раздел <b>Поля ячейки по умолчанию</b> позволяет изменить используемые по умолчанию расстояния между текстом внутри ячейки и границами ячейки.</li>
                <li>
                    Раздел <b>Параметры</b> позволяет изменить следующий параметр:
                    <ul>
                        <li><b>Интервалы между ячейками</b> - разрешает использование между ячейками интервалов, которые будут заливаться цветом <b>Фона таблицы</b>.</li>
                    </ul>
                </li>
            </ul>
            <p><img alt="Таблица - дополнительные параметры" src="../images/table_properties_5.png" /></p>
            <p>На вкладке <b>Ячейка</b> можно изменить свойства отдельных ячеек. Сначала надо выбрать ячейку, к которой требуется применить изменения, или выделить всю таблицу, чтобы изменить свойства всех ее ячеек.</p>
            <ul>
                <li>
                    Раздел <b>Размер ячейки</b> содержит следующие параметры:
                    <ul>
                        <li>Опция <b>Ширина</b> позволяет задать предпочтительную ширину ячейки. Это размер, которому ячейка стремится соответствовать, хотя в некоторых случаях точное соответствие может быть невозможно. Например, если текст внутри ячейки превышает заданную ширину, он будет переноситься на следующую строку, чтобы предпочтительная ширина ячейки оставалась неизменной, но если вставить новый столбец, предпочтительная ширина будет уменьшена.</li>
                        <li>
                            Опция <b>Единицы</b> - позволяет указать, надо ли задавать ширину ячейки в абсолютных единицах, то есть <b>Сантиметрах</b>/<b>Пунктах</b>/<b>Дюймах</b> (в зависимости от того, какой параметр указан на вкладке <b>Файл</b> -> <b>Дополнительные параметры...</b>) или в <b>Процентах</b> от общей ширины таблицы.
                            <p class="note"><b>Примечание</b>: можно также регулировать ширину ячейки вручную. Чтобы сделать отдельную ячейку в столбце шире или уже общей ширины столбца, выделите нужную ячейку, наведите указатель мыши на ее правую границу, чтобы он превратился в двустороннюю стрелку, затем перетащите границу. Чтобы изменить ширину всех ячеек в столбце, используйте маркеры <span class = "icon icon-rowheightmarker"></span> на горизонтальной линейке для изменения ширины столбцов.</p>
                        </li>
                    </ul>
                </li>
                <li>Раздел <b>Поля ячейки</b> позволяет регулировать расстояние между текстом внутри ячейки и границами ячейки. По умолчанию установлены стандартные значения (значения, используемые по умолчанию, тоже можно изменить на вкладке <b>Таблица</b>), но можно снять флажок <b>Использовать поля по умолчанию</b> и ввести нужные значения вручную.</li>
                <li>
                    В разделе <b>Параметры ячейки</b> можно изменить следующий параметр:
                    <ul>
                        <li>Опция <b>Перенос текста</b> включена по умолчанию. Она позволяет переносить текст внутри ячейки, превышающий ее ширину, на следующую строку, увеличивая высоту строки и оставляя ширину столбца неизменной.</li>
                    </ul>
                </li>
            </ul>
            <p><img alt="Таблица - дополнительные параметры" src="../images/table_properties_3.png" /></p>
            <p>Вкладка <b>Границы и фон</b> содержит следующие параметры:</p>
            <ul>
                <li>
                    Параметры <b>границы</b> (ширина, цвет и наличие или отсутствие) - задайте ширину границ, выберите их цвет и то, как они должны отображаться в ячейках.
                    <p class="note">
                        <b>Примечание</b>: если вы решили скрыть границы таблицы, нажав кнопку <span class = "icon icon-noborders"></span> или отключив все границы вручную на схеме, в документе они будут обозначены пунктиром.
                        Чтобы они совсем исчезли, нажмите значок <b>Непечатаемые символы</b> <span class = "icon icon-nonprintingcharacters"></span> на вкладке <b>Главная</b> верхней панели инструментов и выберите опцию <b>Скрытые границы таблиц</b>.
                    </p>
                </li>
                <li><b>Фон ячейки</b> - цвет фона внутри ячейки (опция доступна только в том случае, если выделены одна или более ячеек или выбрана опция <b>Интервалы между ячейками</b> на вкладке <b>Таблица</b>).</li>
                <li><b>Фон таблицы</b> - цвет фона таблицы или фона пространства между ячейками в том случае, если выбрана опция <b>Интервалы между ячейками</b> на вкладке <b>Таблица</b>.</li>
            </ul>
            <p id="position"><img alt="Таблица - дополнительные параметры" src="../images/table_properties_4.png" /></p>
            <p>Вкладка <b>Положение таблицы</b> доступна только в том случае, если на вкладке <b>Обтекание текстом</b> выбрана опция <b>Плавающая таблица</b>. Эта вкладка содержит следующие параметры:</p>
            <ul>
                <li>Параметры раздела <b>По горизонтали</b> включают в себя <b>выравнивание</b> таблицы (по левому краю, по центру, по правому краю) <b>относительно</b> поля, страницы или текста, а также <b>положение</b> таблицы <b>справа от</b> поля, страницы или текста.</li>
                <li>Параметры раздела <b>По вертикали</b> включают в себя <b>выравнивание</b> таблицы (по верхнему краю, по центру, по нижнему краю) <b>относительно</b> поля, страницы или текста, а также <b>положение</b> таблицы <b>ниже</b> поля, страницы или текста.</li>
                <li>
                    В разделе <b>Параметры</b> можно изменить следующие параметры:
                    <ul>
                        <li>Опция <b>Перемещать с текстом</b> определяет, будет ли таблица перемещаться вместе с текстом, в который она вставлена.</li>
                        <li>Опция <b>Разрешить перекрытие</b> определяет, будут ли две таблицы объединяться в одну большую таблицу или перекрываться, если перетащить их близко друг к другу на странице.</li>
                    </ul>
                </li>
            </ul>
            <p><img alt="Таблица - дополнительные параметры" src="../images/table_properties_2.png" /></p>
            <p>Вкладка <b>Обтекание текстом</b> содержит следующие параметры:</p>
            <ul>
                <li><b>Стиль обтекания</b> текстом - <b>Встроенная таблица</b> или <b>Плавающая таблица</b>. Используйте нужную опцию, чтобы изменить способ размещения таблицы относительно текста: или она будет являться частью текста (если Вы выбрали вариант "Встроенная таблица"), или текст будет обтекать ее со всех сторон (если Вы выбрали вариант "Плавающая таблица").</li>
                <li>
                    После того, как Вы выберете стиль обтекания, можно задать дополнительные параметры обтекания как для встроенных, так и для плавающих таблиц:
                    <ul>
                        <li>Для встроенной таблицы Вы можете указать <b>выравнивание</b> таблицы и <b>отступ слева</b>.</li>
                        <li>Для плавающей таблицы Вы можете указать <b>расстояние до текста</b> и <b>положение</b> таблицы на вкладке <b>Положение таблицы</b>.</li>
                    </ul>
                </li>
            </ul>
            <p><img alt="Таблица - дополнительные параметры" src="../images/table_properties_6.png" /></p>
            <p>Вкладка <b>Альтернативный текст</b> позволяет задать <b>Заголовок</b> и <b>Описание</b>, которые будут зачитываться для людей с нарушениями зрения или когнитивными нарушениями, чтобы помочь им лучше понять, какую информацию содержит таблица.</p>
			
		</div>
	</body>
</html>