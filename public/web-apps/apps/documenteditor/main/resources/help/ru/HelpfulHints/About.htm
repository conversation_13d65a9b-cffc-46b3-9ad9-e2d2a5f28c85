<!DOCTYPE html>
<html>
	<head>
		<title>О редакторе документов</title>
		<meta charset="utf-8" />
        <meta name="description" content="Краткое описание редактора документов" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
    </head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
		<h1>О редакторе документов</h1>
			<p><b>Редактор документов</b> - это <span class="onlineDocumentFeatures">
    онлайн-</span>приложение, которое позволяет просматривать
    и редактировать документы<span class="onlineDocumentFeatures"> непосредственно в браузере</span>.
</p>
			<p>Используя <span class="onlineDocumentFeatures">
    онлайн-</span>редактор документов, Вы можете выполнять различные операции редактирования, как в любом десктопном редакторе,
    распечатывать отредактированные документы, сохраняя все детали форматирования, или сохранять документы на жесткий диск компьютера
    как файлы в формате DOCX, PDF, ODT, TXT, DOTX, PDF/A, OTT, RTF, HTML, FB2, EPUB, DOCXF и OFORM.
</p>
      <p> <span class="onlineDocumentFeatures">Для просмотра текущей версии программы, номера сборки и информации о владельце лицензии в <em>онлайн-версии</em> щелкните по значку <span class = "icon icon-about"></span> на левой боковой панели инструментов.</span> <span class="desktopDocumentFeatures"> Для просмотра текущей версии программы и информации о владельце лицензии в <em>десктопной версии</em> для Windows выберите пункт меню <b>О программе</b> на левой боковой панели в главном окне приложения. В <em>десктопной версии</em> для Mac OS откройте меню <b>ONLYOFFICE</b> в верхней части и выберите пункт меню <b>О программе ONLYOFFICE</b>.</span></p>
    </div>
	</body>
</html>