<!DOCTYPE html>
<html>
	<head>
		<title>Вставка изображений</title>
		<meta charset="utf-8" />
		<meta name="description" content="Добавьте в документ изображение и скорректируйте его положение и свойства" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Вставка изображений</h1>
            <p>В редакторе документов можно вставлять в документ изображения самых популярных форматов. Поддерживаются следующие форматы изображений: <b>BMP</b>, <b>GIF</b>, <b>JPEG</b>, <b>JPG</b>, <b>PNG</b>.</p>
            <h3>Вставка изображения</h3>
            <p>Для вставки изображения в текст документа:</p>
			<ol>
				<li>установите курсор там, где требуется поместить изображение,</li>
                <li>перейдите на вкладку <b>Вставка</b> верхней панели инструментов,</li>
                <li>нажмите значок <div class = "icon icon-image"></div> <b>Изображение</b> на верхней панели инструментов,</li>
				<li>для загрузки изображения выберите одну из следующих опций:
					<ul>
						<li>при выборе опции <b>Изображение из файла</b> откроется стандартное диалоговое окно для выбора файлов. Выберите нужный файл на жестком диске компьютера и нажмите кнопку <b>Открыть</b>
                           <p class="note">В <em>онлайн-редакторе</em> вы можете выбрать сразу несколько изображений.</p>
                        </li>
						<li>при выборе опции <b>Изображение по URL</b> откроется окно, в котором Вы можете ввести веб-адрес нужного изображения, а затем нажмите кнопку <b>OK</b></li>
                        <li class="onlineDocumentFeatures">при выборе опции  <b>Изображение из хранилища</b> откроется окно <b>Выбрать источник данных</b>. Выберите изображение, сохраненное на вашем портале, и нажмите кнопку <b>OK</b></li>
					</ul>
				</li>
				<li>после того, как изображение будет добавлено, можно изменить его размер, свойства и положение.</li>
			</ol>
            <p>К изображению также можно добавить подпись. Для получения дополнительной информации о работе с подписями к изображениям вы можете обратиться к <a href="../UsageInstructions/AddCaption.htm" onclick="onhyperlinkclick(this)">этой статье</a>.</p>
            <h3>Перемещение и изменение размера изображений</h3>
            <p><span class="big big-moving_image"></span></p>
			<p>Для изменения размера изображения перетаскивайте маленькие квадраты <span class = "icon icon-resize_square"></span>, расположенные по его краям. Чтобы сохранить исходные пропорции выбранного изображения при изменении размера, удерживайте клавишу <b>Shift</b> и перетаскивайте один из угловых значков.</p>
			<p>Для изменения местоположения изображения используйте значок <span class = "icon icon-arrow"></span>, который появляется после наведения курсора мыши на изображение. Перетащите изображение на нужное место, не отпуская кнопку мыши.</p>
      <p>При перемещении изображения на экране появляются направляющие, которые помогают точно расположить объект на странице (если выбран стиль обтекания, отличный от стиля "В тексте").</p>
      <p>Чтобы повернуть изображение, наведите курсор мыши на маркер поворота <span class = "icon icon-greencircle"></span> и перетащите его по часовой стрелке или против часовой стрелки. Чтобы ограничить угол поворота шагом в 15 градусов, при поворачивании удерживайте клавишу <b>Shift</b>.</p>
            <p class="note">
                <b>Примечание</b>: список сочетаний клавиш, которые можно использовать при работе с объектами, доступен <a href="../HelpfulHints/KeyboardShortcuts.htm#workwithobjects" onclick="onhyperlinkclick(this)">здесь</a>.
            </p>
            <hr />
            <h3>Изменение параметров изображения</h3>
			<p><img alt="Вкладка Параметры изображения" src="../images/right_image.png" />
			<p>Некоторые параметры изображения можно изменить с помощью вкладки <b>Параметры изображения</b> на правой боковой панели. Чтобы ее активировать, щелкните по изображению и выберите значок <b>Параметры изображения</b> <span class = "icon icon-image_settings_icon"></span> справа. Здесь можно изменить следующие свойства:</p>
			<ul>
				<li><b>Размер</b> - используется, чтобы просмотреть текущую <b>Ширину</b> и <b>Высоту</b> изображения. При необходимости можно восстановить размер изображения по умолчанию, нажав кнопку <b>По умолчанию</b>. Кнопка <b>Вписать</b> позволяет изменить размер изображения таким образом, чтобы оно занимало все пространство между левым и правым полями страницы.
                    <p>Кнопка <b>Обрезать</b> используется, чтобы обрезать изображение. Нажмите кнопку <b>Обрезать</b>, чтобы активировать маркеры обрезки, которые появятся в углах изображения и в центре каждой его стороны. Вручную перетаскивайте маркеры, чтобы задать область обрезки. Вы можете навести курсор мыши на границу области обрезки, чтобы курсор превратился в значок <span class = "icon icon-arrow"></span>, и перетащить область обрезки. </p>
                    <ul>
                        <li>Чтобы обрезать одну сторону, перетащите маркер, расположенный в центре этой стороны.</li>
                        <li>Чтобы одновременно обрезать две смежных стороны, перетащите один из угловых маркеров.</li>
                        <li>Чтобы равномерно обрезать две противоположные стороны изображения, удерживайте нажатой клавишу <em>Ctrl</em> при перетаскивании маркера в центре одной из этих сторон. </li>
                        <li>Чтобы равномерно обрезать все стороны изображения, удерживайте нажатой клавишу <em>Ctrl</em> при перетаскивании любого углового маркера.</li>
                    </ul>
                    <p>Когда область обрезки будет задана, еще раз нажмите на кнопку <b>Обрезать</b>, или нажмите на клавишу <em>Esc</em>, или щелкните мышью за пределами области обрезки, чтобы применить изменения.</p>
                    <p>После того, как область обрезки будет задана, также можно использовать опции <b>Обрезать</b>, <b>Заливка</b> и <b>Вписать</b>, доступные в выпадающем меню <b>Обрезать</b>. Нажмите кнопку <b>Обрезать</b> еще раз и выберите нужную опцию: </p>
                    <ul>
                        <li>При выборе опции <b>Обрезать</b> изображение будет заполнять определенную форму. Вы можете выбрать фигуру из галереи, которая открывается при наведении указателя мыши на опцию <b>Обрезать по фигуре</b>. Вы по-прежнему можете использовать опции <b>Заливка</b> и <b>Вписать</b>, чтобы настроить, как изображение будет соответствовать фигуре.</li>
                        <li>При выборе опции <b>Заливка</b> центральная часть исходного изображения будет сохранена и использована в качестве заливки выбранной области обрезки, в то время как остальные части изображения будут удалены.</li>
                        <li>При выборе опции <b>Вписать</b> размер изображения будет изменен, чтобы оно соответствовало высоте или ширине области обрезки. Никакие части исходного изображения не будут удалены, но внутри выбранной области обрезки могут появится пустые пространства.</li>
                    </ul>
                </li>
                <li><b>Поворот</b> - используется, чтобы повернуть изображение на 90 градусов по часовой стрелке или против часовой стрелки, а также чтобы отразить изображение слева направо или сверху вниз. Нажмите на одну из кнопок:
                    <ul>
                        <li><div class = "icon icon-rotatecounterclockwise"></div> чтобы повернуть изображение на 90 градусов против часовой стрелки</li>
                        <li><div class = "icon icon-rotateclockwise"></div> чтобы повернуть изображение на 90 градусов по часовой стрелке</li>
                        <li><div class = "icon icon-fliplefttoright"></div> чтобы отразить изображение по горизонтали (слева направо)</li>
                        <li><div class = "icon icon-flipupsidedown"></div> чтобы отразить изображение по вертикали (сверху вниз)</li>
                    </ul>
                </li>
                <li><b>Стиль обтекания</b> - используется, чтобы выбрать один из доступных стилей обтекания текстом - в тексте, вокруг рамки, по контуру, сквозное, сверху и снизу, перед текстом, за текстом (для получения дополнительной информации смотрите описание дополнительных параметров ниже).</li>
                <li><b>Заменить изображение</b> - используется, чтобы заменить текущее изображение, загрузив другое <b>Из файла</b>, <b>Из хранилища</b> или <b>По URL</b>.</li>
			</ul>			
			<p>Некоторые из этих опций можно также найти в <b>контекстном меню</b>. Меню содержит следующие пункты:</p>
			<ul>
                <li><b>Вырезать, копировать, вставить</b> - стандартные опции, которые используются для вырезания или копирования выделенного текста/объекта и вставки ранее вырезанного/скопированного фрагмента текста или объекта в то место, где находится курсор.</li>
                <li><b>Порядок</b> - используется, чтобы вынести выбранное изображение на передний план, переместить на задний план, перенести вперед или назад, а также сгруппировать или разгруппировать изображения для выполнения операций над несколькими из них сразу. Подробнее о расположении объектов в определенном порядке рассказывается на <a href="../UsageInstructions/AlignArrangeObjects.htm" onclick="onhyperlinkclick(this)">этой странице</a>.</li>
				<li><b>Выравнивание</b> - используется, чтобы выровнять изображение по левому краю, по центру, по правому краю, по верхнему краю, по середине, по нижнему краю. Подробнее о выравнивании объектов рассказывается на <a href="../UsageInstructions/AlignArrangeObjects.htm" onclick="onhyperlinkclick(this)">этой странице</a>.</li>
				<li><b>Стиль обтекания</b> - используется, чтобы выбрать один из доступных стилей обтекания текстом - в тексте, вокруг рамки, по контуру, сквозное, сверху и снизу, перед текстом, за текстом - или для изменения границы обтекания. Опция <b>Изменить границу обтекания</b> доступна только в том случае, если выбран стиль обтекания, отличный от стиля "В тексте". Чтобы произвольно изменить границу, перетаскивайте точки границы обтекания. Чтобы создать новую точку границы обтекания, щелкните в любом месте на красной линии и перетащите ее в нужную позицию. <div class = "big big-wrap_boundary"></div></li>
                <li><b>Поворот</b> - используется, чтобы повернуть изображение на 90 градусов по часовой стрелке или против часовой стрелки, а также чтобы отразить изображение слева направо или сверху вниз.</li>
                <li><b>Обрезать</b> - используется, чтобы применить один из вариантов обрезки: <b>Обрезать</b>, <b>Заливка</b> или <b>Вписать</b>. Выберите из подменю пункт <b>Обрезать</b>, затем перетащите маркеры обрезки, чтобы задать область обрезки, и нажмите на одну из этих трех опций в подменю еще раз, чтобы применить изменения.</li>
                <li><b>Реальный размер</b> - используется для смены текущего размера изображения на реальный размер.</li>
                <li><b>Заменить изображение</b> - используется, чтобы заменить текущее изображение, загрузив другое <b>Из файла</b> или <b>По URL</b>.</li>
				<li><b>Дополнительные параметры изображения</b> - используется для вызова окна 'Изображение - дополнительные параметры'.</li>
			</ul>
            <p><img alt="Вкладка Параметры фигуры" src="../images/right_image_shape.png" /></p>
            <p> Когда изображение выделено, справа также доступен значок <b>Параметры фигуры</b> <span class="icon icon-shape_settings_icon"></span>. Можно щелкнуть по нему, чтобы открыть вкладку <b>Параметры фигуры</b> на правой боковой панели и настроить тип, толщину и цвет <a href="../UsageInstructions/InsertAutoshapes.htm#shape_stroke" onclick="onhyperlinkclick(this)"><b>Контуров</b></a> фигуры, а также изменить тип фигуры, выбрав другую фигуру в меню <b>Изменить автофигуру</b>. Форма изображения изменится соответствующим образом.</p>
            <p>На вкладке <b>Параметры фигуры</b> также можно использовать опцию <b>Отображать тень</b>, чтобы добавить тень к изображеню.</p>
            <hr />
            <h3>Изменение дополнительных параметров изображения</h3>
			<p>Чтобы изменить дополнительные параметры изображения, щелкните по нему правой кнопкой мыши и выберите из контекстного меню пункт <b>Дополнительные параметры изображения</b>. Или нажмите ссылку <b>Дополнительные параметры</b> на правой боковой панели. Откроется окно свойств изображения:</p>
			<p><img alt="Изображение - дополнительные параметры: Размер" src="../images/image_properties.png" /></p>
			<p>Вкладка <b>Размер</b> содержит следующие параметры:</p>
			<ul>
				<li><b>Ширина</b> и <b>Высота</b> - используйте эти опции, чтобы изменить ширину и/или высоту изображения. Если нажата кнопка <b>Сохранять пропорции</b> <div class = "icon icon-constantproportions"></div> (в этом случае она выглядит так: <div class = "icon icon-constantproportionsactivated"></div>), ширина и высота будут изменены пропорционально, сохраняя исходное соотношение сторон изображения. Чтобы восстановить реальный размер добавленного изображения, нажмите кнопку <b>Реальный размер</b>.</li>
			</ul>
            <p><img alt="Изображение - дополнительные параметры: Вращение" src="../images/image_properties_4.png" /></p>
            <p>Вкладка <b>Поворот</b> содержит следующие параметры:</p>
            <ul>
                <li><b>Угол</b> - используйте эту опцию, чтобы повернуть изображение на точно заданный угол. Введите в поле нужное значение в градусах или скорректируйте его, используя стрелки справа. </li>
                <li><b>Отражено</b> - отметьте галочкой опцию <b>По горизонтали</b>, чтобы отразить изображение по горизонтали (слева направо), или отметьте галочкой опцию <b>По вертикали</b>, чтобы отразить изображение по вертикали (сверху вниз).</li>
            </ul>
			<p><img alt="Изображение - дополнительные параметры: Обтекание текстом" src="../images/image_properties_1.png" /></p>
			<p>Вкладка <b>Обтекание текстом</b> содержит следующие параметры:</p>
			<ul>
				<li><b>Стиль обтекания</b> - используйте эту опцию, чтобы изменить способ размещения изображения относительно текста: или оно будет являться частью текста (если выбран стиль обтекания "В тексте") или текст будет обтекать его со всех сторон (если выбран один из остальных стилей).
				<ul>
				    <li><p><div class = "icon icon-wrappingstyle_inline"></div> <b>В тексте</b> - изображение считается частью текста, как отдельный символ, поэтому при перемещении текста изображение тоже перемещается. В этом случае параметры расположения недоступны.</p>
				    <p>Если выбран один из следующих стилей, изображение можно перемещать независимо от текста и точно задавать положение изображения на странице:</p>
				    </li>
				    <li><p><div class = "icon icon-wrappingstyle_square"></div> <b>Вокруг рамки</b> - текст обтекает прямоугольную рамку, которая окружает изображение.</p></li>
				    <li><p><div class = "icon icon-wrappingstyle_tight"></div> <b>По контуру</b> - текст обтекает реальные контуры изображения.</p></li>
				    <li><p><div class = "icon icon-wrappingstyle_through"></div> <b>Сквозное</b> - текст обтекает вокруг контуров изображения и заполняет незамкнутое свободное место внутри него. Чтобы этот эффект проявился, используйте опцию <b>Изменить границу обтекания</b> из контекстного меню.</p></li>
				    <li><p><div class = "icon icon-wrappingstyle_topandbottom"></div> <b>Сверху и снизу</b> - текст находится только выше и ниже изображения.</p></li>
				    <li><p><div class = "icon icon-wrappingstyle_infront"></div> <b>Перед текстом</b> - изображение перекрывает текст.</p></li>
				    <li><p><div class = "icon icon-wrappingstyle_behind"></div> <b>За текстом</b> - текст перекрывает изображение.</p></li>
				    </ul>
				</li>
			</ul>
			<p>При выборе стиля обтекания вокруг рамки, по контуру, сквозное или сверху и снизу можно задать дополнительные параметры - <b>расстояние до текста</b> со всех сторон (сверху, снизу, слева, справа).</p>
            <p id="position"><img alt="Изображение - дополнительные параметры: Положение" src="../images/image_properties_2.png" /></p>
            <p>Вкладка <b>Положение</b> доступна только в том случае, если выбран стиль обтекания, отличный от стиля "В тексте". Вкладка содержит следующие параметры, которые различаются в зависимости от выбранного стиля обтекания:</p>
			<ul>
				<li>В разделе <b>По горизонтали</b> можно выбрать один из следующих трех способов позиционирования изображения:
                <ul>
                    <li><b>Выравнивание</b> (по левому краю, по центру, по правому краю) <b>относительно</b> символа, столбца, левого поля, поля, страницы или правого поля,</li>
                    <li>Абсолютное <b>Положение</b>, определяемое в абсолютных единицах, то есть <b>Сантиметрах</b>/<b>Пунктах</b>/<b>Дюймах</b> (в зависимости от того, какой параметр указан на вкладке <b>Файл</b> -> <b>Дополнительные параметры...</b>), <b>справа от</b> символа, столбца, левого поля, поля, страницы или правого поля,</li>
                    <li><b>Относительное положение</b>, определяемое в процентах, <b>относительно</b> левого поля, поля, страницы или правого поля.</li>
                </ul> 
                </li>
				<li>В разделе <b>По вертикали</b> можно выбрать один из следующих трех способов позиционирования изображения:
                <ul>
                    <li><b>Выравнивание</b> (по верхнему краю, по центру, по нижнему краю) <b>относительно</b> строки, поля, нижнего поля, абзаца, страницы или верхнего поля,</li>
                    <li>Абсолютное <b>Положение</b>, определяемое в абсолютных единицах, то есть <b>Сантиметрах</b>/<b>Пунктах</b>/<b>Дюймах</b> (в зависимости от того, какой параметр указан на вкладке <b>Файл</b> -> <b>Дополнительные параметры...</b>), <b>ниже</b> строки, поля, нижнего поля, абзаца, страницы или верхнего поля,</li>
                    <li><b>Относительное положение</b>, определяемое в процентах, <b>относительно</b> поля, нижнего поля, страницы или верхнего поля.</li>
                </ul>
                </li>
				<li>Опция <b>Перемещать с текстом</b> определяет, будет ли изображение перемещаться вместе с текстом, к которому оно привязано.</li>
				<li>Опция <b>Разрешить перекрытие</b> определяет, будут ли перекрываться два изображения, если перетащить их близко друг к другу на странице.</li>
			</ul>
            <p><img alt="Изображение - дополнительные параметры" src="../images/image_properties_3.png" /></p>
            <p>Вкладка <b>Альтернативный текст</b> позволяет задать <b>Заголовок</b> и <b>Описание</b>, которые будут зачитываться для людей с нарушениями зрения или когнитивными нарушениями, чтобы помочь им лучше понять, какую информацию содержит изображение.</p>			
		</div>
	</body>
</html>