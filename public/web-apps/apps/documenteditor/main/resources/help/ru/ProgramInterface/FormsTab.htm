<!DOCTYPE html>
<html>
<head>
    <title>Вкладка Формы</title>
    <meta charset="utf-8" />
    <meta name="description" content="Знакомство с пользовательским интерфейсом редактора документов - Вкладка Формы" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Вкладка Формы</h1>
        <p class="note">эта вкладка доступна только для файлов DOCXF.</p>
        <p>Вкладка <b>Формы</b> <a href="https://www.onlyoffice.com/ru/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Редактора документов</b></a> позволяет создавать в документах заполняемые формы, например, проекты договоров или опросы. Добавляйте и редактируйте текст и поля формы, чтобы создать заполняемую форму любой сложности.</p>
        <div class="onlineDocumentFeatures">
            <p>Окно онлайн-редактора документов::</p>
            <p><img alt="Вкладка Формы" src="../images/interface/formstab.png" /></p>
        </div>
        <div class="desktopDocumentFeatures">
            <p>Окно десктопного редактора документов:</p>
            <p><img alt="Вкладка Формы" src="../images/interface/desktop_formstab.png" /></p>
        </div>
        <p>С помощью этой вкладки вы можете выполнить следующие действия:</p>
        <ul>
            <li>
                <a href="../UsageInstructions/CreateFillableForms.htm">Вставлять и редактировать</a>
                <ul>
                    <li><a href="../UsageInstructions/CreateFillableForms.htm#textfield">текстовые поля</a>,</li>
                    <li><a href="../UsageInstructions/CreateFillableForms.htm#combobox">поля со списком</a>,</li>
                    <li><a href="../UsageInstructions/CreateFillableForms.htm#dropdownlist">выпадающие списки</a>,</li>
                    <li><a href="../UsageInstructions/CreateFillableForms.htm#checkbox">флажки</a>,</li>
                    <li><a href="../UsageInstructions/CreateFillableForms.htm#radiobutton">переключатели</a>,</li>
                    <li><a href="../UsageInstructions/CreateFillableForms.htm#image">изображения</a>,</li>
                    <li> <a href="../UsageInstructions/CreateFillableForms.htm#emailaddress">адреса e-mail</a>,</li>
                    <li> <a href="../UsageInstructions/CreateFillableForms.htm#phonenumber">номера телефона</a>,</li>
                    <li> <a href="../UsageInstructions/CreateFillableForms.htm#datetime">дату и время</a>,</li>
                    <li> <a href="../UsageInstructions/CreateFillableForms.htm#zipcode">индексы</a>,</li>
                    <li> <a href="../UsageInstructions/CreateFillableForms.htm#creditcard">номера кредитных карт</a>,</li>
                    <li> <a href="../UsageInstructions/CreateFillableForms.htm#complexfield">составные поля</a>,</li>
                </ul>
            <li>очистить все поля и использовать настройки подсветки,</li>
            <li>перемещаться по полям формы с помощью кнопок <b>Предыдущее поле</b> и <b>Следующее поле</b>,</li>
            <li>просмотреть полученные формы в вашем документе,</li>
            <li><a href="../UsageInstructions/CreateFillableForms.htm#managing_roles">управлять ролями</a>,</li>
            <li>сохранить форму как заполняемый файл OFORM.</li>
        </ul>
    </div>
</body>
</html>