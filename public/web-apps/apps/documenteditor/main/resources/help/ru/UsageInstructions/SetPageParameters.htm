<!DOCTYPE html>
<html>
<head>
    <title>Настройка параметров страницы</title>
    <meta charset="utf-8" />
    <meta name="description" content="Задайте параметры страницы: измените ориентацию и размер страницы, настройте поля и вставьте колонки" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Настройка параметров страницы</h1>
        <p>Чтобы изменить разметку страницы, а именно задать ориентацию и размер страницы, настроить поля и вставить колонки, используйте соответствующие значки на вкладке <b>Макет</b> верхней панели инструментов.</p>
        <p class="note"><b>Примечание</b>: все эти параметры применяются ко всему документу. Если вам нужно установить разные поля, ориентацию, размер страниц или количество колонок в разных частях документа, обратитесь к <a href="../UsageInstructions/SectionBreaks.htm" onclick="onhyperlinkclick(this)">этой странице</a>.</p>
        <h3 id="orientation">Ориентация страницы</h3>
        <p>Измените текущий тип ориентации, нажав на значок <span class = "icon icon-orientation"></span> <b>Ориентация</b>. По умолчанию используется <b>Портретный</b> тип ориентации, который можно переключить на <b>Альбомный</b>.</p>
        <h3 id="size">Размер страницы</h3>
        <p>Измените используемый по умолчанию формат A4, нажав на значок <span class = "icon icon-pagesize"></span> <b>Размер</b> и выбрав нужный из списка. Доступны следующие предустановленные размеры:</p>
        <ul>
            <li>US Letter (21,59 см x 27,94 см)</li>
            <li>US Legal (21,59 см x 35,56 см)</li>
            <li>A4 (21 см x 29,7 см)</li>
            <li>A5 (14,81 см x 20,99 см)</li>
            <li>B5 (17,6 см x 25,01 см)</li>
            <li>Envelope #10 (10,48 см x 24,13 см)</li>
            <li>Envelope DL (11,01 см x 22,01 см)</li>
            <li>Tabloid (27,94 см x 43,17 см)</li>
            <li>AЗ (29,7 см x 42,01 см)</li>
            <li>Tabloid Oversize (30,48 см x 45,71 см)</li>
            <li>ROC 16K (19,68 см x 27,3 см)</li>
            <li>Envelope Choukei 3 (11,99 см x 23,49 см)</li>
            <li>Super B/A3 (33,02 см x 48,25 см)</li>
        </ul>
        <p>Можно также задать нестандартный размер страницы, выбрав из списка опцию <b>Особый размер страницы</b>. Откроется окно <b>Размер страницы</b>, в котором можно будет выбрать нужную <b>Предустановку</b> (US Letter, US Legal, A4, A5, B5, Envelope #10, Envelope DL, Tabloid, AЗ, Tabloid Oversize, ROC 16K, Envelope Choukei 3, Super B/A3, A0, A1, A2, A6) или указать произвольные значения <b>Ширины</b> и <b>Высоты</b>. Введите новые значения в поля ввода или скорректируйте имеющиеся значения с помощью кнопок со стрелками.</p> Когда все будет готово, нажмите кнопку <b>OK</b>, чтобы применить изменения.</p>
        <p><img alt="Особый размер страницы" src="../images/custompagesize.png" /></p>
        <h3 id="margins">Поля страницы</h3>
        <p>Измените используемые по умолчанию поля, то есть пустое пространство между левым, правым, верхним и нижним краями страницы и текстом абзаца, нажав на значок <span class = "icon icon-pagemargins"></span> <b>Поля</b> и выбрав один из доступных предустановленных вариантов: <b>Обычные</b>, <b>Обычные (американский стандарт)</b>, <b>Узкие</b>, <b>Средние</b>, <b>Широкие</b>. Можно также использовать опцию <b>Настраиваемые поля</b> и указать свои собственные значения в открывшемся окне <b>Поля</b>. Введите в поля ввода нужные значения для <b>Верхнего</b>, <b>Нижнего</b>, <b>Левого</b> и <b>Правого</b> полей страницы или скорректируйте имеющиеся значения с помощью кнопок со стрелками.</p>
        <p><img alt="Настраиваемые поля" src="../images/custommargins.png" /></p>
        <p><b>Положение переплета</b> используется для установки дополнительного пространства слева или сверху документа. Функция <b>Положение переплета</b> может пригодиться, чтобы убедиться, что переплет книги или документа не закрывает текст. В окне <b>Поля</b> введите нужную длину переплета в поле ввода и выберите место, куда он будет помещен.</p>
        <p class="note"><b>Примечание</b>: Когда выбрана опция <b>Зеркальные поля</b>, вы можете только изменить расстояние положения переплета, поскольку в данном случае он будет располагаться параллельно с двух противоположных сторон листа.</p>
        <p>В выпадающем списке <b>Несколько страниц</b> выберите опцию <b>Зеркальные поля</b>, чтобы настроить лицевые страницы для двусторонних документов. Когда выбрана данная опция, левые и правые поля превращаются в внутренние и внешние поля соответственно.</p>
        <p>В раскрывающемся меню  <b>Ориентация</b> выберите <b>Книжная</b> или <b>Альбомная</b>.</p>
        <p>Все внесенные в документ изменения будут отображены в окне <b>Предварительного просмотра</b>.</p>
        <p>Когда все будет готово, нажмите кнопку <b>OK</b>. Особые поля будут применены к текущему документу, а в списке <span class = "icon icon-pagemargins"></span> <b>Поля</b> появится пункт <b>Последние настраиваемые</b> с указанными параметрами, чтобы можно было применить их к каким-то другим документам.</p>
        <p>Поля можно также изменить вручную, перемещая мышью границу между серой и белой областью на линейке (серые области на линейке обозначают поля страниц):</p>
        <p><span class = "big big-margins"></span></p>
        <h3 id="columns">Колонки</h3>
        <p>Примените разметку с несколькими колонками, нажав на значок <span class = "icon icon-insertcolumns"></span> <b>Колонки</b> и выбрав из выпадающего списка нужный тип колонок. Доступны следующие варианты:</p>
        <ul>
            <li><b>Две</b> <div class = "icon icon-twocolumns"></div> - чтобы добавить две колонки одинаковой ширины,</li>
            <li><b>Три</b> <div class = "icon icon-threecolumns"></div> - чтобы добавить три колонки одинаковой ширины,</li>
            <li><b>Слева</b> <div class = "icon icon-leftcolumn"></div> - чтобы добавить две колонки: узкую слева и широкую справа,</li>
            <li><b>Справа</b> <div class = "icon icon-rightcolumn"></div> - чтобы добавить две колонки: узкую справа и широкую слева.</li>
        </ul>
        <p>Если требуется изменить параметры колонок, выберите из списка опцию <b>Настраиваемые колонки</b>. Откроется окно <b>Колонки</b>, в котором можно будет указать нужное <b>Количество колонок</b> (можно добавить не более 12 колонок) и <b>Интервал между колонками</b>. Введите новые значения в поля ввода или скорректируйте имеющиеся значения с помощью кнопок со стрелками. Отметьте опцию <b>Разделитель</b>, чтобы добавить вертикальную линию между колонками. Когда все будет готово, нажмите кнопку <b>OK</b>, чтобы применить изменения.</p>
        <p><img alt="Настраиваемые колонки" src="../images/customcolumns.png" /></p>
        <p>Чтобы точно определить, где должна начинаться новая колонка, установите курсор перед текстом, который требуется перенести в новую колонку, нажмите на значок <span class = "icon icon-pagebreak1"></span> <b>Разрывы</b> на верхней панели инструментов, а затем выберите опцию <b>Вставить разрыв колонки</b>. Текст будет перенесен в следующую колонку.</p>
        <p>Добавленные разрывы колонок обозначаются в документе пунктирной линией: <span class = "big big-columnbreak"></span>. Если вы не видите вставленных разрывов колонок, для их отображения нужно нажать на кнопку <span class = "icon icon-nonprintingcharacters"></span> на вкладке <b>Главная</b> верхней панели инструментов. Для того чтобы убрать разрыв колонки, выделите его мышью и нажмите клавишу <b>Delete</b>.</p>
        <p>Чтобы вручную изменить ширину колонок и расстояние между ними, можно использовать горизонтальную линейку.</p>
        <p><div class = "big big-columnspacing"></div></p>
        <p>Чтобы отменить разбиение на колонки и вернуться к обычной разметке с одной колонкой, нажмите на значок <span class = "icon icon-insertcolumns"></span> <b>Колонки</b> на верхней панели инструментов и выберите из списка опцию <b>Одна</b> <span class = "icon icon-onecolumn"></span>.</p>
    </div>
</body>
</html>