<!DOCTYPE html>
<html>
	<head>
		<title>Вставка автофигур</title>
		<meta charset="utf-8" />
		<meta name="description" content="Добавьте в документ автофигуру и настройте ее свойства." />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Вставка автофигур</h1>
            <h3>Вставка автофигуры</h3>
			<p>Для добавления автофигуры в документ:</p>
			<ol>
                <li>перейдите на вкладку <b>Вставка</b> верхней панели инструментов,</li>
                <li>щелкните по значку <div class = "icon icon-insertautoshape"></div> <b>Фигура</b> на верхней панели инструментов,</li>
				<li>выберите одну из доступных групп автофигур: <b>Последние использованные</b>, <b>Основные фигуры</b>, <b>Фигурные стрелки</b>, <b>Математические знаки</b>, <b>Схемы</b>, <b>Звезды и ленты</b>, <b>Выноски</b>, <b>Кнопки</b>, <b>Прямоугольники</b>, <b>Линии</b>,</li>
				<li>щелкните по нужной автофигуре внутри выбранной группы,</li>
				<li>установите курсор там, где требуется поместить автофигуру,
				</li>
				<li>после того, как автофигура будет добавлена, можно изменить ее размер, местоположение и свойства.
				<p class="note"><b>Примечание</b>: чтобы добавить надпись внутри фигуры, убедитесь, что фигура на странице выделена, и начинайте печатать текст. Текст, добавленный таким способом, становится частью автофигуры (при перемещении или повороте автофигуры текст будет перемещаться или поворачиваться вместе с ней).</p>
				</li>
			</ol>
            <p>К автофигуре также можно добавить подпись. Для получения дополнительной информации о работе с подписями к автофигурам вы можете обратиться к <a href="../UsageInstructions/AddCaption.htm" onclick="onhyperlinkclick(this)">этой статье</a>.</p>
            <h3>Перемещение и изменение размера автофигур</h3>
            <p id="shape_resize">Для изменения размера автофигуры перетаскивайте маленькие квадраты <span class = "icon icon-resize_square"></span>, расположенные по краям автофигуры. Чтобы сохранить исходные пропорции выбранной автофигуры при изменении размера, удерживайте клавишу <b>Shift</b> и перетаскивайте один из угловых значков.</p>
            <p><span class="big big-reshaping"></span></p>
            <p>При изменении некоторых фигур, например, фигурных стрелок или выносок, также доступен желтый значок в форме ромба <span class = "icon icon-yellowdiamond"></span>. Он позволяет изменять отдельные параметры формы, например, длину указателя стрелки.</p>
			<p>
				Для изменения местоположения автофигуры используйте значок <span class = "icon icon-arrow"></span>, который появляется после наведения курсора мыши на автофигуру. Перетащите автофигуру на нужное место, не отпуская кнопку мыши.
				При перемещении автофигуры на экране появляются направляющие, которые помогают точно расположить объект на странице (если выбран стиль обтекания, отличный от стиля "В тексте"). Чтобы перемещать автофигуру с шагом в один пиксель, удерживайте клавишу <b>Ctrl</b> и используйте стрелки на клавиатуре. 
				Чтобы перемещать автофигуру строго по горизонтали/вертикали и предотвратить ее смещение в перпендикулярном направлении, при перетаскивании удерживайте клавишу <b>Shift</b>.
			</p>
			<p>Чтобы повернуть автофигуру, наведите курсор мыши на маркер поворота <span class = "icon icon-greencircle"></span> и перетащите его по часовой стрелке или против часовой стрелки. Чтобы ограничить угол поворота шагом в 15 градусов, при поворачивании удерживайте клавишу <b>Shift</b>.</p>
            <p class="note">
                <b>Примечание</b>: список сочетаний клавиш, которые можно использовать при работе с объектами, доступен <a href="../HelpfulHints/KeyboardShortcuts.htm#workwithobjects" onclick="onhyperlinkclick(this)">здесь</a>.
            </p>
            <hr />
            <h3>Изменение параметров автофигуры</h3>
            <p id="shape_rightclickmenu">Чтобы выровнять или расположить автофигуры в определенном порядке, используйте <b>контекстное меню</b>. Меню содержит следующие пункты:</p>
			<ul>
				<li><b>Вырезать, копировать, вставить</b> - стандартные опции, которые используются для вырезания или копирования выделенного текста/объекта и вставки ранее вырезанного/скопированного фрагмента текста или объекта в то место, где находится курсор.</li>
				<li><b>Напечатать выделенное</b> - используется для печати только выбранной части документа.</li>
				<li><b>Принять / Отклонить изменения</b> - используется для принятия или отклонения отслеживаемых изменений в общем документе.</li>
				<li><b>Изменить точки</b> - используется для редактирования формы или изменения кривизны автофигуры.
					<ol>
						<li>Чтобы активировать редактируемые опорные точки фигуры, щелкните по фигуре правой кнопкой мыши и в контекстном меню выберите пункт <b>Изменить точки</b>. Черные квадраты, которые становятся активными, — это точки, где встречаются две линии, а красная линия очерчивает фигуру. Щелкните и перетащите квадрат, чтобы изменить положение точки и изменить контур фигуры.</li>
						<li>
							После сдвига опорной точки фигуры, появятся две синие линии с белыми квадратами на концах. Это кривые Безье, которые позволяют создавать кривую и изменять ее значение.
							<p><img alt="Редактировать Точки" src="../images/editpoints_example.png" /></p>
						</li>
						<li>
							Пока опорные точки активны, вы можете добавлять и удалять их.
							<p><b>Чтобы добавить точку к фигуре</b>, удерживайте <b>Ctrl</b> и щелкните место, где вы хотите добавить опорную точку.</p>
							<p><b>Чтобы удалить точку</b>, удерживайте <b>Ctrl</b> и щелкните по ненужной точке.</p>
						</li>
					</ol>
				</li>
				<li><b>Порядок</b> - используется, чтобы вынести выбранную автофигуру на передний план, переместить на задний план, перенести вперед или назад, а также сгруппировать или разгруппировать автофигуры для выполнения операций над несколькими из них сразу. Подробнее о расположении объектов в определенном порядке рассказывается на <a href="../UsageInstructions/AlignArrangeObjects.htm" onclick="onhyperlinkclick(this)">этой странице</a>.</li>
				<li><b>Выравнивание</b> - используется, чтобы выровнять фигуру по левому краю, по центру, по правому краю, по верхнему краю, по середине, по нижнему краю. Подробнее о выравнивании объектов рассказывается на <a href="../UsageInstructions/AlignArrangeObjects.htm" onclick="onhyperlinkclick(this)">этой странице</a>.</li>
				<li><b>Стиль обтекания</b> - используется, чтобы выбрать один из доступных стилей обтекания текстом - в тексте, вокруг рамки, по контуру, сквозное, сверху и снизу, перед текстом, за текстом - или для изменения границы обтекания. Опция <b>Изменить границу обтекания</b> доступна только в том случае, если выбран стиль обтекания, отличный от стиля "В тексте". Чтобы произвольно изменить границу, перетаскивайте точки границы обтекания. Чтобы создать новую точку границы обтекания, щелкните в любом месте на красной линии и перетащите ее в нужную позицию. <div class = "big big-wrap_boundary"></div></li>
				<li><b>Поворот</b> - используется, чтобы повернуть фигуру на 90 градусов по часовой стрелке или против часовой стрелки, а также чтобы отразить фигуру слева направо или сверху вниз.</li>
				<li><b>Дополнительные параметры фигуры</b> - используется для вызова окна 'Фигура - дополнительные параметры'.</li>
			</ul>
			<hr />
			<p>Некоторые параметры автофигуры можно изменить с помощью вкладки <b>Параметры фигуры</b> на правой боковой панели. Чтобы ее активировать, щелкните по фигуре и выберите значок <b>Параметры фигуры</b> <span class = "icon icon-shape_settings_icon"></span> справа. Здесь можно изменить следующие свойства:</p>
			<ul>
			<li id="shape_fill"><b>Заливка</b> - используйте этот раздел, чтобы выбрать заливку автофигуры. Можно выбрать следующие варианты:
			    <ul>
				<li><b>Заливка цветом</b> - выберите эту опцию, чтобы задать сплошной цвет, которым требуется заполнить внутреннее пространство выбранной фигуры.
				<p><img alt="Заливка цветом" src="../images/fill_color.png" /></p>
				<p id="color">Нажмите на цветной прямоугольник, расположенный ниже, и выберите нужный цвет из доступных <a href="../UsageInstructions/ChangeColorScheme.htm" onclick="onhyperlinkclick(this)">наборов цветов</a> или задайте любой цвет, который вам нравится.</p>
        		</li>
				<li><b>Градиентная заливка</b> - выберите эту опцию, чтобы залить фигуру двумя цветами, плавно переходящими друг в друга.
				<p><img alt="Градиентная заливка" src="../images/fill_gradient.png" /></p>
				<ul>
					<li>
						<b>Стиль</b> - выберите <b>Линейный</b> или <b>Радиальный</b>:
						<ul>
							<li><b>Линейный</b> используется, когда вам нужно, чтобы цвета изменялись слева направо, сверху вниз или под любым выбранным вами углом в одном направлении. Чтобы выбрать предустановленное направление, щелкните на стрелку рядом с окном предварительного просмотра <b>Направление</b> или же задайте точное значение угла градиента в поле <b>Угол</b>.</li>
							<li><b>Радиальный</b> используется, когда вам нужно, чтобы цвета изменялись по кругу от центра к краям.</li>
						</ul>
					</li>
					<li>
						<b>Точка градиента</b> - это определенная точка перехода от одного цвета к другому.
						<ul>
							<li>Чтобы добавить точку градиента, Используйте кнопку <div class = "icon icon-addgradientpoint"></div> <b>Добавить точку градиента</b> или ползунок. Вы можете добавить до 10 точек градиента. Каждая следующая добавленная точка градиента никоим образом не повлияет на внешний вид текущей градиентной заливки. Чтобы удалить определенную точку градиента, используйте кнопку  <div class = "icon icon-removegradientpoint"></div> <b>Удалить точку градиента</b>.</li>
							<li>Чтобы изменить положение точки градиента, используйте ползунок или укажите <b>Положение</b> в процентах для точного местоположения.</li>
							<li>Чтобы применить цвет к точке градиента, щелкните точку на панели ползунка, а затем нажмите <b>Цвет</b>, чтобы выбрать нужный цвет.</li>
						</ul>
					</li>
				</ul>			
				</li>
				<li><b>Изображение или текстура</b> - выберите эту опцию, чтобы использовать в качестве фона фигуры какое-то изображение или готовую текстуру.
				<p><img alt="Заливка с помощью изображения или текстуры" src="../images/fill_picture.png" /></p>
					<ul>
						<li>Если Вы хотите использовать изображение в качестве фона фигуры, можно добавить изображение <b>Из файла</b>, выбрав его на жестком диске компьютера, или <b>По URL</b>, вставив в открывшемся окне соответствующий URL-адрес, или <b>Из хранилища</b>, выбрав нужное изображение, сохраненное на портале.</li>
						<li>Если Вы хотите использовать текстуру в качестве фона фигуры, разверните меню <b>Из текстуры</b> и выберите нужную предустановленную текстуру.
				    <p>В настоящее время доступны следующие текстуры: Холст, Картон, Темная ткань, Песок, Гранит, Серая бумага, Вязание, Кожа, Крафт-бумага, Папирус, Дерево.</p>
						</li>
					</ul>
					<ul>
						<li>В том случае, если выбранное <b>изображение</b> имеет большие или меньшие размеры, чем автофигура, можно выбрать из выпадающего списка параметр <b>Растяжение</b> или <b>Плитка</b>.
			        <p>Опция <b>Растяжение</b> позволяет подогнать размер изображения под размер автофигуры, чтобы оно могло полностью заполнить пространство.</p>
			        <p>Опция <b>Плитка</b> позволяет отображать только часть большего изображения, сохраняя его исходные размеры, или повторять меньшее изображение, сохраняя его исходные размеры, по всей площади автофигуры, чтобы оно могло полностью заполнить пространство.</p>
			        <p class="note"><b>Примечание</b>: любая выбранная предустановленная <b>текстура</b> полностью заполняет пространство, но в случае необходимости можно применить эффект <b>Растяжение</b>.</p>
						</li>
					</ul>
				</li>
				<li><b>Узор</b> - выберите эту опцию, чтобы залить фигуру с помощью двухцветного рисунка, который образован регулярно повторяющимися элементами.
				<p><img alt="Заливка с помощью узора" src="../images/fill_pattern.png" /></p>
				    <ul>
				        <li><b>Узор</b> - выберите один из готовых рисунков в меню.</li>
				        <li><b>Цвет переднего плана</b> - нажмите на это цветовое поле, чтобы изменить цвет элементов узора.</li>
				        <li><b>Цвет фона</b> - нажмите на это цветовое поле, чтобы изменить цвет фона узора.</li>
				    </ul>			
				</li>
				<li><b>Без заливки</b> - выберите эту опцию, если Вы вообще не хотите использовать заливку.</li>
			</ul>
			</li>
			</ul>
			<p><img class="floatleft" alt="Вкладка Параметры автофигуры" src="../images/right_autoshape.png" /></p>
			<ul style="margin-left: 280px;">
			<li><b>Непрозрачность</b> - используйте этот раздел, чтобы задать уровень <b>Непрозрачности</b>, перетаскивая ползунок или вручную вводя значение в процентах. Значение, заданное по умолчанию, составляет <b>100%</b>. Оно соответствует полной непрозрачности. Значение <b>0%</b> соответствует полной прозрачности.</li>
			<li id="shape_stroke"><b>Контур</b> - используйте этот раздел, чтобы изменить толщину, цвет или тип контура.
				<ul>
				<li>Для изменения <b>толщины</b> контура выберите из выпадающего списка <b>Толщина</b> одну из доступных опций. Доступны следующие опции: 0.5 пт, 1 пт, 1.5 пт, 2.25 пт, 3 пт, 4.5 пт, 6 пт. Или выберите опцию <b>Без линии</b>, если вы вообще не хотите использовать контур.</li>
				<li>Для изменения <b>цвета</b> контура щелкните по цветному прямоугольнику и <a href="../UsageInstructions/ChangeColorScheme.htm" onclick="onhyperlinkclick(this)">выберите нужный цвет</a>.</li>
				<li>Для изменения <b>типа</b> контура выберите нужную опцию из соответствующего выпадающего списка (по умолчанию применяется сплошная линия, ее можно изменить на одну из доступных пунктирных линий).</li>
				</ul>
			</li>
            <li><b>Поворот</b> - используется, чтобы повернуть фигуру на 90 градусов по часовой стрелке или против часовой стрелки, а также чтобы отразить фигуру слева направо или сверху вниз. Нажмите на одну из кнопок:
                <ul>
                    <li><div class = "icon icon-rotatecounterclockwise"></div> чтобы повернуть фигуру на 90 градусов против часовой стрелки</li>
                    <li><div class = "icon icon-rotateclockwise"></div> чтобы повернуть фигуру на 90 градусов по часовой стрелке</li>
                    <li><div class = "icon icon-fliplefttoright"></div> чтобы отразить фигуру по горизонтали (слева направо)</li>
                    <li><div class = "icon icon-flipupsidedown"></div> чтобы отразить фигуру по вертикали (сверху вниз)</li>
                </ul>
            </li>
			<li><b>Стиль обтекания</b> - используйте этот раздел, чтобы выбрать один из доступных стилей обтекания текстом - в тексте, вокруг рамки, по контуру, сквозное, сверху и снизу, перед текстом, за текстом (для получения дополнительной информации смотрите описание дополнительных параметров ниже).</li>
			<li><b>Изменить автофигуру</b> - используйте этот раздел, чтобы заменить текущую автофигуру на другую, выбрав ее из выпадающего списка.</li>
            <li><b>Отображать тень</b> - отметьте эту опцию, чтобы отображать фигуру с тенью.</li>
			</ul>
			<hr />
            <h3 id="autoshape_advanced">Изменение дополнительных параметров автофигуры</h3>
			<p>Чтобы изменить дополнительные параметры автофигуры, щелкните по ней правой кнопкой мыши и выберите из контекстного меню пункт <b>Дополнительные параметры</b>. Или нажмите ссылку <b>Дополнительные параметры</b> на правой боковой панели. Откроется окно 'Фигура - дополнительные параметры':</p>
            <p><img alt="Фигура - дополнительные параметры" src="../images/shape_properties.png" /></p>
			<p>Вкладка <b>Размер</b> содержит следующие параметры:</p>
			<ul>
                <li>
                    <b>Ширина</b> - используйте одну из этих опций, чтобы изменить ширину автофигуры.
                    <ul>
                        <li><b>Абсолютная</b> - укажите точное значение, определяемое в абсолютных единицах, то есть <b>Сантиметрах</b>/<b>Пунктах</b>/<b>Дюймах</b> (в зависимости от того, какой параметр указан на вкладке <b>Файл</b> -> <b>Дополнительные параметры...</b>).</li>
                        <li><b>Относительная</b> - укажите размер в процентах <b>относительно</b> ширины <em>левого поля</em>, <em>поля</em> (то есть расстояния между левым и правым полями), ширины <em>страницы</em> или ширины <em>правого</em> поля.</li>
                    </ul>
                </li>
                <li>
                    <b>Высота</b> - используйте одну из этих опций, чтобы изменить высоту автофигуры.
                    <ul>
                        <li><b>Абсолютная</b> - укажите точное значение, определяемое в абсолютных единицах, то есть <b>Сантиметрах</b>/<b>Пунктах</b>/<b>Дюймах</b> (в зависимости от того, какой параметр указан на вкладке <b>Файл</b> -> <b>Дополнительные параметры...</b>).</li>
                        <li><b>Относительная</b> - укажите размер в процентах <b>относительно</b> <em>поля</em> (то есть расстояния между верхним и нижним полями), высоты <em>нижнего поля</em>, высоты <em>страницы</em> или высоты <em>верхнего поля</em>.</li>
                    </ul>
                </li>
                <li>Если установлен флажок <b>Сохранять пропорции</b>, ширина и высота будут изменены пропорционально, сохраняя исходное соотношение сторон фигуры.</li>
            </ul>
            <p><img alt="Фигура - дополнительные параметры: Поворот" src="../images/shape_properties_6.png" /></p>
            <p>Вкладка <b>Поворот</b> содержит следующие параметры:</p>
            <ul>
                <li><b>Угол</b> - используйте эту опцию, чтобы повернуть фигуру на точно заданный угол. Введите в поле нужное значение в градусах или скорректируйте его, используя стрелки справа. </li>
                <li><b>Отражено</b> - отметьте галочкой опцию <b>По горизонтали</b>, чтобы отразить фигуру по горизонтали (слева направо), или отметьте галочкой опцию <b>По вертикали</b>, чтобы отразить фигуру по вертикали (сверху вниз).</li>
            </ul>
            <p id="shape_wrapping"><img alt="Фигура - дополнительные параметры" src="../images/shape_properties_1.png" /></p>
			<p>Вкладка <b>Обтекание текстом</b> содержит следующие параметры:</p>
			<ul>
				<li><b>Стиль обтекания</b> - используйте эту опцию, чтобы изменить способ размещения автофигуры относительно текста: или она будет являться частью текста (если выбран стиль обтекания "В тексте") или текст будет обтекать ее со всех сторон (если выбран один из остальных стилей).
				<ul>
				    <li><p><div class = "icon icon-wrappingstyle_inline"></div> <b>В тексте</b> - автофигура считается частью текста, как отдельный символ, поэтому при перемещении текста фигура тоже перемещается. В этом случае параметры расположения недоступны.</p>
				    <p>Если выбран один из следующих стилей, автофигуру можно перемещать независимо от текста и и точно задавать положение фигуры на странице:</p>
				    </li>
				    <li><p><div class = "icon icon-wrappingstyle_square"></div> <b>Вокруг рамки</b> - текст обтекает прямоугольную рамку, которая окружает автофигуру.</p></li>
				    <li><p><div class = "icon icon-wrappingstyle_tight"></div> <b>По контуру</b> - текст обтекает реальные контуры автофигуры.</p></li>
				    <li><p><div class = "icon icon-wrappingstyle_through"></div> <b>Сквозное</b> - текст обтекает вокруг контуров автофигуры и заполняет незамкнутое свободное место внутри фигуры. Чтобы этот эффект проявился, используйте опцию <b>Изменить границу обтекания</b> из контекстного меню.</p></li>
				    <li><p><div class = "icon icon-wrappingstyle_topandbottom"></div> <b>Сверху и снизу</b> - текст находится только выше и ниже автофигуры.</p></li>
				    <li><p><div class = "icon icon-wrappingstyle_infront"></div> <b>Перед текстом</b> - автофигура перекрывает текст.</p></li>
				    <li><p><div class = "icon icon-wrappingstyle_behind"></div> <b>За текстом</b> - текст перекрывает автофигуру.</p></li>
				    </ul>
				</li>
			</ul>
			<p>При выборе стиля обтекания вокруг рамки, по контуру, сквозное или сверху и снизу можно задать дополнительные параметры - <b>расстояние до текста</b> со всех сторон (сверху, снизу, слева, справа).</p>
            <p id="position"><img alt="Фигура - дополнительные параметры" src="../images/shape_properties_2.png" /></p>
            <p>Вкладка <b>Положение</b> доступна только в том случае, если выбран стиль обтекания, отличный от стиля "В тексте". Вкладка содержит следующие параметры, которые различаются в зависимости от выбранного стиля обтекания:</p>
			<ul>
                <li>
                    В разделе <b>По горизонтали</b> можно выбрать один из следующих трех способов позиционирования автофигуры:
                    <ul>
                        <li><b>Выравнивание</b> (по левому краю, по центру, по правому краю) <b>относительно</b> символа, столбца, левого поля, поля, страницы или правого поля,</li>
                        <li>Абсолютное <b>Положение</b>, определяемое в абсолютных единицах, то есть <b>Сантиметрах</b>/<b>Пунктах</b>/<b>Дюймах</b> (в зависимости от того, какой параметр указан на вкладке <b>Файл</b> -> <b>Дополнительные параметры...</b>), <b>справа от</b> символа, столбца, левого поля, поля, страницы или правого поля,</li>
                        <li><b>Относительное положение</b>, определяемое в процентах, <b>относительно</b> левого поля, поля, страницы или правого поля.</li>
                    </ul>
                </li>
                <li>
                    В разделе <b>По вертикали</b> можно выбрать один из следующих трех способов позиционирования автофигуры:
                    <ul>
                        <li><b>Выравнивание</b> (по верхнему краю, по центру, по нижнему краю) <b>относительно</b> строки, поля, нижнего поля, абзаца, страницы или верхнего поля,</li>
                        <li>Абсолютное <b>Положение</b>, определяемое в абсолютных единицах, то есть <b>Сантиметрах</b>/<b>Пунктах</b>/<b>Дюймах</b> (в зависимости от того, какой параметр указан на вкладке <b>Файл</b> -> <b>Дополнительные параметры...</b>), <b>ниже</b> строки, поля, нижнего поля, абзаца, страницы или верхнего поля,</li>
                        <li><b>Относительное положение</b>, определяемое в процентах, <b>относительно</b> поля, нижнего поля, страницы или верхнего поля.</li>
                    </ul>
                </li>
                <li>Опция <b>Перемещать с текстом</b> определяет, будет ли автофигура перемещаться вместе с текстом, к которому она привязана.</li>
				<li>Опция <b>Разрешить перекрытие</b> определяет, будут ли перекрываться две автофигуры, если перетащить их близко друг к другу на странице.</li>
			</ul>
			<p><img alt="Фигура - дополнительные параметры" src="../images/shape_properties_3.png" /></p>
			<p>Вкладка <b>Линии и стрелки</b> содержит следующие параметры:</p>
			<ul>
				<li><b>Стиль линии</b> - эта группа опций позволяет задать такие параметры:
				<ul>
				<li><b>Тип окончания</b> - эта опция позволяет задать стиль окончания линии, поэтому ее можно применить только для фигур с разомкнутым контуром, таких как линии, ломаные линии и т.д.: 
					<ul>
					<li><b>Плоский</b> - конечные точки будут плоскими.</li>
				    <li><b>Закругленный</b> - конечные точки будут закругленными.</li>
				    <li><b>Квадратный</b> - конечные точки будут квадратными.</li>
					</ul>
				</li>
				<li><b>Тип соединения</b> - эта опция позволяет задать стиль пересечения двух линий, например, она может повлиять на контур ломаной линии или углов треугольника или прямоугольника:
					<ul>
					<li><b>Закругленный</b> - угол будет закругленным.</li>
    				<li><b>Скошенный</b> - угол будет срезан наискось.</li>
    				<li><b>Прямой</b> - угол будет заостренным. Хорошо подходит для фигур с острыми углами.</li>
					</ul>
					<p class="note"><b>Примечание</b>: эффект будет лучше заметен при использовании контура большей толщины.</p>
				</li>
				</ul>
				</li>
				<li><b>Стрелки</b> - эта группа опций доступна только в том случае, если выбрана фигура из группы автофигур <b>Линии</b>. Она позволяет задать <b>Начальный</b> и <b>Конечный стиль</b> и <b>Размер</b> стрелки, выбрав соответствующие опции из выпадающих списков.</li>
			</ul>
            <p><img alt="Фигура - дополнительные параметры" src="../images/shape_properties_4.png" /></p>
			<p>На вкладке <b>Текстовое поле</b> можно <b>Подгонять размер фигуры под текст</b> или изменить внутренние поля автофигуры <b>Сверху</b>, <b>Снизу</b>, <b>Слева</b> и <b>Справа</b> (то есть расстояние между текстом внутри фигуры и границами автофигуры).</p>
            <p class="note"><b>Примечание</b>: эта вкладка доступна, только если в автофигуру добавлен текст, в противном случае вкладка неактивна.</p>
            <p><img alt="Фигура - дополнительные параметры" src="../images/shape_properties_5.png" /></p>
            <p>Вкладка <b>Альтернативный текст</b> позволяет задать <b>Заголовок</b> и <b>Описание</b>, которые будут зачитываться для людей с нарушениями зрения или когнитивными нарушениями, чтобы помочь им лучше понять, какую информацию содержит фигура.</p>
		</div>
	</body>
</html>