<!DOCTYPE html>
<html>
	<head>
		<title>Выравнивание текста в абзаце</title>
		<meta charset="utf-8" />
		<meta name="description" content="Все о выравнивании текста в абзаце: выравнивание по левому или правому краю, по ширине, по центру" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Выравнивание текста в абзаце</h1>
			<p>Текст обычно выравнивается четырьмя способами: по левому краю, по правому краю, по центру или по ширине. Для этого:</p>
			<ol>
				<li>установите курсор в том месте, где требуется применить выравнивание (это может быть новая строка или уже введенный текст),</li>
                <li>перейдите на вкладку <b>Главная</b> верхней панели инструментов,</li>
                <li>выберите тип выравнивания, который надо применить:
					<ul>
						<li>Выравнивание по <b>левому краю</b>, при котором текст выравнивается по левому краю страницы (правый край остается невыровненным), выполняется с помощью значка <b>Выравнивание по левому краю</b> <div class = "icon icon-alignleft"></div>, расположенного на верхней панели инструментов.</li>
						<li>Выравнивание по <b>центру</b>, при котором текст выравнивается по центру страницы (правый и левый края остаются невыровненными), выполняется с помощью значка <b>Выравнивание по центру</b> <div class = "icon icon-aligncenter"></div>, расположенного на верхней панели инструментов.</li>
						<li>Выравнивание по <b>правому краю</b>, при котором текст выравнивается по правому краю страницы (левый край остается невыровненным), выполняется с помощью значка <b>Выравнивание по правому краю</b> <div class = "icon icon-alignright"></div>, расположенного на верхней панели инструментов.</li>
						<li>Выравнивание по <b>ширине</b>, при котором текст выравнивается как по левому, так и по правому краю страницы (выравнивание осуществляется за счет добавления дополнительных интервалов там, где это необходимо), выполняется с помощью значка <b>Выравнивание по ширине</b> <div class = "icon icon-onecolumn"></div>, расположенного на верхней панели инструментов.</li>
					</ul>
				</li>
			</ol>
            <p>Параметры выравнивания также доступны в окне <b>Абзац - дополнительные параметры</b>:</p>
            <ol>
                <li>щелкните по тексту правой кнопкой мыши и выберите в контекстном меню опцию <b>Дополнительные параметры абзаца</b> или используйте опцию <b>Дополнительные параметры</b> на правой боковой панели,</li>
                <li>откройте окно <b>Абзац - дополнительные параметры</b>, перейдите на вкладку <b>Отступы и интервалы</b>,</li>
                <li>в списке <b>Выравнивание</b> выберите один из типов выравнивания: <b>По левому краю</b>, <b>По центру</b>, <b>По правому краю</b>, <b>По ширине</b>,</li>
                <li>нажмите кнопку <b>OK</b>, чтобы применить изменения.</li>
            </ol>
            <p><img alt="Дополнительные параметры абзаца - Отступы и интервалы" src="../images/paradvsettings_indents.png" /></p>
		</div>
	</body>
</html>