<!DOCTYPE html>
<html>
	<head>
		<title>Вставка текстовых объектов</title>
		<meta charset="utf-8" />
        <meta name="description" content="Вставьте текстовые объекты, такие как надписи и объекты Text Art, чтобы сделать текст выразительнее" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Вставка текстовых объектов</h1>
            <p>Чтобы сделать текст более выразительным и привлечь внимание к определенной части документа, можно вставить надпись (прямоугольную рамку, внутри которой вводится текст) или объект Text Art (текстовое поле с предварительно заданным стилем и цветом шрифта, позволяющее применять текстовые эффекты).</p>
			<h3>Добавление текстового объекта</h3>
            <p>Текстовый объект можно добавить в любом месте страницы. Для этого:</p>
            <ol>
                <li>перейдите на вкладку <b>Вставка</b> верхней панели инструментов,</li>
                <li>выберите нужный тип текстового объекта:
                <ul>
                    <li>чтобы добавить текстовое поле, щелкните по значку <div class = "icon icon-inserttexticon"></div> <b>Надпись</b> на верхней панели инструментов, затем щелкните там, где требуется поместить надпись, удерживайте кнопку мыши и перетаскивайте границу текстового поля, чтобы задать его размер. Когда вы отпустите кнопку мыши, в добавленном текстовом поле появится курсор, и вы сможете ввести свой текст.
                    <p class="note"><b>Примечание</b>: надпись можно также вставить, если щелкнуть по значку <span class = "icon icon-insertautoshape"></span> <b>Фигура</b> на верхней панели инструментов и выбрать фигуру <span class = "icon icon-text_autoshape"></span> из группы <b>Основные фигуры</b>.</p>
                    </li>
                    <li>чтобы добавить объект Text Art, щелкните по значку <div class = "icon icon-inserttextarticon"></div> <b>Text Art</b>, затем щелкните по нужному шаблону стиля – объект Text Art будет добавлен в текущей позиции курсора. Выделите мышью стандартный текст внутри текстового поля и напишите вместо него свой текст.</li>
                </ul>
                </li>
                <li>щелкните за пределами текстового объекта, чтобы применить изменения и вернуться к документу.</li>
            </ol>
            <p>Текст внутри текстового объекта является его частью (при перемещении или повороте текстового объекта текст будет перемещаться или поворачиваться вместе с ним).</p>
            <p>Поскольку вставленный текстовый объект представляет собой прямоугольную рамку с текстом внутри (у объектов Text Art по умолчанию невидимые границы), а эта рамка является обычной автофигурой, можно изменять свойства и фигуры, и текста.</p>
            <p>Чтобы удалить добавленный текстовый объект, щелкните по краю текстового поля и нажмите клавишу <b>Delete</b> на клавиатуре. Текст внутри текстового поля тоже будет удален.</p>
            <h3>Форматирование текстового поля</h3>
            <p>Выделите текстовое поле, щелкнув по его границе, чтобы можно было изменить его свойства. Когда текстовое поле выделено, его границы отображаются как сплошные, а не пунктирные линии.</p>
            <p><img alt="Выделенное текстовое поле" src="../images/textbox_boxselected.png" /></p>
            <ul>
                <li>чтобы <a href="../UsageInstructions/InsertAutoshapes.htm#shape_resize" onclick="onhyperlinkclick(this)">изменить размер текстового поля, переместить, повернуть</a> его, используйте специальные маркеры по краям фигуры.</li>
                <li>чтобы изменить <a href="../UsageInstructions/InsertAutoshapes.htm#shape_fill" onclick="onhyperlinkclick(this)">заливку</a>, <a href="../UsageInstructions/InsertAutoshapes.htm#shape_stroke" onclick="onhyperlinkclick(this)">контур</a>, <a href="../UsageInstructions/InsertAutoshapes.htm#shape_wrapping" onclick="onhyperlinkclick(this)">стиль обтекания</a> текстового поля или <b>заменить</b> прямоугольное поле на какую-то другую фигуру, щелкните по значку <b>Параметры фигуры</b> <div class = "icon icon-shape_settings_icon"></div> на правой боковой панели и используйте соответствующие опции.</li>                
                <li>чтобы <b>выровнять</b> текстовое поле на странице, расположить текстовые поля <b>в определенном порядке</b> относительно других объектов, <b>повернуть или отразить</b> текстовое поле, изменить <b>стиль обтекания</b> или открыть <b>дополнительные параметры</b> фигуры, щелкните правой кнопкой мыши по границе текстового поля и используйте <a href="../UsageInstructions/InsertAutoshapes.htm#shape_rightclickmenu" onclick="onhyperlinkclick(this)">опции контекстного меню</a>. Подробнее о выравнивании и расположении объектов в определенном порядке рассказывается на <a href="../UsageInstructions/AlignArrangeObjects.htm" onclick="onhyperlinkclick(this)">этой странице</a>.</li>
            </ul>
            <h3>Форматирование текста внутри текстового поля</h3>
            <p>Щелкните по тексту внутри текстового поля, чтобы можно было изменить его свойства. Когда текст выделен, границы текстового поля отображаются как пунктирные линии.</p>
            <p><img alt="Выделенный текст" src="../images/textbox_textselected.png" /></p>
            <p class="note"><b>Примечание</b>: форматирование текста можно изменить и в том случае, если выделено текстовое поле, а не сам текст. В этом случае любые изменения будут применены ко всему тексту в текстовом поле. Некоторые параметры форматирования шрифта (тип, размер, цвет и стили оформления шрифта) можно отдельно применить к предварительно выделенному фрагменту текста.</p>
            <p>Чтобы <b>повернуть</b> текст внутри текстового поля, щелкните по тексту правой кнопкой мыши, выберите опцию <b>Направление текста</b>, а затем - один из доступных вариантов: <b>Горизонтальное</b> (выбран по умолчанию), <b>Повернуть текст вниз</b> (задает вертикальное направление, сверху вниз) или <b>Повернуть текст вверх</b> (задает вертикальное направление, снизу вверх).</p>
            <p>Чтобы <b>выровнять текст внутри текстового поля по вертикали</b>, щелкните по тексту правой кнопкой мыши, выберите опцию <b>Вертикальное выравнивание</b>, а затем - один из доступных вариантов: <b>По верхнему краю</b>, <b>По центру</b> или <b>По нижнему краю</b>.</p>
            <p>Другие параметры форматирования, которые можно применить, точно такие же, как и для обычного текста. Обратитесь к соответствующим разделам справки за дополнительными сведениями о нужном действии. Вы можете:</p>
            <ul>
                <li><a href="../UsageInstructions/AlignText.htm" onclick="onhyperlinkclick(this)">выровнять текст внутри текстового поля по горизонтали</a></li>
                <li>изменить <a href="../UsageInstructions/FontTypeSizeColor.htm" onclick="onhyperlinkclick(this)">тип, размер, цвет шрифта</a>, применить <a href="../UsageInstructions/DecorationStyles.htm" onclick="onhyperlinkclick(this)">стили оформления</a> и <a href="../UsageInstructions/FormattingPresets.htm" onclick="onhyperlinkclick(this)">предустановленные стили форматирования</a></li>
                <li>задать <a href="../UsageInstructions/LineSpacing.htm" onclick="onhyperlinkclick(this)">междустрочный интервал</a>, изменить <a href="../UsageInstructions/ParagraphIndents.htm" onclick="onhyperlinkclick(this)">отступы абзаца</a>, настроить <a href="../UsageInstructions/SetTabStops.htm" onclick="onhyperlinkclick(this)">позиции табуляции</a> для многострочного текста внутри текстового поля</li>
                <li>вставить <a href="../UsageInstructions/AddHyperlinks.htm" onclick="onhyperlinkclick(this)">гиперссылку</a></li>
            </ul>
            <p>Можно также нажать на значок <b>Параметры объекта Text Art</b> <span class = "icon icon-textart_settings_icon"></span> на правой боковой панели и изменить некоторые параметры стиля.</p>
            <h3>Изменение стиля объекта Text Art</h3>
            <p>Выделите текстовый объект и щелкните по значку <b>Параметры объекта Text Art</b> <span class = "icon icon-textart_settings_icon"></span> на правой боковой панели.</p>
            <p><img alt="Вкладка Параметры объекта Text Art" src="../images/right_textart.png" /></p>
            <p>Измените примененный стиль текста, выбрав из галереи новый <b>Шаблон</b>. Можно также дополнительно изменить этот базовый стиль, выбрав другой тип, размер шрифта и т.д.</p>
            <p>Измените <b>Заливку</b> шрифта. Можно выбрать следующие варианты:</p>
            <ul>
                <li>
                    <b>Заливка цветом</b> - выберите эту опцию, чтобы задать сплошной цвет, которым требуется заполнить внутреннее пространство букв.
                    <p><img alt="Заливка цветом" src="../images/fill_color.png" /></p>
                    <p id="color">Нажмите на цветной прямоугольник, расположенный ниже, и выберите нужный цвет из доступных <a href="../UsageInstructions/ChangeColorScheme.htm" onclick="onhyperlinkclick(this)">наборов цветов</a> или задайте любой цвет, который вам нравится:</p>
                </li>
                <li>
                    <b>Градиентная заливка</b> - выберите эту опцию, чтобы залить буквы двумя цветами, плавно переходящими друг в друга.
                    <p><img alt="Градиентная заливка" src="../images/fill_gradient.png" /></p>
                    <ul>
                        <li>
                            <b>Стиль</b> - выберите <b>Линейный</b> или <b>Радиальный</b>:
                            <ul>
                                <li><b>Линейный</b> используется, когда вам нужно, чтобы цвета изменялись слева направо, сверху вниз или под любым выбранным вами углом в одном направлении. Чтобы выбрать предустановленное направление, щелкните <b>Направление</b> или же задайте точное значение угла градиента в поле <b>Угол</b>.</li>
                                <li><b>Радиальный</b> используется, когда вам нужно, чтобы цвета изменялись по кругу от центра к краям.</li>
                            </ul>
                        </li>
                        <li>
                            <b>Точка градиента</b> - это определенная точка перехода от одного цвета к другому.
                            <ul>
                                <li>Чтобы добавить точку градиента, Используйте кнопку <div class = "icon icon-addgradientpoint"></div> <b>Добавить точку градиента</b> или ползунок. Вы можете добавить до 10 точек градиента. Каждая следующая добавленная точка градиента никоим образом не повлияет на внешний вид текущей градиентной заливки. Чтобы удалить определенную точку градиента, используйте кнопку  <div class = "icon icon-removegradientpoint"></div> <b>Удалить точку градиента</b>.</li>
                                <li>Чтобы изменить положение точки градиента, используйте ползунок или укажите <b>Положение</b> в процентах для точного местоположения.</li>
                                <li>Чтобы применить цвет к точке градиента, щелкните точку на панели ползунка, а затем нажмите <b>Цвет</b>, чтобы выбрать нужный цвет.</li>
                            </ul>
                        </li>
                    </ul>
                    <p class="note"><b>Примечание</b>: при выборе одной из этих двух опций можно также задать уровень <b>Непрозрачности</b>, перетаскивая ползунок или вручную вводя значение в процентах. Значение, заданное по умолчанию, составляет <b>100%</b>. Оно соответствует полной непрозрачности. Значение <b>0%</b> соответствует полной прозрачности.</p>
                </li>
                <li><b>Без заливки</b> - выберите эту опцию, если Вы вообще не хотите использовать заливку.</li>
            </ul>
            <p>Настройте толщину, цвет и тип <b>Контура</b> шрифта.</p>
            <ul>
                <li>Для изменения <b>толщины</b> контура выберите из выпадающего списка <b>Толщина</b> одну из доступных опций. Доступны следующие опции: 0.5 пт, 1 пт, 1.5 пт, 2.25 пт, 3 пт, 4.5 пт, 6 пт. Или выберите опцию <b>Без линии</b>, если вы вообще не хотите использовать контур.</li>
                <li>Для изменения <b>цвета</b> контура щелкните по цветному прямоугольнику и <a href="../UsageInstructions/ChangeColorScheme.htm" onclick="onhyperlinkclick(this)">выберите нужный цвет</a>.</li>
                <li>Для изменения <b>типа</b> контура выберите нужную опцию из соответствующего выпадающего списка (по умолчанию применяется сплошная линия, ее можно изменить на одну из доступных пунктирных линий).</li>
            </ul>
            <p>Примените текстовый эффект, выбрав нужный тип трансформации текста из галереи <b>Трансформация</b>. Можно скорректировать степень искривления текста, перетаскивая розовый маркер в форме ромба.</p>
            <p><img alt="Трансформация объекта Text Art" src="../images/textart_transformation.png" /></p>
		</div>
	</body>
</html>