
<!DOCTYPE html>
<html>
<head>
    <title>Вставка нумерации строк</title>
    <meta charset="utf-8" />
    <meta name="description" content="Вставить нумерацию строк в Редакторе документов" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Вставка нумерации строк</h1>
        <p>Редактор документов может автоматически подсчитывать строки в документе. Эта функция может быть полезна, когда вам нужно найти определенную строку документа, например, в юридическом соглашении или скрипте кода. Используйте функцию <span class = "icon icon-linenumbers_icon"></span> <b>Нумерация строк</b>, чтобы пронумеровать строки в документе. Обратите внимание, что последовательность нумерации строк не применяется к тексту в таких объектах, как таблицы, текстовые поля, диаграммы, верхние / нижние колонтитулы и т.д. Эти объекты обрабатываются как одна строка.</p>
        <h2>Применение нумерации строк</h2>
        <ol>
            <li>Перейдите на вкладку <b>Макет</b> верхней панели инструментов и щелкните значок <div class = "icon icon-linenumbers_icon"></div> <b>Нумерация строк</b>.</li>
            <li>
                В открывшемся выпадающем меню выберите необходимые параметры для быстрой настройки:
                <ul>
                    <li><b>Непрерывная</b> - каждой строке документа будет присвоен порядковый номер.</li>
                    <li><b>На каждой странице</b> - последовательность нумерации строк будет перезапущена на каждой странице документа.</li>
                    <li><b>В каждом разделе</b> - последовательность нумерации строк будет перезапущена в каждом разделе документа. Чтобы узнать больше о разрывах раздела, пожалуйста, обратитесь к <a href="../UsageInstructions/SectionBreaks.htm" onclick="onhyperlinkclick(this)">этому руководству</a>.</li>
                    <li><b>Отключить для текущего абзаца</b> - текущий абзац будет пропущен в последовательности нумерации строк. Чтобы исключить несколько абзацев из последовательности, выберите их левой кнопкой мыши перед применением этого параметра.</li>
                </ul>
            </li>
            <li>
                При необходимости укажите дополнительные параметры. Для этого щелкните на <b>Варианты нумерации строк</b> в раскрывающемся меню <b>Нумерация строк</b>. Установите флажок <b>Добавить нумерацию строк</b>, чтобы применить нумерацию строк к документу и получить доступ к расширенным параметрам параметра:
                <p><img alt="Нумерация строк окно" src="../images/linenumbers_window.png" /></p>
                <ul>
                    <li><b>Начать с</b> устанавливает начальное числовое значение последовательности нумерации строк. По умолчанию для параметра установлено значение <b>1</b>.</li>
                    <li><b>От текста</b> указывает расстояние между номерами строк и текстом. Введите необходимое значение в см. По умолчанию для параметра установлено значение <b>Авто</b>.</li>
                    <li><b>Шаг</b> задает порядковые номера, которые отображаются, если не подсчитываются по 1, то есть числа считаются в группе по 2, 3, 4 и т. д. Введите необходимое числовое значение. По умолчанию для параметра установлено значение <b>1</b>.</li>
                    <li><b>На каждой странице</b> - последовательность нумерации строк будет перезапущена на каждой странице документа.</li>
                    <li><b>В каждом разделе</b> - последовательность нумерации строк будет перезапущена в каждом разделе документа.</li>
                    <li><b>Непрерывная</b> - каждой строке документа будет присвоен порядковый номер.</li>
                    <li>Параметр <b>Применить изменения к</b> определяет часть документа, к которой вы хотите применить порядковые номера. Выберите одну из доступных предустановок: <b>К текущему разделу</b>, чтобы применить нумерацию строк к выбранному разделу документа; <b>До конца документа</b>, чтобы применить нумерацию строк к тексту, следующему за текущей позицией курсора; <b>Ко всему документу</b>, чтобы применить нумерацию строк ко всему документу. По умолчанию для параметра установлено значение <b>Ко всему документу</b>.</li>
                    <li>Нажмите <b>OK</b>, чтобы применить изменения.</li>
                </ul>
            </li>
        </ol>
        <h2>Удаление нумерации строк</h2>
        <p>Чтобы удалить последовательность нумерации строк,</p>
        <ol>
            <li>Перейдите на вкладку <b>Макет</b> верхней панели инструментов и щелкните значок <div class = "icon icon-linenumbers_icon"></div><b>Нумерация строк</b>,</li>
            <li>В открывшемся выпадающем меню выберите пункт <b>Нет</b> или выберите в меню пункт <b>Варианты нумерации строк</b> и в открывшемся окне <b>Нумерация строк</b> уберите галочку с поля <b>Добавить нумерацию строк</b>.</li>
        </ol>
    </div>
</body>
</html>