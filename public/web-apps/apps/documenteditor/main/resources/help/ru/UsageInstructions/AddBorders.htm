<!DOCTYPE html>
<html>
	<head>
		<title>Добавление границ</title>
		<meta charset="utf-8" />
		<meta name="description" content="Добавляйте в документ границы, выбирая их стиль" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Добавление границ</h1>
			<p>Для добавления границ к абзацу, странице или всему документу:</p>
			<ol>
				<li>установите курсор в пределах нужного абзаца или выделите мышью несколько абзацев или весь текст документа, нажав сочетание клавиш <b>Ctrl+A</b>,</li>
				<li>щелкните правой кнопкой мыши и выберите из меню пункт <b>Дополнительные параметры абзаца</b> или используйте ссылку <b>Дополнительные параметры</b> на правой боковой панели,</li>
				<li>в открывшемся окне <b>Абзац - дополнительные параметры</b> переключитесь на вкладку <b>Границы и заливка</b>,</li>
				<li>задайте нужное значение для <b>Ширины границ</b> и выберите <b>Цвет границ</b>,</li>
				<li>щелкайте по имеющейся схеме или используйте кнопки, чтобы выбрать границы и применить к ним выбранный стиль,</li>
				<li>нажмите кнопку <b>OK</b>.</li>
			</ol>
			<p><img alt="Дополнительные параметры абзаца - Границы и заливка" src="../images/paradvsettings_borders.png" /></p>
			<p>После добавления границ вы также сможете задать <b>внутренние поля</b>, то есть расстояния между текстом абзаца, который находится внутри границ, и этими границами <b>справа</b>, <b>слева</b>, <b>сверху</b> и <b>снизу</b>.</p>
			<p>Чтобы задать нужные значения, переключитесь на вкладку <b>Внутренние поля</b> в окне <b>Абзац - дополнительные параметры</b>:</p>
			<p><img alt="Дополнительные параметры абзаца - Внутренние поля" src="../images/paradvsettings_margins.png" /></p>
		</div>
	</body>
</html>