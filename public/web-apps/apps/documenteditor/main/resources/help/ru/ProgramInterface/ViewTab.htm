<!DOCTYPE html>
<html>
	<head>
        <title>Вкладка Вид</title>
		<meta charset="utf-8" />
        <meta name="description" content="Знакомство с пользовательским интерфейсом редактора документов - Вкладка Вид" />
        <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Вкладка Вид</h1>
            <p>
                Вкладка <b>Вид</b> <a href="https://www.onlyoffice.com/ru/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Редактора документов</b></a> позволяет управлять тем, как выглядит ваш документ во время работы над ним.
            </p>
            <div class="onlineDocumentFeatures">
                <p>Окно онлайн-редактора документов:</p>
                <p><img alt="Вкладка Вид" src="../images/interface/viewtab.png" /></p>
            </div>
            <div class="desktopDocumentFeatures">
                <p>Окно десктопного редактора документов:</p>
                <p><img alt="Вкладка Вид" src="../images/interface/desktop_viewtab.png" /></p>
            </div>
            <p>На этой вкладке доступны следующие параметры просмотра:</p>
            <ul>
                <li><a href="../UsageInstructions/CreateTableOfContents.htm" onclick="onhyperlinkclick(this)"><b>Навигация</b></a> - позволяет отображать заголовки в документе и перемещаться по ним,</li>
                <li><b>Масштаб</b> - позволяет увеличивать и уменьшать масштаб документа,</li>
                <li><b>По размеру страницы</b> - позволяет изменить размер страницы, чтобы на экране отображалась вся страница,</li>
                <li><b>По ширине</b> - позволяет изменить размер страницы, чтобы она соответствовала ширине экрана,</li>
                <li><b>Тема интерфейса</b> - позволяет изменить тему интерфейса на <b>Системную</b>, <b>Светлую</b>, <b>Классическую светлую</b>, <b>Темную</b> или <b>Контрастную темную</b>,</li>
                <li>Параметр <b>Темный документ</b> становится активным, когда включена Темная или Контрастная темная тема. Нажмите на нее, чтобы сделать рабочую область темной.</li>
            </ul>
            <p>Следующие параметры позволяют настроить отображение или скрытие элементов. Чтобы сделать их видимыми, отметьте галочкой следующие элементы:</p>
            <ul>
                <li><b>Всегда показывать панель инструментов</b> - чтобы верхняя панель инструментов всегда отображалась,</li>
                <li><b>Строка состояния</b> - чтобы строка состояния всегда отображалась,</li>
                <li><b>Левая панель</b> - чтобы левая панель отображалась,</li>
                <li><b>Правая панель</b> - чтобы правая панель отображалась,</li>
                <li><b>Линейки</b> - всегда отображать линейки.</li>
            </ul>
        </div>
	</body>
</html>