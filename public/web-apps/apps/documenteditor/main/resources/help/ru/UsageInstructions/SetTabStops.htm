<!DOCTYPE html>
<html>
	<head>
		<title>Установка позиций табуляции</title>
		<meta charset="utf-8" />
		<meta name="description" content="Установите позиции табуляции" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Установка позиций табуляции</h1>
			<p>В онлайн-редакторе документов можно изменить позиции табуляции, то есть те позиции, куда переходит курсор при нажатии клавиши <b>Tab</b> на клавиатуре.</p>
			<p>Для установки позиций табуляции можно использовать горизонтальную линейку:</p>
			<ol>
			    <li>Выберите нужный тип позиции табуляции, нажав на кнопку <div class = "icon icon-tabstopleft"></div> в левом верхнем углу рабочей области. Доступны следующие три типа табуляции:
			        <ul>
			            <li><b>По левому краю</b> <div class = "icon icon-tabstopleft"></div> - выравнивает текст по левому краю относительно позиции табуляции; при наборе текст движется вправо от позиции табуляции. Такая позиция табуляции будет обозначена на горизонтальной линейке маркером <div class = "icon icon-tabstopleft_marker"></div>.</li>
			            <li><b>По центру</b> <div class = "icon icon-tabstopcenter"></div> - центрирует текст относительно позиции табуляции. Такая позиция табуляции будет обозначена на горизонтальной линейке маркером <div class = "icon icon-tabstopcenter_marker"></div>.</li>
			            <li><b>По правому краю</b> <div class = "icon icon-tabstopright"></div> - выравнивает текст по правому краю относительно позиции табуляции; при наборе текст движется влево от позиции табуляции. Такая позиция табуляции будет обозначена на горизонтальной линейке маркером <div class = "icon icon-tabstopright_marker"></div>.</li>
			        </ul>
			    </li>
			    <li>Щелкните по нижнему краю линейки в том месте, где требуется установить позицию табуляции. Для изменения местоположения позиции табуляции перетащите ее по линейке. Для удаления добавленной позиции табуляции перетащите ее за пределы линейки. 
			    <p><div class = "big big-tabstops_ruler"></div></p>
			    </li>			
			</ol>
			<hr />
			<p>Для настройки позиций табуляции можно также использовать окно свойств абзаца. Щелкните правой кнопкой мыши, выберите в контекстном меню пункт <b>Дополнительные параметры абзаца</b> или используйте ссылку <b>Дополнительные параметры</b> на правой боковой панели. В открывшемся окне <b>Абзац - дополнительные параметры</b> переключитесь на вкладку <b>Табуляция</b>.</p>
			<img alt="Свойства абзаца - вкладка Табуляция" src="../images/paradvsettings_tab.png" />
			<p>Можно задать следующие параметры:</p>
			<ul>
			    <li>Позиция табуляции <b>По умолчанию</b> имеет значение 1.25 см. Это значение можно уменьшить или увеличить, используя кнопки со стрелками или введя в поле нужное значение.</li>
                <li><b>Позиция</b> - используется, чтобы задать пользовательские позиции табуляции. Введите в этом поле нужное значение, настройте его более точно, используя кнопки со стрелками, и нажмите на кнопку <b>Задать</b>. Пользовательская позиция табуляции будет добавлена в список в расположенном ниже поле. Если ранее Вы добавили какие-то позиции табуляции при помощи линейки, все эти позиции тоже будут отображены в списке.</li>
                <li><b>Выравнивание</b> - используется, чтобы задать нужный тип выравнивания для каждой из позиций табуляции в расположенном выше списке. Выделите нужную позицию табуляции в списке, выберите опцию <b>По левому краю</b>, <b>По центру</b> или <b>По правому краю</b> из выпадающего списка и нажмите на кнопку <b>Задать</b>.</li>
                <li>
                    <b>Заполнитель</b> - позволяет выбрать символ, который используется для создания заполнителя для каждой из позиций табуляции. Заполнитель - это строка символов (точек или дефисов), заполняющая пространство между позициями табуляции. Выделите нужную позицию табуляции в списке, выберите тип заполнителя из выпадающего списка и нажмите на кнопку <b>Задать</b>.
                    <p>Для удаления позиций табуляции из списка выделите позицию табуляции и нажмите кнопку <b>Удалить</b> или <b>Удалить все</b>.</p>
                </li>
</ul>			
		</div>
	</body>
</html>