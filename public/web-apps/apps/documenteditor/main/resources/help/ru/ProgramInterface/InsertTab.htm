<!DOCTYPE html>
<html>
	<head>
		<title>Вкладка Вставка</title>
		<meta charset="utf-8" />
        <meta name="description" content="Узнайте, что такое вкладка Вставка, как она позволяет добавлять в документы Word элементы форматирования страницы, а также визуальные объекты и комментарии." />
        <meta name="keywords" content="вкладка вставка">
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Вкладка Вставка</h1>
            <h2>Что такое вкладка Вставка?</h2>
            <p>Вкладка <b>Вставка</b> <a href="https://www.onlyoffice.com/ru/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Редактора документов</b></a> позволяет добавлять элементы форматирования страницы, а также визуальные объекты и комментарии.</p>
            <div class="onlineDocumentFeatures">
                <p>Окно онлайн-редактора документов:</p>
                <p><img alt="Вкладка Вставка" src="../images/interface/inserttab.png" /></p>
            </div>
            <div class="desktopDocumentFeatures">
                <p>Окно десктопного редактора документов:</p>
                <p><img alt="Вкладка Вставка" src="../images/interface/desktop_inserttab.png" /></p>
            </div>
            <h2>Назначение вкладки Вставка</h2>
            <p>С помощью этой вкладки вы можете выполнить следующие действия:</p>
            <ul>
                <li>вставлять <a href="../UsageInstructions/PageBreaks.htm" onclick="onhyperlinkclick(this)">пустую страницу</a>,</li>
                <li>вставлять <a href="../UsageInstructions/PageBreaks.htm" onclick="onhyperlinkclick(this)">разрывы страниц</a>, <a href="../UsageInstructions/SectionBreaks.htm" onclick="onhyperlinkclick(this)">разрывы разделов</a> и <a href="../UsageInstructions/SetPageParameters.htm#columns" onclick="onhyperlinkclick(this)">разрывы колонок</a>,</li>                
                <li>вставлять <a href="../UsageInstructions/InsertTables.htm" onclick="onhyperlinkclick(this)">таблицы</a>, <a href="../UsageInstructions/InsertImages.htm" onclick="onhyperlinkclick(this)">изображения</a>, <a href="../UsageInstructions/InsertCharts.htm" onclick="onhyperlinkclick(this)">диаграммы</a>, <a href="../UsageInstructions/InsertAutoshapes.htm" onclick="onhyperlinkclick(this)">фигуры</a>,</li>
                <li>вставлять <a href="../UsageInstructions/AddHyperlinks.htm" onclick="onhyperlinkclick(this)">гиперссылки</a>, <a href="../HelpfulHints/CollaborativeEditing.htm#comments" onclick="onhyperlinkclick(this)">комментарии</a>,</li>
                <li>вставлять <a href="../UsageInstructions/InsertHeadersFooters.htm" onclick="onhyperlinkclick(this)">колонтитулы</a> и <a href="../UsageInstructions/InsertPageNumbers.htm" onclick="onhyperlinkclick(this)">номера страниц</a>, <a href="../UsageInstructions/InsertDateTime.htm" onclick="onhyperlinkclick(this)">дату и время</a>,</li>
                <li>вставлять <a href="../UsageInstructions/InsertTextObjects.htm" onclick="onhyperlinkclick(this)">текстовые поля и объекты Text Art</a>, <a href="../UsageInstructions/InsertEquation.htm" onclick="onhyperlinkclick(this)">уравнения</a>, <a href="../UsageInstructions/InsertSymbols.htm" onclick="onhyperlinkclick(this)">символы</a>, <a href="../UsageInstructions/InsertDropCap.htm" onclick="onhyperlinkclick(this)">буквицы</a>, <a href="../UsageInstructions/InsertContentControls.htm" onclick="onhyperlinkclick(this)">элементы управления содержимым</a>,</li>
                <li>вставлять <a href="../UsageInstructions/InsertSmartArt.htm" onclick="onhyperlinkclick(this)">объекты SmartArt</a>.</li>
            </ul>
		</div>
	</body>
</html>