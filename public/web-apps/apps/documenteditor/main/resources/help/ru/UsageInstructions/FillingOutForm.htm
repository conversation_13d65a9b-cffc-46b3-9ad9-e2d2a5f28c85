<!DOCTYPE html>
<html>
<head>
    <title>Заполнение формы</title>
    <meta charset="utf-8" />
    <meta name="description" content="Заполняйте шаблон формы" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Заполнение формы</h1>
        <p>Заполняемая форма представляет собой файл OFORM. OFORM - это формат для заполнения форм-шаблонов, их скачивания или печати формы после ее заполнения.</p> 
            <h2>Как заполнить форму:</h2>
                <ol>
                    <li>
                        Откройте файл OFORM.
                        <p><img alt="oform" src="../images/oform.png" /><p>
                    </li>
                    <li>Заполните все необходимые поля. Границы обязательных полей окрашены в красный. Используйте <div class="icon icon-arrows_formfilling"></div> или <div class="icon icon-next_formfilling"></div> <b>Следующее поле</b> на верхней панели инструментов для навигации между полями или щелкните поле, которое вы хотите заполнить.
<li>Используйте кнопку <b>Очистить все поля</b> <img alt="Очистить все заполнение формы" src="../images/clearall_formfilling.png" />, чтобы очистить все поля.</li>
                    <li>Заполнив все поля, нажмите кнопку <b>Сохранить как PDF</b>, чтобы сохранить форму на свой компьютер в виде файла PDF.</li> 
                    <li>
                        Нажмите кнопку <div class = "icon icon-morebutton"></div> в правом верхнем углу панели инструментов, чтобы открыть дополнительные параметры. Вы можете <b>Распечатать</b>, <b>Скачать как docx</b> или <b>Скачать как pdf</b>.
                        <p><img alt="Дополнительные параметры OFORM" src="../images/more_oform.png" /></p>
                        <p>Вы также можете изменить <b>Тему интерфейса</b> формы, выбрав один из доступных вариантов: <b>Системная</b>, <b>Светлая</b>, <b>Классическая светлая</b>, <b>Темная</b>, <b>Контрастная темная</b>. При выборе <b>Темной</b> или <b>Контрастной темной</b> темы интерфейса становится доступным <b>Темный режим</b>.</p>
                        <p><img alt="Темный режим Формы" src="../images/darkmode_oform.png" /></p>
                        <p><b>Масштаб</b> позволяет масштабировать и изменять размер страницы с помощью параметров <b>По размеру страницы</b>, <b>По ширине</b> и <b>Увеличить</b> или <b>Уменьшить</b>:</p>
                        <p><img alt="Масштаб" src="../images/pagescaling.png" /></p>
                        <ul>
                            <li><b>По размеру страницы</b> позволяет изменить размер страницы, чтобы на экране отображалась вся страница.</li>
                            <li><b>По ширине</b> позволяет изменить размер страницы, чтобы она соответствовала ширине экрана.</li>
                            <li><b>Масштаб</b> позволяет увеличивать и уменьшать масштаб страницы.</li>
                        </ul>
                        <p>Когда вам нужно просмотреть папку, в которой хранится форма, нажмите <b>Открыть расположение файла</b>.</p>
                    </li> 
                 </ol>
     </div>
</body>
</html>