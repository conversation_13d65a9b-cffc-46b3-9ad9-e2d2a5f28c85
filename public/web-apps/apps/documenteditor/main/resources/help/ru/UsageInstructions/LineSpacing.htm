<!DOCTYPE html>
<html>
	<head>
		<title>Настройка междустрочного интервала в абзацах</title>
		<meta charset="utf-8" />
		<meta name="description" content="Задайте междустрочный интервал в абзацах своего документа" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Настройка междустрочного интервала в абзацах</h1>
			<p>В редакторе документов можно задать высоту строки для строк текста в абзаце, а также поля между текущим и предыдущим или последующим абзацем.</p>
			<p>Для этого:</p>
			<ol>
				<li>установите курсор в пределах нужного абзаца или выделите мышью несколько абзацев или весь текст документа, нажав сочетание клавиш <b>Ctrl+A</b>,</li>
				<li>используйте соответствующие поля на правой боковой панели для получения нужного результата:
					<ul>
						<li><b>Междустрочный интервал</b> - задайте высоту строки для строк текста в абзаце. Можно выбрать одну из трех опций: <b>минимум</b> (устанавливает минимальный междустрочный интервал, который требуется, чтобы соответствовать самому крупному шрифту или графическому элементу на строке), <b>множитель</b> (устанавливает междустрочный интервал, который может быть выражен в числах больше 1), <b>точно</b> (устанавливает фиксированный междустрочный интервал). Необходимое значение можно указать в поле справа.</li>
						<li><b>Интервал между абзацами</b> - задайте величину свободного пространства между абзацами.
                            <ul>
                                <li><b>Перед</b> - задайте величину свободного пространства перед абзацем.</li>
                                <li><b>После</b> - задайте величину свободного пространства после абзаца.</li>
                                <li>
                                    <b>Не добавлять интервал между абзацами одного стиля</b> - установите этот флажок, если свободное пространство между абзацами одного стиля не требуется.
                                    <p><img alt="Правая боковая панель - Настройки абзаца" src="../images/right_paragraph.png" /></p>
                                </li>
                            </ul>
                        </li>                        
					</ul>
				</li>
			</ol>
            <p>Эти параметры также можно найти в окне <b>Абзац - Дополнительные параметры</b>. Чтобы открыть окно <b>Абзац - Дополнительные параметры</b>, щелкните по тексту правой кнопкой мыши и выберите в контекстном меню пункт <b>Дополнительные параметры абзаца</b> или используйте опцию <b>Дополнительные параметры</b> на правой боковой панели. Затем переключитесь на вкладку <b>Отступы и интервалы</b> и перейдите в раздел <b>Интервал между абзацами</b>.</p>
            <p><img alt="Дополнительные параметры абзаца - Отступы и интервалы" src="../images/paradvsettings_indents.png" /></p>
			<p>Чтобы быстро изменить междустрочный интервал в текущем абзаце, можно также использовать значок <b>Междустрочный интервал в абзацах</b> <span class = "icon icon-linespacing"></span> на вкладке <b>Главная</b> верхней панели инструментов, выбрав нужное значение из списка: 1.0, 1.15, 1.5, 2.0, 2.5, или 3.0 строки.</p>
		</div>
	</body>
</html>