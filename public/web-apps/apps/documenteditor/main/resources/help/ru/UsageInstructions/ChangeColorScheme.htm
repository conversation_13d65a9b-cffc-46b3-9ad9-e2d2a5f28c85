<!DOCTYPE html>
<html>
	<head>
		<title>Выбор цветовой схемы</title>
		<meta charset="utf-8" />
		<meta name="description" content="Узнайте, как изменить цветовую схему документа" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Выбор цветовой схемы</h1>
			<p>Цветовые схемы применяются ко всему документу. Они используются для быстрого изменения вида документа, поскольку они определяют палитру 
      <b>Цвета темы</b> для элементов документа (<a href="../UsageInstructions/FontTypeSizeColor.htm" onclick="onhyperlinkclick(this)">шрифт</a>, <a href="../UsageInstructions/BackgroundColor.htm" onclick="onhyperlinkclick(this)">фон</a>,
      <a href="../UsageInstructions/InsertTables.htm" onclick="onhyperlinkclick(this)">таблицы</a>, <a href="../UsageInstructions/InsertAutoshapes.htm" onclick="onhyperlinkclick(this)">автофигуры</a>,
      <a href="../UsageInstructions/InsertCharts.htm" onclick="onhyperlinkclick(this)">диаграммы</a>). Если вы применили какие-то <b>Цвета темы</b> к элементам документа, а потом
      выбрали другую <b>Цветовую схему</b>, то цвета, примененные в вашем документе, соответствующим образом изменятся.
      </p>
      <p>
        Для изменения цветовой схемы щелкните по направленной вниз стрелке рядом со значком <b>Изменить цветовую схему</b> <span class = "icon icon-changecolorscheme"></span> на вкладке <b>Главная</b> верхней панели инструментов
        и выберите нужную цветовую схему из имеющихся вариантов: <b>Стандартная</b>, <b>Оттенки серого</b>, 
        <b>Апекс</b>, <b>Аспект</b>, <b>Официальная</b>, <b>Открытая</b>, <b>Справедливость</b>, <b>Поток</b>, <b>Литейная</b>, <b>Обычная</b>, 
        <b>Метро</b>, <b>Модульная</b>, <b>Изящная</b>, <b>Эркер</b>, <b>Начальная</b>, <b>Бумажная</b>, <b>Солнцестояние</b>, <b>Техническая</b>, <b>Трек</b>, <b>Городская</b>, <b>Яркая</b>. Выбранная цветовая схема будет выделена в списке.
      </p>
      <p>
        <img alt="Цветовые схемы" src="../images/colorscheme.png" />
      </p>
      <p>
        Выбрав предпочтительную цветовую схему, вы сможете выбрать цвета в окне цветовых палитр, соответствующем тому элементу документа, к которому вы хотите 
        применить тот или иной цвет. Для большинства элементов документа к окну цветовых палитр можно получить доступ, нажав на цветной прямоугольник на правой боковой панели, когда нужный элемент выделен. Для шрифта это окно можно открыть с помощью направленной вниз стрелки рядом со значком <b>Цвет шрифта</b> <span class = "icon icon-fontcolor"></span> на вкладке <b>Главная</b> верхней панели инструментов. Предусмотрены следующие палитры:</p>
      <p>
        <p>
          <img alt="Палитра" src="../images/palette.png" />
        </p>
        <ul>
          <li>
            <b>Цвета темы</b> - цвета, соответствующие выбранной цветовой схеме документа.
          </li>
          <li>
            <b>Стандартные цвета</b> - набор стандартных цветов. Выбранная цветовая схема на них не влияет.
          </li>
          <li>
            <b>Пользовательский цвет</b> - щелкните по этой надписи, если на доступных палитрах нет нужного цвета. Выберите нужный цветовой диапазон, перемещая вертикальный ползунок цвета, и определите конкретный цвет, перетаскивая инструмент для выбора цвета внутри большого квадратного цветового поля. Как только Вы выберете какой-то цвет, в полях справа отобразятся соответствующие цветовые значения RGB и sRGB. Также можно задать цвет на базе цветовой модели RGB, введя нужные числовые значения в полях <b>R</b>, <b>G</b>, <b>B</b> (красный, зеленый, синий), или указать шестнадцатеричный код sRGB в поле, отмеченном знаком <b>#</b>. Выбранный цвет появится в окне предварительного просмотра <b>Новый</b>. Если к объекту был ранее применен какой-то пользовательский цвет, этот цвет отображается в окне <b>Текущий</b>, так что вы можете сравнить исходный и измененный цвета. Когда цвет будет задан, нажмите на кнопку <b>Добавить</b>:
            <p>
              <img alt="Палитра - Пользовательский цвет" src="../../../../../../common/main/resources/help/ru/images/palette_custom.png" />
            </p>
            <p>
              Пользовательский цвет будет применен к выбранному элементу и добавлен в палитру <b>Пользовательский цвет</b>.
            </p>
        </li>
      </ul>
    </div>
</body>
</html>