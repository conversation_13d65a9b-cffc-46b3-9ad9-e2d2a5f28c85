<!DOCTYPE html>
<html>
	<head>
		<title>Вставка элементов управления содержимым</title>
		<meta charset="utf-8" />
        <meta name="description" content="Вставляйте элементы управления содержимым создать форму с полями ввода, которую могут заполнять другие пользователи, или защитить некоторые части документа от редактирования или удаления" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Вставка элементов управления содержимым</h1>
            <p><a href="https://www.onlyoffice.com/ru/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Редактор документов</b></a> ONLYOFFICE позволяет вставлять <b>классические</b> элементы управления содержимым, т.е. они <b>обратно совместимы</b> со сторонними текстовыми редакторами, такими как Microsoft Word.</p>
            <p>В настоящее время можно добавить следующие типы элементов управления содержимым: <em>Обычный текст</em>, <em>Форматированный текст</em>, <em>Рисунок</em>, <em>Поле со списком</em>, <em>Выпадающий список</em>, <em>Дата</em>, <em>Флажок</em>.</p>
            <ul>
                <li><em>Обычный текст</em> - это объект, содержащий текст, который можно форматировать. Элементы управления содержимым "Обычный текст" могут содержать не более одного абзаца.</li>
                <li><em>Форматированный текст</em> - это объект, содержащий текст, который можно форматировать. Элементы управления содержимым "Форматированный текст" могут содержать несколько абзацев, списки и объекты (изображения, фигуры, таблицы и так далее).</li>
                <li><em>Рисунок</em> - это объект, содержащий отдельное изображение.</li>
                <li><em>Поле со списком</em> - это объект, содержащий выпадающий список с рядом вариантов для выбора. Он позволяет выбрать из списка одно из предварительно заданных значений и при необходимости отредактировать выбранное значение.</li>
                <li><em>Выпадающий список</em> - это объект, содержащий выпадающий список с рядом вариантов для выбора. Он позволяет выбрать из списка одно из предварительно заданных значений. выбранное значение нельзя отредактировать.</li>
                <li><em>Дата</em> - это объект, содержащий календарь, из которого можно выбрать дату.</li>
                <li><em>Флажок</em> - это объект, позволяющий отобразить два состояния: флажок выбран и флажок снят.</li>
            </ul>
            <h3>Добавление элементов управления содержимым</h3>
            <h5>Создание нового элемента управления содержимым "Обычный текст"</h5>
            <ol>
                <li>установите курсор в строке текста там, где требуется добавить элемент управления,<br />или выделите фрагмент текста, который должен стать содержимым элемента управления.</li>
                <li>перейдите на вкладку <b>Вставка</b> верхней панели инструментов.</li>
                <li>нажмите на стрелку рядом со значком <div class = "icon icon-insertccicon"></div> <b>Элементы управления содержимым</b>.</li>
                <li>выберите в меню опцию <b>Обычный текст</b>.</li>
            </ol>
            <p>Элемент управления будет вставлен в позиции курсора в строке существующего текста. Замените стандартный текст в элементе управления ("Введите ваш текст") на свой собственный: выделите стандартный текст и введите новый текст или скопируйте откуда-нибудь фрагмент текста и вставьте его в элемент управления содержимым. Элементы управления содержимым "Обычный текст" не позволяют добавлять разрывы строки и не могут содержать другие объекты, такие как изображения, таблицы и так далее.</p>
            <p><img alt="Элемент управления содержимым Обычный текст" src="../images/addedcontentcontrol.png" /></p>
            <h5>Создание нового элемента управления содержимым "Форматированный текст"</h5>
            <ol>
                <li>установите курсор в конце абзаца, после которого требуется добавить элемент управления,<br />или выделите один или несколько существующих абзацев, которые должны стать содержимым элемента управления.</li>
                <li>перейдите на вкладку <b>Вставка</b> верхней панели инструментов.</li>
                <li>нажмите на стрелку рядом со значком <div class = "icon icon-insertccicon"></div> <b>Элементы управления содержимым</b>.</li>
                <li>выберите в меню опцию <b>Форматированный текст</b>.</li>
            </ol>
            <p>Элемент управления содержимым "Форматированный текст" будет вставлен в новом абзаце. Замените стандартный текст в элементе управления ("Введите ваш текст") на свой собственный: выделите стандартный текст и введите новый текст или скопируйте откуда-нибудь фрагмент текста и вставьте его в элемент управления содержимым. Элементы управления содержимым "Форматированный текст" позволяют добавлять разрывы строки, то есть могут содержать несколько абзацев, а также какие-либо объекты, такие как изображения, таблицы, другие элементы управления содержимым и так далее.</p>
            <p><img alt="Элемент управления содержимым Форматированный текст" src="../images/richtextcontentcontrol.png" /></p>
            <h5>Создание нового элемента управления содержимым "Рисунок"</h5>
            <ol>
                <li>установите курсор в строке текста там, где требуется добавить элемент управления.</li>
                <li>перейдите на вкладку <b>Вставка</b> верхней панели инструментов.</li>
                <li>нажмите на стрелку рядом со значком <div class = "icon icon-insertccicon"></div> <b>Элементы управления содержимым</b>.</li>
                <li>выберите в меню опцию <b>Изображение</b> - элемент управления будет вставлен в позиции курсора.</li>
                <li>нажмите на значок <div class = "icon icon-image_settings_icon"></div> в кнопке, расположенной над границей элемента управления, - откроется стандартное окно выбора файла. Выберите изображение, сохраненное на компьютере, и нажмите кнопку <b>Открыть</b>.</li>
            </ol>
            <p>Выбранное изображение будет отображено внутри элемента управления содержимым. Чтобы заменить изображение, нажмите на значок <span class = "icon icon-image_settings_icon"></span> в кнопке, расположенной над границей элемента управления и выберите другое изображение.</p>
            <p><span class = "big big-picturecontentcontrol"></span></p>
            <h5>Создание нового элемента управления содержимым "Поле со списком" или "Выпадающий список"</h5>
            <p>Элементы управления содержимым <em>Поле со списком</em> и <em>Выпадающий список</em> содержат выпадающий список с рядом вариантов для выбора. Их можно создавать почти одним и тем же образом. Основное различие между ними заключается в том, что выбранное значение в выпадающем списке нельзя отредактировать, тогда как выбранное значение в поле со списком можно заменить на ваше собственное.</p>
            <ol>
                <li>установите курсор в строке текста там, где требуется добавить элемент управления.</li>
                <li>перейдите на вкладку <b>Вставка</b> верхней панели инструментов.</li>
                <li>нажмите на стрелку рядом со значком <div class = "icon icon-insertccicon"></div> <b>Элементы управления содержимым</b>.</li>
                <li>выберите в меню опцию <b>Поле со списком</b> или <b>Выпадающий список</b> - элемент управления будет вставлен в позиции курсора.</li>
                <li>щелкните по добавленному элементу управления правой кнопкой мыши и выберите в контекстном меню пункт <b>Параметры элемента управления содержимым</b>.</li>
                <li>
                    в открывшемся окне <b>Параметры элемента управления содержимым</b> перейдите на вкладку <b>Поле со списком</b> или <b>Выпадающий список</b>, в зависимости от выбранного типа элемента управления содержимым.
                    <p><img alt="Окно настроек элемента управления Поле со списком" src="../images/comboboxsettings.png" /></p>
                </li>
                <li>
                    для добавления нового пункта списка нажмите кнопку <b>Добавить</b> и заполните доступные поля в открывшемся окне:
                    <p><img alt="Поле со списком - добавление значения" src="../images/comboboxaddvalue.png" /></p>
                    <ol>
                        <li>укажите нужный текст в поле <b>Отображаемое имя</b>, например, <em>Да</em>, <em>Нет</em>, <em>Другое</em>. Этот текст будет отображаться в элементе управления содержимым в документе.</li>
                        <li>по умолчанию текст в поле <b>Значение</b> соответствует введенному в поле <b>Отображаемое имя</b>. Если вы хотите отредактировать текст в поле <b>Значение</b>, обратите внимание на то, что веденное значение должно быть уникальным для каждого элемента. </li>
                        <li>нажмите кнопку <b>OK</b>.</li>
                    </ol>
                </li>
                <li>можно редактировать или удалять пункты списка, используя кнопки <b>Редактировать</b> или <b>Удалить</b> справа, или изменять порядок элементов с помощью кнопок <b>Вверх</b> и <b>Вниз</b>.</li>
                <li>когда все нужные варианты выбора будут заданы, нажмите кнопку <b>OK</b>, чтобы сохранить изменения и закрыть окно.</li>
            </ol>
            <p><img alt="Элемент управления содержимым Поле со списком" src="../images/comboboxcontentcontrol.png" /></p>
            <p>Вы можете нажать на кнопку со стрелкой справа, чтобы открыть список значений и выбрать нужное. Когда нужный элемент будет выбран из <b>Поля со списком</b>, можно отредактировать значение, заменив его на свое собственное полностью или частично. В <b>Выпадающем списке</b> нельзя отредактировать выбранное значение.</p>
            <p><img alt="Элемент управления содержимым Поле со списком" src="../images/comboboxcontentcontrol2.png" /></p>
            <h5>Создание нового элемента управления содержимым "Дата"</h5>
            <ol>
                <li>установите курсор в строке текста там, где требуется добавить элемент управления.</li>
                <li>перейдите на вкладку <b>Вставка</b> верхней панели инструментов.</li>
                <li>нажмите на стрелку рядом со значком <div class = "icon icon-insertccicon"></div> <b>Элементы управления содержимым</b>.</li>
                <li>выберите в меню опцию <b>Дата</b> - элемент управления будет вставлен в позиции курсора.</li>
                <li>щелкните по добавленному элементу управления правой кнопкой мыши и выберите в контекстном меню пункт <b>Параметры элемента управления содержимым</b>.</li>
                <li>
                    в открывшемся окне <b>Параметры элемента управления содержимым</b> перейдите на вкладку <b>Формат даты</b>.
                    <p><img alt="Окно настроек элемента управления Дата" src="../images/datesettings.png" /></p>
                </li>
                <li>выберите нужный <b>Язык</b> и нужный формат даты в списке <b>Отображать дату следующим образом</b>.</li>
                <li>нажмите кнопку <b>OK</b>, чтобы сохранить изменения и закрыть окно.</li>
            </ol>
            <p><span class = "big big-datecontentcontrol"></span></p>
            <p>Вы можете нажать на кнопку со стрелкой в правой части добавленного элемента управления содержимым <b>Дата</b>, чтобы открыть календарь и выбрать нужную дату.</p>
            <p><img alt="Элемент управления содержимым Дата" src="../images/datecontentcontrol2.png" /></p>
            <h5>Создание нового элемента управления содержимым "Флажок"</h5>
            <ol>
                <li>установите курсор в строке текста там, где требуется добавить элемент управления.</li>
                <li>перейдите на вкладку <b>Вставка</b> верхней панели инструментов.</li>
                <li>нажмите на стрелку рядом со значком <div class = "icon icon-insertccicon"></div> <b>Элементы управления содержимым</b>.</li>
                <li>выберите в меню опцию <b>Флажок</b> - элемент управления будет вставлен в позиции курсора.</li>
                <li>щелкните по добавленному элементу управления правой кнопкой мыши и выберите в контекстном меню пункт <b>Параметры элемента управления содержимым</b>.</li>
                <li>
                    в открывшемся окне <b>Параметры элемента управления содержимым</b> перейдите на вкладку <b>Флажок</b>.
                    <p><img alt="Окно настроек элемента управления Флажок" src="../images/checkboxsettings.png" /></p>
                </li>
                <li>нажмите на кнопку <b>Символ установленного флажка</b>, чтобы задать нужный символ для выбранного флажка, или <b>Символ снятого флажка</b>, чтобы выбрать, как должен выглядеть снятый флажок. Откроется окно <b>Символ</b>. Для получения дополнительной информации о работе с символами вы можете обратиться к <a href="../UsageInstructions/InsertSymbols.htm" onclick="onhyperlinkclick(this)">этой статье</a>.</li>
                <li>когда символы будут заданы, нажмите кнопку <b>OK</b>, чтобы сохранить изменения и закрыть окно.</li>
            </ol>
            <p>Добавленный флажок отображается в режиме снятого флажка.</p>
            <p><span class = "icon icon-checkboxcontentcontrol"></span></p>
            <p>Если щелкнуть по добавленному флажку, он будет отмечен символом, выбранным в списке <b>Символ установленного флажка</b>.</p>
            <p><span class = "icon icon-checkboxcontentcontrol2"></span></p>

            <p class="note"><b>Примечание</b>: Граница элемента управления содержимым видна только при выделении элемента управления. Границы не отображаются в печатной версии.</p>

            <h3>Перемещение элементов управления содержимым</h3>
            <p>Элементы управления можно <b>перемещать</b> на другое место в документе: нажмите на кнопку слева от границы элемента управления, чтобы выделить элемент управления, и перетащите его, не отпуская кнопку мыши, на другое место в тексте документа.</p>
            <p><img alt="Перемещение элементов управления" src="../images/movecontentcontrol.png" /></p>
            <p>Элементы управления содержимым можно также <b>копировать и вставлять</b>: выделите нужный элемент управления и используйте сочетания клавиш <b>Ctrl+C/Ctrl+V</b>.</p>
            
            <h3>Редактирование содержимого элементов управления "Обычный текст" и "Форматированный текст"</h3>
            <p>Текст внутри элемента управления содержимым "Обычный текст" и "Форматированный текст" можно отформатировать с помощью значков на верхней панели инструментов: вы можете изменить <a href="../UsageInstructions/FontTypeSizeColor.htm" onclick="onhyperlinkclick(this)">тип, размер, цвет шрифта</a>, применить <a href="../UsageInstructions/DecorationStyles.htm" onclick="onhyperlinkclick(this)">стили оформления</a> и <a href="../UsageInstructions/FormattingPresets.htm" onclick="onhyperlinkclick(this)">предустановленные стили форматирования</a>. Для изменения свойств текста можно также использовать окно <b>Абзац - Дополнительные параметры</b>, доступное из контекстного меню или с правой боковой панели. Текст в элементах управления "Форматированный текст" можно форматировать, как обычный текст документа, то есть вы можете задать <a href="../UsageInstructions/LineSpacing.htm" onclick="onhyperlinkclick(this)">междустрочный интервал</a>, изменить <a href="../UsageInstructions/ParagraphIndents.htm" onclick="onhyperlinkclick(this)">отступы абзаца</a>, настроить <a href="../UsageInstructions/SetTabStops.htm" onclick="onhyperlinkclick(this)">позиции табуляции</a>.</p>
            
            <h3>Изменение настроек элементов управления содержимым</h3>
            <p>Независимо от того, какой тип элемента управления содержимым выбран, вы можете изменить настройки элемента управления в разделах <b>Общие</b> и <b>Блокировка</b> окна <b>Параметры элемента управления содержимым</b>.</p>
            <p>Чтобы открыть настройки элемента управления содержимым, можно действовать следующим образом:</p>
            <ul>
                <li>Выделите нужный элемент управления содержимым, нажмите на стрелку рядом со значком <div class = "icon icon-insertccicon"></div> <b>Элементы управления содержимым</b> на верхней панели инструментов и выберите в меню опцию <b>Параметры элемента управления</b>.</li>
                <li>Щелкните правой кнопкой мыши по элементу управления содержимым и используйте команду контекстного меню <b>Параметры элемента управления содержимым</b>.</li>
            </ul>
            <p>Откроется новое окно. На вкладке <b>Общие</b> можно настроить следующие параметры:</p>
            <p><img alt="Окно настроек элемента управления содержимым - Общие" src="../images/ccsettingswindow.png" /></p>
            <ul>
                <li>Укажите <b>Заголовок</b>, <b>Заполнитель</b> или <b>Тег</b> элемента управления содержимым в соответствующих полях. Заголовок будет отображаться при выделении элемента управления в документе. Заполнитель -  это основной текст, отображаемый внутри элемента управления содержимым. Теги используются для идентификации элементов управления, чтобы можно было ссылаться на них в коде. </li>
                <li>Выберите, требуется ли отображать элемент управления <b>C ограничивающей рамкой</b> или <b>Без рамки</b>. В том случае, если вы выбрали вариант <b>C ограничивающей рамкой</b>, можно выбрать <b>Цвет</b> рамки в расположенном ниже поле. Нажмите кнопку <b>Применить ко всем</b>, чтобы применить указанные настройки из раздела <b>Вид</b> ко всем элементам управления в документе.</li>
            </ul>
            <p>На вкладке <b>Блокировка</b> можно защитить элемент управления содержимым от удаления или редактирования, используя следующие параметры:</p>
            <p><img alt="Окно настроек элемента управления содержимым - Блокировка" src="../images/ccsettingswindow2.png" /></p>
                <ul>
                    <li><b>Элемент управления содержимым нельзя удалить</b> - отметьте эту опцию, чтобы защитить элемент управления содержимым от удаления.</li>
                    <li><b>Содержимое нельзя редактировать</b> - отметьте эту опцию, чтобы защитить содержимое элемента управления от редактирования.</li>
                </ul>
            <p>Для определенных типов элементов управления содержимым также доступна третья вкладка, которая содержит настройки, специфичные только для выбранного типа элементов управления: <em>Поле со списком</em>, <em>Выпадающий список</em>, <em>Дата</em>, <em>Флажок</em>. Эти настройки описаны выше в разделах о добавлении соответствующих элементов управления содержимым.</p>
            <p>Нажмите кнопку <b>OK</b> в окне настроек, чтобы применить изменения.</p>
            <p>Также доступна возможность выделения элементов управления определенным цветом. Для того, чтобы выделить элементы цветом:</p>
            <ol>
                <li>Нажмите на кнопку слева от границы элемента управления, чтобы выделить элемент управления,</li>
                <li>Нажмите на стрелку рядом со значком <div class = "icon icon-insertccicon"></div> <b>Элементы управления содержимым</b> на верхней панели инструментов,</li>
                <li>Выберите в меню опцию <b>Параметры выделения</b>,</li>
                <li>Выберите нужный цвет на одной из доступных палитр: <b>Цвета темы</b>, <b>Стандартные цвета</b> или задайте <b>Пользовательский цвет</b>. Чтобы убрать ранее примененное выделение цветом, используйте опцию <b>Без выделения</b>.</li>
            </ol>
            <p>Выбранные параметры выделения будут применены ко всем элементам управления в документе. </p>
            
            <h3>Удаление элементов управления содержимым</h3>
            <p>Чтобы удалить элемент управления и оставить все его содержимое, щелкните по элементу управления содержимым, чтобы выделить его, затем действуйте одним из следующих способов:</p>
            <ul>
                <li>Нажмите на стрелку рядом со значком <div class = "icon icon-insertccicon"></div> <b>Элементы управления содержимым</b> на верхней панели инструментов и выберите в меню опцию <b>Удалить элемент управления содержимым</b>.</li>
                <li>Щелкните правой кнопкой мыши по элементу управления содержимым и используйте команду контекстного меню <b>Удалить элемент управления содержимым</b>.</li>
            </ul>
            <p>Чтобы удалить элемент управления и все его содержимое, выделите нужный элемент управления и нажмите клавишу <b>Delete</b> на клавиатуре.</p>
                        
		</div>
	</body>
</html>