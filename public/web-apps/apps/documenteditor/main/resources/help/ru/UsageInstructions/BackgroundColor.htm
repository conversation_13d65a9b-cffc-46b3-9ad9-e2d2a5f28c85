<!DOCTYPE html>
<html>
	<head>
		<title>Выбор цвета фона для абзаца</title>
		<meta charset="utf-8" />
		<meta name="description" content="Узнайте, как выбрать цвет фона для абзаца" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Выбор цвета фона для абзаца</h1>
			<p>Цвет фона применяется ко всему абзацу и полностью заполняет пространство абзаца от левого поля страницы до правого поля страницы.</p>
			<p>Чтобы применить цвет фона к определенному абзацу или изменить тот цвет, который выбран в данный момент:</p>
            <ol>
                <li>выберите цветовую схему документа из доступных, нажав на значок <b>Изменение цветовой схемы</b> <div class = "icon icon-changecolorscheme"></div> на вкладке <b>Главная</b> верхней панели инструментов</li>
                <li>установите курсор в пределах нужного абзаца или выделите мышью несколько абзацев или весь текст с помощью сочетания клавиш <b>Ctrl+A</b>,</li>
                <li>откройте окно цветовых палитр. Это можно сделать это одним из следующих способов:
                    <ul>
                        <li>нажмите на направленную вниз стрелку рядом со значком <div class = "icon icon-backgroundcolor"></div> на вкладке <b>Главная</b> верхней панели инструментов, или</li>
                        <li>нажмите на цветовое поле рядом с надписью <b>Цвет фона</b> на правой боковой панели инструментов, или</li>
                        <li>нажмите на ссылку "Дополнительные параметры" на правой боковой панели инструментов или воспользуйтесь опцией "Дополнительные параметры абзаца" в контекстном меню, а затем перейдите на вкладку "Границы и заливка" в окне "Абзац - дополнительные параметры" и нажмите на цветовое поле рядом с надписью <b>Цвет фона</b>.</li>
                    </ul>
                </li>
                <li>выберите любой цвет на доступных <a href="../UsageInstructions/ChangeColorScheme.htm" onclick="onhyperlinkclick(this)">палитрах</a> </li>
            </ol>
     <p>Выбрав нужный цвет с помощью значка <span class = "icon icon-backgroundcolor"></span>, вы сможете применить этот цвет к любому выделенному абзацу, просто щелкнув по значку <span class = "icon icon-backgroundcolor_selected"></span> (он показывает выбранный цвет).
     При этом не придется снова выбирать этот цвет на палитре. Если вы используете опцию <b>Цвет фона</b> на правой боковой панели или 
     в окне "Абзац - дополнительные параметры", имейте в виду, что выбранный цвет не запоминается. (Эти опции могут быть полезны, если вы хотите выбрать другой цвет фона для конкретного абзаца, 
     продолжая при этом использовать один и тот же основной цвет, выбранный с помощью значка <span class = "icon icon-backgroundcolor"></span>).
     </p>
            <hr />
      <p>Чтобы очистить цвет фона определенного абзаца:</p>
			<ol>
				<li>установите курсор в пределах нужного абзаца, или выделите мышью несколько абзацев, или весь текст с помощью сочетания клавиш <b>Ctrl+A</b>,</li>
				<li>откройте окно цветовых палитр, нажав на цветовое поле рядом с надписью <b>Цвет фона</b> на правой боковой панели,</li>
				<li>выберите значок <div class = "icon icon-nofill"></div>.</li>
			</ol>
		</div>
	</body>
</html>