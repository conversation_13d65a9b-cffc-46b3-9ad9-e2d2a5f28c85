<!DOCTYPE html>
<html>
	<head>
		<title>Вставка колонтитулов</title>
		<meta charset="utf-8" />
		<meta name="description" content="Вставьте в документ колонтитулы, добавьте особые колонтитулы на первую страницу или разные колонтитулы для четных и нечетных страниц" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Вставка колонтитулов</h1>
			<p>Чтобы добавить в документ верхний или нижний колонтитулы, удалить или изменить существующие,</p>
            <ol>
                <li>перейдите на вкладку <b>Вставка</b> верхней панели инструментов,</li>
                <li>нажмите значок <div class = "icon icon-headerfooter"></div> <b>Колонтитулы</b> на верхней панели инструментов,</li>
                <li>
                    выберите одну из следующих опций:
                    <ul>
                        <li><b>Изменить верхний колонтитул</b>, чтобы вставить или отредактировать текст верхнего колонтитула.</li>
                        <li><b>Изменить нижний колонтитул</b>, чтобы вставить или отредактировать текст нижнего колонтитула.</li>
                        <li><b>Удалить верхний колонтитул</b>, чтобы удалить верхний колонтитул.</li>
                        <li><b>Удалить нижний колонтитул</b>, чтобы удалить нижний колонтитул.</li>
                    </ul>
                </li>
                <li>измените текущие параметры колонтитулов на правой боковой панели:
                    <p><img alt="Правая боковая панель - Настройки колонтитулов" src="../images/right_headerfooter.png" /></p>
                    <ul>
                        <li>Установите <b>положение</b> текста относительно верхнего (для верхних колонтитулов) или нижнего (для нижних колонтитулов) края страницы.</li>
                        <li>Установите флажок <b>Особый для первой страницы</b>, если надо применить особый верхний или нижний колонтитул для самой первой страницы или вообще не добавлять на нее колонтитулы.</li>
                        <li>Опция <b>Разные для четных и нечетных</b> используется для вставки разных колонтитулов для четных и нечетных страниц.</li>
                        <li>Опция <b>Связать с предыдущим</b> доступна, если вы ранее добавили в документ <a href="../UsageInstructions/SectionBreaks.htm" onclick="onhyperlinkclick(this)">разделы</a>. В противном случае она будет неактивна.
                            Кроме того, эта опция недоступна для самого первого раздела (то есть когда выделен верхний или нижний колонтитул, относящийся к первому разделу).
                            По умолчанию эта опция включена и ко всем разделам применяются одни и те же верхние/нижние колонтитулы. Если выбрать область верхних или нижних колонтитулов,
                            она будет отмечена надписью <b>Как в предыдущем</b>. Для того чтобы использовать разные верхние/нижние колонтитулы в каждом разделе документа,
                            уберите галочку <b>Связать с предыдущим</b>, и надпись <b>Как в предыдущем</b> больше отображаться не будет.</li>
                    </ul>
                    <p><img alt="Надпись Как в предыдущем" src="../images/sameasprevious_label.png" /></p>
                </li>
            </ol>          
          <p>Чтобы ввести текст или отредактировать уже введенный текст и настроить параметры верхнего или нижнего колонтитулов, можно также дважды щелкнуть в верхней или нижней части страницы или щелкнуть там правой кнопкой мыши и выбрать единственную опцию меню - <b>Изменить верхний колонтитул</b> или <b>Изменить нижний колонтитул</b>.</p>
			<p>Чтобы переключиться на текст документа, дважды щелкните по рабочей области. Текст, используемый в колонтитулах будет отображаться серым цветом.</p>
			<p class="note"><b>Примечание</b>: пожалуйста, обратитесь к разделу <a href="InsertPageNumbers.htm" onclick="onhyperlinkclick(this)">Вставка номеров страниц</a>, чтобы узнать, как добавить в документ номера страниц.</p>
		</div>
	</body>
</html>
