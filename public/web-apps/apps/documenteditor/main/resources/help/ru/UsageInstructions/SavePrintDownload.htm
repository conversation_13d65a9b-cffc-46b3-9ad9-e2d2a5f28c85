<!DOCTYPE html>
<html>
	<head>
		<title>Сохранение, скачивание, печать документа</title>
		<meta charset="utf-8" />
		<meta name="description" content="Сохраните, распечатайте и скачайте документ в разных форматах" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Сохранение, <span class="onlineDocumentFeatures"> скачивание,</span> печать документа</h1>
            <h3>Сохранение</h3>
            <p class="onlineDocumentFeatures">По умолчанию онлайн-редактор документов автоматически сохраняет файл каждые 2 секунды, когда вы работаете над ним, чтобы не допустить потери данных в случае непредвиденного закрытия программы. Если вы совместно редактируете файл в <b>Быстром</b> режиме, таймер запрашивает наличие изменений 25 раз в секунду и сохраняет их, если они были внесены. При совместном редактировании файла в <b>Строгом</b> режиме изменения автоматически сохраняются каждые 10 минут. При необходимости можно легко выбрать предпочтительный режим совместного редактирования или отключить функцию автоматического сохранения на странице <a href="../HelpfulHints/AdvancedSettings.htm" onclick="onhyperlinkclick(this)">Дополнительные параметры</a>.</p>
            <p>Чтобы сохранить текущий документ вручную в текущем формате и местоположении,</p>
            <ul>
                <li>нажмите значок <b>Сохранить</b> <div class = "icon icon-save"></div> в левой части шапки редактора, или</li>
                <li>используйте сочетание клавиш <b>Ctrl+S</b>, или</li>
                <li>нажмите на вкладку <b>Файл</b> на верхней панели инструментов и выберите опцию <b>Сохранить</b>.</li>
            </ul>
            <p class="note desktopDocumentFeatures">Чтобы не допустить потери данных в <em>десктопной версии</em> в случае непредвиденного закрытия программы, вы можете включить опцию <b>Автовосстановление</b> на странице <a href="../HelpfulHints/AdvancedSettings.htm" onclick="onhyperlinkclick(this)">Дополнительные параметры</a>. </p>
            <div class="desktopDocumentFeatures">
                <p>Чтобы в <em>десктопной версии</em> сохранить документ под другим именем, в другом местоположении или в другом формате,</p>
                <ol>
                    <li>нажмите на вкладку <b>Файл</b> на верхней панели инструментов,</li>
                    <li>выберите опцию <b>Сохранить как</b>,</li>
                    <li>выберите один из доступных форматов: DOCX, ODT, RTF, TXT, PDF, PDF/A, HTML, FB2, EPUB, DOCXF, OFORM. Также можно выбрать вариант <b>Шаблон документа</b> DOTX или OTT.</li>
                </ol>
            </div>
            <div class="onlineDocumentFeatures">
                <h3>Скачивание</h3>
                <p>Чтобы в <em>онлайн-версии</em> скачать готовый документ и сохранить его на жестком диске компьютера,</p>
                <ol>
                    <li>нажмите на вкладку <b>Файл</b> на верхней панели инструментов,</li>
                    <li>выберите опцию <b>Скачать как</b>,</li>
                    <li>выберите один из доступных форматов: DOCX, PDF, ODT, TXT, DOTX, PDF/A, OTT, RTF, HTML, FB2, EPUB, DOCXF, OFORM.</li>
                </ol>
                <h3>Сохранение копии</h3>
                <p>Чтобы в <em>онлайн-версии</em> сохранить копию документа на портале,</p>
                <ol>
                    <li>нажмите на вкладку <b>Файл</b> на верхней панели инструментов,</li>
                    <li>выберите опцию <b>Сохранить копию как</b>,</li>
                    <li>выберите один из доступных форматов: DOCX, PDF, ODT, TXT, DOTX, PDF/A, OTT, RTF, HTML, FB2, EPUB, DOCXF, OFORM.</li>
                    <li>выберите местоположение файла на портале и нажмите <b>Сохранить</b>.</li>
                </ol>
            </div>
            <h3 id="print">Печать</h3>
            <p>Чтобы распечатать текущий документ,</p>
            <ul>
                <li>нажмите значок <b>Напечатать файл</b> <div class = "icon icon-print"></div> в левой части шапки редактора, или</li>
                <li>используйте сочетание клавиш <b>Ctrl+P</b>, или</li>
                <li>нажмите на вкладку <b>Файл</b> на верхней панели инструментов и выберите опцию <b>Печать</b>.</li>
            </ul>
            <div class="note">
                В браузере Firefox возможна печатать документа без предварительной загрузки в виде файла .pdf.
            </div>
            <p>Также можно распечатать выделенный фрагмент текста с помощью пункта контекстного меню <b>Напечатать выделенное</b> как в режиме <b>Редактирования</b>, так и в режиме <b>Просмотра</b> (<b>кликните правой кнопкой мыши</b> и выберите опцию <b>Напечатать выделенное</b>).</p>
            <p><span class="desktopDocumentFeatures">В <em>десктопной версии</em> документ будет напрямую отправлен на печать.</span> <span class="onlineDocumentFeatures">В <em>онлайн-версии</em> на основе данного документа будет сгенерирован файл PDF. Вы можете открыть и распечатать его, или сохранить его на жестком диске компьютера или съемном носителе чтобы распечатать позже. В некоторых браузерах, например Хром и Опера, есть встроенная возможность для прямой печати.</span></p>
        </div>
	</body>
</html>