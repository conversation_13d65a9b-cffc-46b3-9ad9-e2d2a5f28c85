<!DOCTYPE html>
<html>
	<head>
		<title>Вкладка Главная</title>
		<meta charset="utf-8" />
        <meta name="description" content="Знакомство с пользовательским интерфейсом редактора документов - Вкладка Главная" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Вкладка Главная</h1>
            <p>Вкладка <b>Главная</b> <a href="https://www.onlyoffice.com/ru/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Редактора документов</b></a> открывается по умолчанию при открытии документа. Она позволяет форматировать шрифт и абзацы. Здесь также доступны некоторые другие опции, такие как <span class="onlineDocumentFeatures">cлияние и</span> цветовые схемы.</p>
            <div class="onlineDocumentFeatures">
                <p>Окно онлайн-редактора документов:</p>
                <p><img alt="Вкладка Главная" src="../images/interface/hometab.png" /></p>
            </div>
            <div class="desktopDocumentFeatures">
                <p>Окно десктопного редактора документов:</p>
                <p><img alt="Вкладка Главная" src="../images/interface/desktop_hometab.png" /></p>
            </div>
            <p>С помощью этой вкладки вы можете выполнить следующие действия:</p>
            <ul>
                <li>задавать <a href="../UsageInstructions/FontTypeSizeColor.htm" onclick="onhyperlinkclick(this)">тип, размер и цвет</a> шрифта,</li>
                <li>применять <a href="../UsageInstructions/DecorationStyles.htm" onclick="onhyperlinkclick(this)">стили оформления</a> шрифта,</li>
                <li>выбирать <a href="../UsageInstructions/BackgroundColor.htm" onclick="onhyperlinkclick(this)">цвет фона</a> для абзаца,</li>
                <li>создавать маркированные и нумерованные <a href="../UsageInstructions/CreateLists.htm" onclick="onhyperlinkclick(this)">списки</a>,</li>
                <li>изменять <a href="../UsageInstructions/ParagraphIndents.htm" onclick="onhyperlinkclick(this)">отступы</a> абзацев,</li>
                <li>задавать <a href="../UsageInstructions/LineSpacing.htm" onclick="onhyperlinkclick(this)">междустрочный интервал</a> в абзацах,</li>
                <li><a href="../UsageInstructions/AlignText.htm" onclick="onhyperlinkclick(this)">выравнивать текст</a> в абзаце,</li>
                <li>отображать и скрывать <a href="../UsageInstructions/NonprintingCharacters.htm" onclick="onhyperlinkclick(this)">непечатаемые символы</a>,</li>
                <li><a href="../UsageInstructions/CopyClearFormatting.htm" onclick="onhyperlinkclick(this)">копировать и очищать</a> форматирование текста,</li>
                <li>изменять <a href="../UsageInstructions/ChangeColorScheme.htm" onclick="onhyperlinkclick(this)">цветовую схему</a>,</li>
                <li class="onlineDocumentFeatures">использовать функцию <a href="../UsageInstructions/UseMailMerge.htm" onclick="onhyperlinkclick(this)">слияния</a> (доступно только в <em>онлайн-версии</em>),</li>
                <li>управлять <a href="../UsageInstructions/FormattingPresets.htm" onclick="onhyperlinkclick(this)">стилями</a>.</li>
            </ul>
		</div>
	</body>
</html>