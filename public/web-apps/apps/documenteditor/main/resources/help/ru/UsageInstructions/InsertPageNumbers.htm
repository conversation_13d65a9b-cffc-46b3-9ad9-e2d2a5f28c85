<!DOCTYPE html>
<html>
	<head>
		<title>Вставка номеров страниц</title>
		<meta charset="utf-8" />
		<meta name="description" content="Вставьте номера страниц для удобства перемещения по документу" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Вставка номеров страниц</h1>
			<p>Для вставки в документ номеров страниц:</p>
			<ol>
                <li>перейдите на вкладку <b>Вставка</b> верхней панели инструментов,</li>
                <li>нажмите значок <div class = "icon icon-headerfooter"></div> <b>Колонтитулы</b> на верхней панели инструментов,</li>
				<li>выберите подменю <b>Вставить номер страницы</b></li>
				<li>выберите одну из следующих опций:
					<ul>
						<li>Чтобы поместить номер страницы на каждую страницу документа, выберите положение номеров страниц на странице.</li>
						<li>Чтобы вставить номер текущей страницы в текущей позиции курсора, выберите опцию <b>В текущей позиции</b>.
                            <p class="note">
                                <b>Примечание</b>: для вставки номера текущей страницы в текущей позиции курсора можно также использовать сочетание клавиш <em>Ctrl+Shift+P</em>.
                            </p>
                        </li>
					</ul>
				</li>
			</ol>
            <p>ИЛИ</p>
            <ol>
                <li>перейдите на вкладку <b>Вставка</b> верхней панели инструментов,</li>
                <li>нажмите значок <div class="icon icon-headerfooter"></div> <b>Колонтитулы</b> на верхней панели инструментов,</li>
                <li>выберите в меню опцию <b>Вставить номер страницы</b> и выберите положение номеров страниц.</li>
            </ol>
            <p>Для вставки общего числа страниц в документе (например, если вы хотите создать запись вида "<em>Страница X из Y</em>"):</p>
            <ol>
                <li>установите курсор там, где требуется вставить общее число страниц,</li>
                <li>нажмите значок <div class = "icon icon-headerfooter"></div> <b>Колонтитулы</b> на верхней панели инструментов,</li>
                <li>выберите опцию <b>Вставить число страниц</b>.</li>
            </ol>
			<hr />
			<p>Для изменения параметров номеров страниц:</p>
            <ol>
                <li>дважды щелкните в области, где расположен номер страницы,</li>
                <li>измените текущие параметры на правой боковой панели:
                    <p><img alt="Правая боковая панель - Настройки колонтитулов" src="../images/right_headerfooter.png" /></p>
                    <ul>
                        <li>Установите <b>положение</b> номеров страниц на странице, а также положение относительно верхнего и нижнего края страницы.</li>
                        <li>Установите флажок <b>Особый для первой страницы</b>, если надо применить особый номер для самой первой страницы, или вообще не добавлять на нее номер.</li>
                        <li>Установите флажок <b>Разные для четных и нечетных</b> для вставки разных номеров для четных и нечетных страниц.</li>
                        <li>Опция <b>Связать с предыдущим</b> доступна, если вы ранее добавили в документ <a href="../UsageInstructions/SectionBreaks.htm" onclick="onhyperlinkclick(this)">разделы</a>. В противном случае она будет неактивна.
                            Кроме того, эта опция недоступна для самого первого раздела (то есть когда выделен верхний или нижний колонтитул, относящийся к первому разделу).
                            По умолчанию эта опция включена и ко всем разделам применяются одна и та же нумерация страниц. Если выбрать область верхних или нижних колонтитулов,
                            она будет отмечена надписью <b>Как в предыдущем</b>. Для того чтобы использовать разную нумерацию страниц в каждом разделе документа,
                            уберите галочку <b>Связать с предыдущим</b>, и надпись <b>Как в предыдущем</b> больше отображаться не будет.
                            <p><img alt="Надпись Как в предыдущем" src="../images/sameasprevious_label.png" /></p></li>
                        <li>Раздел <b>Нумерация страниц</b> позволяет настроить параметры нумерации страниц между различными <a href="../UsageInstructions/SectionBreaks.htm" onclick="onhyperlinkclick(this)">разделами</a> документа. 
                        По умолчанию выбрана опция <b>Продолжить</b>, которая позволяет сохранить последовательную нумерацию страниц после разрыва раздела. 
                        Если вы хотите начать нумерацию страниц текущего раздела документа с особого номера, выберите переключатель <b>Начать с</b> и введите нужное начальное значение в поле справа. </li>
                    </ul>
                </li>
            </ol>
            <p>Чтобы вернуться к редактированию документа, дважды щелкните по рабочей области.</p>
		</div>
	</body>
</html>