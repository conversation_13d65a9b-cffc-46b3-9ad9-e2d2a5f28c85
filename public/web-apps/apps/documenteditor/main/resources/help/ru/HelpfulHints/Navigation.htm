<!DOCTYPE html>
<html>
	<head>
		<title>Параметры представления и инструменты навигации</title>
		<meta charset="utf-8" />
		<meta name="description" content="Описание параметров представления и инструментов навигации, таких как масштаб, кнопки предыдущей/следующей страницы" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
		<h1>Параметры представления и инструменты навигации</h1>
			<p>В <b>редакторе документов</b> доступен ряд инструментов, позволяющих облегчить просмотр и навигацию по документу: масштаб, указатель номера страницы и другие.</p>
			<h3>Настройте параметры представления</h3>
			<p>Чтобы настроить стандартные параметры представления и установить наиболее удобный режим работы с документом, перейдите на вкладку <b>Вид</b> и выберите, какие элементы интерфейса требуется скрыть или отобразить.
            На вкладке <b>Вид</b> можно выбрать следующие опции:
			</p>			
			<ul>
				<li><b>Заголовки</b> – чтобы показать заголовки документа в левой панели.</li>
				<li><b>Масштаб</b> – чтобы выбрать из выпадающего списка нужное значение масштаба от 50% до 500%.</li>
				<li><b>По размеру страницы</b> - чтобы вся страница целиком помещалась в видимой части рабочей области.</li>
				<li><b>По ширине</b> - чтобы ширина страницы документа соответствовала видимой части рабочей области.</li>
				<li><b>Тема интерфейса</b> – выберите из выпадающего меню одну из доступных тем интерфейса: <em>Системная</em>, <em>Светлая</em>, <em>Классическая светлая</em>, <em>Темная</em>, <em>Контрастная темная</em>. Когда включена <em>Темная</em> или <em>Контрастная темная</em> тема, становится активным переключатель <b>Темный документ</b>; используйте его, чтобы выбрать для рабочей области белый или темно-серый цвет.</li>
				<li>
					<b>Всегда показывать панель инструментов</b> – когда эта опция отключена, будет скрыта верхняя панель инструментов, которая содержит команды. Названия вкладок при этом остаются видимыми.
					<p class="note">Можно также дважды щелкнуть по любой вкладке, чтобы скрыть верхнюю панель инструментов или отобразить ее снова.</p>
				</li>
				<li><b>Строка состояния</b> – когда эта опция отключена, будет скрыта самая нижняя панель, на которой находится <b>Указатель номера страницы</b>, <b>Счетчик количества слов</b> и кнопки <b>Масштаба</b>. Чтобы отобразить скрытую <b>строку состояния</b>, щелкните по этой опции еще раз.</li>
				<li><b>Линейки</b> - когда эта опция отключена, будут скрыты линейки, которые используются для выравнивания текста, графики, таблиц, и других элементов в документе, установки полей, позиций табуляции и отступов абзацев. Чтобы отобразить скрытые <b>линейки</b>, щелкните по этой опции еще раз.</li>
				<li><b>Левая панель</b> - когда эта опция отключена, будет скрыта левая панель, на которой расположены кнопки <b>Поиск</b>, <b>Комментарии</b> и т. д. Чтобы отобразить скрытую <b>Левую панель</b>, щелкните по этой опции еще раз.</li>
				<li><b>Правая панель</b> - когда эта опция отключена, будет скрыта правая панель, на которой расположены <b>Параметры</b>. Чтобы отобразить скрытую <b>Правую панель</b>, щелкните по этой опции еще раз.</li>
			</ul>
			<p>Правая боковая панель свернута по умолчанию. Чтобы ее развернуть, выделите любой объект или фрагмент текста и щелкните по значку вкладки, которая в данный момент активирована (чтобы свернуть правую боковую панель, щелкните по этому значку еще раз).</p>
			<p>Когда открыта панель <b>Комментарии</b><span class="onlineDocumentFeatures"> или <b>Чат</b></span>, левую боковую панель можно настроить путем простого перетаскивания: 
			наведите курсор мыши на край левой боковой панели, чтобы курсор превратился в двунаправленную стрелку, и перетащите край панели вправо, чтобы увеличить ширину панели. Чтобы восстановить исходную ширину, перетащите край панели влево.</p>
            <h3 id="navigationtools">Используйте инструменты навигации</h3>
			<p>Для осуществления навигации по документу используйте следующие инструменты:</p>
			<p>Кнопки <b>Масштаб</b> расположены в правом нижнем углу и используются для увеличения и уменьшения текущего документа. 
			Чтобы изменить выбранное в текущий момент значение масштаба в процентах, щелкните по нему и выберите в списке один из доступных параметров масштабирования (50% / 75% / 100% / 125% / 150% / 175% / 200%)
			или используйте кнопки <b>Увеличить</b> <span class = "icon icon-zoomin"></span> или <b>Уменьшить</b> <span class = "icon icon-zoomout"></span>. 
			Нажмите значок <b>По ширине</b> <span class = "icon icon-fitwidth"></span>, чтобы ширина страницы документа соответствовала видимой части рабочей области.
			Чтобы вся страница целиком помещалась в видимой части рабочей области, нажмите значок <b>По размеру страницы</b> <span class = "icon icon-fitpage"></span>.
			Параметры масштаба доступны также на вкладке <a href="../ProgramInterface/ViewTab.htm" onclick="onhyperlinkclick(this)">Вид</a>.</p>
			<p><b>Указатель номера страницы</b> показывает текущую страницу в составе всех страниц текущего документа (страница 'n' из 'nn'). 
			Щелкните по этой надписи, чтобы открыть окно, в котором Вы можете ввести номер нужной страницы и быстро перейти на нее.</p>
		</div>
	</body>
</html>