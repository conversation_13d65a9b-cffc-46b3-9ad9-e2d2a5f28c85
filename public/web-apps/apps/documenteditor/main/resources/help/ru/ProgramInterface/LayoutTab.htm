<!DOCTYPE html>
<html>
	<head>
		<title>Вкладка Макет</title>
		<meta charset="utf-8" />
        <meta name="description" content="Узнайте, что такое вкладка Макет, и как она позволяет изменить внешний вид документов Word, задать параметры страниц и определить расположение визуальных элементов." />
        <meta name="keywords" content="вкладка макет word, вкладка макет в word, где вкладка макет, где найти вкладку макет">
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Вкладка Макет</h1>
            <p>Вкладка <b>Макет</b> <a href="https://www.onlyoffice.com/ru/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Редактора документов</b></a> позволяет изменить внешний вид документа: задать параметры страницы и определить расположение визуальных элементов.</p>
            <div class="onlineDocumentFeatures">
                <p>Окно онлайн-редактора документов:</p>
                <p><img alt="Вкладка Макет" src="../images/interface/layouttab.png" /></p>
            </div>
            <div class="desktopDocumentFeatures">
                <p>Окно десктопного редактора документов:</p>
                <p><img alt="Вкладка Макет" src="../images/interface/desktop_layouttab.png" /></p>
            </div>
            <p>С помощью этой вкладки вы можете выполнить следующие действия:</p>
            <ul>
                <li>настраивать <a href="../UsageInstructions/SetPageParameters.htm#margins" onclick="onhyperlinkclick(this)">поля</a>, <a href="../UsageInstructions/SetPageParameters.htm#orientation" onclick="onhyperlinkclick(this)">ориентацию</a>, <a href="../UsageInstructions/SetPageParameters.htm#size" onclick="onhyperlinkclick(this)">размер</a> страницы,</li>
                <li>добавлять <a href="../UsageInstructions/SetPageParameters.htm#columns" onclick="onhyperlinkclick(this)">колонки</a>,</li>
                <li>вставлять <a href="../UsageInstructions/PageBreaks.htm" onclick="onhyperlinkclick(this)">разрывы страниц</a>, <a href="../UsageInstructions/SectionBreaks.htm" onclick="onhyperlinkclick(this)">разрывы разделов</a> и <a href="../UsageInstructions/SetPageParameters.htm#columns" onclick="onhyperlinkclick(this)">разрывы колонок</a>,</li>
                <li>вставить <a href="../UsageInstructions/InsertLineNumbers.htm" onclick="onhyperlinkclick(this)">нумерацию строк</a></li>
                <li>выравнивать и располагать в определенном порядке объекты (<a href="../UsageInstructions/InsertTables.htm" onclick="onhyperlinkclick(this)">таблицы</a>, <a href="../UsageInstructions/InsertImages.htm" onclick="onhyperlinkclick(this)">изображения</a>, <a href="../UsageInstructions/InsertCharts.htm" onclick="onhyperlinkclick(this)">диаграммы</a>, <a href="../UsageInstructions/InsertAutoshapes.htm" onclick="onhyperlinkclick(this)">фигуры</a>),</li>
                <li>изменять <a href="../UsageInstructions/ChangeWrappingStyle.htm" onclick="onhyperlinkclick(this)">стиль обтекания</a> и редактировать границу обтекания,</li>
                <li>добавлять <a href="../UsageInstructions/AddWatermark.htm" onclick="onhyperlinkclick(this)">подложку</a>.</li>
            </ul>
		</div>
	</body>
</html>