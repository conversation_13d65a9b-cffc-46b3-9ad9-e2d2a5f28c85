<!DOCTYPE html>
<html>
	<head>
		<title>Отображение/скрытие непечатаемых символов</title>
		<meta charset="utf-8" />
		<meta name="description" content="Показывайте или скрывайте непечатаемые символы при форматировании текста, создании таблиц и редактировании документов" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Отображение/скрытие непечатаемых символов</h1>
			<p>Непечатаемые символы помогают редактировать документ. Они обозначают присутствие разных типов форматирования, но не выводятся на печать при печати документа, даже если отображаются на экране.</p>
			<p>Чтобы показать или скрыть непечатаемые символы, нажмите значок <b>Непечатаемые символы</b> <span class = "icon icon-nonprintingcharacters"></span> на вкладке <b>Главная</b> верхней панели инструментов. Вы также можете использовать сочетание клавиш <em>Ctrl+Shift+Num8</em>.</p>
			<p>Непечатаемые символы включают в себя:</p>
			<table>
				<tr>
					<td>Пробел</td>
					<td><div class = "icon icon-space"></div></td>
					<td>Вставляется при нажатии на клавишу <b>Пробел</b> на клавиатуре. Создает пробел между символами.</td>
				</tr>
				<tr>
					<td>Символ табуляции</td>
					<td><div class = "icon icon-tab"></div></td>
					<td>Вставляется при нажатии на клавишу <b>Tab</b>. Используется для переноса курсора на следующую позицию табуляции.</td>
				</tr>
				<tr>
					<td>Символ конца абзаца (т.е. жесткий перевод строки)</td>
					<td><div class = "icon icon-hard"></div></td>
					<td>Вставляется при нажатии на клавишу <b>Enter</b>. Завершает абзац и добавляет после него некоторое свободное место. Содержит информацию о форматировании абзаца.</td>
				</tr>
				<tr>
					<td>Символ разрыва строки (т.е. мягкий перевод строки)</td>
					<td><div class = "icon icon-soft"></div></td>
					<td>Вставляется при использовании сочетания клавиш <b>Shift+Enter</b>. Разрывает текущую строку и помещает строки текста рядом друг с другом. Мягкий перевод строки используется, в основном, в названиях и заголовках.</td>
				</tr>
				<tr>
					<td>Неразрываемый пробел</td>
					<td><div class = "icon icon-nonbreakspace"></div></td>
					<td>Вставляется при использовании сочетания клавиш <b>Ctrl+Shift+Пробел</b>. Создает пробел между символами, который нельзя использовать, чтобы начать новую строку.</td>
				</tr>
				<tr>
					<td>Символ разрыва страницы</td>
					<td><div class = "big big-pagebreak"></div></td>
					<td>Вставляется при использовании значка <div class = "icon icon-pagebreak1"></div> <b>Разрывы</b> на вкладке <b>Вставка</b> верхней панели инструментов и последующем выборе опции <b>Вставить разрыв страницы</b>. Также вставляется при выборе опции <b>С новой страницы</b> в контекстном меню или окне дополнительных параметров.</td>
				</tr>
				<tr>
                  <td>Символ разрыва раздела</td>
                  <td>
                    <div class = "big big-sectionbreak"></div>
                  </td>
                    <td>
                        Вставляется при использовании значка <div class = "icon icon-pagebreak1"></div> <b>Разрывы</b> на вкладке <b>Вставка</b> верхней панели инструментов и последующем выборе одной из опций подменю <b>Вставить разрыв раздела</b> (используются различные указатели разрыва раздела в зависимости от того, какая опция выбрана: Со следующей страницы, На текущей странице, С четной страницы или С нечетной страницы).
                    </td>
                </tr>
                <tr>
                    <td>Символ разрыва колонки</td>
                    <td><div class = "big big-columnbreak"></div></td>
                    <td>Вставляется при использовании значка <div class = "icon icon-pagebreak1"></div> <b>Разрывы</b> на вкладке <b>Вставка</b> верхней панели инструментов и последующем выборе опции <b>Вставить разрыв колонки</b>.</td>
                </tr>
                <tr>
                    <td>Маркер ячейки и конца строки в таблицах</td>
                    <td><div class = "icon icon-cellrow"></div></td>
                    <td>Эти маркеры содержат коды форматирования для отдельных ячеек и строк соответственно.</td>
                </tr>
				<tr>
					<td>Маленький черный квадрат в поле слева от абзаца</td>
					<td><div class = "icon icon-square"></div></td>
					<td>Он обозначает, что применен, по крайней мере, один из параметров абзаца, например, <b>Не разрывать абзац</b>, <b>С новой страницы</b>.</td>
				</tr>
				<tr>
					<td>Символы якоря</td>
					<td><div class = "icon icon-anchor"></div></td>
					<td>Они показывают, где находятся плавающие объекты (то есть те, для которых выбран любой стиль обтекания, кроме стиля обтекания <b>В тексте</b>), например, изображения, автофигуры, диаграммы. Для отображения символа якоря выберите объект.</td>
				</tr>
			</table>
		</div>
	</body>
</html>