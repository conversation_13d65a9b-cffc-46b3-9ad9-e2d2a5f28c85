<!DOCTYPE html>
<html>
	<head>
        <title>Вставка объектов SmartArt</title>
		<meta charset="utf-8" />
        <meta name="description" content="Вставляйте новые графические объекты SmartArt или редактируйте объекты SmartArt, добавленные с помощью сторонних редакторов" />
        <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Вставка объектов SmartArt</h1>
            <p><b>Графика SmartArt</b> используется для создания визуального представления иерархической структуры при помощи выбора наиболее подходящего макета. Вставляйте новые графические объекты SmartArt или редактируйте объекты SmartArt, добавленные с помощью сторонних редакторов.</p>
            <p>Чтобы <b>вставить</b> объект SmartArt,</p>
            <ol>
                <li>перейдите на вкладку <b>Вставка</b>,</li>
                <li>нажмите кнопку <b>SmartArt</b>,</li>
                <li>наведите курсор на один из доступных стилей макета, например, <em>Список</em> или <em>Процесс</em>,</li>
                <li>выберите один из доступных типов макета из списка, появившегося справа от выделенного пункта меню.</li>
            </ol>
            <p>Вы можете <b>настроить</b> параметры SmartArt на правой панели:</p>
            <img alt="" src="../images/smartart_settings.png" />
            <ul>
                <li>
                    <b>Заливка</b> - используйте этот раздел, чтобы выбрать заливку объекта SmartArt. Можно выбрать следующие варианты:
                    <ul>
                        <li>
                            <b>Заливка цветом</b> - выберите эту опцию, чтобы задать сплошной цвет, которым требуется заполнить внутреннее пространство выбранного объекта SmartArt.
                            <p><img alt="Заливка цветом" src="../images/fill_color.png" /></p>
                            <p id="color">Нажмите на цветной прямоугольник, расположенный ниже, и выберите нужный цвет из доступных <a href="../UsageInstructions/ChangeColorScheme.htm" onclick="onhyperlinkclick(this)">наборов цветов</a> или задайте любой цвет, который вам нравится.</p>
                        </li>
                        <li>
                            <b>Градиентная заливка</b> - выберите эту опцию, чтобы залить объект SmartArt двумя цветами, плавно переходящими друг в друга. Настраивайте градиентную заливку без ограничений. Нажмите значок <b>Параметры фигуры</b> <div class="icon icon-shape_settings_icon"></div>, чтобы открыть меню <b>Заливка</b> на правой панели:
                            <p><img alt="Градиентная заливка" src="../images/fill_gradient.png" /></p>
                            <p>Доступные пункты меню:</p>
                            <ul>
                                <li>
                                    <b>Стиль</b> - выберите <b>Линейный</b> или <b>Радиальный</b>:
                                    <ul>
                                        <li><b>Линейный</b> используется, когда вам нужно, чтобы цвета изменялись слева направо, сверху вниз или под любым выбранным вами углом в одном направлении. Чтобы выбрать предустановленное направление, щелкните на стрелку рядом с окном предварительного просмотра <b>Направление</b> или же задайте точное значение угла градиента в поле <b>Угол</b>.</li>
                                        <li><b>Радиальный</b> используется, когда вам нужно, чтобы цвета изменялись по кругу от центра к краям.</li>
                                    </ul>
                                </li>
                                <li>
                                    <b>Точка градиента</b> - это определенная точка перехода от одного цвета к другому.
                                    <ul>
                                        <li>Чтобы добавить точку градиента, используйте кнопку <div class="icon icon-addgradientpoint"></div> <b>Добавить точку градиента</b> или ползунок. Вы можете добавить до 10 точек градиента. Каждая следующая добавленная точка градиента никоим образом не повлияет на внешний вид текущей градиентной заливки. Чтобы удалить определенную точку градиента, используйте кнопку  <div class="icon icon-removegradientpoint"></div> <b>Удалить точку градиента</b>.</li>
                                        <li>Чтобы изменить положение точки градиента, используйте ползунок или укажите <b>Положение</b> в процентах для точного местоположения.</li>
                                        <li>Чтобы применить цвет к точке градиента, щелкните точку на панели ползунка, а затем нажмите <b>Цвет</b>, чтобы выбрать нужный цвет.</li>
                                    </ul>
                                </li>
                            </ul>
                        </li>
                        <li>
                            <b>Изображение или текстура</b> - выберите эту опцию, чтобы использовать в качестве фона объекта SmartArt какое-то изображение или готовую текстуру.
                            <p><img alt="Заливка с помощью изображения или текстуры" src="../images/fill_picture.png" /></p>
                            <ul>
                                <li>Если вы хотите использовать изображение в качестве фона объекта SmartArt, можно добавить изображение <b>Из файла</b>, выбрав его на жестком диске компьютера, или <b>По URL</b>, вставив в открывшемся окне соответствующий URL-адрес, или <b>Из хранилища</b>, выбрав нужное изображение, сохраненное на портале.</li>
                                <li>
                                    Если вы хотите использовать текстуру в качестве фона объекта SmartArt, разверните меню <b>Из текстуры</b> и выберите нужную предустановленную текстуру.
                                    <p>В настоящее время доступны следующие текстуры: Холст, Картон, Темная ткань, Песок, Гранит, Серая бумага, Вязание, Кожа, Крафт-бумага, Папирус, Дерево.</p>
                                </li>
                            </ul>
                            <ul>
                                <li>
                                    В том случае, если выбранное <b>изображение</b> имеет большие или меньшие размеры, чем объект SmartArt, можно выбрать из выпадающего списка параметр <b>Растяжение</b> или <b>Плитка</b>.
                                    <p>Опция <b>Растяжение</b> позволяет подогнать размер изображения под размер объекта SmartArt, чтобы оно могло полностью заполнить пространство.</p>
                                    <p>Опция <b>Плитка</b> позволяет отображать только часть большего изображения, сохраняя его исходные размеры, или повторять меньшее изображение, сохраняя его исходные размеры, по всей площади объекта SmartArt, чтобы оно могло полностью заполнить пространство.</p>
                                    <p class="note"><b>Примечание</b>: любая выбранная предустановленная <b>текстура</b> полностью заполняет пространство, но в случае необходимости можно применить эффект <b>Растяжение</b>.</p>
                                </li>
                            </ul>
                        </li>
                        <li>
                            <b>Узор</b> - выберите эту опцию, чтобы залить объект SmartArt с помощью двухцветного рисунка, который образован регулярно повторяющимися элементами.
                            <p><img alt="Заливка с помощью узора" src="../images/fill_pattern.png" /></p>
                            <ul>
                                <li><b>Узор</b> - выберите один из готовых рисунков в меню.</li>
                                <li><b>Цвет переднего плана</b> - нажмите на это цветовое поле, чтобы изменить цвет элементов узора.</li>
                                <li><b>Цвет фона</b> - нажмите на это цветовое поле, чтобы изменить цвет фона узора.</li>
                            </ul>
                        </li>
                        <li><b>Без заливки</b> - выберите эту опцию, если вы вообще не хотите использовать заливку.</li>
                        <li>
                            <b>Контур</b> - используйте этот раздел, чтобы изменить толщину, цвет или тип контура объекта SmartArt.
                            <ul>
                                <li>Для изменения <b>толщины</b> контура выберите из выпадающего списка <b>Толщина</b> одну из доступных опций. Доступны следующие опции: 0.5 пт, 1 пт, 1.5 пт, 2.25 пт, 3 пт, 4.5 пт, 6 пт. Или выберите опцию <b>Без линии</b>, если вы вообще не хотите использовать контур.</li>
                                <li>Для изменения <b>цвета</b> контура щелкните по цветному прямоугольнику и <a href="../UsageInstructions/ChangeColorScheme.htm" onclick="onhyperlinkclick(this)">выберите нужный цвет</a>.</li>
                                <li>Для изменения <b>типа</b> контура выберите нужную опцию из соответствующего выпадающего списка (по умолчанию применяется сплошная линия, ее можно изменить на одну из доступных пунктирных линий).</li>
                            </ul>
                        </li>
                        <li><b>Стиль обтекания</b> - используйте этот раздел, чтобы выбрать один из доступных стилей обтекания текстом - в тексте, вокруг рамки, по контуру, сквозное, сверху и снизу, перед текстом, за текстом.</li>
                        <li><b>Отображать тень</b> - отметьте эту опцию, чтобы отображать объект SmartArt с тенью.</li>
                    </ul>
                </li>
            </ul>
            <p>Нажмите ссылку <b>Дополнительные параметры</b>, чтобы открыть <a href="../UsageInstructions/InsertAutoshapes.htm#autoshape_advanced" onclick="onhyperlinkclick(this)">дополнительные параметры</a>.</p>
            <!--<p><b>Параметры абзаца</b> - для редактирования отступов и интервалов, положения на странице, границ и заливки, шрифтов, табуляций и внутренних полей. Обратитесь к разделу <a href="https://helpcenter.onlyoffice.com/ru/userguides/docs-de.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Форматирование абзаца</b></a> для подробного описания каждого параметра. Эта вкладка становится активной только для объектов <b>SmartArt</b>.</p>
            <p><a href="../UsageInstructions/InsertAutoshapes.htm" onclick="onhyperlinkclick(this)"><b>Параметры фигуры</b></a> - для редактирования фигур, используемых в макете. Вы можете изменять размер формы, редактировать заливку, контур, толщину, стиль обтекания, положение, линии и стрелки, текстовое поле и альтернативный текст.</p>
            <p>
                <a href="../UsageInstructions/InsertTextObjects.htm" onclick="onhyperlinkclick(this)"><b>Параметры объекта Text Art</b></a> - для редактирования <b>стиля Text Art</b>, который используется <b>SmartArt</b> для выделения текста. Вы можете изменить шаблон <b>Text Art</b>, тип заливки, цвет и непрозрачность, толщину линии, цвет и тип. Эта вкладка становится активной только для объектов <b>SmartArt</b>.
            </p>
            <p>Щелкните правой кнопкой мыши по <b>SmartArt</b> или по границе данного элемента, чтобы получить доступ к следующим параметрам форматирования:</p>
            <p><img alt="Меню SmartArt" src="../images/smartart_rightclick.png" /></p>
            <p> <a href="../UsageInstructions/InsertTextObjects.htm" onclick="onhyperlinkclick(this)"><b>Стиль обтекания</b></a> - для определения способа расположения объекта относительно текста. Параметр <b>Стиль обтекания</b> доступен после щелчка по границе графического объекта <b>SmartArt</b>.</p>
            <p><a href="../UsageInstructions/AddCaption.htm" onclick="onhyperlinkclick(this)"><b>Вставить название</b></a> - для добавления возможности ссылаться на <b>графический объект SmartArt</b> в документе.</p>
            <p><a href="../UsageInstructions/InsertAutoshapes.htm" onclick="onhyperlinkclick(this)"><b>Дополнительные параметры фигуры</b></a> - для доступа к дополнительным параметрам форматирования фигуры.</p>
            
            <p>Щелкните правой кнопкой мыши по графическому объекту <b>SmartArt</b>, чтобы получить доступ к следующим параметрам форматирования текста:</p>
            <p><img alt="Меню SmartArt" src="../images/smartart_rightclick2.png" /></p>
            <p><b>Выравнивание по вертикали</b> - для выбрать выравнивание текста внутри выбранного объекта <b>SmartArt</b>: <b>Выровнять по верхнему краю</b>, <b>Выровнять по середине</b>, <b> Выровнять по нижнему краю</b>.</p>
            <p><b>Направление текста</b> - выбрать направление направления текста внутри выбранного объекта <b>SmartArt</b>: <b>Горизонтальное</b>, <b>Повернуть текст вниз</b>, <b> Повернуть текст вверх</b>.</p>
            <p>Обратитесь к <a href="https://helpcenter.onlyoffice.com/ru/userguides/docs-de.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Дополнительным параметрам абзаца</b></a>, чтобы получить информацию о дополнительных параметрах форматирования абзаца.</p>-->
        </div>
	</body>
</html>