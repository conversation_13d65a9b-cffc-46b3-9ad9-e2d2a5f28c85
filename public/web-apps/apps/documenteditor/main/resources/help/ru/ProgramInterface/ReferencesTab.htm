<!DOCTYPE html>
<html>
	<head>
		<title>Вкладка Ссылки</title>
		<meta charset="utf-8" />
        <meta name="description" content="Знакомство с пользовательским интерфейсом редактора документов - Вкладка Ссылки" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Вкладка Ссылки</h1>
            <p>Вкладка <b>Ссылки</b> <a href="https://www.onlyoffice.com/ru/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Редактора документов</b></a> позволяет управлять различными типами ссылок: добавлять и обновлять оглавление, создавать и редактировать сноски, вставлять гиперссылки.</p>
            <div class="onlineDocumentFeatures">
                <p>Окно онлайн-редактора документов:</p>
                <p><img alt="Вкладка Ссылки" src="../images/interface/referencestab.png" /></p>
            </div>
            <div class="desktopDocumentFeatures">
                <p>Окно десктопного редактора документов:</p>
                <p><img alt="Вкладка Ссылки" src="../images/interface/desktop_referencestab.png" /></p>
            </div>
            <p>С помощью этой вкладки вы можете выполнить следующие действия:</p>
            <ul>
                <li>создавать и автоматически обновлять <a href="../UsageInstructions/CreateTableOfContents.htm" onclick="onhyperlinkclick(this)">оглавление</a>,</li>
                <li>вставлять <a href="../UsageInstructions/InsertFootnotes.htm" onclick="onhyperlinkclick(this)">сноски</a> и <a href="../UsageInstructions/InsertEndnotes.htm" onclick="onhyperlinkclick(this)">концевые сноски</a>,</li>
                <li>вставлять <a href="../UsageInstructions/AddHyperlinks.htm" onclick="onhyperlinkclick(this)">гиперссылки</a>,</li>
                <li>добавлять <a href="../UsageInstructions/InsertBookmarks.htm" onclick="onhyperlinkclick(this)">закладки</a>,</li>
                <li>добавлять <a href="../UsageInstructions/AddCaption.htm" onclick="onhyperlinkclick(this)">названия</a>.</li>
                <li>вставлять <a href="../UsageInstructions/InsertCrossReference.htm" onclick="onhyperlinkclick(this)">перекрестные ссылки</a>.</li>
                <li>добавлять a <a href="../UsageInstructions/AddTableofFigures.htm" onclick="onhyperlinkclick(this)">список иллюстраций</a>.</li>
            </ul>
		</div>
	</body>
</html>