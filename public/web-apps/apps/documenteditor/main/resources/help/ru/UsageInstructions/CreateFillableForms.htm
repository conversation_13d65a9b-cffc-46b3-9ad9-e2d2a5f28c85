<!DOCTYPE html>
<html>
<head>
    <title>Создание заполняемых форм</title>
    <meta charset="utf-8" />
    <meta name="description" content="Создавайте заполняемые формы для расширенных возможностей работы с формами" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>

        <h1>Создание заполняемых форм</h1>
        <p>Редактор Документов ONLYOFFICE позволяет с легкостью создавать <b>заполняемые формы</b> в ваших документах, таких как проекты соглашений или опросы.</p>
        <p><b>Шаблон формы</b> - это формат DOCXF, который предлагает ряд инструментов для создания заполняемых форм. Сохраните полученную форму как файл <b>DOCXF</b>, и у вас будет шаблон формы, который вы сможете совместно редактировать и просматривать. Чтобы сделать шаблон формы заполняемым и ограничить редактирование файла другими пользователями, сохраните его как файл <b>OFORM</b>. Чтобы узнать больше, пожалуйста, обратитесь к <a href="../UsageInstructions/FillingOutForm.htm">руководству по заполнению формы</a>.</p>
        <p class="note"> <b>DOCXF</b> и <b>OFORM</b> - это новые форматы <b>ONLYOFFICE</b>, которые позволяют создавать шаблоны форм и заполнять формы. Используйте онлайн или десктопную версию <b>Редактора документов ONLYOFFICE</b>, чтобы в полной мере использовать все возможности, связанные с формами.</p>
        <p>Вы также можете сохранить любой существующий файл <b>DOCX</b> как <b>DOCXF</b>, чтобы использовать его в качестве шаблона формы. Перейдите на вкладку <b>Файл</b>, нажмите кнопку <b>Скачать как...</b> или <b>Сохранить как...</b> в меню слева и выберите значок <b>DOCXF</b>. Теперь вы можете использовать все доступные функции редактирования формы для создания формы.</p>
        <p>В файле <b>DOCXF</b> можно редактировать не только поля формы, но и добавлять, редактировать и форматировать текст или использовать другие функции <b>Редактора документов</b>.</p>
        <p>Создание заполняемых форм возможно с помощью редактируемых пользователем объектов, которые обеспечивают общую согласованность итоговых документов и расширенное взаимодействие с формами.</p>
        <p>На данный момент вы можете создавать редактируемые <b>текстовые поля</b>, <b>поля со списками</b>, <b>выпадающие списки</b>, <b>флажки</b>, <b>переключатели</b> и добавлять специальные области для <b>изображений</b>, а также создавать поля <b>адреса email</b>, <b>номера телефона</b>, <b>даты и времени</b>, <b>индекса</b>, <b>кредитной карты</b>, <b>составные поля</b>. Все эти функции находятся на вкладке <b>Формы</b> и доступны только для файлов <b>DOCXF</b>.</p>

        <h2 id="textfield">Создание нового Текстового поля</h2>
        <p><em>Текстовые поля</em> - это редактируемое текстовое поле, текст внутри которого не может быть отформатирован, и никакие другие объекты не могут быть добавлены.</p>
        <div class="forms">
            <details class="details-example">
                <summary>Чтобы добавить текстовое поле,</summary>
                <ol>
                    <li>поместите курсор в то место, куда вы хотите поместить текстовое поле,</li>
                    <li>перейдите на вкладку <b>Формы</b> верхней панели инструментов,</li>
                    <li>
                        щелкните на значок <div class="icon icon-text_field_icon"></div> <b>Текстовое поле</b>
                        <p>или</p>
                        <p>нажмите стрелку рядом со значком <span class="icon icon-text_field_icon"></span> <b>Текстовое поле</b> и выберите, надо ли вставить <b>встроенное текстовое поле</b> или <b>фиксированное текстовое поле</b>. Чтобы узнать больше о фиксированном поле, прочитайте расположенный ниже абзац <b>Поле фиксированного размера</b> в этом разделе.</p>
                    </li>
                </ol>
                <p><img alt="текстовое поле" src="../images/text_field_inserted.png" /></p>
                <p>В указанном месте добавится текстовое поле, а на правой боковой панели откроется вкладка <b>Параметры формы</b>.</p>
                <div id="text_field_settings">
                    <img alt="параметры текстового поля" src="../images/text_field_settings.png" />
                    <ul>
                        <li><b>Кто должен это заполнять?</b>: выберите роль из выпадающего списка, чтобы задать группу пользователей с доступом к этому полю. Чтобы узнать больше о назначении ролей, прочитайте раздел этой инструкции <a href="../UsageInstructions/CreateFillableForms.htm#managing_roles">Управление ролями</a>.</li>
                        <li><b>Ключ</b>: ключ группировки полей для одновременного заполнения. Чтобы создать новый ключ, введите его название в поле и нажмите клавишу <b>Enter</b>, затем при помощи раскрывающегося списка присвойте необходимый ключ каждому текстовому полю. Будет отображено сообщение <em>Подключенные поля: 2/3...</em>. Чтобы отключить поля, нажмите кнопку <b>Отключить</b>.</li>
                        <li><b>Заполнитель</b>: введите текст, который будет отображаться во вставленном текстовом поле. Значение <em>«Введите ваш текст»</em> установлено по умолчанию.</li>
                        <li><b>Тег</b>: введите текст, который будет использоваться в качестве тега для внутреннего использования, например, будет отображаться только для участников совместного редактирования.</li>
                        <li>
                            <b>Подсказка</b>: введите текст, который будет отображаться в виде подсказки при наведении курсора на текстовое поле.
                            <br /> <img alt="подсказка" src="../images/text_field_tip.png" />
                        </li>
                        <li>
                            <b>Формат</b>: выберите формат содержимого в текстовом поле, например, можно разрешить ввод только выбранных символов: <em>Нет</em>, <em>Цифры</em>, <em>Буквы</em>, <em>Произвольная маска</em> (текст должен соответствовать пользовательской маске, например, (999) 999 99 99), <em>Регулярное выражение</em> (текст должен соответствовать пользовательскому выражению).
                            <p>При выборе формата <em>Произвольная маска</em> или <em>Регулярное выражение</em> под полем <b>Формат</b> появляется дополнительное поле.</p>
                        </li>
                        <li><b>Допустимые символы</b>: введите символы, которые разрешается вводить в текстовое поле.</li>
                        <li>
                            <b>Поле фиксированного размера</b>: установите флажок, чтобы создать поле фиксированного размера. Когда данная опция включена, также доступными становятся параметры <b>Автоподбор</b> и / или <b>Многострочное поле</b>.<br />
                            Поле фиксированного размера выглядит как автофигура. Вы можете установить для него стиль обтекания, а также отрегулировать его положение.
                        </li>
                        <li><b>Автоподбор</b>: данный параметр можно включить только если выбран параметр <b>Поле фиксированного размера</b>. Установите флажок, чтобы размер шрифта автоматически соответствовал размеру поля.</li>
                        <li><b>Многострочное поле</b>: данный параметр можно включить только если выбран параметр <b>Поле фиксированного размера</b>. Установите флажок, чтобы создать поле формы с несколькими строками, в противном случае текст будет занимать только одну строку.</li>
                        <li><b>Максимальное число знаков</b>: по умолчанию без ограничений; поставьте флажок и укажите максимальное количество символов в поле справа.</li>
                        <li>
                            <b>Комбинировать символы</b>: равномерно распределить текст внутри вставленного текстового поля и настроить его общий вид. Не устанавливайте флажок, чтобы сохранить настройки по умолчанию, или установите его, чтобы указать следующие параметры:
                            <ul>
                                <li><b>Ширина ячейки</b>: выберите необходимый параметр - <em>Авто</em> (ширина вычисляется автоматически), <em>Минимум</em> (ширина не меньше значения, заданного вручную) или <em>точно</em> (ширина соответствует значению, заданному вручную). Текст внутри будет выровнен в соответствии с указанным значением.</li>
                            </ul>
                        </li>
                        <li><b>Цвет границ</b>: щелкните на значок <div class="icon icon-nofill"></div>, чтобы выбрать цвет границ вставляемого текстового поля. Выберите подходящий цвет из палитры. При необходимости вы можете создать новый <b>Пользовательский цвет</b>.</li>
                        <li><b>Цвет фона</b>: щелкните на значок <div class="icon icon-nofill"></div> чтобы выбрать цвет фона вставляемого текстового поля. Выберите подходящий цвет из палитры <b>Цветов темы</b>, <b>Стандартных цветов</b> или создайте новый <b>Пользовательский цвет</b>.</li>
                        <li><b>Обязательно</b>: установите этот флажок, чтобы сделать текстовое поле обязательным для заполнения.</li>
                    </ul>
                </div>
                <p><img alt="комбинировать символы" src="../images/comb_of_characters.png" /></p>
                <p>Щелкните текстовое поле и <a href="../UsageInstructions/FontTypeSizeColor.htm">отредактируйте тип, размер и цвет шрифта</a>, примените <a href="../UsageInstructions/DecorationStyles.htm">стили оформления</a> и <a href="../UsageInstructions/FormattingPresets.htm">стили форматирования</a>.</p>
            </details>
        </div>


        <h2>Создание Поля со списком</h2>
        <p><em> Поля со списком </em> содержат раскрывающийся список с набором вариантов, которые могут редактировать пользователи.</p>
        <div class="forms">
            <details class="details-example">
                <summary>Чтобы добавить поле со списком,</summary>
                <ol>
                    <li>поместите курсор в то место, куда вы хотите поместить поле со списком,</li>
                    <li>перейдите на вкладку <b>Формы</b> верхней панели инструментов,</li>
                    <li>
                        щелкните на значок <div class="icon icon-combo_box_icon"></div> <b>Поле со списком</b>.
                    </li>
                </ol>
                <p><img alt="поле со списком" src="../images/combo_box_inserted.png" /></p>
                <p>В указанном месте добавится поле со списком, а на правой боковой панели откроется вкладка <b>Параметры формы</b>.</p>
                <div id="combo_box_settings">
                    <img alt="параметры форм полей со списком" src="../images/combo_box_settings.png" />
                    <ul>
                        <li><b>Кто должен это заполнять?</b>: выберите роль из выпадающего списка, чтобы задать группу пользователей с доступом к этому полю. Чтобы узнать больше о назначении ролей, прочитайте раздел этой инструкции <a href="../UsageInstructions/CreateFillableForms.htm#managing_roles">Управление ролями</a>.</li>
                        <li><b>Ключ</b>: ключ группировки полей со списком для одновременного заполнения. Чтобы создать новый ключ, введите его название в поле и нажмите клавишу <b>Enter</b>, затем при помощи раскрывающегося списка присвойте необходимый ключ каждому полю со списком. Будет отображено сообщение <em>Подключенные поля: 2/3...</em>. Чтобы отключить поля, нажмите кнопку <b>Отключить</b>.</li>
                        <li><b>Заполнитель</b>: введите текст, который будет отображаться во вставленном поле со списком. По умолчанию установлено значение <em>«Введите ваш текст»</em>.</li>
                        <li><b>Тег</b>: введите текст, который будет использоваться в качестве тега для внутреннего использования, например, будет отображаться только для участников совместного редактирования.</li>
                        <li>
                            <b>Подсказка</b>: введите текст, который будет отображаться в виде подсказки при наведении курсора на поле со списком.
                            <br /> <img alt="подсказка" src="../images/combo_box_tip.png" />
                        </li>
                        <li><b>Параметры значений</b>: добавьте <div class="icon icon-combo_add_values"></div> новые значения, удалите <div class="icon icon-combo_delete_values"></div> их или переместите их вверх <div class="icon icon-combo_values_up"></div> и <div class="icon icon-combo_values_down"></div> вниз списка.</li>
                        <li>
                            <b>Поле фиксированного размера</b>: установите флажок, чтобы создать поле фиксированного размера.<br />
                            Поле фиксированного размера выглядит как автофигура. Вы можете установить для него стиль обтекания, а также отрегулировать его положение.
                        </li>
                        <li><b>Цвет границ</b>: щелкните на значок <div class="icon icon-nofill"></div>, чтобы выбрать цвет границ вставляемого поля со списком. Выберите подходящий цвет из палитры. При необходимости вы можете создать новый <b>Пользовательский цвет</b>.</li>
                        <li><b>Цвет фона</b>: щелкните на значок <div class="icon icon-nofill"></div> чтобы выбрать цвет фона вставляемого поля со списком. Выберите подходящий цвет из палитры <b>Цветов темы</b>, <b>Стандартных цветов</b> или создайте новый <b>Пользовательский цвет</b>.</li>
                        <li><b>Обязательно</b>: установите этот флажок, чтобы сделать поле со списком обязательным для заполнения.</li>
                    </ul>
                </div>
                <p>Чтобы открыть список элементов и выбрать нужный, нажмите кнопку со стрелкой справа от добавленного <b>Поля со списком</b>. После выбора необходимого элемента вы можете полностью или частично отредактировать отображаемый текст, заменив его своим.</p>
                <p><img alt="открытое поле со списком" src="../images/combo_box_opened.png" /></p>
                <p>Вы можете изменить оформление, цвет и размер шрифта. Для этого щелкните по полю со списком и следуйте данному <a href="../UsageInstructions/FontTypeSizeColor.htm">руководству</a>. Форматирование будет применено ко всему тексту внутри поля.</p>
            </details>
        </div>


        <h2>Создание Выпадающего списка</h2>
        <p><em>Выпадающие списки</em> содержат список с набором вариантов, которые пользователи не могут редактировать.</p>
        <div class="forms">
            <details class="details-example">
                <summary>Чтобы вставить поле выпадающего списка,</summary>
                <ol>
                    <li>поместите курсор в то место, куда вы хотите поместить выпадающий список,</li>
                    <li>перейдите на вкладку <b>Формы</b> верхней панели инструментов,</li>
                    <li>
                        щелкните на значок <div class="icon icon-dropdown_list_icon"></div> <b>Выпадающий список</b>.
                    </li>
                </ol>
                <p><img alt="выпадающий список" src="../images/combo_box_inserted.png" /></p>
                <p>В указанном месте добавится выпадающий список, а на правой боковой панели откроется вкладка <b>Параметры формы</b>.</p>
                <div id="dropdown_list_settings">
                    <img alt="параметры форм выпадающего списка" src="../images/dropdown_list_settings.png" />
                    <ul>
                        <li><b>Кто должен это заполнять?</b>: выберите роль из выпадающего списка, чтобы задать группу пользователей с доступом к этому полю. Чтобы узнать больше о назначении ролей, прочитайте раздел этой инструкции <a href="../UsageInstructions/CreateFillableForms.htm#managing_roles">Управление ролями</a>.</li>
                        <li><b>Ключ</b>: ключ группировки выпадающих списков для одновременного заполнения. Чтобы создать новый ключ, введите его название в поле и нажмите клавишу <b>Enter</b>, затем при помощи раскрывающегося списка присвойте необходимый ключ каждому выпадающему списку. Будет отображено сообщение <em>Подключенные поля: 2/3...</em>. Чтобы отключить поля, нажмите кнопку <b>Отключить</b>.</li>
                        <li><b>Заполнитель</b>: введите текст, который будет отображаться во вставленном выпадающем списке. По умолчанию установлено значение <em>«Введите ваш текст»</em>.</li>
                        <li><b>Тег</b>: введите текст, который будет использоваться в качестве тега для внутреннего использования, например, будет отображаться только для участников совместного редактирования.</li>
                        <li>
                            <b>Подсказка</b>: введите текст, который будет отображаться в виде подсказки при наведении курсора на выпадающий список.
                            <br /> <img alt="подсказка" src="../images/combo_box_tip.png" />
                        </li>
                        <li><b>Параметры значений</b>: добавьте <div class="icon icon-combo_add_values"></div> новые значения, удалите <div class="icon icon-combo_delete_values"></div> их или переместите их вверх <div class="icon icon-combo_values_up"></div> и <div class="icon icon-combo_values_down"></div> вниз списка.</li>
                        <li>
                            <b>Поле фиксированного размера</b>: установите флажок, чтобы создать поле фиксированного размера.<br />
                            Поле фиксированного размера выглядит как автофигура. Вы можете установить для него стиль обтекания, а также отрегулировать его положение.
                        </li>
                        <li><b>Цвет границ</b>: щелкните на значок <div class="icon icon-nofill"></div>, чтобы выбрать цвет границ вставляемого поля выпадающего списка. Выберите подходящий цвет из палитры. При необходимости вы можете создать новый <b>Пользовательский цвет</b>.</li>
                        <li><b>Цвет фона</b>: щелкните на значок <div class="icon icon-nofill"></div> чтобы выбрать цвет фона вставляемого поля выпадающего списка. Выберите подходящий цвет из палитры <b>Цветов темы</b>, <b>Стандартных цветов</b> или создайте новый <b>Пользовательский цвет</b>.</li>
                        <li><b>Обязательно</b>: установите этот флажок, чтобы сделать поле выпадающего списка обязательным для заполнения.</li>
                    </ul>
                </div>
                <p>Чтобы открыть список элементов и выбрать нужный, нажмите кнопку со стрелкой справа от добавленного <b>Выпадающего списка</b>. После выбора необходимого элемента вы можете полностью или частично отредактировать отображаемый текст, заменив его своим.</p>
                <p><img alt="открытый выпадающий список" src="../images/dropdown_list_opened.png" /></p>
            </details>
        </div>


        <h2>Создание Флажков</h2>
        <p><em>Флажки</em> используются для предоставления пользователям множества опций, из которых можно выбрать любое количество. Поля для флажков вставляются индивидуально, следовательно, их можно устанавливать или снимать независимо друг от друга.</p>
        <div class="forms">
            <details class="details-example">
                <summary>Чтобы вставить поле флажка,</summary>
                <ol>
                    <li>поместите курсор в то место, куда вы хотите поместить флажок,</li>
                    <li>перейдите на вкладку <b>Формы</b> верхней панели инструментов,</li>
                    <li>
                        щелкните на значок <div class="icon icon-checkbox_icon"></div> <b>Флажок</b>.
                    </li>
                </ol>
                <p><span class="big big-checkbox_inserted"></span></p>
                <p>В указанном месте добавится поле флажка, а на правой боковой панели откроется вкладка <b>Параметры формы</b>.</p>
                <div id="checkbox_settings">
                    <img alt="параметры форм флажков" src="../images/checkbox_settings.png" />
                    <ul>
                        <li><b>Кто должен это заполнять?</b>: выберите роль из выпадающего списка, чтобы задать группу пользователей с доступом к этому полю. Чтобы узнать больше о назначении ролей, прочитайте раздел этой инструкции <a href="../UsageInstructions/CreateFillableForms.htm#managing_roles">Управление ролями</a>.</li>
                        <li><b>Ключ</b>: ключ группировки флажков для одновременного заполнения. Чтобы создать новый ключ, введите его название в поле и нажмите клавишу <b>Enter</b>, затем при помощи раскрывающегося списка присвойте необходимый ключ каждому флажку. Будет отображено сообщение <em>Подключенные поля: 2/3...</em>. Чтобы отключить поля, нажмите кнопку <b>Отключить</b>.</li>
                        <li><b>Тег</b>: введите текст, который будет использоваться в качестве тега для внутреннего использования, например, будет отображаться только для участников совместного редактирования.</li>
                        <li>
                            <b>Подсказка</b>: введите текст, который будет отображаться в виде подсказки при наведении курсора на флажок.
                            <br /> <img alt="подсказка" src="../images/checkbox_tip.png" />
                        </li>
                        <li>
                            <b>Поле фиксированного размера</b>: установите флажок, чтобы создать поле фиксированного размера.<br />
                            Поле фиксированного размера выглядит как автофигура. Вы можете установить для него стиль обтекания, а также отрегулировать его положение.
                        </li>
                        <li><b>Цвет границ</b>: щелкните на значок <div class="icon icon-nofill"></div>, чтобы выбрать цвет границ вставляемого поля флажка. Выберите подходящий цвет из палитры. При необходимости вы можете создать новый <b>Пользовательский цвет</b>.</li>
                        <li><b>Цвет фона</b>: щелкните на значок <div class="icon icon-nofill"></div>, чтобы выбрать цвет фона вставляемого поля флажка. Выберите подходящий цвет из палитры <b>Цветов темы</b>, <b>Стандартных цветов</b> или создайте новый <b>Пользовательский цвет</b>.</li>
                        <li><b>Обязательно</b>: установите этот флажок, чтобы сделать поле флажка обязательным для заполнения.</li>
                    </ul>
                </div>
                <p>Чтобы установить флажок, щелкните по нему один раз.</p>
                <p><span class="big big-checkbox_checked"></span></p>
            </details>
        </div>


        <h2>Создание Переключателя</h2>
        <p><em>Переключатели</em> используются, чтобы предоставить пользователям множество вариантов, из которых можно выбрать только один. Переключатели можно сгруппировать, чтобы не было выбора нескольких кнопок в одной группе.</p>
        <div class="forms">
            <details class="details-example">
                <summary>Чтобы вставить поле переключателя,</summary>
                <ol>
                    <li>поместите курсор в то место, куда вы хотите поместить поле переключателя,</li>
                    <li>перейдите на вкладку <b>Формы</b> верхней панели инструментов,</li>
                    <li>
                        щелкните на значок <div class="icon icon-radio_button_icon"></div> <b>Переключатель</b>.
                    </li>
                </ol>
                <p><span class="big big-radio_button_inserted"></span></p>
                <p>В указанном месте добавится поле переключателя, а на правой боковой панели откроется вкладка <b>Параметры формы</b>.</p>
                <div id="radio_button_settings">
                    <img alt="параметры форм переключателя" src="../images/radio_button_settings.png" />
                    <ul>
                        <li><b>Кто должен это заполнять?</b>: выберите роль из выпадающего списка, чтобы задать группу пользователей с доступом к этому полю. Чтобы узнать больше о назначении ролей, прочитайте раздел этой инструкции <a href="../UsageInstructions/CreateFillableForms.htm#managing_roles">Управление ролями</a>.</li>
                        <li><b>Ключ группы</b>: создайте новую группу переключателей, введите имя группы в поле и нажмите клавишу <b>Enter</b>, затем присвойте нужную группу каждому переключателю.</li>
                        <li><b>Тег</b>: введите текст, который будет использоваться в качестве тега для внутреннего использования, например, будет отображаться только для участников совместного редактирования.</li>
                        <li>
                            <b>Подсказка</b>: введите текст, который будет отображаться в виде подсказки при наведении курсора на переключатель.
                            <br /> <img alt="подсказка" src="../images/radio_button_tip.png" />
                        </li>
                        <li>
                            <b>Поле фиксированного размера</b>: установите флажок, чтобы создать поле фиксированного размера.<br />
                            Поле фиксированного размера выглядит как автофигура. Вы можете установить для него стиль обтекания, а также отрегулировать его положение.
                        </li>
                        <li><b>Цвет границ</b>: щелкните на значок <div class="icon icon-nofill"></div>, чтобы выбрать цвет границ вставляемого поля переключателя. Выберите подходящий цвет из палитры. При необходимости вы можете создать новый <b>Пользовательский цвет</b>.</li>
                        <li><b>Цвет фона</b>: щелкните на значок <div class="icon icon-nofill"></div> чтобы выбрать цвет фона вставляемого поля переключателя. Выберите подходящий цвет из палитры <b>Цветов темы</b>, <b>Стандартных цветов</b> или создайте новый <b>Пользовательский цвет</b>.</li>
                        <li><b>Обязательно</b>: установите этот флажок, чтобы сделать поле переключателя обязательным для заполнения.</li>
                    </ul>
                </div>
                <p>Чтобы включить переключатель, щелкните его один раз.</p>
                <p><span class="big big-radio_button_checked"></span></p>
            </details>
        </div>


        <h2>Создание поля Изображение</h2>
        <p><em>Изображения</em> - это поля формы, которые используются для вставки изображения с установленными вами ограничениями, т.е. местоположением изображения или его размером.</p>
        <div class="forms">
            <details class="details-example">
                <summary>Чтобы вставить поле изображения,</summary>
                <ol>
                    <li>поместите курсор в то место, куда вы хотите поместить поле изображения,</li>
                    <li>перейдите на вкладку <b>Формы</b> верхней панели инструментов,</li>
                    <li>
                        щелкните на значок <div class="icon icon-image_form_icon"></div> <b>Изображение</b>.
                    </li>
                </ol>
                <p><span class="big big-image_form_inserted"></span></p>
                <p>В указанном месте добавится поле изображения, а на правой боковой панели откроется вкладка <b>Параметры формы</b>.</p>
                <div id="image_form_settings">
                    <img alt="параметры форм изображения" src="../images/image_form_settings.png" />
                    <ul>
                        <li><b>Кто должен это заполнять?</b>: выберите роль из выпадающего списка, чтобы задать группу пользователей с доступом к этому полю. Чтобы узнать больше о назначении ролей, прочитайте раздел этой инструкции <a href="../UsageInstructions/CreateFillableForms.htm#managing_roles">Управление ролями</a>.</li>
                        <li><b>Ключ</b>: ключ группировки изображений для одновременного заполнения. Чтобы создать новый ключ, введите его название в поле и нажмите клавишу <b>Enter</b>, затем при помощи раскрывающегося списка присвойте необходимый ключ каждому изображению. Будет отображено сообщение <em>Подключенные поля: 2/3...</em>. Чтобы отключить поля, нажмите кнопку <b>Отключить</b>.</li>
                        <li><b>Заполнитель</b>: введите текст, который будет отображаться во вставленном изображении. По умолчанию установлено значение <em>«Нажмите, чтобы загрузить изображение»</em>.</li>
                        <li><b>Тег</b>: введите текст, который будет использоваться в качестве тега для внутреннего использования, например, будет отображаться только для участников совместного редактирования.</li>
                        <li>
                            <b>Подсказка</b>: введите текст, который будет отображаться в виде подсказки при наведении курсора на нижнюю границу изображения.
                        </li>
                        <li><b>Когда масштабировать</b>: откройте раскрывающееся меню и выберите соответствующий вариант изменения размера изображения: <b>Всегда</b>, <b>Никогда</b>, когда <b>Изображение слишком большое</b> или когда <b>Изображение слишком маленькое</b>. Выбранное изображение будет масштабироваться внутри поля соответствующим образом.</li>
                        <li><b>Сохранять пропорции</b>: установите этот флажок, чтобы сохранить соотношение сторон изображения без искажения. Когда флажок установлен, используйте вертикальный и горизонтальный ползунки, чтобы расположить изображение внутри вставленного поля. Когда флажок снят, ползунки позиционирования неактивны.</li>
                        <li><b>Выбрать изображение</b>: нажмите эту кнопку, чтобы загрузить изображение <b>Из файла</b>, <b>По URL</b> или <b>Из хранилища</b>.</li>
                        <li><b>Цвет границ</b>: щелкните на значок <div class="icon icon-nofill"></div>, чтобы выбрать цвет границ вставляемого поля изображения. Выберите подходящий цвет из палитры. При необходимости вы можете создать новый <b>Пользовательский цвет</b>.</li>
                        <li><b>Цвет фона</b>: щелкните на значок <div class="icon icon-nofill"></div> чтобы выбрать цвет фона вставляемого поля изображения. Выберите подходящий цвет из палитры <b>Цветов темы</b>, <b>Стандартных цветов</b> или создайте новый <b>Пользовательский цвет</b>.</li>
                        <li><b>Обязательно</b>: установите этот флажок, чтобы сделать поле изображения обязательным для заполнения.</li>

                    </ul>
                </div>
                <p>Чтобы заменить изображение, щелкните значок изображения <span class="icon icon-image"></span> над границей поля формы и выберите другое.</p>
                <p>Чтобы <b>настроить</b> параметры изображения, откройте вкладку <b>Параметры изображения</b> на правой боковой панели. Чтобы узнать больше, пожалуйста, прочтите руководство по <a href="../UsageInstructions/InsertImages.htm">изменению параметров изображения</a>.</p>
            </details>
        </div>

        <h2 id="emailaddress">Создание нового поля Адрес email</h2>
        <p>Поле <em>Адрес email</em> используется для ввода адресов электронной почты, соответствующих регулярному выражению \S+@\S+\.\S+.</p>
        <div class="forms">
            <details class="details-example">
                <summary>Для вставки поля адреса email,</summary>
                <ol>
                    <li>поместите курсор в то место, куда вы хотите поместить поле,</li>
                    <li>перейдите на вкладку <b>Формы</b> верхней панели инструментов,</li>
                    <li>
                        щелкните на значок <div class="icon icon-email_address_icon"></div> <b>Адрес email</b>.
                    </li>
                </ol>
                <p><span class="big big-email_address_inserted"></span></p>
                <p>В указанном месте добавится поле адреса email, а на правой боковой панели откроется вкладка <b>Параметры формы</b>.</p>
                <div id="email_address_settings">
                    <img alt="Настройки поля Адрес email" src="../images/email_address_settings.png" />
                    <ul>
                        <li><b>Кто должен это заполнять?</b>: выберите роль из выпадающего списка, чтобы задать группу пользователей с доступом к этому полю. Чтобы узнать больше о назначении ролей, прочитайте раздел этой инструкции <a href="../UsageInstructions/CreateFillableForms.htm#managing_roles">Управление ролями</a>.</li>
                        <li><b>Ключ</b>: чтобы создать новую группу адресов электронной почты, введите название группы в поле и нажмите клавишу <b>Enter</b>, затем присвойте необходимую группу каждому полю адреса email.</li>
                        <li><b>Заполнитель</b>: введите текст, который будет отображаться во вставленном поле. По умолчанию установлено значение <em>“<EMAIL>”</em>.</li>
                        <li><b>Тег</b>: введите текст, который будет использоваться в качестве тега для внутреннего использования, например, будет отображаться только для участников совместного редактирования.</li>
                        <li>
                            <b>Подсказка</b>: введите текст, который будет отображаться в виде подсказки при наведении курсора на поле адреса email.
                            <br /> <img alt="Подсказка" src="../images/email_address_tip.png" />
                        </li>
                        <li><b>Формат</b>: выберите формат содержимого в поле, например, <em>Нет</em>, <em>Цифры</em>, <em>Буквы</em>, <em>Произвольная маска</em> или <em>Регулярное выражение</em>. По умолчанию для этого поля выбрано значение <em>Регулярное выражение</em>, чтобы сохранить формат адреса электронной почты <code>\S+@\S+\.\S+</code>.</li>
                        <li><b>Допустимые символы</b>: введите символы, которые разрешается вводить в поле адреса email.</li>
                        <li>
                            <b>Поле фиксированного размера</b>: установите флажок, чтобы создать поле фиксированного размера. Когда данная опция включена, также доступными становятся параметры <b>Автоподбор</b> и / или <b>Многострочное поле</b>.<br />
                            Поле фиксированного размера выглядит как автофигура. Вы можете установить для него стиль обтекания, а также отрегулировать его положение.
                        </li>
                        <li><b>Автоподбор</b>: данный параметр можно включить только если выбран параметр <b>Поле фиксированного размера</b>. Установите флажок, чтобы размер шрифта автоматически соответствовал размеру поля.</li>
                        <li><b>Многострочное поле</b>: данный параметр можно включить только если выбран параметр <b>Поле фиксированного размера</b>. Установите флажок, чтобы создать поле формы с несколькими строками, в противном случае текст будет занимать только одну строку.</li>
                        <li><b>Максимальное число знаков</b>: по умолчанию без ограничений; поставьте флажок и укажите максимальное количество символов в поле справа.</li>
                        <li>
                            <b>Комбинировать символы</b>: равномерно распределить текст внутри вставленного поля адреса email и настроить его общий вид. Не устанавливайте флажок, чтобы сохранить настройки по умолчанию, или установите его, чтобы указать следующие параметры:
                            <ul>
                                <li><b>Ширина ячейки</b>: выберите необходимый параметр - <em>Авто</em> (ширина вычисляется автоматически), <em>Минимум</em> (ширина не меньше значения, заданного вручную) или <em>точно</em> (ширина соответствует значению, заданному вручную). Текст внутри будет выровнен в соответствии с указанным значением.</li>
                            </ul>
                        </li>
                        <li><b>Цвет границ</b>: щелкните на значок <div class="icon icon-nofill"></div>, чтобы выбрать цвет границ вставляемого поля адреса email. Выберите подходящий цвет из палитры. При необходимости вы можете создать новый <b>Пользовательский цвет</b>.</li>
                        <li><b>Цвет фона</b>: щелкните на значок <div class="icon icon-nofill"></div> чтобы выбрать цвет фона вставляемого поля адреса email. Выберите подходящий цвет из палитры <b>Цветов темы</b>, <b>Стандартных цветов</b> или создайте новый <b>Пользовательский цвет</b>.</li>
                        <li><b>Обязательно</b>: установите этот флажок, чтобы сделать поле адреса email обязательным для заполнения.</li>
                    </ul>
                </div>
            </details>
        </div>

        <h2 id="phonenumber">Создание нового поля Номер телефона</h2>
        <p>Поле <em>Номер телефона</em> используется для ввода номера телефона, соответствующего произвольной маске, заданной создателем формы. По умолчанию установлено значение <code>(999)999-9999</code>.</p>
        <div class="forms">
            <details class="details-example">
                <summary>Для вставки поля номера телефона,</summary>
                <ol>
                    <li>поместите курсор в то место, куда вы хотите поместить поле,</li>
                    <li>перейдите на вкладку <b>Формы</b> верхней панели инструментов,</li>
                    <li>
                        щелкните на значок <div class="icon icon-phone_number_icon"></div> <b>Номер телефона</b>.
                    </li>
                </ol>
                <p><span class="big big-phone_number_inserted"></span></p>
                <p>В указанном месте добавится поле номера телефона, а на правой боковой панели откроется вкладка <b>Параметры формы</b>.</p>
                <div id="phone_number_settings">
                    <img alt="Настройки поля Номер телефона" src="../images/phone_number_settings.png" />
                    <ul>
                        <li><b>Кто должен это заполнять?</b>: выберите роль из выпадающего списка, чтобы задать группу пользователей с доступом к этому полю. Чтобы узнать больше о назначении ролей, прочитайте раздел этой инструкции <a href="../UsageInstructions/CreateFillableForms.htm#managing_roles">Управление ролями</a>.</li>
                        <li><b>Ключ</b>: чтобы создать новую группу номеров телефона, введите название группы в поле и нажмите клавишу <b>Enter</b>, затем присвойте необходимую группу каждому полю номера телефона.</li>
                        <li><b>Заполнитель</b>: введите текст, который будет отображаться во вставленном поле. По умолчанию установлено значение <em>“(999)999-9999”</em>.</li>
                        <li><b>Тег</b>: введите текст, который будет использоваться в качестве тега для внутреннего использования, например, будет отображаться только для участников совместного редактирования.</li>
                        <li>
                            <b>Подсказка</b>: введите текст, который будет отображаться в виде подсказки при наведении курсора на поле номера телефона.
                            <br /> <img alt="Подсказка" src="../images/phone_number_tip.png" />
                        </li>
                        <li><b>Формат</b>: выберите формат содержимого в поле, например, <em>Нет</em>, <em>Цифры</em>, <em>Буквы</em>, <em>Произвольная маска</em> или <em>Регулярное выражение</em>. По умолчанию для этого поля выбрано значение <em>Произвольная маска</em>. Чтобы изменить ее формат, введите нужную маску в поле ниже.</li>
                        <li><b>Допустимые символы</b>: введите символы, которые разрешается вводить в поле номера телефона.</li>
                        <li>
                            <b>Поле фиксированного размера</b>: установите флажок, чтобы создать поле фиксированного размера. Когда данная опция включена, также доступными становятся параметры <b>Автоподбор</b> и / или <b>Многострочное поле</b>.<br />
                            Поле фиксированного размера выглядит как автофигура. Вы можете установить для него стиль обтекания, а также отрегулировать его положение.
                        </li>
                        <li><b>Автоподбор</b>: данный параметр можно включить только если выбран параметр <b>Поле фиксированного размера</b>. Установите флажок, чтобы размер шрифта автоматически соответствовал размеру поля.</li>
                        <li><b>Многострочное поле</b>: данный параметр можно включить только если выбран параметр <b>Поле фиксированного размера</b>. Установите флажок, чтобы создать поле формы с несколькими строками, в противном случае текст будет занимать только одну строку.</li>
                        <li><b>Максимальное число знаков</b>: по умолчанию без ограничений; поставьте флажок и укажите максимальное количество символов в поле справа.</li>
                        <li>
                            <b>Комбинировать символы</b>: равномерно распределить текст внутри вставленного поля номера телефона и настроить его общий вид. Не устанавливайте флажок, чтобы сохранить настройки по умолчанию, или установите его, чтобы указать следующие параметры:
                            <ul>
                                <li><b>Ширина ячейки</b>: выберите необходимый параметр - <em>Авто</em> (ширина вычисляется автоматически), <em>Минимум</em> (ширина не меньше значения, заданного вручную) или <em>точно</em> (ширина соответствует значению, заданному вручную). Текст внутри будет выровнен в соответствии с указанным значением.</li>
                            </ul>
                        </li>
                        <li><b>Цвет границ</b>: щелкните на значок <div class="icon icon-nofill"></div>, чтобы выбрать цвет границ вставляемого поля номера телефона. Выберите подходящий цвет из палитры. При необходимости вы можете создать новый <b>Пользовательский цвет</b>.</li>
                        <li><b>Цвет фона</b>: щелкните на значок <div class="icon icon-nofill"></div> чтобы выбрать цвет фона вставляемого поля номера телефона. Выберите подходящий цвет из палитры <b>Цветов темы</b>, <b>Стандартных цветов</b> или создайте новый <b>Пользовательский цвет</b>.</li>
                        <li><b>Обязательно</b>: установите этот флажок, чтобы сделать поле номера телефона обязательным для заполнения.</li>
                    </ul>
                </div>
            </details>
        </div>

        <h2 id="datetime">Создание нового поля Дата и время</h2>
        <p>Поле <em>Дата и время</em> используется для вставки даты. По умолчанию установлено значение DD-MM-YYYY.</p>
        <div class="forms">
            <details class="details-example">
                <summary>Для вставки поля даты и времени,</summary>
                <ol>
                    <li>поместите курсор в то место, куда вы хотите поместить поле,</li>
                    <li>перейдите на вкладку <b>Формы</b> верхней панели инструментов,</li>
                    <li>
                        щелкните на значок <div class="icon icon-date_time_icon"></div> <b>Дата и время</b>.
                    </li>
                </ol>
                <p><span class="big big-date_time_inserted"></span></p>
                <p>В указанном месте добавится поле даты и времени. Чтобы ввести дату, нажмите на стрелку внутри поля и выберите нужную дату в календаре.</p>
            </details>
        </div>

        <h2 id="zipcode">Создание нового поля Индекс</h2>
        <p>Поле <em>Индекс</em> используется для ввода индекса, соответствующего произвольной маске, заданной создателем формы. По умолчанию установлено значение <code>99999-9999</code>.</p>
        <div class="forms">
            <details class="details-example">
                <summary>Для вставки поля индекса,</summary>
                <ol>
                    <li>поместите курсор в то место, куда вы хотите поместить поле,</li>
                    <li>перейдите на вкладку <b>Формы</b> верхней панели инструментов,</li>
                    <li>
                        щелкните на значок <div class="icon icon-zip_code_icon"></div> <b>Индекс</b>.
                    </li>
                </ol>
                <p><span class="big big-zip_code_inserted"></span></p>
                <p>В указанном месте добавится поле индекса, а на правой боковой панели откроется вкладка <b>Параметры формы</b>.</p>
                <div id="zip_code_settings">
                    <img alt="Настройки поля индекса" src="../images/zip_code_settings.png" />
                    <ul>
                        <li><b>Кто должен это заполнять?</b>: выберите роль из выпадающего списка, чтобы задать группу пользователей с доступом к этому полю. Чтобы узнать больше о назначении ролей, прочитайте раздел этой инструкции <a href="../UsageInstructions/CreateFillableForms.htm#managing_roles">Управление ролями</a>.</li>
                        <li><b>Ключ</b>: чтобы создать новую группу индексов, введите название группы в поле и нажмите клавишу <b>Enter</b>, затем присвойте необходимую группу каждому полю индекса.</li>
                        <li><b>Заполнитель</b>: введите текст, который будет отображаться во вставленном поле индекса. По умолчанию установлено значение <em>“99999-9999”</em>.</li>
                        <li><b>Тег</b>: введите текст, который будет использоваться в качестве тега для внутреннего использования, например, будет отображаться только для участников совместного редактирования.</li>
                        <li>
                            <b>Подсказка</b>: введите текст, который будет отображаться в виде подсказки при наведении курсора на поле индекса.
                            <br /> <img alt="Подсказка" src="../images/zip_code_tip.png" />
                        </li>
                        <li><b>Формат</b>: выберите формат содержимого в поле, например, <em>Нет</em>, <em>Цифры</em>, <em>Буквы</em>, <em>Произвольная маска</em> или <em>Регулярное выражение</em>. По умолчанию для этого поля выбрано значение <em>Произвольная маска</em>. Чтобы изменить ее формат, введите нужную маску в поле ниже.</li>
                        <li><b>Допустимые символы</b>: введите символы, которые разрешается вводить в поле индекса.</li>
                        <li>
                            <b>Поле фиксированного размера</b>: установите флажок, чтобы создать поле фиксированного размера. Когда данная опция включена, также доступными становятся параметры <b>Автоподбор</b> и / или <b>Многострочное поле</b>.<br />
                            Поле фиксированного размера выглядит как автофигура. Вы можете установить для него стиль обтекания, а также отрегулировать его положение.
                        </li>
                        <li><b>Автоподбор</b>: данный параметр можно включить только если выбран параметр <b>Поле фиксированного размера</b>. Установите флажок, чтобы размер шрифта автоматически соответствовал размеру поля.</li>
                        <li><b>Многострочное поле</b>: данный параметр можно включить только если выбран параметр <b>Поле фиксированного размера</b>. Установите флажок, чтобы создать поле формы с несколькими строками, в противном случае текст будет занимать только одну строку.</li>
                        <li><b>Максимальное число знаков</b>: по умолчанию без ограничений; поставьте флажок и укажите максимальное количество символов в поле справа.</li>
                        <li>
                            <b>Комбинировать символы</b>: равномерно распределить текст внутри вставленного поля индекса и настроить его общий вид. Не устанавливайте флажок, чтобы сохранить настройки по умолчанию, или установите его, чтобы указать следующие параметры:
                            <ul>
                                <li><b>Ширина ячейки</b>: выберите необходимый параметр - <em>Авто</em> (ширина вычисляется автоматически), <em>Минимум</em> (ширина не меньше значения, заданного вручную) или <em>точно</em> (ширина соответствует значению, заданному вручную). Текст внутри будет выровнен в соответствии с указанным значением.</li>
                            </ul>
                        </li>
                        <li><b>Цвет границ</b>: щелкните на значок <div class="icon icon-nofill"></div>, чтобы выбрать цвет границ вставляемого поля индекса. Выберите подходящий цвет из палитры. При необходимости вы можете создать новый <b>Пользовательский цвет</b>.</li>
                        <li><b>Цвет фона</b>: щелкните на значок <div class="icon icon-nofill"></div> чтобы выбрать цвет фона вставляемого поля индекса. Выберите подходящий цвет из палитры <b>Цветов темы</b>, <b>Стандартных цветов</b> или создайте новый <b>Пользовательский цвет</b>.</li>
                        <li><b>Обязательно</b>: установите этот флажок, чтобы сделать поле индекса обязательным для заполнения.</li>
                    </ul>
                </div>
            </details>
        </div>

        <h2 id="creditcard">Создание нового поля Кредитная карта</h2>
        <p>Поле <em>Кредитная карта</em> используется для ввода номера кредитной карты, соответствующего произвольной маске, заданной создателем формы. По умолчанию установлено значение <code>9999-9999-9999-9999</code>.</p>
        <div class="forms">
            <details class="details-example">
                <summary>Для вставки поля кредитной карты,</summary>
                <ol>
                    <li>поместите курсор в то место, куда вы хотите поместить поле,</li>
                    <li>перейдите на вкладку <b>Формы</b> верхней панели инструментов,</li>
                    <li>
                        щелкните на значок <div class="icon icon-credit_card_icon"></div> <b>Кредитная карта</b>.
                    </li>
                </ol>
                <p><span class="big big-credit_card_inserted"></span></p>
                <p>В указанном месте добавится поле кредитной карты, а на правой боковой панели откроется вкладка <b>Параметры формы</b>.</p>
                <div id="credit_card_settings">
                    <img alt="Настройки поля кредитной карты" src="../images/credit_card_settings.png" />
                    <ul>
                        <li><b>Кто должен это заполнять?</b>: выберите роль из выпадающего списка, чтобы задать группу пользователей с доступом к этому полю. Чтобы узнать больше о назначении ролей, прочитайте раздел этой инструкции <a href="../UsageInstructions/CreateFillableForms.htm#managing_roles">Управление ролями</a>.</li>
                        <li><b>Ключ</b>: чтобы создать новую группу номеров кредитных карт, введите название группы в поле и нажмите клавишу <b>Enter</b>, затем присвойте необходимую группу каждому полю кредитной карты.</li>
                        <li><b>Заполнитель</b>: введите текст, который будет отображаться во вставленном поле кредитной карты. По умолчанию установлено значение <em>“9999-9999-9999-9999”</em>.</li>
                        <li><b>Тег</b>: введите текст, который будет использоваться в качестве тега для внутреннего использования, например, будет отображаться только для участников совместного редактирования.</li>
                        <li>
                            <b>Подсказка</b>: введите текст, который будет отображаться в виде подсказки при наведении курсора на поле кредитной карты.
                            <br /> <img alt="Подсказка" src="../images/credit_card_tip.png" />
                        </li>
                        <li><b>Формат</b>: выберите формат содержимого в поле, например, <em>Нет</em>, <em>Цифры</em>, <em>Буквы</em>, <em>Произвольная маска</em> или <em>Регулярное выражение</em>. По умолчанию для этого поля выбрано значение <em>Произвольная маска</em>. Чтобы изменить ее формат, введите нужную маску в поле ниже.</li>
                        <li><b>Допустимые символы</b>: введите символы, которые разрешается вводить в поле кредитной карты.</li>
                        <li>
                            <b>Поле фиксированного размера</b>: установите флажок, чтобы создать поле фиксированного размера. Когда данная опция включена, также доступными становятся параметры <b>Автоподбор</b> и / или <b>Многострочное поле</b>.<br />
                            Поле фиксированного размера выглядит как автофигура. Вы можете установить для него стиль обтекания, а также отрегулировать его положение.
                        </li>
                        <li><b>Автоподбор</b>: данный параметр можно включить только если выбран параметр <b>Поле фиксированного размера</b>. Установите флажок, чтобы размер шрифта автоматически соответствовал размеру поля.</li>
                        <li><b>Многострочное поле</b>: данный параметр можно включить только если выбран параметр <b>Поле фиксированного размера</b>. Установите флажок, чтобы создать поле формы с несколькими строками, в противном случае текст будет занимать только одну строку.</li>
                        <li><b>Максимальное число знаков</b>: по умолчанию без ограничений; поставьте флажок и укажите максимальное количество символов в поле справа.</li>
                        <li>
                            <b>Комбинировать символы</b>: равномерно распределить текст внутри вставленного поля кредитной карты и настроить его общий вид. Не устанавливайте флажок, чтобы сохранить настройки по умолчанию, или установите его, чтобы указать следующие параметры:
                            <ul>
                                <li><b>Ширина ячейки</b>: выберите необходимый параметр - <em>Авто</em> (ширина вычисляется автоматически), <em>Минимум</em> (ширина не меньше значения, заданного вручную) или <em>точно</em> (ширина соответствует значению, заданному вручную). Текст внутри будет выровнен в соответствии с указанным значением.</li>
                            </ul>
                        </li>
                        <li><b>Цвет границ</b>: щелкните на значок <div class="icon icon-nofill"></div>, чтобы выбрать цвет границ вставляемого поля кредитной карты. Выберите подходящий цвет из палитры. При необходимости вы можете создать новый <b>Пользовательский цвет</b>.</li>
                        <li><b>Цвет фона</b>: щелкните на значок <div class="icon icon-nofill"></div> чтобы выбрать цвет фона вставляемого поля кредитной карты. Выберите подходящий цвет из палитры <b>Цветов темы</b>, <b>Стандартных цветов</b> или создайте новый <b>Пользовательский цвет</b>.</li>
                        <li><b>Обязательно</b>: установите этот флажок, чтобы сделать поле кредитной карты обязательным для заполнения.</li>
                    </ul>
                </div>
            </details>
        </div>

        <h2 id="complexfield">Создание нового Составного поля</h2>
        <p><em>Составное поле</em> комбинирует несколько типов полей, например, текстовое поле и выпадающий список. Вы можете комбинировать поля, как вам необходимо.</p>
        <div class="forms">
            <details class="details-example">
                <summary>Для вставки составного поля,</summary>
                <ol>
                    <li>поместите курсор в то место, куда вы хотите поместить поле,</li>
                    <li>перейдите на вкладку <b>Формы</b> верхней панели инструментов,</li>
                    <li>
                        щелкните на значок <div class="icon icon-complex_field_icon"></div> <b>Составное поле</b>.
                    </li>
                </ol>
                <p><img alt="Составное поле" src="../images/complex_field_inserted.png"></p>
                <p>В указанном месте добавится составное поле, а на правой боковой панели откроется вкладка <b>Параметры формы</b>.</p>
                <div id="complex_field_settings">
                    <img alt="Настройки составного поля" src="../images/complex_field_settings.png" />
                    <ul>
                        <li><b>Кто должен это заполнять?</b>: выберите роль из выпадающего списка, чтобы задать группу пользователей с доступом к этому полю. Чтобы узнать больше о назначении ролей, прочитайте раздел этой инструкции <a href="../UsageInstructions/CreateFillableForms.htm#managing_roles">Управление ролями</a>.</li>
                        <li><b>Ключ</b>: чтобы создать новую группу составных полей, введите название группы в поле и нажмите клавишу <b>Enter</b>, затем присвойте необходимую группу каждому составному полю.</li>
                        <li><b>Заполнитель</b>: введите текст, который будет отображаться во вставленном составном поле. По умолчанию установлено значение <em>«Введите ваш текст»</em>.</li>
                        <li><b>Тег</b>: введите текст, который будет использоваться в качестве тега для внутреннего использования, например, будет отображаться только для участников совместного редактирования.</li>
                        <li>
                            <b>Подсказка</b>: введите текст, который будет отображаться в виде подсказки при наведении курсора на составное поле.
                            <br /> <img alt="Подсказка" src="../images/complex_field_tip.png" />
                        </li>
                        <li>
                            <b>Поле фиксированного размера</b>: установите флажок, чтобы создать поле фиксированного размера.<br />
                            Поле фиксированного размера выглядит как автофигура. Вы можете установить для него стиль обтекания, а также отрегулировать его положение.
                        </li>
                        <li><b>Цвет границ</b>: щелкните на значок <div class="icon icon-nofill"></div>, чтобы выбрать цвет границ вставляемого составного поля. Выберите подходящий цвет из палитры. При необходимости вы можете создать новый <b>Пользовательский цвет</b>.</li>
                        <li><b>Цвет фона</b>: щелкните на значок <div class="icon icon-nofill"></div> чтобы выбрать цвет фона вставляемого составного поля. Выберите подходящий цвет из палитры <b>Цветов темы</b>, <b>Стандартных цветов</b> или создайте новый <b>Пользовательский цвет</b>.</li>
                        <li><b>Обязательно</b>: установите этот флажок, чтобы сделать составное поле обязательным для заполнения.</li>
                    </ul>
                    <p>Чтобы вставить в составное поле различные поля, щелкните внутри него и выберите нужное поле на верхней панели инструментов вкладки <b>Формы</b>, затем настройте поле, как вам необходимо. Для получения дополнительной информации о каждом типе полей прочитайте соответствующие разделы выше.</p>
                    <p class="note">Пожалуйста, обратите внимание, что внутри составных полей нельзя использовать поле <em>Изображение</em>.</p>
                </div>
            </details>
        </div>

        <h2 id="managing_roles">Управление ролями</h2>
        <p>Вы можете создать новые роли, которые будут определять, кто может заполнять определенные поля формы.</p>
        <div class="forms">
            <details class="details-example">
                <summary>Для управления ролями,</summary>
                <div id="managing_roles">
                    <img alt="Управление ролями" src="../images/managing_roles.png" />
                    <ul>
                        <li>перейдите на вкладку <b>Формы</b> верхней панели инструментов,</li>
                        <li>щелкните на значок <div class="icon icon-sharingicon"></div> <b>Управление ролями</b>,</li>
                        <li>
                            нажмите кнопку <b>Новая</b>, чтобы создать новую роль,
                            <p><img alt="Новая роль" src="../images/edit_role.png" /></p>
                        </li>
                        <li>введите имя роли и выберите ее цвет, если необходимо. Также можно создать пользовательский цвет, нажав на соответствующий пункт меню,</li>
                        <li>нажмите кнопку <b>OK</b>, чтобы создать новую роль,</li>
                        <li>задайте порядок, в котором заполняющие получают и подписывают документ с помощью кнопок <div class="icon icon-role_up"></div> и <div class="icon icon-role_down"></div>,</li>
                        <li>используйте кнопки <b>Редактировать</b> или <b>Удалить</b>, чтобы изменить роли или удалить их,</li>
                        <li>нажмите кнопку <b>Закрыть</b>, чтобы вернуться к редактированию формы.</li>
                    </ul>
                    <p>При сохранении формы в файл .oform вы можете просмотреть все роли, созданные для формы.</p>
                </div>

            </details>
        </div>

        <!--<h2>Цвет подсветки</h2>
    <p>Вы можете выделить вставленные поля формы определенным цветом.</p>
    <div class="forms">
        <details class="details-example">
            <summary>Чтобы изменить цвет подсветки,</summary>
            <div id="highlight_settings">
                <img alt="параметры подсветки" src="../images/highlight_settings.png" />
                <ul>
                    <li>на вкладке <b>Формы</b> верхней панели инструментов щелкните на <b>Цвет подсветки</b>,</li>
                    <li>выберите цвет из палитры <b>Стандартных цветов</b>. Вы также можете создать новый <b>Пользовательский цвет</b>,</li>
                    <li>чтобы удалить ранее измененный цвет подсветки, используйте опцию <b>Без подсветки</b>.</li>
                </ul>
            </div>
            <p>Выбранные параметры цвета подсветки будут применены ко всем полям формы в документе.</p>
            <p class="note">
                <b>Примечание</b>: Граница поля формы видна только тогда, когда поле выбрано. Границы не отображаются на печатной версии.
            </p>
        </details>
    </div>-->

        <h2>Просмотр форм</h2>
        <p class="note">
            <b>Примечание</b>: После входа в режим <b>Просмотра формы</b> все возможности редактирования станут недоступны.
        </p>
        <p>Нажмите кнопку <span class="icon icon-view_form_icon"></span> <b>Просмотреть форму</b> на вкладке <b>Формы</b> верхней панели инструментов,  чтобы увидеть, как все вставленные формы будут отображаться в вашем документе.</p>
        <p><img alt="просмотр форм включен" src="../images/view_form_active2.png" /></p>
        <p>Вы можете просматривать форму от каждой созданной роли. Для этого нажмите стрелку под кнопкой <span class="icon icon-view_form_icon"></span> <b>Просмотреть форму</b> и выбрать нужную роль.</p>
        <p><img alt="просмотр форм от определенной роли" src="../images/view_form_role.png" /></p>
        <p>Чтобы <b>выйти</b> из режима просмотра, снова щелкните тот же значок.</p>

        <h2>Перемещение полей форм</h2>
        <p>Поля формы можно переместить в другое место в документе: нажмите кнопку слева от границы элемента управления, чтобы выбрать поле, и перетащите его в другое место в тексте не отпуская кнопку мыши.</p>
        <p><img alt="перемещение полей форм" src="../images/moving_form_fields.png" /></p>
        <p>Вы также можете <b>копировать и вставлять</b> поля формы: выберите нужное поле и используйте комбинации клавиш <b>Ctrl + C / Ctrl + V</b>.</p>

        <h2>Создание обязательных полей</h2>
        <p>Чтобы <b>сделать поле обязательным для заполнения</b>, установите флажок напротив опции <b>Обязательно</b>. Границы обязательного поля будут окрашены в красный.</p>

        <h2>Блокировка полей форм</h2>
        <p>Чтобы <b>предотвратить дальнейшее редактирование</b> вставленного поля формы, щелкните значок <span class="icon icon-lock_form_icon"></span> <b>Заблокировать</b>. Заполнение полей остается доступным.</p>

        <h2>Очистка полей форм</h2>
        <p>Чтобы очистить все вставленные поля и удалить все значения, на верхней панели инструментов щелкните кнопку <span class="icon icon-clear_fields_icon"></span> <b>Очистить все поля</b>. Очистку полей можно производить только в режиме заполнения формы.</p>

        <h2>Заполнение и отправка форм</h2>
        <p><img alt="панель заполнения форм" src="../images/fill_form.png" /></p>
        <p>Перейдите на вкладку <b>Формы</b> верхней панели инструментов.</p>
        <p>Перемещайтесь по полям формы с помощью кнопок <span class="icon icon-previous_field_icon"></span> <b>Предыдущее поле</b> и <span class="icon icon-next_field_icon"></span> <b>Следующее поле</b> на верхней панели инструментов.</p>
        <p>Нажмите кнопку <span class="icon icon-save_form_icon"></span> <b>Сохранить как заполняемый документ OFORM</b> на верхней панели инструментов, чтобы сохранить форму с расширением <b>OFORM</b> для дальнейшего заполнения. Вы можете сохранить любое количество файлов <b>OFORM</b>.</p>

        <h2>Удаление полей форм</h2>
        <p>Чтобы удалить поле формы и оставить все его содержимое, выберите его и щелкните значок <span class="icon icon-combo_delete_values"></span> <b>Удалить</b> (убедитесь, что поле не заблокировано) или нажмите клавишу <b>Delete</b> на клавиатуре.</p>
    </div>
</body>
</html>