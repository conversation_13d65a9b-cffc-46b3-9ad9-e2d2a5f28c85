<!DOCTYPE html>
<html>
<head>
    <title>Вставка даты и времени</title>
    <meta charset="utf-8" />
    <meta name="description" content="Вставьте в документ дату и время" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Вставка даты и времени</h1>
        <p>Чтобы вставить в документ <b>Дату и время</b>,</p>
        <ol>
            <li>установите курсор там, где требуется вставить <b>Дату и время</b>,</li>
            <li>перейдите на вкладку <b>Вставка</b> верхней панели инструментов,</li>
            <li>нажмите кнопку <div class = "icon icon-date_time_icon"></div> <b>Дата и время</b>,</li>
            <li>
                в открывшемся окне <b>Дата и время</b> укажите следующие параметры:
                <ul>
                    <li>Выберите нужный язык.</li>
                    <li>Выберите один из предложенных форматов.</li>
                    <li>
                        Поставьте галочку <b>Обновлять автоматически</b>, чтобы дата и время обновлялись автоматически на основе текущего состояния.
                        <p class="note">
                            <b>Примечание</b>: также можно обновлять дату и время вручную, используя пункт контекстного меню <b>Обновить поле</b>.
                        </p>
                    </li>
                    <li>Нажмите кнопку <b>Установить по умолчанию</b>, чтобы установить текущий формат как формат по умолчанию для указанного языка.</li>
                </ul>
            </li>
            <li>Нажмите кнопку <b>ОК</b>.</li>
        </ol>
        <p><img alt="Окно Дата и время" src="../images/date_time.png" /></p>
    </div>
</body>
</html>