<!DOCTYPE html>
<html>
	<head>
		<title>Использование формул в таблицах</title>
		<meta charset="utf-8" />
        <meta name="description" content="Вставьте формулы в ячейки таблицы для выполнения простых вычислений" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Использование формул в таблицах</h1>
            <h3>Вставка формулы</h3>
			<p>Вы можете выполнять простые вычисления с данными в ячейках таблицы с помощью формул. Для вставки формулы в ячейку таблицы:</p>
			<ol>
				<li>установите курсор в ячейке, где требуется отобразить результат,</li>
                <li>нажмите кнопку <b>Добавить формулу</b> на правой боковой панели,</li>
				<li>в открывшемся окне <b>Настройки формулы</b> введите нужную формулу в поле <b>Формула</b>. 
                    <p>Нужную формулу можно ввести вручную, используя общепринятые математические операторы (+, -, *, /), например, <em>=A1*B2</em> или использовать выпадающий список <b>Вставить функцию</b>, чтобы выбрать одну из встроенных функций, например, <em>=PRODUCT(A1,B2)</em>.</p>
                <p><img alt="Вставка формулы" src="../images/formula_settings.png" /></p>					
				</li>
                <li>вручную задайте нужные аргументы в скобках в поле <b>Формула</b>. Если функция требует несколько аргументов, их надо вводить через запятую.</li>
                <li>используйте выпадающий список <b>Формат числа</b>, если требуется отобразить результат в определенном числовом формате,</li>
				<li>нажмите кнопку <b>OK</b>.</li>
			</ol>
            <p>Результат будет отображен в выбранной ячейке. </p>
            <p>Чтобы отредактировать добавленную формулу, выделите результат в ячейке и нажмите на кнопку <b>Добавить формулу</b> на правой боковой панели, внесите нужные изменения в окне <b>Настройки формулы</b> и нажмите кнопку <b>OK</b>.</p>
            <hr />
            <h3>Добавление ссылок на ячейки</h3>
            <p>Чтобы быстро добавить ссылки на диапазоны ячеек, можно использовать следующие аргументы:</p>
            <ul>
                <li><b>ABOVE</b> - ссылка на все ячейки в столбце, расположенные выше выделенной ячейки</li>
                <li><b>LEFT</b> - ссылка на все ячейки в строке, расположенные слева от выделенной ячейки</li>
                <li><b>BELOW</b> - ссылка на все ячейки в столбце, расположенные ниже выделенной ячейки</li>
                <li><b>RIGHT</b> - ссылка на все ячейки в строке, расположенные справа от выделенной ячейки</li>
            </ul>
            <p>Эти аргументы можно использовать с функциями AVERAGE, COUNT, MAX, MIN, PRODUCT, SUM.</p>
            <p>Также можно вручную вводить ссылки на определенную ячейку (например, <em>A1</em>) или диапазон ячеек (например, <em>A1:B3</em>).</p>
            <h3>Использование закладок</h3>
            <p>Если вы добавили какие-то <a href="../UsageInstructions/InsertBookmarks.htm" onclick="onhyperlinkclick(this)">закладки</a> на определенные ячейки в таблице, при вводе формул можно использовать эти закладки в качестве аргументов.</p>
            <p>В окне <b>Настройки формулы</b> установите курсор внутри скобок в поле ввода <b>Формула</b>, где требуется добавить аргумент, и используйте выпадающий список <b>Вставить закладку</b>, чтобы выбрать одну из ранее добавленных закладок.</p>
            <h3>Обновление результатов формул</h3>
            <p>Если вы изменили какие-то значения в ячейках таблицы, потребуется вручную обновить результаты формул:</p>
            <ul>
                <li>Чтобы обновить результат отдельной формулы, выделите нужный результат и нажмите клавишу <b>F9</b> или щелкните по результату правой кнопкой мыши и используйте пункт меню <b>Обновить поле</b>.</li>
                <li>Чтобы обновить результаты нескольких формул, выделите нужные ячейки или всю таблицу и нажмите клавишу <b>F9</b>.</li>
            </ul>
            <hr />
            <h3>Встроенные функции</h3>
            <p>Можно использовать следующие стандартные математические, статистические и логические функции:</p>
            <table>
                <tr>
                    <td width="20%"><b>Категория</b></td>
                    <td width="20%"><b>Функция</b></td>
                    <td width="35%"><b>Описание</b></td>
                    <td width="25%"><b>Пример</b></td>
                </tr>
                <tr>
                    <td>Математические</td>
                    <td>ABS(x)</td>
                    <td>Функция используется для нахождения модуля (абсолютной величины) числа.</td>
                    <td>=ABS(-10)<br />Возвращает 10</td>
                </tr>
                <tr>
                    <td>Логические</td>
                    <td>AND(logical1, logical2, ...)</td>
                    <td>Функция используется для проверки, является ли введенное логическое значение истинным или ложным. Функция возвращает значение 1 (ИСТИНА), если все аргументы имеют значение ИСТИНА.</td>
                    <td>=AND(1&gt;0,1&gt;3)<br />Возвращает 0</td>
                </tr>
                <tr>
                    <td>Статистические</td>
                    <td>AVERAGE(argument-list)</td>
                    <td>Функция анализирует диапазон данных и вычисляет среднее значение.</td>
                    <td>=AVERAGE(4,10)<br />Возвращает 7</td>
                </tr>
                <tr>
                    <td>Статистические</td>
                    <td>COUNT(argument-list)</td>
                    <td>Функция используется для подсчета количества ячеек в выбранном диапазоне, содержащих числа, без учета пустых или содержащих текст ячеек.</td>
                    <td>=COUNT(A1:B3)<br />Возвращает 6</td>
                </tr>
                <tr>
                    <td>Логические</td>
                    <td>DEFINED()</td>
                    <td>Функция оценивает, определено ли значение в ячейке. Функция возвращает 1, если значение определено и вычисляется без ошибок, и возвращает 0, если значение не определено или вычисляется с офибкой.</td>
                    <td>=DEFINED(A1)</td>
                </tr>
                <tr>
                    <td>Логические</td>
                    <td>FALSE()</td>
                    <td>Функция возвращает значение 0 (ЛОЖЬ) и <b>не</b> требует аргумента. </td>
                    <td>=FALSE<br />Возвращает 0</td>
                </tr>
                <tr>
                    <td>Логические</td>
                    <td>IF(logical_test, value_if_true, value_if_false)</td>
                    <td>Функция используется для проверки логического выражения и возвращает одно значение, если проверяемое условие имеет значение ИСТИНА, и другое, если оно имеет значение ЛОЖЬ.</td>
                    <td>=IF(3&gt;1,1,0)<br />Возвращает 1</td>
                </tr>
                <tr>
                    <td>Математические</td>
                    <td>INT(x)</td>
                    <td>Функция анализирует и возвращает целую часть заданного числа.</td>
                    <td>=INT(2.5)<br />Возвращает 2</td>
                </tr>
                <tr>
                    <td>Статистические</td>
                    <td>MAX(number1, number2, ...)</td>
                    <td>Функция используется для анализа диапазона данных и поиска наибольшего числа.</td>
                    <td>=MAX(15,18,6)<br />Возвращает 18</td>
                </tr>
                <tr>
                    <td>Статистические</td>
                    <td>MIN(number1, number2, ...)</td>
                    <td>Функция используется для анализа диапазона данных и поиска наименьшего числа.</td>
                    <td>=MIN(15,18,6)<br />Возвращает 6</td>
                </tr>
                <tr>
                    <td>Математические</td>
                    <td>MOD(x, y)</td>
                    <td>Функция возвращает остаток от деления числа на заданный делитель.</td>
                    <td>=MOD(6,3)<br />Возвращает 0</td>
                </tr>
                <tr>
                    <td>Логические</td>
                    <td>NOT(logical)</td>
                    <td>Функция используется для проверки, является ли введенное логическое значение истинным или ложным. Функция возвращает значение 1 (ИСТИНА), если аргумент имеет значение ЛОЖЬ, и 0 (ЛОЖЬ), если аргумент имеет значение ИСТИНА.</td>
                    <td>=NOT(2&lt;5)<br />Возвращает 0</td>
                </tr>
                <tr>
                    <td>Логические</td>
                    <td>OR(logical1, logical2, ...)</td>
                    <td>Функция используется для проверки, является ли введенное логическое значение истинным или ложным. Функция возвращает значение 0 (ЛОЖЬ), если все аргументы имеют значение ЛОЖЬ.</td>
                    <td>=OR(1&gt;0,1&gt;3)<br />Возвращает 1</td>
                </tr>
                <tr>
                    <td>Математические</td>
                    <td>PRODUCT(argument-list)</td>
                    <td>Функция перемножает все числа в заданном диапазоне ячеек и возвращает произведение..</td>
                    <td>=PRODUCT(2,5)<br />Возвращает 10</td>
                </tr>
                <tr>
                    <td>Математические</td>
                    <td>ROUND(x, num_digits)</td>
                    <td>Функция округляет число до заданного количества десятичных разрядов.</td>
                    <td>=ROUND(2.25,1)<br />Возвращает 2.3</td>
                </tr>
                <tr>
                    <td>Математические</td>
                    <td>SIGN(x)</td>
                    <td>Функция определяет знак числа. Если число положительное, функция возвращает значение <b>1</b>. Если число отрицательное, функция возвращает значение <b>-1</b>. Если число равно <b>0</b>, функция возвращает значение <b>0</b>.</td>
                    <td>=SIGN(-12)<br />Возвращает -1</td>
                </tr>
                <tr>
                    <td>Математические</td>
                    <td>SUM(argument-list)</td>
                    <td>Функция возвращает результат сложения всех чисел в выбранном диапазоне ячеек.</td>
                    <td>=SUM(5,3,2)<br />Возвращает 10</td>
                </tr>
                <tr>
                    <td>Логические</td>
                    <td>TRUE()</td>
                    <td>Функция возвращает значение 1 (ИСТИНА) и <b>не</b> требует аргумента.</td>
                    <td>=TRUE<br />Возвращает 1</td>
                </tr>
            </table>             
		</div>
	</body>
</html>