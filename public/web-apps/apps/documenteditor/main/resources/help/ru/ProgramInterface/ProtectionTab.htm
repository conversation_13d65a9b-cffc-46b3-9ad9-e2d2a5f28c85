<!DOCTYPE html>
<html>
	<head>
		<title>Вкладка Защита</title>
		<meta charset="utf-8" />
        <meta name="description" content="Знакомство с пользовательским интерфейсом редактора документов - Вкладка Защита" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Вкладка Защита</h1>
            <p>Вкладка <b>Защита</b> <a href="https://www.onlyoffice.com/ru/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Редактора документов</b></a> позволяет защитить документы при помощи пароля, установив ограниченные права доступа.</p>
            <div class="onlineDocumentFeatures">
                <p>Окно онлайн-редактора документов:</p>
                <p><img alt="Вкладка Защита" src="../images/interface/protectiontab.png" /></p>
            </div>
            <div class="desktopDocumentFeatures">
                <p>Окно десктопного редактора документов:</p>
                <p><img alt="Вкладка Защита" src="../images/interface/desktop_protectiontab.png" /></p>
            </div>
            <p>С помощью этой вкладки вы можете выполнить следующие действия:</p>
            <ul>
                <li><a href="../HelpfulHints/Password.htm" onclick="onhyperlinkclick(this)">задать пароль для документа</a>,</li>
                <li>изменять пароли и удалять их,</li>
                <li>задавать определенные типы редактирования в защищенных документах,</li>
                <li>полностью снять защиту документа.</li>
            </ul>
		</div>
	</body>
</html>