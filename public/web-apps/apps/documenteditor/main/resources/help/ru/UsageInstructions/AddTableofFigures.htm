<!DOCTYPE html>
<html>
	<head>
		<title>Вставка и форматирование списка иллюстраций</title>
		<meta charset="utf-8" />
		<meta name="description" content="Add, format and update Table of Figures using captioned objects and styles" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
			<div class="search-field">
				<input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Вставка и форматирование списка иллюстраций</h1>
			<p><b>Список иллюстраций</b> содержит обзор уравнений, изображений и таблиц, добавленных в документ. Подобно оглавлению, <b>Список иллюстраций</b> перечисляет, сортирует и упорядочивает объекты с заголовками или текстовые заголовки, к которым применен определенный стиль. Это позволяет легко ссылаться на них в вашем документе и перемещаться между рисунками. Щелкните на ссылку в <b>Списке иллюстраций</b>, отформатированную как ссылки, и вы будете направлены прямо к рисунку или заголовку. Любая таблица, уравнение, диаграмма, рисунок, график, диаграмма, карта, фотография или другой тип иллюстраций представлены в виде рисунка.
			<p><img alt="References Tab" src="../images/referencestab.png" /></p>
			<p>Чтобы добавить и редактировать <b>Список иллюстраций</b>, перейдите на вкладку <b>Ссылки</b> верхней панели инструментов и нажмите кнопку <span class = "icon icon-table_figures_button"></span> <b>Список иллюстраций</b>. Используйте кнопку <b>Обновить</b>, чтобы обновить Список иллюстраций тогда, когда вы добавляете новую иллюстрацию в документ.</p>
			<h2>Создание Списка иллюстраций</h2>
			<p class="note"><b>Примечание:</b> Вы можете создать Таблицу рисунков, используя либо рисунки с названием, либо стили. Прежде чем начать, необходимо добавить <a href="../UsageInstructions/AddCaption.htm" onclick="onhyperlinkclick(this)">название</a> к каждому уравнению, таблице или фигуре, либо к тексту необходимо применить <a href="../UsageInstructions/FormattingPresets.htm" onclick="onhyperlinkclick(this)">стиль</a>, чтобы он правильно был включен в Список иллюстраций.</p>
			<ol>
			<li>После добавления названий или стилей поместите курсор в то место, куда вы хотите вставить <b>Список иллюстраций</b>, перейдите на вкладку <b>Ссылки</b> верхней панели инструментов и щелкните на кнопку <b>Список иллюстраций</b>.
            <p><img alt="Параметры Списка иллюстраций" src="../images/table_figures_settings.png" /></p>
			</li>
			<li>Выберите, каким образом создать <b>Список иллюстраций</b>: используя <b>Название</b> или <b>Стиль</b>.
			<ul style = "list-style-type:disc">
			<li>Вы можете создать <b>Список иллюстраций</b> на основе объектов с названием. Для этого поставьте кружок напротив переключателя <b>Название</b> и выберите объект с названием из раскрывающегося списка:
			<ul style = "list-style-type:circle">
					<li>Нет;</li>
                    <li>Рисунок;</li>
                    <li>Таблица;</li>
                    <li>Уравнение.
			<p><img alt="Список иллюстраций" src="../images/table_figures_captioned.png" /></p>
					</li>
				 </ul>
			 </li>
            <li>Вы можете создать <b>Список иллюстраций</b> на основе стилей, используемых для форматирования текста. Для этого поставьте кружок напротив переключателя <b>Стиль</b> и выберите стиль из раскрывающегося списка. Список параметров может изменяться в зависимости от применяемого стиля:
			<ul style = "list-style-type:circle">
                       <li>Заголовок 1;</li>
                       <li>Заголовок 2;</li>
                       <li>Название;</li>
                       <li>Список иллюстраций;</li>
					   <li>Простой.
			<p><img alt="Список иллюстраций Стиль" src="../images/table_figures_style.png" /></p>
						</li>
					</ul>
				</li>
			</ul>
			</li>
			</ol>
			<h2>Форматирование списка иллюстраций</h2>
			<p>Параметры с флажками позволяют редактировать <b>Список иллюстраций</b>. По-умолчанию, все параметры форматирования активированы, так как в большинстве случаев они полезны. Снимите флажки, которые вам не нужны.</p>
			<ul style = "list-style-type:none">
						<li><b>Показать номера страниц</b> - чтобы отображать номер страницы, на которой находится иллюстрация;</li>
						<li><b>Номера страниц по правому краю</b> - чтобы отображать номера страниц справа; снимите флажок, чтобы номера страниц отображались сразу после заголовка; опция доступна только при включенной функции <b>Показать номера страниц</b>;</li>
						<li><b>Форматировать список иллюстраций как ссылки</b> - чтобы добавить гиперссылки в <b>Список иллюстраций</b>;</li>
						<li><b>Полное название</b> - чтобы добавить название и номер в <b>Список иллюстраций</b>.</li>
			</ul>
			<ul style = "list-style-type:disc">
			<li>Выберите стиль из раскрывающегося списка, чтобы связать заголовки с номерами страниц для лучшей визуализации.</li>
			<li>Настройте стиль текста Списка иллюстраций, выбрав из раскрывающегося списка один из доступных стилей:
				<ul style = "list-style-type:circle">
						<li><b>Текущий</b> - отображать стиль, выбранный ранее.</li>
						<li><b>Простой</b> - выделить текст жирным	шрифтом.</li>
						<li><b>Онлайн</b> - выделяет и размещает текст в виде гиперссылки.</li>
						<li><b>Классический</b> - делает текст заглавными.</li>
						<li><b>Изысканный</b> - выделяет текст курсивом.</li>
						<li><b>По центру</b> - помещает текст по центру и не отображает выноски.</li>
						<li><b>Официальный</b> - отображает текст шрифтом Arial размером 11 пт для придания более официального вида.</li>
				</ul>
				</li>
		<li>В окне предварительного просмотра отображается, как <b>Список иллюстраций</b> выглядит в документе или при печати.</li>
			</ul>
		<h2>Обновление списка иллюстраций</h2>
		<p>Обновляйте <b>Список иллюстраций</b> каждый раз, когда вы добавляете новое уравнение, рисунок или таблицу в свой документ. Кнопка <b>Обновить</b> становится активной, когда вы щелкаете <b>Список иллюстраций</b>.</p>
		<ol>
			<li>
				На вкладке <b>Ссылки</b> верхней панели инструментов нажмите кнопку <span class = "icon icon-refresh_button"></span> <b>Обновить</b> и выберите в меню подходящий вариант:
				<ul style="list-style-type:disc">
					<li><b>Обновить только номера страниц</b> - обновить номера страниц без изменения заголовков.</li>
					<li><b>Обновить целиком</b> - для обновления всех измененных заголовков и номеров страниц.</li>
				</ul>
				<p><img alt="Окно Обновить" src="../images/refresh_table_figures_popup.png" /></p>
				<p>Щекните <b>OK</b>, чтобы подтвердить ваш выбор, или
			</li>
			<li>Щелкните правой кнопкой мыши по <b>Списку иллюстраций</b> в вашем документе. В открывшемся контекстном меню выберите пункт <b>Обновить поле</b>. Список иллюстраций будет обновлен целиком.</li>
		</ol>
		</div>
	</body>
</html>	