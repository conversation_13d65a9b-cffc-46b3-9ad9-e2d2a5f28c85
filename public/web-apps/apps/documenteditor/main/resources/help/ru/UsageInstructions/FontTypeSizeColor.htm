<!DOCTYPE html>
<html>
	<head>
		<title>Настройка типа, размера и цвета шрифта</title>
		<meta charset="utf-8" />
		<meta name="description" content="Измените следующие параметры форматирования текста: тип, размер и цвет шрифта" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Настройка типа, размера и цвета шрифта</h1>
			<p>Вы можете выбрать тип шрифта, его размер и цвет, используя соответствующие значки на вкладке <b>Главная</b> верхней панели инструментов.</p>
            <p class="note"><b>Примечание</b>: если требуется отформатировать текст, который уже есть в документе, выделите его мышью или <a href="../HelpfulHints/KeyboardShortcuts.htm#textselection" onclick="onhyperlinkclick(this)">с помощью клавиатуры</a>, а затем примените форматирование. Также можно поместить курсор мыши в нужное слово, чтобы применить форматирование только к этому слову.</p>
            <table>
                <tr>
                    <td width="10%">Шрифт</td>
                    <td width="15%"><div class = "big big-fontfamily"></div></td>
                    <td>Используется для выбора шрифта из списка доступных. <span class="desktopDocumentFeatures">Если требуемый шрифт отсутствует в списке, его можно скачать и установить в вашей операционной системе, после чего он будет доступен для использования в <em>десктопной версии</em>.</span></td>
                </tr>
                <tr>
                    <td>Размер шрифта</td>
                    <td><div class = "icon icon-fontsize"></div></td>
                    <td>Используется для выбора предустановленного значения размера шрифта из выпадающего списка (доступны следующие стандартные значения: 8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 36, 48, 72 и 96). Также можно вручную ввести произвольное значение до 300 пунктов в поле ввода и нажать клавишу <em>Enter</em>.</td>
                </tr>
                <tr>
                    <td>Увеличить размер шрифта</td>
                    <td><div class = "icon icon-larger"></div></td>
                    <td>Используется для изменения размера шрифта, делая его на один пункт крупнее при каждом нажатии на кнопку.</td>
                </tr>
                <tr>
                    <td>Уменьшить размер шрифта</td>
                    <td><div class = "icon icon-smaller"></div></td>
                    <td>Используется для изменения размера шрифта, делая его на один пункт мельче при каждом нажатии на кнопку.</td>
                </tr>
                <tr>
                    <td>Изменить регистр</td>
                    <td><div class = "icon icon-change_case"></div></td>
                    <td>Используется для изменения регистра шрифта. <em>Как в предложениях.</em> - регистр совпадает с обычным предложением. <em>нижнеий регистр</em> - все буквы маленькие. <em>ВЕРХНИЙ РЕГИСТР</em> - все буквы прописные. <em>Каждое Слово С Прописной</em> - каждое слово начинается с заглавной буквы. <em>иЗМЕНИТЬ рЕГИСТР</em> - поменять регистр выделенного текста или слова, в котором находится курсор мыши.</td>
                </tr>
                <tr>
                    <td>Цвет выделения</td>
                    <td><div class = "icon icon-highlightcolor"></div></td>
                    <td>
                        Используется для выделения отдельных предложений, фраз, слов или даже символов путем добавления цветовой полосы, имитирующей отчеркивание текста
                        маркером. Можно выделить нужную часть текста, а потом нажать направленную вниз стрелку рядом с этим значком, чтобы выбрать цвет на палитре (этот набор
                        цветов не зависит от выбранной <b>Цветовой схемы</b> и включает в себя 16 цветов), и этот цвет будет применен к выбранному тексту. Или же можно сначала выбрать
                        цвет выделения, а потом начать выделять текст мышью - указатель мыши будет выглядеть так: <div class = "icon icon-highlight_color_mouse_pointer"></div> - и появится возможность выделить несколько разных частей
                        текста одну за другой. Чтобы остановить выделение текста, просто еще раз щелкните по значку. Для очистки цвета выделения воспользуйтесь опцией <b>Без заливки</b>.
                        <b>Цвет выделения</b> отличается от <a href="../UsageInstructions/BackgroundColor.htm" onclick="onhyperlinkclick(this)"> <b>Цвета фона</b></a> <div class = "icon icon-backgroundcolor"></div>,
                        поскольку последний применяется ко всему абзацу и полностью заполняет пространство абзаца от левого поля страницы до правого поля страницы.
                    </td>
                </tr>
                <tr>
                    <td>Цвет шрифта</td>
                    <td><div class = "icon icon-fontcolor"></div></td>
                    <td>
                        Используется для изменения цвета букв/символов в тексте. По умолчанию в новом пустом документе установлен автоматический цвет шрифта. Он отображается как черный
                        шрифт на белом фоне. Если изменить цвет фона на черный, цвет шрифта автоматически изменится на белый, так чтобы текст по-прежнему был четко виден. Для
                        выбора другого цвета нажмите направленную вниз стрелку рядом со значком и выберите цвет на доступных палитрах (цвета на палитре <b>Цвета темы</b> зависят
                        от выбранной <a href="../UsageInstructions/ChangeColorScheme.htm" onclick="onhyperlinkclick(this)">цветовой схемы</a>). После изменения цвета шрифта по умолчанию можно использовать опцию <b>Автоматический</b> в окне цветовых палитр
                        для быстрого восстановления автоматического цвета выбранного фрагмента текста.
                    </td>
                </tr>
            </table>
      <p class="note">
        <b>Примечание</b>: более подробно о работе с цветовыми палитрами рассказывается на <a href="../UsageInstructions/ChangeColorScheme.htm" onclick="onhyperlinkclick(this)">этой странице</a>.
      </p>
    </div>
	</body>
</html>