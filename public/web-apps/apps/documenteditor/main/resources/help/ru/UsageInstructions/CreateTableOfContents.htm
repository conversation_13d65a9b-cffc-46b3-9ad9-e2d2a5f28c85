<!DOCTYPE html>
<html>
	<head>
		<title>Как сделать оглавление в документах Word - ONLYOFFICE</title>
		<meta charset="utf-8" />
        <meta name="description" content="В оглавлении указан список всех глав документа и номера страниц, на которых начинаются главы. Это позволяет легко перемещаться по многостраничному документу." />
        <meta name="keywords" content="как сделать оглавление в ворде, оглавление в ворде, оглавление, как вставить оглавление в ворде, как вставить содержание в ворде, как сделать автоматическое оглавление, как создать оглавление в ворде, оглавление это, как сделать оглавление, автособираемое оглавление, как сделать оглавление, автоматическое оглавление в ворде, как сделать автоматическое оглавление в ворде, оглавление ворд, word оглавление, как сделать автособираемое оглавление, оформление содержания, как сделать автоматическое содержание, оформление содержания в ворде, создать оглавление в ворде, автосодержание ворд, создание оглавления в ворде, как добавить содержание в word, автоматическое оглавление, вставить содержание ворд, как создать оглавление, как создать автоматическое оглавление, вставить оглавление в ворде, как добавить строку в оглавление в ворде, word как вставить оглавление, как написать содержание в ворде, как сделать оглавление с подзаголовками в ворде, как исправить оглавление в ворде, как сделать автооглавление">
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Оглавление</h1>
            <p>В оглавлении содержится список всех глав (разделов и т.д.) документа и отображаются номера страниц, на которых начинается каждая глава. Это позволяет легко перемещаться по многостраничному документу, быстро переходя к нужной части текста. Содержание генерируется автоматически на основе заголовков документа, отформатированных с помощью встроенных стилей. Это позволяет легко обновлять созданное содержание без необходимости редактировать заголовки и изменять номера страниц вручную при изменении текста документа.</p>
			<h2>Структура заголовков оглавления</h2>
            <p><b>Форматирование заголовков</b></p>
            <p>Прежде всего отформатируйте заголовки в документе с помощью одного из предустановленных стилей. Для этого:</p>
            <ol>
                <li>Выделите текст, который требуется включить в содержание.</li>
                <li>Откройте меню стилей в правой части вкладки <b>Главная</b> на верхней панели инструментов.</li>
                <li>Щелкните по стилю, который хотите применить. По умолчанию можно использовать стили <em>Заголовок 1 - Заголовок 9</em>.
                <p class="note"><b>Примечание</b>: если вы хотите использовать другие стили (например, <em>Название</em>, <em>Подзаголовок</em> и другие) для форматирования заголовков, которые будут включены в оглавление, сначала потребуется изменить настройки оглавления (обратитесь к соответствующему разделу ниже). Для получения дополнительной информации о доступных стилях форматирования можно обратиться к <a href="../UsageInstructions/FormattingPresets.htm" onclick="onhyperlinkclick(this)">этой странице</a>.</p>
                </li>
            </ol>
            <p>Чтобы быстро добавить текст в качестве заголовка,</p>
            <ol>
                <li>Выделите текст, который требуется включить в содержание.</li>
                <li>Перейдите на вкладку <b>Ссылки</b> на верхней панели инструментов.</li>
                <li>Нажмите кнопку <b>Добавить текст</b> на верхней панели инструментов.</li>
                <li>Выберите нужный уровень заголовка.</li>
            </ol>
            <p id="navigation"><b>Управление заголовками</b></p>
            <p>Когда заголовки будут отформатированы, можно нажать на значок <span class = "icon icon-navigationicon"></span> <b>Заголовки</b> на левой боковой панели, чтобы открыть панель, на которой отображается список всех заголовков с учетом соответствующих уровней вложенности. С помощью этой панели можно легко перемещаться между заголовками в тексте документа, а также управлять структурой заголовков.</p>
            <p>Щелкните правой кнопкой мыши по заголовку в списке и используйте один из доступных пунктов меню:</p>
            <p><img alt="Панель Заголовки" src="../images/navigationpanel.png" /></p>
            <ul>
                <li><b>Повысить уровень</b> - чтобы перенести выбранный заголовок на более высокий уровень в иерархической структуре, например, изменить <em>Заголовок 2</em> на <em>Заголовок 1</em>.</li>
                <li><b>Понизить уровень</b> - чтобы перенести выбранный заголовок на более низкий уровень в иерархической структуре, например, изменить <em>Заголовок 1</em> на <em>Заголовок 2</em>.</li>
                <li><b>Новый заголовок перед</b> - чтобы добавить новый пустой заголовок такого же уровня перед выбранным заголовком.</li>
                <li><b>Новый заголовок после</b> - чтобы добавить новый пустой заголовок такого же уровня после выбранного заголовка.</li>
                <li><b>Новый подзаголовок</b> - чтобы добавить новый пустой подзаголовок (то есть заголовок более низкого уровня) после выбранного заголовка.
                <p>После того, как заголовок или подзаголовок будет добавлен, щелкните по добавленному пустому заголовку в списке и введите свой текст. Это можно сделать и в тексте документа, и непосредственно на панели <b>Заголовки</b>.</p>
                </li>
                <li><b>Выделить содержимое</b> - чтобы выделить в документе текст, относящийся к выбранному заголовку (включая текст, относящийся ко всем подзаголовкам этого заголовка).</li>
                <li><b>Развернуть все</b> - чтобы развернуть все уровни заголовков на панели <b>Заголовки</b>.</li>
                <li><b>Свернуть все</b> - чтобы свернуть все уровни заголовков, кроме <em>уровня 1</em>, на панели <b>Заголовки</b>.</li>
                <li><b>Развернуть до уровня</b> - чтобы развернуть структуру заголовков до выбранного уровня. Например, если выбрать <em>уровень 3</em>, то будут развернуты уровни 1, 2 и 3, а уровень 4 и все более низкие уровни будут свернуты.</li>
            </ul>
            <p>Чтобы вручную развернуть или свернуть определенные уровни заголовков, используйте стрелки слева от заголовков.</p>
            <p>Чтобы закрыть панель <b>Заголовки</b>, нажмите на значок <span class = "icon icon-navigationicon"></span> <b>Заголовки</b> на левой боковой панели еще раз.</p>
            <h2>Вставка оглавления в документ</h2>
            <p>Чтобы вставить в документ оглавление:</p>
            <ol>
                <li>Установите курсор там, где требуется добавить содержание.</li>
                <li>Перейдите на вкладку <b>Ссылки</b> верхней панели инструментов.</li>
                <li>Нажмите на значок <div class = "icon icon-tocicon"></div> <b>Оглавление</b> на верхней панели инструментов или<br />
                нажмите на стрелку рядом с этим значком и выберите из меню нужный вариант макета. Можно выбрать макет, в котором отображаются заголовки, номера страниц и заполнители или только заголовки.
                <p><img alt="Варианты макета оглавления" src="../images/tociconmenu.png" /></p>
                    <p class="note"><b>Примечание</b>: внешний вид оглавления можно изменить позже, используя настройки.</p>
                </li>
            </ol>
            <p>Оглавление будет добавлено в текущей позиции курсора. Чтобы изменить его местоположение, можно выделить поле оглавления (элемент управления содержимым) и просто перетащить его на нужное место. Для этого нажмите на кнопку <span class = "icon icon-toccontentcontrol"></span> в левом верхнем углу поля оглавления и перетащите его, не отпуская кнопку мыши.</p>
            <p><img alt="Перемещение оглавления" src="../images/tocmove.png" /></p>
            <p>Для перемещения между заголовками нажмите клавишу <b>Ctrl</b> и щелкните по нужному заголовку в поле оглавления. Вы перейдете на соответствующую страницу.</p>
            <h2>Изменение созданного оглавления</h2>
            <p><b>Обновление оглавления</b></p>
            <p>После того, как содержание будет создано, вы можете продолжить редактирование текста, добавляя новые главы, изменяя их порядок, удаляя какие-то абзацы или дополняя текст, относящийся к заголовку, так что номера страниц, соответствующие предыдущему или следующему разделу могут измениться. В этом случае используйте опцию <b>Обновление</b>, чтобы автоматически применить все изменения.</p>
            <p>Нажмите на стрелку рядом со значком <span class = "icon icon-tocrefreshicon"></span> <b>Обновление</b> на вкладке <b>Ссылки</b> верхней панели инструментов и выберите в меню нужную опцию:</p>
            <ul>
                <li><b>Обновить целиком</b> - чтобы добавить в оглавление заголовки, добавленные в документ, удалить те, которые были удалены из документа, обновить отредактированные (переименованные) заголовки, а также обновить номера страниц.</li>
                <li><b>Обновить только номера страниц</b> - чтобы обновить номера страниц, не применяя изменения к заголовкам.</li>
            </ul>
            <p>Можно выделить оглавление в тексте документа и нажать на значок <span class = "icon icon-tocrefreshiconcc"></span> <b>Обновление</b> в верхней части поля оглавления, чтобы показать указанные выше опции.</p>
            <p><img alt="Обновление оглавления" src="../images/tocrefreshcc.png" /></p>
            <p>Можно также щелкнуть правой кнопкой мыши по оглавлению и использовать соответствующие команды контекстного меню.</p>
            <p><img alt="Контекстное меню" src="../images/tocrefreshcontextual.png" /></p>
            <p><b>Изменение настроек оглавления</b></p>
            <p>Чтобы открыть настройки оглавления, можно действовать одним из следующих способов:</p>
            <ul>
                <li>Нажмите на стрелку рядом со значком <div class = "icon icon-tocicon"></div> <b>Оглавление</b> на верхней панели инструментов и выберите в меню опцию <b>Настройки</b>.</li>
                <li>Выделите оглавление в тексте документа, нажмите на стрелку рядом с заголовком поля оглавления и выберите в меню опцию <b>Настройки</b>.
                <p><img alt="Настройки оглавления" src="../images/tocsettingscc.png" /></p>    
                </li>
                <li>Щелкните правой кнопкой мыши по оглавлению и используйте команду контекстного меню <b>Параметры оглавления</b>.</li>
            </ul>
            <p>Откроется новое окно, в котором можно настроить следующие параметры:</p>
            <p><img alt="Окно настроек оглавления" src="../images/tocsettingswindow.png" /></p> 
            <ul>
                <li><b>Показать номера страниц</b> - эта опция позволяет выбрать, надо ли отображать номера страниц или нет.</li>
                <li><b>Номера страниц по правом краю</b> - эта опция позволяет выбрать, надо ли выравнивать номера страниц по правому краю или нет.</li>
                <li><b>Заполнитель</b> - эта опция позволяет выбрать тип используемого заполнителя. Заполнитель - это строка символов (точек или дефисов), заполняющая пространство между заголовком и соответствующим номером страницы. Можно также выбрать опцию <b>Нет</b>, если вы не хотите использовать заполнители.</li>
                <li><b>Форматировать оглавление как ссылки</b> - эта опция отмечена по умолчанию. Если убрать галочку, нельзя будет переходить к нужной главе, нажав клавишу <b>Ctrl</b> и щелкнув по соответствующему заголовку.</li>
                <li><b>Собрать оглавление, используя</b> - в этом разделе можно указать нужное количество уровней структуры, а также стили по умолчанию, которые будут использоваться для создания оглавления. Выберите нужный переключатель:
                <ul>
                    <li><b>Уровни структуры</b> - когда выбрана эта опция, вы сможете изменить количество используемых иерархических уровней. Используйте стрелки в поле <b>Уровни</b>, чтобы уменьшить или увеличить число уровней (доступны значения от 1 до 9). Например, если выбрать значение 3, заголовки уровней 4 - 9 не будут включены в оглавление.
                    </li>
                    <li><b>Выделенные стили</b> - когда выбрана эта опция, можно указать дополнительные стили, которые будут использоваться для создания оглавления, и назначить каждому из них соответствующий уровень структуры. Укажите нужное значение уровня в поле справа от стиля. После сохранения настроек вы сможете использовать этот стиль при создании оглавления.
                    <p><img alt="Окно настроек оглавления" src="../images/tocsettingswindow2.png" /></p>
                    </li>
                </ul>
                </li>
                <li><b>Стили</b> - эта опция позволяет выбрать нужное оформление. Выберите нужный стиль из выпадающего списка. В поле предварительного просмотра выше отображается то, как должно выглядеть оглавление.
                <p>Доступны следующие четыре стиля по умолчанию: <em>Простой</em>, <em>Стандартный</em>, <em>Современный</em>, <em>Классический</em>. Опция <em>Текущий</em> используется, если вы применили к стилю пользовательские настройки.</p>
                </li>
            </ul>
            <p>Нажмите кнопку <b>OK</b> в окне настроек, чтобы применить изменения.</p>
            <p><b>Настройка стиля оглавления</b></p>
            <p>После применения в окне настроек <b>Оглавления</b> одного из стилей по умолчанию этот стиль можно дополнительно изменить, чтобы текст в поле оглавления выглядел так, как вам нужно.</p>
            <ol>
                <li>Выделите текст в поле оглавления, например, нажав на кнопку <div class = "icon icon-toccontentcontrol"></div> в левом верхнем углу поля оглавления.</li>
                <li>Отформатируйте элементы, изменив тип, размер, цвет шрифта или применив стили оформления шрифта.</li>
                <li>Последовательно обновите стили для элементов всех уровней. Чтобы обновить стиль, щелкните правой кнопкой мыши по отформатированному элементу, выберите в контекстном меню пункт <b>Форматирование как стиль</b> и используйте опцию <b>Обновить стиль toc N</b> (стиль <em>toc 2</em> соответствует элементам с <em>уровнем 2</em>, стиль <em>toc 3</em> соответствует элементам с <em>уровнем 3</em> и так далее).
                    <p><img alt="Обновление стиля оглавления" src="../images/toccustomize.png" /></p>
                </li>
                <li>Обновите оглавление.</li>
            </ol>
            <p><b>Удаление оглавления</b></p>
            <p>Чтобы удалить оглавление из документа:</p>
            <ul>
                <li>Нажмите на стрелку рядом со значком <div class = "icon icon-tocicon"></div> <b>Оглавление</b> на верхней панели инструментов и выберите в меню опцию <b>Удалить оглавление</b>,</li>
                <li>или нажмите стрелку рядом с заголовком поля оглавления и используйте опцию <b>Удалить оглавление</b>.</li>
            </ul>
		</div>
	</body>
</html>