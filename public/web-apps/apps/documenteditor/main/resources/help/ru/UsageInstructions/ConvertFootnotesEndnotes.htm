<!DOCTYPE html>
<html>
<head>
    <title>Преобразование сносок и концевых сносок</title>
    <meta charset="utf-8" />
    <meta name="description" content="Вставьте сноски для объяснения некоторых терминов или сделайте ссылки на источники" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Преобразование сносок и концевых сносок</h1>
        <p>Редактор документов позволяет быстро преобразовать <b>сноски</b> в <b>концевые сноски</b> и наоборот, например, если вы видите, что некоторые сноски в документе должны быть помещены в конец. Вместо того, чтобы воссоздавать их как концевые сноски, используйте соответствующий инструмент для легкого преобразования.</p>
        <ol>
            <li>На вкладке <b>Ссылки</b> верхней панели инструментов нажмите на стрелочку рядом со значком <div class = "icon icon-addfootnote"></div> <b>Сноска</b>,</li>
            <li>Наведите указатель мыши на пункт меню <b>Преобразовать все сноски</b> и выберите один из вариантов в списке справа:
                <p><img alt="Convert footnotes_endnotes" src="../images/convert_footnotes_endnotes.png" /></p></li>
                <li>
                    <ul>
                        <li><b>Преобразовать все обычные сноски в концевые сноски</b>, чтобы заменить все сноски в концевые сноски;</li>
                        <li><b>Преобразовать все концевые сноски в обычные сноски</b>, чтобы заменить все концевые сноски на сноски;</li>
                        <li><b>Поменять сноски</b>, чтобы заменить все концевые сноски на сноски, а все сноски на концевые сноски.</li>
                    </ul>
                </li>
        </ol>
        </div>
</body>
</html>