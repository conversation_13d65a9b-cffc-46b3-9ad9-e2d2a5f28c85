<!DOCTYPE html>
<html>
	<head>
		<title>Применение стилей форматирования</title>
		<meta charset="utf-8" />
		<meta name="description" content="Применяйте стили форматирования: обычный, заголовок, цитата, список и т.д." />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Применение стилей форматирования</h1>
            <p>Каждый стиль форматирования - это набор предварительно заданных параметров форматирования: (размер, цвет шрифта, междустрочный интервал, выравнивание и т.д.). Стили позволяют быстро форматировать различные части документа (заголовки, подзаголовки, списки, обычный текст, цитаты) вместо того, чтобы каждый раз применять несколько параметров форматирования по отдельности. Это также обеспечивает единое оформление на протяжении всего документа.</p>
            <p>Вы также можете использовать стили для того, чтобы добавить <a href="../UsageInstructions/CreateTableOfContents.htm" onclick="onhyperlinkclick(this)">оглавление</a> или <a href="../UsageInstructions/AddTableofFigures.htm" onclick="onhyperlinkclick(this)">список иллюстраций</a>.</p>
            <p class="note">Применение стиля зависит от того, является ли стиль стилем абзаца (обычный, без интервала, абзац списка и др.) или стилем текста (на базе начертания, цвета, размера текста), а также от того, выделен ли фрагмент текста или курсор установлен в слове. В некоторых случаях для корректного применения стиля требуется повторный выбор этого же стиля из библиотеки стилей: в первый раз применяются параметры стиля абзаца, во второй раз - параметры стиля текста.</p>
            <h3>Использование стандартных стилей</h3>
            <p>Чтобы применить один из доступных стилей форматирования текста:</p>
            <ol>
                <li>установите курсор внутри нужного абзаца или выделите несколько абзацев, к которым требуется применить стиль форматирования,</li>
                <li>выберите нужный стиль из библиотеки стилей, расположенной справа на вкладке <b>Главная</b> верхней панели инструментов.</li>
            </ol>
            <p>Доступны следующие стили форматирования: обычный, без интервала, заголовок 1-9, название, подзаголовок, цитата, выделенная цитата, абзац списка, нижний колонтитул, верхний колонтитул, текст сноски.</p>
            <p><img alt="Стили форматирования" src="../images/formattingpresets.png" /></p>
            <h3>Редактирование существующих стилей и создание новых</h3>
            <p><b>Чтобы изменить существующий стиль:</b></p>
            <ol>
                <li>Примените нужный стиль к абзацу.</li>
                <li>Выделите текст абзаца и измените все параметры форматирования, которые нужно.</li>
                <li>
                    Сохраните внесенные изменения:
                    <ul>
                        <li>щелкните правой кнопкой мыши по отредактированному тексту, выберите опцию <b>Форматирование как стиль</b>, а затем - опцию <b>Обновить стиль 'НазваниеСтиля'</b> ('НазваниеСтиля' соответствует тому стилю, который вы применили на первом шаге),</li>
                        <li>или выделите мышью отредактированный фрагмент текста, откройте библиотеку стилей, щелкните правой кнопкой мыши по стилю, который требуется изменить, и выберите опцию <b>Обновить из выделенного фрагмента</b>.</li>
                    </ul>
                </li>
            </ol>
            <p>Как только стиль будет изменен, оформление всех абзацев в документе, отформатированных с помощью этого стиля, изменится соответствующим образом.</p>
            <p><b>Чтобы создать совершенно новый стиль:</b></p>
            <ol>
                <li>Отформатируйте фрагмент текста так, как вам нужно.</li>
                <li>
                    Выберите подходящий способ сохранения стиля:
                    <ul>
                        <li>щелкните правой кнопкой мыши по отредактированному тексту, выберите опцию <b>Форматирование как стиль</b>, а затем - опцию <b>Создать новый стиль</b>,</li>
                        <li>или выделите мышью отредактированный фрагмент текста, откройте библиотеку стилей и щелкните по надписи <b>Новый стиль из выделенного фрагмента</b>.</li>
                    </ul>
                </li>
                <li>
                    Задайте параметры нового стиля в открывшемся окне <b>Создание нового стиля</b>:
                    <p><img alt="Окно Создание нового стиля" src="../images/createnewstylewindow.png" /></p>
                    <ul>
                        <li>Укажите название нового стиля в поле ввода текста.</li>
                        <li>Выберите из списка <b>Стиль следующего абзаца</b> нужный стиль для последующего абзаца. Также можно выбрать опцию <b>Такой же, как создаваемый стиль</b>.</li>
                        <li>Нажмите кнопку <b>OK</b>.</li>
                    </ul>
                </li>
            </ol>
            <p>Созданный стиль будет добавлен в библиотеку стилей.</p>
            <p><b>Управление пользовательскими стилями:</b></p>
            <ul>
                <li>Чтобы восстановить параметры по умолчанию определенного стиля, который вы изменили, щелкните правой кнопкой мыши по стилю, который вы хотите восстановить, и выберите опцию <b>Восстановить параметры по умолчанию</b>.</li>
                <li>
                    Чтобы восстановить параметры по умолчанию всех стилей, которые вы изменили, щелкните правой кнопкой мыши по любому стандартному стилю стилю в библиотеке стилей и выберите опцию <b>Восстановить все стандартные стили</b>.
                    <p><img alt="Меню измененного стиля" src="../images/editedstylemenu.png" /></p>
                </li>
                <li>Чтобы удалить один из новых стилей, который вы создали, щелкните правой кнопкой мыши по стилю, который вы хотите удалить, и выберите опцию <b>Удалить стиль</b>.</li>
                <li>
                    Чтобы удалить все новые стили, которые вы создали, щелкните правой кнопкой мыши по любому новому стилю, который вы создали, и выберите опцию <b>Удалить все пользовательские стили</b>.
                    <p><img alt="Меню пользовательского стиля" src="../images/customstylemenu.png" /></p>
                </li>
            </ul>
        </div>
	</body>
</html>