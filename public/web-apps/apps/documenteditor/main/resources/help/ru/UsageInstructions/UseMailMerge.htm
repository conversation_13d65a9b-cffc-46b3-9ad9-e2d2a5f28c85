<!DOCTYPE html>
<html>
	<head>
		<title>Использование слияния</title>
		<meta charset="utf-8" />
        <meta name="description" content="Используйте функцию слияния, чтобы создавать множество персонализированных писем и отправлять их получателям" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Использование слияния</h1>
            <p class="note"><b>Примечание</b>: эта возможность доступна только в <em>онлайн-версии</em>.</p>
			<p>Функция <b>слияния</b> используется для создания набора документов, в которых сочетается общее содержание, взятое из текстового документа, и ряд индивидуальных компонентов (переменных, таких как имена, приветствия и т.д.), взятых из электронной таблицы (например, из списка клиентов). Это может быть полезно, если вам надо создать множество персонализированных писем и отправить их получателям.</p>
            <ul>
                <li><b>Подготовьте источник данных и загрузите его в основной документ</b>
                <ol>
                    <li>Источник данных, используемый для слияния, должен быть электронной таблицей в формате <b>.xlsx</b>, сохраненной на вашем портале. Откройте существующую электронную таблицу или создайте новую и убедитесь, что она соответствует следующим требованиям.</li>
                    <li>Таблица должна содержать строку заголовков с названиями столбцов, так как значения в первой ячейке каждого столбца определяют поля слияния (то есть переменные, которые можно вставить в текст). Каждый столбец должен содержать набор конкретных значений для переменной. Каждая строка в таблице должна соответствовать отдельной записи (то есть ряду значений, относящихся к определенному получателю). В ходе слияния для каждой записи будет создана копия основного документа, и каждое поле слияния, вставленное в основной текст, будет заменено фактическим значением из соответствующего столбца. Если вы собираетесь отправлять результаты по электронной почте, таблица также должна содержать столбец с адресами электронной почты получателей.</li>
                    <li>
                        Откройте существующий текстовый документ или создайте новый. Он должен содержать основной текст, который будет одинаковым для каждой версии документа, полученного в результате слияния. Нажмите значок <b>Слияние</b> <div class = "icon icon-mailmergeicon"></div> на вкладке <b>Главная</b> верхней панели инструментов и выберите источник данных: <b>Из файла</b>, <b>По URL</b> или <b>Из хранилища</b>.
                        <p><img alt="Настройки слияния" src="../images/mailmerge_options.png" /></p>
                    </li>
                    <li>
                        Откроется окно <b>Выбрать источник данных</b>. В нем отображается список всех ваших электронных таблиц в формате <b>.xlsx</b>, которые сохранены в разделе <b>Мои документы</b>. Для перехода по другим разделам модуля <b>Документы</b> используйте меню в левой части окна. Выберите нужный файл и нажмите кнопку <b>OK</b>.
                        <p>Когда источник данных будет загружен, на правой боковой панели станет доступна вкладка <b>Параметры слияния</b>.</p>
                        <p><img alt="Вкладка Параметры слияния" src="../images/right_mailmerge.png" /></p>
                    </li>
                </ol>
                <li><b>Проверьте или измените список получателей</b>
                <ol>
                    <li>Нажмите на кнопку <b>Изменить список получателей</b> наверху правой боковой панели, чтобы открыть окно <b>Получатели слияния</b>, в котором отображается содержание выбранного источника данных.
                    <p><img alt="Окно Получатели слияния" src="../images/mailmergerecipients.png" /></p>
                    </li>
                    <li>Здесь можно добавить новую информацию, изменить или удалить существующие данные, если это необходимо. Чтобы облегчить работу с данными, можно использовать значки в верхней части окна:
                    <ul>
                        <li><div class = "icon icon-copy"></div> и <div class = "icon icon-paste"></div> - чтобы копировать и вставлять скопированные данные</li>
                        <li><div class = "icon icon-undo1"></div> и <div class = "icon icon-redo1"></div> - чтобы отменять и повторять отмененные действия</li>
                        <li><div class = "icon icon-sortatoz"></div> и <div class = "icon icon-sortztoa"></div> - чтобы сортировать данные внутри выделенного диапазона ячеек в порядке возрастания или убывания</li>
                        <li><div class = "icon icon-sortandfilter"></div> - чтобы включить фильтр для предварительно выделенного диапазона ячеек или чтобы удалить примененный фильтр</li>
                        <li><div class = "icon icon-clearfilter"></div> - чтобы очистить все примененные параметры фильтра
                        <p class="note"><b>Примечание</b>: для получения дополнительной информации по использованию фильтра можно обратиться к разделу <b>Сортировка и фильтрация данных</b> в справке по <b>Редактору таблиц</b>.</p>
                        </li>
                        <li><div class = "icon icon-searchicon"></div> - чтобы найти определенное значение и заменить его на другое, если это необходимо
                        <p class="note"><b>Примечание</b>: для получения дополнительной информации по использованию средства <b>поиска и замены</b> можно обратиться к разделу <b>Функция поиска и замены</b> в справке по <b>Редактору таблиц</b>.</p>
                        </li>
                    </ul>
                    </li>
                    <li>После того как все необходимые изменения будут внесены, нажмите кнопку <b>Сохранить и выйти</b>. Чтобы сбросить изменения, нажмите кнопку <b>Закрыть</b>.</li>
                </ol>
                </li>
                <li><b>Вставьте поля слияния и проверьте результаты</b>
                <ol>
                    <li>Установите курсор в тексте основного документа там, куда требуется вставить поле слияния, нажмите кнопку <b>Вставить поле слияния</b> на правой боковой панели и выберите нужное поле из списка. Доступные поля соответствуют данным в первой ячейке каждого столбца выбранного источника данных. Добавьте все поля, которые вам нужны, в любом месте документа.
                    <p><img alt="Раздел Поля слияния" src="../images/mergefields.png" /></p>
                    </li>
                    <li>Включите переключатель <b>Выделить поля слияния</b> на правой боковой панели, чтобы вставленные поля стали заметнее в тексте документа.
                    <p><img alt="Основной документ со вставленными полями" src="../images/insertedfields.png" /></p>
                    </li>
                    <li>Включите переключатель <b>Просмотр результатов</b> на правой боковой панели, чтобы просмотреть текст документа с полями слияния, замененными на фактические значения из источника данных. Используйте кнопки со стрелками, чтобы просмотреть версии документа, созданного в результате слияния, для каждой записи.
                    <p><img alt="Предварительный просмотр результатов" src="../images/previewinsertedfields.png" /></p>
                    </li>
                </ol>
                <ul>
                    <li>Чтобы удалить вставленное поле, отключите режим <b>Просмотр результатов</b>, выделите поле мышью и нажмите клавишу <b>Delete</b> на клавиатуре. </li>
                    <li>Чтобы заменить вставленное поле, отключите режим <b>Просмотр результатов</b>, выделите поле мышью, нажмите кнопку <b>Вставить поле слияния</b> на правой боковой панели и выберите новое поле из списка.</li>
                </ul>
                </li>
                <li><b>Задайте параметры слияния</b>
                <ol>
                    <li>Выберите тип слияния. Вы можете запустить рассылку или сохранить результат как файл в формате PDF или Docx, чтобы в дальнейшем можно было распечатать или отредактировать его. Выберите нужную опцию из списка <b>Слияние в</b>:
                    <p><img alt="Выбор типа слияния" src="../images/mergeto.png" /></p>
                    <ul>
                        <li><b>PDF</b> - для создания единого документа в формате PDF, содержащего все копии, полученные в результате слияния, чтобы в дальнейшем его можно было распечатать</li>
                        <li><b>Docx</b> - для создания единого документа в формате Docx, содержащего все копии, полученные в результате слияния, чтобы в дальнейшем можно было отредактировать отдельные копии</li>
                        <li><b>Email</b> - для отправки результатов получателям по электронной почте
                        <p class="note"><b>Примечание</b>: адреса электронной почты получателей должны быть указаны в загруженном источнике данных, а у вас должна быть хотя бы одна подключенная учетная запись электронной почты в модуле <b>Почта</b> на портале.</p>
                        </li>
                    </ul>
                    </li>
                    <li>Выберите записи, к которым надо применить слияние:
                    <ul>
                        <li><b>Все записи</b> (эта опция выбрана по умолчанию) - чтобы создать объединенные документы для всех записей из загруженного источника данных</li>
                        <li><b>Текущая запись</b> - чтобы создать объединенный документ для записи, отображенной в данный момент</li>
                        <li><b>С</b> ... <b>По</b> - чтобы создать объединенные документы для диапазона записей (в этом случае необходимо указать два значения: номер первой записи и последней записи в требуемом диапазоне)
                        <p class="note"><b>Примечание</b>: максимально допустимое количество получателей - 100. Если в источнике данных более 100 получателей, выполните слияние поэтапно: укажите значения от 1 до 100, дождитесь завершения слияния, и повторите операцию, указав значения от 101 до N и т.д.</p>
                        </li>
                    </ul>
                    </li>
                    <li>Завершите слияние
                        <ul>
                            <li>Если вы решили сохранить результаты слияния как файл,
                            <ul>
                                <li>нажмите кнопку <b>Скачать</b>, чтобы сохранить файл на компьютере. Загруженный файл вы найдете в папке <em>Загрузки</em>, выбранной по умолчанию.</li>
                                <li>нажмите кнопку <b>Сохранить</b>, чтобы сохранить файл на портале. В открывшемся окне <b>Папка для сохранения</b> можно изменить имя файла и задать папку, в которую надо его сохранить. Можно также установить флажок <b>Открыть объединенный документ в новой вкладке</b>, чтобы проверить результат сразу после слияния. Наконец нажмите кнопку <b>Сохранить</b> в окне выбора папки.</li>
                            </ul>
                            </li>
                            <li>Если вы выбрали опцию <b>Email</b>, на правой боковой панели будет доступна кнопка <b>Слияние</b>. После нажатия на нее откроется окно <b>Отправить по электронной почте</b>:
                                <p><img alt="Окно Отправить по электронной почте" src="../images/sendtoemail.png" /></p>
                            <ul>
                                <li>В списке <b>От кого</b> выберите учетную запись электронной почты, которую вы хотите использовать для отправки писем, если у вас есть несколько подключенных учетных записей в модуле <b>Почта</b>.</li>
                                <li>В списке <b>Кому</b> выберите поле слияния, соответствующее адресам электронной почты получателей, если оно не было выбрано автоматически.</li>
                                <li>Введите тему сообщения в поле <b>Тема</b>.</li>
                                <li>Выберите из списка формат: <b>HTML</b>, <b>Прикрепить как DOCX</b> или <b>Прикрепить как PDF</b>. При выборе одной из двух последних опций необходимо также задать <b>Имя файла</b> для вложений и ввести <b>Сообщение</b> (текст письма, которое будет отправлено получателям).</li>
                                <li>Нажмите на кнопку <b>Отправить</b>.</li>
                            </ul>
                            <p>Когда рассылка завершится, вы получите оповещение на адрес электронной почты, указанный в поле <b>От кого</b>.</p>
                            </li>
                        </ul>
                    </li>
                </ol>
                </li>
            </ul>
		</div>
	</body>
</html>