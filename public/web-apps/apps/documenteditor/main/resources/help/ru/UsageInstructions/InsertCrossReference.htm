<!DOCTYPE html>
<html>
<head>
    <title>Вставка перекрестной ссылки</title>
    <meta charset="utf-8" />
    <meta name="description" content="Используйте перекрестные ссылки для создания ссылок, ведущих на другие части того же документа" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Вставка перекрестной ссылки</h1>
        <p>Перекрестные ссылки используются для создания ссылок, ведущих на другие части того же документа, например заголовки или объекты, такие как диаграммы или таблицы. Такие ссылки отображаются в виде гиперссылки.</p>
        <h2>Создание перекрестной ссылки</h2>
        <ol>
            <li>Поместите курсор в то место, куда вы хотите вставить перекрестную ссылку.</li>
            <li>Перейдите на вкладку <b>Ссылки</b> верхней панели инструментов и щелкните значок <div class = "icon icon-cross_refference_icon"></div><b>Перекрестнаяя ссылка</b>.</li>
            <li>
                В открывшемся окне <b>Перекрестная ссылка</b> задайте необходимые параметры:
                <p><img alt="Перекрестная ссылка окно" src="../images/cross_refference_window.png" /></p>
                <ul>
                    <li>В раскрывающемся списке <b>Тип ссылки</b> указывается элемент, на который вы хотите сослаться, т.е. нумерованный список (установлен по умолчанию), <em>заголовок, закладку, сноску, концевую сноску, уравнение, фигуру</em> или <em>таблицу</em>.</li>
                    <li>
                        В раскрывающемся списке <b>Вставить ссылку на</b> указывается текстовое или числовое значение ссылки, которую вы хотите вставить, в зависимости от элемента, выбранного в меню <b>Тип ссылки</b>. Например, если вы выбрали параметр <b>Заголовок</b>, вы можете указать следующее содержимое: <em>текст заголовка, номер страницы, номер заголовка, номер заголовка (краткий), номер заголовка (полный), Выше / ниже </em>.
                        <details class="details-example">
                            <summary>Полный список доступных опций в зависимости от выбранного типа ссылки</summary>
                            <table class="cross-reference">
                                <tr>
                                    <th>Тип ссылки</th>
                                    <th>Вставить ссылку на</th>
                                    <th style="text-align: left;">Описание</th>
                                </tr>
                                <tr>
                                    <td rowspan="6">Пронумерованный список</td>
                                    <td style="text-align: left;">Номер страницы</td>
                                    <td style="text-align: left;">Вставляет номер страницы пронумерованного элемента</td>
                                </tr>
                                <tr>
                                    <td style="text-align: left;">Номер абзаца</td>
                                    <td style="text-align: left;">Вставляет номер абзаца пронумерованного элемента</td>
                                </tr>
                                <tr>
                                    <td style="text-align: left;">Номер абзаца (краткий)</td>
                                    <td style="text-align: left;">Вставляет сокращенный номер абзаца. Ссылка делается только на конкретный элемент нумерованного списка, например, вместо «4.1.1» вы ссылаетесь только на «1»</td>
                                </tr>
                                <tr>
                                    <td style="text-align: left;">Номер абзаца (полный)</td>
                                    <td style="text-align: left;">Вставляет полный номер абзаца, например, «4.1.1»</td>
                                </tr>
                                <tr>
                                    <td style="text-align: left;">Текст абзаца</td>
                                    <td style="text-align: left;">Вставляет текстовое значение абзаца, например, абзац называется «4.1.1. Положения и условия», вы ссылаетесь только на «Положения и условия»</td>
                                </tr>
                                <tr>
                                    <td style="text-align: left;">Выше/ниже</td>
                                    <td style="text-align: left;">Вставляет текст «выше» или «ниже» в зависимости от положения элемента</td>
                                </tr>
                                <tr>
                                    <td rowspan="6">Заголовок</td>
                                    <td style="text-align: left;">Текст заголовка</td>
                                    <td style="text-align: left;">Вставляет весь текст заголовка</td>
                                </tr>
                                <tr>
                                    <td style="text-align: left;">Номер страницы</td>
                                    <td style="text-align: left;">Вставляет номер страницы заголовка</td>
                                </tr>
                                <tr>
                                    <td style="text-align: left;">Номер заголовка</td>
                                    <td style="text-align: left;">Вставляет порядковый номер заголовка</td>
                                </tr>
                                <tr>
                                    <td style="text-align: left;">Номер заголовка (краткий)</td>
                                    <td style="text-align: left;">Вставляет сокращенный номер заголовка. Убедитесь, что курсор находится в разделе, на который вы ссылаетесь, например, вы находитесь в разделе 4 и хотите сослаться на заголовок 4.B, поэтому вместо «4.B» результатом будет только «B»</td>
                                </tr>
                                <tr>
                                    <td style="text-align: left;">Номер заголовка (полный)</td>
                                    <td style="text-align: left;">Вставляет полный номер заголовка, даже если курсор находится в том же разделе</td>
                                </tr>
                                <tr>
                                    <td style="text-align: left;">Выше/ниже</td>
                                    <td style="text-align: left;">Вставляет текст «выше» или «ниже» в зависимости от положения элемента</td>
                                </tr>
                                <tr>
                                    <td rowspan="6">Закладка</td>
                                    <td style="text-align: left;">Текст закладки</td>
                                    <td style="text-align: left;">Вставляет весь текст закладки</td>
                                </tr>
                                <tr>
                                    <td style="text-align: left;">Номер страницы</td>
                                    <td style="text-align: left;">Вставляет номер страницы закладки</td>
                                </tr>
                                <tr>
                                    <td style="text-align: left;">Номер абзаца</td>
                                    <td style="text-align: left;">Вставляет номер абзаца закладки</td>
                                </tr>
                                <tr>
                                    <td style="text-align: left;">Номер абзаца (краткий)</td>
                                    <td style="text-align: left;">Вставляет сокращенный номер абзаца. Ссылка делается только на конкретный элемент, например, вместо «4.1.1» вы ссылаетесь только на «1».</td>
                                </tr>
                                <tr>
                                    <td style="text-align: left;">Номер абзаца (полный)</td>
                                    <td style="text-align: left;">Вставляет полный номер абзаца, например, «4.1.1»</td>
                                </tr>
                                <tr>
                                    <td style="text-align: left;">Выше/ниже</td>
                                    <td style="text-align: left;">Вставляет текст «выше» или «ниже» в зависимости от положения элемента</td>
                                </tr>
                                <tr>
                                    <td rowspan="4">Сноска</td>
                                    <td style="text-align: left;">Номер сноски</td>
                                    <td style="text-align: left;">Вставляет номер сноски</td>
                                </tr>
                                <tr>
                                    <td style="text-align: left;">Номер страницы</td>
                                    <td style="text-align: left;">Вставляет номер страницы сноски</td>
                                </tr>
                                <tr>
                                    <td style="text-align: left;">Выше/ниже</td>
                                    <td style="text-align: left;">Вставляет текст «выше» или «ниже» в зависимости от положения элемента</td>
                                </tr>
                                <tr>
                                    <td style="text-align: left;">Номер сноски (форм)</td>
                                    <td style="text-align: left;">Вставляет номер сноски, отформатированной как сноска. Нумерация реальных сносок не изменяется.</td>
                                </tr>
                                <tr>
                                    <td rowspan="4">Концевая сноска</td>
                                    <td style="text-align: left;">Номер коцневой сноски</td>
                                    <td style="text-align: left;">Вставляет номер концевой сноски</td>
                                </tr>
                                <tr>
                                    <td style="text-align: left;">Номер страницы</td>
                                    <td style="text-align: left;">Вставляет номер страницы концевой сноски</td>
                                </tr>
                                <tr>
                                    <td style="text-align: left;">Выше/ниже</td>
                                    <td style="text-align: left;">Вставляет текст «выше» или «ниже» в зависимости от положения элемента</td>
                                </tr>
                                <tr>
                                    <td style="text-align: left;">Номер коцневой сноски (форм)</td>
                                    <td style="text-align: left;">Вставляет номер концевой сноски в формате концевой сноски. Нумерация фактических примечаний не изменяется.</td>
                                </tr>
                                <tr>
                                    <td rowspan="5">Рисунок / Таблица / Уравнение</td>
                                    <td style="text-align: left;">Название целиком</td>
                                    <td style="text-align: left;">Вставляет полный текст</td>
                                </tr>
                                <tr>
                                    <td style="text-align: left;">Постоянная часть и номер</td>
                                    <td style="text-align: left;">Вставляет только название и номер объекта, например, «Таблица 1.1»</td>
                                </tr>
                                <tr>
                                    <td style="text-align: left;">Только текст названия</td>
                                    <td style="text-align: left;">Вставляет только текст названия</td>
                                </tr>
                                <tr>
                                    <td style="text-align: left;">Номер страницы</td>
                                    <td style="text-align: left;">Вставляет номер страницы, содержащей указанный объект</td>
                                </tr>
                                <tr>
                                    <td style="text-align: left;">Выше/ниже</td>
                                    <td style="text-align: left;">Вставляет текст «выше» или «ниже» в зависимости от положения элемента</td>
                                </tr>
                            </table>
                        </details>
                        <br />
                    </li>

                    <li>Установите галочку напротив <b>Вставить как гиперссылку</b>, чтобы превратить ссылку в активную ссылку.</li>
                    <li>Установите галочку напротив <b>Добавить слово "выше" или "ниже"</b> (если доступно), чтобы указать положение элемента, на который вы ссылаетесь. Редактор документов автоматически вставляет слова «<em>выше</em>» или «<em>ниже</em>» в зависимости от положения элемента.</li>
                    <li>Установите галочку напротив <b>Разделитель номеров</b>, чтобы указать разделитель в поле справа. Разделители необходимы для полных контекстных ссылок.</li>
                    <li>В поле <b>Для какого...</b> представлены элементы, доступные в соответствии с выбранным <b>типом ссылки</b>, например если вы выбрали вариант <b>Заголовок</b>, вы увидите полный список заголовков в документе.</li>
                </ul>
            </li>
            <li>Нажмите <b>Вставить</b>, чтобы создать перекрестную ссылку.</li>
        </ol>
        <h2>Удаление перекрестной ссылки</h2>
        <p>Чтобы удалить перекрестную ссылку, выберите перекрестную ссылку, которую вы хотите удалить, и нажмите кнопку <b>Удалить</b>.</p>
    </div>
</body>
</html>