<!DOCTYPE html>
<html>
	<head>
		<title>Изменение стиля обтекания текстом</title>
		<meta charset="utf-8" />
        <meta name="description" content="Измените стиль обтекания текстом, чтобы определить способ размещения объекта относительно текста." />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Изменение стиля обтекания текстом</h1>
            <p>Опция <b>Стиль обтекания</b> определяет способ размещения объекта относительно текста. Можно изменить стиль обтекания текстом для вставленных объектов, таких как <a href="../UsageInstructions/InsertAutoshapes.htm" onclick="onhyperlinkclick(this)">фигуры</a>, <a href="../UsageInstructions/InsertImages.htm" onclick="onhyperlinkclick(this)">изображения</a>, <a href="../UsageInstructions/InsertCharts.htm#" onclick="onhyperlinkclick(this)">диаграммы</a>, <a href="../UsageInstructions/InsertTextObjects.htm" onclick="onhyperlinkclick(this)">текстовые поля</a> или <a href="../UsageInstructions/InsertTables.htm" onclick="onhyperlinkclick(this)">таблицы</a>.</p>
            <h3>Изменение стиля обтекания текстом для фигур, изображений, диаграмм, текстовых полей</h3>
            <p>Для изменения выбранного в данный момент стиля обтекания:</p>
            <ol>
                <li>выделите отдельный объект на странице, щелкнув по нему левой кнопкой мыши. Чтобы выделить текстовое поле, щелкайте по его границе, а не по тексту внутри него.</li>
                <li>откройте настройки обтекания текстом:
                    <ul>
                        <li>перейдите на вкладку <b>Макет</b> верхней панели инструментов и нажмите на стрелку рядом со значком <div class = "icon icon-wrapping_toptoolbar"></div> <b>Обтекание</b> или</li>
                        <li>щелкните по объекту правой кнопкой мыши и выберите в контекстном меню пункт <b>Стиль обтекания</b> или</li>
                        <li>щелкните по объекту правой кнопкой мыши, выберите опцию <b>Дополнительные параметры</b> и перейдите на вкладку <b>Обтекание текстом</b> в окне <b>Дополнительные параметры</b> объекта.</li>
                    </ul>
                </li>
                <li>выберите нужный стиль обтекания:
                    <ul>
                        <li>
                            <p><span class = "icon icon-wrappingstyle_inline_toptoolbar"></span> <b>В тексте</b> - объект считается частью текста, как отдельный символ, поэтому при перемещении текста объект тоже перемещается. В этом случае параметры расположения недоступны.</p>
                            <p>Если выбран один из следующих стилей, объект можно перемещать независимо от текста и и точно задавать положение объекта на странице:</p>
                        </li>
                        <li><p><span class = "icon icon-wrappingstyle_square_toptoolbar"></span> <b>Вокруг рамки</b> - текст обтекает прямоугольную рамку, которая окружает объект.</p></li>
                        <li><p><span class = "icon icon-wrappingstyle_tight_toptoolbar"></span> <b>По контуру</b> - текст обтекает реальные контуры объекта.</p></li>
                        <li><p><span class="icon icon-wrappingstyle_through_toptoolbar"></span> <b>Сквозное</b> - текст обтекает вокруг контуров объекта и заполняет незамкнутое свободное место внутри объекта. Чтобы этот эффект проявился, используйте опцию <b>Изменить границу обтекания</b> из контекстного меню.</p></li>
                        <li><p><span class="icon icon-wrappingstyle_topandbottom_toptoolbar"></span> <b>Сверху и снизу</b> - текст находится только выше и ниже объекта.</p></li>
                        <li><p><span class="icon icon-wrappingstyle_infront_toptoolbar"></span> <b>Перед текстом</b> - объект перекрывает текст.</p></li>
                        <li><p><span class="icon icon-wrappingstyle_behind_toptoolbar"></span> <b>За текстом</b> - текст перекрывает объект.</p></li>
                    </ul>
                </li>
            </ol>
            <p>При выборе стиля обтекания <b>Вокруг рамки</b>, <b>По контуру</b>, <b>Сквозное</b> или <b>Сверху и снизу</b> можно задать дополнительные параметры - <b>Расстояние до текста</b> со всех сторон (сверху, снизу, слева, справа). Чтобы открыть эти настройки, щелкните по объекту правой кнопкой мыши, выберите опцию <b>Дополнительные параметры</b> и перейдите на вкладку <b>Обтекание текстом</b> в окне <b>Дополнительные параметры</b> объекта. Укажите нужные значения и нажмите кнопку <b>OK</b>.</p>
            <p>Если выбран стиль обтекания, отличный от стиля <b>В тексте</b>, в окне <b>Дополнительные параметры</b> объекта также становится доступна вкладка <b>Положение</b>. Для получения дополнительной информации об этих параметрах обратитесь к соответствующим страницам с инструкциями по работе с <a href="../UsageInstructions/InsertAutoshapes.htm#position" onclick="onhyperlinkclick(this)">фигурами</a>, <a href="../UsageInstructions/InsertImages.htm#position" onclick="onhyperlinkclick(this)">изображениями</a> или <a href="../UsageInstructions/InsertCharts.htm#position" onclick="onhyperlinkclick(this)">диаграммами</a>.</p>
            <p>Если выбран стиль обтекания, отличный от стиля <b>В тексте</b>, можно также редактировать контур обтекания для <b>изображений</b> или <b>фигур</b>. Щелкните по объекту правой кнопкой мыши, выберите в контекстном меню пункт <b>Стиль обтекания</b> и щелкните по опции <b>Изменить границу обтекания</b>. Вы также можете использовтаь опцию <b>Обтекание</b> -> <b>Изменить границу обтекания</b> на вкладке <b>Макет</b> верхней панели инструментов. Чтобы произвольно изменить границу, перетаскивайте точки границы обтекания. Чтобы создать новую точку границы обтекания, щелкните в любом месте на красной линии и перетащите ее в нужную позицию. <span class = "big big-wrap_boundary"></span></p>
            <h3>Изменение стиля обтекания текстом для таблиц</h3>
            <p>Для <a href="../UsageInstructions/InsertTables.htm" onclick="onhyperlinkclick(this)">таблиц</a> доступны два следующих стиля обтекания: <b>Встроенная таблица</b> и <b>Плавающая таблица</b>.</p>
            <p>Для изменения выбранного в данный момент стиля обтекания:</p>
            <ol>
                <li>щелкните по таблице правой кнопкой мыши и выберите пункт контекстного меню <b>Дополнительные параметры таблицы</b>,</li>
                <li>перейдите на вкладку <b>Обтекание текстом</b> окна <b>Таблица - дополнительные параметры</b></li>
                <li>
                    выберите одну из следующих опций:
                    <ul>
                        <li><b>Встроенная таблица</b> - используется для выбора стиля обтекания, при котором таблица разрывает текст, а также для настройки выравнивания: по левому краю, по центру, по правому краю.</li>
                        <li><b>Плавающая таблица</b> - используется для выбора стиля обтекания, при котором текст размещается вокруг таблицы.</li>
                    </ul>
                </li>
            </ol>
            <p>На вкладке <b>Обтекание текстом</b> окна <b>Таблица - дополнительные параметры</b> можно также задать следующие дополнительные параметры:</p>
            <ul>
                <li>Для встроенных таблиц можно задать тип <b>Выравнивания</b> таблицы (по левому краю, по центру или по правому краю) и <b>Отступ слева</b>.</li>
                <li>Для плавающих таблиц можно задать <b>Расстояние до текста</b> и <b>положение</b> на вкладке <a href="../UsageInstructions/InsertTables.htm#position" onclick="onhyperlinkclick(this)">Положение таблицы</a>.</li>
            </ul>
		</div>
	</body>
</html>