<!DOCTYPE html>
<html>
	<head>
		<title>Добавление закладок</title>
		<meta charset="utf-8" />
        <meta name="description" content="Закладки позволяют быстро перейти к определенному месту в текущем документе или добавить ссылку на эту позицию внутри документа." />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Добавление закладок</h1>
            <p>Закладки позволяют быстро перейти к определенному месту в текущем документе или добавить ссылку на эту позицию внутри документа.</p>
			<p>Чтобы добавить в документ закладку:</p>
			<ol>
                <li>определите место, где надо добавить закладку:
                    <ul>
                        <li>установите курсор в начале нужного фрагмента текста или</li>
                        <li>выделите нужный фрагмент текста,</li>
                    </ul>
                </li>
                <li>перейдите на вкладку <b>Ссылки</b> верхней панели инструментов,</li>
                <li>нажмите на значок <div class = "icon icon-bookmark"></div> <b>Закладка</b> на верхней панели инструментов,</li>
				<li>в открывшемся окне <b>Закладки</b> введите <b>Имя закладки</b> и нажмите кнопку <b>Добавить</b> - закладка будет добавлена в список закладок, расположенный ниже,
                    <p class="note"><b>Примечание</b>: имя закладки должно начинаться с буквы, но оно может также содержать цифры. Имя закладки не может содержать пробелы, но может содержать символ подчеркивания "_".</p>
                    <p><img alt="Окно Закладки" src="../images/bookmark_window.png" /></p>
                </li>
			</ol>
            <p>Чтобы перейти к одной из добавленных закладок в тексте документа:</p>
            <ol>
                <li>нажмите на значок <div class = "icon icon-bookmark"></div> <b>Закладка</b> на вкладке <b>Ссылки</b> верхней панели инструментов,</li>
                <li>в открывшемся окне <b>Закладки</b> выберите закладку, к которой надо перейти. Чтобы проще найти нужную закладку в списке, его можно сортировать по <b>Имени</b> закладки или по <b>Положению</b> закладки в тексте документа,</li>
                <li>отметьте опцию <b>Скрытые закладки</b>, чтобы отобразить в списке скрытые закладки (то есть закладки, автоматически создаваемые программой при добавлении ссылок на какую-то часть документа. Например, если вы создадите гиперссылку на определенный заголовок внутри документа, редактор документов автоматически создаст скрытую закладку на целевой объект этой ссылки).</li>
                <li>нажмите кнопку <b>Перейти</b> - курсор будет установлен в том месте документа, где добавлена данная закладка, или будет выделен соответствующий фрагмент текста,</li>
                <li>
                    нажмите кнопку <b>Получить ссылку</b> - откроется новое окно, в котором можно нажать кнопку <b>Копировать</b>, чтобы скопировать ссылку на файл, указывающую местоположение закладки в документе. Если вставить эту ссылку в адресную строку браузера и нажать Enter, документ откроется в том месте, где добавлена данная закладка.
                    <p><img alt="Окно Закладки" src="../images/bookmark_window2.png" /></p>
                    <p class="note"><b>Примечание</b>: если вы хотите поделиться этой ссылкой с другими пользователями, необходимо также предоставить соответствующие права доступа к файлу для определенных пользователей с помощью опции <b>Совместный доступ</b> на вкладке <b>Совместная работа</b>.</p>
                </li>
                <li>нажмите кнопку <b>Закрыть</b>, чтобы закрыть окно.</li>
            </ol>
            <p>Чтобы удалить закладку, выберите ее в списке закладок и используйте кнопку <b>Удалить</b>.</p>
            <p>Чтобы узнать, как использовать закладки при создании ссылок, обратитесь к разделу <a href="../UsageInstructions/AddHyperlinks.htm" onclick="onhyperlinkclick(this)">Добавление гиперссылок</a>.</p>

		</div>
	</body>
</html>