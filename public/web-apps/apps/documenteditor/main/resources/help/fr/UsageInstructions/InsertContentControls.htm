﻿<!DOCTYPE html>
<html>
	<head>
		<title>Insérer des contrôles de contenu</title>
		<meta charset="utf-8" />
        <meta name="description" content="Insérer des contrôles de contenu pour créer un formulaire avec des champs de saisie à remplir par d'autres utilisateurs, ou d'empêcher toute modification ou suppression des certains éléments du document." />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Rechercher" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Insérer des contrôles de contenu</h1>
            <p><a target="_blank" href="https://www.onlyoffice.com/fr/document-editor.aspx" onclick="onhyperlinkclick(this)"><b>Éditeur de Documents ONLYOFFICE</b></a> permet d'ajouter les contrôles de contenu <b>traditionnels</b>, c'est à dire les contrôles qui sont <b>entièrement rétrocompatible</b> avec les éditeurs alternatifs tels que Microsoft Word.</p>
            <p>Actuellement, l'Éditeur de Documents ONLYOFFICE prend en charge les contrôles de contenu traditionnels tels que <em>Texte brut</em>, <em>Texte enrichi</em>, <em>Image</em>, <em>Zone de liste déroulante</em>, <em>Liste déroulante</em>, <em>Date</em>, <em>Case à cocher</em>.</p>
            <ul>
                <li><em>Texte brut</em> est le texte comportant un objet qui ne peut pas être modifié. C'est seulement un paragraphe que le contrôle en texte brut peut contenir.</li>
                <li><em>Texte enrichi</em> est le texte comportant un objet qui peut être modifié. Les contrôles en texte enrichi peuvent contenir plusieurs paragraphes, listes et objets (images, formes, tableaux etc.).</li>
                <li><em>Image</em> est un objet comportant une image.</li>
                <li><em>Zone de liste déroulante</em> est un objet comportant une liste déroulante avec un ensemble de choix. Ce contrôle permet de choisir une valeur prédéfinie et la modifier au besoin.</li>
                <li><em>Liste déroulante</em> est un objet comportant une liste déroulante avec un ensemble de choix. Ce contrôle permet de choisir une valeur prédéfinie. La valeur choisie ne peut pas être modifiée.</li>
                <li><em>Date</em> est l'objet comportant le calendrier qui permet de choisir une date.</li>
                <li><em>Case à cocher</em> est un objet permettant d'afficher deux options : la case cochée et la case décochée.</li>
            </ul>
            <h3>Ajouter des contrôles de contenu</h3>
            <h4>Créer un nouveau contrôle de contenu de texte brut</h4>
            <ol>
                <li>positionnez le point d'insertion dans une ligne du texte où vous souhaitez ajouter le contrôle,<br />ou sélectionnez un passage de texte que vous souhaitez transformer en contrôle du contenu.</li>
                <li>passez à l'onglet <b>Insertion</b> de la barre d'outils supérieure.</li>
                <li>cliquez sur la flèche en regard de l'icône <div class = "icon icon-insertccicon"></div> <b>Contrôles de contenu</b>.</li>
                <li>choisissez l'option <b>Insérer un contrôle de contenu en texte brut</b> dans le menu.</li>
            </ol>
            <p>Le contrôle sera inséré au point d'insertion dans une ligne du texte existant. Tapez votre texte à remplacer du texte par défaut à l'intérieur du contrôle de contenu (Votre texte ici) : sélectionnez le texte par défaut et tapez du texte approprié ou copiez le texte que vous voulez et collez le à l'intérieur du contrôle de contenu. Les contrôles de contenu de texte brut ne permettent pas l'ajout de sauts de ligne et ne peuvent pas contenir d'autres objets tels que des images, des tableaux, etc.</p>
            <p><img alt="Nouveau contrôle du contenu de texte brut" src="../images/addedcontentcontrol.png" /></p>
            <h4>Créer un nouveau contrôle de contenu de texte enrichi</h4>
            <ol>
                <li>positionnez le point d'insertion dans une ligne du texte où vous souhaitez ajouter le contrôle,<br />ou sélectionnez un passage de texte que vous souhaitez transformer en contrôle du contenu.</li>
                <li>passez à l'onglet <b>Insertion</b> de la barre d'outils supérieure.</li>
                <li>cliquez sur la flèche en regard de l'icône <div class = "icon icon-insertccicon"></div> <b>Contrôles de contenu</b>.</li>
                <li>choisissez l'option <b>Insérer un contrôle de contenu en texte enrichi</b> dans le menu.</li>
            </ol>
            <p>Le contrôle sera inséré dans un nouveau paragraphe du texte. Tapez votre texte à remplacer du texte par défaut à l'intérieur du contrôle de contenu (Votre texte ici) : sélectionnez le texte par défaut et tapez du texte approprié ou copiez le texte que vous voulez et collez le à l'intérieur du contrôle de contenu. Les contrôles de contenu de texte enrichi permettent d'ajouter des sauts de ligne, c'est-à-dire peuvent contenir plusieurs paragraphes ainsi que certains objets, tels que des images, des tableaux, d'autres contrôles de contenu, etc.</p>
            <p><img alt="Contrôle de contenu de texte enrichi" src="../images/richtextcontentcontrol.png" /></p>
            <h4>Créer un nouveau contrôle de contenu d'image</h4>
            <ol>
                <li>positionnez le point d'insertion dans une ligne du texte où vous souhaitez ajouter le contrôle.</li>
                <li>passez à l'onglet <b>Insertion</b> de la barre d'outils supérieure.</li>
                <li>cliquez sur la flèche en regard de l'icône <div class = "icon icon-insertccicon"></div> <b>Contrôles de contenu</b>.</li>
                <li>choisissez l'option <b>Image</b> dans le menu et le contrôle de contenu sera inséré au point d'insertion.</li>
                <li>cliquez sur l'icône <div class = "icon icon-image_settings_icon"></div> Image dans le bouton au dessus de la bordure du contrôle de contenu, la fenêtre de sélection standard va apparaître. Choisissez l'image stockée sur votre ordinateur et cliquez sur <b>Ouvrir</b>.</li>
            </ol>
            <p>L'image choisie sera affichée à l'intérieur du contrôle de contenu. Pour remplacer l'image, cliquez sur l'icône d'image <span class = "icon icon-image_settings_icon"></span> dans le bouton au dessus de la bordure du contrôle de contenu et sélectionnez une autre image.</p>
            <p><span class = "big big-picturecontentcontrol"></span></p>
            <h4>Créer un nouveau contrôle de contenu Zone de liste déroulante ou Liste déroulante</h4>
            <p><em>Zone de liste déroulante</em> et la <em>Liste déroulante</em> sont des objets comportant une liste déroulante avec un ensemble de choix. Ces contrôles de contenu sont crées de la même façon. La différence entre les contrôles Zone de liste déroulante et Liste déroulante est que la liste déroulante propose des choix que vous êtes obligé de sélectionner tandis que la Zone de liste déroulante accepte la saisie d'un autre élément.</p>
            <ol>
                <li>positionnez le point d'insertion dans une ligne du texte où vous souhaitez ajouter le contrôle de contenu.</li>
                <li>passez à l'onglet <b>Insertion</b> de la barre d'outils supérieure.</li>
                <li>cliquez sur la flèche en regard de l'icône <div class = "icon icon-insertccicon"></div> <b>Contrôles de contenu</b>.</li>
                <li>choisissez l'option <b>Zone de liste déroulante</b> et <b>Liste déroulante</b> dans le menu et le contrôle de contenu sera inséré au point d'insertion.</li>
                <li>cliquez avec le bouton droit sur le contrôle de contenu ajouté et sélectionnez <b>Paramètres du contrôle de contenu</b> du menu contextuel.</li>
                <li>dans la fenêtre <b>Paramètres du contrôle de contenu</b> qui s'ouvre, passez à l'onglet <b>Zone de liste déroulante</b> ou <b>Liste déroulante</b> en fonction du type de contrôle de contenu sélectionné.
                <p><img alt="La fenêtre Paramètres de la Zone de liste déroulante" src="../images/comboboxsettings.png" /></p>
                </li>
                <li>
                    pour ajouter un nouveau élément à la liste, cliquez sur le bouton Ajouter et remplissez les rubriques disponibles dans la fenêtre qui s'ouvre :
                    <p><img alt=" Zone de liste déroulante - ajouter une valeur" src="../images/comboboxaddvalue.png" /></p>
                    <ol>
                        <li>saisissez le texte approprié dans le champ Nom d'affichage, par exemple, <em>Oui, Non, Autre</em> <em>option</em>. Ce texte sera affiché à l'intérieur du contrôle de contenu dans le document.</li>
                        <li>par défaut le texte du champ <b>Valeur</b> coïncide avec celui-ci du champ <b>Nom d'affichage</b>. Si vous souhaitez modifier le texte du champ Valeur, veuillez noter que la valeur doit être unique pour chaque élément. </li>
                        <li>Cliquez sur <b>OK</b>.</li>
                    </ol>
                </li>
                <li>les boutons <b>Modifier</b> et <b>Effacer </b>à droite servent à modifier ou supprimer les éléments de la liste et les boutons <b> En haut</b> et <b>Bas</b> à changer l'ordre d'affichage des éléments.</li>
                <li>Une fois les paramètres configurés, cliquez sur <b>OK</b> pour enregistrer la configuration et fermer la fenêtre.</li>
            </ol>
            <p><img alt="Nouveau contrôle du contenu de la zone de liste déroulante" src="../images/comboboxcontentcontrol.png" /></p>
            <p>Vous pouvez cliquez sur la flèche à droite du contrôle de contenu <b>Zone de liste déroulante</b> ou <b>Liste déroulante</b> ajouté pour ouvrir la liste et sélectionner l'élément approprié. Une fois sélectionné dans la <b>Zone de liste déroulante</b>, on peut modifier le texte affiché en saisissant propre texte partiellement ou entièrement. La Liste déroulante ne permet pas la saisie d'un autre élément.</p>
            <p><img alt="Le contrôle de contenu de la zone de liste déroulante" src="../images/comboboxcontentcontrol2.png" /></p>
            <h4>Créer un nouveau contrôle de contenu sélecteur des dates</h4>
            <ol>
                <li>positionnez le point d'insertion dans le texte où vous souhaitez ajouter le contrôle de contenu.</li>
                <li>passez à l'onglet <b>Insertion</b> de la barre d'outils supérieure.</li>
                <li>cliquez sur la flèche en regard de l'icône <div class = "icon icon-insertccicon"></div> <b>Contrôles de contenu</b>.</li>
                <li>choisissez l'option <b>Date</b> dans le menu et le contrôle de contenu indiquant la date actuelle sera inséré au point d'insertion.</li>
                <li>cliquez avec le bouton droit sur le contrôle de contenu ajouté et sélectionnez <b>Paramètres du contrôle de contenu</b> du menu contextuel.</li>
                <li>
                    dans la fenêtre <b>Paramètres du contrôle de contenu</b>, passez à l'onglet <b>Format de date</b>.
                    <p><img alt="Le fenêtre Paramètres de date" src="../images/datesettings.png" /></p>
                </li>
                <li>Sélectionnez la <b>Langue</b> et le format de date appropriée dans la liste <b>Afficher le date comme suit</b>.</li>
                <li>Cliquez sur <b>OK</b> pour appliquer toutes les modifications et fermer la fenêtre.</li>
            </ol>
            <p><span class = "big big-datecontentcontrol"></span></p>
            <p>Vous pouvez cliquez sur la flèche à droite du contrôle de contenu <b>Date</b> ajouté pour ouvrir le calendrier et sélectionner la date appropriée.</p>
            <p><span alt="Le contrôle de contenu de date" src="../images/datecontentcontrol2.png" /></p>
            <h4>Créer un nouveau contrôle de contenu de case à cocher</h4>
            <ol>
                <li>positionnez le point d'insertion dans le texte où vous souhaitez ajouter le contrôle de contenu.</li>
                <li>passez à l'onglet <b>Insertion</b> de la barre d'outils supérieure.</li>
                <li>cliquez sur la flèche en regard de l'icône <div class = "icon icon-insertccicon"></div> <b>Contrôles de contenu</b>.</li>
                <li>choisissez l'option <b>Case à cocher</b> dans le menu et le contrôle de contenu sera inséré au point d'insertion.</li>
                <li>cliquez avec le bouton droit sur le contrôle de contenu ajouté et sélectionnez <b>Paramètres du contrôle de contenu</b> du menu contextuel.</li>
                <li>
                    dans la fenêtre <b>Paramètres du contrôle de contenu</b>, passez à l'onglet <b>Case à cocher</b>.
                    <p><img alt="La fenêtre Paramètres de la case à cocher" src="../images/checkboxsettings.png" /></p>
                </li>
                <li>cliquez sur le bouton <b>Symbole Activé</b> pour spécifier le symbole indiquant la case cochée ou le <b>Symbole Désactivé</b> pour spécifier la façon d'afficher la case décochée. La fenêtre <b>Symbole</b> s'ouvre. Veuillez consulter <a href="../UsageInstructions/InsertSymbols.htm" onclick="onhyperlinkclick(this)">cet article</a> pour en savoir plus sur utilisation des symboles.</li>
                <li>Une fois les paramètres configurés, cliquez sur <b>OK</b> pour enregistrer la configuration et fermer la fenêtre.</li>
            </ol>
            <p>La case à cocher s'affiche désactivée.</p>
            <p><span class = "icon icon-checkboxcontentcontrol"></span></p>
            <p>Une fois que vous cliquiez la case à cocher, le symbole qu'on a spécifié comme le <b>Symbole Activé</b> est inséré.</p>
            <p><span class = "icon icon-checkboxcontentcontrol2"></span></p>
            
            <p class="note"><b>Remarque</b> : La bordure du contrôle de contenu est visible uniquement lorsque le contrôle est sélectionné. Les bordures n'apparaissent pas sur une version imprimée.</p>

            <h3>Déplacer des contrôles de contenu</h3>
            <p>Les contrôles peuvent être déplacés à un autre endroit du document : cliquez sur le bouton à gauche de la bordure de contrôle pour sélectionner le contrôle et faites-le glisser sans relâcher le bouton de la souris à un autre endroit dans le texte du document.</p>
            <p><img alt="Déplacer des contrôles de contenu" src="../images/movecontentcontrol.png" /></p>
            <p>Vous pouvez également <b>copier et coller</b> des contrôles de contenu : sélectionnez le contrôle voulu et utilisez les combinaisons de touches <b>Ctrl+C/Ctrl+V</b>.</p>
            
            <h3>Modifier des contrôles de contenu en texte brut et en texte enrichi</h3>
            <p>On peut modifier le texte à l'intérieur des contrôles de contenu en texte brut et en texte enrichi  à l'aide des icônes de la barre d'outils supérieure : vous pouvez ajuster <a href="../UsageInstructions/FontTypeSizeColor.htm" onclick="onhyperlinkclick(this)">le type, la taille et la couleur de police</a>, appliquer des <a href="../UsageInstructions/DecorationStyles.htm" onclick="onhyperlinkclick(this)">styles de décoration</a> et <a href="../UsageInstructions/FormattingPresets.htm" onclick="onhyperlinkclick(this)">les configurations de mise en forme</a>. Il est également possible d'utiliser la fenêtre <b>Paragraphe - Paramètres avancés</b> accessible depuis le menu contextuel ou depuis la barre latérale de droite pour modifier les propriétés du texte. Le texte contenu dans les contrôles de contenu de texte enrichi peut être mis en forme comme un texte normal du document, c'est-à-dire que vous pouvez définir <a href="../UsageInstructions/LineSpacing.htm" onclick="onhyperlinkclick(this)">l'interlignage</a>, modifier <a href="../UsageInstructions/ParagraphIndents.htm" onclick="onhyperlinkclick(this)"> les retraits de paragraphe</a>, ajuster <a href="../UsageInstructions/SetTabStops.htm" onclick="onhyperlinkclick(this)"> les taquets de tabulation</a>, etc.</p>
            
            <h3>Modification des paramètres de contrôle du contenu</h3>
            <p>Quel que soit le type du contrôle de contenu, on peut configurer ses paramètres sous les onglets <b>Général</b> et <b>Verrouillage</b> dans la fenêtre <b>Paramètres du contrôle de contenu</b>.</p>
            <p>Pour ouvrir les paramètres de contrôle du contenu, vous pouvez procéder de la manière suivante :</p>
            <ul>
                <li>Sélectionnez le contrôle de contenu nécessaire, cliquez sur la flèche en regard de l'icône <div class = "icon icon-insertccicon"></div> <b>Contrôles de contenu</b>  dans la barre d'outils supérieure et sélectionnez l'option <b>Paramètres de contrôle</b> du menu.</li>
                <li>Cliquez avec le bouton droit n'importe où dans le contrôle de contenu et utilisez l'option <b>Paramètres de contrôle du contenu</b> dans le menu contextuel.</li>
            </ul>
            <p>Une nouvelle fenêtre s'ouvrira. Sous l'onglet <b>Général</b> vous pouvez configurer les paramètres suivants :</p>
            <p><img alt="Fenêtre des Paramètres de contrôle du contenu - Général" src="../images/ccsettingswindow.png" /></p>
            <ul>
                <li>Spécifiez le <b>Titre</b>, l'<b>Espace réservé</b> ou le <b>Tag</b> dans les champs correspondants. Le titre s'affiche lorsque le contrôle est sélectionné dans le document. L'espace réservé est le texte principal qui s'affiche à l'intérieur du contrôle de contenu. Les Tags sont utilisées pour identifier les contrôles de contenu afin que vous puissiez y faire référence dans votre code. </li>
                <li>Choisissez si vous voulez afficher le contrôle de contenu avec une <b>Boîte d'encombrement</b> ou non. Utilisez l'option <b>Aucun</b> pour afficher le contrôle sans aucune boîte d'encombrement. Si vous sélectionnez l'option <b>Boîte d'encombrement</b>, vous pouvez choisir la <b>Couleur</b> de la boîte à l'aide du champ ci-dessous. Cliquez sur le bouton <b>Appliquer à tous</b> pour appliquer les paramètres d'<b>Apparence</b> spécifiés à tous les contrôles de contenu du document.</li>
            </ul>
            <p>Sous l'onglet <b>Verrouillage</b> vous pouvez empêchez toute suppression ou modification du contrôle de contenu en utilisant les paramètres suivants :</p>
            <p><img alt="Fenêtre des Paramètres de contrôle du contenu - Verrouillage" src="../images/ccsettingswindow2.png" /></p>
                <ul>
                    <li><b>Le contrôle du contenu ne peut pas être supprimé</b> - cochez cette case pour empêcher la suppression du contrôle de contenu.</li>
                    <li><b>Le contenu ne peut pas être modifié</b> - cochez cette case pour protéger le contenu du contrôle de contenu contre une modification.</li>
                </ul>  
                <p>Sous le troisième onglet on peut configurer les paramètres spécifiques de certain type du contrôle de contenu, comme : <em>Zone de liste déroulante, Liste déroulante, Date, Case à cocher</em>. Tous ces paramètres ont déjà été décrits ci-dessus dans la section appropriée à chaque contrôle de contenu.</p>          
            <p>Cliquez sur le bouton <b>OK</b> dans la fenêtre des paramètres pour appliquer les changements.</p>
            <p>Il est également possible de surligner les contrôles de contenu avec une certaine couleur. Pour surligner les contrôles avec une couleur :</p>
            <ol>
                <li>Cliquez sur le bouton situé à gauche de la bordure du champ pour sélectionner le contrôle,</li>
                <li>Cliquez sur la flèche à côté de l'icône <div class = "icon icon-insertccicon"></div> <b>Contrôles de contenu</b> dans la barre d'outils supérieure,</li>
                <li>Sélectionnez l'option <b>Paramètres de surbrillance</b> du menu,</li>
                <li>Sélectionnez la couleur souhaitée dans les palettes disponibles : <b>Couleurs du thème</b>, <b>Couleurs standard</b> ou spécifiez une nouvelle <b>Couleur personnalisée</b>. Pour supprimer la surbrillance des couleurs précédemment appliquée, utilisez l'option <b>Pas de surbrillance</b>.</li>
            </ol>
            <p>Les options de surbrillance sélectionnées seront appliquées à tous les contrôles de contenu du document.</p>
            <h3>Supprimer des contrôles de contenu</h3>
            <p>Pour supprimer un contrôle et laisser tout son contenu, cliquez sur le contrôle de contenu pour le sélectionner, puis procédez de l'une des façons suivantes :</p>
            <ul>
                <li>Cliquez sur la flèche en regard de l'icône <div class = "icon icon-insertccicon"></div> <b>Contrôles de contenu</b> dans la barre d'outils supérieure et sélectionnez l'option <b>Supprimer le contrôle du contenu</b> dans le menu.</li>
                <li>Cliquez avec le bouton droit sur le contrôle de contenu et utilisez l'option <b>Supprimer le contrôle du contenu</b> dans le menu contextuel.</li>
            </ul>
            <p>Pour supprimer un contrôle et tout son contenu, sélectionnez le contrôle nécessaire et appuyez sur la touche <b>Suppr</b> du clavier.</p>
                        
		</div>
	</body>
</html>