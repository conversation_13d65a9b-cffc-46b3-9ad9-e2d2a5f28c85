﻿<!DOCTYPE html>
<html>
	<head>
		<title>Appliquer les styles de police</title>
		<meta charset="utf-8" />
		<meta name="description" content="Appliquer les styles de police : augmentation/diminution, gras, italique, souligné, barré, exposant/indice" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Rechercher" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Appliquer les styles de police</h1>
			<p>Dans l'<a target="_blank" href="https://www.onlyoffice.com/fr/document-editor.aspx" onclick="onhyperlinkclick(this)"><b>Éditeur de Documents</b></a>, vous pouvez appliquer différents styles de police à l'aide des icônes correspondantes situées dans l'onglet <b>Accueil</b> de la barre d'outils supérieure.</p>
			<p class="note"><b>Remarque</b> : si vous voulez appliquer la mise en forme au texte déjà saisi, sélectionnez-le avec la souris ou <a href="../HelpfulHints/KeyboardShortcuts.htm#textselection" onclick="onhyperlinkclick(this)">en utilisant le clavier</a> et appliquez la mise en forme.</p>
			<table>
				<tr>
					<td>Gras</td>
					<td><div class = "icon icon-bold"></div></td>
					<td>Sert à mettre la police en gras pour lui donner plus de poids.</td>
				</tr>
				<tr>
					<td>Italique</td>
					<td><div class = "icon icon-italic"></div></td>
					<td>Sert à mettre la police en italique pour lui donner une certaine inclinaison à droite.</td>
				</tr>
				<tr>
					<td>Souligné</td>
					<td><div class = "icon icon-underline"></div></td>
					<td>Sert à souligner le texte avec la ligne qui passe sous les lettres.</td>
				</tr>
				<tr>
					<td>Barré</td>
					<td><div class = "icon icon-strike"></div></td>
					<td>Sert à barrer le texte par la ligne passant par les lettres.</td>
				</tr>
				<tr>
					<td>Exposant</td>
					<td><div class = "icon icon-sup"></div></td>
					<td>Sert à rendre le texte plus petit et le déplacer vers la partie supérieure de la ligne du texte, par exemple comme dans les fractions.</td>
				</tr>
				<tr>
					<td>Indice</td>
					<td><div class = "icon icon-sub"></div></td>
					<td>Sert à rendre le texte plus petit et le déplacer vers la partie inférieure de la ligne du texte, par exemple comme dans les formules chimiques.</td>
				</tr>
			</table>
			<p>Pour accéder aux paramètres avancés de la police, cliquez avec le bouton droit de la souris et sélectionnez l'option <b>Paramètres avancés du paragraphe</b> du menu contextuel ou utilisez le lien <b>Afficher les paramètres avancés</b> sur la barre latérale droite. Dans la fenêtre <b>Paragraphe - Paramètres avancés</b> ouverte passez à l'onglet <b>Police</b>.</p>
			<p>Ici vous pouvez utiliser les styles de décoration de police et les paramètres suivants :</p>
			<ul>
				<li><b>Barré</b> sert à barrer le texte par la ligne passant par les lettres.</li>
				<li><b>Barré double</b> sert à barrer le texte par la ligne double passant par les lettres.</li>
				<li><b>Exposant</b> sert à rendre le texte plus petit et le déplacer vers la partie supérieure de la ligne du texte, par exemple comme dans les fractions.</li>
				<li><b>Indice</b> sert à rendre le texte plus petit et le déplacer vers la partie inférieure de la ligne du texte, par exemple comme dans les formules chimiques.</li>
				<li><b>Petites majuscules</b> sert à mettre toutes les lettres en petite majuscule.</li>
				<li><b>Majuscules</b> sert à mettre toutes les lettres en majuscule.</li>
				<li><b>Espacement</b> sert à définir l'espace entre les caractères. Augmentez la valeur par défaut pour appliquer l'espacement <b>Étendu</b>, ou diminuez la valeur par défaut pour appliquer l'espacement <b>Condensé</b>. Utilisez les touches fléchées ou entrez la valeur voulue dans la case.</li>
                <li><b>Position</b> permet de définir la position des caractères (décalage vertical) dans la ligne. Augmentez la valeur par défaut pour déplacer les caractères vers le haut ou diminuez la valeur par défaut pour les déplacer vers le bas. Utilisez les touches fléchées ou entrez la valeur voulue dans la case.</li>
                <li><b>Ligatures</b> sont des lettres jointes d'un mot tapé dans l'une des polices OpenType. Veuillez noter que l'utilisation de ligatures peut perturber l'espacement des caractères. Les options de ligatures disponibles sont :
				<ul>
					<li><em>Aucune</em></li>
					<li><em>Standards uniquement</em> (inclut “fi”, “fl”, “ff” ; améliore la lisibilité)</li>
					<li><em>Contextuelles</em> (ligatures sont appliquées en fonction des lettres environnantes ; améliore la lisibilité)</li>
					<li><em>Historiques</em> (ligatures ont plus de descentes et lignes courbées ; réduit la lisibilité)</li>
					<li><em>Discrétionnaires</em> (ligatures ornementales ; réduit la lisibilité)</li>
					<li><em>Standards et contextuelles</em></li>
					<li><em>Standards et historiques</em></li>
					<li><em>Contextuelles et historiques</em></li>
					<li><em>Standards et discrétionnaires</em></li>
					<li><em>Contextuelles et discrétionnaires</em></li>
					<li><em>Historiques et discrétionnaires</em></li>
					<li><em>Standards, contextuelles et historiques</em></li>
					<li><em>Standards, contextuelles et discrétionnaires</em></li>
					<li><em>Standards, historiques et discrétionnaires</em></li>
					<li><em>Contextuelles, historiques et discrétionnaires</em></li>
					<li><em>Toutes</em></li>
				</ul>
                <p>Tous les changements seront affichés dans le champ de prévisualisation ci-dessous.</p>
                </li>
			</ul>
			<p><img alt="Paramètres avancés du paragraphe - Police" src="../images/paradvsettings_font.png" /></p>
		</div>
	</body>
</html>