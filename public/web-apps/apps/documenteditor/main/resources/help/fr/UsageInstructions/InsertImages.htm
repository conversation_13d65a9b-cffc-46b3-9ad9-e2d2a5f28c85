﻿<!DOCTYPE html>
<html>
	<head>
		<title>Insérer des images</title>
		<meta charset="utf-8" />
		<meta name="description" content="Insérer des images et configurer la position et les paramètres." />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Rechercher" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Insérer des images</h1>
            <p><a target="_blank" href="https://www.onlyoffice.com/fr/document-editor.aspx" onclick="onhyperlinkclick(this)"><b>Éditeur de Documents</b></a> vous permet d'insérer des images aux formats populaires. Les formats d'image pris en charge sont les suivants : <b>BMP, GIF, JPEG, JPG, PNG</b>.</p>
            <h3>Insérer une image</h3>
            <p>Pour insérer une image dans votre document de texte,</p>
			<ol>
				<li>placez le curseur là où vous voulez insérer l'image,</li>
                <li>passez à l'onglet <b>Insérer</b> de la barre d'outils supérieure,</li>
				<li>cliquez sur l'icône <div class = "icon icon-image"></div> <b>Image</b> de la barre d'outils supérieure,</li>
				<li>sélectionnez l'une des options suivantes pour charger l'image :
					<ul>
                        <li>l'option <b>Image à partir d'un fichier</b> ouvre la fenêtre de dialogue standard pour sélectionner le fichier. Sélectionnez le fichier de votre choix sur le disque dur de votre ordinateur et cliquez sur le bouton <b>Ouvrir</b>
                            <p class="note">Dans <em>l’éditeur en ligne</em>, vous pouvez sélectionner plusieurs images à la fois.</p>
                        </li>
                        <li>l'option<b> Image à partir d'une URL</b> ouvre la fenêtre où vous pouvez saisir l'adresse Web de l'image et cliquer sur le bouton <b>OK</b></li>
                        <li class="onlineDocumentFeatures"> l'option <b>Image de stockage</b> ouvrira la fenêtre<b> Sélectionner la source de données</b>. Sélectionnez une image stockée sur votre portail et cliquez sur le bouton <b>OK</b></li>
					</ul>
				</li>
                <li>après avoir ajouté l'image, vous pouvez modifier sa taille, ses paramètres et sa position.</li>
			</ol>
            <p>On peut aussi ajouter une légende à l'image. Veuillez consulter <a href="../UsageInstructions/AddCaption.htm" onclick="onhyperlinkclick(this)">cet article</a> pour en savoir plus sur utilisation des légendes.</p>
            <h3>Déplacer et redimensionner des images</h3>
            <p><span class="big big-moving_image"></span></p>
			<p>Pour changer la taille de l'image, faites glisser les petits carreaux  <span class = "icon icon-resize_square"></span> situés sur ses bords. Pour garder les proportions de l'image sélectionnée lors du redimensionnement, maintenez la touche <b>Shift</b> enfoncée et faites glisser l'une des icônes de coin.</p>
			<p>Pour modifier la position de l'image, utilisez l'icône <span class = "icon icon-arrow"></span> qui apparaît si vous placez le curseur de votre souris sur l'image. Faites glisser l'image vers la position choisie sans relâcher le bouton de la souris.</p>
            <p>Lorsque vous déplacez l'image, des lignes de guidage s'affichent pour vous aider à la positionner sur la page avec précision (si un style d'habillage autre que aligné est sélectionné).</p>
			<p>Pour faire pivoter une image, déplacer le curseur vers la poignée de rotation <span class = "icon icon-greencircle"></span> et faites la glisser  dans le sens horaire ou antihoraire. Pour faire pivoter par incréments de 15 degrés, maintenez enfoncée la touche <b>Shift</b> tout en faisant pivoter.</p>
            <p class="note">
                <b>Remarque</b> : la liste des raccourcis clavier qui peuvent être utilisés lorsque vous travaillez avec des objets est disponible <a href="../HelpfulHints/KeyboardShortcuts.htm#workwithobjects" onclick="onhyperlinkclick(this)">ici</a>.
            </p>
            <hr />
            <h3>Ajuster les paramètres de l'image</h3>
            <p><img alt="L'onglet Paramètres de l'image" src="../images/right_image.png" /></p>
            <p>Certains paramètres de l'image peuvent être modifiés en utilisant l'onglet <b>Paramètres de l'image </b>de la barre latérale droite. Pour l'activer, cliquez sur l'image et sélectionnez l'icône <b>Paramètres de l'image </b><span class="icon icon-image_settings_icon"></span> à droite. Vous pouvez y modifier les paramètres suivants :</p>
			<ul>
                <li><b>Taille</b> est utilisée pour afficher la <b>Largeur</b> et la <b>Hauteur</b> de l'image actuel. Si nécessaire, vous pouvez restaurer la taille d'origine de l'image en cliquant sur le bouton Taille actuelle.  Le bouton <b>Ajuster aux marges</b> permet de redimensionner l'image et de l'ajuster dans les marges gauche et droite.
                    <p>Le bouton <b>Rogner</b> sert à recadrer l'image. Cliquez sur le bouton <b>Rogner</b> pour activer les poignées de recadrage qui appairaient par chaque coin et sur les côtés. Faites glisser manuellement les pognées pour définir la zone de recadrage. Vous pouvez positionner le curseur sur la bordure de la zone de recadrage  lorsque il se transforme en <span class = "icon icon-arrow"></span> et faites la glisser. </p>
                    <ul>
                        <li>Pour rogner un seul côté, faites glisser la poignée située au milieu de ce côté.</li>
                        <li>Pour rogner simultanément deux côtés adjacents, faites glisser l'une des poignées d'angle.</li>
                        <li>Pour rogner également deux côtés opposés de l'image, maintenez la touche <em>Ctrl</em> enfoncée lorsque vous faites glisser la poignée au milieu de l'un de ces côtés. </li>
                        <li>Pour rogner également tous les côtés de l'image, maintenez la touche <em>Ctrl</em> enfoncée lorsque vous faites glisser l'une des poignées d'angle.</li>
                    </ul>
                    <p>Lorsque la zone de recadrage est définie, cliquez à nouveau sur le bouton <b>Rogner</b>, ou appuyez sur la touche <I>Echap</I>, ou cliquez n'importe où à l'extérieur de la zone de recadrage pour appliquer les modifications.</p>
                    <p>Une fois la zone de recadrage sélectionnée, il est également possible d'utiliser les options <b>Rogner à la forme</b>, <b>Remplir</b> ou <b>Ajuster</b> disponibles dans le menu déroulant <b>Rogner</b>. Cliquez de nouveau sur le bouton <b>Rogner</b> et sélectionnez l'option de votre choix : </p>
                    <ul>
                        <li>Si vous sélectionnez l'option <b>Rogner à la forme</b>, l'image va s'ajuster à une certaine forme. Vous pouvez sélectionner la forme appropriée dans la galerie qui s'affiche lorsque vous placez le pointeur de la souris sur l'option <b>Rogner à la forme</b>. Vous pouvez toujours utiliser les options <b>Remplir</b> et <b>Ajuster</b> pour choisir le façon d'ajuster votre image à la forme.</li>
                        <li>Si vous sélectionnez l'option <b>Remplir</b>, la partie centrale de l'image originale sera conservée et utilisée pour remplir la zone de cadrage sélectionnée, tandis que les autres parties de l'image seront supprimées.</li>
                        <li>Si vous sélectionnez l'option <b>Ajuster</b>, l'image sera redimensionnée pour correspondre à la hauteur ou à la largeur de la zone de recadrage. Aucune partie de l'image originale ne sera supprimée, mais des espaces vides peuvent apparaître dans la zone de recadrage sélectionnée.</li>
                    </ul>
                </li>
                <li><b>Rotation</b> permet de faire pivoter l'image de 90 degrés dans le sens des aiguilles d'une montre ou dans le sens inverse des aiguilles d'une montre, ainsi que de retourner l'image horizontalement ou verticalement. Cliquez sur l'un des boutons :
                    <ul>
                        <li><div class = "icon icon-rotatecounterclockwise"></div> pour faire pivoter l'image de 90 degrés dans le sens inverse des aiguilles d'une montre</li>
                        <li><div class = "icon icon-rotateclockwise"></div> pour faire pivoter l'image de 90 degrés dans le sens des aiguilles d'une montre</li>
                        <li><div class = "icon icon-fliplefttoright"></div> pour retourner l'image horizontalement (de gauche à droite)</li>
                        <li><div class = "icon icon-flipupsidedown"></div> pour retourner l'image verticalement (à l'envers)</li>
                    </ul>
                </li>
                <li><b>Style d'habillage</b> sert à sélectionner un des styles d'habillage - aligné sur le texte, carré, rapproché, au travers, haut et bas, devant le texte, derrière le texte (pour en savoir plus, consultez la section des paramètres avancés ci-dessous).</li>
                <li><b>Remplacer l'image</b> sert à remplacer l'image actuelle et charger une autre <b>À partir d'un fichier</b> ou <b>À partir d'une URL</b>.</li>
			</ul>
            <p>Certains paramètres de l'image peuvent être également modifiés en utilisant le <b>menu contextuel</b>. Les options du menu sont les suivantes :</p>
			<ul>
                <li><b>Couper</b>, <b>Copier</b>, <b>Coller</b> - les options nécessaires pour couper ou coller le texte / l'objet sélectionné et coller un passage de texte précédemment coupé / copié ou un objet à la position actuelle du curseur.</li>
                <li><b>Organiser</b> sert à placer l'image sélectionnée au premier plan, envoyer à fond, avancer ou reculer ainsi que grouper ou dégrouper des images pour effectuer des opérations avec plusieurs images à la fois. Pour en savoir plus sur l'organisation des objets, vous pouvez vous référer à <a href="../UsageInstructions/AlignArrangeObjects.htm" onclick="onhyperlinkclick(this)">cette page</a>.</li>
                <li><b>Aligner</b> sert à aligner le texte à gauche, au centre, à droite, en haut, au milieu, en bas. Pour en savoir plus sur l'organisation des objets, vous pouvez vous référer à <a href="../UsageInstructions/AlignArrangeObjects.htm" onclick="onhyperlinkclick(this)">cette page</a>.</li>
                <li><b>Style d'habillage</b> sert à sélectionner un des styles d'habillage - aligné sur le texte, carré, rapproché, au travers, haut et bas, devant le texte, derrière le texte - ou modifier le contour de l'habillage. L'option <b>Modifier les limites du renvoi</b> <b>à la ligne</b> n'est disponible qu'au cas où vous sélectionnez le style d'habillage autre que 'aligné sur le texte'. Faites glisser les points d'habillage pour personnaliser les limites. Pour créer un nouveau point d'habillage, cliquez sur la ligne rouge et faites-la glisser vers la position désirée. <div class = "big big-wrap_boundary"></div></li>
				<li><b>Faire pivoter</b> permet de faire pivoter l'image de 90 degrés dans le sens des aiguilles d'une montre ou dans le sens inverse des aiguilles d'une montre, ainsi que de retourner l'image horizontalement ou verticalement.</li>
                <li><b>Rogner</b> est utilisé pour appliquer l'une des options de rognage : <b>Rogner</b>, <b>Remplir</b> ou <b>Ajuster</b>. Sélectionnez l'option <b>Rogner</b> dans le sous-menu, puis faites glisser les poignées de recadrage pour définir la zone de rognage, puis cliquez à nouveau sur l'une de ces trois options dans le sous-menu pour appliquer les modifications.</li>
                <li><b>Taille actuelle</b> sert à changer la taille actuelle de l'image et rétablir la taille d'origine.</li>
                <li><b>Remplacer l'image</b> sert à remplacer l'image actuelle et charger une autre <b>À partir d'un fichier</b> ou <b>À partir d'une URL</b>.</li>
				<li><b>Paramètres avancés de l'image</b> sert à ouvrir la fenêtre 'Image - Paramètres avancés'.</li>
			</ul>
            <p><img alt="L'onglet Paramètres de la forme" src="../images/right_image_shape.png" /></p> 
            <p>Lorsque l'image est sélectionnée, l'icône <b>Paramètres de la forme</b> <span class="icon icon-shape_settings_icon"></span> est également disponible sur la droite. Vous pouvez cliquer sur cette icône pour ouvrir l'onglet <b>Paramètres de la forme </b>dans la barre latérale droite et ajuster le type du <a href="../UsageInstructions/InsertAutoshapes.htm#shape_stroke" onclick="onhyperlinkclick(this)"><b>Ligne</b></a> a taille et la couleur ainsi que le type de forme en sélectionnant une autre forme dans le menu <b>Modifier la forme automatique</b>. La forme de l'image changera en conséquence.</p>
            <p>Sous l'onglet <b>Paramètres de la forme</b>, vous pouvez utiliser l'option <b>Ajouter une ombre</b> pour créer une zone ombrée autour de l'image.</p>
			<hr />
            <h3>Ajuster les paramètres de l'image</h3>
			<p>Pour modifier les paramètres avancés, cliquez sur l'image avec le bouton droit de la souris et sélectionnez <b>Paramètres avancés de l'image</b> du menu contextuel ou cliquez sur le lien de la barre latérale droite <b>Afficher les paramètres avancés</b>. La fenêtre paramètres de l'image s'ouvre :</p>
			<p><img alt="La fenêtre Image - Paramètres avancés Taille" src="../images/image_properties.png" /></p>
			<p>L'onglet <b>Taille</b> comporte les paramètres suivants :</p>
			<ul>
				<li><b>Largeur</b> et <b>Hauteur</b> - utilisez ces options pour changer la largeur et/ou la hauteur. Lorsque le bouton <b>Proportions constantes</b> <div class = "icon icon-constantproportions"></div> est activé (Ajouter un ombre <div class = "icon icon-constantproportionsactivated"></div>), le rapport largeur/hauteur d'origine s'ajuste proportionnellement. Pour rétablir la taille par défaut de l'image ajoutée, cliquez sur le bouton <b>Taille actuelle</b>.</li>
			</ul>
            <p><img alt="La fenêtre Image - Paramètres avancés Rotation" src="../images/image_properties_4.png" /></p>
            <p>L'onglet <b>Rotation</b> comporte les paramètres suivants :</p>
            <ul>
                <li><b>Angle</b> - utilisez cette option pour faire pivoter l'image d'un angle exactement spécifié. Entrez la valeur souhaitée mesurée en degrés dans le champ ou réglez-la à l'aide des flèches situées à droite. </li>
                <li><b>Retourné</b> - cochez la case <b>Horizontalement</b> pour retourner l'image horizontalement (de gauche à droite) ou la case <b>Verticalement</b> pour retourner l'image verticalement (à l'envers).</li>
            </ul>
			<p><img alt="La fenêtre Image - Paramètres avancés Habillage du texte" src="../images/image_properties_1.png" /></p>
			<p>L'onglet <b>Habillage du texte</b> contient les paramètres suivants :</p>
			<ul>
				<li><b>Style d'habillage</b> - utilisez cette option pour changer la manière dont l'image est positionnée par rapport au texte : elle peut faire partie du texte (si vous sélectionnez le style 'aligné sur le texte') ou être contournée par le texte de tous les côtés (si vous sélectionnez l'un des autres styles).
				<ul>
				    <li><p><div class = "icon icon-wrappingstyle_inline"></div> <b>En ligne</b> - l'image fait partie du texte, comme un caractère, ainsi si le texte est déplacé, le graphique est déplacé lui aussi. Dans ce cas-là les options de position ne sont pas accessibles.</p>
				    <p>Si vous sélectionnez un des styles suivants, vous pouvez déplacer l'image indépendamment du texte et définir sa position exacte :</p>
				    </li>
				    <li><p><div class = "icon icon-wrappingstyle_square"></div> <b>Carré</b> - le texte est ajusté autour des bords de l'image.</p></li>
				    <li><p><div class = "icon icon-wrappingstyle_tight"></div> <b>Rapproché</b> - le texte est ajusté sur le contour de l'image.</p></li>
				    <li><p><div class = "icon icon-wrappingstyle_through"></div> <b>Au travers</b> - le texte est ajusté autour des bords du graphique et occupe l'espace vide à l'intérieur de celui-ci. Pour créer l'effet, utilisez l'option <b>Modifier les limites du renvoi</b> à la ligne du menu contextuel.</p></li>
				    <li><p><div class = "icon icon-wrappingstyle_topandbottom"></div> <b>Haut et bas</b> - le texte est ajusté en haut et en bas de l'image.</p></li>
				    <li><p><div class = "icon icon-wrappingstyle_infront"></div> <b>Devant le texte</b> - l'image est affichée sur le texte</p></li>
				    <li><p><div class = "icon icon-wrappingstyle_behind"></div> <b>Derrière le texte</b> - le texte est affiché sur l'image.</p></li>
				    </ul>
				</li>
			</ul>
			<p>Si vous avez choisi le style carré, rapproché, au travers, haut et bas, vous avez la possibilité de configurer des paramètres supplémentaires - <b>Distance du texte</b> de tous les côtés (haut, bas, gauche, droit).</p>
            <p id="position"><img alt="La fenêtre Image - Paramètres avancés Position" src="../images/image_properties_2.png" /></p>
            <p>L'onglet <b>Position</b> n'est disponible qu'au cas où vous choisissez le style d'habillage autre que 'aligné sur le texte'. Il contient les paramètres suivants qui varient selon le type d'habillage sélectionné :</p>
			<ul>
                <li>
                    La section <b>Horizontal</b> vous permet de sélectionner l'un des trois types de positionnement d'image suivants :
                    <ul>
                        <li><b>Alignement</b> (gauche, centre, droite) <b>par rapport au</b> caractère, à la colonne, à la marge de gauche, à la marge, à la page ou à la marge de droite,</li>
                        <li><b>Position</b> <b>absolue</b> mesurée en unités absolues, c'est-à-dire <b>Centimètres/Points/Pouces</b> (selon l'option spécifiée dans l'onglet <b>Fichier</b> -> <b>Paramètres avancés</b>...) <b>à droite du</b> caractère, de la colonne, de la marge de gauche, de la marge, de la page ou de la marge de droite,</li>
                        <li><b>Position relative</b> mesurée en pourcentage <b>par rapport à</b> la marge gauche, à la marge, à la page ou à la marge de droite.</li>
                    </ul>
                </li>
                <li>
                    La section <b>Vertical</b> vous permet de sélectionner l'un des trois types de positionnement d'image suivants :
                    <ul>
                        <li><b>Alignement</b> (haut, centre, bas) <b>par rapport à</b> la ligne, à la marge, à la marge inférieure, au paragraphe, à la page ou à la marge supérieure,</li>
                        <li><b>Position absolue</b> mesurée en unités absolues, c'est-à-dire <b>Centimètres/Points/Pouces</b> (selon l'option spécifiée dans l'onglet <b>Fichier</b> -> <b>Paramètres avancés...</b>) au-dessous de la ligne, de la marge, de la marge inférieure, du paragraphe, de la page ou la marge supérieure,</li>
                        <li><b>Position relative</b> mesurée en pourcentage <b>par rapport à</b> la marge, à la marge inférieure, à la page ou à la marge supérieure.</li>
                    </ul>
                </li>
                <li><b>Déplacer avec le texte</b> détermine si l'image se déplace en même temps que le texte sur lequel il est aligné.</li>
                <li><b>Chevauchement</b> détermine si deux images sont fusionnées en une seule ou se chevauchent si vous les faites glisser les unes près des autres sur la page.</li>
			</ul>
            <p><img alt="La fenêtre Image - Paramètres avancés" src="../images/image_properties_3.png" /></p>
            <p>L'onglet <b>Texte de remplacement</b> permet de spécifier un <b>Titre</b> et une <b>Description</b> qui sera lue aux personnes avec des déficiences cognitives ou visuelles pour les aider à mieux comprendre l'information de l'image.</p>
		</div>
	</body>
</html>