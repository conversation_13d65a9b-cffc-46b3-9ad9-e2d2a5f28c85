﻿<!DOCTYPE html>
<html>
	<head>
		<title>Sélectionner la couleur d'arrière-plan pour un paragraphe</title>
		<meta charset="utf-8" />
		<meta name="description" content="Apprenez à sélectionner la couleur d'arrière-plan pour un paragraphe" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Rechercher" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Sélectionner la couleur d'arrière-plan pour un paragraphe</h1>
			<p>Dans <a target="_blank" href="https://www.onlyoffice.com/fr/document-editor.aspx" onclick="onhyperlinkclick(this)"><b>Éditeur de Documents</b></a>, la couleur d'arrière-plan est appliquée au paragraphe entier et remplit complètement l'espace du paragraphe de la marge de page gauche à la marge de page droite.</p>
			<p>Pour appliquer la couleur d'arrière-plan au paragraphe particulier ou changer la couleur actuelle,</p>
			<ol>
				<li>sélectionnez un jeu de couleurs pour votre document à partir des modèles disponibles en cliquant sur l'icône <b>Modifier le jeu de couleurs</b> <div class = "icon icon-changecolorscheme"></div> dans l'onglet <b>Accueil</b> de la barre d'outils supérieure</li>
				<li>placez le curseur dans le paragraphe choisi, ou sélectionnez plusieurs paragraphes avec la souris ou le texte entier en utilisant la combinaison de touches <b>Ctrl+A</b></li>
				<li>ouvrez la fenêtre des palettes de couleurs. Vous pouvez l'accéder par une des façons suivantes :
                <ul>
				<li>cliquez sur la flèche vers le bas à côté de l'icône <div class = "icon icon-backgroundcolor"></div> dans l'onglet <b>Accueil</b> de la barre d'outils supérieure, ou</li>
				<li>utilisez le champ de couleurs à côté de la légende <b>Сouleur d'arrière plan</b> sur la barre latérale droite,</li>
				<li>cliquez sur le lien 'Afficher les paramètres avancés' sur la barre latérale droite ou sélectionnez l'option 'Paramètres avancés du paragraphe' dans le menu contextuel, puis passez à l'onglet 'Bordures et remplissage' dans la fenêtre 'Paragraphe - Paramètres avancés' et cliquez sur le champ de couleurs à coté de la légende <b>Couleur d'arrière-plan</b>.</li>
				</ul>
				</li>
				<li>choisissez une couleur dans les <a href="../UsageInstructions/ChangeColorScheme.htm" onclick="onhyperlinkclick(this)">palettes</a> disponibles</li>
			</ol>
			<p>Après avoir sélectionné la couleur voulue à l'aide de l'icône <span class = "icon icon-backgroundcolor"></span>, vous pourrez appliquer cette couleur à n’importe quel paragraphe sélectionné. Pour le faire, cliquez sur l'icône <span class = "icon icon-backgroundcolor_selected"></span> (elle affiche la couleur sélectionnée), sans avoir à choisir cette couleur dans la palette encore une fois. Si vous utilisez l'option <b>Couleur d'arrière plan</b> sur la barre de droite ou dans la fenêtre 'Paragraphe - Paramètres avancés', n'oubliez pas que la couleur sélectionnée n'est pas conservée pour l'accéder rapidement. (Ces options peuvent être utiles si vous souhaitez sélectionner une couleur d’arrière-plan différente pour un paragraphe spécifique, lors de l'utilisation de la couleur de base sélectionnée à l'aide l'icône <span class = "icon icon-backgroundcolor"></span>).</p>
			<hr />
			<p>Pour effacer la couleur d'arrière d'un paragraphe particulier,</p>
			<ol>
				<li>placez le curseur dans le paragraphe choisi, ou sélectionnez plusieurs paragraphes avec la souris ou le texte entier en utilisant la combinaison de touches <b>Ctrl+A</b></li>
				<li>en cliquant sur le champ de couleur à côté de la légende <b>Сouleur d'arrière plan</b> sur la barre latérale droite, ouvrez la fenêtre des palettes de couleurs</li>
				<li>sélectionnez l'icône <div class = "icon icon-nofill"></div>.</li>
			</ol>
		</div>
	</body>
</html>