﻿<!DOCTYPE html>
<html>
	<head>
		<title>Onglet Mise en page - ONLYOFFICE</title>
		<meta charset="utf-8" />
        <meta name="description" content="Découvrez l'onglet Mise en page et apprenez comment modifier l'apparence des fichiers Word, paramétrer des pages et positionner des objets  visuels sous cet onglet." />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Rechercher" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Onglet Mise en page</h1>
            <p>L'onglet <b>Mise en page</b> dans l'<a href="https://www.onlyoffice.com/fr/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)">Éditeur de Documents</a> permet de modifier l'apparence du document : configurer les paramètres de la page et définir la mise en page des éléments visuels.</p>
            <div class="onlineDocumentFeatures">
                <p>Fenêtre de l'Éditeur de Documents en ligne :</p>
                <p><img alt="Onglet Mise en page" src="../images/interface/layouttab.png" /></p>
            </div>
            <div class="desktopDocumentFeatures">
                <p>Fenêtre de l'Éditeur de Documents de bureau :</p>
                <p><img alt="Onglet Mise en page" src="../images/interface/desktop_layouttab.png" /></p>
            </div>
            <p>En utilisant cet onglet, vous pouvez :</p>
            <ul>
                <li>paramétrer <a href="../UsageInstructions/SetPageParameters.htm#margins" onclick="onhyperlinkclick(this)">les marges</a>, <a href="../UsageInstructions/SetPageParameters.htm#orientation" onclick="onhyperlinkclick(this)">l'orientation</a> et <a href="../UsageInstructions/SetPageParameters.htm#size" onclick="onhyperlinkclick(this)">la taille</a> de la page,</li>
                <li>ajouter <a href="../UsageInstructions/SetPageParameters.htm#columns" onclick="onhyperlinkclick(this)">des colonnes</a>,</li>
                <li>insérer <a href="../UsageInstructions/PageBreaks.htm" onclick="onhyperlinkclick(this)">des sauts de page</a>, <a href="../UsageInstructions/SectionBreaks.htm" onclick="onhyperlinkclick(this)">des sauts de section</a> et <a href="../UsageInstructions/SetPageParameters.htm#columns" onclick="onhyperlinkclick(this)">des sauts de colonne</a>,</li>
                <li>insérer <a href="../UsageInstructions/InsertLineNumbers.htm" onclick="onhyperlinkclick(this)">des numéros des lignes</a></li>
                <li>aligner et grouper des objets (<a href="../UsageInstructions/InsertTables.htm" onclick="onhyperlinkclick(this)">des tableaux</a>, <a href="../UsageInstructions/InsertImages.htm" onclick="onhyperlinkclick(this)">des images</a>, <a href="../UsageInstructions/InsertCharts.htm" onclick="onhyperlinkclick(this)">des graphiques</a>, <a href="../UsageInstructions/InsertAutoshapes.htm" onclick="onhyperlinkclick(this)">des formes</a>),</li>
                <li>modifier <a href="../UsageInstructions/ChangeWrappingStyle.htm" onclick="onhyperlinkclick(this)">le retour à la ligne</a> et modifier les limites de renvoi à la ligne,</li>
                <li>ajouter un <a href="../UsageInstructions/AddWatermark.htm" onclick="onhyperlinkclick(this)">filigrane</a>.</li>
            </ul>
		</div>
	</body>
</html>