﻿<!DOCTYPE html>
<html>
<head>
    <title>Communiquer lors de l'édition</title>
    <meta charset="utf-8" />
    <meta name="description" content="Dans l'Éditeur de Documents ONLYOFFICE vous pouvez toujours rester en contact avec vos collègues et utiliser des messageries en ligne populaires, par exemple Telegram et Rainbow" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Communiquer lors de l'édition</h1>
        <p>Dans l'<a href="https://www.onlyoffice.com/fr/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Éditeur de Documents ONLYOFFICE</b></a> vous pouvez toujours rester en contact avec vos collègues et utiliser des messageries en ligne populaires, par exemple Telegram et Rainbow.</p>
        <div class="note">Les plug-ins Telegram et Rainbow ne sont pas installés par défaut. Pour en savoir plus sur leur installation, veuillez consulter l'article approprié : <span class="desktopDocumentFeatures"><a target="_blank" href="https://helpcenter.onlyoffice.com/fr/installation/desktop-getting-started.aspx#AddingPlugins_block" onclick="onhyperlinkclick(this)">Ajouter des modules complémentaires à ONLYOFFICE Desktop Editors</a></span><span class="onlineDocumentFeatures">, <a target="_blank" href="https://api.onlyoffice.com/plugin/installation/cloud" onclick="onhyperlinkclick(this)">Adding plugins to ONLYOFFICE Cloud</a> ou <a target="_blank" href="https://helpcenter.onlyoffice.com/fr/installation/docs-add-plugin.aspx" onclick="onhyperlinkclick(this)">Ajouter de nouveaux modules complémentaires aux éditeurs de serveur</a></span>.</div>
        <h2>Telegram</h2>
        <p>Pour commencer à chatter dans le plug-in Telegram,</p>
        <ul>
            <li>Passez à l'onglet <b>Modules complémentaires</b> et cliquez sur <div class = "icon icon-telegram_icon"></div> <b>Telegram</b>,</li>
            <li>saisissez votre numéro de téléphone dans le champ correspondant,</li>
            <li>cochez la case <b>Rester connecté</b> lorsque vous souhaitez enregistrer vos données pour la session en cours, ensuite cliquez sur le bouton <b>Suivant</b>,</li>
            <li>
                saisissez le code reçu dans votre application Telegram,
                <p>ou</p>
            </li>
            <li>connectez-vous en utilisant le <b>Code QR</b>,</li>
            <li>ouvrez l'application Telegram sur votre téléphone,</li>
            <li>passez à Paramètres > Appareils > Numériser QR,</li>
            <li>numérisez l'image pour vous connecter.</li>
        </ul>
        <p>Vous pouvez maintenant utiliser Telegram au sein de l'interface des éditeurs ONLYOFFICE.</p>
        <img alt="Telegram gif" src="../../images/telegram.gif" />
        <h2>Rainbow</h2>
        <p>Pour commencer à chatter dans le plug-in Rainbow,</p>
        <ol>
            <li>Passez à l'onglet <b>Modules complémentaires</b> et cliquez sur <div class = "icon icon-rainbow_icon"></div> <b>Rainbow</b>,</li>
            <li>enregistrez un nouveau compte en cliquant sur le bouton <b>Inscription</b> ou connectez-vous à un compte déjà créé. Pour le faire, saisissez votre email dans le champ correspondant et cliquez sur <b>Continuer</b>,</li>
            <li>puis saisissez le mot de passe de votre compte,</li>
            <li>cochez la case <b>Maintenir ma session</b> lorsque vous souhaitez enregistrer vos données pour la session en cours, ensuite cliquez sur le bouton <b>Connecter</b>.</li>
        </ol>
        <p>Vous êtes maintenant prêt à chatter dans Rainbow et travailler au sein de l'interface des éditeurs ONLYOFFICE en même temps.</p>
        <img alt="Rainbow gif" src="../../images/rainbow.gif" />
    </div>
</body>
</html>