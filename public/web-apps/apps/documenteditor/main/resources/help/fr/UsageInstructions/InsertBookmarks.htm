﻿<!DOCTYPE html>
<html>
	<head>
		<title>Ajouter des signets</title>
		<meta charset="utf-8" />
        <meta name="description" content="Signets permettent de passer rapidement passer rapidement à une position marquée par un signet ou d'ajouter un lien vers ce point dans le document." />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Rechercher" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Ajouter des signets</h1>
            <p>Les signets permettent d’aller rapidement à une certaine position dans le document en cours ou d'ajouter un lien vers cette position dans le document.</p>
			<p>Pour ajouter un signet dans l'<a target="_blank" href="https://www.onlyoffice.com/fr/document-editor.aspx" onclick="onhyperlinkclick(this)"><b>Éditeur de Documents</b></a> :</p>
			<ol>
                <li>spécifiez l'endroit où vous voulez que le signet soit ajouté :
                <ul>
                    <li>placez le curseur de la souris au début du passage de texte nécessaire, ou</li>
                    <li>sélectionnez le passage de texte nécessaire,</li>
                </ul>
                </li>
                <li>passez à l'onglet <b>Références</b> de la barre d'outils supérieure,</li>
                <li>cliquez sur l'icône <div class = "icon icon-bookmark"></div> <b>Signet</b> de la barre d'outils supérieure,</li>
				<li>dans la fenêtre <b>Signets</b> qui s'ouvre, entrez le <b>Nom du signet</b> et cliquez sur le bouton <b>Ajouter</b> - un signet sera ajouté à la liste des signets affichée ci-dessous,
                    <p class="note"><b>Note</b> : le nom du signets doit commencer par une lettre, mais il peut aussi contenir des chiffres. Le nom du signets ne peut pas contenir d'espaces, mais peut inclure le caractère de soulignement "_".</p>
                    <p><img alt="Fenêtre Signets" src="../images/bookmark_window.png" /></p>
                </li>
			</ol>
            <p>Pour accéder à un des signets ajoutés dans le texte du document :</p>
            <ol>
                <li>cliquez sur l'icône <div class = "icon icon-bookmark"></div> <b>Signet</b> dans l'onglet <b>Références</b> de la barre d'outils supérieure,</li>
                <li>dans la fenêtre <b>Signets</b> qui s'ouvre, sélectionnez le signet vers lequel vous voulez vous déplacer. Pour trouver facilement le signet voulu dans la liste, vous pouvez trier la liste par <b>Nom</b> ou par <b>Emplacement</b> de signets dans le texte du document,</li>
                <li>cochez l'option <b>Marque-pages cachés</b> pour afficher les signets cachés dans la liste (c'est-à-dire les signets automatiquement créés par le programme lors de l'ajout de références à une certaine partie du document. Par exemple, si vous créez un lien hypertexte vers un certain titre dans le document, l'éditeur de document crée automatiquement un signet caché vers la cible de ce lien).</li>
                <li>cliquez sur le bouton <b>Aller à</b> - le curseur sera positionné à l'endroit du document où le signet sélectionné a été ajouté, ou le passage de texte correspondant sera sélectionné,</li>
                <li>
                    cliquez sur le bouton <b>Obtenir le lien</b> - une nouvelle fenêtre va apparaître dans laquelle vous pouvez appuyer sur <b>Copier</b> pour copier le lien vers le fichier spécifiant la position du signet référencé. Lorsque vous collez le lien dans la barre d'adresse de votre navigateur et appuyez sur la touche Entrée, le document s'ouvre à l'endroit où le signet est ajouté.
                    <p><img alt="Bookmarks window" src="../images/bookmark_window2.png" /></p>
                    <p class="note"><b>Remarque</b> : si vous voulez partager un lien avec d'autres personnes, vous devez définir les autorisations d’accès en utilisant l'option <b>Partage</b> soul l'onglet <b>Collaboration</b>.</p>
                </li>
                <li>cliquez sur le bouton <b>Fermer</b> pour fermer la fenêtre.</li>
            </ol>
            <p>Pour supprimer un signet, sélectionnez-le dans la liste et cliquez sur le bouton <b>Supprimer</b>.</p>
            <p>Pour savoir comment utiliser les signets lors de la création de liens, veuillez consulter la section <a href="../UsageInstructions/AddHyperlinks.htm" onclick="onhyperlinkclick(this)">Ajouter des liens hypertextes</a>.</p>

		</div>
	</body>
</html>