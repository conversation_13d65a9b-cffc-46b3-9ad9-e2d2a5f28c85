﻿<!DOCTYPE html>
<html>
<head>
    <title>Compter les mots</title>
    <meta charset="utf-8" />
    <meta name="description" content="La description du plug-in Compter les mots pour les éditeurs ONLYOFFICE qui permet de compter les mots, caractères et paragraphes du texte" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Compter les mots</h1>
        <p>Pour connaître le nombre exact de mots et de symboles avec et sans espaces dans votre document, ainsi que le nombre total de paragraphes, utilisez le plug-in Compter les mots.</p>
        <ol>
            <li>Ouvrez l'onglet <b>Modules complémentaires</b> et cliquez sur <b>Compter les mots et les caractères</b>.</li>
            <li>Sélectionnez une partie du texte ou le texte entier.</li>
        </ol>
        <div class="note">Veuillez noter que les éléments suivants ne sont pas inclus dans le nombre total de mots :
        <ul>
            <li>caractères dans les notes de bas de page/notes de fin,</li>
            <li>numéros des listes numérotées,</li>
            <li>numéros des pages.</li>
        </ul>
        </div>
        <img class="gif" alt="Word counter plugin gif" src="../../images/wordcounter_plugin.gif" width="600" />
        <p>Pour plus d'informations sur le plug-in Compter les mots et son installation, veuillez consulter la page de <a href="https://www.onlyoffice.com/fr/app-directory/word-counter">plug-in</a> dans AppDirectory.</p>
    </div>
</body>
</html>