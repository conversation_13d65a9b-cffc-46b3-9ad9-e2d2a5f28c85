﻿<!DOCTYPE html>
<html>
<head>
    <title>Onglet Formulaires</title>
    <meta charset="utf-8" />
    <meta name="description" content="Présentation de l'interface de l'Éditeur de Documents - l'onglet Formulaires" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Rechercher" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Onglet Formulaires</h1>
        <p class="note"><b>Remarque</b>: cet ongle est disponible uniquement avec des fichiers au format DOCXF.</p>
        <p>L'onglet <b>Formulaires</b> permet de créer des formulaires à remplir dans votre document, par ex. les projets de contrats ou les enquêtes. Ajoutez, mettez en forme et paramétrez votre texte et des champs de formulaire aussi complexe soit-il.</p>
        <div class="onlineDocumentFeatures">
            <p>La fenêtre de l'onglet dans Document Editor en ligne:</p>
            <p><img alt="Onglet Formulaires" src="../images/interface/formstab.png" /></p>
         </div>
         <div class="desktopDocumentFeatures">
            <p>La fenêtre de l'onglet dans Document Editor de bureau:</p>
            <p><img alt="Onglet Formulaires" src="../images/interface/desktop_formstab.png" /></p>
        </div>
        <p>En utilisant cet onglet, vous pouvez:</p>
        <ul>
            <li>
                <a href="../UsageInstructions/CreateFillableForms.htm">ajouter et modifier</a>
                <ul>
                    <li> <a href="../UsageInstructions/CreateFillableForms.htm#textfield">des champs texte</a></li>
                    <li> <a href="../UsageInstructions/CreateFillableForms.htm#combobox">des zones de liste déroulante,</a></li>
                    <li> <a href="../UsageInstructions/CreateFillableForms.htm#dropdownlist">listes déroulantes</a></li>
                    <li> <a href="../UsageInstructions/CreateFillableForms.htm#checkbox">des cases à cocher</a></li>
                    <li> <a href="../UsageInstructions/CreateFillableForms.htm#radiobutton">des boutons radio,</a></li>
                    <li> <a href="../UsageInstructions/CreateFillableForms.htm#image">des champs image</a></li>
                    <li> <a href="../UsageInstructions/CreateFillableForms.htm#emailaddress">adresses e-mail</a></li>
                    <li> <a href="../UsageInstructions/CreateFillableForms.htm#phonenumber">numéros de téléphone</a></li>
                    <li> <a href="../UsageInstructions/CreateFillableForms.htm#datetime">date et heure</a></li>
                    <li> <a href="../UsageInstructions/CreateFillableForms.htm#zipcode">code postales</a></li>
                    <li> <a href="../UsageInstructions/CreateFillableForms.htm#creditcard">numéros de carte bancaire</a></li>
                    <li> <a href="../UsageInstructions/CreateFillableForms.htm#complexfield">champs complexes</a></li>
                </ul>
            <li>effacer tous les champs et paramétrer la surbrillance,</li>
            <li>naviguer entre les champs du formulaire en utilisant les boutons <b>Champ précédent</b> et <b>Champ suivant</b>,</li>
            <li>afficher un aperçu du formulaire final,</li>
            <li><a href="../UsageInstructions/CreateFillableForms.htm#managing_roles">gérer des rôles</a>,</li>
            <li>enregistrer le formulaire en tant que formulaire remplissable au format OFORM.</li>
        </ul>
    </div>
</body>
</html>