﻿<!DOCTYPE html>
<html>
<head>
    <title>Remplir un formulaire</title>
    <meta charset="utf-8" />
    <meta name="description" content="Fill out a Form template" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Rechercher" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Remplir un formulaire</h1>
        <p>Un formulaire à remplir est le fichier au format OFORM. OFORM est un format de fichier destiné à remplir ; à télécharger ou à imprimer des modèles de formulaires une fois que vous avez terminé de le remplir.</p> 
            <h2>Comment remplir un formulaire :</h2>
                <ol>
                    <li>
                        Ouvrez le formulaire au format OFORM.
                    <p><img alt="oform" src="../images/oform.png" /><p>
                    </li>
                    <li>Remplissez tous les champs obligatoires. La bordure des champs obligatoires est rouge. Utilisez <div class="icon icon-arrows_formfilling"></div> <b>Champ précédent</b> ou <div class="icon icon-next_formfilling"></div> <b>Champ suivant</b> dans la barre d'outils supérieure pour naviguer entre les champs ou cliquez sur le champ à remplir.
<li>Utilisez le bouton <b>Effacer tous les champs</b> <img alt="Effacer les champs remplis" src="../images/clearall_formfilling.png" /> pour vider tous les champs de saisie.</li>
                    <li>Une fois tous les champs remplis, cliquez sur <b>Enregistrer comme PDF</b> pour sauvegarder le formulaire sur votre ordinateur en tant que fichier PDF.</li> 
                    <li>
                        Cliquer sur <div class = "icon icon-morebutton"></div> dans le coin droit de la barre d'outils supérieure pour accéder aux options supplémentaires. Vous pouvez <b>Imprimer</b>, <b>Télécharger en tant que docx</b> ou <b>Télécharger en tant que pdf</b>.
                        <p><img alt="Plus OFORM" src="../images/more_oform.png" /></p>
                        <p>Il est même possible de modifier le thème d'<b>interface</b> en choisissant <b>Identique à système</b>, <b>Claire</b>, <b>Classique claire</b>, <b>Sombre</b> ou <b>Contraste sombre</b>. Une fois le thème d'interface <b>Sombre</b> ou <b>Contraste sombre</b> activé, l'option <b>Mode sombre</b> devient disponible.</p>
                        <p><img alt="Mode sombre" src="../images/darkmode_oform.png" /></p>
                        <p><b>Zoom</b> permet de mettre à l'échelle et de redimensionner la page en utilisant des options <b>Ajuster à la page</b>, <b>Ajuster à la largeur</b> et l'outil pour régler le niveau de <b>Zoom</b> :</p>
                        <p><img alt="Zoom" src="../images/pagescaling.png" /></p>
                        <ul>
                            <li><b>Ajuster à la page</b> sert à redimensionner la page pour afficher une page entière sur l'écran.</li>
                            <li><b>Ajuster à la largeur</b> sert à redimensionner la page pour l'adapter à la largeur de l'écran.</li>
                            <li>Outil <b>Zoom</b> sert à zoomer et dézoomer sur une page.</li>
                        </ul>
                        <p><b>Ouvrir l'emplacement de fichier</b> lorsque vous avez besoin d'accéder le dossier où le fichier est stocké.</p>

                    </li>
                 </ol>
     </div>
</body>
</html>