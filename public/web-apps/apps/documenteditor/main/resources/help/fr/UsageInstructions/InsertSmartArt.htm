﻿<!DOCTYPE html>
<html>
	<head>
        <title>Insérer des graphiques SmartArt</title>
		<meta charset="utf-8" />
        <meta name="description" content="Insert SmartArt objects"/>
        <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Rechercher" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Insérer des graphiques SmartArt</h1>
            <p>Un graphique <b>SmartArt</b> sert à créer une représentation visuelle de la structure hiérarchique en choisissant le type du graphique qui convient le mieux. Insérez des graphiques SmartArt ou modifiez des graphiques SmartArt qui ont été créés dans des logiciels d'éditeurs tiers.</p>
            <p>Pour ajouter un  graphiques SmartArt,</p>
            <ol>
                <li>passez à l'onglet <b>Insertion,</b></li>
                <li>cliquez sur le bouton <b>SmartArt</b>,</li>
                <li>placez le pointeur sur l'un des styles de mise en page disponibles, par ex. <em>Liste</em> ou <em>Processus</em>.</li>
                <li>sélectionnez le type de mise en page de la liste qui apparaît à droite de l'option du menu mis en surbrillance.</li>
            </ol>
            <p>Vous pouvez personnaliser la configuration SmartArt sur le panneau à droite:</p>
            <p class="note">Veuillez noter qu'il est possible de personnaliser les paramètres de couleur, style et forme.</p>
            <img alt="" src="../images/smartart_settings.png" />
            <ul>
                <li>
                    <b>Remplissage</b> - utilisez cette section pour sélectionner le remplissage du graphique SmartArt. Les options disponibles sont les suivantes:
                    <ul>
                        <li>
                            <b>Couleur de remplissage</b> - sélectionnez cette option pour spécifier la couleur unie à utiliser pour remplir l'espace intérieur du  graphique SmartArt.
                            <p><img alt="Couleur de remplissage" src="../images/fill_color.png" /></p>
                            <p id="color">Cliquez sur la case de couleur et sélectionnez la couleur voulue à partir de <a href="../UsageInstructions/ChangeColorScheme.htm" onclick="onhyperlinkclick(this)">l'ensemble de couleurs</a> disponibles ou spécifiez n'importe quelle couleur de votre choix:</p>
                        </li>
                        <li>
                            <b>Remplissage en dégradé</b> - sélectionnez cette option pour spécifier deux couleurs pour créer une transition douce entre elles et remplir la forme. Personnaliser votre dégradé sans aucune contrainte. Cliquez sur l'icône <b>Paramètres de la forme</b>. <div class="icon icon-shape_settings_icon"></div> pour ouvrir le menu <b>Remplissage</b> de la barre latérale sur la droite:
                            <p><img alt="Remplissage en dégradé" src="../images/fill_gradient.png" /></p>
                            <p>Les options disponibles du menu:</p>
                            <ul>
                                <li>
                                    <b>Style</b> - choisissez <b>Linéaire</b> or <b>Radial</b>:
                                    <ul>
                                        <li><b>Linéaire</b> sert à remplir par un dégradé de gauche à droite, de bas en haut ou sous l'angle partant en direction définie. La fenêtre d'aperçu <b>Direction</b> affiche la couleur de dégradé sélectionnée, cliquez sur la flèche pour définir la direction du dégradé. Utilisez les paramètres <b>Angle</b> pour définir un angle précis du dégradé.</li>
                                        <li>Radial sert à remplir par un dégradé de forme circulaire entre le point de départ et le point d'arrivée.</li>
                                    </ul>
                                </li>
                                <li>
                                    <b>Point de dégradé</b> est le point d'arrêt de d'une couleur et de la transition entre les couleurs.
                                    <ul>
                                        <li>Utilisez le bouton <div class="icon icon-addgradientpoint"></div> <b>Ajouter un point de dégradé</b>  ou le curseur de dégradé pour ajouter un point de dégradé. Vous pouvez ajouter 10 points de dégradé. Le nouveau arrêt de couleur n'affecte pas l'aspect actuel du dégradé. Utilisez le bouton <div class="icon icon-removegradientpoint"></div> <b>Supprimer un point de dégradé</b> pour supprimer un certain point de dégradé.</li>
                                        <li>Faites glisser le curseur de déragé pour changer l'emplacement des points de dégradé ou spécifiez la <b>Position</b> en pourcentage pour l'emplacement plus précis.</li>
                                        <li>Pour choisir la couleur au dégradé, cliquez sur l'arrêt concerné sur le curseur de dégradé, ensuite cliquez sur <b>Couleur</b> pour sélectionner la couleur appropriée.</li>
                                    </ul>
                                </li>
                            </ul>
                        </li>
                        <li>
                            <b>Image ou Texture</b> - sélectionnez cette option pour utiliser une image ou une texture prédéfinie en tant que l'arrière-plan du graphique SmartArt.
                            <p><img alt="Remplissage Image ou Texture" src="../images/fill_picture.png" /></p>
                            <ul>
                                <li>Si vous souhaitez utiliser une image en tant que l'arrière-plan du graphique SmartArt, vous pouvez ajouter une image <b>D'un fichier</b> en la sélectionnant sur le disque dur de votre ordinateur ou <b>D'une URL</b> en insérant l'adresse URL appropriée dans la fenêtre ouverte, ou <b>À partir de l'espace de stockage</b> en sélectionnant l'image nécessaire sur votre portail.</li>
                                <li>
                                    Si vous souhaitez utiliser une texture en tant que arrière-plan du graphique SmartArt, utilisez le menu déroulant <b>D'une texture</b> et sélectionnez le préréglage de la texture nécessaire.
                                    <p>Actuellement, les textures suivantes sont disponibles:
Toile, Carton, Tissu foncé, Grain, Granit, Papier gris, Tricot, Cuir, Papier brun, Papyrus, Bois.</p>
                                </li>
                            </ul>
                            <ul>
                                <li>
                                    Si <b>l'image</b> sélectionnée est plus grande ou plus petite que le graphique SmartArt, vous pouvez utiliser l'une des options <b>Prolonger</b> ou<b>Tuile</b> depuis la liste déroulante.
                                    <p>L'option <b>Prolonger</b> permet de régler la taille de l'image pour l'adapter à la taille de la forme automatique afin qu'elle puisse remplir tout l'espace uniformément.</p>
                                    <p>L'option <b>Tuile </b>permet d'afficher seulement une partie de l'image plus grande en gardant ses dimensions d'origine, ou de répéter l'image plus petite en conservant ses dimensions initiales sur la surface du graphique SmartArt afin qu'elle puisse remplir tout l'espace uniformément.</p>
                                    <p class="note"><b>Remarque</b>: tout préréglage <b>Texture</b> sélectionné remplit l'espace de façon uniforme, mais vous pouvez toujours appliquer l'effet <b>Prolonger</b>, si nécessaire.</p>
                                </li>
                            </ul>
                        </li>
                        <li>
                            <b>Modèle</b> - sélectionnez cette option pour sélectionner le modèle à deux couleurs composé des éléments répétés pour remplir l'espace intérieur du graphique SmartArt.
                            <p><img alt="Remplissage avec un modèle" src="../images/fill_pattern.png" /></p>
                            <ul>
                                <li><b>Modèle</b> - sélectionnez un des modèles prédéfinis du menu.</li>
                                <li><b>Couleur de premier plan</b> - cliquez sur cette palette de couleurs pour changer la couleur des éléments du modèle.</li>
                                <li><b>Couleur d'arrière-plan</b> - cliquez sur cette palette de couleurs pour changer de l'arrière-plan du modèle.</li>
                            </ul>
                        </li>
                        <li><b>Pas de remplissage</b> - sélectionnez cette option si vous ne voulez pas utiliser un remplissage.</li>
                        <li>
                            <b>Trait</b> - sert à régler la taille, la couleur et le type du contour du graphique SmartArt.
                            <ul>
                                <li>Pour modifier la largeur du trait, sélectionnez une des options disponibles depuis la liste déroulante <b>Taille</b>. Les options disponibles sont les suivantes: 0,5 pt, 1 pt, 1,5 pt, 2,25 pt, 3 pt, 4,5 pt, 6 pt ou <b>Pas de ligne</b> si vous ne voulez pas utiliser de trait.</li>
                                <li>Pour changer la couleur du contour, cliquez sur la case colorée et <a href="../UsageInstructions/ChangeColorScheme.htm" onclick="onhyperlinkclick(this)">sélectionnez la couleur voulue</a>.</li>
                                <li>Pour modifier le <b>type</b> de contour, sélectionnez l'option voulue dans la liste déroulante correspondante (une ligne continue est appliquée par défaut, vous pouvez la remplacer par l'une des lignes pointillées disponibles).</li>
                            </ul>
                        </li>
                        <li><b>Style d'habillage</b> sert à sélectionner un des styles d'habillage - aligné sur le texte, carré, rapproché, au travers, haut et bas, devant le texte, derrière le texte (pour en savoir plus, consultez la section des paramètres avancés ci-dessous).</li>
                        <li><b>Ajouter une ombre</b> activez cette option pour ajouter une ombre portée à un graphique SmartArt.</li>
                    </ul>
                    </li>
                </ul>
                    <p>Cliquez sur <b>Afficher les paramètres avancés</b> pour accéder aux <a href="../UsageInstructions/InsertAutoshapes.htm#autoshape_advanced" onclick="onhyperlinkclick(this)">paramètres avancés</a>.</p>
</div>
	</body>
</html>