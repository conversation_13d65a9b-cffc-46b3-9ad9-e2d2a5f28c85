﻿<!DOCTYPE html>
<html>
	<head>
		<title>Onglet Insertion</title>
		<meta charset="utf-8" />
        <meta name="description" content="Découvrez l'onglet Insertion et apprenez comment ajouter des éléments de mise en forme, des objets visuels et des commentaires dans un document Word sous cet onglet." />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Rechercher" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Onglet Insertion</h1>
            <p>L'onglet <b>Insertion</b> dans <a href="https://www.onlyoffice.com/fr/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Document Editor</b></a> permet d'ajouter des éléments de mise en forme de la page, des objets visuels et des commentaires.</p>
            <div class="onlineDocumentFeatures">
                <p>La fenêtre de l'onglet dans Document Editor en ligne:</p>
                <p><img alt="Onglet Insertion" src="../images/interface/inserttab.png" /></p>
            </div>
            <div class="desktopDocumentFeatures">
                <p>La fenêtre de l'onglet dans Document Editor de bureau:</p>
                <p><img alt="Onglet Insertion" src="../images/interface/desktop_inserttab.png" /></p>
            </div>
            <p>En utilisant cet onglet, vous pouvez:</p>
            <ul>
                <li>ajouter une <a href="../UsageInstructions/PageBreaks.htm" onclick="onhyperlinkclick(this)">page vide</a>,</li>
                <li>insérer <a href="../UsageInstructions/PageBreaks.htm" onclick="onhyperlinkclick(this)">des sauts de page</a>, <a href="../UsageInstructions/SectionBreaks.htm" onclick="onhyperlinkclick(this)">des sauts de section</a> et <a href="../UsageInstructions/SetPageParameters.htm#columns" onclick="onhyperlinkclick(this)">des sauts de colonne</a>,</li>
                <li>ajouter <a href="../UsageInstructions/InsertTables.htm" onclick="onhyperlinkclick(this)">des tableaux</a>, <a href="../UsageInstructions/InsertImages.htm" onclick="onhyperlinkclick(this)">des images</a>, <a href="../UsageInstructions/InsertCharts.htm" onclick="onhyperlinkclick(this)">des graphiques</a>, <a href="../UsageInstructions/InsertAutoshapes.htm" onclick="onhyperlinkclick(this)">des formes</a>,</li>
                <li>ajouter <a href="../UsageInstructions/AddHyperlinks.htm" onclick="onhyperlinkclick(this)">des liens hypertexte</a>, <a href="../HelpfulHints/CollaborativeEditing.htm#comments" onclick="onhyperlinkclick(this)">des commentaires</a>,</li>
                <li>ajouter <a href="../UsageInstructions/InsertHeadersFooters.htm" onclick="onhyperlinkclick(this)">des en-têtes et des pieds de page</a>, <a href="../UsageInstructions/InsertPageNumbers.htm" onclick="onhyperlinkclick(this)">des numéros de page</a>, <a href="../UsageInstructions/InsertDateTime.htm" onclick="onhyperlinkclick(this)">la date et l'heure</a>,</li>
                <li>ajouter <a href="../UsageInstructions/InsertTextObjects.htm" onclick="onhyperlinkclick(this)">des zones tde texte et des objets Text Art</a>, <a href="../UsageInstructions/InsertEquation.htm" onclick="onhyperlinkclick(this)">des équations</a>, <a href="../UsageInstructions/InsertSymbols.htm" onclick="onhyperlinkclick(this)">des symboles</a>, <a href="../UsageInstructions/InsertDropCap.htm" onclick="onhyperlinkclick(this)">des lettrines</a>, <a href="../UsageInstructions/InsertContentControls.htm" onclick="onhyperlinkclick(this)">des contrôles de contenu</a>,</li>
                <li>insérer <a href="../UsageInstructions/InsertSmartArt.htm" onclick="onhyperlinkclick(this)">des graphiques SmartArt</a>.</li>
            </ul>
		</div>
	</body>
</html>