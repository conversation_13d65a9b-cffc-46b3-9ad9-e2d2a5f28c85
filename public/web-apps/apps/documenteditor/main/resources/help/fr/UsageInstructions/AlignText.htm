﻿<!DOCTYPE html>
<html>
	<head>
		<title>Alignement du texte d'un paragraphe</title>
		<meta charset="utf-8" />
		<meta name="description" content="Tout à propos de l'alignement du texte d'un paragraphe : aligné à gauche, à droite, justifié, au centre" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Rechercher" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Alignement du texte d'un paragraphe</h1>
			<p>Dans l'<a target="_blank" href="https://www.onlyoffice.com/fr/document-editor.aspx" onclick="onhyperlinkclick(this)"><b>Éditeur de Documents</b></a>, le texte peut être aligné de quatre façons : aligné à gauche, au centre, aligné à droite et justifié. Pour le faire,</p>
			<ol>
				<li>placez le curseur à la position où vous voulez appliquer l'alignement (une nouvelle ligne ou le texte déjà saisi ),</li>
                <li>passez à l'onglet <b>Accueil</b> de la barre d'outils supérieure,</li>
				<li>sélectionnez le type d'alignement que vous allez appliquer :<ul>
						<li><b>Gauche</b> - pour aligner du texte à gauche de la page (le côté droit reste non aligné), cliquez sur l'icône <b>Aligner à gauche</b> <div class = "icon icon-alignleft"></div> située sur la barre d'outils supérieure.</li>
						<li><b>Centre</b> - pour aligner du texte au centre de la page (le côté droit et le côté gauche restent non alignés), cliquez sur l'icône <b>Aligner au centre</b> <div class = "icon icon-aligncenter"></div> située sur la barre d'outils supérieure.</li>
						<li><b>Droit</b> - pour aligner du texte à droite de la page (le côté gauche reste non aligné), cliquez sur l'icône <b>Aligner à droite</b> <div class = "icon icon-alignright"></div> située sur la barre d'outils supérieure.</li>
						<li><b>Justifier</b> - pour aligner du texte à gauche et à droite à la fois (l'espacement supplémentaire est ajouté si nécessaire pour garder l'alignement), cliquez sur l'icône <b>Justifier</b> <div class = "icon icon-onecolumn"></div> située sur la barre d'outils supérieure.</li>
					</ul>
				</li>
			</ol>
			<p>Configuration des paramètres d'alignement est aussi disponible dans la fenêtre <b>Paragraphe - Paramètres avancés</b>:</p>
            <ol>
                <li>faites un clic droit sur le texte et sélectionnez <b>Paragraphe - Paramètres avancés</b> du menu contextuel ou utilisez l'option <b>Afficher le paramètres avancée</b> sur la barre d'outils à droite,</li>
                <li>ouvrez la fenêtre <b>Paragraphe - Paramètres avancés</b>, passez à l'onglet <b>Retraits et espacement</b>,</li>
                <li>sélectionnez le type d'alignement approprié de la <b>Liste d'Alignement</b> : <b>À gauche</b>, <b>Au centre</b>, <b>À droite</b>, <b>Justifié</b>,</li>
                <li>cliquez sur <b>OK</b> pour appliquer les modifications apportées.</li>
            </ol>
            <p><img alt="Paragraph Advanced Settings - Indents &amp; Spacing" src="../images/paradvsettings_indents.png" /></p>

		</div>
	</body>
</html>