﻿<!DOCTYPE html>
<html>
	<head>
		<title>Configurer le niveau hiérarchique de paragraphe</title>
		<meta charset="utf-8" />
        <meta name="description" content="Attribuer un niveau hiérarchique au paragraphe" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Rechercher" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Configurer le niveau hiérarchique de paragraphe</h1>
			
            <p>Le niveau de plan désigne le niveau de paragraphe dans la structure du document. Les niveaux suivants sont disponibles dans l'<a target="_blank" href="https://www.onlyoffice.com/fr/document-editor.aspx" onclick="onhyperlinkclick(this)"><b>Éditeur de Documents</b></a> : <em>Texte de base</em>, <em>Niveau 1-</em> - <em>Niveau 9</em>. Le niveau hiérarchique peut être spécifié de différentes manières, par exemple, en utilisant <a href="../UsageInstructions/FormattingPresets.htm" onclick="onhyperlinkclick(this)">des styles de titre</a> : quand vous appliquez un style de titre (<em>Titre 1</em> - <em>Titre 9</em>) à un paragraphe, il acquiert un niveau de plan correspondant. Si vous appliquez un niveau à un paragraphe à l'aide des paramètres avancés du paragraphe, le paragraphe acquiert le niveau de structure uniquement tendis que son style reste intact. Le niveau hiérarchique peut également être modifié à l'aide du panneau de <a href="../UsageInstructions/CreateTableOfContents.htm#navigation" onclick="onhyperlinkclick(this)"><b>Navigation</b></a> à gauche en utilisant des options contextuels du menu.</p>
            <p>Pour modifier un niveau hiérarchique de paragraphe à l'aide des paramètres avancés du paragraphe:</p>
            <ol>
                <li>Cliquez droit sur le texte et choisissez l'option <b>Paramètres avancés du paragraphe</b> du menu contextuel ou utilisez l'option <b>Afficher les paramètres avancés</b> sur la barre latérale droite,</li>
                <li>Ouvrez la fenêtre <b>Paragraphe - Paramètres avancés</b>, passez à l'onglet <b>Retraits et espacement,</b></li>
                <li>Sélectionnez le niveau hiérarchique nécessaire dans la liste du <b>niveau hiérarchique</b>.</li>
                <li>Cliquez sur le bouton <b>OK</b> pour appliquer les modifications.</li>
            </ol>
            <p><img alt="Paragraphe - Paramètres avancés" src="../images/paradvsettings_indents.png" /></p>
		</div>
	</body>
</html>