﻿<!DOCTYPE html>
<html>
<head>
    <title>Lire un texte à haute voix</title>
    <meta charset="utf-8" />
    <meta name="description" content="Description de l'extension Parole pour les éditeurs ONLYOFFICE permettant de lire un texte à haute voix" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Rechercher" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Lire un texte à haute voix</h1>
        <p><a target="_blank" href="https://www.onlyoffice.com/fr/document-editor.aspx" onclick="onhyperlinkclick(this)"><b>Éditeur de documents ONLYOFFICE</b></a> dispose d'une extension qui va lire un texte à voix haute.</p>
        <ol>
            <li>Sélectionnez le texte à lire à haute voix.</li>
            <li>Passez à l'onglet <b>Modules complémentaires</b> et choisissez <div class = "icon icon-speech"></div> <b>Parole</b>.</li>
        </ol>
        <p>Le texte sera lu à haute voix.</p>
    </div>
</body>
</html>