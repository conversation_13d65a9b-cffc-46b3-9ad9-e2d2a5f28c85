﻿<!DOCTYPE html>
<html>
	<head>
		<title>Insérer et mettre en forme un tableau des figures</title>
		<meta charset="utf-8" />
		<meta name="description" content="Insérer et mettre en forme et actualiser un tableau des figures à partir des illustrations légendées et des styles" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
			<div class="search-field">
				<input id="search" class="searchBar" placeholder="Rechercher" type="text" onkeypress="doSearch(event)">
        </div>
		<h1>Insérer et mettre en forme un tableau des figures</h1>
			<p>Un tableau des figures permet de répertorier toutes les équations, les graphiques et les tableaux accompagnant votre document. Un tableau des figures, au même titre qu'une table des matières, permet d'obtenir la liste des illustrations et d'organiser des objets légendés ou des en-têtes personnalisés avec un certain style de mise en forme. Les illustrations organisées de telle façon sont facile à citer et retrouver. Cliquez sur le lien dans le <b>Tableau des figures</b> mise sous forme de liens pour accéder à l'élément référencé. Tous les tableaux, les équations, les diagrammes, les images, les graphiques, les cartes, les photos et d'autres illustrations sont présentées en tant qu'une figure.
            <p><img alt="L'onglet Références" src="../images/referencestab.png" /></p>
			<p>Pour insérer un <b>Tableau des figures</b> dans l'<a target="_blank" href="https://www.onlyoffice.com/fr/document-editor.aspx" onclick="onhyperlinkclick(this)"><b>Éditeur de Documents</b></a> passez à l'onglet <b>Références</b> et utilisez le bouton <b>Tableau des figures</b> <span class = "icon icon-table_figures_button"></span> dans la barre d'outils supérieure pour créer et mettre en forme le tableau des figures. Utiliser le bouton <b>Actualiser</b> pour mettre à jour le tableau des figures  à chaque fois que vous ajoutez une nouvelle illustration à votre document.</p>
			<h2>Créer un tableau des figures</h2>
			<p class="note"><b>Remarque :</b> Pour créer un tableau des figures vous devez légender chaque figure ou la personnaliser avec un style de mise en forme. Avant de créer un tableau des figures, vous devez ajouter une <a href="../UsageInstructions/AddCaption.htm" onclick="onhyperlinkclick(this)">légende</a> à chaque équation, tableau ou illustration de votre document ou appliquer un <a href="../UsageInstructions/FormattingPresets.htm" onclick="onhyperlinkclick(this)">style</a> de texte afin que l'éditeur puisse créer automatiquement le tableau des figures.</p>
			<ol>
			<li>Une fois toutes les légendes et les styles ajoutés, placez le curseur à l'endroit où le <b>tableau des figures</b> doit être inséré et passez à l'onglet <b>Références</b>, ensuite cliquez sur le bouton <b>Tableau des figure</b> pour ouvrir la boîte de dialogue et créer une liste des figures.
            <p><img alt="Paramètres du tableau des figures" src="../images/table_figures_settings.png" /></p>
			</li>
			<li>Sélectionnez l'option appropriée pour construire un tableau des figures à partir des illustrations légendées ou mises en forme avec un style.
			<ul style = "list-style-type:disc">
			<li>Pour créer un tableau des figures à partir des illustrations légendées : Activez la case Légende et sélectionnez l'illustration légendée dans la liste déroulante :
			<ul style = "list-style-type:circle">
					<li>Aucune ;</li>
                    <li>Équation ;</li>
                    <li>Figure ;</li>
                    <li>Tableau.
			<p><img alt="Tableau des figures légendées" src="../images/table_figures_captioned.png" /></p>
					</li>
				 </ul>
			 </li>
            <li>Pour créer un <b>tableau des figures</b> à partir des illustrations avec un style de mise en forme : Activez la case <b>Style</b> et sélectionnez le style d'illustration dans la liste déroulante. Le menu des options varie en fonction du style choisi :
			<ul style = "list-style-type:circle">
                       <li>Titre 1 ;</li>
                       <li>Titre 2 ;</li>
                       <li>Légende ;</li>
                       <li>Tableau des figures ;</li>
					   <li>Normal.
			<p><img alt="Tableau des figures avec un style" src="../images/table_figures_style.png" /></p>
						</li>
					</ul>
				</li>
			</ul>
			</li>
			</ol>
			<h2>Mettre en forme un tableau des figures</h2>
            <p>Les cases d'options permettent de mettre en forme un tableau des figures. Par défaut, toutes les cases d'options sont actives, car, dans la plupart des cas, l'activation de celles-ci semble raisonnable. Désactivez toutes les options dont vous n'avez pas besoin.</p>
			<ul style = "list-style-type:none">
						<li><b>Afficher les numéros de pages</b> permet d'afficher le numéro de page correspondant ;</li>
						<li><b>Aligner les numéros de page à droite</b> permet d'afficher les numéros de page à droite dès lors que <b>Afficher les numéros de pages</b> est active, décochez-la pour afficher le numéro de page juste après le titre.</li>
						<li><b>Mettre le tableau des figures sous forme de liens</b> permet de créer des liens hypertexte dans la <b>table des figures</b> ;</li>
						<li><b>Inclure l'étiquette et le numéro</b> permet d'ajouter des étiquettes et de la numérotation au tableau des figures.</li>
			</ul>
			<ul style = "list-style-type:disc">
			<li>Sélectionnez les points de suite dans la liste déroulante à remplir l'espace entre un titre et le numéro de page correspondant pour améliorer alors la lisibilité et la lecture des tableaux.</li>
			<li>Personnalisez le style du texte dans la table des figures en choisissant un style dans la liste déroulante.
				<ul style = "list-style-type:circle">
						<li><b>Actuel</b> pour afficher le style plus récent.</li>
						<li><b>Simple</b> pour mettre le texte en gras.</li>
						<li><b>En ligne</b> pour afficher le texte sous forme de lien hypertexte.</li>
						<li><b>Classique</b> pour mettre du texte en majuscule.<b> </b></li>
						<li><b>Distinctif</b> pour mettre du texte en italique.</li>
						<li><b>Centré </b>pour aligner le texte au centre et n'afficher pas des points de suite.</li>
						<li><b>Formel </b>pour mettre en forme du texte en 11 pt Arial et donner un aspect formel.</li>
				</ul>
				</li>
		<li>La fenêtre Aperçu affiche la sortie finale du tableau des figures </li>
			</ul>
		<h2>Mettre à jour un tableau des figures</h2>
		<p>On doit actualiser un <b>Tableau des figures</b> à chaque fois qu'une nouvelle équation, illustration ou tableau est inséré dans le document. Le bouton <b>Actualiser</b> ne devient actif que lorsque vous sélectionnez ou cliquez sur le Tableau des figures. Cliquez sur le bouton<b> Actualiser </b> <span class = "icon icon-refresh_button"></span> sous l'onglet <b>Références</b> de la barre d'outils supérieure et sélectionnez l'option appropriée dans le menu.</p>
		<img alt="La fenêtre contextuelle Actualiser" src="../images/refresh_table-figures_popup.png" />
		<ul style = "list-style-type:disc">
				<li><b>Actualiser les numéros de page uniquement</b> permet d'actualiser les numéros de page sans modifier les changements de titres.</li>
				<li><b>Actualiser le tableau entier</b> permet de mettre en place toutes les modifications des titres et des numéros de pages.</li>
			</ul>
		<p>Cliquez sur <b>OK</b> pour confirmer votre choix ou</p>
		<p>Ouvrez le menu contextuel en cliquant avec le bouton droit de la souris sur le <b>Tableaux des figures</b> dans votre document, ensuite cliquez sur <b>Actualiser</b> dans le menu pour le mettre à jour.</p>
		</div>
	</body>
</html>