var indexes = 
[
   {
        "id": "HelpfulHints/About.htm", 
        "title": "À propos de l'Éditeur de Documents", 
        "body": "Éditeur de Documents est une application en ligne qui vous permet de parcourir et de modifier des documents dans votre navigateur . En utilisant l'Éditeur de Documents, vous pouvez effectuer différentes opérations d'édition comme avec n'importe quel éditeur de bureau, imprimer les documents modifiés en gardant la mise en forme ou les télécharger sur votre disque dur au format DOCX, PDF, ODT, TXT, DOTX, PDF/A, OTT, RTF, HTML, FB2, EPUB. Pour afficher la version actuelle du logiciel, le numéro de build et les informations de licence dans la version en ligne, cliquez sur l'icône dans la barre latérale gauche. Pour afficher la version actuelle du logiciel et les informations de licence dans la version de bureau pour Windows, sélectionnez l'élément de menu À propos dans la barre latérale gauche de la fenêtre principale du programme. Dans la version de bureau pour Mac OS, accédez au menu ONLYOFFICE en haut de l'écran et sélectionnez l'élément de menu À propos d'ONLYOFFICE."
    },
   {
        "id": "HelpfulHints/AdvancedSettings.htm", 
        "title": "Paramètres avancés de l'Éditeur de Documents", 
        "body": "Éditeur de Documents vous permet de modifier ses paramètres avancés. Pour y accéder, ouvrez l'onglet Fichier de la barre d'outils supérieure et sélectionnez l'option Paramètres avancés.... Les paramètres avancés sont les suivants : Édition et enregistrement Enregistrement automatique est utilisé dans la version en ligne pour activer/désactiver l'enregistrement automatique des modifications que vous effectuez pendant l'édition. Récupération automatique est utilisée dans la version de bureau pour activer/désactiver l'option qui permet de récupérer automatiquement les documents en cas de fermeture inattendue du programme. Afficher le bouton \"Options de collage\" lorsque le contenu est collé. L'icône correspondante sera affichée lorsque vous collez le contenu au document. Rendre les fichiers compatibles avec les anciennes versions de MS Word lorsqu'ils sont enregistrés au format DOCX. Les fichiers enregistrés au format DOCX deviendront compatibles avec les anciennes versions de Microsoft Word. Collaboration Le Mode de co-édition permet de sélectionner le mode d'affichage des modifications effectuées lors de la co-édition : Rapide (par défaut). Les utilisateurs qui participent à la co-édition du document verront les changements en temps réel une fois qu'ils sont faits par d'autres utilisateurs. Strict. Tous les changements apparaîtront seulement après avoir cliqué sur l'icône Enregistrer pour vous informer qu'il y a des changements effectués par d'autres utilisateurs. Afficher le suivi des modifications sert à sélectionner la façon d'affichage des modifications. Afficher par clic dans les ballons. Les modifications s'affichent dans les ballons lorsque vous activez le suivi des modifications. Afficher lorsque le pointeur est maintenu sur l'infobulle. L'infobulle s'affiche lorsque vous faites glisser le pointeur sur la modification suivie. Le paragraphe Changements de collaboration en temps réel vous permet de spécifier comment les nouveaux changements et commentaires seront affichés en temps réel. Surligner aucune modification. Les modifications effectuées au cours de la session actuelle ne seront pas mises en surbrillance. Surligner toutes les modifications. Toutes les modifications effectuées au cours de la session actuelle seront mises en surbrillance. Voir le dernier. Seules les modifications apportées depuis le dernier clic sur l'icône Enregistrer seront mises en surbrillance. Cette option n'est disponible que lorsque le mode de co-édition Strict est sélectionné. Activer l'affichage des commentaires. Si cette option est désactivée, les passages commentés seront mis en surbrillance uniquement si vous cliquez sur l'icône Commentaires dans la barre latérale gauche. Activer l'affichage des commentaires résolus. Cette fonction est désactivée par défaut pour que les commentaires résolus soient cachés dans le texte du document. Vous ne pourrez voir ces commentaires que si vous cliquez sur l'icône Commentaires dans la barre latérale gauche. Activez cette option si vous voulez afficher les commentaires résolus dans le texte du document. Vérification Vérification de l'orthographe sert à activer/désactiver l'option de vérification de l'orthographe. Ignorer les mots en MAJUSCULES. Les mots tapés en majuscules sont ignorés lors de la vérification de l'orthographe. Ignorer les mots contenant des chiffres. Les mots contenant des chiffres sont ignorés lors de la vérification de l'orthographe. Le menu Options d'auto-correction... permet d'acceder aux paramètres d'auto-correction tels que remplacement au cours de la frappe, fonctions de reconnaissance, mise en forme automatique etc. Espace de travail L'option Guides d'alignement est utilisée pour activer/désactiver les guides d'alignement qui apparaissent lorsque vous déplacez des objets et vous permettent de les positionner précisément sur la page. L'option Hiéroglyphes est utilisée pour activer/désactiver l'affichage des hiéroglyphes. L'option Utiliser la touche Alt pour naviguer dans l'interface utilisateur à l'aide du clavier est utilisée pour activer l'utilisation de la touche Alt / Option à un raccourci clavier. L'option Thème d'interface permet de modifier les jeux de couleurs de l'interface d'éditeur. L'option Identique à système rend le thème d'interface de l'éditeur identique à celui de votre système. Le mode Claire comprend l'affichage des éléments de l'interface utilisateur en couleurs standards bleu, blanc et gris claire à contraste réduit et est destiné à un travail de jour. Le mode Claire classique comprend l'affichage en couleurs standards bleu, blanc et gris claire. Le mode Sombre comprend l'affichage en tons sombres noir, gris foncé et gris claire destinés à un travail de nuit. L'option Activer le mode sombre pour les documents devient activé lorsque le thème de l'interface Sombre est choisi. Le cas échéant, cochez la case Activer le mode sombre pour les documents pour activez le mode sombre. Le mode Contraste sombre comprend l'affichage des éléments de l'interface utilisateur en couleurs noir, gris foncé et blanc à plus de contraste et est destiné à mettre en surbrillance la zone de travail du fichier. L'option Activer le mode sombre pour les documents est utilisée pour rendre la zone de travail plus sombre lorsque le thème de l'interface Sombre ou Contraste sombre est choisi. Cochez la case Activer le mode sombre pour les documents afin de l'activer. Remarque : En plus des thèmes de l'interface disponibles Claire, Classique claire, Sombre et Contraste sombre, il est possible de personnaliser les éditeurs ONLYOFFICE en utilisant votre propre couleur de thème. Pour en savoir plus, veuillez consulter ces instructions. L'option Unité de mesure sert à spécifier les unités de mesure utilisées sur les règles et dans les fenêtres de paramètres pour les paramètres tels que largeur, hauteur, espacement, marges etc. Vous pouvez choisir l'option Centimètre, Point ou Pouce. L'option Valeur du zoom par défaut sert à définir la valeur de zoom par défaut en la sélectionnant de la liste des options disponibles de 50% à 500%. Vous pouvez également choisir l'option Ajuster à la page ou Ajuster à la largeur. L'option Hinting de la police sert à sélectionner le type d'affichage de la police dans l'Éditeur de Documents. Choisissez Comme Windows si vous aimez la façon dont les polices sont habituellement affichées sous Windows, c'est-à-dire en utilisant la police de Windows. Choisissez Comme OS X si vous aimez la façon dont les polices sont habituellement affichées sous Mac, c'est-à-dire sans hinting. Choisissez Natif si vous voulez que votre texte soit affiché avec les hintings intégrés dans les fichiers de polices. Mise en cache par défaut sert à sélectionner cache de police. Il n'est pas recommandé de désactiver ce mode-ci sans raison évidente. C'est peut être utile dans certains cas, par exemple les problèmes d'accélération matérielle activé sous Google Chrome. Éditeur de Documents gère deux modes de mise en cache : Dans le premier mode de mise en cache chaque lettre est mise en cache comme une image indépendante. Dans le deuxième mode de mise en cache l'image d'une certaine taille est sélectionnée avec les lettres dynamiques et avec de l'allocation/libération de la mémoire mise en place. La deuxième image est créée s'il n'y a pas de mémoire suffisante etc. Le Mode de mise en cache par défaut est activé en fonction du navigateur utilisé : Avec la mise en cache par défaut activée, dans Internet Explorer (v. 9, 10, 11) le deuxième mode de mise en cache est utilisé, le premier mode de mise en cache est utilisé dans les autres navigateurs. Avec la mise en cache par défaut désactivée, dans Internet Explorer (v. 9, 10, 11) le premier mode de mise en cache est utilisé, le deuxième mode de mise en cache est utilisé dans les autres navigateurs. L'option Réglages macros s'utilise pour définir l'affichage des macros avec notification. Choisissez Désactivez tout pour désactiver toutes les macros dans votre document. Choisissez Montrer la notification pour afficher les notifications lorsque des macros sont présentes dans un document. Choisissez Activer tout pour exécuter automatiquement toutes les macros dans un document. Pour enregistrer toutes les modifications, cliquez sur le bouton Appliquer."
    },
   {
        "id": "HelpfulHints/CollaborativeEditing.htm", 
        "title": "Collaborer sur un document en temps réel", 
        "body": "Éditeur de Documents permet de gérer le flux de travail continu par l'ensemble de l'équipe : partager des fichiers et des dossiers, communiquer directement depuis l'éditeur, laisser des commentaires pour des fragments de la présentation nécessitant la participation d'une tierce personne, sauvegarder des versions du document pour une utilisation ultérieure, réviser les documents et ajouter les modifications sans modifier le fichier, comparer et fusionner les documents pour faciliter le traitement et l'édition. Dans l'Éditeur de Documents il y a deux modes de collaborer sur des documents en temps réel : Rapide et Strict. Vous pouvez basculer entre les modes depuis Paramètres avancés. Il est également possible de choisir le mode voulu à l'aide de l'icône Mode de coédition dans l'onglet Collaboration de la barre d'outils supérieure : Le nombre d'utilisateurs qui travaillent sur le document actuel est spécifié sur le côté droit de l'en-tête de l'éditeur - . Pour afficher les personnes qui travaillent sur le fichier, cliquez sur cette icône pour ouvrir le panneau Chat avec la liste complète affichée. Mode Rapide Le mode Rapide est utilisé par défaut et affiche les modifications effectuées par d'autres utilisateurs en temps réel. Lorsque vous co-éditez un document en mode Rapide, la possibilité de Rétablir la dernière opération annulée n'est pas disponible. Le mode Rapide affichera les actions et les noms des co-éditeurs tandis qu'ils modifient le texte. Pour voir qui est en train d'éditer le fichier au présent, placez le curseur de la souris sur cette icône - les noms des utilisateurs seront affichés dans la fenêtre contextuelle. Mode Strict Le mode Strict est sélectionné pour masquer les modifications d'autres utilisateurs jusqu'à ce que vous cliquiez sur l'icône Enregistrer pour enregistrer vos propres modifications et accepter les modifications apportées par d'autres utilisateurs. Lorsqu'un document est en cours de modification par plusieurs utilisateurs simultanément dans le mode Strict, les passages de texte modifiés sont marqués avec des lignes pointillées de couleurs différentes. Dès que l'un des utilisateurs sauvegarde ses modifications en cliquant sur l'icône , les autres verront une note dans la barre d'état indiquant qu'il y a des mises à jour. Pour enregistrer les modifications apportées et récupérer les mises à jour de vos co-auteurs cliquez sur l'icône dans le coin supérieur gauche de la barre supérieure. Les mises à jour seront marquées pour vous aider à contrôler ce qui a été exactement modifié. Vous pouvez spécifier les modifications que vous souhaitez mettre en surbrillance pendant la co-édition si vous cliquez sur l'onglet Fichier dans la barre d'outils supérieure, sélectionnez l'option Paramètres avancés... et choisissez l'une des options : Surligner toutes modifications toutes les modifications apportées au cours de la session seront mises en surbrillance. Voir le dernier - uniquement les modifications apportées après le dernier clic sur l'icône seront mises en surbrillance. Surligner aucune modification - aucune modification apportée au cours de la session ne sera mise en surbrillance. Mode Visionneuse en direct Le mode Visionneuse en direct est utilisé pour voir les modifications apportées par d'autres utilisateurs en temps réel lorsque le document est ouvert par un utilisateur avec les droits d'accès Lecture seule. Pour que le mode fonctionne correctement, assurez-vous que la case Afficher les modifications apportées par d'autres utilisateurs est cochée dans les Paramètres avancés de l'éditeur. Utilisateurs anonymes Les utilisateurs du portail qui ne sont pas enregistrés et n'ont pas du profil sont les utilisateurs anonymes qui quand même peuvent collaborer sur des documents. Pour affecter le nom à un utilisateur anonyme, ce dernier doit saisir le nom préféré dans le coin supérieur droit de l'écran lorsque l'on accède au document pour la première fois. Activez l'option \"Ne plus poser cette question\" pour enregistrer ce nom-ci."
    },
   {
        "id": "HelpfulHints/Commenting.htm", 
        "title": "Commenter des documents", 
        "body": "Éditeur de Documents permet de gérer le flux de travail continu par l'ensemble de l'équipe : partager des fichiers et des dossiers, collaborer sur des documents en temps réel, communiquer directement depuis l'éditeur, sauvegarder des versions du document< pour une utilisation ultérieure, réviser les documents et ajouter les modifications sans modifier le fichier, comparer et fusionner les documents pour faciliter le traitement et l'édition. Dans l'Éditeur de Documents vous pouvez laisser les commentaires pour le contenue de documents sans le modifier. Contrairement au messages de chat, les commentaires sont stockés jusqu'à ce que vous décidiez de les supprimer. Laisser et répondre aux commentaires Pour laisser un commentaire : sélectionnez le fragment du texte où vous pensez qu'il y a une erreur ou un problème, passez à l'onglet Insertion ou Collaboration sur la barre d'outils supérieure et cliquer sur le bouton Commentaires ou utilisez l'icône sur la barre latérale de gauche pour ouvrir le panneau Commentaireset cliquez sur le lien Ajouter un commentaire au document ou cliquez avec le bouton droit sur le fragment du texte sélectionné et sélectionnez l'option Ajouter un commentaire dans le menu contextuel, saisissez le texte nécessaire, cliquez sur le bouton Ajouter commentaire/Ajouter. Le commentaire sera affiché sur le panneau Commentaires à gauche. Tout autre utilisateur peut répondre au commentaire ajouté en posant une question ou en faisant référence au travail fait. Pour le faire il suffit de cliquer sur le lien Ajouter une réponse situé au-dessous du commentaire, saisissez votre réponse dans le champ de saisie et appuyez sur le bouton Répondre. Si vous utilisez le mode de co-édition Strict, les nouveaux commentaires ajoutés par d'autres utilisateurs ne seront visibles qu'après un clic sur l'icône dans le coin supérieur gauche de la barre supérieure. Désactiver l'affichage des commentaires Le fragment du texte commenté sera mis en surbrillance dans le document. Pour voir le commentaire, cliquez à l'intérieur du fragment. Pour désactiver cette fonctionnalité, cliquez sur l'onglet Fichier de la barre d'outils supérieure, sélectionnez l'option Paramètres avancés..., décochez la case Activer l'affichage de commentaires. Dans ce cas, les fragments commentés ne seront mis en évidence que si vous cliquez sur l'icône . Gérer les commentaires Vous pouvez gérer les commentaires ajoutés en utilisant les icônes de la bulle de commentaire ou sur le panneau gauche Commentaires : trier les commentaires ajoutés en cliquant sur l'icône : par date : Plus récent ou Plus ancien. C'est 'ordre de tri par défaut. par auteur : Auteur de A à Z ou Auteur de Z à Z par emplacement : Du haut ou Du bas. L'ordre habituel de tri des commentaires par l'emplacement dans un document est comme suit (de haut) : commentaires au texte, commentaires aux notes de bas de page, commentaires aux notes de fin, commentaires aux en-têtes/pieds de page, commentaires aux texte entier. Filtrer par groupe : Tout ou sélectionnez un groupe de la liste. Cette option de trie n'est disponible que si votre version prend en charge cette fonctionnalité. modifier le commentaire actuel en cliquant sur l'icône, supprimer le commentaire actuel en cliquant sur l'icône, fermer la discussion actuelle en cliquant sur l'icône si la tâche ou le problème décrit dans votre commentaire est résolu, après quoi la discussion ouverte par votre commentaire reçoit le statut résolu. Pour l'ouvrir à nouveau, cliquez sur l'icône. l'icône. Si vous souhaitez masquer les commentaires résolus, cliquez sur l'onglet Fichier dans la barre d'outils supérieure, sélectionnez l'option Paramètres avancés..., décochez la case Activer l'affichage des commentaires résolus puis cliquez sur Appliquer. Dans ce cas, les commentaires résolus ne seront mis en évidence que si vous cliquez sur l'icône, si vous souhaitez gérer plusieurs commentaires à la fois, ouvrez le menu contextuel Résoudre sous l'onglet Collaboration. Sélectionnez l'une des options disponibles : résoudre les commentaires actuels, marquer mes commentaires comme résolus ou marquer tous les commentaires comme résolus. Ajouter les mentions Ce n'est qu'aux fragments du texte que vous pouvez ajouter des mentions et pas au document lui-même. Lorsque vous ajoutez un commentaire sur lequel vous voulez attirer l'attention d'une personne, vous pouvez utiliser la fonction de mentions et envoyer une notification par courrier électronique ou Talk. Pour ajouter une mention, Saisissez \"+\" ou \"@\" n'importe où dans votre commentaire, alors la liste de tous les utilisateurs du portail s'affichera. Pour faire la recherche plus rapide, tapez les premières lettres du prénom de la personne, la liste propose les suggestions au cours de la frappe. Puis sélectionnez le nom souhaité dans la liste. Si la personne mentionnée n'a pas l'autorisation d'ouvrir ce fichier, la fenêtre Paramètres de partage va apparaître. Par défaut, un document est partagé en Lecture seule. Modifiez la permission, le cas échéant.. Cliquez sur OK. La personne mentionnée recevra une notification par courrier électronique informant que son nom est mentionné dans un commentaire. La personne recevra encore une notification lorsque un fichier commenté est partagé. Supprimer des commentaires Pour supprimer les commentaires, cliquez sur le bouton Supprimer sous l'onglet Collaboration dans la barre d'outils supérieure, sélectionnez l'option convenable du menu : Supprimer les commentaires actuels à supprimer la sélection du moment. Toutes les réponses qui déjà ont été ajoutées seront supprimés aussi. Supprimer mes commentaires à supprimer vos commentaire et laisser les commentaires d'autres. Toutes les réponses qui déjà ont été ajoutées à vos commentaires seront supprimés aussi. Supprimer tous les commentaires sert à supprimer tous les commentaires du document. Pour fermer le panneau avec les commentaires, cliquez sur l'icône de la barre latérale de gauche encore une fois."
    },
   {
        "id": "HelpfulHints/Communicating.htm", 
        "title": "Communiquer en temps réel", 
        "body": "Éditeur de Documents permet de gérer le flux de travail continu par l'ensemble de l'équipe : partager des fichiers et des dossiers, collaborer sur des documents en temps réel, laisser des commentaires pour des fragments du document nécessitant la participation d'une tierce personne, sauvegarder des versions du document pour une utilisation ultérieure, réviser les documents et ajouter les modifications sans modifier le fichier, comparer et fusionner les documents pour faciliter le traitement et l'édition. Dans l'Éditeur de Documents il est possible de communiquer avec vos co-auteurs en temps réel en utilisant l'outil intégré Chat et les modules complémentaires utiles, par ex. Telegram ou Rainbow. Pour accéder au Chat et laisser un message pour les autres utilisateurs, cliquez sur l'icône dans la barre latérale gauche ou passez à l'onglet Collaboration de la barre d'outils supérieure et cliquez sur le bouton Chat, saisissez le texte dans le champ correspondant, cliquez sur le bouton Envoyer. Les messages de discussion sont stockés uniquement pendant une session. Pour discuter le contenu du document, il est préférable d'utiliser les commentaires, car ils sont stockés jusqu'à ce que vous décidiez de les supprimer. Tous les messages envoyés par les utilisateurs seront affichés sur le panneau à gauche. S'il y a de nouveaux messages à lire, l'icône chat sera affichée de la manière suivante - . Pour fermer le panneau avec des messages de discussion, cliquez à nouveau sur l'icône dans la barre latérale gauche ou sur le bouton Chat dans la barre d'outils supérieure."
    },
   {
        "id": "HelpfulHints/Comparison.htm", 
        "title": "Comparer des documents", 
        "body": "Éditeur de Documents permet de gérer le flux de travail continu par l'ensemble de l'équipe : partager des fichiers et des dossiers, collaborer sur des documents en temps réel, communiquer directement depuis l'éditeur, laisser des commentaires pour des fragments du document nécessitant la participation d'une tierce personne, sauvegarder des versions du document pour une utilisation ultérieure, réviser les documents et ajouter les modifications sans modifier le fichier. Si vous avez besoin de comparer et de fusionner deux documents, l'Éditeur de Documents dispose de la fonctionnalité de Comparaison des documents. Cette fonctionnalité permet d'afficher des différences entre deux documents et de fusionner des documents en acceptant les modifications une par une ou toutes à la fois. Lors de la comparaison et du fusionnement des documents, le fichier résultant sera stocké sur le portail comme une nouvelle version du fichier original. Si vous ne souhaitez pas fusionner des documents lors de la comparaison, vous pouvez rejeter toutes les modifications pour que le document original demeure inchangé. Choisir des documents à comparer Pour comparer deux documents, ouvrez le document original et sélectionnez le deuxième document à comparer : passez à l'onglet Collaboration de la barre d'outils supérieure et cliquez sur le bouton Comparer, sélectionnez l'une des options pour télécharger le document : l'option Document à partir d'un fichier permet d'ouvrir la fenêtre de dialogue standard pour sélectionner le fichier. Recherchez le fichier .docx nécessaire sur votre disque local et cliquez sur le bouton Ouvrir. l'option Document à partir d'une URL permet d'ouvrir la fenêtre pour saisir un lien vers le fichier d'un stockage de nuage externe (par exemple, Nextcloud) si vous avez l'autorisation appropriée pour accéder au fichier. Ce lien doit être un lien de téléchargement direct. Lorsque le lien est indiqué, cliquez sur le bouton OK : Remarque : Un lien de téléchargement direct permet de démarrer le téléchargement sans ouvrir le navigateur. Par exemple, pour obtenir un lien dans Nextcloud, recherchez le document nécessaire dans la liste, sélectionnez l'option Détails dans le menu. Cliquez sur l'icône Copier le lien direct (pour des utilisateurs qui ont l'autorisation pour accéder à ce fichier/dossier) à droite du nom de fichier sur le panneau de détails. Pour apprendre comment obtenir un lien direct dans un autre stockage externe, veuillez consulter la documentation approprié de cet stockage. l'option Document à partir de stockage permet d'ouvrir la fenêtreSélectionner la source de données. Une liste de tous documents .docx stockés sur votre portail auxquels vous avez accès s'affichera. Pour parcourir toutes les sections du module Documents, utilisez le menu dans la partie gauche de la fenêtre. Sélectionnez le document .docx nécessaire et cliquez sur le bouton OK. Le processus de comparaison des fichiers démarrera lors de la sélection du dixième fichier et votre document ressemblera un document en mode Révision. Toutes modifications sont mises en surbrillance, alors vous pouvez voir les modifications, naviguer entre elles, les accepter ou rejeter une par une ou toutes à la fois. Vous pouvez aussi modifier le mode d'affichage pour afficher le document avant la comparaison, en cours de la comparaison et après la comparaison si vous acceptez les modifications. Choisir le mode d'affichage des modifications Cliquez sur le bouton Mode d'affichage dans la barre d'outils supérieure et sélectionnez l'un des modes disponibles dans la liste : Balisage - cette option est sélectionnée par défaut. Ce mode est utilisé pour afficher le document en cours de comparaison. Ce mode permet d'afficher toutes les modifications et de modifier le document. Final - ce mode est utilisé pour afficher le document après la comparaison comme si toutes les modifications avaient été acceptées. Cette option n'accepte pas toutes les modifications, elle vous permet de voir à quoi ressemblera le document après avoir accepté toutes les modifications. Dans ce mode, vous ne pouvez pas modifier le document. Original - ce mode est utilisé pour afficher le document avant la comparaison comme si toutes les modifications ont été rejetées. Cette option ne rejette pas toutes les modifications, elle vous permet uniquement d'afficher le document sans modifications. Dans ce mode, vous ne pouvez pas modifier le document. Accepter ou rejeter les modifications Utilisez les boutons Précédente et Suivante de la barre d'outils supérieure pour naviguer entre les modifications. Pour accepter la modification actuellement sélectionnée, vous pouvez : cliquez sur le bouton Accepter de la barre d'outils supérieure, ou cliquez sur la flèche vers le bas au-dessous du bouton Accepter et sélectionnez l'option Accepter la modification en cours (dans ce cas, la modification sera acceptée et vous passerez à la modification suivante), ou cliquez sur le bouton Accepter dans la fenêtre contextuelle. Pour accepter rapidement toutes les modifications, cliquez sur la flèche vers le bas au-dessous du bouton Accepter et sélectionnez l'option Accepter toutes les modifications. Pour rejeter la modification actuelle, vous pouvez : cliquez sur le bouton Rejeter de la barre d'outils supérieure, ou cliquer sur la flèche vers le bas au-dessous du bouton Rejeter et sélectionnez l'option Rejeter la modification en cours (dans ce cas, la modification sera rejetée et vous passerez à la modification suivante), ou cliquez sur le bouton Rejeter dans la fenêtre contextuelle. Pour rejeter rapidement toutes les modifications, cliquez sur la flèche vers le bas au-dessous du bouton Rejeter et sélectionnez l'option Rejeter toutes les modifications. Informations supplémentaires sur la fonction de comparaison Méthode de comparaison Les documents sont comparés par des mots. Si au moins un caractère dans un mot est modifié (par exemple, si un caractère a été supprimé ou remplacé), à la suite la différence sera affichée comme le changement du mot entier, pas du caractère. L'image ci-dessous illustre le cas où le fichier d'origine contient le mot « caractères » et le document de comparaison contient le mot « Caractères ». Auteur du document Lors du lancement de la comparaison, le deuxième document de comparaison est chargé et comparé au document actuel. Si le document chargé contient des données qui ne sont pas représentées dans le document d'origine, les données seront marquées comme ajoutées par un réviseur. Si le document d'origine contient des données qui ne sont représentées dans le document chargé, les données seront marquées comme supprimées par un réviseur. Si le document original et le document chargé sont du même auteur, le réviseur est le même utilisateur. Son nom s'affiche dans la bulle de modification. Si les deux fichiers sont des auteurs différents, l'auteur du deuxième fichier chargé à des fins de comparaison est l'auteur des modifications ajoutées/supprimées. Présence des modifications suivies dans le document comparé Si le document d'origine contient des modifications apportées en mode révision, elles seront acceptées pendant la comparaison. Lorsque vous choisissez le deuxième fichier à comparer, vous verrez le message d'avertissement correspondant. Dans ce cas, lorsque vous choisissez le mode d'affichage Original, il n'y aura aucune modification dans le document."
    },
   {
        "id": "HelpfulHints/KeyboardShortcuts.htm", 
        "title": "Raccourcis clavier", 
        "body": "Raccourcis clavier pour les touches d'accès Utiliser les raccourcis clavier pour faciliter et accélérer l'accès à l'Éditeur de Documents sans l'aide de la souris. Appuyez sur la touche Altpour activer toutes les touches d'accès pour l'en-tête, la barre d'outils supérieure, les barres latérales droite et gauche et la barre d'état. Appuyez sur la lettre qui correspond à l'élément dont vous avez besoin. D'autres suggestions de touches peuvent s'afficher en fonction de la touche que vous appuyez. Les premières suggestions de touches se cachent lorsque les suggestions supplémentaires s'affichent. Par exemple, pour accéder à l'onglet Insertion, appuyez sur la touche Alt pour activer les primaires suggestions de touches d'accès. Appuyez sur la lettre I pour accéder à l'onglet Insertion et activer tous les raccourcis clavier disponibles sous cet onglet. Appuyez sur la lettre qui correspond à l'élément que vous allez paramétrer. Appuyez sur la touche Alt pour désactiver toutes les suggestions de touches d'accès ou appuyez sur Échap pour revenir aux suggestions de touches précédentes. Trouverez ci-dessous les raccourcis clavier les plus courants : Windows/Linux Mac OS Traitement du document Ouvrir le panneau \"Fichier\" Alt+F ^ Ctrl+⌥ Option+F Ouvrir le volet Fichier pour enregistrer, télécharger, imprimer le document actuel, afficher ses informations, créer un nouveau document ou ouvrir un existant, accéder à l'aide de l'Éditeur de Documents ou aux paramètres avancés. Ouvrir la fenêtre \"Rechercher et remplacer\" Ctrl+F ^ Ctrl+F, &#8984; Cmd+F Ouvrir la fenêtre Rechercher et remplacer pour commencer à chercher un caractère/mot/phrase dans le document actuellement édité. Ouvrir la fenêtre \"Rechercher et remplacer\" avec le champ de remplacement Ctrl+H ^ Ctrl+H Ouvrir la fenêtre Rechercher et remplacer avec le champ de remplacement pour remplacer une ou plusieurs occurrences des caractères trouvés. Répéter la dernière action « Rechercher ». ⇧ Shift+F4 ⇧ Shift+F4, &#8984; Cmd+G, &#8984; Cmd+⇧ Shift+F4 Répéter l'action de Rechercher qui a été effectuée avant d'appuyer sur la combinaison de touches. Ouvir le panneau \"Commentaires\" Ctrl+⇧ Shift+H ^ Ctrl+⇧ Shift+H, &#8984; Cmd+⇧ Shift+H Ouvrir le volet Commentaires pour ajouter votre commentaire ou pour répondre aux commentaires des autres utilisateurs. Ouvrir le champ de commentaires Alt+H &#8984; Cmd+⌥ Option+A Ouvrir un champ de saisie où vous pouvez ajouter le texte de votre commentaire. Ouvrir le panneau \"Chat\" Alt+Q ^ Ctrl+⌥ Option+Q Ouvrir le panneau Chat et envoyer un message. Enregistrer le document Ctrl+S ^ Ctrl+S, &#8984; Cmd+S Enregistrer toutes les modifications dans le document actuellement modifié à l'aide de l'Éditeur de Documents. Le fichier actif sera enregistré avec son nom de fichier actuel, son emplacement et son format de fichier. Imprimer le document Ctrl+P ^ Ctrl+P, &#8984; Cmd+P Imprimer le document avec l'une des imprimantes disponibles ou l'enregistrer sous forme de fichier. Enregistrer (Télécharger comme) Ctrl+⇧ Shift+S ^ Ctrl+⇧ Shift+S, &#8984; Cmd+⇧ Shift+S Ouvrir l'onglet Télécharger comme pour enregistrer le document actuellement affiché sur le disque dur de l'ordinateur dans l'un des formats pris en charge : DOCX, PDF, ODT, TXT, DOTX, PDF/A, OTT, RTF, HTML. Plein écran F11 Passer à l'affichage plein écran pour adapter l'Éditeur de Documents à votre écran. Menu d'aide F1 F1 Ouvrir le menu Aide de l'Éditeur de Documents Ouvrir un fichier existant (Desktop Editors) Ctrl+O L'onglet Ouvrir fichier local dans Desktop Editors, ouvre la boîte de dialogue standard qui permet de sélectionner un fichier existant. Fermer un fichier (Desktop Editors) Ctrl+W, Ctrl+F4 ^ Ctrl+W, &#8984; Cmd+W Fermer la fenêtre du document en cours de modification dans Desktop Editors. Menu contextuel de l'élément ⇧ Shift+F10 ⇧ Shift+F10 Ouvrir le menu contextuel de l'élément sélectionné. Réinitialiser le niveau de zoom Ctrl+0 ^ Ctrl+0 or &#8984; Cmd+0 Réinitialiser le niveau de zoom du document actuel par défaut à 100%. Navigation Sauter au début de la ligne Début Début Placer le curseur au début de la ligne en cours d'édition. Sauter au début du document Ctrl+Début ^ Ctrl+Début Placer le curseur au début du document en cours d'édition. Sauter à la fin de la ligne Fin Fin Placer le curseur à la fin de la ligne en cours d'édition. Sauter à la fin du document Ctrl+Fin ^ Ctrl+Fin Placer le curseur à la fin du document en cours d'édition. Sauter au début de la page précédente Alt+Ctrl+Pg. préc Placez le curseur au tout début de la page qui précède la page en cours d'édition. Sauter au début de la page suivante Alt+Ctrl+Pg. suiv ⌥ Option+&#8984; Cmd+⇧ Shift+Pg. suiv Placez le curseur au tout début de la page qui suit la page en cours d'édition. Faire défiler vers le bas Pg. suiv Pg. suiv, ⌥ Option+Fn+↑ Faire défiler le document vers le bas d'une page visible. Faire défiler vers le haut Pg. préc Pg. préc, ⌥ Option+Fn+↓ Faire défiler le document vers le haut d'une page visible. Page suivante Alt+Pg. suiv ⌥ Option+Pg. suiv Passer à la page suivante du document en cours d'édition. Page précédente Alt+Pg. préc ⌥ Option+Pg. préc Passer à la page précédente du document en cours d'édition. Zoom avant Ctrl++ ^ Ctrl+=, &#8984; Cmd+= Agrandir le zoom du document en cours d'édition. Zoom arrière Ctrl+- ^ Ctrl+-, &#8984; Cmd+- Réduire le zoom du document en cours d'édition. Déplacer un caractère vers la gauche ← ← Déplacer le curseur d'un caractère vers la gauche. Déplacer un caractère vers la droite → → Déplacer le curseur d'un caractère vers la droite. Déplacer vers le début d'un mot ou un mot vers la gauche Ctrl+← ^ Ctrl+←, &#8984; Cmd+← Déplacer le curseur au début d'un mot ou d'un mot vers la gauche. Déplacer un caractère vers la droite Ctrl+→ ^ Ctrl+→, &#8984; Cmd+→ Déplacer le curseur d'un mot vers la droite. Déplacer une ligne vers le haut ↑ ↑ Déplacer le curseur d'une ligne vers le haut. Déplacer une ligne vers le bas ↓ ↓ Déplacer le curseur d'une ligne vers le bas. Naviguer entre les contrôles dans un dialogue modal ↹ Tab/⇧ Shift+↹ Tab ↹ Tab/⇧ Shift+↹ Tab Naviguer entre les contrôles pour mettre en évidence le contrôle précédent ou suivant dans les dialogues modaux. Écriture Terminer le paragraphe ↵ Entrée ↵ Retour Terminer le paragraphe actuel et commencer un nouveau. Ajouter un saut de ligne ⇧ Shift+↵ Entrée ⇧ Shift+↵ Retour Ajouter un saut de ligne sans commencer un nouveau paragraphe. Supprimer ← Retour arrière, Supprimer ← Retour arrière, Supprimer Supprimer un caractère à gauche (← Retour arrière) ou vers la droite (Supprimer) du curseur. Supprimer le mot à gauche du curseur Ctrl+← Retour arrière ^ Ctrl+← Retour arrière, &#8984; Cmd+← Retour arrière Supprimer un mot à gauche du curseur. Supprimer le mot à droite du curseur Ctrl+Supprimer ^ Ctrl+Supprimer, &#8984; Cmd+Supprimer Supprimer un mot à droite du curseur. Créer un espace insécable Ctrl+⇧ Shift+␣ Barre d'espace ^ Ctrl+⇧ Shift+␣ Barre d'espace, &#8984; Cmd+⇧ Shift+␣ Barre d'espace Créer un espace entre les caractères qui ne peuvent pas être utilisés pour passer à la ligne suivante. Créer un trait d'union insécable Ctrl+⇧ Shift+_ ^ Ctrl+⇧ Shift+Trait d'union, &#8984; Cmd+⇧ Shift+Trait d'union Créer un trait d'union entre les caractères qui ne peuvent pas être utilisés pour passer à la ligne suivante. Annuler et Rétablir Annuler Ctrl+Z ^ Ctrl+Z, &#8984; Cmd+Z Inverser la dernière action effectuée. Rétablir Ctrl+Y ^ Ctrl+Y, &#8984; Cmd+Y, &#8984; Cmd+⇧ Shift+Z Répéter la dernière action annulée. Couper, Copier et Coller Couper Ctrl+X, ⇧ Shift+Supprimer &#8984; Cmd+X, ⇧ Shift+Supprimer Supprimer le fragment du texte sélectionné et l'envoyer vers le presse-papiers. Le texte copié peut être inséré ensuite à un autre endroit dans le même document, dans un autre document, ou dans un autre programme. Copier Ctrl+C, Ctrl+Inser &#8984; Cmd+C Envoyer le fragment du texte sélectionné et l'envoyer vers le presse-papiers. Le texte copié peut être inséré ensuite à un autre endroit dans le même document, dans un autre document, ou dans un autre programme. Coller Ctrl+V, ⇧ Shift+Inser &#8984; Cmd+V Insérer le fragment du texte précédemment copié du presse-papiers à la position actuelle du texte. Le texte peut être copié précédemment à partir du même document, à partir d'un autre document, ou provenant d'un autre programme. Insérer un lien hypertexte Ctrl+K &#8984; Cmd+K Insérer un lien hypertexte qui peut être utilisé pour accéder à une adresse web. Copier le style Ctrl+⇧ Shift+C &#8984; Cmd+⇧ Shift+C Copier la mise en forme du fragment sélectionné du texte en cours d'édition. La mise en forme copiée peut être ensuite appliquée à un autre texte dans le même document. Appliquer le style Ctrl+⇧ Shift+V &#8984; Cmd+⇧ Shift+V Appliquer la mise en forme précédemment copiée dans le texte du document en cours d'édition. Sélection de texte Sélectionner tout Ctrl+A &#8984; Cmd+A Sélectionner tout le texte du document avec des tableaux et des images. Sélectionner une plage ⇧ Shift+→ ← ⇧ Shift+→ ← Sélectionner le texte caractère par caractère. Sélectionner depuis le curseur jusqu'au début de la ligne ⇧ Shift+Début ⇧ Shift+Début Sélectionner le fragment du texte depuis le curseur jusqu'au début de la ligne actuelle. Sélectionner depuis le curseur jusqu'à la fin de la ligne ⇧ Shift+Fin ⇧ Shift+Fin Sélectionner le fragment du texte depuis le curseur jusqu'à la fin de la ligne actuelle. Sélectionner un caractère à droite ⇧ Shift+→ ⇧ Shift+→ Sélectionner un caractère à droite de la position du curseur. Sélectionner un caractère à gauche ⇧ Shift+← ⇧ Shift+← Sélectionner un caractère à gauche de la position du curseur. Sélectionner jusqu'à la fin d'un mot Ctrl+⇧ Shift+→ Sélectionner un fragment de texte depuis le curseur jusqu'à la fin d'un mot. Sélectionner au début d'un mot Ctrl+⇧ Shift+← Sélectionner un fragment de texte depuis le curseur jusqu'au début d'un mot. Sélectionner une ligne vers le haut ⇧ Shift+↑ ⇧ Shift+↑ Sélectionner une ligne vers le haut (avec le curseur au début d'une ligne). Sélectionner une ligne vers le bas ⇧ Shift+↓ ⇧ Shift+↓ Sélectionner une ligne vers le bas (avec le curseur au début d'une ligne). Sélectionner la page vers le haut ⇧ Shift+Pg. préc ⇧ Shift+Pg. préc Sélectionner la partie de page de la position du curseur à la partie supérieure de l'écran. Sélectionner la page vers le bas ⇧ Shift+Pg. suiv ⇧ Shift+Pg. suiv Sélectionner la partie de page de la position du curseur à la partie inférieure de l'écran. Style de texte Gras Ctrl+B ^ Ctrl+B, &#8984; Cmd+B Mettre la police du fragment de texte sélectionné en gras pour lui donner plus de poids. Italique Ctrl+I ^ Ctrl+I, &#8984; Cmd+I Mettre la police du fragment de texte sélectionné en italique pour lui donner une certaine inclinaison à droite. Souligné Ctrl+U ^ Ctrl+U, &#8984; Cmd+U Souligner le fragment de texte sélectionné avec la ligne qui passe sous les lettres. Barré Ctrl+5 ^ Ctrl+5, &#8984; Cmd+5 Souligner le fragment de texte sélectionné avec la ligne qui passe sous les lettres. Indice Ctrl+. ^ Ctrl+⇧ Shift+&gt;, &#8984; Cmd+⇧ Shift+&gt; Rendre le fragment du texte sélectionné plus petit et le placer à la partie inférieure de la ligne du texte, par exemple comme dans les formules chimiques. Exposant Ctrl+, ^ Ctrl+⇧ Shift+&lt;, &#8984; Cmd+⇧ Shift+&lt; Sélectionner le fragment du texte et le placer sur la partie supérieure de la ligne de texte, par exemple comme dans les fractions. Style Titre 1 Alt+1 ⌥ Option+^ Ctrl+1 Appliquer le style Titre 1 au fragment du texte sélectionné. Style Titre 2 Alt+2 ⌥ Option+^ Ctrl+2 Appliquer le style Titre 2 au fragment du texte sélectionné. Style Titre 3 Alt+3 ⌥ Option+^ Ctrl+3 Appliquer le style Titre 3 au fragment du texte sélectionné. Liste à puces Ctrl+⇧ Shift+L ^ Ctrl+⇧ Shift+L, &#8984; Cmd+⇧ Shift+L Créer une liste à puces non numérotée du fragment de texte sélectionné ou créer une nouvelle liste. Supprimer la mise en forme Ctrl+␣ Barre d'espace Supprimer la mise en forme du fragment du texte sélectionné. Agrandir la police Ctrl+] &#8984; Cmd+] Augmenter la taille de la police du fragment de texte sélectionné de 1 point. Réduire la police Ctrl+[ &#8984; Cmd+[ Réduire la taille de la police du fragment de texte sélectionné de 1 point. Alignement centré/à gauche Ctrl+E ^ Ctrl+E, &#8984; Cmd+E Passer de l'alignement centré à l'alignement à gauche. Justifié/à gauche Ctrl+J, Ctrl+L ^ Ctrl+J, &#8984; Cmd+J Passer de l'alignement justifié à gauche. Alignement à droite/à gauche Ctrl+R ^ Ctrl+R, &#8984; Cmd+R Passer de l'aligné à droite à l'aligné à gauche. Appliquer la mise en forme de l'indice (espacement automatique) Ctrl+= Appliquer la mise en forme d'indice au fragment de texte sélectionné. Appliquer la mise en forme d’exposant (espacement automatique) Ctrl+⇧ Shift++ Appliquer la mise en forme d'exposant au fragment de texte sélectionné. Insérer des sauts de page Ctrl+↵ Entrée ^ Ctrl+↵ Retour, &#8984; Cmd+↵ Retour Insérer un saut de page à la position actuelle du curseur. Augmenter le retrait Ctrl+M ^ Ctrl+M, &#8984; Cmd+M Augmenter le retrait gauche. Réduire le retrait Ctrl+⇧ Shift+M ^ Ctrl+⇧ Shift+M, &#8984; Cmd+⇧ Shift+M Diminuer le retrait gauche. Ajouter un numéro de page Ctrl+⇧ Shift+P ^ Ctrl+⇧ Shift+P, &#8984; Cmd+⇧ Shift+P Ajoutez le numéro de page actuel à la position actuelle du curseur. Caractères non imprimables Ctrl+⇧ Shift+Num8 Afficher ou masque l'affichage des caractères non imprimables. Supprimer un caractère à gauche ← Retour arrière ← Retour arrière Supprimer un caractère à gauche du curseur. Supprimer un caractère à droite Supprimer Supprimer Supprimer un caractère à droite du curseur. Modification des objets Limiter le déplacement ⇧ Shift + faire glisser ⇧ Shift + faire glisser Limiter le déplacement de l'objet sélectionné horizontalement ou verticalement. Régler une rotation de 15 degrés ⇧ Shift + faire glisser (lors de la rotation) ⇧ Shift + faire glisser (lors de la rotation) Contraindre une rotation à un angle de 15 degrés. Conserver les proportions ⇧ Shift + faire glisser (lors du redimensionnement) ⇧ Shift + faire glisser (lors du redimensionnement) Conserver les proportions de l'objet sélectionné lors du redimensionnement. Tracer une ligne droite ou une flèche ⇧ Shift + faire glisser (lors du tracé de lignes/flèches) ⇧ Shift + faire glisser (lors du tracé de lignes/flèches) Tracer une ligne droite ou une flèche verticale/horizontale/inclinée de 45 degrés. Mouvement par incréments de 1 pixel Ctrl+← → ↑ ↓ Maintenez la touche Ctrl enfoncée en faisant glisser et utilisez les flèches pour déplacer l'objet sélectionné d'un pixel à la fois. Utilisation des tableaux Passer à la cellule suivante d’une ligne ↹ Tab ↹ Tab Aller à la cellule suivante d'une ligne de tableau. Passer à la cellule précédente d'une ligne ⇧ Shift+↹ Tab ⇧ Shift+↹ Tab Aller à la cellule précédente d'une ligne de tableau. Passer à la ligne suivante ↓ ↓ Aller à la ligne suivante d'un tableau. Passer à la ligne précédente ↑ ↑ Aller à la ligne précédente d'un tableau. Commencer un nouveau paragraphe ↵ Entrée ↵ Retour Commencer un nouveau paragraphe dans une cellule. Ajouter une nouvelle ligne ↹ Tab dans la cellule inférieure droite du tableau. ↹ Tab dans la cellule inférieure droite du tableau. Ajouter une nouvelle ligne au bas du tableau. Insérer un saut de tableau Ctrl+⇧ Shift+↵ Entrée ^ Ctrl+⇧ Shift+↵ Retour, &#8984; Cmd+⇧ Shift+↵ Retour Insérer un saut de tableau dans le tableau. Insertion des caractères spéciaux Insérer une équation Alt+= ⌥ Option+^ Ctrl+= Insérer une équation à la position actuelle du curseur. Insérer un tiret sur cadratin Alt+Ctrl+Verr.num- ⌥ Option+⇧ Shift+- Insérer un tiret sur cadratin ‘—’ dans le document actuel à droite du curseur. Insérer un trait d'union insécable Ctrl+⇧ Shift+_ ^ Ctrl+⇧ Shift+Trait d'union, &#8984; Cmd+⇧ Shift+Trait d'union Insérer un trait d'union insécable ‘-’ dans le document actuel à droite du curseur. Insérer un espace insécable Ctrl+⇧ Shift+␣ Barre d'espace ⌥ Option+␣ Barre d'espace Insérer un espace insécable ‘o’ dans le document actuel à droite du curseur."
    },
   {
        "id": "HelpfulHints/Navigation.htm", 
        "title": "Paramètres d'affichage et outils de navigation", 
        "body": "Document Editor est doté de plusieurs outils qui vous aide à visionner et naviguer à travers votre document: le zoom, l'indicateur de numéro de page etc. Régler les paramètres d'affichage Pour régler les paramètres d'affichage par défaut et définir le mode le plus pratique pour travailler avec un document, passez à l'onglet Affichage et choisissez d'afficher ou de masquer les éléments d'interface. Les options disponibles sous l'onglet Affichage: En-têtes sert à afficher les en-têtes du document sur le panneau à gauche. Zoom sert à définir la valeur de zoom de 50% à 200% en sélectionnant de la liste déroulante. Ajuster à la page sert à adapter la page entière du document à la partie visible de la zone de travail. Ajuster à la largeur sert à adapter la largeur de la page du document à la partie visible de la zone de travail.. Thème d'interface - sélectionnez l'une des thèmes d'interface disponibles de la liste déroulante: Identique à système, Claire, Classique claire, Sombre, Contraste élevé sombre. Lors de l'activation du thème Sombre ou Contraste élevé sombre, l'option Document sombre devient actif. Utilisez cette option pour définir la couleur de la zone de travail sur blanc ou grise. Toujours afficher la barre d'outils - une fois désactivé, la barre d'outils supérieure comportant toutes les commandes sera masquée mais tous les onglets restent visibles. Vous pouvez également double-cliquer sur un onglet pour masquer la barre d'outils supérieure ou l'afficher à nouveau. Barre d'état - une fois désactivé, la barre qui se trouve tout en bas avec les boutons indiquant les Numéros de pages, les Statistiques et le Zoom sera masquée. Activez cette option pour afficher la Barre d'état masquée. Règles une fois désactivé, les règles qui sont utilisées pour aligner le texte, les graphiques, les tableaux et d'autres éléments dans un document, pour définir des marges, des tabulations et des retraits de paragraphe sont masquées. Pour afficher les Règles, activez cette option. Panneau gauche - une fois désactivé, le panneau gauche comportant les onglets Rechercher, Commentaireset autres onglets sera masqué. Pour afficher le panneau gauche, cochez cette case. Panneau droit - une fois désactivé, le panneau droit comportant les options de configurations des Paramètres sera masqué. Pour afficher le panneau droit, cochez cette case. La barre latérale sur la droite est réduite par défaut. Pour l'agrandir, sélectionnez un objet (par exemple, image, graphique, forme) ou un passage de texte et cliquez sur l'icône de l'onglet actuellement activé sur la droite. Pour réduire la barre latérale sur la droite, cliquez à nouveau sur l'icône. Lorsque le panneau Commentaires ou Chat , la largeur de la barre latérale gauche est ajustée par simple glisser-déposer: déplacez le curseur de la souris sur la bordure gauche pour qu'elle se transforme en flèche bidirectionnelle et déplacez la bordure vers la droite pour agrandir la largeur du panneau latéral. Pour rétablir sa largeur originale faites glisser le bord à gauche. Utiliser les outils de navigation Pour naviguer à travers votre document, utilisez les outils suivants: Les boutons Zoom sont situés en bas à droite et sont utilisés pour faire un zoom avant et arrière dans le document actif. Pour modifier la valeur de zoom sélectionnée en pourcentage, cliquez dessus et sélectionnez l'une des options de zoom disponibles dans la liste (50% / 75% / 100% / 125% / 150% / 175% / 200% / 300% / 400% / 500%) ou utilisez les boutons Zoom avant ou Zoom arrière . Cliquez sur l'icône Ajuster à la largeur pour adapter la largeur de la page du document à la partie visible de la zone de travail.. Pour adapter la page entière du document à la partie visible de la zone de travail, cliquez sur l'icône Ajuster à la page . Les options de zoom sont également disponibles sous l'onglet Affichage. Numéro de page affiche la page active dans l'ensemble des pages du document actif (page 'n' sur 'nn'). Cliquez sur ce libellé pour ouvrir la fenêtre où vous pouvez entrer le numéro de la page et y accéder rapidement. Statistiques affiche des informations statistiques sur votre document."
    },
   {
        "id": "HelpfulHints/Password.htm", 
        "title": "Protéger un document avec un mot de passe", 
        "body": "Vous pouvez protéger vos documents avec un mot de passe afin que tous les coauteurs puissent d'accéder en mode d'édition. On peut modifier ou supprimer le mot de passe, le cas échéant. Il n'est pas possible de récupérer un mot de passe perdu ou oublié. Gardez vos mots de passe dans un endroit sécurisé. Il est également possible de protéger vos documents en limitant l'accès. Définir un mot de passe passez à l'onglet Fichier de la barre d'outils supérieure et sélectionnez l'option Protéger. ou passer à l'onglet Protection et cliquer sur l'icône Chiffrer. définissez le mot de passe dans le champ Mot de passe et validez-le dans le champ Confirmer le mot de passe au-dessous. Cliquez sur pour afficher ou masquer les caractères lors de la saisie. Cliquez sur OK pour valider. Pour ouvrir le document avec les permissions d'accès complet, l'utilisateur doit saisir ce mot de passe. Modifier le mot de passe passez à l'onglet Fichier de la barre d'outils supérieure et sélectionnez l'option Protéger. ou passer à l'onglet Protection et cliquer sur l'icône Chiffrer. définissez le nouveau mot de passe dans le champ Mot de passe et validez-le dans le champ Confirmer le mot de passe au-dessous. Cliquez sur pour afficher ou masquer les caractères lors de la saisie. cliquez sur le bouton Modifier un mot de passe. saisissez le mot de passe dans le champ Mot de passe et validez-le dans le champ Confirmer le mot de passe au-dessous, ensuite cliquez sur OK. Supprimer le mot de passe passez à l'onglet Fichier de la barre d'outils supérieure, choisissez l'option Protéger, cliquez sur le bouton Supprimer un mot de passe. Protéger un document passez à l'onglet Protection, cliquez sur le bouton Protéger le document le cas échéant, définissez le mot de passe, sélectionnez les permissions d'accès appropriées au document si l'utilisateur n'a pas encore saisi le mot de passe: Aucune modification (lecture seule) - l'utilisateur peut uniquement afficher le document. Remplissage des formulaires - l'utilisateur peut uniquement remplir le formulaire. Modifications - l'utilisateur peut uniquement afficher le suivi de modifications du document. Commentaires - l'utilisateur peut uniquement commenter ou répondre au commentaires. cliquez sur Protéger pour valider. Pour supprimer la protection du document protégé par un mot de passe, passez à l'onglet Protection, cliquez sur le bouton Protéger le document saisissez le mot de passe dans la fenêtre Déprotéger le document."
    },
   {
        "id": "HelpfulHints/Review.htm", 
        "title": "Réviser des documents", 
        "body": "Éditeur de Documents permet de gérer le flux de travail continu par l'ensemble de l'équipe : partager des fichiers et des dossiers, collaborer sur des documents en temps réel, communiquer directement depuis l'éditeur, laisser des commentaires pour des fragments du document nécessitant la participation d'une tierce personne, sauvegarder des versions du document pour une utilisation ultérieure, comparer et fusionner pour faciliter le traitement et l'édition. Lorsque quelqu'un disposant des autorisations de révision partage avec vous un fichier, vous devez utiliser la fonction Révision du document. Dans l'Éditeur de Documents, si vous êtes le relecteur, vous pouvez utiliser l'option Révision pour réviser le document, modifier des propositions, des phrases et d'autres éléments de la page, corriger l'orthographe et faire d'autres modifications dans le document sans l'éditer. Toutes vos modifications seront enregistrées et montrées à la personne qui vous a envoyé le document. Si vous êtes la personne qui envoie le fichier pour la révision, vous devrez afficher toutes les modifications qui y ont été apportées, les afficher et les accepter ou les rejeter. Activer la fonctionnalité Suivi des modifications Pour voir les modifications suggérées par un réviseur, activez l'option Suivi des modifications de l'une des manières suivantes : cliquez sur le bouton dans le coin inférieur droit de la barre d'état, ou passez à l'onglet Collaboration de la barre d'outils supérieure et cliquez sur le bouton Suivi des modifications. Il n'est pas nécessaire que le réviseur active l'option Suivi des modifications. Elle est activée par défaut et ne peut pas être désactivée lorsque le document est partagé avec des droits d'accès de révision uniquement. Les options disponibles pour dans le menu contextuel : ON pour moi - le suivi des modifications est activé uniquement pour l'utilisateur actuel. L'option est activée pendant la session d'édition actuelle, c-à-d elle est désactivée lors de l'actualisation ou la réouverture du document. L'activation et la désactivation du suivi des modifications par d'autres utilisateurs ne l'affecte pas. OFF pour moi - le suivi des modifications est désactivé uniquement pour l'utilisateur actuel. L'option demeure désactivée pendant la session d'édition actuelle. L'activation et la désactivation du suivi des modifications par d'autres utilisateurs ne l'affecte pas. ON pour moi et tout le monde - le suivi des modifications est actif et demeure activé lors de l'actualisation ou la réouverture du document (le suivi des modifications sera actif pour tous les utilisateurs après la mise à jour du document). Lorsque le suivi des modifications est désactivé par d'autres utilisateurs, ceux-ci passe en mode OFF pour moi et tout le monde. OFF pour moi et tout le monde - le suivi des modifications est désactivé et demeure désactivé lors de l'actualisation ou la réouverture du document (le suivi des modifications sera désactivé pour tous les utilisateurs après la mise à jour du document). Lorsque le suivi des modifications est activé par d'autres utilisateurs, ceux-ci passe en mode ON pour moi et tout le monde. Un message d'alerte s'affiche pour tous les utilisateurs qui travaillent sur le même document. Suivi des modifications Toutes les modifications apportées par un autre utilisateur sont marquées par différentes couleurs dans le texte. Lorsque vous cliquez sur le texte modifié, une bulle apparaîtra comportant le nom d'utilisateur, la date et l'heure de la modification et une description de la modification. Les icônes dan la bulle permettent d'accepter ou de rejeter la modification actuelle. Lorsque vous glissez et déposez du texte dans un document, ce texte est marqué d'un soulignement double. Le texte d'origine est signalé par un barré double. Ceux-ci sont considérés comme une seule modification Cliquez sur le texte mis en forme de double barré dans la position initiale et appuyer sur la flèche dans la bulle de la modification pour vous déplacer vers le texte en position nouvelle. Cliquez sur le texte mis en forme d'un soulignement double dans la nouvelle position et appuyer sur la flèche dans la bulle de la modification pour vous déplacer vers le texte en position initiale. Choisir le mode d'affichage des modifications Cliquez sur le bouton Mode d'affichage dans la barre d'outils supérieure et sélectionnez l'un des modes disponibles dans la liste : Balisage et bulles - cette option est sélectionnée par défaut. Elle permet à la fois d'afficher les modifications suggérées et de modifier le document. Les modifications sont mises en surbrillance dans le document et s'affichent dans les bulles. Balisage uniquement - ce mode permet d'afficher toutes les modifications et de modifier le document. Les modifications s'affichent uniquement dans le documents et toutes les bulles sont masquées. Final - ce mode est utilisé pour afficher toutes les modifications comme si elles étaient acceptées. Cette option n'accepte pas toutes les modifications, elle vous permet de voir à quoi ressemblera le document après avoir accepté toutes les modifications. Dans ce mode, vous ne pouvez pas modifier le document. Original - ce mode est utilisé pour afficher toutes les modifications comme si elles avaient été rejetées. Cette option ne rejette pas toutes les modifications, elle vous permet uniquement d'afficher le document sans modifications. Dans ce mode, vous ne pouvez pas modifier le document. Accepter ou rejeter les modifications Utilisez les boutons Précédente et Suivante de la barre d'outils supérieure pour naviguer entre les modifications. Pour accepter la modification actuellement sélectionnée, vous pouvez : cliquez sur le bouton Accepter de la barre d'outils supérieure, ou cliquer sur la flèche vers le bas au-dessous du bouton Accepter et sélectionnez l'option Accepter la modification en cours (dans ce cas, la modification sera acceptée et vous passerez à la modification suivante), ou cliquez sur le bouton Accepter dans la bulle de modification. Pour accepter rapidement toutes les modifications, cliquez sur la flèche vers le bas au-dessous du bouton Accepter et sélectionnez l'option Accepter toutes les modifications. Pour rejeter la modification actuelle, vous pouvez : cliquer sur le bouton Rejeter de la barre d'outils supérieure, ou cliquer sur la flèche vers le bas au-dessous du bouton Rejeter et sélectionnez l'option Rejeter la modification en cours (dans ce cas, la modification sera rejetée et vous passerez à la modification suivante), ou cliquez sur le bouton Rejeter dans la bulle de modification. Pour rejeter rapidement toutes les modifications, cliquez sur la flèche vers le bas au-dessous du bouton Rejeter et sélectionnez l'option Rejeter toutes les modifications. Si vous souhaitez accepter ou rejeter une modification, faites un clic droit dessus et sélectionnez Accepter la modification ou Rejeter la modification dans le menu contextuel. Si vous révisez le document, les options Accepter et Rejeter ne sont pas disponibles pour vous. Vous pouvez supprimer vos modifications en utilisant l'icône dans la bulle de modification."
    },
   {
        "id": "HelpfulHints/Search.htm", 
        "title": "Fonction de recherche et remplacement", 
        "body": "Pour rechercher des caractères, des mots ou des phrases utilisés dans le document, cliquez sur l'icône située sur la barre latérale gauche de l'Éditeur de Documents, sur l'icône située au coin droit en haut ou appuyez sur la combinaison de touches Ctrl+F (Command+F pour MacOS) pour ouvrir le petit panneau Recherche ou sur la combinaison de touches Ctrl+H pour ouvrir le panneau complet Recherche. Un petit panneau Recherche s'affiche au coin droit en haut de la zone de travail. Afin d'accéder aux paramètres avancés, cliquez sur l'icône ou appuyez sur la combinaison de touches Ctrl+H. La fenêtre Rechercher et remplacer s'affiche : Saisissez votre enquête dans le champ de saisie correspondant Recherche. Si vous avez besoin de remplacer une ou plusieurs occurrences des caractères saisis, saisissez le texte de remplacement dans le champ de saisie correspondant Remplacer par. Vous pouvez remplacer l'occurrence actuellement sélectionnée ou remplacer toutes les occurrences en cliquant sur les boutons correspondants Remplacer et Remplacer tout. Afin de parcourir les occurrences trouvées, cliquez sur un des boutons à flèche. Le bouton affiche l'occurrence suivante, le bouton ) affiche l'occurrence précédente. Spécifiez les paramètres de recherche en cochant les options nécessaires au-dessous du champ de saisie : Sensible à la casse - sert à trouver les occurrences saisies de la même casse (par exemple, si votre enquête est 'Éditeur' et cette option est sélectionnée, les mots tels que 'éditeur' ou 'EDITEUR' etc. ne seront pas trouvés). Seulement les mots entiers - sert à surligner les mots entiers uniquement. Toutes les occurrences seront mises en surbrillance dans le fichier et affichées sous forme de liste dans le panneau gauche Recherche. Utilisez la liste pour passer à l'occurrence requise ou utilisez les boutons de navigation et . Éditeur de Documents prend en charge la recherche de caractères spéciaux. Pour rechercher un caractère spécial, saisissez-le dans le champ de recherche. La liste des caractères spéciaux qu'on peut utiliser dans une requête Caractère spécial Description ^l Saut de ligne ^t Taquet de tabulation ^? Tout symbole ^# Tout chiffre ^$ Toute lettre ^n Saut de colonne ^e Note de fin ^f Note de bas de page ^g élément graphique ^m Saut de page ^~ Trait d'union insécable ^s Espace insécable ^^ échappement du caret lui-même ^w Tout espace ^+ Tiret cadratin ^= Tiret demi-cadratin ^y Tout tiret Les caractères qu'on peut utiliser pour remplacement : Caractère spécial Description ^l Saut de ligne ^t Taquet de tabulation ^n Saut de colonne ^m Saut de page ^~ Trait d'union insécable ^s Espace insécable ^+ Tiret cadratin ^= Tiret demi-cadratin"
    },
   {
        "id": "HelpfulHints/SpellChecking.htm", 
        "title": "Vérification de l'orthographe", 
        "body": "Éditeur de Documents vous permet de vérifier l'orthographe du texte saisi dans une certaine langue et corriger des fautes lors de l'édition. L'édition de bureau de tous les trois éditeurs permet d'ajouter les mots au dictionnaire personnel. À partir de la version 6.3, les éditeurs ONLYOFFICE prennent en chatge l'interface SharedWorker pour un meilleur fonctionnement en évitant une utilisation élevée de la mémoire. Si votre navigateur ne prend pas en charge SharedWorker, alors c'est seulement Worker qui sera actif. Pour en savoir plus sur SharedWorker, veuillez consulter cette page. Tout d'abord, choisissez la langue pour tout le document. cliquer sur l'icône Définir la langue du document dans la barre d'état. Dans la fenêtre ouverte sélectionnez la langue nécessaire et cliquez sur OK. La langue sélectionnée sera appliquée à tout le document. Pour sélectionner une langue différente pour un fragment, sélectionnez le fragment nécessaire avec la souris et utilisez le menu de la barre d'état. Pour activer l'option de vérification orthographique, vous pouvez : cliquer sur l'icône Vérification orthographique dans la barre d'état, ou basculer vers l'onglet Fichier de la barre d'outils supérieure, aller à la section Paramètres avancés..., cocher la case Activer la vérification orthographique et cliquer sur le bouton Appliquer. Les mots mal orthographiés seront soulignés par une ligne rouge. Cliquez droit sur le mot nécessaire pour activer le menu et : choisissez une des variantes suggérées pour remplacer le mot mal orthographié. S'il y a trop de variantes, l'option Plus de variantes... apparaît dans le menu ; utilisez l'option Ignorer pour ignorer juste ce mot et supprimer le surlignage ou Ignorer tout pour ignorer tous les mots identiques présentés dans le texte ; si le mot n’est pas dans le dictionnaire, vous pouvez l’ajouter au votre dictionnaire personnel. La foi prochaine ce mot ne sera donc plus considéré comme erroné. Cette option est disponible sur l’édition de bureau. sélectionnez une autre langue pour ce mot. Pour désactiver l'option de vérification orthographique, vous pouvez : cliquer sur l'icône Vérification orthographique dans la barre d'état, ou ouvrir l'onglet Fichier de la barre d'outils supérieure, sélectionner l'option Paramètres avancés..., décocher la case Activer la vérification orthographique, et cliquer sur le bouton Appliquer."
    },
   {
        "id": "HelpfulHints/SupportedFormats.htm", 
        "title": "Formats des documents électroniques pris en charge", 
        "body": "Les documents électroniques représentent l'un des types des fichiers les plus utilisés en informatique. Grâce à l'utilisation du réseau informatique tant développé aujourd'hui, il est possible et plus pratique de distribuer des documents électroniques que des versions imprimées. Les formats de fichier ouverts et propriétaires sont bien nombreux à cause de la variété des périphériques utilisés pour la présentation des documents. Éditeur de Documents prend en charge les formats les plus populaires. Lors du téléchargement ou de l'ouverture d'un fichier, celui-ci sera converti au format Open Office XML (DOCX). Cette conversion permet d'accélérer le traitement des fichiers et d'améliorer l'interopérabilité des données. Le tableau ci-dessous présente les formats de fichiers pour l'affichage et/ou pour l'édition. Formats Description Affichage au format natif Affichage lors de la conversion en OOXML Édition au format natif Édition lors de la conversion en OOXML DjVu Le format de fichier conçu principalement pour stocker les documents numérisés, en particulier ceux qui contiennent une combinaison du texte, des dessins au trait et des photographies + DOC L'extension de nom de fichier pour les documents du traitement textuel créé avec Microsoft Word + + DOCM Macro-Enabled Microsoft Word Document Une extension de fichier Microsoft Word 2007 ou version ultérieure comportant des macros incorporées pouvant être exécutées dans le document. + + DOCX Office Open XML Le format de fichier compressé basé sur XML développé par Microsoft pour représenter des feuilles de calcul et les graphiques, les présentations et les document du traitement textuel + + DOCXF Le format pour créer, modifier et collaborer sur un Modèle de formulaire. + + DOTX Word Open XML Document Template Format de fichier zippé, basé sur XML, développé par Microsoft pour les modèles de documents texte. Un modèle DOTX contient des paramètres de mise en forme, des styles, etc. et peut être utilisé pour créer plusieurs documents avec la même mise en forme + + EPUB Electronic Publication Le format ebook standardisé, gratuit et ouvert créé par l'International Digital Publishing Forum + + FB2 Une extension de livres électroniques qui peut être lancé par votre ordinateur ou appareil mobile + + HTML HyperText Markup Language Le principale langage de balisage pour les pages web + + ODT Le format de fichier du traitement textuel d'OpenDocument, le standard ouvert pour les documents électroniques + + OFORM Le format pour remplir un formulaire. Les champs du formulaire sont à remplir mais les utilisateurs ne peuvent pas modifier la mise en forme ou les paramètres des éléments du formulaire.* + + OTT OpenDocument Document Template Format de fichier OpenDocument pour les modèles de document texte. Un modèle OTT contient des paramètres de mise en forme, des styles, etc. et peut être utilisé pour créer plusieurs documents avec la même mise en forme + + PDF Portable Document Format Format de fichier utilisé pour représenter les documents d'une manière indépendante du logiciel, du matériel et des systèmes d'exploitation + PDF/A Portable Document Format / A Une version normalisée ISO du format PDF (Portable Document Format) conçue pour l'archivage et la conservation à long terme des documents électroniques. + RTF Rich Text Format Le format de fichier du document développé par Microsoft pour la multiplateforme d'échange des documents + + TXT L'extension de nom de fichier pour les fichiers de texte contenant habituellement une mise en forme minimale + + XML Extensible Markup Language (XML). Le langage de balisage extensible est une forme restreinte d'application du langage de balisage généralisé standard SGM (ISO 8879) conçu pour stockage et traitement de données. + + XPS Open XML Paper Specification Le format ouvert de la mise en page fixe, libre de redevance créé par Microsoft + *Remarque : le format OFORM c'est un format qui sert à remplir un formulaire. Alors, ce sont des champs du formulaire qui sont modifiables. Le tableau ci-dessous présente les formats pris en charge pour le téléchargement d'un document dans le menu Fichier -> Télécharger comme. Format en entrée Téléchargeable comme DjVu DjVu, PDF DOC DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT DOCM DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT DOCX DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT DOCXF DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT DOTX DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT EPUB DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT FB2 DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT HTML DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT ODT DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT OFORM DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT OTT DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT PDF DOCX, DOCXF, DOTX, EPUB, FB2, HTML, OFORM, PDF, RTF, TXT PDF/A DOCX, DOCXF, DOTX, EPUB, FB2, HTML, OFORM, PDF, RTF, TXT RTF DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT TXT DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT XML DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT XPS DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT, XPS Veuillez consulter la matrice de conversion sur api.onlyoffice.com pour vérifier s'il est possible de convertir vos documents dans des formats les plus populaires."
    },
   {
        "id": "HelpfulHints/VersionHistory.htm", 
        "title": "Historique des versions", 
        "body": "Éditeur de Documents permet de gérer le flux de travail continu par l'ensemble de l'équipe : partager des fichiers et des dossiers, collaborer sur des documents en temps réel, communiquer directement depuis l'éditeur, laisser des commentaires pour des fragments du document nécessitant la participation d'une tierce personne, réviser les documents et ajouter les modifications sans modifier le fichier, comparer et fusionner les documents pour faciliter le traitement et l'édition. Dans l'Éditeur de Documents vous pouvez afficher l'historique des versions du document sur lequel vous collaborez. Afficher l'historique des versions : Pour afficher toutes les modifications apportées au document, passez à l'onglet Fichier, sélectionnez l'option Historique des versions sur la barre latérale gauche, ou passez à l'onglet Collaboration, accédez à l'historique des versions en utilisant l'icône Historique des versions de la barre d'outils supérieure. La liste des versions du document s'affichera à gauche comportant le nom de l'auteur de chaque version/révision, la date et l'heure de création. Pour les versions de document, le numéro de la version est également indiqué (par exemple ver. 2). Afficher la version : Pour savoir exactement quelles modifications ont été apportés à chaque version/révision, vous pouvez voir celle qui vous intéresse en cliquant dessus dans la barre latérale de gauche. Les modifications apportées par l'auteur de la version/révision sont marquées avec la couleur qui est affichée à côté du nom de l'auteur dans la barre latérale gauche. Pour revenir à la version actuelle du document, cliquez sur Fermer l'historique en haut de le liste des versions. Restaurer une version : Si vous souhaitez restaurer l'une des versions précédentes du document, cliquez sur Restaurer au-dessous de la version/révision sélectionnée. Pour en savoir plus sur la gestion des versions et des révisions intermédiaires, et sur la restauration des versions précédentes, veuillez consulter cet article."
    },
   {
        "id": "HelpfulHints/Viewer.htm", 
        "title": "Lecteur de documents ONLYOFFICE", 
        "body": "Vous pouvez utiliser Lecteur de documents ONLYOFFICE pour ouvrir et parcourir des fichiers PDF, XPS et DjVu. Le lecteur de documents ONLYOFFICE permet de : consulter des fichiers PDF, XPS, DjVu, ajouter des annotations à l'aide de chat, parcourir des fichiers à l'aide du panneau de navigation et des vignettes de page, utiliser l'outil de sélection et l'outil Main, imprimer et télécharger des fichiers, utiliser des liens internes et externes, accéder aux paramètres avancés du fichier dans l'éditeur et consulter le descriptif du document en utilisant l'onglet Fichier et Affichage : Emplacement (disponible uniquement dans la version en ligne) le dossier dans le module Documents où le fichier est stocké. Propriétaire (disponible uniquement dans la version en ligne) - le nom de l'utilisateur qui a créé le fichier. Chargé (disponible uniquement dans la version en ligne) - la date et l'heure quand le fichier a été téléchargé. Statistiques - le nombre de pages, paragraphes, mots, symboles, symboles avec des espaces. Taille de la page - la taille des pages dans le fichier Dernière modification - la date et l'heure quand le fichier a été modifié la dernière fois. Créé - la date et l'heure quand le fichier a été créé. Application - l'application dans laquelle on a créé le document. Auteur - la personne qui a créé le document. Producteur PDF - l'application qu'on a utilisé pour convertir le document en PDF. Version PDF - la version du fichier PDF : PDF marqué - affiche si le fichier PDF comporte des balises. Affichage rapide sur le Web - affiche si l'affichage rapide des pages web a été activé pour ce document. utiliser les modules complémentaires Les modules complémentaires disponibles dans la version du bureau : Traducteur, Send, Thésaurus. Les modules complémentaires disponibles dans la version en ligne : Controls example, Get and paste html, Telegram, Typograf, Count word, Speech, Thésaurus, Traducteur. L'interface du Lecteur de documents ONLYOFFICE : La barre d'outils supérieure fournit l'accès aux onglets Fichier, Affichage et Plug-ins et aux icônes suivantes : Imprimer permet d'imprimer le fichier ; Télécharger permet de sauvegarder le fichier sur votre orrdinateur ; Partager (disponible uniquement dans la version en ligne) permet de définir les droits d'accès au fichier directement à partir du document : inviter de nouveaux utilisateurs leur donnant les permissions de modifier, lire, commenter, remplir des formulaires ou réviser le document, ou refuser à certains utilisateurs des droits d'accès au fichier ; Ouvrir l'emplacement du fichier dans la version de bureau, sert à ouvrir le dossier où le fichier est stocké dans la fenêtre Explorateur de fichiers. Dans la version en ligne, elle permet d'ouvrir le dossier du module Documents où le fichier est stocké dans un nouvel onglet du navigateur ; Marquer en tant que favori / Enlever des favoris (disponible uniquement dans la version en ligne) cliquez sur l'étoile vide pour ajouter le fichier aux favoris et le retrouver rapidement ou cliquez sur l'étoile remplie pour effacer le fichier des favoris. Ce n'est qu'un fichier de raccourcis car le fichier lui-même est dans l'emplacement de stockage d'origine. Le fichier réel n'est pas supprimé quand vous le supprimez des favoris ; Utilisateur affiche le nom d'utilisateur lorsque vous placer le curseur de votre souris sur l'icône ; Recherche - permet de rechercher dans le document un mot ou un symbole particulier, etc. La Barre d'état au bas de la fenêtre du Lecteur de documents ONLYOFFICE contient l'indicateur du numéro de page et affiche certaines notifications des processus d'arrière plan. La barre d'état comporte aussi les outils suivants : Outil de sélection sert à sélectionner du texte dans un fichier. Outil Main sert à faire glisser et défiler la page. Ajuster à la page sert à redimensionner la page pour afficher une page entière sur l'écran. Ajuster à la largeur sert à redimensionner la page pour l'adapter à la largeur de l'écran. Outil zoom sert à zoomer et dézoomer sur une page. La barre latérale gauche comporte les icônes suivantes : - permet d'utiliser l'outil Rechercher et remplacer , - (disponible dans la version en ligne seulement) permet d'ouvrir le panneau de Chat, - permet d'ouvrir le panneau de Navigation pour parcourir la liste hiérarchique des titres avec des éléments de navigation imbriqués. Cliquez sur le titre pour passer directement sur la page nécessaire. Cliquez sur l'icône Paramètres à droite du panneau Titres et utilisez l'une des options disponibles dans le menu : Développer tout pour développer tous les niveaux de titres sur le panneau de navigation. Réduire tout pour réduire touts les niveaux de titres sauf le niveau 1 sur le panneau de Navigation. Développer jusqu'au niveau pour développer la liste hiérarchique des titres jusqu'au niveau sélectionné. Par ex., si vous sélectionnez le niveau 3, on va développer les niveaux 1, 2 et 3 mais le niveau 4 et tous niveaux inférieurs seront réduits. Taille de la police – pour personnaliser la taille de la police du texte sur le panneau Navigation en choisissant l'un des préréglages disponibles : Petit, Moyen et Grand. Renvoyer à la ligne les longs titres – pour renvoyer à la ligne le texte des longs titres. Pour développer et réduire chaque niveau de titres à part, utilisez la flèche à droit du titre. Pour fermer le panneau de Navigation, cliquez sur l'icône Navigation sur la barre latérale gauche encore une fois. - sert à afficher les vignettes des pages afin de parcourir rapidement un document. Cliquez sur sur le panneau Miniatures des pages pour accéder aux Paramètres des miniatures : Faites glisser le curseur pour définir la taille de la vignette, Par défaut, l'option Mettre en surbrillance la partie visible de la page est active pour indiquer sur la vignette la zone affichée maintenant sur l'écran. Cliquez sur la case à coché pour désactiver cette option. Pour fermer le panneau Miniatures des pages, cliquez sur l'icône Miniatures des pages sur la barre latérale gauche encore une fois. - permet de contacter notre équipe d'assistance technique, - (disponible uniquement dans la version en ligne) permet de visualiser les informations sur le programme."
    },
   {
        "id": "ProgramInterface/FileTab.htm", 
        "title": "Onglet Fichier", 
        "body": "L'onglet Fichier dans l'Éditeur de Documents permet d'effectuer certaines opérations de base sur le fichier en cours. Fenêtre de l'onglet dans l'Éditeur de Documents en ligne : Fenêtre de l'onglet dans l'Éditeur de Documents de bureau : En utilisant cet onglet, vous pouvez : dans la version en ligne : enregistrer le fichier actuel (si l'option Enregistrement automatique est désactivée), enregistrer le document dans le format sélectionné sur le disque dur de l'ordinateur en utilisant Télécharger comme , enregistrer une copie du document dans le format sélectionné dans les documents du portail en utilisant Enregistrer la copie sous , imprimer ou renommer le fichier actuel. dans la version de bureau : enregistrer le fichier actuel en conservant le format et l'emplacement actuel à l'aide de l'option Enregistrer ou enregistrer le fichier actuel avec un nom, un emplacement ou un format différent à l'aide de l'option Enregistrer sous, imprimer le fichier actuel protéger le fichier avec un mot de passe, modifier ou supprimer le mot de passe; protéger un fichier avec une signature numérique (disponible dans la version de bureau uniquement) créer un nouveau document ou en ouvrir un récemment édité (disponible dans la version en ligne uniquement), voir le descriptif du document ou modifier les paramètres du fichier, gérer les droits d'accès (disponible dans la version en ligne uniquement), suivre l'historique des versions (disponible dans la version en ligne uniquement), accéder aux Paramètres avancés de l'éditeur, dans la version de bureau, ouvrez le dossier où le fichier est stocké dans la fenêtre Explorateur de fichiers. Dans la version en ligne, ouvrez le dossier du module Documents où le fichier est stocké dans un nouvel onglet du navigateur."
    },
   {
        "id": "ProgramInterface/FormsTab.htm", 
        "title": "Onglet Formulaires", 
        "body": "Remarque: cet ongle est disponible uniquement avec des fichiers au format DOCXF. L'onglet Formulaires permet de créer des formulaires à remplir dans votre document, par ex. les projets de contrats ou les enquêtes. Ajoutez, mettez en forme et paramétrez votre texte et des champs de formulaire aussi complexe soit-il. La fenêtre de l'onglet dans Document Editor en ligne: La fenêtre de l'onglet dans Document Editor de bureau: En utilisant cet onglet, vous pouvez: ajouter et modifier des champs texte des zones de liste déroulante, listes déroulantes des cases à cocher des boutons radio, des champs image adresses e-mail numéros de téléphone date et heure code postales numéros de carte bancaire champs complexes effacer tous les champs et paramétrer la surbrillance, naviguer entre les champs du formulaire en utilisant les boutons Champ précédent et Champ suivant, afficher un aperçu du formulaire final, gérer des rôles, enregistrer le formulaire en tant que formulaire remplissable au format OFORM."
    },
   {
        "id": "ProgramInterface/HomeTab.htm", 
        "title": "Onglet Accueil", 
        "body": "L'onglet Accueil dans l'Éditeur de Documents s'ouvre par défaut lorsque vous ouvrez un document. Il permet de formater la police et les paragraphes. D'autres options sont également disponibles ici, telles que Fusion et publipostage et le jeu de couleurs. Fenêtre de l'Éditeur de Documents en ligne : Fenêtre de l'Éditeur de Documents de bureau : En utilisant cet onglet, vous pouvez : Définir le type de police, la taille et la couleur, Appliquer les styles de police, Sélectionner la couleur d'arrière-plan pour un paragraphe, Créer des listes à puces et numérotées, Changer les retraits de paragraphe, Régler l'interligne du paragraphe, Aligner le texte d'un paragraphe, Afficher/masquer les caractères non imprimables, Copier/effacer la mise en forme du texte, Modifier le jeu de couleurs, Utiliser Fusion et publipostage (disponible uniquement dans la version en ligne), Gérer les styles."
    },
   {
        "id": "ProgramInterface/InsertTab.htm", 
        "title": "Onglet Insertion", 
        "body": "L'onglet Insertion dans Document Editor permet d'ajouter des éléments de mise en forme de la page, des objets visuels et des commentaires. La fenêtre de l'onglet dans Document Editor en ligne: La fenêtre de l'onglet dans Document Editor de bureau: En utilisant cet onglet, vous pouvez: ajouter une page vide, insérer des sauts de page, des sauts de section et des sauts de colonne, ajouter des tableaux, des images, des graphiques, des formes, ajouter des liens hypertexte, des commentaires, ajouter des en-têtes et des pieds de page, des numéros de page, la date et l'heure, ajouter des zones tde texte et des objets Text Art, des équations, des symboles, des lettrines, des contrôles de contenu, insérer des graphiques SmartArt."
    },
   {
        "id": "ProgramInterface/LayoutTab.htm", 
        "title": "Onglet Mise en page - ONLYOFFICE", 
        "body": "Onglet Mise en page L'onglet Mise en page dans l'Éditeur de Documents permet de modifier l'apparence du document : configurer les paramètres de la page et définir la mise en page des éléments visuels. Fenêtre de l'Éditeur de Documents en ligne : Fenêtre de l'Éditeur de Documents de bureau : En utilisant cet onglet, vous pouvez : paramétrer les marges, l'orientation et la taillede la page, ajouter des colonnes, insérer des sauts de page, des sauts de section et des sauts de colonne, insérer des numéros des lignes aligner et grouper des objets (des tableaux, des images, des graphiques, des formes), modifier le retour à la ligne et modifier les limites de renvoi à la ligne, ajouter un filigrane."
    },
   {
        "id": "ProgramInterface/PluginsTab.htm", 
        "title": "Onglet Modules complémentaires", 
        "body": "L'onglet Modules complémentaires dans l'Éditeur de Documents permet d'accéder à des fonctions d'édition avancées à l'aide de composants tiers disponibles. Ici vous pouvez également utiliser des macros pour simplifier les opérations de routine. Fenêtre principale de l'Éditeur de Documents en ligne : Fenêtre principale de l'Éditeur de Documents de bureau : Le bouton Paramètres permet d'ouvrir la fenêtre où vous pouvez visualiser et gérer tous les modules complémentaires installés et ajouter vos propres modules. Le bouton Macros permet d'ouvrir la fenêtre où vous pouvez créer vos propres macros et les exécuter. Pour en savoir plus sur les macros, veuillez vous référer à la documentation de notre API. Actuellement, les modules suivants sont disponibles : Send sert à envoyer les documents par courriel en utilisant un client de messagerie de bureau (disponible uniquement dans la version en ligne), Code en surbrillance sert à surligner la syntaxe du code en sélectionnant la langue, le style, la couleur de fond appropriés, OCR sert à extraire le texte incrusté dans des images et l'insérer dans votre document, Éditeur de photos sert à modifier les images : rogner, retourner, pivoter, dessiner les lignes et le formes, ajouter des icônes et du texte, charger l’image de masque et appliquer des filtres comme Niveaux de gris, Inverser, Sépia, Flou, Embosser, Affûter etc., Parole permet la lecture du texte sélectionné à voix haute (disponible uniquement dans la version en ligne), Thésaurus sert à trouver les synonymes et les antonymes et les utiliser à remplacer le mot sélectionné, Traducteur sert à traduire le texte dans des langues disponibles, Remarque : ce plugin ne fonctionne pas dans Internet Explorer. You Tube permet d’ajouter les videos YouTube dans votre document, Mendeley permet de gérer les mémoires de recherche et de générer une bibliographie pour les articles scientifiques (disponible uniquement dans la version en ligne), Zotero permet de gérer des références bibliographiques et des documents associés (disponible uniquement dans la version en ligne), EasyBib sert à trouver et ajouter les livres, les journaux, les articles et les sites Web (disponible uniquement dans la version en ligne). Les modulesWordpress et EasyBib peuvent être utilisés si vous connectez les services correspondants dans les paramètres de votre portail. Vous pouvez utiliser les instructions suivantes pour la version serveur ou pour la version SaaS. Les plug-ins Wordpress et EasyBib ne sont pas inclus dans la version gratuite des éditeurs. Pour en savoir plus sur les modules complémentaires, veuillez vous référer à la documentation de notre API. Tous les exemples de modules open source actuellement disponibles sont disponibles sur GitHub."
    },
   {
        "id": "ProgramInterface/ProgramInterface.htm", 
        "title": "Présentation de l'interface utilisateur de Document Editor", 
        "body": "Document Editor utilise une interface à onglets où les commandes d'édition sont regroupées en onglets par fonctionnalité. La fenêtre principale de l'éditeur en ligne Document Editor: La fenêtre principale de l'éditeur de bureau Document Editor: L'interface de l'éditeur est composée des éléments principaux suivants: L'en-tête de l'éditeur affiche le logo ONLYOFFICE, les onglets des documents ouverts, le nom du document et les onglets du menu. Dans la partie gauche de l'en-tête de l'éditeur se trouvent les boutons Enregistrer, Imprimer le fichier, Annuler et Rétablir. Dans la partie droite de l'en-tête de l'éditeur, le nom de l'utilisateur est affiché ainsi que les icônes suivantes: Ouvrir l'emplacement de fichier - dans la version de bureau, permet d'ouvrir le dossier où le fichier est stocké, dans la fenêtre Explorateur de fichiers. Dans la version en ligne, elle permet d'ouvrir le dossier du module Documents où le fichier est stocké dans un nouvel onglet du navigateur. Partager (disponible dans la version en ligne uniquement). permet de définir les droits d'accès aux documents stockés dans le cloud. Marquer en tant que favori - cliquez sur l'étoile pour ajouter le fichier aux favoris et pour le retrouver rapidement. Ce n'est qu'un fichier de raccourcis car le fichier lui-même est dans l'emplacement de stockage d'origine. Le fichier réel n'est pas supprimé quand vous le supprimez de Favoris. Recherche - permet de rechercher un certain mot ou symbole dans un document. La barre d'outils supérieure affiche un ensemble de commandes d'édition en fonction de l'onglet de menu sélectionné. Actuellement, les onglets suivants sont disponibles: Fichier, Accueil, Insertion, Mise en page, Références, Formulaires (disponible avec uniquement des fichiers au format DOCXF), Collaboration, Protection, Modules complémentaires. Les options Copier, Coller, Couper et Sélectionner tout sont toujours disponibles dans la partie gauche de la Barre d'outils supérieure quel que soit l'onglet sélectionné. La Barre d'état se trouve en bas de la fenêtre de l'éditeur et affiche l'indicateur du numéro de page et certaines notifications (telles que Toutes les modifications sauvegardées, La connexion est perdue quand il n'y a pas de connexion et l'éditeur tente de se connecter etc.). Cette barre permet de définir la langue du texte, enabling spell checking et d'activer la vérification orthographique, d'activer le mode suivi des modifications et de régler le zoom. La barre latérale gauche contient les icônes suivantes: - permet d'utiliser l'outil Rechercher et remplacer, - permet d'ouvrir le panneau Commentaires, - permet de passer au panneau En-têtes et gérer des en-têtes, - (disponible uniquement dans la version en ligne ) permet d'ouvrir le panneau Chat, - (disponible uniquement dans la version en ligne ) permet de contacter notre équipe d'assistance technique, - (disponible uniquement dans la version en ligne ) permet d'afficher les informations sur le programme. La barre latérale droite permet d'ajuster les paramètres supplémentaires de différents objets. Lorsque vous sélectionnez un objet particulier dans le texte, l'icône correspondante est activée dans la barre latérale droite. Cliquez sur cette icône pour développer la barre latérale droite. Les Règles horizontales et verticales permettent d'aligner le texte et d'autres éléments dans un document, définir des marges, des taquets et des retraits de paragraphe. La Zone de travail permet d'afficher le contenu du document, d'entrer et de modifier les données. La Barre de défilement sur la droite permet de faire défiler vers le haut et vers le bas des documents de plusieurs pages. Pour plus de commodité, vous pouvez masquer certains composants et les afficher à nouveau lorsque cela est nécessaire. Pour en savoir plus sur l'ajustement des paramètres d'affichage, reportez-vous à cette page."
    },
   {
        "id": "ProgramInterface/ProtectionTab.htm", 
        "title": "Onglet Protection", 
        "body": "L'onglet Protection dans Document Editor permet de protéger vos documents par un mot de passe et gérer des droits d'accès. La fenêtre de l'onglet dans Document Editor en ligne: La fenêtre de l'onglet dans Document Editor de bureau: En utilisant cet onglet, vous pouvez: définir un mot de passe passe pour ouvrir votre document, modifier et supprimer des mots de passe, définir les modes d'édition des documents protégés, supprimer la protection du document."
    },
   {
        "id": "ProgramInterface/ReferencesTab.htm", 
        "title": "Onglet Références", 
        "body": "L'onglet Références dans l'Éditeur de Documents permet de gérer différents types de références : ajouter et rafraîchir une table des matières, créer et éditer des notes de bas de page, insérer des hyperliens. Fenêtre de l'Éditeur de Documents en ligne : Fenêtre de l'Éditeur de Documents de bureau : En utilisant cet onglet, vous pouvez : créer et mettre à jour automatiquement une table des matières, insérer des notes de bas de page et des notes de fin insérer des liens hypertexte, ajouter des signets, ajouter des légendes, insérer des renvois. insérer un tableau des figures."
    },
   {
        "id": "ProgramInterface/ReviewTab.htm", 
        "title": "Onglet Collaboration", 
        "body": "L'onglet Collaboration dans l'Éditeur de Documents permet d'organiser le travail collaboratif sur le document. Dans la version en ligne, vous pouvez partager le fichier, sélectionner un mode de co-édition, gérer les commentaires, suivre les modifications apportées par un réviseur, visualiser toutes les versions et révisions. En mode Commentaires vous pouvez ajouter et supprimer des commentaires, naviguer entre les modifications suivies, utilisez le chat intégré et afficher l'historique des versions. Dans la versionde bureau, vous pouvez gérer les commentaires et utiliser la fonction Suivi des modifications . Fenêtre de l'onglet dans l'Éditeur de Documents en ligne : Fenêtre de l'onglet dans l'Éditeur de Documents de bureau : En utilisant cet onglet, vous pouvez : spécifier les paramètres de partage (disponible uniquement dans la version en ligne), basculer entre les modes d'édition collaborative Strict et Rapide (disponible uniquement dans la version en ligne), ajouter et supprimer des commentaires sur un document, activer le Suivi des modifications , choisir le mode d'affichage des modifications, gérer les changements suggérés, télécharger le document à comparer (disponible uniquement dans la version en ligne), ouvrir le panneau de Chat (disponible uniquement dans la version en ligne>), suivre l'historique des versions (disponible dans la version en ligne< uniquement),"
    },
   {
        "id": "ProgramInterface/ViewTab.htm", 
        "title": "Onglet Affichage", 
        "body": "L'onglet Affichage de Document Editor permet de gérer l'apparence du document pendant que vous travaillez sur celui-ci. L'onglet dans l'éditeur en ligne Document Editor: L'onglet dans l'éditeur de bureau Document Editor: Les options d'affichage disponibles sous cet onglet: Titres permet d'afficher et de parcourir des titres dans votre document,. Zoom permet de zoomer et dézoomer sur une page du document. Ajuster à la page permet de redimensionner la page pour afficher une page entière sur l'écran. Ajuster à la largeur permet de redimensionner la page pour l'adapter à la largeur de l'écran.. Thème d'interface permet de modifier le thème d'interface en choisissant le thème Identique à système, Clair, Classique clair, Sombre ou Contraste élevé sombre. L'option Document sombre devient active lorsque vous activez le thème Sombre ou Contraste élevé sombre. Cliquez sur cette option pour rendre sombre encore l'espace de travail. Les options suivantes permettent de choisir les éléments à afficher ou à cacher. Cachez les cases appropriées aux éléments que vous souhaitez rendre visible: Toujours afficher la barre d'outils permet de rendre visible la barre d'outils supérieure. Barre d'état permet de rendre visible la barre d'état. Panneau gauche pour rendre visible le panneau gauche. Panneau droit permet de rendre visible le panneau droit. Règles permet de rendre visible les règles."
    },
   {
        "id": "UsageInstructions/AddBorders.htm", 
        "title": "Ajouter des bordures", 
        "body": "Pour ajouter des bordures à un paragraphe, à une page ou à tout le document dans l'Éditeur de Documents, placez le curseur dans le paragraphe voulu, ou sélectionnez plusieurs paragraphes avec la souris ou tout le texte en utilisant la combinaison de touches Ctrl+A, cliquez sur le bouton droit de la souris et sélectionnez l'option Paramètres avancés du paragraphe du menu contextuel ou utilisez le lien Afficher les paramètres avancés sur la barre latérale droite, passez à l'onglet Bordures et remplissage dans la fenêtre Paragraphe - Paramètres avancés ouverte, définissez la valeur nécessaire pour la Taille de bordure et sélectionnez une Couleur de la bordure, cliquez sur le diagramme disponible ou utilisez les boutons pour sélectionner les bordures et appliquer le style choisi, cliquez sur le bouton OK. Après avoir ajouté des bordures, vous pouvez également définir les Marges intérieures c'est-à-dire la distance entre les bordures à droite, à gauche, en haut et en bas et le texte du paragraphe à l'intérieur. Pour définir les valeurs nécessaires, passez à l'onglet Marges intérieures de la fenêtre Paragraphe - Paramètres avancés :"
    },
   {
        "id": "UsageInstructions/AddCaption.htm", 
        "title": "Ajouter une légende", 
        "body": "La légende est une étiquette agrafée que vous pouvez appliquer à un objet, comme des tableaux d'équations, des figures et des images dans vos documents. Cela facilite la référence dans votre texte car il y a une étiquette facilement reconnaissable sur votre objet. Vous pouvez aussi utiliser les légendes pour créer des tableaux des figures. Pour ajouter une légende à un objet dans l'Éditeur de Documents : Sélectionnez l'objet à appliquer une légende ; Passez à l'onglet Références de la barre d'outils supérieure ; Cliquez sur l'icône Légende sur la barre d'outils supérieure ou cliquez avec le bouton droit sur l'objet et sélectionnez l'option Insérer une légende pour ouvrir la fenêtre de dialogue Insérer une légende Choisissez l'étiquette à utiliser pour votre légende en choisissant l'objet de la liste déroulante d'étiquette, ou Créez une nouvelle étiquette en cliquant sur le bouton Ajouter. Saisissez un nom pour l'étiquette dans le champs. Cliquez ensuite sur le bouton OK pour ajouter une nouvelle ; Cochez la case Inclure le numéro de chapitre pour modifier la numérotation de votre légende ; Du menu déroulant Insérer, choisissez Avant pour placer l'étiquette au-dessus de l'objet ou Après pour placer sous l'objet ; Cochez la case Exclure le de la légende pour n'avoir qu'un numéro pour cette légende particulière conformément à un numéro de séquence. Vous pouvez ensuite choisir le numéro de votre légende en attribuant un style spécifique à la légende et en ajoutant un séparateur ; Pour appliquer la légende, cliquez sur le bouton OK. Supprimer une étiquette Pour supprimer une étiquette que vous avez créée, choisissez l'étiquette dans la liste des étiquettes dans la fenêtre de légende, puis cliquez sur le bouton Supprimer. L'étiquette que vous avez créée sera immédiatement supprimée. Remarque:vous pouvez supprimer les étiquettes que vous avez créées mais vous ne pouvez pas supprimer les étiquettes par défaut. Mettre en forme des légendes Dès que vous ajoutez une légende, un nouveau style de légende est automatiquement ajouté à la section. Afin de changer le style de toutes les légendes dans le document, suivez ces étapes : Sélectionnez le texte à partir duquel le nouveau style de Légende sera copié ; Recherchez le style de Légende (surligné en bleu par défaut) dans la galerie de styles que vous pouvez trouver sur l'onglet Accueil de la barre d'outils supérieure ; Cliquez droit et choisissez l'option Mettre à jour à partir de la sélection. Regrouper des sous-titres Si vous souhaitez pouvoir déplacer l'objet et la légende ensemble, il est nécessaire de regrouper l'objet et la légende. Sélectionnez l'objet ; Sélectionnez l'un des styles d'habillage à l'aide de la barre latérale droite ; Ajoutez la légende comme mentionnée ci-dessus ; Maintenez la touche Maj enfoncée et sélectionnez les éléments que vous souhaitez regrouper ; Faites un clic droit sur l'un des éléments, choisissez Réorganiser > Grouper. Maintenant, les deux éléments se déplaceront simultanément si vous les faites glisser quelque part ailleurs dans le texte. Pour dissocier l'objet, cliquez respectivement sur Réorganiser > Dissocier."
    },
   {
        "id": "UsageInstructions/AddFormulasInTables.htm", 
        "title": "Utiliser des formules dans les tableaux", 
        "body": "Insérer une formule Dans l'Éditeur de Documents, vous pouvez effectuer des calculs simples sur les données des cellules de table en ajoutant des formules. Pour insérer une formule dans une cellule du tableau, placez le curseur dans la cellule où vous voulez afficher le résultat, cliquez sur le bouton Ajouter une formule dans la barre latérale de droite, dans la fenêtre Paramètres de formule qui s'ouvre, saisissez la formule nécessaire dans la zone Formule. Vous pouvez saisir une formule manuellement à l'aide des opérateurs mathématiques courants (+, -, *, /), par exemple =A1*B2 ou utiliser la liste déroulante Coller une fonction pour sélectionner une des fonctions intégrées, par exemple =PRODUIT(A1,B2). spécifier manuellement les arguments nécessaires entre parenthèses dans le champ Formule. Si la fonction nécessite plusieurs arguments, ils doivent être séparés par des virgules. utilisez la liste déroulante Format de nombre si vous voulez afficher le résultat dans un certain format numérique, cliquez sur OK. Le résultat sera affiché dans la cellule sélectionnée. Pour modifier la formule ajoutée, sélectionnez le résultat dans la cellule et cliquez sur le bouton Ajouter une formule dans la barre latérale droite, effectuez les modifications nécessaires dans la fenêtre Paramètres de formule et cliquez sur OK. Ajouter des références aux cellules Vous pouvez utiliser les arguments suivants pour ajouter rapidement des références à des plages de cellules : HAUT - une référence à toutes les cellules de la colonne au-dessus de la cellule sélectionnée GAUCHE - une référence à toutes les cellules de la ligne située à gauche de la cellule sélectionnée BAS - une référence à toutes les cellules de la colonne sous la cellule sélectionnée DROITE - une référence à toutes les cellules de la ligne à droite de la cellule sélectionnée Ces arguments peuvent être utilisés avec les fonctions MOYENNE, NB, MAX, MIN, PRODUIT, SOMME. Vous pouvez également saisir manuellement des références à une cellule donnée (par exemple, A1) ou à une plage de cellules (par exemple, A1:B3). Utiliser des signets Si vous avez ajouté des signets à certaines cellules de votre tableau, vous pouvez utiliser ces signets comme arguments lorsque vous saisissez des formules. Dans la fenêtre Paramètres de formule, placez le curseur entre parenthèses dans la zone de saisie du champ Formule où vous voulez que l'argument soit ajouté et utilisez la liste déroulante Insérer le signets pour sélectionner un des signets préalablement ajoutés. Mise à jour des résultats de la formule Si vous modifiez certaines valeurs dans les cellules du tableau, vous devez mettre à jour manuellement les résultats de formule : Pour mettre à jour un résultat de formule unique, sélectionnez le résultat nécessaire et appuyez sur F9 ou cliquez avec le bouton droit de la souris sur le résultat et utilisez l'option Mettre à jour un champ dans le menu. Pour mettre à jour plusieurs résultats de formule, sélectionnez les cellules nécessaires ou la table entière et appuyez sur F9. Fonctions intégrées Vous pouvez utiliser les fonctions mathématiques, statistiques et logiques standard suivantes : Catégorie Fonctions Description Exemple Mathématique ABS(nombre) La fonction est utilisée pour afficher la valeur absolue d'un nombre. =ABS(-10) Renvoie 10 Logique ET(logical1, logical2, ...) La fonction sert à vérifier si la valeur logique saisie est VRAI ou FAUX. La fonction affiche 1 (VRAI) si tous les arguments sont VRAI =ET(1&gt;0,1&gt;3) Renvoie 0 Statistique MOYENNE(argument-list) La fonction est utilisée pour analyser la plage de données et trouver la valeur moyenne.. =MOYENNE(4,10) Renvoie 7 Statistique NB(plage) La fonction est utilisée pour compter le nombre de cellules sélectionnées qui contiennent des nombres en ignorant les cellules vides ou avec du texte. =NB(A1:B3) Renvoie 6 Logique DEFINED() La fonction évalue si une valeur est définie dans la cellule. La fonction renvoie 1 si la valeur est définie et calculée sans erreur et renvoie 0 si la valeur n'est pas définie ou calculée avec erreur. =DEFINED(A1) Logique FAUX() La fonction renvoie 0 (FAUX) et ne nécessite pas d’argument. =FAUX Renvoie 0 Logique SI(logical_test, value_if_true, value_if_false) La fonction est utilisée pour vérifier l'expression logique et retourne une valeur si l'argument est VRAI ou une autre si l'argument est FAUX. =SI(3&gt;1,1,0) Renvoie 1 Mathématique ENT(nombre) La fonction est utilisée pour analyser et retourner la partie entière du nombre spécifié. =ENT(2,5) Renvoie 2 Statistique MAX(number1, number2, ...) La fonction est utilisée pour analyser la plage de données et trouver le plus grand nombre. =MAX(15,18,6) Renvoie 18 Statistique MIN(number1, number2, ...) La fonction est utilisée pour analyser la plage de données et trouver le plus petit nombre. =MIN(15,18,3) Renvoie 6 Mathématique MOD(x, y) La fonction est utilisée pour retourner le reste après la division d'un nombre par le diviseur spécifié. =MOD(6,3) Renvoie 0 Logique PAS(logical) La fonction sert à vérifier si la valeur logique saisie est VRAI ou FAUX. La fonction retourne 1 (VRAI) si l'argument est FAUX et 0 (FAUX) si l'argument est VRAI. =PAS(2&lt;5) Renvoie 0 Logique OU(logical1, logical2, ...) La fonction sert à vérifier si la valeur logique saisie est VRAI ou FAUX. La fonction renvoie faux 0 (FAUX) si tous les arguments sont FAUX. =OU(1&gt;0,1&gt;3) Renvoie 1 Mathématique PRODUIT(number1, number2, ...) La fonction est utilisée pour multiplier tous les nombres dans la plage de cellules sélectionnée et renvoyer le produit. =PRODUIT(2,5) Renvoie 10 Mathématique ARRONDI(number, num_digits) La fonction est utilisée pour arrondir un nombre à un nombre de décimales spécifié. =ARRONDI(2,25,1) Renvoie 2,3 Mathématique SIGNE(number) La fonction est utilisée pour renvoyer le signe d'un nombre. Si le nombre est positif, la fonction renvoie 1. Si le nombre est négatif, la fonction renvoie -1. Si le nombre est 0, la fonction renvoie 0. =SIGNE(-12) Renvoie -1 Mathématique SOMME(number1, number2, ...) La fonction est utilisée pour additionner tous les nombres contenus dans une plage de cellules et renvoyer le résultat. =SOMME(5,3,2) Renvoie 10 Logique VRAI() La fonction renvoie 1 (VRAI) et n'exige aucun argument. =VRAI Renvoie 1"
    },
   {
        "id": "UsageInstructions/AddHyperlinks.htm", 
        "title": "Ajouter des liens hypertextes", 
        "body": "Pour ajouter un lien hypertexte dans l'Éditeur de Documents, placez le curseur là où vous voulez insérer un lien hypertexte, passez à l'onglet Insérer ou Références de la barre d'outils supérieure, cliquez sur l'icône Ajouter un lien hypertexte de la barre d'outils supérieure, dans la fenêtre ouverte précisez les Paramètres du lien hypertexte : Sélectionnez le type de lien que vous voulez insérer : Utilisez l'option Lien externe et entrez une URL au format http://www.example.com dans le champ Lien vers, si vous avez besoin d'ajouter un lien hypertexte menant vers un site externe. Si vous avez besoin d'ajouter un lien hypertexte vers un fichier local, entrez une URL au format file://path/Document.docx (pour Windows) ou file:///path/Document.docx (pour MacOS et Linux) dans le champ Lien vers. Le lien hypertexte file://path/Document.docx ou file:///path/Document.docx ne peut être ouvert que dans la version de bureau de l'éditeur. Dans l'éditeur web il est seulement possible d'ajouter un lien hypertexte sans pouvoir l'ouvrir. Utilisez l'option Emplacement dans le document et sélectionnez l'un des titres existants dans le texte du document ou l'un des marque-pages précédemment ajoutés si vous devez ajouter un lien hypertexte menant à un certain emplacement dans le même document. Afficher - entrez un texte qui sera cliquable et amènera à l'adresse Web indiquée dans le champ supérieur. Texte de l'infobulle - entrez un texte qui sera visible dans une petite fenêtre contextuelle offrant une courte note ou étiquette lorsque vous placez le curseur sur un lien hypertexte. Cliquez sur le bouton OK. Pour ajouter un lien hypertexte, vous pouvez également utiliser la combinaison des touches Ctrl+K ou cliquez avec le bouton droit sur l'emplacement choisi et sélectionnez l'option Lien hypertexte du menu contextuel. Note : il est également possible de sélectionner un caractère, un mot, une combinaison de mots, un passage de texte avec la souris ou en utilisant le clavier, puis d'ouvrir la fenêtre Paramètres de lien hypertexte comme décrit ci-dessus. Dans ce cas, le champ Afficher sera rempli avec le fragment de texte que vous avez sélectionné. Si vous placez le curseur sur le lien hypertexte ajouté, vous verrez l'info-bulle contenant le texte que vous avez spécifié. Pour suivre le lien appuyez sur la touche CTRL et cliquez sur le lien dans votre document. Pour modifier ou supprimer le lien hypertexte ajouté, cliquez-le droit, sélectionnez l'option Lien hypertexte et l'opération à effectuer - Modifier le lien hypertexte ou Supprimer le lien hypertexte."
    },
   {
        "id": "UsageInstructions/AddTableofFigures.htm", 
        "title": "Insérer et mettre en forme un tableau des figures", 
        "body": "Un tableau des figures permet de répertorier toutes les équations, les graphiques et les tableaux accompagnant votre document. Un tableau des figures, au même titre qu'une table des matières, permet d'obtenir la liste des illustrations et d'organiser des objets légendés ou des en-têtes personnalisés avec un certain style de mise en forme. Les illustrations organisées de telle façon sont facile à citer et retrouver. Cliquez sur le lien dans le Tableau des figures mise sous forme de liens pour accéder à l'élément référencé. Tous les tableaux, les équations, les diagrammes, les images, les graphiques, les cartes, les photos et d'autres illustrations sont présentées en tant qu'une figure. Pour insérer un Tableau des figures dans l'Éditeur de Documents passez à l'onglet Références et utilisez le bouton Tableau des figures dans la barre d'outils supérieure pour créer et mettre en forme le tableau des figures. Utiliser le bouton Actualiser pour mettre à jour le tableau des figures à chaque fois que vous ajoutez une nouvelle illustration à votre document. Créer un tableau des figures Remarque : Pour créer un tableau des figures vous devez légender chaque figure ou la personnaliser avec un style de mise en forme. Avant de créer un tableau des figures, vous devez ajouter une légende à chaque équation, tableau ou illustration de votre document ou appliquer un style de texte afin que l'éditeur puisse créer automatiquement le tableau des figures. Une fois toutes les légendes et les styles ajoutés, placez le curseur à l'endroit où le tableau des figures doit être inséré et passez à l'onglet Références, ensuite cliquez sur le bouton Tableau des figure pour ouvrir la boîte de dialogue et créer une liste des figures. Sélectionnez l'option appropriée pour construire un tableau des figures à partir des illustrations légendées ou mises en forme avec un style. Pour créer un tableau des figures à partir des illustrations légendées : Activez la case Légende et sélectionnez l'illustration légendée dans la liste déroulante : Aucune ; Équation ; Figure ; Tableau. Pour créer un tableau des figures à partir des illustrations avec un style de mise en forme : Activez la case Style et sélectionnez le style d'illustration dans la liste déroulante. Le menu des options varie en fonction du style choisi : Titre 1 ; Titre 2 ; Légende ; Tableau des figures ; Normal. Mettre en forme un tableau des figures Les cases d'options permettent de mettre en forme un tableau des figures. Par défaut, toutes les cases d'options sont actives, car, dans la plupart des cas, l'activation de celles-ci semble raisonnable. Désactivez toutes les options dont vous n'avez pas besoin. Afficher les numéros de pages permet d'afficher le numéro de page correspondant ; Aligner les numéros de page à droite permet d'afficher les numéros de page à droite dès lors que Afficher les numéros de pages est active, décochez-la pour afficher le numéro de page juste après le titre. Mettre le tableau des figures sous forme de liens permet de créer des liens hypertexte dans la table des figures ; Inclure l'étiquette et le numéro permet d'ajouter des étiquettes et de la numérotation au tableau des figures. Sélectionnez les points de suite dans la liste déroulante à remplir l'espace entre un titre et le numéro de page correspondant pour améliorer alors la lisibilité et la lecture des tableaux. Personnalisez le style du texte dans la table des figures en choisissant un style dans la liste déroulante. Actuel pour afficher le style plus récent. Simple pour mettre le texte en gras. En ligne pour afficher le texte sous forme de lien hypertexte. Classique pour mettre du texte en majuscule. Distinctif pour mettre du texte en italique. Centré pour aligner le texte au centre et n'afficher pas des points de suite. Formel pour mettre en forme du texte en 11 pt Arial et donner un aspect formel. La fenêtre Aperçu affiche la sortie finale du tableau des figures Mettre à jour un tableau des figures On doit actualiser un Tableau des figures à chaque fois qu'une nouvelle équation, illustration ou tableau est inséré dans le document. Le bouton Actualiser ne devient actif que lorsque vous sélectionnez ou cliquez sur le Tableau des figures. Cliquez sur le bouton Actualiser sous l'onglet Références de la barre d'outils supérieure et sélectionnez l'option appropriée dans le menu. Actualiser les numéros de page uniquement permet d'actualiser les numéros de page sans modifier les changements de titres. Actualiser le tableau entier permet de mettre en place toutes les modifications des titres et des numéros de pages. Cliquez sur OK pour confirmer votre choix ou Ouvrez le menu contextuel en cliquant avec le bouton droit de la souris sur le Tableaux des figures dans votre document, ensuite cliquez sur Actualiser dans le menu pour le mettre à jour."
    },
   {
        "id": "UsageInstructions/AddWatermark.htm", 
        "title": "Ajouter un filigrane", 
        "body": "Le filigrane est un texte ou une image placé sous le calque de texte principal. Les filigranes de texte permettent d'indiquer l'état de votre document (par exemple, confidentiel, brouillon, etc.), les filigranes d'image permettent d'ajouter une image par exemple de logo de votre entreprise. Pour ajouter un filigrane dans l'Éditeur de Documents : Basculez vers l'onglet Mise en page de la barre d'outils supérieure. Cliquez sur l'icône Filigrane sur la barre d'outils supérieure et choisissez l'option Filigrane personnalisé du menu. Après cela, la fenêtre Paramètres du filigrane apparaîtra. Sélectionnez le type de filigrane que vous souhaitez insérer : Utilisez l'option de Filigrane de texte et ajustez les paramètres disponibles : Langue - sélectionnez la langue de votre filigrane. L'Éditeur de Documents prend en charge des langues suivantes de filigrane : Anglais, Français, Allemand, Italien, Japonais, Mandarin, Russe, Espagnol. Texte - Sélectionnez l'un des exemples de texte disponibles de la langue sélectionnée. Pour le français les textes de filigrane suivants sont disponibles : BROUILLON, CONFID, COPIE, DOCUMENT INTERNE, EXEMPLE, HAUTEMENT CONFIDENTIEL, IMPORTANT, NE PAS DIFFUSER, Police - Sélectionnez le nom et la taille de la police des listes déroulantes correspondantes. Utilisez les icônes à droite pour définir la couleur de la police ou appliquez l'un des styles de décoration de police: Gras, Italique, Souligné, Barré, Semi-transparent - Cochez cette case si vous souhaitez appliquer la transparence, Disposition - Sélectionnez l'option Diagonale ou Horizontale. Utilisez l'option Image en filigrane et ajustez les paramètres disponibles : Choisissez la source du fichier image en utilisant l'un des boutons : Depuis un fichier ou D'une URL. L'image sera affichée dans la fenêtre à droite, Échelle - sélectionnez la valeur d'échelle nécessaire parmi celles disponibles : Auto, 200%, 150%, 100%, 50%. Cliquez sur le bouton OK. Pour modifier le filigrane ajouté, ouvrez la fenêtre Paramètres du filigrane comme décrit ci-dessus, modifiez les paramètres nécessaires et cliquez sur OK. Pour supprimer le filigrane ajouté, cliquez sur l'icône de Filigrane dans l'onglet de Disposition de la barre d'outils supérieure et choisissez l'option Supprimer le filigrane du menu. Il est également possible d'utiliser l'option Aucun dans la fenêtre des Paramètres du filigrane."
    },
   {
        "id": "UsageInstructions/AlignArrangeObjects.htm", 
        "title": "Aligner et organiser des objets sur une page", 
        "body": "Dans l'Éditeur de Documents, les formes automatiques, les images, les graphiques ou les zones de texte ajoutés peuvent être alignés, regroupés et ordonnésr sur une page. Pour effectuer une de ces actions, sélectionnez d'abord un ou plusieurs objets sur la page. Pour sélectionner plusieurs objets, maintenez la touche Ctrl enfoncée et cliquez avec le bouton gauche sur les objets nécessaires. Pour sélectionner une zone de texte, cliquez sur son bord, pas sur le texte à l'intérieur. Après quoi vous pouvez utiliser soit les icônes de l'onglet Mise en page de la barre d'outils supérieure décrites ci-après soit les options similaires du menu contextuel. Aligner des objets Pour aligner deux ou plusieurs objets sélectionnés, cliquez sur l'icône Aligner située dans l'onglet Mise en page de la barre d'outils supérieure et sélectionnez une des options disponibles : Aligner sur la page pour aligner les objets par rapport aux bords de la page, Aligner sur la marge pour aligner les objets par rapport aux bords de la marge, Aligner les objets sélectionnés (cette option est sélectionnée par défaut) pour aligner les objets les uns par rapport aux autres, Cliquez à nouveau sur l'icône Aligner et sélectionnez le type d'alignement nécessaire dans la liste : Aligner à gauche - pour aligner les objets horizontalement par le bord gauche de l'objet le plus à gauche / bord gauche de la page / marge gauche de la page, Aligner au centre - pour aligner les objets horizontalement par leur centre/centre de la page/centre de l'espace entre les marges gauche et droite de la page, Aligner à droite - pour aligner les objets horizontalement par le bord droit de l'objet le plus à droite / bord droit de la page / marge droite de la page, Aligner en haut - pour aligner les objets verticalement par le bord supérieur de l'objet le plus haut/bord supérieur de la page / marge supérieure de la page, Aligner au milieu - pour aligner les objets verticalement par leur milieu/milieu de la page/milieu de l'espace entre les marges de la page supérieure et de la page inférieure, Aligner en bas - pour aligner les objets verticalement par le bord inférieur de l'objet le plus bas/bord inférieur de la page/bord inférieur de la page. Vous pouvez également cliquer avec le bouton droit sur les objets sélectionnés, choisir l'option Aligner dans le menu contextuel, puis utiliser une des options d'alignement disponibles. Si vous voulez aligner un objet unique, il peut être aligné par rapport aux bords de la page ou aux marges de la page. L'option Aligner sur la marge est sélectionnée par défaut dans ce cas. Distribuer des objets Pour distribuer horizontalement ou verticalement trois objets sélectionnés ou plus de façon à ce que la même distance apparaisse entre eux, Cliquez sur l'icône Aligner située dans l'onglet Mise en page de la barre d'outils supérieure et sélectionnez une des options disponibles : Aligner sur la page pour répartir les objets entre les bords de la page, Aligner sur la marge pour répartir les objets entre les marges de la page, Aligner les objets sélectionnés (cette option est sélectionnée par défaut) pour distribuer les objets entre les deux objets les plus externes sélectionnés, Cliquez à nouveau sur l'icône Aligner et sélectionnez le type de distribution nécessaire dans la liste : Distribuer horizontalement - pour répartir uniformément les objets entre les bords gauche et droit des objets sélectionnés/bords gauche et droit de la page/marges gauche et droite de la page. Distribuer verticalement - pour répartir uniformément les objets entre les objets les plus en haut et les objets les plus bas sélectionnés/bords supérieur et inférieur de la page/marges de la page/bord supérieur et inférieur de la page. Vous pouvez également cliquer avec le bouton droit sur les objets sélectionnés, choisir l'option Aligner dans le menu contextuel, puis utiliser une des options de distribution disponibles. Remarque  : les options de distribution sont désactivées si vous sélectionnez moins de trois objets. Grouper plusieurs objets Pour grouper deux ou plusieurs objets sélectionnés ou les dissocier, cliquez sur la flèche à côté de l'icône Grouper dans l'onglet Mise en page de la barre d'outils supérieure et sélectionnez l'option nécessaire depuis la liste : Grouper - pour combiner plusieurs objets de sorte que vous puissiez les faire pivoter, déplacer, redimensionner, aligner, organiser, copier, coller, mettre en forme simultanément comme un objet unique. Dissocier - pour dissocier le groupe sélectionné d'objets préalablement combinés. Vous pouvez également cliquer avec le bouton droit sur les objets sélectionnés, choisir l'option Ordonner dans le menu contextuel, puis utiliser l'option Grouper ou Dissocier. Remarque : l'option Grouper est désactivée si vous sélectionnez moins de deux objets. L'option Dissocier n'est disponible que lorsqu'un groupe des objets précédemment joints est sélectionné. Organiser plusieurs objets Pour organiser les objets sélectionnés (c'est à dire pour modifier leur ordre lorsque plusieurs objets se chevauchent), vous pouvez utiliser les icônes Avancer et Reculer dans l'onglet Mise en page de la barre d'outils supérieure et sélectionner le type d'arrangement nécessaire dans la liste. Pour déplacer un ou plusieurs objets sélectionnés vers l'avant, cliquez sur la flèche en regard de l'icône Avancer dans l'onglet Mise en page de la barre d'outils supérieure et sélectionnez le type d'arrangement nécessaire dans la liste : Mettre au premier plan - pour déplacer les objets devant tous les autres objets, Avancer - pour déplacer les objets d'un niveau en avant par rapport à d'autres objets. Pour déplacer un ou plusieurs objets sélectionnés vers l'arrière, cliquez sur la flèche en regard de l'icône Reculer dans l'onglet Mise en page de la barre d'outils supérieure et sélectionnez le type d'arrangement nécessaire dans la liste : Mettre en arrière-plan - pour déplacer les objets derrière tous les autres objets, Reculer - pour déplacer les objets d'un niveau en arrière par rapport à d'autres objets. Vous pouvez également cliquer avec le bouton droit sur les objets sélectionnés, choisir l'option Organiser dans le menu contextuel, puis utiliser une des options de rangement disponibles."
    },
   {
        "id": "UsageInstructions/AlignText.htm", 
        "title": "Alignement du texte d'un paragraphe", 
        "body": "Dans l'Éditeur de Documents, le texte peut être aligné de quatre façons : aligné à gauche, au centre, aligné à droite et justifié. Pour le faire, placez le curseur à la position où vous voulez appliquer l'alignement (une nouvelle ligne ou le texte déjà saisi ), passez à l'onglet Accueil de la barre d'outils supérieure, sélectionnez le type d'alignement que vous allez appliquer : Gauche - pour aligner du texte à gauche de la page (le côté droit reste non aligné), cliquez sur l'icône Aligner à gauche située sur la barre d'outils supérieure. Centre - pour aligner du texte au centre de la page (le côté droit et le côté gauche restent non alignés), cliquez sur l'icône Aligner au centre située sur la barre d'outils supérieure. Droit - pour aligner du texte à droite de la page (le côté gauche reste non aligné), cliquez sur l'icône Aligner à droite située sur la barre d'outils supérieure. Justifier - pour aligner du texte à gauche et à droite à la fois (l'espacement supplémentaire est ajouté si nécessaire pour garder l'alignement), cliquez sur l'icône Justifier située sur la barre d'outils supérieure. Configuration des paramètres d'alignement est aussi disponible dans la fenêtre Paragraphe - Paramètres avancés: faites un clic droit sur le texte et sélectionnez Paragraphe - Paramètres avancés du menu contextuel ou utilisez l'option Afficher le paramètres avancée sur la barre d'outils à droite, ouvrez la fenêtre Paragraphe - Paramètres avancés, passez à l'onglet Retraits et espacement, sélectionnez le type d'alignement approprié de la Liste d'Alignement : À gauche, Au centre, À droite, Justifié, cliquez sur OK pour appliquer les modifications apportées."
    },
   {
        "id": "UsageInstructions/BackgroundColor.htm", 
        "title": "Sélectionner la couleur d'arrière-plan pour un paragraphe", 
        "body": "Dans Éditeur de Documents, la couleur d'arrière-plan est appliquée au paragraphe entier et remplit complètement l'espace du paragraphe de la marge de page gauche à la marge de page droite. Pour appliquer la couleur d'arrière-plan au paragraphe particulier ou changer la couleur actuelle, sélectionnez un jeu de couleurs pour votre document à partir des modèles disponibles en cliquant sur l'icône Modifier le jeu de couleurs dans l'onglet Accueil de la barre d'outils supérieure placez le curseur dans le paragraphe choisi, ou sélectionnez plusieurs paragraphes avec la souris ou le texte entier en utilisant la combinaison de touches Ctrl+A ouvrez la fenêtre des palettes de couleurs. Vous pouvez l'accéder par une des façons suivantes : cliquez sur la flèche vers le bas à côté de l'icône dans l'onglet Accueil de la barre d'outils supérieure, ou utilisez le champ de couleurs à côté de la légende Сouleur d'arrière plan sur la barre latérale droite, cliquez sur le lien 'Afficher les paramètres avancés' sur la barre latérale droite ou sélectionnez l'option 'Paramètres avancés du paragraphe' dans le menu contextuel, puis passez à l'onglet 'Bordures et remplissage' dans la fenêtre 'Paragraphe - Paramètres avancés' et cliquez sur le champ de couleurs à coté de la légende Couleur d'arrière-plan. choisissez une couleur dans les palettes disponibles Après avoir sélectionné la couleur voulue à l'aide de l'icône , vous pourrez appliquer cette couleur à n’importe quel paragraphe sélectionné. Pour le faire, cliquez sur l'icône (elle affiche la couleur sélectionnée), sans avoir à choisir cette couleur dans la palette encore une fois. Si vous utilisez l'option Couleur d'arrière plan sur la barre de droite ou dans la fenêtre 'Paragraphe - Paramètres avancés', n'oubliez pas que la couleur sélectionnée n'est pas conservée pour l'accéder rapidement. (Ces options peuvent être utiles si vous souhaitez sélectionner une couleur d’arrière-plan différente pour un paragraphe spécifique, lors de l'utilisation de la couleur de base sélectionnée à l'aide l'icône ). Pour effacer la couleur d'arrière d'un paragraphe particulier, placez le curseur dans le paragraphe choisi, ou sélectionnez plusieurs paragraphes avec la souris ou le texte entier en utilisant la combinaison de touches Ctrl+A en cliquant sur le champ de couleur à côté de la légende Сouleur d'arrière plan sur la barre latérale droite, ouvrez la fenêtre des palettes de couleurs sélectionnez l'icône ."
    },
   {
        "id": "UsageInstructions/ChangeColorScheme.htm", 
        "title": "Modifier le jeu de couleurs", 
        "body": "Dans l'Éditeur de Documents, les jeux de couleurs s'appliquent au document entier. Utilisés pour le changement rapide de l'apparence de votre document, les jeux de couleurs définissent la palette Couleurs de thème pour les éléments du document (police, arrière-plan, tableaux, formes automatiques, graphiques). Si vous appliquez des Couleurs de thèmes aux éléments du document et sélectionnez un nouveau Jeu de couleurs, les couleurs appliquées aux éléments de votre document, par conséquent, seront modifiées. Pour modifier le jeu de couleurs, cliquez sur la flèche vers le bas située à côté de l'icône Modifier le jeu de couleurs dans l'onglet Accueil de la barre d'outils supérieure et sélectionnez le jeu de couleurs voulu parmi les variantes disponibles : New Office, Office, Niveaux de gris, Apex, Aspect, Civil, Rotonde, Capitaux, Flux, Fonderie, Médian, Métro, Module, Opulent, Oriel, Origine, Papier, Solstice, Technique, Promenade, Urban, Verve. Lorsque le jeu de couleurs est sélectionné, vous pouvez choisir des couleurs dans une fenêtre de palettes de couleurs qui vont correspondre à l'élément auquel vous souhaitez appliquer la couleur. Pour la plupart des éléments du document, la fenêtre des palettes de couleurs est accessible en cliquant sur la case colorée dans la barre latérale droite lorsque l'élément est sélectionné. Pour la police, cette fenêtre peut être ouverte à l'aide de la flèche vers le bas à côté de l'icône Couleur de police de l'onglet Accueil de la barre d'outils supérieure. Les palettes suivantes sont disponibles : Couleurs de thème - les couleurs qui correspondent à la palette de couleurs sélectionnée du document. Couleurs standard - le jeu de couleurs par défaut. Le jeu de couleurs sélectionné ne les affecte pas. Couleur personnalisée - choisissez cette option si la couleur voulue ne se trouve pas dans les palettes disponibles. Sélectionnez la gamme de couleurs nécessaire en déplaçant le curseur vertical et définissez la couleur spécifique en faisant glisser le sélecteur de couleur dans le grand champ de couleur carré. Une fois que vous sélectionnez une couleur avec le sélecteur de couleur, les valeurs de couleur appropriées RGB et sRGB seront affichées dans les champs à droite. Vous pouvez également spécifier une couleur sur la base du modèle de couleur RGB (RVB) en entrant les valeurs numériques nécessaires dans les champs R, G, B (rouge, vert, bleu) ou saisir le code hexadécimal dans le champ RGB marqué par le signe #. La couleur sélectionnée apparaît dans la case de prévisualisation Nouveau. Si l'objet a déjà été rempli d'une couleur personnalisée, cette couleur sera affichée dans la case Actuel afin que vous puissiez comparer les couleurs originales et modifiées. Lorsque la couleur est spécifiée, cliquez sur le bouton Ajouter : La couleur personnalisée sera appliquée à l’élément sélectionné et ajoutée dans la palette Couleur personnalisée."
    },
   {
        "id": "UsageInstructions/ChangeWrappingStyle.htm", 
        "title": "Changer l'habillage du texte", 
        "body": "L'option Style d'habillage détermine la position de l'objet par rapport au texte. Dans l'Éditeur de Documents, vous pouvez modifier le style d'habillage de texte pour les objets insérés, tels que les formes, , les images, les graphiques,, les zones de texte ou les tableaux. Modifier l'habillage de texte pour les formes, les images, les graphiques, les zones de texte Pour changer le style d'habillage actuellement sélectionné : sélectionnez un objet séparé sur la page en cliquant dessus. Pour sélectionner un bloc de texte, cliquez sur son bord, pas sur le texte à l'intérieur. ouvrez les paramètres d'habillage du texte : Passez à l'onglet Mise en page de la barre d'outils supérieure et cliquez sur la flèche située en regard de l'icône Retour à la ligne, ou cliquez avec le bouton droit sur l'objet et sélectionnez l'option Style d'habillage dans le menu contextuel, ou cliquez avec le bouton droit sur l'objet, sélectionnez l'option Paramètres avancés et passez à l'onglet Habillage du texte de la fenêtre Paramètres avancés de l'objet. sélectionnez le style d'habillage voulu : En ligne sur le texte - l'image fait partie du texte, comme un caractère, ainsi si le texte est déplacé, l'image est déplacée elle aussi. Dans ce cas-là les options de position ne sont pas accessibles. Si vous sélectionnez un des styles suivants, vous pouvez déplacer l'image indépendamment du texte et définir sa position exacte : Carré - le texte est ajusté autour des bords de l'objet. Rapproché - le texte est ajusté sur le contour de l'objet. Au travers - le texte est ajusté autour des bords de l'image et occupe l'espace vide à l'intérieur de celle-ci. Pour créer l'effet, utilisez l'option Modifier les limites du renvoi à la ligne du menu contextuel. Haut et bas - le texte est ajusté en haut et en bas de l'image. Devant le texte - l'image est affichée sur le texte. Derrière le texte - le texte est affiché sur l'objet. Si vous avez choisi l'un des styles Carré, Rapproché, Au travers, Haut et bas, vous avez la possibilité de configurer des paramètres supplémentaires - Distance du texte de tous les côtés (haut, bas, droite, gauche). Pour accéder à ces paramètres, cliquez avec le bouton droit sur l'objet, sélectionnez l'option Paramètres avancés et passez à l'onglet Style d'habillage du texte de la fenêtre Paramètres avancés de l'objet. Définissez les valeurs voulues et cliquez sur OK. Si vous sélectionnez un style d'habillage autre que En ligne, l'onglet Position est également disponible dans la fenêtre Paramètres avancés de l'objet. Pour en savoir plus sur ces paramètres, reportez-vous aux pages correspondantes avec les instructions sur la façon de travailler avec des formes, des images ou des graphiques. Si vous sélectionnez un style d'habillage autre que En ligne, vous pouvez également modifier la limite d'habillage pour les images ou les formes. Cliquez avec le bouton droit sur l'objet, sélectionnez l'option Style d'habillage dans le menu contextuel et cliquez sur Modifier les limites du renvoi à la ligne. Il est aussi possible d'utiliser le menu Retour à la ligne -> Modifier les limites du renvoi à la ligne sous l'onglet Mise en page de la barre d'outils supérieure. Faites glisser les points d'habillage pour personnaliser les limites. Pour créer un nouveau point d'habillage, cliquez sur la ligne rouge et faites-la glisser vers la position désirée. Modifier l'habillage de texte pour les tableaux Pour les tableaux, les deux styles d'habillage suivants sont disponibles : Tableau aligné et Tableau flottant. Pour changer le style d'habillage actuellement sélectionné: cliquez avec le bouton droit sur le tableau et sélectionnez l'option Paramètres avancés du tableau, passez à l'onglet Habillage du texte dans la fenêtre Tableau - Paramètres avancés ouverte, sélectionnez l'une des options suivantes : Tableau aligné est utilisé pour sélectionner le style d'habillage où le texte est interrompu par le tableau ainsi que l'alignement : gauche, au centre, droit. Tableau flottant est utilisé pour sélectionner le style d'habillage où le texte est enroulé autour du tableau. À l'aide de l'onglet Habillage du texte de la fenêtre Tableau - Paramètres avancésvous pouvez également configurer les paramètres suivants : Pour les tableaux alignés, vous pouvez définir le type d'alignement du tableau (à gauche, centre ou à droite) et le Retrait à gauche. Pour les tableaux flottants, vous pouvez spécifier la Distance du texte et la position du tableau dans l'onglet Position du tableau ."
    },
   {
        "id": "UsageInstructions/CommunicationPlugins.htm", 
        "title": "Communiquer lors de l'édition", 
        "body": "Dans l'Éditeur de Documents ONLYOFFICE vous pouvez toujours rester en contact avec vos collègues et utiliser des messageries en ligne populaires, par exemple Telegram et Rainbow. Les plug-ins Telegram et Rainbow ne sont pas installés par défaut. Pour en savoir plus sur leur installation, veuillez consulter l'article approprié : Ajouter des modules complémentaires à ONLYOFFICE Desktop Editors Adding plugins to ONLYOFFICE Cloud, ou Ajouter de nouveaux modules complémentaires aux éditeurs de serveur . Telegram Pour commencer à chatter dans le plug-in Telegram, Passez à l'onglet Modules complémentaires et cliquez sur Telegram, saisissez votre numéro de téléphone dans le champ correspondant, cochez la case Rester connecté lorsque vous souhaitez enregistrer vos données pour la session en cours, ensuite cliquez sur le bouton Suivant, saisissez le code reçu dans votre application Telegram, ou connectez-vous en utilisant le Code QR, ouvrez l'application Telegram sur votre téléphone, passez à Paramètres > Appareils > Numériser QR, numérisez l'image pour vous connecter. Vous pouvez maintenant utiliser Telegram au sein de l'interface des éditeurs ONLYOFFICE. Rainbow Pour commencer à chatter dans le plug-in Rainbow, Passez à l'onglet Modules complémentaires et cliquez sur Rainbow, enregistrez un nouveau compte en cliquant sur le bouton Inscription ou connectez-vous à un compte déjà créé. Pour le faire, saisissez votre email dans le champ correspondant et cliquez sur Continuer, puis saisissez le mot de passe de votre compte, cochez la case Maintenir ma session lorsque vous souhaitez enregistrer vos données pour la session en cours, ensuite cliquez sur le bouton Connecter. Vous êtes maintenant prêt à chatter dans Rainbow et travailler au sein de l'interface des éditeurs ONLYOFFICE en même temps."
    },
   {
        "id": "UsageInstructions/ConvertFootnotesEndnotes.htm", 
        "title": "Conversion de notes de bas de page en notes de fin", 
        "body": "Éditeur de Documents ONLYOFFICE vous permet de convertir les notes de bas de page en notes de fin et vice versa, par exemple, quand vous voyez que les notes de bas de page dans le document résultant doivent être placées à la fin. Au lieu de créer les notes de fin à nouveau, utiliser les outils appropriés pour conversion rapide et sans effort. Cliquez sur la flèche à côté de l'icône Note de bas de page dans l'onglet Références dans la barre d'outils en haut, Glisser votre curseur sur Convertir toutes les notes et sélectionnez l'une des options dans le menu à droite : Convertir tous les pieds de page aux notes de fin pour transformer toutes les notes de bas de page en notes de fin ; Convertir toutes les notes de fin aux pieds de page pour transformer toutes les notes de fin en notes de bas de page ; Changer les notes de pied de page et les notes de fin pour changer un type de note à un autre."
    },
   {
        "id": "UsageInstructions/CopyClearFormatting.htm", 
        "title": "Copier/effacer la mise en forme du texte", 
        "body": "Pour copier une certaine mise en forme du texte dans l'Éditeur de Documents, sélectionnez le fragment du texte contenant la mise en forme à copier en utilisant la souris ou le clavier, cliquez sur l'icône Copier le style sous l'onglet Acceuil sur la barre d'outils supérieure (le pointeur de la souris aura la forme suivante ), sélectionnez le fragment de texte à mettre en forme. Pour appliquer la mise en forme copiée aux plusieurs fragments du texte, sélectionnez le fragment du texte contenant la mise en forme à copier en utilisant la souris ou le clavier, double-cliquez sur l'icône Copier le style sous l'onglet Accueil de la barre d'outils supérieure (le pointeur de la souris aura la forme suivante et l'icône Copier le style restera sélectionnée : ), sélectionnez les fragments du texte nécessaires un par un pour appliquer la même mise en forme pour chacun d'eux, pour quitter ce mode, cliquez sur l'icône Copier le style encore une fois ou appuyez sur la touche Échap sur le clavier. Pour effacer la mise en forme appliquée à partir de votre texte, sélectionnez le fragment de texte dont vous souhaitez supprimer la mise en forme, cliquez sur l'icône Effacer le style sous l'onglet Accueil de la barre d'outils supérieure."
    },
   {
        "id": "UsageInstructions/CopyPasteUndoRedo.htm", 
        "title": "Copier/coller les passages de texte, annuler/rétablir vos actions", 
        "body": "Utiliser les opérations de base du presse-papiers Pour couper, copier, coller des passages de texte et des objets insérés (formes automatiques, images, graphiques) dans l'Éditeur de Documents utilisez les options correspondantes dans le menu contextuel ou les icônes de la barre d'outils supérieure : Couper – sélectionnez un fragment de texte ou un objet et utilisez l'option Couper dans le menu contextuel pour supprimer la sélection et l'envoyer dans le presse-papiers de l'ordinateur. Les données coupées peuvent être insérées plus tard dans un autre endroit dans le même document. Copier – sélectionnez un fragment de texte ou un objet et utilisez l'option Copier dans le menu contextuel, ou l'icône Copier de la barre d'outils supérieure pour copier la sélection dans le presse-papiers de l'ordinateur. Les données coupées peuvent être insérées plus tard dans un autre endroit dans le même document. Coller – trouvez l'endroit dans votre document où vous voulez coller le fragment de texte/l'objet précédemment copié et utilisez l'option Coller dans le menu contextuel, ou l'icône Coller de la barre d'outils supérieure. Le texte / objet sera inséré à la position actuelle du curseur. Le texte peut être copié à partir du même document. Dans la version en ligne, les combinaisons de touches suivantes ne sont utilisées que pour copier ou coller des données de/vers un autre document ou un autre programme, dans la version de bureau, les boutons/options de menu et les combinaisons de touches correspondantes peuvent être utilisées pour toute opération copier/coller : Ctrl+X pour couper ; Ctrl+C pour copier ; Ctrl+V pour coller. Remarque : au lieu de couper et coller du texte dans le même document, vous pouvez sélectionner le passage de texte nécessaire et le faire glisser à la position nécessaire. Utiliser la fonctionnalité Collage spécial Note : Pendant le travail collaboratif, la fonctionnalité Collage spécial n'est disponible que pour le mode de collaboration Strict. Une fois le texte copié et collé, le bouton Collage spécial apparaît à côté du passage de texte inséré. Cliquez sur ce bouton pour sélectionner l'option de collage requise ou utilisez la touche Ctrl en combinaison avec la touche indiquée entre parenthèses à côté de l'option requise. Lorsque vous collez le texte de paragraphe ou du texte dans des formes automatiques, les options suivantes sont disponibles : Garder la mise en forme source (Ctrl+K) - permet de coller le texte copié en conservant sa mise en forme d'origine. Garder le texte seulement (Ctrl+T) - permet de coller le texte sans sa mise en forme d'origine. Si vous collez le tableau copié dans un tableau existant, les options suivantes sont disponibles : Remplacer les cellules (Ctrl+O) - permet de remplacer le contenu de la table existante par les données collées. Cette option est sélectionnée par défaut. Imbriquer le tableau (Ctrl+N) - permet de coller le tableau copié en tant que tableau imbriqué dans la cellule sélectionnée du tableau existant. Conserver le texte uniquement (Ctrl+T) - permet de coller le contenu du tableau sous forme de valeurs de texte séparées par le caractère de tabulation. Pour activer / désactiver l'affichage du bouton Collage spécial lorsque vous collez le texte, passez à l'onglet Fichier > Paramètres avancés... et cochez / décochez la case Afficher le bouton \"Options de collage\" lorsque le contenu est collé. Annuler/rétablir vos actions Pour effectuer les opérations annuler/rétablir, utilisez les icônes correspondantes dans l'en-tête de l'éditeur ou les raccourcis clavier : Annuler – utilisez l'icône Annuler située dans la partie gauche de l'en-tête de l'éditeur ou la combinaison de touches Ctrl+Z pour annuler la dernière opération effectuée. Rétablir – utilisez l'icône Rétablir située dans la partie gauche de l'en-tête de l'éditeur ou la combinaison de touches Ctrl+Y pour rétablir l'opération précédemment annulée. Remarque : lorsque vous co-éditez un document en mode Rapide, la possibilité de Rétablir la dernière opération annulée n'est pas disponible."
    },
   {
        "id": "UsageInstructions/CreateFillableForms.htm", 
        "title": "Créer des formulaires à remplir", 
        "body": "ONLYOFFICE Document Editor permet de créer plus facilement des formulaires à remplir dans votre document, par ex. les projets de contrats ou les enquêtes. Modèle de formulaire fournit un ensemble d'outils pour créer des formulaires à remplir au format DOCXF. Sauvegardez le formulaire résultant au format DOCXF et vous aurez un modèle de formulaire modifiable que vous pourrez réviser ou travailler à plusieurs. Pour créer un formulaire à remplir et restreindre la modification du formulaire par d'autres utilisateurs, sauvegardez-le au format OFORM . Pour en savoir plus, veuillez consulter les instructions pour remplir un formulaire . Les formats DOCXF et OFORM sont de nouveaux formats ONLYOFFICE permettant de créer des modèles de formulaires et de remplir les formulaires: Optez pour les versions ONLYOFFICE Document Editor en ligne ou de bureau pour utiliser pleinement tous les fonctionnalités et les options liées aux formulaires. Vous pouvez sauvegarder tout fichier DOCX existant au format DOCXF pour l'utiliser en tant que Modèle de formulaire. Passez à l'onglet Fichier, cliquez sur Télécharger comme... ou Enregistrer sous... sur le panneau latéral gauche et sélectionnez l'icône DOCXF. Vous pouvez maintenant utiliser toutes les fonctionnalités d'édition d'un formulaire. Ce ne sont pas seulement des champs de formulaire qu'on peut modifier dans un fichier DOCXF, il vous est toujours possible d'ajouter, de modifier et de mettre en forme du texte et d'utiliser d'autres fonctionnalités de Document Editor . Les formulaires à remplir peuvent être réalisés en utilisant des objets modifiables afin d'assurer une cohérence globale du document final et d'améliorer l'expérience de travail avec des formulaires interactifs. Actuellement, vous pouvez ajouter un champ texte, une zone de liste déroulante, une liste déroulante, une case à cocher, un bouton radio, définir les zones désignées pour des images et ajouter les zones destinées à l'adresse email, le numéro de téléphone, la date et l'heure, le code postal, la carte bancaire et les champs complexes. Vous pouvez accéder à ces fonctionnalités sous l'onglet Formulaires qui n'est disponible qu'avec des fichiers DOCXF. Créer un champ texte Les Champs texte sont les champs de texte brut, aucun objet ne peut être ajouté. Pour ajouter un champ texte, positionnez le point d'insertion à la ligne du texte où vous souhaitez ajouter un champ, passez à l'onglet Formulaires de la barre d'outils supérieure, cliquez sur l'icône Champ texte ou cliquez sur la flèche à côté de l'icône Champ texte et sélectionnez le champ à insérer champ texte en ligne ou champ texte fixe. Pour en savoir plus sur les champs fixes, veuillez consulter la section Taille de champ fixe ci-dessous. Un champ du formulaire apparaîtra à la point d'insertion de la ligne de texte existante. Le menu Paramètres du formulaire s'affiche à droite. Qui doit remplir ce champ?: sélectionnez le rôle dans la liste déroulante pour affecter les utilisateurs au groupe d'utilisateurs qui ont accès à ce champ. Pour en savoir plus sur l'affectation des utilisateurs aux rôles, veuillez consulter l'article Gérer des rôles de ce guide-ci. Clé: une clé à grouper les champs afin de les remplir simultanément. Pour créer une nouvelle clé, saisissez le nom de celle-là et appuyez sur Entrée, ensuite attribuez cette clé à chaque champ texte en choisissant de la liste déroulante. Message Champs connectés: 2/3/... s'affiche. Pour dissocier les champs, cliquez sur Déconnexion. Espace réservé: saisissez le texte à afficher dans le champ de saisie. Le texte par défaut est Votre texte ici. Tag: saisissez le texte à afficher en tant que tag pour l'utilisation interne, c-à-d à afficher aux co-éditeurs uniquement. Conseil: saisissez le texte à afficher quand l'utilisateur fait passer la souris sur le champ texte. Format: définissez le format du champ texte, c-à-d les caractères sont autorisés en fonction du format défini: Aucun, Chiffres, Lettres, Masque arbitraire (le texte doit correspondre au masque personnalisé, par ex., (999) 999 99 99), Expression régulière (le texte doit correspondre à l'expression régulière). Lorsque vous sélectionnez Masque arbitraire ou Expression régulière, un champ supplémentaire apparaît au-dessous du champ Format. Symboles autorisés: saisissez les symboles qui sont autorisés dans ce champ. Taille de champ fixe: activez cette option pour fixer la taille du champ. Lors de l'activation de cette option, les options Ajuster automatiquement et Champ de saisie à plusieurs lignes deviennent aussi disponibles. Un champ de taille fixe ressemble à une forme automatique. Vous pouvez définir le style d'habillage et ajuster son position. Ajuster automatiquement: il est possible d'activer cette option lors de l'activation de l'option Taille de champ fixe, cochez cette case pour ajuster automatiquement la police en fonction de la taille du champ. Champ de saisie à plusieurs lignes: il est possible d'activer cette option lors de l'activation de l'option Taille de champ fixe, cochez cette case pour créer un champ à plusieurs lignes, sinon le champ va contenir une seule ligne de texte. Limite de caractères: le nombre de caractères n'est pas limité par défaut. Activez cette option pour indiquer le nombre maximum de caractères dans le champ à droite. Peigne de caractères: configurer l'aspect général pour une présentation claire et équilibré. Laissez cette case décoché pour garder les paramètres par défaut ou activez cette option pour configurer les paramètres suivants: Largeur de cellule: définissez la largeur en sélectionnant Automatique (la largeur est redimentionnée automatiquement), Au moins (la largeur doit être au moins égale à la valeur définie) ou Exactement (la largeur doit être égale à la valeur définie). Le texte sera justifié selon les paramètres. Couleur de bordure: cliquez sur l'icône pour définir la couleur des bordures du champ texte ajouté. Sélectionnez la couleur appropriée des bordures dans la palette. Le cas échéant, vous pouvez ajouter une couleur personnalisée. Couleur d'arrière plan: cliquez sur l'icône pour choisir la couleur d'arrière-plan du champ texte ajouté. Sélectionnez la couleur appropriée de la palette Couleurs du thème, Couleurs standard ou ajoutez une nouvelle couleur personnalisée, le cas échéant. Obligatoire: cochez cette case pour rendre ce champ obligatoire de remplir. Cliquez sur le champ texte ajouté et réglez le type, la taille et la couleur de la police, appliquez les styles de décoration et les styles de mise en forme. La mise en forme sera appliquée à l'ensemble du texte dans le champ. Créer une zone de liste déroulante La Zone de liste déroulante comporte une liste déroulante avec un ensemble de choix modifiables. Pour ajouter une zone de liste déroulante, positionnez le point d'insertion à la ligne du texte où vous souhaitez ajouter un champ, passez à l'onglet Formulaires de la barre d'outils supérieure, cliquez sur l'icône Zone de liste déroulante. Le champ de formulaire apparaîtra à la point d'insertion de la ligne de texte existante. Le menu Paramètres du formulaire s'affiche à droite. Qui doit remplir ce champ?: sélectionnez le rôle dans la liste déroulante pour affecter les utilisateurs au groupe d'utilisateurs qui ont accès à ce champ. Pour en savoir plus sur l'affectation des utilisateurs aux rôles, veuillez consulter l'article Gérer des rôles de ce guide-ci. Clé: une clé à grouper les zones de liste déroulante afin de les remplir simultanément. Pour créer une nouvelle clé, saisissez le nom de celle-là et appuyez sur Entrée, ensuite attribuez cette clé à chaque zone de liste déroulante. Choisissez la clé de la liste déroulante. Message Champs connectés: 2/3/... s'affiche. Pour dissocier les champs, cliquez sur Déconnexion. Espace réservé: saisissez le texte à afficher dans zone de liste déroulante. Le texte par défaut est Choisir un élément. Tag: saisissez le texte à afficher en tant que tag pour l'utilisation interne, c-à-d à afficher aux co-éditeurs uniquement. Conseil: saisissez le texte à afficher quand l'utilisateur fait passer la souris sur le champ du formulaire. Options de valeur: ajoutez de nouvelles valeurs, supprimez ou déplacez des valeurs vers le haut et vers le bas dans la liste. Taille de champ fixe: activez cette option pour fixer la taille du champ. Un champ de taille fixe ressemble à une forme automatique. Vous pouvez définir le style d'habillage et ajuster son position. Couleur de bordure: cliquez sur l'icône pour définir la couleur des bordures de la zone de liste déroulante ajoutée. Sélectionnez la couleur appropriée des bordures dans la palette. Le cas échéant, vous pouvez ajouter une couleur personnalisée. Couleur d'arrière plan: cliquez sur l'icône pour choisir la couleur d'arrière-plan de la zone de liste déroulante ajoutée. Sélectionnez la couleur appropriée de la palette Couleurs du thème, Couleurs standard ou ajoutez une nouvelle couleur personnalisée, le cas échéant. Obligatoire: cochez cette case pour rendre la zone de liste déroulante obligatoire de remplir. Cliquez sur la flèche dans la partie droite de la Zone de liste déroulante ajoutée pour accéder à la liste d'éléments disponibles et choisir un élément approprié. Lorsque vous avez choisi un élément, vous pouvez modifier le texte affiché partiellement ou entièrement ou le remplacer du texte. Vous pouvez changer le style, la couleur et la taille de la police. Cliquez sur la zone de liste déroulante et suivez les instructions. La mise en forme sera appliquée à l'ensemble du texte dans le champ. Créer une liste déroulante La Liste déroulante comporte une liste déroulante avec un ensemble de choix non modifiable. Pour ajouter une liste déroulante, positionnez le point d'insertion à la ligne du texte où vous souhaitez ajouter un champ, passez à l'onglet Formulaires de la barre d'outils supérieure, cliquez sur l'icône Liste déroulante. Le champ de formulaire apparaîtra à la point d'insertion de la ligne de texte existante. Le menu Paramètres du formulaire s'affiche à droite. Qui doit remplir ce champ?: sélectionnez le rôle dans la liste déroulante pour affecter les utilisateurs au groupe d'utilisateurs qui ont accès à ce champ. Pour en savoir plus sur l'affectation des utilisateurs aux rôles, veuillez consulter l'article Gérer des rôles de ce guide-ci. Clé: une clé à grouper les listes déroulantes afin de les remplir simultanément. Pour créer une nouvelle clé, saisissez le titre de celle-là et appuyez sur Entrée, ensuite attribuez cette clé aux champs du formulaire en choisissant de la liste déroulante. Message Champs connectés: 2/3/... s'affiche. Pour dissocier les champs, cliquez sur Déconnexion. Espace réservé: saisissez le texte à afficher dans la liste déroulante. Le texte par défaut est Choisir un élément. Tag: saisissez le texte à afficher en tant que tag pour l'utilisation interne, c-à-d à afficher aux co-éditeurs uniquement. Conseil: saisissez le texte à afficher quand l'utilisateur fait passer la souris sur le champ du formulaire. Options de valeur: ajoutez de nouvelles valeurs, supprimez ou déplacez des valeurs vers le haut et vers le bas dans la liste. Taille de champ fixe: activez cette option pour fixer la taille du champ. Un champ de taille fixe ressemble à une forme automatique. Vous pouvez définir le style d'habillage et ajuster son position. Couleur de bordure: cliquez sur l'icône pour définir la couleur des bordures de la liste déroulante ajoutée. Sélectionnez la couleur appropriée des bordures dans la palette. Le cas échéant, vous pouvez ajouter une couleur personnalisée. Couleur d'arrière plan: cliquez sur l'icône pour choisir la couleur d'arrière-plan de la liste déroulante ajoutée. Sélectionnez la couleur appropriée de la palette Couleurs du thème, Couleurs standard ou ajoutez une nouvelle couleur personnalisée, le cas échéant. Obligatoire: cochez cette case pour rendre la liste déroulante obligatoire de remplir. Cliquez sur la flèche dans la partie droite de la Liste déroulante ajoutée pour accéder à la liste d'éléments disponibles et choisir un élément approprié. Créer une case à cocher La Case à cocher fournit plusieurs options permettant à l'utilisateur de sélectionner autant de cases à cocher que nécessaire. Cases à cocher sont utilisées indépendamment, alors chaque case peut être coché et ou décoché. Pour insérer une case à cocher, positionnez le point d'insertion à la ligne du texte où vous souhaitez ajouter un champ, passez à l'onglet Formulaires de la barre d'outils supérieure, cliquez sur l'icône Case à cocher. Le champ du formulaire apparaîtra à la point d'insertion de la ligne de texte existante. Le menu Paramètres du formulaire s'affiche à droite. Qui doit remplir ce champ?: sélectionnez le rôle dans la liste déroulante pour affecter les utilisateurs au groupe d'utilisateurs qui ont accès à ce champ. Pour en savoir plus sur l'affectation des utilisateurs aux rôles, veuillez consulter l'article Gérer des rôles de ce guide-ci. Clé: une clé à grouper les cases à cocher afin de les remplir simultanément. Pour créer une nouvelle clé, saisissez le titre de celle-là et appuyez sur Entrée, ensuite attribuez cette clé aux champs du formulaire en choisissant de la liste déroulante. Message Champs connectés: 2/3/... s'affiche. Pour dissocier les champs, cliquez sur Déconnexion. Tag: saisissez le texte à afficher en tant que tag pour l'utilisation interne, c-à-d à afficher aux co-éditeurs uniquement. Conseil: saisissez le texte à afficher quand l'utilisateur fait passer la souris sur la case à cocher. Taille de champ fixe: activez cette option pour fixer la taille du champ. Un champ de taille fixe ressemble à une forme automatique. Vous pouvez définir le style d'habillage et ajuster son position. Couleur de bordure: cliquez sur l'icône pour définir la couleur des bordures de la case à cocher ajoutée. Sélectionnez la couleur appropriée des bordures dans la palette. Le cas échéant, vous pouvez ajouter une couleur personnalisée. Couleur d'arrière plan: cliquez sur l'icône pour choisir la couleur d'arrière-plan de la case à cocher ajoutée. Sélectionnez la couleur appropriée de la palette Couleurs du thème, Couleurs standard ou ajoutez une nouvelle couleur personnalisée, le cas échéant. Obligatoire: cochez cette case pour rendre cette case à cocher obligatoire de remplir. Cliquez sur la case pour la cocher. Créer un bouton radio Le Bouton radio fournit plusieurs options permettant à l'utilisateur de choisir une seule option parmi plusieurs possibles. Boutons radio sont utilisés en groupe, alors on ne peut pas choisir plusieurs boutons de la même groupe. Pour ajouter un bouton radio, positionnez le point d'insertion à la ligne du texte où vous souhaitez ajouter un champ, passez à l'onglet Formulaires de la barre d'outils supérieure, cliquez sur l'icône du bouton radio Le champ du formulaire apparaîtra à la point d'insertion de la ligne de texte existante. Le menu Paramètres du formulaire s'affiche à droite. Qui doit remplir ce champ?: sélectionnez le rôle dans la liste déroulante pour affecter les utilisateurs au groupe d'utilisateurs qui ont accès à ce champ. Pour en savoir plus sur l'affectation des utilisateurs aux rôles, veuillez consulter l'article Gérer des rôles de ce guide-ci. Clé de groupe: pour créer un nouveau groupe de boutons radio, saisissez le nom du groupe et appuyez sur Entrée, ensuite attribuez chaque bouton radio au groupe approprié. Tag: saisissez le texte à afficher en tant que tag pour l'utilisation interne, c-à-d à afficher aux co-éditeurs uniquement. Conseil: saisissez le texte à afficher quand l'utilisateur fait passer la souris sur le bouton radio. Taille de champ fixe: activez cette option pour fixer la taille du champ. Un champ de taille fixe ressemble à une forme automatique. Vous pouvez définir le style d'habillage et ajuster son position. Couleur de bordure: cliquez sur l'icône pour définir la couleur des bordures du bouton radio ajouté. Sélectionnez la couleur appropriée des bordures dans la palette. Le cas échéant, vous pouvez ajouter une couleur personnalisée. Couleur d'arrière plan: cliquez sur l'icône pour choisir la couleur d'arrière-plan du bouton radio ajouté. Sélectionnez la couleur appropriée de la palette Couleurs du thème, Couleurs standard ou ajoutez une nouvelle couleur personnalisée, le cas échéant. Obligatoire: cochez cette case pour rendre le bouton radio ajouté obligatoire de remplir. Cliquez sur le bouton radio pour le choisir. Créer un champ image Le champ Image est le champ du formulaire permettant d'insérer une image selon des limites définies, c-à-d, la position et la taille de l'image. Pour ajouter un champ d'image, positionnez le point d'insertion à la ligne du texte où vous souhaitez ajouter un champ, passez à l'onglet Formulaires de la barre d'outils supérieure, cliquez sur l'icône Image. Le champ du formulaire apparaîtra à la point d'insertion de la ligne de texte existante. Le menu Paramètres du formulaire s'affiche à droite. Qui doit remplir ce champ?: sélectionnez le rôle dans la liste déroulante pour affecter les utilisateurs au groupe d'utilisateurs qui ont accès à ce champ. Pour en savoir plus sur l'affectation des utilisateurs aux rôles, veuillez consulter l'article Gérer des rôles de ce guide-ci. Clé: une clé à grouper les images afin de les remplir simultanément. Pour créer une nouvelle clé, saisissez le nom de celle-là et appuyez sur Entrée, ensuite attribuez cette clé aux champs du formulaire en choisissant de la liste déroulante. Message Champs connectés: 2/3/... s'affiche. Pour dissocier les champs, cliquez sur Déconnexion. Espace réservé: saisissez le texte à afficher dans le champ d'image. Le texte par défaut est Cliquer pour télécharger l'image. Tag: saisissez le texte à afficher en tant que tag pour l'utilisation interne, c-à-d à afficher aux co-éditeurs uniquement. Conseil: saisissez le texte à afficher quand l'utilisateur fait passer la souris sur la bordure inférieure de l'image. Mise à l'échelle: cliquez sur la liste déroulante et sélectionnez l'option de dimensionnement de l'image appropriée: Toujours, Jamais, lorsque L'image semble trop grande ou L'image semble trop petite. L'image sélectionnée sera redimensionnée dans le champ en fonction de l'option choisie. Verrouiller les proportions: cochez cette case pour maintenir le rapport d'aspect de l'image sans la déformer. Lors de l'activation de cette option, faites glisser le curseur vertical ou horizontal pour positionner l'image à en dedans du champ ajouté. Si la case n'est pas cochée, les curseurs de positionnement ne sont pas activés. Sélectionnez une image: cliquez sur ce bouton pour télécharger une image Depuis un fichier, À partir d'une URL ou À partir de l'espace de stockage. Couleur de bordure: cliquez sur l'icône pour définir la couleur des bordures du champ image ajouté. Sélectionnez la couleur appropriée des bordures dans la palette. Le cas échéant, vous pouvez ajouter une couleur personnalisée. Couleur d'arrière plan: cliquez sur l'icône pour choisir la couleur d'arrière-plan du champ image ajouté. Sélectionnez la couleur appropriée de la palette Couleurs du thème, Couleurs standard ou ajoutez une nouvelle couleur personnalisée, le cas échéant. Obligatoire: cochez cette case pour rendre ce champ image obligatoire de remplir. Pour remplacer une image, cliquez sur l'icône d'image au-dessus de la bordure de champ image et sélectionnez une autre image. Pour paramétrer l'image, accédez à l'onglet Paramètres de l'image sur la barre latérale droite. Pour en savoir plus, veuillez consulter les instructions sur paramètres d'image. Créer un champ adresse e-mail Le champ Adresse e-mail sert à saisir une adresse e-mail suivant l'expression régulière \\S+@\\S+\\.\\S+. Pour ajouter un champ adresse e-mail, positionnez le point d'insertion à la ligne du texte où vous souhaitez ajouter un champ, passez à l'onglet Formulaires de la barre d'outils supérieure, cliquez sur l'icône Adresse e-mail. Le champ du formulaire apparaîtra à la point d'insertion de la ligne de texte existante. Le menu Paramètres du formulaire s'affiche à droite. Qui doit remplir ce champ?: sélectionnez le rôle dans la liste déroulante pour affecter les utilisateurs au groupe d'utilisateurs qui ont accès à ce champ. Pour en savoir plus sur l'affectation des utilisateurs aux rôles, veuillez consulter l'article Gérer des rôles de ce guide-ci. Clé: pour créer un nouveau groupe d'adresses e-mail, saisissez le nom du groupe et appuyez sur Entrée, ensuite attribuez chaque champ adresse e-mail au groupe approprié. Espace réservé: saisissez le texte à afficher dans le champ d'adresse e-mail. Le texte par dé<NAME_EMAIL>. Tag: saisissez le texte à afficher en tant que tag pour l'utilisation interne, c-à-d à afficher aux co-éditeurs uniquement. Conseil: saisissez le texte à afficher quand l'utilisateur fait passer la souris sur le champ adresse e-mail. Format: définissez le format du champ, c-à-d Aucun, Chiffres, Lettres, Masque arbitraire ou Expression régulière. Le format par défaut est Expression régulière afin de conserver le format d'adresse e-mail \\S+@\\S+\\.\\S+. Symboles autorisés: saisissez les symboles qui sont autorisés dans le champ adresse e-mail. Taille de champ fixe: activez cette option pour fixer la taille du champ. Lors de l'activation de cette option, les options Ajuster automatiquement et Champ de saisie à plusieurs lignes deviennent aussi disponibles. Un champ de taille fixe ressemble à une forme automatique. Vous pouvez définir le style d'habillage et ajuster son position. Ajuster automatiquement: il est possible d'activer cette option lors de l'activation de l'option Taille de champ fixe, cochez cette case pour ajuster automatiquement la police en fonction de la taille du champ. Champ de saisie à plusieurs lignes: il est possible d'activer cette option lors de l'activation de l'option Taille de champ fixe, cochez cette case pour créer un champ à plusieurs lignes, sinon le champ va contenir une seule ligne de texte. Limite de caractères: le nombre de caractères n'est pas limité par défaut. Activez cette option pour indiquer le nombre maximum de caractères dans le champ à droite. Peigne de caractères: configurer l'aspect général pour une présentation claire et équilibré dans le champ adresse e-mail. Laissez cette case décoché pour garder les paramètres par défaut ou activez cette option pour configurer les paramètres suivants: Largeur de cellule: définissez la largeur en sélectionnant Automatique (la largeur est redimentionnée automatiquement), Au moins (la largeur doit être au moins égale à la valeur définie) ou Exactement (la largeur doit être égale à la valeur définie). Le texte sera justifié selon les paramètres. Couleur de bordure: cliquez sur l'icône pour définir la couleur des bordures du champ adresse e-mail ajouté. Sélectionnez la couleur appropriée des bordures dans la palette. Le cas échéant, vous pouvez ajouter une couleur personnalisée. Couleur d'arrière plan: cliquez sur l'icône pour choisir la couleur d'arrière-plan du champ adresse e-mail ajouté. Sélectionnez la couleur appropriée de la palette Couleurs du thème, Couleurs standard ou ajoutez une nouvelle couleur personnalisée, le cas échéant. Obligatoire: cochez cette case pour rendre le champ d'adresse e-mail obligatoire de remplir. Créer un champ numéro de téléphone Le champ Numéro de téléphone sert à saisir le numéro de téléphone selon le masque arbitraire défini par l'auteur du formulaire. Par défaut, c'est (999)999-9999. Pour ajouter un champ numéro de téléphone, positionnez le point d'insertion à la ligne du texte où vous souhaitez ajouter un champ, passez à l'onglet Formulaires de la barre d'outils supérieure, cliquez sur l'icône Numéro de téléphone. Le champ du formulaire apparaîtra à la point d'insertion de la ligne de texte existante. Le menu Paramètres du formulaire s'affiche à droite. Qui doit remplir ce champ?: sélectionnez le rôle dans la liste déroulante pour affecter les utilisateurs au groupe d'utilisateurs qui ont accès à ce champ. Pour en savoir plus sur l'affectation des utilisateurs aux rôles, veuillez consulter l'article Gérer des rôles de ce guide-ci. Clé: pour créer un nouveau groupe de numéros de téléphones, saisissez le nom du groupe et appuyez sur Entrée, ensuite attribuez chaque numéro de téléphone au groupe approprié. Espace réservé: saisissez le texte à afficher dans le champ numéro de téléphone. Le texte par défaut est (999)999-9999. Tag: saisissez le texte à afficher en tant que tag pour l'utilisation interne, c-à-d à afficher aux co-éditeurs uniquement. Conseil: saisissez le texte à afficher quand l'utilisateur fait passer la souris sur le champ numéro de téléphone. Format: définissez le format du champ, c-à-d Aucun, Chiffres, Lettres, Masque arbitraire ou Expression régulière. Le format par défaut est Masque arbitraire Pour modifier le format, saisissez le masque approprié dans le champ au-dessous. Symboles autorisés: saisissez les symboles qui sont autorisés dans le champ numéro de téléphone. Taille de champ fixe: activez cette option pour fixer la taille du champ. Lors de l'activation de cette option, les options Ajuster automatiquement et Champ de saisie à plusieurs lignes deviennent aussi disponibles. Un champ de taille fixe ressemble à une forme automatique. Vous pouvez définir le style d'habillage et ajuster son position. Ajuster automatiquement: il est possible d'activer cette option lors de l'activation de l'option Taille de champ fixe, cochez cette case pour ajuster automatiquement la police en fonction de la taille du champ. Champ de saisie à plusieurs lignes: il est possible d'activer cette option lors de l'activation de l'option Taille de champ fixe, cochez cette case pour créer un champ à plusieurs lignes, sinon le champ va contenir une seule ligne de texte. Limite de caractères: le nombre de caractères n'est pas limité par défaut. Activez cette option pour indiquer le nombre maximum de caractères dans le champ à droite. Peigne de caractères: configurer l'aspect général pour une présentation claire et équilibré dans le champ numéro de téléphone. Laissez cette case décoché pour garder les paramètres par défaut ou activez cette option pour configurer les paramètres suivants: Largeur de cellule: définissez la largeur en sélectionnant Automatique (la largeur est redimentionnée automatiquement), Au moins (la largeur doit être au moins égale à la valeur définie) ou Exactement (la largeur doit être égale à la valeur définie). Le texte sera justifié selon les paramètres. Couleur de bordure: cliquez sur l'icône pour définir la couleur des bordures du champ numéro de téléphone ajouté. Sélectionnez la couleur appropriée des bordures dans la palette. Le cas échéant, vous pouvez ajouter une couleur personnalisée. Couleur d'arrière plan: cliquez sur l'icône pour choisir la couleur d'arrière-plan du champ numéro de téléphone ajouté. Sélectionnez la couleur appropriée de la palette Couleurs du thème, Couleurs standard ou ajoutez une nouvelle couleur personnalisée, le cas échéant. Obligatoire: cochez cette case pour rendre le champ numéro de téléphone ajouté obligatoire de remplir. Créer un champ Date et heure Le champ Date et heure sert à insérer la date. Par défaut, le format de date est DD-MM-YYYY. Pour ajouter un champ date et heure, positionnez le point d'insertion à la ligne du texte où vous souhaitez ajouter un champ, passez à l'onglet Formulaires de la barre d'outils supérieure, cliquez sur l'icône Date et heure. Le champ du formulaire apparaîtra à la point d'insertion de la ligne de texte existante. Pour saisir une date, cliquez sur la flèche dans le champ et sélectionnez la date appropriée dans le calendrier. Créer un champ code postal Le champ Code postal sert à saisir le code postal selon le masque arbitraire défini par l'auteur du formulaire. Par défaut, c'est 99999-9999. Pour ajouter un champ code postal, positionnez le point d'insertion à la ligne du texte où vous souhaitez ajouter un champ, passez à l'onglet Formulaires de la barre d'outils supérieure, cliquez sur l'icône Code postal. Le champ du formulaire apparaîtra à la point d'insertion de la ligne de texte existante. Le menu Paramètres du formulaire s'affiche à droite. Qui doit remplir ce champ?: sélectionnez le rôle dans la liste déroulante pour affecter les utilisateurs au groupe d'utilisateurs qui ont accès à ce champ. Pour en savoir plus sur l'affectation des utilisateurs aux rôles, veuillez consulter l'article Gérer des rôles de ce guide-ci. Clé: pour créer un nouveau groupe de numéros de téléphones, saisissez le nom du groupe et appuyez sur Entrée, ensuite attribuez chaque numéro de téléphone au groupe approprié. Espace réservé: saisissez le texte à afficher dans le champ code postal. Le texte par défaut est 99999-9999. Tag: saisissez le texte à afficher en tant que tag pour l'utilisation interne, c-à-d à afficher aux co-éditeurs uniquement. Conseil: saisissez le texte à afficher quand l'utilisateur fait passer la souris sur le champ code postal. Format: définissez le format du champ, c-à-d Aucun, Chiffres, Lettres, Masque arbitraire ou Expression régulière. Le format par défaut est Masque arbitraire Pour modifier le format, saisissez le masque approprié dans le champ au-dessous. Symboles autorisés: saisissez les symboles qui sont autorisés dans le champ code postal. Taille de champ fixe: activez cette option pour fixer la taille du champ. Lors de l'activation de cette option, les options Ajuster automatiquement et Champ de saisie à plusieurs lignes deviennent aussi disponibles. Un champ de taille fixe ressemble à une forme automatique. Vous pouvez définir le style d'habillage et ajuster son position. Ajuster automatiquement: il est possible d'activer cette option lors de l'activation de l'option Taille de champ fixe, cochez cette case pour ajuster automatiquement la police en fonction de la taille du champ. Champ de saisie à plusieurs lignes: il est possible d'activer cette option lors de l'activation de l'option Taille de champ fixe, cochez cette case pour créer un champ à plusieurs lignes, sinon le champ va contenir une seule ligne de texte. Limite de caractères: le nombre de caractères n'est pas limité par défaut. Activez cette option pour indiquer le nombre maximum de caractères dans le champ à droite. Peigne de caractères: configurer l'aspect général pour une présentation claire et équilibré dans le champ code postal. Laissez cette case décoché pour garder les paramètres par défaut ou activez cette option pour configurer les paramètres suivants: Largeur de cellule: définissez la largeur en sélectionnant Automatique (la largeur est redimentionnée automatiquement), Au moins (la largeur doit être au moins égale à la valeur définie) ou Exactement (la largeur doit être égale à la valeur définie). Le texte sera justifié selon les paramètres. Couleur de bordure: cliquez sur l'icône pour définir la couleur des bordures du champ code postal ajouté. Sélectionnez la couleur appropriée des bordures dans la palette. Le cas échéant, vous pouvez ajouter une couleur personnalisée. Couleur d'arrière plan: cliquez sur l'icône pour choisir la couleur d'arrière-plan du champ code postal ajouté. Sélectionnez la couleur appropriée de la palette Couleurs du thème, Couleurs standard ou ajoutez une nouvelle couleur personnalisée, le cas échéant. Obligatoire: cochez cette case pour rendre le champ code postal ajouté obligatoire de remplir. Créer un champ carte bancaire Le champ Carte bancaire sert à saisir le numéro de carte la bancaire selon le masque arbitraire défini par l'auteur du formulaire. Par défaut, c'est **************-9999. Pour ajouter un champ numéro de carte la bancaire, positionnez le point d'insertion à la ligne du texte où vous souhaitez ajouter un champ, passez à l'onglet Formulaires de la barre d'outils supérieure, cliquez sur l'icône Carte bancaire. Le champ du formulaire apparaîtra à la point d'insertion de la ligne de texte existante. Le menu Paramètres du formulaire s'affiche à droite. Qui doit remplir ce champ?: sélectionnez le rôle dans la liste déroulante pour affecter les utilisateurs au groupe d'utilisateurs qui ont accès à ce champ. Pour en savoir plus sur l'affectation des utilisateurs aux rôles, veuillez consulter l'article Gérer des rôles de ce guide-ci. Clé: pour créer un nouveau groupe de numéros des cartes bancaires, saisissez le nom du groupe et appuyez sur Entrée, ensuite attribuez chaque champ numéro de carte la bancaire au groupe approprié. Espace réservé: saisissez le texte à afficher dans le champ numéro de la carte la bancaire. Le texte par défaut est **************-9999. Tag: saisissez le texte à afficher en tant que tag pour l'utilisation interne, c-à-d à afficher aux co-éditeurs uniquement. Conseil: saisissez le texte à afficher quand l'utilisateur fait passer la souris sur le champ numéro de la carte la bancaire. Format: définissez le format du champ, c-à-d Aucun, Chiffres, Lettres, Masque arbitraire ou Expression régulière. Le format par défaut est Masque arbitraire Pour modifier le format, saisissez le masque approprié dans le champ au-dessous. Symboles autorisés: saisissez les symboles qui sont autorisés dans le champ numéro de la carte la bancaire. Taille de champ fixe: activez cette option pour fixer la taille du champ. Lors de l'activation de cette option, les options Ajuster automatiquement et Champ de saisie à plusieurs lignes deviennent aussi disponibles. Un champ de taille fixe ressemble à une forme automatique. Vous pouvez définir le style d'habillage et ajuster son position. Ajuster automatiquement: il est possible d'activer cette option lors de l'activation de l'option Taille de champ fixe, cochez cette case pour ajuster automatiquement la police en fonction de la taille du champ. Champ de saisie à plusieurs lignes: il est possible d'activer cette option lors de l'activation de l'option Taille de champ fixe, cochez cette case pour créer un champ à plusieurs lignes, sinon le champ va contenir une seule ligne de texte. Limite de caractères: le nombre de caractères n'est pas limité par défaut. Activez cette option pour indiquer le nombre maximum de caractères dans le champ à droite. Peigne de caractères: configurer l'aspect général pour une présentation claire et équilibré dans le champ numéro de la carte la bancaire. Laissez cette case décoché pour garder les paramètres par défaut ou activez cette option pour configurer les paramètres suivants: Largeur de cellule: définissez la largeur en sélectionnant Automatique (la largeur est redimentionnée automatiquement), Au moins (la largeur doit être au moins égale à la valeur définie) ou Exactement (la largeur doit être égale à la valeur définie). Le texte sera justifié selon les paramètres. Couleur de bordure: cliquez sur l'icône pour définir la couleur des bordures du champ numéro de la carte la bancaire ajouté. Sélectionnez la couleur appropriée des bordures dans la palette. Le cas échéant, vous pouvez ajouter une couleur personnalisée. Couleur d'arrière plan: cliquez sur l'icône pour choisir la couleur d'arrière-plan du champ numéro de la carte la bancaire ajouté. Sélectionnez la couleur appropriée de la palette Couleurs du thème, Couleurs standard ou ajoutez une nouvelle couleur personnalisée, le cas échéant. Obligatoire: cochez cette case pour rendre le champ numéro de la carte la bancaire ajouté obligatoire de remplir. Créer un champ complexe Un champ complexe peut combiner plusieurs champs, par ex. le champ texte et le champ liste déroulante. Vous pouvez combiner les champs selon vos besoins. Pour ajouter un champ complexe, positionnez le point d'insertion à la ligne du texte où vous souhaitez ajouter un champ, passez à l'onglet Formulaires de la barre d'outils supérieure, cliquez sur l'icône Champ complexe. Le champ du formulaire apparaîtra à la point d'insertion de la ligne de texte existante. Le menu Paramètres du formulaire s'affiche à droite. Qui doit remplir ce champ?: sélectionnez le rôle dans la liste déroulante pour affecter les utilisateurs au groupe d'utilisateurs qui ont accès à ce champ. Pour en savoir plus sur l'affectation des utilisateurs aux rôles, veuillez consulter l'article Gérer des rôles de ce guide-ci. Clé: pour créer un nouveau groupe de champs complexes, saisissez le nom du groupe et appuyez sur Entrée, ensuite attribuez chaque champ complexe au groupe approprié. Espace réservé: saisissez le texte à afficher dans le champ complexe. Le texte par défaut est Votre texte ici. Tag: saisissez le texte à afficher en tant que tag pour l'utilisation interne, c-à-d à afficher aux co-éditeurs uniquement. Conseil: saisissez le texte à afficher quand l'utilisateur fait passer la souris sur le champ complexe. Taille de champ fixe: activez cette option pour fixer la taille du champ. Un champ de taille fixe ressemble à une forme automatique. Vous pouvez définir le style d'habillage et ajuster son position. Couleur de bordure: cliquez sur l'icône pour définir la couleur des bordures du champ complexe ajouté. Sélectionnez la couleur appropriée des bordures dans la palette. Le cas échéant, vous pouvez ajouter une couleur personnalisée. Couleur d'arrière plan: cliquez sur l'icône pour choisir la couleur d'arrière-plan du champ complexe ajouté. Sélectionnez la couleur appropriée de la palette Couleurs du thème, Couleurs standard ou ajoutez une nouvelle couleur personnalisée, le cas échéant. Obligatoire: cochez cette case pour rendre ce champ complexe obligatoire de remplir. Pour ajouter plusieurs champs au champ complexe, cliquez dessous et sélectionnez le champ approprié de la barre d'outils supérieure sous l'onglet Formulaire. pour en savoir plus sur chaque champ, veuillez consulter les sections appropriées ci-dessus. Veuillez noter qu'on ne peut pas ajouter le champ image dans un champ complexe. Gérer des rôles Vous pouvez créer de nouveaux rôles pour permettre aux utilisateurs de remplir les champs du formulaire. Pour gérer les rôles, passez à l'onglet Formulaires de la barre d'outils supérieure. cliquez sur l'icône Gérer les rôles, cliquez sur le bouton Nouveau pour ajouter un nouveau rôle. saisissez le nom de rôle et sélectionnez la couleur, le cas échéant. Vous pouvez également ajouter la couleur personnalisée en cliquant sur l'option appropriée dans le menu, cliquez sur OK pour ajouter un nouveau rôle. définissez l'ordre dans lequel on va recevoir et signer le document à l'aide de et boutons, utilisez le boutons Modifier et Supprimer pour modifier et supprimer les rôles, cliquez sur Fermer pour revenir à l'édition du formulaire. Lors du sauvegarde du formulaire au format .oform, vous pouvez afficher tous les rôles du formulaire. Afficher l'aperçu du formulaire Remarque: Les options de modification ne sont pas disponibles lorsque vous activez le mode Aperçu du formulaire. Cliquez sur le bouton Aperçu du formulaire sous l'onglet Formulaire de la barre d'outils supérieure, pour afficher un aperçu du formulaire sur un document. Vous pouvez afficher l'aperçu du formulaire à travers le regard de chaque rôle créé pour le formulaire. Pour ce faire, cliquez sur la flèche au-dessous du bouton Aperçu du formulaire et sélectionnez le rôle approprié. Pour quitter le mode d'aperçu, cliquer sur la même icône encore une fois. Déplacer les champs du formulaire Il est possible de déplacer les champs du formulaire vers un autre emplacement du document: cliquez sur le bouton à gauche de la bordure de contrôle et faites la glisser vers un autre emplacement sans relâcher le bouton de la souris. Vous pouvez également copier et coller les champs du formulaire: sélectionnez le champ approprié et appuyez sur le raccourci Ctrl+C/Ctrl+V. Rendre un champ de formulaire obligatoire Pour rendre un champ de formulaire obligatoire, activez l'option Obligatoire. La bordure des champs obligatoires est marquée d'un trait rouge. Verrouiller les champs du formulaire Pour empêcher toute la modification ultérieure des champs du formulaire, cliquez sur l'icône Verrou . La fonctionnalité de remplissage reste disponible. Effacer les champs remplis dans un formulaire Pour effacer tous les champs remplis et supprimer toutes informations, cliquez sur le bouton Effacer tous les champs sous l'onglet Formulaires de la barre d'outils supérieure. Il est possible d'effacer les champs remplis uniquement en mode de remplissage du formulaire. Naviguer, afficher et enregistrer un formulaire Passez à l'onglet Formulaires de la barre d'outils supérieure. Naviguez entre les champs du formulaire en utilisant les boutons Champ précédent et Champ suivant de la barre d'outil supérieure. Une fois terminé, cliquez sur le bouton Enregistrer sous oform de la barre d'outils supérieure pour enregistrer le formulaire au format OFORM prêt à remplir. Vous pouvez sauvegarder autant de formulaires au format OFORM que vous le souhaitez. Supprimer les champs du formulaire Pour supprimer un champ du formulaire et garder tous son contenu, sélectionnez-le et cliquez sur l'icône Supprimer (assurez-vous que le champ n'est pas verrouillé) ou appuyez sur la touche Supprimer du clavier."
    },
   {
        "id": "UsageInstructions/CreateLists.htm", 
        "title": "Créer des listes", 
        "body": "Pour créer une liste dans l'Éditeur de Documents, placez le curseur à la position où vous voulez commencer la liste (cela peut être une nouvelle ligne ou le texte existant), passez à l'onglet Accueil de la barre d'outils supérieure, sélectionnez le type de liste à créer : Liste non ordonnée avec des marqueurs est créée à l'aide de l'icône Puces de la barre d'outils supérieure Liste ordonnée avec numérotage spécial est créée à l'aide de l'icône Numérotation de la barre d'outils supérieure Cliquez sur la flèche vers le bas à côté de l'icône Puces ou Numérotation pour sélectionner le format de puces ou de numérotation souhaité. appuyez sur la touche Entrée à la fin de la ligne pour ajouter un nouvel élément à la liste. Pour terminer la liste, appuyez sur la touche Retour arrière et continuez le travail. L'éditeur commence automatiquement une liste numérotée lorsque vous tapez 1 et un point ou une parenthèse droite et un espace : 1., 1). La liste à puces commence automatiquement lorsque vous tapez - ou * et un espace. Vous pouvez aussi changer le retrait du texte dans les listes et leur imbrication en utilisant les icônes Liste multi-niveaux , Réduire le retrait et Augmenter le retrait sous l'onglet Accueil de la barre d'outils supérieure. Pour modifier le niveau de la liste, cliquez sur l'icône Numérotation , Puces , ou Liste multi-niveaux et choisissez Changer le niveau de liste, ou placer le curseur au début de la ligne et appuyez sur la touche Tab du clavier pour augmenter le niveau de la liste. Procédez au niveau de liste approprié. Vous pouvez configurez les paramètres supplémentaires du retrait et de l'espacement sur la barre latérale droite et dans la fenêtre de configuration de paramètres avancées. Pour en savoir plus, consultez les pages Modifier le retrait des paragraphes et Régler l'interligne du paragraphe . Joindre et séparer des listes Pour joindre une liste à la précédente : cliquez avec le bouton droit sur le premier élément de la seconde liste, utilisez l'option Joindre à la liste précédente du menu contextuel. Les listes seront jointes et la numérotation se poursuivra conformément à la numérotation de la première liste. Pour séparer une liste : cliquez avec le bouton droit de la souris sur l'élément de la liste où vous voulez commencer une nouvelle liste, sélectionnez l'option Séparer la liste du menu contextuel. La liste sera séparée et la numérotation dans la deuxième liste recommencera. Modifier la numérotation Poursuivre la numérotation séquentielle dans la deuxième liste selon la numérotation de la liste précédente : cliquez avec le bouton droit sur le premier élément de la seconde liste, sélectionnez l'option Continuer la numérotation du menu contextuel. La numérotation se poursuivra conformément à la numérotation de la première liste. Pour définir une certaine valeur initiale de numérotation : cliquez avec le bouton droit de la souris sur l'élément de la liste où vous souhaitez appliquer une nouvelle valeur de numérotation, sélectionnez l'option Définit la valeur de la numérotation du menu contextuel, dans une nouvelle fenêtre qui s'ouvre, définissez la valeur numérique voulue et cliquez sur le bouton OK. Configurer les paramètres de la liste Pour configurer les paramètres de la liste comme la puce/la numérotation, l'alignement, la taille et la couleur : cliquez sur un élément de la liste actuelle ou sélectionnez le texte à partir duquel vous souhaitez créer une liste, cliquez sur l'icône Puces ou Numérotation sous l'onglet Accueil dans la barre d'outils en haut, sélectionnez l'option Paramètres de la liste, la fenêtre Paramètres de la liste s'affiche. La fenêtre de paramètres de la liste à puces se présente sous cet aspect : La fenêtre de paramètres de la liste numérotée se présente sous cet aspect : Pour la liste à puces on peut choisir le caractère à utiliser comme puce et pour la liste numérotée on peut choisir le type de numérotation. Les options Alignement, Taille et Couleur sont identiques pour toutes les listes soit à puces soit numérotée. Puce permet de sélectionner le caractère approprié pour les éléments de la liste. Lorsque vous appuyez sur le champ Symboles et caractères, la fenêtre Symbole va apparaître dans laquelle vous pouvez choisir parmi les caractères disponibles. Veuillez consulter cet articlepour en savoir plus sur utilisation des symboles. Type permet de sélectionner la numérotation appropriée pour la liste. Les options suivantes sont disponibles : Rien, 1, 2, 3,..., a, b, c,..., A, B, C,..., i, ii, iii,..., I, II, III,.... Alignement permet de sélectionner le type d'alignement approprié pour aligner les puces/nombres horizontalement. Les options d'alignement disponibles : À gauche, Au centre, À droite. Taille permet d'ajuster la taille des puces/numéros. L'option par défaut est En tant que texte. Lorsque cette option est active, la taille des puces correspond à la taille du texte ; Ajustez la taille en utilisant les valeurs prédéfinies de 8 à 96. Couleur permet de choisir la couleur des puces/numéros. L'option par défaut est En tant que texte. Lorsque cette option est active, la couleur des puces ou des chiffres correspond à la couleur du texte. Choisissez l'option Automatique pour appliquer la couleur automatiquement, sélectionnez les Couleurs de thème ou les Couleurs standard de la palette ou définissez la Couleur personnalisée. Toutes les modifications sont affichées dans le champ Aperçu. Cliquez sur OK pour appliquer toutes les modifications et fermer la fenêtre. Pour configurer les paramètres de la liste à plusieurs niveaux, appuyez sur un élément de la liste, cliquez sur l'icône Liste multiniveaux sous l'onglet Accueil dans la barre d'outils en haut, sélectionnez l'option Paramètres de la liste, la fenêtre Paramètres de la liste s'affiche. La fenêtre Paramètres de la liste multiniveaux se présente sous cet aspect : Choisissez le niveau approprié dans la liste Niveau à gauche, puis personnalisez l'aspect des puces et des nombres pour le niveau choisi : Type permet de sélectionner la numérotation appropriée pour la liste numérotée ou le caractère approprié pour la liste à puces. Les options disponibles pour la liste numérotée : Rien, 1, 2, 3,..., a, b, c,..., A, B, C,..., i, ii, iii,..., I, II, III,.... Pour la liste à puces vous pouvez choisir un des symboles prédéfinis ou utiliser l'option Nouvelle puce. Lorsque vous appuyez sur cette option, la fenêtre Symbole va apparaître dans laquelle vous pouvez choisir parmi les caractères disponibles. Veuillez consulter cet articlepour en savoir plus sur utilisation des symboles. Alignement permet de sélectionner le type d'alignement approprié pour aligner les puces/nombres horizontalement du début du paragraphe. À gauche, Au centre, À droite. Les options d'alignement disponibles : À gauche, Au centre, À droite. Taille permet d'ajuster la taille des puces/numéros. L'option par défaut est En tant que texte. Ajustez la taille en utilisant les paramètres prédéfinis de 8 à 96. Couleur permet de choisir la couleur des puces/numéros. L'option par défaut est En tant que texte. Lorsque cette option est active, la couleur des puces ou des chiffres correspond à la couleur du texte. Choisissez l'option Automatique pour appliquer la couleur automatiquement, sélectionnez les Couleurs de thème ou les Couleurs standard de la palette ou définissez la Couleur personnalisée. Toutes les modifications sont affichées dans le champ Aperçu. Cliquez sur OK pour appliquer toutes les modifications et fermer la fenêtre."
    },
   {
        "id": "UsageInstructions/CreateTableOfContents.htm", 
        "title": "Comment faire une table des matières sur document Word", 
        "body": "Table des matières Une table des matières contient une liste de tous les chapitres (sections, etc.) d'un document et affiche les numéros des pages où chaque chapitre est démarré. Cela permet de naviguer facilement dans un document de plusieurs pages en passant rapidement à la partie voulue du texte. La table des matières est générée automatiquement sur la base des titres de document formatés à l'aide de styles prédéfinis. Cela facilite la mise à jour de la table des matières créée sans qu'il soit nécessaire de modifier les titres et de changer manuellement les numéros de page si le texte du document a été modifié. Structure des titres dans la table des matières Formater les titres Tout d'abord, formatez les titres dans votre document en utilisant l'un des styles prédéfinis dans l'Éditeur de Documents. Pour le faire, Sélectionnez le texte que vous souhaitez inclure dans la table des matières. Ouvrez le menu Style sur le côté droit de l'onglet Accueil dans la barre d'outils supérieure. Cliquez sur le style que vous souhaitez appliquer. Par défaut, vous pouvez utiliser les styles Titre 1 - Titre 9. Remarque : si vous souhaitez utiliser d'autres styles (par exemple Titre, Sous-titre, etc.) pour formater les titres qui seront inclus dans la table des matières, vous devrez d'abord ajuster les paramètres de la table des matières (voir la section correspondante ci-dessous). Pour en savoir plus sur les styles de mise en forme disponibles, vous pouvez vous référer à cette page. Pour ajouter rapidement un texte en tant que'en-tête, Sélectionnez le texte que vous souhaitez inclure dans la table des matières. Passez à l'onglet Référence dans la barre d'outils supérieure. Cliquez sur le bouton Ajouter du texte dans la barre d'outils supérieure. Choisissez le niveau de titre requis. Gérer les titres Une fois les titres formatés, vous pouvez cliquer sur l'icône Navigation dans la barre latérale de gauche pour ouvrir le panneau qui affiche la liste de tous les titres avec les niveaux d'imbrication correspondants. Ce panneau permet de naviguer facilement entre les titres dans le texte du document et de gérer la structure de titre. Cliquez avec le bouton droit sur un titre de la liste et utilisez l'une des options disponibles dans le menu : Promouvoir - pour déplacer le titre actuellement sélectionné vers le niveau supérieur de la structure hiérarchique, par ex. le changer de Titre 2 à Titre 1. Abaisser - pour déplacer le titre actuellement sélectionné vers le niveau inférieur de la structure hiérarchique, par ex. le changer de Titre 1 à. Titre 2. Nouvel en-tête avant - pour ajouter un nouveau titre vide du même niveau avant celui actuellement sélectionné. Nouvel en-tête après- pour ajouter un nouveau titre vide du même niveau après celui actuellement sélectionné. Nouveau sous-titre - pour ajouter un nouveau sous-titre vide (c'est-à-dire un titre de niveau inférieur) après le titre actuellement sélectionné. Lorsque le titre ou la sous-rubrique est ajouté, cliquez sur l'en-tête vide ajouté dans la liste et tapez votre propre texte. Cela peut être fait à la fois dans le texte du document et sur le panneau Navigation lui-même. Sélectionner le contenu - pour sélectionner le texte sous le titre actuel du document (y compris le texte relatif à tous les sous-titres de cette rubrique). Tout développer - pour développer tous les niveaux de titres dans le panneau Navigation. Tout réduire - pour réduire tous les niveaux de titres excepté le niveau 1 dans le panneau Titres. Développer au niveau - pour étendre la structure de titre au niveau sélectionné. Par exemple. Si vous sélectionnez le niveau 3, les niveaux 1, 2 et 3 seront développés, tandis que le niveau 4 et tous les niveaux inférieurs seront réduits. Pour développer ou réduire manuellement des niveaux de titre différents, utilisez les flèches situées à gauche des en-têtes. Pour fermer le panneau Navigation cliquez sur l'icône encore une fois. Insérer une table des matières dans le document Pour insérer une table des matières dans votre document : Positionnez le point d'insertion à l'endroit où vous souhaitez ajouter la table des matières. passez à l'onglet Références de la barre d'outils supérieure, cliquez sur l'icône Table des matières dans la barre d'outils supérieure ou cliquez sur la flèche en regard de cette icône et sélectionnez l'option voulue dans le menu. Vous pouvez sélectionner la table des matières qui affiche les titres, les numéros de page et les repères, ou les en-têtes uniquement. Remarque : l'apparence de la table des matières peut être ajustée ultérieurement via les paramètres de la table des matières. La table des matières sera insérée à la position actuelle du curseur. Pour modifier la position de la table des matières, vous pouvez sélectionner le champ de la table des matières (contrôle du contenu) et le faire simplement glisser vers l'emplacement souhaité. Pour ce faire, cliquez sur le bouton dans le coin supérieur gauche du champ Table des matières et faites-le glisser sans relâcher le bouton de la souris sur une autre position dans le texte du document. Pour naviguer entre les titres, appuyez sur la touche Ctrl et cliquez sur le titre souhaité dans le champ de la table des matières. Vous serez ramené à la page correspondante. Ajuster la table des matières créée Actualiser la table des matières Une fois la table des matières créée, vous pouvez continuer à modifier votre texte en ajoutant de nouveaux chapitres, en modifiant leur ordre, en supprimant certains paragraphes ou en développant le texte associé à un titre de manière à changer les numéros de page correspondants à la section considérée. Dans ce cas, utilisez l'option Actualiser pour appliquer automatiquement toutes les modifications à la table des matières. Cliquez sur la flèche en regard de l'icône Actualiser dans l'onglet Références de la barre d'outils supérieure et sélectionnez l'option voulue dans le menu : Actualiser la totalité de la table : pour ajouter les titres que vous avez ajoutés au document, supprimer ceux que vous avez supprimés du document, mettre à jour les titres modifiés (renommés) et les numéros de page. Actualiser uniquement les numéros de page pour mettre à jour les numéros de page sans appliquer de modifications aux titres. Vous pouvez également sélectionner la table des matières dans le texte du document et cliquer sur l'icône Actualiser en haut du champ Table des matières pour afficher les options mentionnées ci-dessus. Il est également possible de cliquer avec le bouton droit n'importe où dans la table des matières et d'utiliser les options correspondantes dans le menu contextuel. Ajuster les paramètres de la table des matières Pour ouvrir les paramètres de la table des matières, vous pouvez procéder de la manière suivante : Cliquez sur la flèche en regard de l'icône Table des matières dans la barre d'outils supérieure et sélectionnez l'option Paramètres dans le menu. Sélectionnez la table des matières dans le texte du document, cliquez sur la flèche à côté du titre du champ Table des matières et sélectionnez l'option Paramètres dans le menu. Cliquez avec le bouton droit n'importe où dans la table des matières et utilisez l'option Paramètres de table des matières dans le menu contextuel. Une nouvelle fenêtre s'ouvrira où vous pourrez ajuster les paramètres suivants : Afficher les numéros de page - cette option permet de choisir si vous souhaitez afficher les numéros de page ou non. Aligner à droite les numéros de page - cette option permet de choisir si vous souhaitez aligner les numéros de page sur le côté droit de la page ou non. Points de suite - cette option permet de choisir le type de points de suite que vous voulez utiliser. Un point de suite est une ligne de caractères (points ou traits d'union) qui remplit l'espace entre un titre et le numéro de page correspondant. Il est également possible de sélectionner l'option Aucun si vous ne souhaitez pas utiliser de points de suite. Mettre en forme la table des matières en tant que liens - cette option est cochée par défaut. Si vous le décochez, vous ne pourrez pas passer au chapitre souhaité en appuyant sur Ctrl et en cliquant sur le titre correspondant. Créer une table des matières à partir de - cette section permet de spécifier le nombre de niveaux hiérarchiques voulus ainsi que les styles par défaut qui seront utilisés pour créer la table des matières. Cochez le champ correspondant : Niveaux hiérarchiques - lorsque cette option est sélectionnée, vous pouvez ajuster le nombre de niveaux hiérarchiques utilisés dans la table des matières. Cliquez sur les flèches dans le champ Niveaux pour diminuer ou augmenter le nombre de niveaux (les valeurs de 1 à 9 sont disponibles). Par exemple, si vous sélectionnez la valeur 3, les en-têtes de niveau 4 à 9 ne seront pas inclus dans la table des matières. Styles sélectionnés - lorsque cette option est sélectionnée, vous pouvez spécifier des styles supplémentaires pouvant être utilisés pour créer la table des matières et affecter un niveau de plan correspondant à chacun d'eux. Spécifiez la valeur de niveau souhaitée dans le champ situé à droite du style. Une fois les paramètres enregistrés, vous pourrez utiliser ce style lors de la création de la table des matières. Styles - cette option permet de sélectionner l'apparence souhaitée de la table des matières. Sélectionnez l'option souhaitée dans la liste déroulante : Le champ d'aperçu ci-dessus affiche l'apparence de la table des matières. Les quatre styles par défaut suivants sont disponibles : Simple, Standard, Moderne, Classique. L'option Actuel est utilisée si vous personnalisez le style de la table des matières. Cliquez sur le bouton OK dans la fenêtre des paramètres pour appliquer les changements. Personnaliser le style de la table des matières Après avoir appliqué l'un des styles de table des matières par défaut dans la fenêtre des paramètres de la Table des matières, vous pouvez le modifier afin que le texte figurant dans le champ de la table des matières ressemble au résultat que vous recherchez. Sélectionnez le texte dans le champ de la table des matières, par ex. en appuyant sur le bouton dans le coin supérieur gauche du contrôle du contenu de la table des matières. Mettez en forme la table des matières en modifiant le type de police, la taille, la couleur ou en appliquant les styles de décoration de police. Mettez à jour les styles pour les éléments de chaque niveau. Pour mettre à jour le style, cliquez avec le bouton droit sur l'élément mis en forme, sélectionnez l'option Mise en forme du style dans le menu contextuel et cliquez sur l'option Mettre à jour le style TM N (le style TM 2 correspond aux éléments de niveau 2, le style correspond aux éléments de et ainsi de suite). Actualiser la table des matières. Supprimer la table des matières Pour supprimer la table des matières du document : cliquez sur la flèche en regard de l'icône Table des matières dans la barre d'outils supérieure et utilisez l'option Supprimer la table des matières, ou cliquez sur la flèche en regard du titre de contrôle du contenu de la table des matières et utilisez l'option Supprimer la table des matières."
    },
   {
        "id": "UsageInstructions/DecorationStyles.htm", 
        "title": "Appliquer les styles de police", 
        "body": "Dans l'Éditeur de Documents, vous pouvez appliquer différents styles de police à l'aide des icônes correspondantes situées dans l'onglet Accueil de la barre d'outils supérieure. Remarque : si vous voulez appliquer la mise en forme au texte déjà saisi, sélectionnez-le avec la souris ou en utilisant le clavier et appliquez la mise en forme. Gras Sert à mettre la police en gras pour lui donner plus de poids. Italique Sert à mettre la police en italique pour lui donner une certaine inclinaison à droite. Souligné Sert à souligner le texte avec la ligne qui passe sous les lettres. Barré Sert à barrer le texte par la ligne passant par les lettres. Exposant Sert à rendre le texte plus petit et le déplacer vers la partie supérieure de la ligne du texte, par exemple comme dans les fractions. Indice Sert à rendre le texte plus petit et le déplacer vers la partie inférieure de la ligne du texte, par exemple comme dans les formules chimiques. Pour accéder aux paramètres avancés de la police, cliquez avec le bouton droit de la souris et sélectionnez l'option Paramètres avancés du paragraphe du menu contextuel ou utilisez le lien Afficher les paramètres avancés sur la barre latérale droite. Dans la fenêtre Paragraphe - Paramètres avancés ouverte passez à l'onglet Police. Ici vous pouvez utiliser les styles de décoration de police et les paramètres suivants : Barré sert à barrer le texte par la ligne passant par les lettres. Barré double sert à barrer le texte par la ligne double passant par les lettres. Exposant sert à rendre le texte plus petit et le déplacer vers la partie supérieure de la ligne du texte, par exemple comme dans les fractions. Indice sert à rendre le texte plus petit et le déplacer vers la partie inférieure de la ligne du texte, par exemple comme dans les formules chimiques. Petites majuscules sert à mettre toutes les lettres en petite majuscule. Majuscules sert à mettre toutes les lettres en majuscule. Espacement sert à définir l'espace entre les caractères. Augmentez la valeur par défaut pour appliquer l'espacement Étendu, ou diminuez la valeur par défaut pour appliquer l'espacement Condensé. Utilisez les touches fléchées ou entrez la valeur voulue dans la case. Position permet de définir la position des caractères (décalage vertical) dans la ligne. Augmentez la valeur par défaut pour déplacer les caractères vers le haut ou diminuez la valeur par défaut pour les déplacer vers le bas. Utilisez les touches fléchées ou entrez la valeur voulue dans la case. Ligatures sont des lettres jointes d'un mot tapé dans l'une des polices OpenType. Veuillez noter que l'utilisation de ligatures peut perturber l'espacement des caractères. Les options de ligatures disponibles sont : Aucune Standards uniquement (inclut “fi”, “fl”, “ff” ; améliore la lisibilité) Contextuelles (ligatures sont appliquées en fonction des lettres environnantes ; améliore la lisibilité) Historiques (ligatures ont plus de descentes et lignes courbées ; réduit la lisibilité) Discrétionnaires (ligatures ornementales ; réduit la lisibilité) Standards et contextuelles Standards et historiques Contextuelles et historiques Standards et discrétionnaires Contextuelles et discrétionnaires Historiques et discrétionnaires Standards, contextuelles et historiques Standards, contextuelles et discrétionnaires Standards, historiques et discrétionnaires Contextuelles, historiques et discrétionnaires Toutes Tous les changements seront affichés dans le champ de prévisualisation ci-dessous."
    },
   {
        "id": "UsageInstructions/Drawio.htm", 
        "title": "Créer et insérer des diagrammes", 
        "body": "Lorsque vous avez besoin de créer beaucoup de diagrammes variés et complexes, l'Éditeur de Documents ONLYOFFICE vous offre un plug-in draw.io qui peut les créer et configurer. Sélectionnez l'endroit sur la page où vous souhaitez insérer un diagramme. Passez à l'onglet Modules complémentaires et cliquez sur draw.io. La fenêtre draw io s'ouvre avec les sections suivantes : Barre d'outils supérieure contient des outils pour gérer les fichiers, configurer l'interface, modifier des données en utilisant les onglets Fichier, Modifier, Afficher, Organiser, Extras, Aide et les options appropriées. Barre latérale gauche contient des formes variées pour sélectionner parmi : Standard, Logiciel , Réseau, Business, Autres. Pour ajouter de nouvelles formes à celles disponibles par défaut, cliquez sur le bouton Plus de formes, choisissez les types d'objet nécessaires et cliquez sur Appliquer. Barre latérale droite contient des outils et paramètres pour personnaliser la feuille de calcul, les formes, les graphiques, les feuilles, le texte et les flèches : Paramètres de la Feuille de calcul : Affichage : Grille, ses taille et couleur, Affichage de la page, Arrière-plan - vous pouvez sélectionner une image locale, fournir l'URL ou choisir une couleur appropriée à l'aide de la palette de couleurs, ainsi qu'ajouter des effets Ombre. Options : Flèches de connexion, Points de connexion, Guides. Taille de papier : orientation Portrait ou Paysage avec les paramètres de longueur et de largeur spécifiés. Paramètres de Forme : Couleur : Couleur remplissage, Gradient. Ligne : Couleur, Type, Largeur, Largeur du périmètre. Opacité. Paramètres de Flèche : Couleur : Couleur remplissage, Gradient. Ligne : Couleur, Type, Largeur, Fin de ligne, Début de ligne. Opacité. Zone de travail pour afficher des diagrammes, saisir et modifier des données. Ici vous pouvez déplacer des objets, former des diagrammes séquentiels et connecter des objets avec des flèches. Barre d'état contient des outils de navigation pour basculer facilement entre les feuilles et les gérer. En utilisant ces outils créez les diagrammes nécessaires et modifiez-les. Quand tout est prêt cliquez sur le bouton Insérer pour les ajouter au document."
    },
   {
        "id": "UsageInstructions/FillingOutForm.htm", 
        "title": "Remplir un formulaire", 
        "body": "Un formulaire à remplir est le fichier au format OFORM. OFORM est un format de fichier destiné à remplir ; à télécharger ou à imprimer des modèles de formulaires une fois que vous avez terminé de le remplir. Comment remplir un formulaire : Ouvrez le formulaire au format OFORM. Remplissez tous les champs obligatoires. La bordure des champs obligatoires est rouge. Utilisez ou Champ suivant dans la barre d'outils supérieure pour naviguer entre les champs ou cliquez sur le champ à remplir. Utilisez le bouton Effacer tous les champs pour vider tous les champs de saisie. Une fois tous les champs remplis, cliquez sur Enregistrer comme PDF pour sauvegarder le formulaire sur votre ordinateur en tant que fichier PDF. Cliquer sur dans le coin droit de la barre d'outils supérieure pour accéder aux options supplémentaires. Vous pouvez Imprimer, Télécharger en tant que docx ou Télécharger en tant que pdf. Il est même possible de modifier le thème d'interface en choisissant Identique à système, Claire, Classique claire, Sombre ou Contraste sombre. Une fois le thème d'interface Sombre ou Contraste sombre activé, l'option Mode sombre devient disponible. Zoom permet de mettre à l'échelle et de redimensionner la page en utilisant des options Ajuster à la page, Ajuster à la largeur et l'outil pour régler le niveau de Zoom : Ajuster à la page sert à redimensionner la page pour afficher une page entière sur l'écran. Ajuster à la largeur sert à redimensionner la page pour l'adapter à la largeur de l'écran. Outil Zoom sert à zoomer et dézoomer sur une page. Ouvrir l'emplacement de fichier lorsque vous avez besoin d'accéder le dossier où le fichier est stocké."
    },
   {
        "id": "UsageInstructions/FontTypeSizeColor.htm", 
        "title": "Définir le type de police, la taille et la couleur", 
        "body": "Dans l'Éditeur de Documents, vous pouvez sélectionner le type, la taille et la couleur de police à l'aide des icônes correspondantes situées dans l'onglet Accueil de la barre d'outils supérieure. Si vous voulez appliquer la mise en forme au texte déjà saisi, sélectionnez-le avec la souris ou en utilisant le clavier et appliquez la mise en forme appropriée. Vous pouvez aussi positionner le curseur de la souris sur le mot à mettre en forme. Nom de la police Sert à sélectionner l'une des polices disponibles dans la liste. Si une police requise n'est pas disponible dans la liste, vous pouvez la télécharger et l'installer sur votre système d'exploitation, après quoi la police sera disponible pour utilisation dans la version de bureau. Taille de la police Sert à sélectionner la taille de la police parmi les valeurs disponibles dans la liste déroulante, les valeurs par défaut sont : 8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 36, 48, 72 et 96). Il est également possible d'entrer manuellement une valeur personnalisée dans le champ de taille de police jusqu'à 300 pt. Appuyer sur la touche Entrée pour confirmer Augmenter la taille de la police Sert à modifier la taille de la police en la rendant plus grande à un point chaque fois que vous appuyez sur le bouton. Diminuer la taille de la police Sert à modifier la taille de la police en la rendant plus petite à un point chaque fois que vous appuyez sur le bouton. Modifier la casse Sert à modifier la casse du texte. Majuscule en début de phrase - la casse à correspondre la casse de la proposition ordinaire. minuscule - mettre en minuscule toutes les lettres. MAJUSCULES - mettre en majuscule toutes les lettres. Mettre en majuscule chaque mot - mettre en majuscule la première lettre de chaque mot. Inverser la casse - basculer entre d'affichages de la casse du texte ou le mot sur lequel le curseur de la souris est positionné. Couleur de surlignage Est utilisé pour marquer des phrases, des fragments, des mots ou même des caractères séparés en ajoutant une bande de couleur qui imite l'effet du surligneur sur le texte. Vous pouvez sélectionner la partie voulue du texte, puis cliquer sur la flèche vers le bas à côté de l'icône pour sélectionner une couleur dans la palette (cet ensemble de couleurs ne dépend pas du Jeux de couleurs sélectionné et comprend 16 couleurs). La couleur sera appliquée à la sélection. Alternativement, vous pouvez d'abord choisir une couleur de surbrillance et ensuite commencer à sélectionner le texte avec la souris - le pointeur de la souris ressemblera à ceci et vous serez en mesure de surligner plusieurs parties différentes de votre texte de manière séquentielle. Pour enlever la mise en surbrillance, cliquez à nouveau sur l'icône. Pour effacer la couleur de surbrillance, choisissez l'option Pas de remplissage. La Couleur de surlignage est différente de la Couleur de fond car cette dernière est appliquée au paragraphe entier et remplit complètement l'espace du paragraphe de la marge de page gauche à la marge de page droite. Couleur de police Sert à changer la couleur des lettres /caractères dans le texte. Par défaut, la couleur de police automatique est définie dans un nouveau document vide. Elle s'affiche comme la police noire sur l'arrière-plan blanc. Si vous choisissez le noir comme la couleur d'arrière-plan, la couleur de la police se change automatiquement à la couleur blanche pour que le texte soit visible. Pour choisir une autre couleur, cliquez sur la flèche vers le bas située à côté de l'icône et sélectionnez une couleur disponible dans les palettes (les couleurs de la palette Couleurs de thème dépend du jeu de couleurssélectionné). Après avoir modifié la couleur de police par défaut, vous pouvez utiliser l'option Automatique dans la fenêtre des palettes de couleurs pour restaurer rapidement la couleur automatique pour le fragment du texte sélectionné. Pour en savoir plus sur l'utilisation des palettes de couleurs, consultez cette page."
    },
   {
        "id": "UsageInstructions/FormattingPresets.htm", 
        "title": "Appliquer les styles de formatage", 
        "body": "Chaque style de mise en forme représente un ensemble des options de mise en forme : (taille de la police, couleur, interligne, alignment etc.). Dans l'Éditeur de Documents, les styles permettent de mettre en forme rapidement les parties différentes du texte (en-têtes, sous-titres, listes,texte normal, citations) au lieu d'appliquer les options de mise en forme différentes individuellement chaque fois que vous en avez besoin. Cela permet également d'assurer une apparence uniforme de tout le document. Un style n'est appliqué au'au paragraphe entier. Vous pouvez appliquer les styles pour créer une table des matières ou une table des figures. Le style à appliquer depend du fait si c'est le paragraphe (normal, pas d'espace, titres, paragraphe de liste etc.) ou le texte (d'aprèz type, taille et couleur de police) que vous souhaitez mettre en forme. Différents styles sont aussi appliqués quand vous sélectionnez une partie du texte ou seulement placez le curseur sur un mot. Parfois, il faut sélectionner le style approprié deux fois de la bibliothèque de styles pour le faire appliquer correctement : lorsque vous appuyer sur le style dans le panneau pour la première fois, le style de paragraphe est appliqué. Lorsque vous appuyez pour la deuxième fois, le style du texte est appliqué. Appliquer des styles par défault Pour appliquer un des styles de mise en forme disponibles, placez le curseur dans le paragraphe nécessaire, ou sélectionnez plusieurs paragraphes pour appliquer un des styles de mise en forme, sélectionnez le style nécessaire à partir de la galerie de styles située à droite de la barre d'outils supérieure. Les styles de mise en forme disponibles sont : normal, non-espacemen, titre 1-9, title, sous-titre, citation, citation intense, paragraphe de liste. Modifier des styles disponibles et créer de nouveaux Pour modifier le style existant : Appliquez le style nécessaire à un paragraphe. Sélectionnez le texte du paragraphe et modifiez tous les paramètres de mise en forme dont vous avez besoin. Enregistrez les modifications effectuées : cliquez avec le bouton droit de la souris sur le texte en cours de modification, sélectionnez l'option En tant que style et sélectionnez l'option Mettre à jour le style 'Nomdestyle' ('Nomdestyle' correspond au style appliqué à l'étape 1), ou sélectionnez le fragment du texte en cours de modification avec la souris, ouvrez le menu déroulant de la galerie des styles, cliquez avec le bouton droit de la souris sur le style à modifier et sélectionnez l'option Mettre à jour selon la sélection. Une fois que le style est modifié, tous les paragraphes dans le document qui a été mis en forme à l'aide de ce style vont changer leur apparence de manière correspondante. Pour créer un tout nouveau style : Mettez en forme un fragment du texte d'une manière nécessaire. Choisissez une façon appropriée de sauvegarder le style : cliquez avec le bouton droit de la souris sur le texte en cours de modification, sélectionnez l'option En tant que style et puis choisissez l'option Créer un nouveau style, ou sélectionnez le fragment du texte à l'aide de la souris, ouvrez la liste déroulante de la galerie de styles et cliquez sur l'option Nouveau style à partir du fragment sélectionné. Définissez les paramètres du nouveau style dans la fenêtre Créer un nouveau style qui s'ouvre : Spécifiez un nom du nouveau style en utilisant le champ correspondant. Chosissez le style nécessaire du paragraphe suivant en utilisant la liste Style du nouveau paragraphe. Cliquez sur le bouton OK. Le style créé sera ajouté à la galerie des styles. Gérez vos styles personnalisés : Pour restaurer les paramètres par défaut d'un style que vous avez modifié, cliquez avec le bouton droit de la souris sur le style que vous voulez restaurer et sélectionnez l'option Restaurer les paramètres par défaut. Pour restaurer les paramètres par défaut de tous les styles que vous avez modifiés, cliquez avec le bouton droit de la souris sur le style dans la galerie des styles et sélectionnez l'option Restaurer tous les styles par défaut. Pour supprimer un des styles que vous avez créé, cliquez avec le bouton droit de la souris sur le style à supprimer et sélectionnez l'option Supprimer le style. Pour supprimer tous les nouveaux style que vous avez crées, cliquez avec le bouton droit de la souris sur un des nouveaux styles crées et sélectionnez l'option Supprimer tous les styles personnalisés."
    },
   {
        "id": "UsageInstructions/HTML.htm", 
        "title": "Modifier le code HTML", 
        "body": "Si vous êtes en train d'écrire une page de site Web dans un éditeur de texte et souhaitez l'obtenir sous forme de code HTML, utilisez le plug-in HTML. Ouvrez l'onglet Modules complémentaires et cliquez sur Obtenir et coller du html. Sélectionnez le contenu nécessaire. Le code HTML du paragraphe sélectionné s'affiche dans le champ de module sur le panneau latéral gauche. Vous pouvez modifier le code pour modifier les caractéristiques du texte, par exemple la taille de la police ou la famille de la police etc. Cliquez sur Coller dans le document afin d'insérer le texte avec son code HTML modifié à la position actuelle du curseur dans votre document. Vous pouvez également ecrire votre propre code HTML (sans sélectionner le contenu du document) et ensuite le coller dans votre document. Pour plus d'informations sur le plug-in HTML et son installation, veuillez consulter la page de plug-in dans AppDirectory."
    },
   {
        "id": "UsageInstructions/HighlightedCode.htm", 
        "title": "Insérer le code en surbrillance", 
        "body": "Dans l'Éditeur de Documents, vous pouvez intégrer votre code mis en surbrillance auquel le style est déjà appliqué à correspondre avec le langage de programmation et le style de coloration dans le programme choisi. Accédez à votre document et placez le curseur à l'endroit où le code doit être inséré. Passez à l'onglet Modules complémentaires et choisissez Code en surbrillance. Spécifiez la Langue de programmation. Choisissez le Style du code pour qu'il apparaisse de manière à sembler celui dans le programme. Spécifiez si on va remplacer les tabulations par des espaces. Choisissez la Couleur de fond. Pour le faire manuellement, déplacez le curseur sur la palette de couleurs ou passez la valeur de type RBG/HSL/HEX. Cliquez sur OK pour insérer le code."
    },
   {
        "id": "UsageInstructions/InsertAutoshapes.htm", 
        "title": "Insérer les formes automatiques", 
        "body": "Insérer une forme automatique Pour insérer une forme automatique à votre document dans l'Éditeur de Documents, passez à l'onglet Insérer de la barre d'outils supérieure, cliquez sur l'icône Forme de la barre d'outils supérieure, sélectionnez l'un des groupes des formes automatiques disponibles dans la Galerie des formes : Récemment utilisé, Formes de base, Flèches figurées, Maths, Graphiques, Étoiles et rubans, Légendes, Boutons, Rectangles, Lignes, cliquez sur la forme automatique nécessaire du groupe sélectionné, placez le curseur de la souris là où vous voulez insérer la forme, après avoir ajouté la forme automatique vous pouvez modifier sa taille, sa position et ses propriétés. Remarque : pour ajouter une légende à la forme, assurez-vous que la forme est sélectionnée et commencez à taper le texte. Le texte que vous ajoutez fait partie de la forme (ainsi si vous déplacez ou faites pivoter la forme, le texte change de position lui aussi). Il est également possible d'ajouter une légende à la forme automatique. Pour en savoir plus sur le travail avec des légendes pour des formes automatiques, vous pouvez vous référer à cet article. Déplacer et redimensionner des formes automatiques Pour modifier la taille de la forme automatique, faites glisser les petits carreaux situés sur les bords de la forme. Pour garder les proportions de la forme automatique sélectionnée lors du redimensionnement, maintenez la touche Maj enfoncée et faites glisser l'une des icônes de coin. Lors de la modification des formes, par exemple des flèches figurées ou les légendes, l'icône jaune en forme de diamant est aussi disponible. Elle permet d'ajuster certains aspects de la forme, par exemple, la longueur de la pointe d'une flèche. Pour modifier la position de la forme automatique, utilisez l'icône qui apparaît si vous placez le curseur de votre souris sur la forme. Faites glisser la forme à la position nécessaire sans relâcher le bouton de la souris. Lorsque vous déplacez la forme automatique, des lignes de guidage s'affichent pour vous aider à positionner l'objet sur la page avec précision (si un style d'habillage autre que aligné est sélectionné). Pour déplacer la forme automatique de trois incréments, maintenez la touche Ctrl enfoncée et utilisez les flèches du clavier. Pour déplacer la forme automatique strictement horizontallement / verticallement et l'empêcher de se déplacer dans une direction perpendiculaire, maintenez la touche Shift enfoncée lors du déplacement. Pour faire pivoter la forme automatique, placez le curseur de la souris sur la poignée de rotation ronde et faites-la glisser vers la droite ou la gauche. Pour limiter la rotation de l'angle à des incréments de 15 degrés, maintenez la touche Maj enfoncée. Note : la liste des raccourcis clavier qui peuvent être utilisés lorsque vous travaillez avec des objets est disponible ici. Modifier les paramètres de la forme automatique Pour aligner et organiser les formes automatiques, utilisez le menu contextuel. Les options du menu sont les suivantes : Couper, Copier, Coller - les options nécessaires pour couper ou coller le texte / l'objet sélectionné et coller un passage de texte précedement coupé / copié ou un objet à la position actuelle du curseur. Imprimer la sélection sert à imprimer la partie sélectionnée du document. Accepter / Rejeter les modifications sert à accepter ou rejeter des modifications suivies dans un document partagé. Modifier les points sert à personnaliser ou modifier le contour d'une forme. Pour activer les points d'ancrage modifiables, faites un clic droit sur la forme et sélectionnez Modifier les points dans le menu. Les carrés noirs qui apparaissent sont les points de rencontre entre deux lignes et la ligne rouge trace le contour de la forme. Cliquez sur l'un de ces points et faites-le glisser pour repositionner et modifier le contour de la forme. Lorsque vous cliquez sur le point d'ancrage, deux lignes bleus avec des carrés blanches apparaissent. Ce sont les points de contrôle Bézier permettant de créer une courbe et de modifier la finesse de la courbe. Autant que les points d'ancrage sont actifs, vous pouvez les modifier et supprimer. Pour ajouter un point de contrôle à une forme, maintenez la touche Ctrl enfoncée et cliquez sur l'emplacement du point de contrôle souhaité. Pour supprimer un point, maintenez la touche, Ctrl enfoncée et cliquez sur le point superflu. Organiser sert à placer la forme automatique choisie au premier plan, envoyer à fond, avancer ou reculer ainsi que grouper ou dégrouper les formes pour effectuer des opérations avec plusieurs formes à la fois. Pour en savoir plus sur l'organisation des objets, vous pouvez vous référer à cette page. Aligner sert à aligner la forme à gauche, au centre, à droite, en haut, au milieu, en bas. Pour en savoir plus sur l'alignement des objets, vous pouvez vous référer à cette page. Style d'habillage sert à sélectionner un des styles d'habillage - aligné sur le texte, carré, rapproché, au travers, haut et bas, devant le texte, derrière le texte - ou modifier le contour de l'habillage. L'option Modifier les limites du renvoi à la ligne n'est disponible qu'au cas où vous sélectionnez le style d'habillage autre que 'aligné sur le texte'. Faites glisser les points d'habillage pour personnaliser les limites. Pour créer un nouveau point d'habillage, cliquez sur la ligne rouge et faites-la glisser vers la position désirée. Rotation permet de faire pivoter la forme de 90 degrés dans le sens des aiguilles d'une montre ou dans le sens inverse des aiguilles d'une montre, ainsi que de retourner la forme horizontalement ou verticalement. Paramètres avancés sert à ouvrir la fenêtre 'Forme - Paramètres avancés'. Certains paramètres de la forme automatique peuvent être modifiés en utilisant l'onglet Paramètres de la forme de la barre latérale droite. Pour l'activer, sélectionnez la forme ajoutée avec la souris et sélectionnez l'icône Paramètres de la forme à droite. Vous pouvez y modifier les paramètres suivants : Remplissage - utilisez cette section pour sélectionner le remplissage de la forme automatique. Les options disponibles sont les suivantes : Couleur de remplissage - sélectionnez cette option pour spécifier la couleur unie à remplir l'espace intérieur de la forme automatique sélectionnée. Cliquez sur la case de couleur et sélectionnez la couleur voulue à partir du jeux de couleurs disponible ou spécifiez n'importe quelle couleur de votre choix : Remplissage en dégradé - sélectionnez cette option pour remplir une forme avec deux ou plusieurs couleurs et créer une transition douce entre elles. Particulariser le remplissage en dégrédé sans limites. Cliquez sur l'icône Paramètres de la forme pour ouvrir le menu de Remplissage de la barre latérale droite : Les options de menu disponibles : Style - choisissez une des options disponibles : Linéaire ou Radial : Linéaire sert à remplir par un dégradé de gauche à droite, de bas en haut ou sous l'angle partant en direction définie. La fenêtre d'aperçu Direction affiche la couleur de dégradé sélectionnée, cliquez sur la flèche pour définir la direction du dégradé. Utilisez les paramètres Angle pour définir un angle précis du dégradé. Radial - la transition se fait autour d'un point, les couleurs se fondent progressivement du centre aux bords en formant un cercle. Point de dégradé est un points spécifique dans lequel se fait la transition dégradé de couleurs. Utiliser le bouton Ajouter un point de dégradé dans la barre de défilement pour ajouter un dégradé. Le nombre maximal de points est 10. Chaque dégradé suivant n'affecte pas l'apparence du remplissage en dégradé actuel. Utilisez le bouton Supprimer le point de dégradé pour supprimer un certain dégradé. Ajustez la position du dégradé en glissant l'arrêt dans la barre de défilement ou utiliser le pourcentage de Position pour préciser l'emplacement du point. Pour appliquer une couleur à un point de dégradé, cliquez sur un point dans la barre de défilement, puis cliquez sur Couleur pour sélectionner la couleur appropriée. Image ou texture - sélectionnez cette option pour utiliser une image ou une texture prédéfinie en tant que arrière-plan de la forme. Si vous souhaitez utiliser une image en tant que l'arrière-plan de la forme, vous pouvez ajouter une image D'un fichier en la sélectionnant sur le disque dur de votre ordinateur ou D'une URL en insérant l'adresse URL appropriée dans la fenêtre ouverte ou À partir de l'espace de stockage en sélectionnant l'image enregistrée sur vortre portail. Si vous souhaitez utiliser une texture en tant que arrière-plan de la forme, utilisez le menu déroulant D'une texture et sélectionnez le préréglage de la texture nécessaire. Actuellement, les textures suivantes sont disponibles : Toile, Carton, Tissu foncé, Grain, Granit, Papier gris, Tricot, Cuir, Papier brun, Papyrus, Bois. Si l'Image sélectionnée est plus grande ou plus petite que la forme automatique, vous pouvez profiter d'une des options : Étirement ou Mosaïque depuis la liste déroulante. L'option Étirement permet de régler la taille de l'image pour l'adapter à la taille de la forme automatique afin qu'elle puisse remplir tout l'espace uniformément. L'option Mosaïque permet d'afficher seulement une partie de l'image plus grande en gardant ses dimensions d'origine, ou de répéter l'image plus petite en conservant ses dimensions initiales sur la surface de la forme automatique afin qu'elle puisse remplir tout l'espace uniformément. Remarque : tout préréglage Texture sélectionné remplit l'espace de façon uniforme, mais vous pouvez toujours appliquer l'effet Étirement, si nécessaire. Modèle - sélectionnez cette option pour sélectionner le modèle à deux couleurs composé des éléments répétés. Modèle - sélectionnez un des modèles prédéfinis du menu. Couleur de premier plan - cliquez sur cette palette de couleurs pour changer la couleur des éléments du modèle. Couleur d'arrière-plan - cliquez sur cette palette de couleurs pour changer de l'arrière-plan du modèle. Pas de remplissage - sélectionnez cette option si vous ne voulez pas utiliser un remplissage. Opacité - utilisez cette section pour régler le niveau d'Opacité des formes automatiques en faisant glisser le curseur ou en saisissant la valeur de pourcentage à la main. La valeur par défaut est 100%. Elle correspond à l'opacité complète. La valeur 0% correspond à la transparence totale. Ligne - utilisez cette section pour changer la largeur et la couleur du ligne de la forme automatique. Pour modifier la largeur du ligne, sélectionnez une des options disponibles depuis la liste déroulante Taille. Les options disponibles sont les suivantes : 0,5 pt, 1 pt, 1,5 pt, 2,25 pt, 3 pt, 4,5 pt, 6 pt ou Pas de ligne si vous ne voulez pas utiliser de ligne. Pour changer la couleur du contour, cliquez sur la case colorée et sélectionnez la couleur voulue. Pour modifier le type de contour, sélectionnez l'option voulue dans la liste déroulante correspondante (une ligne continue est appliquée par défaut, vous pouvez la remplacer par l'une des lignes pointillées disponibles). Rotation permet de faire pivoter la forme de 90 degrés dans le sens des aiguilles d'une montre ou dans le sens inverse des aiguilles d'une montre, ainsi que de retourner la forme horizontalement ou verticalement. Cliquez sur l'un des boutons : pour faire pivoter la forme de 90 degrés dans le sens inverse des aiguilles d'une montre pour faire pivoter la forme de 90 degrés dans le sens des aiguilles d'une montre pour retourner la forme horizontalement (de gauche à droite) pour retourner la forme verticalement (à l'envers) Style d'habillage - utilisez cette section pour sélectionner un des styles d'habillage - aligné sur le texte, carré, rapproché, au travers, haut et bas, devant le texte, derrière le texte (pour en savoir plus, consultez la section des paramètres avancés ci-dessous). Modifier la forme - utilisez cette section pour remplacer la forme automatique insérée par une autre sélectionnée de la liste déroulante. Ajouter une ombre - cochez cette case pour affichage de la forme ombré. Adjuster les paramètres avancés d'une forme automatique Pour changer les paramètres avancés de la forme automatique, cliquez sur la forme avec le bouton droit et sélectionnez l'option Paramètres avancés dans le menu ou utilisez le lien Afficher paramètres avancés sur la barre latérale droite. La fenêtre \"Forme - Paramètres avancés\" s'ouvre : L'onglet Taille comporte les paramètres suivants : Largeur - utilisez l'une de ces options pour modifier la largeur de la forme automatique. Absolue - spécifiez une valeur exacte mesurée en unités absolues, c.-à-d. Centimètres/Points/Pouces (selon l'option spécifiée dans l'onglet Fichier -> Paramètres avancés...). Relatif - spécifiez un pourcentage relatif à la largeur de la marge de gauche, la marge (c'est-à-dire la distance entre les marges gauche et droite), la largeur de la page ou la largeur de la marge de droite. Hauteur - utilisez l'une de ces options pour modifier la hauteur de la forme automatique. Absolue - spécifiez une valeur exacte mesurée en unités absolues, c'est-à-dire Centimètres/Points/Pouces (selon l'option spécifiée dans l'onglet Fichier -> Paramètres avancés...). Relatif - spécifiez un pourcentage relatif à la marge (c'est-à-dire la distance entre les marges supérieure et inférieure), la hauteur de la marge inférieure, la hauteur de la page ou la hauteur de la marge supérieure. Si l'option Verrouiller le ratio d'aspect est cochée, la largeur et la hauteur seront modifiées en conservant le ratio d'aspect original. L'onglet Rotation comporte les paramètres suivants : Angle - utilisez cette option pour faire pivoter la forme d'un angle exactement spécifié. Entrez la valeur souhaitée mesurée en degrés dans le champ ou réglez-la à l'aide des flèches situées à droite. Retourné - cochez la case Horizontalement pour retourner la forme horizontalement (de gauche à droite) ou la case Verticalement pour retourner la forme verticalement (à l'envers). L'onglet Habillage du texte contient les paramètres suivants : Style d'habillage - utilisez cette option pour changer la façon dont la forme est positionnée par rapport au texte : elle peut faire partie du texte (si vous sélectionnez le style 'aligné sur le texte') ou être contournée par le texte de tous les côtés (si vous sélectionnez l'un des autres styles). En ligne sur le texte - la forme fait partie du texte, comme un caractère, ainsi si le texte est déplacé, la forme est déplacée elle aussi. Dans ce cas-là les options de position ne sont pas accessibles. Si vous sélectionnez un des styles suivants, vous pouvez déplacer l'image indépendamment du texte et définir sa position exacte : Carré - le texte est ajusté autour des bords de la forme. Rapproché - le texte est ajusté sur le contour de la forme. Au travers - le texte est ajusté autour des bords de la forme et occupe l'espace vide à l'intérieur d'elle. Pour créer l'effet, utilisez l'option Modifier les limites du renvoi à la ligne du menu contextuel. Haut et bas - le texte est ajusté en haut et en bas de la forme. Devant le texte - la forme est affichée sur le texte. Derrière le texte - le texte est affiché sur la forme. Si vous avez choisi le style carré, rapproché, au travers, haut et bas, vous avez la possibilité de configurer des paramètres supplémentaires - Distance du texte de tous les côtés (haut, bas, droit, gauche). L'onglet Position n'est disponible qu'au cas où vous choisissez le style d'habillage autre que 'aligné sur le texte'. Il contient les paramètres suivants qui varient selon le type d'habillage sélectionné : La section Horizontal vous permet de sélectionner l'un des trois types de positionnement de forme automatique suivants : Alignement (gauche, centre, droite) par rapport au caractère, à la colonne, à la marge de gauche, à la marge, à la page ou à la marge de droite, Position absolue mesurée en unités absolues, c'est-à-dire Centimètres/Points/Pouces (selon l'option spécifiée dans l'onglet Fichier -> Paramètres avancés...) à droite du caractère, de la colonne, de la marge de gauche, de la marge, de la page ou de la marge de droite, Position relative mesurée en pourcentage par rapport à la marge gauche, à la marge, à la page ou à la marge de droite. La section Vertical vous permet de sélectionner l'un des trois types de positionnement de forme automatique suivants : Alignement (haut, centre, bas) par rapport à la ligne, à la marge, à la marge inférieure, au paragraphe, à la page ou à la marge supérieure, Position absolue mesurée en unités absolues, c.-à-d. Centimètres/Points/Pouces (selon l'option spécifiée dans l'onglet Fichier -> Paramètres avancés...) sous la ligne, la marge, la marge inférieure, le paragraphe, la page la marge supérieure, Position relative mesurée en pourcentage par rapport à la marge, à la marge inférieure, à la page ou à la marge supérieure. Déplacer avec le texte détermine si la forme automatique se déplace en même temps que le texte sur lequel elle est alignée. Chevauchement détermine si deux formes automatiques sont fusionnées en une seule ou se chevauchent si vous les faites glisser les unes près des autres sur la page. L'onglet Poids et flèches contient les paramètres suivants : Style de ligne - ce groupe d'options vous permet de spécifier les paramètres suivants : Type de litterine - cette option permet de définir le style de la fin de la ligne, ainsi elle peut être appliquée seulement aux formes avec un contour ouvert telles que des lignes, des polylignes etc. : Plat - les points finaux seront plats. Arrondi - les points finaux seront arrondis. Carré - les points finaux seront carrés. Type de jointure - cette option permet de définir le style de l'intersection de deux lignes, par exemple, une polyligne, les coins du triangle ou le contour du rectangle : Arrondi - le coin sera arrondi. Plaque - le coin sera coupé d'une manière angulaire. Onglet - l'angle sera aiguisé. Bien adapté pour les formes à angles vifs. Remarque : l'effet sera plus visible si vous utilisez un contour plus épais. Flèches - ce groupe d'options est disponible pour les formes du groupe Lignes. Il permet de définir le Style de début et Style de fin aussi bien que la Taille des flèches en sélectionnant l'option appropriée de la liste déroulante. La section Rembourrage texte vous permet de changer les marges internes En haut, En bas, A gauche et A droite (c'est-à-dire la distance entre le texte à l'intérieur de la forme et les bordures de la forme automatique). Remarque : cet onglet n'est disponible que si tu texte est ajouté dans la forme automatique, sinon l'onglet est désactivé. L'onglet Texte de remplacement permet de spécifier un Titre et une Description qui sera lue aux personnes avec des déficiences cognitives ou visuelles pour les aider à mieux comprendre l'information du tableau."
    },
   {
        "id": "UsageInstructions/InsertBookmarks.htm", 
        "title": "Ajouter des signets", 
        "body": "Les signets permettent d’aller rapidement à une certaine position dans le document en cours ou d'ajouter un lien vers cette position dans le document. Pour ajouter un signet dans l'Éditeur de Documents : spécifiez l'endroit où vous voulez que le signet soit ajouté : placez le curseur de la souris au début du passage de texte nécessaire, ou sélectionnez le passage de texte nécessaire, passez à l'onglet Références de la barre d'outils supérieure, cliquez sur l'icône Signet de la barre d'outils supérieure, dans la fenêtre Signets qui s'ouvre, entrez le Nom du signet et cliquez sur le bouton Ajouter - un signet sera ajouté à la liste des signets affichée ci-dessous, Note : le nom du signets doit commencer par une lettre, mais il peut aussi contenir des chiffres. Le nom du signets ne peut pas contenir d'espaces, mais peut inclure le caractère de soulignement \"_\". Pour accéder à un des signets ajoutés dans le texte du document : cliquez sur l'icône Signet dans l'onglet Références de la barre d'outils supérieure, dans la fenêtre Signets qui s'ouvre, sélectionnez le signet vers lequel vous voulez vous déplacer. Pour trouver facilement le signet voulu dans la liste, vous pouvez trier la liste par Nom ou par Emplacement de signets dans le texte du document, cochez l'option Marque-pages cachés pour afficher les signets cachés dans la liste (c'est-à-dire les signets automatiquement créés par le programme lors de l'ajout de références à une certaine partie du document. Par exemple, si vous créez un lien hypertexte vers un certain titre dans le document, l'éditeur de document crée automatiquement un signet caché vers la cible de ce lien). cliquez sur le bouton Aller à - le curseur sera positionné à l'endroit du document où le signet sélectionné a été ajouté, ou le passage de texte correspondant sera sélectionné, cliquez sur le bouton Obtenir le lien - une nouvelle fenêtre va apparaître dans laquelle vous pouvez appuyer sur Copier pour copier le lien vers le fichier spécifiant la position du signet référencé. Lorsque vous collez le lien dans la barre d'adresse de votre navigateur et appuyez sur la touche Entrée, le document s'ouvre à l'endroit où le signet est ajouté. Remarque : si vous voulez partager un lien avec d'autres personnes, vous devez définir les autorisations d’accès en utilisant l'option Partage soul l'onglet Collaboration. cliquez sur le bouton Fermer pour fermer la fenêtre. Pour supprimer un signet, sélectionnez-le dans la liste et cliquez sur le bouton Supprimer. Pour savoir comment utiliser les signets lors de la création de liens, veuillez consulter la section Ajouter des liens hypertextes."
    },
   {
        "id": "UsageInstructions/InsertCharts.htm", 
        "title": "Insérer des graphiques", 
        "body": "Insérer un graphique Pour insérer un graphique dans Document Editor, Placez le curseur à l'endroit où vous voulez insérer un graphique. Passez à l'onglet Insertion de la barre d'outils supérieure. Cliquez sur l'icône Graphique de la barre d'outils supérieure. Sélectionnez le type de graphique approprié: Graphique à colonnes Histogramme groupé Histogramme empilé Histogramme empilé 100 % Histogramme groupé en 3D Histogramme empilé en 3D Histogramme empilé 100 % en 3D Histogrammes en 3D Graphiques en ligne Ligne Lignes empilées Lignes empilées 100 % Lignes avec marques de données Lignes empilées avec marques de données Lignes empilées 100 % avec des marques de données Lignes 3D Graphiques en secteurs Secteurs Donut Camembert 3D Graphiques à barres Barres groupées Barres empilées Barres empilées 100 % Barres groupées en 3D Barres empilées en 3D Barres empilées 100 % en 3D Graphiques en aires Aires Aires empilées Aires empilées 100 % Graphiques boursiers Nuage de points (XY) Disperser Barres empilées Disperser avec lignes lissées et marqueurs Disperser avec lignes lissées Disperser avec des lignes droites et marqueurs Disperser avec des lignes droites Graphiques Combo Histogramme groupé - lignes Histogramme groupé - ligne sur un axe secondaire Aires empilées - histogramme groupé Combinaison personnalisée Remarque: ONLYOFFICE Document Editor prend en charge la modification des graphiques créés dans d'autres applications tels que: Graphiques en pyramides, à barres (pyramides), horizontal/vertical à cylindre, horizontal/vertical à cônes. Vous pouvez ouvrir le fichier comportant tels graphiques et les modifier en utilisant les outils disponibles. Lorsque la fenêtre Éditeur du graphique s'affiche, vous pouvez saisir les données en utilisant des boutons suivants: et pour copier et coller des données et pour annuler et rétablir une action pour insérer une fonction et pour réduire et ajouter une décimale modifier le format de nombre, c'est à dire l'apparence d'un nombre saisi pour modifier le type de graphique. Cliquez sur le bouton Sélection de données dans la fenêtre Éditeur du graphique. La fenêtre Données du graphique s'affiche. Utiliser la boîte de dialogue Données du graphique pour gérer la Plage de données du graphique, la Série de la légende, le Nom de l'axe horizontal, et Changer de ligne ou de colonne. Plage de données du graphique - sélectionnez les données pour votre graphique. Cliquez sur l'icône à droite de la boîte Plage de données du graphique pour sélectionner la plage de données. Série de la légende - ajouter, modifier ou supprimer les entrées de légende. Tapez ou sélectionnez le nom de série des entrées de légende. Dans la Série de la légende, cliquez sur le bouton Ajouter. Dans Modifier la série, saisissez une nouvelle entrée de légende ou cliquez sur à droite de la zone Sélectionner un nom. Nom de l'axe horizontal - modifier le texte de l'étiquette de l'axe Dans la fenêtre Nom de l'axe horizontal cliquez sur Modifier. Dans Plage de données de l'étiquette de l'axe, saisissez les étiquettes à ajouter ou cliquez sur l'icône à droite de la zone Plage de données de l'étiquette de l'axe pour sélectionner la plage de données. Changer de ligne ou de colonne - modifier le façon de traçage des données dans la feuille de calcul. Changer de ligne ou de colonne pour afficher des données sur un autre axe. Cliquez sur OK pour appliquer toutes les modifications et fermer la fenêtre. Cliquez sur le bouton Modifier le type de graphique dans la fenêtre Éditeur du graphique pour choisir le type et le style du graphique. Sélectionnez le graphique approprié dans des sections disponibles: Colonne, Graphique en ligne, Graphique à secteurs, En barres, En aires, Nuages de points (XY), Boursier. Lorsque vous choisissez Graphiques Combo, la fenêtre Type de graphique représente les séries du graphiques et permet de choisir les types de graphiques à combiner et de sélectionner la série de données à placer sur l'axe secondaire. Paramétrer le graphique en cliquant sur Modifier le graphique dans la fenêtre Éditeur du graphique. La fenêtre Graphique - Paramètres avancés s'affiche. L'onglet Disposition vous permet de modifier la disposition des éléments de graphique. Spécifiez la position du Titre du graphique sur votre graphique en sélectionnant l'option voulue dans la liste déroulante: Rien pour ne pas afficher le titre du graphique Superposition pour superposer et centrer le titre sur la zone de tracé, Sans superposition pour afficher le titre au-dessus de la zone de tracé. Spécifiez la position de la Légende sur votre graphique en sélectionnant l'option voulue dans la liste déroulante: Aucun pour ne pas afficher de légende, Bas pour afficher la légende et l'aligner au bas de la zone de tracé, En haut pour afficher la légende et l'aligner en haut de la zone de tracé, À droite pour afficher la légende et l'aligner à droite de la zone de tracé, À gauche pour afficher la légende et l'aligner à gauche de la zone de tracé, Superposition à gauche pour superposer et centrer la légende à gauche de la zone de tracé, Superposition à droite pour superposer et centrer la légende à droite de la zone de tracé. Spécifiez les paramètres des Étiquettes de données (c'est-à-dire les étiquettes de texte représentant les valeurs exactes des points de données): Définissez la position des Étiquettes de données par rapport aux points de données en sélectionnant l'option nécessaire dans la liste déroulante. Les options disponibles varient en fonction du type de graphique sélectionné. Pour les graphiques en Colonnes/Barres, vous pouvez choisir les options suivantes: Rien, Au centre, En haut à l'intérieur, En haut à l'intérieur, En haut à l'extérieur. Pour les graphiques en Ligne/ Nuage de points (XY)/Boursier, vous pouvez choisir les options suivantes: Aucun, Centre, À gauche, À droite, En haut. Pour les graphiques Secteur, vous pouvez choisir les options suivantes: Rien, Au centre</b>, Ajuster à la largeur, En haut à l'intérieur, En haut à l'extérieur. Pour les graphiques en Aire ainsi que pour les graphiques 3D en Colonnes, Ligne, Barres et Combo vous pouvez choisir les options suivantes: Rien, Au centre. Sélectionnez les données que vous souhaitez inclure dans vos étiquettes en cochant les cases correspondantes: Nom de la série, Nom de la catégorie, Valeur, Saisissez un caractère (virgule, point-virgule, etc.) que vous souhaitez utiliser pour séparer plusieurs étiquettes dans le champ de saisie Séparateur d'étiquettes de données. Lignes - permet de choisir un style de ligne pour les graphiques en Ligne/Nuage de points (XY). Vous pouvez choisir parmi les options suivantes: Droit pour utiliser des lignes droites entre les points de données, Lisse pour utiliser des courbes lisses entre les points de données, ou Rien pour ne pas afficher les lignes. Marqueurs - est utilisé pour spécifier si les marqueurs doivent être affichés (si la case est cochée) ou non (si la case n'est pas cochée) pour les graphiques Ligne/Nuage de points (XY). Remarque: les options Lignes et Marqueurs sont disponibles uniquement pour les graphiques en Ligne et Ligne/Nuage de points (XY). L'onglet Axe vertical vous permet de modifier les paramètres de l'axe vertical, également appelés axe des valeurs ou axe y, qui affiche des valeurs numériques. Notez que l'axe vertical sera l'axe des catégories qui affiche des étiquettes de texte pour les Graphiques à barres. Dans ce cas, les options de l'onglet Axe vertical correspondront à celles décrites dans la section suivante. Pour les Graphiques Nuage de points (XY), les deux axes sont des axes de valeur. Remarque: les sections Paramètres des axes et Quadrillage seront désactivées pour les Graphiques à secteurs, car les graphiques de ce type n'ont ni axes ni lignes de quadrillage. Sélectionnez Masquer pour masquer l'axe vertical du graphique, laissez cette option décochée pour afficher l'axe. Définissez l'orientation du Titre en choisissant l'option appropriée de la liste déroulante: Rien pour ne pas afficher le titre de l'axe vertical Incliné pour afficher le titre de bas en haut à gauche de l'axe vertical, Horizontal pour afficher le titre horizontalement à gauche de l'axe vertical. Valeur minimale sert à définir la valeur la plus basse à afficher au début de l'axe vertical. L'option Auto est sélectionnée par défaut, dans ce cas la valeur minimale est calculée automatiquement en fonction de la plage de données sélectionnée. Vous pouvez sélectionner l'option Fixé dans la liste déroulante et spécifier une valeur différente dans le champ de saisie sur la droite. Valeur maximale sert à définir la valeur la plus élevée à afficher à la fin de l'axe vertical. L'option Auto est sélectionnée par défaut, dans ce cas la valeur maximale est calculée automatiquement en fonction de la plage de données sélectionnée. Vous pouvez sélectionner l'option Fixé dans la liste déroulante et spécifier une valeur différente dans le champ de saisie sur la droite. Axes croisés - est utilisé pour spécifier un point sur l'axe vertical où l'axe horizontal doit le traverser. L'option Auto est sélectionnée par défaut, dans ce cas la valeur du point d'intersection est calculée automatiquement en fonction de la plage de données sélectionnée. Vous pouvez sélectionner l'option Valeur dans la liste déroulante et spécifier une valeur différente dans le champ de saisie à droite, ou définir le point d'intersection des axes à la Valeur minimum/maximum sur l'axe vertical. Unités d'affichage - est utilisé pour déterminer la représentation des valeurs numériques le long de l'axe vertical. Cette option peut être utile si vous travaillez avec de grands nombres et souhaitez que les valeurs sur l'axe soient affichées de manière plus compacte et plus lisible (par exemple, vous pouvez représenter 50 000 comme 50 en utilisant les unités d'affichage de Milliers). Sélectionnez les unités souhaitées dans la liste déroulante: Centaines, Milliers, 10 000, 100 000, Millions, 10 000 000, 100 000 000, Milliards, Billions, ou choisissez l'option Rien pour retourner aux unités par défaut. Valeurs dans l'ordre inverse - est utilisé pour afficher les valeurs dans la direction opposée. Lorsque la case n'est pas cochée, la valeur la plus basse est en bas et la valeur la plus haute est en haut de l'axe. Lorsque la case est cochée, les valeurs sont triées de haut en bas. La section Options de graduations permet d'ajuster l'apparence des graduations sur l'échelle verticale. Les graduations du type principal sont les divisions à plus grande échelle qui peuvent avoir des étiquettes affichant des valeurs numériques. Les graduations du type secondaire sont les subdivisions d'échelle qui sont placées entre les graduations principales et n'ont pas d'étiquettes. Les graduations définissent également l'endroit où le quadrillage peut être affiché, si l'option correspondante est définie dans l'onglet Disposition. Les listes déroulantes Type principal/secondaire contiennent les options de placement suivantes: Rien pour ne pas afficher les graduations principales/secondaires, Sur l'axe pour afficher les graduations principales/secondaires des deux côtés de l'axe, Dans pour afficher les graduations principales/secondaires dans l'axe, A l'extérieur pour afficher les graduations principales/secondaires à l'extérieur de l'axe. La section Options d'étiquettes permet d'ajuster l'apparence des étiquettes de graduations du type principal qui affichent des valeurs. Pour spécifier la Position de l'étiquette par rapport à l'axe vertical, sélectionnez l'option voulue dans la liste déroulante: Rien pour ne pas afficher les étiquettes de graduations, En bas pour afficher les étiquettes de graduations à gauche de la zone de tracé, En haut pour afficher les étiquettes de graduations à droite de la zone de tracé, À côté de l'axe pour afficher les étiquettes de graduations à côté de l'axe. Pour définir le Format d'étiquette cliquez sur le bouton format d'étiquette et choisissez la catégorie appropriée. Les catégories du format d'étiquette disponibles: Général Nombre Scientifique Comptabilité Monétaire Date Heure Pourcentage Fraction Texte Personnalisé Les options du format d'étiquette varient en fonction de la catégorie sélectionné. Pour en savoir plus sur la modification du format de nombre, veuillez consulter cette page. Activez Lié à la source pour conserver la représentation de nombre de la source de données du graphique. Remarque: Les axes secondaires sont disponibles sur les graphiques Combo uniquement. Axes secondaires sont utiles pour des graphiques Combo lorsque les nombres varient considérablement, ou lorsque des types de données mixtes sont utilisés pour créer un graphique. Avec des axes secondaires on peut lire et comprendre un graphique combiné plus facilement. L'onglet Second axe vertical/horizontal s'affiche lorsque vous choisissez une série de données appropriée pour votre graphique combiné. Les options et les paramètres disponibles sous l'onglet Second axe vertical/horizontal sont les mêmes que ceux sous l'onglet Axe vertical/horizontal. Pour une description détaillée des options disponibles sous l'onglet Axe vertical/horizontal, veuillez consulter les sections appropriées ci-dessus/ci-dessous. L'onglet Axe horizontal vous permet de modifier les paramètres de l'axe horizontal, également appelés axe des catégories ou axe x, qui affiche des étiquettes textuels. Notez que l'axe horizontal sera l'axe des valeurs qui affiche des valeurs numériques pour les Graphiques à barres. Dans ce cas, les options de l'onglet Axe horizontal correspondent à celles décrites dans la section précédente. Pour les Graphiques Nuage de points (XY), les deux axes sont des axes de valeur. Sélectionnez Masquer pour masquer l'axe horizontal du graphique, laissez cette option décochée pour afficher l'axe. Définissez l'orientation du Titre en choisissant l'option appropriée de la liste déroulante: Rien pour ne pas afficher le titre de l'axe horizontal. Sans superposition pour afficher le titre en-dessous de l'axe horizontal. Quadrillage permet de spécifier les lignes du Quadrillage horizontal que vous souhaitez afficher en sélectionnant l'option voulue dans la liste déroulante: Rien, Principaux, Secondaires ou Principaux et secondaires. Croisement de l'axe - est utilisé pour spécifier un point sur l'axe vertical où l'axe horizontal doit le traverser. L'option Auto est sélectionnée par défaut, dans ce cas la valeur du point d'intersection est calculée automatiquement en fonction de la plage de données sélectionnée. Vous pouvez sélectionner l'option Valeur dans la liste déroulante et spécifier une valeur différente dans le champ de saisie à droite, ou définir le point d'intersection des axes à la Valeur minimum/maximum (correspondant à la première et la dernière catégorie) sur l'axe vertical. Position de l'étiquette - est utilisé pour spécifier où les étiquettes de l'axe doivent être placés: Graduation ou Entre graduations. Valeurs dans l'ordre inverse - est utilisé pour afficher les catégories dans la direction opposée. Lorsque la case est désactivée, les valeurs sont affichées de gauche à droite. Lorsque la case est activée, les valeurs sont affichées de droite à gauche. La section Options de graduations permet d'ajuster l'apparence des graduations sur l'échelle horizontale. Les graduations du type principal sont les divisions à plus grande échelle qui peuvent avoir des étiquettes affichant des valeurs de catégorie. Les graduations secondaires sont les divisions à moins grande d'échelle qui sont placées entre les graduations principales et n'ont pas d'étiquettes. Les graduations définissent également l'endroit où le quadrillage peut être affiché, si l'option correspondante est définie dans l'onglet Disposition. Vous pouvez ajuster les paramètres de graduation suivants: Type principal/secondaire est utilisé pour spécifier les options de placement suivantes: Rien pour ne pas afficher les graduations principales/secondaires, Sur l'axe pour afficher les graduations principales/secondaires des deux côtés de l'axe, Dans pour afficher les graduations principales/secondaires dans l'axe, A l'extérieur pour afficher les graduations principales/secondaires à l'extérieur de l'axe. Intervalle entre les marques - est utilisé pour spécifier le nombre de catégories à afficher entre deux marques de graduation adjacentes. La section Options d'étiquettes permet d'ajuster l'apparence des étiquettes qui affichent des catégories. Position de l'étiquette est utilisé pour spécifier où les étiquettes de l'axe doivent être placés par rapport à l'axe horizontal: Sélectionnez l'option souhaitée dans la liste déroulante: Rien pour ne pas afficher les étiquettes de catégorie, En bas pour afficher les étiquettes de catégorie au bas de la zone de tracé, En haut pour afficher les étiquettes de catégorie en haut de la zone de tracé, À côté de l'axe pour afficher les étiquettes de catégorie à côté de l'axe. Distance de l'étiquette de l'axe - est utilisé pour spécifier la distance entre les étiquettes et l'axe. Spécifiez la valeur nécessaire dans le champ situé à droite. Plus la valeur que vous définissez est élevée, plus la distance entre l'axe et les étiquettes est grande. Intervalle entre les étiquettes - est utilisé pour spécifier la fréquence à laquelle les étiquettes doivent être affichés. L'option Auto est sélectionnée par défaut, dans ce cas les étiquettes sont affichés pour chaque catégorie. Vous pouvez sélectionner l'option Manuel dans la liste déroulante et spécifier la valeur voulue dans le champ de saisie sur la droite. Par exemple, entrez 2 pour afficher les étiquettes pour une catégorie sur deux. Pour définir le Format d'étiquette cliquez sur le bouton format d'étiquette et choisissez la catégorie appropriée. Les catégories du format d'étiquette disponibles: Général Nombre Scientifique Comptabilité Monétaire Date Heure Pourcentage Fraction Texte Personnalisé Les options du format d'étiquette varient en fonction de la catégorie sélectionné. Pour en savoir plus sur la modification du format de nombre, veuillez consulter cette page. Activez Lié à la source pour conserver la représentation de nombre de la source de données du graphique. L'onglet Alignement dans une cellule comprend les options suivantes: Déplacer et dimensionner avec des cellules - cette option permet de placer le graphique derrière la cellule. Quand une cellule se déplace (par exemple: insertion ou suppression des lignes/colonnes), le graphique se déplace aussi. Quand vous ajustez la largeur ou la hauteur de la cellule, la dimension du graphique s'ajuste aussi. Déplacer sans dimensionner avec les cellules - cette option permet de placer le graphique derrière la cellule mais d'empêcher son redimensionnement. Quand une cellule se déplace, le graphique se déplace aussi, mais si vous redimensionnez la cellule, le graphique demeure inchangé. Ne pas déplacer et dimensionner avec les cellules - cette option empêche le déplacement ou redimensionnement du graphique si la position ou la dimension de la cellule restent inchangées. L'onglet Texte de remplacement permet de spécifier un Titre et une Description qui sera lue aux personnes avec des déficiences cognitives ou visuelles pour les aider à mieux comprendre l'information du graphique. Déplacer et redimensionner des graphiques Une fois le graphique ajouté, on peut également modifier sa taille et sa position. Pour changer la taille du graphique, faites glisser les petits carreaux situés sur ses bords. Pour garder les proportions de l'objet sélectionné lors du redimensionnement, maintenez la touche Maj enfoncée et faites glisser l'une des icônes de coin. Pour modifier la position du graphique, utilisez l'icône qui apparaît si vous placez le curseur de votre souris sur le graphique. Faites glisser l'objet vers la position choisie sans relâcher le bouton de la souris. Lorsque vous déplacez le graphique, des lignes de guidage s'affichent pour vous aider à positionner l'objet sur la page avec précision (si un style d'habillage autre que aligné est sélectionné). Remarque: la liste des raccourcis clavier qui peuvent être utilisés lorsque vous travaillez avec des objets est disponible ici. Modifier les éléments de graphique Pour modifier le Titre du graphique, sélectionnez le texte par défaut à l'aide de la souris et saisissez le vôtre à la place. Pour modifier la mise en forme de la police dans les éléments de texte, tels que le titre du graphique, les titres des axes, les entrées de légende, les étiquettes de données, etc., sélectionnez l'élément de texte nécessaire en cliquant dessus. Utilisez ensuite les icônes de l'onglet Accueil de la barre d'outils supérieure pour modifier le type de police, la taille, la couleur ou le style de décoration. Une fois le graphique sélectionné, l'icône Paramètres de la forme est aussi disponible à la droite car une forme est utilisé en arrière plan du graphique. Vous pouvez appuyer sur cette icône pour accéder l'onglet Paramètres de la forme dans la barre latérale droite et configurer le Remplissage, le Trait et le Style d'habillage de la forme. Veuillez noter qu'on ne peut pas modifier le type de la forme. Sous l'onglet Paramètres de la forme dans le panneau droit, vous pouvez configurer la zone du graphique là-même aussi que les éléments du graphique tels que la zone de tracé, la série de données, le titre du graphique, la légende et les autres et ajouter les différents types de remplissage. Sélectionnez l'élément du graphique nécessaire en cliquant sur le bouton gauche de la souris et choisissez le type de remplissage approprié: couleur de remplissage, remplissage en dégradé, image ou texture, modèle. Configurez les paramètres du remplissage et spécifier le niveau d'opacité si nécessaire. Lorsque vous sélectionnez l'axe vertical ou horizontal ou le quadrillage, vous pouvez configurer le paramètres du trait seulement sous l'onglet Paramètres de la forme: couleur, taille et type. Pour plus de détails sur utilisation des couleurs de la forme, du remplissage et du trait veuillez accéder à cette page. Remarque: l'option Ajouter un ombre est aussi disponible sous l'onglet Paramètres de la forme, mais elle est désactivée pour les éléments du graphique. Si vous voulez redimensionner les éléments du graphique, sélectionnez l'élément nécessaire en cliquant sur le bouton gauche de la souris et faites glisser un des huit carreaux blancs le long du périmètre de l'élément. Pour modifier la position d'un élément, cliquez sur cet élément avec le bouton gauche de souris, vérifiez si le curseur est devenu , maintenez le bouton gauche de la souris enfoncé et faites-le glisser vers la position souhaité. Pour supprimer un élément de graphique, sélectionnez-le en cliquant sur le bouton gauche et appuyez sur la touche Suppr. Vous pouvez également faire pivoter les graphiques 3D à l'aide de la souris. Faites un clic gauche dans la zone de tracé et maintenez le bouton de la souris enfoncé. Faites glisser le curseur sans relâcher le bouton de la souris pour modifier l'orientation du graphique 3D. Ajuster les paramètres du graphique Certains paramètres du graphique peuvent être modifiés en utilisant l'onglet Paramètres du graphique de la barre latérale droite. Pour l'activer, cliquez sur le graphique et sélectionne l'icône Paramètres du graphique à droite. Vous pouvez y modifier les paramètres suivants: Taille est utilisée pour afficher la Largeur et la Hauteur du graphique actuel. Style d'habillage sert à sélectionner un des styles d'habillage - aligné sur le texte, carré, rapproché, au travers, haut et bas, devant le texte, derrière le texte (pour en savoir plus, consultez la section des paramètres avancés ci-dessous). Changer le type de graphique permet de modifier le type et/ou le style du graphique sélectionné. Pour sélectionner le Style de graphique nécessaire, utilisez le deuxième menu déroulant de la section Modifier le type de graphique. Modifier les données est utilisé pour ouvrir la fenêtre Éditeur de graphique. Remarque: pour ouvrir rapidement la fenêtre Éditeur de graphique, vous pouvez également double-cliquer sur le graphique dans le document. Certains paramètres de l'image peuvent être également modifiés en utilisant le menu contextuel. Les options du menu sont les suivantes: Couper, Copier, Coller - les options nécessaires pour couper ou coller le texte / l'objet sélectionné et coller un passage de texte précédemment coupé / copié ou un objet à la position actuelle du curseur. Organiser sert à placer le graphique sélectionné au premier plan, envoyer à l'arrière-plan, avancer ou reculer ainsi que grouper ou dégrouper des graphiques pour effectuer des opérations avec plusieurs à la fois. . Pour en savoir plus sur l'organisation des objets, vous pouvez vous référer à cette page. Aligner sert à aligner le texte à gauche, au centre, à droite, en haut, au milieu, en bas. Pour en savoir plus sur l'alignement des objets, vous pouvez vous référer à cette page. Style d'habillage sert à sélectionner un des styles d'habillage - aligné sur le texte, carré, rapproché, au travers, haut et bas, devant le texte, derrière le texte. L'option Modifier la limite d'habillage n'est pas disponible pour les graphiques. Modifier les données est utilisé pour ouvrir la fenêtre Éditeur de graphique. Paramètres avancés du graphique sert à ouvrir la fenêtre Graphique - Paramètres avancés. En outre, la configuration de Rotation 3D est disponible pour des graphiques 3D: Rotation X - définissez la valeur de rotation de l'axe X en utilisant le clavier ou les flèches Gauche et Droite. Rotation Y - définissez la valeur de rotation de l'axe Y en utilisant le clavier ou les flèches Haut et Bas. Perspective - définissez la valeur appropriée de profondeur en utilisant le clavier ou les flèches Rétrécir le champ ou Élargir le champ. Axes à angle droit sert à définir l'angle de l'axe à droite. Mise à l'échelle automatique - activez cette option pour mettre automatiquement à l'échelle la profondeur et la hauteur du graphique ou désactivez cette option pour définir manuellement la profondeur et la hauteur. Profondeur (% de la base) - définissez la profondeur en utilisant le clavier ou le flèches. Hauteur (% de la base) - définissez la hauteur en utilisant le clavier ou le flèches. Rotation par défaut - restaurer les paramètres 3D par défaut. Veuillez noter qu'il n'est pas possible de modifier chaque élément du graphique à part, tous paramètres seront appliqués au graphique dans son ensemble. Pour modifier les paramètres avancés, cliquez sur le graphique avec le bouton droit de la souris et sélectionnez Paramètres avancés du graphique du menu contextuel ou cliquez sur le lien de la barre latérale droite Afficher les paramètres avancés. La fenêtre de paramètres du graphique s'ouvre: L'onglet Taille comporte les paramètres suivants: Largeur et Hauteur utilisez ces options pour changer la largeur et/ou la hauteur du graphique. Lorsque le bouton Proportions constantes est activé (dans ce cas, il ressemble à ceci ), la largeur et la hauteur seront changées en même temps, le ratio d'aspect du graphique original sera préservé. L'onglet Habillage du texte contient les paramètres suivants: Style d'habillage - utilisez cette option pour changer la manière dont le graphique est positionné par rapport au texte: il peut faire partie du texte (si vous sélectionnez le style « aligné sur le texte ») ou être contourné par le texte de tous les côtés (si vous sélectionnez l'un des autres styles). En ligne - le graphique fait partie du texte, comme un caractère, ainsi si le texte est déplacé, le graphique est déplacé lui aussi. Dans ce cas-là les options de position ne sont pas accessibles. Si vous sélectionnez un des styles suivants, vous pouvez déplacer le graphique indépendamment du texte et définir sa position exacte: Carré - le texte est ajusté autour des bords du graphique. Rapproché - le texte est ajusté sur le contour du graphique. Au travers - le texte est ajusté autour des bords du graphique et occupe l'espace vide à l'intérieur de celui-ci. Haut et bas - le texte est ajusté en haut et en bas du graphique. Devant le texte - le graphique est affiché sur le texte. Derrière le texte - le texte est affiché sur le graphique. Si vous avez choisi le style carré, rapproché, au travers, haut et bas, vous avez la possibilité de configurer des paramètres supplémentaires - Distance du texte de tous les côtés (haut, bas, droit, gauche). L'onglet Position n'est disponible qu'au cas où vous choisissez le style d'habillage autre que 'aligné sur le texte'. Cet onglet contient les paramètres suivants qui varient selon le type d'habillage sélectionné: La section Horizontal vous permet de sélectionner l'un des trois types de positionnement de graphique suivants: Alignement (gauche, centre, droite) par rapport au caractère, à la colonne, à la marge de gauche, à la marge, à la page ou à la marge de droite, Position absolue mesurée en unités absolues, c.-à-d. Centimètres/Points/Pouces (selon l'option spécifiée dans l'onglet Fichier -> Paramètres avancés...) à droite du caractère, de la colonne, de la marge de gauche, de la marge, de la page ou de la marge de droite, Position relative mesurée en pourcentage par rapport à la marge gauche, à la marge, à la page ou à la marge de droite. La section Vertical vous permet de sélectionner l'un des trois types de positionnement de graphique suivants: Alignement (haut, centre, bas) par rapport à la ligne, à la marge, à la marge inférieure, au paragraphe, à la page ou à la marge supérieure, Position absolue mesurée en unités absolues, c.-à-d. Centimètres/Points/Pouces (selon l'option spécifiée dans l'onglet Fichier -> Paramètres avancés...) au-dessous de la ligne, de la marge, de la marge inférieure, du paragraphe, de la page ou la marge supérieure, Position relative mesurée en pourcentage par rapport à la marge, à la marge inférieure, à la page ou à la marge supérieure. Déplacer avec le texte détermine si le graphique se déplace en même temps que le texte sur lequel il est aligné. Chevauchement détermine si deux graphiques se chevauchent ou non si vous les faites glisser les unes près des autres sur la page. L'onglet Texte de remplacement permet de spécifier un Titre et une Description qui sera lue aux personnes avec des déficiences cognitives ou visuelles pour les aider à mieux comprendre l'information du graphique."
    },
   {
        "id": "UsageInstructions/InsertContentControls.htm", 
        "title": "Insérer des contrôles de contenu", 
        "body": "Éditeur de Documents ONLYOFFICE permet d'ajouter les contrôles de contenu traditionnels, c'est à dire les contrôles qui sont entièrement rétrocompatible avec les éditeurs alternatifs tels que Microsoft Word. Actuellement, l'Éditeur de Documents ONLYOFFICE prend en charge les contrôles de contenu traditionnels tels que Texte brut, Texte enrichi, Image, Zone de liste déroulante, Liste déroulante, Date, Case à cocher. Texte brut est le texte comportant un objet qui ne peut pas être modifié. C'est seulement un paragraphe que le contrôle en texte brut peut contenir. Texte enrichi est le texte comportant un objet qui peut être modifié. Les contrôles en texte enrichi peuvent contenir plusieurs paragraphes, listes et objets (images, formes, tableaux etc.). Image est un objet comportant une image. Zone de liste déroulante est un objet comportant une liste déroulante avec un ensemble de choix. Ce contrôle permet de choisir une valeur prédéfinie et la modifier au besoin. Liste déroulante est un objet comportant une liste déroulante avec un ensemble de choix. Ce contrôle permet de choisir une valeur prédéfinie. La valeur choisie ne peut pas être modifiée. Date est l'objet comportant le calendrier qui permet de choisir une date. Case à cocher est un objet permettant d'afficher deux options : la case cochée et la case décochée. Ajouter des contrôles de contenu Créer un nouveau contrôle de contenu de texte brut positionnez le point d'insertion dans une ligne du texte où vous souhaitez ajouter le contrôle, ou sélectionnez un passage de texte que vous souhaitez transformer en contrôle du contenu. passez à l'onglet Insertion de la barre d'outils supérieure. cliquez sur la flèche en regard de l'icône Contrôles de contenu. choisissez l'option Insérer un contrôle de contenu en texte brut dans le menu. Le contrôle sera inséré au point d'insertion dans une ligne du texte existant. Tapez votre texte à remplacer du texte par défaut à l'intérieur du contrôle de contenu (Votre texte ici) : sélectionnez le texte par défaut et tapez du texte approprié ou copiez le texte que vous voulez et collez le à l'intérieur du contrôle de contenu. Les contrôles de contenu de texte brut ne permettent pas l'ajout de sauts de ligne et ne peuvent pas contenir d'autres objets tels que des images, des tableaux, etc. Créer un nouveau contrôle de contenu de texte enrichi positionnez le point d'insertion dans une ligne du texte où vous souhaitez ajouter le contrôle, ou sélectionnez un passage de texte que vous souhaitez transformer en contrôle du contenu. passez à l'onglet Insertion de la barre d'outils supérieure. cliquez sur la flèche en regard de l'icône Contrôles de contenu. choisissez l'option Insérer un contrôle de contenu en texte enrichi dans le menu. Le contrôle sera inséré dans un nouveau paragraphe du texte. Tapez votre texte à remplacer du texte par défaut à l'intérieur du contrôle de contenu (Votre texte ici) : sélectionnez le texte par défaut et tapez du texte approprié ou copiez le texte que vous voulez et collez le à l'intérieur du contrôle de contenu. Les contrôles de contenu de texte enrichi permettent d'ajouter des sauts de ligne, c'est-à-dire peuvent contenir plusieurs paragraphes ainsi que certains objets, tels que des images, des tableaux, d'autres contrôles de contenu, etc. Créer un nouveau contrôle de contenu d'image positionnez le point d'insertion dans une ligne du texte où vous souhaitez ajouter le contrôle. passez à l'onglet Insertion de la barre d'outils supérieure. cliquez sur la flèche en regard de l'icône Contrôles de contenu. choisissez l'option Image dans le menu et le contrôle de contenu sera inséré au point d'insertion. cliquez sur l'icône Image dans le bouton au dessus de la bordure du contrôle de contenu, la fenêtre de sélection standard va apparaître. Choisissez l'image stockée sur votre ordinateur et cliquez sur Ouvrir. L'image choisie sera affichée à l'intérieur du contrôle de contenu. Pour remplacer l'image, cliquez sur l'icône d'image dans le bouton au dessus de la bordure du contrôle de contenu et sélectionnez une autre image. Créer un nouveau contrôle de contenu Zone de liste déroulante ou Liste déroulante Zone de liste déroulante et la Liste déroulante sont des objets comportant une liste déroulante avec un ensemble de choix. Ces contrôles de contenu sont crées de la même façon. La différence entre les contrôles Zone de liste déroulante et Liste déroulante est que la liste déroulante propose des choix que vous êtes obligé de sélectionner tandis que la Zone de liste déroulante accepte la saisie d'un autre élément. positionnez le point d'insertion dans une ligne du texte où vous souhaitez ajouter le contrôle de contenu. passez à l'onglet Insertion de la barre d'outils supérieure. cliquez sur la flèche en regard de l'icône Contrôles de contenu. choisissez l'option Zone de liste déroulante et Liste déroulante dans le menu et le contrôle de contenu sera inséré au point d'insertion. cliquez avec le bouton droit sur le contrôle de contenu ajouté et sélectionnez Paramètres du contrôle de contenu du menu contextuel. dans la fenêtre Paramètres du contrôle de contenu qui s'ouvre, passez à l'onglet Zone de liste déroulante ou Liste déroulante en fonction du type de contrôle de contenu sélectionné. pour ajouter un nouveau élément à la liste, cliquez sur le bouton Ajouter et remplissez les rubriques disponibles dans la fenêtre qui s'ouvre : saisissez le texte approprié dans le champ Nom d'affichage, par exemple, Oui, Non, Autre option. Ce texte sera affiché à l'intérieur du contrôle de contenu dans le document. par défaut le texte du champ Valeur coïncide avec celui-ci du champ Nom d'affichage. Si vous souhaitez modifier le texte du champ Valeur, veuillez noter que la valeur doit être unique pour chaque élément. Cliquez sur OK. les boutons Modifier et Effacer à droite servent à modifier ou supprimer les éléments de la liste et les boutons En haut et Bas à changer l'ordre d'affichage des éléments. Une fois les paramètres configurés, cliquez sur OK pour enregistrer la configuration et fermer la fenêtre. Vous pouvez cliquez sur la flèche à droite du contrôle de contenu Zone de liste déroulante ou Liste déroulante ajouté pour ouvrir la liste et sélectionner l'élément approprié. Une fois sélectionné dans la Zone de liste déroulante, on peut modifier le texte affiché en saisissant propre texte partiellement ou entièrement. La Liste déroulante ne permet pas la saisie d'un autre élément. Créer un nouveau contrôle de contenu sélecteur des dates positionnez le point d'insertion dans le texte où vous souhaitez ajouter le contrôle de contenu. passez à l'onglet Insertion de la barre d'outils supérieure. cliquez sur la flèche en regard de l'icône Contrôles de contenu. choisissez l'option Date dans le menu et le contrôle de contenu indiquant la date actuelle sera inséré au point d'insertion. cliquez avec le bouton droit sur le contrôle de contenu ajouté et sélectionnez Paramètres du contrôle de contenu du menu contextuel. dans la fenêtre Paramètres du contrôle de contenu, passez à l'onglet Format de date. Sélectionnez la Langue et le format de date appropriée dans la liste Afficher le date comme suit. Cliquez sur OK pour appliquer toutes les modifications et fermer la fenêtre. Vous pouvez cliquez sur la flèche à droite du contrôle de contenu Date ajouté pour ouvrir le calendrier et sélectionner la date appropriée. Créer un nouveau contrôle de contenu de case à cocher positionnez le point d'insertion dans le texte où vous souhaitez ajouter le contrôle de contenu. passez à l'onglet Insertion de la barre d'outils supérieure. cliquez sur la flèche en regard de l'icône Contrôles de contenu. choisissez l'option Case à cocher dans le menu et le contrôle de contenu sera inséré au point d'insertion. cliquez avec le bouton droit sur le contrôle de contenu ajouté et sélectionnez Paramètres du contrôle de contenu du menu contextuel. dans la fenêtre Paramètres du contrôle de contenu, passez à l'onglet Case à cocher. cliquez sur le bouton Symbole Activé pour spécifier le symbole indiquant la case cochée ou le Symbole Désactivé pour spécifier la façon d'afficher la case décochée. La fenêtre Symbole s'ouvre. Veuillez consulter cet article pour en savoir plus sur utilisation des symboles. Une fois les paramètres configurés, cliquez sur OK pour enregistrer la configuration et fermer la fenêtre. La case à cocher s'affiche désactivée. Une fois que vous cliquiez la case à cocher, le symbole qu'on a spécifié comme le Symbole Activé est inséré. Remarque : La bordure du contrôle de contenu est visible uniquement lorsque le contrôle est sélectionné. Les bordures n'apparaissent pas sur une version imprimée. Déplacer des contrôles de contenu Les contrôles peuvent être déplacés à un autre endroit du document : cliquez sur le bouton à gauche de la bordure de contrôle pour sélectionner le contrôle et faites-le glisser sans relâcher le bouton de la souris à un autre endroit dans le texte du document. Vous pouvez également copier et coller des contrôles de contenu : sélectionnez le contrôle voulu et utilisez les combinaisons de touches Ctrl+C/Ctrl+V. Modifier des contrôles de contenu en texte brut et en texte enrichi On peut modifier le texte à l'intérieur des contrôles de contenu en texte brut et en texte enrichi à l'aide des icônes de la barre d'outils supérieure : vous pouvez ajuster le type, la taille et la couleur de police, appliquer des styles de décoration et les configurations de mise en forme. Il est également possible d'utiliser la fenêtre Paragraphe - Paramètres avancés accessible depuis le menu contextuel ou depuis la barre latérale de droite pour modifier les propriétés du texte. Le texte contenu dans les contrôles de contenu de texte enrichi peut être mis en forme comme un texte normal du document, c'est-à-dire que vous pouvez définir l'interlignage, modifier les retraits de paragraphe, ajuster les taquets de tabulation, etc. Modification des paramètres de contrôle du contenu Quel que soit le type du contrôle de contenu, on peut configurer ses paramètres sous les onglets Général et Verrouillage dans la fenêtre Paramètres du contrôle de contenu. Pour ouvrir les paramètres de contrôle du contenu, vous pouvez procéder de la manière suivante : Sélectionnez le contrôle de contenu nécessaire, cliquez sur la flèche en regard de l'icône Contrôles de contenu dans la barre d'outils supérieure et sélectionnez l'option Paramètres de contrôle du menu. Cliquez avec le bouton droit n'importe où dans le contrôle de contenu et utilisez l'option Paramètres de contrôle du contenu dans le menu contextuel. Une nouvelle fenêtre s'ouvrira. Sous l'onglet Général vous pouvez configurer les paramètres suivants : Spécifiez le Titre, l'Espace réservé ou le Tag dans les champs correspondants. Le titre s'affiche lorsque le contrôle est sélectionné dans le document. L'espace réservé est le texte principal qui s'affiche à l'intérieur du contrôle de contenu. Les Tags sont utilisées pour identifier les contrôles de contenu afin que vous puissiez y faire référence dans votre code. Choisissez si vous voulez afficher le contrôle de contenu avec une Boîte d'encombrement ou non. Utilisez l'option Aucun pour afficher le contrôle sans aucune boîte d'encombrement. Si vous sélectionnez l'option Boîte d'encombrement, vous pouvez choisir la Couleur de la boîte à l'aide du champ ci-dessous. Cliquez sur le bouton Appliquer à tous pour appliquer les paramètres d'Apparence spécifiés à tous les contrôles de contenu du document. Sous l'onglet Verrouillage vous pouvez empêchez toute suppression ou modifcation du contrôle de contenu en utilisant les paramètres suivants : Le contrôle du contenu ne peut pas être supprimé - cochez cette case pour empêcher la suppression du contrôle de contenu. Le contenu ne peut pas être modifié - cochez cette case pour protéger le contenu du contrôle de contenu contre une modification. Sous le troisième onglet on peut configurer les paramètres spécifiques de certain type du contrôle de contenu, comme : Zone de liste déroulante, Liste déroulante, Date, Case à cocher. Tous ces paramètres ont déjà été décrits ci-dessus dans la section appropriée à chaque contrôle de contenu. Cliquez sur le bouton OK dans la fenêtre des paramètres pour appliquer les changements. Il est également possible de surligner les contrôles de contenu avec une certaine couleur. Pour surligner les contrôles avec une couleur : Cliquez sur le bouton situé à gauche de la bordure du champ pour sélectionner le contrôle, Cliquez sur la flèche à côté de l'icône Contrôles de contenu dans la barre d'outils supérieure, Sélectionnez l'option Paramètres de surbrillance du menu, Sélectionnez la couleur souhaitée dans les palettes disponibles : Couleurs du thème, Couleurs standard ou spécifiez une nouvelle Couleur personnalisée. Pour supprimer la surbrillance des couleurs précédemment appliquée, utilisez l'option Pas de surbrillance. Les options de surbrillance sélectionnées seront appliquées à tous les contrôles de contenu du document. Supprimer des contrôles de contenu Pour supprimer un contrôle et laisser tout son contenu, cliquez sur le contrôle de contenu pour le sélectionner, puis procédez de l'une des façons suivantes : Cliquez sur la flèche en regard de l'icône Contrôles de contenu dans la barre d'outils supérieure et sélectionnez l'option Supprimer le contrôle du contenu dans le menu. Cliquez avec le bouton droit sur le contrôle de contenu et utilisez l'option Supprimer le contrôle du contenu dans le menu contextuel. Pour supprimer un contrôle et tout son contenu, sélectionnez le contrôle nécessaire et appuyez sur la touche Suppr du clavier."
    },
   {
        "id": "UsageInstructions/InsertCrossReference.htm", 
        "title": "Insertion de renvoi", 
        "body": "Dans l'Éditeur de Documents, les renvois permettent de créer les liens vers d'autres parties du même document telles que les diagrammes ou les tableaux. Le renvoi apparaît sous la forme d'un lien hypertexte. Créer un renvoi Positionnez le curseur dans le texte à l'endroit où vous souhaitez insérer un renvoi. Passez à l'onglet Références et cliquez sur l'icône Renvoi. Paramétrez le renvoi dans la fenêtre contextuelle Renvoi. Dans la liste déroulante Type de référence spécifiez l'élément vers lequel on veut renvoyer, par exemple, l'objet numéroté (option par défaut), en-tête, signet, note de bas de page, note de fin, équation, figure, et tableau. Sélectionnez l'élément approprié. Dans la liste Insérer la référence à spécifiez les informations que vous voulez insérer dans le document. Votre choix dépend de la nature des éléments que vous avez choisi dans la liste Type de référence. Par exemple, pour l'option En-tête on peut spécifier les informations suivantes : Texte de l'en-tête, Numéro de page, Numéro de l'en-tête, Numéro de l'en-tête (pas de contexte), Numéro de l'en-tête (contexte global), Au-dessus/au-dessous. La liste complète des options dépend du type de référence choisi : Type de référence Insérer la référence à Description Objet numéroté Numéro de page Pour insérer le numéro de page du l'objet numéroté Numéro de paragraphe Pour insérer le numéro de paragraphe du l'objet numéroté Numéro de paragraphe (pas de contexte) Pour insérer le numéro de paragraphe abrégé. On fait la référence à un élément spécifique de la liste numérotée, par exemple vous faites une référence seulement à 1 au lieu de 4.1.1. Numéro de paragraphe (contexte global) Pour insérer le numéro de paragraphe complet, par exemple 4.1.1. Texte du paragraphe Pour insérer la valeur textuelle du paragraphe, par exemple pour 4.1.1. Conditions générales on fait référence seulement à Conditions générales Au-dessus/au-dessous Pour insérer automatiquement des mots Au-dessus ou Au-dessous en fonction de la position de l'élément. En-tête Texte de l'en-tête Pour insérer le texte complet de l'en-tête Numéro de page Pour insérer le numéro de page d'un en-tête Numéro de l'en-tête Pour insérer la numérotation consécutive de l'en-tête Numéro de l'en-tête (pas de contexte) Pour insérer le numéro de l'en-tête abrégé. Assurez-vous de placer le curseur dans la section à laquelle vous souhaiter faire une référence, par exemple, vous êtes dans la section 4 et vous souhaiter faire référence à l'en-tête 4.B alors au lieu de 4.B s'affiche seulement B. Numéro de l'en-tête (contexte global) Pour insérer le numéro de l'en-tête complet même si le curseur est dans la même section Au-dessus/au-dessous Pour insérer automatiquement des mots Au-dessus ou Au-dessous en fonction de la position de l'élément. Signet Le texte du signet Pour insérer le texte complet du signet Numéro de page Pour insérer le numéro de page du signet Numéro de paragraphe Pour insérer le numéro de paragraphe du signet Numéro de paragraphe (pas de contexte) Pour insérer le numéro de paragraphe abrégé. On fait la référence seulement à un élément spécifique, par exemple vous faites une référence seulement à 1 au lieu de 4.1.1. Numéro de paragraphe (contexte global) Pour insérer le numéro de paragraphe complet, par exemple 4.1.1. Au-dessus/au-dessous Pour insérer automatiquement des mots Au-dessus ou Au-dessous en fonction de la position de l'élément. Note de bas de page Numéro de la note de bas de page Pour insérer le numéro de la note de bas de page Numéro de page Pour insérer le numéro de page de la note de bas de page Au-dessus/au-dessous Pour insérer automatiquement des mots Au-dessus ou Au-dessous en fonction de la position de l'élément. Le numéro de la note de bas de page (mis en forme) Pour insérer le numéro de la note de bas de page mis en forme d'une note de bas de page. La numérotation de notes de bas de page existantes n'est pas affectée Note de fin Le numéro de la note de fin Pour insérer le numéro de la note de fin Numéro de page Pour insérer le numéro de page de la note de fin Au-dessus/au-dessous Pour insérer automatiquement des mots Au-dessus ou Au-dessous en fonction de la position de l'élément. Le numéro de la note de fin (mis en forme) Pour insérer le numéro de la note de fin mis en forme d'une note de fin. La numérotation de notes de fin existantes n'est pas affectée Équation / Figure / Tableau La légende complète Pour insérer le texte complet de la légende Seulement l'étiquette et le numéro Pour insérer l'étiquette et le numéro de l'objet, par exemple, Tableau 1.1 Seulement le texte de la légende Pour insérer seulement le texte de la légende Numéro de page Pour insérer le numéro de la page comportant du l'objet référencé Au-dessus/au-dessous Pour insérer automatiquement des mots Au-dessus ou Au-dessous en fonction de la position de l'élément. Pour faire apparaître une référence comme un lien actif, cochez la case Insérer en tant que lien hypertexte. Pour spécifier la position de l'élément référencé, cochez la case Inclure au-dessus/au-dessous (si disponible). Éditeur de Documents ONLYOFFICE insère automatiquement des mots “au-dessus” ou “au-dessous” en fonction de la position de l'élément. Pour spécifier le séparateur, cochez la case à droite Séparer les numéros avec. Il faut spécifier le séparateur pour les références dans le contexte global. Dans la zone Pour quel en-tête choisissez l'élément spécifique auquel renvoyer en fonction de la Type référence choisi, par exemple : pour l'option En-tête on va afficher la liste complète des en-têtes dans ce document. Pour créer le renvoi cliquez sur Insérer. Supprimer des renvois Pour supprimer un renvoi, sélectionnez le renvoi que vous souhaitez supprimer et appuyez sur la touche Suppr."
    },
   {
        "id": "UsageInstructions/InsertDateTime.htm", 
        "title": "Insérer la date et l'heure", 
        "body": "Pour insérer la Date et l'heure dans l'Éditeur de Documents, placer le curseur à l'endroit où vous voulez insérer la Date et heure, passez à l'onglet Insérer dans la barre d'outils en haut, cliquez sur l'icône Date et heure dans la barre d'outils en haut, dans la fenêtre Date et l'heure qui s'affiche, configurez les paramètres, comme suit : Sélectionnez la langue visée. Sélectionnez le format parmi ceux proposés. Cochez la case Mettre à jour automatiquement en tant que la date et l'heure sont automatiquement mis à jour. Remarque : Si vous préférez mettre à jour la date et l'heure manuellement, vous pouvez utiliser l'option de Mettre à jour dans le menu contextuel. Sélectionnez l'option Définir par défaut pour utiliser ce format par défaut pour cette langue. Cliquez sur OK."
    },
   {
        "id": "UsageInstructions/InsertEndnotes.htm", 
        "title": "Insérer des notes de fin", 
        "body": "Dans l'Éditeur de Documents, utilisez les notes de fin pour donner des explications ou ajouter des commentaires à un terme ou une proposition et citer une source à la fin du document. Insertion de notes de fin Pour insérer la note de fin dans votre document, placez un point d'insertion à la fin du texte ou du mot concerné, passez à l'onglet Références dans la barre d'outils en haut, cliquez sur l'icône Note de bas de page dans la barre d'outils en haut et sélectionnez dans la liste Insérer une note de fin. Le symbole de la note de fin est alors ajouté dans le corps de texte (symbole en exposant qui indique la note de fin), et le point d'insertion se déplace à la fin de document. Vous pouvez saisir votre texte. Il faut suivre la même procédure pour insérer une note de fin suivante sur un autre fragment du texte. La numérotation des notes de fin est appliquée automatiquement. i, ii, iii, etc. par défaut. Affichage des notes de fin dans le document Faites glisser le curseur au symbole de la note de fin dans le texte du document, la note de fin s'affiche dans une petite fenêtre contextuelle. Parcourir les notes de fin Vous pouvez facilement passer d'une note de fin à une autre dans votre document, cliquez sur la flèche à côté de l'icône Note de bas de page dans l'onglet Références dans la barre d'outils en haut, dans la section Passer aux notes de fin utilisez la flèche gauche pour se déplacer à la note de fin suivante ou la flèche droite pour se déplacer à la note de fin précédente. Modification des notes de fin Pour particulariser les notes de fin, cliquez sur la flèche à côté de l'icône Note de bas de page dans l'onglet Références dans la barre d'outils en haut, appuyez sur Paramètres des notes dans le menu, modifiez les paramètres dans la boîte de dialogue Paramètres des notes qui va apparaître : Spécifiez l'Emplacement des notes de fin et sélectionnez l'une des options dans la liste déroulante à droite : Fin de section - les notes de fin son placées à la fin de la section. Fin de document - les notes de fin son placées à la fin du document. Modifiez le Format des notes de fin : Format de nombre - sélectionnez le format de nombre disponible dans la liste : 1, 2, 3,..., a, b, c,..., A, B, C,..., i, ii, iii,..., I, II, III,.... Début - utilisez les flèches pour spécifier le nombre ou la lettre à utiliser pour la première note de fin. Numérotation - sélectionnez les options de numérotation : Continue - numérotation de manière séquentielle dans le document, À chaque section - numérotation redémarre à 1 (ou à une autre séquence de symboles) au début de chaque section du document, À chaque page - numérotation redémarre à 1 (ou à une autre séquence de symboles) au début de chaque page du document. Marque particularisée - séquence de symboles ou mots spéciaux à utiliser comme symbole de note de fin (Exemple : * ou Note1). Tapez le symbole/mot dans le champ et cliquez sur Insérer en bas de la boîte dialogue Paramètres des notes. Dans la liste déroulante Appliquer les modifications à sélectionnez si vous voulez appliquer les modifications À tout le document ou seulement À cette section. Remarque : pour définir un format différent pour les notes de fin sur différentes sections du document, il faut tout d'abord utiliser les sauts de sections . Une fois que vous avez terminé, cliquez sur Appliquer. Supprimer le notes de fin Pour supprimer une note de fin, placez un point d'insertion devant le symbole de note de fin dans le texte et cliquez sur la touche Suppr. Toutes les autres notes de fin sont renumérotées. Pour supprimer toutes les notes de fin du document, cliquez sur la flèche à côté de l'icône Note de bas de page dans l'onglet Références dans la barre d'outils en haut, sélectionnez Supprimer les notes dans le menu. dans la boîte de dialogue sélectionnez Supprimer toutes les notes de fin et cliquez sur OK."
    },
   {
        "id": "UsageInstructions/InsertEquation.htm", 
        "title": "Insérer des équations", 
        "body": "Document Editor vous permet de créer des équations à l'aide des modèles intégrés, de les modifier, d'insérer des caractères spéciaux (à savoir des opérateurs mathématiques, des lettres grecques, des accents, etc.). Ajouter une nouvelle équation Pour insérer une équation depuis la galerie, placez le curseur sur la ligne nécessaire, passez à l'onglet Insertion de la barre d'outils supérieure, cliquez sur la flèche à côté de l'icône Équation de la barre d'outils supérieure, sélectionnez la catégorie de l'équation sur la barre d'outils supérieure au-dessus de l'équation ajoutée, ou sélectionnez la catégorie d'équation nécessaire dans la liste déroulante. Les catégories suivantes sont actuellement disponibles: Symboles, Fractions, Scripts, Radicaux, Intégrales, Grands opérateurs, Crochets, Fonctions, Accentuations, Limites et logarithmes, Opérateurs, Matrices, Cliquez sur Paramètres d'équations dans la barre d'outils au-dessus de l'équation ajoutée pour accéder à d'autres paramètres, tels que Unicode ou LaTeX, Professionnel ou Linéaire et passer à mode en ligne, cliquez sur le symbole/l'équation voulu(e) dans l'ensemble de modèles correspondant. La zone de symbole/équation sélectionnée sera inséré à la position actuelle du curseur. Si la ligne sélectionnée est vide, l'équation sera centré. Pour aligner une telle équation à gauche ou à droite, cliquez sur la zone de l'équation et utilisez les icônes ou sous l'onglet Accueil de la barre d'outils supérieure. Chaque modèle d'équation comporte un ensemble d'emplacements. Un emplacement est une position pour chaque élément qui compose l'équation. Un emplacement vide (également appelé un espace réservé) a un contour en pointillé . Vous devez remplir tous les espaces réservés en spécifiant les valeurs nécessaires. Remarque: pour commencer une équation, utilisez le raccourci Alt + =. On peut aussi ajouter une légende à l'équation. Veuillez consulter cet article pour en savoir plus sur utilisation des légendes avec une équation. Entrer des valeurs Le point d'insertion spécifie où le prochain caractère que vous entrez apparaîtra. Pour positionner le point d'insertion avec précision, cliquez dans un espace réservé et utilisez les flèches du clavier pour déplacer le point d'insertion d'un caractère vers la gauche/la droite ou le haut/le bas. S'il vous faut créer un nouveau espace réservé en dessous de l'emplacement du point d'insertion dans le modèle sélectionné, appuyez sur Entrée. Une fois le point d'insertion positionné, vous pouvez remplir l'espace réservé: entrez la valeur numérique/littérale souhaitée à l'aide du clavier, insérez un caractère spécial de la palette des symboles du menu Équation sous l'onglet Insertion de la barre d'outils supérieure ou saisissez avec le clavier (consultez le guide AutoMaths), ajoutez un autre modèle d'équation à partir de la palette pour créer une équation imbriquée complexe. La taille de l'équation primaire sera automatiquement ajustée pour s'adapter à son contenu. La taille des éléments de l'équation imbriquée dépend de la taille de l'espace réservé de l'équation primaire, mais elle ne peut pas être inférieure à la taille de sous-indice. Pour ajouter de nouveaux éléments d'équation, vous pouvez également utiliser les options du menu contextuel: Pour ajouter un nouvel argument avant ou après celui existant dans les Crochets, vous pouvez cliquer avec le bouton droit sur l'argument existant et sélectionner l'option Insérer un argument avant/après dans le menu. Pour ajouter une nouvelle équation dans les Cas avec plusieurs conditions du groupe Crochets (ou d'autres types d'équations si vous avez ajouté de nouveaux espaces réservés en appuyant sur Entrée), vous pouvez cliquer avec le bouton droit de la souris sur un espace réservé vide ou sur une équation existante et sélectionnez l'option Insérer une équation avant/après dans le menu. Pour ajouter une nouvelle ligne ou une colonne dans une Matrice, vous pouvez cliquer avec le bouton droit de la souris sur un espace réservé, sélectionner l'option Insérer dans le menu, puis sélectionner Ligne au-dessus/en dessous ou Colonne à gauche/à droite. Remarque: actuellement, les équations ne peuvent pas être entrées en utilisant le format linéaire, c'est-à-dire \\sqrt(4&x^3). Lorsque vous entrez les valeurs des expressions mathématiques, vous n'avez pas besoin d'utiliser la Barre d'espace car les espaces entre les caractères et les signes des opérations sont définis automatiquement. Si l'équation est trop longue et ne tient pas en une seule ligne, le saut de ligne automatique se produit pendant que vous tapez. Vous pouvez également insérer un saut de ligne à une position spécifique en cliquant avec le bouton droit sur un opérateur mathématique et en sélectionnant l'option Insérer un saut manuel dans le menu. L'opérateur sélectionné va commencer une nouvelle ligne. Une fois le un saut de ligne ajouté, vous pouvez appuyer sur la touche Tabulation pour aligner la nouvelle ligne sur un opérateur mathématique quelconque de la ligne précédente. Pour supprimer le saut de ligne manuel ajouté, cliquez avec le bouton droit sur l'opérateur mathématique qui commence une nouvelle ligne et sélectionnez l'option Supprimer un saut manuel. Mise en forme des équations Pour augmenter ou diminuer la taille de la police d'équation, cliquez n'importe où dans la zone d'équation et utilisez les boutons et sous l'onglet Accueil de la barre d'outils supérieure ou sélectionnez la taille de police nécessaire dans la liste. Tous les éléments d'équation changeront en conséquence. Les lettres de l'équation sont en italique par défaut. Si nécessaire, vous pouvez changer le style de police (gras, italique, barré) ou la couleur pour une équation entière ou une portion. Le style souligné peut être appliqué uniquement à l'équation entière et non aux caractères individuels. Sélectionnez la partie de l'équation voulue en cliquant dessus et en le faisant glisser. La partie sélectionnée sera surlignée en bleu. Utilisez ensuite les boutons nécessaires sous l'onglet Accueil de la barre d'outils supérieure pour mettre en forme la sélection. Par exemple, vous pouvez supprimer le format italique pour les mots ordinaires qui ne sont pas des variables ou des constantes. Pour modifier certains éléments d'équation, vous pouvez également utiliser les options du menu contextuel: Pour modifier le format des Fractions, vous pouvez cliquer sur une fraction avec le bouton droit de la souris et sélectionner l'option Changer en fraction en biais/linéaire/empilée dans le menu (les options disponibles varient en fonction du type de fraction sélectionné). Pour modifier la position des Scripts par rapport au texte, vous pouvez faire un clic droit sur l'équation contenant des scripts et sélectionner l'option Scripts avant/après le texte dans le menu. Pour modifier la taille des arguments pour Scripts, Radicaux, Intégrales, Grands opérateurs, Limites et Logarithmes, Opérateurs ainsi que pour les accolades supérieures/inférieures et les Modèles avec des caractères de regroupement du groupe Accentuations, vous pouvez cliquer avec le bouton droit sur l'argument que vous souhaitez modifier et sélectionner l'option Augmenter/Diminuer la taille de l'argument dans le menu. Pour spécifier si un espace libre vide doit être affiché ou non pour un Radical, vous pouvez cliquer avec le bouton droit de la souris sur le radical et sélectionner l'option Masquer/Afficher le degré dans le menu. Pour spécifier si un espace réservé de limite vide doit être affiché ou non pour une Intégrale ou un Grand opérateur, vous pouvez cliquer sur l'équation avec le bouton droit de la souris et sélectionner l'option Masquer/Afficher la limite supérieure/inférieure dans le menu. Pour modifier la position des limites relative au signe d'intégrale ou d'opérateur pour les Intégrales ou les Grands opérateurs, vous pouvez cliquer avec le bouton droit sur l'équation et sélectionner l'option Modifier l'emplacement des limites dans le menu. Les limites peuvent être affichées à droite du signe de l'opérateur (sous forme d'indices et d'exposants) ou directement au-dessus et au-dessous du signe de l'opérateur. Pour modifier la position des limites par rapport au texte des Limites et des Logarithmes et des modèles avec des caractères de regroupement du groupe Accentuations, vous pouvez cliquer avec le bouton droit sur l'équation et sélectionner l'option Limites sur/sous le texte dans le menu. Pour choisir lequel des Crochets doit être affiché, vous pouvez cliquer avec le bouton droit de la souris sur l'expression qui s'y trouve et sélectionner l'option Masquer/Afficher les parenthèses ouvrantes/fermantes dans le menu. Pour contrôler la taille des Crochets, vous pouvez cliquer avec le bouton droit sur l'expression qui s'y trouve. L'option Étirer les parenthèses est sélectionnée par défaut afin que les parenthèses puissent croître en fonction de l'expression qu'elles contiennent, mais vous pouvez désélectionner cette option pour empêcher l'étirement des parenthèses. Lorsque cette option est activée, vous pouvez également utiliser l'option Faire correspondre les crochets à la hauteur de l'argument. Pour modifier la position du caractère par rapport au texte des accolades ou des barres supérieures/inférieures du groupe Accentuations, vous pouvez cliquer avec le bouton droit sur le modèle et sélectionner l'option Caractère/Barre sur/sous le texte dans le menu. Pour choisir les bordures à afficher pour une Formule encadrée du groupe Accentuations, vous pouvez cliquer sur l'équation avec le bouton droit de la souris et sélectionner l'option Propriétés de bordure dans le menu, puis sélectionner Masquer/Afficher bordure supérieure/inférieure/gauche/droite ou Ajouter/Masquer ligne horizontale/verticale/diagonale. Pour spécifier si un espace réservé vide doit être affiché ou non pour une Matrice, vous pouvez cliquer avec le bouton droit de la souris sur le radical et sélectionner l'option Masquer/Afficher l'espace réservé dans le menu. Pour aligner certains éléments d'équation, vous pouvez utiliser les options du menu contextuel: Pour aligner des équations dans les Cas avec plusieurs conditions du groupe Crochets (ou d'autres types d'équations si vous avez ajouté de nouveaux espaces réservés en appuyant sur Entrée), vous pouvez cliquer avec le bouton droit de la souris sur une équation, sélectionnez l'option Alignement dans le menu, puis sélectionnez le type d'alignement: Haut, Centre ou Bas Pour aligner une Matrice verticalement, vous pouvez cliquer avec le bouton droit sur la matrice, sélectionner l'option Alignement de Matrice dans le menu, puis sélectionner le type d'alignement: Haut, Centre ou Bas Pour aligner les éléments d'une colonne Matrice horizontalement, vous pouvez cliquer avec le bouton droit sur la colonne, sélectionner l'option Alignement de Colonne dans le menu, puis sélectionner le type d'alignement: Gauche, Centre ou Droite. Supprimer les éléments d'une équation Pour supprimer une partie de l'équation, sélectionnez la partie appropriée en faisant glisser la souris ou en maintenant la touche Maj enfoncée et en utilisant les boutons fléchés, puis appuyez sur la touche Suppr. Un emplacement ne peut être supprimé qu'avec le modèle auquel il appartient. Pour supprimer toute équation, sélectionnez celle-ci en faisant glisser la souris ou faites des doubles clics sur la zone d'équation et appuyez sur la touche Suppr. Pour supprimer certains éléments d'équation, vous pouvez également utiliser les options du menu contextuel: Pour supprimer un Radical, vous pouvez faire un clic droit dessus et sélectionner l'option Supprimer radical dans le menu. Pour supprimer un Indice et/ou un Exposant, vous pouvez cliquer avec le bouton droit sur l'expression qui les contient et sélectionner l'option Supprimer indice/exposant dans le menu. Si l'expression contient des scripts qui viennent avant le texte, l'option Supprimer les scripts est disponible. Pour supprimer des Crochets, vous pouvez cliquer avec le bouton droit de la souris sur l'expression qu'ils contiennent et sélectionner l'option Supprimer les caractères englobants ou Supprimer les caractères et séparateurs englobants dans le menu. Si l'expression contenue dans les Crochets comprend plus d'un argument, vous pouvez cliquer avec le bouton droit de la souris sur l'argument que vous voulez supprimer et sélectionner l'option Supprimer l'argument dans le menu. Si les Crochets contiennent plus d'une équation (c'est-à-dire des Cas avec plusieurs conditions), vous pouvez cliquer avec le bouton droit sur l'équation que vous souhaitez supprimer et sélectionner l'option Supprimer l'équation dans le menu. Cette option est également disponible pour d'autres types d'équations si vous avez ajouté de nouveaux espaces réservés en appuyant sur Entrée. Pour supprimer une Limite, vous pouvez faire un clic droit dessus et sélectionner l'option Supprimer limite dans le menu. Pour supprimer une Accentuation, vous pouvez cliquer avec le bouton droit de la souris et sélectionner l'option Supprimer le caractère d'accentuation, Supprimer le caractère ou Supprimer la barre dans le menu (les options disponibles varient en fonction de l'accent sélectionné). Pour supprimer une ligne ou une colonne d'une Matrice, vous pouvez cliquer avec le bouton droit de la souris sur l'espace réservé dans la ligne/colonne à supprimer, sélectionner l'option Supprimer dans le menu, puis sélectionner Supprimer la ligne/Colonne. Convertir des équations Si votre document comporte des équations qu'on a créé avec des versions antérieures de l'éditeur d'équations (par ex. des versions de MS Office lancées avant 2007), il vous faut les convertir au format Office Math ML pour avoir la possibilité de les modifier. Pour convertir une équation, faites un clic double dessus. Un message d'avertissement va apparaître. Pour convertir uniquement la équation sélectionnée, cliquez sur Oui dans le message d'avertissement. Pour convertir toutes équations du document, activez l'option Appliquer à toutes les équations et cliquez sur Oui. Une fois l'équation convertie, vous pouvez la modifier."
    },
   {
        "id": "UsageInstructions/InsertFootnotes.htm", 
        "title": "Insérer des notes de bas de page", 
        "body": "Dans l'Éditeur de Documents, utilisez les notes de bas de page pour donner des explications ou ajouter des commentaires à un terme ou une proposition et citer une source. Insérer des notes de bas de page Pour insérer une note de bas de page dans votre document, placez un point d'insertion à la fin du texte ou du mot concerné, passez à l'onglet Références dans la barre d'outils en haut, cliquez sur l'icône les Notes de bas de page dans la barre d'outils en haut ou appuyez sur la flèche à côté de l'icône Notes de bas de page et sélectionnez l'option Insérer une note de bas de page du menu, Le symbole de la note de bas de page est alors ajouté dans le corps de texte (symbole en exposant qui indique la note de bas de page), et le point d'insertion se déplace à la fin de document. Vous pouvez saisir votre texte. Il faut suivre la même procédure pour insérer une note de bas de page suivante sur un autre fragment du texte. La numérotation des notes de fin est appliquée automatiquement. Affichage des notes de fin dans le document Faites glisser le curseur au symbole de la note de bas de page dans le texte du document, la note de bas de page s'affiche dans une petite fenêtre contextuelle. Parcourir les notes de bas de page Vous pouvez facilement passer d'une note de bas de page à une autre dans votre document, cliquez sur la flèche à côté de l'icône Note de bas de page dans l'onglet Références dans la barre d'outils en haut, dans la section Accéder aux notes de bas de page utilisez la flèche gauche pour se déplacer à la note de bas de page suivante ou la flèche droite pour se déplacer à la note de bas de page précédente. Modification des notes de bas de page Pour particulariser les notes de bas de page, cliquez sur la flèche à côté de l'icône Note de bas de page dans l'onglet Références dans la barre d'outils en haut, appuyez sur Paramètres des notes dans le menu, modifiez les paramètres dans la boîte de dialogue Paramètres des notes qui va apparaître : Cochez la casse Note de bas de page pour modifier seulement les notes de bas de page. Spécifiez l'Emplacement des notes de bas de page et sélectionnez l'une des options dans la liste déroulante à droite : Bas de page - les notes de bas de page sont placées au bas de la page (cette option est activée par défaut). Sous le texte - les notes de bas de page sont placées près du texte. Cette fonction est utile si le texte sur une page est court. Modifiez le Format des notes de bas de page : Format de nombre- sélectionnez le format de nombre disponible dans la liste : 1, 2, 3,..., a, b, c,..., A, B, C,..., i, ii, iii,..., I, II, III,.... Début- utilisez les flèches pour spécifier le nombre ou la lettre à utiliser pour la première note de fin. Numérotation- sélectionnez les options de numérotation des notes de bas de page : Continue- numérotation de manière séquentielle dans le document, À chaque section- numérotation redémarre à 1 (ou à une autre séquence de symboles) au début de chaque section du document, À chaque page- numérotation redémarre à 1 (ou à une autre séquence de symboles) au début de chaque page du document. Marque personalisée - séquence de symboles ou mots spéciaux à utiliser comme symbole de note de bas de page (Exemple : * ou Note1). Tapez le symbole/mot dans le champ et cliquez sur Insérer en bas de la boîte dialogue Paramètres des notes. Dans la liste déroulante Appliquer les modifications à sélectionnez si vous voulez appliquer les modifications À tout le document ou seulement À cette section. Remarque : pour définir un format différent pour les notes de fin sur différentes sections du document, il faut tout d'abord utiliser les sauts de sections. Une fois que vous avez terminé, cliquez sur Appliquer. Supprimer les notes de bas de page Pour supprimer une note de bas de page, placez un point d'insertion devant le symbole de note de bas de page dans le texte et cliquez sur la touche Supprimer. Toutes les autres notes de bas de page sont renumérotées. Pour supprimer toutes les notes de bas de page du document, cliquez sur la flèche à côté de l'icône Note de bas de page dans l'onglet Références dans la barre d'outils en haut, sélectionnez Supprimer les notes dans le menu. dans la boîte de dialogue sélectionnez Supprimer les notes de bas de page et cliquez sur OK."
    },
   {
        "id": "UsageInstructions/InsertHeadersFooters.htm", 
        "title": "Insérer les en-têtes et pieds de page", 
        "body": "Insérer les en-têtes et les pieds de page Pour ajouter ou supprimer un en-tête ou un pied de page à votre document ou modifier ceux qui déjà existent dans l'Éditeur de Documents, passez à l'onglet Insérer de la barre d'outils supérieure, cliquez sur l'icône En-tête/Pied de page sur la barre d'outils supérieure, sélectionnez l'une des options suivantes : Modifier l'en-tête pour insérer ou modifier le texte d'en-tête. Modifier le pied de page pour insérer ou modifier le texte de pied de page. Supprimer l'en-tête pour supprimer un en-tête. Supprimer le pied de page pour supprimer un pied de page. modifiez les paramètres actuels pour les en-têtes ou les pieds de page sur la barre latérale droite : Définissez la Position du texte par rapport à la partie supérieure pour les en-têtes ou à la partie inférieure pour pieds de la page. Cochez la case Première page différente pour appliquer un en-tête ou un pied de page différent pour la première page ou si vous ne voulez pas ajouter un en-tête / un pied de page. Utilisez la case Pages paires et impaires différentes pour ajouter de différents en-têtes ou pieds de page pour les pages paires et impaires. L'option Lier au précédent est disponible si vous avez déjà ajouté des sections dans votre document. Sinon, elle sera grisée. En outre, cette option est non disponible pour toute première section (c'est-à-dire quand un en-tête ou un pied qui appartient à la première section est choisi). Par défaut, cette case est cochée, alors que les mêmes en-têtes/pieds de page sont appliqués à toutes les sections. Si vous sélectionnez une zone d'en-tête ou de pied de page, vous verrez que cette zone est marquée par l'étiquette Identique au précédent. Décochez la case Lien vers précédent pour utiliser de différents en-têtes et pieds de page pour chaque section du document. L'étiquette Identique au précédent ne sera plus affichée. Pour saisir un texte ou modifier le texte déjà saisi et régler les paramètres de l'en-tête ou du pied de page, vous pouvez également double-cliquer sur la partie supérieure ou inférieure de la page ou cliquer avec le bouton droit de la souris et sélectionner l'option - Modifier l'en-tête ou Modifier le pied de page du menu contextuel. Pour passer au corps du document, double-cliquez sur la zone de travail. Le texte que vous utilisez dans l'en-tête ou dans le pied de page sera affiché en gris. Remarque : consultez la section Insérer les numéros de page pour apprendre à ajouter des numéros de page à votre document."
    },
   {
        "id": "UsageInstructions/InsertImages.htm", 
        "title": "Insérer des images", 
        "body": "Éditeur de Documents vous permet d'insérer des images aux formats populaires. Les formats d'image pris en charge sont les suivants : BMP, GIF, JPEG, JPG, PNG. Insérer une image Pour insérer une image dans votre document de texte, placez le curseur là où vous voulez insérer l'image, passez à l'onglet Insérer de la barre d'outils supérieure, cliquez sur l'icône Image de la barre d'outils supérieure, sélectionnez l'une des options suivantes pour charger l'image : l'option Image à partir d'un fichier ouvre la fenêtre de dialogue standard pour sélectionner le fichier. Sélectionnez le fichier de votre choix sur le disque dur de votre ordinateur et cliquez sur le bouton Ouvrir Dans l’éditeur en ligne, vous pouvez sélectionner plusieurs images à la fois. l'option Image à partir d'une URL ouvre la fenêtre où vous pouvez saisir l'adresse Web de l'image et cliquer sur le bouton OK l'option Image de stockage ouvrira la fenêtre Sélectionner la source de données. Sélectionnez une image stockée sur votre portail et cliquez sur le bouton OK après avoir ajouté l'image, vous pouvez modifier sa taille, ses paramètres et sa position. On peut aussi ajouter une légende à l'image. Veuillez consulter cet article pour en savoir plus sur utilisation des légendes. Déplacer et redimensionner des images Pour changer la taille de l'image, faites glisser les petits carreaux situés sur ses bords. Pour garder les proportions de l'image sélectionnée lors du redimensionnement, maintenez la touche Maj enfoncée et faites glisser l'une des icônes de coin. Pour modifier la position de l'image, utilisez l'icône qui apparaît si vous placez le curseur de votre souris sur l'image. Faites glisser l'image vers la position choisie sans relâcher le bouton de la souris. Lorsque vous déplacez l'image, des lignes de guidage s'affichent pour vous aider à la positionner sur la page avec précision (si un style d'habillage autre que aligné est sélectionné). Pour faire pivoter une image, déplacer le curseur vers la poignée de rotation et faites la glisser dans le sens horaire ou antihoraire. Pour faire pivoter par incréments de 15 degrés, maintenez enfoncée la touche Maj tout en faisant pivoter. Remarque : la liste des raccourcis clavier qui peuvent être utilisés lorsque vous travaillez avec des objets est disponible ici. Ajuster les paramètres de l'image Certains paramètres de l'image peuvent être modifiés en utilisant l'onglet Paramètres de l'image de la barre latérale droite. Pour l'activer, cliquez sur l'image et sélectionnez l'icône Paramètres de l'image à droite. Vous pouvez y modifier les paramètres suivants : Taille est utilisée pour afficher la Largeur et la Hauteur de l'image actuel. Si nécessaire, vous pouvez restaurer la taille d'origine de l'image en cliquant sur le bouton Taille actuelle. Le bouton Ajuster aux marges permet de redimensionner l'image et de l'ajuster dans les marges gauche et droite. Le bouton Rogner sert à recadrer l'image. Cliquez sur le bouton Rogner pour activer les poignées de recadrage qui appairaient par chaque coin et sur les côtés. Faites glisser manuellement les pognées pour définir la zone de recadrage. Vous pouvez positionner le curseur sur la bordure de la zone de recadrage lorsque il se transforme en et faites la glisser. Pour rogner un seul côté, faites glisser la poignée située au milieu de ce côté. Pour rogner simultanément deux côtés adjacents, faites glisser l'une des poignées d'angle. Pour rogner également deux côtés opposés de l'image, maintenez la touche Ctrl enfoncée lorsque vous faites glisser la poignée au milieu de l'un de ces côtés. Pour rogner également tous les côtés de l'image, maintenez la touche Ctrl enfoncée lorsque vous faites glisser l'une des poignées d'angle. Lorsque la zone de recadrage est définie, cliquez à nouveau sur le bouton Rogner, ou appuyez sur la touche Echap, ou cliquez n'importe où à l'extérieur de la zone de recadrage pour appliquer les modifications. Une fois la zone de recadrage sélectionnée, il est également possible d'utiliser les options Rogner à la forme, Remplir ou Ajuster disponibles dans le menu déroulant Rogner. Cliquez de nouveau sur le bouton Rogner et sélectionnez l'option de votre choix : Si vous sélectionnez l'option Rogner à la forme, l'image va s'ajuster à une certaine forme. Vous pouvez sélectionner la forme appropriée dans la galerie qui s'affiche lorsque vous placez le poiunteur de la soiris sur l'option Rogner à la forme. Vous pouvez toujours utiliser les options Remplir et Ajuster pour choisir le façon d'ajuster votre image à la forme. Si vous sélectionnez l'option Remplir, la partie centrale de l'image originale sera conservée et utilisée pour remplir la zone de cadrage sélectionnée, tandis que les autres parties de l'image seront supprimées. Si vous sélectionnez l'option Ajuster, l'image sera redimensionnée pour correspondre à la hauteur ou à la largeur de la zone de recadrage. Aucune partie de l'image originale ne sera supprimée, mais des espaces vides peuvent apparaître dans la zone de recadrage sélectionnée. Rotation permet de faire pivoter l'image de 90 degrés dans le sens des aiguilles d'une montre ou dans le sens inverse des aiguilles d'une montre, ainsi que de retourner l'image horizontalement ou verticalement. Cliquez sur l'un des boutons : pour faire pivoter l'image de 90 degrés dans le sens inverse des aiguilles d'une montre pour faire pivoter l'image de 90 degrés dans le sens des aiguilles d'une montre pour retourner l'image horizontalement (de gauche à droite) pour retourner l'image verticalement (à l'envers) Style d'habillage sert à sélectionner un des styles d'habillage - aligné sur le texte, carré, rapproché, au travers, haut et bas, devant le texte, derrière le texte (pour en savoir plus, consultez la section des paramètres avancés ci-dessous). Remplacer l'image sert à remplacer l'image actuelle et charger une autre À partir d'un fichier ou À partir d'une URL. Certains paramètres de l'image peuvent être également modifiés en utilisant le menu contextuel. Les options du menu sont les suivantes : Couper, Copier, Coller - les options nécessaires pour couper ou coller le texte / l'objet sélectionné et coller un passage de texte précédemment coupé / copié ou un objet à la position actuelle du curseur. Organiser sert à placer l'image sélectionnée au premier plan, envoyer à fond, avancer ou reculer ainsi que grouper ou dégrouper des images pour effectuer des opérations avec plusieurs images à la fois. Pour en savoir plus sur l'organisation des objets, vous pouvez vous référer à cette page. Aligner sert à aligner le texte à gauche, au centre, à droite, en haut, au milieu, en bas. Pour en savoir plus sur l'organisation des objets, vous pouvez vous référer à cette page. Style d'habillage sert à sélectionner un des styles d'habillage - aligné sur le texte, carré, rapproché, au travers, haut et bas, devant le texte, derrière le texte - ou modifier le contour de l'habillage. L'option Modifier les limites du renvoi à la ligne n'est disponible qu'au cas où vous sélectionnez le style d'habillage autre que 'aligné sur le texte'. Faites glisser les points d'habillage pour personnaliser les limites. Pour créer un nouveau point d'habillage, cliquez sur la ligne rouge et faites-la glisser vers la position désirée. Faire pivoter permet de faire pivoter l'image de 90 degrés dans le sens des aiguilles d'une montre ou dans le sens inverse des aiguilles d'une montre, ainsi que de retourner l'image horizontalement ou verticalement. Rogner est utilisé pour appliquer l'une des options de rognage : Rogner, Remplir ou Ajuster. Sélectionnez l'option Rogner dans le sous-menu, puis faites glisser les poignées de recadrage pour définir la zone de rognage, puis cliquez à nouveau sur l'une de ces trois options dans le sous-menu pour appliquer les modifications. Taille actuelle sert à changer la taille actuelle de l'image et rétablir la taille d'origine. Remplacer l'image sert à remplacer l'image actuelle et charger une autre À partir d'un fichier ou À partir d'une URL. Paramètres avancés de l'image sert à ouvrir la fenêtre 'Image - Paramètres avancés'. Lorsque l'image est sélectionnée, l'icône Paramètres de la forme est également disponible sur la droite. Vous pouvez cliquer sur cette icône pour ouvrir l'onglet Paramètres de la forme dans la barre latérale droite et ajuster le type du Ligne a taille et la couleur ainsi que le type de forme en sélectionnant une autre forme dans le menu Modifier la forme automatique. La forme de l'image changera en conséquence. Sous l'onglet Paramètres de la forme, vous pouvez utiliser l'option Ajouter une ombre pour créer une zone ombrée autour de l'image. Ajuster les paramètres de l'image Pour modifier les paramètres avancés, cliquez sur l'image avec le bouton droit de la souris et sélectionnez Paramètres avancés de l'image du menu contextuel ou cliquez sur le lien de la barre latérale droite Afficher les paramètres avancés. La fenêtre paramètres de l'image s'ouvre : L'onglet Taille comporte les paramètres suivants : Largeur et Hauteur - utilisez ces options pour changer la largeur et/ou la hauteur. Lorsque le bouton Proportions constantes est activé (Ajouter un ombre) ), le rapport largeur/hauteur d'origine s'ajuste proportionnellement. Pour rétablir la taille par défaut de l'image ajoutée, cliquez sur le bouton Taille actuelle. L'onglet Rotation comporte les paramètres suivants : Angle - utilisez cette option pour faire pivoter l'image d'un angle exactement spécifié. Entrez la valeur souhaitée mesurée en degrés dans le champ ou réglez-la à l'aide des flèches situées à droite. Retourné - cochez la case Horizontalement pour retourner l'image horizontalement (de gauche à droite) ou la case Verticalement pour retourner l'image verticalement (à l'envers). L'onglet Habillage du texte contient les paramètres suivants : Style d'habillage - utilisez cette option pour changer la manière dont l'image est positionnée par rapport au texte : elle peut faire partie du texte (si vous sélectionnez le style 'aligné sur le texte') ou être contournée par le texte de tous les côtés (si vous sélectionnez l'un des autres styles). En ligne - l'image fait partie du texte, comme un caractère, ainsi si le texte est déplacé, le graphique est déplacé lui aussi. Dans ce cas-là les options de position ne sont pas accessibles. Si vous sélectionnez un des styles suivants, vous pouvez déplacer l'image indépendamment du texte et définir sa position exacte : Carré - le texte est ajusté autour des bords de l'image. Rapproché - le texte est ajusté sur le contour de l'image. Au travers - le texte est ajusté autour des bords du graphique et occupe l'espace vide à l'intérieur de celui-ci. Pour créer l'effet, utilisez l'option Modifier les limites du renvoi à la ligne du menu contextuel. Haut et bas - le texte est ajusté en haut et en bas de l'image. Devant le texte - l'image est affichée sur le texte Derrière le texte - le texte est affiché sur l'image. Si vous avez choisi le style carré, rapproché, au travers, haut et bas, vous avez la possibilité de configurer des paramètres supplémentaires - Distance du texte de tous les côtés (haut, bas, gauche, droit). L'onglet Position n'est disponible qu'au cas où vous choisissez le style d'habillage autre que 'aligné sur le texte'. Il contient les paramètres suivants qui varient selon le type d'habillage sélectionné : La section Horizontal vous permet de sélectionner l'un des trois types de positionnement d'image suivants : Alignement (gauche, centre, droite) par rapport au caractère, à la colonne, à la marge de gauche, à la marge, à la page ou à la marge de droite, Position absolue mesurée en unités absolues, c'est-à-dire Centimètres/Points/Pouces (selon l'option spécifiée dans l'onglet Fichier -> Paramètres avancés...) à droite du caractère, de la colonne, de la marge de gauche, de la marge, de la page ou de la marge de droite, Position relative mesurée en pourcentage par rapport à la marge gauche, à la marge, à la page ou à la marge de droite. La section Vertical vous permet de sélectionner l'un des trois types de positionnement d'image suivants : Alignement (haut, centre, bas) par rapport à la ligne, à la marge, à la marge inférieure, au paragraphe, à la page ou à la marge supérieure, Position absolue mesurée en unités absolues, c'est-à-dire Centimètres/Points/Pouces (selon l'option spécifiée dans l'onglet Fichier -> Paramètres avancés...) au-dessous de la ligne, de la marge, de la marge inférieure, du paragraphe, de la page ou la marge supérieure, Position relative mesurée en pourcentage par rapport à la marge, à la marge inférieure, à la page ou à la marge supérieure. Déplacer avec le texte détermine si l'image se déplace en même temps que le texte sur lequel il est aligné. Chevauchement détermine si deux images sont fusionnées en une seule ou se chevauchent si vous les faites glisser les unes près des autres sur la page. L'onglet Texte de remplacement permet de spécifier un Titre et une Description qui sera lue aux personnes avec des déficiences cognitives ou visuelles pour les aider à mieux comprendre l'information de l'image."
    },
   {
        "id": "UsageInstructions/InsertLineNumbers.htm", 
        "title": "Insérer des numéros de ligne", 
        "body": "Éditeur de documents ONLYOFFICE peut automatiquement compter les lignes dans votre document. Cette fonction est utile lorsque vous devez faire référence à des lignes spécifiques dans un document, comme un contrat juridique ou un code de script. Pour ajouter la numérotation de ligne dans un document, utiliser la fonction Numéros de ligne. Veuillez noter que le texte ajouté aux objets tels que les tableaux, les zones de texte, les graphiques, les en-têtes/pieds de page etc n'est pas inclus dans la numérotation consécutive. Tous ces objets comptent pour une ligne. Ajouter des numéros de ligne Accédez à l'onglet Disposition dans la barre d'outils en haut et appuyez sur l'icône Numéros de ligne. Pour une configuration rapide, sélectionnez les paramètres appropriés dans la liste déroulante : Continue - pour une numérotation consécutive dans l'ensemble du document. Restaurer chaque page - pour commencer la numérotation consécutive sur chaque page. Restaurer chaque section - pour commencer la numérotation consécutive sur chaque section du document. Veuillez consulter ce guide pour en savoir plus sur le sauts de sections. Supprimer pour le paragraphe actif - pour supprimer la numérotation de ligne du paragraphe actuel. Cliquez avec le bouton gauche de la souris et sélectionnez les paragraphes pour lesquels on veut supprimer les numéros de ligne avant d'opter pour cette option. Configurez les paramètres avancés si vous en avez besoin. Cliquez sur Paramètres de la numérotation de ligne dans le menu déroulant Numéros de ligne. Cochez la case Ajouter la numérotation des lignes pour ajouter des numéros de ligne au document et pour accéder aux paramètres avancées : Commencer par - spécifiez à quelle ligne la numérotation doit commencer. Par défaut, il démarre à 1. À partir du texte - saisissez les valeurs de l'espace situé entre le texte et le numéro de ligne. Les unités de distance sont affichées en cm. Le paramètre par défaut est Auto. Compter par - spécifiez la valeur de variable si celle-ci n' augmente pas à 1, c'est-à-dire numéroter chaque ligne ou seulement une sur deux, une sur 3, une sur 4, une sur 5 etc. Saisissez une valeur numérique. Par défaut, il démarre à 1. Restaurer chaque page - pour commencer la numérotation consécutive sur chaque page. Restaurer chaque section - pour commencer la numérotation consécutive sur chaque section du document. Continue - pour une numérotation consécutive dans l'ensemble du document. Dans la liste déroulante Appliquer les modifications à sélectionnez la partie du document à laquelle vous souhaitez ajouter des numéros de lignes. Choisissez parmi les options suivantes : À cette section pour ajouter des numéros de ligne à la section du document spécifiée ; A partir de ce point pour ajouter des numéros de ligne à partir du point où votre curseur est placé ; A tout le document pour ajouter des numéros de ligne à tout ce document. Par défaut, le paramètre est A tout le document. Cliquez sur OK pour appliquer les modifications. Supprimer les numéros de ligne Pour supprimer les numéros de ligne, accédez à l'onglet Disposition dans la barre d'outils en haut et appuyez sur l'icône Numéros de ligne. sélectionnez l'option Aucune dans le menu déroulant ou appuyez sur Paramètres de numérotation des lignes et décochez la case Ajouter la numérotation de ligne dans la boîte de dialogue Numéros de ligne."
    },
   {
        "id": "UsageInstructions/InsertPageNumbers.htm", 
        "title": "Insérer les numéros de page", 
        "body": "Pour insérer des numéros de page dans l'Éditeur de Documents,, passez à l'onglet Insérer de la barre d'outils supérieure, cliquez sur l'icône En-tête/Pied de page sur la barre d'outils supérieure, sélectionnez l'option Insérer le numéro de page du sous-menu, sélectionnez l'une des options suivantes : Pour mettre un numéro de page à chaque page de votre document, sélectionnez la position de numéro de page sur la page. Pour insérer un numéro de page à la position actuelle du curseur, sélectionnez l'option À la position actuelle. Remarque : pour insérer le numéro de page courante à la position actuelle du curseur vous pouvez aussi utiliser des raccourcis clavier Ctrl+Shift+P. OU passez à l'onglet Insérer sur la barre d'outils supérieure, cliquez sur l'icône En-tête/Pied de page sur la barre d'outils supérieure, cliquez sur l'option Insérer le nombre de pages dans le menu et choisissez la position du nombre de pages. Pour insérer le nombre total de pages dans votre document (par ex. si vous souhaitez créer une saisie Page X de Y) : placez le curseur où vous souhaitez insérer le nombre total de pages, cliquez sur l'icône En-tête/Pied de page sur la barre d'outils supérieure, sélectionnez l'option Insérer le nombre de pages. Pour modifier les paramètres de la numérotation des pages, double-cliquez sur le numéro de page ajouté, modifiez les paramètres actuels en utilisant la barre latérale droite : Définissez la Position des numéros de page ainsi que la position par rapport à la partie supérieure et inférieure de la page. Cochez la case Première page différente pour appliquer un numéro différent à la première page ou si vous ne voulez pas du tout ajouter le numéro. Utilisez la case Pages paires et impaires différentes pour insérer des numéros de page différents pour les pages paires et impaires. L'option Lier au précédent est disponible si vous avez déjà ajouté des sections dans votre document. Sinon, elle sera grisée. En outre, cette option est non disponible pour toute première section (c'est-à-dire quand un en-tête ou un pied qui appartient à la première section est choisi). Par défaut, cette case est cochée, de sorte que la numérotation unifiée est appliquée à toutes les sections. Si vous sélectionnez une zone d'en-tête ou de pied de page, vous verrez que cette zone est marquée par l'étiquette Identique au précédent. Décochez la case Lier au précédent pour utiliser la numérotation des pages différente pour chaque section du document. L'étiquette Identique au précédent ne sera plus affichée. La section Numérotation des pages sert à configurer les paramètres de numérotation des pages de à travers des différentes sections du document. L'option Continuer à partir de la section précédente est active par défaut pour maintenir la numérotation séquentielle après un saut de section. Si vous voulez que la numérotation commence avec un chiffre ou nombre spécifique sur cette section du document, activez le bouton radio Début et saisissez la valeur initiale dans le champ à droite. Pour retourner à l'édition du document, double-cliquez sur la zone de travail."
    },
   {
        "id": "UsageInstructions/InsertReferences.htm", 
        "title": "Insérer les références", 
        "body": "Éditeur de Documents Insérer les références Éditeur de documents ONLYOFFICE supporte les logiciels de gestion de références bibliographiques Mendeley, Zotero et EasyBib pour insérer des références dans votre document. Mendeley Intégrer Mendeley à ONLYOFFICE Connectez-vous à votre compte personnel Mendeley. Passez à l'onglet Modules complémentaires et choisissez Mendeley, le panneau de gauche s'ouvre dans votre document. Appuyez sur Copier le lien et ouvrir la fiche. Le navigateur ouvre la fiche du site Mendeley. Complétez la fiche et notez l'identifiant de l'application ONLYOFFICE. Revenez à votre document. Entrez l'identifiant de l'application et cliquez sur le bouton Enregistrer. Appuyez sur Me connecter. Appuyez sur Continuer. Or, ONLYOFFICE s'est connecté à votre compte Mendeley. Insertion des références Accédez à votre document et placez le curseur à l'endroit où la référence doit être insérée. Passez à l'onglet Modules complémentaires et choisissez Mendeley. Tapez le texte à chercher et appuyez sur la touche Entrée du clavier. Activez une ou plusieurs cases à cocher. [Facultatif] Tapez le texte nouveau à chercher et activez une ou plusieurs cases à cocher. Sélectionnez le style pour référence de la liste déroulante Style. Cliquez sur le bouton Insérer la bibliographie. Zotero Intégrer Zotero à ONLYOFFICE Connectez-vous à votre compte personnel Zotero. Passez à l'onglet Modules complémentaires de votre document ouvert et choisissez Zotero, le panneau de gauche s'ouvre dans votre document. Cliquez sur le lien Configuration Zotero API. Il faut obtenir une clé sur le site Zotéro, la copier et la conserver en vue d'une utilisation ultérieure. Revenez à votre document et coller la clé API. Appuyez sur Enregistrer. Or, ONLYOFFICE s'est connecté à votre compte Zotero. Insertion des références Accédez à votre document et placez le curseur à l'endroit où la référence doit être insérée. Passez à l'onglet Modules complémentaires et choisissez Zotero. Tapez le texte à chercher et appuyez sur la touche Entrée du clavier. Activez une ou plusieurs cases à cocher. [Facultatif] Tapez le texte nouveau à chercher et activez une ou plusieurs cases à cocher. Sélectionnez le style pour référence de la liste déroulante Style. Cliquez sur le bouton Insérer la bibliographie. EasyBib Accédez à votre document et placez le curseur à l'endroit où la référence doit être insérée. Passez à l'onglet Modules complémentaires et choisissez EasyBib. Sélectionnez le type de source à trouver. Tapez le texte à chercher et appuyez sur la touche Entrée du clavier. Cliquez sur '+' de la partie droite du Livre/Journal, de l'article/site Web. Cette source sera ajoutée dans la Bibliographie. Sélectionnez le style de référence. Appuyez sur Ajouter la bibliographie dans le document pour insérer les références."
    },
   {
        "id": "UsageInstructions/InsertSmartArt.htm", 
        "title": "Insérer des graphiques SmartArt", 
        "body": "Un graphique SmartArt sert à créer une représentation visuelle de la structure hiérarchique en choisissant le type du graphique qui convient le mieux. Insérez des graphiques SmartArt ou modifiez des graphiques SmartArt qui ont été créés dans des logiciels d'éditeurs tiers. Pour ajouter un graphiques SmartArt, passez à l'onglet Insertion, cliquez sur le bouton SmartArt, placez le pointeur sur l'un des styles de mise en page disponibles, par ex. Liste ou Processus. sélectionnez le type de mise en page de la liste qui apparaît à droite de l'option du menu mis en surbrillance. Vous pouvez personnaliser la configuration SmartArt sur le panneau à droite: Veuillez noter qu'il est possible de personnaliser les paramètres de couleur, style et forme. Remplissage - utilisez cette section pour sélectionner le remplissage du graphique SmartArt. Les options disponibles sont les suivantes: Couleur de remplissage - sélectionnez cette option pour spécifier la couleur unie à utiliser pour remplir l'espace intérieur du graphique SmartArt. Cliquez sur la case de couleur et sélectionnez la couleur voulue à partir de l'ensemble de couleurs disponibles ou spécifiez n'importe quelle couleur de votre choix: Remplissage en dégradé - sélectionnez cette option pour spécifier deux couleurs pour créer une transition douce entre elles et remplir la forme. Personnaliser votre dégradé sans aucune contrainte. Cliquez sur l'icône Paramètres de la forme. pour ouvrir le menu Remplissage de la barre latérale sur la droite: Les options disponibles du menu: Style - choisissez Linéaire or Radial: Linéaire sert à remplir par un dégradé de gauche à droite, de bas en haut ou sous l'angle partant en direction définie. La fenêtre d'aperçu Direction affiche la couleur de dégradé sélectionnée, cliquez sur la flèche pour définir la direction du dégradé. Utilisez les paramètres Angle pour définir un angle précis du dégradé. Radial sert à remplir par un dégradé de forme circulaire entre le point de départ et le point d'arrivée. Point de dégradé est le point d'arrêt de d'une couleur et de la transition entre les couleurs. Utilisez le bouton Ajouter un point de dégradé ou le curseur de dégradé pour ajouter un point de dégradé. Vous pouvez ajouter 10 points de dégradé. Le nouveau arrêt de couleur n'affecte pas l'aspect actuel du dégradé. Utilisez le bouton Supprimer un point de dégradé pour supprimer un certain point de dégradé. Faites glisser le curseur de déragé pour changer l'emplacement des points de dégradé ou spécifiez la Position en pourcentage pour l'emplacement plus précis. Pour choisir la couleur au dégradé, cliquez sur l'arrêt concerné sur le curseur de dégradé, ensuite cliquez sur Couleur pour sélectionner la couleur appropriée. Image ou Texture - sélectionnez cette option pour utiliser une image ou une texture prédéfinie en tant que l'arrière-plan du graphique SmartArt. Si vous souhaitez utiliser une image en tant que l'arrière-plan du graphique SmartArt, vous pouvez ajouter une image D'un fichier en la sélectionnant sur le disque dur de votre ordinateur ou D'une URL en insérant l'adresse URL appropriée dans la fenêtre ouverte, ou À partir de l'espace de stockage en sélectionnant l'image nécessaire sur votre portail. Si vous souhaitez utiliser une texture en tant que arrière-plan du graphique SmartArt, utilisez le menu déroulant D'une texture et sélectionnez le préréglage de la texture nécessaire. Actuellement, les textures suivantes sont disponibles: Toile, Carton, Tissu foncé, Grain, Granit, Papier gris, Tricot, Cuir, Papier brun, Papyrus, Bois. Si l'image sélectionnée est plus grande ou plus petite que le graphique SmartArt, vous pouvez utiliser l'une des options Prolonger ouTuile depuis la liste déroulante. L'option Prolonger permet de régler la taille de l'image pour l'adapter à la taille de la forme automatique afin qu'elle puisse remplir tout l'espace uniformément. L'option Tuile permet d'afficher seulement une partie de l'image plus grande en gardant ses dimensions d'origine, ou de répéter l'image plus petite en conservant ses dimensions initiales sur la surface du graphique SmartArt afin qu'elle puisse remplir tout l'espace uniformément. Remarque: tout préréglage Texture sélectionné remplit l'espace de façon uniforme, mais vous pouvez toujours appliquer l'effet Prolonger, si nécessaire. Modèle - sélectionnez cette option pour sélectionner le modèle à deux couleurs composé des éléments répétés pour remplir l'espace intérieur du graphique SmartArt. Modèle - sélectionnez un des modèles prédéfinis du menu. Couleur de premier plan - cliquez sur cette palette de couleurs pour changer la couleur des éléments du modèle. Couleur d'arrière-plan - cliquez sur cette palette de couleurs pour changer de l'arrière-plan du modèle. Pas de remplissage - sélectionnez cette option si vous ne voulez pas utiliser un remplissage. Trait - sert à régler la taille, la couleur et le type du contour du graphique SmartArt. Pour modifier la largeur du trait, sélectionnez une des options disponibles depuis la liste déroulante Taille. Les options disponibles sont les suivantes: 0,5 pt, 1 pt, 1,5 pt, 2,25 pt, 3 pt, 4,5 pt, 6 pt ou Pas de ligne si vous ne voulez pas utiliser de trait. Pour changer la couleur du contour, cliquez sur la case colorée et sélectionnez la couleur voulue. Pour modifier le type de contour, sélectionnez l'option voulue dans la liste déroulante correspondante (une ligne continue est appliquée par défaut, vous pouvez la remplacer par l'une des lignes pointillées disponibles). Style d'habillage sert à sélectionner un des styles d'habillage - aligné sur le texte, carré, rapproché, au travers, haut et bas, devant le texte, derrière le texte (pour en savoir plus, consultez la section des paramètres avancés ci-dessous). Ajouter une ombre activez cette option pour ajouter une ombre portée à un graphique SmartArt. Cliquez sur Afficher les paramètres avancés pour accéder aux paramètres avancés."
    },
   {
        "id": "UsageInstructions/InsertSymbols.htm", 
        "title": "Insérer des symboles et des caractères", 
        "body": "Pour insérer des symboles qui ne sont pas sur votre clavier dans l'Éditeur de Documents, utilisez l'option Insérer un symbole option et suivez ces étapes simples : placez le curseur là où vous souhaitez ajouter un symbole spécifique, passez à l'onglet Insertion de la barre d'outils supérieure, cliquez sur Symbole, De la fenêtre Symbole qui apparaît sélectionnez le symbole approprié, Utilisez la section Plage pour trouvez rapidement le symbole nécessaire. Tous les symboles sont divisés en groupes spécifiques, par exemple, sélectionnez des «symboles monétaires» spécifiques si vous souhaitez insérer un caractère monétaire. Si ce caractère n'est pas dans le jeu, sélectionnez une police différente. Plusieurs d'entre elles ont également des caractères différents que ceux du jeu standard. Ou, entrez la valeur hexadécimale Unicode du symbole souhaité dans le champ Valeur hexadécimale Unicode. Ce code se trouve dans la Carte des caractères. Vous pouvez aussi utiliser l'onglet Symboles spéciaux pour choisir un symbole spéciale proposé dans la liste. Les symboles précédemment utilisés sont également affichés dans le champ des Caractères spéciaux récemment utilisés, cliquez sur Insérer. Le caractère sélectionné sera ajouté au document. Insérer des symboles ASCII La table ASCII est utilisée pour ajouter des caractères. Pour le faire, maintenez la touche ALT enfoncée et utilisez le pavé numérique pour saisir le code de caractère. Remarque : utilisez le pavé numérique, pas les chiffres du clavier principal. Pour activer le pavé numérique, appuyez sur la touche verrouillage numérique. Par exemple, pour ajouter un caractère de paragraphe (§), maintenez la touche ALT tout en tapant 789, puis relâchez la touche ALT. Insérer des symboles à l'aide de la table des caractères Unicode Des caractères et symboles supplémentaires peuvent également être trouvés dans la table des symboles Windows. Pour ouvrir cette table, effectuez l'une des opérations suivantes : Dans le champ Rechercher, tapez 'Table de caractères' et ouvrez-la, Appuyez simultanément sur Win+R, puis dans la fenêtre ouverte tapez charmap.exe et cliquez sur OK. Dans la Table des caractères ouverte, sélectionnez l'un des Jeux de caractères, Groupes et Polices. Ensuite, cliquez sur le caractère nécessaire, copiez-les dans le presse-papier et collez-les au bon endroit du document."
    },
   {
        "id": "UsageInstructions/InsertTables.htm", 
        "title": "Insérer des tableaux", 
        "body": "Insérer un tableau Pour insérer un tableau dans le texte de votre document dans l'Éditeur de Documents, placez le curseur à l'endroit où vous voulez insérer le tableau, passez à l'onglet Insertion de la barre d'outils supérieure, cliquez sur l'icône Tableau sur la la barre d'outils supérieure, sélectionnez une des options pour créer le tableau : soit un tableau avec le nombre prédéfini de cellules (10 par 8 cellules maximum) Si vous voulez ajouter rapidement un tableau, il vous suffit de sélectionner le nombre de lignes (8 au maximum) et de colonnes (10 au maximum). soit un tableau personnalisé Si vous avez besoin d'un tableau de plus de 10 par 8 cellules, sélectionnez l'option Insérer un tableau personnalisé pour ouvrir la fenêtre et spécifiez le nombre nécessaire de lignes et de colonnes, ensuite cliquez sur le bouton OK. Sélectionnez l'option Dessiner un tableau, si vous souhaitez dessiner un tableau à la souris. Cette option est utile lorsque vous devez créer un tableau et délimiter des lignes et des colonnes de tailles différentes. Le pointeur de la souris se transforme en crayon . Tracez le contour du tableau où vous souhaitez l'ajouter, puis tracez les traits horizontaux pour délimiter des lignes et les traits verticaux pour délimiter des colonnes à l'intérieur du contour. Si vous souhaitez convertir du texte en tableau, sélectionnez l'option Convertir un texte en tableau. Cette fonctionnalité peut ce rendre utile lorsque vous avez déjà saisi du texte et il faut l'organiser en tableau. La fenêtre Convertir un texte en tableau comporte 3 sections : Taille du tableau. Définissez le nombre de colonnes/lignes à créer. Pour ce faire, utilisez les flèches vers le haut et le bas ou servez-vous du clavier pour saisir le nombre manuellement. Comportement de l'ajustement automatique. Sélectionnez l'option appropriée pour définir comment l'ajustement du texte doit être fait : Largeur de colonne fixe (la veleur est défini par défaut sur Auto. Utilisez les flèches vers le haut et le bas ou servez-vous du clavier pour saisir le nombre enter manuellement), Ajuster au contenu (la largeur des colonnes correspond aux longueurs des textes), Ajuster à la fenêtre (la largeur des colonnes correspond à la largeur de la page). Séparer le texte au niveau des. Sélectionnez l'option appropriée pour définir le caractère qui va indiquer où générer chaque colonne : Paragraphes, Tabulation, Points-virgules et Autre (saisissez le caractère de séparation souhaité manuellement). Cliquez sur OK pour convertir le texte en tableau. Lorsque vous voulez insérer un tableau comme un objet OLE : Sélectionnez l'option Insérer la feuille de calcul. La fenêtre correspondante s'ouvre dans laquelle vous pouvez saisir les données requises et les modifier en utilisant les outils de formatage du Tableur, par exemple sélectionner la police, le type et le style, saisir le format de nombre, insérer des fonctions, mettre en forme les tableaux etc. L'en-tête contient le bouton Zone visible dans le coin supérieur droit de la fenêtre. Choisissez l'option Modifier la zone visible afin de sélectionner la zone qui sera affichée quand l'objet est inseré au sein du document ; les autres données ne sont pas perdues mais seulement masquées. Cliquez sur Terminé lorsque c'est prêt. Cliquez sur le bouton Afficher la zone visible afin d'afficher la zone sélectionnée qui aura une bordure bleue. Quand tout est prêt, cliquez sur le bouton Enregistrer et quitter. après avoir ajouté le tableau, vous pouvez modifier sa taille, ses paramètres et sa position. Pour redimensionner un tableau, placez le curseur de la souris sur la poignée dans son coin inférieur droit et faites-la glisser jusqu'à ce que la taille du tableau soit atteinte. Vous pouvez également modifier manuellement la largeur d'une certaine colonne ou la hauteur d'une ligne. Déplacez le curseur de la souris sur la bordure droite de la colonne de sorte que le curseur se transforme en flèche bidirectionnelle et faites glisser la bordure vers la gauche ou la droite pour définir la largeur nécessaire. Pour modifier manuellement la hauteur d'une seule ligne, déplacez le curseur de la souris sur la bordure inférieure de la ligne afin que le curseur devienne la flèche bidirectionnelle et déplacez la bordure vers le haut ou le bas. Pour déplacer une table, maintenez la poignée dans son coin supérieur gauche et faites-la glisser à l'endroit voulu dans le document. On peut aussi ajouter une légende au tableau. Veuillez consulter cet article pour en savoir plus sur utilisation des légendes avec les tableaux. Sélectionnez un tableau ou une portion de tableau Pour sélectionner un tableau entier, cliquez sur la poignée dans son coin supérieur gauche. Pour sélectionner une certaine cellule, déplacez le curseur de la souris sur le côté gauche de la cellule nécessaire pour que le curseur devienne la flèche noire , puis cliquez avec le bouton gauche de la souris. Pour sélectionner une certaine ligne, déplacez le curseur de la souris sur la bordure gauche du tableau à côté de la ligne voulue pour que le curseur devienne la flèche noire horizontale , puis cliquez avec le bouton gauche de la souris. Pour sélectionner une certaine colonne, déplacez le curseur de la souris sur la bordure supérieure de la colonne voulue pour que le curseur se transforme en flèche noire dirigée vers le bas , puis cliquez avec le bouton gauche de la souris. Il est également possible de sélectionner une cellule, une ligne, une colonne ou un tableau à l'aide des options du menu contextuel ou de la section Lignes et colonnes de la barre latérale droite. Remarque : pour vous déplacer dans un tableau, vous pouvez utiliser des raccourcis clavier. Ajuster les paramètres du tableau Certaines paramètres du tableau ainsi que sa structure peuvent être modifiés à l'aide du menu contextuel. Les options du menu sont les suivantes : Couper, Copier, Coller - les options nécessaires pour couper ou coller le texte / l'objet sélectionné et coller un passage de texte précédemment coupé / copié ou un objet à la position actuelle du curseur. Sélectionner sert à sélectionner une ligne, une colonne, une cellule ou un tableau. Insérer sert à insérer une ligne au-dessus ou en dessous de la ligne où le curseur est placé ainsi qu'insérer une colonne à gauche ou à droite de la colonne à la position actuelle du curseur. Il est possible d'insérer plusieurs lignes ou colonnes. Lorsque l'option Plusieurs lignes/colonnes est sélectionnée, la fenêtre Insérer plusieurs apparaît. Sélectionnez Lignes ou Colonnes dans la liste, spécifiez le nombre de colonnes/lignes que vous souhaitez insérer et spécifiez l'endroit où les insérer : Au-dessus du curseur ou Au-dessous du curseur et cliquez sur OK. Supprimer sert à supprimer une ligne, une colonne ou un tableau. Lorsque l'option Cellule est sélectionnée, la fenêtre Supprimer les cellules apparaît où on peut choisir parmi les options : Décaler les cellules vers la gauche, Supprimer la ligne entière ou Supprimer la colonne entière. Fusionner les cellules est disponible si deux ou plusieurs cellules sont sélectionnées et est utilisé pour les fusionner. Il est aussi possible de fusionner les cellules en effaçant la bordure à l'aide de l'outil gomme. Pour le faire, cliquez sur l'icône Tableau dans la barre d'outils supérieure et sélectionnez l'option Supprimer un tableau. Le pointeur de la souris se transforme en gomme . Faites glisser le pointeur de la souris vers la bordure séparant les cellules que vous souhaitez fusionner et effacez-la. Fractionner la cellule... sert à ouvrir la fenêtre où vous pouvez sélectionner le nombre nécessaire de colonnes et de lignes de la cellule qui doit être divisée. Il est aussi possible de fractionner la cellule en dessinant les lignes et les colonnes à l'aide de l'outil crayon. Pour le faire, cliquez sur l'icône Tableau dans la barre d'outils supérieure et sélectionnez l'option Dessiner un tableau. Le pointeur de la souris se transforme en crayon . Tracez un trait horizontal pour délimiter une ligne ou un trait vertical pour délimiter une colonne. Distribuer les lignes est utilisé pour ajuster les cellules sélectionnées afin qu'elles aient la même hauteur sans changer la hauteur globale du tableau. Distribuer les colonnes est utilisé pour ajuster les cellules sélectionnées afin qu'elles aient la même largeur sans modifier la largeur globale du tableau. Alignement vertical de cellule sert à aligner le texte en haut, au centre ou en bas de la cellule sélectionnée. Orientation du texte sert à modifier l'orientation du texte dans une cellule. Vous pouvez placer le texte horizontalement, verticalement de haut en bas (Rotation du texte vers le bas), ou verticalement de bas en haut (Rotation du texte vers le haut). Paramètres avancés du tableau sert à ouvrir la fenêtre 'Tableau - Paramètres avancés'. Lien hypertexte sert à insérer un lien hypertexte. Paramètres avancés du paragraphe sert à ouvrir la fenêtre 'Paragraphe - Paramètres avancés'. Vous pouvez également modifier les propriétés du tableau en utilisant la barre latérale droite : Lignes et Colonnes servent à sélectionner les parties du tableau à mettre en surbrillance. Pour les lignes : En-tête - pour souligner la première ligne Total - pour souligner la dernière ligne À bandes - pour souligner chaque deuxième ligne Pour les colonnes : Premier - pour souligner la première colonne Dernier - pour souligner la dernière colonne À bandes - pour souligner chaque deuxième colonne Sélectionner à partir d'un modèle sert à choisir un modèle de tableau à partir de ceux qui sont disponibles. Style des bordures sert à sélectionner la taille de la bordure, la couleur, le style ainsi que la couleur d'arrière-plan. Lignes et colonnes sert à effectuer certaines opérations avec le tableau : sélectionner, supprimer, insérer des lignes et des colonnes, fusionner des cellules, fractionner une cellule. Taille des lignes et des colonnes est utilisée pour ajuster la largeur et la hauteur de la cellule actuellement sélectionnée. Dans cette section, vous pouvez également Distribuer les lignes afin que toutes les cellules sélectionnées aient la même hauteur ou Distribuer les colonnes de sorte que toutes les cellules sélectionnées aient la même largeur. Ajouter une formule permet d'insérer une formule dans la cellule de tableau sélectionnée. Répéter en haut de chaque page en tant que ligne d'en-tête sert à insérer la même rangée d'en-tête en haut de chaque page dans les tableaux longs. Afficher les paramètres avancés sert à ouvrir la fenêtre 'Tableau - Paramètres avancés'. Configurer les paramètres avancés du tableau Pour modifier les paramètres du tableau avancés, cliquez sur le tableau avec le clic droit de la souris et sélectionnez l'option Paramètres avancés du tableau du menu contextuel ou utilisez le lien Afficher les paramètres avancés sur la barre latérale droite. La fenêtre des paramètres du tableau s'ouvre : L'onglet Tableau permet de modifier les paramètres de tout le tableau. La section Taille du tableau contient les paramètres suivants : Largeur - par défaut, la largeur du tableau est ajustée automatiquement à la largeur de la page, c-à-d le tableau occupe tout l'espace entre la marge gauche et la marge droite de la page. Vous pouvez cocher cette case et spécifier la largeur du tableau manuellement. Mesure en permet de définir la largeur du tableau en unités absolues c-à-d Centimètres/Points/Pouces (selon l'option choisie dans l'onglet Fichier -> Paramètres avancés... tab) ou en Pour cent de la largeur totale de la page. Remarque: vous pouvez aussi ajuster la taille du tableau manuellement en changeant la hauteur des lignes et la largeur des colonnes. Déplacez le curseur de la souris sur la bordure de la ligne/ de la colonne jusqu'à ce qu'elle se transforme en flèche bidirectionnelle et faites glisser la bordure. Vous pouvez aussi utiliser les marqueurs sur la règle horizontale pour changer la largeur de la colonne et les marqueurs sur la règle verticale pour modifier la hauteur de la ligne. Redimensionner automatiquement pour ajuster au contenu - permet de changer automatiquement la largeur de chaque colonne selon le texte dans ses cellules. La section Marges des cellules par défaut permet de changer l'espace entre le texte dans les cellules et la bordure de la cellule utilisé par défaut. La section Options permet de modifier les paramètres suivants : Espacement entre les cellules - l'espacement des cellules qui sera rempli par la couleur de l'Arrière-plan de tableau. L'onglet Cellule permet de modifier les paramètres des cellules individuelles. D'abord vous devez sélectionner la cellule à appliquer les modifications ou sélectionner le tableau à modifier les propriétés de toutes ses cellules. La section Taille de la cellule contient les paramètres suivants : Largeur préférée - permet de définir la largeur de la cellule par défaut. C'est la taille à laquelle la cellule essaie de correspondre, mais dans certains cas ce n'est pas possible se s'adapter à cette valeur exacte. Par exemple, si le texte dans une cellule dépasse la largeur spécifiée, il sera rompu par la ligne suivante de sorte que la largeur de cellule préférée reste intacte, mais si vous insérez une nouvelle colonne, la largeur préférée sera réduite. Mesure en - permet de définir la largeur de la cellule en unités absolues c-à-d Centimètres/Points/Pouces (selon l'option spécifiée dans l'onglet Fichier -> Paramètres avancés...) ou en Pour cent de la largeur totale du tableau. Remarque : vous pouvez aussi définir la largeur de la cellule manuellement. Pour rendre une cellule plus large ou plus étroite dans une colonne, sélectionnez la cellule nécessaire et déplacez le curseur de la souris sur sa bordure droite jusqu'à ce qu'elle se transforme en flèche bidirectionnelle, puis faites glisser la bordure. Pour modifier la largeur de toutes les cellules d'une colonne, utilisez les marqueurs sur la règle horizontale pour modifier la largeur de la colonne. La section Marges de la cellule permet d'ajuster l'espace entre le texte dans les cellules et la bordure de la cellule. Par défaut, les valeurs standard sont utilisées (les valeurs par défaut peuvent être modifiées également en utilisant l'onglet Tableau), mais vous pouvez désactiver l'option Utiliser marges par défaut et entrer les valeurs nécessaires manuellement. La section Option de la cellule permet de modifier le paramètre suivant : L'option Envelopper le texte est activée par défaut. Il permet d'envelopper le texte dans une cellule qui dépasse sa largeur sur la ligne suivante en agrandissant la hauteur de la rangée et en gardant la largeur de la colonne inchangée. L'onglet Bordures et arrière-plan contient les paramètres suivants : Les paramètres de la Bordure (taille, couleur, présence ou absence) - définissez la taille de la bordure, sélectionnez sa couleur et choisissez la façon d'affichage dans les cellules. Remarque : si vous choisissez de ne pas afficher les bordures du tableau en cliquant sur le bouton ou en désélectionnant toutes les bordures manuellement sur le diagramme, les bordures seront indiquées par une ligne pointillée. Pour les faire disparaître, cliquez sur l'icône Caractères non imprimables sur la barre d'outils supérieure et sélectionnez l'option Bordures du tableau cachées. Fond de la cellule - la couleur d'arrière plan dans les cellules (disponible uniquement si une ou plusieurs cellules sont sélectionnées ou l'option Espacement entre les cellules est sélectionnée dans l'onglet Tableau). Fond du tableau - la couleur d'arrière plan du tableau ou le fond de l'espacement entre les cellules dans le cas où l'option Espacement entre les cellules est choisie dans l'onglet Tableau. L'onglet Position du tableau est disponible uniquement si l'option Tableau flottant sous l'onglet Habillage du texte est sélectionnée et contient les paramètres suivants : Horizontal sert à régler l'alignement du tableau par rapport à la marge (à gauche, au centre, à droite), la page ou le texte aussi bien que la position à droite de la marge, la page ou le texte. Vertical sert à régler l'alignement du tableau par rapport à la marge (à gauche, au centre, à droite), la page ou le texte aussi bien que la position en dessous de la marge, la page ou le texte. La section Options permet de modifier les paramètres suivants : Déplacer avec le texte contrôle si la tableau se déplace comme le texte dans lequel il est inséré. Autoriser le chevauchement sert à déterminer si deux tableaux sont fusionnés en un seul grand tableau ou se chevauchent si vous les faites glisser les uns près des autres sur la page. L'onglet Habillage du texte contient les paramètres suivants : Style d'habillage du texte - Tableau aligné ou Tableau flottant. Utilisez l'option voulue pour changer la façon de positionner le tableau par rapport au texte : soit il sera une partie du texte ( si vous avez choisi le style aligné), soit il sera entouré par le texte de tous les côtés (si vous avez choisi le style flottant). Après avoir sélectionné le style d'habillage, les paramètres d'habillage supplémentaires peuvent être définis pour les tableaux alignés et flottants : Pour les tableaux alignés, vous pouvez spécifier l'Alignement du tableau et le Retrait à gauche. Pour les tableaux flottants, vous pouvez spécifier la distance du texte et la position du tableau sous l'onglet Position du tableau. L'onglet Texte de remplacement permet de spécifier un Titre et une Description qui sera lue aux personnes avec des déficiences cognitives ou visuelles pour les aider à mieux comprendre l'information du tableau."
    },
   {
        "id": "UsageInstructions/InsertTextObjects.htm", 
        "title": "Insérer des objets textuels", 
        "body": "Pour rendre votre texte plus explicite et attirer l'attention sur une partie spécifique du document, vous pouvez insérer une zone de texte (un cadre rectangulaire qui permet de saisir du texte) ou un objet Text Art (une zone de texte avec un style de police prédéfini et couleur qui permet d'appliquer certains effets de texte) dans l'Éditeur de Documents. Ajouter un objet textuel Vous pouvez ajouter un objet texte n'importe où sur la page. Pour le faire : passez à l'onglet Insérer de la barre d'outils supérieure, sélectionnez le type d'objet textuel voulu : Pour ajouter une zone de texte, cliquez sur l'icône Zone de texte de la barre d'outils supérieure, puis cliquez sur l'emplacement où vous souhaitez insérer la zone de texte, maintenez le bouton de la souris enfoncé et faites glisser la bordure pour définir sa taille. Lorsque vous relâchez le bouton de la souris, le point d'insertion apparaîtra dans la zone de texte ajoutée, vous permettant d'entrer votre texte. Remarque : il est également possible d'insérer une zone de texte en cliquant sur l'icône Forme dans la barre d'outils supérieure et en sélectionnant la forme dans le groupe Formes de base. Pour ajouter un objet Text Art, cliquez sur l'icône Text Art dans la barre d'outils supérieure, puis cliquez sur le modèle de style souhaité - l'objet Text Art sera ajouté à la position actuelle du curseur. Sélectionnez le texte par défaut dans la zone de texte avec la souris et remplacez-le par votre propre texte. cliquez en dehors de l'objet texte pour appliquer les modifications et revenir au document. Le texte dans l'objet textuel fait partie de celui-ci (ainsi si vous déplacez ou faites pivoter l'objet textuel, le texte change de position lui aussi). Comme un objet textuel inséré représente un cadre rectangulaire (avec des bordures de zone de texte invisibles par défaut) avec du texte à l'intérieur et que ce cadre est une forme automatique commune, vous pouvez modifier aussi bien les propriétés de forme que de texte. Pour supprimer l'objet textuel ajouté, cliquez sur la bordure de la zone de texte et appuyez sur la touche Suppr du clavier. Le texte dans la zone de texte sera également supprimé. Mettre en forme une zone de texte Sélectionnez la zone de texte en cliquant sur sa bordure pour pouvoir modifier ses propriétés. Lorsque la zone de texte est sélectionnée, ses bordures sont affichées en tant que lignes pleines (non pointillées). Pour redimensionner, déplacer, faire pivoter la zone de texte, utilisez les poignées spéciales sur les bords de la forme. Pour modifier le remplissage, le contour, le style d'habillage de la zone de texte ou remplacer la boîte rectangulaire par une forme différente, cliquez sur l'icône Paramètres de forme dans la barre latérale de droite et utilisez les options correspondantes. Pour aligner la zone de texte sur la page, organiser les zones de texte en fonction d'autres objets, pivoter ou retourner une zone de texte, modifier un style d'habillage ou accéder aux paramètres avancés de forme, cliquez avec le bouton droit sur la bordure de zone de texte et utilisez les options du menu contextuel. Pour en savoir plus sur l'organisation et l'alignement des objets, vous pouvez vous référer à cette page. Mettre en forme le texte dans la zone de texte Cliquez sur le texte dans la zone de texte pour pouvoir modifier ses propriétés. Lorsque le texte est sélectionné, les bordures de la zone de texte sont affichées en lignes pointillées. Remarque : il est également possible de modifier le formatage du texte lorsque la zone de texte (et non le texte lui-même) est sélectionnée. Dans ce cas, toutes les modifications seront appliquées à tout le texte dans la zone de texte. Certaines options de mise en forme de police (type de police, taille, couleur et styles de décoration) peuvent être appliquées séparément à une partie du texte précédemment sélectionnée. Pour faire pivoter le texte dans la zone de texte, cliquez avec le bouton droit sur le texte, sélectionnez l'option Direction du texte, puis choisissez l'une des options disponibles : Horizontal (sélectionné par défaut), Rotation du texte vers le bas (définit une direction verticale, de haut en bas) ou Rotation du texte vers le haut (définit une direction verticale, de bas en haut). Pour aligner le texte verticalement dans la zone de texte, cliquez avec le bouton droit sur le texte, sélectionnez l'option Alignement vertical, puis choisissez l'une des options disponibles : Aligner en haut, Aligner au centre ou Aligner en bas. Les autres options de mise en forme que vous pouvez appliquer sont les mêmes que celles du texte standard. Veuillez vous reporter aux sections d'aide correspondantes pour en savoir plus sur l'opération concernée. Vous pouvez : aligner le texte horizontalement dans la zone de texte ajuster le type, la taille, la couleur de police, appliquer des styles de décoration et des préréglages de formatage définir l'interligne, modifier les retraits de paragraphe, ajuster les taquets pour le texte multiligne dans la zone de texte insérer un lien hypertexte Vous pouvez également cliquer sur l'icône des Paramètres de Text Art dans la barre latérale droite et modifier certains paramètres de style. Modifier un style Text Art Sélectionnez un objet texte et cliquez sur l'icône des Paramètres de Text Art dans la barre latérale de droite. Modifiez le style de texte appliqué en sélectionnant un nouveau Modèle dans la galerie. Vous pouvez également modifier le style de base en sélectionnant un type de police différent, une autre taille, etc. Changer le Remplissage de la police. Les options disponibles sont les suivantes : Couleur de remplissage - sélectionnez cette option pour spécifier la couleur unie pour le remplissage de l'espace intérieur des lettres. Cliquez sur la case de couleur et sélectionnez la couleur voulue à partir de l'ensemble de couleurs disponibles ou spécifiez n'importe quelle couleur de votre choix : Remplissage en dégradé - sélectionnez cette option pour sélectionner deux couleurs et remplir les lettres d'une transition douce entre elles. Style - choisissez une des options disponibles : Linéaire (la transition se fait selon un axe horizontal/vertical ou en diagonale, sous l'angle de 45 degrés) ou Radial (la transition se fait autour d'un point, les couleurs se fondent progressivement du centre aux bords en formant un cercle). Direction - choisissez un modèle du menu. Si vous avez sélectionné le style Linéaire, vous pouvez choisir une des directions suivantes : du haut à gauche vers le bas à droite, du haut en bas, du haut à droite vers le bas à gauche, de droite à gauche, du bas à droite vers le haut à gauche, du bas en haut, du bas à gauche vers le haut à droite, de gauche à droite. Si vous avez choisi le style Radial, il n'est disponible qu'un seul modèle. Dégradé - cliquez sur le curseur de dégradé gauche au-dessous de la barre de dégradé pour activer la palette de couleurs qui correspond à la première couleur. Cliquez sur la palette de couleurs à droite pour sélectionner la première couleur. Faites glisser le curseur pour définir le point de dégradé c'est-à-dire le point quand une couleur se fond dans une autre. Utilisez le curseur droit au-dessous de la barre de dégradé pour spécifier la deuxième couleur et définir le point de dégradé. Remarque : si une de ces deux options est sélectionnée, vous pouvez toujours régler le niveau d'Opacité en faisant glisser le curseur ou en saisissant la valeur de pourcentage à la main. La valeur par défaut est 100%. Elle correspond à l'opacité complète. La valeur 0% correspond à la transparence totale. Pas de remplissage - sélectionnez cette option si vous ne voulez pas utiliser un remplissage. Ajustez la largeur, la couleur et le type du Ligne de la police. Pour modifier la largeur du ligne, sélectionnez une des options disponibles depuis la liste déroulante Taille. Les options disponibles sont les suivantes : 0,5 pt, 1 pt, 1,5 pt, 2,25 pt, 3 pt, 4,5 pt, 6 pt ou Pas de ligne si vous ne voulez pas utiliser de ligne. Pour changer la couleur du contour, cliquez sur la case colorée et sélectionnez la couleur voulue. Pour modifier le type de contour, sélectionnez l'option voulue dans la liste déroulante correspondante (une ligne continue est appliquée par défaut, vous pouvez la remplacer par l'une des lignes pointillées disponibles). Appliquez un effet de texte en sélectionnant le type de transformation de texte voulu dans la galerie Transformation. Vous pouvez ajuster le degré de distorsion du texte en faisant glisser la poignée en forme de diamant rose."
    },
   {
        "id": "UsageInstructions/Jitsi.htm", 
        "title": "Effectuer des appels audio et vidéo", 
        "body": "Les appels audio et vidéo sont disponibles immédiatement à partir de l'Éditeur de Documents ONLYOFFICE à l'aide du plug-in Jitsi. Jitsi offre les fonctionnalités de vidéoconférence sécurisées et faciles à déployer. Remarque : Plug-in Jitsi n'est pas installé par défaut, il est à installer manuellement. Veuillez consulter l'article correspondant pour trouver le guide d'installation manuelle Adding plugins to ONLYOFFICE Cloud ou Ajouter de nouveaux modules complémentaires aux éditeurs de serveur Passez à l'onglet Modules complémentaires et cliquez sur l'icône Jitsi dans la barre d'outils supérieure. Remplissez les champs en bas de la barre latérale gauche avant de démarrer un appel : Domaine - saisissez le nom du domaine si vous souhaitez connecter votre domaine. Nom de la chambre - saisissez le nom de la salle de réunion. Ce champ est obligatoire et un appel ne peut pas être démarré lorsque vous le quittez. Cliquez sur le bouton Lancer pour ouvrir Jitsi Meet iframe. Saisissez votre nom et autorisez l'accès de la caméra et du microphone à votre navigateur. Si vous souhaitez fermer Jitsi Meet iframe, cliquez sur le bouton Arrêter en bas à gauche. Cliquez sur le bouton Rejoindre la réunion afin de démarrer un appel avec un audio ou cliquez sur la flèche pour rejoindre sans audio. Les éléments d'interface Jitsi Meet iframe avant le début de la réunion : Les paramètres audio et Couper le son/Activer Cliquez sur la flèche pour accéder à l'aperçu des paramètres audio. Cliquez sur le microphone afin de couper le son ou activer votre microphone. Paramètres vidéo et Lancer/Arrêter Cliquez sur la flèche pour accéder à l'aperçu vidéo. Cliquez sur la caméra pour lancer ou arrêter votre vidéo. Inviter des personnes Cliquez sur ce bouton pour inviter plus de personnes à participer à la réunion. Partagez la réunion en copiant le lien de la réunion ou Partagez l'invitation à une réunion en la copiant ou via votre e-mail par défaut, Google, Outlook ou Yahoo. Intégrez la réunion en copiant le lien. Utilisez l'un des numéros disponibles d'appel pour rejoindre la réunion. Sélectionner l'arrière-plan Sélectionnez ou ajoutez un arrière-plan virtuel pour votre réunion. Partagez votre bureau en choisissant l'option appropriée : Écran, Fenêtre ou Onglet. Paramètres Configurez les paramètres avancés qui sont organisés dans les catégories suivantes : Appareils pour configurer vos microphone, caméra et sortie audio et jouer un son de test. Profil pour configurer votre nom à afficher et votre e-mail Gravatar, masquer ou afficher l'auto-affichage. Calendrier pour intégrer votre calendrier Google ou Microsoft. Sons pour sélectionner les actions pour jouer le son. Plus pour configurer certaines options supplémentaires : activer ou désactiver l'écran de pré-réunion et les raccourcis clavier, configurer une langue et une fréquence d'images de partage de bureau. Éléments de l'interface qui apparaissent lors d'une vidéoconférence : Cliquez sur la flèche latérale à droite pour afficher les miniatures des participants en haut. Le minuteur en haut de l'iframe affiche la durée de la réunion. Ouvrir un chat Saisissez un message texte ou créez un sondage. Participants Affichez la liste des participants à la réunion, invitez plus de participants et recherchez un participant. Plus d'actions Trouvez une gamme d'options pour utiliser toutes les fonctionnalités disponibles de Jitsi au maximum. Parcourez les options pour les voir. Options disponibles, Démarrer le partage d'écran Inviter des personnes Entrer/Quitter la vue en mosaïques Paramètres de performance pour ajuster la qualité Afficher en plein écran Options de sécurité Mode Lobby pour que les participants rejoignent la réunion après l'accord du modérateur ; Ajouter un mode de mot de passe pour que les participants rejoignent la réunion à l'aide d'un mot de passe ; Chiffrement de bout en bout est une méthode expérimentale pour effectuer des appels sécurisés (attention aux restrictions telles que la désactivation des services fournis du côté serveur et l'utilisation de navigateurs prenant en charge les flux d'insertion). Lancer le flux en direct Couper le son pour tout le monde Désactiver toutes les caméras Partager la vidéo Sélectionner l'arrière-plan Statistiques des orateurs Paramètres Afficher les raccourcis Intégrer la réunion Laisser un commentaire Aide Quitter la réunion Cliquez dessus lorsque vous souhaitez mettre fin à un appel."
    },
   {
        "id": "UsageInstructions/LineSpacing.htm", 
        "title": "Régler l'interligne du paragraphe", 
        "body": "En utilisant l'Éditeur de Documents, vous pouvez définir la hauteur de la ligne pour les lignes de texte dans le paragraphe ainsi que les marges entre le paragraphe courant et précédent ou suivant. Pour ce faire, placez le curseur dans le paragraphe choisi, ou sélectionnez plusieurs paragraphes avec la souris ou tout le texte en utilisant la combinaison de touches Ctrl+A, utilisez les champs correspondants de la barre latérale droite pour obtenir les résultats nécessaires : Interligne - réglez la hauteur de la ligne pour les lignes de texte dans le paragraphe. Vous pouvez choisir parmi trois options : Au moins (sert à régler l'interligne minimale qui est nécessaire pour adapter la plus grande police ou le graphique à la ligne), Multiple (sert à régler l'interligne exprimée en nombre supérieur à 1), Exactement (sert à définir l'interligne fixe). Spécifiez la valeur nécessaire dans le champ situé à droite. Espacement de paragraphe - définissez l'espace entre les paragraphes. Avant - réglez la taille de l'espace avant le paragraphe. Après - réglez la taille de l'espace après le paragraphe. N'ajoutez pas l'intervalle entre les paragraphes du même style - cochez cette case si vous n'avez pas besoin d'espace entre les paragraphes du même style. On peut configurer les mêmes paramètres dans la fenêtre Paragraphe - Paramètres avancés. Pour ouvrir la fenêtre Paragraphe - Paramètres avancés, cliquer avec le bouton droit sur le texte et sélectionnez l'option Paramètres avancés du paragraphe dans le menu ou utilisez l'option Afficher les paramètres avancés sur la barre latérale droite. Passez à l'onglet Retraits et espacement, section Espacement. Pour modifier rapidement l'interligne du paragraphe actuel, vous pouvez aussi cliquer sur l'icône Interligne du paragraphe sous l'onglet Accueil de la barre d'outils supérieure et sélectionnez la valeur nécessaire dans la liste : 1.0, 1.15, 1.5, 2.0, 2.5, ou 3.0 lignes."
    },
   {
        "id": "UsageInstructions/MathAutoCorrect.htm", 
        "title": "Fonctionnalités de correction automatique", 
        "body": "Les fonctionnalités de correction automatique ONLYOFFICE dans l'Éditeur de Documents, fournissent des options pour définir les éléments à mettre en forme automatiquement ou insérer des symboles mathématiques à remplacer les caractères reconnus. Toutes les options sont disponibles dans la boîte de dialogue appropriée. Pour y accéder, passez à l'onglet Fichier tab -> Paramètres avancés -> Vérification -> Options de correction automatique. La boîte de dialogue Correction automatique comprend quatre onglets : Math Autocorrect, Fonctions reconnues, Mise en forme automatique au cours de la frappe, et Correction automatique de texte. AutoMaths Lorsque vous travaillez dans l'éditeur d'équations, vous pouvez insérer plusieurs symboles, accents et opérateurs mathématiques en les tapant sur clavier plutôt que de les rechercher dans la bibliothèque. Dans l'éditeur d'équations, placez le point d'insertion dans l'espace réservé et tapez le code de correction mathématique, puis touchez la Barre d'espace. Le code que vous avez saisi, serait converti en symbole approprié mais l'espace est supprimé. Remarque : Les codes sont sensibles à la casse. Vous pouvez ajouter, modifier, rétablir et supprimer les éléments de la liste de corrections automatiques. Passez à l'onglet Fichier -> Paramètres avancés -> Vérification -> Options de correction automatique -> AutoMaths. Ajoutez un élément à la liste de corrections automatiques. Saisissez le code de correction automatique dans la zone Remplacer. Saisissez le symbole que vous souhaitez attribuer au code approprié dans la zone Par. Cliquez sur le bouton Ajouter. Modifier un élément de la liste de corrections automatiques Sélectionnez l'élément à modifier. Vous pouvez modifier les informations dans toutes les deux zones : le code dans la zone Remplacer et le symbole dans la zone Par. Cliquez sur le bouton Remplacer. Supprimer les éléments de la liste de corrections automatiques Sélectionnez l'élément que vous souhaitez supprimer de la liste. Cliquez sur le bouton Supprimer. Pour rétablir les éléments supprimés, sélectionnez l'élément que vous souhaitez rétablir dans la liste et appuyez sur Restaurer. Utilisez l'option Rétablir paramètres par défaut pour réinitialiser les réglages par défaut. Tous les éléments que vous avez ajouté, seraient supprimés et toutes les modifications seraient annulées pour rétablir sa valeur d'origine. Pour désactiver la correction automatique mathématique et éviter les changements et les remplacements automatiques, il faut décocher la case Remplacer le texte au cours de la frappe. Le tableau ci-dessous affiche tous le codes disponibles dans l'Éditeur de Présentations. à présent. On peut trouver la liste complète de codes disponibles sous l'onglet Fichier -> Paramètres avancés... -> Vérification -> Options de correction automatique -> AutoMaths. Les codes disponibles Code Symbole Catégorie !! Symboles ... Dots :: Opérateurs := Opérateurs /< Opérateurs relationnels /> Opérateurs relationnels /= Opérateurs relationnels \\above Indices et exposants \\acute Accentuation \\aleph Lettres hébraïques \\alpha Lettres grecques \\Alpha Lettres grecques \\amalg Opérateurs binaires \\angle Notation de géométrie \\aoint Intégrales \\approx Opérateurs relationnels \\asmash Flèches \\ast Opérateurs binaires \\asymp Opérateurs relationnels \\atop Opérateurs \\bar Trait suscrit/souscrit \\Bar Accentuation \\because Opérateurs relationnels \\begin Séparateurs \\below Indices et exposants \\bet Lettres hébraïques \\beta Lettres grecques \\Beta Lettres grecques \\beth Lettres hébraïques \\bigcap Grands opérateurs \\bigcup Grands opérateurs \\bigodot Grands opérateurs \\bigoplus Grands opérateurs \\bigotimes Grands opérateurs \\bigsqcup Grands opérateurs \\biguplus Grands opérateurs \\bigvee Grands opérateurs \\bigwedge Grands opérateurs \\binomial Équations \\bot Notations logiques \\bowtie Opérateurs relationnels \\box Symboles \\boxdot Opérateurs binaires \\boxminus Opérateurs binaires \\boxplus Opérateurs binaires \\bra Séparateurs \\break Symboles \\breve Accentuation \\bullet Opérateurs binaires \\cap Opérateurs binaires \\cbrt Racine carrée et radicaux \\cases Symboles \\cdot Opérateurs binaires \\cdots Dots \\check Accentuation \\chi Lettres grecques \\Chi Lettres grecques \\circ Opérateurs binaires \\close Séparateurs \\clubsuit Symboles \\coint Intégrales \\cong Opérateurs relationnels \\coprod Opérateurs mathématiques \\cup Opérateurs binaires \\dalet Lettres hébraïques \\daleth Lettres hébraïques \\dashv Opérateurs relationnels \\dd Lettres avec double barres \\Dd Lettres avec double barres \\ddddot Accentuation \\dddot Accentuation \\ddot Accentuation \\ddots Dots \\defeq Opérateurs relationnels \\degc Symboles \\degf Symboles \\degree Symboles \\delta Lettres grecques \\Delta Lettres grecques \\Deltaeq Opérateurs \\diamond Opérateurs binaires \\diamondsuit Symboles \\div Opérateurs binaires \\dot Accentuation \\doteq Opérateurs relationnels \\dots Dots \\doublea Lettres avec double barres \\doubleA Lettres avec double barres \\doubleb Lettres avec double barres \\doubleB Lettres avec double barres \\doublec Lettres avec double barres \\doubleC Lettres avec double barres \\doubled Lettres avec double barres \\doubleD Lettres avec double barres \\doublee Lettres avec double barres \\doubleE Lettres avec double barres \\doublef Lettres avec double barres \\doubleF Lettres avec double barres \\doubleg Lettres avec double barres \\doubleG Lettres avec double barres \\doubleh Lettres avec double barres \\doubleH Lettres avec double barres \\doublei Lettres avec double barres \\doubleI Lettres avec double barres \\doublej Lettres avec double barres \\doubleJ Lettres avec double barres \\doublek Lettres avec double barres \\doubleK Lettres avec double barres \\doublel Lettres avec double barres \\doubleL Lettres avec double barres \\doublem Lettres avec double barres \\doubleM Lettres avec double barres \\doublen Lettres avec double barres \\doubleN Lettres avec double barres \\doubleo Lettres avec double barres \\doubleO Lettres avec double barres \\doublep Lettres avec double barres \\doubleP Lettres avec double barres \\doubleq Lettres avec double barres \\doubleQ Lettres avec double barres \\doubler Lettres avec double barres \\doubleR Lettres avec double barres \\doubles Lettres avec double barres \\doubleS Lettres avec double barres \\doublet Lettres avec double barres \\doubleT Lettres avec double barres \\doubleu Lettres avec double barres \\doubleU Lettres avec double barres \\doublev Lettres avec double barres \\doubleV Lettres avec double barres \\doublew Lettres avec double barres \\doubleW Lettres avec double barres \\doublex Lettres avec double barres \\doubleX Lettres avec double barres \\doubley Lettres avec double barres \\doubleY Lettres avec double barres \\doublez Lettres avec double barres \\doubleZ Lettres avec double barres \\downarrow Flèches \\Downarrow Flèches \\dsmash Flèches \\ee Lettres avec double barres \\ell Symboles \\emptyset Ensemble de notations \\emsp Caractères d'espace \\end Séparateurs \\ensp Caractères d'espace \\epsilon Lettres grecques \\Epsilon Lettres grecques \\eqarray Symboles \\equiv Opérateurs relationnels \\eta Lettres grecques \\Eta Lettres grecques \\exists Notations logiques \\forall Notations logiques \\fraktura Fraktur \\frakturA Fraktur \\frakturb Fraktur \\frakturB Fraktur \\frakturc Fraktur \\frakturC Fraktur \\frakturd Fraktur \\frakturD Fraktur \\frakture Fraktur \\frakturE Fraktur \\frakturf Fraktur \\frakturF Fraktur \\frakturg Fraktur \\frakturG Fraktur \\frakturh Fraktur \\frakturH Fraktur \\frakturi Fraktur \\frakturI Fraktur \\frakturk Fraktur \\frakturK Fraktur \\frakturl Fraktur \\frakturL Fraktur \\frakturm Fraktur \\frakturM Fraktur \\frakturn Fraktur \\frakturN Fraktur \\frakturo Fraktur \\frakturO Fraktur \\frakturp Fraktur \\frakturP Fraktur \\frakturq Fraktur \\frakturQ Fraktur \\frakturr Fraktur \\frakturR Fraktur \\frakturs Fraktur \\frakturS Fraktur \\frakturt Fraktur \\frakturT Fraktur \\frakturu Fraktur \\frakturU Fraktur \\frakturv Fraktur \\frakturV Fraktur \\frakturw Fraktur \\frakturW Fraktur \\frakturx Fraktur \\frakturX Fraktur \\fraktury Fraktur \\frakturY Fraktur \\frakturz Fraktur \\frakturZ Fraktur \\frown Opérateurs relationnels \\funcapply Opérateurs binaires \\G Lettres grecques \\gamma Lettres grecques \\Gamma Lettres grecques \\ge Opérateurs relationnels \\geq Opérateurs relationnels \\gets Flèches \\gg Opérateurs relationnels \\gimel Lettres hébraïques \\grave Accentuation \\hairsp Caractères d'espace \\hat Accentuation \\hbar Symboles \\heartsuit Symboles \\hookleftarrow Flèches \\hookrightarrow Flèches \\hphantom Flèches \\hsmash Flèches \\hvec Accentuation \\identitymatrix Matrices \\ii Lettres avec double barres \\iiint Intégrales \\iint Intégrales \\iiiint Intégrales \\Im Symboles \\imath Symboles \\in Opérateurs relationnels \\inc Symboles \\infty Symboles \\int Intégrales \\integral Intégrales \\iota Lettres grecques \\Iota Lettres grecques \\itimes Opérateurs mathématiques \\j Symboles \\jj Lettres avec double barres \\jmath Symboles \\kappa Lettres grecques \\Kappa Lettres grecques \\ket Séparateurs \\lambda Lettres grecques \\Lambda Lettres grecques \\langle Séparateurs \\lbbrack Séparateurs \\lbrace Séparateurs \\lbrack Séparateurs \\lceil Séparateurs \\ldiv Barres obliques \\ldivide Barres obliques \\ldots Dots \\le Opérateurs relationnels \\left Séparateurs \\leftarrow Flèches \\Leftarrow Flèches \\leftharpoondown Flèches \\leftharpoonup Flèches \\leftrightarrow Flèches \\Leftrightarrow Flèches \\leq Opérateurs relationnels \\lfloor Séparateurs \\lhvec Accentuation \\limit Limites \\ll Opérateurs relationnels \\lmoust Séparateurs \\Longleftarrow Flèches \\Longleftrightarrow Flèches \\Longrightarrow Flèches \\lrhar Flèches \\lvec Accentuation \\mapsto Flèches \\matrix Matrices \\medsp Caractères d'espace \\mid Opérateurs relationnels \\middle Symboles \\models Opérateurs relationnels \\mp Opérateurs binaires \\mu Lettres grecques \\Mu Lettres grecques \\nabla Symboles \\naryand Opérateurs \\nbsp Caractères d'espace \\ne Opérateurs relationnels \\nearrow Flèches \\neq Opérateurs relationnels \\ni Opérateurs relationnels \\norm Séparateurs \\notcontain Opérateurs relationnels \\notelement Opérateurs relationnels \\notin Opérateurs relationnels \\nu Lettres grecques \\Nu Lettres grecques \\nwarrow Flèches \\o Lettres grecques \\O Lettres grecques \\odot Opérateurs binaires \\of Opérateurs \\oiiint Intégrales \\oiint Intégrales \\oint Intégrales \\omega Lettres grecques \\Omega Lettres grecques \\ominus Opérateurs binaires \\open Séparateurs \\oplus Opérateurs binaires \\otimes Opérateurs binaires \\over Séparateurs \\overbar Accentuation \\overbrace Accentuation \\overbracket Accentuation \\overline Accentuation \\overparen Accentuation \\overshell Accentuation \\parallel Notation de géométrie \\partial Symboles \\pmatrix Matrices \\perp Notation de géométrie \\phantom Symboles \\phi Lettres grecques \\Phi Lettres grecques \\pi Lettres grecques \\Pi Lettres grecques \\pm Opérateurs binaires \\pppprime Nombres premiers \\ppprime Nombres premiers \\pprime Nombres premiers \\prec Opérateurs relationnels \\preceq Opérateurs relationnels \\prime Nombres premiers \\prod Opérateurs mathématiques \\propto Opérateurs relationnels \\psi Lettres grecques \\Psi Lettres grecques \\qdrt Nombres premiers \\quadratic Nombres premiers \\rangle Séparateurs \\Rangle Séparateurs \\ratio Opérateurs relationnels \\rbrace Séparateurs \\rbrack Séparateurs \\Rbrack Séparateurs \\rceil Séparateurs \\rddots Dots \\Re Symboles \\rect Symboles \\rfloor Séparateurs \\rho Lettres grecques \\Rho Lettres grecques \\rhvec Accentuation \\right Séparateurs \\rightarrow Flèches \\Rightarrow Flèches \\rightharpoondown Flèches \\rightharpoonup Flèches \\rmoust Séparateurs \\root Symboles \\scripta Scripts \\scriptA Scripts \\scriptb Scripts \\scriptB Scripts \\scriptc Scripts \\scriptC Scripts \\scriptd Scripts \\scriptD Scripts \\scripte Scripts \\scriptE Scripts \\scriptf Scripts \\scriptF Scripts \\scriptg Scripts \\scriptG Scripts \\scripth Scripts \\scriptH Scripts \\scripti Scripts \\scriptI Scripts \\scriptk Scripts \\scriptK Scripts \\scriptl Scripts \\scriptL Scripts \\scriptm Scripts \\scriptM Scripts \\scriptn Scripts \\scriptN Scripts \\scripto Scripts \\scriptO Scripts \\scriptp Scripts \\scriptP Scripts \\scriptq Scripts \\scriptQ Scripts \\scriptr Scripts \\scriptR Scripts \\scripts Scripts \\scriptS Scripts \\scriptt Scripts \\scriptT Scripts \\scriptu Scripts \\scriptU Scripts \\scriptv Scripts \\scriptV Scripts \\scriptw Scripts \\scriptW Scripts \\scriptx Scripts \\scriptX Scripts \\scripty Scripts \\scriptY Scripts \\scriptz Scripts \\scriptZ Scripts \\sdiv Barres obliques \\sdivide Barres obliques \\searrow Flèches \\setminus Opérateurs binaires \\sigma Lettres grecques \\Sigma Lettres grecques \\sim Opérateurs relationnels \\simeq Opérateurs relationnels \\smash Flèches \\smile Opérateurs relationnels \\spadesuit Symboles \\sqcap Opérateurs binaires \\sqcup Opérateurs binaires \\sqrt Nombres premiers \\sqsubseteq Ensemble de notations \\sqsuperseteq Ensemble de notations \\star Opérateurs binaires \\subset Ensemble de notations \\subseteq Ensemble de notations \\succ Opérateurs relationnels \\succeq Opérateurs relationnels \\sum Opérateurs mathématiques \\superset Ensemble de notations \\superseteq Ensemble de notations \\swarrow Flèches \\tau Lettres grecques \\Tau Lettres grecques \\therefore Opérateurs relationnels \\theta Lettres grecques \\Theta Lettres grecques \\thicksp Caractères d'espace \\thinsp Caractères d'espace \\tilde Accentuation \\times Opérateurs binaires \\to Flèches \\top Notations logiques \\tvec Flèches \\ubar Accentuation \\Ubar Accentuation \\underbar Accentuation \\underbrace Accentuation \\underbracket Accentuation \\underline Accentuation \\underparen Accentuation \\uparrow Flèches \\Uparrow Flèches \\updownarrow Flèches \\Updownarrow Flèches \\uplus Opérateurs binaires \\upsilon Lettres grecques \\Upsilon Lettres grecques \\varepsilon Lettres grecques \\varphi Lettres grecques \\varpi Lettres grecques \\varrho Lettres grecques \\varsigma Lettres grecques \\vartheta Lettres grecques \\vbar Séparateurs \\vdash Opérateurs relationnels \\vdots Dots \\vec Accentuation \\vee Opérateurs binaires \\vert Séparateurs \\Vert Séparateurs \\Vmatrix Matrices \\vphantom Flèches \\vthicksp Caractères d'espace \\wedge Opérateurs binaires \\wp Symboles \\wr Opérateurs binaires \\xi Lettres grecques \\Xi Lettres grecques \\zeta Lettres grecques \\Zeta Lettres grecques \\zwnj Caractères d'espace \\zwsp Caractères d'espace ~= Opérateurs relationnels -+ Opérateurs binaires +- Opérateurs binaires << Opérateurs relationnels <= Opérateurs relationnels -> Flèches >= Opérateurs relationnels >> Opérateurs relationnels Fonctions reconnues Sous cet onglet, vous pouvez trouver les expressions mathématiques que l'éditeur d'équations reconnait comme les fonctions et lesquelles ne seront pas mises en italique automatiquement. Pour accéder à la liste de fonctions reconnues, passez à l'onglet Fichier -> Paramètres avancés -> Vérification -> Options de correction automatique -> Fonctions reconnues. Pour ajouter un élément à la liste de fonctions reconnues, saisissez la fonction dans le champ vide et appuyez sur Ajouter. Pour supprimer un élément de la liste de fonctions reconnues, sélectionnez la fonction à supprimer et appuyez sur Supprimer. Pour rétablir les éléments supprimés, sélectionnez l'élément que vous souhaitez rétablir dans la liste et appuyez sur Restaurer. Utilisez l'option Rétablir paramètres par défaut pour réinitialiser les réglages par défaut. Toutes les fonctions que vous avez ajoutées, seraient supprimées et celles qui ont été supprimées, seraient rétablies. Mise en forme automatique au cours de la frappe Par défaut, l'éditeur met en forme automatiquement lors de la saisie selon les paramètres de format automatique, comme par exemple remplacer les guillemets ou les traits d'union par un tiret demi-cadratin, convertir des addresses web ou des chemins d'accès réseau en lien hypertextes, appliquer une liste à puces ou une liste numérotée lorsqu'il détecte que vous tapez une liste. L'option Ajouter un point avec un double espace permet d'insérer un point lorsqu'on appuie deux fois sur la barre d'espace. Activez ou désactivez cette option selon le cas. Par défaut, cette option est désactivée sur Linux et Windows et est activée sur macOS. Si vous souhaitez désactiver une des options de mise en forme automatique, désactivez la case à coche de l'élément pour lequel vous ne souhaitez pas de mise en forme automatique sous l'onglet Fichier -> AParamètres avancés -> Vérification -> Correction automatique -> Mise en forme automatique au cours de la frappe. Correction automatique de texte Il est possible d'activer la correction automatique pour convertir en majuscule la première lettre des phrases. Par défaut, cette option est activée. Pour la désactiver, passez à l'onglet Fichier -> Paramètres avancés -> Vérification -> Options de correction automatique -> Correction automatique de texte et désactivez l'option Majuscule en début de phrase."
    },
   {
        "id": "UsageInstructions/NonprintingCharacters.htm", 
        "title": "Afficher/masquer les caractères non imprimables", 
        "body": "Les caractères non imprimables aident à éditer le document. Ils indiquent la présence de différents types de mises en forme, mais ils ne sont pas imprimés, même quand ils sont affichés à l'écran. Pour afficher ou masquer les caractères non imprimables dans l'Éditeur de Documents, cliquez sur l'icône Caractères non imprimables dans l'onglet Accueil de la barre d'outils supérieure. Les caractères non imprimables sont les suivants : Espaces Il est inséré lorsque vous appuyez sur la Barre d'espacement sur le clavier. Il crée un espace entre les caractères. Tabulations Il est inséré lorsque vous appuyez sur la touche Tabulation. Il est utilisé pour faire avancer le curseur sur le prochain taquet de tabulation. Marques de paragraphe Il est inséré lorsque vous appuyez sur la touche Entrée. Il est utilisé pour terminer un paragraphe et ajouter un peu d'espace après. Il contient des informations sur la mise en forme du paragraphe. Sauts de ligne Il est inséré lorsque vous utilisez la combinaison de touches Shift+Entrée. Il rompt la ligne actuelle et met des lignes de texte très rapprochées. Retour à la ligne est principalement utilisé dans les titres et les en-têtes. Espace insécable Il est inséré lorsque vous utilisez la combinaison de touches Ctrl+Shift+Espace. Il crée un espace entre les caractères qui ne peuvent pas être utilisés pour commencer une nouvelle ligne. Sauts de page Il est inséré lorsque vous utilisez l'icône Sauts de page dans l'onglet Insérer de la barre d'outils supérieure et sélectionnez l'option Insérer un saut de page ou sélectionnez l'option Saut de page avant du menu contextuel ou de la fenêtre des paramètres avancés. Sauts de section Il est inséré lorsque vous utilisez l'icône Sauts de page dans l'onglet Insérer ou Disposition de la barre d'outils supérieure puis sélectionnez une des options du sous-menu Insérer un saut de section (l’indicateur de saut de section diffère selon l'option choisie : Page suivante, Page continue, Page paire ou Page impaire). Sauts de colonne Il est inséré lorsque vous utilisez l'icône Sauts de page dans l'onglet Insérer ou Disposition de la barre d'outils supérieure puis sélectionnez l'option Insérer un saut de colonne. Marquers des tableaux Fin de cellule et Fin de ligne Ces marqueurs contiennent des codes de mise en forme de la cellule individuelle et de la ligne respectivement. Petit carré noir dans la marge gauche d'un paragraphe Il indique qu'au moins une des options de paragraphe a été appliquée, par exemple Lignes solidaires, Saut de page avant. Symboles d'ancre Ils indiquent la position des objets flottants ( valables pour tout style d'habillage sauf le style En ligne), par exemple images, formes automatiques, graphiques. Vous devez sélectionner un objet pour faire son ancre visible."
    },
   {
        "id": "UsageInstructions/OCR.htm", 
        "title": "Extraction du texte incrusté dans l'image", 
        "body": "En utilisant l'Éditeur de Documents ONLYOFFICE vous pouvez extraire du texte incrusté dans des images (.png .jpg) et l'insérer dans votre document. Accédez à votre document et placez le curseur à l'endroit où le texte doit être inséré. Passez à l'onglet Modules complémentaires et choisissez OCR dans le menu. Appuyez sur Charger fichier et choisissez l'image. Sélectionnez la langue à reconnaître de la liste déroulante Choisir la langue. Appuyez sur Reconnaître. Appuyez sur Insérer le texte. Vérifiez les erreurs et la mise en page."
    },
   {
        "id": "UsageInstructions/OpenCreateNew.htm", 
        "title": "Créer un nouveau document ou ouvrir un document existant", 
        "body": "Dans l'Éditeur de Documents, vous pouvez créer un nouveau document, ouvrir un document existant ou revenir à la liste des documents existants. Pour créer un nouveau document Dans la version en ligne cliquez sur l'onglet Fichier de la barre d'outils supérieure, sélectionnez l'option Créer nouveau. Dans l'éditeur de bureau dans la fenêtre principale du programme, sélectionnez l'élément de menu Document dans la section Créer nouveau de la barre latérale gauche - un nouveau fichier s'ouvrira dans un nouvel onglet, une fois tous les changements nécessaires effectués, cliquez sur l'icône Enregistrer dans le coin supérieur gauche ou passez à l'onglet Fichier et choisissez l'élément de menu Enregistrer sous. dans la fenêtre du gestionnaire de fichiers, sélectionnez l'emplacement du fichier, spécifiez son nom, choisissez le format dans lequel vous souhaitez enregistrer le document (DOCX, Modèle de document (DOTX), ODT, OTT, RTF, TXT, PDF ou PDFA) et cliquez sur le bouton Enregistrer. Pour ouvrir un document existant Dans l'éditeur de bureau dans la fenêtre principale du programme, sélectionnez l'élément de menu Ouvrir fichier local dans la barre latérale gauche, choisissez le document nécessaire dans la fenêtre du gestionnaire de fichiers et cliquez sur le bouton Ouvrir. Vous pouvez également cliquer avec le bouton droit de la souris sur le document nécessaire dans la fenêtre du gestionnaire de fichiers, sélectionner l'option Ouvrir avec et choisir l'application nécessaire dans le menu. Si les fichiers de documents Office sont associés à l'application, vous pouvez également ouvrir les documents en double-cliquant sur le nom du fichier dans la fenêtre d'exploration de fichiers. Tous les répertoires auxquels vous avez accédé à l'aide de l'éditeur de bureau seront affichés dans la liste Dossiers récents afin que vous puissiez y accéder rapidement. Cliquez sur le dossier nécessaire pour sélectionner l'un des fichiers qui y sont stockés. Pour ouvrir un document récemment édité Dans la version en ligne cliquez sur l'onglet Fichier de la barre d'outils supérieure, sélectionnez l'option Ouvrir récent, sélectionnez le document nécessaire dans la liste des documents récemment édités. Dans l'éditeur de bureau dans la fenêtre principale du programme, sélectionnez l'élément de menu Fichiers récents dans la barre latérale gauche, sélectionnez le document nécessaire dans la liste des documents récemment édités. Pour renommer un document ouvert Dans l'éditeur en ligne cliquez sur le nom du document en haut de la page, saisissez un nouveau nom de document, cliquez sur Entrer afin d'accepter les modifications. Pour ouvrir le dossier dans lequel le fichier est stocké dans un nouvel onglet du navigateur de la version en ligne, dans la fenêtre de l'explorateur de fichiers de la version de bureau, cliquez sur l'icône Ouvrir l'emplacement du fichier à droite de l'en-tête de l'éditeur. Vous pouvez aussi passer à l'onglet Fichier sur la barre d'outils supérieure et sélectionner l'option Ouvrir l'emplacement du fichier."
    },
   {
        "id": "UsageInstructions/PageBreaks.htm", 
        "title": "Insérer des sauts de page", 
        "body": "Dans l'Éditeur de Documents, vous pouvez ajouter un saut de page pour commencer une nouvelle page, insérer une page blanche et régler les options de pagination. Pour insérer un saut de page à la position actuelle du curseur, cliquez sur l'icône Sauts de page sous l'onglet Insertion ou Mise en page de la barre d'outils supérieure ou cliquez sur la flèche en regard de cette icône et sélectionnez l'option Insertion un saut de page dans le menu. Vous pouvez également utiliser la combinaison de touches Ctrl+Entrée. Pour insérer une page blanche à la position actuelle du curseur, cliquez sur l'icône Page vierge sous l'onglet Insertion de la barre d'outils supérieure. Cela insère deux sauts de page qui créent une page blanche. Pour insérer un saut de page avant le paragraphe sélectionné c'est-à-dire pour commencer ce paragraphe en haut d'une nouvelle page : cliquez avec le bouton droit de la souris et sélectionnez l'option Saut de page avant du menu contextuel, ou cliquez avec le bouton droit de la souris, sélectionnez l'option Paramètres avancés du paragraphe du menu contextuel ou utilisez le lien Afficher les paramètres avancés sur la barre latérale droite et cochez la case Saut de page avant sous l'onglet Enchaînements dans la fenêtre Paragraphe - Paramètres avancés ouverte. Pour garder les lignes solidaires de sorte que seulement des paragraphes entiers seront placés sur la nouvelle page (c'est-à-dire il n'y aura aucun saut de page entre les lignes dans un seul paragraphe), cliquez avec le bouton droit de la souris et sélectionnez l'option Lignes solidaires du menu contextuel, ou cliquez avec le bouton droit de la souris, sélectionnez l'option Paramètres avancés du paragraphe du menu contextuel ou utilisez le lien Afficher paramètres avancés sur la barre latérale droite et cochez la case Lignes solidaires sous l'onglet Enchaînements dans la fenêtre Paragraphe - Paramètres avancés ouverte. L'onglet Enchaînements dans la fenêtre Paragraphe - Paramètres avancés vous permet de définir deux autres options de pagination : Paragraphes solidaires sert à empêcher l'application du saut de page entre le paragraphe sélectionné et celui-ci qui le suit. Éviter orphelines est sélectionné par défaut et sert à empêcher l'application d'une ligne (première ou dernière) d'un paragraphe en haut ou en bas d'une page."
    },
   {
        "id": "UsageInstructions/ParagraphIndents.htm", 
        "title": "Modifier les retraits du paragraphe", 
        "body": "En utilisant l'Éditeur de Documents, vous pouvez changer le premier décalage de la ligne sur la partie gauche de la page aussi bien que le décalage du paragraphe du côté gauche et du côté droit de la page. Pour ce faire, configurez les paramètres nécessaires dans la section Retraits sur la barre latérale droite Paramètres du paragraphe, Gauche spécifiez le décalage du paragraphe de la marge gauche de la page et saisissez la valeur numérique appropriée, Droite spécifiez le décalage du paragraphe de la marge droite de la page et saisissez la valeur numérique appropriée, Spécial - spécifier le retrait de la première ligne du paragraphe : sélectionnez l'élément approprié du menu ((aucun), Première ligne, Suspendu) et modifiez la valeur numérique par défaut pour les options Première ligne ou Suspendu, ou placez le curseur dans le paragraphe de votre choix, ou sélectionnez plusieurs paragraphes avec la souris ou tout le texte en utilisant la combinaison de touches Ctrl+A, cliquez sur le bouton droit de la souris et sélectionnez l'option Paramètres avancés du paragraphe du menu contextuel ou utilisez le lien Afficher les paramètres avancés sur la barre latérale droite, dans la fenêtre ouverte Paragraphe - Paramètres avancés, passez à l'onglet Retraits et espacement et configurez les paramètres dans la section Retraits (les paramètres sont présentés ci-dessus), cliquez sur OK. Pour modifier rapidement le retrait de paragraphe du côté gauche de la page, vous pouvez également utiliser les icônes respectives dans l'onglet Accueil de la barre d'outils supérieure : Réduire le retrait et Augmenter le retrait . Vous pouvez également utilisez la règle horizontale pour changer les retraits. Sélectionnez le(s) paragraphe(s) et faites glisser les marqueurs tout au long de la règle Le marqueur Retrait de première ligne sert à définir le décalage du côté gauche de la page pour la première ligne du paragraphe. Le marqueur Retrait suspendu sert à définir le décalage sert à définir le décalage du bord gauche de la page pour la deuxième ligne et toutes les lignes suivantes du paragraphe. Le marqueur Retrait de gauche sert à définir le décalage du paragraphe du côté gauche de la page. Le marqueur Retrait de droite sert à définir le décalage du paragraphe du côté droit de la page."
    },
   {
        "id": "UsageInstructions/PhotoEditor.htm", 
        "title": "Modification d'une image", 
        "body": "Éditeur de Documents ONLYOFFICE dispose d'un éditeur de photos puissant qui permet aux utilisateurs d'appliquer divers effets de filtre à vos images et de faire les différents types d'annotations. Sélectionnez une image incorporée dans votre document. Passez à l'onglet Modules complémentaires et choisissez Photo Editor. Vous êtes dans l'environnement de traitement des images. Au-dessous de l'image il y a les cases à cocher et les filtres en curseur suivants : Niveaux de gris, Sépia 1, Sépia 2, Flou, Embosser, Inverser, Affûter ; Enlever les blancs (Seuil, Distance), Transparence des dégradés, Brillance, Bruit, Pixélateur, Filtre de couleur ; Teinte, Multiplication, Mélange. Au-dessous, les filtres dont vous pouvez accéder avec les boutons Annuler, Rétablir et Remettre à zéro ; Supprimer, Supprimer tout</> ; Rogner (Personnalisé, Carré, 3:2, 4:3, 5:4, 7:5, 16:9) ; Retournement (Retourner X, Retourner Y, Remettre à zéro) ; Rotation (à 30 degrés, -30 degrés, Gamme) ; Dessiner (Libre, Direct, Couleur, Gamme) ; Forme (Rectangle, Cercle, Triangle, Remplir, Trait, Largeur du trait) ; Icône (Flèches, Étoiles, Polygone, Emplacement, Cœur, Bulles, Icône personnalisée, Couleur) ; Texte (Gras, Italique, Souligné, Gauche, Centre, Droite, Couleur, Taille de texte) ; Masque. N'hésitez pas à les essayer tous et rappelez-vous que vous pouvez annuler les modifications à tout moment. Une fois que vous avez terminé, cliquez sur OK. Maintenant l'image modifiée est insérée dans votre document."
    },
   {
        "id": "UsageInstructions/SavePrintDownload.htm", 
        "title": "Enregistrer, exporter, imprimer votre document", 
        "body": "Enregistrement Par défaut, l'Éditeur de Documents en ligne enregistre automatiquement votre fichier toutes les 2 secondes afin de prévenir la perte des données en cas de fermeture inattendue de l'éditeur. Si vous co-éditez le fichier en mode Rapide, le minuteur récupère les mises à jour 25 fois par seconde et enregistre les modifications si elles ont été effectuées. Lorsque le fichier est co-édité en mode Strict, les modifications sont automatiquement sauvegardées à des intervalles de 10 minutes. Si nécessaire, vous pouvez facilement changer la périodicité de l'enregistrement automatique ou même désactiver cette fonction sur la page Paramètres avancés. Pour enregistrer manuellement votre document actuel dans le format et l'emplacement actuels, cliquez sur l'icône Enregistrer dans la partie gauche de l'en-tête de l'éditeur, ou utilisez la combinaison des touches Ctrl+S, ou cliquez sur l'onglet Fichier de la barre d'outils supérieure et sélectionnez l'option Enregistrer. Dans la version de bureau, pour éviter la perte de données en cas de fermeture inattendue du programme, vous pouvez activer l'option Récupération automatique sur la page Paramètres avancés. Dans la version de bureau, vous pouvez enregistrer le document sous un autre nom, dans un nouvel emplacement ou format, cliquez sur l'onglet Fichier de la barre d'outils supérieure, sélectionnez l'option Enregistrer sous..., sélectionnez l'un des formats disponibles selon vos besoins: DOCX, ODT, RTF, TXT, PDF, PDF/A, HTML, FB2, EPUB, DOCXF, OFORM. Vous pouvez également choisir l'option Modèle de document (DOTX or OTT). Téléchargement en cours Dans la version en ligne, vous pouvez télécharger le document résultant sur le disque dur de votre ordinateur, cliquez sur l'onglet Fichier de la barre d'outils supérieure, sélectionnez l'option Télécharger comme..., sélectionnez l'un des formats disponibles selon vos besoins: DOCX, PDF, ODT, TXT, DOTX, PDF/A, OTT, RTF, HTML, FB2, EPUB, DOCXF, OFORM. Enregistrer une copie Dans la version en ligne, vous pouvez enregistrer une copie du fichier sur votre portail, cliquez sur l'onglet Fichier de la barre d'outils supérieure, sélectionnez l'option Enregistrer la copie sous..., sélectionnez l'un des formats disponibles selon vos besoins: DOCX, PDF, ODT, TXT, DOTX, PDF/A, OTT, RTF, HTML, FB2, EPUB,DOCXF, OFORM. sélectionnez un emplacement pour le fichier sur le portail et appuyez sur Enregistrer. Impression Pour imprimer le document actif, cliquez sur l'icône Imprimer le fichier dans la partie gauche de l'en-tête de l'éditeur, ou utilisez la combinaison des touches Ctrl+P, ou cliquez sur l'onglet Fichier de la barre d'outils supérieure et sélectionnez l'option Imprimer. Le navigateur Firefox permet d'imprimer sans télécharger le document au format .pdf d'avance. Il est aussi possible d'imprimer un fragment de texte en utilisant l'option Imprimer la sélection du menu contextuel en mode Édition aussi que en mode Affichage (cliquez avec le bouton droit de la souris et choisissez Imprimer la sélection). Dans la version de bureau, le fichier sera imprimé directement. Dans la version en ligne, un fichier PDF sera généré à partir du document. Vous pouvez l'ouvrir et l'imprimer, ou l'enregistrer sur le disque dur de l'ordinateur ou sur un support amovible pour l'imprimer plus tard. Certains navigateurs (par ex. Chrome et Opera) supportent l'impression directe."
    },
   {
        "id": "UsageInstructions/SectionBreaks.htm", 
        "title": "Insérer les sauts de section", 
        "body": "Les sauts de section vous permettent d'appliquer des mises en page et mises en formes différentes pour de certaines parties de votre document. Dans l'Éditeur de Documents, par exemple, vous pouvez utiliser des en-têtes et pieds de page, des numérotations des pages, des marges, la taille, l'orientation, ou le numéro de colonne individuels pour chaque section séparée. Remarque : un saut de section inséré définit la mise en page de la partie précédente du document. Pour insérer un saut de section à la position actuelle du curseur : cliquez sur l'icône Saut de section dans l'onglet Insertion ou Mise en page de la barre d'outils supérieure, sélectionnez l'option Insérer un saut de section sélectionnez le type du saut de section nécessaire : Page suivante - pour commencer une nouvelle section sur la page suivante Page continue - pour commencer une nouvelle section sur la page actuelle Page paire - pour commencer une nouvelle section sur la page suivante paire Page impaire - pour commencer une nouvelle section sur la page suivante impaire Des sauts d'une section ajoutés sont indiqués dans votre document par un double trait pointillé : Si vous ne voyez pas de sauts de section insérés, cliquez sur l'icône de l'onglet Accueil sur la barre d'outils supérieure pour les afficher. Pour supprimer un saut de section, sélectionnez-le avec le souris et appuyez sur la touche Suppr. Lorsque vous supprimez un saut de section, la mise en forme de cette section sera également supprimée, car un saut de section définit la mise en forme de la section précédente. La partie du document qui précède le saut de section supprimé acquiert la mise en forme de la partie qui la suive."
    },
   {
        "id": "UsageInstructions/SetOutlineLevel.htm", 
        "title": "Configurer le niveau hiérarchique de paragraphe", 
        "body": "Le niveau de plan désigne le niveau de paragraphe dans la structure du document. Les niveaux suivants sont disponibles dans l'Éditeur de Documents : Texte de base, Niveau 1- - Niveau 9. Le niveau hiérarchique peut être spécifié de différentes manières, par exemple, en utilisant des styles de titre : quand vous appliquez un style de titre (Titre 1 - Titre 9) à un paragraphe, il acquiert un niveau de plan correspondant. Si vous appliquez un niveau à un paragraphe à l'aide des paramètres avancés du paragraphe, le paragraphe acquiert le niveau de structure uniquement tendis que son style reste intact. Le niveau hiérarchique peut également être modifié à l'aide du panneau de Navigation à gauche en utilisant des options contextuels du menu. Pour modifier un niveau hiérarchique de paragraphe à l'aide des paramètres avancés du paragraphe: Cliquez droit sur le texte et choisissez l'option Paramètres avancés du paragraphe du menu contextuel ou utilisez l'option Afficher les paramètres avancés sur la barre latérale droite, Ouvrez la fenêtre Paragraphe - Paramètres avancés, passez à l'onglet Retraits et espacement, Sélectionnez le niveau hiérarchique nécessaire dans la liste du niveau hiérarchique. Cliquez sur le bouton OK pour appliquer les modifications."
    },
   {
        "id": "UsageInstructions/SetPageParameters.htm", 
        "title": "Régler les paramètres de page", 
        "body": "Pour modifier la mise en page dans l'Éditeur de Documents, c'est-à-dire définir l'orientation et la taille de la page, ajuster les marges et insérer des colonnes, utilisez les icônes correspondantes dans l'onglet Mise en page de la barre d'outils supérieure. Remarque : tous ces paramètres sont appliqués au document entier. Si vous voulez définir de différentes marges de page, l'orientation, la taille, ou le numéro de colonne pour les parties différentes de votre document, consultez cette page. Orientation de page Changez l'orientation de page actuelle en cliquant sur l'icône Orientation . Le type d'orientation par défaut est Portrait qui peut être commuté sur Paysage. Taille de la page Changez le format A4 par défaut en cliquant sur l'icône Taille de la page et sélectionnez la taille nécessaire dans la liste. Les formats offerts sont les suivants : US Letter (21,59cm x 27,94cm) US Legal (21,59cm x 35,56cm) A4 (21cm x 29,7cm) A5 (14,81cm x 20,99cm) B5 (17,6cm x 25,01cm) Envelope #10 (10,48cm x 24,13cm) Envelope DL (11,01cm x 22,01cm) Tabloid (27,94cm x 43,17cm) AЗ (29,7cm x 42,01cm) Tabloid Oversize (30,48cm x 45,71cm) ROC 16K (19,68cm x 27,3cm) Envelope Choukei 3 (11,99cm x 23,49cm) Super B/A3 (33,02cm x 48,25cm) Vous pouvez définir une taille de la page particulière en utilisant l'option Taille personnalisée dans la liste. La fenêtre Taille de la page s'ouvrira où vous pourrez sélectionner le Préréglage voulu (US Letter, US Legal, A4, A5, B5, Enveloppe #10, Enveloppe DL, Tabloid, AЗ, Tabloid Oversize, ROC 16K, Enveloppe Choukei 3, Super B/A3, A0, A1, A2, A6) ou définir des valeurs personnalisées de Largeur et Hauteur. Entrez vos nouvelles valeurs dans les champs d'entrées ou ajustez les valeurs existantes en utilisant les boutons de direction. Lorsque tout est prêt, cliquez sur OK pour appliquer les changements. Marges de la page Modifiez les marges par défaut, c'est-à-dire l'espace entre les bords de la page et le texte du paragraphe, en cliquant sur l'icône Marges et sélectionnez un des paramètres prédéfinis : Normal, US Normal, Étroit, Modérer, Large. Vous pouvez aussi utiliser l'option Marges personnalisées pour définir les valeurs nécessaires dans la fenêtre Marges qui s'ouvre. Entrez les valeurs des marges Haut, Bas, Gauche et Droite de la page dans les champs d'entrées ou ajustez les valeurs existantes en utilisant les boutons de direction. Position de la reliure permet de définir l'espace supplémentaire à la marge latérale gauche ou supérieure du document. La Position de reliure assure que la reliure n'empiète pas sur le texte. Dans la fenêtre Marges spécifiez la talle de marge et la position de la reliure appropriée. Remarque : ce n'est pas possible de définir la Position de reliure lorsque l'option des Pages en vis-à-vis est active. Dans le menu déroulante Plusieurs pages, choisissez l'option des Pages en vis-à-vis pour configurer des pages en regard dans des documents recto verso. Lorsque cette option est activée, les marges Gauche et Droite se transforment en marges A l'intérieur et A l'extérieur respectivement. Dans le menu déroulante Orientation choisissez Portrait ou Paysage. Toutes les modifications apportées s'affichent dans la fenêtre Aperçu. Lorsque tout est prêt, cliquez sur OK. Les marges personnalisées seront appliquées au document actuel et l'option Dernière mesure avec les paramètres spécifiés apparaît dans la liste des Marges pour que vous puissiez les appliquer à d'autres documents. Vous pouvez également modifier les marges manuellement en faisant glisser la bordure entre les zones grises et blanches sur les règles (les zones grises des règles indiquent les marges de page) : Colonnes Pour appliquez une mise en page multicolonne, cliquez sur l'icône Colonnes et sélectionnez le type de la colonne nécessaire dans la liste déroulante. Les options suivantes sont disponibles : Deux - pour ajouter deux colonnes de la même largeur, Trois - pour ajouter trois colonnes de la même largeur, A gauche - pour ajouter deux colonnes : une étroite colonne à gauche et une large colonne à droite, A droite - pour ajouter deux colonnes : une étroite colonne à droite et une large colonne à gauche. Si vous souhaitez ajuster les paramètres de colonne, sélectionnez l'option Colonnes personnalisées dans la liste. La fenêtre Colonnes s'ouvrira où vous pourrez définir le Nombre de colonnes nécessaire (il est possible d'ajouter jusqu'à 12 colonnes) et l'Espacement entre les colonnes. Entrez vos nouvelles valeurs dans les champs d'entrées ou ajustez les valeurs existantes en utilisant les boutons de direction. Cochez la case Diviseur de colonne pour ajouter une ligne verticale entre les colonnes. Lorsque tout est prêt, cliquez sur OK pour appliquer les changements. Pour spécifier exactement la position d'une nouvelle colonne, placez le curseur avant le texte à déplacer dans une nouvelle colonne, cliquez sur l'icône Sauts de la barre d'outils supérieure et sélectionnez l'option Insérer un saut de colonne. Le texte sera déplacé vers la colonne suivante. Les sauts de colonne ajoutés sont indiqués dans votre document par une ligne pointillée : . Si vous ne voyez pas de sauts de section insérés, cliquez sur l'icône sous l'onglet Accueil de la barre d'outils supérieure pour les afficher. Pour supprimer un saut de colonne,sélectionnez-le avec le souris et appuyez sur une touche Suppr. Pour modifier manuellement la largeur et l'espacement entre les colonnes, vous pouvez utiliser la règle horizontale. Pour annuler les colonnes et revenir à la disposition en une seule colonne, cliquez sur l'icône Colonnes de la barre d'outils supérieure et sélectionnez l'option Une dans la liste."
    },
   {
        "id": "UsageInstructions/SetTabStops.htm", 
        "title": "Définir des taquets de tabulation", 
        "body": "Éditeur de Documents vous permet de changer des taquets de tabulation. Taquet de tabulation est l'emplacement où le curseur s'arrête quand vous appuyez sur la touche Onglet du clavier. Pour définir les taquets de tabulation vous pouvez utiliser la règle horizontale : Sélectionnez le type du taquet de tabulation en cliquant sur le bouton dans le coin supérieur gauche de la zone de travail. Trois types de taquets de tabulation sont disponibles : De gauche sert à aligner le texte sur le côté gauche du taquet de tabulation ; le texte se déplace à droite du taquet de tabulation quand vous saisissez le texte. Le taquet de tabulation sera indiqué sur la règle horizontale par le marqueur de Taquet de tabulation de gauche . Du centre sert à centrer le texte à l'emplacement du taquet de tabulation. Le taquet de tabulation sera indiqué sur la règle horizontale par le marqueur de Taquet de tabulation centré . De droite sert à aligner le texte sur le côté droit du taquet de tabulation ; le texte se déplace à gauche du taquet de tabulation quand vous saisissez le texte. Le taquet de tabulation sera indiqué sur la règle horizontale par le marqueur de Taquet de tabulation de droite . Cliquez sur le bord inférieur de la règle là où vous voulez positionner le taquet de tabulation. Faites-le glisser tout au long de la règle pour changer son emplacement. Pour supprimer le taquet de tabulation ajouté faites-le glisser en dehors de la règle. Vous pouvez également utiliser la fenêtre des paramètres avancés du paragraphe pour régler les taquets de tabulation. Cliquez avec le bouton droit de la souris, sélectionnez l'option Paramètres avancés du paragraphe du menu ou utilisez le lien Afficher les paramètres avancés sur la barre latérale droite, et passez à l'onglet Tabulation de la fenêtre Paragraphe - Paramètres avancés. Vous y pouvez définir les paramètres suivants : La tabulation Par défaut est 1.25 cm. Vous pouvez augmenter ou diminuer cette valeur en utilisant les boutons à flèche ou en saisissant la valeur nécessaire dans le champ. Position sert à personnaliser les taquets de tabulation. Saisissez la valeur nécessaire dans ce champ, réglez-la en utilisant les boutons à flèche et cliquez sur le bouton Spécifier. La position du taquet de tabulation personnalisée sera ajoutée à la liste dans le champ au-dessous. Si vous avez déjà ajouté quelques taquets de tabulation en utilisant la règle, tous ces taquets seront affichés dans cette liste. Alignement sert à définir le type d'alignement pour chaque taquet de tabulation de la liste. Sélectionnez le taquet nécessaire dans la liste, choisissez l'option A gauche, Au centre ou A droite dans la liste déroulante et cliquez sur le bouton Spécifier. Guide permet de choisir un caractère utilisé pour créer un guide pour chacune des positions de tabulation. Le guide est une ligne de caractères (points ou traits d'union) qui remplissent l'espace entre les taquets. Sélectionnez le taquet voulu dans la liste, choisissez le type de points de suite dans la liste dans la liste déroulante et cliquez sur le bouton Spécifier. Pour supprimer un taquet de tabulation de la liste sélectionnez-le et cliquez sur le bouton Supprimer ou utilisez le bouton Supprimer tout pour vider la liste."
    },
   {
        "id": "UsageInstructions/Speech.htm", 
        "title": "Lire un texte à haute voix", 
        "body": "Éditeur de documents ONLYOFFICE dispose d'une extension qui va lire un texte à voix haute. Sélectionnez le texte à lire à haute voix. Passez à l'onglet Modules complémentaires et choisissez Parole. Le texte sera lu à haute voix."
    },
   {
        "id": "UsageInstructions/SupportSmartArt.htm", 
        "title": "Prise en charge des graphiques SmartArt par ONLYOFFICE Document Editor", 
        "body": "Prise en charge des graphiques SmartArt par l'Éditeur de documents ONLYOFFICE Un graphique SmartArt sert à créer une représentation visuelle de la structure hiérarchique en choisissant le type du graphique qui convient le mieux. Éditeur de documents ONLYOFFICE prend en charge les graphiques SmartArt qui étaient créés dans d'autres applications. Vous pouvez ouvrir un fichier contenant SmartArt et le modifier en tant qu'un élément graphique en utilisant les outils d'édition disponibles. Une fois que vous avez cliqué sur un graphique SmartArt, les onglets suivants deviennent actifs sur la barre latérale droite pour modifier la disposition du graphique : Paramètres du paragraphe pour modifier les retraits et l'espacement, les enchaînements, les bordures et le remplissage, la police, les taquets et les marges intérieures. Veuillez consulter la section Mise en forme de paragraphe pour une description détaillée de toutes options disponibles. Cette onglet n'est disponible que pour des éléments du graphique SmartArt. Paramètres de la forme pour modifier les formes inclues dans le graphique. Vous pouvez modifier les formes, le remplissage, les lignes, la taille, le style d'habillage, la position, les poids et les flèches, la zone de texte et le texte de remplacement. Paramètres de Texte Art pour modifier le style des objets Texte Art inclus dans le graphique SmartArt pour mettre en évidence du texte. Vous pouvez modifier le modèle de l'objet Text Art, le remplissage, la couleur et l'opacité, le poids, la couleur et le type des traits. Cette onglet n'est disponible que pour des éléments du graphique SmartArt. Faites un clic droit sur la bordure du graphique SmartArt ou de ses éléments pour accéder aux options suivantes : L'option Style d'habillage permet de déterminer la façon de positionner l'objet par rapport au texte. L'option Style d'habillage ne devient disponible que pour le graphique SmartArt entier. Rotation pour définir le sens de rotation de l'élément inclus dans le graphique SmartArt : Faire pivoter à droite de 90°, Faire pivoter à gauche de 90°, Retourner horizontalement, Retourner verticalement. L'option Rotation n'est disponible que pour des éléments du graphique SmartArt. Insérer une légende pour étiqueter des éléments du graphique SmartArt pour faire un renvoi. Paramètres avancés de la forme pour accéder aux paramètres avancés de mise en forme. Faites un clic droit sur l'élément du graphique SmartArt pour accéder aux options suivantes : Alignement vertical pour définir l'alignement du texte dans l'élément du graphique SmartArt : Aligner en haut, Aligner au milieu ou Aligner en bas. Orientation du texte pour définir l'orientation du texte dans l'élément du graphique SmartArt : Horizontal, Rotation du texte vers le bas, Rotation du texte vers le haut. Paramètres avancés du paragraphe pour accéder aux paramètres avancés de mise en forme du paragraphe."
    },
   {
        "id": "UsageInstructions/Thesaurus.htm", 
        "title": "Remplacer un mot par synonyme", 
        "body": "Si on répète plusieurs fois le même mot ou il ne semble pas que le mot est juste, l'Éditeur de documents ONLYOFFICE vous permet de trouver les synonymes. Retrouvez les antonymes du mot affiché aussi. Sélectionnez le mot dans votre document. Passez à l'onglet Modules complémentaires et choisissez Thésaurus. Le panneau gauche liste les synonymes et les antonymes. Cliquez sur le mot à remplacer dans votre document."
    },
   {
        "id": "UsageInstructions/Translator.htm", 
        "title": "Traduire un texte", 
        "body": "Dans l'Éditeur de Documents, vous pouvez traduire votre document dans de nombreuses langues disponibles. Sélectionnez le texte à traduire. Passez à l'onglet Modules complémentaires et choisissez Traducteur, l'application de traduction fait son apparition dans le panneau de gauche. Cliquez sur la liste déroulante et choisissez la langue. Le texte est traduit dans la langue choisie. Changez la langue cible : Cliquer sur la liste déroulante en bas du panneau et sélectionnez la langue préférée. La traduction va changer tout de suite."
    },
   {
        "id": "UsageInstructions/Typograf.htm", 
        "title": "Corriger la typographie", 
        "body": "Si vous avez besoin de corriger la typographie de votre texte, utilisez le plugin Typograf qui placera automatiquement les espaces insécables et supprimera les espaces supplémentaires, ainsi que corrigera les fautes de frappe mineures, insérera des guillemets corrects, remplacera les traits d'union par des tirets etc. Ouvrez l'onglet Modules complémentaires et cliquez sur Typograf. Cliquez sur le bouton Afficher les paramètres avancés. Choisissez les paramètres régionaux et les règles que vous souhaitez appliquer à votre texte. Sélectionnez le texte que vous souhaitez corriger. Cliquez sur le bouton Corriger le texte. Pour plus d'informations sur le plug-in Typograf et son installation, veuillez consulter la page de plug-in dans AppDirectory."
    },
   {
        "id": "UsageInstructions/UseMailMerge.htm", 
        "title": "Utiliser le Publipostage", 
        "body": "Remarque : cette option n'est disponible que dans la version en ligne. La fonctionnalité Publipostage est utilisée pour créer un ensemble de documents combinant un contenu commun provenant d'un document texte et des composants individuels (variables, tels que des noms, des messages d'accueil, etc.) extraits d'une feuille de calcul (d'une liste de clients par exemple). Elle peut se révéler utile si vous devez créer beaucoup de lettres personnalisées à envoyer aux destinataires. Préparer une source de données et la charger dans le document principal La source de données utilisée pour le publipostage doit être une feuille de calcul .xlsx stockée sur votre portail. Ouvrez une feuille de calcul existante ou créez-en une nouvelle et assurez-vous qu'elle réponde aux exigences suivantes. La feuille de calcul doit comporter une ligne d'en-tête avec les titres des colonnes, car les valeurs de la première cellule de chaque colonne désignent des champs de fusion (c'est-à-dire des variables que vous pouvez insérer dans le texte). Chaque colonne doit contenir un ensemble de valeurs réelles pour une variable. Chaque ligne de la feuille de calcul doit correspondre à un enregistrement distinct (c'est-à-dire un ensemble de valeurs appartenant à un destinataire donné). Pendant le processus de fusion, une copie du document principal sera créée pour chaque enregistrement et chaque champ de fusion inséré dans le texte principal sera remplacé par une valeur réelle de la colonne correspondante. Si vous devez envoyer le résultat par courrier électronique, la feuille de calcul doit également inclure une colonne avec les adresses électroniques des destinataires. Ouvrez un document texte existant ou créez-en un nouveau. Il doit contenir le texte principal qui sera le même pour chaque version du document fusionné. Cliquez sur l'icône Fusionner dans l'onglet Accueil de la barre d'outils supérieure et sélectionnez l'emplacement de la source de données : Depuis un fichier, À partir de l'URL ou À partir de l'espace de stockage. Sélectionnez le fichier nécessaire ou collez l'adresse et cliquez sur OK. Une fois la source de données chargée, l'onglet Paramètres de publipostage sera disponible dans la barre latérale droite. Vérifier ou modifier la liste des destinataires Cliquez sur le bouton Modifier la liste des destinataires en haut de la barre latérale droite pour ouvrir la fenêtre Fusionner les destinataires, où le contenu de la source de données sélectionnée est affiché. Ici, vous pouvez ajouter de nouvelles informations, modifier ou supprimer les données existantes, si nécessaire. Pour simplifier l'utilisation des données, vous pouvez utiliser les icônes situées au haut de la fenêtre : et pour copier et coller les données copiées et pour annuler et rétablir les actions et - pour trier vos données dans une plage sélectionnée de cellules dans l'ordre croissant ou décroissant - pour activer le filtre pour la plage de cellules sélectionnée précédemment ou supprimer le filtre appliqué - pour effacer tous les paramètres de filtre appliqués Remarque : pour en savoir plus sur l'utilisation des filtres, reportez-vous à la section Trier et filtrer les données de l'aide du Tableur. - pour rechercher une certaine valeur et la remplacer par une autre, si nécessaire Remarque : pour en savoir plus sur l'utilisation de l'outil Rechercher et remplacer, reportez-vous à la section Fonctions rechercher et remplacer de l'aide du Tableur. Une fois toutes les modifications nécessaires effectuées, cliquez sur le bouton Enregistrer et quitter. Pour annuler les modifications, cliquez sur le bouton Fermer. Insérer des champs de fusion et vérifier les résultats Placez le curseur de la souris dans le texte du document principal où vous souhaitez insérer un champ de fusion, cliquez sur le bouton Insérer un champ de fusion dans la barre latérale droite et sélectionnez le champ voulu dans la liste. Les champs disponibles correspondent aux données de la première cellule de chaque colonne de la source de données sélectionnée. Ajoutez tous les champs que vous voulez n'importe où dans le document. Activez l'option Mettre en surbrillance les champs de fusion dans la barre latérale droite pour rendre les champs insérés plus visibles dans le texte du document. Activez le sélecteur Aperçu des résultats dans la barre latérale droite pour afficher le texte du document avec les champs de fusion remplacés par les valeurs réelles de la source de données. Utilisez les boutons fléchés pour prévisualiser les versions du document fusionné pour chaque enregistrement. Pour supprimer un champ inséré, désactivez le mode Aperçu des résultats, sélectionnez le champ avec la souris et appuyez sur la touche Suppr du clavier. Pour remplacer un champ inséré, désactivez le mode Aperçu des résultats, sélectionnez le champ avec la souris, cliquez sur le bouton Insérer un champ de fusion dans la barre latérale de droite et choisissez un nouveau champ dans la liste. Spécifier les paramètres de fusion Sélectionnez le type de fusion. Vous pouvez lancer le publipostage ou enregistrer le résultat sous forme de fichier au format PDF ou Docx pour pouvoir l'imprimer ou le modifier ultérieurement. Sélectionnez l'option voulue dans la liste Fusionner vers : PDF - pour créer un document unique au format PDF qui inclut toutes les copies fusionnées afin que vous puissiez les imprimer plus tard Docx - pour créer un document unique au format Docx qui inclut toutes les copies fusionnées afin que vous puissiez éditer les copies individuelles plus tard Email - pour envoyer les résultats aux destinataires par email Remarque : les adresses e-mail des destinataires doivent être spécifiées dans la source de données chargée et vous devez disposer d'au moins un compte de messagerie connecté dans le module Mail de votre portail. Choisissez les enregistrements auxquels vous voulez appliquer la fusion : Tous les enregistrements (cette option est sélectionnée par défaut) - pour créer des documents fusionnés pour tous les enregistrements de la source de données chargée Enregistrement actuel - pour créer un document fusionné pour l'enregistrement actuellement affiché De ... À - pour créer des documents fusionnés pour une série d'enregistrements (dans ce cas, vous devez spécifier deux valeurs : le numéro du premier enregistrement et celui du dernier enregistrement dans la plage souhaitée) Remarque : la quantité maximale autorisée de destinataires est de 100. Si vous avez plus de 100 destinataires dans votre source de données, exécutez le publipostage par étapes : spécifiez les valeurs comprises entre 1 et 100, attendez la fin du processus de fusion, puis répétez l'opération en spécifiant les valeurs comprises entre 101 et N etc. Terminer la fusion Si vous avez décidé d'enregistrer les résultats de la fusion sous forme de fichier, cliquez sur le bouton Télécharger pour stocker le fichier n'importe où sur votre PC. Vous trouverez le fichier téléchargé dans votre dossier Téléchargements par défaut. cliquez sur le bouton Enregistrer pour enregistrer le fichier sur votre portail. Dans la fenêtre Dossier de sauvegarde qui s'ouvre, vous pouvez modifier le nom du fichier et spécifier le dossier dans lequel vous souhaitez enregistrer le fichier. Vous pouvez également cocher la case Ouvrir le document fusionné dans un nouvel onglet pour vérifier le résultat une fois le processus de fusion terminé. Enfin, cliquez sur Enregistrer dans la fenêtre Dossier de sauvegarde. Si vous avez sélectionné l'option Email, le bouton Fusionner sera disponible dans la barre latérale droite. Après avoir cliqué dessus, la fenêtre Envoyer par Email s'ouvre : Dans la liste De, sélectionnez le compte de messagerie que vous souhaitez utiliser pour l'envoi du mail, si plusieurs comptes sont connectés dans le module Courrier. Dans la liste À, sélectionnez le champ de fusion correspondant aux adresses e-mail des destinataires, s'il n'a pas été sélectionné automatiquement. Entrez l'objet de votre message dans le champ Objet. Sélectionnez le format du mail dans la liste déroulante : HTML, Joindre en DOCX ou Joindre en PDF. Lorsque l'une des deux dernières options est sélectionnée, vous devez également spécifier le Nom du fichier pour les pièces jointes et entrer le Message (le texte qui sera envoyé aux destinataires). Cliquez sur le bouton Envoyer. Une fois l'envoi terminé, vous recevrez une notification à votre adresse e-mail spécifiée dans le champ De."
    },
   {
        "id": "UsageInstructions/ViewDocInfo.htm", 
        "title": "Afficher les informations sur le document", 
        "body": "Pour accéder aux informations détaillées sur le document actuellement édité dans l'Éditeur de Documents, cliquez sur l'onglet Fichier de la barre d'outils supérieure et sélectionnez l'option Descriptif du document.... Informations générales Le descriptif du document comprend l'ensemble des propriétés d'un document. Certains de ces paramètres sont mis à jour automatiquement mais les autres peuvent être modifiés. Emplacement - le dossier dans le module Documents où le fichier est stocké. Propriétaire - le nom de l'utilisateur qui a créé le fichier. Chargé - la date et l'heure quand le fichier a été créé. Ces paramètres ne sont disponibles que sous la version en ligne. Statistiques - le nombre de pages, paragraphes, mots, symboles, symboles avec des espaces. Titre, sujet, commentaire - ces paramètres facilitent la classification des documents. Vos pouvez saisir l'information nécessaire dans les champs appropriés. Dernière modification - la date et l'heure quand le fichier a été modifié la dernière fois. Dernière modification par - le nom de l'utilisateur qui a apporté la dernière modification au document. Cette option est disponible pour édition collaborative du document quand plusieurs utilisateurs travaillent sur un même document. Application - l'application dans laquelle on a créé le document. Auteur - la personne qui a créé le fichier. Saisissez le nom approprié dans ce champ. Appuyez sur la touche Entrée pour ajouter un nouveau champ et spécifier encore un auteur. Si vous avez modifié les paramètres du fichier, cliquez sur Appliquer pour enregistrer les modifications. Remarque : Les Éditeurs en ligne vous permettent de modifier le titre du document directement à partir de l'interface de l'éditeur. Pour ce faire, cliquez sur l'onglet Fichier de la barre d'outils supérieure et sélectionnez l'option Renommer..., puis entrez le Nom de fichier voulu dans la nouvelle fenêtre qui s'ouvre et cliquez sur OK. Informations d'autorisation Dans la version en ligne, vous pouvez consulter les informations sur les permissions des fichiers stockés dans le cloud. Remarque : cette option n'est disponible que pour les utilisateurs disposant des autorisations en Lecture seule. Pour savoir qui a le droit d'afficher ou de modifier le document, sélectionnez l'option Droits d'accès... dans la barre latérale de gauche. Vous pouvez également changer les droits d'accès actuels en cliquant sur le bouton Changer les droits d'accès dans la section Personnes qui ont des droits. Historique des versions Dans la version en ligne, vous pouvez consulter l'historique des versions des fichiers stockés dans le cloud. Remarque : cette option n'est pas disponible pour les utilisateurs disposant des autorisations en Lecture seule. Pour afficher toutes les modifications apportées à ce document, sélectionnez l'option Historique des versions dans la barre latérale de gauche. Il est également possible d'ouvrir l'historique des versions à l'aide de l'icône Historique des versions de l'onglet Collaboration de la barre d'outils supérieure. Vous verrez la liste des versions de ce document (changements majeurs) et des révisions (modifications mineures) avec l'indication de l'auteur de chaque version/révision et la date et l'heure de création. Pour les versions de document, le numéro de version est également spécifié (par exemple ver. 2). Pour savoir exactement quels changements ont été apportés à chaque version/révision, vous pouvez voir celle qui vous intéresse en cliquant dessus dans la barre latérale de gauche. Les modifications apportées par l'auteur de la version/révision sont marquées avec la couleur qui est affichée à côté du nom de l'auteur dans la barre latérale gauche. Vous pouvez utiliser le lien Restaurer sous la version/révision sélectionnée pour la restaurer. Pour revenir à la version actuelle du document, utilisez l'option Fermer l'historique en haut de la liste des versions. Pour fermer l'onglet Fichier et reprendre le travail sur votre document, sélectionnez l'option Retour au document."
    },
   {
        "id": "UsageInstructions/WordCounter.htm", 
        "title": "Compter les mots", 
        "body": "Pour connaître le nombre exact de mots et de symboles avec et sans espaces dans votre document, ainsi que le nombre total de paragraphes, utilisez le plug-in Compter les mots. Ouvrez l'onglet Modules complémentaires et cliquez sur Compter les mots et les caractères. Sélectionnez une partie du texte ou le texte entier. Veuillez noter que les éléments suivants ne sont pas inclus dans le nombre total de mots : caractères dans les notes de bas de page/notes de fin, numéros des listes numérotées, numéros des pages. Pour plus d'informations sur le plug-in Compter les mots et son installation, veuillez consulter la page de plug-in dans AppDirectory."
    },
   {
        "id": "UsageInstructions/Wordpress.htm", 
        "title": "Télécharger un document sur Wordpress", 
        "body": "Vous pouvez écrire vos articles dans l'environnement de l'Éditeur de documents ONLYOFFICE et les télécharger sur Wordpress. Se connecter à Wordpress Ouvrez un document. Passez à l'onglet Module complémentaires et choisissez Wordpress. Connectez-vous à votre compte Wordpress et choisissez la page web à laquelle vous souhaitez ajouter votre document. Tapez le titre de votre article. Cliquer sur Publier pour le publier tout de suite ou sur Enregistrer comme brouillon pour le publier plus tard de votre site ou application Wordpress."
    },
   {
        "id": "UsageInstructions/YouTube.htm", 
        "title": "Insérer une vidéo", 
        "body": "Vous pouvez insérer une vidéo dans votre document dans l'Éditeur de Documents. Celle-ci sera affichée comme une image. Faites un double-clic sur l'image pour faire apparaître la boîte de dialogue vidéo. Vous pouvez démarrer votre vidéo ici. Copier l'URL de la vidéo à insérer. (l'adresse complète dans la barre d'adresse du navigateur) Accédez à votre document et placez le curseur à l'endroit où la vidéo doit être insérée. Passezà l'onglet Modules compléméntaires et choisissez YouTube. Collez l'URL et cliquez sur OK. Vérifiez si la vidéo est vrai et cliquez sur OK au-dessous la vidéo. Maintenant la vidéo est insérée dans votre document."
    },
   {
        "id": "UsageInstructions/insertdropcap.htm", 
        "title": "Insérer une lettrine", 
        "body": "Dans l'Éditeur de Documents, une Lettrine est une lettre initiale majuscule placée au début d'un paragraphe ou d'une section. La taille d'une lettrine est généralement plusieurs lignes. Pour ajouter une lettrine, placez le curseur à l'intérieur du paragraphe dans lequel vous voulez insérer une lettrine, passez à l'onglet Insérer de la barre d'outils supérieure, cliquez sur l'icône Lettrine sur la barre d'outils supérieure, sélectionnez l'option nécessaire dans la liste déroulante : Dans le texte - pour insérer une lettrine dans le paragraphe. Dans la marge - pour placer une lettrine dans la marge gauche. La lettre initiale du paragraphe sélectionné sera transformée en une lettrine. Si vous avez besoin d'ajouter quelques lettres, vous pouvez le faire manuellement : sélectionnez la lettrine et tapez les lettres nécessaires. Pour régler l'apparence de la lettrine (par exemple, taille de police, type, style de décoration ou couleur), sélectionnez la lettre et utilisez les icônes correspondantes sous l'onglet Accueil sur la barre d'outils supérieure. La lettrine sélectionnée est entourée par un cadre (un conteneur utilisé pour positionner la lettrine sur la page). Vous pouvez facilement changer la taille du cadre en faisant glisser ses bordures ou changer sa position en utilisant l'icône qui apparaît si vous positionnez le curseur sur le cadre. Pour supprimer la lettrine ajoutée, sélectionnez-la, cliquez sur l'icône Lettrine sous l'onglet Insérer sur la barre d'outils supérieure et choisissez l'option Aucune dans la liste déroulante. Pour modifier les paramètres de la lettrine ajoutée, sélectionnez-la, cliquez sur l'icône Lettrine sous l'onglet Insérer sur la barre d'outils supérieure et choisissez l'option Paramètres de la lettrine dans la liste déroulante. La fenêtre Lettrine - Paramètres avancés s'ouvre : L'onglet Lettrine vous permet de régler les paramètres suivants : Position sert à changer l'emplacement de la lettrine. Sélectionnez l'option Dans le texte ou Dans la marge, ou cliquez sur Aucune pour supprimer la lettrine. Police sert à sélectionner la police dans la liste des polices disponibles. Hauteur des lignes sert à spécifier le nombre des lignes occupées par la lettrine. Il est possible de sélectionner de 1 à 10 lignes. Distance du texte sert à spécifier l'espace entre le texte du paragraphe et la bordure droite du cadre qui entoure la lettrine. L'onglet Bordures et remplissage vous permet d'ajouter une bordure autour de la lettrine et de régler ses paramètres. Ils sont les suivants : Paramètres de la Bordure (taille, couleur, sa présence ou absence) - définissez la taille des bordures, sélectionnez leur couleur et choisissez les bordures auxquelles (en haut, en bas, à gauche, à droite ou quelques unes à la fois) vous voulez appliquer ces paramètres. Couleur d'arrière-plan - choisissez la couleur pour l'arrère-plan de la lettrine. L'onglet Marges vous permet de définir la distance entre la lettrine et les bordures En haut, En bas, A gauche et A droite autour d'elle (si les bordures ont été préalablement ajoutées). Après avoir ajouté la lettrine vous pouvez également changer les paramètres du Cadre. Pour y accéder, cliquez droit à l'intérieur du cadre et sélectionnez l'option Paramètres avancées du cadre du menu contextuel. La fenêtre Cadre - Paramètres avancés s'ouvre : L'onglet Cadre vous permet de régler les paramètres suivants : Position sert à sélectionner une des styles d'habillage Aligné ou Flottant. Ou vous pouvez cliquer sur Aucune pour supprimer le cadre. Largeur et Hauteur servent à changer la taille du cadre. L'option Auto vous permet de régler la taille du cadre automatiquement en l'ajustant à la lettrine à l'intérieur. L'option Exactement vous permet de spécifier les valeurs fixes. L'option Au moins est utilisée pour définir la hauteur minimale (si vous changez la taille du cadre, la hauteur du cadre change en conséquence, mais elle ne peut pas être inférieure à la valeur spécifiée). Horizontal sert à définir la position exacte du cadre des unités sélectionnées par rapport à la marge, la page ou la colonne, ou à aligner le cadre (à gauche, au centre ou à droite) par rapport à un des points de référence. Vous pouvez également définir la Distance du texte horizontale c'est-à-dire l'espace entre les bordures verticales du cadre et le texte du paragraphe. Vertical sert à définir la position exacte du cadre des unités sélectionnées par rapport à la marge, la page ou le paragraphe, ou à aligner le cadre (en haut, au centre ou en bas) par rapport à un des points de référence. Vous pouvez également définir la Distance du texte verticale c'est-à-dire l'espace entre les bordures horizontales et le texte du paragraphe. Déplacer avec le texte contrôle si la lettrine se déplace comme le paragraphe auquel elle est liée. Les onglets Bordures et remplissage et Marges permettent de définir les mêmes paramètres que dans les onglets de la fenêtre Lettrine - Paramètres avancés."
    }
]