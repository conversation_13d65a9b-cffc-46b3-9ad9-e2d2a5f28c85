﻿<!DOCTYPE html>
<html>
	<head>
        <title>Formats des documents électroniques pris en charge</title>
        <meta charset="utf-8" />
        <meta name="description" content="Formats des documents électroniques pris en charge par l'Éditeur de Documents" />
        <link type="text/css" rel="stylesheet" href="../editor.css" />
        <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
    </head>
    <body>
        <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Rechercher" type="text" onkeypress="doSearch(event)">
        </div>
		<h1>Formats des documents électroniques pris en charge</h1>
		<p>
			Les documents électroniques représentent l'un des types des fichiers les plus utilisés en informatique.
            Grâce à l'utilisation du réseau informatique tant développé aujourd'hui, il est possible et plus pratique de distribuer des documents électroniques que des versions imprimées.
            Les formats de fichier ouverts et propriétaires sont bien nombreux à cause de la variété des périphériques utilisés pour la présentation des documents.
            <a href="https://www.onlyoffice.com/fr/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)">Éditeur de Documents</a> prend en charge les formats les plus populaires.
		</p>
            <p class="note">Lors du téléchargement ou de l'ouverture d'un fichier, celui-ci sera converti au format Open Office XML (DOCX). Cette conversion permet d'accélérer le traitement des fichiers et d'améliorer l'interopérabilité des données.</p>
            <p>Le tableau ci-dessous présente les formats de fichiers pour l'affichage et/ou pour l'édition.</p>
			<table>
				<tr>
					<td><b>Formats</b></td>
					<td><b>Description</b></td>
					<td>Affichage au format natif</td>
                    <td>Affichage lors de la conversion en OOXML</td>
					<td>Édition au format natif</td>
                    <td>Édition lors de la conversion en OOXML</td>
				</tr>
                <tr>
                    <td>DjVu</td>
                    <td>Le format de fichier conçu principalement pour stocker les documents numérisés, en particulier ceux qui contiennent une combinaison du texte, des dessins au trait et des photographies</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td></td>
                </tr>
				<tr>
					<td>DOC</td>
					<td>L'extension de nom de fichier pour les documents du traitement textuel créé avec Microsoft Word</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
					<td>+</td>
				</tr>
                <tr>
                    <td>DOCM</td>
                    <td>Macro-Enabled Microsoft Word Document<br /> Une extension de fichier Microsoft Word 2007 ou version ultérieure comportant des macros incorporées pouvant être exécutées dans le document. </td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                </tr>
				<tr>
                    <td>DOCX</td>
					<td>Office Open XML<br />Le format de fichier compressé basé sur XML développé par Microsoft pour représenter des feuilles de calcul et les graphiques, les présentations et les document du traitement textuel</td>
					<td>+</td>
                    <td></td>
					<td>+</td>
                    <td></td>
				</tr>
                <tr>
                    <td>DOCXF</td>
                    <td>Le format pour créer, modifier et collaborer sur un Modèle de formulaire.</td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                </tr>
                <tr>
                    <td>DOTX</td>
                    <td>Word Open XML Document Template<br />Format de fichier zippé, basé sur XML, développé par Microsoft pour les modèles de documents texte. Un modèle DOTX contient des paramètres de mise en forme, des styles, etc. et peut être utilisé pour créer plusieurs documents avec la même mise en forme</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                </tr>
                <tr>
                    <td>EPUB</td>
                    <td>Electronic Publication<br />Le format ebook standardisé, gratuit et ouvert créé par l'International Digital Publishing Forum</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                </tr>
				<tr>
					<td>FB2</td>
					<td>Une extension de livres électroniques qui peut être lancé par votre ordinateur ou appareil mobile</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
					<td>+</td>
				</tr>
                <tr>
                    <td>HTML</td>
                    <td>HyperText Markup Language<br />Le principale langage de balisage pour les pages web</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                </tr>
				<tr>
					<td>ODT</td>
					<td>Le format de fichier du traitement textuel d'OpenDocument, le standard ouvert pour les documents électroniques</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
					<td>+</td>
				</tr>
                <tr>
                    <td>OFORM</td>
                    <td>Le format pour remplir un formulaire. Les champs du formulaire sont à remplir mais les utilisateurs ne peuvent pas modifier la mise en forme ou les paramètres des éléments du formulaire.*</td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                </tr>
                <tr>
                    <td>OTT</td>
                    <td>OpenDocument Document Template<br />Format de fichier OpenDocument pour les modèles de document texte. Un modèle OTT contient des paramètres de mise en forme, des styles, etc. et peut être utilisé pour créer plusieurs documents avec la même mise en forme</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                </tr>
                <tr>
                    <td>PDF</td>
                    <td>Portable Document Format<br />Format de fichier utilisé pour représenter les documents d'une manière indépendante du logiciel, du matériel et des systèmes d'exploitation</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td></td>
                </tr>
                <tr>
                    <td>PDF/A</td>
                    <td>Portable Document Format / A<br />Une version normalisée ISO du format PDF (Portable Document Format) conçue pour l'archivage et la conservation à long terme des documents électroniques. </td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td></td>
                </tr>
				<tr>
					<td>RTF</td>
					<td>Rich Text Format<br />Le format de fichier du document développé par Microsoft pour la multiplateforme d'échange des documents</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
					<td>+</td>
				</tr>
				<tr>
					<td>TXT</td>
					<td>L'extension de nom de fichier pour les fichiers de texte contenant habituellement une mise en forme minimale</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
					<td>+</td>
				</tr>
                <tr>
                    <td>XML</td>
                    <td>Extensible Markup Language (XML).<br />Le langage de balisage extensible est une forme restreinte d'application du langage de balisage généralisé standard SGM (ISO 8879) conçu pour stockage et traitement de données.</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                </tr>
				<tr>
					<td>XPS</td>
					<td>Open XML Paper Specification<br />Le format ouvert de la mise en page fixe, libre de redevance créé par Microsoft</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
					<td></td>
				</tr>
			</table>
			<p class="note"><b>*Remarque</b> : le format OFORM c'est un format qui sert à remplir un formulaire. Alors, ce sont des champs du formulaire qui sont modifiables.</p>
            <p>Le tableau ci-dessous présente les formats pris en charge pour le téléchargement d'un document dans le menu <b>Fichier</b> -> <b>Télécharger comme</b>.</p>
            <table>
                <tr>
                    <td><b>Format en entrée</b></td>
                    <td><b>Téléchargeable comme</b></td>
                </tr>
                <tr>
                    <td>DjVu</td>
                    <td>DjVu, PDF</td>
                </tr>
                <tr>
                    <td>DOC</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
                </tr>
                <tr>
                    <td>DOCM</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
                </tr>
                <tr>
                    <td>DOCX</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
                </tr>
                <tr>
                    <td>DOCXF</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
                </tr>
                <tr>
                    <td>DOTX</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
                </tr>
                <tr>
                    <td>EPUB</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
                </tr>
                <tr>
                    <td>FB2</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
                </tr>
                <tr>
                    <td>HTML</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
                </tr>
                <tr>
                    <td>ODT</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
                </tr>
                <tr>
                    <td>OFORM</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
                </tr>
                <tr>
                    <td>OTT</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
                </tr>
                <tr>
                    <td>PDF</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, OFORM, PDF, RTF, TXT</td>
                </tr>
                <tr>
                    <td>PDF/A</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, OFORM, PDF, RTF, TXT</td>
                </tr>
                <tr>
                    <td>RTF</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
                </tr>
                <tr>
                    <td>TXT</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
                </tr>
                <tr>
                    <td>XML</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
                </tr>
                <tr>
                    <td>XPS</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT, XPS</td>
                </tr>
            </table>
            <p>Veuillez consulter la matrice de conversion sur <a href="https://api.onlyoffice.com/editors/conversionapi#text-matrix" target="_blank" onclick="onhyperlinkclick(this)"><b>api.onlyoffice.com</b></a> pour vérifier s'il est possible de convertir vos documents dans des formats les plus populaires.</p>
		</div>
	</body>
</html>