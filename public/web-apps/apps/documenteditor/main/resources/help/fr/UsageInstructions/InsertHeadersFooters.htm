﻿<!DOCTYPE html>
<html>
	<head>
		<title>Insérer les en-têtes et pieds de page</title>
		<meta charset="utf-8" />
		<meta name="description" content="Insérer les en-têtes et les pieds de page dans votre document, ajouter de différents en-têtes et les pieds de page pour la première page et pour les pages paires et impaires." />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Rechercher" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Insérer les en-têtes et les pieds de page</h1>
			<p>Pour ajouter ou supprimer un en-tête ou un pied de page à votre document ou modifier ceux qui déjà existent dans l'<a target="_blank" href="https://www.onlyoffice.com/fr/document-editor.aspx" onclick="onhyperlinkclick(this)"><b>Éditeur de Documents</b></a>,</p>
			<ol>
                <li>passez à l'onglet <b>Insérer</b> de la barre d'outils supérieure,</li>
				<li>cliquez sur l'icône <div class = "icon icon-headerfooter"></div> <b>En-tête/Pied de page</b> sur la barre d'outils supérieure,</li>
				<li>sélectionnez l'une des options suivantes :
					<ul>
						<li><b>Modifier l'en-tête</b> pour insérer ou modifier le texte d'en-tête.</li>
						<li><b>Modifier le pied de page</b> pour insérer ou modifier le texte de pied de page.</li>
                        <li><b>Supprimer l'en-tête</b> pour supprimer un en-tête.</li>
                        <li><b>Supprimer le pied de page</b> pour supprimer un pied de page.</li>
					</ul>
				</li>
				<li>modifiez les paramètres actuels pour les en-têtes ou les pieds de page sur la barre latérale droite :
					<p><img alt="Barre latérale droite - Paramètres de l'en-tête ou du pied de page" src="../images/right_headerfooter.png" /></p>
					<ul>
						<li>Définissez la <b>Position</b> du texte par rapport à la partie supérieure pour les en-têtes ou à la partie inférieure pour pieds de la page.</li>
						<li>Cochez la case <b>Première page différente</b> pour appliquer un en-tête ou un pied de page différent pour la première page ou si vous ne voulez pas ajouter un en-tête / un pied de page.</li>
						<li>Utilisez la case <b>Pages paires et impaires différentes</b> pour ajouter de différents en-têtes ou pieds de page pour les pages paires et impaires.</li>
						<li>L'option <b>Lier au précédent</b> est disponible si vous avez déjà ajouté des <a href="../UsageInstructions/SectionBreaks.htm" onclick="onhyperlinkclick(this)">sections</a> dans votre document. Sinon, elle sera grisée. En outre, cette option est non disponible pour toute première section (c'est-à-dire quand un en-tête ou un pied qui appartient à la première section est choisi). Par défaut, cette case est cochée, alors que les mêmes en-têtes/pieds de page sont appliqués à toutes les sections. Si vous sélectionnez une zone d'en-tête ou de pied de page, vous verrez que cette zone est marquée par l'étiquette <b>Identique au précédent</b>. Décochez la case <b>Lien vers précédent</b> pour utiliser de différents en-têtes et pieds de page pour chaque section du document. L'étiquette <b>Identique au précédent</b> ne sera plus affichée.</li>
					</ul>
					<p><img alt="L'étiquette Identique au précédent " src="../images/sameasprevious_label.png" /></p>
				</li>
			</ol>
			<p>Pour saisir un texte ou modifier le texte déjà saisi et régler les paramètres de l'en-tête ou du pied de page, vous pouvez également double-cliquer sur la partie supérieure ou inférieure de la page ou cliquer avec le bouton droit de la souris et sélectionner l'option - <b>Modifier l'en-tête</b> ou <b>Modifier le pied de page</b> du menu contextuel.</p>
			<p>Pour passer au corps du document, double-cliquez sur la zone de travail. Le texte que vous utilisez dans l'en-tête ou dans le pied de page sera affiché en gris.</p>
			<p class="note">Remarque : consultez la section <a href="InsertPageNumbers.htm" onclick="onhyperlinkclick(this)">Insérer les numéros de page</a> pour apprendre à ajouter des numéros de page à votre document.</p>
		</div>
	</body>
</html>
