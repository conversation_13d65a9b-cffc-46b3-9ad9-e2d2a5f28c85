﻿<!DOCTYPE html>
<html>
	<head>
		<title>Définir le type de police, la taille et la couleur</title>
		<meta charset="utf-8" />
		<meta name="description" content="Définir les paramètres de mise en forme du texte : le type, le taille et la couleur de police" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Rechercher" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Définir le type de police, la taille et la couleur</h1>
            <p>Dans l'<a href="https://www.onlyoffice.com/fr/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Éditeur de Documents</b></a>, vous pouvez sélectionner le type, la taille et la couleur de police à l'aide des icônes correspondantes situées dans l'onglet <b>Accueil</b> de la barre d'outils supérieure.</p>
			<p class="note">Si vous voulez appliquer la mise en forme au texte déjà saisi, sélectionnez-le avec la souris ou <a href="../HelpfulHints/KeyboardShortcuts.htm#textselection" onclick="onhyperlinkclick(this)">en utilisant le clavier</a> et appliquez la mise en forme appropriée. Vous pouvez aussi positionner le curseur de la souris sur le mot à mettre en forme.</p>
            <table>
				<tr>
                    <td width="10%">Nom de la police</td>
                    <td width="15%"><div class = "big big-fontfamily"></div></td>
					<td>Sert à sélectionner l'une des polices disponibles dans la liste. <span class="desktopDocumentFeatures">Si une police requise n'est pas disponible dans la liste, vous pouvez la télécharger et l'installer sur votre système d'exploitation, après quoi la police sera disponible pour utilisation dans la <I>version de bureau</I>.</span></td>
				</tr>
				<tr>
					<td>Taille de la police</td>
					<td><div class = "icon icon-fontsize"></div></td>
					<td>Sert à sélectionner la taille de la police parmi les valeurs disponibles dans la liste déroulante, les valeurs par défaut sont : 8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 36, 48, 72 et 96).  Il est également possible d'entrer manuellement une valeur personnalisée dans le champ de taille de police jusqu'à 300 pt. Appuyer sur la touche Entrée pour confirmer</td>
				</tr>
                <tr>
                    <td>Augmenter la taille de la police</td>
                    <td><div class = "icon icon-larger"></div></td>
					<td>Sert à modifier la taille de la police en la rendant plus grande à un point chaque fois que vous appuyez sur le bouton.</td>
                </tr>
                <tr>
                    <td>Diminuer la taille de la police</td>
                    <td><div class = "icon icon-smaller"></div></td>
					<td>Sert à modifier la taille de la police en la rendant plus petite à un point chaque fois que vous appuyez sur le bouton.</td>
                </tr>
				<tr>
					<td>Modifier la casse</td>
					<td><div class = "icon icon-change_case"></div></td>
					<td>Sert à modifier la casse du texte. <em>Majuscule en début de phrase</em> - la casse à correspondre la casse de la proposition ordinaire. <em>minuscule</em> - mettre en minuscule toutes les lettres. <em>MAJUSCULES</em> - mettre en majuscule toutes les lettres. <em>Mettre en majuscule chaque mot</em> - mettre en majuscule la première lettre de chaque mot. <em>Inverser la casse</em> - basculer entre d'affichages de la casse du texte ou le mot sur lequel le curseur de la souris est positionné.</td>
				</tr>
				<tr>
					<td>Couleur de surlignage</td>
					<td><div class = "icon icon-highlightcolor"></div></td>
					<td>Est utilisé pour marquer des phrases, des fragments, des mots ou même des caractères séparés en ajoutant une bande de couleur qui imite l'effet du surligneur sur le texte. Vous pouvez sélectionner la partie voulue du texte, puis cliquer sur la flèche vers le bas à côté de l'icône pour sélectionner une couleur dans la palette (cet ensemble de couleurs ne dépend pas du <b>Jeux de couleurs</b> sélectionné et comprend 16 couleurs). La couleur sera appliquée à la sélection. Alternativement, vous pouvez d'abord choisir une couleur de surbrillance et ensuite commencer à sélectionner le texte avec la souris - le pointeur de la souris ressemblera à ceci <div class = "icon icon-highlight_color_mouse_pointer"></div> et vous serez en mesure de surligner plusieurs parties différentes de votre texte de manière séquentielle. Pour enlever la mise en surbrillance, cliquez à nouveau sur l'icône. Pour effacer la couleur de surbrillance, choisissez l'option <b>Pas de remplissage</b>. La <b>Couleur de surlignage</b> est différente de la <a href="../UsageInstructions/BackgroundColor.htm" onclick="onhyperlinkclick(this)"><b>Couleur de fond</b></a> <div class = "icon icon-backgroundcolor"></div> car cette dernière est appliquée au paragraphe entier et remplit complètement l'espace du paragraphe de la marge de page gauche à la marge de page droite.</td>
				</tr>
				<tr>
					<td>Couleur de police</td>
					<td><div class = "icon icon-fontcolor"></div></td>
					<td>Sert à changer la couleur des lettres /caractères dans le texte. Par défaut, la couleur de police automatique est définie dans un nouveau document vide. Elle s'affiche comme la police noire sur l'arrière-plan blanc. Si vous choisissez le noir comme la couleur d'arrière-plan, la couleur de la police se change automatiquement à la couleur blanche pour que le texte soit visible. Pour choisir une autre couleur, cliquez sur la flèche vers le bas située à côté de l'icône et sélectionnez une couleur disponible dans les palettes (les couleurs de la palette <b>Couleurs de thème</b> dépend du <a href="../UsageInstructions/ChangeColorScheme.htm" onclick="onhyperlinkclick(this)">jeu de couleurs</a> sélectionné). Après avoir modifié la couleur de police par défaut, vous pouvez utiliser l'option <b>Automatique </b>dans la fenêtre des palettes de couleurs pour restaurer rapidement la couleur automatique pour le fragment du texte sélectionné.</td>
				</tr>
			</table>
			<p class="note">Pour en savoir plus sur l'utilisation des palettes de couleurs, consultez <a href="../UsageInstructions/ChangeColorScheme.htm" onclick="onhyperlinkclick(this)">cette page</a>.</p>
		</div>
	</body>
</html>