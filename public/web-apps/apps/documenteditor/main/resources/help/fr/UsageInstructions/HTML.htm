﻿<!DOCTYPE html>
<html>
<head>
    <title>Modifier le code HTML</title>
    <meta charset="utf-8" />
    <meta name="description" content="La description du plug-in HTML pour les éditeurs ONLYOFFICE qui permet de modifier le code HTML du texte" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Modifier le code HTML</h1>
        <p>Si vous êtes en train d'écrire une page de site Web dans un éditeur de texte et souhaitez l'obtenir sous forme de code HTML, utilisez le plug-in <b>HTML</b>.</p>
        <ol>
            <li>Ouvrez l'onglet <b>Modules complémentaires</b> et cliquez sur <b>Obtenir et coller du html</b>.</li>
            <li>Sélectionnez le contenu nécessaire.</li>
            <li>Le code HTML du paragraphe sélectionné s'affiche dans le champ de module sur le panneau latéral gauche. Vous pouvez modifier le code pour modifier les caractéristiques du texte, par exemple la <em>taille de la police</em> ou la <em>famille de la police</em> etc.</li>
            <li>Cliquez sur <b>Coller dans le document</b> afin d'insérer le texte avec son code HTML modifié à la position actuelle du curseur dans votre document.</li>
        </ol>
        <p>Vous pouvez également écrire votre propre code HTML (sans sélectionner le contenu du document) et ensuite le coller dans votre document.</p>
        <img class="gif" alt="HTML plugin gif" src="../../images/html_plugin.gif" width="600" />
        <p>Pour plus d'informations sur le plug-in HTML et son installation, veuillez consulter la page de <a href="https://www.onlyoffice.com/fr/app-directory/html">plug-in</a> dans AppDirectory.</p>
    </div>
</body>
</html>