@import "../../../../../common/main/resources/help/images/symbols.css";

.big{
	background-image: url(../images/big.png);
	background-repeat: no-repeat;
	display: inline-block;
}

.big-moving_image {
	background-position: 0px 0px;
	width: 357px;
	height: 286px;
}

.big-picturecontentcontrol {
	background-position: -357px 0px;
	width: 198px;
	height: 238px;
}

.big-image_form_inserted {
	background-position: 0px -286px;
	width: 235px;
	height: 229px;
}

.big-reshaping {
	background-position: -235px -286px;
	width: 123px;
	height: 147px;
}

.big-nestedfraction {
	background-position: -358px -286px;
	width: 146px;
	height: 85px;
}

.big-newslot {
	background-position: -235px -433px;
	width: 192px;
	height: 77px;
}

.big-formatastext {
	background-position: -555px 0px;
	width: 162px;
	height: 75px;
}

.big-resizetable {
	background-position: 0px -515px;
	width: 233px;
	height: 65px;
}

.big-moveelement {
	background-position: -358px -371px;
	width: 188px;
	height: 60px;
}

.big-resizeelement {
	background-position: -233px -515px;
	width: 188px;
	height: 60px;
}

.big-templateslist {
	background-position: -555px -75px;
	width: 83px;
	height: 54px;
}

.big-nonetemplate {
	background-position: -638px -75px;
	width: 63px;
	height: 48px;
}

.big-editedequation2 {
	background-position: -555px -129px;
	width: 97px;
	height: 47px;
}

.big-email_address_inserted {
	background-position: -421px -515px;
	width: 211px;
	height: 45px;
}

.big-checkbox_checked {
	background-position: -555px -176px;
	width: 127px;
	height: 45px;
}

.big-phone_number_inserted {
	background-position: -555px -221px;
	width: 138px;
	height: 44px;
}

.big-radio_button_checked {
	background-position: -555px -265px;
	width: 98px;
	height: 41px;
}

.big-radio_button_inserted {
	background-position: -555px -306px;
	width: 114px;
	height: 39px;
}

.big-checkbox_inserted {
	background-position: -555px -345px;
	width: 82px;
	height: 38px;
}

.big-credit_card_inserted {
	background-position: -555px -383px;
	width: 159px;
	height: 36px;
}

.big-datecontentcontrol {
	background-position: -555px -419px;
	width: 109px;
	height: 33px;
}

.big-indents_ruler {
	background-position: 0px -580px;
	width: 469px;
	height: 30px;
}

.big-tabstops_ruler {
	background-position: 0px -610px;
	width: 465px;
	height: 30px;
}

.big-columnspacing {
	background-position: 0px -640px;
	width: 300px;
	height: 30px;
}

.big-margins {
	background-position: -469px -580px;
	width: 227px;
	height: 30px;
}

.big-select_text_languages {
	background-position: -652px -129px;
	width: 65px;
	height: 28px;
}

.big-fontfamily {
	background-position: -555px -452px;
	width: 87px;
	height: 22px;
}

.big-wrap_boundary {
	background-position: -653px -265px;
	width: 61px;
	height: 22px;
}

.big-date_time_inserted {
	background-position: -555px -474px;
	width: 109px;
	height: 21px;
}

.big-zip_code_inserted {
	background-position: -555px -495px;
	width: 92px;
	height: 20px;
}

.big-pagebreak {
	background-position: -357px -238px;
	width: 185px;
	height: 9px;
}

.big-columnbreak {
	background-position: -421px -560px;
	width: 200px;
	height: 8px;
}

.big-sectionbreak {
	background-position: -465px -610px;
	width: 200px;
	height: 8px;
}

.icon{
	background-image: url(../images/icons.png);
	background-repeat: no-repeat;
	display: inline-block;
}

.icon-insertedequation {
	background-position: 0px 0px;
	width: 41px;
	height: 48px;
}

.icon-editedequation {
	background-position: -41px 0px;
	width: 35px;
	height: 48px;
}

.icon-deleteequation {
	background-position: -76px 0px;
	width: 30px;
	height: 48px;
}

.icon-jitsi_icon {
	background-position: 0px -48px;
	width: 35px;
	height: 47px;
}

.icon-wrappingstyle_behind {
	background-position: -35px -48px;
	width: 43px;
	height: 43px;
}

.icon-wrappingstyle_infront {
	background-position: -106px 0px;
	width: 43px;
	height: 43px;
}

.icon-wrappingstyle_inline {
	background-position: -106px -43px;
	width: 43px;
	height: 43px;
}

.icon-wrappingstyle_square {
	background-position: 0px -95px;
	width: 43px;
	height: 43px;
}

.icon-wrappingstyle_through {
	background-position: -43px -95px;
	width: 43px;
	height: 43px;
}

.icon-wrappingstyle_tight {
	background-position: -86px -95px;
	width: 43px;
	height: 43px;
}

.icon-wrappingstyle_topandbottom {
	background-position: -149px 0px;
	width: 43px;
	height: 43px;
}

.icon-caption_icon {
	background-position: -149px -43px;
	width: 42px;
	height: 42px;
}

.icon-morebutton {
	background-position: -149px -85px;
	width: 23px;
	height: 40px;
}

.icon-jitsi_background {
	background-position: 0px -138px;
	width: 38px;
	height: 38px;
}

.icon-jitsi_micro {
	background-position: -38px -138px;
	width: 38px;
	height: 38px;
}

.icon-jitsi_camera {
	background-position: -76px -138px;
	width: 38px;
	height: 37px;
}

.icon-jitsi_more {
	background-position: -114px -138px;
	width: 37px;
	height: 37px;
}

.icon-jitsi_openchat {
	background-position: -151px -138px;
	width: 37px;
	height: 37px;
}

.icon-jitsi_invite {
	background-position: -192px 0px;
	width: 36px;
	height: 37px;
}

.icon-jitsi_leave {
	background-position: 0px -176px;
	width: 37px;
	height: 36px;
}

.icon-jitsi_settings {
	background-position: -192px -37px;
	width: 36px;
	height: 36px;
}

.icon-jitsi_participants {
	background-position: -192px -73px;
	width: 36px;
	height: 35px;
}

.icon-newequation {
	background-position: -192px -108px;
	width: 20px;
	height: 32px;
}

.icon-checkboxcontentcontrol {
	background-position: -192px -140px;
	width: 29px;
	height: 31px;
}

.icon-checkboxcontentcontrol2 {
	background-position: -37px -176px;
	width: 29px;
	height: 31px;
}

.icon-linenumbers_icon {
	background-position: -66px -176px;
	width: 34px;
	height: 30px;
}

.icon-save_form_icon {
	background-position: -78px -48px;
	width: 25px;
	height: 30px;
}

.icon-arrows_formfilling {
	background-position: -100px -176px;
	width: 31px;
	height: 29px;
}

.icon-next_formfilling {
	background-position: -131px -176px;
	width: 31px;
	height: 29px;
}

.icon-highlight {
	background-position: -162px -176px;
	width: 28px;
	height: 28px;
}

.icon-mendeley {
	background-position: -190px -176px;
	width: 28px;
	height: 28px;
}

.icon-ocr {
	background-position: -228px 0px;
	width: 28px;
	height: 28px;
}

.icon-photoeditor {
	background-position: -228px -28px;
	width: 28px;
	height: 28px;
}

.icon-speech {
	background-position: -228px -56px;
	width: 28px;
	height: 28px;
}

.icon-wordpress {
	background-position: -228px -84px;
	width: 28px;
	height: 28px;
}

.icon-youtube {
	background-position: -228px -112px;
	width: 28px;
	height: 28px;
}

.icon-thumbnailsettingicon {
	background-position: 0px -212px;
	width: 34px;
	height: 27px;
}

.icon-cross_refference_icon {
	background-position: -228px -140px;
	width: 26px;
	height: 26px;
}

.icon-favorites_iconpdf {
	background-position: -228px -166px;
	width: 26px;
	height: 26px;
}

.icon-translator {
	background-position: -34px -212px;
	width: 26px;
	height: 26px;
}

.icon-next_field_icon {
	background-position: -60px -212px;
	width: 25px;
	height: 25px;
}

.icon-previous_field_icon {
	background-position: -85px -212px;
	width: 25px;
	height: 25px;
}

.icon-submit_form_icon {
	background-position: -110px -212px;
	width: 25px;
	height: 25px;
}

.icon-filelocationiconpdf {
	background-position: -135px -212px;
	width: 26px;
	height: 24px;
}

.icon-drawio {
	background-position: -161px -212px;
	width: 24px;
	height: 24px;
}

.icon-help {
	background-position: -185px -212px;
	width: 24px;
	height: 24px;
}

.icon-rainbow_icon {
	background-position: -209px -212px;
	width: 24px;
	height: 24px;
}

.icon-telegram_icon {
	background-position: -256px 0px;
	width: 24px;
	height: 24px;
}

.icon-downloadpdf {
	background-position: -256px -24px;
	width: 22px;
	height: 24px;
}

.icon-printiconpdf {
	background-position: -256px -48px;
	width: 22px;
	height: 24px;
}

.icon-slicer_icon {
	background-position: -256px -72px;
	width: 21px;
	height: 24px;
}

.icon-spellchecking_toptoolbar_activated {
	background-position: -256px -96px;
	width: 21px;
	height: 24px;
}

.icon-usericonpdf {
	background-position: 0px -239px;
	width: 25px;
	height: 23px;
}

.icon-arrow {
	background-position: -256px -120px;
	width: 23px;
	height: 23px;
}

.icon-insert_pivot {
	background-position: -256px -143px;
	width: 23px;
	height: 23px;
}

.icon-comparebutton {
	background-position: -256px -166px;
	width: 20px;
	height: 23px;
}

.icon-review_displaymode {
	background-position: -256px -189px;
	width: 20px;
	height: 23px;
}

.icon-change_case {
	background-position: -25px -239px;
	width: 45px;
	height: 22px;
}

.icon-fontsize {
	background-position: -70px -239px;
	width: 45px;
	height: 22px;
}

.icon-rowsandcolumns {
	background-position: -115px -239px;
	width: 45px;
	height: 22px;
}

.icon-SearchOptions {
	background-position: -160px -239px;
	width: 42px;
	height: 22px;
}

.icon-pastespecialbutton {
	background-position: -202px -239px;
	width: 30px;
	height: 22px;
}

.icon-thesaurus_icon {
	background-position: -232px -239px;
	width: 28px;
	height: 22px;
}

.icon-zip_code_icon {
	background-position: -256px -212px;
	width: 24px;
	height: 22px;
}

.icon-inserttexticon {
	background-position: -233px -212px;
	width: 22px;
	height: 22px;
}

.icon-larger {
	background-position: -280px 0px;
	width: 22px;
	height: 22px;
}

.icon-orientation {
	background-position: -280px -22px;
	width: 22px;
	height: 22px;
}

.icon-radio_button_icon {
	background-position: -280px -44px;
	width: 22px;
	height: 22px;
}

.icon-role_down {
	background-position: -280px -66px;
	width: 22px;
	height: 22px;
}

.icon-role_up {
	background-position: -280px -88px;
	width: 22px;
	height: 22px;
}

.icon-smaller {
	background-position: -280px -110px;
	width: 22px;
	height: 22px;
}

.icon-table_figures_button {
	background-position: -280px -132px;
	width: 22px;
	height: 22px;
}

.icon-zotero {
	background-position: -280px -154px;
	width: 22px;
	height: 22px;
}

.icon-pagebreak1 {
	background-position: -280px -176px;
	width: 21px;
	height: 22px;
}

.icon-trackchangestoptoolbar {
	background-position: -280px -198px;
	width: 21px;
	height: 22px;
}

.icon-refresh_button {
	background-position: -280px -220px;
	width: 20px;
	height: 22px;
}

.icon-spellchecking_toptoolbar {
	background-position: -172px -85px;
	width: 20px;
	height: 22px;
}

.icon-wrapping_toptoolbar {
	background-position: -129px -95px;
	width: 20px;
	height: 22px;
}

.icon-coeditingmode {
	background-position: -260px -239px;
	width: 18px;
	height: 22px;
}

.icon-headerfooter {
	background-position: 0px -262px;
	width: 18px;
	height: 22px;
}

.icon-pagesize {
	background-position: -18px -262px;
	width: 18px;
	height: 22px;
}

.icon-blankpage {
	background-position: -212px -108px;
	width: 16px;
	height: 22px;
}

.icon-watermark {
	background-position: -36px -262px;
	width: 16px;
	height: 22px;
}

.icon-versionhistoryicon {
	background-position: -52px -262px;
	width: 24px;
	height: 21px;
}

.icon-fittowidth {
	background-position: -76px -262px;
	width: 23px;
	height: 21px;
}

.icon-pivot_refresh {
	background-position: -99px -262px;
	width: 21px;
	height: 21px;
}

.icon-changerowheight {
	background-position: -129px -117px;
	width: 17px;
	height: 21px;
}

.icon-toccontentcontrol {
	background-position: -120px -262px;
	width: 16px;
	height: 21px;
}

.icon-bookmark {
	background-position: -136px -262px;
	width: 13px;
	height: 21px;
}

.icon-viewsettingsiconpdf {
	background-position: -228px -192px;
	width: 27px;
	height: 20px;
}

.icon-credit_card_icon {
	background-position: -149px -262px;
	width: 26px;
	height: 20px;
}

.icon-sheetview_icon {
	background-position: -175px -262px;
	width: 25px;
	height: 20px;
}

.icon-date_time_icon {
	background-position: -280px -242px;
	width: 22px;
	height: 20px;
}

.icon-bringforward_toptoolbar {
	background-position: -200px -262px;
	width: 20px;
	height: 20px;
}

.icon-checkbox_icon {
	background-position: -220px -262px;
	width: 20px;
	height: 20px;
}

.icon-constantproportionsactivated {
	background-position: -240px -262px;
	width: 20px;
	height: 20px;
}

.icon-copystyle_selected {
	background-position: -260px -262px;
	width: 20px;
	height: 20px;
}

.icon-email_address_icon {
	background-position: -280px -262px;
	width: 20px;
	height: 20px;
}

.icon-eraser_tool {
	background-position: -302px 0px;
	width: 20px;
	height: 20px;
}

.icon-group_toptoolbar {
	background-position: -302px -20px;
	width: 20px;
	height: 20px;
}

.icon-image {
	background-position: -302px -40px;
	width: 20px;
	height: 20px;
}

.icon-image_form_icon {
	background-position: -302px -60px;
	width: 20px;
	height: 20px;
}

.icon-insertautoshape {
	background-position: -302px -80px;
	width: 20px;
	height: 20px;
}

.icon-insert_symbol_icon {
	background-position: -302px -100px;
	width: 20px;
	height: 20px;
}

.icon-sendbackward_toptoolbar {
	background-position: -302px -120px;
	width: 20px;
	height: 20px;
}

.icon-show_password {
	background-position: -302px -140px;
	width: 20px;
	height: 20px;
}

.icon-spellcheckactivated {
	background-position: -302px -160px;
	width: 20px;
	height: 20px;
}

.icon-align_toptoolbar {
	background-position: -302px -180px;
	width: 19px;
	height: 20px;
}

.icon-fittopage {
	background-position: -302px -200px;
	width: 17px;
	height: 20px;
}

.icon-inserttextarticon {
	background-position: -302px -220px;
	width: 16px;
	height: 20px;
}

.icon-pagemargins {
	background-position: -302px -240px;
	width: 16px;
	height: 20px;
}

.icon-sortcommentsicon {
	background-position: 0px -284px;
	width: 31px;
	height: 19px;
}

.icon-chat_toptoolbar {
	background-position: -31px -284px;
	width: 24px;
	height: 19px;
}

.icon-comment_toptoolbar {
	background-position: -55px -284px;
	width: 22px;
	height: 19px;
}

.icon-insertchart {
	background-position: -77px -284px;
	width: 22px;
	height: 19px;
}

.icon-insertequationicon {
	background-position: -99px -284px;
	width: 22px;
	height: 19px;
}

.icon-pivotselecticon {
	background-position: -121px -284px;
	width: 22px;
	height: 19px;
}

.icon-review_next {
	background-position: -143px -284px;
	width: 22px;
	height: 19px;
}

.icon-review_previous {
	background-position: -165px -284px;
	width: 22px;
	height: 19px;
}

.icon-combo_box_icon {
	background-position: -187px -284px;
	width: 21px;
	height: 19px;
}

.icon-dropdown_list_icon {
	background-position: -302px -260px;
	width: 20px;
	height: 19px;
}

.icon-addhyperlink {
	background-position: -208px -284px;
	width: 19px;
	height: 19px;
}

.icon-hiderulers {
	background-position: -227px -284px;
	width: 19px;
	height: 19px;
}

.icon-noborders {
	background-position: -246px -284px;
	width: 19px;
	height: 19px;
}

.icon-insertcolumns {
	background-position: -265px -284px;
	width: 18px;
	height: 19px;
}

.icon-insert_dropcap_icon {
	background-position: -283px -284px;
	width: 18px;
	height: 19px;
}

.icon-tocrefreshicon {
	background-position: -301px -284px;
	width: 16px;
	height: 19px;
}

.icon-removecomment_toptoolbar {
	background-position: 0px -303px;
	width: 15px;
	height: 19px;
}

.icon-backgroundcolor {
	background-position: -15px -303px;
	width: 26px;
	height: 18px;
}

.icon-backgroundcolor_selected {
	background-position: -41px -303px;
	width: 26px;
	height: 18px;
}

.icon-review_accepttoptoolbar {
	background-position: -67px -303px;
	width: 22px;
	height: 18px;
}

.icon-review_rejecttoptoolbar {
	background-position: -89px -303px;
	width: 22px;
	height: 18px;
}

.icon-new_icon {
	background-position: -172px -107px;
	width: 18px;
	height: 18px;
}

.icon-pencil_tool {
	background-position: -111px -303px;
	width: 18px;
	height: 18px;
}

.icon-phone_number_icon {
	background-position: -129px -303px;
	width: 18px;
	height: 18px;
}

.icon-search_advanced {
	background-position: -147px -303px;
	width: 18px;
	height: 18px;
}

.icon-usericon {
	background-position: -165px -303px;
	width: 18px;
	height: 18px;
}

.icon-searchbuttons {
	background-position: -183px -303px;
	width: 33px;
	height: 17px;
}

.icon-changecolumnwidth {
	background-position: -216px -303px;
	width: 21px;
	height: 17px;
}

.icon-sharingicon {
	background-position: -237px -303px;
	width: 21px;
	height: 17px;
}

.icon-chaticon_new {
	background-position: -258px -303px;
	width: 19px;
	height: 17px;
}

.icon-search_icon_header {
	background-position: -277px -303px;
	width: 17px;
	height: 17px;
}

.icon-easybib {
	background-position: -294px -303px;
	width: 16px;
	height: 17px;
}

.icon-insertslicer {
	background-position: -322px 0px;
	width: 16px;
	height: 17px;
}

.icon-paste_style {
	background-position: -322px -17px;
	width: 16px;
	height: 17px;
}

.icon-slicer_settings {
	background-position: -322px -34px;
	width: 16px;
	height: 17px;
}

.icon-downloadicon {
	background-position: -322px -51px;
	width: 15px;
	height: 17px;
}

.icon-addgradientpoint {
	background-position: -322px -68px;
	width: 12px;
	height: 17px;
}

.icon-removegradientpoint {
	background-position: -322px -85px;
	width: 12px;
	height: 17px;
}

.icon-highlightcolor {
	background-position: 0px -322px;
	width: 25px;
	height: 16px;
}

.icon-complex_field_icon {
	background-position: -25px -322px;
	width: 24px;
	height: 16px;
}

.icon-text_field_icon {
	background-position: -49px -322px;
	width: 24px;
	height: 16px;
}

.icon-view_form_icon {
	background-position: -73px -322px;
	width: 24px;
	height: 16px;
}

.icon-insertccicon {
	background-position: -97px -322px;
	width: 23px;
	height: 16px;
}

.icon-table {
	background-position: -120px -322px;
	width: 22px;
	height: 16px;
}

.icon-usersnumber {
	background-position: -142px -322px;
	width: 22px;
	height: 16px;
}

.icon-close_icon {
	background-position: -164px -322px;
	width: 18px;
	height: 16px;
}

.icon-saveupdate {
	background-position: -182px -322px;
	width: 18px;
	height: 16px;
}

.icon-savewhilecoediting {
	background-position: -200px -322px;
	width: 17px;
	height: 16px;
}

.icon-converttorange {
	background-position: -322px -102px;
	width: 16px;
	height: 16px;
}

.icon-dropdownarrow {
	background-position: -322px -118px;
	width: 16px;
	height: 16px;
}

.icon-favorites_icon {
	background-position: -322px -134px;
	width: 16px;
	height: 16px;
}

.icon-filterbutton {
	background-position: -322px -150px;
	width: 16px;
	height: 16px;
}

.icon-trackchangesstatusbar {
	background-position: -322px -166px;
	width: 15px;
	height: 16px;
}

.icon-distributevertically {
	background-position: -322px -182px;
	width: 14px;
	height: 16px;
}

.icon-handtool {
	background-position: -322px -198px;
	width: 13px;
	height: 16px;
}

.icon-pagethumbnails {
	background-position: -322px -214px;
	width: 12px;
	height: 16px;
}

.icon-textart_settings_icon {
	background-position: -322px -230px;
	width: 12px;
	height: 16px;
}

.icon-selectiontool {
	background-position: -322px -246px;
	width: 10px;
	height: 16px;
}

.icon-search_options {
	background-position: -217px -322px;
	width: 22px;
	height: 15px;
}

.icon-addfootnote {
	background-position: -239px -322px;
	width: 21px;
	height: 15px;
}

.icon-access_rights {
	background-position: -260px -322px;
	width: 19px;
	height: 15px;
}

.icon-chaticon {
	background-position: -279px -322px;
	width: 18px;
	height: 15px;
}

.icon-gotodocuments {
	background-position: -297px -322px;
	width: 18px;
	height: 15px;
}

.icon-print {
	background-position: -315px -322px;
	width: 17px;
	height: 15px;
}

.icon-bgcolor {
	background-position: -322px -262px;
	width: 16px;
	height: 15px;
}

.icon-wrappingstyle_behind_toptoolbar {
	background-position: -322px -277px;
	width: 16px;
	height: 15px;
}

.icon-wrappingstyle_infront_toptoolbar {
	background-position: -322px -292px;
	width: 16px;
	height: 15px;
}

.icon-wrappingstyle_inline_toptoolbar {
	background-position: -322px -307px;
	width: 16px;
	height: 15px;
}

.icon-wrappingstyle_square_toptoolbar {
	background-position: -338px 0px;
	width: 16px;
	height: 15px;
}

.icon-wrappingstyle_through_toptoolbar {
	background-position: -338px -15px;
	width: 16px;
	height: 15px;
}

.icon-wrappingstyle_tight_toptoolbar {
	background-position: -338px -30px;
	width: 16px;
	height: 15px;
}

.icon-wrappingstyle_topandbottom_toptoolbar {
	background-position: -338px -45px;
	width: 16px;
	height: 15px;
}

.icon-about {
	background-position: -338px -60px;
	width: 15px;
	height: 15px;
}

.icon-abouticon {
	background-position: -338px -75px;
	width: 15px;
	height: 15px;
}

.icon-advanced_settings_icon {
	background-position: -338px -90px;
	width: 15px;
	height: 15px;
}

.icon-document_language {
	background-position: -338px -105px;
	width: 15px;
	height: 15px;
}

.icon-insertpivot {
	background-position: -338px -120px;
	width: 15px;
	height: 15px;
}

.icon-tabstopcenter {
	background-position: -338px -135px;
	width: 15px;
	height: 15px;
}

.icon-tabstopleft {
	background-position: -338px -150px;
	width: 15px;
	height: 15px;
}

.icon-tabstopright {
	background-position: -338px -165px;
	width: 15px;
	height: 15px;
}

.icon-anchor {
	background-position: -338px -180px;
	width: 14px;
	height: 15px;
}

.icon-feedback {
	background-position: -338px -195px;
	width: 14px;
	height: 15px;
}

.icon-flipupsidedown {
	background-position: -338px -210px;
	width: 14px;
	height: 15px;
}

.icon-file {
	background-position: -338px -225px;
	width: 13px;
	height: 15px;
}

.icon-fitpage {
	background-position: -338px -240px;
	width: 13px;
	height: 15px;
}

.icon-back {
	background-position: -338px -255px;
	width: 12px;
	height: 15px;
}

.icon-lock_form_icon {
	background-position: -338px -270px;
	width: 12px;
	height: 15px;
}

.icon-changecolorscheme {
	background-position: 0px -338px;
	width: 25px;
	height: 14px;
}

.icon-fontcolor {
	background-position: -25px -338px;
	width: 25px;
	height: 14px;
}

.icon-numbering {
	background-position: -50px -338px;
	width: 24px;
	height: 14px;
}

.icon-distributehorizontally {
	background-position: -338px -285px;
	width: 16px;
	height: 14px;
}

.icon-columnwidthmarker {
	background-position: -338px -299px;
	width: 15px;
	height: 14px;
}

.icon-fliplefttoright {
	background-position: -338px -313px;
	width: 15px;
	height: 14px;
}

.icon-alignobjectbottom {
	background-position: -74px -338px;
	width: 14px;
	height: 14px;
}

.icon-alignobjectleft {
	background-position: -88px -338px;
	width: 14px;
	height: 14px;
}

.icon-alignobjectright {
	background-position: -102px -338px;
	width: 14px;
	height: 14px;
}

.icon-alignobjecttop {
	background-position: -116px -338px;
	width: 14px;
	height: 14px;
}

.icon-bringforward {
	background-position: -130px -338px;
	width: 14px;
	height: 14px;
}

.icon-bringtofront {
	background-position: -144px -338px;
	width: 14px;
	height: 14px;
}

.icon-chart_settings_icon {
	background-position: -158px -338px;
	width: 14px;
	height: 14px;
}

.icon-commentsicon {
	background-position: -172px -338px;
	width: 14px;
	height: 14px;
}

.icon-deleteicon {
	background-position: -186px -338px;
	width: 14px;
	height: 14px;
}

.icon-group {
	background-position: -200px -338px;
	width: 14px;
	height: 14px;
}

.icon-image_settings_icon {
	background-position: -214px -338px;
	width: 14px;
	height: 14px;
}

.icon-save {
	background-position: -228px -338px;
	width: 14px;
	height: 14px;
}

.icon-searchicon {
	background-position: -242px -338px;
	width: 14px;
	height: 14px;
}

.icon-sendbackward {
	background-position: -256px -338px;
	width: 14px;
	height: 14px;
}

.icon-sendtoback {
	background-position: -270px -338px;
	width: 14px;
	height: 14px;
}

.icon-shape_settings_icon {
	background-position: -284px -338px;
	width: 14px;
	height: 14px;
}

.icon-ungroup {
	background-position: -298px -338px;
	width: 14px;
	height: 14px;
}

.icon-alignobjectcenter {
	background-position: -312px -338px;
	width: 13px;
	height: 14px;
}

.icon-highlight_color_mouse_pointer {
	background-position: -325px -338px;
	width: 13px;
	height: 14px;
}

.icon-tocrefreshiconcc {
	background-position: -338px -338px;
	width: 13px;
	height: 14px;
}

.icon-copystyle {
	background-position: -310px -303px;
	width: 12px;
	height: 14px;
}

.icon-gradientslider {
	background-position: -354px 0px;
	width: 12px;
	height: 14px;
}

.icon-combo_delete_values {
	background-position: -354px -14px;
	width: 11px;
	height: 14px;
}

.icon-hard {
	background-position: -354px -28px;
	width: 9px;
	height: 14px;
}

.icon-outline {
	background-position: -149px -125px;
	width: 25px;
	height: 13px;
}

.icon-bullets {
	background-position: -78px -78px;
	width: 24px;
	height: 13px;
}

.icon-review {
	background-position: 0px -352px;
	width: 23px;
	height: 13px;
}

.icon-tocicon {
	background-position: -23px -352px;
	width: 20px;
	height: 13px;
}

.icon-visible_area {
	background-position: -43px -352px;
	width: 20px;
	height: 13px;
}

.icon-cut {
	background-position: -174px -125px;
	width: 16px;
	height: 13px;
}

.icon-removeduplicates {
	background-position: -63px -352px;
	width: 16px;
	height: 13px;
}

.icon-text_autoshape {
	background-position: -79px -352px;
	width: 16px;
	height: 13px;
}

.icon-changerange {
	background-position: -95px -352px;
	width: 15px;
	height: 13px;
}

.icon-clearstyle {
	background-position: -110px -352px;
	width: 15px;
	height: 13px;
}

.icon-clear_fields_icon {
	background-position: -125px -352px;
	width: 15px;
	height: 13px;
}

.icon-feedbackicon {
	background-position: -140px -352px;
	width: 15px;
	height: 13px;
}

.icon-greencircle {
	background-position: -155px -352px;
	width: 15px;
	height: 13px;
}

.icon-alignobjectmiddle {
	background-position: -170px -352px;
	width: 14px;
	height: 13px;
}

.icon-copy {
	background-position: -184px -352px;
	width: 14px;
	height: 13px;
}

.icon-multiselect {
	background-position: -198px -352px;
	width: 14px;
	height: 13px;
}

.icon-paste {
	background-position: -212px -352px;
	width: 14px;
	height: 13px;
}

.icon-selectall {
	background-position: -226px -352px;
	width: 14px;
	height: 13px;
}

.icon-dropcap_none {
	background-position: -240px -352px;
	width: 13px;
	height: 13px;
}

.icon-movetable_handle {
	background-position: -253px -352px;
	width: 13px;
	height: 13px;
}

.icon-zoomin {
	background-position: -266px -352px;
	width: 13px;
	height: 13px;
}

.icon-editcommenticon {
	background-position: -354px -42px;
	width: 12px;
	height: 13px;
}

.icon-equationplaceholder {
	background-position: -354px -55px;
	width: 12px;
	height: 13px;
}

.icon-insertpagenumber {
	background-position: -354px -68px;
	width: 12px;
	height: 13px;
}

.icon-rotateclockwise {
	background-position: -354px -81px;
	width: 12px;
	height: 13px;
}

.icon-rotatecounterclockwise {
	background-position: -354px -94px;
	width: 12px;
	height: 13px;
}

.icon-linespacing {
	background-position: -279px -352px;
	width: 25px;
	height: 12px;
}

.icon-spellcheckdeactivated {
	background-position: -304px -352px;
	width: 15px;
	height: 12px;
}

.icon-slicer_clearfilter {
	background-position: -319px -352px;
	width: 14px;
	height: 12px;
}

.icon-circle {
	background-position: -354px -107px;
	width: 12px;
	height: 12px;
}

.icon-follow_move {
	background-position: -354px -119px;
	width: 12px;
	height: 12px;
}

.icon-nofill {
	background-position: -354px -131px;
	width: 12px;
	height: 12px;
}

.icon-sup {
	background-position: -354px -143px;
	width: 12px;
	height: 12px;
}

.icon-deletecommenticon {
	background-position: -354px -155px;
	width: 11px;
	height: 12px;
}

.icon-review_delete {
	background-position: -354px -167px;
	width: 11px;
	height: 12px;
}

.icon-tabletemplate {
	background-position: -333px -352px;
	width: 26px;
	height: 11px;
}

.icon-footnotes {
	background-position: -366px 0px;
	width: 24px;
	height: 11px;
}

.icon-insertfunction {
	background-position: -366px -11px;
	width: 21px;
	height: 11px;
}

.icon-nonprintingcharacters {
	background-position: -366px -22px;
	width: 20px;
	height: 11px;
}

.icon-fitwidth {
	background-position: -366px -33px;
	width: 16px;
	height: 11px;
}

.icon-table_settings_icon {
	background-position: -366px -44px;
	width: 16px;
	height: 11px;
}

.icon-viewsettingsicon {
	background-position: -366px -55px;
	width: 16px;
	height: 11px;
}

.icon-mailmergeicon {
	background-position: -366px -66px;
	width: 15px;
	height: 11px;
}

.icon-review_accept {
	background-position: -366px -77px;
	width: 13px;
	height: 11px;
}

.icon-sub {
	background-position: -366px -88px;
	width: 13px;
	height: 11px;
}

.icon-aligncenter {
	background-position: -366px -99px;
	width: 12px;
	height: 11px;
}

.icon-alignleft {
	background-position: -378px -99px;
	width: 12px;
	height: 11px;
}

.icon-alignright {
	background-position: -366px -110px;
	width: 12px;
	height: 11px;
}

.icon-clearfilter {
	background-position: -378px -110px;
	width: 12px;
	height: 11px;
}

.icon-decreasedec {
	background-position: -366px -121px;
	width: 12px;
	height: 11px;
}

.icon-decreaseindent {
	background-position: -378px -121px;
	width: 12px;
	height: 11px;
}

.icon-dropcap_margin {
	background-position: -366px -132px;
	width: 12px;
	height: 11px;
}

.icon-dropcap_text {
	background-position: -378px -132px;
	width: 12px;
	height: 11px;
}

.icon-increasedec {
	background-position: -366px -143px;
	width: 12px;
	height: 11px;
}

.icon-increaseindent {
	background-position: -378px -143px;
	width: 12px;
	height: 11px;
}

.icon-justify {
	background-position: -366px -154px;
	width: 12px;
	height: 11px;
}

.icon-leftcolumn {
	background-position: -378px -154px;
	width: 12px;
	height: 11px;
}

.icon-onecolumn {
	background-position: -366px -165px;
	width: 12px;
	height: 11px;
}

.icon-resolvedicon {
	background-position: -378px -165px;
	width: 12px;
	height: 11px;
}

.icon-resolveicon {
	background-position: -366px -176px;
	width: 12px;
	height: 11px;
}

.icon-rightcolumn {
	background-position: -378px -176px;
	width: 12px;
	height: 11px;
}

.icon-sortandfilter {
	background-position: -366px -187px;
	width: 12px;
	height: 11px;
}

.icon-twocolumns {
	background-position: -378px -187px;
	width: 12px;
	height: 11px;
}

.icon-combo_add_values {
	background-position: -379px -77px;
	width: 11px;
	height: 11px;
}

.icon-sortatoz {
	background-position: -379px -88px;
	width: 11px;
	height: 11px;
}

.icon-sortztoa {
	background-position: -366px -198px;
	width: 11px;
	height: 11px;
}

.icon-threecolumns {
	background-position: -377px -198px;
	width: 11px;
	height: 11px;
}

.icon-underline {
	background-position: -366px -209px;
	width: 10px;
	height: 11px;
}

.icon-zoomout {
	background-position: -376px -209px;
	width: 10px;
	height: 11px;
}

.icon-navigationicon {
	background-position: -366px -220px;
	width: 13px;
	height: 10px;
}

.icon-strike {
	background-position: -366px -230px;
	width: 12px;
	height: 10px;
}

.icon-yellowdiamond {
	background-position: -379px -220px;
	width: 11px;
	height: 10px;
}

.icon-firstline_indent {
	background-position: -378px -230px;
	width: 10px;
	height: 10px;
}

.icon-review_reject {
	background-position: -366px -240px;
	width: 10px;
	height: 10px;
}

.icon-bold {
	background-position: -382px -33px;
	width: 8px;
	height: 10px;
}

.icon-italic {
	background-position: -382px -44px;
	width: 7px;
	height: 10px;
}

.icon-selectcolumnpointer {
	background-position: -382px -55px;
	width: 7px;
	height: 10px;
}

.icon-redo {
	background-position: -366px -250px;
	width: 17px;
	height: 9px;
}

.icon-undo {
	background-position: -366px -259px;
	width: 17px;
	height: 9px;
}

.icon-combo_values_up {
	background-position: -376px -240px;
	width: 10px;
	height: 9px;
}

.icon-cellrow {
	background-position: -381px -66px;
	width: 9px;
	height: 9px;
}

.icon-resize_square {
	background-position: -366px -268px;
	width: 9px;
	height: 9px;
}

.icon-searchdownbutton {
	background-position: -375px -268px;
	width: 14px;
	height: 8px;
}

.icon-searchupbutton {
	background-position: -366px -277px;
	width: 14px;
	height: 8px;
}

.icon-constantproportions {
	background-position: -366px -285px;
	width: 13px;
	height: 8px;
}

.icon-rowheightmarker {
	background-position: -366px -293px;
	width: 13px;
	height: 8px;
}

.icon-tabstopcenter_marker {
	background-position: -366px -301px;
	width: 12px;
	height: 8px;
}

.icon-hanging {
	background-position: -380px -277px;
	width: 10px;
	height: 8px;
}

.icon-right_indent {
	background-position: -379px -285px;
	width: 10px;
	height: 8px;
}

.icon-soft {
	background-position: -379px -293px;
	width: 10px;
	height: 8px;
}

.icon-selectcellpointer {
	background-position: -378px -301px;
	width: 8px;
	height: 8px;
}

.icon-tabstopleft_marker {
	background-position: -366px -309px;
	width: 8px;
	height: 8px;
}

.icon-tabstopright_marker {
	background-position: -374px -309px;
	width: 8px;
	height: 8px;
}

.icon-nextfootnote {
	background-position: -383px -250px;
	width: 6px;
	height: 8px;
}

.icon-previousfootnote {
	background-position: -383px -259px;
	width: 6px;
	height: 8px;
}

.icon-collapse {
	background-position: -366px -317px;
	width: 11px;
	height: 7px;
}

.icon-expand {
	background-position: -377px -317px;
	width: 11px;
	height: 7px;
}

.icon-selectrowpointer {
	background-position: -366px -324px;
	width: 10px;
	height: 7px;
}

.icon-resizetable_handle {
	background-position: -382px -309px;
	width: 7px;
	height: 7px;
}

.icon-redo1 {
	background-position: -376px -324px;
	width: 13px;
	height: 6px;
}

.icon-undo1 {
	background-position: -366px -331px;
	width: 13px;
	height: 6px;
}

.icon-combo_values_down {
	background-position: -366px -337px;
	width: 12px;
	height: 6px;
}

.icon-leftindent {
	background-position: -379px -331px;
	width: 10px;
	height: 6px;
}

.icon-nextpage {
	background-position: -378px -337px;
	width: 5px;
	height: 6px;
}

.icon-previouspage {
	background-position: -383px -337px;
	width: 5px;
	height: 6px;
}

.icon-tab {
	background-position: -366px -343px;
	width: 10px;
	height: 5px;
}

.icon-nonbreakspace {
	background-position: -376px -343px;
	width: 5px;
	height: 5px;
}

.icon-square {
	background-position: -381px -343px;
	width: 5px;
	height: 5px;
}

.icon-space {
	background-position: -387px -11px;
	width: 2px;
	height: 3px;
}

