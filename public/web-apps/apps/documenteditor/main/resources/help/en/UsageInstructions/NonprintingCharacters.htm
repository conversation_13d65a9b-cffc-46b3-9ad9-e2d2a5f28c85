﻿<!DOCTYPE html>
<html>
	<head>
		<title>Show/hide nonprinting characters</title>
		<meta charset="utf-8" />
		<meta name="description" content="Show or hide nonprinting characters while formatting text, creating tables, and editing documents" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Show/hide nonprinting characters</h1>
			<p>In the <a href="https://www.onlyoffice.com/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Document Editor</b></a>, you can enable displaying nonprinting characters that help you edit a document. They indicate the presence of various types of formatting elements, but they cannot be printed with the document even if they are displayed on the screen.</p>
			<p>To show or hide nonprinting characters, click the <b>Nonprinting characters</b> <span class = "icon icon-nonprintingcharacters"></span> icon at the <b>Home</b> tab on the top toolbar. Alternatively, you can use the <em>Ctrl+Shift+Num8</em> key combination.</p>
			<p>Nonprinting characters include:</p>
			<table>
				<tr>
					<td>Spaces</td>
					<td><div class = "icon icon-space"></div></td>
					<td>Inserted when you press the <b>Spacebar</b> on the keyboard. They create a space between characters.</td>
				</tr>
				<tr>
					<td>Tabs</td>
					<td><div class = "icon icon-tab"></div></td>
					<td>Inserted when you press the <b>Tab</b> key. They are used to advance the cursor to the next tab stop.</td>
				</tr>
				<tr>
					<td>Paragraph marks (i.e. hard returns)</td>
					<td><div class = "icon icon-hard"></div></td>
					<td>Inserted when you press the <b>Enter</b> key. They ends a paragraph and adds a bit of space after it. They also contain information about the paragraph formatting.</td>
				</tr>
				<tr>
					<td>Line breaks (i.e. soft returns)</td>
					<td><div class = "icon icon-soft"></div></td>
					<td>Inserted when you use the <b>Shift+Enter</b> key combination. They break the current line and put the text lines close together. Soft return are primarily used in titles and headings.</td>
				</tr>
				<tr>
					<td>Nonbreaking spaces</td>
					<td><div class = "icon icon-nonbreakspace"></div></td>
					<td>Inserted when you use the <b>Ctrl+Shift+Spacebar</b> key combination. They create a space between characters which can't be used to start a new line.</td>
				</tr>
				<tr>
					<td>Page breaks</td>
					<td><div class = "big big-pagebreak"></div></td>
					<td>Inserted when you use the <div class = "icon icon-pagebreak1"></div> <b>Breaks</b> icon on the <b>Insert</b> or <b>Layout</b> tabs of the top toolbar and then select one of the <b>Insert Page Break</b> submenu options (the section break indicator differs depending on which option is selected: Next Page, Continuous Page, Even Page or Odd Page).</td>
				</tr>
				<tr>
					<td>Section breaks</td>
					<td><div class = "big big-sectionbreak"></div></td>
					<td>Inserted when you use the <div class = "icon icon-pagebreak1"></div> <b>Breaks</b> icon on the <b>Insert</b> or <b>Layout</b> tab of the top toolbar and then select one of the <b>Insert Section Break</b> submenu options (the section break indicator differs depending on which option is selected: Next Page, Continuous Page, Even Page or Odd Page).</td>
				</tr>
                <tr>
                    <td>Column breaks</td>
                    <td><div class = "big big-columnbreak"></div></td>
                    <td>Inserted when you use the <div class = "icon icon-pagebreak1"></div> <b>Breaks</b> icon on the <b>Insert</b> or <b>Layout</b> tab of the top toolbar and then select the <b>Insert Column Break</b> option.</td>
                </tr>
				<tr>
					<td>End-of-cell and end-of row markers in tables</td>
					<td><div class = "icon icon-cellrow"></div></td>
					<td>Contain formatting codes for an individual cell and a row, respectively.</td>
				</tr>
				<tr>
					<td>Small black square in the margin to the left of a paragraph</td>
					<td><div class = "icon icon-square"></div></td>
					<td>Indicates that at least one of the paragraph options was applied, e.g. <b>Keep lines together</b>, <b>Page break before</b>.</td>
				</tr>
				<tr>
					<td>Anchor symbols</td>
					<td><div class = "icon icon-anchor"></div></td>
					<td>Indicate the position of floating objects (objects whose wrapping style is different from <b>Inline</b>), e.g. images, autoshapes, charts. You should select an object to make its anchor visible.</td>
				</tr>
			</table>
		</div>
	</body>
</html>