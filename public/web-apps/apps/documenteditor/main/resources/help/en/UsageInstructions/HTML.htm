﻿<!DOCTYPE html>
<html>
<head>
    <title>Edit HTML</title>
    <meta charset="utf-8" />
    <meta name="description" content="The description of HTML plugin for ONLYOFFICE editors, which allows to edit HTML code of the text" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Edit HTML</h1>
        <p>If you are writing a website page in a text editor and want to get it as an HTML code, use the <b>HTML</b> plugin.</p>
        <ol>
            <li>Open the <b>Plugins</b> tab and click <b>Get and paste html</b>.</li>
            <li>Select the necessary content.</li>
            <li>The HTML code of the selected paragraph will be displayed in the plugin field on the left-side panel. You can edit the code to alter the text characteristics, e.g. <em>font size</em> or <em>font family</em>, etc.</li>
            <li>Click <b>Paste into the document</b> to insert the text with its HTML code edited at the current cursor position in your document.</li>
        </ol>
        <p>You can also write your own HTML code (without selecting any document content) and then paste it to your document.</p>
        <img class="gif" alt="HTML plugin gif" src="../../images/html_plugin.gif" width="600" />
        <p>For more information on the HTML plugin and its installation, please see the <a href="https://www.onlyoffice.com/en/app-directory/html">plugin’s page</a> on the AppDirectory.</p>
    </div>
</body>
</html>