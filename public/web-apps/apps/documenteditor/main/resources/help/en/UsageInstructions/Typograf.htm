﻿<!DOCTYPE html>
<html>
<head>
    <title>Correct typography</title>
    <meta charset="utf-8" />
    <meta name="description" content="The description of Typograf plugin for ONLYOFFICE editors, which allows to correct typography of the text" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Correct typography</h1>
        <p>If you need to correct typography in your text, use the <b>Typograf</b> plugin that will automatically place non-breaking spaces and remove extra ones, as well as correct minor typos, insert correct quotes, replace hyphens with dashes, etc.</p>
        <ol>
            <li>Open the <b>Plugins</b> tab and click <b>Typograf</b>.</li>
            <li>Click the <b>Show advanced settings</b> button.</li>
            <li>Choose the locale and the rules you want to apply to your text.</li>
            <li>Select the text you want to correct.</li>
            <li>Click the <b>Correct text</b> button.</li>
        </ol>
        <img class="gif" alt="Typograf plugin gif" src="../../images/typograf_plugin.gif" width="600" />
        <p>For more information on the Typograf plugin and its installation, please see the <a href="https://www.onlyoffice.com/en/app-directory/typograph">plugin’s page</a> on the AppDirectory.</p>
    </div>
</body>
</html>