﻿<!DOCTYPE html>
<html>
<head>
    <title>Insert date and time</title>
    <meta charset="utf-8" />
    <meta name="description" content="Insert date and time into your document" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Insert date and time</h1>
        <p>To insert <b>Date and time</b> in the <a href="https://www.onlyoffice.com/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Document Editor</b></a>,</p>
        <ol>
            <li>put the cursor where you want to insert <b>Date and time</b>,</li>
            <li>switch to the <b>Insert</b> tab of the top toolbar,</li>
            <li>click the <b>Date &amp time</b> <div class = "icon icon-date_time_icon"></div> icon on the top toolbar,</li>
            <li>
                in the <b>Date &amp time</b> window that will appear, specify the following parameters:
                <ul>
                    <li>Select the required language.</li>
                    <li>Select one of the suggested formats.</li>
                    <li>
                        Check the <b>Update automatically</b> checkbox to let the date & time update automatically based on the current state.
                        <p class="note">
                            <b>Note</b>: you can also update the date and time manually by using the <b>Refresh field</b> option from the contextual menu.
                        </p>
                    </li>
                    <li>Click the <b>Set as default</b> button to make the current format the default for this language.</li>
                </ul>
            </li>
            <li>Click the <b>OK</b> button.</li>
        </ol>
        <p><img alt="Date and time window" src="../images/date_time.png" /></p>
    </div>
</body>
</html>