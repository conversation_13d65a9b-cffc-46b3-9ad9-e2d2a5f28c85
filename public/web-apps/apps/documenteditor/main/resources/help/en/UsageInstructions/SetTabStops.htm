﻿<!DOCTYPE html>
<html>
	<head>
		<title>Set tab stops</title>
		<meta charset="utf-8" />
		<meta name="description" content="Set tab stops" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Set tab stops</h1>
			<p>In the <a href="https://www.onlyoffice.com/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Document Editor</b></a>, you can change tab stops. A tab stop is a term used to describe the location where the cursor stops after the <b>Tab</b> key is pressed.</p>
			<p>To set tab stops you can use the horizontal ruler:</p>
			<ol>
			    <li>Select the necessary tab stop type by clicking the <div class = "icon icon-tabstopleft"></div> button in the upper left corner of the working area. The following three tab types are available:
			        <ul>
						<li><b>Left Tab Stop</b> <div class = "icon icon-tabstopleft"></div> lines up the text to the left side at the tab stop position; the text moves to the right from the tab stop while you type. Such a tab stop will be indicated on the horizontal ruler with the <div class = "icon icon-tabstopleft_marker"></div> <b>Left Tab Stop</b> marker.</li>
						<li><b>Center Tab Stop</b> <div class = "icon icon-tabstopcenter"></div> centers the text at the tab stop position. Such a tab stop will be indicated on the horizontal ruler with the <div class = "icon icon-tabstopcenter_marker"></div> <b>Center Tab Stop</b> marker.</li>
						<li><b>Right Tab Stop</b> <div class = "icon icon-tabstopright"></div> lines up the text to the right side at the tab stop position; the text moves to the left from the tab stop while you type. Such a tab stop will be indicated on the horizontal ruler with the <div class = "icon icon-tabstopright_marker"></div> <b>Right Tab Stop</b> marker.</li>
			        </ul>
			    </li>
			    <li>Click on the bottom edge of the ruler where you want to place the tab stop. Drag it along the ruler to change its position. To remove the added tab stop drag it out of the ruler. 
			    <p><div class = "big big-tabstops_ruler"></div></p>
			    </li>			
			</ol>
			<hr />
			<p>You can also use the paragraph properties window to adjust tab stops. Click the right mouse button, select the <b>Paragraph Advanced Settings</b> option in the menu or use the <b>Show advanced settings</b> link on the right sidebar, and switch to the <b>Tabs</b> tab in the opened <b>Paragraph - Advanced Settings</b> window.</p>
			<img alt="Paragraph Properties - Tabs tab" src="../images/paradvsettings_tab.png" />
			<p>You can set the following parameters:</p>
			<ul>
				<li><b>Default Tab</b> is set at 1.25 cm. You can decrease or increase this value by using the arrow buttons or entering the required value in the box.</li>
				<li><b>Tab Position</b> is used to set custom tab stops. Enter the required value in this box, adjust it more precisely by using the arrow buttons and press the <b>Specify</b> button. Your custom tab position will be added to the list in the field below. If you've previously added some tab stops using the ruler, all these tab positions will also be displayed in the list.</li>
                <li><b>Alignment</b> - is used to set the necessary alignment type for each of the tab positions in the list above. Select the necessary tab position in the list, choose the <b>Left</b>, <b>Center</b> or <b>Right</b> option from the drop-down list and press the <b>Specify</b> button.</li>
				<li>
					<b>Leader</b> - allows choosing a character to create a leader for each tab positions. A leader is a line of characters (dots or hyphens) that fills the space between tabs. Select the necessary tab position in the list, choose the leader type from the drop-down list and press the <b>Specify</b> button.
					<p>To delete tab stops from the list, select a tab stop and press the <b>Remove</b> or <b>Remove All</b> button.</p>
				</li>
			</ul>			
		</div>
	</body>
</html>