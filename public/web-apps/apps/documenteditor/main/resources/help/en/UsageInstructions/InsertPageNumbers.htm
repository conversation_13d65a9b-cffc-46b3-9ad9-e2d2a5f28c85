﻿<!DOCTYPE html>
<html>
	<head>
		<title>Insert page numbers</title>
		<meta charset="utf-8" />
		<meta name="description" content="Insert page numbers to navigate through your document easier" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Insert page numbers</h1>
			<p>To insert page numbers in the <a href="https://www.onlyoffice.com/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Document Editor</b></a>,</p>
			<ol>
                <li>switch to the <b>Insert</b> tab of the top toolbar,</li>
                <li>click the <b>Header/Footer</b> <div class = "icon icon-headerfooter"></div> icon on the top toolbar,</li>
				<li>choose the <b>Insert Page Number</b> submenu,</li>
				<li>select one of the following options:
					<ul>
						<li>To add a page number to each page of your document, select the page number position on the page.</li>
						<li>To insert a page number at the current cursor position, select the <b>To Current Position</b> option.
                            <p class="note">
                                <b>Note</b>: to insert a current page number at the current cursor position you can also use the <em>Ctrl+Shift+P</em> key combination.
                            </p>
                        </li>
					</ul>
				</li>
			</ol>
			<p>OR</p>
			<ol>
				<li>switch to the <b>Insert</b> tab of the top toolbar,</li>
				<li>click the <b>Header/Footer</b> <div class="icon icon-headerfooter"></div> icon on the top toolbar,</li>
				<li>click the <b>Insert page number</b> option in the menu and choose the position of the page number.</li>
			</ol>
            <p>To insert the total number of pages in your document (e.g. if you want to create the <em>Page X of Y</em> entry):</p>
            <ol>
                <li>put the cursor where you want to insert the total number of pages,</li>
                <li>click the <b>Header/Footer</b> <div class = "icon icon-headerfooter"></div> icon on the top toolbar,</li>
                <li>select the <b>Insert number of pages</b> option.</li>
            </ol>
			<hr />
			<p>To edit the page number settings,</p>
			<ol>
				<li>double-click the page number added,</li>
				<li>change the current parameters on the right sidebar: 
				<p><img alt="Right Sidebar - Header and Footer Settings" src="../images/right_headerfooter.png" /></p>
				<ul>
				<li>Set the <b>Position</b> of page numbers on the page accordingly to the top and bottom of the page.</li>
				<li>Check the <b>Different first page</b> box to apply a different page number to the very first page or in case you don't want to add any number to it at all. </li>
				<li>Use the <b>Different odd and even pages</b> box to insert different page numbers for odd and even pages. </li>
				<li>The <b>Link to Previous</b> option is available in case you've previously added <a href="../UsageInstructions/SectionBreaks.htm" onclick="onhyperlinkclick(this)">sections</a> into your document. 
                    If not, it will be grayed out. Moreover, this option is also unavailable for the very first section (i.e. when a header or footer that belongs to the first section is selected). 
                    By default, this box is checked, so that unified numbering is applied to all the sections. If you select a header or footer area, you will see that the area is marked with the <b>Same as Previous</b> label. 
                    Uncheck the <b>Link to Previous</b> box to use different page numbering for each section of the document. The <b>Same as Previous</b> label will no longer be displayed.
                    <p><img alt="Same as previous label" src="../images/sameasprevious_label.png" /></p></li>
				<li>The <b>Page Numbering</b> section allows adjusting page numbering options throughout different <a href="../UsageInstructions/SectionBreaks.htm" onclick="onhyperlinkclick(this)">sections</a> of the document.
					The <b>Continue from previous section</b> option is selected by default and makes it possible to keep continuous page numbering after a section break.
					If you want to start page numbering with a specific number in the current section of the document, select the <b>Start at</b> radio button and enter the required starting value in the field on the right.
				</li>
				</ul>
				</li>
			</ol>
			<p>To return to the document editing, double-click within the working area.</p>
		</div>
	</body>
</html>