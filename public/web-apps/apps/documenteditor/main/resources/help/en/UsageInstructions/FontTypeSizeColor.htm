﻿<!DOCTYPE html>
<html>
	<head>
		<title>Set font type, size, and color</title>
		<meta charset="utf-8" />
		<meta name="description" content="Change the following text formatting parameters: font type, size, and color" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Set the font type, size, and color</h1>
            <p>In the <a href="https://www.onlyoffice.com/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Document Editor</b></a>, you can select the font type, its size and color using the corresponding icons on the <b>Home</b> tab of the top toolbar.</p>
			<p class="note">In case you want to apply the formatting to the already existing text in the document, select it with the mouse or <a href="../HelpfulHints/KeyboardShortcuts.htm#textselection" onclick="onhyperlinkclick(this)">use the keyboard</a> and apply the formatting. You can also place the mouse cursor within the necessary word to apply the formatting to this word only.</p>
            <table>
				<tr>
                    <td width="10%">Font</td>
                    <td width="15%"><div class = "big big-fontfamily"></div></td>
					<td>Used to select a font from the list of the the available fonts. <span class="desktopDocumentFeatures">If the required font is not available in the list, you can download and install it on your operating system, and the font will be available in the <em>desktop version</em>.</span></td>
				</tr>
				<tr>
					<td>Font size</td>
					<td><div class = "icon icon-fontsize"></div></td>
					<td>Used to choose from the preset font size values in the dropdown list (the default values are: 8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 36, 48, 72 and 96).  It's also possible to manually enter a custom value up to 300 pt in the font size field. Press <em>Enter</em> to confirm.</td>
				</tr>
                <tr>
                    <td>Increment font size</td>
                    <td><div class = "icon icon-larger"></div></td>
					<td>Used to change the font size making it one point bigger each time the button is pressed.</td>
                </tr>
                <tr>
                    <td>Decrement font size</td>
                    <td><div class = "icon icon-smaller"></div></td>
					<td>Used to change the font size making it one point smaller each time the button is pressed.</td>
                </tr>
				<tr>
					<td>Change case</td>
					<td><div class = "icon icon-change_case"></div></td>
					<td>Used to change the font case. <em>Sentence case.</em> - the case matches that of a common sentence. <em>lowercase</em> - all letters are small. <em>UPPERCASE</em> - all letters are capital. <em>Capitalize Each Word</em> - each word starts with a capital letter. <em>tOGGLE cASE</em> - reverse the case of the selected text or the word where the mouse cursor is positioned.</td>
				</tr>
				<tr>
					<td>Highlight color</td>
					<td><div class = "icon icon-highlightcolor"></div></td>
					<td>Used to mark separate sentences, phrases, words, or even characters by adding a color band that imitates the highlighter pen effect throughout the text. You can select the required part of the text and click the downward arrow next to the icon to select a color in the palette (this color set does not depend on the selected <b>Color scheme</b> and includes 16 colors) - the color will be applied to the selected text. Alternatively, you can first choose a highlight color and then start selecting the text with the mouse - the mouse pointer will look like this <div class = "icon icon-highlight_color_mouse_pointer"></div> and you'll be able to highlight several different parts of your text sequentially. To stop highlighting, just click the icon once again. To delete the highlight color, choose the <b>No Fill</b> option. The <b>Highlight color</b> is different from the <a href="../UsageInstructions/BackgroundColor.htm" onclick="onhyperlinkclick(this)"><b>Background color</b></a> <div class = "icon icon-backgroundcolor"></div> as the latter is applied to the whole paragraph and completely fills all the paragraph space from the left page margin to the right page margin.</td>
				</tr>
				<tr>
					<td>Font color</td>
					<td><div class = "icon icon-fontcolor"></div></td>
					<td>Used to change the color of the letters/characters in the text. By default, the automatic font color is set in a new blank document. It is displayed as a black font on the white background. If you change the background color to black, the font color will automatically change to white to keep the text clearly visible. To choose a different color, click the downward arrow next to the icon and select a color from the available palettes (the colors in the <b>Theme Colors</b> palette depend on the selected <a href="../UsageInstructions/ChangeColorScheme.htm" onclick="onhyperlinkclick(this)">color scheme</a>). After you change the default font color, you can use the <b>Automatic</b> option in the color palettes window to quickly restore the automatic color for the selected text passage.</td>
				</tr>
			</table>
			<p class="note">To learn more about color palettes, please refer to <a href="../UsageInstructions/ChangeColorScheme.htm" onclick="onhyperlinkclick(this)">this page</a>.</p>
		</div>
	</body>
</html>