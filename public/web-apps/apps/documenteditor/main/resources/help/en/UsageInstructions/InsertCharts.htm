﻿<!DOCTYPE html>
<html>
	<head>
		<title>Insert charts</title>
		<meta charset="utf-8" />
		<meta name="description" content="Add a chart to your document and adjust its position, size and properties" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Insert charts</h1>
            <h3>Insert a chart</h3>
            <p>To insert a chart in the <a href="https://www.onlyoffice.com/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Document Editor</b></a>,</p>
            <ol>
                <li>Place the cursor where the chart should be added.</li>
                <li>Switch to the <b>Insert</b> tab of the top toolbar.</li>
                <li>Click the <div class="icon icon-insertchart"></div> <b>Chart</b> icon on the top toolbar.</li>
                <li>
                    Select the needed chart type from the available ones:
                    <details class="details-example">
                        <summary>Column Charts</summary>
                        <ul>
                            <li>Clustered column</li>
                            <li>Stacked column</li>
                            <li>100% stacked column</li>
                            <li>3-D Clustered Column</li>
                            <li>3-D Stacked Column</li>
                            <li>3-D 100% stacked column</li>
                            <li>3-D Column</li>
                        </ul>
                    </details>
                    <details class="details-example">
                        <summary>Line Charts</summary>
                        <ul>
                            <li>Line</li>
                            <li>Stacked line</li>
                            <li>100% stacked line</li>
                            <li>Line with markers</li>
                            <li>Stacked line with markers</li>
                            <li>100% stacked line with markers</li>
                            <li>3-D Line</li>
                        </ul>
                    </details>
                    <details class="details-example">
                        <summary>Pie Charts</summary>
                        <ul>
                            <li>Pie</li>
                            <li>Doughnut</li>
                            <li>3-D Pie</li>
                        </ul>
                    </details>
                    <details class="details-example">
                        <summary>Bar Charts</summary>
                        <ul>
                            <li>Clustered bar</li>
                            <li>Stacked bar</li>
                            <li>100% stacked bar</li>
                            <li>3-D clustered bar</li>
                            <li>3-D stacked bar</li>
                            <li>3-D 100% stacked bar</li>
                        </ul>
                    </details>
                    <details class="details-example">
                        <summary>Area Charts</summary>
                        <ul>
                            <li>Area</li>
                            <li>Stacked area</li>
                            <li>100% stacked area</li>
                        </ul>
                    </details>
                    <details class="details-example">
                        <summary>Stock Charts</summary>
                    </details>
                    <details class="details-example">
                        <summary>XY (Scatter) Charts</summary>
                        <ul>
                            <li>Scatter</li>
                            <li>Stacked bar</li>
                            <li>Scatter with smooth lines and markers</li>
                            <li>Scatter with smooth lines</li>
                            <li>Scatter with straight lines and markers</li>
                            <li>Scatter with straight lines</li>
                        </ul>
                    </details>
                    <details class="details-example">
                        <summary>Combo Charts</summary>
                        <ul>
                            <li>Clustered column - line</li>
                            <li>Clustered column - line on secondary axis</li>
                            <li>Stacked area - clustered column</li>
                            <li>Custom combination</li>
                        </ul>
                    </details>
                    <p class="note"><b>Note</b>: <b>ONLYOFFICE Document Editor</b> supports the following types of charts that were created with third-party editors: <b>Pyramid</b>, <b>Bar (Pyramid)</b>, <b>Horizontal/Vertical Cylinders</b>, <b>Horizontal/Vertical Cones</b>. You can open the file containing such a chart and modify it using the available chart editing tools.</p>
                </li>
                <li>
                    After that the <b>Chart Editor</b> window will appear where you can enter the necessary data into the cells using the following controls:
                    <ul>
                        <li><div class="icon icon-copy"></div> and <div class="icon icon-paste"></div> for copying and pasting the copied data</li>
                        <li><div class="icon icon-undo1"></div> and <div class="icon icon-redo1"></div> for undoing and redoing actions</li>
                        <li><div class="icon icon-insertfunction"></div> for inserting a function</li>
                        <li><div class="icon icon-decreasedec"></div> and <div class="icon icon-increasedec"></div> for decreasing and increasing decimal places</li>
                        <li><img alt="Number format" src="../../../../../../common/main/resources/help/en/images/numberformat.png" /> for changing the number format, i.e. the way the numbers you enter appear in cells</li>
                        <li><img alt="Chart Type" src="../../../../../../common/main/resources/help/en/images/charttypebutton.png" /> for choosing a different type of chart.</li>
                    </ul>
                    <p><img alt="Chart Editor window" src="../../../../../../common/main/resources/help/en/images/charteditor.png" /></p>
                </li>
                <li>
                    Click the <b>Select Data</b> button situated in the <b>Chart Editor</b> window. The <b>Chart Data</b> window will open.
                    <ol>
                        <li>
                            Use the <b>Chart Data</b> dialog to manage <b>Chart Data Range</b>, <b>Legend Entries (Series)</b>, <b>Horizontal (Category) Axis Label</b> and <b>Switch Row/Column</b>.
                            <p><img alt="Chart Data window" src="../../../../../../common/main/resources/help/en/images/chartdata.png" /></p>
                            <ul>
                                <li>
                                    <b>Chart Data Range</b> - select data for your chart.
                                    <ul>
                                        <li>
                                            Click the <div class="icon icon-changerange"></div> icon on the right of the <b>Chart data range</b> box to select data range.
                                            <p><img alt="Select Data Range window" src="../../../../../../common/main/resources/help/en/images/selectdata.png" /></p>
                                        </li>
                                    </ul>
                                </li>
                                <li>
                                    <b>Legend Entries (Series)</b> - add, edit, or remove legend entries. Type or select series name for legend entries.
                                    <ul>
                                        <li>In <b>Legend Entries (Series)</b>, click <b>Add</b> button.</li>
                                        <li>
                                            In <b>Edit Series</b>, type a new legend entry or click the <div class="icon icon-changerange"></div> icon on the right of the <b>Select name</b> box.
                                            <p><img alt="Edit Series window" src="../../../../../../common/main/resources/help/en/images/editseries.png" /></p>
                                        </li>
                                    </ul>
                                </li>
                                <li>
                                    <b>Horizontal (Category) Axis Labels</b> - change text for category labels.
                                    <ul>
                                        <li><b>In Horizontal (Category) Axis Labels</b>, click <b>Edit</b>.</li>
                                        <li>
                                            In <b>Axis label range</b>, type the labels you want to add or click the <div class="icon icon-changerange"></div> icon on the right of the <b>Axis label range</b> box to select data range.
                                            <p><img alt="Axis Labels window" src="../images/axislabels.png" /></p>
                                        </li>
                                    </ul>
                                </li>
                                <li><b>Switch Row/Column</b> - rearrange the worksheet data that is configured in the chart not in the way that you want it. Switch rows to columns to display data on a different axis. </li>
                            </ul>
                        </li>
                        <li>Click <b>OK</b> button to apply the changes and close the window.</li>
                    </ol>
                </li>
                <li>
                    Click the <b>Change Chart Type</b> button in the <b>Chart Editor</b> window to choose chart type and style. Select a chart from the available sections: Column, Line, Pie, Bar, Area, Stock, XY (Scatter), or Combo.
                    <p><img alt="Chart Type window" src="../../../../../../common/main/resources/help/en/images/charttype.png" /></p>
                    <p>When you choose <b>Combo Charts</b>, the <b>Chart Type</b> window lists chart series and allows choosing the types of charts to combine and selecting data series to place on a seconary axis.</p>
                    <p><img alt="Chart Type Combo" src="../../../../../../common/main/resources/help/en/images/charttype_combo.png" /></p>
                </li>
                <li>
                    Change the chart settings by clicking the <b>Edit Chart</b> button situated in the <b>Chart Editor</b> window. The <b>Chart - Advanced Settings</b> window will open.
                    <p><img alt="Chart - Advanced Settings window" src="../../../../../../common/main/resources/help/en/images/chartsettings_layout.png" /></p>
                    <p>The <b>Layout</b> tab allows you to change the layout of chart elements.</p>
                    <ul>
                        <li>
                            Specify the <b>Chart Title</b> position in regard to your chart selecting the necessary option from the drop-down list:
                            <ul>
                                <li><b>None</b> to not display a chart title,</li>
                                <li><b>Overlay</b> to overlay and center a title on the plot area,</li>
                                <li><b>No Overlay</b> to display the title above the plot area.</li>
                            </ul>
                        </li>
                        <li>
                            Specify the <b>Legend</b> position in regard to your chart selecting the necessary option from the drop-down list:
                            <ul>
                                <li><b>None</b> to not display a legend,</li>
                                <li><b>Bottom</b> to display the legend and align it to the bottom of the plot area,</li>
                                <li><b>Top</b> to display the legend and align it to the top of the plot area,</li>
                                <li><b>Right</b> to display the legend and align it to the right of the plot area,</li>
                                <li><b>Left</b> to display the legend and align it to the left of the plot area,</li>
                                <li><b>Left Overlay</b> to overlay and center the legend to the left on the plot area,</li>
                                <li><b>Right Overlay</b> to overlay and center the legend to the right on the plot area.</li>
                            </ul>
                        </li>
                        <li>
                            Specify the <b>Data Labels</b> (i.e. text labels that represent exact values of data points) parameters:<br />
                            <ul>
                                <li>
                                    Specify the <b>Data Labels</b> position relative to the data points selecting the necessary option from the drop-down list. The available options vary depending on the selected chart type.
                                    <ul>
                                        <li>For <b>Column/Bar</b> charts, you can choose the following options: <b>None</b>, <b>Center</b>, <b>Inner Bottom</b>, <b>Inner Top</b>, <b>Outer Top</b>.</li>
                                        <li>For <b>Line/XY (Scatter)/Stock</b> charts, you can choose the following options: <b>None</b>, <b>Center</b>, <b>Left</b>, <b>Right</b>, <b>Top</b>, <b>Bottom</b>.</li>
                                        <li>For <b>Pie</b> charts, you can choose the following options: <b>None</b>, <b>Center</b>, <b>Fit to Width</b>, <b>Inner Top</b>, <b>Outer Top</b>.</li>
                                        <li>For <b>Area</b> charts as well as for <b>3D</b> <b>Column</b>, <b>Line</b>, <b>Bar</b> and <b>Combo</b> charts, you can choose the following options: <b>None</b>, <b>Center</b>.</li>
                                    </ul>
                                </li>
                                <li>Select the data you wish to include into your labels checking the corresponding boxes: <b>Series Name</b>, <b>Category Name</b>, <b>Value</b>,</li>
                                <li>Enter a character (comma, semicolon etc.) you wish to use for separating several labels into the <b>Data Labels Separator</b> entry field.</li>
                            </ul>
                        </li>
                        <li><b>Lines</b> - is used to choose a line style for <b>Line/XY (Scatter) charts</b>. You can choose one of the following options: <b>Straight</b> to use straight lines between data points, <b>Smooth</b> to use smooth curves between data points, or <b>None</b> to not display lines.</li>
                        <li>
                            <b>Markers</b> - is used to specify whether the markers should be displayed (if the box is checked) or not (if the box is unchecked) for <b>Line/XY (Scatter) charts</b>.
                            <p class="note"><b>Note</b>: the <b>Lines</b> and <b>Markers</b> options are available for <b>Line charts</b> and <b>XY (Scatter) charts</b> only.</p>
                        </li>
                    </ul>
                    <p><img alt="Chart - Advanced Settings window" src="../../../../../../common/main/resources/help/en/images/chartsettings_verticalaxis.png" /></p>
                    <p>The <b>Vertical Axis</b> tab allows you to change the parameters of the vertical axis also referred to as the values axis or y-axis which displays numeric values. Note that the vertical axis will be the category axis which displays text labels for the <b>Bar charts</b>, therefore in this case the <b>Vertical Axis</b> tab options will correspond to the ones described in the next section. For the <b>XY (Scatter) charts</b>, both axes are value axes.</p>
                    <p class="note"><b>Note</b>: the <b>Axis Settings</b> and <b>Gridlines</b> sections will be disabled for <b>Pie charts</b> since charts of this type have no axes and gridlines.</p>
                    <ul>
                        <li>Select <b>Hide</b> to hide vertical axis in the chart, leave it unchecked to have vertical axis displayed.</li>
                        <li>
                            Specify <b>Title</b> orientation by selecting the necessary option from the drop-down list:
                            <ul>
                                <li><b>None</b> to not display a vertical axis title</li>
                                <li><b>Rotated</b> to display the title from bottom to top to the left of the vertical axis,</li>
                                <li><b>Horizontal</b> to display the title horizontally to the left of the vertical axis.</li>
                            </ul>
                        </li>
                        <li><b>Minimum Value</b> - is used to specify the lowest value displayed at the vertical axis start. The <b>Auto</b> option is selected by default, in this case the minimum value is calculated automatically depending on the selected data range. You can select the <b>Fixed</b> option from the drop-down list and specify a different value in the entry field on the right.</li>
                        <li><b>Maximum Value</b> - is used to specify the highest value displayed at the vertical axis end. The <b>Auto</b> option is selected by default, in this case the maximum value is calculated automatically depending on the selected data range. You can select the <b>Fixed</b> option from the drop-down list and specify a different value in the entry field on the right.</li>
                        <li><b>Axis Crosses</b> - is used to specify a point on the vertical axis where the horizontal axis should cross it. The <b>Auto</b> option is selected by default, in this case the axes intersection point value is calculated automatically depending on the selected data range. You can select the <b>Value</b> option from the drop-down list and specify a different value in the entry field on the right, or set the axes intersection point at the <b>Minimum/Maximum Value</b> on the vertical axis.</li>
                        <li><b>Display Units</b> - is used to determine the representation of the numeric values along the vertical axis. This option can be useful if you're working with great numbers and wish the values on the axis to be displayed in a more compact and readable way (e.g. you can represent 50 000 as 50 by using the <b>Thousands</b> display units). Select desired units from the drop-down list: <b>Hundreds</b>, <b>Thousands</b>, <b>10 000</b>, <b>100 000</b>, <b>Millions</b>, <b>10 000 000</b>, <b>100 000 000</b>, <b>Billions</b>, <b>Trillions</b>, or choose the <b>None</b> option to return to the default units.</li>
                        <li><b>Values in reverse order</b> -  is used to display values in the opposite direction. When the box is unchecked, the lowest value is at the bottom and the highest value is at the top of the axis. When the box is checked, the values are ordered from top to bottom.</li>
                        <li>
                            The <b>Tick Options</b> section allows adjusting the appearance of tick marks on the vertical scale. Major tick marks are the larger scale divisions which can have labels displaying numeric values. Minor tick marks are the scale subdivisions which are placed between the major tick marks and have no labels. Tick marks also define where gridlines can be displayed if the corresponding option is set on the <b>Layout</b> tab. The <b>Major/Minor Type</b> drop-down lists contain the following placement options:
                            <ul>
                                <li><b>None</b> to not display major/minor tick marks,</li>
                                <li><b>Cross</b> to display major/minor tick marks on both sides of the axis,</li>
                                <li><b>In</b> to display major/minor tick marks inside the axis,</li>
                                <li><b>Out</b> to display major/minor tick marks outside the axis.</li>
                            </ul>
                        </li>
                        <li>
                            The <b>Label Options</b> section allows adjusting the appearance of major tick mark labels which display values. To specify a <b>Label Position</b> in regard to the vertical axis, select the necessary option from the drop-down list:
                            <ul>
                                <li><b>None</b> to not display tick mark labels,</li>
                                <li><b>Low</b> to display tick mark labels to the left of the plot area,</li>
                                <li><b>High</b> to display tick mark labels to the right of the plot area,</li>
                                <li><b>Next to axis</b> to display tick mark labels next to the axis.</li>
                                <li>
                                    To specify a <b>Label Format</b> click the Label Format button and choose a category as it deems appropriate.
                                    <p>Available label format categories:</p>
                                    <ul>
                                        <li>General</li>
                                        <li>Number</li>
                                        <li>Scientific</li>
                                        <li>Accounting</li>
                                        <li>Currency</li>
                                        <li>Date</li>
                                        <li>Time</li>
                                        <li>Percentage</li>
                                        <li>Fraction</li>
                                        <li>Text</li>
                                        <li>Custom</li>
                                    </ul>
                                    <p>Label format options vary depending on the selected category. For more information on changing number format, go to <a href="https://helpcenter.onlyoffice.com/ONLYOFFICE-Editors/ONLYOFFICE-Spreadsheet-Editor/UsageInstructions/ChangeNumberFormat.aspx">this page</a>.</p>
                                </li>
                                <li>Check <b>Linked to source</b> to keep number formatting  from the data source in the chart.</li>
                            </ul>
                        </li>
                    </ul>
                    <p>
                        <img alt="Chart - Advanced Settings window" src="../../../../../../common/main/resources/help/en/images/chartsettings_secondaryaxis1.png" />
                    </p>
                    <p class="note"><b>Note</b>: Secondary axes are supported in <b>Combo</b> charts only.</p>
                    <p><b>Secondary axes</b> are useful in Combo charts when data series vary considerably or mixed types of data are used to plot a chart. Secondary Axes make it easier to read and understand a combo chart.</p>
                    <p>The <b>Secondary Vertical /Horizontal Axis</b> tab appears when you choose an appropriate data series for a combo chart. All the settings and options on the <b>Secondary Vertical/Horizontal Axis</b> tab are the same as the settings on the Vertical/Horizontal Axis. For a detailed description of the <b>Vertical/Horizontal Axis</b> options, see description above/below.</p>
                    <p><img alt="Chart - Advanced Settings window" src="../../../../../../common/main/resources/help/en/images/chartsettings_horizontalaxis.png" /></p>
                    <p>The <b>Horizontal Axis</b> tab allows you to change the parameters of the horizontal axis also referred to as the categories axis or x-axis which displays text labels. Note that the horizontal axis will be the value axis which displays numeric values for the <b>Bar charts</b>, therefore in this case the <b>Horizontal Axis</b> tab options will correspond to the ones described in the previous section. For the <b>XY (Scatter) charts</b>, both axes are value axes.</p>
                    <ul>
                        <li>Select <b>Hide</b> to hide horizontal axis in the chart, leave it unchecked to have horizontal axis displayed.</li>
                        <li>
                            Specify <b>Title</b> orientation by selecting the necessary option from the drop-down list:
                            <ul>
                                <li><b>None</b> when you don’t want to display a horizontal axis title,</li>
                                <li><b>No Overlay</b>  to display the title below the horizontal axis,</li>
                            </ul>
                        </li>
                        <li><b>Gridlines</b> is used to specify the <b>Horizontal Gridlines</b> to display by selecting the necessary option from the drop-down list: <b>None</b>,  <b>Major</b>, <b>Minor</b>, or <b>Major and Minor</b>.</li>
                        <li><b>Axis Crosses</b> - is used to specify a point on the horizontal axis where the vertical axis should cross it. The <b>Auto</b> option is selected by default, in this case the axes intersection point value is calculated automatically depending on the selected data range. You can select the <b>Value</b> option from the drop-down list and specify a different value in the entry field on the right, or set the axes intersection point at the <b>Minimum/Maximum Value</b> (that corresponds to the first and last category) on the horizontal axis.</li>
                        <li><b>Axis Position</b> - is used to specify where the axis text labels should be placed: <b>On Tick Marks</b> or <b>Between Tick Marks</b>.</li>
                        <li><b>Values in reverse order</b> - is used to display categories in the opposite direction. When the box is unchecked, categories are displayed from left to right. When the box is checked, the categories are ordered from right to left.</li>
                        <li>
                            The <b>Tick Options</b> section allows adjusting the appearance of tick marks on the horizontal scale. Major tick marks are the larger divisions which can have labels displaying category values. Minor tick marks are the smaller divisions which are placed between the major tick marks and have no labels. Tick marks also define where gridlines can be displayed if the corresponding option is set on the <b>Layout</b> tab. You can adjust the following tick mark parameters:
                            <ul>
                                <li><b>Major/Minor Type</b> - is used to specify the following placement options: <b>None</b> to not display major/minor tick marks, <b>Cross</b> to display major/minor tick marks on both sides of the axis, <b>In</b> to display major/minor tick marks inside the axis, <b>Out</b> to display major/minor tick marks outside the axis.</li>
                                <li><b>Interval between Marks</b> - is used to specify how many categories should be displayed between two adjacent tick marks.</li>
                            </ul>
                        </li>
                        <li>
                            The <b>Label Options</b> section allows adjusting the appearance of labels which display categories.
                            <ul>
                                <li><b>Label Position</b> - is used to specify where the labels should be placed in regard to the horizontal axis. Select the necessary option from the drop-down list: <b>None</b> to not display category labels, <b>Low</b> to display category labels at the bottom of the plot area, <b>High</b> to display category labels at the top of the plot area, <b>Next to axis</b> to display category labels next to the axis.</li>
                                <li><b>Axis Label Distance</b> - is used to specify how closely the labels should be placed to the axis. You can specify the necessary value in the entry field. The more the value you set, the more the distance between the axis and labels is.</li>
                                <li><b>Interval between Labels</b> - is used to specify how often the labels should be displayed. The <b>Auto</b> option is selected by default, in this case labels are displayed for every category. You can select the <b>Manual</b> option from the drop-down list and specify the necessary value in the entry field on the right. For example, enter 2 to display labels for every other category etc.</li>
                                <li>
                                    To specify a <b>Label Format</b> click the Label Format button and choose a category as it deems appropriate.
                                    <p>Available label format categories:</p>
                                    <ul>
                                        <li>General</li>
                                        <li>Number</li>
                                        <li>Scientific</li>
                                        <li>Accounting</li>
                                        <li>Currency</li>
                                        <li>Date</li>
                                        <li>Time</li>
                                        <li>Percentage</li>
                                        <li>Fraction</li>
                                        <li>Text</li>
                                        <li>Custom</li>
                                    </ul>
                                    <p>Label format options vary depending on the selected category. For more information on changing number format, go to <a href="https://helpcenter.onlyoffice.com/ONLYOFFICE-Editors/ONLYOFFICE-Spreadsheet-Editor/UsageInstructions/ChangeNumberFormat.aspx">this page</a>.</p>
                                </li>
                                <li>Check <b>Linked to source</b> to keep number formatting  from the data source in the chart.</li>
                            </ul>
                        </li>
                    </ul>
                    <p><img alt="Chart - Advanced Settings: Cell Snapping" src="../../../../../../common/main/resources/help/en/images/chartsettings_cellsnapping.png" /></p>
                    <p>The <b>Cell Snapping</b> tab contains the following parameters:</p>
                    <ul>
                        <li><b>Move and size with cells</b> - this option allows you to snap the chart to the cell behind it. If the cell moves (e.g. if you insert or delete some rows/columns), the chart will be moved together with the cell. If you increase or decrease the width or height of the cell, the chart will change its size as well.</li>
                        <li><b>Move but don't size with cells</b> - this option allows to snap the chart to the cell behind it preventing the chart from being resized. If the cell moves, the chart will be moved together with the cell, but if you change the cell size, the chart dimensions remain unchanged.</li>
                        <li><b>Don't move or size with cells</b> - this option allows to prevent the chart from being moved or resized if the cell position or size was changed.</li>
                    </ul>
                    <p><img alt="Chart - Advanced Settings" src="../../../../../../common/main/resources/help/en/images/chartsettings_alternativetext.png" /></p>
                    <p>The <b>Alternative Text</b> tab allows specifying a <b>Title</b> and <b>Description</b> which will be read to people with vision or cognitive impairments to help them better understand what information the chart contains.</p>
                </li>
            </ol>
            <hr />
            <h3>Move and resize charts</h3>
            <p><img class="floatleft" alt="Moving chart" src="../images/moving_chart.png" />Once the chart is added, you can change its size and position. To change the chart size, drag small squares <span class="icon icon-resize_square"></span> situated on its edges. To maintain the original proportions of the selected chart while resizing, hold down the <b>Shift</b> key and drag one of the corner icons.</p>
            <p>To alter the chart position, use the <span class="icon icon-arrow"></span> icon that appears after hovering your mouse cursor over the chart. Drag the chart to the necessary position without releasing the mouse button. When you move the chart, guide lines are displayed to help you position the object on the page precisely (if a wrapping style other than inline is selected).</p>
            <p class="note">
                <b>Note</b>: the list of keyboard shortcuts that can be used when working with objects is available <a href="../HelpfulHints/KeyboardShortcuts.htm#workwithobjects" onclick="onhyperlinkclick(this)">here</a>.
            </p>
            <hr />
            <h3>Edit chart elements</h3>
            <p>To edit the chart <b>Title</b>, select the default text with the mouse and type the required text.</p>
            <p>To change the font formatting within text elements, such as the chart title, axes titles, legend entries, data labels etc., select the necessary text element by left-clicking it. Then use the corresponding icons on the <b>Home</b> tab of the top toolbar to change the font <a href="../UsageInstructions/FontTypeSizeColor.htm" onclick="onhyperlinkclick(this)">type, size, color</a> or its <a href="../UsageInstructions/DecorationStyles.htm" onclick="onhyperlinkclick(this)">decoration style</a>.</p>
            <p>When the chart is selected, the <b>Shape settings</b> <span class="icon icon-shape_settings_icon"></span> icon is also available on the right, since a shape is used as a background for the chart. You can click this icon to open the <b>Shape settings</b> tab on the right sidebar and adjust <a href="../UsageInstructions/InsertAutoshapes.htm#shape_fill" onclick="onhyperlinkclick(this)"><b>Fill</b></a>, <a href="../UsageInstructions/InsertAutoshapes.htm#shape_stroke" onclick="onhyperlinkclick(this)"><b>Stroke</b></a> and <b>Wrapping Style</b> of the shape. Note that you cannot change the shape type.</p>
            <p>
                Using the <b>Shape Settings</b> tab on the right panel, you can both adjust the chart area itself and change the chart elements, such as <em>plot area</em>, <em>data series</em>, <em>chart title</em>, <em>legend</em> etc and apply different fill types to them. Select the chart element clicking it with the left mouse button and choose the preferred fill type: <em>solid color</em>, <em>gradient</em>, <em>texture</em> or <em>picture</em>, <em>pattern</em>. Specify the fill parameters and set the <em>Opacity</em> level if necessary.
                When you select a vertical or horizontal axis or gridlines, the stroke settings are only available at the <b>Shape Settings</b> tab: <em>color</em>, <em>width</em> and <em>type</em>. For more details on how to work with shape colors, fills and stroke, you can refer to <a href="../UsageInstructions/InsertAutoshapes.htm" onclick="onhyperlinkclick(this)">this page</a>.
            </p>
            <p class="note"><b>Note</b>: the <b>Show shadow</b> option is also available at the <b>Shape settings</b> tab, but it is disabled for chart elements.</p>
            <p>If you need to resize chart elements, left-click to select the needed element and drag one of 8 white squares <span class="icon icon-resize_square"></span> located along the perimeter of the element.</p>
            <p><span class="big big-resizeelement"></span></p>
            <p>To change the position of the element, left-click on it, make sure your cursor changed to <span class="icon icon-arrow"></span>, hold the left mouse button and drag the element to the needed position.</p>
            <p><span class="big big-moveelement"></span></p>
            <p>To delete a chart element, select it by left-clicking and press the <b>Delete</b> key on the keyboard.</p>
            <p>You can also rotate 3D charts using the mouse. Left-click within the plot area and hold the mouse button. Drag the cursor without releasing the mouse button to change the 3D chart orientation.</p>
            <p><img alt="3D chart" src="../../../../../../common/main/resources/help/en/images/3dchart.png" /></p>
            <hr />
            <h3>Adjust chart settings</h3>
            <p><img class="floatleft" alt="Chart Settings tab" src="../images/right_chart.png" /></p>
            <p>Some of the chart settings can be altered using the <b>Chart settings</b> tab of the right sidebar. To activate it click the chart and choose the <b>Chart settings</b> <span class="icon icon-chart_settings_icon"></span> icon on the right. Here you can change the following properties:</p>
            <ul style="margin-left: 280px;">
                <li><b>Size</b> is used to view the <b>Width</b> and <b>Height</b> of the current chart.</li>
                <li><b>Wrapping Style</b> is used to select a text wrapping style from the available ones - inline, square, tight, through, top and bottom, in front, behind (for more information see the advanced settings description below).</li>
                <li>
                    <b>Change Chart Type</b> is used to change the selected chart type and/or style.
                    <p>To select the necessary chart <b>Style</b>, use the second drop-down menu in the <b>Change Chart Type</b> section.</p>
                </li>
                <li>
                    <b>Edit Data</b> is used to open the 'Chart Editor' window.
                    <p class="note"><b>Note</b>: to quickly open the 'Chart Editor' window you can also double-click the chart in the document.</p>
                </li>
            </ul>
            <p>You can also find some of these options in the <b>right-click menu</b>. The menu options are:</p>
            <ul style="margin-left: 280px;">
                <li><b>Cut, Copy, Paste</b> - standard options which are used to cut or copy the selected text/object and paste the previously cut/copied text passage or object to the current cursor position.</li>
                <li><b>Arrange</b> is used to bring the selected chart to foreground, send it to the background, move forward or backward as well as group or ungroup charts to perform operations with several of them at once. To learn more on how to arrange objects, please refer to <a href="../UsageInstructions/AlignArrangeObjects.htm" onclick="onhyperlinkclick(this)">this page</a>.</li>
                <li><b>Align</b> is used to align the chart left, center, right, top, middle, bottom. To learn more on how to align objects you can refer to <a href="../UsageInstructions/AlignArrangeObjects.htm" onclick="onhyperlinkclick(this)">this page</a>.</li>
                <li><b>Wrapping Style</b> is used to select a text wrapping style from the available ones - inline, square, tight, through, top and bottom, in front, behind. The <b>Edit Wrap Boundary</b> option is unavailable for charts.</li>
                <li><b>Edit Data</b> is used to open the 'Chart Editor' window.</li>
                <li><b>Chart Advanced Settings</b> is used to open the 'Chart - Advanced Settings' window.</li>
            </ul>
            <p>Additionally, <b>3D Rotation</b> settings are available for 3D charts:</p>
            <p><img class="floatleft" alt="Chart Settings tab" src="../../../../../../common/main/resources/help/en/images/right_chart_3d.png" /></p>
            <ul style="margin-left: 280px;">
                <li><b>X rotation</b> - set the required value for the X axis rotation using the keyboard or via the <em>Left</em> and <em>Right</em> arrows to the right.</li>
                <li><b>Y rotation</b> - set the required value for the Y axis rotation using the keyboard or via the <em>Up</em> and <em>Down</em> arrows to the right.</li>
                <li><b>Perspective</b> - set the required value for depth rotation using the keyboard or via the <em>Narrow field of view</em> and <em>Widen field of view</em> arrows to the right.</li>
                <li><b>Right Angle Axis</b> - is used to set the right angle axis view.</li>
                <li><b>Autoscale</b> - check this box to autoscale the depth and height values of the chart, or uncheck this box to set the depth and height values manually.</li>
                <li><b>Depth (% of base)</b> - set the required depth value using the keyboard or via the arrows.</li>
                <li><b>Height (% of base)</b> - set the required height value using the keyboard or via the arrows.</li>
                <li><b>Default Rotation</b> - set the 3D parameters to their default.
                <p class="note">Please note that you cannot edit each element of the chart; the settings will be applied to the chart as a whole.</p></li>
            </ul>
                <hr />
                <p>To change the chart advanced settings, click the needed chart with the right mouse button and select <b>Chart Advanced Settings</b> from the right-click menu or just click the <b>Show advanced settings</b> link on the right sidebar. The chart properties window will open:</p>
                <p><img alt="Chart - Advanced Settings: Size" src="../images/chart_properties.png" /></p>
                <p>The <b>Size</b> tab contains the following parameters:</p>
                <ul>
                    <li><b>Width</b> and <b>Height</b> - use these options to change the width and/or height of the chart. If the <b>Constant Proportions</b> <div class="icon icon-constantproportions"></div> button is clicked (in this case it looks like this <div class="icon icon-constantproportionsactivated"></div>), the width and height will be changed together preserving the original chart aspect ratio.</li>
                </ul>
                <p><img alt="Chart - Advanced Settings: Text Wrapping" src="../images/chart_properties_1.png" /></p>
                <p>The <b>Text Wrapping</b> tab contains the following parameters:</p>
                <ul>
                    <li>
                        <b>Wrapping Style</b> - use this option to change the way the chart is positioned relative to the text: it will either be a part of the text (in case you select the inline style) or bypassed by it from all sides (if you select one of the other styles).
                        <ul>
                            <li>
                                <p><div class="icon icon-wrappingstyle_inline"></div> <b>Inline</b> - the chart is considered to be a part of the text, like a character, so when the text moves, the chart moves as well. In this case the positioning options are inaccessible.</p>
                                <p>If one of the following styles is selected, the chart can be moved independently of the text and positioned on the page exactly:</p>
                            </li>
                            <li><p><div class="icon icon-wrappingstyle_square"></div> <b>Square</b> - the text wraps the rectangular box that bounds the chart.</p></li>
                            <li><p><div class="icon icon-wrappingstyle_tight"></div> <b>Tight</b> - the text wraps the actual chart edges.</p></li>
                            <li><p><div class="icon icon-wrappingstyle_through"></div> <b>Through</b> - the text wraps around the chart edges and fills in the open white space within the chart.</p></li>
                            <li><p><div class="icon icon-wrappingstyle_topandbottom"></div> <b>Top and bottom</b> - the text is only above and below the chart.</p></li>
                            <li><p><div class="icon icon-wrappingstyle_infront"></div> <b>In front</b> - the chart overlaps the text.</p></li>
                            <li><p><div class="icon icon-wrappingstyle_behind"></div> <b>Behind</b> - the text overlaps the chart.</p></li>
                        </ul>
                    </li>
                </ul>
                <p>If you select the square, tight, through, or top and bottom styles, you will be able to set up some additional parameters - <b>distance from text</b> at all sides (top, bottom, left, right).</p>
                <p id="position"><img alt="Chart - Advanced Settings: Position" src="../images/chart_properties_2.png" /></p>
                <p>The <b>Position</b> tab is available only if the selected wrapping style is not inline. This tab contains the following parameters that vary depending on the selected wrapping style:</p>
                <ul>
                    <li>
                        The <b>Horizontal</b> section allows you to select one of the following three chart positioning types:
                        <ul>
                            <li><b>Alignment</b> (left, center, right) <b>relative to</b> character, column, left margin, margin, page or right margin,</li>
                            <li>Absolute <b>Position</b> measured in absolute units i.e. <b>Centimeters</b>/<b>Points</b>/<b>Inches</b> (depending on the option specified on the <b>File</b> -> <b>Advanced Settings...</b> tab) <b>to the right of</b> character, column, left margin, margin, page or right margin,</li>
                            <li><b>Relative position</b> measured in percent <b>relative to</b> the left margin, margin, page or right margin.</li>
                        </ul>
                    </li>
                    <li>
                        The <b>Vertical</b> section allows you to select one of the following three chart positioning types:
                        <ul>
                            <li><b>Alignment</b> (top, center, bottom) <b>relative to</b> line, margin, bottom margin, paragraph, page or top margin,</li>
                            <li>Absolute <b>Position</b> measured in absolute units i.e. <b>Centimeters</b>/<b>Points</b>/<b>Inches</b> (depending on the option specified on the <b>File</b> -> <b>Advanced Settings...</b> tab) <b>below</b> line, margin, bottom margin, paragraph, page or top margin,</li>
                            <li><b>Relative position</b> measured in percent <b>relative to</b> the margin, bottom margin, page or top margin.</li>
                        </ul>
                    </li>
                    <li><b>Move object with text</b> ensures that the chart moves along with the text to which it is anchored.</li>
                    <li><b>Allow overlap</b> makes it possible for two charts to overlap if you drag them near each other on the page.</li>
                </ul>
                <p><img alt="Chart - Advanced Settings" src="../images/chart_properties_3.png" /></p>
                <p>The <b>Alternative Text</b> tab allows specifying a <b>Title</b> and <b>Description</b> which will be read to the people with vision or cognitive impairments to help them better understand what information the chart contains.</p>
        </div>
	</body>
</html>