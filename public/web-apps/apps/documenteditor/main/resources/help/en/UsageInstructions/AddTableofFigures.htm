﻿<!DOCTYPE html>
<html>
	<head>
		<title>Add and Format a Table of Figures</title>
		<meta charset="utf-8" />
		<meta name="description" content="Add, format and update Table of Figures using captioned objects and styles" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
			<div class="search-field">
				<input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
		<h1>Add and Format a Table of Figures</h1>
			<p><b>Table of Figures</b> provides an overview of equations, figures and tables added to a document. Similar to a table of contents, a <b>Table of Figures</b> lists, sorts out and arranges captioned objects or text headings that have a certain style applied. This makes it easy to reference them in your document and to navigate between figures. Click the link in the <b>Table of Figures</b> formatted as links and you will be taken directly to the figure or the heading. In the <a href="https://www.onlyoffice.com/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Document Editor</b></a>, any table, equation, diagram, drawing, graph, chart, map, photograph or another type of illustration is presented as a figure. 
            <p><img alt="References Tab" src="../images/referencestab.png" /></p>
			<p>To add a <b>Table of Figures</b> go to the <b>References</b> tab and use the <b>Table of Figures</b> <span class = "icon icon-table_figures_button"></span> toolbar button  to set up and format a table of figures. Use the Refresh button to update a table of figures each time you add a new figure to your document.</p>
			<h2>Creating a Table of Figures</h2>
			<p class="note"><b>Note:</b> You can create a Table of Figures using either captioned figures or styles. Before proceeding, a <a href="../UsageInstructions/AddCaption.htm" onclick="onhyperlinkclick(this)">caption</a> must be added to each equation, table or figure, or a <a href="../UsageInstructions/FormattingPresets.htm" onclick="onhyperlinkclick(this)">style</a> must be applied to the text so that it is correctly included in a Table of Figures.</p>
			<ol>
			<li>Once you have added captions or styles, place your cursor where you want to inset a <b>Table of Figures</b> and go to the <b>References</b> tab then click the <b>Table of Figures</b> button to open the <b>Table of Figures</b> dialog box, and generate the list of figures.
            <p><img alt="Table of Figures Settings" src="../images/table_figures_settings.png" /></p>
			</li>
			<li>Choose an option to build a <b>Table of Figures</b> from the <b>Caption</b> or <b>Style group</b>.
			<ul style = "list-style-type:disc">
			<li>You can create a Table of Figures based on captioned objects. Check the Caption box and select a captioned object from the drop-down list:
			<ul style = "list-style-type:circle">
					<li>None;</li>
                    <li>Equation;</li>
                    <li>Figure;</li>
                    <li>Table.
			<p><img alt="Table of Figures Captioned" src="../images/table_figures_captioned.png" /></p>
					</li>
				 </ul>
			 </li>
            <li>You can create a <b>Table of Figures</b> based on the styles used to format text. Check the <b>Style</b> box and select a style from the drop-down list. The list of options may vary depending on the style applied:
			<ul style = "list-style-type:circle">
                       <li>Heading 1;</li>
                       <li>Heading 2;</li>
                       <li>Caption;</li>
                       <li>Table of Figures;</li>
					   <li>Normal.
			<p><img alt="Table of Figures Style" src="../images/table_figures_style.png" /></p>
						</li>
					</ul>
				</li>
			</ul>
			</li>
			</ol>
			<h2>Formatting a Table of Figures</h2>
            <p>The check box options allow you to format a <b>Table of Figures</b>. All formatting check boxes are activated by default as in most cases it is more reasonable to have them. Uncheck the boxes you don’t need.</p>
			<ul style = "list-style-type:none">
						<li><b>Show page numbers</b> - to display the page number the figure appears on;</li>
						<li><b>Right align page numbers</b> - to display page numbers on the right when <b>Show page numbers</b> is active; uncheck it to display page numbers right after the title;</li>
						<li><b>Format table of figures as links</b> - to add hyperlinks to the <b>Table of Figures</b>;</li>
						<li><b>Include label and number</b> - to add a label and number to the Table of Figures.</li>
			</ul>
			<ul style = "list-style-type:disc">
			<li>Choose the Leader style from the drop-down list to connect titles to page numbers for a better visualization.</li>
			<li>Customize the table of figures text styles by choosing one of the available styles from the drop-down list:
				<ul style = "list-style-type:circle">
						<li><b>Current</b> - displays the style chosen previously.</li>
						<li><b>Simple</b> - highlights text in bold.</li>
						<li><b>Online</b> - highlights and arranges text as a hyperlink.</li>
						<li><b>Classic</b> - makes the text all caps.</li>
						<li><b>Distinctive</b> - highlights text in italic.</li>
						<li><b>Centered</b> - centers the text and displays no leader.</li>
						<li><b>Formal</b> - displays text in 11 pt Arial to give a more formal look.</li>
				</ul>
				</li>
		<li>Preview window displays how the Table of Figures appears in the document or when printed.</li>
			</ul>
		<h2>Updating a Table of Figures</h2>
		<p>Update a <b>Table of Figures</b> each time you add a new equation, figure or table to your document.The <b>Refresh</b> button becomes active when you click or select the Table of Figures. Click the Refresh <span class = "icon icon-refresh_button"></span> button on the <b>References</b> tab of the top toolbar and select the necessary option from the menu:</p>
		<img alt="Refresh Popup" src="../images/refresh_table-figures_popup.png" />
		<ul style = "list-style-type:disc">
				<li><b>Refresh page numbers only</b> - to update page numbers without applying changes to the headings.</li>
				<li><b>Refresh entire table</b> - to update all the headings that have been modified and page numbers.</li>
			</ul>
		<p>Click <b>OK</b> to confirm your choice or</p>
		<p>Right-click the <b>Table of Figures</b> in your document to open the contextual menu, then choose the <b>Refresh field</b> to update the <b>Table of Figures</b>.</p>
		</div>
	</body>
</html>