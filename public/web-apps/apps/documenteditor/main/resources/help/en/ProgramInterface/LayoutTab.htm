﻿<!DOCTYPE html>
<html>
	<head>
		<title>Layout tab</title>
		<meta charset="utf-8" />
        <meta name="description" content="Introducing the Document Editor user interface - Layout tab" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Layout tab</h1>
            <p>The <b>Layout</b> tab of the <a href="https://www.onlyoffice.com/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Document Editor</b></a> allows changing the appearance of a document: setting up page parameters and defining the arrangement of visual elements.</p>
            <div class="onlineDocumentFeatures">
                <p>The corresponding window of the Online Document Editor:</p>
                <p><img alt="Layout tab" src="../images/interface/layouttab.png" /></p>
            </div>
            <div class="desktopDocumentFeatures">
                <p>The corresponding window of the Desktop Document Editor:</p>
                <p><img alt="Layout tab" src="../images/interface/desktop_layouttab.png" /></p>
            </div>
            <p>Using this tab, you can:</p>
            <ul>
                <li>adjust page <a href="../UsageInstructions/SetPageParameters.htm#margins" onclick="onhyperlinkclick(this)">margins</a>, <a href="../UsageInstructions/SetPageParameters.htm#orientation" onclick="onhyperlinkclick(this)">orientation</a> and <a href="../UsageInstructions/SetPageParameters.htm#size" onclick="onhyperlinkclick(this)">size</a>,</li>
                <li>add <a href="../UsageInstructions/SetPageParameters.htm#columns" onclick="onhyperlinkclick(this)">columns</a>,</li>
                <li>insert <a href="../UsageInstructions/PageBreaks.htm" onclick="onhyperlinkclick(this)">page breaks</a>, <a href="../UsageInstructions/SectionBreaks.htm" onclick="onhyperlinkclick(this)">section breaks</a> and <a href="../UsageInstructions/SetPageParameters.htm#columns" onclick="onhyperlinkclick(this)">column breaks</a>,</li>
                <li>insert <a href="../UsageInstructions/InsertLineNumbers.htm" onclick="onhyperlinkclick(this)">line numbers</a></li>
                <li>align and arrange objects (<a href="../UsageInstructions/InsertTables.htm" onclick="onhyperlinkclick(this)">tables</a>, <a href="../UsageInstructions/InsertImages.htm" onclick="onhyperlinkclick(this)">pictures</a>, <a href="../UsageInstructions/InsertCharts.htm" onclick="onhyperlinkclick(this)">charts</a>, <a href="../UsageInstructions/InsertAutoshapes.htm" onclick="onhyperlinkclick(this)">shapes</a>),</li>
                <li>change the <a href="../UsageInstructions/ChangeWrappingStyle.htm" onclick="onhyperlinkclick(this)">wrapping style</a> and edit wrap boundary,</li>
                <li>add a <a href="../UsageInstructions/AddWatermark.htm" onclick="onhyperlinkclick(this)">watermark</a>.</li>
            </ul>
		</div>
	</body>
</html>