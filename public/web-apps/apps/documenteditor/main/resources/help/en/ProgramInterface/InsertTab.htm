﻿<!DOCTYPE html>
<html>
	<head>
		<title>Insert tab</title>
		<meta charset="utf-8" />
        <meta name="description" content="Introducing the Document Editor user interface - Insert tab" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Insert tab</h1>
            <p>The <b>Insert</b> tab of the <a href="https://www.onlyoffice.com/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Document Editor</b></a> allows adding some page formatting elements as well as visual objects and comments.</p>
            <div class="onlineDocumentFeatures">
                <p>The corresponding window of the Online Document Editor:</p>
                <p><img alt="Insert tab" src="../images/interface/inserttab.png" /></p>
            </div>
            <div class="desktopDocumentFeatures">
                <p>The corresponding window of the Desktop Document Editor:</p>
                <p><img alt="Insert tab" src="../images/interface/desktop_inserttab.png" /></p>
            </div>
            <p>Using this tab, you can:</p>
            <ul>
                <li>insert a <a href="../UsageInstructions/PageBreaks.htm" onclick="onhyperlinkclick(this)">blank page</a>,</li>
                <li>insert <a href="../UsageInstructions/PageBreaks.htm" onclick="onhyperlinkclick(this)">page breaks</a>, <a href="../UsageInstructions/SectionBreaks.htm" onclick="onhyperlinkclick(this)">section breaks</a> and <a href="../UsageInstructions/SetPageParameters.htm#columns" onclick="onhyperlinkclick(this)">column breaks</a>,</li>
                <li>insert <a href="../UsageInstructions/InsertTables.htm" onclick="onhyperlinkclick(this)">tables</a>, <a href="../UsageInstructions/InsertImages.htm" onclick="onhyperlinkclick(this)">images</a>, <a href="../UsageInstructions/InsertCharts.htm" onclick="onhyperlinkclick(this)">charts</a>, <a href="../UsageInstructions/InsertAutoshapes.htm" onclick="onhyperlinkclick(this)">shapes</a>,</li>
                <li>insert <a href="../UsageInstructions/AddHyperlinks.htm" onclick="onhyperlinkclick(this)">hyperlinks</a>, <a href="../HelpfulHints/CollaborativeEditing.htm#comments" onclick="onhyperlinkclick(this)">comments</a>,</li>
                <li>insert <a href="../UsageInstructions/InsertHeadersFooters.htm" onclick="onhyperlinkclick(this)">headers and footers</a>, <a href="../UsageInstructions/InsertPageNumbers.htm" onclick="onhyperlinkclick(this)">page numbers</a>, <a href="../UsageInstructions/InsertDateTime.htm" onclick="onhyperlinkclick(this)">date &amp time</a>,</li>
                <li>insert <a href="../UsageInstructions/InsertTextObjects.htm" onclick="onhyperlinkclick(this)">text boxes and Text Art objects</a>, <a href="../UsageInstructions/InsertEquation.htm" onclick="onhyperlinkclick(this)">equations</a>, <a href="../UsageInstructions/InsertSymbols.htm" onclick="onhyperlinkclick(this)">symbols</a>, <a href="../UsageInstructions/InsertDropCap.htm" onclick="onhyperlinkclick(this)">drop caps</a>, <a href="../UsageInstructions/InsertContentControls.htm" onclick="onhyperlinkclick(this)">content controls</a>,</li>
                <li>insert <a href="../UsageInstructions/InsertSmartArt.htm" onclick="onhyperlinkclick(this)">SmartArt objects</a>.</li>
            </ul>
		</div>
	</body>
</html>