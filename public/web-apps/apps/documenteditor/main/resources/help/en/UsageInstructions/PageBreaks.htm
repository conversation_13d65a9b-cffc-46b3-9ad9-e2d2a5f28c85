﻿<!DOCTYPE html>
<html>
	<head>
		<title>Insert page breaks</title>
		<meta charset="utf-8" />
		<meta name="description" content="Insert page breaks and keep lines together" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Insert page breaks</h1>
			<p>In the <a href="https://www.onlyoffice.com/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Document Editor</b></a>, you can add a page break to start a new page, insert a blank page and adjust pagination options.</p>
			<p>To insert a page break at the current cursor position click the <span class = "icon icon-pagebreak1"></span> <b>Breaks</b> icon on the <b>Insert</b> or <b>Layout</b> tab of the top toolbar or click the arrow next to this icon and select the <b>Insert Page Break</b> option from the menu. You can also use the <b>Ctrl+Enter</b> key combination.</p>
			<p>To insert a blank page at the current cursor position click the <span class = "icon icon-blankpage"></span> <b>Blank Page</b> icon on the <b>Insert</b> tab of the top toolbar. This action inserts two page breaks that create a blank page.</p>
            <p>To insert a page break before the selected paragraph i.e. to start this paragraph at the top of a new page:</p>
			<ul>
				<li>click the right mouse button and select the <b>Page break before</b> option in the menu, or</li>
				<li>click the right mouse button, select the <b>Paragraph Advanced Settings</b> option in the menu or use the <b>Show advanced settings</b> link on the right sidebar, and check the <b>Page break before</b> box at the <b>Line &amp; Page Breaks</b> tab of the opened <b>Paragraph - Advanced Settings</b> window.
				</li>
			</ul>
			<p>To keep lines together so that only whole paragraphs will be moved to the new page (i.e. there will be no page break between the lines within a single paragraph),</p>
			<ul>
				<li>click the right mouse button and select the <b>Keep lines together</b> option in the menu, or</li>
				<li>click the right mouse button, select the <b>Paragraph Advanced Settings</b> option on the menu or use the <b>Show advanced settings</b> link at the right sidebar, and check the <b>Keep lines together</b> box at the <b>Line &amp; Page Breaks</b> in the opened <b>Paragraph - Advanced Settings</b> window.</li>
			</ul>
			<p>The <b>Line &amp; Page Breaks</b> tab of the <b>Paragraph - Advanced Settings</b> window allows you to set two more pagination options:</p>
			<ul>
			    <li><b>Keep with next</b> - is used to prevent a page break between the selected paragraph and the next one.</li>
			    <li><b>Orphan control</b> - is selected by default and used to prevent a single line of the paragraph (the first or last) from appearing at the top or bottom of the page.</li>
			</ul>
			<p><img alt="Paragraph Advanced Settings - Line &amp; Page Breaks" src="../images/paradvsettings_breaks.png" /></p>
		</div>
	</body>
</html>