﻿<!DOCTYPE html>
<html>
	<head>
		<title>Insert images</title>
		<meta charset="utf-8" />
		<meta name="description" content="Add an image to your document and adjust its position and properties" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Insert images</h1>
            <p>In the <a href="https://www.onlyoffice.com/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Document Editor</b></a>, you can insert images in the most popular formats into your document. The following image formats are supported: <b>BMP</b>, <b>GIF</b>, <b>JPEG</b>, <b>JPG</b>, <b>PNG</b>.</p>
            <h3>Insert an image</h3>
            <p>To insert an image into the document text,</p>
			<ol>
				<li>place the cursor where you want the image to be put,</li>
                <li>switch to the <b>Insert</b> tab of the top toolbar,</li>
				<li>click the <div class = "icon icon-image"></div> <b>Image</b> icon on the top toolbar,</li>
				<li>select one of the following options to load the image:
					<ul>
                        <li>the <b>Image from File</b> option will open a standard dialog window for to select a file. Browse your computer hard disk drive for the necessary file and click the <b>Open</b> button
                            <p class="note">In the <em>online editor</em>, you can select several images at once.</p>
                        </li>
                        <li>the <b>Image from URL</b> option will open the window where you can enter the web address of the requiredimage, and click the <b>OK</b> button</li>
                        <li class="onlineDocumentFeatures"> the <b>Image from Storage</b> option will open the <b>Select data source</b> window. Select an image stored on your portal and click the <b>OK</b> button</li>
					</ul>
				</li>
                <li>once the image is added, you can change its size, properties, and position.</li>
			</ol>
            <p>It's also possible to add a caption to the image. To learn more on how to work with captions for images, you can refer to <a href="../UsageInstructions/AddCaption.htm" onclick="onhyperlinkclick(this)">this article</a>.</p>
            <h3>Move and resize images</h3>
            <p><span class="big big-moving_image"></span></p>
			<p>To change the image size, drag small squares <span class = "icon icon-resize_square"></span> situated on its edges. To maintain the original proportions of the selected image while resizing, hold down the <b>Shift</b> key and drag one of the corner icons.</p>
			<p>To alter the image position, use the <span class = "icon icon-arrow"></span> icon that appears after hovering your mouse cursor over the image. Drag the image to the necessary position without releasing the mouse button.</p>
            <p>When you move the image, the guide lines are displayed to help you precisely position the object on the page (if the selected wrapping style is different from the inline).</p>
			<p>To rotate the image, hover the mouse cursor over the rotation handle <span class = "icon icon-greencircle"></span> and drag it clockwise or counterclockwise. To constrain the rotation angle to 15 degree increments, hold down the <b>Shift</b> key while rotating.</p>
            <p class="note">
                <b>Note</b>: the list of keyboard shortcuts that can be used when working with objects is available <a href="../HelpfulHints/KeyboardShortcuts.htm#workwithobjects" onclick="onhyperlinkclick(this)">here</a>.
            </p>
            <hr />
            <h3>Adjust image settings</h3>
			<p><img alt="Image Settings tab" src="../images/right_image.png" /></p>
            <p>Some of the image settings can be altered using the <b>Image settings</b> tab of the right sidebar. To activate it click the image and choose the <b>Image settings</b> <span class = "icon icon-image_settings_icon"></span> icon on the right. Here you can change the following properties:</p>
			<ul>
                <li><b>Size</b> is used to view the <b>Width</b> and <b>Height</b> of the current image. If necessary, you can restore the actual image size clicking the <b>Actual Size</b> button. The <b>Fit to Margin</b> button allows you to resize the image, so that it occupies all the space between the left and right page margin.
                    <p>The <b>Crop</b> button is used to crop the image. Click the <b>Crop</b> button to activate cropping handles which appear on the image corners and in the center of each its side. Manually drag the handles to set the cropping area. You can move the mouse cursor over the cropping area border so that it turns into the <span class = "icon icon-arrow"></span> icon and drag the area. </p>
                    <ul>
                        <li>To crop a single side, drag the handle located in the center of this side.</li>
                        <li>To simultaneously crop two adjacent sides, drag one of the corner handles.</li>
                        <li>To equally crop the two opposite sides of the image, hold down the <em>Ctrl</em> key when dragging the handle in the center of one of these sides. </li>
                        <li>To equally crop all sides of the image, hold down the <em>Ctrl</em> key when dragging any of the corner handles.</li>
                    </ul>
                    <p>When the cropping area is specified, click the <b>Crop</b> button once again, or press the <em>Esc</em> key, or click anywhere outside of the cropping area to apply the changes.</p>
                    <p>After the cropping area is selected, it's also possible to use the <b>Crop to shape</b>, <b>Fill</b> and <b>Fit</b> options available from the <b>Crop</b> drop-down menu. Click the <b>Crop</b> button once again and select the option you need: </p>
                    <ul>
                        <li>If you select the <b>Crop to shape</b> option, the picture will fill a certain shape. You can select a shape from the gallery, which opens when you hover your mouse pointer over the <b>Crop to Shape</b> option. You can still use the <b>Fill</b> and <b>Fit</b> options to choose the way your picture matches the shape.</li>
                        <li>If you select the <b>Fill</b> option, the central part of the original image will be preserved and used to fill the selected cropping area, while the other parts of the image will be removed.</li>
                        <li>If you select the <b>Fit</b> option, the image will be resized so that it fits the height and the width of the cropping area. No parts of the original image will be removed, but empty spaces may appear within the selected cropping area.</li>
                    </ul>
                </li>				
                <li><b>Rotation</b> is used to rotate the image by 90 degrees clockwise or counterclockwise as well as to flip the image horizontally or vertically. Click one of the buttons:
                    <ul>
                        <li><div class = "icon icon-rotatecounterclockwise"></div> to rotate the image by 90 degrees counterclockwise</li>
                        <li><div class = "icon icon-rotateclockwise"></div> to rotate the image by 90 degrees clockwise</li>
                        <li><div class = "icon icon-fliplefttoright"></div> to flip the image horizontally (left to right)</li>
                        <li><div class = "icon icon-flipupsidedown"></div> to flip the image vertically (upside down)</li>
                    </ul>
                </li>
                <li><b>Wrapping Style</b> is used to select a text wrapping style from the available ones - inline, square, tight, through, top and bottom, in front, behind (for more information see the advanced settings description below).</li>
                <li><b>Replace Image</b> is used to replace the current image by loading another one <b>From File</b>, <b>From Storage</b>, or <b>From URL</b>.</li>
			</ul>
            <p>You can also find some of these options in the <b>right-click menu</b>. The menu options are:</p>
			<ul>
                <li><b>Cut, Copy, Paste</b> - standard options which are used to cut or copy the selected text/object and paste the previously cut/copied text passage or object to the current cursor position.</li>
                <li><b>Arrange</b> is used to bring the selected image to foreground, send it to background, move forward or backward as well as group or ungroup images to perform operations with several of them at once. To learn more on how to arrange objects, please refer to <a href="../UsageInstructions/AlignArrangeObjects.htm" onclick="onhyperlinkclick(this)">this page</a>.</li>
                <li><b>Align</b> is used to align the image to the left, in the center, to the right, at the top, in the middle or at the bottom. To learn more on how to align objects, please refer to <a href="../UsageInstructions/AlignArrangeObjects.htm" onclick="onhyperlinkclick(this)">this page</a>.</li>
                <li><b>Wrapping Style</b> is used to select a text wrapping style from the available ones - inline, square, tight, through, top and bottom, in front, behind - or edit the wrap boundary. The <b>Edit Wrap Boundary</b> option is available only if the selected wrapping style is not inline. Drag wrap points to customize the boundary. To create a new wrap point, click anywhere on the red line and drag it to the necessary position. <div class = "big big-wrap_boundary"></div></li>
				<li><b>Rotate</b> is used to rotate the image by 90 degrees clockwise or counterclockwise as well as to flip the image horizontally or vertically.</li>
                <li><b>Crop</b> is used to apply one of the cropping options: <b>Crop</b>, <b>Fill</b> or <b>Fit</b>. Select the <b>Crop</b> option from the submenu, then drag the cropping handles to set the cropping area, and click one of these three options from the submenu once again to apply the changes.</li>
                <li><b>Actual Size</b> is used to change the current image size to the actual one.</li>
                <li><b>Replace image</b> is used to replace the current image by loading another one <b>From File</b> or <b>From URL</b>.</li>
				<li><b>Image Advanced Settings</b> is used to open the 'Image - Advanced Settings' window.</li>
			</ul>
            <p><img alt="Shape Settings tab" src="../images/right_image_shape.png" /></p> 
            <p>When the image is selected, the <b>Shape settings</b> <span class = "icon icon-shape_settings_icon"></span> icon is also available on the right. You can click this icon to open the <b>Shape settings</b> tab on the right sidebar and adjust the shape <a href="../UsageInstructions/InsertAutoshapes.htm#shape_stroke" onclick="onhyperlinkclick(this)"><b>Line</b></a> type, size and color as well as change the shape type selecting another shape from the <b>Change Autoshape</b> menu. The shape of the image will change correspondingly.</p>
            <p>On the <b>Shape Settings</b> tab, you can also use the <b>Show shadow</b> option to add a shadow to the image.</p>
			<hr />
            <h3>Adjust image advanced settings</h3>
			<p>To change the image advanced settings, click the image with the right mouse button and select the <b>Image Advanced Settings</b> option from the right-click menu or just click the <b>Show advanced settings</b> link on the right sidebar. The image properties window will open:</p>
			<p><img alt="Image - Advanced Settings: Size" src="../images/image_properties.png" /></p>
			<p>The <b>Size</b> tab contains the following parameters:</p>
			<ul>
				<li><b>Width</b> and <b>Height</b> - use these options to change the width and/or height. If the <b>Constant proportions</b> <div class = "icon icon-constantproportions"></div> button is clicked (in this case it looks like this <div class = "icon icon-constantproportionsactivated"></div>), the width and height will be changed together preserving the original image aspect ratio. To restore the actual size of the added image, click the <b>Actual Size</b> button.</li>
			</ul>
            <p><img alt="Image - Advanced Settings: Rotation" src="../images/image_properties_4.png" /></p>
            <p>The <b>Rotation</b> tab contains the following parameters:</p>
            <ul>
                <li><b>Angle</b> - use this option to rotate the image by an exactly specified angle. Enter the necessary value measured in degrees into the field or adjust it using the arrows on the right. </li>
                <li><b>Flipped</b> - check the <b>Horizontally</b> box to flip the image horizontally (left to right) or check the <b>Vertically</b> box to flip the image vertically (upside down).</li>
            </ul>
			<p><img alt="Image - Advanced Settings: Text Wrapping" src="../images/image_properties_1.png" /></p>
			<p>The <b>Text Wrapping</b> tab contains the following parameters:</p>
			<ul>
				<li><b>Wrapping Style</b> - use this option to change the way the image is positioned relative to the text: it will either be a part of the text (in case you select the inline style) or bypassed by it from all sides (if you select one of the other styles).
				<ul>
				    <li><p><div class = "icon icon-wrappingstyle_inline"></div> <b>Inline</b> - the image is considered to be a part of the text, like a character, so when the text moves, the image moves as well. In this case the positioning options are inaccessible.</p>
				    <p>If one of the following styles is selected, the image can be moved independently of the text and positioned on the page exactly:</p>
				    </li>
				    <li><p><div class = "icon icon-wrappingstyle_square"></div> <b>Square</b> - the text wraps the rectangular box that bounds the image.</p></li>
				    <li><p><div class = "icon icon-wrappingstyle_tight"></div> <b>Tight</b> - the text wraps the actual image edges.</p></li>
				    <li><p><div class = "icon icon-wrappingstyle_through"></div> <b>Through</b> - the text wraps around the image edges and fills in the open white space within the image. So that the effect can appear, use the <b>Edit Wrap Boundary</b> option from the right-click menu.</p></li>
				    <li><p><div class = "icon icon-wrappingstyle_topandbottom"></div> <b>Top and bottom</b> - the text is only above and below the image.</p></li>
				    <li><p><div class = "icon icon-wrappingstyle_infront"></div> <b>In front</b> - the image overlaps the text.</p></li>
				    <li><p><div class = "icon icon-wrappingstyle_behind"></div> <b>Behind</b> - the text overlaps the image.</p></li>
				    </ul>
				</li>
			</ul>
			<p>If you select the square, tight, through, or top and bottom style, you will be able to set up some additional parameters - <b>distance from text</b> at all sides (top, bottom, left, right).</p>
            <p id="position"><img alt="Image - Advanced Settings: Position" src="../images/image_properties_2.png" /></p>
            <p>The <b>Position</b> tab is available only if you select a wrapping style other than inline. This tab contains the following parameters that vary depending on the selected wrapping style:</p>
			<ul>
                <li>
                    The <b>Horizontal</b> section allows you to select one of the following three image positioning types:
                    <ul>
                        <li><b>Alignment</b> (left, center, right) <b>relative to</b> character, column, left margin, margin, page or right margin,</li>
                        <li>Absolute <b>Position</b> measured in absolute units i.e. <b>Centimeters</b>/<b>Points</b>/<b>Inches</b> (depending on the option specified on the <b>File</b> -> <b>Advanced Settings...</b> tab) <b>to the right of</b> character, column, left margin, margin, page or right margin,</li>
                        <li><b>Relative position</b> measured in percent <b>relative to</b> the left margin, margin, page or right margin.</li>
                    </ul>
                </li>
                <li>
                    The <b>Vertical</b> section allows you to select one of the following three image positioning types:
                    <ul>
                        <li><b>Alignment</b> (top, center, bottom) <b>relative to</b> line, margin, bottom margin, paragraph, page or top margin,</li>
                        <li>Absolute <b>Position</b> measured in absolute units i.e. <b>Centimeters</b>/<b>Points</b>/<b>Inches</b> (depending on the option specified on the <b>File</b> -> <b>Advanced Settings...</b> tab) <b>below</b> line, margin, bottom margin, paragraph, page or top margin,</li>
                        <li><b>Relative position</b> measured in percent <b>relative to</b> the margin, bottom margin, page or top margin.</li>
                    </ul>
                </li>
                <li><b>Move object with text</b> ensures that the image moves along with the text to which it is anchored.</li>
                <li><b>Allow overlap</b> makes is possible for two images to overlap if you drag them near each other on the page.</li>
			</ul>
            <p><img alt="Image - Advanced Settings" src="../images/image_properties_3.png" /></p>
            <p>The <b>Alternative Text</b> tab allows specifying a <b>Title</b> and <b>Description</b> which will be read to people with vision or cognitive impairments to help them better understand what information the image contains.</p>
		</div>
	</body>
</html>