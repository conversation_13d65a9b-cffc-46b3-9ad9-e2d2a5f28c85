﻿<!DOCTYPE html>
<html>
<head>
    <title>Convert footnotes and endnotes</title>
    <meta charset="utf-8" />
    <meta name="description" content="Insert footnotes to provide explanations for some terms or make references to the sources" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Convert footnotes and endnotes</h1>
        <p>The ONLYOFFICE <a href="https://www.onlyoffice.com/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Document Editor</b></a> allows you to quickly <b>convert footnotes</b> to <b>endnotes</b>, and vice versa, e.g., if you see that some footnotes in the resulting document should be placed in the end. Instead of recreating them as endnotes, use the corresponding tool for effortless conversion.</p>
        <ol>
            <li>Click the arrow next to the <div class = "icon icon-addfootnote"></div> <b>Footnote</b> icon on the <b>References</b> tab located at the top toolbar,</li>
            <li>Hover over the <b>Convert all notes</b> menu item and choose one of the options from the list to the right:
                <p><img alt="Convert footnotes_endnotes" src="../images/convert_footnotes_endnotes.png" /></p></li>
                <li>
                    <ul>
                        <li><b>Convert all Footnotes to Endnotes</b> to change all footnotes into endnotes;</li>
                        <li><b>Convert all Endnotes to Footnotes</b> to change all endnotes to footnotes;</li>
                        <li><b>Swap Footnotes and Endnotes</b> to change all endnotes to footnotes, and all footnotes to endnotes.</li>
                    </ul>
                </li>
        </ol>
        </div>
</body>
</html>