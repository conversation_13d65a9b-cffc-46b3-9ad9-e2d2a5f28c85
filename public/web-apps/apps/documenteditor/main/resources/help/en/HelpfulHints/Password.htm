﻿<!DOCTYPE html>
<html>
	<head>
		<title>Protecting documents with a password</title>
		<meta charset="utf-8" />
		<meta name="description" content="Enabling password protection for files" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
			<div class="search-field">
				<input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
			</div>

			<h1>Protecting documents with a password</h1>
			<p>You can protect your documents with a password that is required to enter the editing mode by your co-authors. The password can be changed or removed later on.</p>
			<p class="note">The password cannot be restored if you lose or forget it. Please keep it in a safe place.</p>
			<p>You can also protect your documents by restricting access rights.</p>

			<h2>Setting a password</h2>
			<ul>
				<li>
					go to the <b>File</b> tab at the top toolbar and choose the <b>Protect</b> option,
					<p>or go to the <b>Protection</b> tab and choose the <b>Encrypt</b> option,</p>
				</li>
				<li>
					set a password in the <b>Password</b> field and repeat it in the <b>Repeat password</b> field below. Click <div class="icon icon-show_password"></div> to show or hide password characters when entered.
					<p><img alt="set password" src="../../../../../../common/main/resources/help/en/images/setpassword.png" /></p>
				</li>
				<li>
					Click <b>OK</b> when ready.
					<p class="note">To open the document with the full access rights, the user has to enter the set password.</p>
				</li>
			</ul>

			<h2>Changing a password</h2>
			<ul>
				<li>
					go to the <b>File</b> tab at the top toolbar and choose the <b>Protect</b> option,
					<p>or go to the <b>Protection</b> tab and choose the <b>Encrypt</b> option,</p>
				</li>
				<li>set a new password in the <b>Password</b> field and repeat it in the <b>Repeat password</b> field below. Click <div class="icon icon-show_password"></div> to show or hide password characters when entered.</li>
				<li>click <b>OK</b>.</li>
			</ul>
			<p><img alt="changing password" src="../../../../../../common/main/resources/help/en/images/setpassword.png" /></p>

			<h2>Deleting a password</h2>
			<ul>
				<li>go to the <b>File</b> tab at the top toolbar,</li>
				<li>choose the <b>Protect</b> option,</li>
				<li>click the <b>Delete password</b> button.</li>
			</ul>

			<h2>Protecting a document</h2>
			<ul>
				<li>go to the <b>Protection</b> tab,</li>
				<li>click the <b>Protect Document</b> button,</li>
				<li>set the password if necessary,</li>
				<li>
					choose the required access rights for the document provided the password has not been entered by the user:
					<ul>
						<li><em>No changes (Read only)</em> - the user can only view the document.</li>
						<li><em>Filling forms</em> - the user can only fill a form.</li>
						<li><em>Tracked changes</em> - the user can only view the document changes history and review the document itself.</li>
						<li><em>Comments</em> - the user can only leave and answer comments.</li>
					</ul>
					<p><img alt="setting password" src="../images/protectdocument.png" /></p>
				</li>
				<li>click <b>Protect</b> when ready.</li>
				</ul>
			<p>To <b>remove protection</b> from the document provided that the password has been set,</p>
			<ul>
				<li>go to the <b>Protection</b> tab,</li>
				<li>click the <b>Protect Document</b> button,</li>
				<li>enter the set password in the <b>Unprotect Document</b> window.</li>
			</ul>
		</div>
	</body>
</html>