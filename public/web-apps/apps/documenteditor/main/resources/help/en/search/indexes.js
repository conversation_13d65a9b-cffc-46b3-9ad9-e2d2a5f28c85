var indexes = 
[
   {
        "id": "HelpfulHints/About.htm", 
        "title": "About Document Editor", 
        "body": "About the Document Editor The Document Editor is an online application that allows you to view through and edit documents directly in your browser . Using the Document Editor, you can perform various editing operations like in any desktop editor, print the edited documents keeping all the formatting details or download them onto your computer hard disk drive as DOCX, PDF, ODT, TXT, DOTX, PDF/A, OTT, RTF, HTML, FB2, EPUB, DOCXF and OFORM files. To view the current software version, build number, and licensor details in the online version, click the icon on the left sidebar. To view the current software version and licensor details in the desktop version for Windows, select the About menu item on the left sidebar of the main program window. In the desktop version for Mac OS, open the ONLYOFFICE menu at the top of the screen and select the About ONLYOFFICE menu item."
    },
   {
        "id": "HelpfulHints/AdvancedSettings.htm", 
        "title": "Advanced Settings of the Document Editor", 
        "body": "The Document Editor allows you to change its advanced settings. To access them, open the File tab on the top toolbar and select the Advanced Settings option. The advanced settings are grouped as follows: Editing and saving Autosave is used in the online version to turn on/off automatic saving of changes you make while editing. Autorecover is used in the desktop version to turn on/off the option that allows automatically recovering documents in case the program closes unexpectedly. Show the Paste Options button when the content is pasted. The corresponding icon will appear when you paste content in the document. Make the files compatible with older MS Word versions when saved as DOCX. The files saved in DOCX format will become compatible with older Microsoft Word versions. Collaboration The Co-editing mode subsection allows you to set the preferable mode for seeing changes made to the document when working in collaboration. Fast (by default). The users who take part in the document co-editing will see the changes in real time once they are made by other users. Strict. All the changes made by co-editors will be shown only after you click the Save icon that will notify you about new changes. The Show track changes subsection allows you to choose how new changes will be displayed. Show by click in balloons. The change is shown in a balloon when you click the tracked change. Show by hover in tooltips. A tooltip appears when you hover the mouse pointer over the tracked change. The Real-time Collaboration Changes subsection allows you to choose how new changes and comments will be displayed in real time. View None. All the changes made during the current session will not be highlighted. View All. All the changes made during the current session will be highlighted. View Last. Only the changes made since you last time clicked the Save icon will be highlighted. This option is only available when the Strict co-editing mode is selected. Show changes from other users. This feature allows to see changes made by other users in the document opened for viewing only in the Live Viewer mode. Show comments in text. If you disable this feature, the commented passages will be highlighted only if you click the Comments icon on the left sidebar. Show resolved comments. This feature is disabled by default so that the resolved comments are hidden in the document text. You can view such comments only if you click the Comments icon on the left sidebar. Enable this option if you want to display resolved comments in the document text. Proofing The Spell Checking option is used to turn on/off the spell checking. Ignore words in UPPERCASE. Words typed in capital letters are ignored during the spell checking. Ignore words with numbers. Words with numbers in them are ignored during the spell checking. The AutoCorrect options... menu allows you to access the autocorrect settings such as replacing text as you type, recognizing functions, automatic formatting etc. Workspace The Alignment Guides option is used to turn on/off alignment guides that appear when you move objects. It allows for a more precise object positioning on the page. The Hieroglyphs option is used to turn on/off the display of hieroglyphs. The Use Alt key to navigate the user interface using the keyboard option is used to enable using the Alt / Option key in keyboard shortcuts. The Interface theme option is used to change the color scheme of the editor’s interface. The Same as system option makes the editor follow the interface theme of your system. The Light color scheme incorporates standard blue, white, and light gray colors with less contrast in UI elements suitable for working during daytime. The Classic Light color scheme incorporates standard blue, white, and light gray colors. The Dark color scheme incorporates black, dark gray, and light gray colors suitable for working during nighttime. The Contrast Dark color scheme incorporates black, dark gray, and white colors with more contrast in UI elements highlighting the working area of the file. The Turn on document dark mode option is used to make the working area darker when the editor is set to Dark or Contrast Dark interface theme. Check the Turn on document dark mode box to enable it. Note: Apart from the available Light, Classic Light, Dark, and Contrast Dark interface themes, ONLYOFFICE editors can now be customized with your own color theme. Please follow these instructions to learn how you can do that. The Unit of Measurement option is used to specify what units are used on the rulers and in properties of objects when setting such parameters as width, height, spacing, margins etc. The available units are Centimeter, Point, and Inch. The Default Zoom Value option is used to set the default zoom value, selecting it in the list of available options from 50% to 500%. You can also choose the Fit to Page or Fit to Width option. The Font Hinting option is used to select how fonts are displayed in the Document Editor. Choose As Windows if you like the way fonts are usually displayed on Windows, i.e. using Windows font hinting. Choose As OS X if you like the way fonts are usually displayed on a Mac, i.e. without any font hinting at all. Choose Native if you want your text to be displayed with the hinting embedded into font files. Default cache mode - used to select the cache mode for the font characters. It’s not recommended to switch it without any reason. It can be helpful in some cases only, for example, when an issue in the Google Chrome browser with the enabled hardware acceleration occurs. The Document Editor has two cache modes: In the first cache mode, each letter is cached as a separate picture. In the second cache mode, a picture of a certain size is selected where letters are placed dynamically and a mechanism of allocating/removing memory in this picture is also implemented. If there is not enough memory, a second picture is created, etc. The Default cache mode setting applies two above mentioned cache modes separately for different browsers: When the Default cache mode setting is enabled, Internet Explorer (v. 9, 10, 11) uses the second cache mode, other browsers use the first cache mode. When the Default cache mode setting is disabled, Internet Explorer (v. 9, 10, 11) uses the first cache mode, other browsers use the second cache mode. The Macros Settings option is used to set macros display with a notification. Choose Disable All to disable all macros within the document. Choose Show Notification to receive notifications about macros within the document. Choose Enable All to automatically run all macros within the document. To save the changes you made, click the Apply button."
    },
   {
        "id": "HelpfulHints/CollaborativeEditing.htm", 
        "title": "Co-editing documents in real time", 
        "body": "The Document Editor allows you to maintain constant team-wide approach to work flow: share files and folders, communicate right in the editor, comment certain parts of your documents that require additional third-party input, save document versions for future use, review documents and add your changes without actually editing the file, compare and merge documents to facilitate processing and editing. In Document Editor you can collaborate on documents in real time using two modes: Fast or Strict. The modes can be selected in the Advanced Settings. It's also possible to choose the required mode using the Co-editing Mode icon on the Collaboration tab of the top toolbar: The number of users who are working on the current document is displayed on the right side of the editor header - . If you want to see who exactly is editing the file now, you can click this icon or open the Chat panel with the full list of the users. Fast mode The Fast mode is used by default and shows the changes made by other users in real time. When you co-edit a document in this mode, the possibility to Redo the last undone operation is not available. This mode will show the actions and the names of the co-editors when they are editing the text. By hovering the mouse cursor over one of the edited passages, the name of the user who is editing it at the moment is displayed. Strict mode The Strict mode is selected to hide changes made by other users until you click the Save   icon to save your changes and accept the changes made by co-authors. When a document is being edited by several users simultaneously in this mode, the edited text passages are marked with dashed lines of different colors. As soon as one of the users saves their changes by clicking the icon, the others will see a note within the status bar stating that they have updates. To save the changes you made, so that other users can view them, and get the updates saved by your co-editors, click the icon in the left upper corner of the top toolbar. The updates will be highlighted for you to check what exactly has been changed. You can specify what changes you want to be highlighted during co-editing if you click the File tab on the top toolbar, select the Advanced Settings option and choose one of the three options: View all - all the changes made during the current session will be highlighted. View last - only the changes made since you last time clicked the  icon will be highlighted. View None - changes made during the current session will not be highlighted. Live Viewer mode The Live Viewer mode is used to see the changes made by other users in real time when the document is opened by a user with the View only access rights. For the mode to function properly, make sure that the Show changes from other users checkbox is active in the editor's Advanced Settings. Anonymous Portal users who are not registered and do not have a profile are considered to be anonymous, although they still can collaborate on documents. To have a name assigned to them, the anonymous user should enter a name they prefer in the corresponding field appearing in the right top corner of the screen when they open the document for the first time. Activate the “Don’t ask me again” checkbox to preserve the name."
    },
   {
        "id": "HelpfulHints/Commenting.htm", 
        "title": "Commenting documents", 
        "body": "The Document Editor allows you to maintain constant team-wide approach to work flow: share files and folders, collaborate on documents in real time, communicate right in the editor, save document versions for future use, review documents and add your changes without actually editing the file, compare and merge documents to facilitate processing and editing. In Document Editor you can leave comments to the content of documents without actually editing it. Unlike chat messages, the comments stay until deleted. Leaving comments and replying to them To leave a comment, select a text passage where you think there is an error or problem, switch to the Insert or Collaboration tab of the top toolbar and click the Comment button, or use the icon on the left sidebar to open the Comments panel and click the Add Comment to Document link, or right-click the selected text passage and select the Add Сomment option from the contextual menu, enter the required text, click the Add Comment/Add button. The comment will be seen on the Comments panel on the left. Any other user can answer the added comment asking questions or reporting on the work they have done. For this purpose, click the Add Reply link situated under the comment, type in your reply in the entry field and press the Reply button. If you are using the Strict co-editing mode, new comments added by other users will become visible only after you click the icon in the left upper corner of the top toolbar. Disabling display of comments The text passage you commented will be highlighted in the document. To view the comment, just click within the passage. To disable this feature, click the File tab at the top toolbar, select the Advanced Settings... option, uncheck the Turn on display of the comments box. Now the commented passages will be highlighted only if you click the   icon. Managing comments You can manage the added comments using the icons in the comment balloon or on the Comments panel on the left: sort the added comments by clicking the icon: by date: Newest or Oldest. This is the sort order by default. by author: Author from A to Z or Author from Z to A. by location: From top or From bottom. The usual sort order of comments by their location in a document is as follows (from top): comments to text, comments to footnotes, comments to endnotes, comments to headers/footers, comments to the entire document. by group: All or choose a certain group from the list. This sorting option is available if you are running a version that includes this functionality. edit the currently selected comment by clicking the icon, delete the currently selected comment by clicking the icon, close the currently selected discussion by clicking the icon if the task or problem you stated in your comment was solved, after that the discussion you opened with your comment gets the resolved status. To open it again, click the icon. If you want to hide resolved comments, click the File tab on the top toolbar, select the Advanced Settings... option, uncheck the Turn on display of the resolved comments box and click Apply. In this case the resolved comments will be highlighted only if you click the icon, if you want to manage comments in a bunch, open the Resolve drop-down menu on the Collaboration tab. Select one of the options for resolving comments: resolve current comments, resolve my comments or resolve all comments in the document. Adding mentions You can only add mentions to the comments made to the text parts and not to the document itself. When entering comments, you can use the mentions feature that allows you to attract somebody's attention to the comment and send a notification to the mentioned user via email and Talk. To add a mention, Enter the \"+\" or \"@\" sign anywhere in the comment text - a list of the portal users will open. To simplify the search process, you can start typing a name in the comment field - the user list will change as you type. Select the necessary person from the list. If the file has not yet been shared with the mentioned user, the Sharing Settings window will open. Read only access type is selected by default. Change it if necessary. Click OK. The mentioned user will receive an email notification that they have been mentioned in a comment. If the file has been shared, the user will also receive a corresponding notification. Removing comments To remove comments, click the Remove button on the Collaboration tab of the top toolbar, select the necessary option from the menu: Remove Current Comments - to remove the currently selected comment. If some replies have been added to the comment, all its replies will be removed as well. Remove My Comments - to remove comments you added without removing comments added by other users. If some replies have been added to your comment, all its replies will be removed as well. Remove All Comments - to remove all the comments in the document that you and other users added. To close the panel with comments, click the icon on the left sidebar once again."
    },
   {
        "id": "HelpfulHints/Communicating.htm", 
        "title": "Communicating in real time", 
        "body": "The Document Editor allows you to maintain constant team-wide approach to work flow: share files and folders, collaborate on documents in real time, comment certain parts of your documents that require additional third-party input, save document versions for future use, review documents and add your changes without actually editing the file, compare and merge documents to facilitate processing and editing. In Document Editor you can communicate with your co-editors in real time using the built-in Chat tool as well as a number of useful plugins, i.e. Telegram or Rainbow. To access the Chat tool and leave a message for other users, click the icon on the left sidebar, or switch to the Collaboration tab of the top toolbar and click the Chat button, enter your text into the corresponding field below, press the Send button. The chat messages are stored during one session only. To discuss the document content, it is better to use comments which are stored until they are deleted. All the messages left by users will be displayed on the panel on the left. If there are new messages you haven't read yet, the chat icon will look like this - . To close the panel with chat messages, click the icon on the left sidebar or the Chat button at the top toolbar once again."
    },
   {
        "id": "HelpfulHints/Comparison.htm", 
        "title": "Comparing documents", 
        "body": "The Document Editor allows you to maintain constant team-wide approach to work flow: share files and folders, collaborate on documents in real time, communicate right in the editor, comment certain parts of your documents that require additional third-party input, save document versions for future use, review documents and add your changes without actually editing the file. If you need to compare and merge two documents, the Document Editor provides you with the document Compare feature. It allows displaying the differences between two documents and merge the documents by accepting the changes one by one or all at once. After comparing and merging two documents, the result will be stored on the portal as a new version of the original file. If you do not need to merge documents which are being compared, you can reject all the changes so that the original document remains unchanged. Choose a document for comparison To compare two documents, open the original document that you need to compare and select the second document for comparison: switch to the Collaboration tab on the top toolbar and press the Compare button, select one of the following options to load the document: the Document from File option will open the standard dialog window for file selection. Browse your computer hard disk drive for the necessary .docx file and click the Open button. the Document from URL option will open the window where you can enter a link to the file stored in a third-party web storage (for example, Nextcloud) if you have corresponding access rights to it. The link must be a direct link for downloading the file. When the link is specified, click the OK button. Note: The direct link allows downloading the file directly without opening it in a web browser. For example, to get a direct link in Nextcloud, find the necessary document in the file list, select the Details option from the file menu. Click the Copy direct link (only works for users who have access to this file/folder) icon on the right of the file name on the details panel. To find out how to get a direct link for downloading the file in a different third-party web storage, please refer to the corresponding third-party service documentation. the Document from Storage option will open the Select Data Source window. It displays the list of all the .docx documents stored on your portal you have corresponding access rights to. To navigate through the sections of the Documents module, use the menu on the left part of the window. Select the necessary .docx document and click the OK button. When the second document for comparison is selected, the comparison process will start and the document will look as if it was opened in the Review mode. All the changes are highlighted with a color, and you can view the changes, navigate between them, accept or reject them one by one or all the changes at once. It's also possible to change the display mode and see how the document looks before comparison, in the process of comparison, or how it will look after comparison if you accept all changes. Choose the changes display mode Click the Display Mode button on the top toolbar and select one of the available modes from the list: Markup - this option is selected by default. It is used to display the document in the process of comparison. This mode allows both viewing the changes and editing the document. Final - this mode is used to display the document after comparison as if all the changes were accepted. This option does not actually accept all changes, it only allows you to see how the document will look like after you accept all the changes. In this mode, you cannot edit the document. Original - this mode is used to display the document before comparison as if all the changes were rejected. This option does not actually reject all changes, it only allows you to view the document without changes. In this mode, you cannot edit the document. Accept or reject changes Use the Previous and the Next buttons on the top toolbar to navigate through the changes. To accept the currently selected change, you can: click the Accept button on the top toolbar, or click the downward arrow below the Accept button and select the Accept Current Change option (in this case, the change will be accepted and you will proceed to the next change), or click the Accept button of the change pop-up window. To quickly accept all the changes, click the downward arrow below the Accept button and select the Accept All Changes option. To reject the current change you can: click the Reject button on the top toolbar, or click the downward arrow below the Reject button and select the Reject Current Change option (in this case, the change will be rejected and you will move on to the next available change), or click the Reject button of the change pop-up window. To quickly reject all the changes, click the downward arrow below the Reject button and select the Reject All Changes option. Additional info on the comparison feature Method of comparison Documents are compared by words. If a word contains a change of at least one character (e.g. if a character was removed or replaced), in the result, the difference will be displayed as the change of the entire word, not the character. The image below illustrates the case when the original file contains the word 'Characters' and the document for comparison contains the word 'Character'. Authorship of the document When the comparison process is launched, the second document for comparison is being loaded and compared to the current one. If the loaded document contains some data which is not represented in the original document, the data will be marked as added by a reviewer. If the original document contains some data which is not represented in the loaded document, the data will be marked as deleted by a reviewer. If the authors of the original and loaded documents are the same person, the reviewer is the same user. His/her name is displayed in the change balloon. If the authors of two files are different users, then the author of the second file loaded for comparison is the author of the added/removed changes. Presence of the tracked changes in the compared document If the original document contains some changes made in the review mode, they will be accepted in the comparison process. When you choose the second file for comparison, you'll see the corresponding warning message. In this case, when you choose the Original display mode, the document will not contain any changes."
    },
   {
        "id": "HelpfulHints/KeyboardShortcuts.htm", 
        "title": "Keyboard Shortcuts", 
        "body": "Keyboard Shortcuts for Key Tips Use keyboard shortcuts for a faster and easier access to the features of the Document Editor without using a mouse. Press Alt key to turn on all key tips for the editor header, the top toolbar, the right and the left sidebars and the status bar. Press the letter that corresponds to the item you wish to use. The additional key tips may appear depending on the key you press. The first key tips hide when additional key tips appear. For example, to access the Insert tab, press Alt to see all primary key tips. Press letter I to access the Insert tab, and to see all the available shortcuts for this tab. Then press the letter that corresponds to the item you wish to configure. Press Alt to hide all key tips, or press Escape to go back to the previous group of key tips. Find the most common keyboard shortcuts in the list below: Windows/Linux Mac OS Working with Document Open 'File' panel Alt+F ^ Ctrl+⌥ Option+F Open the File panel panel to save, download, print the current document, view its info, create a new document or open an existing one, access the Document Editor Help Center or advanced settings. Open 'Find and Replace' dialog box Ctrl+F ^ Ctrl+F, &#8984; Cmd+F Open the Find and Replace dialog box to start searching for a character/word/phrase in the currently edited document. Open 'Find and Replace' dialog box with replacement field Ctrl+H ^ Ctrl+H Open the Find and Replace dialog box with the replacement field to replace one or more occurrences of the found characters. Repeat the last 'Find' action ⇧ Shift+F4 ⇧ Shift+F4, &#8984; Cmd+G, &#8984; Cmd+⇧ Shift+F4 Repeat the previous Find performed before the key combination was pressed. Open 'Comments' panel Ctrl+⇧ Shift+H ^ Ctrl+⇧ Shift+H, &#8984; Cmd+⇧ Shift+H Open the Comments panel to add your own comment or reply to other users' comments. Open comment field Alt+H &#8984; Cmd+⌥ Option+A Open a data entry field where you can add the text of your comment. Open 'Chat' panel Alt+Q ^ Ctrl+⌥ Option+Q Open the Chat panel and send a message. Save document Ctrl+S ^ Ctrl+S, &#8984; Cmd+S Save all the changes to the document currently edited with The Document Editor. The active file will be saved with its current file name, location, and file format. Print document Ctrl+P ^ Ctrl+P, &#8984; Cmd+P Print the document with one of the available printers or save it as a file. Download As... Ctrl+⇧ Shift+S ^ Ctrl+⇧ Shift+S, &#8984; Cmd+⇧ Shift+S Open the Download as... panel to save the currently edited document to the hard disk drive of your computer in one of the supported formats: DOCX, PDF, ODT, TXT, DOTX, PDF/A, OTT, RTF, HTML, DOCXF, OFORM, HTML, FB2, EPUB. Full screen F11 Switch to the full screen view to fit the Document Editor into your screen. Help menu F1 F1 Open the Document Editor Help menu. Open existing file (Desktop Editors) Ctrl+O On the Open local file tab in the Desktop Editors, opens the standard dialog box that allows to select an existing file. Close file (Desktop Editors) Ctrl+W, Ctrl+F4 ^ Ctrl+W, &#8984; Cmd+W Close the current document window in the Desktop Editors. Element contextual menu ⇧ Shift+F10 ⇧ Shift+F10 Open the selected element contextual menu. Reset the ‘Zoom’ parameter Ctrl+0 ^ Ctrl+0 or &#8984; Cmd+0 Reset the ‘Zoom’ parameter of the current document to a default 100%. Navigation Jump to the beginning of the line Home Home Put the cursor to the beginning of the currently edited line. Jump to the beginning of the document Ctrl+Home ^ Ctrl+Home Put the cursor to the very beginning of the currently edited document. Jump to the end of the line End End Put the cursor to the end of the currently edited line. Jump to the end of the document Ctrl+End ^ Ctrl+End Put the cursor to the very end of the currently edited document. Jump to the beginning of the previous page Alt+Ctrl+Page Up Put the cursor to the very beginning of the page which preceeds the currently edited one. Jump to the beginning of the next page Alt+Ctrl+Page Down ⌥ Option+&#8984; Cmd+⇧ Shift+Page Down Put the cursor to the very beginning of the page which follows the currently edited one. Scroll down Page Down Page Down, ⌥ Option+Fn+↑ Scroll the document approximately one visible page down. Scroll up Page Up Page Up, ⌥ Option+Fn+↓ Scroll the document approximately one visible page up. Next page Alt+Page Down ⌥ Option+Page Down Go to the next page in the currently edited document. Previous page Alt+Page Up ⌥ Option+Page Up Go to the previous page in the currently edited document. Zoom In Ctrl++ ^ Ctrl+=, &#8984; Cmd+= Zoom in the currently edited document. Zoom Out Ctrl+- ^ Ctrl+-, &#8984; Cmd+- Zoom out the currently edited document. Move one character to the left ← ← Move the cursor one character to the left. Move one character to the right → → Move the cursor one character to the right. Move to the beginning of a word or one word to the left Ctrl+← ^ Ctrl+←, &#8984; Cmd+← Move the cursor to the beginning of a word or one word to the left. Move one word to the right Ctrl+→ ^ Ctrl+→, &#8984; Cmd+→ Move the cursor one word to the right. Move one line up ↑ ↑ Move the cursor one line up. Move one line down ↓ ↓ Move the cursor one line down. Navigate between controls in modal dialogues ↹ Tab/⇧ Shift+↹ Tab ↹ Tab/⇧ Shift+↹ Tab Navigate between controls to give focus to the next or previous control in modal dialogues. Writing End paragraph ↵ Enter ↵ Return End the current paragraph and start a new one. Add line break ⇧ Shift+↵ Enter ⇧ Shift+↵ Return Add a line break without starting a new paragraph. Delete ← Backspace, Delete ← Backspace, Delete Delete one character to the left (← Backspace) or to the right (Delete) of the cursor. Delete word to the left of cursor Ctrl+← Backspace ^ Ctrl+← Backspace, &#8984; Cmd+← Backspace Delete one word to the left of the cursor. Delete word to the right of cursor Ctrl+Delete ^ Ctrl+Delete, &#8984; Cmd+Delete Delete one word to the right of the cursor. Create nonbreaking space Ctrl+⇧ Shift+␣ Spacebar ^ Ctrl+⇧ Shift+␣ Spacebar, &#8984; Cmd+⇧ Shift+␣ Spacebar Create a space between characters which cannot be used to start a new line. Create nonbreaking hyphen Ctrl+⇧ Shift+_ ^ Ctrl+⇧ Shift+Hyphen, &#8984; Cmd+⇧ Shift+Hyphen Create a hyphen between characters which cannot be used to start a new line. Undo and Redo Undo Ctrl+Z ^ Ctrl+Z, &#8984; Cmd+Z Reverse the latest performed action. Redo Ctrl+Y ^ Ctrl+Y, &#8984; Cmd+Y, &#8984; Cmd+⇧ Shift+Z Repeat the latest undone action. Cut, Copy, and Paste Cut Ctrl+X, ⇧ Shift+Delete &#8984; Cmd+X, ⇧ Shift+Delete Delete the selected text fragment and send it to the computer clipboard memory. The copied text can be later inserted to another place in the same document, into another document, or into some other program. Copy Ctrl+C, Ctrl+Insert &#8984; Cmd+C Send the selected text fragment to the computer clipboard memory. The copied text can be later inserted to another place in the same document, into another document, or into some other program. Paste Ctrl+V, ⇧ Shift+Insert &#8984; Cmd+V Insert the previously copied text fragment from the computer clipboard memory to the current cursor position. The text can be previously copied from the same document, from another document, or from some other program. Insert hyperlink Ctrl+K &#8984; Cmd+K Insert a hyperlink which can be used to go to a web address. Copy style Ctrl+⇧ Shift+C &#8984; Cmd+⇧ Shift+C Copy the formatting from the selected fragment of the currently edited text. The copied formatting can be later applied to another text fragment in the same document. Apply style Ctrl+⇧ Shift+V &#8984; Cmd+⇧ Shift+V Apply the previously copied formatting to the text in the currently edited document. Text Selection Select all Ctrl+A &#8984; Cmd+A Select all the document text with tables and images. Select fragment ⇧ Shift+→ ← ⇧ Shift+→ ← Select the text character by character. Select from cursor to beginning of line ⇧ Shift+Home ⇧ Shift+Home Select a text fragment from the cursor to the beginning of the current line. Select from cursor to end of line ⇧ Shift+End ⇧ Shift+End Select a text fragment from the cursor to the end of the current line. Select one character to the right ⇧ Shift+→ ⇧ Shift+→ Select one character to the right of the cursor position. Select one character to the left ⇧ Shift+← ⇧ Shift+← Select one character to the left of the cursor position. Select to the end of a word Ctrl+⇧ Shift+→ Select a text fragment from the cursor to the end of a word. Select to the beginning of a word Ctrl+⇧ Shift+← Select a text fragment from the cursor to the beginning of a word. Select one line up ⇧ Shift+↑ ⇧ Shift+↑ Select one line up (with the cursor at the beginning of a line). Select one line down ⇧ Shift+↓ ⇧ Shift+↓ Select one line down (with the cursor at the beginning of a line). Select the page up ⇧ Shift+Page Up ⇧ Shift+Page Up Select the page part from the cursor position to the upper part of the screen. Select the page down ⇧ Shift+Page Down ⇧ Shift+Page Down Select the page part from the cursor position to the lower part of the screen. Text Styling Bold Ctrl+B ^ Ctrl+B, &#8984; Cmd+B Make the font of the selected text fragment darker and heavier than normal. Italic Ctrl+I ^ Ctrl+I, &#8984; Cmd+I Make the font of the selected text fragment italicized and slightly slanted. Underline Ctrl+U ^ Ctrl+U, &#8984; Cmd+U Make the selected text fragment underlined with a line going below the letters. Strikeout Ctrl+5 ^ Ctrl+5, &#8984; Cmd+5 Make the selected text fragment struck out with a line going through the letters. Subscript Ctrl+. ^ Ctrl+⇧ Shift+&gt;, &#8984; Cmd+⇧ Shift+&gt; Make the selected text fragment smaller and place it to the lower part of the text line, e.g. as in chemical formulas. Superscript Ctrl+, ^ Ctrl+⇧ Shift+&lt;, &#8984; Cmd+⇧ Shift+&lt; Make the selected text fragment smaller and place it to the upper part of the text line, e.g. as in fractions. Heading 1 style Alt+1 ⌥ Option+^ Ctrl+1 Apply the style of the heading 1 to the selected text fragment. Heading 2 style Alt+2 ⌥ Option+^ Ctrl+2 Apply the style of the heading 2 to the selected text fragment. Heading 3 style Alt+3 ⌥ Option+^ Ctrl+3 Apply the style of the heading 3 to the selected text fragment. Bulleted list Ctrl+⇧ Shift+L ^ Ctrl+⇧ Shift+L, &#8984; Cmd+⇧ Shift+L Create an unordered bulleted list from the selected text fragment or start a new one. Remove formatting Ctrl+␣ Spacebar Remove formatting from the selected text fragment. Increase font Ctrl+] &#8984; Cmd+] Increase the size of the font for the selected text fragment 1 point. Decrease font Ctrl+[ &#8984; Cmd+[ Decrease the size of the font for the selected text fragment 1 point. Align center/left Ctrl+E ^ Ctrl+E, &#8984; Cmd+E Switch a paragraph between centered and left-aligned. Align justified/left Ctrl+J, Ctrl+L ^ Ctrl+J, &#8984; Cmd+J Switch a paragraph between justified and left-aligned. Align right/left Ctrl+R ^ Ctrl+R, &#8984; Cmd+R Switch a paragraph between right-aligned and left-aligned. Apply subscript formatting (automatic spacing) Ctrl+= Apply subscript formatting to the selected text fragment. Apply superscript formatting (automatic spacing) Ctrl+⇧ Shift++ Apply superscript formatting to the selected text fragment. Insert page break Ctrl+↵ Enter ^ Ctrl+↵ Return, &#8984; Cmd+↵ Return Insert a page break at the current cursor position. Increase indent Ctrl+M ^ Ctrl+M, &#8984; Cmd+M Indent a paragraph from the left incrementally. Decrease indent Ctrl+⇧ Shift+M ^ Ctrl+⇧ Shift+M, &#8984; Cmd+⇧ Shift+M Remove a paragraph indent from the left incrementally. Add page number Ctrl+⇧ Shift+P ^ Ctrl+⇧ Shift+P, &#8984; Cmd+⇧ Shift+P Add the current page number at the current cursor position. Nonprinting characters Ctrl+⇧ Shift+Num8 Show or hide the display of nonprinting characters. Delete one character to the left ← Backspace ← Backspace Delete one character to the left of the cursor. Delete one character to the right Delete Delete Delete one character to the right of the cursor. Modifying Objects Constrain movement ⇧ Shift + drag ⇧ Shift + drag Constrain the movement of the selected object horizontally or vertically. Set 15-degree rotation ⇧ Shift + drag (when rotating) ⇧ Shift + drag (when rotating) Constrain the rotation angle to 15-degree increments. Maintain proportions ⇧ Shift + drag (when resizing) ⇧ Shift + drag (when resizing) Maintain the proportions of the selected object when resizing. Draw straight line or arrow ⇧ Shift + drag (when drawing lines/arrows) ⇧ Shift + drag (when drawing lines/arrows) Draw a straight vertical/horizontal/45-degree line or arrow. Movement by one-pixel increments Ctrl+← → ↑ ↓ Hold down the Ctrl key and use the keybord arrows to move the selected object by one pixel at a time. Working with Tables Move to the next cell in a row ↹ Tab ↹ Tab Go to the next cell in a table row. Move to the previous cell in a row ⇧ Shift+↹ Tab ⇧ Shift+↹ Tab Go to the previous cell in a table row. Move to the next row ↓ ↓ Go to the next row in a table. Move to the previous row ↑ ↑ Go to the previous row in a table. Start new paragraph ↵ Enter ↵ Return Start a new paragraph within a cell. Add new row ↹ Tab in the lower right table cell. ↹ Tab in the lower right table cell. Add a new row at the bottom of the table. Insert table break Ctrl+⇧ Shift+↵ Enter ^ Ctrl+⇧ Shift+↵ Return, &#8984; Cmd+⇧ Shift+↵ Return Insert a table break within the table. Inserting special characters Insert equation Alt+= ⌥ Option+^ Ctrl+= Insert an equation at the current cursor position. Insert an em dash Alt+Ctrl+Num- ⌥ Option+⇧ Shift+- Insert an em dash ‘—’ within the current document and to the right of the cursor. Insert an en dash ⌥ Option+- Insert an en dash ‘-’ within the current document and to the right of the cursor. Insert a non-breaking hyphen Ctrl+⇧ Shift+_ ^ Ctrl+⇧ Shift+Hyphen, &#8984; Cmd+⇧ Shift+Hyphen Insert a non-breaking hyphen ‘-’ within the current document and to the right of the cursor. Insert a no-break space Ctrl+⇧ Shift+␣ Spacebar ⌥ Option+␣ Spacebar Insert a no-break space ‘o’ within the current document and to the right of the cursor."
    },
   {
        "id": "HelpfulHints/Navigation.htm", 
        "title": "View Settings and Navigation Tools", 
        "body": "The Document Editor offers several tools to help you view and navigate through your document: zoom, page number indicator etc. Adjust the View Settings To adjust default view settings and set the most convenient mode to work with the document, go to the View tab and select which interface elements you want to be hidden or shown. You can select the following options on the View tab: Headings – to show the document headers in the left panel. Zoom – to set the required zoom value from 50% to 500% from the drop-down list. Fit to Page - to fit the whole document page to the visible part of the working area. Fit to Width - to fit the document page width to the visible part of the working area. Interface theme – choose one of the available interface themes from the drop-down menu: Same as system, Light, Classic Light, Dark, Contrast Dark. When the Dark or Contrast Dark theme is enabled, the Dark document switcher becomes active; use it to set the working area to white or dark gray. Always show toolbar – when this option is disabled, the top toolbar that contains commands will be hidden while tab names remain visible. Alternatively, you can just double-click any tab to hide the top toolbar or display it again. Status Bar – when this option is disabled, the bottommost bar where the Page Number Indicator, Word Count Indicator and Zoom buttons are situated will be hidden. To show the hidden Status Bar, enable this option. Rulers - when this option is disabled, the rulers which are used to align text, graphics, tables, and other elements in a document, set up margins, tab stops, and paragraph indents will be hidden. To show the hidden Rulers, enable this option once again. Left Panel - when disabled, hides the left panel where Search, Comments, etc. buttons are located. To show the left panel, check this box. Right Panel - when disabled, hides the right panel where Settings are located. To show the right panel, check this box. The right sidebar is minimized by default. To expand it, select any object (e.g. image, chart, shape) or text passage and click the icon of the currently activated tab on the right. To minimize the right sidebar, click the icon once again. When the Comments or Chat panel is opened, the width of the left sidebar is adjusted by simple drag-and-drop: move the mouse cursor over the left sidebar border so that it turns into the bidirectional arrow and drag the border to the right to extend the width of the sidebar. To restore its original width, move the border to the left. Use the Navigation Tools To navigate through your document, use the following tools: The Zoom buttons are situated in the right lower corner and are used to zoom in and out the current document. To change the currently selected zoom value that is displayed in percent, click it and select one of the available zoom options from the list (50% / 75% / 100% / 125% / 150% / 175% / 200% / 300% / 400% / 500%) or use the Zoom in or Zoom out buttons. Click the Fit to width icon to fit the document page width to the visible part of the working area. To fit the whole document page to the visible part of the working area, click the Fit to page icon. Zoom settings are also available on the View tab. The Page Number Indicator shows the current page as a part of all the pages in the current document (page 'n' of 'nn'). Click this caption to open the window where you can enter the page number and quickly go to it. The Word Count Indicator shows the current document's word count statistics."
    },
   {
        "id": "HelpfulHints/Password.htm", 
        "title": "Protecting documents with a password", 
        "body": "You can protect your documents with a password that is required to enter the editing mode by your co-authors. The password can be changed or removed later on. The password cannot be restored if you lose or forget it. Please keep it in a safe place. You can also protect your documents by restricting access rights. Setting a password go to the File tab at the top toolbar and choose the Protect option, or go to the Protection tab and choose the Encrypt option, set a password in the Password field and repeat it in the Repeat password field below. Click to show or hide password characters when entered. Click OK when ready. To open the document with the full access rights, the user has to enter the set password. Changing a password go to the File tab at the top toolbar and choose the Protect option, or go to the Protection tab and choose the Encrypt option, set a new password in the Password field and repeat it in the Repeat password field below. Click to show or hide password characters when entered. click OK. Deleting a password go to the File tab at the top toolbar, choose the Protect option, click the Delete password button. Protecting a document go to the Protection tab, click the Protect Document button, set the password if necessary, choose the required access rights for the document provided the password has not been entered by the user: No changes (Read only) - the user can only view the document. Filling forms - the user can only fill a form. Tracked changes - the user can only view the document changes history and review the document itself. Comments - the user can only leave and answer comments. click Protect when ready. To remove protection from the document provided that the password has been set, go to the Protection tab, click the Protect Document button, enter the set password in the Unprotect Document window."
    },
   {
        "id": "HelpfulHints/Review.htm", 
        "title": "Reviewing documents", 
        "body": "The Document Editor allows you to maintain constant team-wide approach to work flow: share files and folders, collaborate on documents in real time, communicate right in the editor, comment certain parts of your documents that require additional third-party input, save document versions for future use, compare and merge documents to facilitate processing and editing. When somebody shares a file with you using the review permissions, you need to apply the document Review feature. In the Document Editor, as a reviewer, you can use the Review option to review the document, change the sentences, phrases and other page elements, correct spelling, etc. without actually editing it. All your changes will be recorded and shown to the person who sent you the document. If you send the file for review, you will need to display all the changes which were made to it, view and either accept or reject them. Enable the Track Changes feature To see changes suggested by a reviewer, enable the Track Changes option in one of the following ways: click the button in the right lower corner on the status bar, or switch to the Collaboration tab on the top toolbar and press the Track Changes button. It is not necessary for the reviewer to enable the Track Changes option. It is enabled by default and cannot be disabled when the document is shared with review only access rights. the following options are available in the opened pop-up menu: On for me - tracking changes is enabled for the current user only. The option remains enabled for the current editing session, i.e. will be disabled when you reload or open the document anew. It will not be affected by other users enabling or disabling the general tracking changes option. Off for me - tracking changes is disabled for the current user only. The option remains disabled for the current editing session. It will not be affected by other users enabling or disabling the general tracking changes option. On for me and everyone - tracking changes is enabled and will remain when you reload or open the document anew (when the document is reloaded, all users will have the tracking enabled). When another user disables the general tracking changes option in the file, it will be switched to Off for me and everyone for all users. Off for me and everyone - tracking changes is disabled and will remain when you reload or open the document anew (when the document is reloaded, all users will have the tracking disabled). When another user enables the general tracking changes option in the file, it will be switched to On for me and everyone for all users. The corresponding alert message will be shown to every co-author. View changes Changes made by a user are highlighted with a specific color in the document text. When you click on the changed text, a balloon opens which displays the user name, the date and time when the change has been made, and the change description. The balloon also contains icons used to accept or reject the current change. If you drag and drop a piece of text to some other place in the document, the text in a new position will be underlined with the double line. The text in the original position will be double-crossed. This will count as a single change. Click the double-crossed text in the original position and use the arrow in the change balloon to go to the new location of the text. Click the double-underlined text in the new position and use the arrow in the change balloon to go to to the original location of the text. Choose the changes display mode Click the Display Mode button on the top toolbar and select one of the available modes from the list: Markup and balloons - this option is selected by default. It allows both viewing the suggested changes and editing the document. Changes are highlighted in the document text and displayed in balloons. Only markup - this mode allows both viewing the suggested changes and editing the document. Changes are displayed in the document text only, balloons are hidden. Final - this mode is used to display all the changes as if they were accepted. This option does not actually accept all changes, it only allows you to see how the document will look like after you accept all the changes. In this mode, you cannot edit the document. Original - this mode is used to display all the changes as if they were rejected. This option does not actually reject all changes, it only allows you to view the document without changes. In this mode, you cannot edit the document. Accept or reject changes Use the Previous and the Next buttons on the top toolbar to navigate through the changes. To accept the currently selected change you can: click the Accept button on the top toolbar, or click the downward arrow below the Accept button and select the Accept Current Change option (in this case, the change will be accepted and you will proceed to the next change), or click the Accept button of the change balloon. To quickly accept all the changes, click the downward arrow below the Accept button and select the Accept All Changes option. To reject the current change you can: click the Reject button on the top toolbar, or click the downward arrow below the Reject button and select the Reject Current Change option (in this case, the change will be rejected and you will move on to the next available change), or click the Reject button of the change balloon. To quickly reject all the changes, click the downward arrow below the Reject button and select the Reject All Changes option. If you need to accept or reject one change, right-click it and choose Accept Change or Reject Change from the context menu. If you review the document, the Accept and Reject options are not available for you. You can delete your changes using the icon within the change balloon."
    },
   {
        "id": "HelpfulHints/Search.htm", 
        "title": "Search and Replace Function", 
        "body": "To search for the required characters, words or phrases used in the currently edited document, click the icon situated on the left sidebar of the Document Editor, the icon situated in the upper right corner, or use the Ctrl+F (Command+F for MacOS) key combination to open the small Find panel or the Ctrl+H key combination to open the full Find panel. A small Find panel will open in the upper right corner of the working area. To access the advanced settings, click the icon or use the Ctrl+H key combination. The Find and replace panel will open: Type in your inquiry into the corresponding Find data entry field. If you need to replace one or more occurrences of the found characters, type in the replacement text into the corresponding Replace with data entry field. You can choose to replace a single currently highlighted occurrence or replace all occurrences by clicking the corresponding Replace and Replace All buttons. To navigate between the found occurrences, click one of the arrow buttons. The button shows the next occurrence while the button shows the previous one. Specify search parameters by checking the necessary options below the entry fields: Case sensitive - is used to find only the occurrences typed in the same case as your inquiry (e.g. if your inquiry is 'Editor' and this option is selected, such words as 'editor' or 'EDITOR' etc. will not be found). Whole words only - is used to highlight only whole words. All occurrences will be highlighted in the file and shown as a list in the Find panel to the left. Use the list to skip to the required occurrence, or use the navigation and buttons. The Document Editor supports search for special characters. To find a special character, enter it into the search box. The list of special characters that can be used in searches Special character Description ^l Line break ^t Tab stop ^? Any symbol ^# Any digit ^$ Any letter ^n Column break ^e Endnote ^f Footnote ^g Graphic element ^m Page break ^~ Non-breaking hyphen ^s Non-breaking space ^^ Escaping the caret itself ^w Any space ^+ Em dash ^= En dash ^y Any dash Special characters that may be used for replacement too: Special character Description ^l Line break ^t Tab stop ^n Column break ^m Page break ^~ Non-breaking hyphen ^s Non-breaking space ^+ Em dash ^= En dash"
    },
   {
        "id": "HelpfulHints/SpellChecking.htm", 
        "title": "Spell-checking", 
        "body": "The Document Editor allows you to check the spelling of your text in a certain language and correct mistakes while editing. In the desktop version, it's also possible to add words into a custom dictionary which is common for all three editors. Starting from version 6.3, the ONLYOFFICE editors support the SharedWorker interface for smoother operation without significant memory consumption. If your browser does not support SharedWorker then just Worker will be active. For more information about SharedWorker please refer to this article. First of all, choose a language for your document. Click the Set Document Language icon on the status bar. In the opened window, select the required language and click OK. The selected language will be applied to the whole document. To choose a different language for any piece within the document, select the necessary text passage with the mouse and use the menu on the status bar. To enable the spell checking option, you can: click the Spell checking icon on the status bar, or open the File tab of the top toolbar, select the Advanced Settings option, check the Turn on spell checking option box and click the Apply button. All misspelled words will be underlined by a red line. Right click on the necessary word to activate the menu and: choose one of the suggested similar words spelled correctly to replace the misspelled word with the suggested one. If too many variants are found, the More variants... option appears in the menu; use the Ignore option to skip just that word and remove underlining or Ignore All to skip all the identical words repeated in the text; if the current word is missed in the dictionary, you can add it to the custom dictionary. This word will not be treated as a mistake next time. This option is available in the desktop version. select a different language for this word. To disable the spell checking option, you can: click the Spell checking icon on the status bar, or open the File tab of the top toolbar, select the Advanced Settings option, uncheck the Turn on spell checking option box and click the Apply button."
    },
   {
        "id": "HelpfulHints/SupportedFormats.htm", 
        "title": "Supported Formats of Electronic Documents", 
        "body": "An electronic document is one of the most commonly used computer. Due to the highly developed modern computer network, it's more convenient to distribute electronic documents than printed ones. Nowadays, a lot of devices are used for document presentation, so there are plenty of proprietary and open file formats. The Document Editor handles the most popular of them. While uploading or opening the file for editing, it will be converted to the Office Open XML (DOCX) format. It's done to speed up the file processing and increase the interoperability. The following table contains the formats which can be opened for viewing and/or editing. Formats Description View natively View after conversion to OOXML Edit natively Edit after conversion to OOXML DjVu File format designed primarily to store scanned documents, especially those containing a combination of text, line drawings, and photographs + DOC Filename extension for word processing documents created with Microsoft Word + + DOCM Macro-Enabled Microsoft Word Document Filename extension for Microsoft Word 2007 or higher generated documents with the ability to run macros + + DOCX Office Open XML Zipped, XML-based file format developed by Microsoft for representing spreadsheets, charts, presentations, and word processing documents + + DOCXF A format to create, edit and collaborate on a Form Template. + + DOTX Word Open XML Document Template Zipped, XML-based file format developed by Microsoft for text document templates. A DOTX template contains formatting settings, styles etc. and can be used to create multiple documents with the same formatting + + EPUB Electronic Publication Free and open e-book standard created by the International Digital Publishing Forum + + FB2 An ebook extension that lets you read books on your computer or mobile devices + + HTML HyperText Markup Language The main markup language for web pages + + ODT Word processing file format of OpenDocument, an open standard for electronic documents + + OFORM A format to fill out a Form. Form fields are fillable but users cannot change the formatting or parameters of the form elements*. + + OTT OpenDocument Document Template OpenDocument file format for text document templates. An OTT template contains formatting settings, styles etc. and can be used to create multiple documents with the same formatting + + PDF Portable Document Format File format used to represent documents regardless of the used software, hardware, and operating systems + PDF/A Portable Document Format / A An ISO-standardized version of the Portable Document Format (PDF) specialized for use in the archiving and long-term preservation of electronic documents. + RTF Rich Text Format Document file format developed by Microsoft for cross-platform document interchange + + TXT Filename extension for text files usually containing very little formatting + + XML Extensible Markup Language (XML). A simple and flexible markup language that derived from SGML (ISO 8879) and is designed to store and transport data. + + XPS Open XML Paper Specification Open royalty-free fixed-layout document format developed by Microsoft + *Note: the OFORM format is a format for filling out a form. Therefore, only form fields are editable. The following table contains the formats in which you can download a document from the File -> Download as menu. Input format Can be downloaded as DjVu DjVu, PDF DOC DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT DOCM DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT DOCX DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT DOCXF DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT DOTX DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT EPUB DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT FB2 DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT HTML DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT ODT DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT OFORM DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT OTT DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT PDF DOCX, DOCXF, DOTX, EPUB, FB2, HTML, OFORM, PDF, RTF, TXT PDF/A DOCX, DOCXF, DOTX, EPUB, FB2, HTML, OFORM, PDF, RTF, TXT RTF DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT TXT DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT XML DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT XPS DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT, XPS You can also refer to the conversion matrix on api.onlyoffice.com to see possibility of conversion your documents into the most known file formats."
    },
   {
        "id": "HelpfulHints/VersionHistory.htm", 
        "title": "Version history", 
        "body": "The Document Editor allows you to maintain constant team-wide approach to work flow: share files and folders, collaborate on documents in real time, communicate right in the editor, comment certain parts of your documents that require additional third-party input, review documents and add your changes without actually editing the file, compare and merge documents to facilitate processing and editing. In Document Editor you can view the version history of the document you collaborate on. Viewing version history: To view all the changes made to the document, go to the File tab, select the Version History option at the left sidebar or go to the Collaboration tab, open the history of versions using the  Version History icon at the top toolbar. You'll see the list of the document versions and revisions with the indication of each version/revision author and creation date and time. For document versions, the version number is also specified (e.g. ver. 2). Viewing versions: To know exactly which changes have been made in each separate version/revision, you can view the one you need by clicking it on the left sidebar. The changes made by the version/revision author are marked with the color which is displayed next to the author's name on the left sidebar. To return to the current version of the document, use the Close History option on the top of the version list. Restoring versions: If you need to roll back to one of the previous versions of the document, click the Restore link below the selected version/revision. To learn more about managing versions and intermediate revisions, as well as restoring previous versions, please read the following article."
    },
   {
        "id": "HelpfulHints/Viewer.htm", 
        "title": "ONLYOFFICE Document Viewer", 
        "body": "You can use ONLYOFFICE Document Viewer to open and navigate through PDF, XPS, DjVu files. ONLYOFFICE Document Viewer allows to: view PDF, XPS, DjVu files, add annotations using chat tool, navigate files using contents navigation panel and page thumbnails, use select and hand tools, print and download files, use internal and external links, access advanced settings of the editor and view the following document info using the File and the View tab: Location (available in the online version only) - the folder in the Documents module where the file is stored. Owner (available in the online version only) - the name of the user who has created the file. Uploaded (available in the online version only) - the date and time when the file has been uploaded to the portal. Statistics - the number of pages, paragraphs, words, symbols, symbols with spaces. Page size - the dimensions of the pages in the file. Last Modified - the date and time when the document was last modified. Created - the date and time when the document was created. Application - the application the document has been created with. Author - the person who has created the document. PDF Producer - the application used to convert the document to PDF. PDF Version - the version of the PDF file. Tagged PDF - shows if PDF file contains tags. Fast Web View - shows if the Fast Web View has been enabled for the document. use plugins Plugins available in the desktop version: Translator, Send, Thesaurus. Plugins available in the online version: Controls example, Get and paste html, Telegram, Typograf, Count word, Speech, Thesaurus, Translator. ONLYOFFICE Document Viewer interface: The Top toolbar provides access to File, View and Plugins tabs, and the following icons: Print allows to print out the file; Download allows to download a file to your computer; Share (available in the online version only) allows you to manage the users who have access to the file right from the document: invite new users giving them permissions to edit, read, comment, fill forms or review the document, or deny some users access rights to the file. Open file location in the desktop version allows opening the folder, where the file is stored, in the File explorer window. In the online version, it allows opening the folder of the Documents module, where the file is stored, in a new browser tab; Mark as favorite / Remove from favorites (available in the online version only) click the empty star to add a file to favorites as to make it easy to find, or click the filled star to remove the file from favorites. The added file is just a shortcut so the file itself remains stored in its original location. Deleting a file from favorites does not remove the file from its original location; User displays the user’s name when you hover the mouse over it. Search - allows to search the document for a particular word or symbol, etc. The Status bar located at the bottom of the ONLYOFFICE Document Viewer window indicates the page number and displays the background status notifications. It also contains the following tools: Selection tool allows to select text in a file. Hand tool allows to drag and scroll the page. Fit to page allows to resize the page so that the screen displays the whole page. Fit to width allows to resize the page so that the page scales to fit the width of the screen. Zoom adjusting tool allows to zoom in and zoom out the page. The Left sidebar contains the following icons: - allows to use the Search and Replace tool, - (available in the online version only) allows opening the Chat panel, - allows to open the Headings panel that displays the list of all headings with corresponding nesting levels. Click the heading to jump directly to a specific page. Click the Settings icon to the right of the Headings panel and use one of the available options from the menu: Expand all - to expand all levels of headings at the Headings panel. Collapse all - to collapse all levels of headings, excepting level 1, at the Headings panel. Expand to level - to expand the heading structure to the selected level. E.g. if you select level 3, then levels 1, 2 and 3 will be expanded, while level 4 and all lower levels will be collapsed. Font size – to customize font size of the Headings panel text by choosing one of the available presets: Small, Medium, and Large. Wrap long headings – to wrap long heading text. To manually expand or collapse separate heading levels, use the arrows to the left of the headings. To close the Headings panel, click the   Headings icon on the left sidebar once again. - allows to display page thumbnails for quick navigation. Click on the Page Thumbnails panel to access the Thumbnails Settings: Drag the slider to set the thumbnail size, The Highlight visible part of page is active by default to indicate the area that is currently on screen. Click it to disable. To close the Page Thumbnails panel, click the Page Thumbnails icon on the left sidebar once again. - allows to contact our support team, - (available in the online version only) allows to view the information about the program."
    },
   {
        "id": "ProgramInterface/FileTab.htm", 
        "title": "File tab", 
        "body": "The File tab of the Document Editor allows performing some basic operations. The corresponding window of the Online Document Editor: The corresponding window of the Desktop Document Editor: With this tab, you can use the following options: in the online version: save the current file (in case the Autosave option is disabled), save it in the required format on the hard disk drive of your computer with the Download as option, save a copy of the file in the selected format to the portal documents with the Save copy as option, print or rename the current file. in the desktop version: save the current file without changing its format and location using the Save option, save it changing its name, location or format using the Save as option or print the current file. protect the file using a password, change or remove the password; protect the file using a digital signature (available in the desktop version only); create a new document or open a recently edited one (available in the online version only), view general information about the document or change some file properties, manage access rights (available in the online version only), track version history (available in the online version only), access the Advanced Settings of the editor, in the desktop version, open the folder, where the file is stored, in the File explorer window. In the online version, open the folder of the Documents module, where the file is stored, in a new browser tab."
    },
   {
        "id": "ProgramInterface/FormsTab.htm", 
        "title": "Forms tab", 
        "body": "Note: this tab is available with DOCXF files only. The Forms tab allows you to create fillable forms such as agreements, applications or surveys. Add, format and configure text and form fields to draft a fillable form no matter how complex you need it to be. The corresponding window of the Online Document Editor: The corresponding window of the Desktop Document Editor: Using this tab, you can: insert and edit text fields combo boxes drop-down lists checkboxes radio buttons images e-mail addresses phone numbers date and time zip codes credit card numbers complex fields clear all fields and highlight settings, navigate through form fields using Previous Field and Next Field buttons, view the resulting forms in your document, manage roles, save form as a fillable OFORM file."
    },
   {
        "id": "ProgramInterface/HomeTab.htm", 
        "title": "Home tab", 
        "body": "The Home tab appears by default when you open the Document Editor. It also allows formating fonts and paragraphs. Some other options are also available here, such as Mail Merge and color schemes. The corresponding window of the Online Document Editor: The corresponding window of the Desktop Document Editor: Using this tab, you can: adjust the font type, its size and color, apply font decoration styles, select a background color for a paragraph, create bulleted and numbered lists, change paragraph indents, set paragraph line spacing, align your text in a paragraph, show/hide non-printing characters, copy/clear text formatting, change the color scheme, use Mail Merge (available in the online version only), manage styles."
    },
   {
        "id": "ProgramInterface/InsertTab.htm", 
        "title": "Insert tab", 
        "body": "The Insert tab of the Document Editor allows adding some page formatting elements as well as visual objects and comments. The corresponding window of the Online Document Editor: The corresponding window of the Desktop Document Editor: Using this tab, you can: insert a blank page, insert page breaks, section breaks and column breaks, insert tables, images, charts, shapes, insert hyperlinks, comments, insert headers and footers, page numbers, date &amp time, insert text boxes and Text Art objects, equations, symbols, drop caps, content controls, insert SmartArt objects."
    },
   {
        "id": "ProgramInterface/LayoutTab.htm", 
        "title": "Layout tab", 
        "body": "The Layout tab of the Document Editor allows changing the appearance of a document: setting up page parameters and defining the arrangement of visual elements. The corresponding window of the Online Document Editor: The corresponding window of the Desktop Document Editor: Using this tab, you can: adjust page margins, orientation and size, add columns, insert page breaks, section breaks and column breaks, insert line numbers align and arrange objects (tables, pictures, charts, shapes), change the wrapping style and edit wrap boundary, add a watermark."
    },
   {
        "id": "ProgramInterface/PluginsTab.htm", 
        "title": "Plugins tab", 
        "body": "The Plugins tab of the Document Editor allows accessing the advanced editing features using the available third-party components. This tab also makes it possible to use macros to simplify routine operations. The corresponding window of the Online Document Editor: The corresponding window of the Desktop Document Editor: The Settings button allows viewing and managing all the installed plugins as well as adding new ones. The Macros button allows you to create and run your own macros. To learn more about macros, please refer to our API Documentation. Currently, the following plugins are available by default: Send allows to send the document via email using the default desktop mail client (available in the desktop version only), Highlight code allows to highlight syntax of the code selecting the necessary language, style, background color, OCR allows to recognize text included into a picture and insert it into the document text, Photo Editor allows to edit images: crop, flip, rotate them, draw lines and shapes, add icons and text, load a mask and apply filters such as Grayscale, Invert, Sepia, Blur, Sharpen, Emboss, etc., Speech allows to convert the selected text into speech (available in the online version only), Thesaurus allows to search for synonyms and antonyms of a word and replace it with the selected one, Translator allows to translate the selected text into other languages, This plugin doesn't work in Internet Explorer. YouTube allows to embed YouTube videos into your document, Mendeley allows to manage research papers and generate bibliographies for scholarly articles (available in the online version only), Zotero allows to manage bibliographic data and related research materials (available in the online version only), EasyBib helps to find and insert related books, journal articles and websites (available in the online version only). The Wordpress and EasyBib plugins can be used if you connect the corresponding services in your portal settings. You can use the following instructions for the server version or for the SaaS version. The Wordpress and EasyBib plugins are not included in the free version of the editors. To learn more about plugins, please refer to our API Documentation. All the currently existing open source plugin examples are available on GitHub."
    },
   {
        "id": "ProgramInterface/ProgramInterface.htm", 
        "title": "Introducing the user interface of the Document Editor", 
        "body": "The Document Editor uses a tabbed interface where editing commands are grouped into tabs by functionality. Main window of the Online Document Editor: Main window of the Desktop Document Editor: The editor interface consists of the following main elements: The Editor header displays the ONLYOFFICE logo, tabs for all opened documents with their names and menu tabs. On the left side of the Editor header, the Save, Print file, Undo and Redo buttons are located. On the right side of the Editor header, along with the user name the following icons are displayed: Open file location - in the desktop version, it allows opening the folder, where the file is stored, in the File explorer window. In the online version, it allows opening the folder of the Documents module, where the file is stored, in a new browser tab. Share (available in the online version only). It allows adjusting access rights for the documents stored in the cloud. Mark as favorite - click the star to add a file to favorites as to make it easy to find. The added file is just a shortcut so the file itself remains stored in its original location. Deleting a file from favorites does not remove the file from its original location. Search - allows to search the document for a particular word or symbol, etc. The Top toolbar displays a set of editing commands depending on the selected menu tab. Currently, the following tabs are available: File, Home, Insert, Layout, References, Forms (available with DOCXF files only), Collaboration, Protection, Plugins. The Copy, Paste, Cut and Select All options are always available on the left side of the Top toolbar regardless of the selected tab. The Status bar located at the bottom of the editor window indicates the page number and word count, as well as displays some notifications (for example, \"All changes saved\", ‘Connection is lost’ when there is no connection and the editor is trying to reconnect etc.). It also allows setting the text language, enabling spell checking, turning on the track changes mode and adjusting zoom. The Left sidebar contains the following icons: - allows using the Search and Replace tool, - allows opening the Comments panel, - allows going to the Headings panel and managing headings, - (available in the online version only) allows opening the Chat panel, - (available in the online version only) allows to contact our support team, - (available in the online version only) allows to view the information about the program. Right sidebar sidebar allows adjusting additional parameters of different objects. When you select a particular object in the text, the corresponding icon is activated on the Right sidebar. Click this icon to expand the Right sidebar. The horizontal and vertical Rulers make it possible to align the text and other elements in the document, set up margins, tab stops and paragraph indents. Working area allows to view document content, enter and edit data. Scroll bar on the right allows to scroll up and down multi-page documents. For your convenience, you can hide some components and display them again when them when necessary. To learn more about adjusting view settings, please refer to this page."
    },
   {
        "id": "ProgramInterface/ProtectionTab.htm", 
        "title": "Protection tab", 
        "body": "The Protection tab of the Document Editor allows protecting your documents with a password while setting restricted access rights. The corresponding window of the Online Document Editor: The corresponding window of the Desktop Document Editor: Using this tab, you can: set a password for your document, change passwords and delete them, set certain types of editing in the protected documents, remove document protection altogether."
    },
   {
        "id": "ProgramInterface/ReferencesTab.htm", 
        "title": "References tab", 
        "body": "The References tab of the Document Editor allows managing different types of references: adding and refreshing tables of contents, creating and editing footnotes, inserting hyperlinks. The corresponding window of the Online Document Editor: The corresponding window of the Desktop Document Editor: Using this tab, you can: create and automatically update a table of contents, insert footnotes and endnotes, insert hyperlinks, add bookmarks. add captions, insert cross-references, create a table of figures."
    },
   {
        "id": "ProgramInterface/ReviewTab.htm", 
        "title": "Collaboration tab", 
        "body": "The Collaboration tab of the Document Editor allows collaborating on documents. In the online version, you can share the file, select the required co-editing mode, manage comments, track changes made by a reviewer, view all versions and revisions. In the commenting mode, you can add and remove comments, navigate between the tracked changes, use the built-in chat and view the version history. In the desktop version, you can manage comments and use the Track Changes feature . The corresponding window of the Online Document Editor: The corresponding window of the Desktop Document Editor: Using this tab, you can: specify the sharing settings (available in the online version only), switch between the Strict and Fast co-editing modes (available in the online version only), add or remove comments to the document, enable the Track Changes feature, choose the changes display mode, manage the suggested changes, load a document for comparison (available in the online version only), open the Chat panel (available in the online version only), track the version history (available in the online version only)."
    },
   {
        "id": "ProgramInterface/ViewTab.htm", 
        "title": "View tab", 
        "body": "The View tab of the Document Editor allows you to manage how your document looks like while you are working on it. The corresponding window of the Online Document Editor: The corresponding window of the Desktop Document Editor: View options available on this tab: Headings allow to display and navigate headings in your document. Zoom allows to zoom in and zoom out your document. Fit to Page allows to resize the page so that the screen displays the whole page. Fit to Width allows to resize the page so that the page scales to fit the width of the screen. Interface Theme allows to change interface theme by choosing a Same as system, Light, Classic Light, Dark or Contrast Dark theme. Dark Document option becomes active when the Dark or Contrast Dark theme is enabled. Click it to make the working area dark too. The following options allow you to configure the elements to display or to hide. Check the elements to make them visible: Always Show Toolbar to make the top toolbar always visible. Status Bar to make the status bar always visible. Left Panel to make the left panel visible. Right Panel to make the right panel visible. Rulers to make rulers always visible."
    },
   {
        "id": "UsageInstructions/AddBorders.htm", 
        "title": "Add borders", 
        "body": "To add borders to a paragraph, page, or the whole document in the Document Editor, place the cursor within the required paragraph, or select several paragraphs with the mouse or the whole text by pressing the Ctrl+A key combination, click the right mouse button and select the Paragraph Advanced Settings option from the menu or use the Show advanced settings link on the right sidebar, switch to the Borders &amp; Fill tab in the opened Paragraph - Advanced Settings window, set the needed value for Border Size and select a Border Color, click within the available diagram or use buttons to select borders and apply the chosen style to them, click the OK button. After adding the borders, you can also set paddings i.e. distances between the right, left, top and bottom borders and the paragraph. To set the necessary values, switch to the Paddings tab of the Paragraph - Advanced Settings window:"
    },
   {
        "id": "UsageInstructions/AddCaption.htm", 
        "title": "Add caption", 
        "body": "s A caption is a numbered label that can be applied to objects, such as equations, tables, figures, and images in the document. A caption allows making a reference in the text - an easily recognizable label on an object. In the Document Editor, you can also use captions to create a table of figures. To add a caption to an object: select the required object to apply a caption; switch to the References tab of the top toolbar; click the Caption icon on the top toolbar or right-click on the object and select the Insert Caption option to open the Insert Caption dialogue box choose the label to use for your caption by clicking the label drop-down and choosing the object. or create a new label by clicking the Add label button to open the Add label dialogue box. Enter a name for the label into the label text box. Then click the OK button to add a new label into the label list; check the Include chapter number checkbox to change the numbering for your caption; in Insert drop-down menu choose Before to place the label above the object or After to place it below the object; check the Exclude label from caption checkbox to leave only a number for this particular caption in accordance with a sequence number; you can then choose how to number your caption by assigning a specific style to the caption and adding a separator; to apply the caption click the OK button. Deleting a label To delete a label you have created, choose the label from the label list within the caption dialogue box then click the Delete label button. The label you created will be immediately deleted. Note: You may delete labels you have created but you cannot delete the default labels. Formatting captions As soon as you add a caption, a new style for captions is automatically added to the styles section. To change the style for all captions throughout the document, you should follow these steps: select the text to copy a new Caption style; search for the Caption style (highlighted in blue by default) in the styles gallery on the Home tab of the top toolbar; right-click on it and choose the Update from selection option. Grouping captions up To move the object and the caption as one unit, you need to group the object and the caption: select the object; select one of the Wrapping styles using the right sidebar; add the caption as it is mentioned above; hold down Shift and select the items to be grouped up; right-click item and choose Arrange > Group. Now both items will move simultaneously if you drag them somewhere else in the document. To unbind the objects, click on Arrange > Ungroup respectively."
    },
   {
        "id": "UsageInstructions/AddFormulasInTables.htm", 
        "title": "Use formulas in tables", 
        "body": "Insert a formula In the Document Editor, you can perform simple calculations on data in table cells by adding formulas. To insert a formula into a table cell, place the cursor within the cell where you want to display the result, click the Add formula button on the right sidebar, in the opened Formula Settings window, enter the required formula into the Formula field. You can enter the required formula manually using the common mathematical operators (+, -, *, /), e.g. =A1*B2 or use the Paste Function drop-down list to select one of the embedded functions, e.g. =PRODUCT(A1,B2). manually specify the required arguments within the parentheses in the Formula field. If the function requires several arguments, they must be separated by commas. use the Number Format drop-down list if you want to display the result in a certain number format, click OK. The result will be displayed in the selected cell. To edit the added formula, select the result in the cell and click the Add formula button on the right sidebar, make the required changes in the Formula Settings window and click OK. Add references to cells You can use the following arguments to quickly add references to cell ranges: ABOVE - a reference to all the cells in the column above the selected cell LEFT - a reference to all the cells in the row to the left of the selected cell BELOW - a reference to all the cells in the column below the selected cell RIGHT - a reference to all the cells in the row to the right of the selected cell These arguments can be used with the AVERAGE, COUNT, MAX, MIN, PRODUCT, SUM functions. You can also manually enter references to a certain cell (e.g., A1) or a range of cells (e.g., A1:B3). Use bookmarks If you have added some bookmarks to certain cells within your table, you can use these bookmarks as arguments when entering formulas. In the Formula Settings window, place the cursor within the parentheses in the Formula entry field where you want the argument to be added and use the Paste Bookmark drop-down list to select one of the previously added bookmarks. Update formula results If you change some values in the table cells, you will need to manually update the formula results: To update a single formula result, select the necessary result and press F9 or right-click the result and use the Update field option from the menu. To update several formula results, select the necessary cells or the entire table and press F9. Embedded functions You can use the following standard math, statistical and logical functions: Category Function Description Example Mathematical ABS(x) The function is used to return the absolute value of a number. =ABS(-10) Returns 10 Logical AND(logical1, logical2, ...) The function is used to check if the logical value you entered is TRUE or FALSE. The function returns 1 (TRUE) if all the arguments are TRUE. =AND(1&gt;0,1&gt;3) Returns 0 Statistical AVERAGE(argument-list) The function is used to analyze the range of data and find the average value. =AVERAGE(4,10) Returns 7 Statistical COUNT(argument-list) The function is used to count the number of the selected cells which contain numbers ignoring empty cells or those contaning text. =COUNT(A1:B3) Returns 6 Logical DEFINED() The function evaluates if a value in the cell is defined. The function returns 1 if the value is defined and calculated without errors and returns 0 if the value is not defined or calculated with an error. =DEFINED(A1) Logical FALSE() The function returns 0 (FALSE) and does not require any argument. =FALSE Returns 0 Logical IF(logical_test, value_if_true, value_if_false) The function is used to check the logical expression and return one value if it is TRUE, or another if it is FALSE. =IF(3&gt;1,1,0) Returns 1 Mathematical INT(x) The function is used to analyze and return the integer part of the specified number. =INT(2.5) Returns 2 Statistical MAX(number1, number2, ...) The function is used to analyze the range of data and find the largest number. =MAX(15,18,6) Returns 18 Statistical MIN(number1, number2, ...) The function is used to analyze the range of data and find the smallest number. =MIN(15,18,6) Returns 6 Mathematical MOD(x, y) The function is used to return the remainder after the division of a number by the specified divisor. =MOD(6,3) Returns 0 Logical NOT(logical) The function is used to check if the logical value you entered is TRUE or FALSE. The function returns 1 (TRUE) if the argument is FALSE and 0 (FALSE) if the argument is TRUE. =NOT(2&lt;5) Returns 0 Logical OR(logical1, logical2, ...) The function is used to check if the logical value you entered is TRUE or FALSE. The function returns 0 (FALSE) if all the arguments are FALSE. =OR(1&gt;0,1&gt;3) Returns 1 Mathematical PRODUCT(argument-list) The function is used to multiply all the numbers in the selected range of cells and return the product. =PRODUCT(2,5) Returns 10 Mathematical ROUND(x, num_digits) The function is used to round the number to the desired number of digits. =ROUND(2.25,1) Returns 2.3 Mathematical SIGN(x) The function is used to return the sign of a number. If the number is positive, the function returns 1. If the number is negative, the function returns -1. If the number is 0, the function returns 0. =SIGN(-12) Returns -1 Mathematical SUM(argument-list) The function is used to add all the numbers in the selected range of cells and return the result. =SUM(5,3,2) Returns 10 Logical TRUE() The function returns 1 (TRUE) and does not require any argument. =TRUE Returns 1"
    },
   {
        "id": "UsageInstructions/AddHyperlinks.htm", 
        "title": "Add hyperlinks", 
        "body": "To add a hyperlink in the Document Editor, place the cursor in the text that you want to display as a hyperlink, switch to the Insert or References tab of the top toolbar, click the Hyperlink icon on the top toolbar, after that the Hyperlink Settings window will appear, and you will be able to specify the hyperlink parameters: Select a link type you wish to insert: Use the External Link option and enter a URL in the http://www.example.com format in the Link to field below if you need to add a hyperlink leading to an external website. If you need to add a hyperlink to a local file, enter the URL in the file://path/Document.docx (for Windows) or file:///path/Document.docx (for MacOS and Linux) format in the Link to field. The file://path/Document.docx or file:///path/Document.docx hyperlink can be opened only in the desktop version of the editor. In the web editor you can only add the link without being able to open it. Use the Place in Document option and select one of the existing headings in the document text or one of previously added bookmarks if you need to add a hyperlink leading to a certain place in the same document. Display - enter a text that will get clickable and lead to the address specified in the upper field. ScreenTip text - enter a text that will become visible in a small pop-up window with a brief note or label pertaining to the hyperlink to be pointed. Click the OK button. To add a hyperlink, you can also use the Ctrl+K key combination or click with the right mouse button at a position where a hyperlink will be added and select the Hyperlink option in the right-click menu. Note: it's also possible to select a character, word, word combination, text passage with the mouse or using the keyboard and then open the Hyperlink Settings window as described above. In this case, the Display field will be filled with the text fragment you selected. By hovering the cursor over the added hyperlink, the ScreenTip will appear containing the text you specified. You can follow the link by pressing the CTRL key and clicking the link in your document. To edit or delete the added hyperlink, click it with the right mouse button, select the Hyperlink option and then the action you want to perform - Edit Hyperlink or Remove Hyperlink."
    },
   {
        "id": "UsageInstructions/AddTableofFigures.htm", 
        "title": "Add and Format a Table of Figures", 
        "body": "Table of Figures provides an overview of equations, figures and tables added to a document. Similar to a table of contents, a Table of Figures lists, sorts out and arranges captioned objects or text headings that have a certain style applied. This makes it easy to reference them in your document and to navigate between figures. Click the link in the Table of Figures formatted as links and you will be taken directly to the figure or the heading. In the Document Editor, any table, equation, diagram, drawing, graph, chart, map, photograph or another type of illustration is presented as a figure. To add a Table of Figures go to the References tab and use the Table of Figures toolbar button to set up and format a table of figures. Use the Refresh button to update a table of figures each time you add a new figure to your document. Creating a Table of Figures Note: You can create a Table of Figures using either captioned figures or styles. Before proceeding, a caption must be added to each equation, table or figure, or a style must be applied to the text so that it is correctly included in a Table of Figures. Once you have added captions or styles, place your cursor where you want to inset a Table of Figures and go to the References tab then click the Table of Figures button to open the Table of Figures dialog box, and generate the list of figures. Choose an option to build a Table of Figures from the Caption or Style group. You can create a Table of Figures based on captioned objects. Check the Caption box and select a captioned object from the drop-down list: None; Equation; Figure; Table. You can create a Table of Figures based on the styles used to format text. Check the Style box and select a style from the drop-down list. The list of options may vary depending on the style applied: Heading 1; Heading 2; Caption; Table of Figures; Normal. Formatting a Table of Figures The check box options allow you to format a Table of Figures. All formatting check boxes are activated by default as in most cases it is more reasonable to have them. Uncheck the boxes you don’t need. Show page numbers - to display the page number the figure appears on; Right align page numbers - to display page numbers on the right when Show page numbers is active; uncheck it to display page numbers right after the title; Format table of figures as links - to add hyperlinks to the Table of Figures; Include label and number - to add a label and number to the Table of Figures. Choose the Leader style from the drop-down list to connect titles to page numbers for a better visualization. Customize the table of figures text styles by choosing one of the available styles from the drop-down list: Current - displays the style chosen previously. Simple - highlights text in bold. Online - highlights and arranges text as a hyperlink. Classic - makes the text all caps. Distinctive - highlights text in italic. Centered - centers the text and displays no leader. Formal - displays text in 11 pt Arial to give a more formal look. Preview window displays how the Table of Figures appears in the document or when printed. Updating a Table of Figures Update a Table of Figures each time you add a new equation, figure or table to your document.The Refresh button becomes active when you click or select the Table of Figures. Click the Refresh button on the References tab of the top toolbar and select the necessary option from the menu: Refresh page numbers only - to update page numbers without applying changes to the headings. Refresh entire table - to update all the headings that have been modified and page numbers. Click OK to confirm your choice or Right-click the Table of Figures in your document to open the contextual menu, then choose the Refresh field to update the Table of Figures."
    },
   {
        "id": "UsageInstructions/AddWatermark.htm", 
        "title": "Add watermark", 
        "body": "s A watermark is a text or image placed under the main text layer. Text watermarks allow indicating the status of your document (for example, confidential, draft etc.). Image watermarks allow adding an image, for example, the logo of your company. To add a watermark in the Document Editor: Switch to the Layout tab of the top toolbar. Click the Watermark icon on the top toolbar and choose the Custom Watermark option from the menu. After that the Watermark Settings window will appear. Select a watermark type you wish to insert: Use the Text watermark option and adjust the available parameters: Language - select the watermark language. Languages supported for watermarking: English, French, German, Italian, Japanese, Mandarin Chinese, Russian, Spanish. Text - select one of the available text examples in the selected language. For English, the following watermark texts are available: ASAP, CONFIDENTIAL, COPY, DO NOT COPY, DRAFT, ORIGINAL, PERSONAL, SAMPLE, TOP SECRET, URGENT. Font - select the font name and size from the corresponding drop-down lists. Use the icons on the right to set the font color or apply one of the font decoration styles: Bold, Italic, Underline, Strikeout. Semitransparent - check this box if you want to apply transparency. Layout - select the Diagonal or Horizontal option. Use the Image watermark option and adjust the available parameters: Choose the image file source using one of the options from the drop-down list: From File, From URL or From Storage - the image will be displayed in the preview window on the right, Scale - select the necessary scale value from the available ones: Auto, 500%, 200%, 150%, 100%, 50%. Click the OK button. To edit the added watermark, open the Watermark Settings window as described above, change the necessary parameters and click OK. To delete the added watermark click the Watermark icon on the Layout tab of the top toolbar and choose the Remove Watermark option from the menu. It's also possible to use the None option in the Watermark Settings window."
    },
   {
        "id": "UsageInstructions/AlignArrangeObjects.htm", 
        "title": "Align and arrange objects on a page", 
        "body": "Align and arrange objects on the page In the Document Editor, the added autoshapes, images, charts or text boxes can be aligned, grouped and ordered on the page. To perform any of these actions, first select a separate object or several objects on the page. To select several objects, hold down the Ctrl key and left-click the required objects. To select a text box, click on its border, not the text within it. After that you can use either the icons on the Layout tab of the top toolbar described below or the corresponding options from the right-click menu. Align objects To align two or more selected objects, Click the Align icon on the Layout tab of the top toolbar and select one of the following options: Align to Page to align objects relative to the edges of the page, Align to Margin to align objects relative to the page margins, Align Selected Objects (this option is selected by default) to align objects relative to each other, Click the Align icon once again and select the necessary alignment type from the list: Align Left - to line up the objects horizontally by the left edge of the leftmost object/left edge of the page/left page margin, Align Center - to line up the objects horizontally by their centers/center of the page/center of the space between the left and right page margins, Align Right - to line up the objects horizontally by the right edge of the rightmost object/right edge of the page/right page margin, Align Top - to line up the objects vertically by the top edge of the topmost object/top edge of the page/top page margin, Align Middle - to line up the objects vertically by their middles/middle of the page/middle of the space between the top and bottom page margins, Align Bottom - to line up the objects vertically by the bottom edge of the bottommost object/bottom edge of the page/bottom page margin. Alternatively, you can right-click the selected objects, choose the Align option from the contextual menu and then use one of the available alignment options. If you want to align a single object, it can be aligned relative to the edges of the page or to the page margins. The Align to Margin option is selected by default in this case. Distribute objects To distribute three or more selected objects horizontally or vertically so that there is equal space between them, Click the Align icon on the Layout tab of the top toolbar and select one of the following options: Align to Page to distribute objects between the edges of the page, Align to Margin to distribute objects between the page margins, Align Selected Objects (this option is selected by default) to distribute objects between two outermost selected objects, Click the Align icon once again and select the necessary distribution type from the list: Distribute Horizontally - to distribute objects evenly between the leftmost and rightmost selected objects/left and right edges of the page/left and right page margins. Distribute Vertically - to distribute objects evenly between the topmost and bottommost selected objects/top and bottom edges of the page/top and bottom page margins. Alternatively, you can right-click the selected objects, choose the Align option from the contextual menu and then use one of the available distribution options. Note: the distribution options are disabled if you select less than three objects. Group objects To group two or more selected objects or ungroup them, click the arrow next to the Group icon at the Layout tab on the top toolbar and select the necessary option from the list: Group - to combine several objects into a group so that they can be simultaneously rotated, moved, resized, aligned, arranged, copied, pasted, formatted like a single object. Ungroup - to ungroup the selected group of the previously combined objects. Alternatively, you can right-click the selected objects, choose the Arrange option from the contextual menu and then use the Group or Ungroup option. Note: the Group option is disabled if you select less than two objects. The Ungroup option is available only when a group of the previously combined objects is selected. Arrange objects To arrange objects (i.e. to change their order when several objects overlap each other), you can use the Bring Forward and Send Backward icons on the Layout tab of the top toolbar and select the required arrangement type from the list. To move the selected object(s) forward, click the arrow next to the Bring Forward icon on the Layout tab of the top toolbar and select the required arrangement type from the list: Bring To Foreground - to move the object(s) in front of all other objects, Bring Forward - to move the selected object(s) by one level forward as related to other objects. To move the selected object(s) backward, click the arrow next to the Send Backward icon on the Layout tab of the top toolbar and select the required arrangement type from the list: Send To Background - to move the object(s) behind all other objects, Send Backward - to move the selected object(s) by one level backward as related to other objects. Alternatively, you can right-click the selected object(s), choose the Arrange option from the contextual menu and then use one of the available arrangement options."
    },
   {
        "id": "UsageInstructions/AlignText.htm", 
        "title": "Align your text in a paragraph", 
        "body": "The text is commonly aligned in four ways: left-aligned text, right-aligned text, centered text or justified text. To align the text in the Document Editor, place the cursor to the position where you want the alignment to be applied (this can be a new line or already entered text), switch to the Home tab of the top toolbar, select the alignment type you would like to apply: Left alignment (when the text is lined up to the left side of the page with the right side remaining unaligned) is done by clicking the Align left icon on the top toolbar. Center alignment (when the text is lined up in the center of the page with the right and the left sides remaining unaligned) is done by clicking the Align center icon on the top toolbar. Right alignment (when the text is lined up to the right side of the page with the left side remaining unaligned) is done by clicking the Align right icon on the top toolbar. Justified alignment (when the text is lined up to both the left and the right sides of the page, and additional spacing is added where necessary to keep the alignment) is done by clicking the Justified icon on the top toolbar. The alignment parameters are also available in the Paragraph - Advanced Settings window: right-click the text and choose the Paragraph - Advanced Settings option from the contextual menu or use the Show advanced settings option on the right sidebar, open the Paragraph - Advanced Settings window, switch to the Indents &amp; Spacing tab, select one of the alignment types from the Alignment list: Left, Center, Right, Justified, click the OK button to apply the changes."
    },
   {
        "id": "UsageInstructions/BackgroundColor.htm", 
        "title": "Select background color for a paragraph", 
        "body": "Select a background color for a paragraph A background color is applied to the whole paragraph and completely fills all the paragraph space from the left page margin to the right page margin. To apply a background color to a certain paragraph or change the current one in the Document Editor, select a color scheme for your document from the available ones clicking the Change color scheme icon at the Home tab on the top toolbar place the cursor within the required paragraph, or select several paragraphs with the mouse or the whole text using the Ctrl+A key combination open the color palettes window. You can access it in one of the following ways: click the downward arrow next to the icon on the Home tab of the top toolbar, or click the color field next to the Background Color caption on the right sidebar, or click the 'Show advanced settings' link on the right sidebar or select the 'Paragraph Advanced Settings' option on the right-click menu, then switch to the 'Borders &amp; Fill' tab within the 'Paragraph - Advanced Settings' window and click the color field next to the Background Color caption. select any color among the available palettes After you select the required color by using the icon, you'll be able to apply this color to any selected paragraph just by clicking the icon (it displays the selected color), without having to choose this color in the palette again. If you use the Background Color option on the right sidebar or within the 'Paragraph - Advanced Settings' window, remember that the selected color is not retained for quick access. (These options can be useful if you wish to select a different background color for a specific paragraph and if you are also using some general color selected by clicking the icon). To remove the background color from a certain paragraph, place the cursor within the required paragraph, or select several paragraphs with the mouse or the whole text using the Ctrl+A key combination open the color palettes window by clicking the color field next to the Background Color caption on the right sidebar select the icon."
    },
   {
        "id": "UsageInstructions/ChangeColorScheme.htm", 
        "title": "Change color scheme", 
        "body": "Color schemes are applied to the whole document. In the Document Editor, you can quickly change the appearance of your document because they define the Theme Colors palette for different document elements (font, background, tables, autoshapes, charts). If you applied some Theme Colors to the document elements and then select a different Color Scheme, the applied colors in your document will change correspondingly. To change a color scheme, click the downward arrow next to the Change color scheme icon on the Home tab of the top toolbar and select the required color scheme from the list: New Office, Office, Grayscale, Apex, Aspect, Civic, Concourse, Equity, Flow, Foundry, Median, Metro, Module, Odulent, Oriel, Origin, Paper, Solstice, Technic, Trek, Urban, Verve. The selected color scheme will be highlighted in the list. Once you select the preferred color scheme, you can select other colors in the color palettes window that corresponds to the document element you want to apply the color to. For most document elements, the color palettes window can be accessed by clicking the colored box on the right sidebar when the required element is selected. For the font, this window can be opened using the downward arrow next to the Font color icon on the Home tab of the top toolbar. The following palettes are available: Theme Colors - the colors that correspond to the selected color scheme of the document. Standard Colors - a set of default colors. The selected color scheme does not affect them. Custom Color - click this caption if the required color is missing among the available palettes. Select the necessary color range moving the vertical color slider and set a specific color dragging the color picker within the large square color field. Once you select a color with the color picker, the appropriate RGB and sRGB color values will be displayed in the fields on the right. You can also define a color on the base of the RGB color model by entering the corresponding numeric values into the R, G, B (red, green, blue) fields or enter the sRGB hexadecimal code into the field marked with the # sign. The selected color appears in the New preview box. If the object was previously filled with any custom color, this color is displayed in the Current box so you can compare the original and modified colors. When the color is defined, click the Add button: The custom color will be applied to the selected element and added to the Custom color palette."
    },
   {
        "id": "UsageInstructions/ChangeWrappingStyle.htm", 
        "title": "Change text wrapping", 
        "body": "Change the text wrapping The Wrapping Style option determines the way the object is positioned relative to the text. In the Document Editor, you can change the text wrapping style for inserted objects, such as shapes, images, charts, text boxes or tables. Change text wrapping for shapes, images, charts, text boxes To change the currently selected wrapping style: left-click a separate object to select it. To select a text box, click on its border, not the text within it. open the text wrapping settings: switch to the the Layout tab of the top toolbar and click the arrow next to the Wrapping icon, or right-click the object and select the Wrapping Style option from the contextual menu, or right-click the object, select the Advanced Settings option and switch to the Text Wrapping tab of the object Advanced Settings window. select the necessary wrapping style: Inline - the object is considered to be a part of the text, like a character, so when the text moves, the object moves as well. In this case the positioning options are inaccessible. If one of the following styles is selected, the object can be moved independently of the text and precisely positioned on the page: Square - the text wraps the rectangular box that bounds the object. Tight - the text wraps the actual object edges. Through - the text wraps around the object edges and fills the open white space within the object. To apply this effect, use the Edit Wrap Boundary option from the right-click menu. Top and bottom - the text is only above and below the object. In front - the object overlaps the text. Behind - the text overlaps the object. If you select the Square, Tight, Through, or Top and bottom style, you will be able to set up some additional parameters - Distance from Text at all sides (top, bottom, left, right). To access these parameters, right-click the object, select the Advanced Settings option and switch to the Text Wrapping tab of the object Advanced Settings window. Set the required values and click OK. If you select a wrapping style other than Inline, the Position tab is also available in the object Advanced Settings window. To learn more on these parameters, please refer to the corresponding pages with the instructions on how to work with shapes, images or charts. If you select a wrapping style other than Inline, you can also edit the wrap boundary for images or shapes. Right-click the object, select the Wrapping Style option from the contextual menu and click the Edit Wrap Boundary option. You can also use the Wrapping -> Edit Wrap Boundary menu on the Layout tab of the top toolbar. Drag wrap points to customize the boundary. To create a new wrap point, click anywhere on the red line and drag it to the required position. Change text wrapping for tables For tables, the following two wrapping styles are available: Inline table and Flow table. To change the currently selected wrapping style: right-click the table and select the Table Advanced Settings option, switch to the Text Wrapping tab of the Table - Advanced Settings window, select one of the following options: Inline table is used to select the wrapping style when the text is broken by the table as well as to set the alignment: left, center, right. Flow table is used to select the wrapping style when the text is wrapped around the table. Using the Text Wrapping tab of the Table - Advanced Settings window, you can also set up the following additional parameters: For inline tables, you can set the table Alignment type (left, center or right) and Indent from left. For floating tables, you can set the Distance from text and the table position on the Table Position tab."
    },
   {
        "id": "UsageInstructions/CommunicationPlugins.htm", 
        "title": "Communicate while editing", 
        "body": "In ONLYOFFICE Document Editor, you can always keep in touch with colleagues and use popular online messengers, such as Telegram and Rainbow. Telegram and Rainbow plugins are not installed by default. To find information on how to install them, please, refer to the corresponding article: Adding plugins to the ONLYOFFICE Desktop Editors Adding plugins to ONLYOFFICE Cloud, or Adding new plugins to server editors . Telegram To start chatting in the Telegram plugin, Switch to the Plugins tab and click Telegram, enter your phone number into the corresponding field, check the Keep me signed in checkbox if you want to save credentials for the current session and click the Next button, enter the code you've received in your Telegram app, or log in using the QR code, open Telegram app on your phone, go to Settings > Devices > Scan QR, scan the image to Log in. Now you can use Telegram for instant messaging within ONLYOFFICE editors interface. Rainbow To start chatting in the Rainbow plugin, Switch to the Plugins tab and click Rainbow, register a new account by clicking the Sign up button, or log into an already created one. To do this, enter your email into the corresponding field and click Continue, then enter your account password, check the Keep my session alive checkbox if you want to save credentials for the current session, and click the Connect button. Now you're all set and can simultaneously chat in Rainbow and work within ONLYOFFICE editors interface."
    },
   {
        "id": "UsageInstructions/ConvertFootnotesEndnotes.htm", 
        "title": "Convert footnotes and endnotes", 
        "body": "The ONLYOFFICE Document Editor allows you to quickly convert footnotes to endnotes, and vice versa, e.g., if you see that some footnotes in the resulting document should be placed in the end. Instead of recreating them as endnotes, use the corresponding tool for effortless conversion. Click the arrow next to the Footnote icon on the References tab located at the top toolbar, Hover over the Convert all notes menu item and choose one of the options from the list to the right: Convert all Footnotes to Endnotes to change all footnotes into endnotes; Convert all Endnotes to Footnotes to change all endnotes to footnotes; Swap Footnotes and Endnotes to change all endnotes to footnotes, and all footnotes to endnotes."
    },
   {
        "id": "UsageInstructions/CopyClearFormatting.htm", 
        "title": "Copy/clear text formatting", 
        "body": "To copy a certain text formatting in the Document Editor, select the text passage whose formatting you need to copy with the mouse or using the keyboard, click the Copy style icon on the Home tab of the top toolbar (the mouse pointer will look like this ), select the required text passage to apply the same formatting. To apply the copied formatting to multiple text passages, select the text passage whose formatting you need to copy with the mouse or use the keyboard, double-click the Copy style icon on the Home tab of the top toolbar (the mouse pointer will look like this and the Copy style icon will remain selected: ), select the necessary text passages one by one to apply the same formatting to each of them, to exit this mode, click the Copy style icon once again or press the Esc key on the keyboard. To quickly remove the applied formatting from your text, select the text passage whose formatting you want to remove, click the Clear style icon on the Home tab of the top toolbar."
    },
   {
        "id": "UsageInstructions/CopyPasteUndoRedo.htm", 
        "title": "Copy/paste text passages, undo/redo your actions", 
        "body": "Use basic clipboard operations To cut, copy and paste text passages and inserted objects (autoshapes, images, charts) in the Document Editor, select the corresponding options from the right-click menu or click the icons located on any tab of the top toolbar: Cut – select a text fragment or an object and use the Cut option from the right-click menu, or the Cut icon on the top toolbar to delete the selected text and send it to the computer clipboard memory. The cut text can be later inserted to another place in the same document. Copy – select a text fragment or an object and use the Copy option from the right-click menu, or the Copy icon on the top toolbar to copy the selected text to the computer clipboard memory. The copied text can be later inserted to another place in the same document. Paste – find the place in your document where you need to paste the previously copied text fragment/object and use the the Paste option from the right-click menu, or the Paste icon on the top toolbar. The copied text/object will be inserted to the current cursor position. The data can be previously copied from the same document. In the online version, the key combinations below are only used to copy or paste data from/into another document or a program. In the desktop version, both corresponding buttons/menu options and key combinations can be used for any copy/paste operations: Ctrl+X key combination for cutting; Ctrl+C key combination for copying; Ctrl+V key combination for pasting. Note: instead of cutting and pasting text fragments in the same document, you can just select the required text passage and drag and drop it to the necessary position. Use the Paste Special feature Note: For collaborative editing, the Paste Special feature is available in the Strict co-editing mode only. Once the copied text is pasted, the Paste Special button appears next to the inserted text passage. Click this button to select the necessary paste option or use the Ctrl key in combination with the letter key given in the brackets next to the required option. When pasting a text paragraph or some text within autoshapes, the following options are available: Keep source formatting (Ctrl+K) - allows pasting the copied text keeping its original formatting. Keep text only (Ctrl+T) - allows pasting the text without its original formatting. If you copy a table and paste it into an already existing table, the following options are available: Overwrite cells (Ctrl+O) - allows replacing the contents of the existing table with the copied data. This option is selected by default. Nest table (Ctrl+N) - allows pasting the copied table as a nested table into the selected cell of the existing table. Keep text only (Ctrl+T) - allows pasting the table contents as text values separated by the tab character. To enable / disable the automatic appearance of the Paste Special button after pasting, go to the File tab > Advanced Settings and check / uncheck the Show the Paste Options button when the content is pasted checkbox. Undo/redo your actions To perform undo/redo operations, click the corresponding icons in the editor header or use the following keyboard shortcuts: Undo – use the Undo icon on the left side of the editor header or the Ctrl+Z key combination to undo the last operation you performed. Redo – use the Redo icon on the left part of the editor header or the Ctrl+Y key combination to redo the last undone operation. When you co-edit a document in the Fast mode, the possibility to Redo the last undone operation is not available."
    },
   {
        "id": "UsageInstructions/CreateFillableForms.htm", 
        "title": "Create fillable forms", 
        "body": "ONLYOFFICE Document Editor allows you to effortlessly create fillable forms in your documents, e.g. agreement drafts or surveys. Form Template is DOCXF format that offers a range of tools to create a fillable form. Save the resulting form as a DOCXF file, and you will have a form template you can still edit, revise or collaborate on. To make a Form template fillable and to restrict file editing by other users, save it as an OFORM file. Please refer to form filling instructions for further details. DOCXF and OFORM are new ONLYOFFICE formats that allow to create form templates and fill out forms. Use ONLYOFFICE Document Editor either online or desktop to make full use of form-associated elements and options. You can also save any existing DOCX file as a DOCXF to use it as Form template. Go to the File tab, click the Download as... or Save as... option on the left side menu and choose the DOCXF icon. Now you can use all the available form editing functions to create a form. It is not only the form fields that you can edit in a DOCXF file, you can still add, edit and format text or use other Document Editor functions. Creating fillable forms is enabled through user-editable objects that ensure overall consistency of the resulting documents and allow for advanced form interaction experience. Currently, you can insert editable plain text fields, combo boxes, dropdown lists, checkboxes, radio buttons, assign designated areas for images, as well as create email address, phone number, date and time, zip code, credit card and complex fields. Access these features on the Forms tab that is available for DOCXF files only. Creating a new Plain Text Field Text fields are user-editable plain text form fields; no other objects can be added. To insert a text field, place the insertion point within a line of the text where you want the field to be added, switch to the Forms tab of the top toolbar, click the Text Field icon or click the arrow next to the Text Field icon and choose whether you want to insert an inline text field or a fixed text field. To learn more about fixed field, please read the Fixed size field paragraph of this section below. The form field will appear at the insertion point within the existing text line. The Form Settings menu will open to the right. Who needs to fill this out?: choose the role via the dropdown list to set the group of users with access to this field. To learn more about assigning roles, please read the Managing Roles section of this guide. Key: a key to group fields to fill out simultaneously. To create a new key, enter its name in the field and press Enter, then assign the required key to each text field using the dropdown list. A message Fields connected: 2/3/... will be displayed. To disconnect the fields, click the Disconnect button. Placeholder: type in the text to be displayed in the inserted text field; “Your text here” is set by default. Tag: type in the text to be used as a tag for internal use, i.e., displayed only for co-editors. Tip: type in the text to be displayed as a tip when a user hovers their mouse pointer over the text field. Format: choose the content format of the text field, i.e., only the chosen character format will be allowed: None, Digits, Letters, Arbitrary Mask (the text shall correspond with the custom mask, e.g., (999) 999 99 99), Regular Expression (the text shall correspond with the custom expression). When you choose an Arbitrary Mask or a Regular Expression format, an additional field below the Format field appears. Allowed Symbols: type in the symbols that are allowed in the text field. Fixed size field: check this box to create a field with a fixed size. When this option is enabled, you can also use the AutoFit and/or Multiline field settings. A fixed size field looks like an autoshape. You can set a wrapping style for it as well as adjust its position. AutoFit: this option can be enabled when the Fixed size field setting is selected, check it to automatically fit the font size to the field size. Multiline field: this option can be enabled when the Fixed size field setting is selected, check it to create a form field with multiple lines, otherwise, the text will occupy a single line. Characters limit: no limits by default; check this box to set the maximum characters number in the field to the right. Comb of characters: spread the text evenly within the inserted text field and configure its general appearance. Leave the box unchecked to preserve the default settings or check it to set the following parameters: Cell width: choose whether the width value should be Auto (width is calculated automatically), At least (width is no less than the value given manually), or Exactly (width corresponds to the value given manually). The text within will be justified accordingly. Border color: click the icon to set the color for the borders of the inserted text field. Choose the preferred border color from the palette. You can add a new custom color if necessary. Background color: click the icon to apply a background color to the inserted text field. Choose the preferred color out of Theme Colors, Standard Colors, or add a new custom color if necessary. Required: check this box to make the text field a necessary one to fill in. Click within the inserted text field and adjust the font type, size, color, apply decoration styles and formatting presets. Formatting will be applied to all the text inside the field. Creating a new Combo box Combo boxes contain a dropdown list with a set of choices that can be edited by users. To insert a combo box, place the insertion point within a line of the text where you want the field to be added, switch to the Forms tab of the top toolbar, click the Combo box icon. The form field will appear at the insertion point within the existing text line. The Form Settings menu will open to the right. Who needs to fill this out?: choose the role via the dropdown list to set the group of users with access to this field. To learn more about assigning roles, please read the Managing Roles section of this guide. Key: a key to group combo boxes to fill out simultaneously. To create a new key, enter its name in the field and press Enter, then assign the required key to each combo box using the dropdown list. A message Fields connected: 2/3/... will be displayed. To disconnect the fields, click the Disconnect button. Placeholder: type in the text to be displayed in the inserted combo box; “Choose an item” is set by default. Tag: type in the text to be used as a tag for internal use, i.e., displayed only for co-editors. Tip: type in the text to be displayed as a tip when a user hovers their mouse pointer over the form field. Value Options: add new values, delete them, or move them up and down in the list. Fixed size field: check this box to create a field with a fixed size. A fixed size field looks like an autoshape. You can set a wrapping style for it as well as adjust its position. Border color: click the icon to set the color for the borders of the inserted combo box. Choose the preferred border color from the palette. You can add a new custom color if necessary. Background color: click the icon to apply a background color to the inserted combo box. Choose the preferred color out of Theme Colors, Standard Colors, or add a new custom color if necessary. Required: check this box to make the combo box field a necessary one to fill in. You can click the arrow button in the right part of the added Combo box to open the item list and choose the necessary one. Once the necessary item is selected, you can edit the displayed text entirely or partially by replacing it with yours. You can change font decoration, color, and size. Click within the inserted combo box and proceed according to the instructions. Formatting will be applied to all the text inside the field. Creating a new Dropdown list form field Dropdown lists contain a list with a set of choices that cannot be edited by the users. To insert a dropdown list, place the insertion point within a line of the text where you want the field to be added, switch to the Forms tab of the top toolbar, click the Dropdown icon. The form field will appear at the insertion point within the existing text line. The Form Settings menu will open to the right. Who needs to fill this out?: choose the role via the dropdown list to set the group of users with access to this field. To learn more about assigning roles, please read the Managing Roles section of this guide. Key: a key to group dropdown lists to fill out simultaneously. To create a new key, enter its name in the field and press Enter, then assign the required key to each form field using the dropdown list. A message Fields connected: 2/3/... will be displayed. To disconnect the fields, click the Disconnect button. Placeholder: type in the text to be displayed in the inserted dropdown list; “Choose an item” is set by default. Tag: type in the text to be used as a tag for internal use, i.e., displayed only for co-editors. Tip: type in the text to be displayed as a tip when a user hovers their mouse pointer over the form field. Value Options: add new values, delete them, or move them up and down in the list. Fixed size field: check this box to create a field with a fixed size. A fixed size field looks like an autoshape. You can set a wrapping style for it as well as adjust its position. Border color: click the icon to set the color for the borders of the inserted dropdown field. Choose the preferred border color from the palette. You can add a new custom color if necessary. Background color: click the icon to apply a background color to the inserted dropdown field. Choose the preferred color out of Theme Colors, Standard Colors, or add a new custom color if necessary. Required: check this box to make the dropdown list field a necessary one to fill in. You can click the arrow button in the right part of the added Dropdown list form field to open the item list and choose the necessary one. Creating a new Checkbox Checkboxes are used to provide users with a variety of options, any number of which can be selected. Checkboxes operate individually, so they can be checked or unchecked independently. To insert a checkbox, place the insertion point within a line of the text where you want the field to be added, switch to the Forms tab of the top toolbar, click the Checkbox icon. The form field will appear at the insertion point within the existing text line. The Form Settings menu will open to the right. Who needs to fill this out?: choose the role via the dropdown list to set the group of users with access to this field. To learn more about assigning roles, please read the Managing Roles section of this guide. Key: a key to group checkboxes to fill out simultaneously. To create a new key, enter its name in the field and press Enter, then assign the required key to each form field using the dropdown list. A message Fields connected: 2/3/... will be displayed. To disconnect the fields, click the Disconnect button. Tag: type in the text to be used as a tag for internal use, i.e., displayed only for co-editors. Tip: type in the text to be displayed as a tip when a user hovers their mouse pointer over the checkbox. Fixed size field: check this box to create a field with a fixed size. A fixed size field looks like an autoshape. You can set a wrapping style for it as well as adjust its position. Border color: click the icon to set the color for the borders of the inserted checkbox. Choose the preferred border color from the palette. You can add a new custom color if necessary. Background color: click the icon to apply a background color to the inserted checkbox. Choose the preferred color out of Theme Colors, Standard Colors, or add a new custom color if necessary. Required: check this box to make the checkbox field a necessary one to fill in. To check the box, click it once. Creating a new Radio Button Radio buttons are used to provide users with a variety of options, only one of which can be selected. Radio buttons can be grouped so that there is no selecting several buttons within one group. To insert a radio button, place the insertion point within a line of the text where you want the field to be added, switch to the Forms tab of the top toolbar, click the Radio Button icon. The form field will appear at the insertion point within the existing text line. The Form Settings menu will open to the right. Who needs to fill this out?: choose the role via the dropdown list to set the group of users with access to this field. To learn more about assigning roles, please read the Managing Roles section of this guide. Group key: to create a new group of radio buttons, enter the name of the group in the field and press Enter, then assign the required group to each radio button. Tag: type in the text to be used as a tag for internal use, i.e., displayed only for co-editors. Tip: type in the text to be displayed as a tip when a user hovers their mouse pointer over the radio button. Fixed size field: check this box to create a field with a fixed size. A fixed size field looks like an autoshape. You can set a wrapping style for it as well as adjust its position. Border color: click the icon to set the color for the borders of the inserted radi button. Choose the preferred border color from the palette. You can add a new custom color if necessary. Background color: click the icon to apply a background color to the inserted radio button. Choose the preferred color out of Theme Colors, Standard Colors, or add a new custom color if necessary. Required: check this box to make the radio button field a necessary one to fill in. To check the radio button, click it once. Creating a new Image field Images are form fields which are used to enable inserting an image with the limitations you set, i.e. the location of the image or its size. To insert an image form field, place the insertion point within a line of the text where you want the field to be added, switch to the Forms tab of the top toolbar, click the Image icon. The form field will appear at the insertion point within the existing text line. The Form Settings menu will open to the right. Who needs to fill this out?: choose the role via the dropdown list to set the group of users with access to this field. To learn more about assigning roles, please read the Managing Roles section of this guide. Key: a key to group images to fill out simultaneously. To create a new key, enter its name in the field and press Enter, then assign the required key to each form field using the dropdown list. A message Fields connected: 2/3/... will be displayed. To disconnect the fields, click the Disconnect button. Placeholder: type in the text to be displayed in the inserted image form field; “Click to load image” is set by default. Tag: type in the text to be used as a tag for internal use, i.e., displayed only for co-editors. Tip: type in the text to be displayed as a tip when a user hovers their mouse pointer over the bottom border of the image. When to scale: click the drop down menu and select an appropriate image sizing option: Always, Never, when the Image is Too Big, or when the Image is Too Small. The selected image will scale inside the field correspondingly. Lock aspect ratio: check this box to maintain the image aspect ratio without distortion. When the box is checked, use the vertical and the horizontal slider to position the image inside the inserted field. The positioning sliders are inactive when the box is unchecked. Select Image: click this button to upload an image either From File, From URL, or From Storage. Border color: click the icon to set the color for the borders of the inserted image field. Choose the preferred border color from the palette. You can add a new custom color if necessary. Background color: click the icon to apply a background color to the inserted image field. Choose the preferred color out of Theme Colors, Standard Colors, or add a new custom color if necessary. Required: check this box to make the image field a necessary one to fill in. To replace the image, click the   image icon above the form field border and select another one. To adjust the image settings, open the Image Settings tab on the right toolbar. To learn more, please read the guide on image settings. Creating a new Email Address field Email Address field is used to type in an email address corresponding to a regular expression \\S+@\\S+\\.\\S+. To insert an email address field, place the insertion point within a line of the text where you want the field to be added, switch to the Forms tab of the top toolbar, click the Email Address icon. The form field will appear at the insertion point within the existing text line. The Form Settings menu will open to the right. Who needs to fill this out?: choose the role via the dropdown list to set the group of users with access to this field. To learn more about assigning roles, please read the Managing Roles section of this guide. Key: to create a new group of email addresses, enter the name of the group in the field and press Enter, then assign the required group to each email address field. Placeholder: type in the text to be displayed in the inserted email address form field; “<EMAIL>” is set by default. Tag: type in the text to be used as a tag for internal use, i.e., displayed only for co-editors. Tip: type in the text to be displayed as a tip when a user hovers their mouse pointer over the email address field. Format: choose the content format of the field, i.e., None, Digits, Letters, Arbitrary Mask or Regular Expression. The field is set to Regular Expression by default so as to preserve the email address format \\S+@\\S+\\.\\S+. Allowed Symbols: type in the symbols that are allowed in the email address field. Fixed size field: check this box to create a field with a fixed size. When this option is enabled, you can also use the Autofit and/or Multiline field settings. A fixed size field looks like an autoshape. You can set a wrapping style for it as well as adjust its position. AutoFit: this option can be enabled when the Fixed size field setting is selected, check it to automatically fit the font size to the field size. Multiline field: this option can be enabled when the Fixed size field setting is selected, check it to create a form field with multiple lines, otherwise, the text will occupy a single line. Characters limit: no limits by default; check this box to set the maximum characters number in the field to the right. Comb of characters: spread the text evenly within the inserted email address field and configure its general appearance. Leave the box unchecked to preserve the default settings or check it to set the following parameters: Cell width: choose whether the width value should be Auto (width is calculated automatically), At least (width is no less than the value given manually), or Exactly (width corresponds to the value given manually). The text within will be justified accordingly. Border color: click the icon to set the color for the borders of the inserted email address field. Choose the preferred border color from the palette. You can add a new custom color if necessary. Background color: click the icon to apply a background color to the inserted email address field. Choose the preferred color out of Theme Colors, Standard Colors, or add a new custom color if necessary. Required: check this box to make the email address field a necessary one to fill in. Creating a new Phone Number field Phone Number field is used to type in a phone number corresponding to an arbitrary mask given by the form creator. It is set to (999)999-9999 by default. To insert a phone number field, place the insertion point within a line of the text where you want the field to be added, switch to the Forms tab of the top toolbar, click the Phone Number icon. The form field will appear at the insertion point within the existing text line. The Form Settings menu will open to the right. Who needs to fill this out?: choose the role via the dropdown list to set the group of users with access to this field. To learn more about assigning roles, please read the Managing Roles section of this guide. Key: to create a new group of phone numbers, enter the name of the group in the field and press Enter, then assign the required group to each phone number. Placeholder: type in the text to be displayed in the inserted phone number form field; “(999)999-9999” is set by default. Tag: type in the text to be used as a tag for internal use, i.e., displayed only for co-editors. Tip: type in the text to be displayed as a tip when a user hovers their mouse pointer over the phone number field. Format: choose the content format of the field, i.e., None, Digits, Letters, Arbitrary Mask or Regular Expression. The field is set to Arbitrary Mask by default. To change its format, type in the required mask into the field below. Allowed Symbols: type in the symbols that are allowed in the phone number field. Fixed size field: check this box to create a field with a fixed size. When this option is enabled, you can also use the AutoFit and/or Multiline field settings. A fixed size field looks like an autoshape. You can set a wrapping style for it as well as adjust its position. AutoFit: this option can be enabled when the Fixed size field setting is selected, check it to automatically fit the font size to the field size. Multiline field: this option can be enabled when the Fixed size field setting is selected, check it to create a form field with multiple lines, otherwise, the text will occupy a single line. Characters limit: no limits by default; check this box to set the maximum characters number in the field to the right. Comb of characters: spread the text evenly within the inserted phone number field and configure its general appearance. Leave the box unchecked to preserve the default settings or check it to set the following parameters: Cell width: choose whether the width value should be Auto (width is calculated automatically), At least (width is no less than the value given manually), or Exactly (width corresponds to the value given manually). The text within will be justified accordingly. Border color: click the icon to set the color for the borders of the inserted phone number field. Choose the preferred border color from the palette. You can add a new custom color if necessary. Background color: click the icon to apply a background color to the inserted phone number field. Choose the preferred color out of Theme Colors, Standard Colors, or add a new custom color if necessary. Required: check this box to make the phone number field a necessary one to fill in. Creating a new Date and Time field Date and Time field is used to insert a date. The date is set to DD-MM-YYYY by default. To insert a date and time field, place the insertion point within a line of the text where you want the field to be added, switch to the Forms tab of the top toolbar, click the Date and Time icon. The form field will appear at the insertion point within the existing text line. To enter a date, click the dropdown arrow within the field and choose the required date via the calendar. Creating a new Zip Code field Zip Code field is used to enter a zip code corresponding to an arbitrary mask given by the form creator. It is set to 99999-9999 by default. To insert a zip code field, place the insertion point within a line of the text where you want the field to be added, switch to the Forms tab of the top toolbar, click the Zip Code icon. The form field will appear at the insertion point within the existing text line. The Form Settings menu will open to the right. Who needs to fill this out?: choose the role via the dropdown list to set the group of users with access to this field. To learn more about assigning roles, please read the Managing roles section of this guide. Key: to create a new group of zip codes, enter the name of the group in the field and press Enter, then assign the required group to each zip code. Placeholder: type in the text to be displayed in the inserted zip code form field; “99999-9999” is set by default. Tag: type in the text to be used as a tag for internal use, i.e., displayed only for co-editors. Tip: type in the text to be displayed as a tip when a user hovers their mouse pointer over the zip code field. Format: choose the content format of the field, i.e., None, Digits, Letters, Arbitrary Mask or Regular Expression. The field is set to Arbitrary Mask by default. To change its format, type in the required mask into the field below. Allowed Symbols: type in the symbols that are allowed in the zip code field. Fixed size field: check this box to create a field with a fixed size. When this option is enabled, you can also use the AutoFit and/or Multiline field settings. A fixed size field looks like an autoshape. You can set a wrapping style for it as well as adjust its position. AutoFit: this option can be enabled when the Fixed size field setting is selected, check it to automatically fit the font size to the field size. Multiline field: this option can be enabled when the Fixed size field setting is selected, check it to create a form field with multiple lines, otherwise, the text will occupy a single line. Characters limit: no limits by default; check this box to set the maximum characters number in the field to the right. Comb of characters: spread the text evenly within the inserted zip code field and configure its general appearance. Leave the box unchecked to preserve the default settings or check it to set the following parameters: Cell width: choose whether the width value should be Auto (width is calculated automatically), At least (width is no less than the value given manually), or Exactly (width corresponds to the value given manually). The text within will be justified accordingly. Border color: click the icon to set the color for the borders of the inserted zip code field. Choose the preferred border color from the palette. You can add a new custom color if necessary. Background color: click the icon to apply a background color to the inserted zip code field. Choose the preferred color out of Theme Colors, Standard Colors, or add a new custom color if necessary. Required: check this box to make the zip code field a necessary one to fill in. Creating a new Credit Card field Credit Card field is used to enter a credit card number corresponding to an arbitrary mask given by the form creator. It is set to **************-9999 by default. To insert a credit card field, place the insertion point within a line of the text where you want the field to be added, switch to the Forms tab of the top toolbar, click the Credit Card icon. The form field will appear at the insertion point within the existing text line. The Form Settings menu will open to the right. Who needs to fill this out?: choose the role via the dropdown list to set the group of users with access to this field. To learn more about assigning roles, please read the Managing Roles section of this guide. Key: to create a new group of credit card numbers, enter the name of the group in the field and press Enter, then assign the required group to each credit card field. Placeholder: type in the text to be displayed in the inserted credit card form field; “**************-9999” is set by default. Tag: type in the text to be used as a tag for internal use, i.e., displayed only for co-editors. Tip: type in the text to be displayed as a tip when a user hovers their mouse pointer over the credit card field. Format: choose the content format of the field, i.e., None, Digits, Letters, Arbitrary Mask or Regular Expression. The field is set to Arbitrary Mask by default. To change its format, type in the required mask into the field below. Allowed Symbols: type in the symbols that are allowed in the credit card field. Fixed size field: check this box to create a field with a fixed size. When this option is enabled, you can also use the AutoFit and/or Multiline field settings. A fixed size field looks like an autoshape. You can set a wrapping style for it as well as adjust its position. AutoFit: this option can be enabled when the Fixed size field setting is selected, check it to automatically fit the font size to the field size. Multiline field: this option can be enabled when the Fixed size field setting is selected, check it to create a form field with multiple lines, otherwise, the text will occupy a single line. Characters limit: no limits by default; check this box to set the maximum characters number in the field to the right. Comb of characters: spread the text evenly within the inserted credit card field and configure its general appearance. Leave the box unchecked to preserve the default settings or check it to set the following parameters: Cell width: choose whether the width value should be Auto (width is calculated automatically), At least (width is no less than the value given manually), or Exactly (width corresponds to the value given manually). The text within will be justified accordingly. Border color: click the icon to set the color for the borders of the inserted credit card field. Choose the preferred border color from the palette. You can add a new custom color if necessary. Background color: click the icon to apply a background color to the inserted credit card field. Choose the preferred color out of Theme Colors, Standard Colors, or add a new custom color if necessary. Required: check this box to make the credit card field a necessary one to fill in. Creating a new Complex Field Complex Field combines several field types, e.g., text field and a drop-down list. You can combine fields however you need. To insert a complex field, place the insertion point within a line of the text where you want the field to be added, switch to the Forms tab of the top toolbar, click the Complex Field icon. The form field will appear at the insertion point within the existing text line. The Form Settings menu will open to the right. Who needs to fill this out?: choose the role via the dropdown list to set the group of users with access to this field. To learn more about assigning roles, please read the Managing Roles section of this guide. Key: to create a new group of complex fields, enter the name of the group in the field and press Enter, then assign the required group to each complex field. Placeholder: type in the text to be displayed in the inserted complex field; “Your text here” is set by default. Tag: type in the text to be used as a tag for internal use, i.e., displayed only for co-editors. Tip: type in the text to be displayed as a tip when a user hovers their mouse pointer over the complex field. Fixed size field: check this box to create a field with a fixed size. A fixed size field looks like an autoshape. You can set a wrapping style for it as well as adjust its position. Border color: click the icon to set the color for the borders of the inserted complex field. Choose the preferred border color from the palette. You can add a new custom color if necessary. Background color: click the icon to apply a background color to the inserted complex field. Choose the preferred color out of Theme Colors, Standard Colors, or add a new custom color if necessary. Required: check this box to make the complex field a necessary one to fill in. To insert various form fields in a complex field, click within it and choose the required field at the top toolbar in the Forms tab, then configure it to your liking. To learn more about each field type, read the corresponding sections above. Please note that you cannot use Image form field within complex fields. Managing Roles You can create new roles that will determine who can fill in certain form fields. To manage roles, go to the Forms tab in the top toolbar, click the Managing Roles icon, click the New button to create a new role, type in the role name and choose its color if necessary. You can also create a custom color by clicking the corresponding menu item, click OK to create a new role, set the order in which the fillers receive and sign the document using the and buttons, use the Edit or Delete buttons to change the roles or delete them, click Close to go back to form editing. When saving the form as .oform file, you can view all roles created for the form. Enabling the View form Note: Once you have entered the View form mode, all editing options will become unavailable. Click the View Form button on the Forms tab of the top toolbar to see how all the inserted forms will be displayed in your document. You can view the form from the point of view of each created role. To do that, click the arrow under the View Form button and choose the required role. To exit the viewing mode, click the same icon again. Moving form fields Form fields can be moved to another place in the document: click the button on the left of the control border to select the field and drag it without releasing the mouse button to another position in the text. You can also copy and paste form fields: select the necessary field and use the Ctrl+C/Ctrl+V key combinations. Creating required fields To make a field obligatory, check the Required option. The mandatory fields will be marked with red stroke. Locking form fields To prevent further editing of the inserted form field, click the Lock icon. Filling the fields remains available. Clearing form fields To clear all inserted fields and delete all values, click the Clear All Fields button on the Forms tab on the top toolbar. Clearing fields can be performed in the form filling mode only. Navigate, View and Save a Form Go to the Forms tab at the top toolbar. Navigate through the form fields using the Previous field and Next field buttons at the top toolbar. When you are finished, click the Save as oform button at the top toolbar to save the form as an OFORM file ready to be filled out. You can save as many OFORM files as you need. Removing form fields To remove a form field and leave all its contents, select it and click the Delete icon (make sure the field is not locked) or press the Delete key on the keyboard."
    },
   {
        "id": "UsageInstructions/CreateLists.htm", 
        "title": "Create lists", 
        "body": "To create a list in the Document Editor, place the cursor to the position where a list will be started (this can be a new line or the already entered text), switch to the Home tab of the top toolbar, select the list type you would like to start: Unordered list with markers is created using the Bullets icon on the top toolbar Ordered list with digits or letters is created using the Numbering icon on the top toolbar Click the downward arrow next to the Bullets or Numbering icon to select how the list is going to look like. each time you press the Enter key at the end of the line, a new ordered or unordered list item will appear. To stop that, press the Backspace key and keep on typing common text paragraphs. The program also creates numbered lists automatically when you enter digit 1 with a dot or a bracket and a space after it: 1., 1). Bulleted lists can be created automatically when you enter the -, * characters and a space after them. You can also change the text indentation in the lists and their nesting by clicking the Multilevel list , Decrease indent , and Increase indent icons on the Home tab of the top toolbar. To change the list level, click the Numbering , Bullets , or Multilevel list icon and choose the Change List Level option, or place the cursor at the beginning of the line and press the Tab key on a keyboard to move to the next level of the list. Proceed with the list level needed. The additional indentation and spacing parameters can be changed on the right sidebar and in the advanced settings window. To learn more about it, read the Change paragraph indents and Set paragraph line spacing section. Combine and separate lists To combine a list with the previous one: click the first item of the second list with the right mouse button, use the Join to previous list option from the contextual menu. The lists will be joined and the numbering will continue in accordance with the first list numbering. To separate a list: click the list item where you want to begin a new list with the right mouse button, use the Separate list option from the contextual menu. The lists will be combined, and the numbering will continue in accordance with the first list numbering. Change numbering To continue sequential numbering in the second list according to the previous list numbering: click the first item of the second list with the right mouse button, use the Continue numbering option from the contextual menu. The numbering will continue in accordance with the first list numbering. To set a certain numbering initial value: click the list item where you want to apply a new numbering value with the right mouse button, use the Set numbering value option from the contextual menu, in the new opened window, set the required numeric value and click the OK button. Change the list settings To change the bulleted or numbered list settings, such as a bullet/number type, alignment, size and color: click an existing list item or select the text you want to format as a list, click the Bullets or Numbering icon on the Home tab of the top toolbar, select the List Settings option, the List Settings window will open. The bulleted list settings window looks like this: The numbered list settings window looks like this: For the bulleted list, you can choose a character used as a bullet, while for the numbered list you can choose the numbering type. The Alignment, Size and Color options are the same both for the bulleted and numbered lists. Bullet allows selecting the required character used for the bulleted list. When you click on the Font and Symbol field, the Symbol window will appear, and you will be able to choose one of the available characters. To learn more on how to work with symbols, please refer to this article. Type allows selecting the required numbering type used for the numbered list. The following options are available: None, 1, 2, 3,..., a, b, c,..., A, B, C,..., i, ii, iii,..., I, II, III,.... Alignment allows selecting the required bullet/number alignment type that is used to align bullets/numbers horizontally. The following alignment types are available: Left, Center, Right. Size allows selecting the required bullet/number size. The Like a text option is selected by default. When this option is selected, the bullet or number size corresponds to the text size. You can choose one of the predefined sizes ranging from 8 to 96. Color allows selecting the required bullet/number color. The Like a text option is selected by default. When this option is selected, the bullet or number color corresponds to the text color. You can choose the Automatic option to apply the automatic color, or select one of the theme colors, or standard colors in the palette, or specify a custom color. All the changes are displayed in the Preview field. click OK to apply the changes and close the settings window. To change the multilevel list settings, click a list item, click the Multilevel list icon on the Home tab of the top toolbar, select the List Settings option, the List Settings window will open. The multilevel list settings window looks like this: Choose the necessary level of the list in the Level field on the left, then use the buttons on the top to adjust the bullet or number appearance for the selected level: Type allows selecting the required numbering type used for the numbered list or the required character used for the bulleted list. The following options are available for the numbered list: None, 1, 2, 3,..., a, b, c,..., A, B, C,..., i, ii, iii,..., I, II, III,.... For the bulleted list, you can choose one of the default symbols or use the New bullet option. When you click this option, the Symbol window will appear, and you will be able to choose one of the available characters. To learn more on how to work with symbols, please refer to this article. Alignment allows selecting the required bullet/number alignment type that is used to align bullets/numbers horizontally at the beginning of the paragraph. The following alignment types are available: Left, Center, Right. Size allows selecting the required bullet/number size. The Like a text option is selected by default. You can choose one of the predefined sizes ranging from 8 to 96. Color allows selecting the required bullet/number color. The Like a text option is selected by default. When this option is selected, the bullet or number color corresponds to the text color. You can choose the Automatic option to apply the automatic color, or select one of the theme colors, or standard colors on the palette, or specify a custom color. All the changes are displayed in the Preview field. click OK to apply the changes and close the settings window."
    },
   {
        "id": "UsageInstructions/CreateTableOfContents.htm", 
        "title": "Create a Table of Contents", 
        "body": "A table of contents contains a list of all the chapters (sections, etc.) in a document and displays the numbers of the pages where each chapter begins. In the Document Editor, it allows easily navigating through a multi-page document and quickly switching to the required part of the text. The table of contents is generated automatically on the basis of the document headings formatted using built-in styles. This makes it easy to update the created table of contents without having to edit the headings and change the page numbers manually if the text of the document has been changed. Heading structure in the table of contents Format headings First of all, format the headings in your document using one of the predefined styles. To do that, Select the text you want to include into the table of contents. Open the style menu on the right side of the Home tab at the top toolbar. Click the required style to be applied. By default, you can use the Heading 1 - Heading 9 styles. If you want to use other styles (e.g. Title, Subtitle etc.) to format headings, you will need to adjust the table of contents settings first (see the corresponding section below). To learn more about available formatting styles, please refer to this page. To add text as a heading quickly, Select the text you want to include into the table of contents. Go to the References tab at the top toolbar. Click the Add text button at the top toolbar. Choose the required heading level. Manage headings Once the headings are formatted, you can click the Headings icon on the left sidebar to open the panel that displays the list of all headings with corresponding nesting levels. This panel allows easily navigating between headings in the document text as well as managing the heading structure. Right-click on a heading in the list and use one of the available options from the menu: Promote - to move the currently selected heading up to the higher level in the hierarchical structure, e.g. change it from Heading 2 to Heading 1. Demote - to move the currently selected heading down to the lower level in the hierarchical structure, e.g. change it from Heading 1 to Heading 2. New heading before - to add a new empty heading of the same level before the currently selected one. New heading after - to add a new empty heading of the same level after the currently selected one. New subheading - to add a new empty subheading (i.e. a heading with lower level) after the currently selected heading. When the heading or subheading is added, click on the added empty heading in the list and type in your own text. This can be done both in the document text and on the Headings panel itself. Select content - to select the text below the current heading in the document (including the text related to all subheadings of this heading). Expand all - to expand all levels of headings at the Headings panel. Collapse all - to collapse all levels of headings, excepting level 1, at the Headings panel. Expand to level - to expand the heading structure to the selected level. E.g. if you select level 3, then levels 1, 2 and 3 will be expanded, while level 4 and all lower levels will be collapsed. To manually expand or collapse separate heading levels, use the arrows to the left of the headings. To close the Headings panel, click the Headings icon on the left sidebar once again. Insert a Table of Contents into the document To insert a table of contents into your document: Position the insertion point where the table of contents should be added. Switch to the References tab of the top toolbar. Click the Table of Contents icon on the top toolbar, or click the arrow next to this icon and select the necessary layout option from the menu. You can select the table of contents that displays headings, page numbers and leaders, or headings only. The table of contents appearance can be adjusted later in the settings. The table of contents will be added at the current cursor position. To change its position, you can select the table of contents field (content control) and simply drag it to the desired place. To do that, click the button in the upper left corner of the table of contents field and drag it without releasing the mouse button to another position in the document text. To navigate between headings, press the Ctrl key and click the necessary heading within the table of contents field. You will go to the corresponding page. Adjust the created Table of Contents Refresh the Table of Contents After the table of contents is created, you can continue editing your text by adding new chapters, changing their order, removing some paragraphs, or expanding the text related to a heading so that the page numbers that correspond to the previous or the following section may change. In this case, use the Refresh option to automatically apply all changes. Click the arrow next to the Refresh icon on the References tab of the top toolbar and select the necessary option from the menu: Refresh entire table - to add the headings that you added to the document, remove the ones you deleted from the document, update the edited (renamed) headings as well as update page numbers. Refresh page numbers only - to update page numbers without applying changes to the headings. Alternatively, you can select the table of contents in the document text and click the Refresh icon at the top of the table of contents field to display the above mentioned options. It's also possible to right-click anywhere within the table of contents and use the corresponding options from the contextual menu. Adjust the Table of Contents settings To open the table of contents settings, you can proceed in the following ways: Click the arrow next to the Table of Contents icon on the top toolbar and select the Settings option from the menu. Select the table of contents in the document text, click the arrow next to its field title and select the Settings option from the menu. Right-click anywhere within the table of contents and use the Table of contents settings option from the contextual menu. A new window will open, and you will be able to adjust the following settings: Show page numbers - this option allows displaying the page numbers. Right align page numbers - this option allows aligning the page numbers on the right side of the page. Leader - this option allows choose the required leader type. A leader is a line of characters (dots or hyphens) that fills the space between a heading and the corresponding page number. It's also possible to select the None option if you do not want to use leaders. Format Table of Contents as links - this option is checked by default. If you uncheck it, you will not be able to switch to the necessary chapter by pressing Ctrl and clicking the corresponding heading. Build table of contents from - this section allows specifying the necessary number of outline levels as well as the default styles that will be used to create the table of contents. Check the necessary radio button: Outline levels - when this option is selected, you will be able to adjust the number of hierarchical levels used. Click the arrows in the Levels field to decrease or increase the number of levels (the values from 1 to 9 are available). E.g., if you select the value of 3, headings that have levels 4 - 9 will not be included into the table of contents. Selected styles - when this option is selected, you can specify additional styles that can be used to build the table of contents and assign the corresponding outline level to each of them. Specify the desired level value in the field on the right of the style. Once you save the settings, you will be able to use this style when creating a table of contents. Styles - this options allows selecting the desired appearance of the table of contents. Select the necessary style from the drop-down list. The preview field above displays how the table of contents should look like. The following four default styles are available: Simple, Standard, Modern, Classic. The Current option is used if you customize the table of contents style. Click the OK button within the settings window to apply the changes. Customize the Table of Contents style After you apply one of the default table of contents styles within the Table of Contents settings window, you can additionally modify this style so that the text within the table of contents field looks like you need. Select the text within the table of contents field, e.g. pressing the button in the upper left corner of the table of contents content control. Format table of contents items changing their font type, size, color or applying the font decoration styles. Consequently update styles for items of each level. To update the style, right-click the formatted item, select the Formatting as Style option from the contextual menu and click the Update toc N style option (toc 2 style corresponds to items that have level 2, toc 3 style corresponds to items with level 3 and so on). Refresh the table of contents. Remove the Table of Contents To remove the table of contents from the document: click the arrow next to the Table of Contents icon on the top toolbar and use the Remove table of contents option, or click the arrow next to the table of contents content control title and use the Remove table of contents option."
    },
   {
        "id": "UsageInstructions/DecorationStyles.htm", 
        "title": "Apply font decoration styles", 
        "body": "In the Document Editor, you can apply various font decoration styles using the corresponding icons on the Home tab of the top toolbar. Note: in case you want to apply the formatting to the already existing text in the document, select it with the mouse or use the keyboard and apply the formatting. Bold Used to make the font bold giving it a heavier appearance. Italic Used to make the font slightly slanted to the right. Underline Used to make the text underlined with a line going under the letters. Strikeout Used to make the text struck out with a line going through the letters. Superscript Used to make the text smaller placing it in the upper part of the text line, e.g. as in fractions. Subscript Used to make the text smaller placing it in the lower part of the text line, e.g. as in chemical formulas. To access the advanced font settings, click the right mouse button and select the Paragraph Advanced Settings option from the menu or use the Show advanced settings link on the right sidebar. Then the Paragraph - Advanced Settings window will appear, and you will need to switch to the Font tab. Here you can use the following font decoration styles and settings: Strikethrough is used to make the text struck out with a line going through the letters. Double strikethrough is used to make the text struck out with a double line going through the letters. Superscript is used to make the text smaller placing it in the upper part of the text line, e.g. as in fractions. Subscript is used to make the text smaller placing it in the lower part of the text line, e.g. as in chemical formulas. Small caps is used to make all letters lower case. All caps is used to make all letters upper case. Spacing is used to set the space between the characters. Increase the default value to apply the Expanded spacing, or decrease the default value to apply the Condensed spacing. Use the arrow buttons or enter the necessary value in the box. Position is used to set the characters position (vertical offset) in the line. Increase the default value to move characters upwards, or decrease the default value to move characters downwards. Use the arrow buttons or enter the necessary value in the box. Ligatures are joined letters of a word typed in either of the OpenType fonts. Please note that using ligatures can disrupt character spacing. The available ligature options are as follows: None Standard only (includes “fi”, “fl”, “ff”; enhances readability) Contextual (ligatures are applied based on the surrounding letters; enhances readability) Historical (ligatures have more swoops and curved lines; lowers readability) Discretionary (ornamental ligatures; lowers readability) Standard and Contextual Standard and Historical Contextual and Historical Standard and Discretionary Contextual and Discretionary Historical and Discretionary Standard, Contextual and Historical Standard, Contextual and Discretionary Standard, Historical and Discretionary Contextual, Historical and Discretionary All All the changes will be displayed in the preview field below."
    },
   {
        "id": "UsageInstructions/Drawio.htm", 
        "title": "Create and insert diagrams", 
        "body": "If you need to create a lot of various and complex diagrams, ONLYOFFICE Document Editor provides you with a draw.io plugin that can create and configure such diagrams. Select the place on the page where you want to insert a diagram. Switch to the Plugins tab and click draw.io. draw io window will open containing the following sections: Top toolbar contains tools to manage files, configure interface, edit data via File, Edit, View, Arrange, Extras, Help tabs and corresponding options. Left sidebar contains various forms to select from: Standard, Software, Networking, Business, Other. To add new shapes to those available by default, click the More Shapes button, select the necessary object types and click Apply. Right sidebar contains tools and settings to customize the worksheet, shapes, charts, sheets, text, and arrows: Worksheet settings: View: Grid, its size and color, Page view, Background - you can either select a local image or provide the URL, or choose a suitable color using the color palette, as well as add Shadow effects. Options: Connection Arrows, Connection Points, Guides. Paper size: Portrait or Landscape orientation with specified length and width parameters. Shape settings: Color: Fill color, Gradient. Line: Color, Type, Width, Perimeter width. Opacity. Arrow settings: Color: Fill color, Gradient. Line: Color, Type, Width, Line end, Line start. Opacity. Working area to view diagrams, enter and edit data. Here you can move objects, form sequential diagrams, and connect objects with arrows. Status bar contains navigation tools for convenient switching between sheets and managing them. Use these tools to create the necessary diagram, edit it, and when it is finished, click the Insert button to add it to the document."
    },
   {
        "id": "UsageInstructions/FillingOutForm.htm", 
        "title": "Filling Out a Form", 
        "body": "A fillable form is an OFORM file. OFORM is a format for filling out template forms and downloading or printing the form after you have filled it out. How to fill in a form: Open an OFORM file. Fill in all the required fields. The mandatory fields are marked with red stroke. Use or Next Field on the top toolbar to navigate between fields, or click the field you wish to fill in. Use the Clear All Fields button to empty all input fields. After you have filled in all the fields, click the Save as PDF button to save the form to your computer as a PDF file. Click in the top right corner of the toolbar for additional options. You can Print, Download as docx, or Download as pdf. You can also change the form Interface theme by choosing Same as system, Light, Classic Light, Dark or Contrast Dark. Once the Dark or Contrast Dark interface theme is enabled, the Dark mode becomes available. Zoom allows to scale and to resize the page using the Fit to page, Fit to width and Zoom in or Zoom out options: Fit to page allows to resize the page so that the screen displays the whole page. Fit to width allows to resize the page so that the page scales to fit the width of the screen. Zoom adjusting tool allows to zoom in and zoom out the page. Open file location when you need to browse the folder where the form is stored."
    },
   {
        "id": "UsageInstructions/FontTypeSizeColor.htm", 
        "title": "Set font type, size, and color", 
        "body": "Set the font type, size, and color In the Document Editor, you can select the font type, its size and color using the corresponding icons on the Home tab of the top toolbar. In case you want to apply the formatting to the already existing text in the document, select it with the mouse or use the keyboard and apply the formatting. You can also place the mouse cursor within the necessary word to apply the formatting to this word only. Font Used to select a font from the list of the the available fonts. If the required font is not available in the list, you can download and install it on your operating system, and the font will be available in the desktop version. Font size Used to choose from the preset font size values in the dropdown list (the default values are: 8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 36, 48, 72 and 96). It's also possible to manually enter a custom value up to 300 pt in the font size field. Press Enter to confirm. Increment font size Used to change the font size making it one point bigger each time the button is pressed. Decrement font size Used to change the font size making it one point smaller each time the button is pressed. Change case Used to change the font case. Sentence case. - the case matches that of a common sentence. lowercase - all letters are small. UPPERCASE - all letters are capital. Capitalize Each Word - each word starts with a capital letter. tOGGLE cASE - reverse the case of the selected text or the word where the mouse cursor is positioned. Highlight color Used to mark separate sentences, phrases, words, or even characters by adding a color band that imitates the highlighter pen effect throughout the text. You can select the required part of the text and click the downward arrow next to the icon to select a color in the palette (this color set does not depend on the selected Color scheme and includes 16 colors) - the color will be applied to the selected text. Alternatively, you can first choose a highlight color and then start selecting the text with the mouse - the mouse pointer will look like this and you'll be able to highlight several different parts of your text sequentially. To stop highlighting, just click the icon once again. To delete the highlight color, choose the No Fill option. The Highlight color is different from the Background color as the latter is applied to the whole paragraph and completely fills all the paragraph space from the left page margin to the right page margin. Font color Used to change the color of the letters/characters in the text. By default, the automatic font color is set in a new blank document. It is displayed as a black font on the white background. If you change the background color to black, the font color will automatically change to white to keep the text clearly visible. To choose a different color, click the downward arrow next to the icon and select a color from the available palettes (the colors in the Theme Colors palette depend on the selected color scheme). After you change the default font color, you can use the Automatic option in the color palettes window to quickly restore the automatic color for the selected text passage. To learn more about color palettes, please refer to this page."
    },
   {
        "id": "UsageInstructions/FormattingPresets.htm", 
        "title": "Apply formatting styles", 
        "body": "Each formatting style is a set of predefined formatting options: (font size, color, line spacing, alignment etc.). The styles in the Document Editor allow you to quickly format different parts of the document (headings, subheadings, lists, normal text, quotes) instead of applying several formatting options individually each time. This also ensures the consistent appearance of the whole document. You can also use styles to create a table of contents or a table of figures. Applying a style depends on whether this style is a paragraph style (normal, no spacing, headings, list paragraph etc.), or a text style (based on the font type, size, color). It also depends on whether a text passage is selected, or the mouse cursor is placed on a word. In some cases you might need to select the required style from the style library twice, so that it can be applied correctly: when you click the style in the style panel for the first time, the paragraph style properties are applied. When you click it for the second time, the text properties are applied. Use default styles To apply one of the available text formatting styles, place the cursor within the required paragraph, or select several paragraphs, select the required style from the style gallery on the right on the Home tab of the top toolbar. The following formatting styles are available: normal, no spacing, heading 1-9, title, subtitle, quote, intense quote, list paragraph, footer, header, footnote text. Edit existing styles and create new ones To change an existing style: Apply the necessary style to a paragraph. Select the paragraph text and change all the formatting parameters you need. Save the changes made: right-click the edited text, select the Formatting as Style option and then choose the Update 'StyleName' Style option ('StyleName' corresponds to the style you've applied at the step 1), or select the edited text passage with the mouse, drop-down the style gallery, right-click the style you want to change and select the Update from selection option. Once the style is modified, all the paragraphs in the document formatted with this style will change their appearance correspondingly. To create a completely new style: Format a text passage as you need. Select an appropriate way to save the style: right-click the edited text, select the Formatting as Style option and then choose the Create new Style option, or select the edited text passage with the mouse, drop-down the style gallery and click the New style from selection option. Set the new style parameters in the opened Create New Style window: Specify the new style name in the text entry field. Select the desired style for the subsequent paragraph from the Next paragraph style list. It's also possible to choose the Same as created new style option. Click the OK button. The created style will be added to the style gallery. Manage your custom styles: To restore the default settings of a certain style you've changed, right-click the style you want to restore and select the Restore to default option. To restore the default settings of all the styles you've changed, right-click any default style in the style gallery and select the Restore all to default styles option. To delete one of the new styles you've created, right-click the style you want to delete and select the Delete style option. To delete all the new styles you've created, right-click any new style you've created and select the Delete all custom styles option."
    },
   {
        "id": "UsageInstructions/HTML.htm", 
        "title": "Edit HTML", 
        "body": "If you are writing a website page in a text editor and want to get it as an HTML code, use the HTML plugin. Open the Plugins tab and click Get and paste html. Select the necessary content. The HTML code of the selected paragraph will be displayed in the plugin field on the left-side panel. You can edit the code to alter the text characteristics, e.g. font size or font family, etc. Click Paste into the document to insert the text with its HTML code edited at the current cursor position in your document. You can also write your own HTML code (without selecting any document content) and then paste it to your document. For more information on the HTML plugin and its installation, please see the plugin’s page on the AppDirectory."
    },
   {
        "id": "UsageInstructions/HighlightedCode.htm", 
        "title": "Insert highlighted code", 
        "body": "In the Document Editor, you can embed highlighted code with the already adjusted style in accordance with the programming language and coloring style of the program you have chosen. Go to your document and place the cursor at the location where you want to include the code. Switch to the Plugins tab and choose Highlight code. Specify the programming Language. Select a Style of the code so that it appears as if it were open in this program. Specify if you want to replace tabs with spaces. Choose Background color. To do this, manually move the cursor over the palette or insert the RGB/HSL/HEX value. Click OK to insert the code."
    },
   {
        "id": "UsageInstructions/InsertAutoshapes.htm", 
        "title": "Insert autoshapes", 
        "body": "Insert an autoshape To add an autoshape in the Document Editor, switch to the Insert tab of the top toolbar, click the Shape icon on the top toolbar, select one of the available autoshape groups from the Shape Gallery: Recently Used, Basic Shapes, Figured Arrows, Math, Charts, Stars & Ribbons, Callouts, Buttons, Rectangles, Lines, click the necessary autoshape within the selected group, place the mouse cursor where the shape should be added, once the autoshape is added, you can change its size, position and properties. Note: to add a caption to an autoshape, make sure the required shape is selected on the page and start typing your text. The added text becomes a part of the autoshape (when you move or rotate the shape, the text moves or rotates with it). It's also possible to add a caption to the autoshape. To learn more on how to work with captions for autoshapes, you can refer to this article. Move and resize autoshapes To change the autoshape size, drag small squares situated on the shape edges. To maintain the original proportions of the selected autoshape while resizing, hold down the Shift key and drag one of the corner icons. When modifying some shapes, for example figured arrows or callouts, the yellow diamond-shaped icon is also available. It allows you to adjust some aspects of the shape, for example, the length of the head of an arrow. To alter the autoshape position, use the icon that appears after hovering your mouse cursor over the autoshape. Drag the autoshape to the required position without releasing the mouse button. When you move the autoshape, the guide lines are displayed to help you precisely position the object on the page (if the selected wrapping style is not inline). To move the autoshape by one-pixel increments, hold down the Ctrl key and use the keybord arrows. To move the autoshape strictly horizontally/vertically and prevent it from moving in a perpendicular direction, hold down the Shift key when dragging. To rotate the autoshape, hover the mouse cursor over the rotation handle and drag it clockwise or counterclockwise. To constrain the rotation angle to 15 degree increments, hold down the Shift key while rotating. Note: the list of keyboard shortcuts that can be used when working with objects is available here. Adjust autoshape settings To align and arrange autoshapes, use the right-click menu. The menu options are: Cut, Copy, Paste - standard options which are used to cut or copy the selected text/object and paste the previously cut/copied text passage or object to the current cursor position. Print selection is used to print out only a selected portion of the document. Accept / Reject changes is used to accept or to reject tracked changes in a shared document. Edit Points is used to customize or to change the curvature of your shape. To activate a shape’s editable anchor points, right-click the shape and choose Edit Points from the menu. The black squares that become active are the points where two lines meet, and the red line outlines the shape. Click and drag it to reposition the point, and to change the shape outline. Once you click the anchor point, two blue lines with white squares at the ends will appear. These are Bezier handles that allow you to create a curve and to change a curve’s smoothness. As long as the anchor points are active, you can add and delete them. To add a point to a shape, hold Ctrl and click the position where you want to add an anchor point. To delete a point, hold Ctrl and click the unnecessary point. Arrange is used to bring the selected autoshape to foreground, send it to background, move forward or backward as well as group or ungroup shapes to perform operations with several of them at once. To learn more on how to arrange objects, please refer to this page. Align is used to align the shape to the left, in the center, to the right, at the top, in the middle, at the bottom. To learn more on how to align objects, please refer to this page. Wrapping Style is used to select a text wrapping style from the available ones - inline, square, tight, through, top and bottom, in front, behind - or edit the wrap boundary. The Edit Wrap Boundary option is available only if you select a wrapping style other than Inline. Drag wrap points to customize the boundary. To create a new wrap point, click anywhere on the red line and drag it to the necessary position. Rotate is used to rotate the shape by 90 degrees clockwise or counterclockwise as well as to flip the shape horizontally or vertically. Shape Advanced Settings is used to open the 'Shape - Advanced Settings' window. Some of the autoshape settings can be altered using the Shape settings tab of the right sidebar. To activate it click the shape and choose the Shape settings icon on the right. Here you can change the following properties: Fill - use this section to select the autoshape fill. You can choose the following options: Color Fill - select this option to specify the solid color to fill the inner space of the selected autoshape. Click the colored box below and select the necessary color from the available color sets or specify any color you like: Gradient Fill - use this option to fill the shape with two or more fading colors. Customize your gradient fill with no constraints. Click the Shape settings icon to open the Fill menu on the right sidebar: Available menu options: Style - choose between Linear or Radial: Linear is used  when you need your colors to flow from left-to-right, top-to-bottom, or at any angle you chose in a single direction. The Direction preview window displays the selected gradient color, click the arrow to choose a preset gradient direction. Use Angle settings for a precise gradient angle. Radial is used to move from the center as it starts at a single point and emanates outward. Gradient Point is a specific point for transition from one color to another. Use the Add Gradient Point button or slider bar to add a gradient point. You can add up to 10 gradient points. Each next gradient point added will in no way affect the current gradient fill appearance. Use the Remove Gradient Point button to delete a certain gradient point. Use the slider bar to change the location of the gradient point or specify Position in percentage for precise location. To apply a color to a gradient point, click a point on the slider bar, and then click Color to choose the color you want. Picture or Texture - select this option to use an image or a predefined texture as the shape background. If you wish to use an image as a background for the shape, you can add an image From File by selecting it on your computer hard disc drive, From URL by inserting the appropriate URL address into the opened window, or From Storage by selecting the required image stored on your portal. If you wish to use a texture as a background for the shape, open the From Texture menu and select the necessary texture preset. Currently, the following textures are available: canvas, carton, dark fabric, grain, granite, grey paper, knit, leather, brown paper, papyrus, wood. In case the selected Picture has less or more dimensions than the autoshape has, you can choose the Stretch or Tile setting from the dropdown list. The Stretch option allows you to adjust the image size to fit the autoshape size so that it could fill the space completely. The Tile option allows you to display only a part of the bigger image keeping its original dimensions or repeat the smaller image keeping its original dimensions over the autoshape surface so that it could fill the space completely. Note: any selected Texture preset fills the space completely, but you can apply the Stretch effect if necessary. Pattern - select this option to fill the shape with a two-colored design composed of regularly repeated elements. Pattern - select one of the predefined designs from the menu. Foreground color - click this color box to change the color of the pattern elements. Background color - click this color box to change the color of the pattern background. No Fill - select this option if you don't want to use any fill. Opacity - use this section to set an Opacity level dragging the slider or entering the percent value manually. The default value is 100%. It corresponds to the full opacity. The 0% value corresponds to the full transparency. Line - use this section to change the width, color or type of the autoshape line. To change the line width, select one of the available options from the Size dropdown list. The available options are: 0.5 pt, 1 pt, 1.5 pt, 2.25 pt, 3 pt, 4.5 pt, 6 pt. Alternatively, select the No Line option if you don't want to use any line. To change the line color, click on the colored box below and select the necessary color. To change the line type, select the necessary option from the corresponding dropdown list (a solid line is applied by default, you can change it to one of the available dashed lines). Rotation is used to rotate the shape by 90 degrees clockwise or counterclockwise as well as to flip the shape horizontally or vertically. Click one of the buttons: to rotate the shape by 90 degrees counterclockwise to rotate the shape by 90 degrees clockwise to flip the shape horizontally (left to right) to flip the shape vertically (upside down) Wrapping Style - use this section to select a text wrapping style from the available ones - inline, square, tight, through, top and bottom, in front, behind (for more information see the advanced settings description below). Change Autoshape - use this section to replace the current autoshape with another one selected from the dropdown list. Show shadow - check this option to display the shape with a shadow. Adjust autoshape advanced settings To change the advanced settings of the autoshape, right-click it and select the Advanced Settings option in the menu or use the Show advanced settings link on the right sidebar. The 'Shape - Advanced Settings' window will open: The Size tab contains the following parameters: Width - use one of these options to change the autoshape width. Absolute - specify an exact value measured in absolute units i.e. Centimeters/Points/Inches (depending on the option specified on the File -> Advanced Settings... tab). Relative - specify a percentage relative to the left margin width, the margin (i.e. the distance between the left and right margins), the page width, or the right margin width. Height - use one of these options to change the autoshape height. Absolute - specify an exact value measured in absolute units i.e. Centimeters/Points/Inches (depending on the option specified on the File -> Advanced Settings... tab). Relative - specify a percentage relative to the margin (i.e. the distance between the top and bottom margins), the bottom margin height, the page height, or the top margin height. If the Lock aspect ratio option is checked, the width and height will be changed together preserving the original shape aspect ratio. The Rotation tab contains the following parameters: Angle - use this option to rotate the shape by an exactly specified angle. Enter the necessary value measured in degrees into the field or adjust it using the arrows on the right. Flipped - check the Horizontally box to flip the shape horizontally (left to right) or check the Vertically box to flip the shape vertically (upside down). The Text Wrapping tab contains the following parameters: Wrapping Style - use this option to change the way the shape is positioned relative to the text: it will either be a part of the text (in case you select the inline style) or bypassed by it from all sides (if you select one of the other styles). Inline - the shape is considered to be a part of the text, like a character, so when the text moves, the shape moves as well. In this case the positioning options are inaccessible. If one of the following styles is selected, the shape can be moved independently of the text and positioned on the page exactly: Square - the text wraps the rectangular box that bounds the shape. Tight - the text wraps the actual shape edges. Through - the text wraps around the shape edges and fills in the open white space within the shape. So that the effect can appear, use the Edit Wrap Boundary option from the right-click menu. Top and bottom - the text is only above and below the shape. In front - the shape overlaps the text. Behind - the text overlaps the shape. If you select the square, tight, through, or top and bottom styles, you will be able to set up some additional parameters - distance from text at all sides (top, bottom, left, right). The Position tab is available only if the selected wrapping style is not inline. This tab contains the following parameters that vary depending on the selected wrapping style: The Horizontal section allows you to select one of the following three autoshape positioning types: Alignment (left, center, right) relative to character, column, left margin, margin, page or right margin, Absolute Position measured in absolute units i.e. Centimeters/Points/Inches (depending on the option specified on the File -> Advanced Settings... tab) to the right of character, column, left margin, margin, page or right margin, Relative position measured in percent relative to the left margin, margin, page or right margin. The Vertical section allows you to select one of the following three autoshape positioning types: Alignment (top, center, bottom) relative to line, margin, bottom margin, paragraph, page or top margin, Absolute Position measured in absolute units i.e. Centimeters/Points/Inches (depending on the option specified on the File -> Advanced Settings... tab) below line, margin, bottom margin, paragraph, page or top margin, Relative position measured in percent relative to the margin, bottom margin, page or top margin. Move object with text ensures that the autoshape moves along with the text to which it is anchored. Allow overlap makes it possible for two autoshapes to overlap if you drag them near each other on the page. The Weights &amp; Arrows tab contains the following parameters: Line Style - this option group allows specifying the following parameters: Cap Type - this option allows setting the style for the end of the line, therefore it can be applied only to the shapes with the open outline, such as lines, polylines etc.: Flat - the end points will be flat. Round - the end points will be rounded. Square - the end points will be square. Join Type - this option allows setting the style for the intersection of two lines, for example, it can affect a polyline or the corners of the triangle or rectangle outline: Round - the corner will be rounded. Bevel - the corner will be cut off angularly. Miter - the corner will be pointed. It goes well to shapes with sharp angles. Note: the effect will be more noticeable if you use a large outline width. Arrows - this option group is available if a shape from the Lines shape group is selected. It allows setting the arrow Start and End Style and Size by selecting the appropriate option from the dropdown lists. The Text Padding tab allows changing the Top, Bottom, Left and Right internal margins of the autoshape (i.e. the distance between the text within the shape and the autoshape borders). Note: this tab is only available if text is added within the autoshape, otherwise the tab is disabled. The Alternative Text tab allows specifying a Title and Description which will be read to people with vision or cognitive impairments to help them better understand what information the shape contains."
    },
   {
        "id": "UsageInstructions/InsertBookmarks.htm", 
        "title": "Add bookmarks", 
        "body": "Bookmarks allow quickly access a certain part of the text or add a link to its location in the document. To add a bookmark in the Document Editor: specify the place where you want the bookmark to be added: put the mouse cursor at the beginning of the necessary text passage, or select the necessary text passage, switch to the References tab of the top toolbar, click the Bookmark icon on the top toolbar, in the Bookmarks window, enter the Bookmark name and click the Add button - a bookmark will be added to the bookmark list displayed below, Note: the bookmark name should begin with a letter, but it can also contain numbers. The bookmark name cannot contain spaces, but can include the underscore character \"_\". To access one of the added bookmarks within in the text: click the Bookmark icon on the References tab of the top toolbar, in the Bookmarks window, select the bookmark you want to access. To easily find the required bookmark in the list, you can sort the list of bookmarks by Name or by Location in the text, check the Hidden bookmarks option to display hidden bookmarks in the list (i.e. the bookmarks automatically created by the program when adding references to a certain part of the document. For example, if you create a hyperlink to a certain heading within the document, the document editor automatically creates a hidden bookmark to the target of this link). click the Go to button - the cursor will be positioned where the selected bookmark was added to the text, or the corresponding text passage will be selected, click the Get Link button - a new window will open where you can press the Copy button to copy the link to the file which specifyes the bookmark location in the document. When you paste this link in a browser address bar and press Enter, the document will be opened where the selected bookmark was added. Note: if you want to share this link with other users, you'll need to provide them with the corresponding access rights using the Sharing option on the Collaboration tab. click the Close button to close the window. To delete a bookmark, select it in the bookmark list and click the Delete button. To find out how to use bookmarks when creating links please refer to the Add hyperlinks section."
    },
   {
        "id": "UsageInstructions/InsertCharts.htm", 
        "title": "Insert charts", 
        "body": "Insert a chart To insert a chart in the Document Editor, Place the cursor where the chart should be added. Switch to the Insert tab of the top toolbar. Click the Chart icon on the top toolbar. Select the needed chart type from the available ones: Column Charts Clustered column Stacked column 100% stacked column 3-D Clustered Column 3-D Stacked Column 3-D 100% stacked column 3-D Column Line Charts Line Stacked line 100% stacked line Line with markers Stacked line with markers 100% stacked line with markers 3-D Line Pie Charts Pie Doughnut 3-D Pie Bar Charts Clustered bar Stacked bar 100% stacked bar 3-D clustered bar 3-D stacked bar 3-D 100% stacked bar Area Charts Area Stacked area 100% stacked area Stock Charts XY (Scatter) Charts Scatter Stacked bar Scatter with smooth lines and markers Scatter with smooth lines Scatter with straight lines and markers Scatter with straight lines Combo Charts Clustered column - line Clustered column - line on secondary axis Stacked area - clustered column Custom combination Note: ONLYOFFICE Document Editor supports the following types of charts that were created with third-party editors: Pyramid, Bar (Pyramid), Horizontal/Vertical Cylinders, Horizontal/Vertical Cones. You can open the file containing such a chart and modify it using the available chart editing tools. After that the Chart Editor window will appear where you can enter the necessary data into the cells using the following controls: and for copying and pasting the copied data and for undoing and redoing actions for inserting a function and for decreasing and increasing decimal places for changing the number format, i.e. the way the numbers you enter appear in cells for choosing a different type of chart. Click the Select Data button situated in the Chart Editor window. The Chart Data window will open. Use the Chart Data dialog to manage Chart Data Range, Legend Entries (Series), Horizontal (Category) Axis Label and Switch Row/Column. Chart Data Range - select data for your chart. Click the icon on the right of the Chart data range box to select data range. Legend Entries (Series) - add, edit, or remove legend entries. Type or select series name for legend entries. In Legend Entries (Series), click Add button. In Edit Series, type a new legend entry or click the icon on the right of the Select name box. Horizontal (Category) Axis Labels - change text for category labels. In Horizontal (Category) Axis Labels, click Edit. In Axis label range, type the labels you want to add or click the icon on the right of the Axis label range box to select data range. Switch Row/Column - rearrange the worksheet data that is configured in the chart not in the way that you want it. Switch rows to columns to display data on a different axis. Click OK button to apply the changes and close the window. Click the Change Chart Type button in the Chart Editor window to choose chart type and style. Select a chart from the available sections: Column, Line, Pie, Bar, Area, Stock, XY (Scatter), or Combo. When you choose Combo Charts, the Chart Type window lists chart series and allows choosing the types of charts to combine and selecting data series to place on a seconary axis. Change the chart settings by clicking the Edit Chart button situated in the Chart Editor window. The Chart - Advanced Settings window will open. The Layout tab allows you to change the layout of chart elements. Specify the Chart Title position in regard to your chart selecting the necessary option from the drop-down list: None to not display a chart title, Overlay to overlay and center a title on the plot area, No Overlay to display the title above the plot area. Specify the Legend position in regard to your chart selecting the necessary option from the drop-down list: None to not display a legend, Bottom to display the legend and align it to the bottom of the plot area, Top to display the legend and align it to the top of the plot area, Right to display the legend and align it to the right of the plot area, Left to display the legend and align it to the left of the plot area, Left Overlay to overlay and center the legend to the left on the plot area, Right Overlay to overlay and center the legend to the right on the plot area. Specify the Data Labels (i.e. text labels that represent exact values of data points) parameters: Specify the Data Labels position relative to the data points selecting the necessary option from the drop-down list. The available options vary depending on the selected chart type. For Column/Bar charts, you can choose the following options: None, Center, Inner Bottom, Inner Top, Outer Top. For Line/XY (Scatter)/Stock charts, you can choose the following options: None, Center, Left, Right, Top, Bottom. For Pie charts, you can choose the following options: None, Center, Fit to Width, Inner Top, Outer Top. For Area charts as well as for 3D Column, Line, Bar and Combo charts, you can choose the following options: None, Center. Select the data you wish to include into your labels checking the corresponding boxes: Series Name, Category Name, Value, Enter a character (comma, semicolon etc.) you wish to use for separating several labels into the Data Labels Separator entry field. Lines - is used to choose a line style for Line/XY (Scatter) charts. You can choose one of the following options: Straight to use straight lines between data points, Smooth to use smooth curves between data points, or None to not display lines. Markers - is used to specify whether the markers should be displayed (if the box is checked) or not (if the box is unchecked) for Line/XY (Scatter) charts. Note: the Lines and Markers options are available for Line charts and XY (Scatter) charts only. The Vertical Axis tab allows you to change the parameters of the vertical axis also referred to as the values axis or y-axis which displays numeric values. Note that the vertical axis will be the category axis which displays text labels for the Bar charts, therefore in this case the Vertical Axis tab options will correspond to the ones described in the next section. For the XY (Scatter) charts, both axes are value axes. Note: the Axis Settings and Gridlines sections will be disabled for Pie charts since charts of this type have no axes and gridlines. Select Hide to hide vertical axis in the chart, leave it unchecked to have vertical axis displayed. Specify Title orientation by selecting the necessary option from the drop-down list: None to not display a vertical axis title Rotated to display the title from bottom to top to the left of the vertical axis, Horizontal to display the title horizontally to the left of the vertical axis. Minimum Value - is used to specify the lowest value displayed at the vertical axis start. The Auto option is selected by default, in this case the minimum value is calculated automatically depending on the selected data range. You can select the Fixed option from the drop-down list and specify a different value in the entry field on the right. Maximum Value - is used to specify the highest value displayed at the vertical axis end. The Auto option is selected by default, in this case the maximum value is calculated automatically depending on the selected data range. You can select the Fixed option from the drop-down list and specify a different value in the entry field on the right. Axis Crosses - is used to specify a point on the vertical axis where the horizontal axis should cross it. The Auto option is selected by default, in this case the axes intersection point value is calculated automatically depending on the selected data range. You can select the Value option from the drop-down list and specify a different value in the entry field on the right, or set the axes intersection point at the Minimum/Maximum Value on the vertical axis. Display Units - is used to determine the representation of the numeric values along the vertical axis. This option can be useful if you're working with great numbers and wish the values on the axis to be displayed in a more compact and readable way (e.g. you can represent 50 000 as 50 by using the Thousands display units). Select desired units from the drop-down list: Hundreds, Thousands, 10 000, 100 000, Millions, 10 000 000, 100 000 000, Billions, Trillions, or choose the None option to return to the default units. Values in reverse order - is used to display values in the opposite direction. When the box is unchecked, the lowest value is at the bottom and the highest value is at the top of the axis. When the box is checked, the values are ordered from top to bottom. The Tick Options section allows adjusting the appearance of tick marks on the vertical scale. Major tick marks are the larger scale divisions which can have labels displaying numeric values. Minor tick marks are the scale subdivisions which are placed between the major tick marks and have no labels. Tick marks also define where gridlines can be displayed if the corresponding option is set on the Layout tab. The Major/Minor Type drop-down lists contain the following placement options: None to not display major/minor tick marks, Cross to display major/minor tick marks on both sides of the axis, In to display major/minor tick marks inside the axis, Out to display major/minor tick marks outside the axis. The Label Options section allows adjusting the appearance of major tick mark labels which display values. To specify a Label Position in regard to the vertical axis, select the necessary option from the drop-down list: None to not display tick mark labels, Low to display tick mark labels to the left of the plot area, High to display tick mark labels to the right of the plot area, Next to axis to display tick mark labels next to the axis. To specify a Label Format click the Label Format button and choose a category as it deems appropriate. Available label format categories: General Number Scientific Accounting Currency Date Time Percentage Fraction Text Custom Label format options vary depending on the selected category. For more information on changing number format, go to this page. Check Linked to source to keep number formatting from the data source in the chart. Note: Secondary axes are supported in Combo charts only. Secondary axes are useful in Combo charts when data series vary considerably or mixed types of data are used to plot a chart. Secondary Axes make it easier to read and understand a combo chart. The Secondary Vertical /Horizontal Axis tab appears when you choose an appropriate data series for a combo chart. All the settings and options on the Secondary Vertical/Horizontal Axis tab are the same as the settings on the Vertical/Horizontal Axis. For a detailed description of the Vertical/Horizontal Axis options, see description above/below. The Horizontal Axis tab allows you to change the parameters of the horizontal axis also referred to as the categories axis or x-axis which displays text labels. Note that the horizontal axis will be the value axis which displays numeric values for the Bar charts, therefore in this case the Horizontal Axis tab options will correspond to the ones described in the previous section. For the XY (Scatter) charts, both axes are value axes. Select Hide to hide horizontal axis in the chart, leave it unchecked to have horizontal axis displayed. Specify Title orientation by selecting the necessary option from the drop-down list: None when you don’t want to display a horizontal axis title, No Overlay  to display the title below the horizontal axis, Gridlines is used to specify the Horizontal Gridlines to display by selecting the necessary option from the drop-down list: None,  Major, Minor, or Major and Minor. Axis Crosses - is used to specify a point on the horizontal axis where the vertical axis should cross it. The Auto option is selected by default, in this case the axes intersection point value is calculated automatically depending on the selected data range. You can select the Value option from the drop-down list and specify a different value in the entry field on the right, or set the axes intersection point at the Minimum/Maximum Value (that corresponds to the first and last category) on the horizontal axis. Axis Position - is used to specify where the axis text labels should be placed: On Tick Marks or Between Tick Marks. Values in reverse order - is used to display categories in the opposite direction. When the box is unchecked, categories are displayed from left to right. When the box is checked, the categories are ordered from right to left. The Tick Options section allows adjusting the appearance of tick marks on the horizontal scale. Major tick marks are the larger divisions which can have labels displaying category values. Minor tick marks are the smaller divisions which are placed between the major tick marks and have no labels. Tick marks also define where gridlines can be displayed if the corresponding option is set on the Layout tab. You can adjust the following tick mark parameters: Major/Minor Type - is used to specify the following placement options: None to not display major/minor tick marks, Cross to display major/minor tick marks on both sides of the axis, In to display major/minor tick marks inside the axis, Out to display major/minor tick marks outside the axis. Interval between Marks - is used to specify how many categories should be displayed between two adjacent tick marks. The Label Options section allows adjusting the appearance of labels which display categories. Label Position - is used to specify where the labels should be placed in regard to the horizontal axis. Select the necessary option from the drop-down list: None to not display category labels, Low to display category labels at the bottom of the plot area, High to display category labels at the top of the plot area, Next to axis to display category labels next to the axis. Axis Label Distance - is used to specify how closely the labels should be placed to the axis. You can specify the necessary value in the entry field. The more the value you set, the more the distance between the axis and labels is. Interval between Labels - is used to specify how often the labels should be displayed. The Auto option is selected by default, in this case labels are displayed for every category. You can select the Manual option from the drop-down list and specify the necessary value in the entry field on the right. For example, enter 2 to display labels for every other category etc. To specify a Label Format click the Label Format button and choose a category as it deems appropriate. Available label format categories: General Number Scientific Accounting Currency Date Time Percentage Fraction Text Custom Label format options vary depending on the selected category. For more information on changing number format, go to this page. Check Linked to source to keep number formatting from the data source in the chart. The Cell Snapping tab contains the following parameters: Move and size with cells - this option allows you to snap the chart to the cell behind it. If the cell moves (e.g. if you insert or delete some rows/columns), the chart will be moved together with the cell. If you increase or decrease the width or height of the cell, the chart will change its size as well. Move but don't size with cells - this option allows to snap the chart to the cell behind it preventing the chart from being resized. If the cell moves, the chart will be moved together with the cell, but if you change the cell size, the chart dimensions remain unchanged. Don't move or size with cells - this option allows to prevent the chart from being moved or resized if the cell position or size was changed. The Alternative Text tab allows specifying a Title and Description which will be read to people with vision or cognitive impairments to help them better understand what information the chart contains. Move and resize charts Once the chart is added, you can change its size and position. To change the chart size, drag small squares situated on its edges. To maintain the original proportions of the selected chart while resizing, hold down the Shift key and drag one of the corner icons. To alter the chart position, use the icon that appears after hovering your mouse cursor over the chart. Drag the chart to the necessary position without releasing the mouse button. When you move the chart, guide lines are displayed to help you position the object on the page precisely (if a wrapping style other than inline is selected). Note: the list of keyboard shortcuts that can be used when working with objects is available here. Edit chart elements To edit the chart Title, select the default text with the mouse and type the required text. To change the font formatting within text elements, such as the chart title, axes titles, legend entries, data labels etc., select the necessary text element by left-clicking it. Then use the corresponding icons on the Home tab of the top toolbar to change the font type, size, color or its decoration style. When the chart is selected, the Shape settings icon is also available on the right, since a shape is used as a background for the chart. You can click this icon to open the Shape settings tab on the right sidebar and adjust Fill, Stroke and Wrapping Style of the shape. Note that you cannot change the shape type. Using the Shape Settings tab on the right panel, you can both adjust the chart area itself and change the chart elements, such as plot area, data series, chart title, legend etc and apply different fill types to them. Select the chart element clicking it with the left mouse button and choose the preferred fill type: solid color, gradient, texture or picture, pattern. Specify the fill parameters and set the Opacity level if necessary. When you select a vertical or horizontal axis or gridlines, the stroke settings are only available at the Shape Settings tab: color, width and type. For more details on how to work with shape colors, fills and stroke, you can refer to this page. Note: the Show shadow option is also available at the Shape settings tab, but it is disabled for chart elements. If you need to resize chart elements, left-click to select the needed element and drag one of 8 white squares located along the perimeter of the element. To change the position of the element, left-click on it, make sure your cursor changed to , hold the left mouse button and drag the element to the needed position. To delete a chart element, select it by left-clicking and press the Delete key on the keyboard. You can also rotate 3D charts using the mouse. Left-click within the plot area and hold the mouse button. Drag the cursor without releasing the mouse button to change the 3D chart orientation. Adjust chart settings Some of the chart settings can be altered using the Chart settings tab of the right sidebar. To activate it click the chart and choose the Chart settings icon on the right. Here you can change the following properties: Size is used to view the Width and Height of the current chart. Wrapping Style is used to select a text wrapping style from the available ones - inline, square, tight, through, top and bottom, in front, behind (for more information see the advanced settings description below). Change Chart Type is used to change the selected chart type and/or style. To select the necessary chart Style, use the second drop-down menu in the Change Chart Type section. Edit Data is used to open the 'Chart Editor' window. Note: to quickly open the 'Chart Editor' window you can also double-click the chart in the document. You can also find some of these options in the right-click menu. The menu options are: Cut, Copy, Paste - standard options which are used to cut or copy the selected text/object and paste the previously cut/copied text passage or object to the current cursor position. Arrange is used to bring the selected chart to foreground, send it to the background, move forward or backward as well as group or ungroup charts to perform operations with several of them at once. To learn more on how to arrange objects, please refer to this page. Align is used to align the chart left, center, right, top, middle, bottom. To learn more on how to align objects you can refer to this page. Wrapping Style is used to select a text wrapping style from the available ones - inline, square, tight, through, top and bottom, in front, behind. The Edit Wrap Boundary option is unavailable for charts. Edit Data is used to open the 'Chart Editor' window. Chart Advanced Settings is used to open the 'Chart - Advanced Settings' window. Additionally, 3D Rotation settings are available for 3D charts: X rotation - set the required value for the X axis rotation using the keyboard or via the Left and Right arrows to the right. Y rotation - set the required value for the Y axis rotation using the keyboard or via the Up and Down arrows to the right. Perspective - set the required value for depth rotation using the keyboard or via the Narrow field of view and Widen field of view arrows to the right. Right Angle Axis - is used to set the right angle axis view. Autoscale - check this box to autoscale the depth and height values of the chart, or uncheck this box to set the depth and height values manually. Depth (% of base) - set the required depth value using the keyboard or via the arrows. Height (% of base) - set the required height value using the keyboard or via the arrows. Default Rotation - set the 3D parameters to their default. Please note that you cannot edit each element of the chart; the settings will be applied to the chart as a whole. To change the chart advanced settings, click the needed chart with the right mouse button and select Chart Advanced Settings from the right-click menu or just click the Show advanced settings link on the right sidebar. The chart properties window will open: The Size tab contains the following parameters: Width and Height - use these options to change the width and/or height of the chart. If the Constant Proportions button is clicked (in this case it looks like this ), the width and height will be changed together preserving the original chart aspect ratio. The Text Wrapping tab contains the following parameters: Wrapping Style - use this option to change the way the chart is positioned relative to the text: it will either be a part of the text (in case you select the inline style) or bypassed by it from all sides (if you select one of the other styles). Inline - the chart is considered to be a part of the text, like a character, so when the text moves, the chart moves as well. In this case the positioning options are inaccessible. If one of the following styles is selected, the chart can be moved independently of the text and positioned on the page exactly: Square - the text wraps the rectangular box that bounds the chart. Tight - the text wraps the actual chart edges. Through - the text wraps around the chart edges and fills in the open white space within the chart. Top and bottom - the text is only above and below the chart. In front - the chart overlaps the text. Behind - the text overlaps the chart. If you select the square, tight, through, or top and bottom styles, you will be able to set up some additional parameters - distance from text at all sides (top, bottom, left, right). The Position tab is available only if the selected wrapping style is not inline. This tab contains the following parameters that vary depending on the selected wrapping style: The Horizontal section allows you to select one of the following three chart positioning types: Alignment (left, center, right) relative to character, column, left margin, margin, page or right margin, Absolute Position measured in absolute units i.e. Centimeters/Points/Inches (depending on the option specified on the File -> Advanced Settings... tab) to the right of character, column, left margin, margin, page or right margin, Relative position measured in percent relative to the left margin, margin, page or right margin. The Vertical section allows you to select one of the following three chart positioning types: Alignment (top, center, bottom) relative to line, margin, bottom margin, paragraph, page or top margin, Absolute Position measured in absolute units i.e. Centimeters/Points/Inches (depending on the option specified on the File -> Advanced Settings... tab) below line, margin, bottom margin, paragraph, page or top margin, Relative position measured in percent relative to the margin, bottom margin, page or top margin. Move object with text ensures that the chart moves along with the text to which it is anchored. Allow overlap makes it possible for two charts to overlap if you drag them near each other on the page. The Alternative Text tab allows specifying a Title and Description which will be read to the people with vision or cognitive impairments to help them better understand what information the chart contains."
    },
   {
        "id": "UsageInstructions/InsertContentControls.htm", 
        "title": "Insert content controls", 
        "body": "ONLYOFFICE Document Editor allows you to insert classic content controls, i.e. they are fully backward compatible with the third-party word processors such as Microsoft Word. ONLYOFFICE Document Editor supports the following classic content controls: Plain Text, Rich Text, Picture, Combo box, Drop-down list, Date, Check box. Plain Text is an object containing text that cannot be formatted. Plain text content controls cannot contain more than one paragraph. Rich Text is an object containing text that can be formatted. Rich text content controls can contain several paragraphs, lists, and objects (images, shapes, tables etc.). Picture is an object containing a single image. Combo box is an object containing a drop-down list with a set of choices. It allows choosing one of the predefined values from the list and edit the selected value if necessary. Drop-down list is an object containing a drop-down list with a set of choices. It allows choosing one of the predefined values from the list. The selected value cannot be edited. Date is an object containing a calendar that allows choosing a date. Check box is an object that allows displaying two states: the check box is selected and the check box is cleared. Adding content controls Create a new Plain Text content control position the insertion point within the text line where the content control should be added, or select a text passage to transform it into a content control. switch to the Insert tab of the top toolbar. click the arrow next to the Content Controls icon. choose the Plain Text option from the menu. The content control will be inserted at the insertion point within existing text line. Replace the default text within the content control (\"Your text here\") with your own text: select the default text, and type in a new text or copy a text passage from anywhere and paste it into the content control. The Plain text content controls do not allow adding line breaks and cannot contain other objects such as images, tables, etc. Create a new Rich Text content control position the insertion point within the text line where the content control should be added, or select one or more of the existing paragraphs you want to become the control contents. switch to the Insert tab of the top toolbar. click the arrow next to the Content Controls icon. choose the Rich Text option from the menu. The control will be inserted in a new paragraph. Replace the default text within the control (\"Your text here\") with your own one: select the default text, and type in a new text or copy a text passage from anywhere and paste it into the content control. Rich text content controls allow adding line breaks, i.e. can contain multiple paragraphs as well as some objects, such as images, tables, other content controls etc. Create a new Picture content control position the insertion point within a line of the text where you want the control to be added. switch to the Insert tab of the top toolbar. click the arrow next to the Content Controls icon. choose the Picture option from the menu - the content control will be inserted at the insertion point. click the image icon in the button above the content control border - a standard file selection window will open. Choose an image stored on your computer and click Open. The selected image will be displayed within the content control. To replace the image, click the image icon in the button above the content control border and select another image. Create a new Combo box or Drop-down list content control The Combo box and Drop-down list content controls contain a drop-down list with a set of choices. They can be created amost in the same way. The main difference between them is that the selected value in the drop-down list cannot be edited, while the selected value in the combo box can be replaced. position the insertion point within a line of the text where you want the control to be added. switch to the Insert tab of the top toolbar. click the arrow next to the Content Controls icon. choose the Combo box or Drop-down list option from the menu - the control will be inserted at the insertion point. right-click the added control and choose the Content control settings option from the contextual menu. in the the opened Content Control Settings window, switch to the Combo box or Drop-down list tab, depending on the selected content control type. to add a new list item, click the Add button and fill in the available fields in the the opened window: specify the necessary text in the Display name field, e.g. Yes, No, Other. This text will be displayed in the content control within the document. by default, the text in the Value field corresponds to the one entered in the Display name field. If you want to edit the text in the Value field, note that the entered value must be unique for each item. click the OK button. you can edit or delete the list items by using the Edit or Delete buttons on the right or change the item order using the Up and Down button. when all the necessary choices are set, click the OK button to save the settings and close the window. You can click the arrow button in the right part of the added Combo box or Drop-down list content control to open the item list and choose the necessary one. Once the necessary item is selected from the Combo box, you can edit the displayed text by replacing it with your text entirely or partially. The Drop-down list does not allow editing the selected item. Create a new Date content control position the insertion point within the text where content control should be added. switch to the Insert tab of the top toolbar. click the arrow next to the Content Controls icon. choose the Date option from the menu - the content control with the current date will be inserted at the insertion point. right-click the added content control and choose the Content control settings option from the contextual menu. in the opened Content Control Settings window, switch to the Date format tab. choose the necessary Language and select the necessary date format in the Display the date like this list. click the OK button to save the settings and close the window. You can click the arrow button in the right part of the added Date content control to open the calendar and choose the necessary date. Create a new Check box content control position the insertion point within the text line where the content control should be added. switch to the Insert tab of the top toolbar. click the arrow next to the Content Controls icon. choose the Check box option from the menu - the content control will be inserted at the insertion point. right-click the added content control and choose the Content control settings option from the contextual menu. in the opened Content Control Settings window, switch to the Check box tab. click the Checked symbol button to specify the necessary symbol for the selected check box or the Unchecked symbol to select how the cleared check box should look like. The Symbol window will open. To learn more on how to work with symbols, please refer to this article. when the symbols are specified, click the OK button to save the settings and close the window. The added check box is displayed in the unchecked mode. If you click the added check box it will be checked with the symbol selected in the Checked symbol list. Note: The content control border is only visible when the control is selected. The borders do not appear on a printed version. Moving content controls Content controls can be moved to another place in the document: click the button on the left of the control border to select the control and drag it without releasing the mouse button to another position in the text. You can also copy and paste content controls: select the necessary control and use the Ctrl+C/Ctrl+V key combinations. Editing plain text and rich text content controls Text within plain text and rich text content controls can be formatted by using the icons on the top toolbar: you can adjust the font type, size, color, apply decoration styles and formatting presets. It's also possible to use the Paragraph - Advanced settings window accessible from the contextual menu or from the right sidebar to change the text properties. Text within rich text content controls can be formatted like a regular text, i.e. you can set line spacing, change paragraph indents, adjust tab stops, etc. Changing content control settings No matter which type of content controls is selected, you can change the content control settings in the General and Locking sections of the Content Control Settings window. To open the content control settings, you can proceed in the following ways: Select the necessary content control, click the arrow next to the Content Controls icon on the top toolbar and select the Control Settings option from the menu. Right-click anywhere within the content control and use the Content control settings option from the contextual menu. A new window will open. Ot the General tab, you can adjust the following settings: Specify the content control Title, Placeholder, or Tag in the corresponding fields. The title will be displayed when the control is selected. The placeholder is the main text displayed within the content control element. Tags are used to identify content controls so that you can make a reference to them in your code. Choose if you want to display the content control with a Bounding box or not. Use the None option to display the control without the bounding box. If you select the Bounding box option, you can choose the Color of this box using the field below. Click the Apply to All button to apply the specified Appearance settings to all the content controls in the document. On the Locking tab, you can protect the content control from being deleted or edited using the following settings: Content control cannot be deleted - check this box to protect the content control from being deleted. Contents cannot be edited - check this box to protect the contents of the content control from being edited. For certain types of content controls, the third tab that contains the specific settings for the selected content control type is also available: Combo box, Drop-down list, Date, Check box. These settings are described above in the sections about adding the corresponding content controls. Click the OK button within the settings window to apply the changes. It's also possible to highlight content controls with a certain color. To highlight controls with a color: Click the button on the left of the control border to select the control, Click the arrow next to the Content Controls icon on the top toolbar, Select the Highlight Settings option from the menu, Choose the required color from the available palettes: Theme Colors, Standard Colors or specify a new Custom Color. To remove previously applied color highlighting, use the No highlighting option. The selected highlight options will be applied to all the content controls in the document. Removing content controls To remove a content control and leave all its contents, select a content control, then proceed in one of the following ways: Click the arrow next to the Content Controls icon on the top toolbar and select the Remove content control option from the menu. Right-click the content control and use the Remove content control option from the contextual menu. To remove a control and all its contents, select the necessary control and press the Delete key on the keyboard."
    },
   {
        "id": "UsageInstructions/InsertCrossReference.htm", 
        "title": "Insert cross-references", 
        "body": "In the Document Editor, cross-references are used to create links leading to other parts of the same document, e.g. headings or objects such as charts or tables. Such references appear in the form of a hyperlink. Creating a cross-reference Position your cursor in the place you want to insert a cross-reference. Go to the References tab and click on the Cross-reference icon. Set the required parameters in the opened Cross-reference window: The Reference type drop-down menu specifies the item you wish to refer to, i.e. a numbered item (set by default), a heading, a bookmark, a footnote, an endnote, an equation, a figure, and a table. Choose the required item type. The Insert reference to drop-down menu specifies the text or numeric value of a reference you want to insert depending on the item you chose in the Reference type menu. For example, if you chose the Heading option, you may specify the following contents: Heading text, Page number, Heading number, Heading number (no context), Heading number (full context), Above/below. The full list of the options provided depending on the chosen reference type Reference type Insert reference to Description Numbered item Page number Inserts the page number of the numbered item Paragraph number Inserts the paragraph number of the numbered item Paragraph number (no context) Inserts an abbreviated paragraph number. The reference is made to the specific item of the numbered list only, e.g., instead of “4.1.1” you refer to “1” only Paragraph number (full context) Inserts a full paragraph number, e.g., “4.1.1” Paragraph text Inserts the text value of the paragraph, e.g., if you have “4.1.1. Terms and Conditions”, you refer to “Terms and Conditions” only Above/below Inserts the words “above” or “below” depending on the position of the item Heading Heading text Inserts the entire text of the heading Page number Inserts the page number of the heading Heading number Inserts the sequence number of the heading Heading number (no context) Inserts an abbreviated heading number. Make sure the cursor point is in the section you are referencing to, e.g., you are in section 4 and you wish to refer to heading 4.B, so instead of “4.B” you receive “B” only Heading number (full context) Inserts a full heading number even if the cursor point is in the same section Above/below Inserts the words “above” or “below” depending on the position of the item Bookmark Bookmark text Inserts the entire text of the bookmark Page number Inserts the page number of the bookmark Paragraph number Inserts the paragraph number of the bookmark Paragraph number (no context) Inserts an abbreviated paragraph number. The reference is made to the specific item only, e.g., instead of “4.1.1” you refer to “1” only Paragraph number (full context) Inserts a full paragraph number, e.g., “4.1.1” Above/below Inserts the words “above” or “below” depending on the position of the item Footnote Footnote number Inserts the footnote number Page number Inserts the page number of the footnote Above/below Inserts the words “above” or “below” depending on the position of the item Footnote number (formatted) Inserts the number of the footnote formatted as a footnote. The numbering of the actual footnotes is not affected Endnote Endnote number Inserts the endnote number Page number Inserts the page number of the endnote Above/below Inserts the words “above” or “below” depending on the position of the item Endnote number (formatted) Inserts the number of the endnote formatted as an endnote. The numbering of the actual endnotes is not affected Equation / Figure / Table Entire caption Inserts the full text of the caption Only label and number Inserts the label and object number only, e.g., “Table 1.1” Only caption text Inserts the text of the caption only Page number Inserts the page number containing the referenced object Above/below Inserts the words “above” or “below” depending on the position of the item Check the Insert as hyperlink box to turn the reference into an active link. Check the Include above/below box (if available) to specify the position of the item you refer to. The ONLYOFFICE Document Editor will automatically insert words “above” or “below” depending on the position of the item. Check the Separate numbers with box to specify the separator in the box to the right. The separators are needed for full context references. The For which field offers you the items available according to the Reference type you have chosen, e.g. if you chose the Heading option, you will see the full list of the headings in the document. Click Insert to create a cross-reference. Removing a cross-reference To delete a cross-reference, select the cross-reference you wish to remove and press the Delete key."
    },
   {
        "id": "UsageInstructions/InsertDateTime.htm", 
        "title": "Insert date and time", 
        "body": "To insert Date and time in the Document Editor, put the cursor where you want to insert Date and time, switch to the Insert tab of the top toolbar, click the Date &amp time icon on the top toolbar, in the Date &amp time window that will appear, specify the following parameters: Select the required language. Select one of the suggested formats. Check the Update automatically checkbox to let the date & time update automatically based on the current state. Note: you can also update the date and time manually by using the Refresh field option from the contextual menu. Click the Set as default button to make the current format the default for this language. Click the OK button."
    },
   {
        "id": "UsageInstructions/InsertEndnotes.htm", 
        "title": "Insert endnotes", 
        "body": "In the Document Editor, you can insert endnotes to add explanations or comments to specific terms or sentences, make references to the sources, etc. that are displayed at end of the document. Inserting endnotes To insert an endnote into your document, position the insertion point at the end of the text passage or at the word that you want to add the endnote to, switch to the References tab located at the top toolbar, click the Footnote icon on the top toolbar and select the Insert Endnote option from the menu. The endnote mark (i.e. the superscript character that indicates an endnote) appears in the text of the document, and the insertion point moves to the end of the document. type in the endnote text. Repeat the above mentioned operations to add subsequent endnotes for other text passages in the document. The endnotes are numbered automatically: i, ii, iii, etc. by default. Display of endnotes in the document If you hover the mouse pointer over the endnote mark in the document text, a small pop-up window with the endnote text appears. Navigating through endnotes To easily navigate through the added endnotes in the text of the document, click the arrow next to the Footnote icon on the References tab located at the top toolbar, in the Go to Endnotes section, use the arrow to go to the previous endnote or the arrow to go to the next endnote. Editing endnotes To edit the endnotes settings, click the arrow next to the Footnote icon on the References tab located at the top toolbar, select the Notes Settings option from the menu, change the current parameters in the Notes Settings window that will appear: Set the Location of endnotes on the page selecting one of the available options from the drop-down menu to the right: End of section - to position endnotes at the end of the sections. End of document - to position endnotes at the end of the document (set by default). Adjust the endnotes Format: Number Format - select the necessary number format from the available ones: 1, 2, 3,..., a, b, c,..., A, B, C,..., i, ii, iii,..., I, II, III,.... Start at - use the arrows to set the number or letter you want to start numbering with. Numbering - select a way to number your endnotes: Continuous - to number endnotes sequentially throughout the document, Restart each section - to start endnote numbering with 1 (or another specified character) at the beginning of each section, Restart each page - to start endnote numbering with 1 (or another specified character) at the beginning of each page. Custom Mark - set a special character or a word you want to use as the endnote mark (e.g. * or Note1). Enter the necessary character/word into the text entry field and click the Insert button at the bottom of the Notes Settings window. Use the Apply changes to drop-down list if you want to apply the specified notes settings to the Whole document or the Current section only. Note: to use different endnotes formatting in separate parts of the document, you need to add section breaks first. When you finish, click the Apply button. Removing endnotes To remove a single endnote, position the insertion point directly before the endtnote mark in the text and press Delete. Other endnotes will be renumbered automatically. To delete all the endnotes in the document, click the arrow next to the Footnote icon on the References tab located at the top toolbar, select the Delete All Notes option from the menu. choose the Delete All Endnotes option in the appeared window and click OK."
    },
   {
        "id": "UsageInstructions/InsertEquation.htm", 
        "title": "Insert equations", 
        "body": "The Document Editor allows you to build equations using the built-in templates, edit them, insert special characters (including mathematical operators, Greek letters, accents, etc.). Add a new equation To insert an equation from the gallery, put the cursor within the necessary line, switch to the Insert tab of the top toolbar, click the arrow next to the Equation icon on the top toolbar, select the required equation category in the toolbar above the inserted equation, or in the opened drop-down list select the equation category you need. The following categories are currently available: Symbols, Fractions, Scripts, Radicals, Integrals, Large Operators, Brackets, Functions, Accents, Limits and Logarithms, Operators, Matrices, click the Equation Settings symbol in the toolbar above the inserted equation to access more settings, e.g., Unicode or LaTeX, Professional or Linear, and Change to Inline, click the certain symbol/equation in the corresponding set of templates. The selected symbol/equation box will be inserted at the cursor position. If the selected line is empty, the equation will be centered. To align such an equation to the left or to the right, click on the equation box and use the or icon on the Home tab of the top toolbar. Each equation template represents a set of slots. A slot is a position for each element that makes up the equation. An empty slot (also called as a placeholder) has a dotted outline . You need to fill in all the placeholders specifying the necessary values. Note: to start creating an equation, you can also use the Alt + = keyboard shortcut. It's also possible to add a caption to the equation. To learn more on how to work with captions for equations, please refer to this article. Enter values The insertion point specifies where the next character will appear. To position the insertion point precisely, click within the placeholder and use the keyboard arrows to move the insertion point by one character left/right or one line up/down. If you need to create a new placeholder below the slot with the insertion point within the selected template, press Enter. Once the insertion point is positioned, you can fill in the placeholder: enter the desired numeric/literal value using the keyboard, insert a special character using the Symbols palette from the Equation menu on the Insert tab of the top toolbar or typing them from the keyboard (see the Math AutoСorrect option description), add another equation template from the palette to create a complex nested equation. The size of the primary equation will be automatically adjusted to fit its content. The size of the nested equation elements depends on the primary equation placeholder size, but it cannot be smaller than the sub-subscript size. To add some new equation elements you can also use the right-click menu options: To add a new argument that goes before or after the existing one within Brackets, you can right-click on the existing argument and select the Insert argument before/after option from the menu. To add a new equation within Cases with several conditions from the Brackets group (or equations of other types, if you've previously added new placeholders by pressing Enter), you can right-click on an empty placeholder or entered equation within it and select the Insert equation before/after option from the menu. To add a new row or a column in a Matrix, you can right-click on a placeholder within it, select the Insert option from the menu, then select Row Above/Below or Column Left/Right. Note: currently, equations cannot be entered using the linear format, i.e. \\sqrt(4&x^3). When entering the values of the mathematical expressions, you do not need to use Spacebar as the spaces between the characters and signs of operations are set automatically. If the equation is too long and does not fit a single line, automatic line breaking occurs while typing. You can also insert a line break in a specific position by right-clicking on a mathematical operator and selecting the Insert manual break option from the menu. The selected operator will start a new line. Once the manual line break is added, you can press the Tab key to align the new line to any math operator of the previous line. To delete the added manual line break, right-click on the mathematical operator that starts a new line and select the Delete manual break option. Format equations To increase or decrease the equation font size, click anywhere within the equation box and use the and buttons on the Home tab of the top toolbar or select the necessary font size from the list. All the equation elements will change correspondingly. The letters within the equation are italicized by default. If necessary, you can change the font style (bold, italic, strikeout) or color for a whole equation or its part. The underlined style can be applied to the entire equation only, not to individual characters. Select the necessary part of the equation by clicking and dragging it. The selected part will be highlighted in blue. Then use the necessary buttons on the Home tab of the top toolbar to format the selected part. For example, you can remove the italic format for ordinary words that are not variables or constants. To modify some equation elements, you can also use the right-click menu options: To change the Fractions format, you can right-click on a fraction and select the Change to skewed/linear/stacked fraction option from the menu (the available options differ depending on the selected fraction type). To change the Scripts position relating to text, you can right-click on the equation that includes scripts and select the Scripts before/after text option from the menu. To change the argument size for Scripts, Radicals, Integrals, Large Operators, Limits and Logarithms, Operators as well as for overbraces/underbraces and templates with grouping characters from the Accents group, you can right-click on the argument you want to change and select the Increase/Decrease argument size option from the menu. To specify whether an empty degree placeholder should be displayed or not for a Radical, you can right-click on the radical and select the Hide/Show degree option from the menu. To specify whether an empty limit placeholder should be displayed or not for an Integral or Large Operator, you can right-click on the equation and select the Hide/Show top/bottom limit option from the menu. To change the limits position relating to the integral or operator sign for Integrals or Large Operators, you can right-click on the equation and select the Change limits location option from the menu. The limits can be displayed on the right of the operator sign (as subscripts and superscripts) or directly above and below the operator sign. To change the limits position relating to text for Limits and Logarithms and templates with grouping characters from the Accents group, you can right-click on the equation and select the Limit over/under text option from the menu. To choose which of the Brackets should be displayed, you can right-click on the expression within them and select the Hide/Show opening/closing bracket option from the menu. To control the Brackets size, you can right-click on the expression within them. The Stretch brackets option is selected by default so that the brackets can grow according to the expression within them, but you can deselect this option to prevent brackets from stretching. When this option is activated, you can also use the Match brackets to argument height option. To change the character position relating to text for overbraces/underbraces or overbars/underbars from the Accents group, you can right-click on the template and select the Char/Bar over/under text option from the menu. To choose which borders should be displayed for a Boxed formula from the Accents group, you can right-click on the equation and select the Border properties option from the menu, then select Hide/Show top/bottom/left/right border or Add/Hide horizontal/vertical/diagonal line. To specify whether empty placeholders should be displayed or not for a Matrix, you can right-click on it and select the Hide/Show placeholder option from the menu. To align some equation elements you can use the right-click menu options: To align equations within Cases with several conditions from the Brackets group (or equations of other types, if you've previously added new placeholders by pressing Enter), you can right-click on an equation, select the Alignment option from the menu, then select the alignment type: Top, Center, or Bottom. To align a Matrix vertically, you can right-click on the matrix, select the Matrix Alignment option from the menu, then select the alignment type: Top, Center, or Bottom. To align elements within a Matrix column horizontally, you can right-click on a placeholder within the column, select the Column Alignment option from the menu, then select the alignment type: Left, Center, or Right. Delete equation elements To delete a part of the equation, select it by dragging the mouse or holding down the Shift key and using the arrow buttons, then press the Delete key on the keyboard. A slot can only be deleted together with the template it belongs to. To delete the entire equation, select it completely by dragging the mouse or double-clicking on the equation box and press the Delete key on the keyboard. To delete some equation elements, you can also use the right-click menu options: To delete a Radical, you can right-click on it and select the Delete radical option from the menu. To delete a Subscript and/or Superscript, you can right-click on the expression that contains them and select the Remove subscript/superscript option from the menu. If the expression contains scripts that go before text, the Remove scripts option is available. To delete Brackets, you can right-click on the expression within them and select the Delete enclosing characters or Delete enclosing characters and separators option from the menu. If the expression within Brackets inclides more than one argument, you can right-click on the argument you want to delete and select the Delete argument option from the menu. If Brackets enclose more than one equation (i.e. Cases with several conditions), you can right-click on the equation you want to delete and select the Delete equation option from the menu. This option is also available for equations of other types if you've previously added new placeholders by pressing Enter. To delete a Limit, you can right-click on it and select the Remove limit option from the menu. To delete an Accent, you can right-click on it and select the Remove accent character, Delete char or Remove bar option from the menu (the available options differ depending on the selected accent). To delete a row or a column of a Matrix, you can right-click on the placeholder within the row/column you need to delete, select the Delete option from the menu, then select Delete Row/Column. Convert equations If you open an existing document containing equations which were created with an old version of equation editor (for example, with MS Office versions before 2007), you need to convert these equations to the Office Math ML format to be able to edit them. To convert an equation, double-click it. The warning window will appear: To convert the selected equation only, click the Yes button in the warning window. To convert all equations in this document, check the Apply to all equations box and click Yes. Once the equation is converted, you can edit it."
    },
   {
        "id": "UsageInstructions/InsertFootnotes.htm", 
        "title": "Insert footnotes", 
        "body": "In the Document Editor, you can insert footnotes to add explanations or comments for certain sentences or terms used in your text, make references to the sources, etc. Inserting footnotes To insert a footnote into your document, position the insertion point at the end of the text passage that you want to add the footnote to, switch to the References tab located at the top toolbar, click the Footnote icon on the top toolbar, or click the arrow next to the Footnote icon and select the Insert Footnote option from the menu, The footnote mark (i.e. the superscript character that indicates a footnote) appears in the text of the document, and the insertion point moves to the bottom of the current page. type in the footnote text. Repeat the above mentioned operations to add subsequent footnotes for other text passages in the document. The footnotes are numbered automatically. Display of footnotes in the document If you hover the mouse pointer over the footnote mark in the document text, a small pop-up window with the footnote text appears. Navigating through footnotes To easily navigate through the added footnotes in the text of the document, click the arrow next to the Footnote icon on the References tab located at the top toolbar, in the Go to Footnotes section, use the arrow to go to the previous footnote or the arrow to go to the next footnote. Editing footnotes To edit the footnotes settings, click the arrow next to the Footnote icon on the References tab located at the top toolbar, select the Notes Settings option from the menu, change the current parameters in the Notes Settings window that will appear: Activate the Footnote box to edit the footnotes only. Set the Location of footnotes on the page selecting one of the available options from the drop-down menu to the right: Bottom of page - to position footnotes at the bottom of the page (this option is selected by default). Below text - to position footnotes closer to the text. This option can be useful in cases when the page contains a short text. Adjust the footnotes Format: Number Format - select the necessary number format from the available ones: 1, 2, 3,..., a, b, c,..., A, B, C,..., i, ii, iii,..., I, II, III,.... Start at - use the arrows to set the number or letter you want to start numbering with. Numbering - select a way to number your footnotes: Continuous - to number footnotes sequentially throughout the document, Restart each section - to start footnote numbering with 1 (or another specified character) at the beginning of each section, Restart each page - to start footnote numbering with 1 (or another specified character) at the beginning of each page. Custom Mark - set a special character or a word you want to use as the footnote mark (e.g. * or Note1). Enter the necessary character/word into the text entry field and click the Insert button at the bottom of the Notes Settings window. Use the Apply changes to drop-down list if you want to apply the specified notes settings to the Whole document or the Current section only. Note: to use different footnotes formatting in separate parts of the document, you need to add section breaks first. When you finish, click the Apply button. Removing footnotes To remove a single footnote, position the insertion point directly before the footnote mark in the text and press Delete. Other footnotes will be renumbered automatically. To delete all the footnotes in the document, click the arrow next to the Footnote icon on the References tab located at the top toolbar, select the Delete All Notes option from the menu. choose the Delete All Footnotes option in the appeared window and click OK."
    },
   {
        "id": "UsageInstructions/InsertHeadersFooters.htm", 
        "title": "Insert headers and footers", 
        "body": "To add or remove a new header/footer, or edit one that already exists Document Editor, switch to the Insert tab of the top toolbar, click the Header/Footer icon on the top toolbar, select one of the following options: Edit Header to insert or edit the header text. Edit Footer to insert or edit the footer text. Remove Header to delete header. Remove Footer to delete footer. change the current parameters for headers or footers on the right sidebar: Set the Position of the text: to the top for headers or to the bottom for footers. Check the Different first page box to apply a different header or footer to the very first page or in case you don't want to add any header/ footer to it at all. Use the Different odd and even pages box to add different headers/footer for odd and even pages. The Link to Previous option is available in case you've previously added sections into your document. If not, it will be grayed out. Moreover, this option is also unavailable for the very first section (i.e. when a header or footer that belongs to the first section is selected). By default, this box is checked, so that the same headers/footers are applied to all the sections. If you select a header or footer area, you will see that the area is marked with the Same as Previous label. Uncheck the Link to Previous box to use different headers/footers for each section of the document. The Same as Previous label will no longer be displayed. To enter a text or edit the already entered text and adjust the header or footer settings, you can also double-click anywhere on the top or bottom margin of your document or click with the right mouse button there and select the only menu option - Edit Header or Edit Footer. To switch to the document body, double-click within the working area. The text you use as a header or footer will be displayed in gray. Note: please refer to the Insert page numbers section to learn how to add page numbers to your document."
    },
   {
        "id": "UsageInstructions/InsertImages.htm", 
        "title": "Insert images", 
        "body": "In the Document Editor, you can insert images in the most popular formats into your document. The following image formats are supported: BMP, GIF, JPEG, JPG, PNG. Insert an image To insert an image into the document text, place the cursor where you want the image to be put, switch to the Insert tab of the top toolbar, click the Image icon on the top toolbar, select one of the following options to load the image: the Image from File option will open a standard dialog window for to select a file. Browse your computer hard disk drive for the necessary file and click the Open button In the online editor, you can select several images at once. the Image from URL option will open the window where you can enter the web address of the requiredimage, and click the OK button the Image from Storage option will open the Select data source window. Select an image stored on your portal and click the OK button once the image is added, you can change its size, properties, and position. It's also possible to add a caption to the image. To learn more on how to work with captions for images, you can refer to this article. Move and resize images To change the image size, drag small squares situated on its edges. To maintain the original proportions of the selected image while resizing, hold down the Shift key and drag one of the corner icons. To alter the image position, use the icon that appears after hovering your mouse cursor over the image. Drag the image to the necessary position without releasing the mouse button. When you move the image, the guide lines are displayed to help you precisely position the object on the page (if the selected wrapping style is different from the inline). To rotate the image, hover the mouse cursor over the rotation handle and drag it clockwise or counterclockwise. To constrain the rotation angle to 15 degree increments, hold down the Shift key while rotating. Note: the list of keyboard shortcuts that can be used when working with objects is available here. Adjust image settings Some of the image settings can be altered using the Image settings tab of the right sidebar. To activate it click the image and choose the Image settings icon on the right. Here you can change the following properties: Size is used to view the Width and Height of the current image. If necessary, you can restore the actual image size clicking the Actual Size button. The Fit to Margin button allows you to resize the image, so that it occupies all the space between the left and right page margin. The Crop button is used to crop the image. Click the Crop button to activate cropping handles which appear on the image corners and in the center of each its side. Manually drag the handles to set the cropping area. You can move the mouse cursor over the cropping area border so that it turns into the icon and drag the area. To crop a single side, drag the handle located in the center of this side. To simultaneously crop two adjacent sides, drag one of the corner handles. To equally crop the two opposite sides of the image, hold down the Ctrl key when dragging the handle in the center of one of these sides. To equally crop all sides of the image, hold down the Ctrl key when dragging any of the corner handles. When the cropping area is specified, click the Crop button once again, or press the Esc key, or click anywhere outside of the cropping area to apply the changes. After the cropping area is selected, it's also possible to use the Crop to shape, Fill and Fit options available from the Crop drop-down menu. Click the Crop button once again and select the option you need: If you select the Crop to shape option, the picture will fill a certain shape. You can select a shape from the gallery, which opens when you hover your mouse pointer over the Crop to Shape option. You can still use the Fill and Fit options to choose the way your picture matches the shape. If you select the Fill option, the central part of the original image will be preserved and used to fill the selected cropping area, while the other parts of the image will be removed. If you select the Fit option, the image will be resized so that it fits the height and the width of the cropping area. No parts of the original image will be removed, but empty spaces may appear within the selected cropping area. Rotation is used to rotate the image by 90 degrees clockwise or counterclockwise as well as to flip the image horizontally or vertically. Click one of the buttons: to rotate the image by 90 degrees counterclockwise to rotate the image by 90 degrees clockwise to flip the image horizontally (left to right) to flip the image vertically (upside down) Wrapping Style is used to select a text wrapping style from the available ones - inline, square, tight, through, top and bottom, in front, behind (for more information see the advanced settings description below). Replace Image is used to replace the current image by loading another one From File, From Storage, or From URL. You can also find some of these options in the right-click menu. The menu options are: Cut, Copy, Paste - standard options which are used to cut or copy the selected text/object and paste the previously cut/copied text passage or object to the current cursor position. Arrange is used to bring the selected image to foreground, send it to background, move forward or backward as well as group or ungroup images to perform operations with several of them at once. To learn more on how to arrange objects, please refer to this page. Align is used to align the image to the left, in the center, to the right, at the top, in the middle or at the bottom. To learn more on how to align objects, please refer to this page. Wrapping Style is used to select a text wrapping style from the available ones - inline, square, tight, through, top and bottom, in front, behind - or edit the wrap boundary. The Edit Wrap Boundary option is available only if the selected wrapping style is not inline. Drag wrap points to customize the boundary. To create a new wrap point, click anywhere on the red line and drag it to the necessary position. Rotate is used to rotate the image by 90 degrees clockwise or counterclockwise as well as to flip the image horizontally or vertically. Crop is used to apply one of the cropping options: Crop, Fill or Fit. Select the Crop option from the submenu, then drag the cropping handles to set the cropping area, and click one of these three options from the submenu once again to apply the changes. Actual Size is used to change the current image size to the actual one. Replace image is used to replace the current image by loading another one From File or From URL. Image Advanced Settings is used to open the 'Image - Advanced Settings' window. When the image is selected, the Shape settings icon is also available on the right. You can click this icon to open the Shape settings tab on the right sidebar and adjust the shape Line type, size and color as well as change the shape type selecting another shape from the Change Autoshape menu. The shape of the image will change correspondingly. On the Shape Settings tab, you can also use the Show shadow option to add a shadow to the image. Adjust image advanced settings To change the image advanced settings, click the image with the right mouse button and select the Image Advanced Settings option from the right-click menu or just click the Show advanced settings link on the right sidebar. The image properties window will open: The Size tab contains the following parameters: Width and Height - use these options to change the width and/or height. If the Constant proportions button is clicked (in this case it looks like this ), the width and height will be changed together preserving the original image aspect ratio. To restore the actual size of the added image, click the Actual Size button. The Rotation tab contains the following parameters: Angle - use this option to rotate the image by an exactly specified angle. Enter the necessary value measured in degrees into the field or adjust it using the arrows on the right. Flipped - check the Horizontally box to flip the image horizontally (left to right) or check the Vertically box to flip the image vertically (upside down). The Text Wrapping tab contains the following parameters: Wrapping Style - use this option to change the way the image is positioned relative to the text: it will either be a part of the text (in case you select the inline style) or bypassed by it from all sides (if you select one of the other styles). Inline - the image is considered to be a part of the text, like a character, so when the text moves, the image moves as well. In this case the positioning options are inaccessible. If one of the following styles is selected, the image can be moved independently of the text and positioned on the page exactly: Square - the text wraps the rectangular box that bounds the image. Tight - the text wraps the actual image edges. Through - the text wraps around the image edges and fills in the open white space within the image. So that the effect can appear, use the Edit Wrap Boundary option from the right-click menu. Top and bottom - the text is only above and below the image. In front - the image overlaps the text. Behind - the text overlaps the image. If you select the square, tight, through, or top and bottom style, you will be able to set up some additional parameters - distance from text at all sides (top, bottom, left, right). The Position tab is available only if you select a wrapping style other than inline. This tab contains the following parameters that vary depending on the selected wrapping style: The Horizontal section allows you to select one of the following three image positioning types: Alignment (left, center, right) relative to character, column, left margin, margin, page or right margin, Absolute Position measured in absolute units i.e. Centimeters/Points/Inches (depending on the option specified on the File -> Advanced Settings... tab) to the right of character, column, left margin, margin, page or right margin, Relative position measured in percent relative to the left margin, margin, page or right margin. The Vertical section allows you to select one of the following three image positioning types: Alignment (top, center, bottom) relative to line, margin, bottom margin, paragraph, page or top margin, Absolute Position measured in absolute units i.e. Centimeters/Points/Inches (depending on the option specified on the File -> Advanced Settings... tab) below line, margin, bottom margin, paragraph, page or top margin, Relative position measured in percent relative to the margin, bottom margin, page or top margin. Move object with text ensures that the image moves along with the text to which it is anchored. Allow overlap makes is possible for two images to overlap if you drag them near each other on the page. The Alternative Text tab allows specifying a Title and Description which will be read to people with vision or cognitive impairments to help them better understand what information the image contains."
    },
   {
        "id": "UsageInstructions/InsertLineNumbers.htm", 
        "title": "Insert line numbers", 
        "body": "The ONLYOFFICE Document Editor can count lines in your document automatically. This feature can be useful when you need to refer to a specific line of the document, e.g. in a legal agreement or a code script. Use the Line Numbers tool to apply line numbering to the document. Please note that the line numbering sequence is not applied to the text in the objects such as tables, text boxes, charts, headers/footers, etc. These objects are treated as one line. Applying line numbering Open the Layout tab located at the top toolbar and click on the Line Numbers icon. Choose the required parameters for a quick set-up in the opened drop-down menu: Continuous - each line of the document will be assigned a sequence number. Restart Each Page - the line numbering sequence will restart on each page of the document. Restart Each Section - the line numbering sequence will restart in each section of the document. Please refer to this guide to learn more about section breaks. Suppress for Current Paragraph - the current paragraph will be skipped in the line numbering sequence. To exclude several paragraphs from the sequence, select them via the left-mouse button before applying this parameter. Specify the advanced parameters if needed. Click the Line Numbering Options item in the Line Numbers drop-down menu. Check the Add line numbering box to apply the line numbering to the document and to access the advanced parameters of the option: Start at sets the starting numeric value of the line numbering sequence. The parameter is set to 1 by default. From text specifies the distance between the line numbers and the text. Enter the required value in cm. The parameter is set to Auto by default. Count by specifies the sequence numbers that are displayed if not counted by 1, i.e. the numbers are counted in a bunch by 2s, 3s, 4s, etc. Enter the required numeric value. The parameter is set to 1 by default. Restart Each Page - the line numbering sequence will restart on each page of the document. Restart Each Sectionthe line numbering sequence will restart in each section of the document. Continuous - each line of the document will be assigned a sequence number. The Apply changes to parameter specifies the part of the document you want to assign sequence numbers to. Choose one of the available presets: Current section to apply line numbering to the selected section of the document; This point forward to apply line numbering to the text following the current cursor position; Whole document to apply line numbering to the whole document. The parameter is set to Whole document by default. Click OK to apply the changes. Removing line numbering To remove the line numbering sequence, open the Layout tab located at the top toolbar and click on the Line Numbers icon, choose the None option in the opened drop-down menu or choose the Line Numbering Options item in the menu and deactivate the Add line numbering box in the opened Line Numbers window."
    },
   {
        "id": "UsageInstructions/InsertPageNumbers.htm", 
        "title": "Insert page numbers", 
        "body": "To insert page numbers in the Document Editor, switch to the Insert tab of the top toolbar, click the Header/Footer icon on the top toolbar, choose the Insert Page Number submenu, select one of the following options: To add a page number to each page of your document, select the page number position on the page. To insert a page number at the current cursor position, select the To Current Position option. Note: to insert a current page number at the current cursor position you can also use the Ctrl+Shift+P key combination. OR switch to the Insert tab of the top toolbar, click the Header/Footer icon on the top toolbar, click the Insert page number option in the menu and choose the position of the page number. To insert the total number of pages in your document (e.g. if you want to create the Page X of Y entry): put the cursor where you want to insert the total number of pages, click the Header/Footer icon on the top toolbar, select the Insert number of pages option. To edit the page number settings, double-click the page number added, change the current parameters on the right sidebar: Set the Position of page numbers on the page accordingly to the top and bottom of the page. Check the Different first page box to apply a different page number to the very first page or in case you don't want to add any number to it at all. Use the Different odd and even pages box to insert different page numbers for odd and even pages. The Link to Previous option is available in case you've previously added sections into your document. If not, it will be grayed out. Moreover, this option is also unavailable for the very first section (i.e. when a header or footer that belongs to the first section is selected). By default, this box is checked, so that unified numbering is applied to all the sections. If you select a header or footer area, you will see that the area is marked with the Same as Previous label. Uncheck the Link to Previous box to use different page numbering for each section of the document. The Same as Previous label will no longer be displayed. The Page Numbering section allows adjusting page numbering options throughout different sections of the document. The Continue from previous section option is selected by default and makes it possible to keep continuous page numbering after a section break. If you want to start page numbering with a specific number in the current section of the document, select the Start at radio button and enter the required starting value in the field on the right. To return to the document editing, double-click within the working area."
    },
   {
        "id": "UsageInstructions/InsertReferences.htm", 
        "title": "Insert references", 
        "body": "ONLYOFFICE Document Editor supports Mendeley, Zotero and EasyBib reference managers to insert references into your document. Mendeley Connect ONLYOFFICE to Mendeley Login to your Mendeley account. In your document, switch to the Plugins tab and choose Mendeley, a sidebar will open on the left side of your document. Click the Copy Link and Open Form button. The browser opens a form on the Mendeley site. Complete this form and note the Application ID for ONLYOFFICE. Switch back to your document. Enter the Application ID and click Save. Click Login. Click Proceed. Now ONLYOFFICE is connected to your Mendeley account. Inserting references Open the document and place the cursor on the spot where you want to insert the reference(s). Switch to the Plugins tab and choose Mendeley. Enter a search text and hit Enter on your keyboard. Click one or more check-boxes. [Optional] Enter a new search text and click on one or more check-boxes. Choose the reference style from the Style pull-down menu. Click the Insert Bibliography button. Zotero Connect ONLYOFFICE to Zotero Login to your Zotero account. In your document, switch to the Plugins tab and choose Zotero, a sidebar will open on the left side of your document. Click the Zotero API settings link. On the Zotero site, create a new key for Zotero, copy it and save it for later use. Switch to your document and paste the API key. Click Save. Now ONLYOFFICE is connected to your Zotero account. Inserting references Open the document and place the cursor on the spot where you want to insert the reference(s). Switch to the Plugins tab and choose Zotero. Enter a search text and hit Enter on your keyboard. Click one or more check-boxes. [Optional] Enter a new search text and click on one or more check-boxes. Choose the reference style from the Style pull-down menu. Click the Insert Bibliography button. EasyBib Open the document and place the cursor on the spot where you want to insert the reference(s). Switch to the Plugins tab and choose EasyBib. Select the type of source you want to find. Enter a search text and hit Enter on your keyboard. Click '+' on the right side of the suitable Book/Journal article/Website. It will be added to Bibliography. Select references style. Click the Add Bibliography to Doc to insert the references."
    },
   {
        "id": "UsageInstructions/InsertSmartArt.htm", 
        "title": "Insert SmartArt objects", 
        "body": "SmartArt graphics are used to create a visual representation of a hierarchical structure by choosing a layout that fits best. Insert SmartArt objects or edit the ones added in third-party editors. To insert a SmartArt object, go to the Insert tab, click the SmartArt button, hover over one of the available layout styles, e.g., List or Process, choose one of the available layout types from the list appeared to the right of the highlighted menu item. You can customize the SmartArt settings in the right panel: Please note that color, style and form type settings can be customized individually. Fill - use this section to select the SmartArt object fill. You can choose the following options: Color Fill - select this option to specify the solid color to fill the inner space of the selected SmartArt object. Click the colored box below and select the necessary color from the available color sets or specify any color you like: Gradient Fill - use this option to fill the shape with two or more fading colors. Customize your gradient fill with no constraints. Click the Shape settings icon to open the Fill menu on the right sidebar: Available menu options: Style - choose between Linear or Radial: Linear is used  when you need your colors to flow from left-to-right, top-to-bottom, or at any angle you chose in a single direction. The Direction preview window displays the selected gradient color, click the arrow to choose a preset gradient direction. Use Angle settings for a precise gradient angle. Radial is used to move from the center as it starts at a single point and emanates outward. Gradient Point is a specific point for transition from one color to another. Use the Add Gradient Point button or slider bar to add a gradient point. You can add up to 10 gradient points. Each next gradient point added will in no way affect the current gradient fill appearance. Use the Remove Gradient Point button to delete a certain gradient point. Use the slider bar to change the location of the gradient point or specify Position in percentage for precise location. To apply a color to a gradient point, click a point on the slider bar, and then click Color to choose the color you want. Picture or Texture - select this option to use an image or a predefined texture as the SmartArt object background. If you wish to use an image as a background for the SmartArt object, you can add an image From File by selecting it on your computer hard disc drive, From URL by inserting the appropriate URL address into the opened window, or From Storage by selecting the required image stored on your portal. If you wish to use a texture as a background for the SmartArt object, open the From Texture menu and select the necessary texture preset. Currently, the following textures are available: canvas, carton, dark fabric, grain, granite, grey paper, knit, leather, brown paper, papyrus, wood. In case the selected Picture has less or more dimensions than the SmartArt object has, you can choose the Stretch or Tile setting from the dropdown list. The Stretch option allows you to adjust the image size to fit the SmartArt object size so that it could fill the space completely. The Tile option allows you to display only a part of the bigger image keeping its original dimensions or repeat the smaller image keeping its original dimensions over the SmartArt object surface so that it could fill the space completely. Note: any selected Texture preset fills the space completely, but you can apply the Stretch effect if necessary. Pattern - select this option to fill the SmartArt object with a two-colored design composed of regularly repeated elements. Pattern - select one of the predefined designs from the menu. Foreground color - click this color box to change the color of the pattern elements. Background color - click this color box to change the color of the pattern background. No Fill - select this option if you don't want to use any fill. Line - use this section to change the width, color or type of the SmartArt object line. To change the line width, select one of the available options from the Size dropdown list. The available options are: 0.5 pt, 1 pt, 1.5 pt, 2.25 pt, 3 pt, 4.5 pt, 6 pt. Alternatively, select the No Line option if you don't want to use any line. To change the line color, click on the colored box below and select the necessary color. To change the line type, select the necessary option from the corresponding dropdown list (a solid line is applied by default, you can change it to one of the available dashed lines). Wrapping Style - use this section to select a text wrapping style from the available ones - inline, square, tight, through, top and bottom, in front, behind (for more information see the advanced settings description below). Show shadow - check this box to make the SmartArt object cast a shadow. Click the Show advanced settings link to open the advanced settings."
    },
   {
        "id": "UsageInstructions/InsertSymbols.htm", 
        "title": "Insert symbols and characters", 
        "body": "In the Document Editor, to insert a special symbol which can not be typed on the keyboard, use the Insert symbol option and follow these simple steps: place the cursor where a special symbol should be inserted, switch to the Insert tab of the top toolbar, click the Symbol, The Symbol dialog box will appear, and you will be able to select the required symbol, use the Range section to quickly find the necessary symbol. All symbols are divided into specific groups, for example, select 'Currency Symbols' if you want to insert a currency character. If the required character is not in the set, select a different font. Many of them also have characters that differ from the standard set. Or enter the Unicode hex value of the required symbol you want into the Unicode hex value field. This code can be found in the Character map. You can also use the Special characters tab to choose a special character from the list. The previously used symbols are also displayed in the Recently used symbols field, click Insert. The selected character will be added to the document. Insert ASCII symbols The ASCII table is also used to add characters. To do this, hold down the ALT key and use the numeric keypad to enter the character code. Note: be sure to use the numeric keypad, not the numbers on the main keyboard. To enable the numeric keypad, press the Num Lock key. For example, to add a paragraph character (§), press and hold down ALT while typing 789, and then release the ALT key. Insert symbols using the Unicode table Additional characters and symbols can also be found in the Windows symbol table. To open this table, do of the following: in the Search field write 'Character table' and open it, simultaneously press Win + R, and then in the following window type charmap.exe and click OK. In the opened Character Map, select one of the Character sets, Groups, and Fonts. Next, click on the required characters, copy them to the clipboard, and paste where necessary."
    },
   {
        "id": "UsageInstructions/InsertTables.htm", 
        "title": "Insert tables", 
        "body": "Insert a table To insert a table in the Document Editor, place the cursor where the table should be added, switch to the Insert tab of the top toolbar, click the Table icon on the top toolbar, select the option to create a table: either a table with predefined number of cells (10 by 8 cells maximum) If you want to quickly add a table, just select the number of rows (8 maximum) and columns (10 maximum). or a custom table In case you need more than 10 by 8 cell table, select the Insert Custom Table option that will open the window where you can enter the necessary number of rows and columns respectively, then click the OK button. If you want to draw a table using the mouse, select the Draw Table option. This can be useful, if you want to create a table with rows and columns of different sizes. The mouse cursor will turn into a pencil . Draw a rectangular shape where you want to add a table, then add rows by drawing horizontal lines and columns by drawing vertical lines within the table boundary. If you want to convert an existing text into a table, select the Convert Text to Table option. This feature can prove useful when you already have some text that you have decided to arrange into a table. The Convert Text to Table window consists of 3 sections: Table Size. Choose the required number of columns/rows you want to distribute your text into. You can either use the up/down arrow buttons or enter the number manually via keyboard. Autofit Behavior. Check the needed option to set the text fitting behavior: Fixed column width (set to Auto by default. You can either use the up/down arrow buttons or enter the number manually via keyboard), Autofit to contents (the column width corresponds with the text length), Autofit to window (the column width corresponds with the page width). Separate Text at. Check the needed option to set a delimiter type for your text: Paragraphs, Tabs, Semicolons, and Other (enter the preferred delimiter manually). Click OK to convert your text to table. If you want to insert a table as an OLE object: Select the Insert Spreadsheet option. The corresponding window appears where you can enter the required data and format it using the Spreadsheet Editor formatting tools such as choosing font, type and style, setting number format, inserting functions, formatting tables etc. The header contains the Visible area button in the top right corner of the window. Choose the Edit Visible Area option to select the area that will be shown when the object is inserted into the document; other data is not lost, it is just hidden. Click Done when ready. Click the Show Visible Area button to see the selected area that will have a blue border. When ready, click the Save & Exit button. once the table is added you can change its properties, size and position. To resize a table, hover the mouse cursor over the handle in its lower right corner and drag it until the table reaches the necessary size. You can also manually change the width of a certain column or the height of a row. Move the mouse cursor over the right border of the column so that the cursor turns into the bidirectional arrow and drag the border to the left or right to set the necessary width. To change the height of a single row manually, move the mouse cursor over the bottom border of the row so that the cursor turns into the bidirectional arrow and drag the border up or down. To move a table, hold down the handle in its upper left corner and drag it to the necessary place in the document. It's also possible to add a caption to the table. To learn more on how to work with captions for tables, you can refer to this article. Select a table or its part To select an entire table, click the handle in its upper left corner. To select a certain cell, move the mouse cursor to the left side of the necessary cell so that the cursor turns into the black arrow , then left-click. To select a certain row, move the mouse cursor to the left border of the table next to the necessary row so that the cursor turns into the horizontal black arrow , then left-click. To select a certain column, move the mouse cursor to the top border of the necessary column so that the cursor turns into the downward black arrow , then left-click. It's also possible to select a cell, row, column or table using options from the contextual menu or from the Rows & Columns section on the right sidebar. Note: to move around in a table you can use keyboard shortcuts. Adjust table settings Some of the table properties as well as its structure can be altered using the right-click menu. The menu options are: Cut, Copy, Paste - standard options which are used to cut or copy the selected text/object and paste the previously cut/copied text passage or object to the current cursor position. Select is used to select a row, column, cell, or table. Insert is used to insert a row above or row below the row where the cursor is placed as well as to insert a column at the left or right side from the column where the cursor is placed. It's also possible to insert several rows or columns. If you select the Several Rows/Columns option, the Insert Several window will appear. Select the Rows or Columns option from the list, specify the number of rows/column you want to add, choose where they should be added: Above the cursor or Below the cursor and click OK. Delete is used to delete a row, column, table or cells. If you select the Cells option, the Delete Cells window will open, where you can select if you want to Shift cells left, Delete entire row, or Delete entire column. Merge Cells is available if two or more cells are selected and is used to merge them. It's also possible to merge cells by erasing a boundary between them using the eraser tool. To do this, click the Table icon on the top toolbar, choose the Erase Table option. The mouse cursor will turn into the eraser . Move the mouse cursor over the border between the cells you want to merge and erase it. Split Cell... is used to open a window where you can select the needed number of columns and rows the cell will be split in. It's also possible to split a cell by drawing rows or columns using the pencil tool. To do this, click the Table icon on the top toolbar, choose the Draw Table option. The mouse cursor will turn into the pencil . Draw a horizontal line to create a row or a vertical line to create a column. Distribute rows is used to adjust the selected cells so that they have the same height without changing the overall table height. Distribute columns is used to adjust the selected cells so that they have the same width without changing the overall table width. Cell Vertical Alignment is used to align the text top, center or bottom in the selected cell. Text Direction - is used to change the text orientation in a cell. You can place the text horizontally, vertically from top to bottom (Rotate Text Down), or vertically from bottom to top (Rotate Text Up). Table Advanced Settings is used to open the 'Table - Advanced Settings' window. Hyperlink is used to insert a hyperlink. Paragraph Advanced Settings is used to open the 'Paragraph - Advanced Settings' window. You can also change the table properties on the right sidebar: Rows and Columns are used to select the table parts that you want to be highlighted. For rows: Header - to highlight the first row Total - to highlight the last row Banded - to highlight every other row For columns: First - to highlight the first column Last - to highlight the last column Banded - to highlight every other column Select from Template is used to choose a table template from the available ones. Borders Style is used to select the border size, color, style as well as background color. Rows &amp; Columns is used to perform some operations with the table: select, delete, insert rows and columns, merge cells, split a cell. Rows &amp; Columns Size is used to adjust the width and height of the currently selected cell. In this section, you can also Distribute rows so that all the selected cells have equal height or Distribute columns so that all the selected cells have equal width. Add formula is used to insert a formula into the selected table cell. Repeat as header row at the top of each page is used to insert the same header row at the top of each page in long tables. Convert Table to Text is used to arrange the table in a plain text form. The Convert Table to Text window sets the delimiter type for the conversion: Paragraph marks, Tabs, Semicolons, and Other (enter the preferred delimiter manually). The text in each cell of the table is considered a separate and individual element of the future text. Show advanced settings is used to open the 'Table - Advanced Settings' window. Adjust table advanced settings To change the advanced table properties, click the table with the right mouse button and select the Table Advanced Settings option from the right-click menu or use the Show advanced settings link on the right sidebar. The table properties window will open: The Table tab allows changing the properties of the entire table. The Table Size section contains the following parameters: Width - by default, the table width is automatically adjusted to fit the page width, i.e. the table occupies all the space between the left and right page margin. You can check this box and specify the necessary table width manually. Measure in allows specifying the table width in absolute units i.e. Centimeters/Points/Inches (depending on the option specified on the File -> Advanced Settings... tab) or in Percent of the overall page width. Note: you can also adjust the table size manually changing the row height and column width. Move the mouse cursor over a row/column border until it turns into the bidirectional arrow and drag the border. You can also use the markers on the horizontal ruler to change the column width and the markers on the vertical ruler to change the row height. Automatically resize to fit contents - allows automatically change the width of each column in accordance with the text within its cells. The Default Cell Margins section allows changing the space between the text within the cells and the cell border used by default. The Options section allows changing the following parameter: Spacing between cells - the cell spacing which will be filled with the Table Background color. The Cell tab allows changing the properties of individual cells. First you need to select the required cell or select the entire table to change the properties of all its cells. The Cell Size section contains the following parameters: Preferred width - allows setting the preferred cell width. This is the size that a cell strives to fit, but in some cases, it may not be possible to fit this exact value. For example, if the text within a cell exceeds the specified width, it will be broken into the next line so that the preferred cell width remains unchanged, but if you insert a new column, the preferred width will be reduced. Measure in - allows specifying the cell width in absolute units i.e. Centimeters/Points/Inches (depending on the option specified on the File -> Advanced Settings... tab) or in Percent of the overall table width. Note: you can also adjust the cell width manually. To make a single cell in a column wider or narrower than the overall column width, select the necessary cell and move the mouse cursor over its right border until it turns into the bidirectional arrow, then drag the border. To change the width of all the cells in a column, use the markers on the horizontal ruler to change the column width. The Cell Margins allows adjusting the space between the text within the cells and the cell border. By default, the standard values are used (the default, these values can also be altered on the Table tab), but you can uncheck the Use default margins box and enter the necessary values manually. The Cell Options section allows changing the following parameter: The Wrap text option is enabled by default. It allows wrapping the text within a cell that exceeds its width onto the next line expanding the row height and keeping the column width unchanged. The Borders &amp; Background tab contains the following parameters: Border parameters (size, color and presence or absence) - set the border size, select its color and choose the way it will be displayed in the cells. Note: in case you choose not to show the table borders by clicking the button or deselecting all the borders manually on the diagram, they will be indicated with a dotted line in the document. To make them disappear at all, click the Nonprinting characters icon on the Home tab of the top toolbar and select the Hidden Table Borders option. Cell Background - the color for the background within the cells (available only if one or more cells are selected or the Allow spacing between cells option is selected at the Table tab). Table Background - the color for the table background or the space background between the cells in case the Allow spacing between cells option is selected on the Table tab. The Table Position tab is available only if the Flow table option on the Text Wrapping tab is selected and contains the following parameters: Horizontal parameters include the table alignment (left, center, right) relative to margin, page or text as well as the table position to the right of margin, page or text. Vertical parameters include the table alignment (top, center, bottom) relative to margin, page or text as well as the table position below margin, page or text. The Options section allows changing the following parameters: Move object with text ensures that the table moves with the text. Allow overlap controls whether two tables are merged into one large table or overlap if you drag them near each other on the page. The Text Wrapping tab contains the following parameters: Text wrapping style - Inline table or Flow table. Use the necessary option to change the way the table is positioned relative to the text: it will either be a part of the text (in case you select the inline table) or bypassed by it from all sides (if you select the flow table). After you select the wrapping style, the additional wrapping parameters can be set both for inline and flow tables: For the inline table, you can specify the table alignment and indent from left. For the flow table, you can specify the distance from text and table position on the Table Position tab. The Alternative Text tab allows specifying the Title and Description which will be read to people with vision or cognitive impairments to help them better understand the contents of the table."
    },
   {
        "id": "UsageInstructions/InsertTextObjects.htm", 
        "title": "Insert text objects", 
        "body": "In the Document Editor, you can make your text more emphatic and draw attention to a specific part of the document, you can insert a text box (a rectangular frame that allows entering text within it) or a Text Art object (a text box with a predefined font style and color that allows applying some effects to the text). Add a text object You can add a text object anywhere on the page. To do that: switch to the Insert tab of the top toolbar, select the necessary text object type: to add a text box, click the Text Box icon on the top toolbar, then click where the text box should be added, hold the mouse button and drag the text box border to specify its size. When you release the mouse button, the insertion point will appear in the added text box, allowing you to enter your text. Note: it's also possible to insert a text box by clicking the Shape icon on the top toolbar and selecting the shape from the Basic Shapes group. to add a Text Art object, click the Text Art icon on the top toolbar, then click on the desired style template – the Text Art object will be added at the current cursor position. Select the default text within the text box with the mouse and replace it with your own text. click outside of the text object to apply the changes and return to the document. The text within the text object is a part of the latter (when you move or rotate the text object, the text moves or rotates with it). As the inserted text object represents a rectangular frame with text in it (Text Art objects have invisible text box borders by default), and this frame is a common autoshape, you can change both the shape and text properties. To delete the added text object, click on the text box border and press the Delete key on the keyboard. The text within the text box will also be deleted. Format a text box Select the text box by clicking on its border to be able to change its properties. When the text box is selected, its borders are displayed as solid (not dashed) lines. to resize, move, rotate the text box, use the special handles on the edges of the shape. to edit the text box fill, line, wrapping style or replace the rectangular box with a different shape, click the Shape settings icon on the right sidebar and use the corresponding options. to align the text box on the page, arrange text boxes as related to other objects, rotate or flip a text box, change a wrapping style or access the shape advanced settings, right-click on the text box border and use the contextual menu options. To learn more on how to arrange and align objects, please refer to this page. Format the text within the text box Click the text within the text box to change its properties. When the text is selected, the text box borders are displayed as dashed lines. Note: it's also possible to change the text formatting when the text box (not the text itself) is selected. In thus case, any changes will be applied to all the text within the text box. Some font formatting options (font type, size, color and decoration styles) can be applied to the previously selected text fragment separately. To rotate the text within the text box, right-click the text, select the Text Direction option and then choose one of the available options: Horizontal (is selected by default), Rotate Text Down (sets a vertical direction, from top to bottom) or Rotate Text Up (sets a vertical direction, from bottom to top). To align the text vertically within the text box, right-click the text, select the Vertical Alignment option and then choose one of the available options: Align Top, Align Center or Align Bottom. Other formatting options that you can apply are the same as the ones for regular text. Please refer to the corresponding help sections to learn more about the necessary operation. You can: align the text horizontally within the text box adjust the font type, size, color, apply decoration styles and formatting presets set line spacing, change paragraph indents, adjust tab stops for the multi-line text within the text box insert a hyperlink You can also click the Text Art settings icon on the right sidebar and change some style parameters. Edit a Text Art style Select a text object and click the Text Art settings icon on the right sidebar. Change the applied text style by selecting a new Template from the gallery. You can also change the basic style by selecting a different font type, size etc. Change the font Fill. You can choose the following options: Color Fill - select this option to specify the solid color to fill the inner space of letters. Click the colored box below and select the necessary color from the available color sets or specify any color you like: Gradient Fill - select this option to fill the letters with two colors which smoothly change from one to another. Style - choose one of the available options: Linear (colors change in a straight line i.e. along a horizontal/vertical axis or diagonally at a 45 degree angle) or Radial (colors change in a circular path from the center to the edges). Direction - choose a template from the menu. If the Linear gradient is selected, the following directions are available: top-left to bottom-right, top to bottom, top-right to bottom-left, right to left, bottom-right to top-left, bottom to top, bottom-left to top-right, left to right. If the Radial gradient is selected, only one template is available. Gradient - click on the left slider under the gradient bar to activate the color box which corresponds to the first color. Click on the color box on the right to choose the first color in the palette. Drag the slider to set the gradient stop i.e. the point where one color changes into another. Use the right slider under the gradient bar to specify the second color and set the gradient stop. Note: if one of these two options is selected, you can also set an Opacity level dragging the slider or entering the percent value manually. The default value is 100%. It corresponds to the full opacity. The 0% value corresponds to the full transparency. No Fill - select this option if you don't want to use any fill. Adjust the font Line width, color and type. To change the line width, select one of the available options from the Size dropdown list. The available options are: 0.5 pt, 1 pt, 1.5 pt, 2.25 pt, 3 pt, 4.5 pt, 6 pt. Alternatively, select the No Line option if you don't want to use any line. To change the line color, click on the colored box below and select the necessary color. To change the line type, select the necessary option from the corresponding dropdown list (a solid line is applied by default, you can change it to one of the available dashed lines). Apply a text effect by selecting the necessary text transformation type from the Transform gallery. You can adjust the degree of the text distortion by dragging the pink diamond-shaped handle."
    },
   {
        "id": "UsageInstructions/Jitsi.htm", 
        "title": "Make Audio and Video Calls", 
        "body": "Audio and video calls are immediately accessible from ONLYOFFICE Document Editor, using Jitsi plugin. Jitsi provides video conferencing capabilities that are secure and easy to deploy. Note: Jitsi plugin is not installed by default and shall be added manually. Please, refer to the corresponding article to find the manual installation guide Adding plugins to ONLYOFFICE Cloud or Adding new plugins to server editors Switch to the Plugins tab and click the Jitsi icon on the top toolbar. Fill in the fields at the bottom of the left sidebar before you start a call: Domain - enter the domain name if you want to connect your domain. Room name - enter the name of the meeting room. This field is mandatory and you cannot start a call if you leave it out. Click the Start button to open the Jitsi Meet iframe. Enter your name and allow camera and microphone access to your browser. If you want to close the Jitsi Meet iframe click the Stop button at the bottom of the left. Click the Join the meeting button to start a call with audio or click the arrow to join without audio. The Jitsi Meet iframe interface elements before launching a meeting: Audio settings and Mute/Unmute Click the arrow to access the preview of Audio Settings. Click the micro to mute/unmute your microphone. Video settings and Start/Stop Click the arrow to access video preview. Click the camera to start/stop your video. Invite people Click this button to invite more people to your meeting. Share the meeting by copying the meeting link, or Share meeting invitation by copying it, or via your default email, Google email, Outlook email or Yahoo email. Embed the meeting by copying the link. Use one of the available dial-in numbers to join the meeting. Select background Select or add a virtual background for your meeting. Share your desktop by choosing the appropriate option: Screen, Window or Tab. Settings Configure advanced settings that are organized in the following categories: Devices for setting up your Microphone, Camera and Audio Output, and playing a test sound. Profile for setting up your name to be displayed and gravatar email, hide/show self view. Calendar for integration your Google or Microsoft calendar. Sounds for selecting the actions to play the sound on. More for configuring some additional options: enable/disable pre meeting screen and keyboard shortcuts, set up a language and desktop sharing frame rate. Interface elements that appear during a video conference: Click the side arrow on the right to display the participant thumbnails at the top. The timer at the iframe top shows the meeting duration. Open chat Type a text message or create a poll. Participants View the list of the meeting participants, invite more participants and search a participant. More Actions Find a range of options to use all the available Jitsi features to the full.Scroll through the options to see them all. Available options, Start screen sharing Invite people Enter/Exit tile view Performance settings for adjusting the quality View full screen Security options Lobby mode for participants to join the meeting after the moderator’s approval; Add password mode for participants to join the meeting with a password; End-to-end encryption is an experimental method of making secure calls (mind the restrictions like disabling server-side provided services and using browsers that support insertable streams). Start live stream Mute everyone Disable everyone’s camera Share video Select background Speaker stats Settings View shortcuts Embed meeting Leave feedback Help Leave the meeting Click it whenever you wish to end a call."
    },
   {
        "id": "UsageInstructions/LineSpacing.htm", 
        "title": "Set paragraph line spacing", 
        "body": "In the Document Editor, you can set the line height for the text lines within the paragraph as well as the margins between the current paragraph and the previous one or the subsequent paragraphs. To do that, place the cursor within the required paragraph, or select several paragraphs with the mouse or the whole text by pressing the Ctrl+A key combination, use the corresponding fields on the right sidebar to achieve the desired results: Line Spacing - set the line height for the text lines within the paragraph. You can select among three options: at least (sets the minimum line spacing that is needed to fit the largest font or graphic in the line), multiple (sets line spacing that can be expressed in numbers greater than 1), exactly (sets fixed line spacing). You can specify the necessary value in the field on the right. Paragraph Spacing defines the amount of spacing between paragraphs. Before defines the amount of spacing before the paragraph. After defines the amount of spacing after the paragraph. Don't add interval between paragraphs of the same style - please check this box if you don't need any spacing between paragraphs of the same style. These parameters can also be found in the Paragraph - Advanced Settings window. To open the Paragraph - Advanced Settings window, right-click the text and choose the Paragraph Advanced Settings option from the menu or use the Show advanced settings option on the right sidebar. Then switch to the Indents &amp; Spacing tab and go to the Spacing section. To quickly change the current paragraph line spacing, you can also use the Paragraph line spacing icon on the Home tab of the top toolbar selecting the required value from the list: 1.0, 1.15, 1.5, 2.0, 2.5, or 3.0 lines."
    },
   {
        "id": "UsageInstructions/MathAutoCorrect.htm", 
        "title": "AutoCorrect Features", 
        "body": "The AutoCorrect features in ONLYOFFICE Document Editor are used to automatically format text when detected or insert special math symbols by recognizing particular character usage. The available AutoCorrect options are listed in the corresponding dialog box. To access it, go to the File tab -> Advanced Settings -> Proofing -> AutoCorrect Options. The AutoCorrect dialog box consists of four tabs: Math Autocorrect, Recognized Functions, AutoFormat As You Type, and Text AutoCorrect. Math AutoCorrect When working with equations, you can insert a lot of symbols, accents, and mathematical operation signs typing them on the keyboard instead of choosing a template from the gallery. In the equation editor, place the insertion point within the necessary placeholder, type a math autocorrect code, then press Spacebar. The entered code will be converted into the corresponding symbol, and the space will be eliminated. Note: The codes are case sensitive. You can add, modify, restore, and remove autocorrect entries from the AutoCorrect list. Go to the File tab -> Advanced Settings -> Proofing -> AutoCorrect Options -> Math AutoCorrect. Adding an entry to the AutoCorrect list Enter the autocorrect code you want to use in the Replace box. Enter the symbol to be assigned to the code you entered in the By box. Click the Add button. Modifying an entry on the AutoCorrect list Select the entry to be modified. You can change the information in both fields: the code in the Replace box or the symbol in the By box. Click the Replace button. Removing entries from the AutoCorrect list Select an entry to remove from the list. Click the Delete button. To restore the previously deleted entries, select the entry to be restored from the list and click the Restore button. Use the Reset to default button to restore default settings. Any autocorrect entry you added will be removed and the changed ones will be restored to their original values. To disable Math AutoCorrect and to avoid automatic changes and replacements, uncheck the Replace text as you type box. The table below contains all the currently supported codes available in the Document Editor. The full list of the supported codes can also be found on the File tab -> Advanced Settings -> Proofing -> AutoCorrect Options -> Math AutoCorrect. The supported codes Code Symbol Category !! Symbols ... Dots :: Operators := Operators /< Relational operators /> Relational operators /= Relational operators \\above Above/Below scripts \\acute Accents \\aleph Hebrew letters \\alpha Greek letters \\Alpha Greek letters \\amalg Binary operators \\angle Geometry notation \\aoint Integrals \\approx Relational operators \\asmash Arrows \\ast Binary operators \\asymp Relational operators \\atop Operators \\bar Over/Underbar \\Bar Accents \\because Relational operators \\begin Delimiters \\below Above/Below scripts \\bet Hebrew letters \\beta Greek letters \\Beta Greek letters \\beth Hebrew letters \\bigcap Large operators \\bigcup Large operators \\bigodot Large operators \\bigoplus Large operators \\bigotimes Large operators \\bigsqcup Large operators \\biguplus Large operators \\bigvee Large operators \\bigwedge Large operators \\binomial Equations \\bot Logic notation \\bowtie Relational operators \\box Symbols \\boxdot Binary operators \\boxminus Binary operators \\boxplus Binary operators \\bra Delimiters \\break Symbols \\breve Accents \\bullet Binary operators \\cap Binary operators \\cbrt Square roots and radicals \\cases Symbols \\cdot Binary operators \\cdots Dots \\check Accents \\chi Greek letters \\Chi Greek letters \\circ Binary operators \\close Delimiters \\clubsuit Symbols \\coint Integrals \\cong Relational operators \\coprod Math operators \\cup Binary operators \\dalet Hebrew letters \\daleth Hebrew letters \\dashv Relational operators \\dd Double-struck letters \\Dd Double-struck letters \\ddddot Accents \\dddot Accents \\ddot Accents \\ddots Dots \\defeq Relational operators \\degc Symbols \\degf Symbols \\degree Symbols \\delta Greek letters \\Delta Greek letters \\Deltaeq Operators \\diamond Binary operators \\diamondsuit Symbols \\div Binary operators \\dot Accents \\doteq Relational operators \\dots Dots \\doublea Double-struck letters \\doubleA Double-struck letters \\doubleb Double-struck letters \\doubleB Double-struck letters \\doublec Double-struck letters \\doubleC Double-struck letters \\doubled Double-struck letters \\doubleD Double-struck letters \\doublee Double-struck letters \\doubleE Double-struck letters \\doublef Double-struck letters \\doubleF Double-struck letters \\doubleg Double-struck letters \\doubleG Double-struck letters \\doubleh Double-struck letters \\doubleH Double-struck letters \\doublei Double-struck letters \\doubleI Double-struck letters \\doublej Double-struck letters \\doubleJ Double-struck letters \\doublek Double-struck letters \\doubleK Double-struck letters \\doublel Double-struck letters \\doubleL Double-struck letters \\doublem Double-struck letters \\doubleM Double-struck letters \\doublen Double-struck letters \\doubleN Double-struck letters \\doubleo Double-struck letters \\doubleO Double-struck letters \\doublep Double-struck letters \\doubleP Double-struck letters \\doubleq Double-struck letters \\doubleQ Double-struck letters \\doubler Double-struck letters \\doubleR Double-struck letters \\doubles Double-struck letters \\doubleS Double-struck letters \\doublet Double-struck letters \\doubleT Double-struck letters \\doubleu Double-struck letters \\doubleU Double-struck letters \\doublev Double-struck letters \\doubleV Double-struck letters \\doublew Double-struck letters \\doubleW Double-struck letters \\doublex Double-struck letters \\doubleX Double-struck letters \\doubley Double-struck letters \\doubleY Double-struck letters \\doublez Double-struck letters \\doubleZ Double-struck letters \\downarrow Arrows \\Downarrow Arrows \\dsmash Arrows \\ee Double-struck letters \\ell Symbols \\emptyset Set notations \\emsp Space characters \\end Delimiters \\ensp Space characters \\epsilon Greek letters \\Epsilon Greek letters \\eqarray Symbols \\equiv Relational operators \\eta Greek letters \\Eta Greek letters \\exists Logic notations \\forall Logic notations \\fraktura Fraktur letters \\frakturA Fraktur letters \\frakturb Fraktur letters \\frakturB Fraktur letters \\frakturc Fraktur letters \\frakturC Fraktur letters \\frakturd Fraktur letters \\frakturD Fraktur letters \\frakture Fraktur letters \\frakturE Fraktur letters \\frakturf Fraktur letters \\frakturF Fraktur letters \\frakturg Fraktur letters \\frakturG Fraktur letters \\frakturh Fraktur letters \\frakturH Fraktur letters \\frakturi Fraktur letters \\frakturI Fraktur letters \\frakturk Fraktur letters \\frakturK Fraktur letters \\frakturl Fraktur letters \\frakturL Fraktur letters \\frakturm Fraktur letters \\frakturM Fraktur letters \\frakturn Fraktur letters \\frakturN Fraktur letters \\frakturo Fraktur letters \\frakturO Fraktur letters \\frakturp Fraktur letters \\frakturP Fraktur letters \\frakturq Fraktur letters \\frakturQ Fraktur letters \\frakturr Fraktur letters \\frakturR Fraktur letters \\frakturs Fraktur letters \\frakturS Fraktur letters \\frakturt Fraktur letters \\frakturT Fraktur letters \\frakturu Fraktur letters \\frakturU Fraktur letters \\frakturv Fraktur letters \\frakturV Fraktur letters \\frakturw Fraktur letters \\frakturW Fraktur letters \\frakturx Fraktur letters \\frakturX Fraktur letters \\fraktury Fraktur letters \\frakturY Fraktur letters \\frakturz Fraktur letters \\frakturZ Fraktur letters \\frown Relational operators \\funcapply Binary operators \\G Greek letters \\gamma Greek letters \\Gamma Greek letters \\ge Relational operators \\geq Relational operators \\gets Arrows \\gg Relational operators \\gimel Hebrew letters \\grave Accents \\hairsp Space characters \\hat Accents \\hbar Symbols \\heartsuit Symbols \\hookleftarrow Arrows \\hookrightarrow Arrows \\hphantom Arrows \\hsmash Arrows \\hvec Accents \\identitymatrix Matrices \\ii Double-struck letters \\iiint Integrals \\iint Integrals \\iiiint Integrals \\Im Symbols \\imath Symbols \\in Relational operators \\inc Symbols \\infty Symbols \\int Integrals \\integral Integrals \\iota Greek letters \\Iota Greek letters \\itimes Math operators \\j Symbols \\jj Double-struck letters \\jmath Symbols \\kappa Greek letters \\Kappa Greek letters \\ket Delimiters \\lambda Greek letters \\Lambda Greek letters \\langle Delimiters \\lbbrack Delimiters \\lbrace Delimiters \\lbrack Delimiters \\lceil Delimiters \\ldiv Fraction slashes \\ldivide Fraction slashes \\ldots Dots \\le Relational operators \\left Delimiters \\leftarrow Arrows \\Leftarrow Arrows \\leftharpoondown Arrows \\leftharpoonup Arrows \\leftrightarrow Arrows \\Leftrightarrow Arrows \\leq Relational operators \\lfloor Delimiters \\lhvec Accents \\limit Limits \\ll Relational operators \\lmoust Delimiters \\Longleftarrow Arrows \\Longleftrightarrow Arrows \\Longrightarrow Arrows \\lrhar Arrows \\lvec Accents \\mapsto Arrows \\matrix Matrices \\medsp Space characters \\mid Relational operators \\middle Symbols \\models Relational operators \\mp Binary operators \\mu Greek letters \\Mu Greek letters \\nabla Symbols \\naryand Operators \\nbsp Space characters \\ne Relational operators \\nearrow Arrows \\neq Relational operators \\ni Relational operators \\norm Delimiters \\notcontain Relational operators \\notelement Relational operators \\notin Relational operators \\nu Greek letters \\Nu Greek letters \\nwarrow Arrows \\o Greek letters \\O Greek letters \\odot Binary operators \\of Operators \\oiiint Integrals \\oiint Integrals \\oint Integrals \\omega Greek letters \\Omega Greek letters \\ominus Binary operators \\open Delimiters \\oplus Binary operators \\otimes Binary operators \\over Delimiters \\overbar Accents \\overbrace Accents \\overbracket Accents \\overline Accents \\overparen Accents \\overshell Accents \\parallel Geometry notation \\partial Symbols \\pmatrix Matrices \\perp Geometry notation \\phantom Symbols \\phi Greek letters \\Phi Greek letters \\pi Greek letters \\Pi Greek letters \\pm Binary operators \\pppprime Primes \\ppprime Primes \\pprime Primes \\prec Relational operators \\preceq Relational operators \\prime Primes \\prod Math operators \\propto Relational operators \\psi Greek letters \\Psi Greek letters \\qdrt Square roots and radicals \\quadratic Square roots and radicals \\rangle Delimiters \\Rangle Delimiters \\ratio Relational operators \\rbrace Delimiters \\rbrack Delimiters \\Rbrack Delimiters \\rceil Delimiters \\rddots Dots \\Re Symbols \\rect Symbols \\rfloor Delimiters \\rho Greek letters \\Rho Greek letters \\rhvec Accents \\right Delimiters \\rightarrow Arrows \\Rightarrow Arrows \\rightharpoondown Arrows \\rightharpoonup Arrows \\rmoust Delimiters \\root Symbols \\scripta Scripts \\scriptA Scripts \\scriptb Scripts \\scriptB Scripts \\scriptc Scripts \\scriptC Scripts \\scriptd Scripts \\scriptD Scripts \\scripte Scripts \\scriptE Scripts \\scriptf Scripts \\scriptF Scripts \\scriptg Scripts \\scriptG Scripts \\scripth Scripts \\scriptH Scripts \\scripti Scripts \\scriptI Scripts \\scriptk Scripts \\scriptK Scripts \\scriptl Scripts \\scriptL Scripts \\scriptm Scripts \\scriptM Scripts \\scriptn Scripts \\scriptN Scripts \\scripto Scripts \\scriptO Scripts \\scriptp Scripts \\scriptP Scripts \\scriptq Scripts \\scriptQ Scripts \\scriptr Scripts \\scriptR Scripts \\scripts Scripts \\scriptS Scripts \\scriptt Scripts \\scriptT Scripts \\scriptu Scripts \\scriptU Scripts \\scriptv Scripts \\scriptV Scripts \\scriptw Scripts \\scriptW Scripts \\scriptx Scripts \\scriptX Scripts \\scripty Scripts \\scriptY Scripts \\scriptz Scripts \\scriptZ Scripts \\sdiv Fraction slashes \\sdivide Fraction slashes \\searrow Arrows \\setminus Binary operators \\sigma Greek letters \\Sigma Greek letters \\sim Relational operators \\simeq Relational operators \\smash Arrows \\smile Relational operators \\spadesuit Symbols \\sqcap Binary operators \\sqcup Binary operators \\sqrt Square roots and radicals \\sqsubseteq Set notation \\sqsuperseteq Set notation \\star Binary operators \\subset Set notation \\subseteq Set notation \\succ Relational operators \\succeq Relational operators \\sum Math operators \\superset Set notation \\superseteq Set notation \\swarrow Arrows \\tau Greek letters \\Tau Greek letters \\therefore Relational operators \\theta Greek letters \\Theta Greek letters \\thicksp Space characters \\thinsp Space characters \\tilde Accents \\times Binary operators \\to Arrows \\top Logic notation \\tvec Arrows \\ubar Accents \\Ubar Accents \\underbar Accents \\underbrace Accents \\underbracket Accents \\underline Accents \\underparen Accents \\uparrow Arrows \\Uparrow Arrows \\updownarrow Arrows \\Updownarrow Arrows \\uplus Binary operators \\upsilon Greek letters \\Upsilon Greek letters \\varepsilon Greek letters \\varphi Greek letters \\varpi Greek letters \\varrho Greek letters \\varsigma Greek letters \\vartheta Greek letters \\vbar Delimiters \\vdash Relational operators \\vdots Dots \\vec Accents \\vee Binary operators \\vert Delimiters \\Vert Delimiters \\Vmatrix Matrices \\vphantom Arrows \\vthicksp Space characters \\wedge Binary operators \\wp Symbols \\wr Binary operators \\xi Greek letters \\Xi Greek letters \\zeta Greek letters \\Zeta Greek letters \\zwnj Space characters \\zwsp Space characters ~= Relational operators -+ Binary operators +- Binary operators << Relational operators <= Relational operators -> Arrows >= Relational operators >> Relational operators Recognized Functions In this tab, you will find the list of math expressions that will be recognized by the Equation editor as functions and therefore will not be automatically italicized. For the list of recognized functions go to the File tab -> Advanced Settings -> Proofing -> AutoCorrect Options -> Recognized Functions. To add an entry to the list of recognized functions, enter the function in the blank field and click the Add button. To remove an entry from the list of recognized functions, select the function to be removed and click the Delete button. To restore the previously deleted entries, select the entry to be restored from the list and click the Restore button. Use the Reset to default button to restore default settings. Any function you added will be removed and the removed ones will be restored. AutoFormat As You Type By default, the editor formats the text while you are typing according to the auto-formatting presets: replaces quotation marks, converts hyphens to dashes, converts text recognized as internet or network path into a hyperlink, starts a bullet list or a numbered list when a list is detected. The Add period with double-space option allows to add a period when you double tap the spacebar. Enable or disable it as appropriate. By default, this option is disabled for Linux and Windows, and is enabled for macOS. To enable or disable the auto-formatting presets, go to the File tab -> Advanced Settings -> Proofing -> AutoCorrect Options -> AutoFormat As You Type. Text AutoCorrect You can set the editor to capitalize the first word of each sentence automatically. The option is enabled by default. To disable this option, go to the File tab -> Advanced Settings -> Proofing -> AutoCorrect Options -> Text AutoCorrect and uncheck the Capitalize first letter of sentences option."
    },
   {
        "id": "UsageInstructions/NonprintingCharacters.htm", 
        "title": "Show/hide nonprinting characters", 
        "body": "In the Document Editor, you can enable displaying nonprinting characters that help you edit a document. They indicate the presence of various types of formatting elements, but they cannot be printed with the document even if they are displayed on the screen. To show or hide nonprinting characters, click the Nonprinting characters icon at the Home tab on the top toolbar. Alternatively, you can use the Ctrl+Shift+Num8 key combination. Nonprinting characters include: Spaces Inserted when you press the Spacebar on the keyboard. They create a space between characters. Tabs Inserted when you press the Tab key. They are used to advance the cursor to the next tab stop. Paragraph marks (i.e. hard returns) Inserted when you press the Enter key. They ends a paragraph and adds a bit of space after it. They also contain information about the paragraph formatting. Line breaks (i.e. soft returns) Inserted when you use the Shift+Enter key combination. They break the current line and put the text lines close together. Soft return are primarily used in titles and headings. Nonbreaking spaces Inserted when you use the Ctrl+Shift+Spacebar key combination. They create a space between characters which can't be used to start a new line. Page breaks Inserted when you use the Breaks icon on the Insert or Layout tabs of the top toolbar and then select one of the Insert Page Break submenu options (the section break indicator differs depending on which option is selected: Next Page, Continuous Page, Even Page or Odd Page). Section breaks Inserted when you use the Breaks icon on the Insert or Layout tab of the top toolbar and then select one of the Insert Section Break submenu options (the section break indicator differs depending on which option is selected: Next Page, Continuous Page, Even Page or Odd Page). Column breaks Inserted when you use the Breaks icon on the Insert or Layout tab of the top toolbar and then select the Insert Column Break option. End-of-cell and end-of row markers in tables Contain formatting codes for an individual cell and a row, respectively. Small black square in the margin to the left of a paragraph Indicates that at least one of the paragraph options was applied, e.g. Keep lines together, Page break before. Anchor symbols Indicate the position of floating objects (objects whose wrapping style is different from Inline), e.g. images, autoshapes, charts. You should select an object to make its anchor visible."
    },
   {
        "id": "UsageInstructions/OCR.htm", 
        "title": "Extract text from an image", 
        "body": "With ONLYOFFICE Document Editor you can extract text from an image (.png .jpg) and insert it in your document. Open your document and place the cursor on the spot where you want to insert the text. Switch to the Plugins tab and choose OCR from the menu. Click Load File and select the image. Choose the recognition language from the Choose Language pull-down menu. Click Recognize. Click Insert text. You should check the inserted text for errors and layout."
    },
   {
        "id": "UsageInstructions/OpenCreateNew.htm", 
        "title": "Create a new document or open an existing one", 
        "body": "In the Document Editor, you can open a recently edited document, rename it, create a new one, or return to the list of existing documents . To create a new document In the online editor click the File tab on the top toolbar, select the Create New option. In the desktop editor in the main program window, select the Document menu item from the Create new section on the left sidebar - a new file will open in a new tab, when all the necessary changes are made, click the Save icon in the upper left corner or switch to the File tab and choose the Save as menu item. in the file manager window, select the file location, specify its name, choose the required format for saving (DOCX, DOCXF, OFORM, Document template (DOTX), ODT, OTT, RTF, TXT, PDF or PDFA) and click the Save button. To open an existing document In the desktop editor in the main program window, select the Open local file menu item on the left sidebar, choose the required document from the file manager window and click the Open button. You can also right-click the required document in the file manager window, select the Open with option and choose the necessary application from the menu. If text documents are associated with the application you need, you can also open them by double-clicking the file name in the file explorer window. All the directories that you have navigated through using the desktop editor will be displayed in the Recent folders list so that you can quickly access them afterwards. Click the required folder to select one of the files stored there. To open a recently edited document In the online editor click the File tab on the top toolbar, select the Open Recent option, choose the document you need from the list of recently edited documents. In the desktop editor in the main program window, select the Recent files menu item on the left sidebar, choose the document you need from the list of recently edited documents. To rename an opened document In the online editor click the document name at the top of the page, enter a new document name, press Enter to accept the changes. To open the folder, where the file is stored, in a new browser tab in the online editor in the file explorer window in the desktop editor, click the Open file location icon on the right side of the editor header. Alternatively, you can switch to the File tab on the top toolbar and select the Open file location option."
    },
   {
        "id": "UsageInstructions/PageBreaks.htm", 
        "title": "Insert page breaks", 
        "body": "In the Document Editor, you can add a page break to start a new page, insert a blank page and adjust pagination options. To insert a page break at the current cursor position click the Breaks icon on the Insert or Layout tab of the top toolbar or click the arrow next to this icon and select the Insert Page Break option from the menu. You can also use the Ctrl+Enter key combination. To insert a blank page at the current cursor position click the Blank Page icon on the Insert tab of the top toolbar. This action inserts two page breaks that create a blank page. To insert a page break before the selected paragraph i.e. to start this paragraph at the top of a new page: click the right mouse button and select the Page break before option in the menu, or click the right mouse button, select the Paragraph Advanced Settings option in the menu or use the Show advanced settings link on the right sidebar, and check the Page break before box at the Line &amp; Page Breaks tab of the opened Paragraph - Advanced Settings window. To keep lines together so that only whole paragraphs will be moved to the new page (i.e. there will be no page break between the lines within a single paragraph), click the right mouse button and select the Keep lines together option in the menu, or click the right mouse button, select the Paragraph Advanced Settings option on the menu or use the Show advanced settings link at the right sidebar, and check the Keep lines together box at the Line &amp; Page Breaks in the opened Paragraph - Advanced Settings window. The Line &amp; Page Breaks tab of the Paragraph - Advanced Settings window allows you to set two more pagination options: Keep with next - is used to prevent a page break between the selected paragraph and the next one. Orphan control - is selected by default and used to prevent a single line of the paragraph (the first or last) from appearing at the top or bottom of the page."
    },
   {
        "id": "UsageInstructions/ParagraphIndents.htm", 
        "title": "Change paragraph indents", 
        "body": "In the Document Editor, you can change the first line offset from the left side of the page as well as the paragraph offset from the left and right sides of the page. To do that, set the necessary parameters on the right sidebar Paragraph settings in the Indents section: Left - set the paragraph offset from the left side of the page specifying the necessary numeric value, Right - set the paragraph offset from the right side of the page specifying the necessary numeric value, Special - set an indent for the first line of the paragraph: select the corresponding menu item ((none), First line, Hanging) and change the default numeric value specified for First Line or Hanging, or place the cursor within the required paragraph, or select several paragraphs with the mouse or the whole text by pressing the Ctrl+A key combination, click the right mouse button and select the Paragraph Advanced Settings option from the menu or use the Show advanced settings link on the right sidebar, in the opened Paragraph - Advanced Settings window, switch to the Indents &amp; Spacing tab and set the necessary parameters in the Indents section (the parameters’ description is given above), click the OK button. To quickly change the paragraph offset from the left side of the page, you can also use the corresponding icons on the Home tab of the top toolbar: Decrease indent and Increase indent . You can also use the horizontal ruler to set indents. Select the necessary paragraph(s) and drag the indent markers along the ruler. The First Line Indent marker is used to set an offset from the left side of the page for the first line of the paragraph. The Hanging Indent marker is used to set an offset from the left side of the page for the second line and all the subsequent lines of the paragraph. The Left Indent marker is used to set an offset for the entire paragraph from the left side of the page. The Right Indent marker is used to set a paragraph offset from the right side of the page."
    },
   {
        "id": "UsageInstructions/PhotoEditor.htm", 
        "title": "Edit an image", 
        "body": "ONLYOFFICE Document Editor comes with a very powerful photo editor, that allows you to adjust the image with filters and make all kinds of annotations. Select an image in your document. Switch to the Plugins tab and choose Photo Editor. You are now in the editing environment. Below the image you will find the following checkboxes and slider filters: Grayscale, Sepia, Sepia 2, Blur, Emboss, Invert, Sharpen; Remove White (Threshold, Distance), Gradient transparency, Brightness, Noise, Pixelate, Color Filter; Tint, Multiply, Blend. To the left of the filters you will find buttons for Undo, Redo and Resetting; Delete, Delete all; Crop (Custom, Square, 3:2, 4:3, 5:4, 7:5, 16:9); Flip (Flip X, Flip Y, Reset); Rotate (30 degree, -30 degree,Manual rotation slider); Draw (Free, Straight, Color, Size slider); Shape (Rectangle, Circle, Triangle, Fill, Stroke, Stroke size); Icon (Arrows, Stars, Polygon, Location, Heart, Bubble, Custom icon, Color); Text (Bold, Italic, Underline, Left, Center, Right, Color, Text size); Mask. Feel free to try all of these and remember you can always undo them. When finished, click the OK button. The edited picture is now included in the document."
    },
   {
        "id": "UsageInstructions/SavePrintDownload.htm", 
        "title": "Save, download, print your document", 
        "body": "Saving By default, online Document Editor automatically saves your file each 2 seconds when you work on it to prevent your data loss in case the program closes unexpectedly. If you co-edit the file in the Fast mode, the timer requests for updates 25 times a second and saves the changes if they have been made. When the file is being co-edited in the Strict mode, changes are automatically saved at 10-minute intervals. If necessary, you can easily select the preferred co-editing mode or disable the Autosave feature on the Advanced Settings page. To save your current document manually in the current format and location, press the Save icon in the left part of the editor header, or use the Ctrl+S key combination, or click the File tab of the top toolbar and select the Save option. In the desktop version, to prevent data from loss in case program closes unexpectedly, you can turn on the Autorecover option on the Advanced Settings page. In the desktop version, you can save the document with another name, in a new location or format, click the File tab of the top toolbar, select the Save as option, choose one of the available formats depending on your needs: DOCX, ODT, RTF, TXT, PDF, PDF/A, HTML, FB2, EPUB, DOCXF, OFORM. You can also choose the Document template (DOTX or OTT) option. Downloading In the online version, you can download the resulting document onto your computer hard disk drive, click the File tab of the top toolbar, select the Download as option, choose one of the available formats depending on your needs: DOCX, PDF, ODT, TXT, DOTX, PDF/A, OTT, RTF, HTML, FB2, EPUB, DOCXF, OFORM. Saving a copy In the online version, you can save a copy of the file on your portal, click the File tab of the top toolbar, select the Save Copy as option, choose one of the available formats depending on your needs: DOCX, PDF, ODT, TXT, DOTX, PDF/A, OTT, RTF, HTML, FB2, EPUB, DOCXF, OFORM. select a location of the file on the portal and press Save. Printing To print out the current document, click the Print icon in the left part of the editor header, or use the Ctrl+P key combination, or click the File tab of the top toolbar and select the Print option. The Firefox browser enables printing without downloading the document as a .pdf file first. It's also possible to print a selected text passage using the Print Selection option from the contextual menu both in the Edit and View modes (Right Mouse Button Click and choose option Print selection). In the desktop version, the file will be printed directly. In the online version, a PDF file will be generated on the basis of the document. You can open and print it out, or save onto your computer hard disk drive or removable medium to print it out later. Some browsers (e.g. Chrome and Opera) support direct printing."
    },
   {
        "id": "UsageInstructions/SectionBreaks.htm", 
        "title": "Insert section breaks", 
        "body": "In the Document Editor, you can add section breaks that allow you to apply different layouts or formatting styles to a certain part of your document. For example, you can use individual headers and footers, page numbering, footnotes format, margins, size, orientation, or column number for each separate section. Note: an inserted section break defines formatting of the preceding part of the document. To insert a section break at the current cursor position: click the Breaks icon on the Insert or Layout tab of the top toolbar, select the Insert Section Break submenu select the necessary section break type: Next Page - to start a new section from the next page Continuous Page - to start a new section on the current page Even Page - to start a new section from the next even page Odd Page - to start a new section from the next odd page The added section breaks are indicated in your document with a double dotted line: If you do not see the inserted section breaks, click the icon on the Home tab of the top toolbar to display them. To remove a section break, select it with the mouse and press the Delete key. Since a section break defines formatting of the previous section, when you remove a section break, this section formatting will also be deleted. When you delete a section break, the text before and after the break is combined into one section. The new combined section will use the formatting from the section that followed the section break."
    },
   {
        "id": "UsageInstructions/SetOutlineLevel.htm", 
        "title": "Set up a paragraph outline level", 
        "body": "Set up paragraph outline level An outline level is the paragraph level in the document structure. In the Document Editor, the following levels are available: Basic Text, Level 1 - Level 9. The outline level can be specified in different ways, for example, by using heading styles: once you assign a heading style (Heading 1 - Heading 9) to a paragraph, it acquires the corresponding outline level. If you assign a level to a paragraph using the paragraph advanced settings, the paragraph acquires the structure level only while its style remains unchanged. The outline level can be also changed in the Navigation panel on the left using the contextual menu options. To change a paragraph outline level using the paragraph advanced settings, right-click the text and choose the Paragraph Advanced Settings option from the contextual menu or use the Show advanced settings option on the right sidebar, open the Paragraph - Advanced Settings window, switch to the Indents &amp; Spacing tab, select the necessary outline level from the Outline level list. click the OK button to apply the changes."
    },
   {
        "id": "UsageInstructions/SetPageParameters.htm", 
        "title": "Set page parameters", 
        "body": "To change page layout in the Document Editor, i.e. set page orientation and size, adjust margins and insert columns, use the corresponding icons on the Layout tab of the top toolbar. Note: all these parameters are applied to the entire document. If you need to set different page margins, orientation, size, or column number for the separate parts of your document, please refer to this page. Page Orientation Change the current orientation by type clicking the Orientation icon. The default orientation type is Portrait that can be switched to Album. Page Size Change the default A4 format by clicking the Size icon and selecting the required format from the list. The following preset sizes are available: US Letter (21,59cm x 27,94cm) US Legal (21,59cm x 35,56cm) A4 (21cm x 29,7cm) A5 (14,81cm x 20,99cm) B5 (17,6cm x 25,01cm) Envelope #10 (10,48cm x 24,13cm) Envelope DL (11,01cm x 22,01cm) Tabloid (27,94cm x 43,17cm) AЗ (29,7cm x 42,01cm) Tabloid Oversize (30,48cm x 45,71cm) ROC 16K (19,68cm x 27,3cm) Envelope Choukei 3 (11,99cm x 23,49cm) Super B/A3 (33,02cm x 48,25cm) You can also set a special page size by selecting the Custom Page Size option from the list. The Page Size window will open where you'll be able to select the required Preset (US Letter, US Legal, A4, A5, B5, Envelope #10, Envelope DL, Tabloid, AЗ, Tabloid Oversize, ROC 16K, Envelope Choukei 3, Super B/A3, A0, A1, A2, A6) or set custom Width and Height values. Enter new values into the entry fields or adjust the existing values using the arrow buttons. When you finish, click OK to apply the changes. Page Margins Change the default margins, i.e. the blank space between the left, right, top and bottom page edges and the paragraph text, by clicking the Margins icon and selecting one of the available presets: Normal, US Normal, Narrow, Moderate, Wide. You can also use the Custom Margins option to set your own values in the Margins window. Enter the required Top, Bottom, Left and Right page margin values into the entry fields or adjust the existing values using arrow buttons. Gutter position is used to set up additional space on the left side of the document or at its top. The Gutter option is helpful to make sure that bookbinding does not cover the text. In the Margins enter the required gutter position into the entry fields and choose where it should be placed in. Note: the Gutter position cannot be used when the Mirror margins option is checked. In the Multiple pages drop-down menu, choose the Mirror margins option to set up facing pages for double-sided documents. With this option checked, Left and Right margins turn into Inside and Outside margins respectively. In Orientation drop-down menu choose from Portrait and Landscape options. All applied changes to the document will be displayed in the Preview window. When you finish, click OK. The custom margins will be applied to the current document and the Last Custom option with the specified parameters will appear in the Margins list so that you will be able to apply them to other documents. You can also change the margins manually by dragging the border between the grey and white areas on the rulers (the grey areas of the rulers indicate page margins): Columns Apply a multi-column layout by clicking the Columns icon and selecting the necessary column type from the drop-down list. The following options are available: Two - to add two columns of the same width, Three - to add three columns of the same width, Left - to add two columns: a narrow column on the left and a wide column on the right, Right - to add two columns: a narrow column on the right and a wide column on the left. If you want to adjust column settings, select the Custom Columns option from the list. The Columns window will appear, and you'll be able to set the required Number of columns (you can add up to 12 columns) and Spacing between columns. Enter your new values into the entry fields or adjust the existing values using arrow buttons. Check the Column divider box to add a vertical line between the columns. When you finish, click OK to apply the changes. To exactly specify where a new column should start, place the cursor before the text that you want to move to the new column, click the Breaks icon on the top toolbar and then select the Insert Column Break option. The text will be moved to the next column. The inserted column breaks are indicated in your document with a dotted line: . If you do not see the inserted column breaks, click the icon at the Home tab on the top toolbar to make them visible. To remove a column break select it with the mouse and press the Delete key. To manually change the column width and spacing, you can use the horizontal ruler. To cancel columns and return to a regular single-column layout, click the Columns icon on the top toolbar and select the One option from the list."
    },
   {
        "id": "UsageInstructions/SetTabStops.htm", 
        "title": "Set tab stops", 
        "body": "In the Document Editor, you can change tab stops. A tab stop is a term used to describe the location where the cursor stops after the Tab key is pressed. To set tab stops you can use the horizontal ruler: Select the necessary tab stop type by clicking the button in the upper left corner of the working area. The following three tab types are available: Left Tab Stop lines up the text to the left side at the tab stop position; the text moves to the right from the tab stop while you type. Such a tab stop will be indicated on the horizontal ruler with the Left Tab Stop marker. Center Tab Stop centers the text at the tab stop position. Such a tab stop will be indicated on the horizontal ruler with the Center Tab Stop marker. Right Tab Stop lines up the text to the right side at the tab stop position; the text moves to the left from the tab stop while you type. Such a tab stop will be indicated on the horizontal ruler with the Right Tab Stop marker. Click on the bottom edge of the ruler where you want to place the tab stop. Drag it along the ruler to change its position. To remove the added tab stop drag it out of the ruler. You can also use the paragraph properties window to adjust tab stops. Click the right mouse button, select the Paragraph Advanced Settings option in the menu or use the Show advanced settings link on the right sidebar, and switch to the Tabs tab in the opened Paragraph - Advanced Settings window. You can set the following parameters: Default Tab is set at 1.25 cm. You can decrease or increase this value by using the arrow buttons or entering the required value in the box. Tab Position is used to set custom tab stops. Enter the required value in this box, adjust it more precisely by using the arrow buttons and press the Specify button. Your custom tab position will be added to the list in the field below. If you've previously added some tab stops using the ruler, all these tab positions will also be displayed in the list. Alignment - is used to set the necessary alignment type for each of the tab positions in the list above. Select the necessary tab position in the list, choose the Left, Center or Right option from the drop-down list and press the Specify button. Leader - allows choosing a character to create a leader for each tab positions. A leader is a line of characters (dots or hyphens) that fills the space between tabs. Select the necessary tab position in the list, choose the leader type from the drop-down list and press the Specify button. To delete tab stops from the list, select a tab stop and press the Remove or Remove All button."
    },
   {
        "id": "UsageInstructions/Speech.htm", 
        "title": "Read the text out loud", 
        "body": "ONLYOFFICE Document Editor has a plugin that can read out the text for you. Select the text to be read out. Switch to the Plugins tab and choose Speech. The text will now be read out."
    },
   {
        "id": "UsageInstructions/SupportSmartArt.htm", 
        "title": "Support of SmartArt in ONLYOFFICE Document Editor", 
        "body": "SmartArt graphics are used to create a visual representation of a hierarchical structure by choosing a layout that fits best. ONLYOFFICE Document Editor supports SmartArt graphics that were inserted using third-party editors. You can open a file containing SmartArt and edit it as a graphic object using the available editing tools. Once you click a SmartArt graphic or its element, the following tabs become active on the right sidebar to customize a layout: Paragraph settings to configure indents and spacing, line and page breaks, borders and fill, fonts, tabs and paddings. See Paragraph Formatting section for a detailed description of every option. This tab becomes active for SmartArt elements only Shape settings to configure the shapes used on a layout. You can change shapes, edit the fill, the lines, the weights and arrows, the text box and the alternative text. Text Art settings to configure the Text Art style that is used in a SmartArt graphic to highlight the text. You can change the Text Art template, the fill type, color and opacity, the line size, color and type. This tab becomes active for SmartArt elements only. Right-click the SmartArt graphic or its element border to access the following formatting options: Wrapping style to define the way the object is positioned relative to the text. The Wrapping Style option is available only when you click the SmartArt graphic border. Rotate to choose the rotation direction for the selected element on a SmartArt graphic: Rotate 90° Clockwise, Rotate 90° Counterclockwise.The Rotate option becomes active for SmartArt elements only Insert Caption to label a SmartArt graphic element for reference. Shape Advanced Settings to access additional shape formatting options. Right-click the SmartArt graphic element to access the following text formatting options: Vertical Alignment to choose the text alignment inside the selected SmartArt element: Align Top, Align Middle, Align Bottom. Text Direction to choose the text direction inside the selected SmartArt element: Horizontal, Rotate Text Down, Rotate Text Up. Paragraph Advanced Settings to access additional paragraph formatting options."
    },
   {
        "id": "UsageInstructions/Thesaurus.htm", 
        "title": "Replace a word by a synonym", 
        "body": "If you are using the same word multiple times, or a word is just not quite the word you are looking for, ONLYOFFICE Document Editor lets you look up synonyms. It will show you the antonyms too. Select the word in your document. Switch to the Plugins tab and choose Thesaurus. The synonyms and antonyms will show up in the left sidebar. Click a word to replace the word in your document."
    },
   {
        "id": "UsageInstructions/Translator.htm", 
        "title": "Translate text", 
        "body": "In the Document Editor, you can translate your document from and to numerous languages. Select the text that you want to translate. Switch to the Plugins tab and choose Translator, the Translator appears in a sidebar on the left. Click the drop-down box and choose the preferred language. The text will be translated to the required language. Changing the language of your result: Click the drop-down box and choose the preferred language. The translation will change immediately."
    },
   {
        "id": "UsageInstructions/Typograf.htm", 
        "title": "Correct typography", 
        "body": "If you need to correct typography in your text, use the Typograf plugin that will automatically place non-breaking spaces and remove extra ones, as well as correct minor typos, insert correct quotes, replace hyphens with dashes, etc. Open the Plugins tab and click Typograf. Click the Show advanced settings button. Choose the locale and the rules you want to apply to your text. Select the text you want to correct. Click the Correct text button. For more information on the Typograf plugin and its installation, please see the plugin’s page on the AppDirectory."
    },
   {
        "id": "UsageInstructions/UseMailMerge.htm", 
        "title": "Use Mail Merge", 
        "body": "Note: this option is available in the online version only. The Mail Merge feature is used to create a set of documents combining a common content which is taken from a text document and some individual components (variables, such as names, greetings etc.) taken from a spreadsheet (for example, a customer list). It can be useful if you need to create a lot of personalized letters and send them to recipients. Prepare a data source and load it to the main document A data source used for the mail merge must be an .xlsx spreadsheet stored on your portal. Open an existing spreadsheet or create a new one and make sure that it meets the following requirements. The spreadsheet should have a header row with the column titles, as values in the first cell of each column will designate merge fields (i.e. variables that you can insert into the text). Each column should contain a set of actual values for a variable. Each row in the spreadsheet should correspond to a separate record (i.e. a set of values that belongs to a certain recipient). During the merge process, a copy of the main document will be created for each record and each merge field inserted into the main text will be replaced with an actual value from the corresponding column. If you are goung to send results by email, the spreadsheet must also include a column with the recipients' email addresses. Open an existing text document or create a new one. It must contain the main text which will be the same for each version of the merged document. Click the Mail Merge icon on the Home tab of the top toolbar and select the data source location: From File, From URL or From Storage. Select the necesary file or paste a URL and click OK. Once the data source is loaded, the Mail Merge setting tab will be available on the right sidebar. Verify or change the recipients list Click the Edit recipients list button on the top of the right sidebar to open the Mail Merge Recipients window, where the content of the selected data source is displayed. In the opened window, you can add new information, edit or delete the existing data if necessary. To simplify working with data, you can use the icons at the top of the window: and - to copy and paste the copied data and - to undo and redo undone actions and - to sort your data within a selected range of cells in ascending or descending order - to enable the filter for the previously selected range of cells or to remove the applied filter - to clear all the applied filter parameters Note: to learn more on how to use the filter, please refer to the Sort and filter data section of the Spreadsheet Editor help. - to search for a certain value and replace it with another one, if necessary Note: to learn more on how to use the Find and Replace tool, please refer to the Search and Replace Functions section of the Spreadsheet Editor help. After all the necessary changes are made, click the Save & Exit button. To discard the changes, click the Close button. Insert merge fields and check the results Place the mouse cursor where the merge field should be inserted, click the Insert Merge Field button on the right sidebar and select the necessary field from the list. The available fields correspond to the data in the first cell of each column of the selected data source. All the required fields can be added anywhere. Turn on the Highlight merge fields switcher on the right sidebar to make the inserted fields more noticeable in the text. Turn on the Preview results switcher on the right sidebar to view the text with the merge fields replaced with actual values from the data source. Use the arrow buttons to preview the versions of the merged document for each record. To delete an inserted field, disable the Preview results mode, select the field with the mouse and press the Delete key on the keyboard. To replace an inserted field, disable the Preview results mode, select the field with the mouse, click the Insert Merge Field button on the right sidebar and choose a new field from the list. Specify the merge parameters Select the merge type. You can start mass mailing or save the result as a PDF or Docx file to print or edit it later. Select the necessary option from the Merge to list: PDF - to create a single PDF document that includes all the merged copies that can be printed later Docx - to create a single Docx document that includes all the merged copies that can be edited individually later Email - to send the results to recipients by email Note: the recipients' email addresses must be specified in the loaded data source and you need to have at least one email account connected in the Mail module on your portal. Choose all the required records to be applied: All records (this option is selected by default) - to create merged documents for all records from the loaded data source Current record - to create a merged document for the record that is currently displayed From ... To - to create merged documents for a range of records (in this case you need to specify two values: the number of the first record and the last record in the desired range) Note: the maximum allowed quantity of recipients is 100. If you have more than 100 recipients in your data source, please, perform the mail merge by stages: specify the values from 1 to 100, wait until the mail merge process is over, then repeat the operation specifying the values from 101 to N etc. Complete the merge If you've decided to save the merge results as a file, click the Download button to save the file on your PC. You'll find the downloaded file in your default Downloads folder. click the Save button to save the file on your portal. In the opened Folder for save window, you can change the file name and specify the folder where you want to save the file. You can also check the Open merged document in new tab box to check the result when the merge process is finished. Finally, click Save in the Folder for save window. If you've selected the Email option, the Merge button will be available on the right sidebar. After you click it, the Send to Email window will open: In the From list, select the required mail account if you have several accounts connected to the Mail module. In the To list, select the merge field corresponding to the email addresses of the recipients if this option was not selected automatically. Enter your message subject in the Subject Line field. Select the mail format from the list: HTML, Attach as DOCX or Attach as PDF. When one of the two latter options is selected, you also need to specify the File name for attachments and enter the Message (the text of your letter that will be sent to recipients). Click the Send button. Once the mailing is over, you'll receive a notification to your email specified in the From field."
    },
   {
        "id": "UsageInstructions/ViewDocInfo.htm", 
        "title": "View document information", 
        "body": "To access the detailed information about the currently edited document in the Document Editor, click the File tab of the top toolbar and select the Document Info option. General Information The document information includes a number of the file properties which describe the document. Some of these properties are updated automatically, and some of them can be edited. Location - the folder in the Documents module where the file is stored. Owner - the name of the user who has created the file. Uploaded - the date and time when the file has been created. These properties are available in the online version only. Statistics - the number of pages, paragraphs, words, symbols, symbols with spaces. Title, Subject, Comment - these properties allow yoy to simplify your documents classification. You can specify the necessary text in the properties fields. Last Modified - the date and time when the file was last modified. Last Modified By - the name of the user who has made the latest change to the document. This option is available if the document has been shared and can be edited by several users. Application - the application the document has been created with. Author - the person who has created the file. You can enter the necessary name in this field. Press Enter to add a new field that allows you to specify one more author. If you changed the file properties, click the Apply button to apply the changes. Note: The online Editors allow you to change the name of the document directly in the editor interface. To do that, click the File tab of the top toolbar and select the Rename option, then enter the necessary File name in a new window that will appear and click OK. Permission Information In the online version, you can view the information about permissions to the files stored in the cloud. Note: this option is not available for users with the Read Only permissions. To find out who have rights to view or edit the document, select the Access Rights... option on the left sidebar. You can also change currently selected access rights by pressing the Change access rights button in the Persons who have rights section. Version History In the online version, you can view the version history for the files stored in the cloud. Note: this option is not available for users with the Read Only permissions. To view all the changes made to this document, select the Version History option at the left sidebar. It's also possible to open the history of versions using the Version History icon on the Collaboration tab of the top toolbar. You'll see the list of this document versions (major changes) and revisions (minor changes) with the indication of each version/revision author and creation date and time. For document versions, the version number is also specified (e.g. ver. 2). To know exactly which changes have been made in each separate version/revision, you can view the one you need by clicking it on the left sidebar. The changes made by the version/revision author are marked with the color which is displayed next to the author's name on the left sidebar. You can use the Restore link below the selected version/revision to restore it. To return to the current version of the document, use the Close History option on the top of the version list. To close the File panel and return to document editing, select the Close Menu option."
    },
   {
        "id": "UsageInstructions/WordCounter.htm", 
        "title": "Count words", 
        "body": "To know the exact number of words and symbols both with and without spaces in your document, as well as the number of paragraphs altogether, use the Word counter plugin. Open the Plugins tab and click Count words and characters. Select the text. Please note that the following elements are not included in the word count: footnote/endnote symbols, numbers from numbered lists, page numbers. For more information on the Word counter plugin and its installation, please see the plugin’s page on the AppDirectory."
    },
   {
        "id": "UsageInstructions/Wordpress.htm", 
        "title": "Upload a document to WordPress", 
        "body": "You can write your articles in your ONLYOFFICE Document Editor environment and upload them as a WordPress-article. Connect to WordPress Open your document. Switch to the Plugins tab and choose WordPress. Log in into your WordPress account and choose the website page you want to post your document on. Enter a title for your article. Click Publish to publish immediately or Save as draft to publish later from your WordPress site or app."
    },
   {
        "id": "UsageInstructions/YouTube.htm", 
        "title": "Include a video", 
        "body": "In the Document Editor, you can include a video in your document. It will be shown as an image. By double-clicking the image the video dialog opens. Here you can start the video. Copy the URL of the video you want to include. (the complete address shown in the address line of your browser) Go to your document and place the cursor at the location where you want to include the video. Switch to the Plugins tab and choose YouTube. Paste the URL and click OK. Check if it is the correct video and click the OK button below the video. The video is now included in your document."
    },
   {
        "id": "UsageInstructions/insertdropcap.htm", 
        "title": "Insert a drop cap", 
        "body": "A drop cap is a large capital letter used at the beginning of a paragraph or section. The size of a drop cap is usually several lines. To add a drop cap in the Document Editor, place the cursor within the required paragraph, switch to the Insert tab of the top toolbar, click the Drop Cap icon on the top toolbar, in the opened drop-down list select the option you need: In Text - to place the drop cap within the paragraph. In Margin - to place the drop cap in the left margin. The first character of the selected paragraph will be transformed into a drop cap. If you need the drop cap to include some more characters, add them manually: select the drop cap and type in other letters you need. To adjust the drop cap appearance (i.e. font size, type, decoration style or color), select the letter and use the corresponding icons on the Home tab of the top toolbar. When the drop cap is selected, it's surrounded by a frame (a container used to position the drop cap on the page). You can quickly change the frame size dragging its borders or change its position using the icon that appears after hovering your mouse cursor over the frame. To delete the added drop cap, select it, click the Drop Cap icon on the Insert tab of the top toolbar and choose the None option from the drop-down list. To adjust the added drop cap parameters, select it, click the Drop Cap icon at the Insert tab of the top toolbar and choose the Drop Cap Settings option from the drop-down list. The Drop Cap - Advanced Settings window will appear: The Drop Cap tab allows adjusting the following parameters: Position is used to change the placement of a drop cap. Select the In Text or In Margin option, or click None to delete the drop cap. Font is used to select a font from the list of the available fonts. Height in rows is used to define how many lines a drop cap should span. It's possible to select a value from 1 to 10. Distance from text is used to specify the amount of spacing between the text of the paragraph and the right border of the drop cap frame. The Borders &amp; Fill tab allows adding a border around a drop cap and adjusting its parameters. They are the following: Border parameters (size, color and presence or absence) - set the border size, select its color and choose the borders (top, bottom, left, right or their combination) you want to apply these settings to. Background color - choose the color for the drop cap background. The Margins tab allows setting the distance between the drop cap and the Top, Bottom, Left and Right borders around it (if the borders have previously been added). Once the drop cap is added you can also change the Frame parameters. To access them, right click within the frame and select the Frame Advanced Settings from the menu. The Frame - Advanced Settings window will open: The Frame tab allows adjusting the following parameters: Position is used to select the Inline or Flow wrapping style. You can also click None to delete the frame. Width and Height are used to change the frame dimensions. The Auto option allows automatically adjusting the frame size to fit the drop cap. The Exactly option allows specifying fixed values. The At least option is used to set the minimum height value (if you change the drop cap size, the frame height changes accordingly, but it cannot be less than the specified value). Horizontal parameters are used either to set the exact position of the frame in the selected units of measurement with respect to a margin, page or column, or to align the frame (left, center or right) with respect to one of these reference points. You can also set the horizontal Distance from text i.e. the amount of space between the vertical frame borders and the text of the paragraph. Vertical parameters are used either to set the exact position of the frame is the selected units of measurement with respect to a margin, page or paragraph, or to align the frame (top, center or bottom) with respect to one of these reference points. You can also set the vertical Distance from text i.e. the amount of space between the horizontal frame borders and the text of the paragraph. Move with text is used to make sure that the frame moves as the paragraph to which it is anchored. The Borders &amp; Fill and Margins allow adjusting the same parameters as the corresponding tabs in the Drop Cap - Advanced Settings window."
    }
]