﻿<!DOCTYPE html>
<html>
<head>
    <title>Upload a document to WordPress</title>
    <meta charset="utf-8" />
    <meta name="description" content="The description of WordPress plugin for ONLYOFFICE editors, which allows to upload documents as WordPress articles" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Upload a document to WordPress</h1>
        <p>You can write your articles in your ONLYOFFICE <a href="https://www.onlyoffice.com/en/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Document Editor</b></a> environment and upload them as a WordPress-article.</p>
        <h2>Connect to WordPress</h2>
        <ol>
            <li>Open your document.</li>
            <li>Switch to the <b>Plugins</b> tab and choose <div class = "icon icon-wordpress"></div> <b>WordPress</b>.</li>
            <li>Log in into your WordPress account and choose the website page you want to post your document on.</li>
            <li>Enter a title for your article.</li>
            <li>Click <b>Publish</b> to publish immediately or <b>Save as draft</b> to publish later from your WordPress site or app.</li>
        </ol>
        <img class="gif" alt="WordPress plugin gif" src="../../images/wordpress_plugin.gif" width="600" />
    </div>
</body>
</html>