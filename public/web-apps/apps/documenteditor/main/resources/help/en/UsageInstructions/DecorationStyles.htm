﻿<!DOCTYPE html>
<html>
	<head>
		<title>Apply font decoration styles</title>
		<meta charset="utf-8" />
		<meta name="description" content="Apply font decoration styles: increment/decrement values, bold, italic, underline, strikeout, superscript/subscript" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Apply font decoration styles</h1>
			<p>In the <a href="https://www.onlyoffice.com/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Document Editor</b></a>, you can apply various font decoration styles using the corresponding icons on the <b>Home</b> tab of the top toolbar.</p>
			<p class="note"><b>Note</b>: in case you want to apply the formatting to the already existing text in the document, select it with the mouse or <a href="../HelpfulHints/KeyboardShortcuts.htm#textselection" onclick="onhyperlinkclick(this)">use the keyboard</a> and apply the formatting.</p>
			<table>
				<tr>
					<td>Bold</td>
					<td><div class = "icon icon-bold"></div></td>
					<td>Used to make the font bold giving it a heavier appearance.</td>
				</tr>
				<tr>
					<td>Italic</td>
					<td><div class = "icon icon-italic"></div></td>
					<td>Used to make the font slightly slanted to the right.</td>
				</tr>
				<tr>
					<td>Underline</td>
					<td><div class = "icon icon-underline"></div></td>
					<td>Used to make the text underlined with a line going under the letters.</td>
				</tr>
				<tr>
					<td>Strikeout</td>
					<td><div class = "icon icon-strike"></div></td>
					<td>Used to make the text struck out with a line going through the letters.</td>
				</tr>
				<tr>
					<td>Superscript</td>
					<td><div class = "icon icon-sup"></div></td>
					<td>Used to make the text smaller placing it in the upper part of the text line, e.g. as in fractions.</td>
				</tr>
				<tr>
					<td>Subscript</td>
					<td><div class = "icon icon-sub"></div></td>
					<td>Used to make the text smaller placing it in the lower part of the text line, e.g. as in chemical formulas.</td>
				</tr>
			</table>
			<p>To access the advanced font settings, click the right mouse button and select the <b>Paragraph Advanced Settings</b> option from the menu or use the <b>Show advanced settings</b> link on the right sidebar. Then the <b>Paragraph - Advanced Settings</b> window will appear, and you will need to switch to the <b>Font</b> tab.</p>
			<p>Here you can use the following font decoration styles and settings:</p>
			<ul>
				<li><b>Strikethrough</b> is used to make the text struck out with a line going through the letters.</li>
				<li><b>Double strikethrough</b> is used to make the text struck out with a double line going through the letters.</li>
				<li><b>Superscript</b> is used to make the text smaller placing it in the upper part of the text line, e.g. as in fractions.</li>
				<li><b>Subscript</b> is used to make the text smaller placing it in the lower part of the text line, e.g. as in chemical formulas.</li>
				<li><b>Small caps</b> is used to make all letters lower case.</li>
				<li><b>All caps</b> is used to make all letters upper case.</li>
				<li><b>Spacing</b> is used to set the space between the characters. Increase the default value to apply the <b>Expanded</b> spacing, or decrease the default value to apply the <b>Condensed</b> spacing. Use the arrow buttons or enter the necessary value in the box.</li>
				<li><b>Position</b> is used to set the characters position (vertical offset) in the line. Increase the default value to move characters upwards, or decrease the default value to move characters downwards. Use the arrow buttons or enter the necessary value in the box.</li>
				<li><b>Ligatures</b> are joined letters of a word typed in either of the OpenType fonts. Please note that using ligatures can disrupt character spacing. The available ligature options are as follows:
				<ul>
					<li><em>None</em></li>
					<li><em>Standard only</em> (includes “fi”, “fl”, “ff”; enhances readability)</li>
					<li><em>Contextual</em> (ligatures are applied based on the surrounding letters; enhances readability)</li>
					<li><em>Historical</em> (ligatures have more swoops and curved lines; lowers readability)</li>
					<li><em>Discretionary</em> (ornamental ligatures; lowers readability)</li>
					<li><em>Standard and Contextual</em></li>
					<li><em>Standard and Historical</em></li>
					<li><em>Contextual and Historical</em></li>
					<li><em>Standard and Discretionary</em></li>
					<li><em>Contextual and Discretionary</em></li>
					<li><em>Historical and Discretionary</em></li>
					<li><em>Standard, Contextual and Historical</em></li>
					<li><em>Standard, Contextual and Discretionary</em></li>
					<li><em>Standard, Historical and Discretionary</em></li>
					<li><em>Contextual, Historical and Discretionary</em></li>
					<li><em>All</em></li>
				</ul>
                <p>All the changes will be displayed in the preview field below.</p>
				</li>
			</ul>
			<p><img alt="Paragraph Advanced Settings - Font" src="../images/paradvsettings_font.png" /></p>
		</div>
	</body>
</html>