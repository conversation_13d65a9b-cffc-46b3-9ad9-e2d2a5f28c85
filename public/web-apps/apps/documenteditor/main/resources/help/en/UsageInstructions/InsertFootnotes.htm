﻿<!DOCTYPE html>
<html>
<head>
    <title>Insert footnotes</title>
    <meta charset="utf-8" />
    <meta name="description" content="Insert footnotes to provide explanations for some terms or make references to the sources" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Insert footnotes</h1>
        <p>In the <a href="https://www.onlyoffice.com/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Document Editor</b></a>, you can insert footnotes to add explanations or comments for certain sentences or terms used in your text, make references to the sources, etc.</p>
        <h2>Inserting footnotes</h2>
        <p>To insert a footnote into your document,</p>
        <ol>
            <li>position the insertion point at the end of the text passage that you want to add the footnote to,</li>
            <li>switch to the <b>References</b> tab located at the top toolbar,</li>
            <li>
                click the <div class = "icon icon-addfootnote"></div> <b>Footnote</b> icon on the top toolbar, or<br />
                click the arrow next to the <div class = "icon icon-addfootnote"></div> <b>Footnote</b> icon and select the <b>Insert Footnote</b> option from the menu,
                <p>The footnote mark (i.e. the superscript character that indicates a footnote) appears in the text of the document, and the insertion point moves to the bottom of the current page.</p>
            </li>
            <li>type in the footnote text.</li>
        </ol>
        <p>Repeat the above mentioned operations to add subsequent footnotes for other text passages in the document. The footnotes are numbered automatically.</p>
        <p><img alt="Footnotes" src="../images/footnotesadded.png" /></p>
        <h2>Display of footnotes in the document</h2>
        <p>If you hover the mouse pointer over the footnote mark in the document text, a small pop-up window with the footnote text appears.</p>
        <p><img alt="Footnote text" src="../images/footnotetext.png" /></p>
        <h2>Navigating through footnotes</h2>
        <p>To easily navigate through the added footnotes in the text of the document,</p>
        <ol>
            <li>click the arrow next to the <div class = "icon icon-addfootnote"></div> <b>Footnote</b> icon on the <b>References</b> tab located at the top toolbar,</li>
            <li>in the <b>Go to Footnotes</b> section, use the <div class = "icon icon-previousfootnote"></div> arrow to go to the previous footnote or the <div class = "icon icon-nextfootnote"></div> arrow to go to the next footnote.</li>
        </ol>
        <h2>Editing footnotes</h2>
        <p>To edit the footnotes settings,</p>
        <ol>
            <li>click the arrow next to the <div class = "icon icon-addfootnote"></div> <b>Footnote</b> icon on the <b>References</b> tab located at the top toolbar,</li>
            <li>select the <b>Notes Settings</b> option from the menu,</li>
            <li>
                change the current parameters in the <b>Notes Settings</b> window that will appear:
                <p><img alt="Footnotes Settings window" src="../images/footnotes_settings.png" /></p>
                <ul>
                    <li>Activate the <b>Footnote</b> box to edit the footnotes only.</li>
                    <li>
                        Set the <b>Location</b> of footnotes on the page selecting one of the available options from the drop-down menu to the right:
                        <ul>
                            <li><b>Bottom of page</b> - to position footnotes at the bottom of the page (this option is selected by default).</li>
                            <li><b>Below text</b> - to position footnotes closer to the text. This option can be useful in cases when the page contains a short text.</li>
                        </ul>
                    </li>
                    <li>
                        Adjust the footnotes <b>Format</b>:
                        <ul>
                            <li><b>Number Format</b> - select the necessary number format from the available ones: <em>1, 2, 3,...</em>, <em>a, b, c,...</em>, <em>A, B, C,...</em>, <em>i, ii, iii,...</em>, <em>I, II, III,...</em>.</li>
                            <li><b>Start at</b> - use the arrows to set the number or letter you want to start numbering with.</li>
                            <li>
                                <b>Numbering</b> - select a way to number your footnotes:
                                <ul>
                                    <li><b>Continuous</b> - to number footnotes sequentially throughout the document,</li>
                                    <li><b>Restart each section</b> - to start footnote numbering with 1 (or another specified character) at the beginning of each section,</li>
                                    <li><b>Restart each page</b> - to start footnote numbering with 1 (or another specified character) at the beginning of each page.</li>
                                </ul>
                            </li>
                            <li><b>Custom Mark</b> - set a special character or a word you want to use as the footnote mark (e.g. * or Note1). Enter the necessary character/word into the text entry field and click the <b>Insert</b> button at the bottom of the <b>Notes Settings</b> window.</li>
                        </ul>
                    </li>
                    <li>
                        Use the <b>Apply changes to</b> drop-down list if you want to apply the specified notes settings to the <b>Whole document</b> or the <b>Current section</b> only.
                        <p class="note"><b>Note</b>: to use different footnotes formatting in separate parts of the document, you need to add <a href="../UsageInstructions/SectionBreaks.htm" onclick="onhyperlinkclick(this)">section breaks</a> first.</p>
                    </li>
                </ul>
            </li>
            <li>When you finish, click the <b>Apply</b> button.</li>
        </ol>

        <h2>Removing footnotes</h2>
        <p>To remove a single footnote, position the insertion point directly before the footnote mark in the text and press <b>Delete</b>. Other footnotes will be renumbered automatically.</p>
        <p>To delete all the footnotes in the document,</p>
        <ol>
            <li>click the arrow next to the <div class = "icon icon-addfootnote"></div> <b>Footnote</b> icon on the <b>References</b> tab located at the top toolbar,</li>
            <li>select the <b>Delete All Notes</b> option from the menu.</li>
            <li>choose the <b>Delete All Footnotes</b> option in the appeared window and click <b>OK</b>.</li>
        </ol>
    </div>
</body>
</html>