﻿<!DOCTYPE html>
<html>
	<head>
		<title>Insert section breaks</title>
		<meta charset="utf-8" />
		<meta name="description" content="Insert section breaks to use different formatting for each section of the document" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Insert section breaks</h1>
			<p>In the <a href="https://www.onlyoffice.com/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Document Editor</b></a>, you can add section breaks that allow you to apply different layouts or formatting styles to a certain part of your document. For example, you can use individual <a href="../UsageInstructions/InsertHeadersFooters.htm" onclick="onhyperlinkclick(this)">headers and footers</a>, <a href="../UsageInstructions/InsertPageNumbers.htm" onclick="onhyperlinkclick(this)">page numbering</a>, <a href="../UsageInstructions/InsertFootnotes.htm" onclick="onhyperlinkclick(this)">footnotes format</a>, <a href="../UsageInstructions/SetPageParameters.htm" onclick="onhyperlinkclick(this)">margins, size, orientation, or column number</a> for each separate section.</p>
			<p class="note"><b>Note</b>: an inserted section break defines formatting of the preceding part of the document.</p>
			<p>To insert a section break at the current cursor position:</p>
			<ol>
				<li>click the <div class = "icon icon-pagebreak1"></div> <b>Breaks</b> icon on the <b>Insert</b> or <b>Layout</b> tab of the top toolbar,</li>
				<li>select the <b>Insert Section Break</b> submenu</li>
				<li>select the necessary section break type:
				<ul>
				<li><b>Next Page</b> - to start a new section from the next page</li>
				<li><b>Continuous Page</b> - to start a new section on the current page</li>
				<li><b>Even Page</b> - to start a new section from the next even page</li>
				<li><b>Odd Page</b> - to start a new section from the next odd page</li>
				</ul>
				</li>
			</ol>
			<p>The added section breaks are indicated in your document with a double dotted line: <span class = "big big-sectionbreak"></span></p>
			<p>If you do not see the inserted section breaks, click the <span class = "icon icon-nonprintingcharacters"></span> icon on the <b>Home</b> tab of the top toolbar to display them.</p>
			<p>To remove a section break, select it with the mouse and press the <b>Delete</b> key. Since a section break defines formatting of the previous section, when you remove a section break, this section formatting will also be deleted. When you delete a section break, the text before and after the break is combined into one section. The new combined section will use the formatting from the section that followed the section break.</p>
		</div>
	</body>
</html>