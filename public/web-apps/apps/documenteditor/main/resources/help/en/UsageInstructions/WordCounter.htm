﻿<!DOCTYPE html>
<html>
<head>
    <title>Count words</title>
    <meta charset="utf-8" />
    <meta name="description" content="The description of Word counter plugin for ONLYOFFICE editors, which allows to count words, symbols, and paragraphs of the text" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Count words</h1>
        <p>To know the exact number of words and symbols both with and without spaces  in your document, as well as the number of paragraphs altogether, use the Word counter plugin.</p>
        <ol>            
            <li>Open the <b>Plugins</b> tab and click <b>Count words and characters</b>.</li>
            <li>Select the text.</li>
        </ol>
        <div class="note">Please note that the following elements are not included in the word count:
        <ul>
            <li>footnote/endnote symbols,</li>
            <li>numbers from numbered lists,</li>
            <li>page numbers.</li>
        </ul>
        </div>
        <img class="gif" alt="Word counter plugin gif" src="../../images/wordcounter_plugin.gif" width="600" />
        <p>For more information on the Word counter plugin and its installation, please see the <a href="https://www.onlyoffice.com/en/app-directory/word-counter">plugin’s page</a> on the AppDirectory.</p>
    </div>
</body>
</html>