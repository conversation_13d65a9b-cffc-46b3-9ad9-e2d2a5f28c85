﻿<!DOCTYPE html>
<html>
	<head>
		<title>Create a new document or open an existing one</title>
		<meta charset="utf-8" />
		<meta name="description" content="Open a recently edited document, create a new one, or return to the list of existing documents" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Create a new document or open an existing one</h1>
            <p>In the <a href="https://www.onlyoffice.com/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Document Editor</b></a>, you can open a recently edited document, rename it, create a new one, or return to the list of existing documents .</p>
            <h3>To create a new document</h3>
            <div class="onlineDocumentFeatures">
                <p>In the <em>online editor</em></p>
                <ol>
                    <li>click the <b>File</b> tab on the top toolbar,</li>
                    <li>select the <b>Create New</b> option. <!--Here you can choose whether to create a <b>blank</b> text document or use one of the available document <b>templates</b>: contract, letter, list, or plan.--></li>
                </ol>
            </div>
            <div class="desktopDocumentFeatures">
                <p>In the <em>desktop editor</em></p>
                <ol>
                    <li>in the main program window, select the <b>Document</b> menu item from the <b>Create new</b> section on the left sidebar - a new file will open in a new tab,</li>
                    <li>when all the necessary changes are made, click the <b>Save</b> <div class="icon icon-save"></div> icon in the upper left corner or switch to the <b>File</b> tab and choose the <b>Save as</b> menu item. </li>
                    <li>in the file manager window, select the file location, specify its name, choose the required format for saving (DOCX, DOCXF, OFORM, Document template (DOTX), ODT, OTT, RTF, TXT, PDF or PDFA) and click the <b>Save</b> button.</li>
                </ol>
            </div>

            <div class="desktopDocumentFeatures">
                <h3>To open an existing document</h3>
                <p>In the <em>desktop editor</em></p>
                <ol>
                    <li>in the main program window, select the <b>Open local file</b> menu item on the left sidebar,</li>
                    <li>choose the required document from the file manager window and click the <b>Open</b> button.</li>
                </ol>
                <p>You can also right-click the required document in the file manager window, select the <b>Open with</b> option and choose the necessary application from the menu. If text documents are associated with the application you need, you can also open them by double-clicking the file name in the file explorer window.</p>
                <p>All the directories that you have navigated through using the desktop editor will be displayed in the <b>Recent folders</b> list so that you can quickly access them afterwards. Click the required folder to select one of the files stored there.</p>
            </div>

            <h3>To open a recently edited document  </h3>
            <div class="onlineDocumentFeatures">
                <p>In the <em>online editor</em></p>
                <ol>
                    <li>click the <b>File</b> tab on the top toolbar,</li>
                    <li>select the <b>Open Recent</b> option,</li>
                    <li>choose the document you need from the list of recently edited documents.</li>
                </ol>
            </div>
            <div class="desktopDocumentFeatures">
                <p>In the <em>desktop editor</em></p>
                <ol>
                    <li>in the main program window, select the <b>Recent files</b> menu item on the left sidebar,</li>
                    <li>choose the document you need from the list of recently edited documents.</li>
                </ol>
            </div>

            <h3>To rename an opened document</h3>
            <div class="onlineDocumentFeatures">
                <p>In the <em>online editor</em></p>
                <ol>
                    <li>click the document name at the top of the page,</li>
                    <li>enter a new document name,</li>
                    <li>press <b>Enter</b> to accept the changes.</li>
                </ol>
            </div>

            <p>To open the folder, where the file is stored, <span class="onlineDocumentFeatures"> in a new browser tab in the <em>online editor</em></span> <span class="desktopDocumentFeatures">in the file explorer window in the <em>desktop editor</em>,</span> click the <span class="icon icon-gotodocuments"></span> <b>Open file location</b> icon on the right side of the editor header. Alternatively, you can switch to the <b>File</b> tab on the top toolbar and select the <b>Open file location</b> option.</p>
        </div>
	</body>
</html>