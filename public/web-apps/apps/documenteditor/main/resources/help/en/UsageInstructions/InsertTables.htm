﻿<!DOCTYPE html>
<html>
	<head>
		<title>Insert tables</title>
		<meta charset="utf-8" />
		<meta name="description" content="Add a table to your document and adjust its properties" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Insert tables</h1>
            <h3>Insert a table</h3>
			<p>To insert a table in the <a href="https://www.onlyoffice.com/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Document Editor</b></a>,</p>
			<ol>
                <li>place the cursor where the table should be added,</li>
                <li>switch to the <b>Insert</b> tab of the top toolbar,</li>
				<li>click the <div class = "icon icon-table"></div> <b>Table</b> icon on the top toolbar,</li>
				<li>select the option to create a table:
					<ul>
						<li><p>either a table with predefined number of cells (10 by 8 cells maximum)</p>
						<p>If you want to quickly add a table, just select the number of rows (8 maximum) and columns (10 maximum).</p></li>
						<li><p>or a custom table</p>
						<p>In case you need more than 10 by 8 cell table, select the <b>Insert Custom Table</b> option that will open the window where you can enter the necessary number of rows and columns respectively, then click the <b>OK</b> button.</p>
						<p><img alt="Custom table" src="../images/customtable.png" /></p>
						</li>
                        <li>If you want to draw a table using the mouse, select the <b>Draw Table</b> option. This can be useful, if you want to create a table with rows and columns of different sizes. The mouse cursor will turn into a pencil <div class = "icon icon-pencil_tool"></div>. Draw a rectangular shape where you want to add a table, then add rows by drawing horizontal lines and columns by drawing vertical lines within the table boundary.</li>
                        <li>
                            If you want to convert an existing text into a table, select the <b>Convert Text to Table</b> option. This feature can prove useful when you already have some text that you have decided to arrange into a table. The <b>Convert Text to Table</b> window consists of 3 sections:
                            <p><img alt="Convert Text to Table" src="../images/converttotable.png" /></p>
                            <ul>
                                <li><b>Table Size</b>. Choose the required number of columns/rows you want to distribute your text into. You can either use the up/down arrow buttons or enter the number manually via keyboard.</li>
                                <li><b>Autofit Behavior</b>. Check the needed option to set the text fitting behavior: <b>Fixed column width</b> (set to <em>Auto</em> by default. You can either use the up/down arrow buttons or enter the number manually via keyboard), <b>Autofit to contents</b> (the column width corresponds with the text length), <b>Autofit to window</b> (the column width corresponds with the page width).</li>
                                <li><b>Separate Text at</b>. Check the needed option to set a delimiter type for your text: <b>Paragraphs</b>, <b>Tabs</b>, <b>Semicolons</b>, and <b>Other</b> (enter the preferred delimiter manually).</li>
                                <li>Click <b>OK</b> to convert your text to table.</li>
                            </ul>
                        </li>
                        <li>
                            If you want to insert a table as an OLE object: 
                            <ol>
                                <li>Select the <b>Insert Spreadsheet</b> option.</li>
                                <li>The corresponding window appears where you can enter the required data and format it using the Spreadsheet Editor formatting tools such as <a href="../../../../../../spreadsheeteditor/main/resources/help/en/UsageInstructions/FontTypeSizeStyle.htm">choosing font, type and style</a>, <a href="../../../../../../spreadsheeteditor/main/resources/help/en/UsageInstructions/ChangeNumberFormat.htm">setting number format</a>, <a href="../../../../../../spreadsheeteditor/main/resources/help/en/UsageInstructions/InsertFunction.htm">inserting functions</a>, <a href="../../../../../../spreadsheeteditor/main/resources/help/en/UsageInstructions/FormattedTables.htm">formatting tables</a> etc.
                                <p><img alt="OLE table" src="../../../../../../common/main/resources/help/en/images/ole_table.png" /></p></li>
                                <li>The header contains the <span class="icon icon-visible_area"></span> <b>Visible area</b> button in the top right corner of the window. Choose the <b>Edit Visible Area</b> option to select the area that will be shown when the object is inserted into the document; other data is not lost, it is just hidden. Click <b>Done</b> when ready.</li>
                                <li>Click the <b>Show Visible Area</b> button to see the selected area that will have a blue border.</li>
                                <li>When ready, click the <b>Save & Exit</b> button.</li>
                            </ol>
                        </li>
					</ul>
				</li>
				<li>once the table is added you can change its properties, size and position.</li>
			</ol>
            <p>To resize a table, hover the mouse cursor over the <span class = "icon icon-resizetable_handle"></span> handle in its lower right corner and drag it until the table reaches the necessary size.</p>
            <p><span class = "big big-resizetable"></span></p>
            <p>You can also manually change the width of a certain column or the height of a row. Move the mouse cursor over the right border of the column so that the cursor turns into the bidirectional arrow <span class = "icon icon-changecolumnwidth"></span> and drag the border to the left or right to set the necessary width. To change the height of a single row manually, move the mouse cursor over the bottom border of the row so that the cursor turns into the bidirectional arrow <span class = "icon icon-changerowheight"></span> and drag the border up or down.</p>
            <p>To move a table, hold down the <span class = "icon icon-movetable_handle"></span> handle in its upper left corner and drag it to the necessary place in the document.</p>
            <p>It's also possible to add a caption to the table. To learn more on how to work with captions for tables, you can refer to <a href="../UsageInstructions/AddCaption.htm" onclick="onhyperlinkclick(this)">this article</a>.</p>
            <hr />
            <h3>Select a table or its part</h3>
            <p>To select an entire table, click the <span class = "icon icon-movetable_handle"></span> handle in its upper left corner.</p>
            <p>To select a certain cell, move the mouse cursor to the left side of the necessary cell so that the cursor turns into the black arrow <span class = "icon icon-selectcellpointer"></span>, then left-click.</p>
            <p>To select a certain row, move the mouse cursor to the left border of the table next to the necessary row so that the cursor turns into the horizontal black arrow <span class = "icon icon-selectrowpointer"></span>, then left-click.</p>
            <p>To select a certain column, move the mouse cursor to the top border of the necessary column so that the cursor turns into the downward black arrow <span class = "icon icon-selectcolumnpointer"></span>, then left-click.</p>
            <p>It's also possible to select a cell, row, column or table using options from the contextual menu or from the <b>Rows & Columns</b> section on the right sidebar.</p>
            <p class="note">
                <b>Note</b>: to move around in a table you can use <a href="../HelpfulHints/KeyboardShortcuts.htm#workwithtables" onclick="onhyperlinkclick(this)">keyboard shortcuts</a>.
            </p>
            <hr />
            <h3>Adjust table settings</h3>
			<p>Some of the table properties as well as its structure can be altered using the right-click menu. The menu options are:</p>
			<ul>
                <li><b>Cut, Copy, Paste</b> - standard options which are used to cut or copy the selected text/object and paste the previously cut/copied text passage or object to the current cursor position.</li>
                <li><b>Select</b> is used to select a row, column, cell, or table.</li>
				<li><b>Insert</b> is used to insert a row above or row below the row where the cursor is placed as well as to insert a column at the left or right side from the column where the cursor is placed.
                    <p>It's also possible to insert several rows or columns. If you select the <b>Several Rows/Columns</b> option, the <b>Insert Several</b> window will appear. Select the <b>Rows</b> or <b>Columns</b> option from the list, specify the number of rows/column you want to add, choose where they should be added: <b>Above the cursor</b> or <b>Below the cursor</b> and click <b>OK</b>.</p>
                </li>
				<li><b>Delete</b> is used to delete a row, column, table or cells. If you select the <b>Cells</b> option, the <b>Delete Cells</b> window will open, where you can select if you want to <b>Shift cells left</b>, <b>Delete entire row</b>, or <b>Delete entire column</b>.</li>
				<li><b>Merge Cells</b> is available if two or more cells are selected and is used to merge them.
                    <p>
                        It's also possible to merge cells by erasing a boundary between them using the eraser tool. To do this, click the <span class = "icon icon-table"></span> <b>Table</b> icon on the top toolbar, choose the <b>Erase Table</b> option. The mouse cursor will turn into the eraser <span class = "icon icon-eraser_tool"></span>. Move the mouse cursor over the border between the cells you want to merge and erase it.
                    </p>
                </li>
				<li><b>Split Cell...</b> is used to open a window where you can select the needed number of columns and rows the cell will be split in.
                    <p>
                        It's also possible to split a cell by drawing rows or columns using the pencil tool. To do this, click the <span class = "icon icon-table"></span> <b>Table</b> icon on the top toolbar, choose the <b>Draw Table</b> option. The mouse cursor will turn into the pencil <span class = "icon icon-pencil_tool"></span>. Draw a horizontal line to create a row or a vertical line to create a column.
                    </p>
                </li>
				<li><b>Distribute rows</b> is used to adjust the selected cells so that they have the same height without changing the overall table height.</li>
                <li><b>Distribute columns</b> is used to adjust the selected cells so that they have the same width without changing the overall table width.</li>
                <li><b>Cell Vertical Alignment</b> is used to align the text top, center or bottom in the selected cell.</li>
                <li><b>Text Direction</b> - is used to change the text orientation in a cell. You can place the text horizontally, vertically from top to bottom (<b>Rotate Text Down</b>), or vertically from bottom to top (<b>Rotate Text Up</b>).</li>
				<li><b>Table Advanced Settings</b> is used to open the 'Table - Advanced Settings' window.</li>
				<li><b>Hyperlink</b> is used to insert a hyperlink.</li>
				<li><b>Paragraph Advanced Settings</b> is used to open the 'Paragraph - Advanced Settings' window.</li>
			</ul>
			<hr />
			<p><img class="floatleft"alt="Right Sidebar - Table Settings" src="../images/right_table.png" /></p>
            <p>You can also change the table properties on the right sidebar:</p>
			<ul style="margin-left: 280px;">
				<li><p><b>Rows</b> and <b>Columns</b> are used to select the table parts that you want to be highlighted.</p>
					<p>For rows:</p>
					<ul>
						<li><i>Header</i> - to highlight the first row</li>
						<li><i>Total</i> - to highlight the last row</li>
						<li><i>Banded</i> - to highlight every other row</li>
					</ul>
					<p>For columns:</p>
					<ul>
						<li><i>First</i> - to highlight the first column</li>
						<li><i>Last</i> - to highlight the last column</li>
						<li><i>Banded</i> - to highlight every other column</li>
					</ul>
				</li>
				<li><p><b>Select from Template</b> is used to choose a table template from the available ones.</p></li>
				<li><p><b>Borders Style</b> is used to select the border size, color, style as well as background color.</p></li>
				<li><p><b>Rows &amp; Columns</b> is used to perform some operations with the table: select, delete, insert rows and columns, merge cells, split a cell.</p></li>
                <li><p><b>Rows &amp; Columns Size</b> is used to adjust the width and height of the currently selected cell. In this section, you can also <b>Distribute rows</b> so that all the selected cells have equal height or <b>Distribute columns</b> so that all the selected cells have equal width.</p></li>
                <li><p><b>Add formula</b> is used to <a href="../UsageInstructions/AddFormulasInTables.htm" onclick="onhyperlinkclick(this)">insert a formula</a> into the selected table cell.</p></li>
                <li><p><b>Repeat as header row at the top of each page</b> is used to insert the same header row at the top of each page in long tables.</p></li>
                <li><p><b>Convert Table to Text</b> is used to arrange the table in a plain text form. The <b>Convert Table to Text</b> window sets the delimiter type for the conversion: <b>Paragraph marks</b>, <b>Tabs</b>, <b>Semicolons</b>, and <b>Other</b> (enter the preferred delimiter manually). The text in each cell of the table is considered a separate and individual element of the future text.</p></li>
				<li><p><b>Show advanced settings</b> is used to open the 'Table - Advanced Settings' window.</p></li>
			</ul>
			<hr />
            <h3>Adjust table advanced settings</h3>
            <p>To change the advanced table properties, click the table with the right mouse button and select the <b>Table Advanced Settings</b> option from the right-click menu or use the <b>Show advanced settings</b> link on the right sidebar. The table properties window will open:</p>
            <p><img alt="Table - Advanced Settings" src="../images/table_properties_1.png" /></p>
            <p>The <b>Table</b> tab allows changing the properties of the entire table.</p>
            <ul>
                <li>The <b>Table Size</b> section contains the following parameters:
                <ul>
                    <li>
                        <b>Width</b> - by default, the table width is automatically adjusted to fit the page width, i.e. the table occupies all the space between the left and right page margin. You can check this box and specify the necessary table width manually.
                    </li>
                    <li><b>Measure in</b> allows specifying the table width in absolute units i.e. <b>Centimeters</b>/<b>Points</b>/<b>Inches</b> (depending on the option specified on the <b>File</b> -> <b>Advanced Settings...</b> tab) or in <b>Percent</b> of the overall page width.
                        <p class="note"><b>Note</b>: you can also adjust the table size manually changing the row height and column width. Move the mouse cursor over a row/column border until it turns into the bidirectional arrow and drag the border. You can also use the <span class = "icon icon-columnwidthmarker"></span> markers on the horizontal ruler to change the column width and the <span class = "icon icon-rowheightmarker"></span> markers on the vertical ruler to change the row height.</p>
                    </li>
                    <li><b>Automatically resize to fit contents</b> - allows automatically change the width of each column in accordance with the text within its cells.</li>
                </ul>
                </li>
                <li>The <b>Default Cell Margins</b> section allows changing the space between the text within the cells and the cell border used by default.</li>
                <li>The <b>Options</b> section allows changing the following parameter:
                    <ul>
                        <li><b>Spacing between cells</b> - the cell spacing which will be filled with the <b>Table Background</b> color.</li>
                    </ul>
                </li>
            </ul>
            <p><img alt="Table - Advanced Settings" src="../images/table_properties_5.png" /></p>
            <p>The <b>Cell</b> tab allows changing the properties of individual cells. First you need to select the required cell or select the entire table to change the properties of all its cells.</p>
            <ul>
                <li>
                    The <b>Cell Size</b> section contains the following parameters:
                    <ul>
                        <li><b>Preferred width</b> - allows setting the preferred cell width. This is the size that a cell strives to fit, but in some cases, it may not be possible to fit this exact value. For example, if the text within a cell exceeds the specified width, it will be broken into the next line so that the preferred cell width remains unchanged, but if you insert a new column, the preferred width will be reduced.</li>
                        <li><b>Measure in</b> - allows specifying the cell width in absolute units i.e. <b>Centimeters</b>/<b>Points</b>/<b>Inches</b> (depending on the option specified on the <b>File</b> -> <b>Advanced Settings...</b> tab) or in <b>Percent</b> of the overall table width.
                            <p class="note"><b>Note</b>: you can also adjust the cell width manually. To make a single cell in a column wider or narrower than the overall column width, select the necessary cell and move the mouse cursor over its right border until it turns into the bidirectional arrow, then drag the border. To change the width of all the cells in a column, use the <span class = "icon icon-columnwidthmarker"></span> markers on the horizontal ruler to change the column width.</p>
                        </li>
                    </ul>
                </li>
                <li>The <b>Cell Margins</b> allows adjusting the space between the text within the cells and the cell border. By default, the standard values are used (the default, these values can also be altered on the <b>Table</b> tab), but you can uncheck the <b>Use default margins</b> box and enter the necessary values manually.</li>
                <li>
                    The <b>Cell Options</b> section allows changing the following parameter:
                    <ul>
                        <li>The <b>Wrap text</b> option is enabled by default. It allows wrapping the text within a cell that exceeds its width onto the next line expanding the row height and keeping the column width unchanged.</li>
                    </ul>
                </li>
            </ul>
            <p><img alt="Table - Advanced Settings" src="../images/table_properties_3.png" /></p>
            <p>The <b>Borders &amp; Background</b> tab contains the following parameters:</p>
            <ul>
                <li>
                    <b>Border</b> parameters (size, color and presence or absence) - set the border size, select its color and choose the way it will be displayed in the cells.
                    <p class="note">
                        <b>Note</b>: in case you choose not to show the table borders by clicking the <span class = "icon icon-noborders"></span> button or deselecting all the borders manually on the diagram, they will be indicated with a dotted line in the document.
                        To make them disappear at all, click the <b>Nonprinting characters</b> <span class = "icon icon-nonprintingcharacters"></span> icon on the <b>Home</b> tab of the top toolbar and select the <b>Hidden Table Borders</b> option.
                    </p>
                </li>
                <li><b>Cell Background</b> - the color for the background within the cells (available only if one or more cells are selected or the <b>Allow spacing between cells</b> option is selected at the <b>Table</b> tab).</li>
                <li><b>Table Background</b> - the color for the table background or the space background between the cells in case the <b>Allow spacing between cells</b> option is selected on the <b>Table</b> tab.</li>
            </ul>
            <p id="position"><img alt="Table - Advanced Settings" src="../images/table_properties_4.png" /></p>
			<p>The <b>Table Position</b> tab is available only if the <b>Flow table</b> option on the <b>Text Wrapping</b> tab is selected and contains the following parameters:</p>
			<ul>
				<li><b>Horizontal</b> parameters include the table <b>alignment</b> (left, center, right) <b>relative to</b> margin, page or text as well as the table <b>position to the right of</b> margin, page or text.</li>
				<li><b>Vertical</b> parameters include the table <b>alignment</b> (top, center, bottom) <b>relative to</b> margin, page or text as well as the table <b>position below</b> margin, page or text.</li>
				<li>The <b>Options</b> section allows changing the following parameters: 
                <ul>
                    <li><b>Move object with text</b> ensures that the table moves with the text.</li>
                    <li><b>Allow overlap</b> controls whether two tables are merged into one large table or overlap if you drag them near each other on the page.</li>
                </ul>
                </li>                
			</ul>            
            <p><img alt="Table - Advanced Settings" src="../images/table_properties_2.png" /></p>
            <p>The <b>Text Wrapping</b> tab contains the following parameters:</p>
            <ul>
                <li>Text <b>wrapping style</b> - <b>Inline table</b> or <b>Flow table</b>. Use the necessary option to change the way the table is positioned relative to the text: it will either be a part of the text (in case you select the inline table) or bypassed by it from all sides (if you select the flow table).</li>
                <li>
                    After you select the wrapping style, the additional wrapping parameters can be set both for inline and flow tables:
                    <ul>
                        <li>For the inline table, you can specify the table <b>alignment</b> and <b>indent from left</b>.</li>
                        <li>For the flow table, you can specify the <b>distance from text</b> and table <b>position</b> on the <b>Table Position</b> tab.</li>
                    </ul>
                </li>
            </ul>
            <p><img alt="Table - Advanced Settings" src="../images/table_properties_6.png" /></p>
            <p>The <b>Alternative Text</b> tab allows specifying the <b>Title</b> and <b>Description</b> which will be read to people with vision or cognitive impairments to help them better understand the contents of the table.</p>
                        
		</div>
	</body>
</html>