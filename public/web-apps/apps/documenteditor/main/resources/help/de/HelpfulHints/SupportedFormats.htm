﻿<!DOCTYPE html>
<html>
	<head>
		<title>Unterstützte Formate von elektronischen Dokumenten</title>
		<meta charset="utf-8" />
		<meta name="description" content="The list of document formats supported by Document Editor" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
			<div class="search-field">
				<input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
			</div>
			<h1>Unterstützte Formate von elektronischen Dokumenten</h1>
			<p>
                Elektronische Dokumente stellen die am meisten benutzte Computerdateien dar. 
                Dank des inzwischen hoch entwickelten Computernetzwerks ist es bequemer anstatt von gedruckten Dokumenten elektronische Dokumente zu verbreiten. 
                Aufgrund der Vielfältigkeit der Geräte, die für die Anzeige der Dokumente verwendet werden, gibt es viele proprietäre und offene Dateiformate. 
                Der <a target="_blank" href="https://www.onlyoffice.com/de/document-editor.aspx" onclick="onhyperlinkclick(this)"><b>Dokumenteneditor</b></a> unterstützt die geläufigsten Formate.
            </p>
            <p class="note">Beim Hochladen oder Öffnen der Datei für die Bearbeitung wird sie ins Office-Open-XML-Format (DOCX) konvertiert. Dies wird gemacht, um die Dateibearbeitung zu beschleunigen und die Interfunktionsfähigkeit zu erhöhen.</p>
            <p>Die folgende Tabelle enthält die Formate, die zum Anzeigen und/oder zur Bearbeitung geöffnet werden können.</p>
			<table>
				<tr>
					<td><b>Formate</b></td>
					<td><b>Beschreibung</b></td>
                    <td>Nativ anzeigen</td>
                    <td>Anzeigen nach Konvertierung in OOXML</td>
					<td>Nativ bearbeiten</td>
                    <td>Bearbeitung nach Konvertierung in OOXML</td>
				</tr>
                <tr>
                    <td>DjVu</td>
                    <td>Dateiformat, das hauptsächlich zum Speichern gescannter Dokumente entwickelt wurde, insbesondere solcher, die eine Kombination aus Text, Strichzeichnungen und Fotos enthalten.</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td></td>
                </tr>
				<tr>
					<td>DOC</td>
					<td>Dateierweiterung für Textverarbeitungsdokumente, die mit Microsoft Word erstellt werden.</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
					<td>+</td>
				</tr>
                <tr>
                    <td>DOCM</td>
                    <td>Macro-Enabled Microsoft Word Document<br /> Filename extension for Microsoft Word 2007 or higher generated documents with the ability to run macros</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                </tr>
				<tr>
                    <td>DOCX</td>
					<td>Office Open XML<br />Gezipptes, XML-basiertes, von Microsoft entwickeltes Dateiformat zur Präsentation von Kalkulationstabellen, Diagrammen, Präsentationen und Textverarbeitungsdokumenten.</td>
					<td>+</td>
                    <td></td>
					<td>+</td>
                    <td></td>
				</tr>
                <tr>
                    <td>DOCXF</td>
                    <td>Ein Format zum Erstellen, Bearbeiten und Zusammenarbeiten an einer Formularvorlage.</td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                </tr>
				<tr>
					<td>DOTX</td>
					<td>Word Open XML Dokumenten-Vorlage<br />Gezipptes, XML-basiertes, von Microsoft für Dokumentenvorlagen entwickeltes Dateiformat. Eine DOTX-Vorlage enthält Formatierungseinstellungen, Stile usw. und kann zum Erstellen mehrerer Dokumente mit derselben Formatierung verwendet werden.</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
					<td>+</td>
				</tr>
                <tr>
                    <td>EPUB</td>
                    <td>Electronic Publication<br />Offener Standard für E-Books vom International Digital Publishing Forum.</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                </tr>
				<tr>
					<td>FB2</td>
					<td>Eine E-Book-Dateierweiterung, mit der Sie Bücher auf Ihrem Computer oder Mobilgerät lesen können.</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
					<td>+</td>
				</tr>
                <tr>
                    <td>HTML</td>
                    <td>HyperText Markup Language<br />Hauptauszeichnungssprache für Webseiten.</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                </tr>
				<tr>
					<td>ODT</td>
					<td>Textverarbeitungsformat von OpenDocument, ein offener Standard für elektronische Dokumente.</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
					<td>+</td>
				</tr>
                <tr>
                    <td>OFORM</td>
                    <td>Ein Format zum Ausfüllen eines Formulars. Formularfelder sind ausfüllbar, aber Benutzer können die Formatierung oder Parameter der Formularelemente nicht ändern*.</td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                </tr>
				<tr>
					<td>OTT</td>
					<td>OpenDocument-Dokumentenvorlage<br />OpenDocument-Dateiformat für Dokumentenvorlagen. Eine OTT-Vorlage enthält Formatierungseinstellungen, Stile usw. und kann zum Erstellen mehrerer Dokumente mit derselben Formatierung verwendet werden.</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
					<td>+</td>
				</tr>
                <tr>
                    <td>PDF</td>
                    <td>Portable Document Format<br />Dateiformat, mit dem Dokumente unabhängig vom ursprünglichen Anwendungsprogramm, Betriebssystem und der Hardware originalgetreu wiedergegeben werden können.</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td></td>
                </tr>
                <tr>
                    <td>PDF/A</td>
                    <td>Portable Document Format / A<br />Eine ISO-standardisierte Version des Portable Document Format (PDF), die auf die Archivierung und Langzeitbewahrung elektronischer Dokumente spezialisiert ist.</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td></td>
                </tr>
				<tr>
					<td>RTF</td>
					<td>Rich Text Format<br />Plattformunabhängiges Datei- und Datenaustauschformat von Microsoft für formatierte Texte.</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
					<td>+</td>
				</tr>
				<tr>
					<td>TXT</td>
					<td>Dateierweiterung reiner Textdateien mit wenig Formatierung.</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
					<td>+</td>
				</tr>
                <tr>
                    <td>XML</td>
                    <td>Extensible Markup Language (XML).<br />Eine einfache und flexible Auszeichnungssprache, die von SGML (ISO 8879) abgeleitet ist und zum Speichern und Transportieren von Daten dient.</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td></td>
                </tr>
				<tr>
					<td>XPS</td>
					<td>Open XML Paper Specification<br />Offenes, lizenzfreies Dokumentenformat von Microsoft mit festem Layout.</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
					<td></td>
				</tr>			
			</table>
			<p class="note"><b>*Hinweis</b>: Das OFORM-Format ist ein Format zum Ausfüllen eines Formulars. Daher sind die Formularfelder nur bearbeitbar.</p>
            <p>Die folgende Tabelle enthält die Formate, in denen Sie ein Dokument über das Menü <b>Datei</b> -> <b>Herunterladen als</b> herunterladen können.</p>
            <table>
                <tr>
                    <td><b>Eingabeformat</b></td>
                    <td><b>Kann heruntergeladen werden als</b></td>
                </tr>
                <tr>
                    <td>DjVu</td>
                    <td>DjVu, PDF</td>
                </tr>
                <tr>
                    <td>DOC</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
                </tr>
                <tr>
                    <td>DOCM</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
                </tr>
                <tr>
                    <td>DOCX</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
                </tr>
                <tr>
                    <td>DOCXF</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
                </tr>
                <tr>
                    <td>DOTX</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
                </tr>
                <tr>
                    <td>EPUB</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
                </tr>
                <tr>
                    <td>FB2</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
                </tr>
                <tr>
                    <td>HTML</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
                </tr>
                <tr>
                    <td>ODT</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
                </tr>
                <tr>
                    <td>OFORM</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
                </tr>
                <tr>
                    <td>OTT</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
                </tr>
                <tr>
                    <td>PDF</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, OFORM, PDF, RTF, TXT</td>
                </tr>
                <tr>
                    <td>PDF/A</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, OFORM, PDF, RTF, TXT</td>
                </tr>
                <tr>
                    <td>RTF</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
                </tr>
                <tr>
                    <td>TXT</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
                </tr>
                <tr>
                    <td>XML</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
                </tr>
                <tr>
                    <td>XPS</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT, XPS</td>
                </tr>
            </table>
            <p>Sie können sich auch auf die Conversion-Matrix auf <a href="https://api.onlyoffice.com/editors/conversionapi#text-matrix" target="_blank" onclick="onhyperlinkclick(this)"><b>api.onlyoffice.com</b></a> beziehen, um die Möglichkeiten zu sehen, Ihre Dokumente in die bekanntesten Dateiformate zu konvertieren.</p>
		</div>
	</body>
</html>