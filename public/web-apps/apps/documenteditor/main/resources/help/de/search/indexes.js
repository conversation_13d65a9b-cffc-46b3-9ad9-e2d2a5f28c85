var indexes = 
[
   {
        "id": "HelpfulHints/About.htm", 
        "title": "Über den Dokumenteneditor", 
        "body": "Der Dokumenteneditor ist eine Online- Anwendung, mit der Sie Ihre Dokumente direkt in Ihrem Browser betrachten und bearbeiten können. Mit dem Dokumenteneditor können Sie Editiervorgänge durchführen, wie bei einem beliebigen Desktopeditor, editierte Dokumente unter Beibehaltung aller Formatierungsdetails drucken oder sie auf der Festplatte Ihres Rechners als DOCX-, PDF-, TXT-, ODT-, DOXT, PDF/A, OTF, RTF-, HTML-, FB2, oder HTML-Dateien speichern. Wenn Sie in der Online-Version mehr über die aktuelle Softwareversion, das Build und den Lizenzgeber erfahren möchten, klicken Sie auf das Symbol in der linken Seitenleiste. Wenn Sie in der Desktop-Version für Windows mehr über die aktuelle Softwareversion und den Lizenzgeber erfahren möchten, wählen Sie das Menü Über in der linken Seitenleiste des Hauptfensters. Öffnen Sie in der Desktop-Version für Mac OS das Menü ONLYOFFICE oben auf dem Bildschirm und wählen Sie den Menüpunkt Über ONLYOFFICE."
    },
   {
        "id": "HelpfulHints/AdvancedSettings.htm", 
        "title": "Erweiterte Einstellungen des Dokumenteneditors", 
        "body": "Der Dokumenteneditor ermöglicht es Ihnen, die erweiterten Einstellungen zu ändern. Um darauf zuzugreifen, öffnen Sie die Registerkarte Datei und wählen Sie die Option Erweiterte Einstellungen. Die erweiterten Einstellungen sind wie folgt gruppiert: Bearbeitung und Speicherung Die Option Automatisches speichern wird in der Online-Version verwendet, um das automatische Speichern von Änderungen, die Sie während der Bearbeitung vornehmen, ein-/auszuschalten. Die Option Wiederherstellen wird in der Desktop-Version verwendet, um die Option ein-/auszuschalten, die die automatische Wiederherstellung von Dokumenten ermöglicht, falls das Programm unerwartet geschlossen wird. Die Schaltfläche Einfügeoptionen beim Einfügen von Inhalten anzeigen. Das entsprechende Symbol wird angezeigt, wenn Sie Inhalte in das Dokument einfügen. Die Dateien mit älteren MS Word-Versionen kompatibel machen, wenn sie als DOCX gespeichert werden. Die im DOCX-Format gespeicherten Dateien werden mit älteren Versionen von Microsoft Word kompatibel. Zusammenarbeit Im Unterabschnitt Modus \"Gemeinsame Bearbeitung\" können Sie den bevorzugten Modus zum Anzeigen von Änderungen am Dokument festlegen, wenn Sie gemeinsam arbeiten. Schnell (standardmäßig). Die Benutzer, die an der gemeinsamen Bearbeitung von Dokumenten teilnehmen, sehen die Änderungen in Echtzeit, sobald sie von anderen Benutzern vorgenommen wurden. Formal. Alle von Mitbearbeitern vorgenommenen Änderungen werden erst angezeigt, nachdem Sie auf das Symbol Speichern geklickt haben, das Sie über neue Änderungen informiert. Im Unterabschnitt Änderungen anzeigen können Sie auswählen, wie neue Änderungen angezeigt werden. In Sprechblasen beim Klicken anzeigen. Die Änderung wird in einer Sprechblase angezeigt, wenn Sie auf die nachverfolgte Änderung klicken. In Tipps anzeigen. Ein Tooltip wird angezeigt, wenn Sie den Mauszeiger über die nachverfolgte Änderung bewegen. Im Unterabschnitt Änderungen bei der Echtzeit-Zusammenarbeit zeigen können Sie auswählen, wie neue Änderungen und Kommentare in Echtzeit angezeigt werden. Keine. Alle während der aktuellen Sitzung vorgenommenen Änderungen werden nicht hervorgehoben. Alle anzeigen. Alle während der aktuellen Sitzung vorgenommenen Änderungen werden hervorgehoben. Letzte anzeigen. Alle Änderungen werden hervorgehoben, die Sie vorgenommen haben, seit Sie das letzte Mal das Symbol Speichern angeklickt haben. Diese Option ist nur verfügbar, wenn Sie in der Co-Bearbeitung den Modus Formal ausgewählt haben. Kommentare im Text anzeigen. Wenn Sie diese Funktion deaktivieren, werden die kommentierten Passagen nur dann hervorgehoben, wenn Sie auf das Symbol Kommentare in der linken Seitenleiste klicken. Gelöste Kommentare anzeigen. Diese Funktion ist standardmäßig deaktiviert, sodass die aufgelösten Kommentare im Dokumenttext ausgeblendet werden. Sie können solche Kommentare nur anzeigen, wenn Sie in der linken Seitenleiste auf das Symbol Kommentare klicken. Aktivieren Sie diese Option, wenn Sie aufgelöste Kommentare im Dokumenttext anzeigen möchten. Rechtschreibprüfung Die Option Rechtschreibprüfung wird verwendet, um die Rechtschreibprüfung ein-/auszuschalten. Wörter in GROSSBUCHSTABEN ignorieren. In Großbuchstaben eingegebene Wörter werden bei der Rechtschreibprüfung ignoriert. Wörter mit Zahlen ignorieren. Wörter mit Zahlen werden bei der Rechtschreibprüfung ignoriert. Über das Menü Automatische Korrekturoptionen können Sie auf die Autokorrektur-Einstellungen zugreifen, z. B. Text während der Eingabe ersetzen, Funktionen erkennen, automatische Formatierung usw. Arbeitsbereich Die Option Ausrichtungslinien wird zum Ein-/Ausschalten der Ausrichtungshilfslinien verwendet, die beim Verschieben von Objekten angezeigt werden. Sie ermöglicht eine präzisere Objektpositionierung auf der Seite. Die Option Hieroglyphen wird verwendet, um die Anzeige von Hieroglyphen ein-/auszuschalten. Die Option Verwenden Sie die Alt-Taste, um über die Tastatur in der Benutzeroberfläche zu navigieren wird verwendet, um die Verwendung der Alt-Taste in Tastaturkürzeln zu aktivieren. Die Option Thema der Benutzeroberfläche wird verwendet, um das Farbschema der Benutzeroberfläche des Editors zu ändern. Die Option Wie im System sorgt dafür, dass der Editor dem Oberflächendesign Ihres Systems folgt. Das Farbschema Hell umfasst die Standardfarben Blau, Weiß und Hellgrau mit weniger Kontrast in UI-Elementen, die für die Arbeit tagsüber geeignet sind. Das Farbschema Klassisch Hell umfasst die Standardfarben Blau, Weiß und Hellgrau. Das Farbschema Dunkel umfasst schwarze, dunkelgraue und hellgraue Farben, die für Arbeiten bei Nacht geeignet sind. Das Farbschema Dunkler Kontrast umfasst schwarze, dunkelgraue und weiße Farben mit mehr Kontrast in UI-Elementen, die den Arbeitsbereich der Datei hervorheben. Die Option Dunkelmodus aktivieren wird verwendet, um den Arbeitsbereich dunkler zu machen, wenn der Editor auf das Oberflächendesign Dunkel oder Dunkler Kontrast eingestellt ist. Aktivieren Sie das Kontrollkästchen Dunkelmodus aktivieren, um diese Option zu aktivieren. Abgesehen von den verfügbaren Benutzeroberflächendesigns Hell, Klassisch Hell, Dunkel und Dunkler Kontrast können jetzt ONLYOFFICE-Editoren mit Ihrem eigenen Farbschema angepasst werden. Bitte befolgen Sie diese Anleitung, um zu erfahren, wie Sie das tun können. Die Option Maßeinheit wird verwendet, um anzugeben, welche Einheiten auf den Linealen und in Eigenschaften von Objekten verwendet werden, wenn Parameter wie Breite, Höhe, Abstand, Ränder usw. eingestellt werden. Die verfügbaren Einheiten sind Zentimeter, Punkt und Zoll. Die Option Standard-Zoom-Wert wird verwendet, um den Standard-Zoom-Wert festzulegen, indem Sie ihn in der Liste der verfügbaren Optionen zwischen 50 % und 500 % auswählen. Sie können auch die Option Seite anpassen oder Breite anpassen auswählen. Die Option Schriftglättung wird verwendet, um auszuwählen, wie Schriftarten im Dokumenteneditor angezeigt werden. Wählen Sie Wie Windows, wenn Ihnen die Art gefällt, wie die Schriftarten unter Windows gewöhnlich angezeigt werden, d.h. mit Windows-artigen Hints. Wählen Sie Wie OS X, wenn Ihnen die Art gefällt, wie die Schriftarten auf einem Mac gewöhnlich angezeigt werden, d.h. ohne Hints. Wählen Sie Native, wenn Sie möchten, dass Ihr Text mit den Hints angezeigt wird, die in Schriftartdateien eingebettet sind. Standard-Cache-Modus wird verwendet, um den Cache-Modus für die Schriftzeichen auszuwählen. Es wird nicht empfohlen, es ohne Grund zu wechseln. Es kann nur in manchen Fällen hilfreich sein, beispielsweise wenn ein Problem im Google Chrome-Browser mit aktivierter Hardwarebeschleunigung auftritt. Der Dokumenteneditor verfügt über zwei Cache-Modi: Im ersten Cache-Modus wird jeder Buchstabe als separates Bild zwischengespeichert. Im zweiten Cache-Modus wird ein Bild einer bestimmten Größe ausgewählt, in dem Buchstaben dynamisch platziert werden, und ein Mechanismus zum Zuweisen/Entfernen von Speicher in diesem Bild wird ebenfalls implementiert. Wenn nicht genügend Speicherplatz vorhanden ist, wird ein zweites Bild erstellt usw. Die Einstellung Standard-Cache-Modus wendet zwei oben genannte Cache-Modi separat für verschiedene Browser an: Wenn die Einstellung Standard-Cache-Modus aktiviert ist, verwendet Internet Explorer (Version 9, 10, 11) den zweiten Cache-Modus, andere Browser verwenden den ersten Cache-Modus. Wenn die Einstellung Standard-Cache-Modus deaktiviert ist, verwendet Internet Explorer (Version 9, 10, 11) den ersten Cache-Modus, andere Browser verwenden den zweiten Cache-Modus. Die Option Einstellungen von Makros wird verwendet, um die Anzeige von Makros mit einer Benachrichtigung einzustellen. Wählen Sie Alle deaktivieren, um alle Makros im Dokument zu deaktivieren. Wählen Sie Benachrichtigung anzeigen, um Benachrichtigungen über Makros im Dokument zu erhalten. Wählen Sie Alle aktivieren, um automatisch alle Makros im Dokument auszuführen. Um die vorgenommenen Änderungen zu speichern, klicken Sie auf Anwenden."
    },
   {
        "id": "HelpfulHints/CollaborativeEditing.htm", 
        "title": "Gemeinsame Bearbeitung von Dokumenten in Echtzeit", 
        "body": "Der Dokumenteneditor ermöglicht es Ihnen, einen konstanten teamweiten Ansatz für den Arbeitsablauf beizubehalten: Sie können die Dateien und Ordner freigeben; direkt im Editor kommunizieren; bestimmte Teile Ihrer Dokumente, die zusätzliche Eingaben Dritter erfordern, kommentieren; Dokumentversionen für zukünftige Verwendung speichern; Dokumente überprüfen und Ihre Änderungen hinzufügen, ohne die Datei tatsächlich zu bearbeiten; Dokumente vergleichen und zusammenführen, um die Verarbeitung und Bearbeitung zu erleichtern. Im Dokumenteneditor können Sie in Echtzeit an Dokumenten mit zwei Modi zusammenarbeiten: Schnell oder Formal. Die Modi können in den erweiterten Einstellungen ausgewählt werden. Es ist auch möglich, den gewünschten Modus über das Symbol Modus \"Gemeinsame Bearbeitung\" auf der Registerkarte Zusammenarbeit in der oberen Symbolleiste auswählen: Die Anzahl der Benutzer, die an dem aktuellen Dokument arbeiten, wird auf der rechten Seite der Editor-Kopfzeile angezeigt - . Wenn Sie sehen möchten, wer genau die Datei gerade bearbeitet, können Sie auf dieses Symbol klicken oder das Chat-Bedienfeld mit der vollständigen Liste der Benutzer öffnen. Modus \"Schnell\" Der Modus Schnell wird standardmäßig verwendet und zeigt die von anderen Benutzern vorgenommenen Änderungen in Echtzeit an. Wenn Sie ein Dokument in diesem Modus gemeinsam bearbeiten, ist die Möglichkeit zum Wiederholen des letzten rückgängig gemachten Vorgangs nicht verfügbar. In diesem Modus werden die Aktionen und die Namen der Co-Autoren angezeigt, wenn sie den Text bearbeiten. Wenn Sie den Mauszeiger über eine der bearbeiteten Passagen bewegen, wird der Name des Benutzers angezeigt, der sie gerade bearbeitet. Modus \"Formal\" Der Modus Formal wird ausgewählt, um die von anderen Benutzern vorgenommenen Änderungen auszublenden, bis Sie auf das Symbol Speichern   klicken, um Ihre Änderungen zu speichern und die von Co-Autoren vorgenommenen Änderungen anzunehmen. Wenn ein Dokument in diesem Modus von mehreren Benutzern gleichzeitig bearbeitet wird, werden die bearbeiteten Textpassagen mit gestrichelten Linien in unterschiedlichen Farben gekennzeichnet. Sobald einer der Benutzer seine Änderungen durch Klicken auf das Symbol speichert, sehen die anderen einen Hinweis in der Statusleiste, der darauf hinweist, dass es Aktualisierungen gibt. Um die von Ihnen vorgenommenen Änderungen zu speichern, damit andere Benutzer sie sehen und die von Ihren Mitbearbeitern gespeicherten Aktualisierungen abrufen können, klicken Sie auf das Symbol in der linken oberen Ecke der oberen Symbolleiste. Die Aktualisierungen werden hervorgehoben, damit Sie sehen können, was genau geändert wurde. Sie können angeben, welche Änderungen während der gemeinsamen Bearbeitung hervorgehoben werden sollen, indem Sie auf die Registerkarte Datei in der oberen Symbolleiste klicken, die Option Erweiterte Einstellungen... auswählen und eine der drei Möglichkeiten auswählen: Alle anzeigen: Alle Änderungen, die während der aktuellen Sitzung vorgenommen wurden, werden hervorgehoben. Letzte anzeigen: Nur die Änderungen, die seit dem letzten Klicken auf das Symbol  vorgenommen wurden, werden hervorgehoben. Keine: Änderungen, die während der aktuellen Sitzung vorgenommen wurden, werden nicht hervorgehoben. Modus \"Live Viewer\" Der Modus Live Viewer wird verwendet, um die von anderen Benutzern vorgenommenen Änderungen in Echtzeit anzuzeigen, wenn das Dokument von einem Benutzer mit den Zugriffsrechten Schreibgeschützt geöffnet wird. Damit der Modus richtig funktioniert, stellen Sie sicher, dass das Kontrollkästchen Änderungen von anderen Benutzer anzeigen in den Erweiterten Einstellungen des Editors aktiviert ist. Anonym Portalbenutzer, die nicht registriert sind und kein Profil haben, gelten als anonym, können jedoch weiterhin an Dokumenten zusammenarbeiten. Um ihnen einen Namen zuzuweisen, muss der anonyme Benutzer beim ersten Öffnen des Dokuments einen Namen in das entsprechende Feld in der rechten oberen Ecke des Bildschirms eingeben. Aktivieren Sie das Kontrollkästchen \"Nicht mehr anzeigen\", um den Namen beizubehalten."
    },
   {
        "id": "HelpfulHints/Commenting.htm", 
        "title": "Dokumente kommentieren", 
        "body": "Der Dokumenteneditor ermöglicht es Ihnen, einen konstanten teamweiten Ansatz für den Arbeitsablauf beizubehalten: Sie können die Dateien und Ordner freigeben; an Dokumenten in Echtzeit zusammenarbeiten; direkt im Editor kommunizieren; Dokumentversionen für zukünftige Verwendung speichern; Dokumente überprüfen und Ihre Änderungen hinzufügen, ohne die Datei tatsächlich zu bearbeiten; Dokumente vergleichen und zusammenführen, um die Verarbeitung und Bearbeitung zu erleichtern. Im Dokumenteneditor können Sie Kommentare zum Inhalt von Dokumenten hinterlassen, ohne diese tatsächlich zu bearbeiten. Im Gegensatz zu Chat-Nachrichten bleiben die Kommentare, bis sie gelöscht werden. Kommentare hinterlassen und darauf antworten Um einen Kommentar zu hinterlassen: Wählen Sie eine Textpassage aus, bei der Sie der Meinung sind, dass ein Fehler oder Problem vorliegt. Wechseln Sie zur Registerkarte Einfügen oder Zusammenarbeit der oberen Symbolleiste und klicken Sie auf die Schaltfläche Kommentar hinzufügen, oder Verwenden Sie das Symbol in der linken Seitenleiste, um das Bedienfeld Kommentare zu öffnen, und klicken Sie auf den Link Kommentar zum Dokument hinzufügen, oder Klicken Sie mit der rechten Maustaste auf die ausgewählte Textpassage und wählen Sie im Kontextmenü die Option Kommentar hinzufügen. Geben Sie den erforderlichen Text ein. Klicken Sie auf die Schaltfläche Kommentar hinzufügen/Hinzufügen. Der Kommentar wird links im Bereich Kommentare angezeigt. Jeder andere Benutzer kann den hinzugefügten Kommentar beantworten, indem er Fragen stellt oder über seine Arbeit berichtet. Klicken Sie dazu auf den Link Antwort hinzufügen unterhalb des Kommentars, geben Sie Ihre Antwort in das Eingabefeld ein und drücken Sie die Schaltfläche Antworten. Wenn Sie den Co-Bearbeitungsmodus Formal verwenden, werden neue Kommentare, die von anderen Benutzern hinzugefügt wurden, erst sichtbar, nachdem Sie auf das Symbol in der linken oberen Ecke in der oberen Symbolleiste geklickt haben. Anzeige von Kommentaren deaktivieren Die von Ihnen kommentierte Textpassage wird im Dokument hervorgehoben. Um den Kommentar anzuzeigen, klicken Sie einfach innerhalb der Passage. Um diese Funktion zu deaktivieren: Klicken Sie auf die Registerkarte Datei in der oberen Symbolleiste. Wählen Sie die Option Erweiterte Einstellungen... aus. Deaktivieren Sie das Kontrollkästchen Live-Kommentare einschalten. Jetzt werden die kommentierten Passagen nur dann hervorgehoben, wenn Sie auf das Symbol  klicken. Kommentare verwalten Sie können die hinzugefügten Kommentare mit den Symbolen in der Kommentarsprechblase oder im Bereich Kommentare auf der linken Seite verwalten: Sortieren Sie die hinzugefügten Kommentare, indem Sie auf das Symbol klicken: nach Datum: Neueste zuerst oder Älteste zuerste. Dies ist die standardmäßige Sortierreihenfolge. nach Verfasser: Verfasser (A-Z) oder Verfasser (Z-A). nach Reihenfolge: Von oben oder Von unten. Die übliche Sortierreihenfolge von Kommentaren nach ihrer Position in einem Dokument ist wie folgt (von oben): Kommentare zu Text, Kommentare zu Fußnoten, Kommentare zu Endnoten, Kommentare zu Kopf-/Fußzeilen, Kommentare zum gesamten Dokument. nach Gruppe: Alle oder wählen Sie eine bestimmte Gruppe aus der Liste aus. Diese Sortieroption ist verfügbar, wenn Sie eine Version ausführen, die diese Funktionalität enthält. Bearbeiten Sie den aktuell ausgewählten Kommentar, indem Sie auf das Symbol klicken. Löschen Sie den aktuell ausgewählten Kommentar, indem Sie auf das Symbol klicken. Schließen Sie die aktuell ausgewählte Diskussion, indem Sie auf das Symbol klicken, wenn die von Ihnen in Ihrem Kommentar angegebene Aufgabe oder das Problem gelöst wurde, danach die von Ihnen geöffnete Diskussion mit Ihrem Kommentar erhält den gelösten Status. Klicken Sie auf das Symbol , um die Diskussion neu zu öffnen. Wenn Sie aufgelöste Kommentare ausblenden möchten, klicken Sie auf die Registerkarte Datei in der oberen Symbolleiste, wählen Sie die Option Erweiterte Einstellungen... und deaktivieren Sie die Option Anzeige der aktivieren Kommentare gelöst und klicken Sie auf Anwenden. In diesem Fall werden die gelösten Kommentare nur hervorgehoben, wenn Sie auf das Symbol klicken. Wenn Sie mehrere Kommentare verwalten möchten, öffnen Sie das Drop-Down-Menü Lösen auf der Registerkarte Zusammenarbeit. Wählen Sie eine der Optionen zum Auflösen von Kommentaren aus: Gültige Kommentare lösen, Meine Kommentare lösen oder Alle Kommentare lösen. Erwähnungen hinzufügen Sie können Erwähnungen nur zu den Kommentaren zu den Textteilen hinzufügen, nicht zum Dokument selbst. Beim Eingeben von Kommentaren können Sie die Funktion Erwähnungen verwenden, mit der Sie jemanden auf den Kommentar aufmerksam machen und dem genannten Benutzer per E-Mail und Chat eine Benachrichtigung senden können. Um eine Erwähnung hinzuzufügen: Geben Sie das Zeichen \"+\" oder \"@\" an einer beliebigen Stelle im Kommentartext ein - eine Liste der Portalbenutzer wird geöffnet. Um den Suchvorgang zu vereinfachen, können Sie im Kommentarfeld mit der Eingabe eines Namens beginnen - die Benutzerliste ändert sich während der Eingabe. Wählen Sie die erforderliche Person aus der Liste aus. Wenn die Datei noch nicht für den genannten Benutzer freigegeben wurde, wird das Fenster Freigabeeinstellungen geöffnet. Der Zugriffstyp Schreibgeschützt ist standardmäßig ausgewählt. Ändern Sie es bei Bedarf. Klicken Sie auf OK. Der erwähnte Benutzer erhält eine E-Mail-Benachrichtigung, dass er in einem Kommentar erwähnt wurde. Wurde die Datei freigegeben, erhält der Benutzer auch eine entsprechende Benachrichtigung. Kommentare entfernen Um Kommentare zu entfernen: Klicken Sie auf die Schaltfläche Kommentare entfernen auf der Registerkarte Zusammenarbeit der oberen Symbolleiste. Wählen Sie die erforderliche Option aus dem Menü: Aktuelle Kommentare entfernen, um den aktuell ausgewählten Kommentar zu entfernen. Wenn dem Kommentar einige Antworten hinzugefügt wurden, werden alle seine Antworten ebenfalls entfernt. Meine Kommentare entfernen, um von Ihnen hinzugefügte Kommentare zu entfernen, ohne von anderen Benutzern hinzugefügte Kommentare zu entfernen. Wenn Ihrem Kommentar einige Antworten hinzugefügt wurden, werden auch alle Antworten entfernt. Alle Kommentare entfernen, um alle Kommentare in der Präsentation zu entfernen, die Sie und andere Benutzer hinzugefügt haben. Um die Leiste mit den Kommentaren zu schließen, klicken Sie in der linken Seitenleiste erneut auf das Symbol ."
    },
   {
        "id": "HelpfulHints/Communicating.htm", 
        "title": "Kommunikation in Echtzeit", 
        "body": "Der Dokumenteneditor ermöglicht es Ihnen, einen konstanten teamweiten Ansatz für den Arbeitsablauf beizubehalten: Sie können die Dateien und Ordner freigeben; an Dokumenten in Echtzeit zusammenarbeiten; bestimmte Teile Ihrer Dokumente, die zusätzliche Eingaben Dritter erfordern, kommentieren; Dokumentversionen für zukünftige Verwendung speichern; Dokumente überprüfen und Ihre Änderungen hinzufügen, ohne die Datei tatsächlich zu bearbeiten; Dokumente vergleichen und zusammenführen, um die Verarbeitung und Bearbeitung zu erleichtern. Im Dokumenteneditor können Sie mit Ihren Mitbearbeitern in Echtzeit kommunizieren, indem Sie das integrierte Chat-Tool sowie eine Reihe nützlicher Plugins verwenden, z. B. Telegram oder Rainbow. Um auf das Chat-Tool zuzugreifen und eine Nachricht für andere Benutzer zu hinterlassen: Klicken Sie im linken Seitenbereich auf das Symbol oder wechseln Sie in der oberen Symbolleiste in die Registerkarte Zusammenarbeit und klicken Sie auf die Schaltfläche Chat. Geben Sie Ihren Text in das entsprechende Feld unten ein. Klicken Sie auf Senden. Die Chat-Nachrichten werden nur während einer Sitzung gespeichert. Um den Inhalt des Dokuments zu diskutieren, ist es besser, Kommentare zu verwenden, die gespeichert werden, bis sie gelöscht werden. Alle Nachrichten, die von Benutzern hinterlassen wurden, werden links in der Leiste angezeigt. Liegen ungelesene neue Nachrichten vor, sieht das Chat-Symbol wie folgt aus - . Um die Leiste mit den Chat-Nachrichten zu schließen, klicken Sie in der linken Seitenleiste auf das Symbol oder klicken Sie in der oberen Symbolleiste erneut auf die Schaltfläche Chat."
    },
   {
        "id": "HelpfulHints/Comparison.htm", 
        "title": "Dokumente vergleichen", 
        "body": "Der Dokumenteneditor ermöglicht es Ihnen, einen konstanten teamweiten Ansatz für den Arbeitsablauf beizubehalten: Sie können die Dateien und Ordner freigeben; an Dokumenten in Echtzeit zusammenarbeiten; direkt im Editor kommunizieren; bestimmte Teile Ihrer Dokumente, die zusätzliche Eingaben Dritter erfordern, kommentieren; Dokumentversionen für zukünftige Verwendung speichern; Dokumente überprüfen und Ihre Änderungen hinzufügen, ohne die Datei tatsächlich zu bearbeiten. Wenn Sie zwei Dokumente vergleichen und zusammenführen müssen, bietet Ihnen der Dokumenteneditor die Dokument-Vergleichsfunktion. Es ermöglicht, die Unterschiede zwischen zwei Dokumenten anzuzeigen und die Dokumente zusammenzuführen, indem die Änderungen einzeln oder alle auf einmal akzeptiert werden. Nach dem Vergleichen und Zusammenführen zweier Dokumente wird das Ergebnis als neue Version der Originaldatei im Portal gespeichert. Wenn Sie die zu vergleichenden Dokumente nicht zusammenführen müssen, können Sie alle Änderungen verwerfen, sodass das Originaldokument unverändert bleibt. Dokument zum Vergleich auswählen Um zwei Dokumente zu vergleichen, öffnen Sie das Originaldokument, das Sie vergleichen möchten, und wählen Sie das zweite Dokument zum Vergleich aus: Wechseln Sie in der oberen Symbolleiste zur Registerkarte Zusammenarbeit und klicken Sie auf die Schaltfläche Vergleichen. Wählen Sie eine der folgenden Optionen, um das Dokument zu laden: die Option Dokument aus Datei öffnet das Standarddialogfenster für Dateiauswahl. Finden Sie die gewünschte .docx Datei und klicken Sie die Schaltfläche Öffnen an. Die Option Dokument aus URL öffnet das Fenster, in dem Sie einen Link zu der Datei eingeben können, die in einem Webspeicher eines Drittanbieters (z. B. Nextcloud) gespeichert ist, wenn Sie entsprechende Zugriffsrechte darauf haben. Der Link muss ein direkter Link zum Herunterladen der Datei sein. Wenn der Link angegeben ist, klicken Sie auf die Schaltfläche OK. Die direkte URL lässt die Datei herunterladen, ohne diese Datei im Browser zu öffnen. Z.B., im Nextcloud kann man die direkte URL so erhalten: Finden Sie die gewünschte Datei in der Dateiliste, wählen Sie die Option Details im Menü aus. Klicken Sie die Option Direkte URL kopieren (nur für die Benutzer, die den Zugriff auf diese Datei haben) rechts auf dem Detailspanel an. Lesen Sie die entsprechende Servicedokumentation, um zu lernen, wie kann man eine direkte URL in anderen Online-Services erhalten. Die Option Dokument aus Speicher öffnet das Fenster Datenquelle auswählen. Es zeigt die Liste aller auf Ihrem Portal gespeicherten .docx-Dokumente an, für die Sie entsprechende Zugriffsrechte haben. Um durch die Abschnitte des Moduls Dokumente zu navigieren, verwenden Sie das Menü im linken Teil des Fensters. Wählen Sie das erforderliche .docx-Dokument aus und klicken Sie auf die Schaltfläche OK. Wenn das zweite zu vergleichende Dokument ausgewählt wird, beginnt der Vergleichsprozess und das Dokument sieht so aus, als ob es im Modus Review geöffnet ist. Alle Änderungen werden mit einer Farbe hervorgehoben, und Sie können die Änderungen anzeigen, zwischen ihnen navigieren, die Änderungen einzeln oder alle auf einmal annehmen oder ablehnen. Es ist auch möglich, den Anzeigemodus zu ändern und zu sehen, wie das Dokument vor dem Vergleich, während des Vergleichs oder nach dem Vergleich aussieht, wenn Sie alle Änderungen annehmen. Den Anzeigemodus für die Änderungen auswählen Klicken Sie auf die Schaltfläche Anzeigemodus in der oberen Symbolleiste und wählen Sie einen der verfügbaren Modi aus der Liste aus: Markup - diese Option ist standardmäßig. Verwenden Sie sie, um das Dokument während des Vergleichsprozesses anzuzeigen. Im diesen Modus können Sie das Dokument sehen sowie bearbeiten. Endgültig - der Modus zeigt das Dokument an, als ob alle Änderungen übernommen sind, nämlich nach dem Vergleichsprozess. Diese Option nimmt alle Änderungen nicht, sie zeigt nur das Dokument an, als ob die Änderungen schon übernommen sind. Im diesen Modus können Sie das Dokument nicht bearbeiten. Original - der Modus zeigt das Originaldokument an, nämlich vor dem Vergleichsprozess, als ob alle Änderungen abgelehnt sind. Diese Option lehnt alle Änderungen nicht ab, sie zeigt nur das Dokument an, als ob die Änderungen nicht übernommen sind. Im diesen Modus können Sie das Dokument nicht bearbeiten. Änderungen annehmen oder ablehnen Verwenden Sie die Schaltflächen Zur vorherigen Änderung und Zur nächsten Änderung in der oberen Symbolleiste, um die Änderungen zu navigieren. Um die aktuell ausgewählte Änderung zu akzeptieren, können Sie: Klicken Sie auf die Schaltfläche Annehmen in der oberen Symbolleiste, oder Klicken Sie den Abwärtspfeil unter der Schaltfläche Annehmen an und wählen Sie die Option Aktuelle Änderungen annehmen aus (die Änderung wird angenommen und Sie übergehen zur nächsten Änderung) oder Klicken Sie die Schaltfläche Annehmen im Pop-Up-Fenster an. Um alle Änderungen anzunehmen, klicken Sie den Abwärtspfeil unter der Schaltfläche Annehmen an und wählen Sie die Option Alle Änderungen annehmen aus. Um die aktuelle Änderung abzulehnen: Klicken Sie die Schaltfläche Ablehnen in der oberen Symbolleiste, oder klicken Sie den Abwärtspfeil unter der Schaltfläche Ablehnen an und wählen Sie die Option Aktuelle Änderung ablehnen aus (die Änderung wird abgelehnt und Sie übergehen zur nächsten Änderung) oder Лlicken Sie die Schaltfläche Ablehnen im Pop-Up-Fenster an. Um alle Änderungen abzulehnen, klicken Sie den Abwärtspfeil unter der Schaltfläche Ablehnen an und wählen Sie die Option Alle Änderungen ablehnen aus. Zusatzinformation für die Vergleich-Funktion Die Vergleichsmethode Dokumente werden wortweise verglichen. Wenn ein Wort eine Änderung von mindestens einem Zeichen enthält (z. B. wenn ein Zeichen entfernt oder ersetzt wurde), wird die Differenz im Ergebnis als Änderung des gesamten Wortes und nicht des Zeichens angezeigt. Das folgende Bild zeigt den Fall, dass die Originaldatei das Wort „Symbole“ und das Vergleichsdokument das Wort „Symbol“ enthält. Urheberschaft des Dokuments Wenn der Vergleichsprozess gestartet wird, wird das zweite Dokument zum Vergleichen hochgeladen und mit dem aktuellen verglichen. Wenn das geladene Dokument einige Daten enthält, die nicht im Originaldokument enthalten sind, werden die Daten als von einem Überprüfer hinzugefügt markiert. Wenn das Originaldokument einige Daten enthält, die nicht im geladenen Dokument enthalten sind, werden die Daten von einem Überprüfer als gelöscht markiert. Wenn die Autoren des Originaldokuments und des geladenen Dokuments dieselbe Person sind, ist der Überprüfer derselbe Benutzer. Sein/ihr Name wird in der Änderungssprechblase angezeigt. Wenn die Autoren zweier Dateien unterschiedliche Benutzer sind, dann ist der Autor der zweiten zum Vergleich geladenen Datei der Autor der hinzugefügten/entfernten Änderungen. Die nachverfolgten Änderungen im verglichenen Dokument Wenn das Originaldokument einige Änderungen enthält, die im Modus \"Review\" vorgenommen wurden, werden diese im Vergleichsprozess übernommen. Wenn Sie das zweite Dokument zum Vergleich auswählen, wird die entsprechende Warnmeldung angezeigt. In diesem Fall enthält das Dokument im Originalanzeigemodus keine Änderungen."
    },
   {
        "id": "HelpfulHints/KeyboardShortcuts.htm", 
        "title": "Tastaturkürzel", 
        "body": "Tastenkombinationen Tastenkombinationen für Key-Tipps Verwenden Sie Tastenkombinationen für einen schnelleren und einfacheren Zugriff auf die Funktionen des Dokumenteneditors ohne eine Maus zu verwenden. Drücken Sie die Alt-Taste, um alle wichtigen Tipps für die Kopfzeile des Editors, die obere Symbolleiste, die rechte und linke Seitenleiste und die Statusleiste einzuschalten. Drücken Sie den Buchstaben, der dem Element entspricht, das Sie verwenden möchten. Die zusätzlichen Tastentipps können je nach gedrückter Taste angezeigt werden. Die ersten Tastentipps werden ausgeblendet, wenn zusätzliche Tastentipps angezeigt werden. Um beispielsweise auf die Registerkarte Einfügen zuzugreifen, drücken Sie Alt, um alle Tipps zu den Primärtasten anzuzeigen.</p> Drücken Sie den Buchstaben I, um auf die Registerkarte Einfügen zuzugreifen, und Sie sehen alle verfügbaren Verknüpfungen für diese Registerkarte. Drücken Sie dann den Buchstaben, der dem zu konfigurierenden Element entspricht. Drücken Sie Alt, um alle Tastentipps auszublenden, oder drücken Sie Escape, um zur vorherigen Gruppe von Tastentipps zurückzukehren. In der folgenden Liste finden Sie die gängigsten Tastenkombinationen: Windows/Linux Mac OS Ein Dokument bearbeiten Dateimenü öffnen ALT+F ^ STRG+⌥ Option+F Über das Dateimenü können Sie das aktuelle Dokument speichern, drucken, herunterladen, Informationen einsehen, ein neues Dokument erstellen oder ein vorhandenes öffnen, auf die Hilfefunktion zugreifen oder die erweiterten Einstellungen öffnen. Dialogbox „Suchen und Ersetzen“ öffnen STRG+F ^ STRG+F, &#8984; Cmd+F Über das Dialogfeld Suchen und Finden können Sie im aktuell bearbeiteten Dokument nach Zeichen/Wörtern/Phrasen suchen. Dialogbox „Suchen und Ersetzen“ mit dem Ersetzungsfeld öffnen STRG+H ^ STRG+H Öffnen Sie das Fenster Suchen und Ersetzen, um ein oder mehrere Ergebnisse der gefundenen Zeichen zu ersetzen. Letzten Suchvorgang wiederholen ⇧ UMSCHALT+F4 ⇧ UMSCHALT+F4, &#8984; Cmd+G, &#8984; Cmd+⇧ UMSCHALT+F4 Wiederholung des Suchvorgangs der vor dem Drücken der Tastenkombination ausgeführt wurde. Kommentarleiste öffnen STRG+⇧ UMSCHALT+H ^ STRG+⇧ UMSCHALT+H, &#8984; Cmd+⇧ UMSCHALT+H Über die Kommentarleiste können Sie Kommentare hinzufügen oder auf bestehende Kommentare antworten. Kommentarfeld öffnen ALT+H &#8984; Cmd+⌥ Option+A Ein Textfeld zum Eingeben eines Kommentars öffnen. Chatleiste öffnen ALT+Q ^ STRG+⌥ Option+Q Chatleiste öffnen, um eine Nachricht zu senden. Dokument speichern STRG+S ^ STRG+S, &#8984; Cmd+S Speichert alle Änderungen im aktuellen Dokument. Die aktive Datei wird mit dem aktuellen Dateinamen, Speicherort und Dateiformat gespeichert. Dokument drucken STRG+P ^ STRG+P, &#8984; Cmd+P Ausdrucken mit einem verfügbaren Drucker oder speichern als Datei. Herunterladen als... STRG+⇧ UMSCHALT+S ^ STRG+⇧ UMSCHALT+S, &#8984; Cmd+⇧ UMSCHALT+S Öffnen Sie das Menü Herunterladen als, um das aktuell bearbeitete Dokument in einem der unterstützten Dateiformate auf der Festplatte speichern: DOCX, PDF, ODT, TXT, DOTX, PDF/A, OTT, RTF, HTML, DOCXF, OFORM, HTML, FB2, EPUB. Vollbild F11 Dokumenteneditor wird an Ihren Bildschirm angepasst und im Vollbildmodus ausgeführt. Hilfemenü F1 F1 Das Hilfe-Menü wird geöffnet. Vorhandene Datei öffnen (Desktop-Editoren) STRG+O Auf der Registerkarte Lokale Datei öffnen unter Desktop-Editoren wird das Standarddialogfeld geöffnet, in dem Sie eine vorhandene Datei auswählen können. Datei schließen (Desktop-Editoren) STRG+W, STRG+F4 ^ STRG+W, &#8984; Cmd+W Das aktuelle Fenster in Desktop-Editoren schließen. Element-Kontextmenü ⇧ UMSCHALT+F10 ⇧ UMSCHALT+F10 Öffnen des ausgewählten Element-Kontextmenüs. Den Parameter „Zoom“ zurücksetzen STRG+0 ^ STRG+0 oderr &#8984; Cmd+0 Setzen Sie den „Zoom“-Parameter des aktuellen Dokuments auf einen Standardwert von 100% zurück. Navigation Zum Anfang einer Zeile springen POS1 POS1 Der Cursor wird an den Anfang der aktuellen Zeile verschoben. Zum Anfang eines Dokuments springen STRG+POS1 ^ STRG+POS1 Der Cursor wird an den Anfang des aktuellen Dokuments verschoben. Zum Ende der Zeile springen ENDE ENDE Der Cursor wird an das Ende der aktuellen Zeile verschoben. Zum Ende des Dokuments springen STRG+ENDE ^ STRG+ENDE Der Cursor wird an das Ende der Dokuments verschoben. Zum Anfang der vorherigen Seite springen ALT+STRG+BILD auf Der Cursor wird an den Anfang der Seite verschoben, die der aktuell bearbeiteten Seite vorausgeht. Zum Anfang der nächsten Seite springen ALT+STRG+BILD ab ⌥ Option+&#8984; Cmd+⇧ UMSCHALT+BILD ab Der Cursor wird an den Anfang der Seite verschoben, die auf die aktuell bearbeitete Seite folgt. Nach unten scrollen BILD ab BILD ab, ⌥ Option+Fn+↑ Wechsel zum Ende der Seite. Nach oben scrollen BILD auf BILD auf, ⌥ Option+Fn+↓ Wechsel zum Anfang der Seite. Nächste Seite ALT+BILD ab ⌥ Option+BILD ab Geht zur nächsten Seite im aktuellen Dokument über. Vorherige Seite ALT+BILD auf ⌥ Option+BILD auf Geht zur vorherigen Seite im aktuellen Dokument über. Vergrößern STRG++ ^ STRG+=, &#8984; Cmd+= Die Ansicht des aktuellen Dokuments wird vergrößert. Verkleinern STRG+- ^ STRG+-, &#8984; Cmd+- Die Ansicht des aktuellen Dokuments wird verkleinert. Ein Zeichen nach links bewegen ← ← Der Mauszeiger bewegt sich ein Zeichen nach links. Ein Zeichen nach rechts bewegen → → Der Mauszeiger bewegt sich ein Zeichen nach rechts. Zum Anfang eines Wortes oder ein Wort nach links bewegen STRG+← ^ STRG+←, &#8984; Cmd+← Der Mauszeiger wird zum Anfang eines Wortes oder ein Wort nach links verschoben. Ein Wort nach rechts bewegen STRG+→ ^ STRG+→, &#8984; Cmd+→ Der Mauszeiger bewegt sich ein Wort nach rechts. Eine Reihe nach oben ↑ ↑ Der Mauszeiger wird eine Reihe nach oben verschoben. Eine Reihe nach unten ↓ ↓ Der Mauszeiger wird eine Reihe nach unten verschoben. Zwischen Steuerelementen in modalen Dialogen navigieren ↹ Tab/⇧ UMSCHALT+↹ Tab ↹ Tab/⇧ UMSCHALT+↹ Tab Navigieren Sie zwischen Steuerelementen, um den Fokus auf das nächste oder vorherige Steuerelement in modalen Dialogen zu legen. Schreiben Absatz beenden ↵ Eingabetaste ↵ Zurück Endet den aktuellen und beginnt einen neuen Absatz. Zeilenumbruch einfügen ⇧ UMSCHALT+↵ Eingabetaste ⇧ UMSCHALT+↵ Zurück Fügt einen Zeilenumbruch ein, ohne einen neuen Absatz zu beginnen. ENTF ← Rücktaste, ENTF ← Rücktaste, ENTF Ein Zeichen nach links löschen (← Rücktaste) oder nach rechts (ENTF) vom Mauszeiger. Das Wort links neben dem Mauszeiger löschen STRG+← Rücktaste ^ STRG+← Rücktaste, &#8984; Cmd+← Rücktaste Das Wort links neben dem Mauszeiger wird gelöscht. Das Wort rechts neben dem Mauszeiger löschen STRG+ENTF ^ STRG+ENTF, &#8984; Cmd+ENTF Das Wort rechts neben dem Mauszeiger wird gelöscht. Geschütztes Leerzeichen erstellen STRG+⇧ UMSCHALT+␣ Leertaste ^ STRG+⇧ UMSCHALT+␣ Leertaste, &#8984; Cmd+⇧ UMSCHALT+␣ Leertaste Erstellt ein Leerzeichen zwischen den Zeichen, das nicht zum Anfang einer neuen Zeile führt. Geschützten Bindestrich erstellen STRG+⇧ UMSCHALT+Viertelgeviertstrich ^ STRG+⇧ UMSCHALT+Viertelgeviertstrich, &#8984; Cmd+⇧ UMSCHALT+Viertelgeviertstrich Erstellt einen Bindestrich zwischen den Zeichen, der nicht zum Anfang einer neuen Zeile führt. Rückgängig machen und Wiederholen Rückgängig machen STRG+Z ^ STRG+Z, &#8984; Cmd+Z Die zuletzt durchgeführte Aktion wird rückgängig gemacht. Wiederholen STRG+J ^ STRG+J, &#8984; Cmd+J, &#8984; Cmd+⇧ UMSCHALT+Z Die zuletzt durchgeführte Aktion wird wiederholt. Ausschneiden, Kopieren, Einfügen Ausschneiden STRG+X, ⇧ UMSCHALT+ENTF &#8984; Cmd+X, ⇧ UMSCHALT+ENTF Der gewählte Textabschnitt wird gelöscht und in der Zwischenablage des Rechners abgelegt. Der kopierte Text kann später an einer anderen Stelle in demselben Dokument, in einem anderen Dokument oder einem anderen Programm eingefügt werden. Kopieren STRG+C, STRG+EINFG &#8984; Cmd+C Der gewählte Textabschnitt wird in der Zwischenablage des Rechners abgelegt. Der kopierte Text kann später an einer anderen Stelle in demselben Dokument, in einem anderen Dokument oder einem anderen Programm eingefügt werden. Einfügen STRG+V, ⇧ UMSCHALT+EINFG &#8984; Cmd+V Der vorher kopierte Textabschnitt wird aus der Zwischenablage des Rechners an der aktuellen Cursorposition eingefügt. Der Text kann aus demselben Dokument, aus einem anderen Dokument bzw. Programm kopiert werden. Hyperlink einfügen STRG+K &#8984; Cmd+K Fügt einen Hyperlink ein, der einen Übergang beispielsweise zu einer Webadresse ermöglicht. Format übertragen STRG+⇧ UMSCHALT+C &#8984; Cmd+⇧ UMSCHALT+C Die Formatierung des gewählten Textabschnitts wird kopiert. Die kopierte Formatierung kann später auf einen anderen Textabschnitt in demselben Dokument angewandt werden. Format anwenden STRG+⇧ UMSCHALT+V &#8984; Cmd+⇧ UMSCHALT+V Wendet die vorher kopierte Formatierung auf den Text im aktuellen Dokument an. Textauswahl Alles auswählen STRG+A &#8984; Cmd+A Der gesamte Text wird ausgewählt, einschließlich Tabellen und Bildern. Fragment wählen ⇧ UMSCHALT+→ ← ⇧ UMSCHALT+→ ← Den Text Zeichen für Zeichen auswählen. Von der aktuellen Cursorposition bis zum Zeilenanfang auswählen ⇧ UMSCHALT+POS1 ⇧ UMSCHALT+POS1 Einen Textabschnitt von der aktuellen Cursorposition bis zum Anfang der aktuellen Zeile auswählen. Von der Cursorposition bis zum Zeilenende auswählen ⇧ UMSCHALT+ENDE ⇧ UMSCHALT+ENDE Einen Textabschnitt von der aktuellen Cursorposition bis zum Ende der aktuellen Zeile auswählen. Ein Zeichen nach rechts auswählen ⇧ UMSCHALT+→ ⇧ UMSCHALT+→ Das Zeichen rechts neben dem Mauszeiger wird ausgewählt. Ein Zeichen nach links auswählen ⇧ UMSCHALT+← ⇧ UMSCHALT+← Das Zeichen links neben dem Mauszeiger wird ausgewählt. Bis zum Wortende auswählen STRG+⇧ UMSCHALT+→ Einen Textfragment vom Cursor bis zum Ende eines Wortes wird ausgewählt. Bis zum Wortanfang auswählen STRG+⇧ UMSCHALT+← Einen Textfragment vom Cursor bis zum Anfang eines Wortes wird ausgewählt. Eine Reihe nach oben auswählen ⇧ UMSCHALT+↑ ⇧ UMSCHALT+↑ Eine Reihe nach oben auswählen (mit dem Cursor am Zeilenanfang). Eine Reihe nach unten auswählen ⇧ UMSCHALT+↓ ⇧ UMSCHALT+↓ Eine Reihe nach unten auswählen (mit dem Cursor am Zeilenanfang). Eine Seite nach oben auswählen ⇧ UMSCHALT+BILD auf ⇧ UMSCHALT+BILD auf Die Seite wird von der aktuellen Position des Mauszeigers bis zum oberen Teil des Bildschirms ausgewählt. Eine Seite nach unten auswählen ⇧ UMSCHALT+BILD ab ⇧ UMSCHALT+BILD ab Die Seite wird von der aktuellen Position des Mauszeigers bis zum unteren Teil des Bildschirms ausgewählt. Textformatierung Fett STRG+B ^ STRG+B, &#8984; Cmd+B Zuweisung der Formatierung Fett im gewählten Textabschnitt. Kursiv STRG+I ^ STRG+I, &#8984; Cmd+I Zuweisung der Formatierung Kursiv im gewählten Textabschnitt. Unterstrichen STRG+U ^ STRG+U, &#8984; Cmd+U Der gewählten Textabschnitt wird mit einer Linie unterstrichen. Durchgestrichen STRG+5 ^ STRG+5, &#8984; Cmd+5 Der gewählte Textabschnitt wird durchgestrichen. Tiefgestellt STRG+. ^ STRG+⇧ UMSCHALT+&gt;, &#8984; Cmd+⇧ UMSCHALT+&gt; Der gewählte Textabschnitt wird verkleinert und tiefgestellt. Hochgestellt STRG+, ^ STRG+⇧ UMSCHALT+&lt;, &#8984; Cmd+⇧ UMSCHALT+&lt; Der gewählte Textabschnitt wird verkleinert und hochgestellt wie z. B. in Bruchzahlen. Überschrift 1 ALT+1 ⌥ Option+^ STRG+1 Dem gewählten Textabschnitt wird die Überschriftenformatvorlage Überschrift 1 zugwiesen. Überschrift 2 ALT+2 ⌥ Option+^ STRG+2 Dem gewählten Textabschnitt wird die Überschriftenformatvorlage Überschrift 2 zugwiesen. Überschrift 3 ALT+3 ⌥ Option+^ STRG+3 Dem gewählten Textabschnitt wird die Überschriftenformatvorlage Überschrift 3 zugwiesen. Aufzählungsliste STRG+⇧ UMSCHALT+L ^ STRG+⇧ UMSCHALT+L, &#8984; Cmd+⇧ UMSCHALT+L Baiserend auf dem gewählten Textabschnitt wird eine Aufzählungsliste erstellt oder eine neue Liste begonnen. Formatierung entfernen STRG+␣ Leertaste Entfernt die Formatierung im gewählten Textabschnitt. Schrift vergrößern STRG+] &#8984; Cmd+] Vergrößert die Schrift des gewählten Textabschnitts um 1 Punkt. Schrift verkleinern STRG+[ &#8984; Cmd+[ Verkleinert die Schrift des gewählten Textabschnitts um 1 Punkt. Zentriert/linksbündig ausrichten STRG+E ^ STRG+E, &#8984; Cmd+E Wechselt die Ausrichtung des Absatzes von zentriert auf linksbündig. Blocksatz/linksbündig ausrichten STRG+J, STRG+L ^ STRG+J, &#8984; Cmd+J Wechselt die Ausrichtung des Absatzes von Blocksatz auf linksbündig. Rechtsbündig/linksbündig ausrichten STRG+R ^ STRG+R, &#8984; Cmd+R Wechselt die Ausrichtung des Absatzes von rechtsbündig auf linksbündig. Text tiefstellen (automatischer Abstand) STRG+= Das ausgewählte Textfragment wird tiefgestellt. Text hochstellen (automatischer Abstand) STRG+⇧ UMSCHALT++ Das ausgewählte Textfragment wird hochgestellt. Seitenumbruch einfügen STRG+↵ Eingabetaste ^ STRG+↵ Zurück, &#8984; Cmd+↵ Zurück Einfügen eines Seitenumbruchs an der aktuellen Cursorposition. Einzug vergrößern STRG+M ^ STRG+M, &#8984; Cmd+M Vergrößert den linken Einzug des Absatzes schrittweise. Einzug verkleinern STRG+⇧ UMSCHALT+M ^ STRG+⇧ UMSCHALT+M, &#8984; Cmd+⇧ UMSCHALT+M Verkleinert den linken Einzug des Absatzes schrittweise. Seitenzahl einfügen STRG+⇧ UMSCHALT+P ^ STRG+⇧ UMSCHALT+P, &#8984; Cmd+⇧ UMSCHALT+P Die aktuelle Seitennummer wird an der aktuellen Cursorposition hinzugefügt. Formatierungszeichen STRG+⇧ UMSCHALT+Num8 Ein- oder Ausblenden von nicht druckbaren Zeichen. Ein Zeichen nach links löschen ← Rücktaste ← Rücktaste Das Zeichen links neben dem Mauszeiger wird gelöscht. Ein Zeichen nach rechts löschen ENTF ENTF Das Zeichen rechts neben dem Mauszeiger wird gelöscht. Objekte ändern Verschiebung begrenzen ⇧ UMSCHALT + ziehen ⇧ UMSCHALT + ziehen Die Verschiebung des gewählten Objekts wird horizontal oder vertikal begrenzt. 15-Grad-Drehung einschalten ⇧ UMSCHALT + ziehen (beim Drehen) ⇧ UMSCHALT + ziehen (beim Drehen) Begrenzt den Drehungswinkel auf 15-Grad-Stufen. Seitenverhältnis sperren ⇧ UMSCHALT + ziehen (beim Ändern der Größe) ⇧ UMSCHALT + ziehen (beim Ändern der Größe) Das Seitenverhältnis des gewählten Objekts wird bei der Größenänderung beibehalten. Gerade Linie oder Pfeil zeichnen ⇧ UMSCHALT + ziehen (beim Ziehen von Linien/Pfeilen) ⇧ UMSCHALT + ziehen (beim Ziehen von Linien/Pfeilen) Zeichnen einer geraden vertikalen/horizontalen/45-Grad Linie oder eines solchen Pfeils. Bewegung in 1-Pixel-Stufen STRG+← → ↑ ↓ Halten Sie die Taste STRG gedrückt und nutzen Sie die Pfeile auf der Tastatur, um das gewählte Objekt jeweils um ein Pixel zu verschieben. Tabellen bearbeiten Zur nächsten Zelle in einer Zeile übergeghen ↹ Tab ↹ Tab Zur nächsten Zelle in einer Zeile wechseln. Zur nächsten Zelle in einer Tabellenzeile wechseln. ⇧ UMSCHALT+↹ Tab ⇧ UMSCHALT+↹ Tab Zur vorherigen Zelle in einer Zeile wechseln. Zur nächsten Zeile wechseln ↓ ↓ Zur nächsten Zeile in einer Tabelle wechseln. Zur vorherigen Zeile wechseln ↑ ↑ Zur vorherigen Zeile in einer Tabelle wechseln. Neuen Abstz beginnen ↵ Eingabetaste ↵ Zurück Einen neuen Absatz in einer Zelle beginnen. Neue Zeile einfügen ↹ Tab in der unteren rechten Tabellenzelle. ↹ Tab in der unteren rechten Tabellenzelle. Eine neue Zeile am Ende der Tabelle einfügen. Tabellenumbruch einfügen Strg+⇧ UMSCHALT+↵ Eingabetaste ^ Strg+⇧ UMSCHALT+↵ Zurück, &#8984; Cmd+⇧ UMSCHALT+↵ Zurück Einen Tabellenumbruch innerhalb der Tabelle einfügen. Sonderzeichen einfügen Gleichung einfügen ALT+= ⌥ Option+^ STRG+= Einfügen eine Gleichung an der aktuellen Cursorposition. Einen Gedankenstrich einfügen ALT+STRG+Num- ⌥ Option+⇧ Shift+- Fügen Sie innerhalb des aktuellen Dokuments und rechts vom Cursor einen Gedankenstrich „—“ ein. Einen geschützten Bindestrich einfügen STRG+⇧ UMSCHALT+_ ^ STRG+⇧ UMSCHALT+Bindestrich, &#8984; Cmd+⇧ UMSCHALT+Bindestrich Fügen Sie innerhalb des aktuellen Dokuments und rechts vom Cursor einen geschützten Bindestrich „-“ ein. Ein geschütztes Leerzeichen einfügen STRG+⇧ UMSCHALT+␣ Leerzeichen ⌥ Option+␣ Leerzeichen Fügen Sie innerhalb des aktuellen Dokuments und rechts vom Cursor ein geschütztes Leerzeichen „o“ ein."
    },
   {
        "id": "HelpfulHints/Navigation.htm", 
        "title": "Ansichtseinstellungen und Navigationswerkzeuge", 
        "body": "Der Dokumenteneditor bietet mehrere Tools, die Ihnen beim Anzeigen und Navigieren durch Ihr Dokument helfen: Zoom, Seitenzahlanzeige usw. Ansichtseinstellungen anpassen Um die Standardansichtseinstellungen anzupassen und den bequemsten Modus für die Arbeit mit dem Dokument festzulegen, gehen Sie zur Registerkarte Ansicht und wählen Sie aus, welche Elemente der Benutzeroberfläche ausgeblendet oder angezeigt werden sollen. Auf der Registerkarte Ansicht können Sie die folgenden Optionen auswählen: Überschriften, um die Dokumentkopfzeilen im linken Bereich anzuzeigen. Zoom, um den erforderlichen Zoomwert von 50 % bis 500 % aus der Drop-Down-Liste einzustellen. Seite anpassen, um die gesamte Dokumentseite an den sichtbaren Teil des Arbeitsbereichs anzupassen. An Breite anpassen, um die Seitenbreite des Dokuments an den sichtbaren Teil des Arbeitsbereichs anzupassen. Thema der Benutzeroberfläche – wählen Sie eines der verfügbaren Oberflächenthemen aus dem Dropdown-Menü: Wie im System, Hell, Klassisch Hell, Dunkel, Dunkler Kontrast. Wenn das Thema Dunkel oder Dunkler Kontrast aktiviert ist, wird der Dunkles Dokument-Umschalter aktiv; Verwenden Sie ihn, um den Arbeitsbereich auf Weiß oder Dunkelgrau einzustellen. Symbolleiste immer anzeigen – wenn diese Option deaktiviert ist, wird die obere Symbolleiste, die Befehle enthält, ausgeblendet, während die Registerkartennamen sichtbar bleiben. Alternativ können Sie einfach auf eine beliebige Registerkarte doppelklicken, um die obere Symbolleiste auszublenden oder wieder anzuzeigen. Statusleiste – wenn diese Option deaktiviert ist, wird die unterste Leiste, in der sich die Schaltflächen Seitenzahlanzeige und Zoom befinden, ausgeblendet. Aktivieren Sie diese Option, um die ausgeblendete Statusleiste anzuzeigen. Lineale - wenn diese Option deaktiviert ist, werden die Lineale ausgeblendet, die zum Ausrichten von Text, Grafiken, Tabellen und anderen Elementen in einem Dokument, zum Einrichten von Rändern, Tabstopps und Absatzeinzügen verwendet werden. Um die ausgeblendeten Lineale anzuzeigen, aktivieren Sie diese Option erneut. Linkes Bedienfeld - wenn diese Option deaktiviert ist, wird der linke Bereich ausgeblendet, in dem sich die Schaltflächen Suchen, Kommentare usw. befinden. Aktivieren Sie dieses Kontrollkästchen, um das linke Bedienfeld anzuzeigen. Rechtes Bedienungsfeld - wenn diese Option deaktiviert ist, wird das rechte Bedienfeld ausgeblendet, in dem sich Einstellungen befinden. Aktivieren Sie dieses Kontrollkästchen, um das rechte Bedienfeld anzuzeigen. Die rechte Seitenleiste ist standartmäßig verkleinert. Um sie zu erweitern, wählen Sie ein beliebiges Objekt (z. B. Bild, Diagramm, Form) oder eine Textpassage aus und klicken Sie auf das Symbol des aktuell aktivierten Tabs auf der rechten Seite. Um die Seitenleiste wieder zu minimieren, klicken Sie erneut auf das Symbol. Wenn die Felder Kommentare oder Chat geöffnet sind, wird die Breite der linken Seitenleiste durch einfaches Ziehen und Loslassen angepasst: Bewegen Sie den Mauszeiger über den Rand der linken Seitenleiste, so dass dieser sich in den bidirektionalen Pfeil verwandelt und ziehen Sie den Rand nach rechts, um die Seitenleiste zu erweitern. Um die ursprüngliche Breite wiederherzustellen, ziehen Sie den Rand nach links. Verwendung der Navigationswerkzeuge Mithilfe der folgenden Werkzeuge können Sie durch Ihr Dokument navigieren: Die Zoom-Funktion befindet sich in der rechten unteren Ecke und dient zum Vergrößern und Verkleinern des aktuellen Dokuments. Um den in Prozent angezeigten aktuellen Zoomwert zu ändern, klicken Sie darauf und wählen Sie eine der verfügbaren Zoomoptionen (50% / 75% / 100% / 125% / 150% / 175% / 200% / 300% / 400% / 500%) aus der Liste oder klicken Sie auf Vergrößern oder Verkleinern . Klicken Sie auf das Symbol Breite anpassen , um die ganze Seite im Fenster anzuzeigen. Um das ganze Dokument an den sichtbaren Teil des Arbeitsbereichs anzupassen, klicken Sie auf das Symbol Seite anpassen . Zoomeinstellungen sind auch auf der Registerkarte Ansicht verfügbar. Die Seitenzahlanzeige stellt die aktuelle Seite als Teil aller Seiten im aktuellen Dokument dar (Seite „n“ von „nn“). Klicken Sie auf die Seitenzahlanzeige, um ein Fenster zu öffnen, anschließend können Sie eine Seitenzahl eingeben und direkt zu dieser Seite wechseln. Die Wortzahlanzeige zeigt die Wortzahlstatistik des aktuellen Dokuments."
    },
   {
        "id": "HelpfulHints/Password.htm", 
        "title": "Dokumente mit einem Kennwort schützen", 
        "body": "Sie können Ihre Dokumente mit einem Kennwort schützen, das Ihre Mitautoren benötigen, um zum Bearbeitungsmodus zu wechseln. Das Kennwort kann später geändert oder entfernt werden. Wenn Sie das Kennwort verlieren oder vergessen, lässt es sich nicht mehr wiederherstellen. Bewahren Sie es an einem sicheren Ort auf. Sie können Ihre Dokumente auch schützen, indem Sie die Zugriffsrechte einschränken. Kennwort erstellen gehen Sie zur Registerkarte Datei in der oberen Symbolleiste und wählen Sie die Option Schützen oder gehen Sie zur Registerkarte Schutz und wählen Sie die Option Verschlüsseln, geben Sie das Kennwort im Feld Kennwort ein und wiederholen Sie es im Feld Kennwort wiederholen nach unten. Klicken Sie auf das Symbol , um die Kennwortzeichen bei der Eingabe anzuzeigen oder auszublenden, Klicken Sie auf OK, wenn Sie bereits sind. Um das Dokument mit den vollen Zugriffsrechten zu öffnen, muss der Benutzer das festgelegte Passwort eingeben. Kennwort ändern gehen Sie zur Registerkarte Datei in der oberen Symbolleiste und wählen Sie die Option Schützen oder gehen Sie zur Registerkarte Schutz und wählen Sie die Option Verschlüsseln, legen Sie ein neues Passwort im Feld Passwort fest und wiederholen Sie es im Feld Passwort wiederholen darunter. Klicken Sie auf das Symbol , um die Zeichen des Passworts bei der Eingabe anzuzeigen oder auszublenden, klicken Sie auf OK. Kennwort löschen öffnen Sie die Registerkarte Datei in der oberen Symbolleiste, wählen Sie die Option Schützen aus, klicken Sie auf die Schaltfläche Kennwort löschen. Dokument schützen gehen Sie zur Registerkarte Schutz, klicken Sie auf die Schaltfläche Datei schützen, setzen Sie das Passwort, falls erforderlich, wählen Sie die erforderlichen Zugriffsrechte für das Dokument, sofern das Passwort nicht vom Benutzer eingegeben wurde: Keine Änderungen (Schreibgeschützt) - der Benutzer kann das Dokument nur anzeigen. Ausfüllen von Formularen - der Benutzer kann nur ein Formular ausfüllen. Überarbeitungen - der Benutzer kann nur den Änderungsverlauf des Dokuments anzeigen und das Dokument selbst überarbeiten. Kommentare - der Benutzer kann nur Kommentare hinterlassen und beantworten. klicken Sie auf Schützen, wenn Sie bereits sind. Um den Schutz aufzuheben vom Dokument, sofern das Passwort festgelegt wurde: gehen Sie zur Registerkarte Schutz, klicken Sie auf die Schaltfläche Datei schützen, geben Sie das festgelegte Passwort im Fenster Dokument entschützen ein."
    },
   {
        "id": "HelpfulHints/Review.htm", 
        "title": "Änderungen nachverfolgen", 
        "body": "Der Dokumenteneditor ermöglicht es Ihnen, einen konstanten teamweiten Ansatz für den Arbeitsablauf beizubehalten: Sie können die Dateien und Ordner freigeben; an Dokumenten in Echtzeit zusammenarbeiten; direkt im Editor kommunizieren; bestimmte Teile Ihrer Dokumente, die zusätzliche Eingaben Dritter erfordern, kommentieren; Dokumentversionen für zukünftige Verwendung speichern; Dokumente vergleichen und zusammenführen, um die Verarbeitung und Bearbeitung zu erleichtern. Wenn jemand eine Datei mit den Berechtigungen \"Review\" für Sie freigibt, müssen Sie die Dokumentfunktion Review anwenden. Im Dokumenteneditor als Überprüfer können Sie die Review-Option verwenden, um das Dokument zu überprüfen, die Sätze, Phrasen und andere Seitenelemente zu ändern, die Rechtschreibung zu korrigieren usw., ohne es tatsächlich zu bearbeiten. Alle Ihre Änderungen werden aufgezeichnet und der Person angezeigt, die Ihnen das Dokument gesendet hat. Wenn Sie die Datei zur Überprüfung senden, müssen Sie alle daran vorgenommenen Änderungen anzeigen und sie entweder annehmen oder ablehnen. Die Funktion \"Änderungen nachverfolgen\" aktivieren Um Änderungen anzuzeigen, die von einem Rezensenten vorgeschlagen wurden, aktivieren Sie die Option Änderungen nachverfolgen: Klicken Sie in der rechten unteren Statusleiste auf das Symbol , oder Wechseln Sie in der oberen Symbolleiste zur Registerkarte Zusammenarbeit und klicken Sie auf Änderungen nachverfolgen. Der Überprüfer muss die Option Änderungen nachverfolgen nicht aktivieren. Sie ist standardmäßig aktiviert und kann nicht deaktiviert werden, wenn das Dokument nur mit Zugriffsrechten zum Review freigegeben wird. Im geöffneten Pop-Up-Menü stehen folgende Optionen zur Verfügung: AKTIVIERT für mich: Das Verfolgen von Änderungen ist nur für den aktuellen Benutzer aktiviert. Die Option bleibt für die aktuelle Bearbeitungssitzung aktiviert, d. h. die Option wird deaktiviert, wenn Sie das Dokument neu laden oder erneut öffnen. Es wird nicht von anderen Benutzern beeinflusst, die die Option für allgemeine Nachverfolgung von Änderungen aktivieren oder deaktivieren. DEAKTIVIERT für mich: Das Verfolgen von Änderungen ist nur für den aktuellen Benutzer deaktiviert. Die Option bleibt für die aktuelle Bearbeitungssitzung deaktiviert. Es wird nicht von anderen Benutzern beeinflusst, die die Option für allgemeine Nachverfolgung von Änderungen aktivieren oder deaktivieren. AKTIVIERT für alle: Die Nachverfolgung von Änderungen ist aktiviert und bleibt beim erneuten Laden oder erneuten Öffnen des Dokuments beibehalten (beim erneuten Laden des Dokuments wird die Nachverfolgung für alle Benutzer aktiviert). Wenn ein anderer Benutzer die Option für allgemeine Nachverfolgung von Änderungen in der Datei deaktiviert, wird der Status auf DEAKTIVIERT für alle geändert. DEAKTIVIERT für alle - Die Nachverfolgung von Änderungen ist deaktiviert und bleibt beibehalten, wenn Sie das Dokument neu laden oder erneut öffnen (wenn das Dokument neu geladen wird, ist die Nachverfolgung für alle Benutzer deaktiviert). Wenn ein anderer Benutzer die Option für allgemeine Nachverfolgung von Änderungen in der Datei aktiviert, wird der Status auf AKTIVIERT für alle geändert. Die entsprechende Warnmeldung wird jedem Mitautor angezeigt. Änderungen anzeigen Von einem Benutzer vorgenommene Änderungen werden im Dokumenttext mit einer bestimmten Farbe hervorgehoben. Wenn Sie auf den geänderten Text klicken, öffnet sich eine Sprechblase, die den Benutzernamen, Datum und Uhrzeit der Änderung sowie die Änderungsbeschreibung anzeigt. Die Sprechblase enthält auch Symbole zum Akzeptieren oder Ablehnen der aktuellen Änderung. Wenn Sie einen Textabschnitt per Drag & Drop an eine andere Stelle im Dokument ziehen, wird der Text an einer neuen Position mit der doppelten Linie unterstrichen. Der Text an der ursprünglichen Position wird doppelt gekreuzt. Dies wird als eine einzige Änderung gezählt. Klicken Sie auf den doppelt durchgestrichenen Text an der ursprünglichen Position und verwenden Sie den Pfeil in der Änderungssprechblase, um zur neuen Position des Textes zu wechseln. Klicken Sie an der neuen Position auf den doppelt unterstrichenen Text und verwenden Sie den Pfeil in der Änderungssprechblase, um zur ursprünglichen Position des Textes zu wechseln. Anzeigemodus für Änderungen auswählen Klicken Sie in der oberen Symbolleiste auf das Symbol Anzeigemodus und wählen Sie einen der verfügbaren Modi aus der Liste aus: Markup: Diese Option ist standardmäßig ausgewählt. Dieser Modus ermöglicht es, Änderungsvorschläge anzuzeigen und das Dokument zu bearbeiten. Einfaches Markup: Dieser Modus ermöglicht sowohl das Anzeigen der vorgeschlagenen Änderungen als auch das Bearbeiten des Dokuments. Änderungen werden nur im Dokumenttext angezeigt, Sprechblasen werden ausgeblendet. Endgültig: In diesem Modus werden alle Änderungen so angezeigt, als wären sie angenommen worden. Die Änderungen werden nicht wirklich akzeptiert, die Funktion ermöglicht Ihnen lediglich eine Vorschau, falls alle Änderungen angenommen werden. In diesem Modus kann das Dokument nicht bearbeitet werden. Original: In diesem Modus werden alle Änderungen so angezeigt, als wären sie abgelehnt worden. Die Änderungen werden nicht wirklich abgelehnt, die Funktion ermöglicht Ihnen lediglich eine Vorschau, falls alle Änderungen abgelehnt werden. In diesem Modus kann das Dokument nicht bearbeitet werden. Änderungen annehmen oder ablehnen Über die Schaltflächen Vorherige und Nächste in der oberen Symbolleiste können Sie zwischen den Änderungen navigieren. Um die aktuell ausgewählte Änderung anzunehmen: Klicken Sie auf die Schaltfläche Annehmen in der oberen Symbolleiste, oder Klicken Sie auf den Abwärtspfeil unter der Schaltfläche Annehmen und wählen Sie die Option Aktuelle Änderung annehmen (in diesem Fall wird die Änderung angenommen und Sie fahren mit der nächsten Änderung fort), oder Klicken Sie auf die Schaltfläche Annehmen der Änderungssprechblase. Um alle Änderungen sofort anzunehmen, klicken Sie auf den Abwärtspfeil unter der Schaltfläche Annehmen und wählen Sie die Option Alle Änderungen annehmen aus. Um die aktuell ausgewählte Änderung abzulehnen: Klicken Sie auf die Schaltfläche Ablehnen in der oberen Symbolleiste, oder Klicken Sie auf den Abwärtspfeil unter der Schaltfläche Ablehnen und wählen Sie die Option Aktuelle Änderung ablehnen aus (in diesem Fall wird die Änderung abgelehnt und Sie fahren mit der nächsten verfügbaren Änderung fort), oder Klicken Sie auf die Schaltfläche Ablehnen der Änderungssprechblase. Um alle Änderungen direkt abzulehnen, klicken Sie auf den Abwärtspfeil unter der Schaltfläche Ablehnen und wählen Sie die Option Alle Änderungen ablehnen. Wenn Sie eine Änderung annehmen oder ablehnen müssen, klicken Sie mit der rechten Maustaste darauf und wählen Sie im Kontextmenü Änderung annehmen oder Änderung ablehnen. Wenn Sie das Dokument überprüfen, stehen Ihnen die Optionen Annehmen und Ablehnen nicht zur Verfügung. Sie können Ihre Änderungen mit dem Symbol innerhalb der Änderungssprechblase löschen."
    },
   {
        "id": "HelpfulHints/Search.htm", 
        "title": "Suchen und Ersetzen", 
        "body": "Um nach den erforderlichen Zeichen, Wörtern oder Ausdrücken zu suchen, die im aktuell bearbeiteten Dokument verwendet werden, klicken Sie auf das Symbol in der linken Seitenleiste des Dokumenteneditors, das Symbol in der oberen rechten Ecke, oder verwenden Sie die Tastenkombination Strg+F (Command+F für MacOS), um das kleine Suchfeld zu öffnen, oder die Tastenkombination Strg+H, um das vollständige Suchfenster zu öffnen. Ein kleiner Suchen-Bereich öffnet sich in der oberen rechten Ecke des Arbeitsbereichs. Um auf die erweiterten Einstellungen zuzugreifen, klicken Sie auf das Symbol oder verwenden Sie die Tastenkombination Strg+H. Das Fenster Suchen und Ersetzen wird geöffnet: Geben Sie Ihre Anfrage in das entsprechende Dateneingabefeld Suchen ein. Wenn Sie ein oder mehrere Vorkommen der gefundenen Zeichen ersetzen müssen, geben Sie den Ersetzungstext in das entsprechende Dateneingabefeld Ersetzen durch ein oder verwenden Sie die Tastenkombination Strg+H. Sie können wählen, ob Sie ein einzelnes derzeit markiertes Vorkommen oder alle Vorkommen ersetzen möchten, indem Sie auf die entsprechenden Schaltflächen Ersetzen und Alle ersetzen klicken. Um zwischen den gefundenen Vorkommen zu navigieren, klicken Sie auf eine der Pfeilschaltflächen. Die Schaltfläche zeigt das nächste Vorkommen an, während die Schaltfläche das vorherige Vorkommen anzeigt. Geben Sie Suchparameter an, indem Sie die erforderlichen Optionen unter den Eingabefeldern aktivieren: Die Option Groß-/Kleinschreibung beachten wird verwendet, um nur die Vorkommen zu finden, die in der gleichen Groß-/Kleinschreibung wie Ihre Anfrage eingegeben wurden (z. B. wenn Ihre Anfrage „Editor“ lautet und diese Option ausgewählt ist, werden Wörter wie „Editor“ oder „EDITOR“ usw. nicht gefunden). Die Option Nur ganze Wörter wird verwendet, um nur ganze Wörter hervorzuheben. Alle Vorkommen werden in der Datei hervorgehoben und als Liste im Bereich Suchen auf der linken Seite angezeigt. Verwenden Sie die Liste, um zum gewünschten Vorkommen zu springen, oder verwenden Sie die Navigationsschaltflächen und . Der Dokumenteneditor unterstützt die Suche nach Sonderzeichen. Um ein Sonderzeichen zu finden, geben Sie es in das Suchfeld ein. Die Liste der Sonderzeichen, die in Suchen verwendet werden können Sonderzeichen Beschreibung ^l Zeilenumbruch ^t Tabulator ^? Ein Symbol ^# Eine Ziffer ^$ Eine Buchstabe ^n Spaltenbruch ^e Endnote ^f Fußnote ^g Grafisches Element ^m Seitenumbruch ^~ Bindestrich ^s Geschütztes Leerzeichen ^^ Zirkumflex entkommen ^w Ein Leerzeichen ^+ Geviertstrich ^= Gedankenstrich ^y Ein Strich Sonderzeichen, die auch zum Ersetzen verwendet werden können: Sonderzeichen Beschreibung ^l Zeilenumbruch ^t Tabulator ^n Spaltenbruch ^m Seitenumbruch ^~ Bindestrich ^s Geschütztes Leerzeichen ^+ Geviertstrich ^= Gedankenstrich"
    },
   {
        "id": "HelpfulHints/SpellChecking.htm", 
        "title": "Rechtschreibprüfung", 
        "body": "Der Dokumenteneditor bietet Ihnen die Möglichkeit, die Rechtschreibung Ihres Textes in einer bestimmten Sprache zu überprüfen und Fehler während der Bearbeitung zu korrigieren. Ab Version 6.3 unterstützen die ONLYOFFICE-Editoren das SharedWorker Interface für einen besseren Betrieb ohne großen Speicherverbrauch. Wenn Ihr Browser SharedWorker nicht unterstützt, ist nur Worker aktiv. Weitere Informationen zu SharedWorker finden Sie in diesem Artikel. Wählen Sie zunächst die Sprache für Ihr Dokument aus. Klicken Sie in der Statusleiste auf das Symbol Rechtschreibprüfung. Wählen Sie nun im angezeigten Fenster die gewünschte Sprache und klicken Sie auf OK. Die ausgewählte Sprache wird auf das gesamte Dokument angewandt. Um für einen Abschnitt im Dokument eine andere Sprache auszuwählen, markieren Sie den entsprechenden Abschnitt mit der Maus und klicken Sie anschließend auf das Menü in der Statusleiste. Rechtschreibprüfung aktivieren: klicken Sie in der Statusleiste auf das Symbol Rechtschreibprüfung oder öffnen Sie in der oberen Symbolleiste die Registerkarte Datei und wählen Sie die Option Erweiterte Einstellungen, setzen Sie das Häkchen in der Box Rechtschreibprüfung aktivieren und klicken Sie auf Übernehmen. Falsch geschriebene Wörter werden mit einer roten Linie unterstrichen. Klicken Sie mit der rechten Maustaste auf das entsprechende Wort, um das Kontextmenü zu aktivieren, und: wählen Sie eine der verfügbaren Varianten aus, um das falsch geschriebene Wort durch die korrekte Rechtschreibung zu ersetzen. Wenn zu viel Möglichkeiten vorliegen, wird die Option Weitere... im Menü angezeigt; wählen Sie die Option Ignorieren, um ein Wort zu überspringen und die rote Linie auszublenden oder Alle ignorieren, um ein bestimmtes Fehlerergebnis für den gesamten Text zu überspringen; wenn das aktuelle Wort im Wörterbuch fehlt, können Sie es dem benutzerdefinierten Wörterbuch hinzufügen. Dieses Wort wird beim nächsten Mal nicht als Fehler behandelt. Diese Option ist in der Desktop-Version verfügbar; wählen Sie für dieses Wort eine andere Sprache. Rechtschreibprüfung deaktivieren: klicken Sie in der Statusleiste auf das Symbol Rechtschreibprüfung oder öffnen Sie in der oberen Symbolleiste die Registerkarte Datei und wählen Sie die Option Erweiterte Einstellungen, entfernen Sie das Häkchen in der Box Rechtschreibprüfung aktivieren und klicken Sie auf Übernehmen."
    },
   {
        "id": "HelpfulHints/SupportedFormats.htm", 
        "title": "Unterstützte Formate von elektronischen Dokumenten", 
        "body": "Elektronische Dokumente stellen die am meisten benutzte Computerdateien dar. Dank des inzwischen hoch entwickelten Computernetzwerks ist es bequemer anstatt von gedruckten Dokumenten elektronische Dokumente zu verbreiten. Aufgrund der Vielfältigkeit der Geräte, die für die Anzeige der Dokumente verwendet werden, gibt es viele proprietäre und offene Dateiformate. Der Dokumenteneditor unterstützt die geläufigsten Formate. Beim Hochladen oder Öffnen der Datei für die Bearbeitung wird sie ins Office-Open-XML-Format (DOCX) konvertiert. Dies wird gemacht, um die Dateibearbeitung zu beschleunigen und die Interfunktionsfähigkeit zu erhöhen. Die folgende Tabelle enthält die Formate, die zum Anzeigen und/oder zur Bearbeitung geöffnet werden können. Formate Beschreibung Nativ anzeigen Anzeigen nach Konvertierung in OOXML Nativ bearbeiten Bearbeitung nach Konvertierung in OOXML DjVu Dateiformat, das hauptsächlich zum Speichern gescannter Dokumente entwickelt wurde, insbesondere solcher, die eine Kombination aus Text, Strichzeichnungen und Fotos enthalten. + DOC Dateierweiterung für Textverarbeitungsdokumente, die mit Microsoft Word erstellt werden. + + DOCM Macro-Enabled Microsoft Word Document Filename extension for Microsoft Word 2007 or higher generated documents with the ability to run macros + + DOCX Office Open XML Gezipptes, XML-basiertes, von Microsoft entwickeltes Dateiformat zur Präsentation von Kalkulationstabellen, Diagrammen, Präsentationen und Textverarbeitungsdokumenten. + + DOCXF Ein Format zum Erstellen, Bearbeiten und Zusammenarbeiten an einer Formularvorlage. + + DOTX Word Open XML Dokumenten-Vorlage Gezipptes, XML-basiertes, von Microsoft für Dokumentenvorlagen entwickeltes Dateiformat. Eine DOTX-Vorlage enthält Formatierungseinstellungen, Stile usw. und kann zum Erstellen mehrerer Dokumente mit derselben Formatierung verwendet werden. + + EPUB Electronic Publication Offener Standard für E-Books vom International Digital Publishing Forum. + + FB2 Eine E-Book-Dateierweiterung, mit der Sie Bücher auf Ihrem Computer oder Mobilgerät lesen können. + + HTML HyperText Markup Language Hauptauszeichnungssprache für Webseiten. + + ODT Textverarbeitungsformat von OpenDocument, ein offener Standard für elektronische Dokumente. + + OFORM Ein Format zum Ausfüllen eines Formulars. Formularfelder sind ausfüllbar, aber Benutzer können die Formatierung oder Parameter der Formularelemente nicht ändern*. + + OTT OpenDocument-Dokumentenvorlage OpenDocument-Dateiformat für Dokumentenvorlagen. Eine OTT-Vorlage enthält Formatierungseinstellungen, Stile usw. und kann zum Erstellen mehrerer Dokumente mit derselben Formatierung verwendet werden. + + PDF Portable Document Format Dateiformat, mit dem Dokumente unabhängig vom ursprünglichen Anwendungsprogramm, Betriebssystem und der Hardware originalgetreu wiedergegeben werden können. + PDF/A Portable Document Format / A Eine ISO-standardisierte Version des Portable Document Format (PDF), die auf die Archivierung und Langzeitbewahrung elektronischer Dokumente spezialisiert ist. + RTF Rich Text Format Plattformunabhängiges Datei- und Datenaustauschformat von Microsoft für formatierte Texte. + + TXT Dateierweiterung reiner Textdateien mit wenig Formatierung. + + XML Extensible Markup Language (XML). Eine einfache und flexible Auszeichnungssprache, die von SGML (ISO 8879) abgeleitet ist und zum Speichern und Transportieren von Daten dient. + XPS Open XML Paper Specification Offenes, lizenzfreies Dokumentenformat von Microsoft mit festem Layout. + *Hinweis: Das OFORM-Format ist ein Format zum Ausfüllen eines Formulars. Daher sind die Formularfelder nur bearbeitbar. Die folgende Tabelle enthält die Formate, in denen Sie ein Dokument über das Menü Datei -> Herunterladen als herunterladen können. Eingabeformat Kann heruntergeladen werden als DjVu DjVu, PDF DOC DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT DOCM DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT DOCX DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT DOCXF DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT DOTX DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT EPUB DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT FB2 DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT HTML DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT ODT DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT OFORM DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT OTT DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT PDF DOCX, DOCXF, DOTX, EPUB, FB2, HTML, OFORM, PDF, RTF, TXT PDF/A DOCX, DOCXF, DOTX, EPUB, FB2, HTML, OFORM, PDF, RTF, TXT RTF DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT TXT DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT XML DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT XPS DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT, XPS Sie können sich auch auf die Conversion-Matrix auf api.onlyoffice.com beziehen, um die Möglichkeiten zu sehen, Ihre Dokumente in die bekanntesten Dateiformate zu konvertieren."
    },
   {
        "id": "HelpfulHints/VersionHistory.htm", 
        "title": "Versionshistorie", 
        "body": "Der Dokumenteneditor ermöglicht es Ihnen, einen konstanten teamweiten Ansatz für den Arbeitsablauf beizubehalten: Sie können die Dateien und Ordner freigeben; an Dokumenten in Echtzeit zusammenarbeiten; direkt im Editor kommunizieren; bestimmte Teile Ihrer Dokumente, die zusätzliche Eingaben Dritter erfordern, kommentieren; Dokumente überprüfen und Ihre Änderungen hinzufügen, ohne die Datei tatsächlich zu bearbeiten; Dokumente vergleichen und zusammenführen, um die Verarbeitung und Bearbeitung zu erleichtern. Im Dokumenteneditor können Sie die Versionshistorie des Dokuments anzeigen, an dem Sie mitarbeiten. Versionshistorie anzeigen: Um alle am Dokument vorgenommenen Änderungen anzuzeigen: Öffnen Sie die Registerkarte Datei. Wählen Sie die Option Versionshistorie in der linken Seitenleiste oder Öffnen Sie die Registerkarte Zusammenarbeit. Öffnen Sie die Versionshistorie mithilfe des Symbols Versionshistorie in der oberen Symbolleiste. Sie sehen die Liste der Dokumentversionen und -revisionen mit Angabe des Autors jeder Version/Revision sowie Erstellungsdatum und -zeit. Bei Dokumentversionen wird auch die Versionsnummer angegeben (z. B. ver. 2). Versionen anzeigen: Um genau zu wissen, welche Änderungen in jeder einzelnen Version/Revision vorgenommen wurden, können Sie die gewünschte Version anzeigen, indem Sie in der linken Seitenleiste darauf klicken. Die vom Autor der Version/Revision vorgenommenen Änderungen werden mit der Farbe gekennzeichnet, die neben dem Namen des Autors in der linken Seitenleiste angezeigt wird. Um zur aktuellen Version des Dokuments zurückzukehren, verwenden Sie die Option Historie schließen oben in der Versionsliste. Versionen wiederherstellen: Wenn Sie zu einer der vorherigen Versionen des Dokuments zurückkehren müssen, klicken Sie auf den Link Wiederherstellen unter der ausgewählten Version/Revision. Um mehr über das Verwalten von Versionen und Zwischenrevisionen sowie das Wiederherstellen früherer Versionen zu erfahren, lesen Sie bitte diesen Artikel."
    },
   {
        "id": "HelpfulHints/Viewer.htm", 
        "title": "ONLYOFFICE Document Viewer", 
        "body": "Sie können den ONLYOFFICE Document Viewer verwenden, um PDF, XPS, DjVu-Dateien zu öffnen und darin zu navigieren. Mit dem ONLYOFFICE Document Viewer können Sie: PDF, XPS, DjVu-Dateien anzeigen, Anmerkungen mit dem Chat-Tool hinzufügen, durch Dateien mithilfe des Inhaltsnavigationsfelds und der Seitenminiaturansichten navigieren, die Tools \"Auswählen\" und \"Hand\" verwenden, Dateien drucken und herunterladen, interne und externe Links verwenden, auf die erweiterten Einstellungen des Editors zugreifen und die folgenden Dokumentinformationen über die Registerkarte Datei oder Ansicht anzeigen: Speicherort (nur in der Online-Version verfügbar) - der Ordner im Modul Dokumente, in dem die Datei gespeichert ist. Besitzer (nur in der Online-Version verfügbar) - der Name des Benutzers, der die Datei erstellt hat. Hochgeladen (nur in der Online-Version verfügbar) - Datum und Uhrzeit, wann die Datei in das Portal hochgeladen wurde. Statistiken - die Anzahl der Seiten, Absätze, Wörter, Symbole, Symbole mit Leerzeichen. Seitengröße - die Größe der Seiten in der Datei. Zuletzt geändert - Datum und Uhrzeit der letzten Änderung des Dokuments. Erstellt - Datum und Uhrzeit der Erstellung des Dokuments. Anwendung - die Anwendung, mit der das Dokument erstellt wurde. Verfasser - die Person, die das Dokument erstellt hat. PDF-Ersteller - die Anwendung, die zum Konvertieren des Dokuments in PDF verwendet wird. PDF-Version - die Version der PDF-Datei. PDF mit Tags - zeigt an, ob die PDF-Datei Tags enthält. Scnhelle Web-Anzeige - zeigt an, ob die schnelle Webansicht für das Dokument aktiviert wurde. die folgenden Plugins verwenden: Plugins, die in der Desktop-Version verfügbar sind: Übersetzer, Senden, Thesaurus. Plugins, die in der Online-Version verfügbar sind: Controls example, Get and paste html, Telegram, Typograf, Count words, Rede, Thesaurus, Übersetzer. Die Benutzeroberfläche des ONLYOFFICE Document Viewer: Die obere Symbolleiste zeigt die Registerkarten Datei, Ansicht und Plugins und die folgenden Symbole an: Drucken ermöglicht das Ausdrucken einer Datei; Herunterladen ermöglicht das Herunterladen einer Datei auf Ihren Computer; Freigeben (nur in der Online-Version verfügbar) ermöglicht es Ihnen, die Benutzer zu verwalten, die Zugriff auf die Datei direkt vom Dokument aus haben: Laden Sie neue Benutzer ein und erteilen Sie ihnen Berechtigungen zum Bearbeiten, Lesen, Kommentieren, Ausfüllen von Formularen oder Überprüfen des Dokuments, oder verweigern Sie einigen Benutzern die Zugriffsrechte auf die Datei; Dateispeicherort öffnen in der Desktop-Version ermöglicht das Öffnen des Ordners, in dem die Datei gespeichert ist, im Fenster Datei-Explorer. In der Online-Version ermöglicht es das Öffnen des Ordners des Moduls Dokumente, in dem die Datei gespeichert ist, in einem neuen Browser-Tab; Als Favorit kennzeichnen / Aus Favoriten entfernen. Klicken Sie auf den leeren Stern, um eine Datei zu den Favoriten hinzuzufügen, damit sie leichter zu finden ist, oder klicken Sie auf den gefüllten Stern, um die Datei aus den Favoriten zu entfernen. Die hinzugefügte Datei ist nur eine Verknüpfung, sodass die Datei selbst an ihrem ursprünglichen Speicherort gespeichert bleibt. Durch das Löschen einer Datei aus den Favoriten wird die Datei nicht von ihrem ursprünglichen Speicherort entfernt; Benutzer zeigt den Namen des Benutzers an, wenn Sie den Mauszeiger darüber bewegen. Suchen - ermöglicht das Durchsuchen des Dokuments nach einem bestimmten Wort oder Symbol usw. Die Statusleiste am unteren Rand des ONLYOFFICE Document Viewer-Fensters zeigt die Seitenzahl und die Hintergrundstatusbenachrichtigungen an. Sie enthält auch die folgenden Tools: Mit dem Auswählungstool können Sie Text oder Objekte in einer Datei auswählen. Mit dem Tool Hand können Sie die Seite ziehen und scrollen. Mit dem Tool Seite anpassen können Sie die Seite so skalieren, dass der Bildschirm die ganze Seite anzeigt. Mit dem Tool Breite anpassen können Sie die Seite so skalieren, dass sie an die Breite des Bildschirms angepasst wird. Mit dem Tool Zoom können Sie die Seite vergrößern und verkleinern. Die linke Seitenleiste enthält die folgenden Symbole: - ermöglicht die Verwendung des Tools Suchen und Ersetzen, - (nur in der Online-Version verfügbar) ermöglicht das Öffnen des Chat-Panels, - ermöglicht das Öffnen des Bereichs Überschriften, der die Liste aller Überschriften mit den entsprechenden Ebenen anzeigt. Klicken Sie auf die Überschrift, um direkt zu einer bestimmten Seite zu springen. Klicken Sie auf das Symbol Einstellungen rechts neben dem Bereich Überschriften und verwenden Sie eine der verfügbaren Optionen aus dem Menü: Alle ausklappen - um alle Überschriftenebenen im Überschriften-Panel zu erweitern. Alle einklappen - um alle Überschriftenebenen außer Ebene 1 im Überschriften-Panel auszublenden. Auf Ebene erweitern - um die Überschriftenstruktur auf die ausgewählte Ebene zu erweitern. Z.B. wenn Sie Ebene 3 auswählen, werden die Ebenen 1, 2 und 3 erweitert, während Ebene 4 und alle darunter liegenden Ebenen reduziert werden. Schriftgröße – um die Schriftgröße des Textes im Überschriften-Panel anzupassen, indem Sie eine der verfügbaren Voreinstellungen auswählen: Klein, Mittelgroß und Groß. Lange Überschriften umbrechen – um lange Überschriften umzubrechen. Um separate Überschriftenebenen manuell zu erweitern oder zu reduzieren, verwenden Sie die Pfeile links neben den Überschriften. Klicken Sie zum Schließen des Panels Überschriften auf das Symbol   Überschriften in der linken Seitenleiste noch einmal. - ermöglicht die Anzeige von Seiten-Thumbnails für eine schnelle Navigation. Klicken Sie auf im Bereich Miniaturansichten, um auf Thumbnail-Einstellungen zuzugreifen: Ziehen Sie den Schieberegler, um die Größe der Miniaturansicht festzulegen. Die Option Sichtbaren Teil der Seite hervorheben ist standardmäßig aktiv, um den auf dem Bildschirm angezeigten Bereich anzuzeigen. Klicken Sie darauf, um diese Option zu deaktivieren. Klicken Sie zum Schließen des Panels Miniaturansichten auf das Symbol Miniaturansichten in der linken Seitenleiste noch einmal. - kontaktieren Sie unser Support-Team, - (nur in der Online-Version verfügbar) ermöglicht das Anzeigen von Informationen über das Programm."
    },
   {
        "id": "ProgramInterface/FileTab.htm", 
        "title": "Registerkarte Datei", 
        "body": "Über die Registerkarte Datei im Dokumenteneditor können Sie einige grundlegende Vorgänge in der aktuellen Datei durchführen. Dialogbox Online-Dokumenteneditor: Dialogbox Desktop-Dokumenteneditor: Sie können: In der Online-Version die aktuelle Datei speichern (falls die Option Automatisch speichern deaktiviert ist), herunterladen als (Speichern des Dokuments im ausgewählten Format auf der Festplatte des Computers), eine Kopie speichern als (Speichern einer Kopie des Dokuments im Portal im ausgewählten Format), drucken oder umbenennen. In der Desktop-version können Sie die aktuelle Datei mit der Option Speichern unter Beibehaltung des aktuellen Dateiformats und Speicherorts speichern oder Sie können die aktuelle Datei unter einem anderen Namen, Speicherort oder Format speichern. Nutzen Sie dazu die Option Speichern als. Weiter haben Sie die Möglichkeit die Datei zu drucken. die Datei mit einem Kennwort schützen, das Kennwort ändern oder löschen, die Datei mit einer digitalen Signatur schützen (nur in der Desktop-Version verfügbar), Weiter können Sie ein neues Dokument erstellen oder eine kürzlich bearbeitete Datei öffnen, (nur in der Online-Version verfügbar), allgemeine Informationen zum Dokument einsehen, Zugriffsrechte verwalten (nur in der Online-Version verfügbar, Versionsverläufe nachverfolgen (nur in der Online-Version verfügbar, auf die Erweiterten Einstellungen des Editors zugreifen und in der Desktiop-Version den Ordner öffnen wo die Datei gespeichert ist; nutzen Sie dazu das Fenster Datei-Explorer. In der Online-Version haben Sie außerdem die Möglichkeit den Ordner des Moduls Dokumente, in dem die Datei gespeichert ist, in einem neuen Browser-Fenster zu öffnen."
    },
   {
        "id": "ProgramInterface/FormsTab.htm", 
        "title": "Registerkarte Formulare", 
        "body": "Diese Registerkarte ist nur bei DOCXF-Dateien verfügbar. Auf der Registerkarte Formulare können Sie ausfüllbare Formulare wie Vereinbarungen, Anträge oder Umfragen erstellen. Fügen Sie Text- und Formularfelder hinzu, formatieren und konfigurieren Sie sie, um ein ausfüllbares Formular zu erstellen, egal wie komplex es sein muss. Dialogbox Online-Dokumenteneditor: Dialogbox Desktop-Dokumenteneditor: Sie können: die folgenden Objekte einfügen und bearbeiten: Textfelder Comboboxen Drop-Down Liste Kontrollkästchen Radiobuttons Bilder E-Mail-Adressen Telefonnummern komplexe Felder alle Felder und Hervorhebungseinstellungen löschen, durch die Formularfelder mit den Schaltflächen Vorheriges Feld und Nächstes Feld navigieren, die resultierenden Formulare in Ihrem Dokument anzeigen, Rollen verwalten, Formular als ausfüllbare OFORM-Datei speichern."
    },
   {
        "id": "ProgramInterface/HomeTab.htm", 
        "title": "Registerkarte Startseite", 
        "body": "Die Registerkarte Startseite im Dokumenteneditor wird standardmäßig geöffnet, wenn Sie ein beliebiges Dokument öffnen. Über diese Registerkarte können Sie Schriftart und Absätze formatieren. Auch einige andere Optionen sind hier verfügbar, wie Seriendruck und Farbschemata. Dialogbox Online-Dokumenteneditor: Dialogbox Desktop-Dokumenteneditor: Sie können: Schriftart, -größe und -farbe anpassen, Dekostile anwenden, die Hintergrundfarbe für einen Absatz auswählen, Aufzählungszeichen und nummerierte Listen erstellen, Absatzeinzüge ändern, den Zeilenabstand in den Absätzen einstellen, Ihren Text in der Zeile oder im Absatz ausrichten, Formatierungszeichen ein- und ausblenden, die Textformatierung kopieren/entfernen, das Farbschema ändern, Seriendruck anwenden (nur in der Online-Version verfügbar, Stile verwalten."
    },
   {
        "id": "ProgramInterface/InsertTab.htm", 
        "title": "Registerkarte Einfügen", 
        "body": "Die Registerkarte Einfügen im Dokumenteneditor ermöglicht das Hinzufügen einiger Seitenformatierungselemente sowie visueller Objekte und Kommentare. Dialogbox Online-Dokumenteneditor: Dialogbox Desktop-Dokumenteneditor: Sie können: eine leere Seite einfügen, Seitenumbrüche, Abschnittsumbrüche und Spaltenumbrüche einfügen, Kopf- und Fußzeilen und Seitenzahlen einfügen, Tabellen, Bilder, Diagramme und Formen einfügen, Hyperlinks und Kommentare einfügen, Textboxen und TextArt Objekte, Gleichungen, Initialbuchstaben und Inhaltskontrollen einfügen, SmartArt-Objekte einfügen."
    },
   {
        "id": "ProgramInterface/LayoutTab.htm", 
        "title": "Registerkarte Layout", 
        "body": "Über die Registerkarte Layout im Dokumenteneditor, können Sie die Darstellung des Dokuments ändern: Legen Sie die Seiteneinstellungen fest und definieren Sie die Anordnung der visuellen Elemente. Dialogbox Online-Dokumenteneditor: Dialogbox Desktop-Dokumenteneditor: Sie können: Seitenränder, Seitenausrichtung und Seitengröße anpassen, Spalten hinzufügen, Seitenumbrüche, Abschnittsumbrüche und Spaltenumbrüche einfügen, Zeilennummern einfügen, Objekte ausrichten und anordnen (Tabellen, Bilder, Diagramme, Formen), die Umbruchart ändern und Wrap-Grenze bearbeiten. ein Wasserzeichen hinzufügen."
    },
   {
        "id": "ProgramInterface/PluginsTab.htm", 
        "title": "Registerkarte Plugins", 
        "body": "Die Registerkarte Plugins im Dokumenteneditor ermöglicht den Zugriff auf erweiterte Bearbeitungsfunktionen mit verfügbaren Komponenten von Drittanbietern. Unter dieser Registerkarte können Sie auch Makros festlegen, um Routinevorgänge zu vereinfachen. Dialogbox Online-Dokumenteneditor: Dialogbox Desktop-Dokumenteneditor: Durch Anklicken der Schaltfläche Einstellungen öffnet sich das Fenster, in dem Sie alle installierten Plugins finden und verwalten sowie eigene Plugins hinzufügen können. Durch Anklicken der Schaltfläche Makros öffnet sich ein Fenster in dem Sie Ihre eigenen Makros erstellen und ausführen können. Um mehr über Makros zu erfahren, lesen Sie bitte unsere API-Dokumentation. Derzeit stehen standardmäßig folgende Plugins zur Verfügung: Senden erlaubt das versenden des Dokumentes durch das Standard Desktop Email Programm (nur in der Desktop-Version verfügbar). Code hervorheben - Hervorhebung der Syntax des Codes durch Auswahl der erforderlichen Sprache, des Stils, der Hintergrundfarbe. OCR ermöglicht es, in einem Bild enthaltene Texte zu erkennen und es in den Dokumenttext einzufügen. Foto-Editor - Bearbeitung von Bildern: schneiden, umlegen, rotieren, Linien und Formen zeichnen, Symbole und Text einfügen, eine Bildmaske laden und verschiedene Filter anwenden, wie zum Beispiel Graustufe, Invertiert, Blur, Schärfen, Emboss, usw. Rede ermöglicht es, ausgewählten Text in Sprache zu konvertieren. Thesaurus erlaubt es, nach Synonymen und Antonymen eines Wortes zu suchen und es durch das ausgewählte Wort zu ersetzen. Übersetzer erlaubt es, den ausgewählten Textabschnitten in andere Sprachen zu übersetzen. Dieses Plugin funktioniert nicht im Internet Explorer. YouTube erlaubt es, YouTube-Videos in Ihr Dokument einzubetten. Mendeley ermöglicht die Verwaltung von Forschungsarbeiten und die Erstellung von Bibliografien für wissenschaftliche Artikel (nur in der Online-Version verfügbar). Zotero erlaubt es, die bibliografischen Daten und zugehöriges Forschungsmaterial zu verwalten (nur in der Online-Version verfügbar). EasyBib hilft beim Suchen und Einfügen verwandter Bücher, Zeitschriftenartikel und Websites (nur in der Online-Version verfügbar). Die Plugins WordPress und EasyBib können verwendet werden, wenn Sie die entsprechenden Dienste in Ihren Portaleinstellungen einrichten. Sie können die folgenden Anleitungen für die Serverversion oder für die SaaS-Version verwenden. Um mehr über Plugins zu erfahren, lesen Sie bitte unsere API-Dokumentation. Alle derzeit als Open-Source verfügbaren Plugin-Beispiele sind auf GitHub verfügbar."
    },
   {
        "id": "ProgramInterface/ProgramInterface.htm", 
        "title": "Einführung in die Benutzeroberfläche des Dokumenteneditors", 
        "body": "Der Dokumenteneditor verfügt über eine Benutzeroberfläche mit Registerkarten, in der Bearbeitungsbefehle nach Funktionalität in Registerkarten gruppiert sind. Dialogbox Online-Dokumenteneditor: Dialogbox Desktop-Dokumenteneditor: Die Oberfläche des Editors besteht aus folgenden Hauptelementen: Die Editor-Kopfzeile zeigt das ONLYOFFICE-Logo, Registerkarten für alle geöffneten Dokumente mit ihren Namen und Menüregisterkarten an. Auf der linken Seite der Editor-Kopfzeile befinden sich die folgenden Schaltflächen: Speichern, Datei drucken, Rückgängig und Wiederholen. Auf der rechten Seite der Editor-Kopfzeile werden zusammen mit dem Benutzernamen die folgenden Symbole angezeigt: Dateispeicherort öffnen - in der Desktop-Version ermöglicht es das Öffnen des Ordners, in dem die Datei gespeichert ist, im Datei-Explorer-Fenster. In die Online-Version ermöglicht das Öffnen des Ordners des Moduls Dokumente, in dem die Datei gespeichert ist, in einem neuen Browser-Tab. Freigeben (nur in der Online-Version verfügbar). Es ermöglicht die Anpassung von Zugriffsrechten für die in der Cloud gespeicherten Dokumente. Als Favorit kennzeichnen - klicken Sie auf den Stern, um eine Datei zu den Favoriten hinzuzufügen, damit Sie sie leichter finden können. Die hinzugefügte Datei ist nur eine Verknüpfung, sodass die Datei selbst an ihrem ursprünglichen Speicherort gespeichert bleibt. Durch das Löschen einer Datei aus den Favoriten wird die Datei nicht von ihrem ursprünglichen Speicherort entfernt. Suchen - ermöglicht das Durchsuchen des Dokuments nach einem bestimmten Wort oder Symbol usw. Abhängig von der ausgewählten Registerkarte werden in der oberen Symbolleiste eine Reihe von Bearbeitungsbefehlen angezeigt. Aktuell stehen die folgenden Registerkarten zur Verfügung: Datei, Startseite, Einfügen, Layout, Referenzen, Zusammenarbeit, Schutz, Plugins. Die Befehle Kopieren, Einfügen, Ausschneiden und Alles auswählen stehen unabhängig von der ausgewählten Registerkarte jederzeit im linken Teil der oberen Menüleiste zur Verfügung. Die Statusleiste am unteren Rand des Editorfensters gibt die Seitennummer und die Wortanzahl an, sowie auch einige Benachrichtigungen an (z. B. „Alle Änderungen gespeichert“, „Verbindung unterbrochen“, wenn keine Verbindung besteht und der Editor versucht, die Verbindung wiederherzustellen usw.). Sie ermöglicht auch das Festlegen der Textsprache, das Aktivieren der Rechtschreibprüfung, das Einschalten des Modus \"Änderungen verfolgen\" und Anpassen des Zooms. Symbole in der linken Seitenleiste: - die Funktion Suchen und Ersetzen, - Kommentarfunktion öffnen, - Überschriften-Fenster aufrufen, um Überschriften zu verwalten, (nur in der Online-Version verfügbar) - hier können Sie das Chatfenster öffnen, unser Support-Team kontaktieren und die Programminformationen einsehen. - (nur in der Online-Version verfügbar) kontaktieren Sie unser Support-Team, - (nur in der Online-Version verfügbar) ermöglicht das Anzeigen von Informationen über das Programm. Über die rechte Seitenleiste können zusätzliche Parameter von verschiedenen Objekten angepasst werden. Wenn Sie im Text ein bestimmtes Objekt auswählen, wird das entsprechende Symbol in der rechten Seitenleiste aktiviert. Klicken Sie auf dieses Symbol, um die rechte Seitenleiste zu erweitern. Horizontale und vertikale Lineale ermöglichen das Ausrichten von Text und anderen Elementen in einem Dokument sowie das Festlegen von Rändern, Tabstopps und Absatzeinzügen. Über den Arbeitsbereich können Sie den Dokumenteninhalt anzeigen und Daten eingeben und bearbeiten. Über die Scroll-Leiste auf der rechten Seite können Sie mehrseitige Dokumente nach oben oder unten scrollen. Zur Vereinfachung können Sie bestimmte Komponenten verbergen und bei Bedarf erneut anzeigen. Weitere Informationen zum Anpassen der Ansichtseinstellungen finden Sie auf dieser Seite."
    },
   {
        "id": "ProgramInterface/ProtectionTab.htm", 
        "title": "Registerkarte Schutz", 
        "body": "Die Registerkarte Schutz im Dokumenteneditor ermöglicht den Schutz Ihrer Dokumente mit einem Passwort mit den eingeschränkten Zugriffsrechten. Dialogbox Online-Dokumenteneditor: Dialogbox Desktop-Dokumenteneditor: Sie können: Passwort für Ihr Dokument festlegen, Passwörter ändern und löschen, bestimmte Zugriffsrechte in den geschützten Dokumenten festlegen, den Dokumentenschutz vollständig entfernen."
    },
   {
        "id": "ProgramInterface/ReferencesTab.htm", 
        "title": "Registerkarte Verweise", 
        "body": "Unter der Registerkarte Verweise im Dokumenteneditor, können Sie verschiedene Arten von Verweisen verwalten: ein Inhaltsverzeichnis erstellen und aktualisieren, Fußnoten erstellen und bearbeiten, als auch Verknüpfungen einfügen. Dialogbox Online-Dokumenteneditor: Dialogbox Desktop-Dokumenteneditor: Auf dieser Registerkarte können Sie: ein Inhaltsverzeichnis erstellen und aktualisieren, Fußnoten und Endnoten einfügen, Hyperlinks einfügen, Lesezeichen hinzufügen, Bildbeschriftungen hinzufügen, Querverweise einfügen, Abbildungsverzeichnisse erstellen."
    },
   {
        "id": "ProgramInterface/ReviewTab.htm", 
        "title": "Registerkarte Zusammenarbeit", 
        "body": "Unter der Registerkarte Zusammenarbeit im Dokumenteneditor können Sie die Zusammenarbeit im Dokument organisieren. In der Online-Version können Sie die Datei mit jemanden teilen, einen gleichzeitigen Bearbeitungsmodus auswählen, Kommentare verwalten, von einem Zugriffsberechtigtem vorgenommene Änderungen nachverfolgen und alle Versionen und Überarbeitungen einsehen. In der Desktop-Version können Sie Kommentare verwalten und die Funktion ‘Änderungen nachverfolgen’ nutzen . Dialogbox Online-Dokumenteneditor: Dialogbox Desktop-Dokumenteneditor: Auf dieser Registerkarte können Sie: Freigabeeinstellungen festlegen (nur in der Online-Version verfügbar), zwischen den gleichzeitigen Bearbeitung-Modi Strikt und Schnell wechseln (nur in der Online-Version verfügbar), Kommentare zum Dokument hinzufügen, die Funktion ’Änderungen nachverfolgen’ aktivieren, Änderungen anzeigen lassen, die vorgeschlagenen Änderungen verwalten, ein Dokument zum vergleich laden (nur in der Online-Version verfügbar), die Diskussions-Leiste öffnen (nur in der Online-Version verfügbar), Versionsverläufe nachverfolgen (nur in der Online-Version verfügbar)."
    },
   {
        "id": "ProgramInterface/ViewTab.htm", 
        "title": "Registerkarte Ansicht", 
        "body": "Über die Registerkarte Ansicht im Dokumenteneditor können Sie das Aussehen Ihres Dokuments verwalten, während Sie daran arbeiten. Dialogbox Online-Dokumenteneditor: Dialogbox Desktop-Dokumenteneditor: Auf dieser Registerkarte sind die folgenden Funktionen verfügbar: Überschriften ermöglicht das Anzeigen und Navigieren von Überschriften in Ihrem Dokument. Zoom ermöglicht das Vergrößern und Verkleinern Ihres Dokuments. Seite anpassen ermöglicht es, die Seite so zu skalieren, dass der Bildschirm die ganze Seite anzeigt. An Breite anpassen ermöglicht es, die Seite so zu skalieren, dass sie an die Breite des Bildschirms angepasst wird. Thema der Benutzeroberfläche ermöglicht es, das Design der Benutzeroberfläche zu ändern, indem Sie eine der Optionen auswählen: Wie im System, Hell, Klassisch Hell, Dunkel, Dunkler Kontrast. Die Option Dunkles Dokument wird aktiv, wenn das dunkle Design aktiviert ist. Klicken Sie darauf, um auch den Arbeitsbereich zu verdunkeln. Mit den folgenden Optionen können Sie die anzuzeigenden oder auszublendenden Elemente konfigurieren. Aktivieren Sie die Elemente, um sie sichtbar zu machen: Symbolleiste immer anzeigen, um die obere Symbolleiste immer sichtbar zu machen. Statusleiste, um die Statusleiste immer sichtbar zu machen. Linkes Bedienfeld, um das linke Bedienfeld immer sichtbar zu machen. Rechtes Bedienungsfeld, um das rechte Bedienfeld immer sichtbar zu machen. Lineale, um Lineale immer sichtbar zu machen."
    },
   {
        "id": "UsageInstructions/AddBorders.htm", 
        "title": "Rahmen hinzufügen", 
        "body": "Um Rahmen zu einem Absatz, einer Seite oder dem gesamten Dokument im Dokumenteneditor hinzuzufügen: Postitionieren Sie den Cursor innerhalb des gewünschten Absatzes oder wählen Sie mehrere Absätze mit der Maus aus oder markieren Sie den gesamten Text mithilfe der Tastenkombination Strg+A. Klicken Sie mit der rechten Maustaste und wählen Sie im Kontextmenü die Option Absatz - Erweiterte Einstellungen aus oder nutzen Sie die Verknüpfung Erweiterte Einstellungen anzeigen in der rechten Seitenleiste. Wechseln Sie nun im Fenster Absatz - Erweiterte Einstellungen in die Registerkarte Rahmen &amp; Füllung. Geben Sie den gewünschten Wert für die Rahmenstärke an und wählen Sie eine Rahmenfarbe aus. Klicken Sie nun in das verfügbare Diagramm oder gestalten Sie Ihre Ränder über die entsprechenden Schaltflächen. Klicken Sie auf OK. Nach dem Hinzufügen von Rahmen können Sie die Innenabstände festlegen, d.h. den Abstand zwischen den rechten, linken, oberen und unteren Rahmen und dem darin befindlichen Text. Um die gewünschten Werte einzustellen, wechseln Sie im Fenster Absatz - Erweiterte Einstellungen in die Registerkarte Auffüllen:"
    },
   {
        "id": "UsageInstructions/AddCaption.htm", 
        "title": "Beschriftungen einfügen", 
        "body": "Eine Beschriftung ist eine nummerierte Bezeichnung eines Objektes, z.B. Gleichungen, Tabellen, Formen und Bilder. Eine Beschriftung bedeutet eine Quellenangabe, um ein Objekt im Text schnell zu finden. Im Dokumenteneditor können Sie auch Bildunterschriften verwenden, um ein Abbildingsverzeichnis zu erstellen. Um einem Objekt eine Beschriftung hinzuzufügen: wählen Sie das gewünschte Objekt aus; öffnen Sie die Registerkarte Verweise; klicken Sie auf das Symbol Beschriftung oder drücken Sie die rechte Maustaste und wählen Sie die Option Beschriftung einfügen aus, um das Feld Beschriftung einfügen zu öffnen: öffnen Sie das Bezeichnung-Dropdown-Menü, um den Bezeichnungstyp für die Beschriftung auszuwählen oder klicken Sie auf die Schaltfläche Hinzufügen, um eine neue Bezeichnung zu erstellen. Geben Sie den neuen Namen im Textfeld Bezeichnung ein. Klicken Sie OK, um eine neue Bezeichnung zu erstellen; markieren Sie das Kästchen Kapitelnummer einschließen, um die Nummerierung für die Beschriftung zu ändern; öffnen Sie das Einfügen-Dropdown-Menü und wählen Sie die Option Vor aus, um die Beschriftung über das Objekt zu stellen, oder wählen Sie die Option Nach aus, um die Beschriftung unter das Objekt zu stellen; markieren Sie das Kästchen Bezeichnung aus Beschriftung ausschließen, um die Sequenznummer nur für diese Beschriftung zu hinterlassen; wählen Sie die Nummerierungsart aus und fügen Sie ein Trennzeichnen ein; klicken Sie OK, um die Beschriftung einzufügen. Bezeichnungen löschen Um eine erstellte Bezeichnung zu löschen, wählen Sie diese Bezeichnung in dem Bezeichnung-Dropdown-Menü aus und klicken Sie Löschen. Diese Bezeichnung wird gelöscht. Sie können die erstellten Bezeichnungen löschen, aber die Standardbezeichnungen sind unlöschbar. Formatierung der Beschriftungen Sobald Sie die Beschriftung eingefügt haben, ist ein neuer Stil erstellt. Um den Stil für alle Beschriftungen im Dokument zu ändern: wählen Sie den Text mit dem neuen Stil für die Beschriftung aus; finden Sie den Beschriftungenstil (standardmäßig ist er blau) in der Stilgalerie auf der Registerkarte Startseite; drücken Sie die rechte Maustaste und wählen Sie die Option Aus der Auswahl neu aktualisieren aus. Beschriftungen gruppieren Um das Objekt mit der Beschriftung zusammen zu verschieben, gruppieren Sie sie zusammen: wählen Sie das Objekt aus; wählen Sie einen der Textumbrüche im Feld rechts aus; fügen Sie die Beschriftung ein (obengenannt); drücken und halten Sie die Umschalttaste und wählen Sie die gewünschte Objekte aus; drücken Sie die rechte Maustaste und wählen Sie die Option Anordnen > Gruppieren aus. Jetzt werden die Objekte zusammen bearbeitet. Um die Objekte zu trennen, wählen Sie die Option Anordnen > Gruppierung aufheben aus."
    },
   {
        "id": "UsageInstructions/AddFormulasInTables.htm", 
        "title": "Formeln in Tabellen verwenden", 
        "body": "Eine Formel einfügen Im Dokumenteneditor können Sie einfache Berechnungen für Daten in Tabellenzellen durchführen, indem Sie Formeln hinzufügen. Um eine Formel in eine Tabellenzelle einzufügen: Platzieren Sie den Zeiger in der Zelle, in der Sie das Ergebnis anzeigen möchten. Klicken Sie in der rechten Seitenleiste auf die Schaltfläche Formel hinzufügen. Geben Sie im sich öffnenden Fenster Formeleinstellungen die erforderliche Formel in das Feld Formel ein. Sie können eine benötigte Formel manuell eingeben, indem Sie die allgemeinen mathematischen Operatoren (+, -, *, /) verwenden, z. B. =A1*B2 oder verwenden Sie die Dropdown-Liste Funktion einfügen, um eine der eingebetteten Funktionen auszuwählen, z. B. =PRODUKT (A1, B2). Geben Sie die erforderlichen Argumente manuell in den Klammern im Feld Formel an. Wenn die Funktion mehrere Argumente erfordert, müssen diese durch Kommas getrennt werden. Verwenden Sie die Dropdown-Liste Zahlenformat, wenn Sie das Ergebnis in einem bestimmten Zahlenformat anzeigen möchten. Klicken Sie auf OK. Das Ergebnis wird in der ausgewählten Zelle angezeigt. Um die hinzugefügte Formel zu bearbeiten, wählen Sie das Ergebnis in der Zelle aus und klicken Sie auf die Schaltfläche Formel hinzufügen in der rechten Seitenleiste, nehmen Sie die erforderlichen Änderungen im Fenster Formeleinstellungen vor und klicken Sie auf OK. Verweise auf Zellen hinzufügen Mit den folgenden Argumenten können Sie schnell Verweise auf Zellbereiche hinzufügen: OBEN - Ein Verweis auf alle Zellen in der Spalte über der ausgewählten Zelle LINKS - Ein Verweis auf alle Zellen in der Zeile links von der ausgewählten Zelle UNTEN - Ein Verweis auf alle Zellen in der Spalte unter der ausgewählten Zelle RECHTS - Ein Verweis auf alle Zellen in der Zeile rechts von der ausgewählten Zelle Diese Argumente können mit Funktionen MITTELWERT, ANZAHL, MAX, MIN, PRODUKT, SUMME verwendet werden. Sie können auch manuell Verweise auf eine bestimmte Zelle (z. B. A1) oder einen Zellbereich (z. B. A1:B3) eingeben. Lesezeichen verwenden Wenn Sie bestimmten Zellen in Ihrer Tabelle Lesezeichen hinzugefügt haben, können Sie diese Lesezeichen als Argumente bei der Eingabe von Formeln verwenden. Platzieren Sie im Fenster Formeleinstellungen den Mauszeiger in den Klammern im Eingabefeld Formel, in dem das Argument hinzugefügt werden soll, und wählen Sie in der Dropdown-Liste Lesezeichen einfügen eines der zuvor hinzugefügten Lesezeichen aus. Formelergebnisse aktualisieren Wenn Sie einige Werte in den Tabellenzellen ändern, müssen Sie die Formelergebnisse manuell aktualisieren: Um ein einzelnes Formelergebnis zu aktualisieren, wählen Sie das gewünschte Ergebnis aus und drücken Sie F9 oder klicken Sie mit der rechten Maustaste auf das Ergebnis und verwenden Sie die Option Feld aktualisieren im Menü. Um mehrere Formelergebnisse zu aktualisieren, wählen Sie die erforderlichen Zellen oder die gesamte Tabelle aus und drücken Sie F9. Eingebettete Funktionen Sie können die folgenden mathematischen, statistischen und logischen Standardfunktionen verwenden: Kategorie Funktion Beschreibung Beispiel Mathematisches ABS(Zahl) Mit dieser Funktion wird der Absolutwert einer Zahl zurückgegeben. =ABS(-10) Rückgabe 10 Logisches UND(Wahrheitswert1; [Wahrheitswert2]; ...) Mit dieser Funktion wird überprüft, ob der von Ihnen eingegebene logische Wert WAHR oder FALSCH ist. Die Funktion gibt 1 (WAHR) zurück, wenn alle Argumente WAHR sind. =UND(1&gt;0,1&gt;3) Rückgabe 0 Statistisches MITTELWERT(Zahl1; [Zahl2]; ...) Mit dieser Funktion wird der Datenbereich analysiert und der Durchschnittswert ermittelt. =MITTELWERT(4,10) Rückgabe 7 Statistisches ANZAHL(Wert1; [Wert2]; ...) Mit dieser Funktion wird die Anzahl der ausgewählten Zellen gezählt, die Zahlen enthalten, wobei leere Zellen oder den Text ignorieren werden. =ANZAHL(A1:B3) Rückgabe 6 Logisches DEFINED () (DEFINIERT) Die Funktion wertet aus, ob ein Wert in der Zelle definiert ist. Die Funktion gibt 1 zurück, wenn der Wert fehlerfrei definiert und berechnet wurde, und 0, wenn der Wert nicht fehlerhaft definiert oder berechnet wurde. =DEFINED(A1) Logisches FALSCH() Die Funktion gibt 0 (FALSCH) zurück und benötigt kein Argument. =FALSCH Rückgabe 0 Logisches WENN(Wahrheitstest; [Wert_wenn_wahr]; [Wert_wenn_falsch]) Die Funktion wird verwendet, um den logischen Ausdruck zu überprüfen und einen Wert zurückzugeben, wenn er WAHR ist, oder einen anderen, wenn er FALSCH ist. =WENN(3&gt;1,1,0) Returns 1 Mathematisches GANZZAHL(Zahl) Mit dieser Funktion wird der ganzzahlige Teil der angegebenen Zahl analysiert und zurückgegeben. =GANZZAHL(2,5) Rückgabe 2 Statistisches MAX(Zahl1; [Zahl2]; ...) Mit dieser Funktion wird der Datenbereich analysiert und die größte Anzahl ermittelt. =MAX(15,18,6) Rückgabe 18 Statistisches MIN(Zahl1; [Zahl2]; ...) Mit dieser Funktion wird der Datenbereich analysiert und die kleinste Nummer ermittelt. =MIN(15,18,6) Rückgabe 6 Mathematisches REST(Zahl; Divisor) Mit dieser Funktion wird der Rest nach der Division einer Zahl durch den angegebenen Divisor zurückgegeben. =REST(6,3) Rückgabe 0 Logisches NICHT(Wahrheitswert) Mit dieser Funktion wird überprüft, ob der von Ihnen eingegebene logische Wert WAHR oder FALSCH ist. Die Funktion gibt 1 (WAHR) zurück, wenn das Argument FALSCH ist, und 0 (FALSCH), wenn das Argument WAHR ist. =NICHT(2&lt;5) Rückgabe 0 Logisches ODER(Wahrheitswert1; [Wahrheitswert2]; ...) Mit dieser Funktion wird überprüft, ob der von Ihnen eingegebene logische Wert WAHR oder FALSCH ist. Die Funktion gibt 0 (FALSCH) zurück, wenn alle Argumente FALSCH sind. =ODER(1&gt;0,1&gt;3) Rückgabe 1 Mathematisches PRODUKT(Zahl1; [Zahl2]; ...) Mit dieser Funktion werden alle Zahlen im ausgewählten Zellbereich multipliziert und das Produkt zurückgegeben. =PRODUKT(2,5) Rückgabe 10 Mathematisches RUNDEN(Zahl; Anzahl_Stellen) Mit dieser Funktion wird die Zahl auf die gewünschte Anzahl von Stellen gerundet. =RUNDEN(2,25,1) Rückgabe 2.3 Mathematisches VORZEICHEN(Zahl) Mit dieser Funktion wird das Vorzeichen einer Zahl zurückgegeben. Wenn die Zahl positiv ist, gibt die Funktion 1 zurück. Wenn die Zahl negativ ist, gibt die Funktion -1 zurück. Wenn die Zahl 0 ist, gibt die Funktion 0 zurück. VORZEICHEN(-12) Rückgabe -1 Mathematisches SUMME(Zahl1; [Zahl2]; ...) Mit dieser Funktion werden alle Zahlen im ausgewählten Zellenbereich addiert und das Ergebnis zurückgegeben. =SUMME(5,3,2) Rückgabe 10 Logisches WAHR() Die Funktion gibt 1 (WAHR) zurück und benötigt kein Argument. =WAHR gibt 1 zurück"
    },
   {
        "id": "UsageInstructions/AddHyperlinks.htm", 
        "title": "Hyperlink einfügen", 
        "body": "Einfügen eines Hyperlinks im Dokumenteneditor: Platzieren Sie Ihren Mauszeiger an der Position wo der Hyperlink eingefügt werden soll. Wechseln Sie in der oberen Symbolleiste auf die Registerkarte Einfügen oder Verweise. Klicken Sie auf das Symbol Hyperlink in der oberen Symbolleiste. Im sich nun öffnenden Fenster Einstellungen Hyperlink können Sie die Parameter für den Hyperlink festlegen: Wählen Sie den gewünschten Linktyp aus: Verwenden Sie die Option Externer Link und geben Sie eine URL im Format http://www.example.com in das Feld Verknüpfen mit unten ein, wenn Sie möchten einen Hyperlink hinzufügen, der zu einer externen Website führt. Wenn Sie einen Hyperlink zu einer lokalen Datei hinzufügen müssen, geben Sie die URL in den Datei://Pfad/Dokument.docx (für Windows) oder Datei:///Pfad/Dokument.docx (für MacOS und Linux) Format in das Feld Verknüpfen mit unten ein. Der Hyperlink Datei://Pfad/Dokument.docx oder Datei:///Pfad/Dokument.docx kann nur in der Desktop-Version des Editors geöffnet werden. Im Web-Editor können Sie den Link nur hinzufügen, ohne ihn öffnen zu können. Wenn Sie einen Hyperlink einfügen möchten, der zu einer bestimmten Stelle im gleichen Dokument führt, dann wählen Sie die Option Stelle im Dokument, wählen Sie dann eine der vorhandenen Überschriften im Dokumenttext oder eines der zuvor hinzugefügten Lesezeichen aus. Anzeigen - geben Sie den klickbaren Text ein, der zu der im oberen Feld angegebenen Webadresse führt. QuickInfo-Text - geben Sie einen Text ein, der in einem Dialogfenster angezeigt wird und den Nutzer über den Inhalt des Verweises informiert. Klicken Sie auf OK. Um einen Hyperlink einzufügen, können Sie auch die Tastenkombination STRG+K nutzen oder klicken Sie mit der rechten Maustaste an die Stelle an der Sie den Hyperlink einfügen möchten und wählen Sie die Option Hyperlink im Rechtsklickmenü aus. Es ist auch möglich, ein Zeichen, Wort, eine Wortverbindung oder einen Textabschnitt mit der Maus oder über die Tastatur auszuwählen. Öffnen Sie anschließend das Menü für die Hyperlink-Einstellungen wie zuvor beschrieben. In diesem Fall erscheint im Feld Anzeigen der ausgewählte Textabschnitt. Wenn Sie den Mauszeiger über den eingefügten Hyperlink bewegen, wird der von Ihnen im Feld QuickInfo eingebene Text angezeigt. Sie können den Link öffnen, indem Sie die Taste STRG drücken und dann auf den Link in Ihrem Dokument klicken. Um den hinzugefügten Hyperlink zu bearbeiten oder zu entfernen, klicken Sie mit der rechten Maustaste auf den Link, wählen Sie dann das Optionsmenü für den Hyperlink aus und klicken Sie anschließend auf Hyperlink bearbeiten oder Hyperlink entfernen."
    },
   {
        "id": "UsageInstructions/AddTableofFigures.htm", 
        "title": "Abbildungsverzeichnis hinzufügen und formatieren", 
        "body": "Abbildungsverzeichnis bietet einen Überblick über Gleichungen, Abbildungen und Tabellen, die einem Dokument hinzugefügt wurden. Ähnlich wie bei einem Inhaltsverzeichnis werden in einem Abbildungsverzeichnis Objekte oder Textüberschriften mit einem bestimmten Stil aufgelistet, sortiert und angeordnet. Dies macht es einfach, sie in Ihrem Dokument zu referenzieren und zwischen Abbildungen zu navigieren. Klicken Sie auf den Link im als Links formatierten Abbildungsverzeichnis, und Sie werden direkt zur Abbildung oder Überschrift weitergeleitet. Alle Tabellen, Gleichungen, Diagramme, Zeichnungen, Schaubilder, Fotos oder anderen Arten von Illustration werden als Abbildungen dargestellt. Um ein Abbildungsverzeichnis hinzuzufügen im Dokumenteneditor, öffnen Sie die Registerkarte Verweise und klicken Sie auf das Symbol Abbildungsverzeichnis , um ein Abbildungsverzeichnis zu erstellen und formatieren. Verwenden Sie die Schaltfläche Aktualisieren, um das Abbildungsverzeichnis jedes Mal zu aktualisieren, wenn Sie Ihrem Dokument eine neue Abbildung hinzufügen. Abbildungsverzeichnis erstellen Sie können ein Abbildungsverzeichnis entweder mit Beschriftungen oder mit Stilen erstellen. Eine Beschriftung soll jeder Gleichung, jedem Abbildungsverzeichnis hinzugefügt werden, oder ein Stil soll auf dem Text angewendet werden, damit der Text korrekt in ein Abbildungsverzeichnis aufgenommen ist. Wenn Sie Beschriftungen oder Stile hinzugefügt haben, positionieren Sie den Cursor an der Stelle, an der Sie ein Abbildungsverzeichnis einfügen möchten, und wechseln Sie zur Registerkarte Verweise. Klicken Sie auf die Schaltfläche Abbildungsverzeichnis, um das Dialogfeld Abbildungsverzeichnis zu öffnen und eine Liste der Abbildungen zu erstellen. Wählen Sie eine Option zum Erstellen eines Abbildungsverzeichnisses aus Beschriftungen oder Stilen. Sie können ein Abbildungsverzeichnis basierend auf beschrifteten Objekten erstellen. Aktivieren Sie das Kontrollkästchen Beschriftung und wählen Sie ein beschriftetes Objekt aus der Dropdown-Liste aus: Kein Gleichung Abbildung Tabelle Sie können ein Abbildungsverzeichnis basierend auf den Stilen erstellen, die zur Textformatierung verwendet sind. Aktivieren Sie das Kontrollkästchen Stil und wählen Sie einen Stil aus der Dropdown-Liste aus. Die Liste der Optionen kann je nach angewendetem Stil variieren: Überschrift 1 Überschrift 2 Beschriftung Abbildungsverzeichnis Normal Abbildungsverzeichnis formatieren Mit den Kontrollkästchenoptionen können Sie ein Abbildungsverzeichnis formatieren. Alle Kontrollkästchen für Formatierung sind standardmäßig aktiviert, da es in den meisten Fällen am vernünftigsten ist. Deaktivieren Sie die Kontrollkästchen, die Sie nicht benötigen. Seitenzahlen anzeigen: Um die Seitenzahl anzuzeigen, auf der die Abbildung ist; Seitenzahlen rechtsbündig: Um Seitenzahlen rechts anzuzeigen, wenn Seitenzahlen anzeigen aktiv ist; deaktivieren Sie diese Option, um Seitenzahlen direkt nach dem Titel anzuzeigen; Abbildungsverzeichnis als Links formatieren: Um Hyperlinks dem Abbildungsverzeichnis hinzuzufügen; Bezeichnung und Nummer einschließen: Um dem Abbildungsverzeichnis eine Bezeichnung und eine Nummer hinzuzufügen. Wählen Sie den Füllzeichen-Stil aus der Dropdown-Liste, um Titel mit Seitenzahlen für eine bessere Visualisierung zu verbinden. Passen Sie die Textstile des Abbildungsverzeichnisses an, indem Sie einen der verfügbaren Stile aus der Dropdown-Liste auswählen: Aktuell - zeigt den zuvor ausgewählten Stil an. Einfach - hebt Text als fett hervor. Online - hebt Text als Hyperlink hervor und ordnet ihn an. Klassisch - macht alle Buchstaben als Großbuchstaben. Elegant - hebt Text als kursiv hervor. Zentriert - zentriert den Text und zeigt kein Füllzeichen an. Formell - zeigt Text in 11 pt Arial an, um eine formellere Darstellung zu haben. Das Vorschaufenster zeigt an, wie das Abbildungsverzeichnis im Dokument oder beim Drucken angezeigt wird. Abbildungsverzeichnis aktualisieren Aktualisieren Sie ein Abbildungsverzeichnis jedes Mal, wenn Sie Ihrem Dokument eine neue Gleichung, Abbildung oder Tabelle hinzufügen. Die Schaltfläche Aktualisieren wird aktiv, wenn Sie auf das Abbildungsverzeichnis klicken oder es auswählen. Klicken Sie auf der Registerkarte Verweise in der oberen Symbolleiste auf die Schaltfläche Aktualisieren und wählen Sie die gewünschte Option aus dem Menü: Nur Seitenzahlen aktualisieren - um Seitenzahlen zu aktualisieren, ohne Änderungen an den Überschriften vorzunehmen. Gesamtes Verzeichnis aktualisieren - um alle geänderten Überschriften und Seitenzahlen zu aktualisieren. Klicken Sie auf OK oder Klicken Sie mit der rechten Maustaste auf das Abbildungsverzeichnis in Ihrem Dokument, um das Kontextmenü zu öffnen, und wählen Sie die Option Feld aktualisieren, um das Abbildungsverzeichnis zu aktualisieren."
    },
   {
        "id": "UsageInstructions/AddWatermark.htm", 
        "title": "Wasserzeichen hinzufügen", 
        "body": "Ein Wasserzeichen ist ein Text oder Bild, welches unter dem Haupttextbereich platziert ist. Text-Wasserzeichen erlaubt Ihnen Ihr Dokument mit einem Status zu versehen (bspw. Vertraulich, Entwurf usw.). Bild-Wasserzeichen erlaubt Ihnen ein Bild hinzuzufügen, bspw. Ihr Firmenlogo. Um ein Wasserzeichen in ein Dokument einzufügen im Dokumenteneditor: Wechseln Sie zur Registerkarte Layout in der Hauptmenüleiste. Klicken Sie das Wasserzeichen Symbol in der Menüleiste und wählen Sie die Option Benutzerdefiniertes Wasserzeichen aus dem Menü. Danach erscheint das Fenster mit den Wasserzeichen-Einstellungen. Wählen Sie die Art des Wasserzeichens: Benutzen Sie die Option Text-Wasserzeichen und passen Sie die Parameter an: Sprache - wählen Sie eine Sprache aus der Liste. Für Wasserzeichen unterstützte Sprachen: Englisch, Französisch, Deutsch, Italienisch, Japanisch, Mandarin, Russisch, Spanisch. Text - wählen Sie einen verfügbaren Text der gewählten Sprache aus. Für Deutsch sind die folgenden Text-Optionen verfügbar: BEISPIEL, DRINGEND, ENTWURF, KOPIE, NICHT KOPIEREN, ORIGINAL, PERSÖNLICH, VERTRAULICH, STRENG VERTRAULICH. Schriftart - wählen Sie die Schriftart und Größe aus der entsprechenden Drop-Down-Liste. Benutzen Sie die Symbole rechts daneben, um Farbe und Schriftdekoration zu setzen: Fett, Kursiv, Unterstrichen, Durchgestrichen. Halbtransparent - benutzen Sie diese Option, um Transparenz anzuwenden. Layout - wählen Sie Diagonal oder Horizontal. Benutzen Sie die Option Bild-Wasserzeichen und passen Sie die Parameter an: Wählen Sie die Bild-Datei aus einer der beiden Optionen: Aus Datei oder Aus URL - das Bild wird als Vorschau rechts in dem Fenster dargestellt. Maßstab - wählen Sie die notwendige Skalierung aus: Automatisch, 500%, 200%, 150%, 100%, 50%. Bestätigen Sie die Einstellungen mit OK. Um ein Wasserzeichen zu bearbeiten, öffnen Sie das Fenster mit den Wasserzeichen-Einstellungen wie oben beschrieben, ändern Sie die benötigten Parameter und speichern dies mit OK. Um ein Wasserzeichen zu löschen, klicken Sie das Wasserzeichen Symbol in der Menüleiste und wählen Sie die Option Wasserzeichen entfernen aus dem Menü. Sie können auch die Option Kein aus dem Fenster mit den Wasserzeichen-Einstellungen benutzen."
    },
   {
        "id": "UsageInstructions/AlignArrangeObjects.htm", 
        "title": "Objekte auf einer Seite anordnen und ausrichten", 
        "body": "Im Dokumenteneditor können die hinzugefügten Autoformen, Bilder, Diagramme und Textboxen auf einer Seite ausgerichtet, gruppiert und angeordnet werden. Um eine dieser Aktionen auszuführen, wählen Sie zuerst ein einzelnes Objekt oder mehrere Objekte auf der Seite aus. Um mehrere Objekte zu wählen, halten Sie die Taste STRG gedrückt und klicken Sie auf die gewünschten Objekte. Um ein Textfeld auszuwählen, klicken Sie auf den Rahmen und nicht auf den darin befindlichen Text. Danach können Sie über Symbole in der Registerkarte Layout navigieren, die nachstehend beschrieben werden, oder die entsprechenden Optionen aus dem Rechtsklickmenü nutzen. Objekte ausrichten Ausrichten von zwei oder mehr ausgewählten Objekten: Klicken Sie auf das Symbol Ausrichten auf der oberen Symbolleiste in der Registerkarte Layout und wählen Sie eine der verfügbaren Optionen: An Seite ausrichten, um Objekte relativ zu den Rändern der Seite auszurichten. Am Seitenrand ausrichten, um Objekte relativ zu den Seitenrändern auszurichten. Ausgewählte Objekte ausrichten (diese Option ist standardmäßig ausgewählt), um Objekte im Verhältnis zueinander auszurichten. Klicken Sie erneut auf das Symbol Ausrichten und wählen Sie den gewünschten Ausrichtungstyp aus der Liste aus: Linksbündig ausrichten - Objekte horizontal am linken Rand des am weitesten links befindlichen Objekts/linken Rand der Seite/linken Seitenrands ausrichten. Mittig ausrichten - Objekte horizontal an ihrer Mitte/der Seitenmitte/der Mitte des Abstands zwischen dem linken und rechten Seitenrand ausrichten. Rechtsbündig ausrichten - Objekte horizontal am rechten Rand des am weitesten rechts befindlichen Objekts/rechten Rand der Seite/rechten Seitenrands ausrichten. Oben ausrichten - Objekte vertikal an der Oberkante des obersten Objekts/der Oberkante der Seite/des oberen Seitenrands ausrichten. Mitte ausrichten - Objekte vertikal an ihrer Mitte/Seitenmitte/ Zwischenraummitte zwischen dem oberen und unteren Seitenrand ausrichten. Unten ausrichten - Objekte vertikal an der Unterkante des unteresten Objekts/der Unterkante der Seite/des unteren Seitenrands ausrichten. Alternativ können Sie mit der rechten Maustaste auf die ausgewählten Objekte klicken, wählen Sie anschließend im Kontextmenü die Option Ausrichten aus und nutzen Sie dann eine der verfügbaren Ausrichtungsoptionen. Wenn Sie ein einzelnes Objekt ausrichten möchten, kann es relativ zu den Rändern des Blatts oder zu den Seitenrändern ausgerichtet werden. Die Option An Rand ausrichten ist in diesem Fall standardmäßig ausgewählt. Objekte verteilen Um drei oder mehr ausgewählte Objekte horizontal oder vertikal so zu verteilen, dass der gleiche Abstand zwischen ihnen angezeigt wird: Klicken Sie auf das Symbol Ausrichten auf der oberen Symbolleiste in der Registerkarte Layout und wählen Sie eine der verfügbaren Optionen: An Seite ausrichten, um Objekte zwischen den Rändern der Seite zu verteilen. An Seitenrand ausrichten, um Objekte zwischen den Seitenrändern zu verteilen. Ausgewählte Objekte ausrichten (diese Option ist standardmäßig ausgewählt), um Objekte zwischen zwei ausgewählten äußersten Objekten zu verteilen. Klicken Sie erneut auf das Symbol Ausrichten und wählen Sie den gewünschten Verteilungstyp aus der Liste aus: Horizontal verteilen - um Objekte gleichmäßig zwischen den am weitesten links und rechts liegenden ausgewählten Objekten/dem linken und rechten Seitenrand zu verteilen. Vertikal verteilen - um Objekte gleichmäßig zwischen den am weitesten oben und unten liegenden ausgewählten Objekten / dem oberen und unteren Rand der Seite zu verteilen. Alternativ können Sie mit der rechten Maustaste auf die ausgewählten Objekte klicken, wählen Sie anschließend im Kontextmenü die Option Ausrichten aus und nutzen Sie dann eine der verfügbaren Verteilungsoptionen. Die Verteilungsoptionen sind deaktiviert, wenn Sie weniger als drei Objekte auswählen. Objekte gruppieren Um zwei oder mehr Objekte zu gruppieren oder die Gruppierung aufzuheben, klicken Sie auf den Pfeil links neben dem Symbol Gruppieren in der Registerkarte Layout und wählen Sie die gewünschte Option aus der Liste aus: Gruppieren - um mehrere Objekte zu einer Gruppe zusammenzufügen, so dass sie gleichzeitig gedreht, verschoben, skaliert, ausgerichtet, angeordnet, kopiert, eingefügt und formatiert werden können, wie ein einzelnes Objekt. Gruppierung aufheben - um die ausgewählte Gruppe der zuvor gruppierten Objekte aufzulösen. Alternativ können Sie mit der rechten Maustaste auf die ausgewählten Objekte klicken, wählen Sie anschließend im Kontextmenü die Option Anordnung aus und nutzen Sie dann die Optionen Gruppieren oder Gruppierung aufheben. Die Option Gruppieren ist deaktiviert, wenn Sie weniger als zwei Objekte auswählen. Die Option Gruppierung aufheben ist nur verfügbar, wenn Sie eine zuvor gruppierte Objektgruppe auswählen. Objekte anordnen Um Objekte anzuordnen (z.B. die Reihenfolge bei einer Überlappung zu ändern), klicken Sie auf die Symbole eine Ebene nach vorne und eine Ebene nach hinten in der Registerkarte Layout und wählen Sie die gewünschte Anordnung aus der Liste aus. Um das/die ausgewählte(n) Objekt(e) nach vorne zu bringen, klicken Sie auf das Symbol Eine Ebene nach vorne in der Registerkarte Layout und wählen Sie den gewünschten Ausrichtungstyp aus der Liste aus: In den Vordergrund - Objekt(e) in den Vordergrund bringen. Eine Ebene nach vorne <<div class = \"icon icon-bringforward\"> - um ausgewählte Objekte eine Ebene nach vorne zu schieben.</li> </ul> Um das/die ausgewählte(n) Objekt(e) nach hinten zu verschieben, klicken Sie auf den Pfeil nach hinten verschieben in der Registerkarte Layout und wählen Sie den gewünschten Ausrichtungstyp aus der Liste aus: In den Hintergrund - um ein Objekt/Objekte in den Hintergrund zu schieben. Eine Ebene nach hinten - um ausgewählte Objekte nur eine Ebene nach hinten zu schieben. Alternativ können Sie mit der rechten Maustaste auf die ausgewählten Objekte klicken, wählen Sie anschließend im Kontextmenü die Option Anordnen aus und nutzen Sie dann eine der verfügbaren Optionen. </div>"
    },
   {
        "id": "UsageInstructions/AlignText.htm", 
        "title": "Text in einem Absatz ausrichten", 
        "body": "Der Text wird üblicherweise auf vier Arten ausgerichtet: linksbündiger Text, rechtsbündiger Text, zentrierter Text oder Blocksatz. Um den Text im Dokumenteneditor auszurichten, Bewegen Sie den Zeiger an die Stelle, an der Sie den Text ausrichten möchten (dabei kann es sich um eine neue Zeile oder um bereits eingegebenen Text handeln). Wechseln Sie in der oberen Symbolleiste auf die Registerkarte Startseite. Wählen Sie den gewünschten Ausrichtungstyp: Um den Text linksbündig auszurichten (der linke Textrand wird am linken Seitenrand ausgerichtet, der rechte Textrand bleibt wie vorher), klicken Sie aufs Symbol Linksbündig auf der oberen Symbolleiste. Um den Text zentriert auszurichten (rechter und linker Textrand bleiben ungleichmäßig), klicken Sie aufs Symbol Zentriert auf der oberen Symbolleiste. Um den Text rechtsbündig auszurichten (der rechte Textrand verläuft parallel zum rechten Seitenrand, der linke Textrand bleibt ungleichmäßig), klicken Sie auf der oberen Symbolleiste auf das Symbol Rechtsbündig . Um den Text im Blocksatz auszurichten (der Text wird gleichmäßig ausgerichtet, dazu werden zusätzliche Leerräume im Text eingefügt), klicken Sie aufs Symbol Blocksatz auf der oberen Symbolleiste. Die Ausrichtungparameter sind im Absatz - Erweiterte Einstellungen verfügbar: Drücken Sie die rechte Maustaste und wählen Sie die Option Absatz - Erweiterte Einstellungen von dem Rechts-Klick Menu oder benutzen Sie die Option Erweiterte Einstellungen anzeigen von der rechten Seitenleiste. Im Abschnitt Absatz - Erweiterte Einstellungen wechseln Sie zur Registerkarte Einzüg und Abstände. Wählen Sie einen der Ausrichtungstypen aus der Ausrichtungsliste aus: Links, Zentriert, Rechts, Blocksatz. Klicken Sie auf OK, um die Änderungen anzuwenden."
    },
   {
        "id": "UsageInstructions/BackgroundColor.htm", 
        "title": "Hintergrundfarbe für einen Absatz festlegen", 
        "body": "Im Dokumenteneditor der gesamte Absatz, einschließlich Leerzeichen und Zeilenabständen wird mit der gewählten Farbe hinterlegt. Einen Absatz mit Farbe hinterlegen oder die aktuelle Farbe ändern: Wechseln Sie in die Registerkarte Startseite, klicken Sie auf das Symbol Farbschema ändern und wählen Sie aus den verfügbaren Vorlagen ein Farbschema für Ihr Dokument aus. Postitionieren Sie den Cursor innerhalb des gewünschten Absatzes oder wählen Sie mehrere Absätze mit der Maus aus oder markieren Sie den gesamten Text mithilfe der Tastenkombination Strg+A. Öffnen Sie die Farbpalette mit den verfügbaren Farben. Sie haben dazu verschiedene Möglichkeiten: Klicken Sie auf der Registerkarte Startseite auf das Pfeilsymbol oder klicken Sie in der rechten Seitenleiste auf das Farbfeld neben der Überschrift Hintergrundfarbe oder klicken Sie in der rechten Seitenleiste auf den Verweis \"Erweiterte Einstellungen anzeigen\" oder wählen Sie über einen Rechtsklick im Kontextmenü die Option \"Absatz Erweiterte Einstellungen“ aus und wechseln Sie dann im Fenster \"Absatz - Erweiterte Einstellungen\" in die Registerkarte \"Rahmen &amp; Füllungen\" und klicken Sie auf das Farbfeld neben der Beschriftung Hintergrundfarbe. Wählen Sie eine beliebige Farbe auf den verfügbaren Paletten aus. Nachdem Sie die über das Symbol die gewünschte Farbe ausgewählt haben, können Sie diese Farbe auf jeden ausgewählten Absatz anwenden, indem Sie einfach auf das Symbol klicken (die ausgewählte Farbe wird angezeigt), ohne diese Farbe erneut auf der Palette auswählen zu müssen. Beachten Sie, dass die ausgewählte Farbe nicht für im Schnellzugriff gespeichert wird, wenn Sie die Option Hintergrundfarbe in der rechten Seitenleiste oder im Fenster \"Absatz - Erweiterte Einstellungen\" verwenden. (Diese Optionen können nützlich sein, wenn Sie über das Symbol eine allgemeine Farbe für das Dokument festgelegt haben und für einen bestimmten Absatz eine andere Hintergrundfarbe auswählen möchten). Die Hintergrundfarbe eines bestimmten Absatzes löschen: Positionieren Sie den Cursor innerhalb des gewünschten Absatzes oder wählen Sie mehrere Absätze mit der Maus aus oder markieren Sie den gesamten Text mithilfe der Tastenkombination Strg+A. Klicken Sie in der rechten Seitenleiste auf das Farbfeld neben der Überschrift Hintergrundfarbe. Wählen Sie das Symbol ."
    },
   {
        "id": "UsageInstructions/ChangeColorScheme.htm", 
        "title": "Farbschema ändern", 
        "body": "Farbschemata werden auf das gesamte Dokument angewendet. Im Dokumenteneditor können Sie das Erscheinungsbild Ihres Dokuments schnell ändern, da die Farbschemata die Palette Designfarben für verschiedene Dokumentelemente definieren (Schriftart, Hintergrund, Tabellen, AutoFormen, Diagrammen). Wenn Sie einige Designfarben auf die Dokumentelemente angewendet haben und dann ein anderes Farbschema auswählen, ändern sich die angewendeten Farben in Ihrem Dokument entsprechend. Um ein Farbschema zu ändern, klicken Sie auf den Abwärtspfeil neben dem Symbol Farbschema ändern in der Registerkarte Startseite auf der Haupt-Symbolleiste und wählen Sie aus den verfügbaren Vorgaben das gewünschte Farbschema aus: New Office, Office, Graustufen, Apex, Aspect, Civic, Concourse, Equity, Flow, Foundry, Median, Metro, Module, Odulent, Oriel, Origin, Paper, Solstice, Technic, Trek, Urban, Verve. Das ausgewählte Farbschema wird in der Liste hervorgehoben. Wenn Sie das gewünschte Farbschema ausgewählt haben, können Sie im Fenster Farbpalette die Farben für das jeweilige Dokumentelement auswählen, auf das Sie die Farbe anwenden möchten. Bei den meisten Dokumentelementen können Sie auf das Fenster mit der Farbpalette zugreifen, indem Sie das gewünschte Element markieren und in der rechten Seitenleiste auf das farbige Feld klicken. Für die Schriftfarbe kann dieses Fenster über den Abwärtspfeil neben dem Symbol Schriftfarbe in der Registerkarte Startseite geöffnet werden. Folgende Farbauswahlmöglichkeiten stehen zur Verfügung: Designfarben - die Farben, die dem gewählten Farbschema der Tabelle entsprechen. Standardfarben - die festgelegten Standardfarben. Werden durch das gewählte Farbschema nicht beeinflusst. Benutzerdefinierte Farbe - klicken Sie auf diese Option, wenn Ihre gewünschte Farbe nicht in der Palette mit verfügbaren Farben enthalten ist. Wählen Sie den gewünschten Farbbereich aus, in dem Sie den vertikalen Farbregler verschieben und die entsprechende Farbe festlegen, in dem Sie den Farbwähler innerhalb des großen quadratischen Farbfelds ziehen. Sobald Sie eine Farbe mit dem Farbwähler bestimmt haben, werden die entsprechenden RGB- und sRGB-Farbwerte in den Feldern auf der rechten Seite angezeigt. Sie können eine Farbe auch anhand des RGB-Farbmodells bestimmen, indem Sie die gewünschten nummerischen Werte in den Feldern R, G, B (Rot, Grün, Blau) festlegen oder den sRGB-Hexadezimalcode in das Feld mit dem #-Zeichen eingeben. Die gewählte Farbe erscheint im Vorschaufeld Neu. Wenn das Objekt vorher mit einer benutzerdefinierten Farbe gefüllt war, wird diese Farbe im Feld Aktuell angezeigt, so dass Sie die Originalfarbe und die Zielfarbe vergleichen könnten. Wenn Sie die Farbe festgelegt haben, klicken Sie auf Hinzufügen. Die benutzerdefinierte Farbe wird auf das ausgewählte Element angewendet und zur Palette Benutzerdefinierte Farbe hinzugefügt."
    },
   {
        "id": "UsageInstructions/ChangeWrappingStyle.htm", 
        "title": "Textumbruch ändern", 
        "body": "Die Option Textumbruch legt fest, wie ein Objekt relativ zum Text positioniert wird. Im Dokumenteneditor sie können den Umbruchstil für eingefügt Objekte ändern, wie beispielsweise Formen, Bilder, Diagramme, Textfelder oder Tabellen. Textumbruch für Formen, Bilder, Diagramme oder Tabellen ändern Ändern des aktuellen Umbruchstils: Wählen Sie über einen Linksklick ein einzelnes Objekt auf der Seite aus. Um ein Textfeld auszuwählen, klicken Sie auf den Rahmen und nicht auf den darin befindlichen Text. Öffnen Sie die Einstellungen für den Textumbruch: Wechseln Sie in der oberen Symbolleiste in die Registerkarte Layout und klicken Sie auf das den Pfeil neben dem Symbol Textumbruch oder klicken Sie mit der rechten Maustaste auf das Objekt und wählen Sie die Option Textumbruch im Kontextmenü aus oder klicken Sie mit der rechten Maustaste auf das Objekt, wählen Sie die Option Erweiterte Einstellungen und wechseln Sie im Fenster Erweitere Einstellungen in die Gruppe Textumbruch. Wählen Sie den gewünschten Umbruchstil aus: Mit Text verschieben - das Bild wird als Teil vom Text behandelt und wenn der Text verschoben wird, wird auch das Bild verschoben. In diesem Fall sind die Positionsoptionen nicht verfügbar. Falls einer der folgenden Stile gewählt ist, kann das Bild unabhängig vom Text verschoben werden und genau auf der Seite positioniert werden: Quadrat - der Text bricht um den rechteckigen Kasten, der das Bild begrenzt. Eng - der Text bricht um die Bildkanten um. Transparent - der Text bricht um die Bildkanten um und füllt den offenen weißen Raum innerhalb des Bildes. Wählen Sie für diesen Effekt die Option Umbruchsgrenze bearbeiten aus dem Rechtsklickmenü aus. Oben und unten - der Text ist nur oberhalb und unterhalb des Bildes. Vor den Text - das Bild überlappt mit dem Text. Hinter den Text - der Text überlappt sich mit dem Bild. Wenn Sie die Formate Quadrat, Eng, Transparent oder Oben und unten auswählen, haben Sie die Möglichkeit zusätzliche Parameter festzulegen - Abstand vom Text auf allen Seiten (oben, unten, links, rechts). Klicken Sie dazu mit der rechten Maustaste auf das Objekt, wählen Sie die Option Erweiterte Einstellungen und wechseln Sie im Fenster Erweiterte Einstellungen in die Gruppe Textumbruch. Wählen Sie die gewünschten Werte und klicken Sie auf OK. Wenn Sie einen anderen Umbruchstil als Inline auswählen, steht Ihnen im Fenster Erweiterte Einstellungen auch die Gruppe Position zur Verfügung. Weitere Informationen zu diesen Parametern finden Sie auf den entsprechenden Seiten mit Anweisungen zum Umgang mit Formen, Bildern oder Diagrammen. Wenn Sie einen anderen Umbruchstil als Inline auswählen, haben Sie außerdem die Möglichkeit, die Umbruchränder für Bilder oder Formen zu bearbeiten. Klicken Sie mit der rechten Maustaste auf das Objekt, wählen Sie die Option Textumbruch im Kontextmenü aus und klicken Sie auf Bearbeitung der Umbruchsgrenze. Sie können auch die Option Umbrüche -> Umbruchsgrenze bearbeiten auf der Registerkarte Layout in der oberen Symbolleiste verwenden. Ziehen Sie die Umbruchpunkte, um die Grenze benutzerdefiniert anzupassen. Um einen neuen Rahmenpunkt zu erstellen, klicken Sie auf eine beliebige Stelle auf der roten Linie und ziehen Sie diese an die gewünschte Position. Textumbruch für Tabellen ändern Für Tabellen stehen die folgenden Umbruchstile zur Verfügung: Mit Text verschieben und Umgebend. Ändern des aktuellen Umbruchstils: Klicken Sie mit der rechten Maustaste auf die Tabelle und wählen Sie die Option Tabelle-Erweiterte Einstellungen. Wechseln Sie nun im Fenster Tabelle - Erweiterte Einstellungen in die Gruppe Textumbruch. Wählen Sie eine der folgenden Optionen: Textumbruch - Mit Text in Zeile: Der Text wird durch die Tabelle umgebrochen, außerdem können Sie die Ausrichtung wählen: linksbündig, zentriert, rechtsbündig. Textumbruch - Umgebend: Bei diesem Format wird die Tabelle innerhalb des Textes eingefügt und entsprechend an allen Seiten vom Text umgeben. Über die Gruppe Textumbruch im Fenster Tabelle - Erweiterte Einstellungen, können Sie außerdem die folgenden Parameter einrichten. Für Tabellen, die mit dem Text verschoben werden, können Sie die Ausrichtung der Tabelle festlegen (linksbündig, zentriert, rechtsbündig) sowie den Einzug vom linken Seitenrand. Für Tabellen, deren Position auf einer Seite fixiert ist, können Sie den Abstand vom Text sowie die Tabellenposition in der Gruppe Tabellenposition festlegen."
    },
   {
        "id": "UsageInstructions/CommunicationPlugins.htm", 
        "title": "Kommunikation während der Bearbeitung", 
        "body": "Im ONLYOFFICE Dokumenteneditor können Sie immer mit Kollegen in Kontakt bleiben und beliebte Online-Messenger wie Telegram und Rainbow nutzen. Telegram- und Rainbow-Plugins werden standardmäßig nicht installiert. Informationen zur Installation finden Sie im entsprechenden Artikel: Hinzufügen von Plugins zu den ONLYOFFICE Desktop Editoren Hinzufügen von Plugins zu ONLYOFFICE Cloud oder Hinzufügen neuer Plugins zu Server-Editoren . Telegram Um mit dem Chatten im Telegram-Plugin zu beginnen, Öffnen Sie die Registerkarte Plugins und klicken Sie auf Telegram. Geben Sie Ihre Telefonnummer in das entsprechende Feld ein. Aktivieren Sie das Kontrollkästchen Keep me signed in, wenn Sie die Anmeldeinformationen für die aktuelle Sitzung speichern möchten, und klicken Sie auf die Schaltfläche Next. Geben Sie den erhaltenen Code in Ihre Telegram-App ein oder Melden Sie sich mit dem QR-Code an. Öffnen Sie die Telegram-App auf Ihrem Telefon. Gehen Sie zu Einstellungen > Geräte > QR scannen, Scannen Sie das Bild, um sich anzumelden. Jetzt können Sie Telegram für Instant Messaging innerhalb der ONLYOFFICE-Editor-Oberfläche verwenden. Rainbow Um mit dem Chatten im Rainbow-Plugin zu beginnen, Öffnen Sie die Registerkarte Plugins und klicken Sie auf Rainbow. Registrieren Sie ein neues Konto, indem Sie auf die Schaltfläche Sign up klicken, oder melden Sie sich bei einem bereits erstellten Konto an. Geben Sie dazu Ihre E-Mail in das entsprechende Feld ein und klicken Sie auf Continue. Geben Sie dann Ihr Kontopasswort ein. Aktivieren Sie das Kontrollkästchen Keep my session alive, wenn Sie die Anmeldeinformationen für die aktuelle Sitzung speichern möchten, und klicken Sie auf die Schaltfläche Connect. Jetzt sind Sie fertig und können gleichzeitig in Rainbow chatten und in der ONLYOFFICE-Editor-Oberfläche arbeiten."
    },
   {
        "id": "UsageInstructions/ConvertFootnotesEndnotes.htm", 
        "title": "Fußnoten und Endnoten konvertieren", 
        "body": "Der ONLYOFFICE Dokumenteneditor ermöglicht das schnelle Konvertieren von Fuß- und Endnoten und umgekehrt, z. B. wenn Sie sehen, dass einige Fußnoten im resultierenden Dokument am Ende platziert werden sollten. Verwenden Sie das entsprechende Tool für eine mühelose Konvertierung, anstatt sie als Endnoten neu zu erstellen. Klicken Sie auf den Pfeil neben dem Symbol Fußnote auf der Registerkarte Verweise in der oberen Symbolleiste, Bewegen Sie den Mauszeiger über den Menüpunkt Alle Anmerkungen konvertieren und wählen Sie eine der Optionen aus der Liste rechts aus: Alle Fußnoten in Endnoten konvertieren, um alle Fußnoten in Endnoten zu konvertieren; Alle Endnoten in Fußnoten konvertieren, um alle Endnoten in Fußnoten zu konvertieren; Fußnoten und Endnoten wechseln, um alle Fußnoten in Endnoten und alle Endnoten in Fußnoten zu konvertieren."
    },
   {
        "id": "UsageInstructions/CopyClearFormatting.htm", 
        "title": "Textformatierung übernehmen/entfernen", 
        "body": "Kopieren einer bestimmte Textformatierung im Dokumenteneditor: Wählen Sie mit der Maus oder mithilfe der Tastatur den Textabschnitt aus, dessen Formatierung Sie kopieren möchten. Klicken Sie in der oberen Symbolleiste unter der Registerkarte Start auf das Symbol Format übertragen (der Mauszeiger ändert sich wie folgt ). Wählen Sie einen Textabschnitt, auf den Sie die Formatierung übertragen möchten. Übertragung der Formatierung auf mehrere Textabschnitte: Wählen Sie mit der Maus oder mithilfe der Tastatur den Textabschnitt aus, dessen Formatierung Sie kopieren möchten. Führen Sie in der oberen Symbolleiste unter der Registerkarte Startseite einen Doppelklick auf das Symbol Format übertragen aus (der Mauszeiger ändert sich wie folgt und das Symbol Format übertragen bleibt ausgewählt: ). Markieren Sie die gewünschten Textabschnitte Schritt für Schritt, um die Formatierung zu übertragen. Wenn Sie den Modus beenden möchten, klicken Sie erneut auf das Symbol Format übertragen oder drücken Sie die ESC-Taste auf Ihrer Tastatur. Wenn Sie die angewandte Formatierung wieder entfernen möchten, markieren Sie den entsprechenden Textabschnitt und klicken Sie auf der oberen Symbolleiste, unter der Registerkarte Start auf das Symbol Formatierung löschen ."
    },
   {
        "id": "UsageInstructions/CopyPasteUndoRedo.htm", 
        "title": "Textpassagen kopieren/einfügen, Vorgänge rückgängig machen/wiederholen", 
        "body": "Zwischenablage verwenden Um Textpassagen und eingefügte Objekte (AutoFormen, Bilder, Diagramme) innerhalb des aktuellen Dokuments auszuschneiden im Dokumenteneditor, zu kopieren und einzufügen, verwenden Sie die entsprechenden Optionen aus dem Rechtsklickmenü oder die Symbole, die auf jeder Registerkarte der oberen Symbolleiste verfügbar sind: Ausschneiden - wählen Sie eine Textpassage oder ein Objekt aus und nutzen Sie die Option Ausschneiden im Rechtsklickmenü oder das Symbol Ausschneiden in der oberen Symbolleiste, um die Auswahl zu löschen und in der Zwischenablage des Rechners zu speichern. Die ausgeschnittenen Daten können später an einer anderen Stelle im selben Dokument wieder eingefügt werden. Kopieren – wählen Sie eine Textpassage oder ein Objekt aus und nutzen Sie die Option Kopieren im Rechtsklickmenü oder das Symbol Kopieren in der oberen Symbolleiste, um die Auswahl in der Zwischenablage des Rechners zu speichern. Die kopierten Daten können später an einer anderen Stelle im selben Dokument wieder eingefügt werden. Einfügen – platzieren Sie den Cursor an der Stelle im Dokument, an der Sie den zuvor kopierten Text/das Objekt einfügen möchten und wählen Sie im Rechtsklickmenü die Option Einfügen oder klicken Sie in der oberen Symbolleiste auf Einfügen . Der Text/das Objekt wird an der aktuellen Cursorposition eingefügt. Die Daten können vorher aus demselben Dokument kopiert werden oder auch aus einem anderen Dokument oder Programm oder von einer Webseite. In der Online-Version können nur die folgenden Tastenkombinationen zum Kopieren oder Einfügen von Daten aus/in ein anderes Dokument oder ein anderes Programm verwendet werden. In der Desktop-Version können sowohl die entsprechenden Schaltflächen/Menüoptionen als auch Tastenkombinationen für alle Kopier-/Einfügevorgänge verwendet werden: STRG+X - Ausschneiden; STRG+C - Kopieren; STRG+V - Einfügen. Anstatt Text innerhalb desselben Dokuments auszuschneiden und einzufügen, können Sie die erforderliche Textpassage einfach auswählen und an die gewünschte Position ziehen und ablegen. Verwenden der Funktion Spezielles einfügen Für die gemeinsame Bearbeitung ist die Option Spezielles Einfügen ist nur im Co-Editing-Modus Formal verfügbar. Sobald der kopierte Text eingefügt ist, erscheint die Schaltfläche Spezielles Einfügen neben der eingefügten Textpassage. Klicken Sie auf diese Schaltfläche, um die erforderliche Einfügeoption auszuwählen, oder verwenden Sie die Strg-Taste in Kombination mit der Buchstabentaste, die in den Klammern neben der erforderlichen Option angegeben ist. Zum Einfügen von Textsegmenten oder Text in Verbindung mit AutoFormen sind folgende Optionen verfügbar: Ursprüngliche Formatierung beibehalten (Strg+K) - der kopierte Text wird in Originalformatierung eingefügt. Nur Text beibehalten (Strg+T) - der kopierte Text wird in an die vorhandene Formatierung angepasst. Wenn Sie die kopierte Tabelle in eine vorhandene Tabelle einfügen, sind die folgenden Optionen verfügbar: Zellen überschreiben (Strg+O) - vorhandenen Tabelleninhalt durch die eingefügten Daten ersetzen. Diese Option ist standardmäßig ausgewählt. Geschachtelt (Strg+N) die kopierte Tabelle wird als geschachtelte Tabelle in die ausgewählte Zelle der vorhandenen Tabelle eingefügt. Nur Text beibehalten (Strg+T) - die Tabelleninhalte werden als Textwerte eingefügt, die durch das Tabulatorzeichen getrennt sind. Wenn Sie eine Tabelle kopieren und in eine bereits vorhandene Tabelle einfügen, stehen Ihnen folgende Optionen zur Verfügung: Zellen überschreiben (Strg+O) - ermöglicht das Ersetzen des Inhalts der vorhandenen Tabelle durch die kopierten Daten. Diese Option ist standardmäßig ausgewählt. Tabelle schachteln (Strg+N) - ermöglicht das Einfügen der kopierten Tabelle als verschachtelte Tabelle in die ausgewählte Zelle der vorhandenen Tabelle. Nur Text beibehalten (Strg+T) - ermöglicht das Einfügen der Tabelleninhalte als Textwerte, die durch das Tabulatorzeichen getrennt sind. Um das automatische Erscheinen der Schaltfläche Spezielles Einfügen nach dem Einfügen zu aktivieren/deaktivieren, gehen Sie zur Registerkarte Datei > Erweiterte Einstellungen und aktivieren/deaktivieren Sie das Kontrollkästchen Die Schaltfläche Einfügeoptionen beim Einfügen von Inhalten anzeigen. Vorgänge rückgängig machen/wiederholen Verwenden Sie die entsprechenden Symbole, um Vorgänge rückgängig zu machen/zu wiederholen oder nutzen Sie die entsprechenden Tastenkombinationen: Rückgängig – klicken Sie im linken Teil der Kopfzeile des Editors auf das Symbol Rückgängig oder verwenden Sie die Tastenkombination STRG+Z, um die zuletzt durchgeführte Aktion rückgängig zu machen. Wiederholen – klicken Sie im linken Teil der Kopfzeile des Editors auf das Symbol Wiederholen oder verwenden Sie die Tastenkombination STRG+Y, um die zuletzt durchgeführte Aktion zu wiederholen. Wenn Sie ein Dokument im Modus Schnell gemeinsam bearbeiten, ist die Option letzten rückgängig gemachten Vorgang wiederherstellen nicht verfügbar."
    },
   {
        "id": "UsageInstructions/CreateFillableForms.htm", 
        "title": "Ausfüllbare Formulare erstellen", 
        "body": "Mit dem ONLYOFFICE Dokumenteneditor können Sie ausfüllbare Formulare in Ihren Dokumenten erstellen, z.B. Vertragsentwürfe oder Umfragen. Formularvorlage ist das DOCXF-Format, das eine Reihe von Tools zum Erstellen eines ausfüllbaren Formulars bietet. Speichern Sie das resultierende Formular als DOCXF-Datei, und Sie haben eine Formularvorlage, die Sie noch bearbeiten, überprüfen oder gemeinsam bearbeiten können. Um eine Formularvorlage ausfüllbar zu machen und die Dateibearbeitung durch andere Benutzer einzuschränken, speichern Sie sie als OFORM-Datei. Weitere Informationen finden Sie in den Anleitungen zum Ausfüllen von Formularen. DOCXF und OFORM sind neue ONLYOFFICE-Formate, die es ermöglichen, Formularvorlagen zu erstellen und Formulare auszufüllen. Verwenden Sie den ONLYOFFICE Dokumenteneditor entweder online oder auf dem Desktop, um alle formularbezogenen Elemente und Optionen zu nutzen. Sie können auch jede vorhandene DOCX-Datei als DOCXF speichern, um sie als Formularvorlage zu verwenden. Gehen Sie zur Registerkarte Datei, klicken Sie im linken Menü auf die Option Herunterladen als... oder Speichern als... und wählen Sie die Option DOCXF. Jetzt können Sie alle verfügbaren Formularbearbeitungsfunktionen verwenden, um ein Formular zu erstellen. Sie können nicht nur die Formularfelder in einer DOCXF-Datei bearbeiten sondern auch Text hinzufügen, bearbeiten und formatieren oder andere Funktionen des Dokumenteneditors verwenden. Das Erstellen ausfüllbarer Formulare wird durch vom Benutzer bearbeitbare Objekte ermöglicht, die die Gesamtkonsistenz der resultierenden Dokumente sicherstellen und eine erweiterte Formularinteraktion ermöglichen. Derzeit können Sie bearbeitbare Textfelder, Comboboxen, Dropdown-Listen, Kontrollkästchen, Radiobuttons einfügen und den Bildern bestimmte Bereiche zuweisen, sowie E-Mail-Adresse-, Telefonnummer-, Datum und Uhrzeit-, Postleitzahl-, Kreditkarte- und komplexe Felder erstellen. Greifen Sie auf diese Funktionen auf der Registerkarte Formulare zu, die nur für DOCXF-Dateien verfügbar ist. Erstellen eines neuen Textfelds Textfelder sind vom Benutzer bearbeitbare Text-Formularfelder. Es können keine weiteren Objekte hinzugefügt werden. Um ein Textfeld einzufügen, positionieren Sie die Einfügemarke innerhalb einer Textzeile an der Stelle, an der das Feld hinzugefügt werden soll, wechseln Sie zur Registerkarte Formulare der oberen Symbolleiste, klicken Sie auf das Symbol Textfeld oder Klicken Sie auf den Pfeil neben dem Symbol Textfeld und wählen Sie aus, ob Sie ein Inline-Textfeld oder ein festes Textfeld einfügen möchten. Um mehr über feste Felder zu erfahren, lesen Sie bitte den Absatz Felder mit fester Größe in diesem Abschnitt weiter unten. Das Formularfeld erscheint an der Einfügemarke innerhalb der bestehenden Textzeile. Das Menü Einstellungen des Formulars wird rechts geöffnet. Wer soll das Feld ausfüllen?: Wählen Sie die Rolle über die Dropdown-Liste aus, um die Benutzergruppe mit Zugriff auf dieses Feld festzulegen. Um mehr über das Zuweisen von Rollen zu erfahren, lesen Sie bitte den Abschnitt Verwalten von Rollen in dieser Anleitung. Schlüssel: Die Option zum Gruppieren von Feldern zum gleichzeitigen Ausfüllen. Um einen neuen Schlüssel zu erstellen, geben Sie seinen Namen in das Feld ein und drücken Sie die Eingabetaste, und weisen Sie dann jedem Textfeld den erforderlichen Schlüssel mithilfe der Dropdown-Liste zu. Die Meldung Verbundene Felder: 2/3/... wird angezeigt. Um die Felder zu trennen, klicken Sie auf die Schaltfläche Verbindung trennen. Platzhalter: Geben Sie den anzuzeigenden Text in das eingefügte Textfeld ein; \"Hier den Text eingeben\" ist standardmäßig eingestellt. Tag: Geben Sie den Text ein, der als Tag für den internen Gebrauch verwendet werden soll, d. h. nur für Mitbearbeiter angezeigt wird. Tipp: Geben Sie den Text ein, der als Tipp angezeigt werden soll, wenn ein Benutzer den Mauszeiger über das Textfeld fährt. Format: Wählen Sie das Inhaltsformat des Felds, d. h. Keine, Ziffern, Buchstaben, Beliebige Maske oder Regulärer Ausdruck. Das Feld ist standardmäßig auf Beliebige Maske eingestellt. Um das Format zu ändern, geben Sie die erforderliche Maske in das Feld darunter ein. Wenn Sie ein Beliebige Maske- oder ein Regulärer Ausdruck-Format wählen, erscheint ein zusätzliches Feld unter dem Feld Format. Erlaubte Symbole: Geben Sie die Symbole ein, die im Textfeld erlaubt sind. Feste Feldgröße: Aktivieren Sie dieses Kontrollkästchen, um ein Feld mit einer festen Größe zu erstellen. Wenn diese Option aktiviert ist, können Sie auch die Einstellungen Automatisch anpassen und/oder Mehrzeiliges Feld verwenden. Ein Feld mit fester Größe sieht wie eine Autoform aus. Sie können einen Umbruchstil dafür festlegen und die Position anpassen. Automatisch anpassen: Diese Option kann aktiviert werden, wenn die Einstellung Feste Feldgröße ausgewählt ist. Aktivieren Sie sie, um die Schriftgröße automatisch an die Feldgröße anzupassen. Mehrzeiliges Feld: Diese Option kann aktiviert werden, wenn die Einstellung Feste Feldgröße ausgewählt ist. Aktivieren Sie sie, um ein Formularfeld mit mehreren Zeilen zu erstellen, andernfalls belegt der Text eine einzelne Zeile. Zeichengrenze: Standardmäßig gibt es keine Grenzen. Aktivieren Sie dieses Kontrollkästchen, um die maximale Zeichenanzahl im Feld rechts festzulegen. Zeichenanzahl in Textfeld: Verteilen Sie den Text gleichmäßig innerhalb des eingefügten Textfelds und konfigurieren Sie seine allgemeine Darstellung. Lassen Sie das Kontrollkästchen deaktiviert, um die Standardeinstellungen beizubehalten, oder aktivieren Sie es, um die folgenden Parameter festzulegen: Zeilenbreite: Wählen Sie, ob der Breitenwert Auto (Breite wird automatisch berechnet), Mindestens (Breite ist nicht kleiner als der manuell angegebene Wert) oder Genau sein soll (Breite entspricht dem manuell eingegebenen Wert). Der darin enthaltene Text wird entsprechend ausgerichtet. Rahmenfarbe: Klicken Sie auf das Symbol , um die Farbe für die Ränder des eingefügten Textfelds festzulegen. Wählen Sie die bevorzugte Rahmenfarbe aus der Palette. Sie können bei Bedarf eine neue benutzerdefinierte Farbe hinzufügen. Hintergrundfarbe: Klicken Sie auf das Symbol , um dem eingefügten Textfeld eine Hintergrundfarbe zuzuweisen. Wählen Sie die bevorzugte Farbe aus Designfarben, Standardfarben oder fügen Sie bei Bedarf eine neue benutzerdefinierte Farbe hinzu. Erforderlich: Aktivieren Sie dieses Kontrollkästchen, damit das Feld für das Textfeld zwingend ausgefüllt werden muss. Klicken Sie in das eingefügte Textfeld und passen Sie Schriftart, Größe und Farbe an, wenden Sie Dekorationsstile und Formatierungsvorgaben an. Die Formatierung wird auf den gesamten Text innerhalb des Felds angewendet. Erstellen einer neuen Combobox Comboboxen enthalten eine Dropdown-Liste mit einer Reihe von Auswahlmöglichkeiten, die von Benutzern bearbeitet werden können. Um eine Combobox einzufügen, positionieren Sie die Einfügemarke innerhalb einer Textzeile an der Stelle, an der das Feld hinzugefügt werden soll, wechseln Sie zur Registerkarte Formulare der oberen Symbolleiste, klicken Sie auf das Symbol Combobox. Das Formularfeld erscheint an der Einfügemarke innerhalb der bestehenden Textzeile. Das Menü Einstellungen des Formulars wird rechts geöffnet. Wer soll das Feld ausfüllen?: Wählen Sie die Rolle über die Dropdown-Liste aus, um die Benutzergruppe mit Zugriff auf dieses Feld festzulegen. Um mehr über das Zuweisen von Rollen zu erfahren, lesen Sie bitte den Abschnitt Verwalten von Rollen in dieser Anleitung. Schlüssel: Die Option zum Gruppieren von Comboboxen zum gleichzeitigen Ausfüllen. Um einen neuen Schlüssel zu erstellen, geben Sie seinen Namen in das Feld ein und drücken Sie die Eingabetaste, und weisen Sie dann jedem Feld den erforderlichen Schlüssel mithilfe der Dropdown-Liste zu. Die Meldung Verbundene Felder: 2/3/... wird angezeigt. Um die Felder zu trennen, klicken Sie auf die Schaltfläche Verbindung trennen. Platzhalter: Geben Sie den Text ein, der in der eingefügten Combobox angezeigt werden soll; \"Wählen Sie ein Element aus\" ist standardmäßig eingestellt. Tag: Geben Sie den Text ein, der als Tag für den internen Gebrauch verwendet werden soll, d. h. nur für Mitbearbeiter angezeigt wird. Tipp: Geben Sie den Text ein, der als Tipp angezeigt werden soll, wenn ein Benutzer den Mauszeiger über die Combobox bewegt. Optionen von Werten: Fügen Sie neue Werte hinzu, löschen Sie sie oder verschieben Sie sie nach oben oder unten in der Liste. Feste Feldgröße: Aktivieren Sie dieses Kontrollkästchen, um ein Feld mit einer festen Größe zu erstellen. Ein Feld mit fester Größe sieht wie eine Autoform aus. Sie können einen Umbruchstil dafür festlegen und die Position anpassen. Rahmenfarbe: Klicken Sie auf das Symbol , um die Farbe für die Ränder der eingefügten Combobox festzulegen. Wählen Sie die bevorzugte Rahmenfarbe aus der Palette. Sie können bei Bedarf eine neue benutzerdefinierte Farbe hinzufügen. Hintergrundfarbe: Klicken Sie auf das Symbol , um der eingefügten Combobox eine Hintergrundfarbe zuzuweisen. Wählen Sie die bevorzugte Farbe aus Designfarben, Standardfarben oder fügen Sie bei Bedarf eine neue benutzerdefinierte Farbe hinzu. Erforderlich: Aktivieren Sie dieses Kontrollkästchen, damit das Feld für das Feld zwingend ausgefüllt werden muss. Sie können auf die Pfeilschaltfläche im rechten Teil der hinzugefügten Combobox klicken, um die Elementliste zu öffnen und das erforderliche Element auszuwählen. Sobald das erforderliche Element ausgewählt ist, können Sie den angezeigten Text ganz oder teilweise bearbeiten, indem Sie ihn durch Ihren Text ersetzen. Sie können Schriftdekoration, Farbe und Größe ändern. Klicken Sie in die eingefügte Combobox und fahren Sie gemäß den Anleitungen fort. Die Formatierung wird auf den gesamten Text innerhalb des Felds angewendet. Erstellen einer neuen Dropdown-Liste Dropdown-Liste enthalten eine Liste mit einer Reihe von Auswahlmöglichkeiten, die von den Benutzern nicht bearbeitet werden können. Um eine Dropdown-Liste einzufügen, positionieren Sie die Einfügemarke innerhalb einer Textzeile an der Stelle, an der das Feld hinzugefügt werden soll, wechseln Sie zur Registerkarte Formulare der oberen Symbolleiste, klicken Sie auf das Symbol Dropdown. Das Formularfeld erscheint an der Einfügemarke innerhalb der bestehenden Textzeile. Das Menü Einstellungen des Formulars wird rechts geöffnet. Wer soll das Feld ausfüllen?: Wählen Sie die Rolle über die Dropdown-Liste aus, um die Benutzergruppe mit Zugriff auf dieses Feld festzulegen. Um mehr über das Zuweisen von Rollen zu erfahren, lesen Sie bitte den Abschnitt Verwalten von Rollen in dieser Anleitung. Schlüssel: Die Option zum Gruppieren von Dropdown-Listen zum gleichzeitigen Ausfüllen. Um einen neuen Schlüssel zu erstellen, geben Sie seinen Namen in das Feld ein und drücken Sie die Eingabetaste, und weisen Sie dann jedem Feld den erforderlichen Schlüssel mithilfe der Dropdown-Liste zu. Die Meldung Verbundene Felder: 2/3/... wird angezeigt. Um die Felder zu trennen, klicken Sie auf die Schaltfläche Verbindung trennen. Platzhalter: Geben Sie den Text ein, der in der eingefügten Dropdown-Liste angezeigt werden soll; \"Wählen Sie ein Element aus\" ist standardmäßig eingestellt. Tag: Geben Sie den Text ein, der als Tag für den internen Gebrauch verwendet werden soll, d. h. nur für Mitbearbeiter angezeigt wird. Tipp: Geben Sie den Text ein, der als Tipp angezeigt werden soll, wenn ein Benutzer den Mauszeiger über die Dropdown-Liste bewegt. Optionen von Werten: Fügen Sie neue Werte hinzu, löschen Sie sie oder verschieben Sie sie nach oben oder unten in der Liste. Feste Feldgröße: Aktivieren Sie dieses Kontrollkästchen, um ein Feld mit einer festen Größe zu erstellen. Ein Feld mit fester Größe sieht wie eine Autoform aus. Sie können einen Umbruchstil dafür festlegen und die Position anpassen. Rahmenfarbe: Klicken Sie auf das Symbol , um die Farbe für die Ränder der eingefügten Dropdown-Liste festzulegen. Wählen Sie die bevorzugte Rahmenfarbe aus der Palette. Sie können bei Bedarf eine neue benutzerdefinierte Farbe hinzufügen. Hintergrundfarbe: Klicken Sie auf das Symbol , um der eingefügten Dropdown-Liste eine Hintergrundfarbe zuzuweisen. Wählen Sie die bevorzugte Farbe aus Designfarben, Standardfarben oder fügen Sie bei Bedarf eine neue benutzerdefinierte Farbe hinzu. Erforderlich: Aktivieren Sie dieses Kontrollkästchen, damit das Feld für das Feld zwingend ausgefüllt werden muss. Sie können auf die Pfeilschaltfläche im rechten Teil des hinzugefügten Formularfelds Dropdown-Liste klicken, um die Elementliste zu öffnen und die erforderliche Elemente auszuwählen. Erstellen eines neuen Kontrollkästchens Kontrollkästchen werden verwendet, um dem Benutzer eine Vielzahl von Optionen anzubieten, von denen eine beliebige Anzahl ausgewählt werden kann. Kontrollkästchen funktionieren einzeln, sodass sie unabhängig voneinander aktiviert oder deaktiviert werden können. Um ein Kontrollkästchen einzufügen, positionieren Sie die Einfügemarke innerhalb einer Textzeile an der Stelle, an der das Feld hinzugefügt werden soll, wechseln Sie zur Registerkarte Formulare der oberen Symbolleiste, klicken Sie auf das Symbol Kontrollkästchen. Das Formularfeld erscheint an der Einfügemarke innerhalb der bestehenden Textzeile. Das Menü Einstellungen des Formulars wird rechts geöffnet. Wer soll das Feld ausfüllen?: Wählen Sie die Rolle über die Dropdown-Liste aus, um die Benutzergruppe mit Zugriff auf dieses Feld festzulegen. Um mehr über das Zuweisen von Rollen zu erfahren, lesen Sie bitte den Abschnitt Verwalten von Rollen in dieser Anleitung. Schlüssel: Die Option zum Gruppieren von Kontrollkästchen zum gleichzeitigen Ausfüllen. Um einen neuen Schlüssel zu erstellen, geben Sie seinen Namen in das Feld ein und drücken Sie die Eingabetaste, und weisen Sie dann jedem Feld den erforderlichen Schlüssel mithilfe der Dropdown-Liste zu. Die Meldung Verbundene Felder: 2/3/... wird angezeigt. Um die Felder zu trennen, klicken Sie auf die Schaltfläche Verbindung trennen. Tag: Geben Sie den Text ein, der als Tag für den internen Gebrauch verwendet werden soll, d. h. nur für Mitbearbeiter angezeigt wird. Tipp: Geben Sie den Text ein, der als Tipp angezeigt werden soll, wenn ein Benutzer den Mauszeiger über das Kontrollkästchen bewegt. Feste Feldgröße: Aktivieren Sie dieses Kontrollkästchen, um ein Feld mit einer festen Größe zu erstellen. Ein Feld mit fester Größe sieht wie eine Autoform aus. Sie können einen Umbruchstil dafür festlegen und die Position anpassen. Rahmenfarbe: Klicken Sie auf das Symbol , um die Farbe für die Ränder des eingefügten Kontrollkästchens festzulegen. Wählen Sie die bevorzugte Rahmenfarbe aus der Palette. Sie können bei Bedarf eine neue benutzerdefinierte Farbe hinzufügen. Hintergrundfarbe: Klicken Sie auf das Symbol , um dem eingefügten Kontrollkästchen eine Hintergrundfarbe zuzuweisen. Wählen Sie die bevorzugte Farbe aus Designfarben, Standardfarben oder fügen Sie bei Bedarf eine neue benutzerdefinierte Farbe hinzu. Erforderlich: Aktivieren Sie dieses Kontrollkästchen, damit das Feld für das Feld zwingend ausgefüllt werden muss. Um das Kontrollkästchen zu aktivieren, klicken Sie einmal darauf. Erstellen eines neuen Radiobuttons Radiobuttons werden verwendet, um Benutzern eine Vielzahl von Optionen anzubieten, von denen nur eine Option ausgewählt werden kann. Radiobuttons können gruppiert werden, sodass nicht mehrere Schaltflächen innerhalb einer Gruppe ausgewählt werden müssen. Um einen Radiobutton einzufügen, positionieren Sie die Einfügemarke innerhalb einer Textzeile an der Stelle, an der das Feld hinzugefügt werden soll, wechseln Sie zur Registerkarte Formulare der oberen Symbolleiste, klicken Sie auf das Symbol Radiobutton. Das Formularfeld erscheint an der Einfügemarke innerhalb der bestehenden Textzeile. Das Menü Einstellungen des Formulars wird rechts geöffnet. Wer soll das Feld ausfüllen?: Wählen Sie die Rolle über die Dropdown-Liste aus, um die Benutzergruppe mit Zugriff auf dieses Feld festzulegen. Um mehr über das Zuweisen von Rollen zu erfahren, lesen Sie bitte den Abschnitt Verwalten von Rollen in dieser Anleitung. Gruppenschlüssel: Um eine neue Gruppe von Radiobuttons zu erstellen, geben Sie den Namen der Gruppe in das Feld ein und drücken Sie die Eingabetaste, und weisen Sie dann jedem Radiobutton die erforderliche Gruppe zu. Tag: Geben Sie den Text ein, der als Tag für den internen Gebrauch verwendet werden soll, d. h. nur für Mitbearbeiter angezeigt wird. Tipp: Geben Sie den Text ein, der als Tipp angezeigt werden soll, wenn ein Benutzer den Mauszeiger über den Radiobutton bewegt. Feste Feldgröße: Aktivieren Sie dieses Kontrollkästchen, um ein Feld mit einer festen Größe zu erstellen. Ein Feld mit fester Größe sieht wie eine Autoform aus. Sie können einen Umbruchstil dafür festlegen und die Position anpassen. Rahmenfarbe: Klicken Sie auf das Symbol , um die Farbe für die Ränder des eingefügten Radiobuttons festzulegen. Wählen Sie die bevorzugte Rahmenfarbe aus der Palette. Sie können bei Bedarf eine neue benutzerdefinierte Farbe hinzufügen. Hintergrundfarbe: Klicken Sie auf das Symbol , um dem eingefügten Radiobutton eine Hintergrundfarbe zuzuweisen. Wählen Sie die bevorzugte Farbe aus Designfarben, Standardfarben oder fügen Sie bei Bedarf eine neue benutzerdefinierte Farbe hinzu. Erforderlich: Aktivieren Sie dieses Kontrollkästchen, damit das Feld für das Feld zwingend ausgefüllt werden muss. Um den Radiobutton zu aktivieren, klicken Sie einmal darauf. Erstellen eines neuen Bild-Felds Bilder sind Formularfelder, die das Einfügen eines Bildes mit den von Ihnen festgelegten Einschränkungen, d. h. der Position des Bildes oder seiner Größe, ermöglichen. Um ein Bildformularfeld einzufügen, positionieren Sie die Einfügemarke innerhalb einer Textzeile an der Stelle, an der das Feld hinzugefügt werden soll, wechseln Sie zur Registerkarte Formulare der oberen Symbolleiste, klicken Sie auf das Symbol Bild. Das Formularfeld erscheint an der Einfügemarke innerhalb der bestehenden Textzeile. Das Menü Einstellungen des Formulars wird rechts geöffnet. Wer soll das Feld ausfüllen?: Wählen Sie die Rolle über die Dropdown-Liste aus, um die Benutzergruppe mit Zugriff auf dieses Feld festzulegen. Um mehr über das Zuweisen von Rollen zu erfahren, lesen Sie bitte den Abschnitt Verwalten von Rollen in dieser Anleitung. Schlüssel: Die Option zum Gruppieren von Bildern zum gleichzeitigen Ausfüllen. Um einen neuen Schlüssel zu erstellen, geben Sie seinen Namen in das Feld ein und drücken Sie die Eingabetaste, und weisen Sie dann jedem Feld den erforderlichen Schlüssel mithilfe der Dropdown-Liste zu. Die Meldung Verbundene Felder: 2/3/... wird angezeigt. Um die Felder zu trennen, klicken Sie auf die Schaltfläche Verbindung trennen. Platzhalter: Geben Sie den Text ein, der in das eingefügte Bildformularfeld angezeigt werden soll; \"Klicken Sie, um das Bild herunterzuladen\" ist standardmäßig eingestellt. Tag: Geben Sie den Text ein, der als Tag für den internen Gebrauch verwendet werden soll, d. h. nur für Mitbearbeiter angezeigt wird. Tipp: Geben Sie den Text ein, der als Tipp angezeigt werden soll, wenn ein Benutzer seinen Mauszeiger über den unteren Bildrand bewegt. Wann skalieren: Klicken Sie auf das Dropdown-Menü und wählen Sie eine geeignete Bildskalierungsoption: Immer, Nie, Das Bild ist zu groß oder Das Bild ist zu klein. Das ausgewählte Bild wird innerhalb des Felds entsprechend skaliert. Seitenverhältnis sperren: Aktivieren Sie dieses Kontrollkästchen, um das Bildseitenverhältnis ohne Verzerrung beizubehalten. Wenn das Kontrollkästchen aktiviert ist, verwenden Sie den vertikalen und den horizontalen Schieberegler, um das Bild innerhalb des eingefügten Felds zu positionieren. Die Positionierungsschieber sind inaktiv, wenn das Kontrollkästchen deaktiviert ist. Bild auswählen: Klicken Sie auf diese Schaltfläche, um ein Bild entweder Aus einer Datei, Aus einer URL oder Aus dem Speicher hochzuladen. Rahmenfarbe: Klicken Sie auf das Symbol , um die Farbe für die Ränder der eingefügten Bild festzulegen. Wählen Sie die bevorzugte Rahmenfarbe aus der Palette. Sie können bei Bedarf eine neue benutzerdefinierte Farbe hinzufügen. Hintergrundfarbe: Klicken Sie auf das Symbol , um der eingefügten Bild eine Hintergrundfarbe zuzuweisen. Wählen Sie die bevorzugte Farbe aus Designfarben, Standardfarben oder fügen Sie bei Bedarf eine neue benutzerdefinierte Farbe hinzu. Erforderlich: Aktivieren Sie dieses Kontrollkästchen, damit das Feld für das Feld zwingend ausgefüllt werden muss. Um das Bild zu ersetzen, klicken Sie auf das   Bildsymbol über dem Rahmen des Formularfelds und wählen Sie ein anderes Bild aus. Um die Bildeinstellungen anzupassen, öffnen Sie die Registerkarte Bildeinstellungen in der rechten Symbolleiste. Um mehr zu erfahren, lesen Sie bitte die Anleitung zu Bildeinstellungen. Erstellen eines neuen E-Mail-Adresse-Felds Das Feld E-Mail-Adresse wird verwendet, um eine E-Mail-Adresse einzugeben, die einem regulären Ausdruck \\S+@\\S+\\.\\S+ entspricht. Um ein neues E-Mail-Adresse-Feld einzufügen, positionieren Sie die Einfügemarke innerhalb einer Textzeile an der Stelle, an der das Feld hinzugefügt werden soll, wechseln Sie zur Registerkarte Formulare der oberen Symbolleiste, klicken Sie auf das Symbol E-Mail-Adresse. Das Formularfeld erscheint an der Einfügemarke innerhalb der bestehenden Textzeile. Das Menü Einstellungen des Formulars wird rechts geöffnet. Wer soll das Feld ausfüllen?: Wählen Sie die Rolle über die Dropdown-Liste aus, um die Benutzergruppe mit Zugriff auf dieses Feld festzulegen. Um mehr über das Zuweisen von Rollen zu erfahren, lesen Sie bitte den Abschnitt Verwalten von Rollen in dieser Anleitung. Schlüssel: Die Option zum Gruppieren von E-Mail-Adresse-Felder zum gleichzeitigen Ausfüllen. Um einen neuen Schlüssel zu erstellen, geben Sie seinen Namen in das Feld ein und drücken Sie die Eingabetaste, und weisen Sie dann jedem Feld den erforderlichen Schlüssel mithilfe der Dropdown-Liste zu. Platzhalter: Geben Sie den Text ein, der in das eingefügte E-Mail-Adresse-Feld angezeigt werden soll; “<EMAIL>” ist standardmäßig eingestellt. Tag: Geben Sie den Text ein, der als Tag für den internen Gebrauch verwendet werden soll, d. h. nur für Mitbearbeiter angezeigt wird. Tipp: Geben Sie den Text ein, der als Tipp angezeigt werden soll, wenn ein Benutzer seinen Mauszeiger über die E-Mail-Adresse bewegt. Format: Wählen Sie das Inhaltsformat des Felds, d. h. Keine, Ziffern, Buchstaben, Beliebige Maske oder Regulärer Ausdruck. Das Feld ist standardmäßig auf Beliebige Maske eingestellt. Um das Format zu ändern, geben Sie die erforderliche Maske in das Feld darunter ein. Erlaubte Symbole: Geben Sie die Symbole ein, die im E-Mail-Adresse-Feld erlaubt sind. Feste Feldgröße: Aktivieren Sie dieses Kontrollkästchen, um ein Feld mit einer festen Größe zu erstellen. Wenn diese Option aktiviert ist, können Sie auch die Einstellungen Automatisch anpassen und/oder Mehrzeiliges Feld verwenden. Ein Feld mit fester Größe sieht wie eine Autoform aus. Sie können einen Umbruchstil dafür festlegen und die Position anpassen. Automatisch anpassen: Diese Option kann aktiviert werden, wenn die Einstellung Feste Feldgröße ausgewählt ist. Aktivieren Sie sie, um die Schriftgröße automatisch an die Feldgröße anzupassen. Mehrzeiliges Feld: Diese Option kann aktiviert werden, wenn die Einstellung Feste Feldgröße ausgewählt ist. Aktivieren Sie sie, um ein Formularfeld mit mehreren Zeilen zu erstellen, andernfalls belegt der Text eine einzelne Zeile. Zeichengrenze: Standardmäßig gibt es keine Grenzen. Aktivieren Sie dieses Kontrollkästchen, um die maximale Zeichenanzahl im Feld rechts festzulegen. Zeichenanzahl in Textfeld: Verteilen Sie den Text gleichmäßig innerhalb des eingefügten E-Mail-Adresse-Felds und konfigurieren Sie seine allgemeine Darstellung. Lassen Sie das Kontrollkästchen deaktiviert, um die Standardeinstellungen beizubehalten, oder aktivieren Sie es, um die folgenden Parameter festzulegen: Zeilenbreite: Wählen Sie, ob der Breitenwert Auto (Breite wird automatisch berechnet), Mindestens (Breite ist nicht kleiner als der manuell angegebene Wert) oder Genau sein soll (Breite entspricht dem manuell eingegebenen Wert). Der darin enthaltene Text wird entsprechend ausgerichtet. Rahmenfarbe: Klicken Sie auf das Symbol , um die Farbe für die Ränder des eingefügten E-Mail-Adresse-Felds festzulegen. Wählen Sie die bevorzugte Rahmenfarbe aus der Palette. Sie können bei Bedarf eine neue benutzerdefinierte Farbe hinzufügen. Hintergrundfarbe: Klicken Sie auf das Symbol , um dem eingefügten E-Mail-Adresse-Feld eine Hintergrundfarbe zuzuweisen. Wählen Sie die bevorzugte Farbe aus Designfarben, Standardfarben oder fügen Sie bei Bedarf eine neue benutzerdefinierte Farbe hinzu. Erforderlich: Aktivieren Sie dieses Kontrollkästchen, damit das E-Mail-Adresse-Feld zwingend ausgefüllt werden muss. Erstellen eines neuen Telefonnummernfelds Das Feld Telefonnummer wird verwendet, um eine Telefonnummer einzugeben, die einer beliebigen Maske entspricht, die vom Ersteller des Formulars vorgegeben wurde. Es ist standardmäßig auf (999)999-9999 eingestellt. Um ein Telefonnummernfeld einzufügen, positionieren Sie die Einfügemarke innerhalb einer Textzeile an der Stelle, an der das Feld hinzugefügt werden soll, wechseln Sie zur Registerkarte Formulare der oberen Symbolleiste, klicken Sie auf das Symbol Telefonnummer. Das Formularfeld erscheint an der Einfügemarke innerhalb der bestehenden Textzeile. Das Menü Einstellungen des Formulars wird rechts geöffnet. Wer soll das Feld ausfüllen?: Wählen Sie die Rolle über die Dropdown-Liste aus, um die Benutzergruppe mit Zugriff auf dieses Feld festzulegen. Um mehr über das Zuweisen von Rollen zu erfahren, lesen Sie bitte den Abschnitt Verwalten von Rollen in dieser Anleitung. Schlüssel: Die Option zum Gruppieren von Telefonnummernfelder zum gleichzeitigen Ausfüllen. Um einen neuen Schlüssel zu erstellen, geben Sie seinen Namen in das Feld ein und drücken Sie die Eingabetaste, und weisen Sie dann jedem Feld den erforderlichen Schlüssel mithilfe der Dropdown-Liste zu. Platzhalter: Geben Sie den Text ein, der in das eingefügte Telefonnummernfeld angezeigt werden soll; “(999)999-9999” ist standardmäßig eingestellt. Tag: Geben Sie den Text ein, der als Tag für den internen Gebrauch verwendet werden soll, d. h. nur für Mitbearbeiter angezeigt wird. Tipp: Geben Sie den Text ein, der als Tipp angezeigt werden soll, wenn ein Benutzer seinen Mauszeiger über die E-Mail-Adresse bewegt. Format: Wählen Sie das Inhaltsformat des Felds, d. h. Keine, Ziffern, Buchstaben, Beliebige Maske oder Regulärer Ausdruck. Das Feld ist standardmäßig auf Beliebige Maske eingestellt. Um das Format zu ändern, geben Sie die erforderliche Maske in das Feld darunter ein. Erlaubte Symbole: Geben Sie die Symbole ein, die im Telefonnummernfeld erlaubt sind. Feste Feldgröße: Aktivieren Sie dieses Kontrollkästchen, um ein Feld mit einer festen Größe zu erstellen. Wenn diese Option aktiviert ist, können Sie auch die Einstellungen Automatisch anpassen und/oder Mehrzeiliges Feld verwenden. Ein Feld mit fester Größe sieht wie eine Autoform aus. Sie können einen Umbruchstil dafür festlegen und die Position anpassen. Automatisch anpassen: Diese Option kann aktiviert werden, wenn die Einstellung Feste Feldgröße ausgewählt ist. Aktivieren Sie sie, um die Schriftgröße automatisch an die Feldgröße anzupassen. Mehrzeiliges Feld: Diese Option kann aktiviert werden, wenn die Einstellung Feste Feldgröße ausgewählt ist. Aktivieren Sie sie, um ein Formularfeld mit mehreren Zeilen zu erstellen, andernfalls belegt der Text eine einzelne Zeile. Zeichengrenze: Standardmäßig gibt es keine Grenzen. Aktivieren Sie dieses Kontrollkästchen, um die maximale Zeichenanzahl im Feld rechts festzulegen. Zeichenanzahl in Textfeld: Verteilen Sie den Text gleichmäßig innerhalb des eingefügten Telefonnummernfelds und konfigurieren Sie seine allgemeine Darstellung. Lassen Sie das Kontrollkästchen deaktiviert, um die Standardeinstellungen beizubehalten, oder aktivieren Sie es, um die folgenden Parameter festzulegen: Zeilenbreite: Wählen Sie, ob der Breitenwert Auto (Breite wird automatisch berechnet), Mindestens (Breite ist nicht kleiner als der manuell angegebene Wert) oder Genau sein soll (Breite entspricht dem manuell eingegebenen Wert). Der darin enthaltene Text wird entsprechend ausgerichtet. Rahmenfarbe: Klicken Sie auf das Symbol , um die Farbe für die Ränder des eingefügten Telefonnummernfelds festzulegen. Wählen Sie die bevorzugte Rahmenfarbe aus der Palette. Sie können bei Bedarf eine neue benutzerdefinierte Farbe hinzufügen. Hintergrundfarbe: Klicken Sie auf das Symbol , um dem eingefügten Telefonnummernfeld eine Hintergrundfarbe zuzuweisen. Wählen Sie die bevorzugte Farbe aus Designfarben, Standardfarben oder fügen Sie bei Bedarf eine neue benutzerdefinierte Farbe hinzu. Erforderlich: Aktivieren Sie dieses Kontrollkästchen, damit das Telefonnummernfeld zwingend ausgefüllt werden muss. Erstellen eines neuen Datum und Zeit-Felds Das Feld Datum und Uhrzeit wird verwendet, um ein Datum einzufügen. Das Datum ist standardmäßig auf TT-MM-JJJJ eingestellt. Um ein Datum und Zeit-Feld einzufügen, positionieren Sie die Einfügemarke innerhalb einer Textzeile an der Stelle, an der das Feld hinzugefügt werden soll, wechseln Sie zur Registerkarte Formulare der oberen Symbolleiste, klicken Sie auf das Symbol Datum und Zeit. Das Formularfeld erscheint an der Einfügemarke innerhalb der bestehenden Textzeile. Um ein Datum einzugeben, klicken Sie auf den Dropdown-Pfeil innerhalb des Felds und wählen Sie das gewünschte Datum über den Kalender aus. Erstellen eines neuen Zip Code-Felds Das Feld Zip Code wird verwendet, um eine Postleitzahl einzugeben, die einer beliebigen Maske entspricht, die vom Ersteller des Formulars angegeben wurde. Die Maske ist standardmäßig auf 99999-9999 eingestellt. Um ein Postleitzahlenfeld einzufügen, positionieren Sie die Einfügemarke innerhalb einer Textzeile an der Stelle, an der das Feld hinzugefügt werden soll, wechseln Sie zur Registerkarte Formulare der oberen Symbolleiste, klicken Sie auf das Symbol Zip Code. Das Formularfeld erscheint an der Einfügemarke innerhalb der bestehenden Textzeile. Das Menü Einstellungen des Formulars wird rechts geöffnet. Wer soll das Feld ausfüllen?: Wählen Sie die Rolle über die Dropdown-Liste aus, um die Benutzergruppe mit Zugriff auf dieses Feld festzulegen. Um mehr über das Zuweisen von Rollen zu erfahren, lesen Sie bitte den Abschnitt Verwalten von Rollen in dieser Anleitung. Schlüssel: Die Option zum Gruppieren von Postleitzahlenfelder zum gleichzeitigen Ausfüllen. Um einen neuen Schlüssel zu erstellen, geben Sie seinen Namen in das Feld ein und drücken Sie die Eingabetaste, und weisen Sie dann jedem Feld den erforderlichen Schlüssel mithilfe der Dropdown-Liste zu. Die Meldung Verbundene Felder: 2/3/... wird angezeigt. Um die Felder zu trennen, klicken Sie auf die Schaltfläche Verbindung trennen. Platzhalter: Geben Sie den Text ein, der in das eingefügte Zip Code-Formularfeld angezeigt werden soll; \"Klicken Sie, um das Zip Code herunterzuladen\" ist standardmäßig eingestellt. Tag: Geben Sie den Text ein, der als Tag für den internen Gebrauch verwendet werden soll, d. h. nur für Mitbearbeiter angezeigt wird. Tipp: Geben Sie den Text ein, der als Tipp angezeigt werden soll, wenn ein Benutzer seinen Mauszeiger über das Zip Code-Feld bewegt. Format: Wählen Sie das Inhaltsformat des Felds, d. h. Keine, Ziffern, Buchstaben, Beliebige Maske oder Regulärer Ausdruck. Das Feld ist standardmäßig auf Beliebige Maske eingestellt. Um das Format zu ändern, geben Sie die erforderliche Maske in das Feld darunter ein. Erlaubte Symbole: Geben Sie die Symbole ein, die im Zip Code-Feld erlaubt sind. Feste Feldgröße: Aktivieren Sie dieses Kontrollkästchen, um ein Feld mit einer festen Größe zu erstellen. Wenn diese Option aktiviert ist, können Sie auch die Einstellungen Automatisch anpassen und/oder Mehrzeiliges Feld verwenden. Ein Feld mit fester Größe sieht wie eine Autoform aus. Sie können einen Umbruchstil dafür festlegen und die Position anpassen. Automatisch anpassen: Diese Option kann aktiviert werden, wenn die Einstellung Feste Feldgröße ausgewählt ist. Aktivieren Sie sie, um die Schriftgröße automatisch an die Feldgröße anzupassen. Mehrzeiliges Feld: Diese Option kann aktiviert werden, wenn die Einstellung Feste Feldgröße ausgewählt ist. Aktivieren Sie sie, um ein Formularfeld mit mehreren Zeilen zu erstellen, andernfalls belegt der Text eine einzelne Zeile. Zeichengrenze: Standardmäßig gibt es keine Grenzen. Aktivieren Sie dieses Kontrollkästchen, um die maximale Zeichenanzahl im Feld rechts festzulegen. Zeichenanzahl in Textfeld: Verteilen Sie den Text gleichmäßig innerhalb des eingefügten Postleitzahlfelds und konfigurieren Sie seine allgemeine Darstellung. Lassen Sie das Kontrollkästchen deaktiviert, um die Standardeinstellungen beizubehalten, oder aktivieren Sie es, um die folgenden Parameter festzulegen: Zeilenbreite: Wählen Sie, ob der Breitenwert Auto (Breite wird automatisch berechnet), Mindestens (Breite ist nicht kleiner als der manuell angegebene Wert) oder Genau sein soll (Breite entspricht dem manuell eingegebenen Wert). Der darin enthaltene Text wird entsprechend ausgerichtet. Rahmenfarbe: Klicken Sie auf das Symbol , um die Farbe für die Ränder des eingefügten Zip Code-Felds festzulegen. Wählen Sie die bevorzugte Rahmenfarbe aus der Palette. Sie können bei Bedarf eine neue benutzerdefinierte Farbe hinzufügen. Hintergrundfarbe: Klicken Sie auf das Symbol , um dem eingefügten Zip Code-Feld eine Hintergrundfarbe zuzuweisen. Wählen Sie die bevorzugte Farbe aus Designfarben, Standardfarben oder fügen Sie bei Bedarf eine neue benutzerdefinierte Farbe hinzu. Erforderlich: Aktivieren Sie dieses Kontrollkästchen, damit das Feld für die Postleitzahl zwingend ausgefüllt werden muss. Erstellen eines neuen Kreditkartefelds Das Feld Kreditkarte wird verwendet, um eine Kreditkartennummer einzugeben, die einer beliebigen Maske entspricht, die vom Ersteller des Formulars vorgegeben wird. Das Feld ist standardmäßig auf 9999-9999-9999-9999 eingestellt. Um ein Kreditkartenfeld einzufügen, positionieren Sie die Einfügemarke innerhalb einer Textzeile an der Stelle, an der das Feld hinzugefügt werden soll, wechseln Sie zur Registerkarte Formulare der oberen Symbolleiste, klicken Sie auf das Symbol Credit Card. Das Formularfeld erscheint an der Einfügemarke innerhalb der bestehenden Textzeile. Das Menü Einstellungen des Formulars wird rechts geöffnet. Wer soll das Feld ausfüllen?: Wählen Sie die Rolle über die Dropdown-Liste aus, um die Benutzergruppe mit Zugriff auf dieses Feld festzulegen. Um mehr über das Zuweisen von Rollen zu erfahren, lesen Sie bitte den Abschnitt Verwalten von Rollen in dieser Anleitung. Schlüssel: Die Option zum Gruppieren von Kreditkartenfelder zum gleichzeitigen Ausfüllen. Um einen neuen Schlüssel zu erstellen, geben Sie seinen Namen in das Feld ein und drücken Sie die Eingabetaste, und weisen Sie dann jedem Feld den erforderlichen Schlüssel mithilfe der Dropdown-Liste zu. Die Meldung Verbundene Felder: 2/3/... wird angezeigt. Um die Felder zu trennen, klicken Sie auf die Schaltfläche Verbindung trennen. Platzhalter: Geben Sie den Text ein, der in das eingefügte Credit Card-Formularfeld angezeigt werden soll; \"Klicken Sie, um die Kreditkarte herunterzuladen\" ist standardmäßig eingestellt. Tag: Geben Sie den Text ein, der als Tag für den internen Gebrauch verwendet werden soll, d. h. nur für Mitbearbeiter angezeigt wird. Tipp: Geben Sie den Text ein, der als Tipp angezeigt werden soll, wenn ein Benutzer seinen Mauszeiger über das Kreditkarte-Feld bewegt. Format: Wählen Sie das Inhaltsformat des Felds, d. h. Keine, Ziffern, Buchstaben, Beliebige Maske oder Regulärer Ausdruck. Das Feld ist standardmäßig auf Beliebige Maske eingestellt. Um das Format zu ändern, geben Sie die erforderliche Maske in das Feld darunter ein. Erlaubte Symbole: Geben Sie die Symbole ein, die im Kreditkarte-Feld erlaubt sind. Feste Feldgröße: Aktivieren Sie dieses Kontrollkästchen, um ein Feld mit einer festen Größe zu erstellen. Wenn diese Option aktiviert ist, können Sie auch die Einstellungen Automatisch anpassen und/oder Mehrzeiliges Feld verwenden. Ein Feld mit fester Größe sieht wie eine Autoform aus. Sie können einen Umbruchstil dafür festlegen und die Position anpassen. Automatisch anpassen: Diese Option kann aktiviert werden, wenn die Einstellung Feste Feldgröße ausgewählt ist. Aktivieren Sie sie, um die Schriftgröße automatisch an die Feldgröße anzupassen. Mehrzeiliges Feld: Diese Option kann aktiviert werden, wenn die Einstellung Feste Feldgröße ausgewählt ist. Aktivieren Sie sie, um ein Formularfeld mit mehreren Zeilen zu erstellen, andernfalls belegt der Text eine einzelne Zeile. Zeichengrenze: Standardmäßig gibt es keine Grenzen. Aktivieren Sie dieses Kontrollkästchen, um die maximale Zeichenanzahl im Feld rechts festzulegen. Zeichenanzahl in Textfeld: Verteilen Sie den Text gleichmäßig innerhalb des eingefügten Kreditkartefelds und konfigurieren Sie seine allgemeine Darstellung. Lassen Sie das Kontrollkästchen deaktiviert, um die Standardeinstellungen beizubehalten, oder aktivieren Sie es, um die folgenden Parameter festzulegen: Zeilenbreite: Wählen Sie, ob der Breitenwert Auto (Breite wird automatisch berechnet), Mindestens (Breite ist nicht kleiner als der manuell angegebene Wert) oder Genau sein soll (Breite entspricht dem manuell eingegebenen Wert). Der darin enthaltene Text wird entsprechend ausgerichtet. Rahmenfarbe: Klicken Sie auf das Symbol , um die Farbe für die Ränder des eingefügten Kreditkartefelds festzulegen. Wählen Sie die bevorzugte Rahmenfarbe aus der Palette. Sie können bei Bedarf eine neue benutzerdefinierte Farbe hinzufügen. Hintergrundfarbe: Klicken Sie auf das Symbol , um dem eingefügten Kreditkartefeld eine Hintergrundfarbe zuzuweisen. Wählen Sie die bevorzugte Farbe aus Designfarben, Standardfarben oder fügen Sie bei Bedarf eine neue benutzerdefinierte Farbe hinzu. Erforderlich: Aktivieren Sie dieses Kontrollkästchen, damit das Kreditkartefeld zwingend ausgefüllt werden muss. Erstellen eines neuen komplexen Feld Komplexes Feld kombiniert mehrere Feldtypen, z. B. Textfeld und eine Dropdown-Liste. Sie können Felder beliebig kombinieren. Um ein komplexes Feld einzufügen, positionieren Sie die Einfügemarke innerhalb einer Textzeile an der Stelle, an der das Feld hinzugefügt werden soll, wechseln Sie zur Registerkarte Formulare der oberen Symbolleiste, klicken Sie auf das Symbol Komplexes Feld. Das Formularfeld erscheint an der Einfügemarke innerhalb der bestehenden Textzeile. Das Menü Einstellungen des Formulars wird rechts geöffnet. Wer soll das Feld ausfüllen?: Wählen Sie die Rolle über die Dropdown-Liste aus, um die Benutzergruppe mit Zugriff auf dieses Feld festzulegen. Um mehr über das Zuweisen von Rollen zu erfahren, lesen Sie bitte den Abschnitt Verwalten von Rollen in dieser Anleitung. Schlüssel: Die Option zum Gruppieren von Komplexfelder zum gleichzeitigen Ausfüllen. Um einen neuen Schlüssel zu erstellen, geben Sie seinen Namen in das Feld ein und drücken Sie die Eingabetaste, und weisen Sie dann jedem Feld den erforderlichen Schlüssel mithilfe der Dropdown-Liste zu. Die Meldung Verbundene Felder: 2/3/... wird angezeigt. Um die Felder zu trennen, klicken Sie auf die Schaltfläche Verbindung trennen. Platzhalter: Geben Sie den Text ein, der in das eingefügte komplexe Feld angezeigt werden soll; \"Klicken Sie, um das komplexe Feld herunterzuladen\" ist standardmäßig eingestellt. Tag: Geben Sie den Text ein, der als Tag für den internen Gebrauch verwendet werden soll, d. h. nur für Mitbearbeiter angezeigt wird. Tipp: Geben Sie den Text ein, der als Tipp angezeigt werden soll, wenn ein Benutzer seinen Mauszeiger über das Kreditkarte-Feld bewegt. Feste Feldgröße: Aktivieren Sie dieses Kontrollkästchen, um ein Feld mit einer festen Größe zu erstellen. Wenn diese Option aktiviert ist, können Sie auch die Einstellungen Automatisch anpassen und/oder Mehrzeiliges Feld verwenden. Ein Feld mit fester Größe sieht wie eine Autoform aus. Sie können einen Umbruchstil dafür festlegen und die Position anpassen. Rahmenfarbe: Klicken Sie auf das Symbol , um die Farbe für die Ränder des eingefügten Kreditkartefelds festzulegen. Wählen Sie die bevorzugte Rahmenfarbe aus der Palette. Sie können bei Bedarf eine neue benutzerdefinierte Farbe hinzufügen. Hintergrundfarbe: Klicken Sie auf das Symbol , um dem eingefügten Kreditkartefeld eine Hintergrundfarbe zuzuweisen. Wählen Sie die bevorzugte Farbe aus Designfarben, Standardfarben oder fügen Sie bei Bedarf eine neue benutzerdefinierte Farbe hinzu. Erforderlich: Aktivieren Sie dieses Kontrollkästchen, damit das Kreditkartefeld zwingend ausgefüllt werden muss. Um verschiedene Formularfelder in ein komplexes Feld einzufügen, klicken Sie darauf und wählen Sie das gewünschte Feld in der oberen Symbolleiste auf der Registerkarte Formulare aus und konfigurieren Sie es dann nach Ihren Wünschen. Um mehr über die einzelnen Feldtypen zu erfahren, lesen Sie die entsprechenden Abschnitte oben. Bitte beachten Sie, dass Sie das Formularfeld Bild nicht innerhalb komplexer Felder verwenden können. Verwalten von Rollen Sie können neue Rollen erstellen, die bestimmen, wer bestimmte Formularfelder ausfüllen kann. Um die Rollen zu verwalten, gehen Sie zur Registerkarte Formulare in der oberen Symbolleiste, klicken Sie auf das Symbol Managing Roles, klicken Sie auf die Schaltfläche New, um eine neue Rolle zu erstellen, geben Sie den Namen der Rolle ein und wählen Sie bei Bedarf die Farbe aus. Sie können auch eine benutzerdefinierte Farbe erstellen, indem Sie auf den entsprechenden Menüpunkt klicken, klicken Sie auf OK, um eine neue Rolle zu erstellen, legen Sie die Reihenfolge fest, in der die Ausfüller das Dokument erhalten und unterschreiben. Verwenden Sie dazu die Schaltfläche und , verwenden Sie die Schaltflächen Bearbeiten oder Löschen, um die Rollen zu ändern oder zu löschen, klicken Sie auf Schließen, um zur Formularbearbeitung zurückzukehren. Wenn Sie das Formular als .oform-Datei speichern, können Sie alle für das Formular erstellten Rollen anzeigen. Aktivieren des Modus \"Formular anzeigen\" Sobald Sie den Modus Formular anzeigen aktiviert haben, stehen alle Bearbeitungsoptionen nicht mehr zur Verfügung. Klicken Sie auf die Schaltfläche Formular anzeigen auf der Registerkarte Formulare der oberen Symbolleiste, um zu sehen, wie alle eingefügten Formulare in Ihrem Dokument angezeigt werden. Sie können das Formular aus der Sicht jeder erstellten Rolle anzeigen. Klicken Sie dazu auf den Pfeil unter der Schaltfläche Formular anzeigen und wählen Sie die gewünschte Rolle aus. Um den Anzeigemodus zu verlassen, klicken Sie erneut auf dasselbe Symbol. Verschieben von Formularfeldern Formularfelder können an eine andere Stelle im Dokument verschoben werden: Klicken Sie auf die Schaltfläche links neben dem Steuerrahmen, um das Feld auszuwählen und ziehen Sie es ohne loslassen der Maustaste an eine andere Position im Text. Sie können Formularfelder auch kopieren und einfügen: Wählen Sie das erforderliche Feld aus und verwenden Sie die Tastenkombination Strg + C/Strg + V. Erstellen von erforderlichen Feldern Um ein Feld obligatorisch zu machen, aktivieren Sie die Option Erforderlich. Die Pflichtfelder werden mit rotem Strich markiert. Sperren von Formfeldern Um die weitere Bearbeitung des eingefügten Formularfelds zu verhindern, klicken Sie auf das Symbol Sperren. Das Ausfüllen der Felder bleibt verfügbar. Löschen von Formularfeldern Um alle eingefügten Felder und alle Werte zu löschen, klicken Sie auf die Schaltfläche Alle Felder löschen auf der Registerkarte Formulare in der oberen Symbolleiste. Das Löschen von Feldern kann nur im Formularausfüllmodus durchgeführt werden. Navigieren, Anzeigen und Speichern eines Formulars Gehen Sie zur Registerkarte Formulare in der oberen Symbolleiste. Navigieren Sie durch die Formularfelder mit den Schaltflächen Vorheriges Feld und Nächstes Feld in der oberen Symbolleiste. Wenn Sie fertig sind, klicken Sie in der oberen Symbolleiste auf die Schaltfläche Als OFORM speichern, um das Formular als eine zum Ausfüllen bereite OFORM-Datei zu speichern. Sie können beliebig viele OFORM-Dateien speichern. Entfernen von Formularfeldern Um ein Formularfeld zu entfernen und seinen gesamten Inhalt beizubehalten, wählen Sie es aus und klicken Sie auf das Symbol Löschen (stellen Sie sicher, dass das Feld nicht gesperrt ist) oder drücken Sie die Taste Löschen auf der Tastatur."
    },
   {
        "id": "UsageInstructions/CreateLists.htm", 
        "title": "Listen erstellen", 
        "body": "Um eine Liste in Ihrem Dokument zu erstellen im Dokumenteneditor, Platzieren Sie den Zeiger an der Stelle, an der eine Liste gestartet wird (dies kann eine neue Zeile oder der bereits eingegebene Text sein). Wechseln Sie zur Registerkarte Startseite der oberen Symbolleiste. Wählen Sie den Listentyp aus, den Sie starten möchten: Eine ungeordnete Liste mit Markierungen wird mithilfe des Aufzählungssymbols in der oberen Symbolleiste erstellt Die geordnete Liste mit Ziffern oder Buchstaben wird mithilfe des Nummerierungssymbols in der oberen Symbolleiste erstellt Klicken Sie auf den Abwärtspfeil neben dem Symbol Aufzählungszeichen oder Nummerierung, um auszuwählen, wie die Liste aussehen soll. Jedes Mal, wenn Sie die Eingabetaste am Ende der Zeile drücken, wird ein neues geordnetes oder ungeordnetes Listenelement angezeigt. Um dies zu stoppen, drücken Sie die Rücktaste und fahren Sie mit dem allgemeinen Textabsatz fort. Das Programm erstellt auch automatisch nummerierte Listen, wenn Sie Ziffer 1 mit einem Punkt oder einer Klammer und einem Leerzeichen danach eingeben: 1., 1). Listen mit Aufzählungszeichen können automatisch erstellt werden, wenn Sie die Zeichen -, * und ein Leerzeichen danach eingeben. Sie können den Texteinzug in den Listen und ihre Verschachtelung auch mithilfe der Symbole Liste mit mehreren Ebenen , Einzug verringern und Einzug vergrößern auf der Registerkarte Startseite der oberen Symbolleiste ändern. Um die Listenebene zu ändern, klicken Sie auf das Symbol Nummerierung , Aufzählungszeichen oder Liste mit mehreren Ebenen und wählen Sie die Option Listenebene ändern, oder positionieren Sie den Cursor am Zeilenanfang und drücken Sie die Tabulatortaste auf der Tastatur, um zur nächsten Ebene der Liste zu gelangen. Fahren Sie mit der erforderlichen Listenebene fort. Die zusätzlichen Einrückungs- und Abstandsparameter können in der rechten Seitenleiste und im Fenster mit den erweiterten Einstellungen geändert werden. Weitere Informationen finden Sie im Abschnitt Ändern von Absatzeinzügen und Festlegen des Absatzzeilenabstands. Listen verbinden und trennen So verbinden Sie eine Liste mit der vorhergehenden: Klicken Sie mit der rechten Maustaste auf das erste Element der zweiten Liste, Verwenden Sie die Option Mit vorheriger Liste verbinden aus dem Kontextmenü. Die Listen werden zusammengefügt und die Nummerierung wird gemäß der ersten Listennummerierung fortgesetzt. So trennen Sie eine Liste: Klicken Sie mit der rechten Maustaste auf das Listenelement, mit dem Sie eine neue Liste beginnen möchten. Verwenden Sie die Option Liste trennen aus dem Kontextmenü. Die Liste wird getrennt und die Nummerierung in der zweiten Liste beginnt von neuem. Nummerierung ändern So setzen Sie die fortlaufende Nummerierung in der zweiten Liste gemäß der vorherigen Listennummerierung fort: Klicken Sie mit der rechten Maustaste auf das erste Element der zweiten Liste. Verwenden Sie die Option Nummerierung fortsetzen im Kontextmenü. Die Nummerierung wird gemäß der ersten Listennummerierung fortgesetzt. So legen Sie einen bestimmten Nummerierungsanfangswert fest: Klicken Sie mit der rechten Maustaste auf das Listenelement, auf das Sie einen neuen Nummerierungswert anwenden möchten. Verwenden Sie die Option Nummerierungswert festlegen aus dem Kontextmenü. Stellen Sie in einem neuen Fenster, das geöffnet wird, den erforderlichen numerischen Wert ein und klicken Sie auf die Schaltfläche OK. Ändern Sie die Listeneinstellungen So ändern Sie die Listeneinstellungen mit Aufzählungszeichen oder nummerierten Listen, z. B. Aufzählungszeichen / Nummerntyp, Ausrichtung, Größe und Farbe: Klicken Sie auf ein vorhandenes Listenelement oder wählen Sie den Text aus, den Sie als Liste formatieren möchten. Klicken Sie auf der Registerkarte Startseite der oberen Symbolleiste auf das Symbol Aufzählungszeichen oder Nummerierung . Wählen Sie die Option Listeneinstellungen. Das Fenster Listeneinstellungen wird geöffnet. Das Fenster mit den Listen mit Aufzählungszeichen sieht folgendermaßen aus: Das Fenster mit den nummerierten Listeneinstellungen sieht folgendermaßen aus: Für die Liste mit Aufzählungszeichen können Sie ein Zeichen auswählen, das als Aufzählungszeichen verwendet wird, während Sie für die nummerierte Liste den Nummerierungstyp auswählen können. Die Optionen Ausrichtung, Größe und Farbe sind sowohl für die Listen mit Aufzählungszeichen als auch für die nummerierten Listen gleich. Aufzählungszeichen - Ermöglicht die Auswahl des erforderlichen Zeichens für die Aufzählungsliste. Wenn Sie auf das Feld Schriftart und Symbol klicken, wird das Symbolfenster geöffnet, in dem Sie eines der verfügbaren Zeichen auswählen können. Weitere Informationen zum Arbeiten mit Symbolen finden Sie in diesem Artikel. Typ - Ermöglicht die Auswahl des erforderlichen Nummerierungstyps für die nummerierte Liste. Folgende Optionen stehen zur Verfügung: Keine, 1, 2, 3, ..., a, b, c, ..., A, B, C, ..., i, ii, iii, ..., I, II, III, .... Ausrichtung - Ermöglicht die Auswahl des erforderlichen Typs für die Ausrichtung von Aufzählungszeichen / Zahlen, mit dem Aufzählungszeichen / Zahlen horizontal innerhalb des dafür vorgesehenen Bereichs ausgerichtet werden. Folgende Ausrichtungstypen stehen zur Verfügung: Links, Mitte, Rechts. Größe - Ermöglicht die Auswahl der erforderlichen Aufzählungszeichen- / Zahlengröße. Die Option Wie ein Text ist standardmäßig ausgewählt. Wenn diese Option ausgewählt ist, entspricht die Aufzählungszeichen- oder Zahlengröße der Textgröße. Sie können eine der vordefinierten Größen von 8 bis 96 auswählen. Farbe - Ermöglicht die Auswahl der erforderlichen Aufzählungszeichen- / Zahlenfarbe. Die Option Wie ein Text ist standardmäßig ausgewählt. Wenn diese Option ausgewählt ist, entspricht die Aufzählungszeichen- oder Zahlenfarbe der Textfarbe. Sie können die Option Automatisch auswählen, um die automatische Farbe anzuwenden, oder eine der Themenfarben oder Standardfarben in der Palette auswählen oder eine benutzerdefinierte Farbe angeben. Alle Änderungen werden im Feld Vorschau angezeigt. Klicken Sie auf OK, um die Änderungen zu übernehmen und das Einstellungsfenster zu schließen. So ändern Sie die Einstellungen für mehrstufige Listen: Klicken Sie auf ein Listenelement. Klicken Sie auf der Registerkarte Startseite der oberen Symbolleiste auf das Symbol Mehrstufige Liste . Wählen Sie die Option Listeneinstellungen. Das Fenster Listeneinstellungen wird geöffnet. Das Fenster mit den Einstellungen für mehrstufige Listen sieht folgendermaßen aus: Wählen Sie die gewünschte Ebene der Liste im Feld Ebene links aus und passen Sie das Aufzählungszeichen oder die Zahl mit den Schaltflächen oben an Aussehen für die ausgewählte Ebene: Typ - Ermöglicht die Auswahl des erforderlichen Nummerierungstyps für die nummerierte Liste oder des erforderlichen Zeichens für die Liste mit Aufzählungszeichen. Für die nummerierte Liste stehen folgende Optionen zur Verfügung: Keine, 1, 2, 3, ..., a, b, c, ..., A, B, C, ..., i, ii, iii, .. ., I, II, III, .... Für die Liste mit Aufzählungszeichen können Sie eines der Standardsymbole auswählen oder die Option Neues Aufzählungszeichen verwenden. Wenn Sie auf diese Option klicken, wird das Symbolfenster geöffnet, in dem Sie eines der verfügbaren Zeichen auswählen können. Weitere Informationen zum Arbeiten mit Symbolen finden Sie in diesem Artikel. Ausrichtung - Ermöglicht die Auswahl des erforderlichen Typs für die Ausrichtung von Aufzählungszeichen / Zahlen, mit dem Aufzählungszeichen / Zahlen horizontal innerhalb des am Anfang des Absatzes dafür vorgesehenen Bereichs ausgerichtet werden. Folgende Ausrichtungstypen stehen zur Verfügung: Links, Mitte, Rechts. Größe - Ermöglicht die Auswahl der erforderlichen Aufzählungszeichen- / Zahlengröße. Die Option Wie ein Text ist standardmäßig ausgewählt. Sie können eine der vordefinierten Größen von 8 bis 96 auswählen. Farbe - Ermöglicht die Auswahl der erforderlichen Aufzählungszeichen- / Zahlenfarbe. Die Option Wie ein Text ist standardmäßig ausgewählt. Wenn diese Option ausgewählt ist, entspricht die Aufzählungszeichen- oder Zahlenfarbe der Textfarbe. Sie können die Option Automatisch auswählen, um die automatische Farbe anzuwenden, oder eine der Themenfarben oder Standardfarben in der Palette auswählen oder eine benutzerdefinierte Farbe angeben. Alle Änderungen werden im Feld Vorschau angezeigt. Klicken Sie auf OK, um die Änderungen zu übernehmen und das Einstellungsfenster zu schließen."
    },
   {
        "id": "UsageInstructions/CreateTableOfContents.htm", 
        "title": "Inhaltsverzeichnis erstellen", 
        "body": "Ein Inhaltsverzeichnis enthält eine Liste aller Kapitel (Abschnitte usw.) in einem Dokument und zeigt die jeweilige Seitennummer an, auf der ein Kapitel beginnt. Mithilfe eines Inhaltsverzeichnisses können Sie leicht durch ein mehrseitiges Dokument navigieren und schnell zu gewünschten Textstellen wechseln. Das Inhaltsverzeichnis wird automatisch basierend auf mit vordefinierten Stilen formatierten Dokumentüberschriften generiert. So können Sie Ihr Inhaltsverzeichnis bequem aktualisieren wenn der Text im Dokument geändert wurde, ohne dass Sie Überschriften bearbeiten und Seitenzahlen manuell ändern müssen. Überschriftenstruktur des Inhaltsverzeichnisses festlegen Überschriften formatieren Formatieren Sie zunächst alle Überschriften in Ihrem Dokument mit einer der Stilvorlagen im Dokumenteneditor. Überschriften formatieren: Wählen Sie den Text aus, den Sie in das Inhaltsverzeichnis aufnehmen wollen. Öffnen Sie rechts in der Registerkarte Start das Menü mit den Stilvorlagen. Klicken Sie auf den gewünschten Stil. Standardmäßig können Sie die Stile Überschrift 1 - Überschrift 9 nutzen. Wenn Sie einen anderen Stil verwenden (z.B. Titel, Untertitel etc.), um Überschriften zu formatieren, die im Inhaltsverzeichnis angezeigt werden sollen, müssen Sie zunächst die Einstellungen für das Inhaltsverzeichnis anpassen (wie im nachfolgenden Absatz beschrieben). Weitere Informationen zu verfügbaren Formatvorlagen finden Sie auf dieser Seite. Um schnell Text als Überschrift hinzuzufügen, Wählen Sie den Text aus, den Sie in das Inhaltsverzeichnis aufnehmen möchten. Gehen Sie zur Registerkarte Verweise in der oberen Symbolleiste. Klicken Sie in der oberen Symbolleiste auf die Schaltfläche Text hinzufügen. Wählen Sie die gewünschte Überschriftenebene aus. Überschriften verwalten Wenn Sie die Überschriften formatiert haben, können Sie in der linken Seitenleiste auf Navigation klicken, um das Fenster mit den Überschriften mit den entsprechenden Verschachtelungsebenen zu öffnen. Dieser Bereich ermöglicht die einfache Navigation zwischen Überschriften im Dokumenttext sowie die Verwaltung der Überschriftenstruktur. Klicken Sie mit der rechten Maustaste auf eine Überschrift in der Liste und verwenden Sie eine der verfügbaren Optionen aus dem Menü: Heraufstufen - um die aktuell ausgewählte Überschrift in der hierarchischen Struktur auf die höhere Ebene zu verschieben, z. B. von Überschrift2 auf Überschrift1. Herabstufen - um die aktuell ausgewählte Überschrift in der hierarchischen Struktur auf die niedrigere Ebene zu verschieben, z. B. von Überschrift1 auf Überschrift2. Neue Überschrift davor - um eine neue leere Überschrift derselben Ebene vor der aktuell ausgewählten hinzuzufügen. Neue Überschrift dahinter - um eine neue leere Überschrift derselben Ebene hinter der aktuell ausgewählten hinzuzufügen. Neue Zwischenüberschrift - um eine neue leere Neue Zwischenüberschrift (z.B. eine Überschrift auf einer niedrigeren Ebene) hinter der aktuell ausgewählten hinzuzufügen. Wenn die Überschrift oder Zwischenüberschrift hinzugefügt wurde, klicken Sie auf die hinzugefügte leere Überschrift in der Liste und geben Sie Ihren eigenen Text ein. Das kann sowohl im Dokumenttext als auch in der Navigationsleiste selbst erfolgen. Inhalt auswählen - um den Text unterhalb der aktuellen Überschrift im Dokument auszuwählen (einschließlich des Textes für alle Zwischenüberschriften dieser Überschrift). Alle erweitern - um alle Überschriften-Ebenen in der Navigationsleiste zu erweitern. Alle ausblenden - um alle Überschriften-Ebenen außer Ebene 1 in der Navigationsleiste auszublenden. Erweitern auf Ebene - um die Überschriftenstruktur auf die ausgewählte Ebene zu erweitern. Wenn Sie z. B. Ebene 3 wählen, werden die Level 1, 2 und 3 erweitert, während Level 4 und alle niedrigeren Level ausgeblendet werden. Um separate Überschriftenebenen manuell zu erweitern oder auszublenden, verwenden Sie die Pfeile links von den Überschriften. Um die Navigationsleiste zu schließen, klicken Sie in der linken Seitenleiste erneut auf Navigation. Inhaltsverzeichnis in Ihr aktuelles Dokument einfügen Ein Inhaltsverzeichnis in Ihr aktuelles Dokument einfügen: Platzieren Sie die Einfügemarke an der Stelle, an der Sie ein Inhaltsverzeichnis hinzufügen möchten. Wechseln Sie in der oberen Symbolleiste auf die Registerkarte Verweise. Klicken Sie auf das Symbol Inhaltsverzeichnis in der oberen Symbolleiste oder klicken Sie auf das Pfeilsymbol neben diesem Symbol und wählen Sie die gewünschte Layout-Option aus dem Menü aus. Sie haben die Wahl zwischen einem Inhaltsverzeichnis, das Überschriften, Seitenzahlen und Leader anzeigt oder nur Überschriften. Die Formatierung des Inhaltsverzeichnisses kann später über die Einstellungen des Inhaltsverzeichnisses angepasst werden. Das Inhaltsverzeichnis wird an der aktuellen Cursorposition eingefügt. Um die Position des Inhaltsverzeichnisses zu ändern, können Sie das Inhaltsverzeichnis auswählen (Steuerelement) und einfach an die gewünschte Position ziehen. Klicken Sie dazu auf die Schaltfläche , in der oberen linken Ecke des Inhaltsverzeichnisses und ziehen Sie es mit gedrückter Maustaste an die gewünschte Position. Um zwischen den Überschriften zu navigieren, drücken Sie die Taste STRG auf Ihrer Tastatur und klicken Sie dann auf die gewünschte Überschrift im Inhaltsverzeichnis. Sie wechseln automatisch auf die entsprechende Seite. Inhaltsverzeichnis anpassen Inhaltsverzeichnis aktualisieren Nach dem Erstellen des Inhaltsverzeichnisses, können Sie Ihren Text weiter bearbeiten und beispielsweise neue Kapitel hinzufügen, die Reihenfolge ändern, Absätze entfernen oder den Text für eine Überschrift erweitern. Durch solche Vorgänge ändern sich die Seitenzahlen. Über die Option Aktualisieren übernehmen Sie alle Änderungen in Ihrem Inhaltsverzeichnis. Klicken Sie auf der Registerkarte Referenzen in der oberen Symbolleiste auf den Pfeil neben dem Symbol Aktualisieren und wählen Sie die gewünschte Option aus dem Menü aus. Gesamtes Verzeichnis aktualisieren - die von Ihnen eingefügten Überschriften werden dem Verzeichnis hinzugefügt, gelöschte Überschriften werden entfernt, bearbeitete (umbenannte) Überschriften sowie Seitenzahlen werden aktualisiert. Nur Seitenzahlen aktualisieren - nur die Seitenzahlen werden aktualisiert, Änderungen an Überschriften werden nicht übernommen. Alternativ können Sie das Inhaltsverzeichnis direkt im Dokument auswählen, wählen Sie dann oben im Verzeichnis das Symbol Aktualisieren aus, um die zuvor beschriebenen Optionen anzuzeigen. Sie können auch einfach mit der rechten Maustaste an eine beliebige Stelle im Inhaltsverzeichnis klicken und die gewünschte Option aus dem Kontextmenü auswählen. Einstellungen für das Inhaltsverzeichnis anpassen Zum Öffnen der Einstellungen für das Inhaltsverzeichnis, gehen Sie vor wie folgt: Klicken Sie auf den Pfeil neben Inhaltsverzeichnis in der oberen Symbolleiste und wählen Sie die Option Einstellungen aus dem Menü aus. Wählen Sie das Inhaltsverzeichnis aus, klicken Sie auf den Pfeil und wählen Sie die Option Einstellungen aus dem Menü aus. Klicken Sie mit der rechten Maustaste an eine beliebige Stelle im Inhaltsverzeichnis und wählen sie die Option Inhaltsverzeichnis - Einstellungen aus dem Kontextmenü aus. Im sich nun öffnenden Fenstern können Sie die folgenden Parameter festlegen: Seitenzahlen anzeigen: Mit dieser Option wählen Sie aus, ob Seitenzahlen angezeigt werden sollen oder nicht. Seitenzahlen rechtsbündig: Mit dieser Option legen Sie fest, ob die Seitenzahlen am rechten Seitenrand ausgerichtet werden sollen oder nicht. Füllzeichen: Mit dieser Option können Sie die Art des Leaders auswählen, den Sie verwenden möchten. Ein Leader ist eine Reihe von Zeichen (Punkte oder Trennzeichen), die den Abstand zwischen einer Überschrift und der entsprechenden Seitenzahl ausfüllen. Wenn Sie keinen Leader nutzen wollen, wählen Sie die Option Keinen. Inhaltsverzeichnis als Links formatieren: Diese Option ist standardmäßig aktiviert. Wenn Sie das Kontrollkästchen deaktivieren, können Sie nicht länger in ein gewünschtes Kapitel wechseln, indem Sie die Taste STRG drücken und die entsprechende Überschrift anklicken. Erstellen eines Inhaltsverzeichnisses mithilfe von: In diesem Abschnitt können Sie die erforderliche Anzahl von Gliederungsebenen sowie die Standardstile angeben, die zum Erstellen des Inhaltsverzeichnisses verwendet werden sollen. Aktivieren Sie das erforderliche Optionsfeld: Gliederungsebenen - ist diese Option ausgewählt, können Sie die Anzahl der hierarchischen Ebenen anpassen, die im Inhaltsverzeichnis verwendet werden. Klicken Sie auf die Pfeile im Feld Ebenen, um die Anzahl der Ebenen zu verringern oder zu erhöhen (es sind die Werte 1 bis 9 verfügbar). Wenn Sie z. B. den Wert 3 auswählen, werden Überschriften der Ebenen 4 - 9 nicht in das Inhaltsverzeichnis aufgenommen. Ausgewählte Stile - ist diese Option ausgewählt, können Sie zusätzliche Stile angeben, die zum Erstellen des Inhaltsverzeichnisses verwendet werden können, und ihnen eine entsprechende Gliederungsebene zuweisen. Geben Sie die gewünschte Ebene in das Feld rechts neben dem Stil ein. Sobald Sie die Einstellungen gespeichert haben, können Sie diesen Stil zum Erstellen des Inhaltsverzeichnisses verwenden. Stile - mit dieser Option wählen Sie ein Inhaltsverzeichnis in dem von Ihnen gewünschte Stil aus einer Liste mit Vorlagen aus. Wählen Sie den gewünschten Stil aus der Dropdown-Liste aus: Im Feld Vorschau sehen Sie wie das Inhaltsverzeichnis mit dem gewählten Stil aussehen würde. Die folgenden vier Standardstile sind verfügbar: Einfach, Standard, Modern, Klassisch. Mit der Option Aktuell können Sie den Stil des Inhaltsverzeichnisses nach Ihren eigenen Wünschen anpassen. Klicken Sie im Fenster Einstellungen auf OK, um die Änderungen zu bestätigen. Stil des Inhaltsverzeichnisses anpassen Nachdem Sie eine der Standardvorlagen für Verzeichnisstile Fenster Inhaltsverzeichnis - Einstellungen angewendet haben, können Sie diesen Stil auch ändern, sodass der Text im Feld Inhaltsverzeichnis Ihren Wünschen entsprechend formatiert ist. Wählen Sie den Text innerhalb des Inhaltsverzeichnisfelds aus, z. B. indem Sie im Steuerungskasten für das Inhaltsverzeichnis auf die Schaltfläche klicken. Sie können die Elemente des Inhaltsverzeichnisses wie gewohnt formatieren und z. B. Schriftart, Größe, Farbe oder den Stile für die Schriftgestaltung ändern. Aktualisieren Sie entsprechend die Stile für die Elemente jeder Ebene. Um den Stil zu aktualisieren, klicken Sie mit der rechten Maustaste auf das formatierte Element, wählen Sie im Kontextmenü die Option Formatierung als Stil aus und klicken Sie auf die Option toc N Stil aktualisieren (Stil toc 2 entspricht Elementen der Ebene 2, Stil toc 3 entspricht Elementen der Ebene 3 usw.). Inhaltsverzeichnis aktualisieren Inhaltsverzeichnis entfernen Inhaltsverzeichnis aus dem Dokument entfernen: Klicken Sie auf den Pfeil neben Inhaltsverzeichnis in der oberen Symbolleiste und wählen Sie die Option Inhaltsverzeichnis entfernen aus dem Menü aus, oder klicken Sie auf den Pfeil neben dem Steuerelement Inhaltsverzeichnis und wählen Sie die Option Inhaltsverzeichnis entfernen aus."
    },
   {
        "id": "UsageInstructions/DecorationStyles.htm", 
        "title": "Dekoschriften anwenden", 
        "body": "Im Dokumenteneditor, können Sie mithilfe der entsprechenden Symbole auf der Registerkarte Startseite in der oberen Symbolleiste verschiedene Schriftdekorationsstile anwenden. Wenn Sie die Formatierung auf Text anwenden möchten, der bereits im Dokument vorhanden ist, wählen Sie diesen mit der Maus oder mithilfe der Tastatur aus und legen Sie die gewünschte Formatierung fest. Fett Der gewählte Textabschnitt wird durch fette Schrift hervorgehoben. Kursiv Der gewählte Textabschnitt wird durch die Schrägstellung der Zeichen hervorgehoben. Unterstrichen Der gewählten Textabschnitt wird mit einer Linie unterstrichen. Durchgestrichen Der gewählten Textabschnitt wird mit einer Linie durchgestrichen. Hochgestellt Der gewählte Textabschnitt wird verkleinert und im oberen Teil der Textzeile platziert z.B. in Bruchzahlen. Tiefgestellt Der gewählte Textabschnitt wird verkleinert und im unteren Teil der Textzeile platziert z.B. in chemischen Formeln. Um auf erweiterte Schrifteinstellungen zuzugreifen, klicken Sie mit der rechten Maustaste und wählen Sie die Option Absatz - Erweiterte Einstellungen im Menü oder nutzen Sie den Link Erweiterte Einstellungen anzeigen in der rechten Seitenleiste. Das Fenster Absatz - Erweiterte Einstellungen öffnet sich, wechseln Sie nun in die Registerkarte Schriftart. Hier stehen Ihnen die folgenden Deckoschriften und Einstellungen zur Verfügung: Durchgestrichen - durchstreichen einer Textstelle mithilfe einer Linie. Doppelt durchgestrichen - durchstreichen einer Textstelle mithilfe einer doppelten Linie. Hochgestellt - Textstellen verkleinern und hochstellen, wie beispielsweise in Brüchen. Tiefgestellt - Textstellen verkleinern und tiefstellen, wie beispielsweise in chemischen Formeln. Kapitälchen - erzeugt Großbuchstaben in Höhe von Kleinbuchstaben. Großbuchstaben - alle Buchstaben als Großbuchstaben schreiben. Abstand - Abstand zwischen den einzelnen Zeichen einstellen. Erhöhen Sie den Standardwert für den Abstand Erweitert oder verringern Sie den Standardwert für den Abstand Verkürzt. Nutzen Sie die Pfeiltasten oder geben Sie den erforderlichen Wert in das dafür vorgesehene Feld ein. Position - Zeichenposition (vertikaler Versatz) in der Zeile festlegen. Erhöhen Sie den Standardwert, um Zeichen nach oben zu verschieben, oder verringern Sie den Standardwert, um Zeichen nach unten zu verschieben. Nutzen Sie die Pfeiltasten oder geben Sie den erforderlichen Wert in das dafür vorgesehene Feld ein. Doppelbuchstaben sind verbundene Buchstaben eines Wortes, die in einer der OpenType-Schriftarten eingegeben werden. Bitte beachten Sie, dass die Verwendung von Ligaturen den Zeichenabstand stören kann. Die verfügbaren Doppebuchstabenoptionen sind wie folgt: Kein Nur standartisierte (includes “fi”, “fl”, “ff”; enhances readability) Kontextbezogene (ligatures are applied based on the surrounding letters; enhances readability) Historische (ligatures have more swoops and curved lines; lowers readability) Freie (ornamental ligatures; lowers readability) Standartisierte und kontextbezogene Standartisierte und historische Kontextbezogene und historische Standartisierte und freie Kontextbezogene und freie Historische und freie Standartisierte, kontextbezogene und historische Standartisierte, kontextbezogene und freie Standartisierte, historische und freie Kontextbezogene, historische und freie Alle Alle Änderungen werden im Feld Vorschau unten angezeigt."
    },
   {
        "id": "UsageInstructions/FillingOutForm.htm", 
        "title": "Formular ausfüllen", 
        "body": "Ein ausfüllbares Formular ist eine OFORM-Datei. OFORM ist ein Format zum Ausfüllen von Formularvorlagen und zum Herunterladen oder Ausdrucken des Formulars, nachdem Sie es ausgefüllt haben. Wie kann man ein Formular ausfüllen: Öffnen Sie die OFORM-Datei. Füllen Sie alle erforderlichen Felder aus. Die Pflichtfelder sind mit rotem Strich gekennzeichnet. Verwenden Sie oder Nächstes Feld auf dem obere Symbolleiste, um zwischen den Feldern zu navigieren, oder klicken Sie auf das Feld, das Sie ausfüllen möchten. Verwenden Sie die Schaltfläche Alle Felder leeren, um alle Eingabefelder zu leeren. Nachdem Sie alle Felder ausgefüllt haben, klicken Sie auf die Schaltfläche Als PDF speichern, um das Formular als PDF-Datei auf Ihrem Computer zu speichern. Klicken Sie auf in der oberen rechten Ecke der Symbolleiste, um zusätzliche Optionen anzuzeigen. Sie können das Formular Drucken, Als DOCX herunterladen oder Als PDF herunterladen. Sie können auch das Thema der Benutzeroberfläche des Formulars ändern, indem Sie Wie im System, Hell, Klassisch Hell, Dunkel oder Dunkler Kontrast auswählen. Sobald das Thema Dunkelmodus aktiviert ist, wird der Dunkelmodus verfügbar. Mit Zoom können Sie die Seite mithilfe der Funktionen Seite anpassen, Breite anpassen und Vergrößern oder Verkleinern skalieren und die Seitengröße ändern. Seite anpassen ermöglicht es, die Seite so zu skalieren, dass der Bildschirm die ganze Seite anzeigt. Breite anpassen ermöglicht es, die Seite so zu skalieren, dass sie an die Breite des Bildschirms angepasst wird. Zoom ermöglicht das Vergrößern und Verkleinern der Seite. Dateispeicherort öffnen, wenn Sie den Ordner durchsuchen müssen, in dem das Formular gespeichert ist."
    },
   {
        "id": "UsageInstructions/FontTypeSizeColor.htm", 
        "title": "Schriftart, -größe und -farbe festlegen", 
        "body": "Im Dokumenteneditor sie können Schriftart, Größe und Farbe der Schrift mithilfe der entsprechenden Symbole in der Registerkarte Start auf der oberen Symbolleiste festlegen. Wenn Sie die Formatierung auf Text anwenden möchten, der bereits im Dokument vorhanden ist, wählen Sie diesen mit der Maus oder mithilfe der Tastatur aus und legen Sie die gewünschte Formatierung fest. Sie können den Mauszeiger auch innerhalb des erforderlichen Wortes platzieren, um die Formatierung nur auf dieses Wort anzuwenden. Schriftart Wird verwendet, um eine Schriftart aus der Liste mit den verfügbaren Schriftarten zu wählen. Wenn eine gewünschte Schriftart nicht in der Liste verfügbar ist, können Sie diese runterladen und in Ihrem Betriebssystem speichern. Anschließend steht Ihnen diese Schrift in der Desktop-Version zur Nutzung zur Verfügung. Schriftgröße Über diese Option kann der gewünschte Wert für die Schriftgröße aus der Dropdown-List ausgewählt werden (die Standardwerte sind: 8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 36, 48, 72 und 96). Sie können auch manuell einen Wert in das Feld für die Schriftgröße eingeben und anschließend die Eingabetaste drücken. Schrift vergrößern Wird verwendet, um die Schriftgröße mit jedem Klick auf das Symbol um einen Punkt zu vergrößern. Schrift verkleinern Wird verwendet, um die Schriftgröße mit jedem Klick auf das Symbol um einen Punkt zu verkleinern. Groß-/Kleinschreibung Wird verwendet, um die Groß- und Kleinschreibung zu ändern. Ersten Buchstaben im Satz großschreiben - die Buchstaben werden wie in einem Satz geschrieben. Kleinbuchstaben - alle Buchstaben sind klein. GROSSBUCHSTABEN - alle Buchstaben sind groß. Ersten Buchstaben im Wort großschreiben - jedes Wort beginnt mit einem Großbuchstaben. gROSS-/kLEINSCHREIBUNG - kehrt die Groß- und Kleinschreibung des ausgewählten Textes oder des Wortes um, an dem sich der Mauszeiger befindet. Texthervorhebungsfarbe Wird verwendet, um den Hintergrund für einzelne Sätze, Phrasen, Wörter oder sogar Zeichen durch Hinzufügen eines Farbbandes zu markieren, das den Effekt eines Textmarker um den Text herum imitiert. Wählen Sie dazu den gewünschten Text aus und klicken Sie anschließend auf den Abwärtspfeil neben dem Symbol, um eine Farbe auf der Palette auszuwählen (diese Farbeinstellung ist unabhängig vom ausgewählten Farbschema und enthält 16 Farben) - die Farbe wird auf den ausgewählten Text angewendet. Alternativ können Sie zuerst eine Hervorhebungsfarbe auswählen und dann den Text mit der Maus auswählen - der Mauszeiger sieht in diesem Fall so aus und Sie können mehrere verschiedene Teile Ihres Textes nacheinander markieren. Um die Hervorhebung zu beenden, klicken Sie einfach erneut auf das Symbol. Um die Markierungsfarbe zu löschen, klicken Sie auf die Option Keine Füllung. Bei Hervorhebungen werden nur die Zeichen hinterlegt, im Gegensatz zu Hintergrundfarbe, die auf den gesamten Absatz angewendet wird und auch Leerräume im Absatz farblich hinterlegt. Schriftfarbe Wird verwendet, um die Farbe der Buchstaben/Zeichen im Text zu ändern. Standardmäßig wird die Schriftfarbe in einem neuen leeren Dokument automatisch festgelegt, als schwarze Schrift auf weißem Hintergrund. Wenn Sie die Hintergrundfarbe auf schwarz ändern, wechselt die Schriftfarbe automatisch auf weiß, damit sie weiter zu erkennen ist. Um eine andere Farbe auszuwählen, klicken Sie auf den Abwärtspfeil neben dem Symbol und wählen Sie eine Farbe aus den verfügbaren Paletten aus (die angezeigten Designfarben sind abhängig vom ausgewählten Farbschema. Wenn Sie die Standardschriftfarbe geändert haben, können Sie die Standardfarbe für die ausgewählte Textpassage über die Option Automatisch in der Gruppe Farbpaletten wiederherstellen. Weitere Informationen zum Umgang mit Farbpaletten finden Sie auf dieser Seite."
    },
   {
        "id": "UsageInstructions/FormattingPresets.htm", 
        "title": "Formatvorlagen anwenden", 
        "body": "Jeder Formatierungsstil ist ein Satz vordefinierter Formatierungsoptionen: (Schriftgröße, Farbe, Zeilenabstand, Ausrichtung usw.). Die Stile im Dokumenteneditor ermöglichen es Ihnen, verschiedene Teile des Dokuments (Überschriften, Zwischenüberschriften, Listen, normaler Text, Anführungszeichen) schnell zu formatieren, anstatt jedes Mal mehrere Formatierungsoptionen einzeln anzuwenden. Dadurch wird auch das einheitliche Erscheinungsbild des gesamten Dokuments sichergestellt. Sie können Stile auch verwenden, um ein Inhaltsverzeichnis oder ein Abbildungsverzeichnis zu erstellen. Das Anwenden eines Stils hängt davon ab, ob es sich um einen Absatzstil (normal, kein Abstand, Überschriften, Listenabsatz usw.) oder einen Textstil (basierend auf Schriftart, -größe, -farbe) handelt. Es hängt auch davon ab, ob eine Textpassage markiert ist oder der Mauszeiger auf einem Wort steht. In einigen Fällen müssen Sie den gewünschten Stil möglicherweise zweimal aus der Stilbibliothek auswählen, damit er korrekt angewendet werden kann: Wenn Sie den Stil zum ersten Mal im Stilbereich anklicken, werden die Eigenschaften des Absatzstils angewendet. Beim zweiten Anklicken werden die Texteigenschaften übernommen. Standardstile verwenden: Verfügbaren Textformatvorlagen anwenden: Positionieren Sie den Cursor im gewünschten Abschnitt bzw. wählen Sie mehrere Absätze aus, auf die Sie eine Formatvorlage anwenden möchten. Wählen Sie eine gewünschte Vorlage rechts in der Registerkarte Start aus. Die folgenden Formatvorlagen sind verfügbar: Standard, Kein Leerraum, Überschrift 1-9, Titel, Untertitel, Zitat, intensives Zitat, Listenabsatz. Vorhandene Formatierungen bearbeiten und neue erstellen Eine vorhandene Formatierung ändern Wenden Sie die gewünschte Formatierung auf einen Absatz an. Markieren Sie den entsprechenden Absatz und ändern Sie alle benötigten Formatierungsparameter. Speichern Sie die vorgenommenen Änderungen: Klicken Sie mit der rechten Maustaste auf den editierten Text, wählen Sie die Option Formatierung als Vorlage und klicken Sie dann auf die Option „Stilname“ aktualisieren (der „Stilname“ entspricht dem Namen der Vorlagen, die Sie in Schritt 1 angewendet haben) oder wählen Sie die bearbeitete Textstelle mit der Maus aus, ziehen Sie die Stilgalerie herunter, klicken Sie mit der rechten Maustaste auf die Formatvorlage, die Sie ändern möchten und wählen Sie die Option Aus Auswahl aktualisieren. Wenn eine Formatvorlage geändert wird, werden alle Absätze innerhalb eines Dokuments entsprechend geändert, die mit dieser Vorlage formatiert worden sind. Erstellen einer neuen Vorlage: Formatieren Sie einen Textabschnitt nach Ihrem Belieben. Wählen Sie eine geeignete Methode um den Stil als Vorlage zu speichern: Klicken Sie mit der rechten Maustaste auf den editierten Text, wählen Sie die Option Formatierung als Vorlage und klicken Sie dann auf die Option Neue Vorlage erstellen oder wählen Sie die bearbeitete Textstelle mit der Maus aus, ziehen Sie die Stilgalerie herunter, klicken Sie auf die Option Auswahl als neue Vorlage übernehmen. Legen Sie die Parameter im Fenster Neue Vorlage erstellen fest: Geben Sie den Namen der neuen Formatvorlage in das dafür vorgesehene Texteingabefeld ein. Wählen Sie den gewünschten Stil für den folgenden Absatz aus der Liste Nächsten Absatz formatieren. Klicken Sie auf OK. Die neue Stilvorlage wird der Stilgalerie hinzugefügt. Benutzerdefinierten Vorlagen verwalten: Um die Standardeinstellungen einer bestimmten geänderten Formatvorlage wiederherzustellen, klicken Sie mit der rechten Maustaste auf die Vorlage, die Sie wiederherstellen möchten und wählen Sie die Option Standard wiederherstellen Um die Standardeinstellungen aller Formatvorlage wiederherzustellen, die Sie geändert haben, klicken Sie mit der rechten Maustaste auf eine Standardvorlage in der Stilgalerie und wählen Sie die Option Ale Standardvorlagen wiederherstellen Um eine der neu erstellten Vorlagen zu löschen, klicken Sie mit der rechten Maustaste auf den entsprechenden Stil und wählen Sie die Option Formatvorlage löschen. Um alle neu erstellten Vorlagen zu löschen, klicken Sie mit der rechten Maustaste auf eine beliebige neu erstellte Vorlage und wählen Sie die Option Alle benutzerdefinierten Vorlagen löschen."
    },
   {
        "id": "UsageInstructions/HTML.htm", 
        "title": "HTML bearbeiten", 
        "body": "Wenn Sie eine Website-Seite in einem Texteditor schreiben und sie als HTML-Code erhalten möchten, verwenden Sie das HTML-Plugin. Öffnen Sie die Registerkarte Plugins und klicken Sie auf Get and paste html. Wählen Sie die erforderlichen Inhalte aus. Der HTML-Code des ausgewählten Absatzes wird im Plugin-Feld auf der linken Seite angezeigt. Sie können den Code bearbeiten, um die Textmerkmale zu ändern, z.B. Schriftgröße oder Schriftfamilie usw. Klicken Sie auf Paste into the document, um den Text mit seinem bearbeiteten HTML-Code an der aktuellen Cursorposition in Ihr Dokument einzufügen. Sie können auch Ihren eigenen HTML-Code schreiben (ohne einen Dokumentinhalt auszuwählen) und ihn dann in Ihr Dokument einfügen. Weitere Informationen zum HTML-Plugin und seiner Installation finden Sie auf der Plugin-Seite in AppDirectory."
    },
   {
        "id": "UsageInstructions/HighlightedCode.htm", 
        "title": "Hervorgehobenen Code einfügen", 
        "body": "Im Dokumenteneditor können Sie hervorgehobenen Code mit dem schon angepassten Stil entsprechend der Programmiersprache und dem Farbstil des von Ihnen ausgewählten Programms einfügen. Gehen Sie zu Ihrem Dokument und platzieren Sie den Cursor an der Stelle, an der Sie den Code einfügen möchten. Öffnen Sie die Registerkarte Plugins und wählen Sie den Menüpunkt Code hervorheben aus. Geben Sie die Programmiersprache an. Wählen Sie einen Code-Stil aus, der so aussieht, als wäre er in diesem Programm geöffnet. Geben Sie an, ob Sie Tabulatoren durch Leerzeichen ersetzen möchten. Wählen Sie Hintergrundfarbe. Bewegen Sie dazu den Cursor manuell über die Palette oder fügen Sie den RGB/HSL/HEX-Wert ein. Klicken Sie auf OK, um den Code einzufügen."
    },
   {
        "id": "UsageInstructions/InsertAutoshapes.htm", 
        "title": "AutoFormen einfügen", 
        "body": "Fügen Sie eine automatische Form ein So fügen Sie Ihrem Dokument eine automatische Form hinzu im Dokumenteneditor: Wechseln Sie zur Registerkarte Einfügen in der oberen Symbolleiste. Klicken Sie auf das Formsymbol in der oberen Symbolleiste. Wählen Sie eine der verfügbaren Autoshape-Gruppen aus der Formengalerie aus: Zuletzt verwendet, Standardformen, Geformte Pfeile, Mathematik, Diagramme, Sterne und Bänder, Legenden, Buttons, Rechtecke, Linien. Klicken Sie auf die erforderliche automatische Form innerhalb der ausgewählten Gruppe. Platzieren Sie den Mauszeiger an der Stelle, an der die Form plaziert werden soll. Sobald die automatische Form hinzugefügt wurde, können Sie ihre Größe, Position und Eigenschaften ändern. Um eine Beschriftung innerhalb der automatischen Form hinzuzufügen, stellen Sie sicher, dass die Form auf der Seite ausgewählt ist, und geben Sie Ihren Text ein. Der auf diese Weise hinzugefügte Text wird Teil der automatischen Form (wenn Sie die Form verschieben oder drehen, wird der Text mit verschoben oder gedreht). Es ist auch möglich, der automatischen Form eine Beschriftung hinzuzufügen. Weitere Informationen zum Arbeiten mit Untertiteln für Autoformen finden Sie in diesem Artikel. Verschieben und ändern Sie die Größe von Autoformen Um die Größe der automatischen Form zu ändern, ziehen Sie kleine Quadrate an den Formkanten. Halten Sie die Umschalttaste gedrückt und ziehen Sie eines der Eckensymbole, um die ursprünglichen Proportionen der ausgewählten automatischen Form während der Größenänderung beizubehalten. Wenn Sie einige Formen ändern, z. B. abgebildete Pfeile oder Beschriftungen, ist auch das gelbe rautenförmige Symbol verfügbar. Hier können Sie einige Aspekte der Form anpassen, z. B. die Länge der Pfeilspitze. Verwenden Sie zum Ändern der Position der automatischen Form das Symbol , das angezeigt wird, nachdem Sie den Mauszeiger über die automatische Form bewegt haben. Ziehen Sie die Autoform an die gewünschte Position, ohne die Maustaste loszulassen. Wenn Sie die automatische Form verschieben, werden die Hilfslinien angezeigt, damit Sie das Objekt präzise auf der Seite positionieren können (wenn der ausgewählte Umbruchstil nicht „Inline“ ist). Um die automatische Form in Ein-Pixel-Schritten zu verschieben, halten Sie die Strg-Taste gedrückt und verwenden Sie die Pfeiltasten. Um die automatische Form streng horizontal/vertikal zu verschieben und zu verhindern, dass sie sich in eine senkrechte Richtung bewegt, halten Sie beim Ziehen die Umschalttaste gedrückt. Um die automatische Form zu drehen, bewegen Sie den Mauszeiger über den Drehgriff und ziehen Sie ihn im oder gegen den Uhrzeigersinn. Halten Sie die Umschalttaste gedrückt, um den Drehwinkel auf Schritte von 15 Grad zu beschränken. Die Liste der Tastaturkürzel, die beim Arbeiten mit Objekten verwendet werden können, finden Sie hier. AutoForm-Einstellungen anpassen Verwenden Sie das Rechtsklick-Menü, um Autoshapes auszurichten und anzuordnen. Die Menüoptionen sind: Ausschneiden, Kopieren, Einfügen - Standardoptionen, mit denen ein ausgewählter Text / ein ausgewähltes Objekt ausgeschnitten oder kopiert und eine zuvor ausgeschnittene / kopierte Textpassage oder ein Objekt an die aktuelle Zeigerposition eingefügt wird. Auswahl drucken wird verwendet, um nur einen ausgewählten Teil des Dokuments auszudrucken. Änderungen annehmen / ablehnen wird verwendet, um nachverfolgte Änderungen in einem freigegebenen Dokument anzunehmen oder abzulehnen. Punkte bearbeiten wird verwendet, um die Krümmung Ihrer Form anzupassen oder zu ändern. Um die bearbeitbaren Ankerpunkte einer Form zu aktivieren, klicken Sie mit der rechten Maustaste auf die Form und wählen Sie im Menü Punkte bearbeiten aus. Die schwarzen Quadrate, die aktiv werden, sind die Punkte, an denen sich zwei Linien treffen, und die rote Linie umreißt die Form. Klicken und ziehen Sie ihn, um den Punkt neu zu positionieren und den Umriss der Form zu ändern. Sobald Sie auf den Ankerpunkt klicken, werden zwei blaue Linien mit weißen Quadraten an den Enden angezeigt. Dies sind Bezier-Ziehpunkte, mit denen Sie eine Kurve erstellen und die Glätte einer Kurve ändern können. Solange die Ankerpunkte aktiv sind, können Sie sie hinzufügen und löschen. Um einer Form einen Punkt hinzuzufügen, halten Sie Strg gedrückt und klicken Sie auf die Position, an der Sie einen Ankerpunkt hinzufügen möchten. Um einen Punkt zu löschen, halten Sie Strg gedrückt und klicken Sie auf den unnötigen Punkt. Anordnen wird verwendet, um die ausgewählte automatische Form in den Vordergrund zu bringen, in den Hintergrund zu senden, vorwärts oder rückwärts zu bewegen sowie Formen zu gruppieren oder die Gruppierung aufzuheben, um Operationen mit mehreren von ihnen gleichzeitig auszuführen. Weitere Informationen zum Anordnen von Objekten finden Sie auf dieser Seite. Ausrichten wird verwendet, um die Form links, mittig, rechts, oben, Mitte, unten auszurichten. Weitere Informationen zum Ausrichten von Objekten finden Sie auf dieser Seite. Der Umbruchstil wird verwendet, um einen Textumbruchstil aus den verfügbaren auszuwählen - inline, quadratisch, eng, durch, oben und unten, vorne, hinten - oder um die Umbruchgrenze zu bearbeiten. Die Option Wrap-Grenze bearbeiten ist nur verfügbar, wenn Sie einen anderen Wrap-Stil als Inline auswählen. Ziehen Sie die Umbruchpunkte, um die Grenze anzupassen. Um einen neuen Umbruchpunkt zu erstellen, klicken Sie auf eine beliebige Stelle auf der roten Linie und ziehen Sie sie an die gewünschte Position. Drehen wird verwendet, um die Form um 90 Grad im oder gegen den Uhrzeigersinn zu drehen sowie um die Form horizontal oder vertikal zu drehen. Mit den Erweiterten Einstellungen der Form wird das Fenster \"Form - Erweiterte Einstellungen\" geöffnet. Einige der Einstellungen für die automatische Form können über die Registerkarte Formeinstellungen in der rechten Seitenleiste geändert werden. Um es zu aktivieren, klicken Sie auf die Form und wählen Sie rechts das Symbol Formeinstellungen . Hier können Sie folgende Eigenschaften ändern: Füllen - Verwenden Sie diesen Abschnitt, um die automatische Formfüllung auszuwählen. Sie können folgende Optionen auswählen: Farbfüllung - Wählen Sie diese Option aus, um die Volltonfarbe anzugeben, mit der Sie den Innenraum der ausgewählten Autoform füllen möchten. Klicken Sie auf das farbige Feld unten und wählen Sie die gewünschte Farbe aus den verfügbaren Farbsätzen aus oder geben Sie eine beliebige Farbe an: Füllung mit Farbverlauf - wählen Sie diese Option, um die Form mit einem sanften Übergang von einer Farbe zu einer anderen zu füllen. Die verfügbaren Menüoptionen: Stil - wählen Sie Linear oder Radial aus: Linear wird verwendet, wenn Ihre Farben von links nach rechts, von oben nach unten oder in einem beliebigen Winkel in eine Richtung fließen sollen. Das Vorschaufenster Richtung zeigt die ausgewählte Verlaufsfarbe an. Klicken Sie auf den Pfeil, um eine voreingestellte Verlaufsrichtung auszuwählen. Verwenden Sie Winkel-Einstellungen für einen präzisen Verlaufswinkel. Radial wird verwendet, um sich von der Mitte zu bewegen, da die Farbe an einem einzelnen Punkt beginnt und nach außen ausstrahlt. Punkt des Farbverlaufs ist ein bestimmter Punkt für den Verlauf von einer Farbe zur anderen. Verwenden Sie die Schaltfläche Punkt des Farbverlaufs einfügen oder den Schieberegler, um einen Punkt des Verlaufs einzufügen. Sie können bis zu 10 Punkte einfügen. Jeder nächste eingefügte Punkt des Farbverlaufs beeinflusst in keiner Weise die aktuelle Darstellung der Farbverlaufsfüllung. Verwenden Sie die Schaltfläche Punkt des Farbverlaufs entfernen, um den bestimmten Punkt zu löschen. Verwenden Sie den Schieberegler, um die Position des Farbverlaufspunkts zu ändern, oder geben Sie Position in Prozent an, um eine genaue Position zu erhalten. Um eine Farbe auf einen Verlaufspunkt anzuwenden, klicken Sie auf einen Punkt im Schieberegler und dann auf Farbe, um die gewünschte Farbe auszuwählen. Bild oder Textur - Wählen Sie diese Option, um ein Bild oder eine vordefinierte Textur als Formhintergrund zu verwenden. Wenn Sie ein Bild als Hintergrund für die Form verwenden möchten, können Sie ein Bild aus der Datei hinzufügen, indem Sie es auf der Festplatte Ihres Computers auswählen, oder aus der URL, indem Sie die entsprechende URL-Adresse in das geöffnete Fenster einfügen. Wenn Sie eine Textur als Hintergrund für die Form verwenden möchten, öffnen Sie das Menü Von Textur und wählen Sie die gewünschte Texturvoreinstellung aus. Derzeit sind folgende Texturen verfügbar: Leinwand, Karton, dunkler Stoff, Maserung, Granit, graues Papier, Strick, Leder, braunes Papier, Papyrus, Holz. Falls das ausgewählte Bild weniger oder mehr Abmessungen als die automatische Form hat, können Sie die Einstellung Dehnen oder Kacheln aus der Dropdown-Liste auswählen.</p> Mit der Option Dehnen können Sie die Bildgröße an die Größe der automatischen Form anpassen, sodass sie den Raum vollständig ausfüllen kann. Mit der Option Kacheln können Sie nur einen Teil des größeren Bilds anzeigen, wobei die ursprünglichen Abmessungen beibehalten werden, oder das kleinere Bild wiederholen, wobei die ursprünglichen Abmessungen über der Oberfläche der automatischen Form beibehalten werden, sodass der Raum vollständig ausgefüllt werden kann. Jede ausgewählte Texturvoreinstellung füllt den Raum vollständig aus. Sie können jedoch bei Bedarf den Dehnungseffekt anwenden. Muster - Wählen Sie diese Option, um die Form mit einem zweifarbigen Design zu füllen, das aus regelmäßig wiederholten Elementen besteht. Muster - Wählen Sie eines der vordefinierten Designs aus dem Menü. Vordergrundfarbe - Klicken Sie auf dieses Farbfeld, um die Farbe der Musterelemente zu ändern. Hintergrundfarbe - Klicken Sie auf dieses Farbfeld, um die Farbe des Musterhintergrunds zu ändern. Keine Füllung - wählen Sie diese Option, wenn Sie keine Füllung verwenden möchten. Deckkraft - Verwenden Sie diesen Abschnitt, um eine Deckkraftstufe festzulegen, indem Sie den Schieberegler ziehen oder den Prozentwert manuell eingeben. Der Standardwert ist 100%. Es entspricht der vollen Deckkraft. Der Wert 0% entspricht der vollen Transparenz. Strich - Verwenden Sie diesen Abschnitt, um die Breite, Farbe oder den Typ des Strichs für die automatische Formgebung zu ändern. Um die Strichbreite zu ändern, wählen Sie eine der verfügbaren Optionen aus der Dropdown-Liste Größe. Die verfügbaren Optionen sind: 0,5 pt, 1 pt, 1,5 pt, 2,25 pt, 3 pt, 4,5 pt, 6 pt. Alternativ können Sie die Option Keine Linie auswählen, wenn Sie keinen Strich verwenden möchten. Um die Strichfarbe zu ändern, klicken Sie auf das farbige Feld unten und wählen Sie die gewünschte Farbe aus. Um den Strich-Typ zu ändern, wählen Sie die erforderliche Option aus der entsprechenden Dropdown-Liste aus (standardmäßig wird eine durchgezogene Linie angewendet, Sie können sie in eine der verfügbaren gestrichelten Linien ändern). Die Drehung wird verwendet, um die Form um 90 Grad im oder gegen den Uhrzeigersinn zu drehen sowie um die Form horizontal oder vertikal zu drehen. Klicken Sie auf eine der Schaltflächen: um die Form um 90 Grad gegen den Uhrzeigersinn zu drehen um die Form um 90 Grad im Uhrzeigersinn zu drehen um die Form horizontal zu drehen (von links nach rechts) um die Form vertikal zu drehen (verkehrt herum) Umbruchstil - Verwenden Sie diesen Abschnitt, um einen Textumbruchstil aus den verfügbaren auszuwählen - inline, quadratisch, eng, durch, oben und unten, vorne, hinten (weitere Informationen finden Sie in der Beschreibung der erweiterten Einstellungen unten). Autoshape ändern - Verwenden Sie diesen Abschnitt, um die aktuelle Autoshape durch eine andere zu ersetzen, die aus der Dropdown-Liste ausgewählt wurde. Schatten anzeigen - Aktivieren Sie diese Option, um die Form mit Schatten anzuzeigen. Die erweiterten Einstellungen für die automatische Form anpassen Um die erweiterten Einstellungen der automatischen Form zu ändern, klicken Sie mit der rechten Maustaste darauf und wählen Sie die Option Erweiterte Einstellungen im Menü oder verwenden Sie den Link Erweiterte Einstellungen anzeigen in der rechten Seitenleiste. Das Fenster 'Form - Erweiterte Einstellungen' wird geöffnet: Die Registerkarte Größe enthält die folgenden Parameter: Breite - Verwenden Sie eine dieser Optionen, um die automatische Formbreite zu ändern. Absolut - Geben Sie einen genauen Wert an, der in absoluten Einheiten gemessen wird, d. H. Zentimeter / Punkte / Zoll (abhängig von der auf der Registerkarte Datei -> Erweiterte Einstellungen ... angegebenen Option). Relativ - Geben Sie einen Prozentsatz relativ zur linken Randbreite, dem Rand (d. H. Dem Abstand zwischen dem linken und rechten Rand), der Seitenbreite oder der rechten Randbreite an. Höhe - Verwenden Sie eine dieser Optionen, um die Höhe der automatischen Form zu ändern. Absolut - Geben Sie einen genauen Wert an, der in absoluten Einheiten gemessen wird, d. H. Zentimeter / Punkte / Zoll (abhängig von der auf der Registerkarte Datei -> Erweiterte Einstellungen ... angegebenen Option). Relativ - Geben Sie einen Prozentsatz relativ zum Rand (d. H. Dem Abstand zwischen dem oberen und unteren Rand), der Höhe des unteren Randes, der Seitenhöhe oder der Höhe des oberen Randes an. Wenn die Option Seitenverhältnis sperren aktiviert ist, werden Breite und Höhe zusammen geändert, wobei das ursprüngliche Seitenverhältnis beibehalten wird. Die Registerkarte Rotation enthält die folgenden Parameter: Winkel - Verwenden Sie diese Option, um die Form um einen genau festgelegten Winkel zu drehen. Geben Sie den erforderlichen Wert in Grad in das Feld ein oder passen Sie ihn mit den Pfeilen rechts an. Gekippt - Aktivieren Sie das Kontrollkästchen Horizontal, um die Form horizontal umzudrehen (von links nach rechts), oder aktivieren Sie das Kontrollkästchen Vertikal, um die Form vertikal zu spiegeln (verkehrt herum). Die Registerkarte Textumbruch enthält die folgenden Parameter: Umbruchstil - Verwenden Sie diese Option, um die Position der Form relativ zum Text zu ändern: Sie ist entweder Teil des Textes (falls Sie den Inline-Stil auswählen) oder wird von allen Seiten umgangen (wenn Sie einen auswählen) die anderen Stile). Inline - Die Form wird wie ein Zeichen als Teil des Textes betrachtet. Wenn sich der Text bewegt, bewegt sich auch die Form. In diesem Fall sind die Positionierungsoptionen nicht zugänglich. Wenn einer der folgenden Stile ausgewählt ist, kann die Form unabhängig vom Text verschoben und genau auf der Seite positioniert werden: Quadratisch - Der Text umschließt das rechteckige Feld, das die Form begrenzt. Eng - Der Text umschließt die tatsächlichen Formkanten. Durch - Der Text wird um die Formkanten gewickelt und füllt den offenen weißen Bereich innerhalb der Form aus. Verwenden Sie die Option Umbruchgrenze bearbeiten im Kontextmenü, damit der Effekt angezeigt wird. Oben und unten - der Text befindet sich nur über und unter der Form. Vorne - die Form überlappt den Text. Dahinter - der Text überlappt die Form. Wenn Sie den quadratischen, engen, durchgehenden oder oberen und unteren Stil auswählen, können Sie einige zusätzliche Parameter festlegen - Abstand vom Text an allen Seiten (oben, unten, links, rechts). Die Registerkarte Position ist nur verfügbar, wenn Sie einen anderen Umbruchstil als Inline auswählen. Diese Registerkarte enthält die folgenden Parameter, die je nach ausgewähltem Verpackungsstil variieren: Im horizontalen Bereich können Sie einen der folgenden drei Positionierungstypen für die automatische Form auswählen: Ausrichtung (links, Mitte, rechts) relativ zu Zeichen, Spalte, linkem Rand, Rand, Seite oder rechtem Rand, Absolute Position, gemessen in absoluten Einheiten, d. H. Zentimeter/Punkte/Zoll (abhängig von der auf der Registerkarte Datei -&gt; Erweiterte Einstellungen... angegebenen Option), rechts neben Zeichen, Spalte, linkem Rand, Rand, Seite oder rechtem Rand. Relative Position gemessen in Prozent relativ zum linken Rand, Rand, Seite oder rechten Rand. Im vertikalen Bereich können Sie einen der folgenden drei Positionierungstypen für die automatische Form auswählen: Ausrichtung (oben, Mitte, unten) relativ zu Linie, Rand, unterem Rand, Absatz, Seite oder oberem Rand, Absolute Position gemessen in absoluten Einheiten, d. H. Zentimeter / Punkte / Zoll (abhängig von der auf der Registerkarte Datei -&gt; Erweiterte Einstellungen... angegebenen Option), unter Linie, Rand, unterem Rand, Absatz, Seite oder oberem Rand, Relative Position gemessen in Prozent relativ zum Rand, unteren Rand, Seite oder oberen Rand. Objekt mit Text verschieben steuert, ob sich die automatische Form so bewegt, wie sich der Text bewegt, an dem sie verankert ist. Überlappung steuert, ob sich zwei automatische Formen überlappen oder nicht, wenn Sie sie auf der Seite nebeneinander ziehen Die Registerkarte Stärken und Pfeile enthält die folgenden Parameter: Linienart - In dieser Optionsgruppe können die folgenden Parameter angegeben werden: Abschlusstyp - Mit dieser Option können Sie den Stil für das Ende der Linie festlegen. Daher kann er nur auf Formen mit offenem Umriss angewendet werden, z. B. Linien, Polylinien usw.: Flach - Die Endpunkte sind flach. Runden - Die Endpunkte werden gerundet. Quadrat - Die Endpunkte sind quadratisch. Verknüpfungstyp - Mit dieser Option können Sie den Stil für den Schnittpunkt zweier Linien festlegen. Dies kann sich beispielsweise auf eine Polylinie oder die Ecken des Dreiecks oder Rechteckumrisses auswirken: Rund - die Ecke wird abgerundet. Abschrägung - die Ecke wird eckig abgeschnitten. Gehrung - die Ecke wird spitz. Es passt gut zu Formen mit scharfen Winkeln. Der Effekt macht sich stärker bemerkbar, wenn Sie eine große Umrissbreite verwenden. Pfeile - Diese Optionsgruppe ist verfügbar, wenn eine Form aus der Formgruppe Linien ausgewählt ist. Sie können den Pfeil Start- und Endstil und -größe festlegen, indem Sie die entsprechende Option aus den Dropdown-Listen auswählen. Auf der Registerkarte Ränder um den Text können Sie die inneren Ränder der oberen, unteren, linken und rechten Form der automatischen Form ändern (d. H. Den Abstand zwischen dem Text innerhalb der Form und den Rahmen der automatischen Form). Diese Registerkarte ist nur verfügbar, wenn Text innerhalb der automatischen Form hinzugefügt wird. Andernfalls ist die Registerkarte deaktiviert. Auf der Registerkarte Der alternative Text können Sie einen Titel und eine Beschreibung angeben, die Personen mit Seh- oder kognitiven Beeinträchtigungen vorgelesen werden, damit sie besser verstehen, welche Informationen in der Form enthalten sind."
    },
   {
        "id": "UsageInstructions/InsertBookmarks.htm", 
        "title": "Lesezeichen hinzufügen", 
        "body": "Lesezeichen ermöglichen den schnellen Wechsel zu einer bestimmten Position im aktuellen Dokument oder das Hinzufügen eines Links an dieser Position im Dokument. Einfügen eines Lesezeichens im Dokumenteneditor: Legen Sie die Position fest, an der das Lesezeichen eingefügt werden soll: positionieren Sie den Mauszeiger am Beginn der gewünschten Textstelle oder wählen Sie die gewünschte Textpassage aus. Wechseln Sie in der oberen Symbolleiste auf die Registerkarte Verweise. Klicken Sie in der oberen Symbolleiste auf das Symbol Lesezeichen. Geben Sie im sich öffnenden Fenster Lesezeichen den Namen des Lesezeichens ein und klicken Sie auf die Schaltfläche Hinzufügen - das Lesezeichen wird der unten angezeigten Lesezeichenliste hinzugefügt. Der Name des Lesezeichens sollte mit einem Buchstaben beginnen, er kann jedoch auch Zahlen enthalten. Der Name des Lesezeichens darf keine Leerzeichen enthalten, ein Unterstrich \"_\" ist jedoch möglich. Um zu einem der hinzugefügten Lesezeichen im Dokumenttext zu wechseln: Klicken Sie in der oberen Symbolleiste in der Registerkarte Verweise auf das Symbol Lesezeichen. Wählen Sie im sich öffnenden Fenster Lesezeichen das Lesezeichen aus, zu dem Sie springen möchten. Um das erforderliche Lesezeichen in der Liste zu finden, können Sie die Liste nach Name oder Position eines Lesezeichens im Dokumenttext sortieren. Aktivieren Sie die Option Versteckte Lesezeichen, um ausgeblendete Lesezeichen in der Liste anzuzeigen (z. B. die vom Programm automatisch erstellten Lesezeichen, wenn Sie Verweise auf einen bestimmten Teil des Dokuments hinzufügen. Wenn Sie beispielsweise einen Hyperlink zu einer bestimmten Überschrift innerhalb des Dokuments erstellen, erzeugt der Dokumenteditor automatisch ein ausgeblendetes Lesezeichen für das Ziel dieses Links). Klicken Sie auf die Schaltfläche Wechseln zu - der Zeiger wird an der Stelle innerhalb des Dokuments positioniert, an der das ausgewählte Lesezeichen hinzugefügt wurde oder es wird die entsprechende Textpassage ausgewählt. Klicken Sie auf die Schaltfläche Link bekommen und ein neues Fenster öffnet sich wo Sie den Copy Schaltfläche drücken koennen um den Link zu der Datei zu kopieren, welches die Buckmarkierungstelle im Dokument spezifiziert. Um den Link mit anderen Benutzern zu teilen, muss auch die entsprechende Zugangskontrolle zu dieser Datei gesetzt werden. Benutzen Sie dazu die Teilen Funktion in die Schaltfläche Zusammenarbeit. Klicken Sie auf die Schaltfläche Schließen, um das Dialogfenster zu schließen. Um ein Lesezeichen zu löschen, wählen Sie den entsprechenden Namen aus der Liste der Lesezeichen aus und klicken Sie auf Löschen. Informationen zum Verwenden von Lesezeichen beim Erstellen von Links finden Sie im Abschnitt Hyperlinks hinzufügen."
    },
   {
        "id": "UsageInstructions/InsertCharts.htm", 
        "title": "Diagramme einfügen", 
        "body": "Fügen Sie ein Diagramm ein Um ein Diagramm im Dokumenteneditor einzufügen, Setzen Sie den Zeiger an die Stelle, an der Sie ein Diagramm hinzufügen möchten. Wechseln Sie zur Registerkarte Einfügen in der oberen Symbolleiste. Klicken Sie auf das Symbol Diagramm in der oberen Symbolleiste. Wählen Sie den gewünschten Diagrammtyp aus der Liste der verfügbaren Typen aus: Spalte Gruppierte Säule Gestapelte Säulen 100% Gestapelte Säule Gruppierte 3D-Säule Gestapelte 3D-Säule 3-D 100% Gestapelte Säule 3D-Säule Linie Linie Gestapelte Linie 100% Gestapelte Linie Linie mit Datenpunkten Gestapelte Linie mit Datenpunkten 100% Gestapelte Linie mit Datenpunkten 3D-Linie Kreis Kreis Ring 3D-Kreis Balken Gruppierte Balken Gestapelte Balken 100% Gestapelte Balken Gruppierte 3D-Balken Gestapelte 3D-Balken 3-D 100% Gestapelte Balken Fläche Fläche Gestapelte Fläche 100% Gestapelte Fläche Kurs Punkte (XY) Punkte Gestapelte Balken Punkte mit interpolierten Linien und Datenpunkten Punkte mit interpolierten Linien Punkte mit geraden Linien und Datenpunkten Punkte mit geraden Linien Verbund Gruppierte Säulen - Linie Gruppierte Säulen / Linien auf der Sekundärachse Gestapelte Flächen / Gruppierte Säulen Benutzerdefinierte Kombination ONLYOFFICE Dokumenteneditor unterstützt die folgenden Arten von Diagrammen, die mit Editoren von Drittanbietern erstellt wurden: Pyramide, Balken (Pyramide), horizontale/vertikale Zylinder, horizontale/vertikale Kegel. Sie können die Datei, die ein solches Diagramm enthält, öffnen und sie mit den verfügbaren Diagrammbearbeitungswerkzeugen ändern. Danach erscheint das Fenster Diagrammeditor, in dem Sie die erforderlichen Daten mit den folgenden Steuerelementen in die Zellen eingeben können: und zum Kopieren und Einfügen der kopierten Daten. und zum Rückgängigmachen und Wiederherstellen von Aktionen zum Einfügen einer Funktion und zum Verringern und Erhöhen von Dezimalstellen zum Ändern des Zahlenformats, d. h. der Art und Weise, wie die von Ihnen eingegebenen Zahlen in Zellen angezeigt werden zur Auswahl eines anderen Diagrammtyps. Klicken Sie auf die Schaltfläche Daten auswählen im Fenster Diagramm bearbeiten. Das Fenster Diagrammdaten wird geöffnet. Verwenden Sie das Dialogfeld Diagrammdaten, um den Diagrammdatenbereich, Legendeneinträge (Reihen), Horizontale Achsenbeschriftungen (Rubrik) zu verwalten und Zeile/Spalte ändern. Diagrammdatenbereich - wählen Sie Daten für Ihr Diagramm aus. Klicken Sie auf das Symbol rechts neben dem Feld Diagrammdatenbereich, um den Datenbereicht auszuwählen. Legendeneinträge (Reihen) - Hinzufügen, Bearbeiten oder Entfernen von Legendeneinträgen. Geben Sie den Reihennamen für Legendeneinträge ein oder wählen Sie ihn aus. Im Feld Legendeneinträge (Reihen) klicken Sie auf die Schaltfläche Hinzufügen. Im Fenster Datenreihe bearbeiten geben Sie einen neuen Legendeneintrag ein oder klicken Sie auf das Symbol rechts neben dem Feld Reihenname. Horizontale Achsenbeschriftungen (Rubrik) - den Text für Achsenbeschriftungen ändern. Im Feld Horizontale Achsenbeschriftungen (Rubrik) klicken Sie auf Bearbeiten. Im Feld Der Bereich von Achsenbeschriftungen geben Sie die gewünschten Achsenbeschriftungen ein oder klicken Sie auf das Symbol rechts neben dem Feld Der Bereich von Achsenbeschriftungen, um den Datenbereich auszuwählen. Zeile/Spalte ändern - ordnen Sie die im Diagramm konfigurierten Arbeitsblattdaten so an, wie Sie sie möchten. Wechseln Sie zu Spalten, um Daten auf einer anderen Achse anzuzeigen. Klicken Sie auf die Schaltfläche OK, um die Änderungen anzuwenden und das Fenster schließen. Klicken Sie auf die Schaltfläche Diagramm bearbeiten im Fenster Diagrammeditor, um den Diagrammtyp und -stil auszuwählen. Wählen Sie den Diagrammtyp aus der Liste der verfügbaren Typen aus: Spalte, Linie, Kreis, Balken, Fläche, Kurs, Punkte (XY) oder Verbund. Wenn Sie den Typ Verbund auswählen, listet das Fenster Diagrammtyp die Diagrammreihen auf und ermöglicht die Auswahl der zu kombinierenden Diagrammtypen und die Auswahl von Datenreihen, die auf einer Sekundärachse platziert werden sollen. Ändern Sie die Diagrammeinstellungen, indem Sie auf die Schaltfläche Diagramm bearbeiten im Fenster Diagrammeditor klicken. Das Fenster Diagramm - Erweiterte Einstellungen wird geöffnet. Auf der Registerkarte Layout können Sie das Layout von Diagrammelementen ändern. Geben Sie die Position des Diagrammtitels in Bezug auf Ihr Diagramm an und wählen Sie die erforderliche Option aus der Dropdown-Liste aus: Keine, um keinen Diagrammtitel anzuzeigen, Überlagern, um einen Titel auf dem Plotbereich zu überlagern und zu zentrieren. Keine Überlagerung, um den Titel über dem Plotbereich anzuzeigen. Geben Sie die Position der Legende in Bezug auf Ihr Diagramm an und wählen Sie die erforderliche Option aus der Dropdown-Liste aus: Keine, um keine Legende anzuzeigen, Unten, um die Legende anzuzeigen und am unteren Rand des Plotbereichs auszurichten. Oben, um die Legende anzuzeigen und am oberen Rand des Plotbereichs auszurichten. Rechts, um die Legende anzuzeigen und rechts vom Plotbereich auszurichten, Links, um die Legende anzuzeigen und links vom Plotbereich auszurichten. Linke Überlagerung zum Überlagern und Zentrieren der Legende links im Plotbereich. Rechte Überlagerung zum Überlagern und Zentrieren der Legende rechts im Plotbereich. Geben Sie die Parameter für Datenbeschriftungen (d. H. Textbeschriftungen, die exakte Werte von Datenpunkten darstellen) an: Geben Sie die Position der Datenbeschriftungen relativ zu den Datenpunkten an und wählen Sie die erforderliche Option aus der Dropdown-Liste aus. Die verfügbaren Optionen variieren je nach ausgewähltem Diagrammtyp. Für Spalten- / Balkendiagramme können Sie die folgenden Optionen auswählen: Keine, Mitte, Innen unten, Innen oben, Außen oben. Für Linien- / XY- / Kurs-Diagramme können Sie die folgenden Optionen auswählen: Keine, Mitte, Links, Rechts, Oben, Unten. Für Kreisdiagramme können Sie die folgenden Optionen auswählen: Keine, Mitte, An Breite anpassen, Innen oben, Außen oben. Für Flächendiagramme sowie für 3D-Spalten-, Linien- und Balkendiagramme können Sie die folgenden Optionen auswählen: Keine, Mitte. Wählen Sie die Daten aus, die Sie in Ihre Etiketten aufnehmen möchten, und aktivieren Sie die entsprechenden Kontrollkästchen: Serienname, Kategorienname, Wert. Geben Sie ein Zeichen (Komma, Semikolon etc.), das Sie zum Trennen mehrerer Beschriftungen verwenden möchten, in das Eingabefeld Datenetiketten-Trennzeichen ein. Linien - wird verwendet, um einen Linienstil für Linien- / XY-Diagramme (Punktdiagramme) auszuwählen. Sie können eine der folgenden Optionen auswählen: Gerade, um gerade Linien zwischen Datenpunkten zu verwenden, Glätten, um glatte Kurven zwischen Datenpunkten zu verwenden, oder Keine, um keine Linien anzuzeigen. Markierungen - wird verwendet, um anzugeben, ob die Markierungen für Linien- / XY-Diagramme angezeigt werden sollen (wenn das Kontrollkästchen aktiviert ist) oder nicht (wenn das Kontrollkästchen deaktiviert ist). Die Optionen Linien und Markierungen sind nur für Liniendiagramme und XY-Diagramme verfügbar. Auf der Registerkarte Vertikale Achse können Sie die Parameter der vertikalen Achse ändern, die auch als Werteachse oder y-Achse bezeichnet wird und numerische Werte anzeigt. Beachten Sie, dass die vertikale Achse die Kategorieachse ist, auf der Textbeschriftungen für die Balkendiagramme angezeigt werden. In diesem Fall entsprechen die Optionen der Registerkarte Vertikale Achse den Optionen, die im nächsten Abschnitt beschrieben werden. Für die Punkte (XY)-Diagramme sind beide Achsen Wertachsen. Die Abschnitte Achseneinstellungen und Gitterlinien werden für Kreisdiagramme deaktiviert, da Diagramme dieses Typs keine Achsen und Gitterlinien haben. Wählen Sie Ausblenden, um die vertikale Achse im Diagramm auszublenden, und lassen Sie das Kontrollkästchen deaktiviert, damit die vertikale Achse angezeigt wird. Geben Sie die Ausrichtung des Titels an und wählen Sie die erforderliche Option aus der Dropdown-Liste aus: Keine, um keinen vertikalen Achsentitel anzuzeigen, Gedreht, um den Titel von unten nach oben links von der vertikalen Achse anzuzeigen. Horizontal, um den Titel horizontal links von der vertikalen Achse anzuzeigen. Die Option Minimalwert wird verwendet, um den niedrigsten Wert anzugeben, der beim Start der vertikalen Achse angezeigt wird. Die Option Automatisch ist standardmäßig ausgewählt. In diesem Fall wird der Mindestwert abhängig vom ausgewählten Datenbereich automatisch berechnet. Sie können die Option Fixiert aus der Dropdown-Liste auswählen und im Eingabefeld rechts einen anderen Wert angeben. Die Option Maximalwert wird verwendet, um den höchsten Wert anzugeben, der am Ende der vertikalen Achse angezeigt wird. Die Option Automatisch ist standardmäßig ausgewählt. In diesem Fall wird der Maximalwert abhängig vom ausgewählten Datenbereich automatisch berechnet. Sie können die Option Fixiert aus der Dropdown-Liste auswählen und im Eingabefeld rechts einen anderen Wert angeben. Die Option Schnittpunkt mit der Achse wird verwendet, um einen Punkt auf der vertikalen Achse anzugeben, an dem die horizontale Achse ihn kreuzen soll. Die Option Automatisch ist standardmäßig ausgewählt. In diesem Fall wird der Achsenschnittpunktwert abhängig vom ausgewählten Datenbereich automatisch berechnet. Sie können die Option Wert aus der Dropdown-Liste auswählen und im Eingabefeld rechts einen anderen Wert angeben oder den Achsenschnittpunkt auf den Minimal-/Maximalwert auf der vertikalen Achse setzen. Die Option Anzeigeeinheiten wird verwendet, um die Darstellung der numerischen Werte entlang der vertikalen Achse zu bestimmen. Diese Option kann nützlich sein, wenn Sie mit großen Zahlen arbeiten und möchten, dass die Werte auf der Achse kompakter und lesbarer angezeigt werden (z.B. können Sie 50.000 als 50 darstellen, indem Sie die Option Tausende verwenden). Wählen Sie die gewünschten Einheiten aus der Dropdown-Liste aus: Hunderte, Tausende, 10 000, 100 000, Millionen, 10 000 000, 100 000 000, Milliarden, Billionen oder wählen Sie die Option Kein, um zu den Standardeinheiten zurückzukehren. Die Option Werte in umgekehrter Reihenfolge wird verwendet, um Werte in die entgegengesetzte Richtung anzuzeigen. Wenn das Kontrollkästchen deaktiviert ist, befindet sich der niedrigste Wert unten und der höchste Wert oben auf der Achse. Wenn das Kontrollkästchen aktiviert ist, werden die Werte von oben nach unten sortiert. Im Abschnitt Parameter der Teilstriche können Sie das Erscheinungsbild von Häkchen auf der vertikalen Skala anpassen. Hauptmarkierungen sind die größeren Teilungen, bei denen Beschriftungen numerische Werte anzeigen können. Kleinere Häkchen sind die Skalenunterteilungen, die zwischen den großen Häkchen platziert werden und keine Beschriftungen haben. Häkchen definieren auch, wo Gitterlinien angezeigt werden können, wenn die entsprechende Option auf der Registerkarte Layout festgelegt ist. Die Dropdown-Listen Primärer / Sekundärer Typ enthalten die folgenden Platzierungsoptionen: Kein, um keine Haupt- / Nebenmarkierungen anzuzeigen, Schnittpunkt, um auf beiden Seiten der Achse Haupt- / Nebenmarkierungen anzuzeigen. In, um Haupt- / Nebenmarkierungen innerhalb der Achse anzuzeigen, Außen, um Haupt- / Nebenmarkierungen außerhalb der Achse anzuzeigen. Im Abschnitt Beschriftungsoptionen können Sie das Erscheinungsbild der wichtigsten Häkchenbeschriftungen anpassen, auf denen Werte angezeigt werden. Um eine Beschriftungsposition in Bezug auf die vertikale Achse festzulegen, wählen Sie die erforderliche Option aus der Dropdown-Liste aus: Keine, um keine Häkchenbeschriftungen anzuzeigen, Niedrig, um Markierungsbeschriftungen links vom Plotbereich anzuzeigen. Hoch, um Markierungsbeschriftungen rechts vom Plotbereich anzuzeigen. Neben der Achse, um Markierungsbezeichnungen neben der Achse anzuzeigen. Um das Bezeichnungsformat anzupassen, klicken Sie auf die Schaltfläche Bezeichnungsformat und wählen Sie den gewünschten Typ aus. Verfügbare Bezeichnungsformate: Allgemein Nummer Wissenschaftlich Rechnungswesen Währung Datum Zeit Prozentsatz Bruch Text Benutzerdefiniert Die Optionen für das Bezeichnungsformat variieren je nach ausgewähltem Typ. Weitere Informationen zum Ändern des Zahlenformats finden Sie auf dieser Seite. Aktivieren Sie das Kästchen Mit Quelle verknüpft, um die Formatierung der Zahlen aus der Datenquelle im Diagramm beizubehalten. Sekundärachsen werden nur in den Verbund-Diagrammen verfügbar. Sekundärachsen sind in Verbund-Diagrammen nützlich, wenn Datenreihen erheblich variieren oder gemischte Datentypen zum Zeichnen eines Diagramms verwendet werden. Sekundärachsen erleichtern das Lesen und Verstehen eines Verbund-Diagramms. Die Registerkarte Vertikale/horizontale Sekundärachse wird angezeigt, wenn Sie eine geeignete Datenreihe für ein Verbund-Diagramm auswählen. Alle Einstellungen und Optionen auf der Registerkarte Vertikale/horizontale Sekundärachse sind den Einstellungen auf der vertikalen/horizontalen Achse ähnlich. Eine detaillierte Beschreibung der Optionen Vertikale/Horizontale Achse finden Sie in der Beschreibung oben/unten. Auf der Registerkarte Horizontale Achse können Sie die Parameter der horizontalen Achse ändern, die auch als Kategorieachse oder x-Achse bezeichnet wird und Textbeschriftungen anzeigt. Beachten Sie, dass die horizontale Achse die Werteachse ist, auf der numerische Werte für die Balkendiagramme angezeigt werden. In diesem Fall entsprechen die Optionen der Registerkarte Horizontale Achse den Optionen im vorherigen Abschnitt. Für die Punkte (XY)-Diagramme sind beide Achsen Wertachsen. Wählen Sie Ausblenden, um die horizontale Achse im Diagramm auszublenden, und lassen Sie das Kontrollkästchen deaktiviert, damit die horizontale Achse angezeigt wird. Geben Sie die Ausrichtung des Titels an und wählen Sie die erforderliche Option aus der Dropdown-Liste aus: Kein, um keinen horizontalen Achsentitel anzuzeigen, Ohne Überlagerung, um den Titel unterhalb der horizontalen Achse anzuzeigen, Die Option Gitternetzlinien wird verwendet, um die anzuzeigenden horizontalen Gitternetzlinien anzugeben, indem die erforderliche Option aus der Dropdown-Liste ausgewählt wird: Kein, Primäre, Sekundär oder Primäre und Sekundäre. Die Option Schnittpunkt mit der Achse wird verwendet, um einen Punkt auf der horizontalen Achse anzugeben, an dem die vertikale Achse ihn kreuzen soll. Die Option Automatisch ist standardmäßig ausgewählt. In diesem Fall wird der Achsenschnittpunktwert abhängig vom ausgewählten Datenbereich automatisch berechnet. Sie können die Option Wert aus der Dropdown-Liste auswählen und im Eingabefeld rechts einen anderen Wert angeben oder den Achsenschnittpunkt auf den Minimal-/Maximalwert auf der horizontalen Achse setzen. Die Option Position der Achse wird verwendet, um anzugeben, wo die Achsentextbeschriftungen platziert werden sollen: Teilstriche oder Zwischen den Teilstrichen. Die Option Werte in umgekehrter Reihenfolge wird verwendet, um Werte in die entgegengesetzte Richtung anzuzeigen. Wenn das Kontrollkästchen deaktiviert ist, befindet sich der niedrigste Wert unten und der höchste Wert oben auf der Achse. Wenn das Kontrollkästchen aktiviert ist, werden die Werte von oben nach unten sortiert. Im Abschnitt Parameter der Teilstriche können Sie das Erscheinungsbild von Häkchen auf der horizontalen Skala anpassen. Hauptmarkierungen sind die größeren Teilungen, bei denen Beschriftungen numerische Werte anzeigen können. Kleinere Häkchen sind die Skalenunterteilungen, die zwischen den großen Häkchen platziert werden und keine Beschriftungen haben. Häkchen definieren auch, wo Gitterlinien angezeigt werden können, wenn die entsprechende Option auf der Registerkarte Layout festgelegt ist. Die Dropdown-Listen Primärer / Sekundärer Typ enthalten die folgenden Platzierungsoptionen: Die Option Primärer/Sekundärer Typ wird verwendet, um die folgenden Platzierungsoptionen anzugeben: Kein, um keine primäre/sekundäre Teilstriche anzuzeigen, Schnittpunkt, um primäre/sekundäre Teilstriche auf beiden Seiten der Achse anzuzeigen, In, um primäre/sekundäre Teilstriche innerhalb der Achse anzuzeigen, Außen, um primäre/sekundäre Teilstriche außerhalb der Achse anzuzeigen. Die Option Abstand zwischen Teilstrichen wird verwendet, um anzugeben, wie viele Kategorien zwischen zwei benachbarten Teilstrichen angezeigt werden sollen. Im Abschnitt Beschriftungsoptionen können Sie das Erscheinungsbild der wichtigsten Häkchenbeschriftungen anpassen, auf denen Werte angezeigt werden. Die Option Beschriftungsposition wird verwendet, um anzugeben, wo die Beschriftungen in Bezug auf die horizontale Achse platziert werden sollen. Wählen Sie die gewünschte Option aus der Dropdown-Liste: Kein, um die Beschriftungen nicht anzuzeigen, Niedrig, um Beschriftungen am unteren Rand anzuzeigen, Hoch, um Beschriftungen oben anzuzeigen, Neben der Achse, um Beschriftungen neben der Achse anzuzeigen. Die Option Abstand bis zur Beschriftung wird verwendet, um anzugeben, wie eng die Beschriftungen an der Achse platziert werden sollen. Sie können den erforderlichen Wert im Eingabefeld angeben. Je mehr Wert Sie einstellen, desto größer ist der Abstand zwischen Achse und Beschriftung. Die Option Abstand zwischen Teilstrichen wird verwendet, um anzugeben, wie oft die Beschriftungen angezeigt werden sollen. Die Option Automatisch ist standardmäßig ausgewählt. In diesem Fall werden Beschriftungen für jede Kategorie angezeigt. Sie können die Option Manuell aus der Dropdown-Liste auswählen und den erforderlichen Wert im Eingabefeld rechts angeben. Geben Sie beispielsweise 2 ein, um Beschriftungen für jede zweite Kategorie usw. anzuzeigen. Um das Bezeichnungsformat anzupassen, klicken Sie auf die Schaltfläche Bezeichnungsformat und wählen Sie den gewünschten Typ aus. Verfügbare Bezeichnungsformate: Allgemein Nummer Wissenschaftlich Rechnungswesen Währung Datum Zeit Prozentsatz Bruch Text Benutzerdefiniert Die Optionen für das Bezeichnungsformat variieren je nach ausgewähltem Typ. Weitere Informationen zum Ändern des Zahlenformats finden Sie auf dieser Seite. Aktivieren Sie das Kästchen Mit Quelle verknüpft, um die Formatierung der Zahlen aus der Datenquelle im Diagramm beizubehalten. Im Abschnitt Andocken an die Zelle sind die folgenden Parameter verfügbar: Verschieben und Ändern der Größe mit Zellen - mit dieser Option können Sie das Diagramm an der Zelle dahinter ausrichten. Wenn sich die Zelle verschiebt (z.B. wenn Sie einige Zeilen/Spalten einfügen oder löschen), wird das Diagramm zusammen mit der Zelle verschoben. Wenn Sie die Breite oder Höhe der Zelle erhöhen oder verringern, ändert das Diagramm auch seine Größe. Verschieben, aber die Größe nicht ändern mit Zellen - mit dieser Option können Sie das Diagramm in der Zelle dahinter fixieren, um zu verhindern, dass die Größe des Diagramms geändert wird. Wenn sich die Zelle verschiebt, wird das Diagramm zusammen mit der Zelle verschoben. Wenn Sie jedoch die Zellengröße ändern, bleiben die Diagrammabmessungen unverändert. Kein Verschieben oder Ändern der Größe mit Zellen - mit dieser Option können Sie es verhindern, dass das Diagramm verschoben oder in der Größe geändert wird, wenn die Zellenposition oder -größe geändert wurde. Im Abschnitt Der alternative Text können Sie einen Titel und eine Beschreibung angeben, die Personen mit Seh- oder kognitiven Beeinträchtigungen vorgelesen werden, damit sie besser verstehen, welche Informationen das Diagramm enthält. Verschieben und Ändern der Größe von Diagrammen Sobald das Diagramm hinzugefügt wurde, können Sie seine Größe und Position ändern. Um die Diagrammgröße zu ändern, ziehen Sie kleine Quadrate an den Rändern. Halten Sie die Umschalttaste gedrückt und ziehen Sie eines der Eckensymbole, um die ursprünglichen Proportionen des ausgewählten Diagramms während der Größenänderung beizubehalten. Verwenden Sie zum Ändern der Diagrammposition das Symbol , das angezeigt wird, nachdem Sie den Mauszeiger über das Diagramm bewegt haben. Ziehen Sie das Diagramm an die gewünschte Position, ohne die Maustaste loszulassen. Wenn Sie das Diagramm verschieben, werden Hilfslinien angezeigt, mit denen Sie das Objekt präzise auf der Seite positionieren können (wenn ein anderer Umbruchstil als Inline ausgewählt ist). Die Liste der Tastaturkürzel, die beim Arbeiten mit Objekten verwendet werden können, finden Sie hier. Diagrammelemente bearbeiten Um den Diagrammtitel zu bearbeiten, wählen Sie den Standardtext mit der Maus aus und geben Sie stattdessen Ihren eigenen ein. Um die Schriftformatierung in Textelementen wie Diagrammtitel, Achsentiteln, Legendeneinträgen, Datenbeschriftungen usw. zu ändern, wählen Sie das gewünschte Textelement aus, indem Sie mit der linken Maustaste darauf klicken. Verwenden Sie dann die Symbole auf der Registerkarte Start der oberen Symbolleiste, um den Schrifttyp, die Größe, die Farbe oder den Dekorationsstil zu ändern. Wenn das Diagramm ausgewählt ist, ist rechts auch das Symbol für die Formeinstellungen verfügbar, da eine Form als Hintergrund für das Diagramm verwendet wird. Sie können auf dieses Symbol klicken, um die Registerkarte Formeinstellungen in der rechten Seitenleiste zu öffnen und die Form Füll-, Strich- und Umhüllungsstil anzupassen. Beachten Sie, dass Sie den Formtyp nicht ändern können. Über die Registerkarte Formeinstellungen im rechten Bereich können Sie nicht nur den Diagrammbereich selbst anpassen, sondern auch die Diagrammelemente wie Plotbereich, Datenreihen, Diagrammtitel, Legende usw. ändern und verschiedene Füllarten auf sie anwenden. Wählen Sie das Diagrammelement aus, indem Sie mit der linken Maustaste darauf klicken, und wählen Sie den bevorzugten Fülltyp aus: Volltonfarbe, Verlauf, Textur oder Bild, Muster. Geben Sie die Füllparameter an und legen Sie gegebenenfalls die Deckkraftstufe fest. Wenn Sie eine vertikale oder horizontale Achse oder Gitterlinien auswählen, sind die Stricheinstellungen nur auf der Registerkarte Formeinstellungen verfügbar: Farbe, Breite und Typ. Weitere Informationen zum Arbeiten mit Formfarben, Füllungen und Strichen finden Sie auf dieser Seite. Die Option Schatten anzeigen ist auch auf der Registerkarte Formeinstellungen verfügbar, für Diagrammelemente jedoch deaktiviert. Wenn Sie die Größe von Diagrammelementen ändern möchten, klicken Sie mit der linken Maustaste, um das gewünschte Element auszuwählen, und ziehen Sie eines der 8 weißen Quadrate entlang des Umfangs von das Element. Um die Position des Elements zu ändern, klicken Sie mit der linken Maustaste darauf, vergewissern Sie sich, so dass sich Ihr Cursor in geändert hat, halten Sie die linke Maustaste gedrückt und ziehen Sie das Element in die benötigte Position. Um ein Diagrammelement zu löschen, wählen Sie es mit der linken Maustaste aus und drücken Sie die Entf-Taste auf der Tastatur. Sie können 3D-Diagramme auch mit der Maus drehen. Klicken Sie mit der linken Maustaste in den Plotbereich und halten Sie die Maustaste gedrückt. Ziehen Sie den Zeiger, ohne die Maustaste loszulassen, um die Ausrichtung des 3D-Diagramms zu ändern. Die Diagrammeinstellungen anpassen Einige Diagrammeigenschaften können über der Registerkarte Diagrammeinstellungen in der rechten Seitenleiste geändert werden. Um es zu aktivieren, klicken Sie auf das Diagramm und wählen Sie rechts das Symbol Diagrammeinstellungen . Hier können die folgenden Eigenschaften ändern: Größe wird verwendet, um die aktuelle Diagrammbreite und -höhe anzuzeigen. Umbruchstil wird verwendet, um einen Textumbruchstil auszuwählen - inline, quadratisch, eng, durch, oben und unten, vorne, hinten (weitere Informationen finden Sie in der Beschreibung der erweiterten Einstellungen unten). Diagrammtyp ändern wird verwendet, um den ausgewählten Diagrammtyp und / oder -stil zu ändern. Verwenden Sie zum Auswählen des erforderlichen Diagrammstils das zweite Dropdown-Menü im Abschnitt Diagrammtyp ändern. Daten bearbeiten wird verwendet, um das Fenster 'Diagrammeditor' zu öffnen. Um das Fenster Diagrammeditor schnell zu öffnen, können Sie auch auf das Diagramm im Dokument doppelklicken. Einige dieser Optionen finden Sie auch im Kontextmenu. Die Menüoptionen sind: Ausschneiden, Kopieren, Einfügen - Standardoptionen mit denen ein ausgewählter Text / ein ausgewähltes Objekt ausgeschnitten oder kopiert und eine zuvor ausgeschnittene / kopierte Textpassage oder ein Objekt an die aktuelle Zeigerposition eingefügt wird. Anordnen wird verwendet, um das ausgewählte Diagramm in den Vordergrund zu bringen, in den Hintergrund zu senden, vorwärts oder rückwärts zu bewegen sowie Diagramme zu gruppieren oder die Gruppierung aufzuheben, um Operationen mit mehreren von ihnen gleichzeitig auszuführen. Weitere Informationen zum Anordnen von Objekten finden Sie auf dieser Seite. Ausrichten wird verwendet, um das Diagramm links, in der Mitte, rechts, oben, in der Mitte und unten auszurichten. Weitere Informationen zum Ausrichten von Objekten finden Sie auf dieser Seite. Der Umbruchstil wird verwendet, um einen Textumbruchstil aus den verfügbaren auszuwählen - Inline, Quadrat, Eng, Durch, Oben und Unten, vorne, hinten. Die Option Wrap-Grenze bearbeiten ist für Diagramme nicht verfügbar. Daten bearbeiten wird verwendet, um das Fenster,Diagrammeditor zu öffnen. Mit den Erweiterten Karteneinstellungen wird das Fenster „Diagramme - Erweiterte Einstellungen“ geöffnet. Zusätzlich sind Einstellungen für die 3D-Drehung für 3D-Diagramme verfügbar: X-Rotation - stellen Sie den gewünschten Wert für die Drehung der X-Achse mit der Tastatur oder über die Pfeile Links und Rechts nach rechts ein. Y-Rotation - stellen Sie den gewünschten Wert für die Drehung der Y-Achse mit der Tastatur oder über die Pfeile Aufwärts und Unten nach rechts ein. Perspektive - stellen Sie den gewünschten Wert für die Tiefenrotation mit der Tastatur oder über die Pfeile Blickfeld verengen und Blickfeld verbreitern nach rechts ein. Rechtwinklige Achsen - wird verwendet, um die rechtwinklige Achsenansicht einzustellen. Autoskalierung - aktivieren Sie dieses Kontrollkästchen, um die Tiefen- und Höhenwerte des Diagramms automatisch zu skalieren, oder deaktivieren Sie dieses Kontrollkästchen, um die Tiefen- und Höhenwerte manuell festzulegen. Tiefe (% der Basis) - stellen Sie den gewünschten Tiefenwert mit der Tastatur oder über die Pfeile ein. Höhe (% der Basis) - stellen Sie den gewünschten Höhenwert über die Tastatur oder über die Pfeile ein. Standardmäßige Drehung - setzen Sie die 3D-Parameter auf ihre Standardwerte. Bitte beachten Sie, dass Sie nicht jedes Element des Diagramms bearbeiten können. Die Einstellungen werden auf das Diagramm als Ganzes angewendet. Um die erweiterten Diagrammeinstellungen zu ändern, klicken Sie mit der rechten Maustaste auf das gewünschte Diagramm und wählen Sie im Kontextmenü die Option Erweiterte Einstellungen des Diagramms aus, oder klicken Sie einfach auf den Link Erweiterte Einstellungen anzeigen in der rechten Seitenleiste. Das Fenster mit den Diagrammeigenschaften wird geöffnet: Die Registerkarte Größe enthält die folgenden Parameter: Breite und Höhe - Verwenden Sie diese Optionen, um die Diagrammbreite und / oder -höhe zu ändern. Wenn Sie auf die Schaltfläche Konstante Proportionen klicken (in diesem Fall sieht es so aus ), werden Breite und Höhe zusammen geändert, wobei das ursprüngliche Diagrammseitenverhältnis beibehalten wird. Die Registerkarte Textumbruch enthält die folgenden Parameter: Umbruchstil - Verwenden Sie diese Option, um die Position des Diagramms relativ zum Text zu ändern: Es ist entweder Teil des Textes (falls Sie den Inline-Stil auswählen) oder wird von allen Seiten umgangen (wenn Sie einen auswählen) die anderen Stile). Inline - Das Diagramm wird wie ein Zeichen als Teil des Textes betrachtet. Wenn sich der Text bewegt, bewegt sich auch das Diagramm. In diesem Fall sind die Positionierungsoptionen nicht zugänglich. Wenn einer der folgenden Stile ausgewählt ist, kann das Diagramm unabhängig vom Text verschoben und genau auf der Seite positioniert werden: Quadratisch - Der Text umschließt das rechteckige Feld, das das Diagramm begrenzt. Eng - Der Text umschließt die tatsächlichen Diagrammkanten. Durch - Der Text wird um die Diagrammkanten gewickelt und füllt den offenen weißen Bereich innerhalb des Diagramms aus. Oben und unten - Der Text befindet sich nur über und unter dem Diagramm. Vorne - das Diagramm überlappt dem Text. Dahinter - der Text überlappt das Diagramm. Wenn Sie den quadratischen, engen, durchgehenden oder oberen und unteren Stil auswählen, können Sie einige zusätzliche Parameter festlegen - Abstand zum Text an allen Seiten (oben, unten, links, rechts). Die Registerkarte Position ist nur verfügbar, wenn Sie einen anderen Umbruchstil als Inline auswählen. Diese Registerkarte enthält die folgenden Parameter, die je nach ausgewähltem Verpackungsstil variieren: Im horizontalen Bereich können Sie einen der folgenden drei Diagrammpositionierungstypen auswählen: Ausrichtung (links, Mitte, rechts) relativ zu Zeichen, Spalte, linkem Rand, Rand, Seite oder rechtem Rand. Absolute Position, gemessen in absoluten Einheiten, d. H. Zentimeter/Punkte/Zoll (abhängig von der auf der Registerkarte Datei -&gt; Erweiterte Einstellungen...angegebenen Option), rechts neben Zeichen, Spalte, linkem Rand, Rand, Seite oder rechtem Rand, Relative Position gemessen in in Prozent relativ zum linken Rand, Rand, Seite oder rechten Rand Im vertikalen Bereich können Sie einen der folgenden drei Diagrammpositionierungstypen auswählen: Ausrichtung (oben, Mitte, unten) relativ zu Linie, Rand, unterem Rand, Absatz, Seite oder oberem Rand, Absolute Position, gemessen in absoluten Einheiten, d. H. Zentimeter/Punkte/Zoll (abhängig von der auf der Registerkarte Datei -&gt; Erweiterte Einstellungen...angegebenen Option) unter Linie, Rand, unterem Rand, Absatz, Seite oder oberem Rand, Relative Position gemessen in Prozent relativ zum Rand, unteren Rand, Seite oder oberen Rand. Objekt mit Text verschieben steuert, ob sich das Diagramm so bewegt, wie sich der Text bewegt, an dem es verankert ist. Überlappungssteuerung zulassen steuert, ob sich zwei Diagramme überlappen oder nicht, wenn Sie sie auf der Seite nebeneinander ziehen. Auf der Registerkarte Der alternative Text können Sie einen Titel und eine Beschreibung angeben, die Personen mit Seh- oder kognitiven Beeinträchtigungen vorgelesen werden, damit sie besser verstehen, welche Informationen in der Tabelle enthalten sind."
    },
   {
        "id": "UsageInstructions/InsertContentControls.htm", 
        "title": "Inhaltssteuerelemente einfügen", 
        "body": "Mit dem ONLYOFFICE Dokumenteneditor können Sie klassische Inhaltssteuerelemente einfügen, d. h. sie sind vollständig abwärtskompatibel mit Textverarbeitungsprogrammen von Drittanbietern wie Microsoft Word. ONLYOFFICE Dokumenteneditor unterstützt die folgenden klassischen Inhaltssteuerelemente: Einfacher Text, Rich-Text, Bild, Kombinationsfeld, Dropdownliste, Datum und Kontrollkästchen. Einfacher Text ist ein Objekt, das Text enthält, der nicht formatiert werden kann. Einfacher-Text-Inhaltssteuerelemente können nicht mehr als einen Absatz enthalten. Rich-Text ist ein Objekt, das Text enthält, der formatiert werden kann. Rich-Text-Inhaltssteuerelemente können mehrere Absätze, Listen und Objekte (Bilder, Formen, Tabellen usw.) enthalten. Bild ist ein Objekt, das ein einzelnes Bild enthält. Kombinationsfeld ist ein Objekt, das eine Dropdown-Liste mit einer Reihe von Auswahlmöglichkeiten enthält. Es ermöglicht die Auswahl eines der vordefinierten Werte aus der Liste und die Bearbeitung des ausgewählten Werts bei Bedarf. Dropdownliste ist ein Objekt, das eine Dropdown-Liste mit einer Reihe von Auswahlmöglichkeiten enthält. Es ermöglicht die Auswahl eines der vordefinierten Werte aus der Liste. Der ausgewählte Wert kann nicht bearbeitet werden. Datum ist ein Objekt, das einen Kalender enthält, der die Auswahl eines Datums ermöglicht. Kontrollkästchen ist ein Objekt, das die Anzeige von zwei Zuständen ermöglicht: Das Kontrollkästchen ist aktiviert und das Kontrollkästchen ist deaktiviert. Inhaltssteuerelemente hinzufügen Ein neues Einfacher-Text-Inhaltssteuerelement erstellen Positionieren Sie den Einfügepunkt innerhalb einer Textzeile, in die das Steuerelement eingefügt werden soll oder wählen Sie eine Textpassage aus, die Sie zum Steuerungsinhalt machen wollen. Wechseln Sie auf die Registerkarte Einfügen in der oberen Symbolleiste. Klicken Sie auf den Pfeil neben dem Symbol Inhaltssteuerelemente. Wählen Sie die Option Einfacher Text aus dem Menü aus. Das Inhaltssteuerelement wird am Einfügepunkt innerhalb der vorhandenen Textzeile eingefügt. Ersetzen Sie den Standardtext innerhalb des Inhaltssteuerelements (\"Ihr Text hier\") durch Ihren eigenen Text: Wählen Sie den Standardtext aus und geben Sie einen neuen Text ein oder kopieren Sie eine Textpassage von einer beliebigen Stelle und fügen Sie sie in das Inhaltssteuerelement ein. Die Einfacher-Text-Inhaltssteuerelemente erlauben keine Zeilenumbrüche und können keine anderen Objekte wie Bilder, Tabellen usw. enthalten. Ein neues Rich-Text-Inhaltssteuerelement erstellen Positionieren Sie die Einfügemarke am Ende eines Absatzes, nach dem das Steuerelement hinzugefügt werden soll oder wählen Sie einen oder mehrere der vorhandenen Absätze aus, die Sie zum Steuerungsinhalt machen wollen. Wechseln Sie auf die Registerkarte Einfügen in der oberen Symbolleiste. Klicken Sie auf den Pfeil neben dem Symbol Inhaltssteuerelemente. Wählen Sie die Option Rich Text aus dem Menü aus. Das Steuerelement wird in einen neuen Absatz eingefügt. Ersetzen Sie den Standardtext innerhalb des Steuerelements (\"Ihr Text hier\") durch Ihren eigenen: Wählen Sie den Standardtext aus und geben Sie einen neuen Text ein oder kopieren Sie eine Textpassage von einer beliebigen Stelle und fügen Sie sie in das Inhaltssteuerelement ein. Rich-Text-Inhaltssteuerelemente ermöglichen das Hinzufügen von Zeilenumbrüchen, d. h. sie können mehrere Absätze sowie einige Objekte wie Bilder, Tabellen, andere Inhaltssteuerelemente usw. enthalten. Ein neues Bild-Inhaltssteuerelement erstellen Positionieren Sie die Einfügemarke innerhalb einer Textzeile, in der das Steuerelement hinzugefügt werden soll. Wechseln Sie zur Registerkarte Einfügen in der oberen Symbolleiste. Klicken Sie auf den Pfeil neben dem Symbol Inhaltssteuerelemente. Wählen Sie die Option Bild aus dem Menü - das Inhaltssteuerelement wird am Einfügepunkt eingefügt. Klicken Sie auf das Bildsymbol in der Schaltfläche über dem Rand der Inhaltssteuerung - ein Standard-Dateiauswahlfenster wird geöffnet. Wählen Sie ein auf Ihrem Computer gespeichertes Bild aus und klicken Sie auf Öffnen. Das ausgewählte Bild wird im Inhaltssteuerelement angezeigt. Um das Bild zu ersetzen, klicken Sie auf das Bildsymbol in der Schaltfläche über dem Rand der Inhaltssteuerung und wählen Sie ein anderes Bild aus. Ein neues Kombinationsfeld- oder Dropdownliste-Inhaltssteuerelement erstellen Die Inhaltssteuerelemente Kombinationsfeld und Dropdownliste enthalten eine Dropdownliste mit einer Reihe von Auswahlmöglichkeiten. Sie können fast auf die gleiche Weise erstellt werden. Der Hauptunterschied zwischen ihnen besteht darin, dass der ausgewählte Wert in der Dropdownliste nicht bearbeitet werden kann, während der ausgewählte Wert im Kombinationsfeld ersetzt werden kann. Positionieren Sie die Einfügemarke innerhalb einer Textzeile, in der das Steuerelement hinzugefügt werden soll. Wechseln Sie zur Registerkarte Einfügen in der oberen Symbolleiste. Klicken Sie auf den Pfeil neben dem Symbol Inhaltssteuerelemente. Wählen Sie die Option Kombinationsfeld oder Dropdownliste aus dem Menü - das Steuerelement wird am Einfügepunkt eingefügt. Klicken Sie mit der rechten Maustaste auf das hinzugefügte Steuerelement und wählen Sie im Kontextmenü die Option Einstellungen des Inhaltssteuerelements. Wechseln Sie im geöffneten Fenster Einstellungen des Inhaltssteuerelements je nach ausgewähltem Inhaltssteuerungstyp auf die Registerkarte Kombinationsfeld oder Dropdownliste. Um ein neues Listenelement hinzuzufügen, klicken Sie auf die Schaltfläche Hinzufügen und füllen Sie die verfügbaren Felder im geöffneten Fenster aus: Geben Sie im Feld Anzeigename den erforderlichen Text an, z. B. Ja, Nein, Anderes. Dieser Text wird im Inhaltssteuerelement innerhalb des Dokuments angezeigt. Standardmäßig entspricht der Text im Feld Wert dem Text, der im Feld Anzeigename eingegeben wurde. Wenn Sie den Text im Feld Wert bearbeiten möchten, beachten Sie, dass der eingegebene Wert für jeden Artikel eindeutig sein muss. Klicken Sie auf die Schaltfläche OK. Sie können die Listenelemente mit den Schaltflächen Bearbeiten oder Löschen auf der rechten Seite bearbeiten oder löschen oder die Reihenfolge der Elemente mit den Schaltflächen Aufwärts und Unten ändern. Wenn alle erforderlichen Optionen festgelegt sind, klicken Sie auf die Schaltfläche OK, um die Einstellungen zu speichern und das Fenster zu schließen. Sie können auf die Pfeilschaltfläche im rechten Teil des hinzugefügten Inhaltssteuerelements Kombinationsfeld oder Dropdownliste klicken, um die Elementliste zu öffnen und das gewünschte Element auszuwählen. Sobald das erforderliche Element aus dem Kombinationsfeld ausgewählt wurde, können Sie den angezeigten Text bearbeiten, indem Sie ihn ganz oder teilweise durch Ihren Text ersetzen. Die Dropdownliste erlaubt keine Bearbeitung des ausgewählten Elements. Ein neues Datum-Inhaltssteuerelement erstellen Positionieren Sie die Einfügemarke innerhalb des Textes, wo die Inhaltssteuerung hinzugefügt werden soll. Wechseln Sie zur Registerkarte Einfügen in der oberen Symbolleiste. Klicken Sie auf den Pfeil neben dem Symbol Inhaltssteuerelemente. Wählen Sie die Option Datum aus dem Menü - das Inhaltssteuerelement mit dem aktuellen Datum wird an der Einfügemarke eingefügt. Klicken Sie mit der rechten Maustaste auf die hinzugefügte Inhaltssteuerung und wählen Sie im Kontextmenü die Option Einstellungen des Inhaltssteuerelements. Wechseln Sie im geöffneten Fenster Einstellungen des Inhaltssteuerelements auf die Registerkarte Datumsformat. Wählen Sie die erforderliche Sprache und wählen Sie das erforderliche Datumsformat in der Liste Datum wie folgt anzeigen aus. Klicken Sie auf die Schaltfläche OK, um die Einstellungen zu speichern und das Fenster zu schließen. Sie können auf die Pfeilschaltfläche im rechten Teil des hinzugefügten Inhaltssteuerelements Datum klicken, um den Kalender zu öffnen und das erforderliche Datum auszuwählen. Ein neues Kontrollkästchen-Inhaltssteuerelement erstellen Positionieren Sie die Einfügemarke innerhalb der Textzeile, in der das Inhaltssteuerelement hinzugefügt werden soll. Wechseln Sie zur Registerkarte Einfügen in der oberen Symbolleiste. Klicken Sie auf den Pfeil neben dem Symbol Inhaltssteuerelemente. Wählen Sie die Option Kontrollkästchen aus dem Menü - das Inhaltssteuerelement wird am Einfügepunkt eingefügt. Klicken Sie mit der rechten Maustaste auf die hinzugefügte Inhaltssteuerung und wählen Sie im Kontextmenü die Option Einstellungen des Inhaltssteuerelements. Wechseln Sie im geöffneten Fenster Einstellungen des Inhaltssteuerelements auf die Registerkarte Kontrollkästchen. Klicken Sie auf die Schaltfläche Häkchen-Symbol, um das erforderliche Symbol für das ausgewählte Kontrollkästchen festzulegen, oder auf das Nicht aktiviertes Häkchen, um auszuwählen, wie das deaktivierte Kontrollkästchen aussehen soll. Das Fenster Symbol wird geöffnet. Weitere Informationen zum Arbeiten mit Symbolen finden Sie in diesem Artikel. Wenn die Symbole festgelegt sind, klicken Sie auf die Schaltfläche OK, um die Einstellungen zu speichern und das Fenster zu schließen. Das hinzugefügte Kontrollkästchen wird im deaktivierten Modus angezeigt. Wenn Sie auf das hinzugefügte Kontrollkästchen klicken, wird es mit dem in der Liste Häkchen-Symbol ausgewählten Symbol markiert. Der Rahmen des Inhaltssteuerelements ist nur sichtbar, wenn das Steuerelement ausgewählt ist. Die Ränder erscheinen nicht auf einer gedruckten Version. Inhaltssteuerelemente verschieben Steuerelemente können an eine andere Stelle im Dokument verschoben werden: Klicken Sie auf die Schaltfläche links neben dem Rahmen des Steuerelements, um das Steuerelement auszuwählen, und ziehen Sie es bei gedrückter Maustaste an die gewünschte Position. Sie können Inhaltssteuerelemente auch kopieren und einfügen: Wählen Sie das entsprechende Steuerelement aus und verwenden Sie die Tastenkombinationen STRG+C/STRG+V. Bearbeiten von Einfacher-Text- und Rich-Text-Inhaltssteuerelementen Text in einfachen Text- und Rich-Text-Inhaltssteuerelementen kann mithilfe der Symbole in der oberen Symbolleiste formatiert werden: Sie können den Schriftart, -größe, -farbe anpassen, Dekorationsstile und Formatierungsvorlagen anwenden. Es ist auch möglich, das Fenster Absatz – Erweiterte Einstellungen zu verwenden, auf das Sie über das Kontextmenü oder die rechte Seitenleiste zugreifen können, um die Texteigenschaften zu ändern. Text in Rich-Text-Inhaltssteuerelementen kann wie normaler Text formatiert werden, d. h. Sie können Zeilenabstand festlegen, Absatzeinzüge anpassen, Tabstopps anpassen usw. Einstellungen für Inhaltssteuerelemente ändern Unabhängig davon, welche Art von Inhaltssteuerung ausgewählt ist, können Sie die Inhaltssteuerungseinstellungen in den Abschnitten Allgemein und Sperrung des Fensters Einstellungen des Inhaltssteuerelements ändern. Um die Einstellungen der Inhaltssteuerung zu öffnen, können Sie auf folgende Weise vorgehen: Wählen Sie das gewünschte Inhaltssteuerelement aus und klicken Sie auf den Pfeil neben dem Symbol Inhaltssteuerelemente in der oberen Symbolleiste und wählen Sie dann die Option Einstellungen Inhaltssteuerelemente aus dem Menü aus. Klicken Sie mit der rechten Maustaste auf eine beliebige Stelle im Inhaltssteuerelement und nutzen Sie die Option Einstellungen Steuerungselement im Kontextmenü. Im sich nun öffnenden Fenstern können Sie die folgenden Parameter festlegen: Legen Sie in den entsprechenden Feldern Titel oder Tag des Steuerelements fest. Der Titel wird angezeigt, wenn das Steuerelement im Dokument ausgewählt wird. Tags werden verwendet, um Inhaltssteuerelemente zu identifizieren, damit Sie im Code darauf verweisen können. Wählen Sie aus, ob Sie Steuerelemente mit einem Begrenzungsrahmen anzeigen möchten oder nicht. Wählen Sie die Option Keine, um das Steuerelement ohne Begrenzungsrahmen anzuzeigen. Über die Option Begrenzungsrahmen können Sie die Feldfarbe im untenstehenden Feld auswählen. Klicken Sie auf die Schaltfläche Auf alle anwenden, um die festgelegten Darstellungseinstellungen auf alle Inhaltssteuerelemente im Dokument anzuwenden. Im Abschnitt Sperrung können Sie das Inhaltssteuerelement mit der entsprechenden Option vor ungewolltem Löschen oder Bearbeiten schützen: Inhaltssteuerelement kann nicht gelöscht werden - Aktivieren Sie dieses Kontrollkästchen, um ein Löschen des Steuerelements zu verhindern. Inhaltssteuerelement kann nicht bearbeitet werden - Aktivieren Sie dieses Kontrollkästchen, um ein Bearbeiten des Steuerelements zu verhindern. Für bestimmte Arten von Inhaltssteuerelementen ist auch die dritte Registerkarte verfügbar, die die spezifischen Einstellungen für den ausgewählten Inhaltssteuerelementtyp enthält: Kombinationsfeld, Dropdownliste, Datum, Kontrollkästchen. Diese Einstellungen werden oben in den Abschnitten zum Hinzufügen der entsprechenden Inhaltssteuerelemente beschrieben. Klicken Sie im Fenster Einstellungen auf OK, um die Änderungen zu bestätigen. Es ist auch möglich, Inhaltssteuerelemente mit einer bestimmten Farbe hervorzuheben. Inhaltssteuerelemente farblich hervorheben: Klicken Sie auf die Schaltfläche links neben dem Rahmen des Steuerelements, um das Steuerelement auszuwählen. Klicken Sie auf der oberen Symbolleiste auf den Pfeil neben dem Symbol Inhaltssteuerelemente. Wählen Sie die Option Einstellungen hervorheben aus dem Menü aus. Wählen Sie die gewünschte Farbe aus den verfügbaren Paletten aus: Themenfarben, Standardfarben oder neue benutzerdefinierte Farbe angeben. Um zuvor angewendete Farbmarkierungen zu entfernen, verwenden Sie die Option Keine Markierung. Die ausgewählten Hervorhebungsoptionen werden auf alle Inhaltssteuerelemente im Dokument angewendet. Inhaltssteuerelemente entfernen Um ein Steuerelement zu entfernen ohne den Inhalt zu löschen, klicken Sie auf das entsprechende Steuerelement und gehen Sie vor wie folgt: Klicken Sie auf den Pfeil neben dem Symbol Inhaltssteuerelemente in der oberen Symbolleiste und wählen Sie dann die Option Steuerelement entfernen aus dem Menü aus. Klicken Sie mit der rechten Maustaste auf das Steuerelement und wählen Sie die Option Steuerungselement entfernen im Kontextmenü aus. Um ein Steuerelement einschließlich Inhalt zu entfernen, wählen Sie das entsprechende Steuerelement aus und drücken Sie die Taste Löschen auf der Tastatur."
    },
   {
        "id": "UsageInstructions/InsertCrossReference.htm", 
        "title": "Querverweise einfügen", 
        "body": "Im Dokumenteneditor werden Querverweise verwendet, um Links zu erstellen, die zu anderen Teilen desselben Dokuments führen, z.B. Überschriften oder Objekte wie Diagramme oder Tabellen. Solche Verweise erscheinen in Form eines Hyperlinks. Querverweis erstellen Positionieren Sie den Cursor an der Stelle, an der Sie einen Querverweis einfügen möchten. Öffnen Sie die Registerkarte Verweise und klicken Sie auf das Symbol Querverweis. Im geöffneten Fenster Querverweis stellen Sie die gewünschten Parameter ein: Das Drop-Down-Menü Bezugstyp gibt das Element an, auf das Sie sich beziehen möchten, z.B. ein nummeriertes Element (standarmäßig), eine Überschrift, ein Lesezeichen, eine Fuß-/Endnote, eine Gleichung, eine Tabelle. Wählen Sie den gewünschten Typ aus. Das Drop-Down-Menü Verweisen auf gibt den Text oder den numerischen Wert des Verweises an, abhängig vom Element, das Sie im Menü Bezugstyp ausgewählt haben. Z.B., wenn Sie die Option Überschrift ausgewählt haben, können Sie den folgenden Inhalt angeben: Überschriftentext, Seitennummer, Nummer der Überschrift, Nummer der Überschrift (kein Kontext), Nummer der Überschrift (der ganze Text), Oben/unten. Die vollständige Liste der verfügbaren Optionen im Menü \"Bezugstyp\": Bezugstyp Verweisen auf Beschreibung Nummeriertes Element Seitennummer Die Seitennummer des nummerierten Elements wird eingefügt Absatznummer Die Absatznummer des nummerierten Elements wird eingefügt Absatznummer (kein Kontext) Die abgekürzte Absatznummer wird eingefügt. Der Verweis bezieht sich nur auf das spezifische Element der nummerierten Liste, z.B. beziehen Sie sich anstelle von \"4.1.1\" nur auf \"1\" Absatznummer (der ganze Kontext) Die ganze Absatznummer wird eingefügt, z.B. \"4.1.1\" Text im Absatz Der Textwert des Absatzes wird eingefügt, z.B anstelle von \"4.1.1. Allgemeine Geschäftsbedingungen\" beziehen Sie sich nur auf \"Allgemeine Geschäftsbedingungen\" Oben/unten Die Wörter \"oben\" oder \"unten\" werden je nach Position des Elements eingefügt Überschrift Überschriftentext Der ganze Überschriftentext wird eingefügt Seitennummer Die Seitennummer der Überschrift wird eingefügt Nummer der Überschrift Die Sequenznummer der Überschrift wird eingefügt Nummer der Überschrift (kein Kontext) Die abgekürzte Nummer der Überschrift wird eingefügt. Stellen Sie den Cursorpunkt in dem Abschnitt, auf den Sie sich beziehen, z.B. im Abschnitt 4. Anstelle von „4.B“ erhalten Sie also nur „B“ Nummer der Überschrift (der ganze Kontext) Die ganze Nummer der Überschrift wird eingefügt, auch wenn sich der Cursorpunkt im selben Abschnitt befindet Oben/unten Die Wörter \"oben\" oder \"unten\" werden je nach Position des Elements eingefügt Lesezeichen Text des Lesezeichens Der gesamten Text des Lesezeichens wird eingefügt Seitennummer Die Seitennummer des Lesezeichnis wird eingefügt Absatznummer Die Absatznummer des Lesezeichnis wird eingefügt Absatznummer (kein Kontext) Die abgekürzte Absatznummer wird eingefügt. Der Verweis bezieht sich nur auf das spezifische Element, z.B. beziehen Sie sich anstelle von \"4.1.1\" nur auf \"1\" Absatznummer (der ganze Kontext) Die ganze Absatznummer wird eingefügt, z.B., \"4.1.1\" Oben/unten Die Wörter \"oben\" oder \"unten\" werden je nach Position des Elements eingefügt Fußnote Nummer der Fußnote Die Nummer der Fußnote wird eingefügt Seitennummer Die Seitennummer der Fußnote wird eingefügt Oben/unten Die Wörter \"oben\" oder \"unten\" werden je nach Position des Elements eingefügt Nummer der Fußnote (formatiert) Die Nummer wird eingefügt, die als Fußnote formatiert ist. Die Nummerierung der tatsächlichen Fußnoten ist nicht betroffen Endnote Nummer der Endnote Die Nummer der Endnote wird eingefügt Seitennummer Die Seitennummer der Endnote wird eingefügt Oben/unten Die Wörter \"oben\" oder \"unten\" werden je nach Position des Elements eingefügt Nummer der Endnote (formatiert) Die Nummer wird eingefügt, die als Endnote formatiert ist. Die Nummerierung der tatsächlichen Endnoten ist nicht betroffen Gleichung / Abbildung / Tabelle Ganze Beschriftung Der ganze Text der Beschriftung wird eingefügt Nur Bezeichnung und Nummer Nur die Beschriftung und die Objektnummer werden eingefügt, z.B. \"Tabelle 1.1\" Nur der Text von der Legende Nur der Text der Beschriftung wird eingefügt Seitennummer Die Seitennummer des verweisenden Objekts wird eingefügt Oben/unten Die Wörter \"oben\" oder \"unten\" werden je nach Position des Elements eingefügt Markieren Sie das Kästchen Als Hyperlink einfügen, um den Verweis in einen aktiven Link zu verwandeln. Markieren Sie das Kästchen Oben/unten einschließen (wenn verfügbar), um die Position des Elements anzugeben, auf das Sie sich beziehen. Der ONLYOFFICE-Dokumenteneditor fügt je nach Position des Elements automatisch die Wörter \"oben\" und \"unten\" ein. Markieren Sie das Kästchen Nummern trennen mit, um das Trennzeichen im Feld rechts anzugeben. Die Trennzeichen werden für vollständige Kontextverweise benötigt. Das Feld Für welche bietet Ihnen die verfügbaren Elemente gemäß dem von Ihnen ausgewählten Bezugstyp, z.B. wenn Sie die Option Überschrift ausgewählt haben, wird die vollständige Liste der Überschriften im Dokument angezeigt. Klicken Sie auf Einfügen, um einen Querverweis zu erstellen. Querverweise löschen Um einen Querverweis zu löschen, wählen Sie den gewünschten Querverweis und drücken Sie die Entfernen-Taste."
    },
   {
        "id": "UsageInstructions/InsertDateTime.htm", 
        "title": "Datum und Uhrzeit einfügen", 
        "body": "Um Datum und Uhrzeit einzufügen im Dokumenteneditor, positionieren Sie den Textkursor an der Stelle, an der Sie das Datum und die Uhrzeit einfügen wollen, öffnen Sie die Registerkarte Einfügen, klicken Sie das Symbol Datum & Uhrzeit an, im geöffneten Fenster Datum & Uhrzeit konfigurieren Sie die folgenden Einstellungen: Wählen Sie die Sprache aus. Wählen Sie das Format aus. Markieren Sie das Kästchen Automatisch aktualisieren, damit das Datum und die Uhrzeit immer aktuell sind. Verwenden Sie die Kontextmenüoption Aktualisieren, um das Datum und die Uhrzeit manuell zu ändern. Klicken Sie Als Standard setzen an, um das aktive Format als Standard für diese Sprache zu setzen. Klicken Sie OK an."
    },
   {
        "id": "UsageInstructions/InsertDropCap.htm", 
        "title": "Initialbuchstaben einfügen", 
        "body": "Im Dokumenteneditor ein Initial ist der erste Buchstabe eines Absatzes, der viel größer als die anderen ist sich in der Höhe über mehrere Zeilen erstreckt. Ein Initial einfügen: Positionieren Sie den Mauszeiger an der gewünschten Stelle. Wechseln Sie in der oberen Symbolleiste auf die Registerkarte Einfügen. Klicken Sie in der oberen Symbolleiste auf das Symbol Initialbuchstaben. Wählen Sie im geöffneten Listenmenü die gewünschte Option: Im Text - um das Initial innerhalb des Absatzes zu positionieren. Am Seitenrand - um das Initial am linken Seitenrand zu positionieren. Das erste Zeichen des ausgewählten Absatzes wird in ein Initial umgewandelt. Wenn das Initial mehrere Zeichen umfassen soll, fügen Sie diese manuell hinzu - wählen Sie das Initial aus und geben Sie die restlichen gewünschten Buchstaben ein. Um das Initial anzupassen (d.h. Schriftgrad, Typ, Formatvorlage oder Farbe), markieren Sie den Buchstaben und nutzen Sie die entsprechenden Symbole in der Registerkarte Startseite in der oberen Symbolleiste. Wenn das Initial markiert ist, wird es von einem Rahmen umgeben (eine Box, um das Initial auf der Seite zu positionieren). Sie können die Rahmengröße leicht ändern, indem Sie mit dem Mauszeiger an den Rahmenlinien ziehen, oder die Position ändern, indem Sie auf das Symbol klicken, das angezeigt wird, wenn Sie den Mauszeiger über den Rahmen bewegen. Um das hinzugefügte Initial zu löschen, wählen Sie es aus, klicken Sie in der Registerkarte Einfügen auf das Symbol Intial und wählen Sie die Option Keins aus dem Listenmenü aus. Um die Parameter des hinzugefügten Initials anzupassen, wählen Sie es aus, klicken Sie in der Registerkarte Einfügen auf die Option Initialbuchstaben und wählen Sie die Option Initialformatierung aus dem Listenmenü aus. Das Fenster Initialbuchstaben - Erweiterte Einstellungen wird geöffnet: Über die Gruppe Initialbuchstaben können Sie die folgenden Parameter festlegen: Position - die Platzierung des Initials ändern. Wählen Sie die Option Im Text oder Am Seitenrand oder klicken Sie auf Keins, um das Initial zu löschen. Schriftart - eine Schriftart aus der Liste mit den Vorlagen auswählen. Höhe in Zeilen - gibt an, über wie viele Zeilen sich das Initial erstrecht. Der Wert kann von 1 bis 10 gewählt werden. Abstand von Text - gibt den Abstand zwischen dem Absatztext und der rechten Rahmenlinie des Rahmens an, der das Initial umgibt. Auf der Registerkarte Rahmen &amp; Füllung können Sie dem Initial einen Rahmen hinzufügen und die zugehörigen Parameter anpassen. Folgende Parameter lassen sich anpassen: Rahmen (Größe, Farbe und das Vorhandensein oder Fehlen) - legen Sie die Rahmengröße fest und wählen Sie die Farbe und die Art der gewünschten Rahmenlinien aus (oben, unten, links, rechts oder eine Kombination). Hintergrundfarbe - wählen Sie die Hintergrundfarbe für das Initial aus. Über die Registerkarte Ränder können Sie den Abstand zwischen dem Initial und den Oberen, Unteren, Linken und Rechten Rahmenlinien festlegen (falls diese vorher hinzugefügt wurden). Sobald das Initial hinzugefügt wurde, können Sie auch die Parameter des Rahmens ändern. Klicken Sie dazu mit der rechten Maustaste innerhalb des Rahmens und wählen Sie Rahmen - Erweiterte Einstellungen im Menü aus. Das Fenster Rahmen - Erweiterte Einstellungen wird geöffnet: In der Gruppe Rahmen können Sie die folgenden Parameter festlegen: Position - wird genutzt, um den Textumbruch Fixiert oder Schwebend zu wählen. Alternativ können Sie auf Keine klicken, um den Rahmen zu löschen. Breite und Höhe - zum Ändern der Rahmendimensionen. Über die Option Auto können Sie die Rahmengröße automatisch an das Initial anpassen. Im Feld Genau können Sie bestimmte Werte festlegen. Mit der Option Mindestens wird der Wert für die Mindesthöhe festgelegt (wenn Sie die Größe des Initials ändern, ändert sich die Rahmenhöhe entsprechend, wird jedoch nicht kleiner als der angegebene Wert). Über die Parameter Horizontal kann die genaue Position des Rahmens in den gewählten Maßeinheiten in Bezug auf den Randn, die Seite oder die Spalte, oder um den Rahmen (links, zentriert oder rechts) in Bezug auf einen der Referenzpunkte auszurichten. Sie können auch die horizontale Distanz vom Text festlegen, d.h. den Platz zwischen dem Text des Absatzes und den horizontalen Rahmenlinien des Rahmens. Die Parameter Vertikal werden genutzt, entweder um die genaue Position des Rahmens in gewählten Maßeinheiten relativ zu einem Rand, einer Seite oder einem Absatz festzulegen oder um den Rahmen (oben, zentriert oder unten) relativ zu einem dieser Referenzpunkte auszurichten. Außerdem können auch den vertikalen Abstand von Text festlegen, also den Abstand zwischen den horizontalen Rahmenrändern und dem Text des Absatzes. Mit Text verschieben - kontrolliert, ob der Rahmen verschoben wird, wenn der Absatz, mit dem der Rahmen verankert ist, verschoben wird. Über die Gruppen Rahmen &amp; Füllung und Ränder können sie dieselben Parameter festlegen wie über die gleichnamigen Gruppen im Fenster Initialbuchstaben - Erweiterte Einstellungen."
    },
   {
        "id": "UsageInstructions/InsertEndnotes.htm", 
        "title": "Endnoten einfügen", 
        "body": "Im Dokumenteneditor können Sie Endnoten einfügen, um Kommentare zu bestimmten Sätzen oder Begriffen in Ihrem Text einzufügen, Referenzen und Quellen anzugeben usw., die am Ende des Dokuments angezeigt werden. Endnoten einfügen Um eine Endnote einzufügen, positionieren Sie den Einfügepunkt am Ende der Textpassage, der Sie eine Endnote hinzufügen möchten, wechseln Sie in der oberen Symbolleiste zur Registerkarte Verweise, klicken Sie auf das Symbol Fußnote in der oberen Symbolleiste oder klicken Sie auf den Pfeil neben dem Symbol Fußnote und wählen Sie die Option Endnote einfügen aus dem Menü aus. Das Endnotenzeichen (d.h. das hochgestellte Zeichen, das eine Endnote anzeigt) wird im Dokumenttext angezeigt und die Textmarke springt zum Ende des Dokuments. geben Sie den Text der Endnote ein. Wiederholen Sie den beschriebenen Vorgang, um weitere Endnoten für andere Textpassagen in Ihrem Dokument hinzuzufügen. Die Endnoten werden automatisch nummeriert: i, ii, iii, usw. (standardmäßig). Darstellung der Endnoten im Dokument Wenn Sie den Mauszeiger über das Endnotenzeichen bewegen, öffnet sich ein kleines Popup-Fenster mit dem Endnotentext. Navigieren durch Endnoten Um zwischen den hinzugefügten Endnoten im Dokument zu navigieren, klicken Sie auf der Registerkarte Verweise in der oberen Symbolleiste auf den Pfeil neben dem Symbol Fußnote, navigieren Sie im Abschnitt Zu Endnoten über die Pfeile und zur nächsten oder zur vorherigen Endnote. Endnoten bearbeiten Um die Einstellungen der Endnoten zu ändern, Klicken Sie auf der Registerkarte Verweise in der oberen Symbolleiste auf den Pfeil neben dem Symbol Fußnote. Wählen Sie die Option Hinweise Einstellungen aus dem Menü aus. Ändern Sie im Fenster Hinweise Einstellungen die aktuellen Parameter: Legen Sie die Position der Endnoten auf der Seite fest, indem Sie eine der verfügbaren Optionen aus dem Drop-Down-Menü rechts auswählen: Ende des Abschnitts - um Endnoten am ende des aktiven Abschnitts zu positionieren. Ende des Dokuments - um Endnoten am Ende des Dokuments zu positionieren (diese Option ist standardmäßig ausgewählt). Passen Sie den Format der Endnoten an: Zahlenformat - wählen Sie das gewünschte Format aus der Liste mit verfügbaren Formaten aus: 1, 2, 3,..., a, b, c,..., A, B, C,..., i, ii, iii,..., I, II, III,.... Starten - über die Pfeiltasten können Sie festlegen, bei welchem Buchstaben oder welcher Zahl Sie beginnen möchten. Nummerierung - wählen Sie aus, auf welche Weise Sie Ihre Endnoten nummerieren möchten: Kontinuierlich - um Endnoten im gesamten Dokument fortlaufend zu nummerieren. Jeden Abschnitt neu beginnen - die Nummerierung der Endnoten beginnt in jedem neuen Abschnitt wieder bei 1 (oder einem anderen festgelegten Wert). Jede Seite neu beginnen - die Nummerierung der Endnoten beginnt auf jeder neuen Seite wieder bei 1 (oder einem anderen festgelegten Wert). Benutzerdefiniert - Legen Sie ein Sonderzeichen oder ein Wort fest, das Sie als Endnotenzeichen verwenden möchten (z. B. * oder Note1). Geben Sie das gewünschte Wort/Zeichen in das dafür vorgesehene Feld ein und klicken Sie auf Einfügen im Fenster Hinweise Einstellungen Legen Sie in der Dropdown-Liste Änderungen anwenden fest, ob Sie die angegebenen Endnoteneinstellungen auf das ganze Dokument oder nur den aktuellen Abschnitt anwenden wollen. Um unterschiedliche Endnotenformatierungen in verschiedenen Teilen des Dokuments zu verwenden, müssen Sie zunächst Abschnittsumbrüche einfügen. Wenn Sie bereits sind, klicken Sie auf Anwenden. Endnoten entfernen Um eine einzelne Endnote zu entfernen, positionieren Sie den Einfügepunkt direkt vor der Endnotenmarkierung und drücken Sie auf ENTF. Andere Endnoten werden automatisch neu nummeriert. Um alle Endnoten in einem Dokument zu entfernen: Klicken Sie auf den Pfeil neben dem Symbol Fußnote auf der Registerkarte Verweise in der oberen Symbolleiste. Wählen Sie im Menü die Option Alle Anmerkungen löschen. Wählen Sie im erscheinenden Fenster die Option Alle Endnoten löschen und klicken Sie auf OK."
    },
   {
        "id": "UsageInstructions/InsertEquation.htm", 
        "title": "Formeln einfügen", 
        "body": "Gleichungen einfügen Mit dem Dokumenteneditor können Sie Gleichungen mithilfe der integrierten Vorlagen erstellen, sie bearbeiten, Sonderzeichen einfügen (einschließlich mathematischer Operatoren, griechischer Buchstaben, Akzente usw.). Eine neue Gleichung einfügen Eine Gleichung aus den Vorlagen einfügen: Positionieren Sie den Mauszeiger an der gewünschten Stelle. Wechseln Sie in der oberen Symbolleiste auf die Registerkarte Einfügen. Klicken Sie auf den Pfeil neben dem Symbol Gleichung. Wählen Sie die gewünschte Gleichungskategorie in der Symbolleiste über der eingefügten Gleichung aus oder Wählen Sie in der geöffneten Dropdown-Liste die gewünschte Gleichungskategorie aus. Derzeit sind die folgenden Kategorien verfügbar: Symbole, Brüche, Skripte, Wurzeln, Integrale, Große Operatoren, Klammern, Funktionen, Akzente, Grenzwerte und Logarithmen, Operators, Matrizen. Klicken Sie auf das Symbol Gleichungseinstellungen in der Symbolleiste über der eingefügten Gleichung, um auf weitere Einstellungen zuzugreifen, z. B. Unicode oder LaTeX, Aktuell - Professionell oder Aktuell - Linear und Zum Inline wechseln. Klicken Sie im entsprechenden Vorlagensatz auf das gewünschte Symbol/die gewünschte Gleichung. Das ausgewählte Symbol/die ausgewählte Formel wird an der aktuellen Cursorposition eingefügt. Wenn die ausgewählte Zeile leer ist, wird die Gleichung zentriert. Um die Formel links- oder rechtsbündig auszurichten, klicken Sie auf der Registerkarte Startseite auf oder . Jede Gleichungsvorlage repräsentiert einen Satz von Slots. Ein Slot ist eine Position für jedes Element, aus dem die Gleichung besteht. Ein leerer Platz (auch als Platzhalter bezeichnet) hat einen gepunkteten Umriss . Sie müssen alle Platzhalter mit den erforderlichen Werten ausfüllen. Um eine Gleichung zu erstellen, können Sie auch die Tastenkombination Alt + = verwenden. Es ist auch möglich, der Gleichung eine Beschriftung hinzuzufügen. Weitere Informationen zum Arbeiten mit Beschriftungen für Gleichungen finden Sie in diesem Artikel. Werte eingeben Der Einfügepunkt zeigt an, an welcher Stelle das nächste Zeichen erscheint, das Sie eingeben. Um den Cursor präzise zu positionieren, klicken Sie in einen Platzhalter und verschieben Sie den Einfügepunkt mithilfe der Tastaturpfeile um ein Zeichen nach links/rechts oder eine Zeile nach oben/unten. Wenn Sie unter dem Slot einen neuen Platzhalter erstellen wollen, positionieren Sie den Cursor in der ausgwählten Vorlage und drücken Sie die Eingabetaste. Wenn Sie den Einfügepunkt positioniert haben, können Sie die Werte in den Platzhaltern einfügen: Geben Sie geben Sie den gewünschten numerischen/literalen Wert über die Tastatur ein. Wechseln Sie zum Einfügen von Sonderzeichen in die Registerkarte Einfügen und wählen Sie im Menü Formel das gewünschte Zeichen aus der Palette mit den Symbolen aus. Fügen Sie eine weitere Vorlage aus der Palette hinzu, um eine komplexe verschachtelte Gleichung zu erstellen. Die Größe der primären Formel wird automatisch an den Inhalt angepasst. Die Größe der verschachtelten Gleichungselemente hängt von der Platzhaltergröße der primären Gleichung ab, sie darf jedoch nicht kleiner sein als die Vorlage für tiefgestellte Zeichen. Alternativ können Sie auch über das Rechtsklickmenü neue Elemente in Ihre Formel einfügen: Um ein neues Argument vor oder nach einem vorhandenen Argument einzufügen, das in Klammern steht, klicken Sie mit der rechten Maustaste auf das vorhandene Argument und wählen Sie die Option Argument vorher/nachher einfügen. Um in Fällen mit mehreren Bedingungen eine neue Formel aus der Gruppe Klammern hinzuzufügen (oder eine beliebige andere Formel, wenn Sie zuvor über die Eingabetaste einen neuen Platzhalter eingefügt haben), klicken Sie mit der rechten Maustaste auf einen leeren Platzhalter oder eine im Platzhalter eingegebene Gleichung und wählen Sie Formel vorher/nachher einfügen aus dem Menü aus. Um in einer Matrix eine neue Zeile oder Spalte einzugeben, wählen Sie die Option Einfügen aus dem Menü, und klicken Sie dann auf Zeile oberhalb/unterhalb oder Spalte links/rechts. Aktuell ist es nicht möglich Gleichungen im linearen Format einzugeben werden, d.h. \\sqrt(4&amp;x^3). Wenn Sie die Werte der mathematischen Ausdrücke eingeben, ist es nicht notwendig die Leertaste zu verwenden, da die Leerzeichen zwischen den Zeichen und Werten automatisch gesetzt werden. Wenn die Formel zu lang ist und nicht in eine einzelnen Zeile passt, wird während der Eingabe automatisch ein Zeilenumbruch ausgeführt. Bei Bedarf können Sie auch manuell einen Zeilenumbruch an einer bestimmten Position einfügen. Klicken sie dazu mit der rechten Maustaste auf einen der Platzhalter und wählen Sie im Menü die Option manuellen Umbruch einfügen aus. Der ausgewählte Platzhalter wird in die nächste Zeile verschoben. Wenn Sie einen manuellen Zeilenumbruch eingefügt haben können Sie die neue Zeile mithilfe der Tab- Taste an die mathematischen Operatoren der vorherigen Zeile anpassen und die Zeile entsprechend ausrichten. Um einen manuell hinzugefügten Zeilenumbruch zu entfernen, klicken Sie mit der rechten Maustaste auf den Platzhalter der die neue Zeile einleitet und wählen Sie die Option manuellen Umbruch entfernen. Formeln formatieren Um die Schriftgröße der Formel zu verkleinern oder zu vergrößern, klicken Sie an eine beliebige Stelle im Formelfeld und verwenden Sie die Schaltflächen und in der Registerkarte Startseite oder wählen Sie die gewünschte Schriftgröße aus der Liste aus. Alle Elemente in der Formel werden entsprechend angepasst. Die Buchstaben innerhalb der Formel werden standardmäßig kursiv gestellt. Bei Bedarf können Sie Schriftart (fett, kursiv, durchgestrichen) oder Schriftfarbe für die gesamte Formel oder Teile davon ändern. Unterstreichen ist nur für die gesamte Formel nötig und nicht für einzelne Zeichen. Wählen Sie den gewünschten Teil der Formel durch anklicken und ziehen aus. Der ausgewählte Teil wird blau markiert. Wechseln Sie in der oberen Symbolleiste auf die Registerkarte Startseite, um die Auswahl zu formatieren. Sie können zum Beispiel das Kursivformat für gewöhnliche Wörter entfernen, die keine Variablen oder Konstanten darstellen. Einige Elemente aus der Formel lassen sich auch über das Rechtsklickmenü ändern: Um das Format von Brüchen zu ändern, klicken Sie mit der rechten Maustaste auf einen Bruch und wählen Sie im Menü die Option in schrägen/linearen/gestapelten Bruch ändern (die verfügbaren Optionen hängen vom ausgewählten Bruchtyp ab). Um die Position der Skripte in Bezug auf Text zu ändern, klicken Sie mit der rechten Maustaste auf die Formel, die Skripte enthält und wählen Sie die Option Skripte vor/nach Text aus dem Menü aus. Um die Größe der Argumente für Skripte, Wurzeln, Integrale, Große Operatoren, Grenzwerte und Logarithmen und Operatoren sowie für über- und untergeordnete Klammern und Vorlagen mit Gruppierungszeichen aus der Gruppe Akzente zu ändern, klicken Sie mit der rechten Maustaste auf das Argument, das Sie ändern wollen und wählen Sie die Option Argumentgröße vergrößern/verkleinern aus dem Menü aus. Um festzulegen, ob ein leerer Grad-Platzhalter für eine Wurzel angezeigt werden soll oder nicht, klicken Sie mit der rechten Maustaste auf die Wurzel und wählen Sie die Option Grad anzeigen/ausblenden aus dem Menü aus. Um festzulegen, ob ein leerer Grenzwert-Platzhalter für ein Integral oder für Große Operatoren angezeigt werden soll oder nicht, klicken Sie mit der rechten Maustaste auf die Gleichung und wählen Sie im Menü die Option oberen/unteren Grenzwert anzeigen/ausblenden aus. Um die Position der Grenzwerte in Bezug auf das Integral oder einen Operator für Integrale oder einen großen Operator zu ändern, klicken Sie mit der rechten Maustaste auf die Formel und wählen Sie die Option Position des Grenzwertes ändern aus dem Menü aus. Die Grenzwerte können rechts neben dem Operatorzeichen (als tiefgestellte und hochgestellte Zeichen) oder direkt über und unter dem Operatorzeichen angezeigt werden. Um die Positionen der Grenzwerte für Grenzwerte und Logarithmen und Vorlagen mit Gruppierungszeichen aus der Gruppe Akzente, klicken Sie mit der rechten Maustaste auf die Formel und wählen Sie die Option Grenzwert über/unter Text aus dem Menü aus. Um festzulegen, welche Klammern angezeigt werden sollen, klicken Sie mit der rechten Maustaste auf den darin enthaltenen Ausdruck und wählen Sie die Option öffnende/schließende Klammer anzeigen/verbergen aus dem Menü aus. Um die Größe der Klammern zu ändern, klicken Sie mit der rechten Maustaste auf den darin enthaltenen Ausdruck. Standardmäßig ist die Option Klammern ausdehnen aktiviert, so dass die Klammern an den eingegebenen Ausdruck angepasst werden. Sie können diese Option jedoch deaktivieren und die Klammern werden nicht mehr ausgedehnt. Wenn die Option aktiviert ist, können Sie auch die Option Klammern an Argumenthöhe anpassen verwenden. Um die Position der Zeichen in Bezug auf Text für Klammern (über dem Text/unter dem Text) oder Überstriche/Unterstriche aus der Gruppe Akzente zu ändern, klicken Sie mit der rechten Maustaste auf die Vorlage und wählen Sie die Option Überstrich/Unterstrich über/unter Text aus dem Menü aus. Um festzulegen, welche Rahmen für die Box-Formel aus der Gruppe Akzente angezeigt werden sollen, klicken Sie mit der rechten Maustaste auf die Formel, klicken Sie im Menü auf die Option Umrandungen und legen Sie die Parameter Einblenden/Ausblenden oberer/unterer/rechter/linker Rand oder Hinzufügen/Verbergen horizontale/vertikale/diagonale Grenzlinie fest. Um festzulegen, ob ein leerer Platzhalter für eine Matrix angezeigt werden soll oder nicht, klicken Sie mit der rechten Maustaste darauf und wählen Sie die Option Platzhalter einblenden/ausblenden aus dem Menü aus. Einige Elemente aus der Formel lassen sich auch über das Rechtsklickmenü ausrichten: Um Formeln in Fällen mit mehreren Bedingungen aus der Gruppe Klammern auszurichten (oder beliebige andere Formeln, wenn Sie zuvor über die Eingabetaste einen neuen Platzhalter eingefügt haben), klicken Sie mit der rechten Maustaste auf eine Gleichung, wählen Sie die Option Ausrichten im Menü aus und legen Sie den Ausrichtungstyp fest: Oben, Zentriert oder Unten. Um eine Matrix vertikal auszurichten, klicken Sie mit der rechten Maustaste auf die Matrix, wählen Sie die Option Matrixausrichtung aus dem Menü aus und legen Sie den Ausrichtungstyp fest: Oben, Zentriert oder Unten. Um Elemente in einer Matrix-Spalte vertikal auszurichten, klicken Sie mit der rechten Maustaste auf einen Platzhalter in der Spalte, wählen Sie die Option Spaltenausrichtung aus dem Menü aus und legen Sie den Ausrichtungstyp fest: Links, Zentriert oder Rechts. Formelelemente löschen Um Teile einer Formel zu löschen, wählen Sie den Teil den Sie löschen wollen mit der Maus aus oder halten Sie die Umschalttaste gedrückt, und drücken sie dann auf Ihrer Tastatur auf ENTF. Ein Slot kann nur zusammen mit der zugehörigen Vorlage gelöscht werden. Um die gesamte Formel zu löschen, wählen Sie diese aus, entweder durch Markieren mit der Maus oder durch einen Doppelklick auf das Formelfeld und drücken Sie dann auf die Taste ENTF auf Ihrer Tastatur. Einige Elemente aus der Formel lassen sich auch über das Rechtsklickmenü löschen: Um eine Wurzel zu löschen, klicken Sie diese mit der rechten Maustaste an und wählen Sie die Option Wurzel löschen im Menü aus. Um ein tiefgestelltes Zeichen bzw. ein hochgestelltes Zeichen zu löschen, klicken sie mit der rechten Maustaste auf das entsprechende Element und wählen Sie die Option hochgestelltes/tiefgestelltes Zeichen entfernen im Menü aus. Wenn der Ausdruck Skripte mit Vorrang vor dem Text enthält, ist die Option Skripte entfernen verfügbar. Um Klammern zu entfernen, klicken Sie mit der rechten Maustaste auf den darin enthaltenen Ausdruck und wählen Sie die Option umschließende Zeichen entfernen oder die Option Umschließende Zeichen und Trennzeichen entfernen im Menü aus. Wenn ein Ausdruck in Klammern mehr als ein Argument enthält, klicken Sie mit der rechten Maustaste auf das Argument das Sie löschen wollen und wählen Sie die Option Argument löschen im Menü aus. Wenn Klammern mehr als eine Formel umschließen (in Fällen mit mehreren Bedingungen), klicken Sie mit der rechten Maustaste auf die Formel die Sie löschen wollen und wählen Sie die Option Formel löschen im Menü aus. Diese Option ist auch für andere Formelarten verfügbar, wenn Sie zuvor über die Eingabetaste neue Platzhalter hinzugefügt haben. Um einen Grenzwert zu löschen, klicken Sie diesen mit der rechten Maustaste an und wählen Sie die Option Grenzwert entfernen im Menü aus. Um einen Akzent zu löschen, klicken Sie diesen mit der rechten Maustaste an und wählen Sie im Menü die Option Akzentzeichen entfernen, Überstrich entfernen oder Unterstrich entfernen (die verfügbaren Optionen hängen vom ausgewählten Akzent ab). Um eine Zeile bzw. Spalte in einer Matrix zu löschen, klicken Sie mit der rechten Maustaste auf den Platzhalter in der entsprechenden Zeile/Spalte, wählen Sie im Menü die Option Entfernen und wählen Sie dann Zeile/Spalte entfernen. Gleichungen konvertieren Wenn Sie ein vorhandenes Dokument öffnen, das Formeln enthält, die mit einer alten Version des Formeleditors erstellt wurden (z. B. mit MS Office-Versionen vor 2007), müssen Sie diese Formeln in das Office Math ML-Format konvertieren, um sie bearbeiten zu können. Um eine Gleichung zu konvertieren, doppelklicken Sie darauf. Das Warnfenster wird angezeigt: Um nur die ausgewählte Gleichung zu konvertieren, klicken Sie im Warnfenster auf die Schaltfläche Ja. Um alle Gleichungen in diesem Dokument zu konvertieren, aktivieren Sie das Kontrollkästchen Auf alle Gleichungen anwenden und klicken Sie auf Ja. Nachdem die Gleichung konvertiert wurde, können Sie sie bearbeiten."
    },
   {
        "id": "UsageInstructions/InsertFootnotes.htm", 
        "title": "Fußnoten einfügen", 
        "body": "Im Dokumenteneditor sie können Fußnoten einfügen, um Kommentare zu bestimmten Sätzen oder Begriffen in Ihrem Text einzufügen, Referenzen und Quellen anzugeben usw. Fußnoten einfügen Eine Fußnote einfügen: Positionieren Sie den Einfügepunkt am Ende der Textpassage, der Sie eine Fußnote hinzufügen möchten. Wechseln Sie in der oberen Symbolleiste auf die Registerkarte Verweise. Klicken Sie auf das Symbol Fußnote in der oberen Symbolleiste oder klicken Sie auf den Pfeil neben dem Symbol Fußnote und wählen Sie die Option Fußnote einfügen aus dem Menü aus. Das Fußnotenzeichen (d.h. das hochgestellte Zeichen, das eine Fußnote anzeigt) wird im Dokumenttext angezeigt und die Textmarke springt an das unteren Ende der aktuellen Seite. Geben Sie den Text der Fußnote ein. Wiederholen Sie den beschriebenen Vorgang, um weitere Fußnoten für andere Textpassagen in Ihrem Dokument hinzuzufügen. Die Fußnoten werden automatisch nummeriert. Darstellung der Fußnoten im Dokument Wenn Sie den Mauszeiger über das Fußnotenzeichen bewegen, öffnet sich ein kleines Popup-Fenster mit dem Fußnotentext. Navigieren durch Fußnoten Um zwischen den hinzugefügten Fußnoten im Dokument zu navigieren, Klicken Sie auf der Registerkarte Verweise in der oberen Symbolleiste auf den Pfeil neben dem Symbol Fußnote. Navigieren Sie im Abschnitt Zu Fußnoten übergehen über die Pfeile und zur nächsten oder zur vorherigen Fußnote. Fußnoten bearbeiten Um die Einstellungen der Fußnoten zu ändern, Klicken Sie auf der Registerkarte Verweise in der oberen Symbolleiste auf den Pfeil neben dem Symbol Fußnote. Wählen Sie die Option Hinweise Einstellungen aus dem Menü aus. Ändern Sie im Fenster Hinweise Einstellungen die aktuellen Parameter: Markieren Sie das Kästchen Fußnote, um nur die Fußnoten zu bearbeiten. Legen Sie die Position der Fußnoten auf der Seite fest, indem Sie eine der verfügbaren Optionen aus dem Drop-Down-Menü rechts auswählen: Seitenende - um Fußnoten am unteren Seitenrand zu positionieren (diese Option ist standardmäßig ausgewählt). Unter dem Text - um Fußnoten dicht am entsprechenden Textabschnitt zu positionieren. Diese Option ist nützlich, wenn eine Seite nur einen kurzen Textabschnitt enthält. Passen Sie den Format der Fußnoten an: Zahlenformat - wählen Sie das gewünschte Format aus der Liste mit verfügbaren Formaten aus: 1, 2, 3,..., a, b, c,..., A, B, C,..., i, ii, iii,..., I, II, III,.... Starten - über die Pfeiltasten können Sie festlegen, bei welchem Buchstaben oder welcher Zahl Sie beginnen möchten. Nummerierung - wählen Sie aus, auf welche Weise Sie Ihre Fußnoten nummerieren möchten: Kontinuierlich - um Fußnoten im gesamten Dokument fortlaufend zu nummerieren. Jeden Abschnitt neu beginnen - die Nummerierung der Fußnoten beginnt in jedem neuen Abschnitt wieder bei 1 (oder einem anderen festgelegten Wert). Jede Seite neu beginnen - die Nummerierung der Fußnoten beginnt auf jeder neuen Seite wieder bei 1 (oder einem anderen festgelegten Wert). Benutzerdefiniert - legen Sie ein Sonderzeichen oder ein Wort fest, das Sie als Fußnotenzeichen verwenden möchten (z. B. * oder Note1). Geben Sie das gewünschte Wort/Zeichen in das dafür vorgesehene Feld ein und klicken Sie auf Einfügen im Fenster Hinweise Einstellungen. Legen Sie in der Dropdown-Liste Änderungen anwenden fest, ob Sie die angegebenen Fußnoteneinstellungen auf das ganze Dokument oder nur den aktuellen Abschnitt anwenden wollen. Um unterschiedliche Fußnotenformatierungen in verschiedenen Teilen des Dokuments zu verwenden, müssen Sie zunächst Abschnittsumbrüche einfügen. Wenn Sie bereits sind, klicken Sie auf Anwenden. Fußnoten entfernen Um eine einzelne Fußnote zu entfernen, positionieren Sie den Einfügepunkt direkt vor der Fußnotenmarkierung und drücken Sie auf ENTF. Andere Fußnoten werden automatisch neu nummeriert. Um alle Fußnoten in einem Dokument zu entfernen, Klicken Sie auf den Pfeil neben dem Symbol Fußnote auf der Registerkarte Verweise in der oberen Symbolleiste. Wählen Sie im Menü die Option Alle Anmerkungen löschen. Wählen Sie im erscheinenden Fenster die Option Alle Fußnoten löschen und klicken Sie auf OK."
    },
   {
        "id": "UsageInstructions/InsertHeadersFooters.htm", 
        "title": "Kopf- und Fußzeilen einfügen", 
        "body": "Um eine neue Kopf-/Fußzeile hinzuzufügen oder zu entfernen oder eine bereits vorhandene Dokumenteneditor zu bearbeiten: Wechseln Sie in der oberen Symbolleiste auf die Registerkarte Einfügen. Klicken Sie auf das Symbol Kopf-/Fußzeile bearbeiten. Wählen Sie eine der folgenden Optionen: Kopfzeile bearbeiten, um den Text in der Kopfzeile einzugeben oder zu bearbeiten. Fußzeile bearbeiten, um den Text in der Fußzeile einzugeben oder zu bearbeiten. Kopfzeile entfernen, um die Kopfzeile zu löschen. Fußzeile entfernen, um die Fußzeile zu löschen. Ändern der aktuellen Parameter für die Kopf- oder Fußzeile in der rechten Seitenleiste: Legen Sie die aktuelle Position des Texts relativ zum Seitenanfang (für Kopfzeilen) oder zum Seitenende (für Fußzeilen) fest. Wenn Sie der ersten Seite eine andere oder keine Kopf- oder Fußzeile zuweisen wollen, aktivieren Sie die Option Erste Seite anders. Mit der Option Gerade &amp; ungerade Seiten unterschiedlich können Sie geraden und ungeraden Seiten unterschiedliche Kopf- oder Fußzeilen zuweisen. Die Option Mit vorheriger verknüpfen ist verfügbar, wenn Sie zuvor Abschnitte in Ihr Dokument eingefügt haben. Sind keine Abschnitte vorhanden, ist die Option ausgeblendet. Außerdem ist diese Option auch für den allerersten Abschnitt nicht verfügbar (oder wenn eine Kopf- oder Fußzeile ausgewählt ist, die zu dem ersten Abschnitt gehört). Standardmäßig ist dieses Kontrollkästchen aktiviert, sodass die selbe Kopf-/Fußzeile auf alle Abschnitte angewendet wird. Wenn Sie einen Kopf- oder Fußzeilenbereich auswählen, sehen Sie, dass der Bereich mit der Verlinkung Wie vorherige markiert ist. Deaktivieren Sie das Kontrollkästchen Wie vorherige, um für jeden Abschnitt des Dokuments eine andere Kopf-/Fußzeile zu verwenden. Die Markierung Wie vorherige wird nicht mehr angezeigt. Um einen Text einzugeben oder den bereits vorhandenen Text zu bearbeiten und die Einstellungen der Kopf- oder Fußzeilen zu ändern, können Sie auch im oberen oder unteren Bereich der Seite einen Doppelklick ausführen oder in diesem Bereich die rechte Maustaste klicken und über das Kontextmenü die gewünschte Option wählen: Kopfzeile bearbeiten oder Fußzeile bearbeiten. Um zur Dokumentbearbeitung zurückzukehren, führen Sie einen Doppelklick im Arbeitsbereich aus. Der Inhalt Ihrer Kopf- oder Fußzeile wird grau angezeigt. Für Informationen über die Erstellung von Seitenzahlen, lesen Sie bitte den Abschnitt Seitenzahlen einfügen."
    },
   {
        "id": "UsageInstructions/InsertImages.htm", 
        "title": "Bilder einfügen", 
        "body": "Im Dokumenteneditor können Sie Bilder in den gängigsten Formaten in Ihr Dokument einfügen. Die folgenden Bildformate werden unterstützt: BMP, GIF, JPEG, JPG, PNG. Bild einfügen Um ein Bild in den Dokumenttext einzufügen, Plazieren Sie den Zeiger an der Stelle, an der das Bild platziert werden soll. Wechseln Sie zur Registerkarte Einfügen in der oberen Symbolleiste. Klicken Sie auf das Bildsymbol in der oberen Symbolleiste. Wählen Sie eine der folgenden Optionen, um das Bild zu laden: Die Option Bild aus Datei öffnet das Standarddialogfenster für die Dateiauswahl. Durchsuchen Sie das Festplattenlaufwerk Ihres Computers nach der erforderlichen Date und klicken Sie auf die Schaltfläche Öffnen. Im Online-Editor können Sie mehrere Bilder gleichzeitig auswählen. Die Option Bild von URL öffnet das Fenster, in dem Sie die erforderliche Bild-Webadresse eingeben und auf die Schaltfläche OK klicken können. Die Option Bild aus Speicher öffnet das Fenster Datenquelle auswählen. Wählen Sie ein in Ihrem Portal gespeichertes Bild aus und klicken Sie auf die Schaltfläche OK. Sobald das Bild hinzugefügt wurde, können Sie seine Größe, Eigenschaften und Position ändern. Es ist auch möglich, dem Bild eine Beschriftung hinzuzufügen. Weitere Informationen zum Arbeiten mit Bildunterschriften finden Sie in diesem Artikel. Die Größe von Bildern ändern und Bilder verschieben Um die Bildgröße zu ändern, ziehen Sie kleine Quadrate an den Rändern. Halten Sie die Umschalttaste gedrückt und ziehen Sie eines der Eckensymbole, um die ursprünglichen Proportionen des ausgewählten Bilds während der Größenänderung beizubehalten. Verwenden Sie zum Ändern der Bildposition das Symbol , das angezeigt wird, nachdem Sie den Mauszeiger über das Bild bewegt haben. Ziehen Sie das Bild an die gewünschte Position, ohne die Maustaste loszulassen. Wenn Sie das Bild verschieben, werden Hilfslinien angezeigt, mit denen Sie das Objekt präzise auf der Seite positionieren können (wenn ein anderer Umbruchstil als Inline ausgewählt ist). Um das Bild zu drehen, bewegen Sie den Mauszeiger über den Drehgriff und ziehen Sie ihn im oder gegen den Uhrzeigersinn. Halten Sie die Umschalttaste gedrückt, um den Drehwinkel auf Schritte von 15 Grad zu beschränken. Die Liste der Tastaturkürzel, die beim Arbeiten mit Objekten verwendet werden können, finden Sie hier. Die Bildeinstellungen anpassen Einige der Bildeinstellungen können über die Registerkarte Bildeinstellungen in der rechten Seitenleiste geändert werden. Um es zu aktivieren, klicken Sie auf das Bild und wählen Sie rechts das Symbol Bildeinstellungen . Hier können Sie folgende Eigenschaften ändern: Größe wird verwendet, um die aktuelle Bildbreite und -höhe anzuzeigen. Bei Bedarf können Sie die tatsächliche Bildgröße wiederherstellen, indem Sie auf die Schaltfläche Tatsächliche Größe klicken. Mit der Schaltfläche An Rand anpassen können Sie die Größe des Bilds so ändern, dass es den gesamten Abstand zwischen dem linken und rechten Seitenrand einnimmt. Mit der Schaltfläche Zuschneiden können Sie das Bild zuschneiden. Klicken Sie auf die Schaltfläche Zuschneiden, um die Beschneidungsgriffe zu aktivieren, die an den Bildecken und in der Mitte jeder Seite angezeigt werden. Ziehen Sie die Ziehpunkte manuell, um den Zuschneidebereich festzulegen. Sie können den Mauszeiger über den Rand des Zuschneidebereichs bewegen, sodass er zum Symbol wird, und den Bereich ziehen. Um eine einzelne Seite zuzuschneiden, ziehen Sie den Griff in der Mitte dieser Seite. Ziehen Sie einen der Eckgriffe, um zwei benachbarte Seiten gleichzeitig zuzuschneiden. Um zwei gegenüberliegende Seiten des Bildes gleichermaßen zuzuschneiden, halten Sie die Strg-Taste gedrückt, wenn Sie den Griff in die Mitte einer dieser Seiten ziehen. Um alle Seiten des Bildes gleichmäßig zuzuschneiden, halten Sie die Strg-Taste gedrückt, wenn Sie einen der Eckgriffe ziehen. Wenn der Zuschneidebereich angegeben ist, klicken Sie erneut auf die Schaltfläche Zuschneiden oder drücken Sie die Esc-Taste oder klicken Sie auf eine beliebige Stelle außerhalb des Zuschneidebereichs, um die Änderungen zu übernehmen. Nachdem der Zuschneidebereich ausgewählt wurde, können Sie auch die Optionen Auf Form zuschneiden, Ausfüllen und Anpassen verwenden, die im Dropdown-Menü Zuschneiden verfügbar sind. Klicken Sie erneut auf die Schaltfläche Zuschneiden und wählen Sie die gewünschte Option aus: Wenn Sie die Option Auf Form zuschneiden auswählen, füllt das Bild eine bestimmte Form aus. Sie können eine Form aus der Galerie auswählen, die geöffnet wird, wenn Sie Ihren Mauszeiger über die Option Auf Form zuschneiden bewegen. Sie können weiterhin die Optionen Füllen und Anpassen verwenden, um auszuwählen, wie Ihr Bild der Form entspricht. Wenn Sie die Option Füllen auswählen, bleibt der zentrale Teil des Originalbilds erhalten und wird zum Füllen des ausgewählten Zuschneidebereichs verwendet, während andere Teile des Bildes entfernt werden. Wenn Sie die Option Anpassen auswählen, wird die Größe des Bilds so angepasst, dass es der Höhe oder Breite des Zuschneidebereichs entspricht. Es werden keine Teile des Originalbilds entfernt, es können jedoch leere Bereiche innerhalb des ausgewählten Zuschneidebereichs angezeigt werden. Durch Drehen wird das Bild um 90 Grad im oder gegen den Uhrzeigersinn gedreht sowie das Bild horizontal oder vertikal gespiegelt. Klicken Sie auf eine der Schaltflächen: um das Bild um 90 Grad gegen den Uhrzeigersinn zu drehen um das Bild um 90 Grad im Uhrzeigersinn zu drehen um das Bild horizontal zu drehen (von links nach rechts) um das Bild vertikal zu drehen (verkehrt herum) Der Umbruchstil wird verwendet, um einen Textumbruchstil aus den verfügbaren auszuwählen - Inline, Quadrat, Eng, Durch, Oben und Unten, vorne, hinten (weitere Informationen finden Sie in der Beschreibung der erweiterten Einstellungen unten). Bild ersetzen wird verwendet, um das aktuelle Bild zu ersetzen, das ein anderes aus Datei oder Von URL lädt. Einige dieser Optionen finden Sie auch im Kontextmenü. Die Menüoptionen sind: Ausschneiden, Kopieren, Einfügen - Standardoptionen, mit denen ein ausgewählter Text / ein ausgewähltes Objekt ausgeschnitten oder kopiert und eine zuvor ausgeschnittene / kopierte Textpassage oder ein Objekt an die aktuelle Zeigerposition eingefügt wird. Anordnen wird verwendet, um das ausgewählte Bild in den Vordergrund zu bringen, in den Hintergrund zu senden, vorwärts oder rückwärts zu bewegen sowie Bilder zu gruppieren oder die Gruppierung aufzuheben, um Operationen mit sieben auszuführen von ihnen sofort. Weitere Informationen zum Anordnen von Objekten finden Sie auf dieser Seite. Ausrichten wird verwendet, um das Bild links, in der Mitte, rechts, oben, in der Mitte und unten auszurichten. Weitere Informationen zum Ausrichten von Objekten finden Sie auf dieser Seite. Der Umbruchstil wird verwendet, um einen Textumbruchstil aus den verfügbaren auszuwählen - inline, quadratisch, eng, durch, oben und unten, vorne, hinten - oder um die Umbruchgrenze zu bearbeiten. Die Option Wrap-Grenze bearbeiten ist nur verfügbar, wenn Sie einen anderen Wrap-Stil als Inline auswählen. Ziehen Sie die Umbruchpunkte, um die Grenze anzupassen. Um einen neuen Umbruchpunkt zu erstellen, klicken Sie auf eine beliebige Stelle auf der roten Linie und ziehen Sie sie an die gewünschte Position. Drehen wird verwendet, um das Bild um 90 Grad im oder gegen den Uhrzeigersinn zu drehen sowie um das Bild horizontal oder vertikal zu spiegeln. Zuschneiden wird verwendet, um eine der Zuschneideoptionen anzuwenden: Zuschneiden, Füllen oder Anpassen. Wählen Sie im Untermenü die Option Zuschneiden, ziehen Sie dann die Zuschneidegriffe, um den Zuschneidebereich festzulegen, und klicken Sie im Untermenü erneut auf eine dieser drei Optionen, um die Änderungen zu übernehmen. Die Tatsächliche Größe wird verwendet, um die aktuelle Bildgröße in die tatsächliche zu ändern. Bild ersetzen wird verwendet, um das aktuelle Bild zu ersetzen, das ein anderes aus Datei oder Von URL lädt. Mit den Erweiterte Einstellungen des Bildes wird das Fenster \"Bild - Erweiterte Einstellungen\" geöffnet. Wenn das Bild ausgewählt ist, ist rechts auch das Symbol für die Formeinstellungen verfügbar. Sie können auf dieses Symbol klicken, um die Registerkarte Formeinstellungen in der rechten Seitenleiste zu öffnen und die Form anzupassen. Strichart, -größe und -farbe sowie den Formtyp ändern, indem Sie im Menü Autoshape ändern eine andere Form auswählen. Die Form des Bildes ändert sich entsprechend. Auf der Registerkarte Formeinstellungen können Sie auch die Option Schatten anzeigen verwenden, um dem Bild einen Schatten hinzuzufügen. Die erweiterten Bildeinstellungen anpassen Um die erweiterten Einstellungen des Bildes zu ändern, klicken Sie mit der rechten Maustaste auf das Bild und wählen Sie im Kontextmenü die Option Bild - Erweiterte Einstellungen oder klicken Sie einfach auf den Link Erweiterte Einstellungen anzeigen in der rechten Seitenleiste. Das Fenster mit den Bildeigenschaften wird geöffnet: Die Registerkarte Größe enthält die folgenden Parameter: Breite und Höhe - Verwenden Sie diese Optionen, um die Bildbreite und / oder -höhe zu ändern. Wenn Sie auf die Schaltfläche Konstante Proportionen klicken (in diesem Fall sieht es so aus ), werden Breite und Höhe zusammen geändert, wobei das ursprüngliche Bildseitenverhältnis beibehalten wird. Klicken Sie auf die Schaltfläche Tatsächliche Größe, um die tatsächliche Größe des hinzugefügten Bilds wiederherzustellen. Die Registerkarte Rotation enthält die folgenden Parameter: Winkel - Verwenden Sie diese Option, um das Bild um einen genau festgelegten Winkel zu drehen. Geben Sie den erforderlichen Wert in Grad in das Feld ein oder passen Sie ihn mit den Pfeilen rechts an. Spiegeln - Aktivieren Sie das Kontrollkästchen Horizontal, um das Bild horizontal zu spiegeln (von links nach rechts), oder aktivieren Sie das Kontrollkästchen Vertikal, um das Bild vertikal zu spiegeln (verkehrt herum). Die Registerkarte Textumbruch enthält die folgenden Parameter: Umbruchstil - Verwenden Sie diese Option, um die Position des Bilds relativ zum Text zu ändern: Es ist entweder Teil des Textes (falls Sie den Inline-Stil auswählen) oder wird von allen Seiten umgangen (wenn Sie einen auswählen) die anderen Stile). Inline - Das Bild wird wie ein Zeichen als Teil des Textes betrachtet. Wenn sich der Text bewegt, bewegt sich auch das Bild. In diesem Fall sind die Positionierungsoptionen nicht zugänglich. Wenn einer der folgenden Stile ausgewählt ist, kann das Bild unabhängig vom Text verschoben und genau auf der Seite positioniert werden: Quadratisch - Der Text umschließt das rechteckige Feld, das das Bild begrenzt. Eng - Der Text umschließt die eigentlichen Bildkanten. Durch - Der Text wird um die Bildränder gewickelt und füllt den offenen weißen Bereich innerhalb des Bildes aus. Verwenden Sie die Option Umbruchgrenze bearbeiten im Kontextmenü, damit der Effekt angezeigt wird. Oben und unten - der Text befindet sich nur über und unter dem Bild. Vorne - das Bild überlappt den Text. Dahinter - der Text überlappt das Bild. Wenn Sie den quadratischen, engen, durchgehenden oder oberen und unteren Stil auswählen, können Sie einige zusätzliche Parameter festlegen - Abstand zum Text an allen Seiten (oben, unten, links, rechts). Die Registerkarte Position ist nur verfügbar, wenn Sie einen anderen Umbruchstil als Inline auswählen. Diese Registerkarte enthält die folgenden Parameter, die je nach ausgewähltem Verpackungsstil variieren: Im horizontalen Bereich können Sie einen der folgenden drei Bildpositionierungstypen auswählen: Ausrichtung (links, Mitte, rechts) relativ zu Zeichen, Spalte, linkem Rand, Rand, Seite oder rechtem Rand, Absolute Position gemessen in absoluten Einheiten, d. H. Zentimeter / Punkte / Zoll (abhängig von der auf der Registerkarte Datei -&gt; Erweiterte Einstellungen... angegebenen Option), rechts neben Zeichen, Spalte, linkem Rand, Rand, Seite oder rechtem Rand, Relative Position gemessen in Prozent relativ zum linken Rand, Rand, Seite oder rechten Rand. Im vertikalen Bereich können Sie einen der folgenden drei Bildpositionierungstypen auswählen: Ausrichtung (oben, Mitte, unten) relativ zu Linie, Rand, unterem Rand, Absatz, Seite oder oberem Rand, Absolute Position gemessen in absoluten Einheiten, d. H. Zentimeter / Punkte / Zoll (abhängig von der auf der Registerkarte Datei -&gt; Erweiterte Einstellungen... angegebenen Option) unter Zeile, Rand, unterem Rand, Absatz, Seite oder oberem Rand, Relative Position gemessen in Prozent relativ zum Rand, unteren Rand, Seite oder oberen Rand. Objekt mit Text verschieben steuert, ob sich das Bild bewegt, während sich der Text, an dem es verankert ist, bewegt. Überlappungssteuerung zulassen steuert, ob sich zwei Bilder überlappen oder nicht, wenn Sie sie auf der Seite nebeneinander ziehen. Auf der Registerkarte Alternativer Text können Sie einen Titel und eine Beschreibung angeben, die Personen mit Seh- oder kognitiven Beeinträchtigungen vorgelesen werden, damit sie besser verstehen, welche Informationen im Bild enthalten sind."
    },
   {
        "id": "UsageInstructions/InsertLineNumbers.htm", 
        "title": "Zeilennummern einfügen", 
        "body": "Der ONLYOFFICE Dokumenteneditor kann Zeilen in Ihrem Dokument automatisch zählen. Diese Funktion kann nützlich sein, wenn Sie auf eine bestimmte Zeile des Dokuments verweisen müssen, z.B. in einer Vereinbarung oder einem Code-Skript. Verwenden Sie das Tool Zeilennummern, um die Zeilennummerierung auf das Dokument anzuwenden. Bitte beachten Sie, dass die Zeilennummerierungssequenz nicht auf den Text in den Objekten wie Tabellen, Textfeldern, Diagrammen, Kopf- / Fußzeilen usw. angewendet wird. Diese Objekte werden als eine Zeile behandelt. Zeilennummern anwenden Öffnen Sie die Registerkarte Layout in der oberen Symbolleiste und klicken Sie auf das Symbol Zeilennummern. Wählen Sie im geöffneten Drop-Down-Menü die gewünschten Parameter für eine schnelle Einrichtung: Ununterbrochen - jeder Zeile des Dokuments wird eine Sequenznummer zugewiesen. Jede Seite neu beginnen - die Zeilennummerierungssequenz wird auf jeder Seite des Dokuments neu gestartet. Jeden Abschnitt neu beginnen - die Zeilennummerierungssequenz wird in jedem Abschnitt des Dokuments neu gestartet. Bitte lesen Sie diese Anleitung, um mehr über die Abschnittsümbrüche zu lernen. Im aktuellen Absatz verbieten - der aktuelle Absatz wird in der Zeilennummerierungssequenz übersprungen. Um mehrere Absätze von der Sequenz auszuschließen, wählen Sie sie mit der linken Maustaste aus, bevor Sie diesen Parameter anwenden. Geben Sie bei Bedarf die erweiterten Parameter an. Klicken Sie auf dem Menüpunkt Zeilennummerierungsoptionen im Drop-Down-Menü Zeilennummern. Markieren Sie das Kästchen Zeilennummer hinzufügen, um die Zeilennummerierung auf das Dokument anzuwenden und auf die erweiterten Einstellungen zuzugreifen: Die Option Beginnen mit legt den numerischen Startwert der Zeilennummerierungssequenz fest. Der Parameter ist auf 1 standarmäßig eingestellt. Die Option Aus dem Text gibt den Abstand zwischen den Zeilennummern und dem Text an. Geben Sie den gewünschten Wert in cm ein. Der Parameter ist auf Auto standarmäßig eingestellt. Die Option Zählintervall bestimmt, wie viel Zeilen eine Zeilennummerierung erscheinen soll, d.h. die Zahlen werden in einem Bündel (zweifach, dreifach usw.) gezählt. Geben Sie den erforderlichen numerischen Wert ein. Der Parameter ist auf 1 standardmäßig eingestellt. Die Option Jede Seite neu beginnen: Die Zeilennummerierungssequenz wird auf jeder Seite des Dokuments neu gestartet. Die Option Jeden Abschnitt neu beginnen: Die Zeilennummerierungssequenz wird in jedem Abschnitt des Dokuments neu gestartet. Die Option Ununterbrochen: Jeder Zeile des Dokuments wird eine Sequenznummer zugewiesen. Die Option Anwendung von Änderungen auf gibt den Teil des Dokuments an, dem Sie Sequenznummern zuweisen möchten. Wählen Sie eine der verfügbaren Voreinstellungen: Aktueller Abschnitt, um die Zeilennummerierung auf den ausgewählten Abschnitt des Dokuments anzuwenden; Bis zum Ende des Dokuments, um die Zeilennummerierung auf den Text anzuwenden, der der aktuellen Cursorposition folgt; Zum ganzen Dokument, um die Zeilennummerierung auf das gesamte Dokument anzuwenden. Der Parameter ist auf Zum ganzen Dokument standardmäßig eingestellt. Klicken Sie auf OK, um die Änderungen anzunehmen. Zeilennummern entfernen Um die Zeilennummerierungssequenz zu entfernen, öffnen Sie die Registerkarte Layout in der oberen Symbolleiste und klicken Sie auf das Symbol Zeilennummern, wählen Sie die Option Kein im geöffneten Drop-Down-Menü oder wählen Sie den Menüpunkt Zeilennummerierungsoptionen aus und im geöffneten Fenster Zeilennummern deaktivieren Sie das Kästchen Zeilennummer hinzufügen."
    },
   {
        "id": "UsageInstructions/InsertPageNumbers.htm", 
        "title": "Seitenzahlen einfügen", 
        "body": "Um Seitenzahlen in ein Dokument einfügen im Dokumenteneditor: Wechseln Sie zu der oberen Symbolleiste auf die Registerkarte Einfügen. Klicken Sie in der oberen Symbolleiste auf das Symbol Kopf- und Fußzeile bearbeiten . Klicken Sie auf Seitenzahl einfügen. Wählen Sie eine der folgenden Optionen: Wählen Sie die Position der Seitenzahl aus, um zu jeder Dokumentseite eine Seitenzahl hinzuzufügen. Um an der aktuellen Zeigerposition eine Seitenzahl einzufügen, wählen Sie die Option An aktueller Position. Um in der aktuellen Seite an der derzeitigen Position eine Seitennummer einzufügen, kann die Tastenkombination STRG+UMSCHALT+P benutzt werden. ODER Wechseln Sie zur Registerkarte Einfügen der oberen Symbolleiste. Klicken Sie auf das Symbol Kopf- und Fußzeile in der oberen Symbolleiste. Klicken Sie im Menü auf die Option Seitenzahl einfügen und wählen Sie die Position der Seitenzahl. Um die Anzahl der Seiten einfügen (z.B. wenn Sie den Eintrag Seite X von Y erstellen möchten): Positionieren Sie den Zeiger an die Position, an der Sie die Anzahl der Seiten einfügen wollen. Klicken Sie in der oberen Symbolleiste auf das Symbol Kopf- und Fußzeile bearbeiten . Wählen Sie die Option Anzahl der Seiten einfügen. Einstellungen der Seitenzahlen ändern: Klicken Sie zweimal auf die hinzugefügte Seitenzahl. Ändern Sie die aktuellen Parameter in der rechten Seitenleiste: Legen Sie die aktuelle Position der Seitenzahlen auf der Seite sowie im Verhältnis zum oberen und unteren Teil der Seite fest. Wenn Sie der ersten Seite eine andere Zahl zuweisen wollen oder als dem restlichen Dokument oder keine Seitenzahl auf der ersten Seite einfügen wollen, aktivieren Sie die Option Erste Seite anders. Wenn Sie geraden und ungeraden Seiten unterschiedliche Seitenzahlen hinzufügen wollen, aktivieren Sie die Option Gerade &amp; ungerade Seiten unterschiedlich. Die Option Mit vorheriger verknüpfen ist verfügbar, wenn Sie zuvor Abschnitte in Ihr Dokument eingefügt haben. Sind keine Abschnitte vorhanden, ist die Option ausgeblendet. Außerdem ist diese Option auch für den allerersten Abschnitt nicht verfügbar (oder wenn eine Kopf- oder Fußzeile ausgewählt ist, die zu dem ersten Abschnitt gehört). Standardmäßig ist dieses Kontrollkästchen aktiviert, sodass auf alle Abschnitte die vereinheitlichte Nummerierung angewendet wird. Wenn Sie einen Kopf- oder Fußzeilenbereich auswählen, sehen Sie, dass der Bereich mit der Verlinkung Wie vorherige markiert ist. Deaktivieren Sie das Kontrollkästchen Wie vorherige, um in jedem Abschnitt des Dokuments eine andere Seitennummerierung anzuwenden. Die Markierung Wie vorherige wird nicht mehr angezeigt. Der Abschnitt Seitennummerierung ermöglicht das Anpassen der Seitennummerierungsoptionen in verschiedenen Abschnitten des Dokuments. Die Option Fortsetzen vom vorherigen Abschnitt ist standardmäßig ausgewählt und ermöglicht es, die fortlaufende Seitennummerierung nach einem Abschnittswechsel beizubehalten. Wenn Sie die Seitennummerierung mit einer bestimmten Nummer im aktuellen Abschnitt des Dokuments beginnen möchten, wählen Sie das Optionsfeld Starten mit und geben Sie den gewünschten Startwert in das Feld rechts ein. Um zur Dokumentbearbeitung zurückzukehren, führen Sie einen Doppelklick im Arbeitsbereich aus."
    },
   {
        "id": "UsageInstructions/InsertReferences.htm", 
        "title": "Verweise einfügen", 
        "body": "ONLYOFFICE Dokumenteneditor unterstützt die Literaturverwaltungsprogramme wie Mendeley, Zotero und EasyBib beim Einfügen von Quellenangaben in Ihr Dokument. Mendeley ONLYOFFICE mit Mendeley verbinden Melden Sie sich bei Ihrem Mendeley-Konto an. Wechseln Sie in Ihrem Dokument zur Registerkarte Plugins und wählen Sie Mendeley aus. Eine Seitenleiste wird links in Ihrem Dokument geöffnet. Klicken Sie auf die Schaltfläche Link kopieren und Formular öffnen. Der Browser öffnet ein Formular auf der Mendeley-Seite. Füllen Sie dieses Formular aus und notieren Sie die Anwendungs-ID für ONLYOFFICE. Wechseln Sie zurück zu Ihrem Dokument. Geben Sie die Anwendungs-ID ein und klicken Sie auf Speichern. Klicken Sie auf Anmelden. Klicken Sie auf Weiter. Jetzt ist ONLYOFFICE mit Ihrem Mendeley-Konto verbunden. Verweise einfügen Öffnen Sie das Dokument und platzieren Sie den Cursor an der Stelle, an der Sie den Verweis(-e) einfügen möchten. Öffnen Sie die Registerkarte Plugins und wählen Sie den Menüpunkt Mendeley aus. Geben Sie einen Suchtext ein und drücken Sie die Eingabetaste auf Ihrer Tastatur. Klicken Sie auf ein oder mehrere Kontrollkästchen. [Optional] Geben Sie einen neuen Suchtext ein und klicken Sie auf ein oder mehrere Kontrollkästchen. Wählen Sie den Referenzstil aus dem Pulldown-Menü Stil. Klicken Sie auf die Schaltfläche Bibliographie einfügen. Zotero ONLYOFFICE mit Zotero verbinden Melden Sie sich bei Ihrem Zotero-Konto an. Wechseln Sie in Ihrem Dokument zur Registerkarte Plugins und wählen Sie Zotero aus. Eine Seitenleiste wird links in Ihrem Dokument geöffnet. Klicken Sie auf Zotero API-Einstellungen. Erstellen Sie auf der Zotero-Seite einen neuen Schlüssel für Zotero, kopieren Sie ihn und speichern Sie ihn für später. Wechseln Sie zu Ihrem Dokument und fügen Sie den API-Schlüssel ein. Klicken Sie auf Speichern. Jetzt ist ONLYOFFICE mit Ihrem Zotero-Konto verbunden. Verweise einfügen Öffnen Sie das Dokument und platzieren Sie den Cursor an der Stelle, an der Sie den Verweis(-e) einfügen möchten. Öffnen Sie die Registerkarte Plugins und wählen Sie den Menüpunkt Zotero aus. Geben Sie einen Suchtext ein und drücken Sie die Eingabetaste auf Ihrer Tastatur. Klicken Sie auf ein oder mehrere Kontrollkästchen. [Optional] Geben Sie einen neuen Suchtext ein und klicken Sie auf ein oder mehrere Kontrollkästchen. Wählen Sie den Referenzstil aus dem Pulldown-Menü Stil. Klicken Sie auf die Schaltfläche Bibliographie einfügen. EasyBib Öffnen Sie das Dokument und platzieren Sie den Cursor an der Stelle, an der Sie den Verweis(-e) einfügen möchten. Öffnen Sie die Registerkarte Plugins und wählen Sie den Menüpunkt EasyBib aus. Wählen Sie den Quellentyp aus, den Sie suchen möchten. Geben Sie einen Suchtext ein und drücken Sie die Eingabetaste auf Ihrer Tastatur. Klicken Sie auf der rechten Seite des entsprechenden Buch-/Zeitschriftenartikels/der Website auf '+'. Es wird der Bibliographie hinzugefügt. Wählen Sie den Referenzstil. Klicken Sie auf Bibliographie zum Dokument hinzufügen, um die Referenzen einzufügen."
    },
   {
        "id": "UsageInstructions/InsertSmartArt.htm", 
        "title": "SmartArt-Objekte einfügen", 
        "body": "SmartArt-Grafiken werden verwendet, um eine visuelle Darstellung einer hierarchischen Struktur zu erstellen, indem ein Layout ausgewählt wird, das am besten passt. Fügen Sie SmartArt-Objekte ein oder bearbeiten Sie die in Editoren von Drittanbietern hinzugefügten Objekte. Um ein SmartArt-Objekt einzufügen, Gehen Sie zur Registerkarte Einfügen. Klicken Sie auf die Schaltfläche SmartArt. Bewegen Sie den Mauszeiger über einen der verfügbaren Layoutstile, z. B. Liste oder Prozess. Wählen Sie einen der verfügbaren Layouttypen aus der Liste, die rechts neben dem hervorgehobenen Menüelement angezeigt wird. Sie können die SmartArt-Einstellungen im rechten Bereich anpassen: Bitte beachten Sie, dass Farb-, Stil- und Formtypeinstellungen individuell angepasst werden können. Füllen - Verwenden Sie diesen Abschnitt, um die automatische SmartArt-Objekt-Füllung auszuwählen. Sie können folgende Optionen auswählen: Farbfüllung - Wählen Sie diese Option aus, um die Volltonfarbe anzugeben, mit der Sie den Innenraum des ausgewählten SmartArt-Objektes füllen möchten. Klicken Sie auf das farbige Feld unten und wählen Sie die gewünschte Farbe aus den verfügbaren Farbsätzen aus oder geben Sie eine beliebige Farbe an: Füllung mit Farbverlauf - wählen Sie diese Option, um die Form mit einem sanften Übergang von einer Farbe zu einer anderen zu füllen. Die verfügbaren Menüoptionen: Stil - wählen Sie Linear oder Radial aus: Linear wird verwendet, wenn Ihre Farben von links nach rechts, von oben nach unten oder in einem beliebigen Winkel in eine Richtung fließen sollen. Das Vorschaufenster Richtung zeigt die ausgewählte Verlaufsfarbe an. Klicken Sie auf den Pfeil, um eine voreingestellte Verlaufsrichtung auszuwählen. Verwenden Sie Winkel-Einstellungen für einen präzisen Verlaufswinkel. Radial wird verwendet, um sich von der Mitte zu bewegen, da die Farbe an einem einzelnen Punkt beginnt und nach außen ausstrahlt. Punkt des Farbverlaufs ist ein bestimmter Punkt für den Verlauf von einer Farbe zur anderen. Verwenden Sie die Schaltfläche Punkt des Farbverlaufs einfügen oder den Schieberegler, um einen Punkt des Verlaufs einzufügen. Sie können bis zu 10 Punkte einfügen. Jeder nächste eingefügte Punkt des Farbverlaufs beeinflusst in keiner Weise die aktuelle Darstellung der Farbverlaufsfüllung. Verwenden Sie die Schaltfläche Punkt des Farbverlaufs entfernen, um den bestimmten Punkt zu löschen. Verwenden Sie den Schieberegler, um die Position des Farbverlaufspunkts zu ändern, oder geben Sie Position in Prozent an, um eine genaue Position zu erhalten. Um eine Farbe auf einen Verlaufspunkt anzuwenden, klicken Sie auf einen Punkt im Schieberegler und dann auf Farbe, um die gewünschte Farbe auszuwählen. Bild oder Textur - Wählen Sie diese Option, um ein Bild oder eine vordefinierte Textur als Formhintergrund zu verwenden. Wenn Sie ein Bild als Hintergrund für die Form verwenden möchten, können Sie ein Bild aus der Datei hinzufügen, indem Sie es auf der Festplatte Ihres Computers auswählen, oder aus der URL, indem Sie die entsprechende URL-Adresse in das geöffnete Fenster einfügen. Wenn Sie eine Textur als Hintergrund für die Form verwenden möchten, öffnen Sie das Menü Von Textur und wählen Sie die gewünschte Texturvoreinstellung aus. Derzeit sind folgende Texturen verfügbar: Leinwand, Karton, dunkler Stoff, Maserung, Granit, graues Papier, Strick, Leder, braunes Papier, Papyrus, Holz. Falls das ausgewählte Bild weniger oder mehr Abmessungen als die automatische Form hat, können Sie die Einstellung Dehnen oder Kacheln aus der Dropdown-Liste auswählen.</p> Mit der Option Dehnen können Sie die Bildgröße an die Größe der automatischen Form anpassen, sodass sie den Raum vollständig ausfüllen kann. Mit der Option Kacheln können Sie nur einen Teil des größeren Bilds anzeigen, wobei die ursprünglichen Abmessungen beibehalten werden, oder das kleinere Bild wiederholen, wobei die ursprünglichen Abmessungen über der Oberfläche der automatischen Form beibehalten werden, sodass der Raum vollständig ausgefüllt werden kann. Jede ausgewählte Texturvoreinstellung füllt den Raum vollständig aus. Sie können jedoch bei Bedarf den Dehnungseffekt anwenden. Muster - Wählen Sie diese Option, um die Form mit einem zweifarbigen Design zu füllen, das aus regelmäßig wiederholten Elementen besteht. Muster - Wählen Sie eines der vordefinierten Designs aus dem Menü. Vordergrundfarbe - Klicken Sie auf dieses Farbfeld, um die Farbe der Musterelemente zu ändern. Hintergrundfarbe - Klicken Sie auf dieses Farbfeld, um die Farbe des Musterhintergrunds zu ändern. Keine Füllung - wählen Sie diese Option, wenn Sie keine Füllung verwenden möchten. Strich - Verwenden Sie diesen Abschnitt, um die Breite, Farbe oder den Typ des Strichs für das SmartArt-Objekt zu ändern. Um die Strichbreite zu ändern, wählen Sie eine der verfügbaren Optionen aus der Dropdown-Liste Größe. Die verfügbaren Optionen sind: 0,5 pt, 1 pt, 1,5 pt, 2,25 pt, 3 pt, 4,5 pt, 6 pt. Alternativ können Sie die Option Keine Linie auswählen, wenn Sie keinen Strich verwenden möchten. Um die Strichfarbe zu ändern, klicken Sie auf das farbige Feld unten und wählen Sie die gewünschte Farbe aus. Um den Strich-Typ zu ändern, wählen Sie die erforderliche Option aus der entsprechenden Dropdown-Liste aus (standardmäßig wird eine durchgezogene Linie angewendet, Sie können sie in eine der verfügbaren gestrichelten Linien ändern). Umbruchstil - verwenden Sie diesen Abschnitt, um einen Textumbruchstil aus den verfügbaren auszuwählen - inline, quadratisch, eng, durch, oben und unten, vorne, hinten (weitere Informationen finden Sie in der Beschreibung der erweiterten Einstellungen unten). Schatten anzeigen - Aktivieren Sie diese Option, um die Form mit Schatten anzuzeigen. Klicken Sie auf den Link Erweiterte Einstellungen anzeigen, um die erweiterten Einstellungen zu öffnen."
    },
   {
        "id": "UsageInstructions/InsertSymbols.htm", 
        "title": "Symbole und Sonderzeichen einfügen", 
        "body": "Während des Arbeitsprozesses im Dokumenteneditor wollen Sie ein Symbol einfügen, das sich nicht auf der Tastatur befindet. Um solche Symbole einzufügen, verwenden Sie die Option Symbol einfügen: positionieren Sie den Textcursor an der Stelle für das Sonderzeichen, öffnen Sie die Registerkarte Einfügen, klicken Sie Symbol an, das Dialogfeld Symbol wird angezeigt, in dem Sie das gewünschte Symbol auswählen können, öffnen Sie das Dropdown-Menü Bereich, um ein Symbol schnell zu finden. Alle Symbole sind in Gruppen unterteilt, wie z.B. “Währungssymbole” für Währungszeichen, falls Sie das gewünschte Symbol nicht finden können, wählen Sie eine andere Schriftart aus. Viele von ihnen haben auch die Sonderzeichen, die es nicht in den Standartsatz gibt, Sie können auch das Unicode HEX Wert-Feld verwenden, um den Code einzugeben. Die Codes können Sie in der Zeichentabelle finden, verwenden Sie auch die Registerkarte Sonderzeichen, um ein Sonderzeichen auszuwählen, die Symbole, die zuletzt verwendet wurden, befinden sich im Feld Kürzlich verwendete Symbole, klicken Sie Einfügen an. Das ausgewählte Symbol wird eingefügt. ASCII-Symbole einfügen Man kann auch die ASCII-Tabelle verwenden, um die Zeichen und Symbole einzufügen. Drücken und halten Sie die ALT-Taste und verwenden Sie den Ziffernblock, um einen Zeichencode einzugeben. Verwenden Sie nur den Ziffernblock. Um den Ziffernblock zu aktivieren, drücken Sie die NumLock-Taste. Z.B., um das Paragraphenzeichen (§) einzufügen, drücken und halten Sie die ALT-Taste und geben Sie 789 ein, dann lassen Sie die ALT-Taste los. Symbole per Unicode-Tabelle einfügen Sonstige Symbole und Zeichen befinden sich auch in der Windows-Symboltabelle. Um diese Tabelle zu öffnen: geben Sie “Zeichentabelle” in dem Suchfeld ein, drücken Sie die Windows-Taste+R und geben Sie charmap.exe im Suchfeld ein, dann klicken Sie OK. Wählen Sie die Zeichensätze, Gruppen und Schriftarten aus. Klicken Sie die gewünschte Zeichen an, dann kopieren und fügen an der gewünschten Stelle ein."
    },
   {
        "id": "UsageInstructions/InsertTables.htm", 
        "title": "Tabellen einfügen", 
        "body": "Fügen Sie eine Tabelle ein So fügen Sie eine Tabelle in den Dokumenttext ein im Dokumenteneditor: Positionieren Sie den Zeiger an der Stelle, an der die Tabelle eingefügt werden soll. Wechseln Sie zur Registerkarte Einfügen in der oberen Symbolleiste. Klicken Sie auf das Tabellensymbol in der oberen Symbolleiste. Wählen Sie die Option zum Erstellen einer Tabelle aus: entweder eine Tabelle mit einer vordefinierten Anzahl von Zellen (maximal 10 mal 8 Zellen) Wenn Sie schnell eine Tabelle hinzufügen möchten, wählen Sie einfach die Anzahl der Zeilen (maximal 8) und Spalten (maximal 10). oder eine benutzerdefinierte Tabelle Wenn Sie mehr als 10 x 8 Zellen benötigen, wählen Sie die Option Benutzerdefinierte Tabelle einfügen, um das Fenster zu öffnen, in dem Sie die erforderliche Anzahl von Zeilen bzw. Spalten eingeben können, und klicken Sie dann auf die Schaltfläche OK. Wenn Sie eine Tabelle mit der Maus zeichnen möchten, wählen Sie die Option Tabelle zeichnen. Dies kann nützlich sein, wenn Sie eine Tabelle mit Zeilen und Spalten unterschiedlicher Größe erstellen möchten. Der Mauszeiger verwandelt sich in einen Bleistift . Zeichnen Sie eine rechteckige Form, in der Sie eine Tabelle hinzufügen möchten, und fügen Sie dann Zeilen hinzu, indem Sie horizontale Linien zeichnen, und Spalten, indem Sie vertikale Linien innerhalb der Tabellengrenze zeichnen. Wenn Sie einen vorhandenen Text in eine Tabelle umwandeln möchten, wählen Sie die Option Text in Tabelle umwandeln. Diese Funktion kann sich als nützlich erweisen, wenn Sie bereits Text haben, den Sie in einer Tabelle anordnen möchten. Das Fenster Text in Tabelle umwandeln besteht aus 3 Abschnitten: Größe der Tabelle. Wählen Sie die erforderliche Anzahl von Spalten/Zeilen, in die Sie Ihren Text verteilen möchten. Sie können entweder die Auf-/Ab-Pfeiltasten verwenden oder die Nummer manuell über die Tastatur eingeben. Einstellung für AutoAnpassen. Aktivieren Sie die erforderliche Option, um die Textanpassung einzustellen: Feste Spaltenbreite (standardmäßig auf Auto eingestellt. Sie können entweder die Auf-/Ab-Pfeiltasten verwenden oder die Zahl manuell über die Tastatur eingeben), Autoanpassen an Inhalt (die Spaltenbreite entspricht der Textlänge), Größe an Fenster anpassen (die Spaltenbreite entspricht der Seitenbreite). Text trennen bei. Aktivieren Sie die erforderliche Option, um einen Trennzeichentyp für Ihren Text festzulegen: Absätze, Tabulatoren, Semikolons und Sonstiges (geben Sie das bevorzugte Trennzeichen manuell ein). Klicken Sie auf OK, um den Text in Tabelle umzuwandeln. Wenn Sie eine Tabelle als OLE-Objekt einfügen möchten: Wählen Sie die Option Tabelle einfügen im Menü Tabelle auf der Registerkarte Einfügen. Es erscheint das entsprechende Fenster, in dem Sie die erforderlichen Daten eingeben und mit den Formatierungswerkzeugen der Tabellenkalkulation wie Auswahl von Schriftart, Typ und Stil, Zahlenformat einstellen, Funktionen einfügen, Tabellen formatieren usw. sie formatieren. Die Kopfzeile enthält die Schaltfläche Sichtbarer Bereich in der oberen rechten Ecke des Fensters. Wählen Sie die Option Sichtbaren Bereich bearbeiten, um den Bereich auszuwählen, der angezeigt wird, wenn das Objekt in das Dokument eingefügt wird; andere Daten gehen nicht verloren, sie werden nur ausgeblendet. Klicken Sie auf Fertig, wenn Sie fertig sind. Klicken Sie auf die Schaltfläche Sichtbaren Bereich anzeigen, um den ausgewählten Bereich mit einem blauen Rand anzuzeigen. Wenn Sie fertig sind, klicken Sie auf die Schaltfläche Speichern und beenden. Sobald die Tabelle hinzugefügt wurde, können Sie ihre Eigenschaften, Größe und Position ändern. Um die Größe einer Tabelle zu ändern, bewegen Sie den Mauszeiger über den Griff in der unteren rechten Ecke und ziehen Sie ihn, bis die Tabelle die erforderliche Größe erreicht hat. Sie können die Breite einer bestimmten Spalte oder die Höhe einer Zeile auch manuell ändern. Bewegen Sie den Mauszeiger über den rechten Rand der Spalte, sodass sich der Zeiger in einen bidirektionalen Pfeil verwandelt, und ziehen Sie den Rand nach links oder rechts, um die erforderliche Breite festzulegen. Um die Höhe einer einzelnen Zeile manuell zu ändern, bewegen Sie den Mauszeiger über den unteren Rand der Zeile, sodass sich der Zeiger in den bidirektionalen Pfeil verwandelt, und ziehen Sie den Rand nach oben oder nach unten. Um eine Tabelle zu verschieben, halten Sie den Griff in der oberen linken Ecke gedrückt und ziehen Sie ihn an die gewünschte Stelle im Dokument. Es ist auch möglich, der Tabelle eine Beschriftung hinzuzufügen. Weitere Informationen zum Arbeiten mit Beschriftungen für Tabellen finden Sie in diesem Artikel. Eine Tabelle oder deren Teil auswählen Um eine gesamte Tabelle auszuwählen, klicken Sie auf den Griff in der oberen linken Ecke. Um eine bestimmte Zelle auszuwählen, bewegen Sie den Mauszeiger auf die linke Seite der gewünschten Zelle, sodass sich der Zeiger in den schwarzen Pfeil verwandelt, und klicken Sie dann mit der linken Maustaste. Um eine bestimmte Zeile auszuwählen, bewegen Sie den Mauszeiger an den linken Rand der Tabelle neben der erforderlichen Zeile, sodass sich der Zeiger in den horizontalen schwarzen Pfeil verwandelt, und klicken Sie dann mit der linken Maustaste. Um eine bestimmte Spalte auszuwählen, bewegen Sie den Mauszeiger an den oberen Rand der erforderlichen Spalte, sodass sich der Zeiger in den schwarzen Pfeil nach unten verwandelt, und klicken Sie dann mit der linken Maustaste. Es ist auch möglich, eine Zelle, Zeile, Spalte oder Tabelle mithilfe von Optionen aus dem Kontextmenü oder aus dem Abschnitt Zeilen und Spalten in der rechten Seitenleiste auszuwählen. Um sich in einer Tabelle zu bewegen, können Sie Tastaturkürzel verwenden. Die Tabelleneinstellungen anpassen Einige der Tabelleneigenschaften sowie deren Struktur können über das Kontextmenü geändert werden. Die Menüoptionen sind: Ausschneiden, Kopieren, Einfügen - Standardoptionen, mit denen ein ausgewählter Text / ein ausgewähltes Objekt ausgeschnitten oder kopiert und eine zuvor ausgeschnittene / kopierte Textpassage oder ein Objekt an die aktuelle Cursorposition eingefügt wird. Mit Auswahl können Sie eine Zeile, Spalte, Zelle oder Tabelle auswählen. Einfügen wird verwendet, um eine Zeile über oder unter der Zeile einzufügen, in der sich der Cursor befindet, sowie um eine Spalte links oder rechts von der Spalte einzufügen, in der sich der Cursor befindet. Es ist auch möglich, mehrere Zeilen oder Spalten einzufügen. Wenn Sie die Option Mehrere Zeilen / Spalten auswählen, wird das Fenster Mehrere Zeilen einfügen geöffnet. Wählen Sie die Option Zeilen oder Spalten aus der Liste aus, geben Sie die Anzahl der Zeilen / Spalten an, die Sie hinzufügen möchten, wählen Sie aus, wo sie hinzugefügt werden sollen: Über dem Cursor oder Unter dem Cursor und klicken Sie auf OK. Löschen wird verwendet, um eine Zeile, Spalte, Tabelle oder Zellen zu löschen. Wenn Sie die Option Zellen auswählen, wird das Fenster Zellen löschen geöffnet, in dem Sie auswählen können, ob Sie Zellen nach links verschieben, die gesamte Zeile löschen oder die gesamte Spalte löschen möchten. Zellen zusammenführen ist verfügbar, wenn zwei oder mehr Zellen ausgewählt sind, und wird zum Zusammenführen verwendet. Es ist auch möglich, Zellen zusammenzuführen, indem mit dem Radiergummi eine Grenze zwischen ihnen gelöscht wird. Klicken Sie dazu in der oberen Symbolleiste auf das Tabellensymbol und wählen Sie die Option Tabelle löschen. Der Mauszeiger wird zum Radiergummi . Bewegen Sie den Mauszeiger über den Rand zwischen den Zellen, die Sie zusammenführen und löschen möchten. Zelle teilen... wird verwendet, um ein Fenster zu öffnen, in dem Sie die erforderliche Anzahl von Spalten und Zeilen auswählen können, in die die Zelle aufgeteilt werden soll. Sie können eine Zelle auch teilen, indem Sie mit dem Bleistiftwerkzeug Zeilen oder Spalten zeichnen. Klicken Sie dazu in der oberen Symbolleiste auf das Tabellensymbol und wählen Sie die Option Tabelle zeichnen. Der Mauszeiger verwandelt sich in einen Bleistift . Zeichnen Sie eine horizontale Linie, um eine Zeile zu erstellen, oder eine vertikale Linie, um eine Spalte zu erstellen. Mit Zeilen verteilen werden die ausgewählten Zellen so angepasst, dass sie dieselbe Höhe haben, ohne die Gesamthöhe der Tabelle zu ändern.. Mit Spalten verteilen werden die ausgewählten Zellen so angepasst, dass sie dieselbe Breite haben, ohne die Gesamtbreite der Tabelle zu ändern. Die vertikale Ausrichtung der Zelle wird verwendet, um den Text oben, in der Mitte oder unten auszurichten in der ausgewählten Zelle. Textrichtung - wird verwendet, um die Textausrichtung in einer Zelle zu ändern. Sie können den Text horizontal, vertikal von oben nach unten (Text nach unten drehen) oder vertikal von unten nach oben (Text nach oben drehen) platzieren. Mit Tabelle - Erweiterte Einstellungen wird das Fenster Tabelle - Erweiterte Einstellungen\" geöffnet. Hyperlink wird verwendet, um einen Hyperlink einzufügen. Mit den erweiterten Absatzeinstellungen wird das Fenster \"Absatz - Erweiterte Einstellungen\" geöffnet. Sie können auch die Tabelleneigenschaften in der rechten Seitenleiste ändern: Zeilen und Spalten werden verwendet, um die Tabellenteile auszuwählen, die hervorgehoben werden sollen. Für Zeilen: Kopfzeile - um die erste Zeile hervorzuheben Insgesamt - um die letzte Zeile hervorzuheben Gestreift - um jede zweite Zeile hervorzuheben Für Spalten: Erste - um die erste Spalte hervorzuheben Letzte - um die letzte Spalte hervorzuheben Gestreift - um jede andere Spalte hervorzuheben Aus Vorlage auswählen wird verwendet, um eine Tabellenvorlage aus den verfügbaren auszuwählen. Rahmenstil wird verwendet, um die Rahmengröße, Farbe, den Stil sowie die Hintergrundfarbe auszuwählen. Zeilen &amp; Spalten werden verwendet, um einige Operationen mit der Tabelle auszuführen: Auswählen, Löschen, Einfügen von Zeilen und Spalten, Zusammenführen von Zellen, Teilen einer Zelle. Zeilen- und Spaltengröße wird verwendet, um die Breite und Höhe der aktuell ausgewählten Zelle anzupassen. In diesem Abschnitt können Sie auch Zeilen so verteilen, dass alle ausgewählten Zellen die gleiche Höhe haben, oder Spalten so verteilen, dass alle ausgewählten Zellen die gleiche Breite haben. Formel hinzufügen wird verwendet, um eine Formel in die ausgewählte Tabellenzelle einzufügen. Gleiche Kopfzeile auf jeder Seite wiederholen wird verwendet, um dieselbe Kopfzeile oben auf jeder Seite in lange Tabellen einzufügen. Die Option Tabelle in Text umwandeln wird verwendet, um die Tabelle in einer einfachen Textform anzuordnen. Das Fenster Tabelle in Text umwandeln legt den Trennzeichentyp für das Umwandeln fest: Absatzmarken, Tabulatoren, Semikolons und Sonstiges (geben Sie das bevorzugte Trennzeichen manuell ein). Der Text in jeder Zelle der Tabelle wird als separates und individuelles Element des zukünftigen Textes betrachtet. Erweiterte Einstellungen anzeigen wird verwendet, um das Fenster \"Tabelle - Erweiterte Einstellungen\" zu öffnen. Die erweiterten Tabelleneinstellungen anpassen Um die Eigenschaften der erweiterten Tabelle zu ändern, klicken Sie mit der rechten Maustaste auf die Tabelle und wählen Sie im Kontextmenü die Option Erweiterte Tabelleneinstellungen aus, oder verwenden Sie den Link Erweiterte Einstellungen anzeigen in der rechten Seitenleiste. Das Fenster mit den Tabelleneigenschaften wird geöffnet: Auf der Registerkarte Tabelle können Sie die Eigenschaften der gesamten Tabelle ändern. Der Abschnitt Tabellengröße enthält die folgenden Parameter: Breite: Standardmäßig wird die Tabellenbreite automatisch an die Seitenbreite angepasst, d. H. Die Tabelle nimmt den gesamten Raum zwischen dem linken und rechten Seitenrand ein. Sie können dieses Kontrollkästchen aktivieren und die erforderliche Tabellenbreite manuell angeben. Messen in: Ermöglicht die Angabe, ob Sie die Tabellenbreite in absoluten Einheiten festlegen möchten, d. H. Zentimeter / Punkte / Zoll (abhängig von der auf der Registerkarte Datei -&gt; Erweiterte Einstellungen...angegebenen Option) oder in Prozent der Gesamtseitenbreite. Sie können die Tabellengröße auch manuell anpassen, indem Sie die Zeilenhöhe und Spaltenbreite ändern. Bewegen Sie den Mauszeiger über einen Zeilen- / Spaltenrand, bis er sich in einen bidirektionalen Pfeil verwandelt, und ziehen Sie den Rand. Sie können auch die Markierungen auf dem horizontalen Lineal verwenden, um die Spaltenbreite zu ändern, und die Markierungen auf dem vertikalen Lineal, um die Zeilenhöhe zu ändern. Automatische Größenanpassung an den Inhalt: Ermöglicht die automatische Änderung jeder Spaltenbreite entsprechend dem Text in den Zellen. Im Abschnitt Standardzellenränder können Sie den Abstand zwischen dem Text innerhalb der Zellen und dem standardmäßig verwendeten Zellenrand ändern. Im Abschnitt Optionen können Sie den folgenden Parameter ändern: Abstand zwischen Zellen - Der Zellenabstand, der mit der Farbe des Tabellenhintergrunds gefüllt wird. Auf der Registerkarte Zelle können Sie die Eigenschaften einzelner Zellen ändern. Zuerst müssen Sie die Zelle auswählen, auf die Sie die Änderungen anwenden möchten, oder die gesamte Tabelle auswählen, um die Eigenschaften aller Zellen zu ändern. Der Abschnitt Zellengröße enthält die folgenden Parameter: Bevorzugte Breite - Ermöglicht das Festlegen einer bevorzugten Zellenbreite. Dies ist die Größe, an die eine Zelle angepasst werden soll. In einigen Fällen ist es jedoch möglicherweise nicht möglich, genau an diesen Wert anzupassen. Wenn der Text in einer Zelle beispielsweise die angegebene Breite überschreitet, wird er in die nächste Zeile aufgeteilt, sodass die bevorzugte Zellenbreite unverändert bleibt. Wenn Sie jedoch eine neue Spalte einfügen, wird die bevorzugte Breite verringert. Messen in - ermöglicht die Angabe, ob Sie die Zellenbreite in absoluten Einheiten festlegen möchten, d. H. Zentimeter / Punkte / Zoll (abhängig von der auf der Registerkarte Datei -&gt; Erweiterte Einstellungen... angegebenen Option) oder in Prozent der gesamten Tabellenbreite. Sie können die Zellenbreite auch manuell anpassen. Um eine einzelne Zelle in einer Spalte breiter oder schmaler als die gesamte Spaltenbreite zu machen, wählen Sie die gewünschte Zelle aus und bewegen Sie den Mauszeiger über den rechten Rand, bis sie sich in den bidirektionalen Pfeil verwandelt. Ziehen Sie dann den Rand. Um die Breite aller Zellen in einer Spalte zu ändern, verwenden Sie die Markierungen auf dem horizontalen Lineal, um die Spaltenbreite zu ändern. Im Abschnitt Zellenränder können Sie den Abstand zwischen dem Text innerhalb der Zellen und dem Zellenrand anpassen. Standardmäßig werden Standardwerte verwendet (die Standardwerte können auch auf der Registerkarte Tabelle geändert werden). Sie können jedoch das Kontrollkästchen Standardränder verwenden deaktivieren und die erforderlichen Werte manuell eingeben. Im Abschnitt Zellenoptionen können Sie den folgenden Parameter ändern: Die Option Text umbrechen ist standardmäßig aktiviert. Es erlaubts, um Text in eine Zelle, die ihre Breite überschreitet, in die nächste Zeile zu umbrechen, um die Zeilenhöhe zu erweitern und die Spaltenbreite unverändert zu lassen. Die Registerkarte Rahmen &amp; Hintergrund enthält die folgenden Parameter: Rahmenparameter (Größe, Farbe und Vorhandensein oder Nichtvorhandensein): Legen Sie die Rahmengröße fest, wählen Sie die Farbe aus und legen Sie fest, wie sie in den Zellen angezeigt werden soll. Wenn Sie festlegen, dass Tabellenränder nicht angezeigt werden, indem Sie auf die Schaltfläche klicken oder alle Ränder manuell im Diagramm deaktivieren, werden sie im Dokument durch eine gepunktete Linie angezeigt. Um sie überhaupt verschwinden zu lassen, klicken Sie auf der Registerkarte Startseite der oberen Symbolleiste auf das Symbol Nicht druckbare Zeichen und wählen Sie die Option Versteckte Tabellenränder. Zellenhintergrund: Die Farbe für den Hintergrund innerhalb der Zellen (nur verfügbar, wenn eine oder mehrere Zellen ausgewählt sind oder die Option Abstand zwischen Zellen zulassen auf der Registerkarte Tabelle ausgewählt ist). Tabellenhintergrund: Die Farbe für den Tabellenhintergrund oder den Abstand zwischen den Zellen, falls auf der Registerkarte Tabelle die Option Abstand zwischen Zellen zulassen ausgewählt ist. Die Registerkarte Tabellenposition ist nur verfügbar, wenn die Option Flusstabelle auf der Registerkarte Textumbruch ausgewählt ist und die folgenden Parameter enthält: Zu den horizontalen Parametern gehören die Tabellenausrichtung (links, Mitte, rechts) relativ zu Rand, Seite oder Text sowie die Tabellenposition rechts von Rand, Seite oder Text. Zu den vertikalen Parametern gehören die Tabellenausrichtung (oben, Mitte, unten) relativ zu Rand, Seite oder Text sowie die Tabellenposition unter Rand, Seite oder Text. Im Abschnitt Optionen können Sie die folgenden Parameter ändern: Objekt mit Text verschieben steuert, ob die Tabelle verschoben wird, während sich der Text, in den sie eingefügt wird, bewegt. Überlappungssteuerung zulassen steuert, ob zwei Tabellen zu einer großen Tabelle zusammengeführt werden oder überlappen, wenn Sie sie auf der Seite nebeneinander ziehen. Die Registerkarte Textumbruch enthält die folgenden Parameter: Textumbruchstil - Inline-Tabelle oder Flow-Tabelle. Verwenden Sie die erforderliche Option, um die Position der Tabelle relativ zum Text zu ändern: Sie ist entweder Teil des Textes (falls Sie die Inline-Tabelle auswählen) oder wird von allen Seiten umgangen (wenn Sie die Flusstabelle auswählen). Nachdem Sie den Umbruchstil ausgewählt haben, können die zusätzlichen Umbruchparameter sowohl für Inline- als auch für Flusstabellen festgelegt werden: Für die Inline-Tabelle können Sie die Tabellenausrichtung und den Einzug von links angeben. Für die Flusstabelle können Sie den Abstand von Text und Tabellenposition auf der Registerkarte Tabellenposition angeben. Auf der Registerkarte Alternativer Text können Sie einen Titel und eine Beschreibung angeben, die Personen mit Seh- oder kognitiven Beeinträchtigungen vorgelesen werden, damit sie besser verstehen, welche Informationen in der Tabelle enthalten sind."
    },
   {
        "id": "UsageInstructions/InsertTextObjects.htm", 
        "title": "Textobjekte einfügen", 
        "body": "Um Ihren Text lesbarer zu gestalten und die Aufmerksamkeit auf einen bestimmten Teil des Dokuments zu lenken, können Sie ein Textfeld (rechteckigen Rahmen, in den ein Text eingegeben werden kann) oder ein TextArt-Textfeld (Textfeld mit einer vordefinierten Schriftart und Farbe, das die Anwendung von Texteffekten ermöglicht) einfügen im Dokumenteneditor. Textobjekt einfügen Sie können überall auf der Seite ein Textobjekt einfügen. Textobjekt einfügen: Wechseln Sie in der oberen Symbolleiste auf die Registerkarte Einfügen. Wählen Sie das gewünschten Textobjekt aus: Um ein Textfeld hinzuzufügen, klicken Sie in der oberen Symbolleiste auf das Symbol Textfeld und dann auf die Stelle, an der Sie das Textfeld einfügen möchten. Halten Sie die Maustaste gedrückt, und ziehen Sie den Rahmen des Textfelds in die gewünschte Größe. Wenn Sie die Maustaste loslassen, erscheint die Einfügemarke im hinzugefügten Textfeld und Sie können Ihren Text eingeben. Alternativ können Sie ein Textfeld einfügen, indem Sie in der oberen Symbolleiste auf Form klicken und das Symbol aus der Gruppe Standardformen auswählen. Um ein TextArt-Objekt einzufügen, klicken Sie auf das Symbol TextArt in der oberen Symbolleiste und klicken Sie dann auf die gewünschte Stilvorlage - das TextArt-Objekt wird an der aktuellen Cursorposition eingefügt. Markieren Sie den Standardtext innerhalb des Textfelds mit der Maus und ersetzen Sie diesen durch Ihren eigenen Text. Klicken Sie in einen Bereich außerhalb des Text-Objekts, um die Änderungen anzuwenden und zum Dokument zurückzukehren. Der Text innerhalb des Textfelds ist Bestandteil der AutoForm (wenn Sie die AutoForm verschieben oder drehen, wird der Text mit ihr verschoben oder gedreht). Da ein eingefügtes Textobjekt einen rechteckigen Rahmen mit Text darstellt (TextArt-Objekte haben standardmäßig unsichtbare Rahmen) und dieser Rahmen eine allgemeine AutoForm ist, können Sie sowohl die Form als auch die Texteigenschaften ändern. Um das hinzugefügte Textobjekt zu löschen, klicken Sie auf den Rand des Textfelds und drücken Sie die Taste ENTF auf der Tastatur. Dadurch wird auch der Text im Textfeld gelöscht. Textfeld formatieren Wählen Sie das entsprechende Textfeld durch Anklicken der Rahmenlinien aus, um die Eigenschaften zu verändern. Wenn das Textfeld markiert ist, werden alle Rahmenlinien als durchgezogene Linien (nicht gestrichelt) angezeigt. Sie können das Textfeld mithilfe der speziellen Ziehpunkte an den Ecken der Form verschieben, drehen und die Größe ändern. Um das Textfeld zu bearbeiten, mit einer Füllung zu versehen, Rahmenlinien zu ändern, den Textumbruch zu formatieren oder das rechteckige Feld mit einer anderen Form zu ersetzen, klicken Sie in der rechten Seitenleiste auf Formeinstellungen und nutzen Sie die entsprechenden Optionen. Um das Textfeld auf der Seite auszurichten, Textfelder mit andern Objekten zu verknüpfen, ein Textfeld zu rotieren oder umzudrehen, den Umbruchstil zu ändern oder auf Formen - Erweiterte Einstellungen zuzugreifen, klicken Sie mit der rechten Maustaste auf den Feldrand und öffnen Sie so das Kontextmenü. Weitere Informationen zum Ausrichten und Anordnen von Objekten finden Sie auf dieser Seite. Text im Textfeld formatieren Markieren Sie den Text im Textfeld, um die Eigenschaften zu verändern. Wenn der Text markiert ist, werden alle Rahmenlinien als gestrichelte Linien angezeigt. Es ist auch möglich die Textformatierung zu ändern, wenn das Textfeld (nicht der Text selbst) ausgewählt ist. In einem solchen Fall werden alle Änderungen auf den gesamten Text im Textfeld angewandt. Einige Schriftformatierungsoptionen (Schriftart, -größe, -farbe und -stile) können separat auf einen zuvor ausgewählten Teil des Textes angewendet werden. Um den Text innerhalb des Textfeldes zu drehen, klicken Sie mit der rechten Maustaste auf den Text, klicken Sie auf Textausrichtung und wählen Sie eine der verfügbaren Optionen: Horizontal (Standardeinstellung), Text um 180° drehen (vertikale Ausrichtung von oben nach unten) oder Text um 270° drehen (vertikale Ausrichtung von unten nach oben). Um den Text innerhalb des Textfeldes vertikal auszurichten, klicken Sie mit der rechten Maus auf den Text, wählen Sie die Option vertikale Textausrichtung und klicken Sie auf eine der verfügbaren Optionen: Oben ausrichten, Zentrieren oder Unten ausrichten. Die andere Formatierungsoptionen, die Ihnen zur Verfügung stehen sind die gleichen wie für normalen Text. Bitte lesen Sie die entsprechenden Hilfeabschnitte, um mehr über die erforderlichen Vorgänge zu erfahren. Sie können: den Text im Textfeld horizontal ausrichten Schriftart, Größe und Farbe festlegen und DekoSchriften und Formatierungsvorgaben anwenden Zeilenabstände festlegen, Absatzeinzüge ändern und die Tabulatoren für den mehrzeiligen Text innerhalb des Textfelds anpassen einen Hyperlink einfügen Sie können auch in der rechten Seitenleiste auf das Symbol TextArt-Einstellungen klicken und die gewünschten Stilparameter ändern. TextArt-Stil bearbeiten Wählen Sie ein Textobjekt aus und klicken Sie in der rechten Seitenleiste auf das Symbol TextArt-Einstellungen . Ändern Sie den angewandten Textstil, indem Sie eine neue Vorlage aus der Galerie auswählen. Sie können den Grundstil außerdem ändern, indem Sie eine andere Schriftart, -größe usw. auswählen. Füllung der Schriftart ändern. Folgende Optionen stehen Ihnen zur Verfügung: Farbfüllung - wählen Sie die Volltonfarbe für den Innenbereich der Buchstaben aus. Klicken Sie auf das Farbfeld unten und wählen Sie die gewünschte Farbe aus den verfügbaren Farbpaletten aus oder legen Sie eine beliebige Farbe fest: Farbverlauf - wählen Sie diese Option, um die Buchstaben mit zwei Farben zu füllen, die sanft ineinander übergehen. Stil - wählen Sie eine der verfügbaren Optionen: Linear (Farben ändern sich linear, d.h. entlang der horizontalen/vertikalen Achse oder diagonal in einem 45-Grad Winkel) oder Radial (Farben ändern sich kreisförmig vom Zentrum zu den Kanten). Richtung - wählen Sie eine Vorlage aus dem Menü aus. Wenn der Farbverlauf Linear ausgewählt ist, sind die folgenden Richtungen verfügbar: von oben links nach unten rechts, von oben nach unten, von oben rechts nach unten links, von rechts nach links, von unten rechts nach oben links, von unten nach oben, von unten links nach oben rechts, von links nach rechts. Wenn der Farbverlauf Radial ausgewählt ist, steht nur eine Vorlage zur Verfügung. Farbverlauf - klicken Sie auf den linken Schieberegler unter der Farbverlaufsleiste, um das Farbfeld für die erste Farbe zu aktivieren. Klicken Sie auf das Farbfeld auf der rechten Seite, um die erste Farbe in der Farbpalette auszuwählen. Nutzen Sie den rechten Schieberegler unter der Farbverlaufsleiste, um den Wechselpunkt festzulegen, an dem eine Farbe in die andere übergeht. Nutzen Sie den rechten Schieberegler unter der Farbverlaufsleiste, um die zweite Farbe anzugeben und den Wechselpunkt festzulegen. Ist eine dieser beiden Optionen ausgewählt, haben Sie zusätzlich die Wahl, die Transparenz der Füllung festzulegen, ziehen Sie dazu den Schieberegler in die gewünschte Position oder geben Sie den Prozentwert manuell ein. Der Standardwert beträgt 100%. Also volle Deckkraft. Der Wert 0% steht für vollständige Transparenz. Keine Füllung - wählen Sie diese Option, wenn Sie keine Füllung verwenden möchten. Schriftstärke, -farbe und -stil anpassen. Um die Strichstärke zu ändern, wählen Sie eine der verfügbaren Optionen im Listenmenü Größe aus. Die folgenden Optionen stehen Ihnen zur Verfügung: 0,5 Pt., 1 Pt., 1,5 Pt., 2,25 Pt., 3 Pt., 4,5 Pt., 6 Pt. Alternativ können Sie die Option Keine Linie auswählen, wenn Sie keine Umrandung wünschen. Um die Konturfarbe zu ändern, klicken Sie auf das farbige Feld und wählen Sie die gewünschte Farbe aus. Um den Stil der Kontur zu ändern, wählen Sie die gewünschte Option aus der entsprechenden Dropdown-Liste aus (standardmäßig wird eine durchgezogene Linie verwendet, diese können Sie in eine der verfügbaren gestrichelten Linien ändern). Wenden Sie einen Texteffekt an, indem Sie aus der Galerie mit den verfügbaren Vorlagen die gewünschte Formatierung auswählen. Sie können den Grad der Textverzerrung anpassen, indem Sie den rosafarbenen, rautenförmigen Ziehpunkt in die gewünschte Position ziehen."
    },
   {
        "id": "UsageInstructions/Jitsi.htm", 
        "title": "Audio- und Videoanrufe durchführen", 
        "body": "Audio- und Videoanrufe sind sofort über ONLYOFFICE Dokumenteneditor mit Jitsi-Plugin zugänglich. Jitsi bietet Videokonferenzfunktionen, die sicher und einfach bereitzustellen sind. Das Jitsi-Plugin ist standardmäßig nicht installiert und muss manuell hinzugefügt werden. Bitte lesen Sie den entsprechenden Artikel, um die manuelle Installationsanleitung zu finden: Hinzufügen von Plugins zu ONLYOFFICE Cloud oder Hinzufügen neuer Plugins zu Server-Editoren. Wechseln Sie zur Registerkarte Plugins und klicken Sie auf das Symbol Jitsi in der oberen Symbolleiste. Füllen Sie die Felder unten in der linken Seitenleiste aus, bevor Sie einen Anruf starten: Domain: Geben Sie den Domainnamen ein, wenn Sie Ihre Domain verbinden möchten. Room name: Geben Sie den Namen des Besprechungsraums ein. Dieses Feld ist obligatorisch und Sie können keinen Anruf starten, wenn Sie es auslassen. Klicken Sie auf die Schaltfläche Start, um den Jitsi Meet-Iframe zu öffnen. Geben Sie Ihren Namen ein und erlauben Sie Kamera- und Mikrofonzugriff auf Ihren Browser. Wenn Sie den Jitsi Meet Iframe schließen möchten, klicken Sie unten links auf die Schaltfläche Stop. Klicken Sie auf die Schaltfläche Join the meeting, um einen Anruf mit Audio zu starten, oder klicken Sie auf den Pfeil, um ohne Audio beizutreten. Die Elemente der Benutzeroberfläche von Jitsi Meet iframe vor dem Start eines Meetings: Audioeinstellungen und Stumm/Stumm aufheben Klicken Sie auf den Pfeil, um auf die Vorschau der Audio Settings zuzugreifen. Klicken Sie auf das Mikro, um Ihr Mikrofon stumm zu schalten/die Stummschaltung aufzuheben. Videoeinstellungen und Start/Stop Klicken Sie auf den Pfeil, um auf die Videovorschau zuzugreifen. Klicken Sie auf die Kamera, um Ihr Video zu starten/stoppen. Personen einladen Klicken Sie auf diese Schaltfläche, um weitere Personen zu Ihrem Meeting einzuladen. Geben Sie das Meeting frei, indem Sie den Meeting-Link kopieren, oder Geben Sie die Besprechungseinladung durch Kopieren oder über Ihre Standard-E-Mail, Google-E-Mail, Outlook-E-Mail oder Yahoo-E-Mail frei. Betten Sie das Meeting ein, indem Sie den Link kopieren. Verwenden Sie eine der verfügbaren Einwahlnummern, um am Meeting teilzunehmen. Hintergrund auswählen Wählen Sie einen virtuellen Hintergrund für Ihr Meeting aus oder fügen Sie einen hinzu. Geben Sie Ihren Desktop frei, indem Sie die entsprechende Option auswählen: Screen, Window oder Tab. Einstellungen Konfigurieren Sie erweiterte Einstellungen, die in den folgenden Kategorien organisiert sind: Devices zum Einrichten Ihres Mikrofons, Ihrer Kamera und Ihres Audioausgangs und zum Abspielen eines Testtons. Profile zum Einrichten Ihres anzuzeigenden Namens und Gravatar-E-Mail, Verstecken/Anzeigen der Selbstansicht. Calendar zur Integration Ihres Google- oder Microsoft-Kalenders. Sounds, um die Aktionen auszuwählen, bei denen der Sound abgespielt werden soll. More zum Konfigurieren einiger zusätzlicher Optionen: Aktivieren/Deaktivieren des Bildschirms vor dem Meeting und der Tastenkombinationen, Einrichten einer Sprache und Bildrate für die Desktopfreigabe. Elemente der Benutzeroberfläche, die während einer Videokonferenz angezeigt werden: Klicken Sie rechts auf den Seitenpfeil, um die Miniaturansichten der Teilnehmer oben anzuzeigen. Der Timer oben im Iframe zeigt die Meetingsdauer an. Chat öffnen Geben Sie eine Textnachricht ein oder erstellen Sie eine Umfrage. Teilnehmer Zeigen Sie die Liste der Meetingsteilnehmer an, laden Sie weitere Teilnehmer ein und suchen Sie einen Teilnehmer. Mehr Aktionen Finden Sie eine Reihe von Optionen, um alle verfügbaren Jitsi-Funktionen vollständig zu nutzen. Scrollen Sie durch die Optionen, um sie alle anzuzeigen. Verfügbare Optionen sind: Start screen sharing | Starten Sie die Bildschirmfreigabe Invite people | Personen einladen Enter/Exit tile view | Kachelansicht öffnen/verlassen Performance settings for adjusting the quality | Leistungseinstellungen zum Anpassen der Qualität View full screen | Vollbild anzeigen Security options | Sicherheitsoptionen Lobby mode, damit Teilnehmer dem Meeting nach Zustimmung des Moderators beitreten können; Add password mode, damit Teilnehmer dem Meeting mit einem Passwort beitreten können; End-to-end encryption ist eine experimentelle Methode zum Tätigen sicherer Anrufe (beachten Sie die Einschränkungen wie das Deaktivieren serverseitig bereitgestellter Dienste und die Verwendung von Browsern, die einfügbare Streams unterstützen). Start live stream | Livestream starten Mute everyone | Alle stummschalten Disable everyone’s camera | Die Kamera aller Teilnehmer deaktivieren Share video | Video freigeben Select background | Hintergrund auswählen Speaker stats | Sprecherstatistik Settings | Einstellungen View shortcuts | Verknüpfungen anzeigen Embed meeting | Meeting einbetten Leave feedback | Feedback hinterlassen Help | Hilfe Leave the meeting | Meeting verlassen Klicken Sie darauf, wenn Sie einen Anruf beenden möchten."
    },
   {
        "id": "UsageInstructions/LineSpacing.htm", 
        "title": "Zeilenabstand in Absätzen festlegen", 
        "body": "Im Dokumenteneditor können Sie die Zeilenhöhe für die Textzeilen innerhalb des Absatzes sowie die Ränder zwischen dem aktuellen und dem vorhergehenden oder dem nachfolgenden Absatz festlegen. Dazu: Setzen Sie den Zeiger in den gewünschten Absatz oder wählen Sie mehrere Absätze mit der Maus oder selektieren Sie den gesamten Text im Dokument aus, indem Sie die Tastenkombination STRG + A drücken. Verwenden Sie die entsprechenden Felder in der rechten Seitenleiste, um die gewünschten Ergebnisse zu erzielen: Zeilenabstand: Legen Sie die Zeilenhöhe für die Textzeilen innerhalb des Absatzes fest. Sie können zwischen drei Optionen wählen: mindestens (legt den minimalen Zeilenabstand fest, der für die größte Schriftart oder Grafik auf der Zeile erforderlich ist), mehrfach (legt den Zeilenabstand fest, der in Zahlen größer als 1 ausgedrückt werden kann), genau (setzt fest Zeilenabstand). Den erforderlichen Wert können Sie im Feld rechts angeben. Absatzabstand: Legen Sie den Abstand zwischen den Absätzen fest. Vor: Legen Sie den Platz vor dem Absatz fest. Nach: Legen Sie den Platz nach dem Absatz. Kein Abstand zwischen Absätzen gleicher Formatierung: Aktivieren Sie dieses Kontrollkästchen, falls Sie keinen Abstand zwischen Absätzen desselben Stils benötigen. Diese Parameter finden Sie auch im Fenster Absatz - Erweiterte Einstellungen. Um das Fenster Absatz - Erweiterte Einstellungen zu öffnen, klicken Sie mit der rechten Maustaste auf den Text und wählen Sie im Menü die Option Erweiterte Absatzeinstellungen oder verwenden Sie die Option Erweiterte Einstellungen anzeigen in der rechten Seitenleiste. Wechseln Sie dann zur Registerkarte Einzüge und Abstände und gehen Sie zum Abschnitt Abstand. Um den aktuellen Absatzzeilenabstand schnell zu ändern, können Sie auch das Symbol für den Absatzzeilenabstand verwenden. Wählen Sie auf der Registerkarte Startseite in der oberen Symbolleiste das Symbol für den Absatzwert aus der Liste aus: 1,0; 1,15; 1,5; 2,0; 2,5; oder 3,0."
    },
   {
        "id": "UsageInstructions/MathAutoCorrect.htm", 
        "title": "AutoKorrekturfunktionen", 
        "body": "Die Autokorrekturfunktionen in ONLYOFFICE Dokumenteneditor werden verwendet, um Text automatisch zu formatieren, wenn sie erkannt werden, oder um spezielle mathematische Symbole einzufügen, indem bestimmte Zeichen verwendet werden. Die verfügbaren AutoKorrekturoptionen werden im entsprechenden Dialogfeld aufgelistet. Um darauf zuzugreifen, öffnen Sie die Registerkarte Datei -> Erweiterte Einstellungen -> Rechtschreibprüfung -> Optionen von AutoKorrektur. Das Dialogfeld Autokorrektur besteht aus vier Registerkarten: Mathematische Autokorrektur, Erkannte Funktionen, AutoFormat während der Eingabe und Autokorrektur für Text. Math. AutoKorrektur Sie können manuell die Symbole, Akzente und mathematische Symbole für die Gleichungen mit der Tastatur statt der Galerie eingeben. Positionieren Sie die Einfügemarke am Platzhalter im Formel-Editor, geben Sie den mathematischen AutoKorrektur-Code ein, drücken Sie die Leertaste. Für die Codes muss die Groß-/Kleinschreibung beachtet werden. Sie können Autokorrektur-Einträge zur Autokorrektur-Liste hinzufügen, ändern, wiederherstellen und entfernen. Wechseln Sie zur Registerkarte Datei -> Erweiterte Einstellungen -> Rechtschreibprüfung -> Optionen von AutoKorrektur -> Mathematische Autokorrektur. Einträge zur Autokorrekturliste hinzufügen Geben Sie den Autokorrekturcode, den Sie verwenden möchten, in das Feld Ersetzen ein. Geben Sie das Symbol ein, das dem früher eingegebenen Code zugewiesen werden soll, in das Feld Nach ein. Klicken Sie auf die Schaltfläche Hinzufügen. Einträge in der Autokorrekturliste bearbeiten Wählen Sie den Eintrag, den Sie bearbeiten möchten. Sie können die Informationen in beiden Feldern ändern: den Code im Feld Ersetzen oder das Symbol im Feld Nach. Klicken Sie auf die Schaltfläche Ersetzen. Einträge aus der Autokorrekturliste entfernen Wählen Sie den Eintrag, den Sie entfernen möchten. Klicken Sie auf die Schaltfläche Löschen. Um die zuvor gelöschten Einträge wiederherzustellen, wählen Sie den wiederherzustellenden Eintrag aus der Liste aus und klicken Sie auf die Schaltfläche Wiederherstellen. Verwenden Sie die Schaltfläche Zurücksetzen auf die Standardeinstellungen, um die Standardeinstellungen wiederherzustellen. Alle von Ihnen hinzugefügten Autokorrektur-Einträge werden entfernt und die geänderten werden auf ihre ursprünglichen Werte zurückgesetzt. Deaktivieren Sie das Kontrollkästchen Text bei der Eingabe ersetzen, um Math AutoKorrektur zu deaktivieren und automatische Änderungen und Ersetzungen zu verbieten. Die folgende Tabelle enthält alle derzeit unterstützten Codes, die im Dokumenteneditor verfügbar sind. Die vollständige Liste der unterstützten Codes finden Sie auch auf der Registerkarte Datei -> Erweiterte Einstellungen -> Rechtschreibprüfung -> Optionen von AutoKorrektur -> Mathematische Autokorrektur. Die unterstützte Codes Code Symbol Bereich !! Symbole ... Punkte :: Operatoren := Operatoren /< Vergleichsoperatoren /> Vergleichsoperatoren /= Vergleichsoperatoren \\above Hochgestellte/Tiefgestellte Skripts \\acute Akzente \\aleph Hebräische Buchstaben \\alpha Griechische Buchstaben \\Alpha Griechische Buchstaben \\amalg Binäre Operatoren \\angle Geometrische Notation \\aoint Integrale \\approx Vergleichsoperatoren \\asmash Pfeile \\ast Binäre Operatoren \\asymp Vergleichsoperatoren \\atop Operatoren \\bar Über-/Unterstrich \\Bar Akzente \\because Vergleichsoperatoren \\begin Trennzeichen \\below Above/Below Skripts \\bet Hebräische Buchstaben \\beta Griechische Buchstaben \\Beta Griechische Buchstaben \\beth Hebräische Buchstaben \\bigcap Große Operatoren \\bigcup Große Operatoren \\bigodot Große Operatoren \\bigoplus Große Operatoren \\bigotimes Große Operatoren \\bigsqcup Große Operatoren \\biguplus Große Operatoren \\bigvee Große Operatoren \\bigwedge Große Operatoren \\binomial Gleichungen \\bot Logische Notation \\bowtie Vergleichsoperatoren \\box Symbole \\boxdot Binäre Operatoren \\boxminus Binäre Operatoren \\boxplus Binäre Operatoren \\bra Trennzeichen \\break Symbole \\breve Akzente \\bullet Binäre Operatoren \\cap Binäre Operatoren \\cbrt Wurzeln \\cases Symbole \\cdot Binäre Operatoren \\cdots Punkte \\check Akzente \\chi Griechische Buchstaben \\Chi Griechische Buchstaben \\circ Binäre Operatoren \\close Trennzeichen \\clubsuit Symbole \\coint Integrale \\cong Vergleichsoperatoren \\coprod Mathematische Operatoren \\cup Binäre Operatoren \\dalet Hebräische Buchstaben \\daleth Hebräische Buchstaben \\dashv Vergleichsoperatoren \\dd Buchstaben mit Doppelstrich \\Dd Buchstaben mit Doppelstrich \\ddddot Akzente \\dddot Akzente \\ddot Akzente \\ddots Punkte \\defeq Vergleichsoperatoren \\degc Symbole \\degf Symbole \\degree Symbole \\delta Griechische Buchstaben \\Delta Griechische Buchstaben \\Deltaeq Operatoren \\diamond Binäre Operatoren \\diamondsuit Symbole \\div Binäre Operatoren \\dot Akzente \\doteq Vergleichsoperatoren \\dots Punkte \\doublea Buchstaben mit Doppelstrich \\doubleA Buchstaben mit Doppelstrich \\doubleb Buchstaben mit Doppelstrich \\doubleB Buchstaben mit Doppelstrich \\doublec Buchstaben mit Doppelstrich \\doubleC Buchstaben mit Doppelstrich \\doubled Buchstaben mit Doppelstrich \\doubleD Buchstaben mit Doppelstrich \\doublee Buchstaben mit Doppelstrich \\doubleE Buchstaben mit Doppelstrich \\doublef Buchstaben mit Doppelstrich \\doubleF Buchstaben mit Doppelstrich \\doubleg Buchstaben mit Doppelstrich \\doubleG Buchstaben mit Doppelstrich \\doubleh Buchstaben mit Doppelstrich \\doubleH Buchstaben mit Doppelstrich \\doublei Buchstaben mit Doppelstrich \\doubleI Buchstaben mit Doppelstrich \\doublej Buchstaben mit Doppelstrich \\doubleJ Buchstaben mit Doppelstrich \\doublek Buchstaben mit Doppelstrich \\doubleK Buchstaben mit Doppelstrich \\doublel Buchstaben mit Doppelstrich \\doubleL Buchstaben mit Doppelstrich \\doublem Buchstaben mit Doppelstrich \\doubleM Buchstaben mit Doppelstrich \\doublen Buchstaben mit Doppelstrich \\doubleN Buchstaben mit Doppelstrich \\doubleo Buchstaben mit Doppelstrich \\doubleO Buchstaben mit Doppelstrich \\doublep Buchstaben mit Doppelstrich \\doubleP Buchstaben mit Doppelstrich \\doubleq Buchstaben mit Doppelstrich \\doubleQ Buchstaben mit Doppelstrich \\doubler Buchstaben mit Doppelstrich \\doubleR Buchstaben mit Doppelstrich \\doubles Buchstaben mit Doppelstrich \\doubleS Buchstaben mit Doppelstrich \\doublet Buchstaben mit Doppelstrich \\doubleT Buchstaben mit Doppelstrich \\doubleu Buchstaben mit Doppelstrich \\doubleU Buchstaben mit Doppelstrich \\doublev Buchstaben mit Doppelstrich \\doubleV Buchstaben mit Doppelstrich \\doublew Buchstaben mit Doppelstrich \\doubleW Buchstaben mit Doppelstrich \\doublex Buchstaben mit Doppelstrich \\doubleX Buchstaben mit Doppelstrich \\doubley Buchstaben mit Doppelstrich \\doubleY Buchstaben mit Doppelstrich \\doublez Buchstaben mit Doppelstrich \\doubleZ Buchstaben mit Doppelstrich \\downarrow Pfeile \\Downarrow Pfeile \\dsmash Pfeile \\ee Buchstaben mit Doppelstrich \\ell Symbole \\emptyset Notationen von Mengen \\emsp Leerzeichen \\end Trennzeichen \\ensp Leerzeichen \\epsilon Griechische Buchstaben \\Epsilon Griechische Buchstaben \\eqarray Symbole \\equiv Vergleichsoperatoren \\eta Griechische Buchstaben \\Eta Griechische Buchstaben \\exists Logische Notationen \\forall Logische Notationen \\fraktura Fraktur \\frakturA Fraktur \\frakturb Fraktur \\frakturB Fraktur \\frakturc Fraktur \\frakturC Fraktur \\frakturd Fraktur \\frakturD Fraktur \\frakture Fraktur \\frakturE Fraktur \\frakturf Fraktur \\frakturF Fraktur \\frakturg Fraktur \\frakturG Fraktur \\frakturh Fraktur \\frakturH Fraktur \\frakturi Fraktur \\frakturI Fraktur \\frakturk Fraktur \\frakturK Fraktur \\frakturl Fraktur \\frakturL Fraktur \\frakturm Fraktur \\frakturM Fraktur \\frakturn Fraktur \\frakturN Fraktur \\frakturo Fraktur \\frakturO Fraktur \\frakturp Fraktur \\frakturP Fraktur \\frakturq Fraktur \\frakturQ Fraktur \\frakturr Fraktur \\frakturR Fraktur \\frakturs Fraktur \\frakturS Fraktur \\frakturt Fraktur \\frakturT Fraktur \\frakturu Fraktur \\frakturU Fraktur \\frakturv Fraktur \\frakturV Fraktur \\frakturw Fraktur \\frakturW Fraktur \\frakturx Fraktur \\frakturX Fraktur \\fraktury Fraktur \\frakturY Fraktur \\frakturz Fraktur \\frakturZ Fraktur \\frown Vergleichsoperatoren \\funcapply Binäre Operatoren \\G Griechische Buchstaben \\gamma Griechische Buchstaben \\Gamma Griechische Buchstaben \\ge Vergleichsoperatoren \\geq Vergleichsoperatoren \\gets Pfeile \\gg Vergleichsoperatoren \\gimel Hebräische Buchstaben \\grave Akzente \\hairsp Leerzeichen \\hat Akzente \\hbar Symbole \\heartsuit Symbole \\hookleftarrow Pfeile \\hookrightarrow Pfeile \\hphantom Pfeile \\hsmash Pfeile \\hvec Akzente \\identitymatrix Matrizen \\ii Buchstaben mit Doppelstrich \\iiint Integrale \\iint Integrale \\iiiint Integrale \\Im Symbole \\imath Symbole \\in Vergleichsoperatoren \\inc Symbole \\infty Symbole \\int Integrale \\integral Integrale \\iota Griechische Buchstaben \\Iota Griechische Buchstaben \\itimes Mathematische Operatoren \\j Symbole \\jj Buchstaben mit Doppelstrich \\jmath Symbole \\kappa Griechische Buchstaben \\Kappa Griechische Buchstaben \\ket Trennzeichen \\lambda Griechische Buchstaben \\Lambda Griechische Buchstaben \\langle Trennzeichen \\lbbrack Trennzeichen \\lbrace Trennzeichen \\lbrack Trennzeichen \\lceil Trennzeichen \\ldiv Bruchteile \\ldivide Bruchteile \\ldots Punkte \\le Vergleichsoperatoren \\left Trennzeichen \\leftarrow Pfeile \\Leftarrow Pfeile \\leftharpoondown Pfeile \\leftharpoonup Pfeile \\leftrightarrow Pfeile \\Leftrightarrow Pfeile \\leq Vergleichsoperatoren \\lfloor Trennzeichen \\lhvec Akzente \\limit Grenzwerte \\ll Vergleichsoperatoren \\lmoust Trennzeichen \\Longleftarrow Pfeile \\Longleftrightarrow Pfeile \\Longrightarrow Pfeile \\lrhar Pfeile \\lvec Akzente \\mapsto Pfeile \\matrix Matrizen \\medsp Leerzeichen \\mid Vergleichsoperatoren \\middle Symbole \\models Vergleichsoperatoren \\mp Binäre Operatoren \\mu Griechische Buchstaben \\Mu Griechische Buchstaben \\nabla Symbole \\naryand Operatoren \\nbsp Leerzeichen \\ne Vergleichsoperatoren \\nearrow Pfeile \\neq Vergleichsoperatoren \\ni Vergleichsoperatoren \\norm Trennzeichen \\notcontain Vergleichsoperatoren \\notelement Vergleichsoperatoren \\notin Vergleichsoperatoren \\nu Griechische Buchstaben \\Nu Griechische Buchstaben \\nwarrow Pfeile \\o Griechische Buchstaben \\O Griechische Buchstaben \\odot Binäre Operatoren \\of Operatoren \\oiiint Integrale \\oiint Integrale \\oint Integrale \\omega Griechische Buchstaben \\Omega Griechische Buchstaben \\ominus Binäre Operatoren \\open Trennzeichen \\oplus Binäre Operatoren \\otimes Binäre Operatoren \\over Trennzeichen \\overbar Akzente \\overbrace Akzente \\overbracket Akzente \\overline Akzente \\overparen Akzente \\overshell Akzente \\parallel Geometrische Notation \\partial Symbole \\pmatrix Matrizen \\perp Geometrische Notation \\phantom Symbole \\phi Griechische Buchstaben \\Phi Griechische Buchstaben \\pi Griechische Buchstaben \\Pi Griechische Buchstaben \\pm Binäre Operatoren \\pppprime Prime-Zeichen \\ppprime Prime-Zeichen \\pprime Prime-Zeichen \\prec Vergleichsoperatoren \\preceq Vergleichsoperatoren \\prime Prime-Zeichen \\prod Mathematische Operatoren \\propto Vergleichsoperatoren \\psi Griechische Buchstaben \\Psi Griechische Buchstaben \\qdrt Wurzeln \\quadratic Wurzeln \\rangle Trennzeichen \\Rangle Trennzeichen \\ratio Vergleichsoperatoren \\rbrace Trennzeichen \\rbrack Trennzeichen \\Rbrack Trennzeichen \\rceil Trennzeichen \\rddots Punkte \\Re Symbole \\rect Symbole \\rfloor Trennzeichen \\rho Griechische Buchstaben \\Rho Griechische Buchstaben \\rhvec Akzente \\right Trennzeichen \\rightarrow Pfeile \\Rightarrow Pfeile \\rightharpoondown Pfeile \\rightharpoonup Pfeile \\rmoust Trennzeichen \\root Symbole \\scripta Skripts \\scriptA Skripts \\scriptb Skripts \\scriptB Skripts \\scriptc Skripts \\scriptC Skripts \\scriptd Skripts \\scriptD Skripts \\scripte Skripts \\scriptE Skripts \\scriptf Skripts \\scriptF Skripts \\scriptg Skripts \\scriptG Skripts \\scripth Skripts \\scriptH Skripts \\scripti Skripts \\scriptI Skripts \\scriptk Skripts \\scriptK Skripts \\scriptl Skripts \\scriptL Skripts \\scriptm Skripts \\scriptM Skripts \\scriptn Skripts \\scriptN Skripts \\scripto Skripts \\scriptO Skripts \\scriptp Skripts \\scriptP Skripts \\scriptq Skripts \\scriptQ Skripts \\scriptr Skripts \\scriptR Skripts \\scripts Skripts \\scriptS Skripts \\scriptt Skripts \\scriptT Skripts \\scriptu Skripts \\scriptU Skripts \\scriptv Skripts \\scriptV Skripts \\scriptw Skripts \\scriptW Skripts \\scriptx Skripts \\scriptX Skripts \\scripty Skripts \\scriptY Skripts \\scriptz Skripts \\scriptZ Skripts \\sdiv Bruchteile \\sdivide Bruchteile \\searrow Pfeile \\setminus Binäre Operatoren \\sigma Griechische Buchstaben \\Sigma Griechische Buchstaben \\sim Vergleichsoperatoren \\simeq Vergleichsoperatoren \\smash Pfeile \\smile Vergleichsoperatoren \\spadesuit Symbole \\sqcap Binäre Operatoren \\sqcup Binäre Operatoren \\sqrt Wurzeln \\sqsubseteq Notation von Mengen \\sqsuperseteq Notation von Mengen \\star Binäre Operatoren \\subset Notation von Mengen \\subseteq Notation von Mengen \\succ Vergleichsoperatoren \\succeq Vergleichsoperatoren \\sum Mathematische Operatoren \\superset Notation von Mengen \\superseteq Notation von Mengen \\swarrow Pfeile \\tau Griechische Buchstaben \\Tau Griechische Buchstaben \\therefore Vergleichsoperatoren \\theta Griechische Buchstaben \\Theta Griechische Buchstaben \\thicksp Leerzeichen \\thinsp Leerzeichen \\tilde Akzente \\times Binäre Operatoren \\to Pfeile \\top Logische Notationen \\tvec Pfeile \\ubar Akzente \\Ubar Akzente \\underbar Akzente \\underbrace Akzente \\underbracket Akzente \\underline Akzente \\underparen Akzente \\uparrow Pfeile \\Uparrow Pfeile \\updownarrow Pfeile \\Updownarrow Pfeile \\uplus Binäre Operatoren \\upsilon Griechische Buchstaben \\Upsilon Griechische Buchstaben \\varepsilon Griechische Buchstaben \\varphi Griechische Buchstaben \\varpi Griechische Buchstaben \\varrho Griechische Buchstaben \\varsigma Griechische Buchstaben \\vartheta Griechische Buchstaben \\vbar Trennzeichen \\vdash Vergleichsoperatoren \\vdots Punkte \\vec Akzente \\vee Binäre Operatoren \\vert Trennzeichen \\Vert Trennzeichen \\Vmatrix Matrizen \\vphantom Pfeile \\vthicksp Leerzeichen \\wedge Binäre Operatoren \\wp Symbole \\wr Binäre Operatoren \\xi Griechische Buchstaben \\Xi Griechische Buchstaben \\zeta Griechische Buchstaben \\Zeta Griechische Buchstaben \\zwnj Leerzeichen \\zwsp Leerzeichen ~= Vergleichsoperatoren -+ Binäre Operatoren +- Binäre Operatoren << Vergleichsoperatoren <= Vergleichsoperatoren -> Pfeile >= Vergleichsoperatoren >> Vergleichsoperatoren Erkannte Funktionen Auf dieser Registerkarte finden Sie die Liste der mathematischen Ausdrücke, die vom Gleichungseditor als Funktionen erkannt und daher nicht automatisch kursiv dargestellt werden. Die Liste der erkannten Funktionen finden Sie auf der Registerkarte Datei -> Erweiterte Einstellungen -> Rechtschreibprüfung -> Optionen von Autokorrektur -> Erkannte Funktionen. Um der Liste der erkannten Funktionen einen Eintrag hinzuzufügen, geben Sie die Funktion in das leere Feld ein und klicken Sie auf die Schaltfläche Hinzufügen. Um einen Eintrag aus der Liste der erkannten Funktionen zu entfernen, wählen Sie die gewünschte Funktion aus und klicken Sie auf die Schaltfläche Löschen. Um die zuvor gelöschten Einträge wiederherzustellen, wählen Sie den gewünschten Eintrag aus der Liste aus und klicken Sie auf die Schaltfläche Wiederherstellen. Verwenden Sie die Schaltfläche Zurücksetzen auf die Standardeinstellungen, um die Standardeinstellungen wiederherzustellen. Alle von Ihnen hinzugefügten Funktionen werden entfernt und die entfernten Funktionen werden wiederhergestellt. AutoFormat während der Eingabe Standardmäßig formatiert der Editor den Text während der Eingabe gemäß den Voreinstellungen für die automatische Formatierung: Ersetzt Anführungszeichen, konvertiert Bindestriche in Gedankenstriche, ersetzt Internet- oder Netzwerkpfade durch Hyperlinks, startet eine Aufzählungsliste oder eine nummerierte Liste, wenn eine Liste wird erkannt. Mit der Option Punkt mit doppeltem Leerzeichen hinzufügen können Sie einen Punkt hinzufügen, wenn Sie zweimal die Leertaste drücken. Aktivieren oder deaktivieren Sie sie nach Bedarf. Standardmäßig ist diese Option für Linux und Windows deaktiviert und für macOS aktiviert. Um die Voreinstellungen für die automatische Formatierung zu aktivieren oder zu deaktivieren, öffnen Sie die Registerkarte Datei -> Erweiterte Einstellungen -> Rechtschreibprüfung -> Optionen von Autokorrektur -> AutoFormat während der Eingabe. Autokorrektur für Text Sie können den Editor so einstellen, dass das erste Wort jedes Satzes automatisch groß geschrieben wird. Die Option ist standardmäßig aktiviert. Um diese Option zu deaktivieren, gehen Sie zur Registerkarte Datei -> Erweiterte Einstellungen -> Rechtschreibprüfung -> Optionen von Autokorrektur -> Autokorrektur für Text und deaktivieren Sie die Option Jeden Satz mit einem Großbuchstaben beginnen."
    },
   {
        "id": "UsageInstructions/NonprintingCharacters.htm", 
        "title": "Formatierungszeichen ein-/ausblenden", 
        "body": "Die Formatierungszeichen helfen Ihnen bei der Bearbeitung eines Dokuments. Sie zeigen das Vorhandensein verschiedener Formatierungen an, aber sie werden nicht mit dem Dokument gedruckt, auch wenn sie auf dem Bildschirm angezeigt werden. Um Formatierungszeichen ein- bzw. auszublenden im Dokumenteneditor, klicken Sie auf das Symbol Formatierungszeichen in der Registerkarte Startseite. Die Formatierungszeichen: Leerzeichen Wird eingefügt, wenn Sie die Leertaste drücken. Dieses Zeichen zeigt einen Zwischenraum zwischen den Zeichen an. Tabulatoren Wird eingefügt, wenn Sie die Tabulatortaste drücken. Dudurch wird der Cursor zur nächsten Tabulatormarke bewegt. Absatzzeichen (d.h. harter Zeilenumbruch) Wird eingefügt, wenn Sie die Eingabetaste drücken. Dadurch wird der aktuelle Absatz beendet und ein Abstand zum folgenden Absatz eingefügt. Das Zeichen enthält Informationen zur Absatzformatierung. Zeilenumbruch (d.h. weicher Zeilenumbruch) Wird eingefügt, wenn Sie die Tastenkombination UMSCHALT+ENTER verwenden. Dadurch wird die aktuelle Zeile umgebrochen und die folgenden Zeilen werden ohne zusätzlichen Zwischenabstand angefügt. Weiche Zeilenumbrüche werden hauptsächlich in Überschriften und Titeln verwendet. Geschütztes Leerzeichen Wird eingefügt, wenn Sie die Tastenkombination STRG+UMSCHALT+LEERTASTE verwenden. Es erzeugt ein Leerzeichen zwischen Zeichen, die nicht als Beginn einer neuen Zeile verwendet werden können. Seitenumbruch Wird eingefügt, wenn Sie das Symbol Umbrüche in der oberen Symbolleiste auf den Registerkarten Einfügen oder Layout anklicken und die Option Seitenumbruch einfügen auswählen oder im Rechtsklickmenü oder im Fenster mit den erweiterten Einstellungen die Option Seitenumbruch vorher auswählen. Abschnittsumbrüche Wird eingefügt, wenn Sie das Symbol Umbrüche in der oberen Symbolleiste auf den Registerkarten Einfügen oder Layout anklicken und eine der Optionen im Untermenü Abschnittsumbruch einfügen auswählen (die Anzeige für den jeweiligen Umbruch unterscheidet sich je nachdem, welche Option ausgewählt ist): Nächste Seite, Fortlaufend, Gerade Seite, Ungerade Seite). Spaltenumbrüche Wird eingefügt, wenn Sie das Symbol Umbrüche in der oberen Symbolleiste auf den Registerkarten Einfügen oder Layout anklicken und die Option Spaltenumbruch einfügen auswählen. Markierungen für das Zell- und Zeilenende in Tabellen Diese Markierungen enthalten die Formatierungscodes für die jeweilige Zelle bzw. Zeile. Kleines schwarzes Quadrat am Seitenrand links von einem Absatz Zeigt an, dass mindestens eine der Absatzoptionen angewendet wurde, z.B. Zeilen zusammenhalten, Seitenumbruch oberhalb. Anker Zeigt die Position von schwebenden Objekten an (Objekte deren Position nicht auf der Seite Fixiert ist), z.B. von Bildern, AutoFormen, Diagrammen. Um den Anker sichtbar zu machen, wählen Sie einfach ein beliebiges Objekt aus."
    },
   {
        "id": "UsageInstructions/OCR.htm", 
        "title": "Text aus einem Bild extrahieren", 
        "body": "Mit ONLYOFFICE Dokumenteneditor können Sie Text aus einem Bild (.png .jpg) extrahieren und in Ihr Dokument einfügen. Öffnen Sie Ihr Dokument und platzieren Sie den Cursor an der Stelle, an der Sie den Text einfügen möchten. Öffnen Sie die Registerkarte Plugins und wählen Sie den Menüpunkt OCR aus. Klicken Sie auf Datei laden und wählen Sie das Bild aus. Wählen Sie die Erkennungssprache aus dem Drop-Down-Menü Sprache auswählen. Klicken Sie auf Erkennen. Klicken Sie auf Text einfügen. Sie sollten den eingefügten Text auf Fehler und Layout überprüfen."
    },
   {
        "id": "UsageInstructions/OpenCreateNew.htm", 
        "title": "Ein neues Dokument erstellen oder ein vorhandenes öffnen", 
        "body": "Im Dokumenteneditor können Sie eine kürzlich bearbeitete Dokument öffnen, das Dokument umbenennen, ein neues Dokument erstellen oder zur Liste der vorhandenen Dokumente zurückkehren. Ein neues Dokument erstellen: Online-Editor Klicken Sie in der oberen Menüleiste auf die Registerkarte Datei. Wählen Sie die Option Neu erstellen. Desktop-Editor Wählen Sie im Hauptfenster des Programms das Menü Dokument im Abschnitt Neu erstellen der linken Seitenleiste aus - eine neue Datei wird in einer neuen Registerkarte geöffnet. Wenn Sie alle gewünschten Änderungen durchgeführt haben, klicken Sie auf das Symbol Speichern in der oberen linken Ecke oder wechseln Sie in die Registerkarte Datei und wählen Sie das Menü Speichern als aus. Wählen Sie im Fenster Dateiverwaltung den Speicherort, geben Sie den Namen an, wählen Sie das Format aus in dem Sie das Dokument speichern möchten (DOCX, DOCXF, OFORM, Dokumentvorlage (DOTX), ODT, OTT, RTF, TXT, PDF oder PDFA) und klicken Sie auf die Schaltfläche Speichern. Ein vorhandenes Dokument öffnen: Desktop-Editor Wählen Sie in der linken Seitenleiste im Hauptfenster des Programms den Menüpunkt Lokale Datei öffnen. Wählen Sie im Fenster Dateiverwaltung das gewünschte Dokument aus und klicken Sie auf die Schaltfläche Öffnen. Sie können auch im Fenster Dateiverwaltung mit der rechten Maustaste auf das gewünschte Dokument klicken, die Option Öffnen mit auswählen und die gewünschte Anwendung aus dem Menü auswählen. Wenn die Office-Dokumentdateien mit der Anwendung verknüpft sind, können Sie Dokumente auch öffnen, indem Sie im Datei-Explorer-Fenster auf den Dateinamen doppelklicken. Alle Verzeichnisse, auf die Sie mit dem Desktop-Editor zugegriffen haben, werden in der Liste Aktuelle Ordner angezeigt, um Ihnen einen schnellen Zugriff zu ermöglichen. Klicken Sie auf den gewünschten Ordner, um eine der darin gespeicherten Dateien auszuwählen. Ein kürzlich bearbeitetes Dokument öffnen: Online-Editor Klicken Sie in der oberen Menüleiste auf die Registerkarte Datei. Wählen Sie die Option Zuletzt benutztes Dokument öffnen. Wählen Sie das gewünschte Dokument aus der Liste mit den zuletzt bearbeiteten Dokumenten aus. Desktop-Editor Wählen Sie in der linken Seitenleiste im Hauptfenster des Programms den Menüpunkt Aktuelle Dateien. Wählen Sie das gewünschte Dokument aus der Liste mit den zuletzt bearbeiteten Dokumenten aus. Ein geöffnetes Dokument umbenennen: Online-Editor Klicken Sie oben auf der Seite auf den Dokumentnamen. Geben Sie einen neuen Dokumentnamen ein. Drücken Sie die Eingabetaste, um die Änderungen zu akzeptieren. Um den Ordner in dem die Datei gespeichert ist in der Online-Version in einem neuen Browser-Tab oder in der Desktop-Version im Fenster Datei-Explorer zu öffnen, klicken Sie auf der rechten Seite des Editor-Hauptmenüs auf das Symbol Dateispeicherort öffnen. Alternativ können Sie in der oberen Menüleiste auf die Registerkarte Datei wechseln und die Option Dateispeicherort öffnen auswählen."
    },
   {
        "id": "UsageInstructions/PageBreaks.htm", 
        "title": "Seitenumbrüche einfügen", 
        "body": "Im Dokumenteneditor können Sie einen Seitenumbruch hinzufügen, um eine neue Seite zu starten, eine leere Seite einzufügen und die Paginierungsoptionen anzupassen. Um einen Seitenumbruch an der aktuellen Zeigerposition einzufügen, klicken Sie auf das Symbol Unterbrechungen auf der Registerkarte Einfügen oder Layout der oberen Symbolleiste oder klicken Sie auf den Pfeil neben diesem Symbol und wählen Sie im Menu die Option Seitenumbruch einfügen. Sie können auch die Tastenkombination Strg + Eingabetaste verwenden. Um eine leere Seite einzufügen, klicken Sie auf das Symbol Leere Seite auf der Registerkarte Einfügen der oberen Symbolleiste. Dadurch werden zwei Seitenumbrüche eingefügt, wodurch eine leere Seite erstellt wird. Um einen Seitenumbruch vor dem ausgewählten Absatz einzufügen, d. H. um diesen Absatz oben auf einer neuen Seite zu beginnen: Klicken Sie mit der rechten Maustaste und wählen Sie im Menü die Option Seitenumbruch vor oder Klicken Sie mit der rechten Maustaste, wählen Sie im Menü die Option Absatz - Erweiterte Einstellungen oder verwenden Sie den Link Erweiterte Einstellungen anzeigen in der rechten Seitenleiste, und aktivieren Sie das Kontrollkästchen Seitenumbruch davor auf der Registerkarte Zeilen- und Seitenumbrüche des geöffneten Fensters Absatz - Erweiterte Einstellungen. Um die Zeilen so zusammenzuhalten, dass nur ganze Absätze auf die neue Seite verschoben werden (d. H. Es gibt keinen Seitenumbruch zwischen den Zeilen innerhalb eines einzelnen Absatzes), Klicken Sie mit der rechten Maustaste und wählen Sie im Menü die Option Linien zusammenhalten oder Klicken Sie mit der rechten Maustaste, wählen Sie im Menü die Option Erweiterte Absatzeinstellungen aus, oder verwenden Sie den Link Erweiterte Einstellungen anzeigen in der rechten Seitenleiste, und aktivieren Sie das Kontrollkästchen Absatz zusammenhalten unter Zeilen- und Seitenumbrüche im geöffneten Fenster Absatz - Erweiterte Einstellungen. Auf der Registerkarte Zeilen- und Seitenumbrüche des Fensters Absatz - Erweiterte Einstellungen können Sie zwei weitere Paginierungsoptionen festlegen: Mit dem nächsten zusammen - wird verwendet, um einen Seitenumbruch zwischen dem ausgewählten und dem nächsten Absatz zu verhindern. Verwaiste Steuerung - ist standardmäßig ausgewählt und wird verwendet, um zu verhindern, dass eine einzelne Zeile des Absatzes (die erste oder letzte) oben oder unten auf der Seite angezeigt wird."
    },
   {
        "id": "UsageInstructions/ParagraphIndents.htm", 
        "title": "Absatzeinzüge ändern", 
        "body": "Im Dokumenteneditor können Sie den Einzug der ersten Zeile vom linken Seitenrand sowie die Absatzeinzüge von links und rechts einstellen. Ändern der Absatzeinzüge: Stellen Sie die erforderlichen Parameter in der rechten Seitenleiste Absatzeinstellungen im Abschnitt Einzüge ein: Links - setzen Sie den Absatzabstand von der linken Seite mit einem numerischen Wert. Rechts - setzen Sie den Absatzabstand von der rechten Seite mit einem numerischen Wert. Spezial - setzen Sie den Einzug der ersten Linie des Absatzes: Selektieren Sie den entsprechenden Menüpunkt: ((Nichts), Erste Linie, Hängend) und ändern Sie den Standard numerischen Wert entweder für die erste Linie oder Hängend, oder Positionieren Sie den Zeiger innerhalb des gewünschten Absatzes oder wählen Sie mehrere Absätze mit der Maus aus oder markieren Sie den gesamten Text mithilfe der Tastenkombination Strg+A. Klicken Sie mit der rechten Maustaste und wählen Sie im Kontextmenü die Option Absatz - Erweiterte Einstellungen aus oder nutzen Sie den Link Erweiterte Einstellungen anzeigen in der rechten Seitenleiste. Im Fenster Absatz - Erweiterte Einstellungen wechseln Sie zu Einzug & Abstand und setzen Sie die notwendigen Parameter für die Einzug-Sektion: (die Beschreibung der Parameter wird nach oben gegeben). Klicken Sie auf OK. Um den Abstand zum linken Seitenrand zu ändern, können Sie auch die entsprechenden Symbole in der Registerkarte Start auf der oberen Symbolleiste benutzen: Einzug verkleinern und Einzug vergrößern . Sie können auch das horizontale Lineal nutzen, um Einzüge festzulegen.Wählen Sie den gewünschten Absatz (Absätze) und ziehen Sie die Einzugsmarken auf dem Lineal in die gewünschte Position. Mit der Markierung für den Erstzeileneinzug lässt sich der Versatz des Absatzes vom linken Seitenbereich für die erste Zeile eines Absatzes festlegen. Mit der Einzugsmarke für den hängenden Einzug lässt sich der Versatz vom linken Seitenrand für die zweite Zeile sowie alle Folgezeilen eines Absatzes festlegen. Mit der linken Einzugsmarke lässt sich der Versatz des Absatzes vom linken Seitenrand festlegen. Mit der rechten Einzugsmarke lässt sich der Versatz des Absatzes vom rechten Seitenrand festlegen."
    },
   {
        "id": "UsageInstructions/PhotoEditor.htm", 
        "title": "Bild bearbeiten", 
        "body": "ONLYOFFICE Dokumenteneditor hat einen sehr effektiven Fotoeditor, mit dem Sie das Bild mit Filtern anpassen und alle Arten von Anmerkungen einfügen können. Wählen Sie das Bild in Ihrem Dokument aus. Öffnen Sie die Registerkarte Plugins und wählen Sie den Menüpunkt Foto-Editor aus. Sie befinden sich jetzt im Bearbeitungsmodus. Unter dem Bild finden Sie die folgenden Kontrollkästchen und Schieberegler für Filter: Graustufe, Sepia, Sepia 2, Blur, Emboss, Invertieren, Schärfen; Weiß entfernen (Grenzwert, Abstand), Farbtransparenz, Helligkeit, Rauschen, Verpixelt, Farbfilter; Farbton, Multiplizieren, Mix. Links neben den Filtern finden Sie die folgenden Schaltflächen Rückgängig machen, Wiederholen und Zurücksetzen; Löschen, Alles löschen; Menge (Benutzerdefiniert, Quadrat, 3:2, 4:3, 5:4, 7:5, 16:9); Kippen (Kippen X, Kippen Y, Zurücksetzen); Drehen (30 Grad., -30 Grad.,Schieberegler für manuelle Drehung); Zeichnen (Frei, Gerade, Farbe, Schieberegler für Größe); Form (Rechteck, Kreis, Dreieck, Ausfüllen, Strich, Strichgröße); Symbol (Pfeile, Sterne, Polygon, Speicherort, Herz, Blase, Benutzerdefiniertes Symbol, Farbe); Text (Fett, Kursiv, Unterstrichen, Links, Zentriert, Rechts, Farbe, Textgröße); Schablone. Sie können alle diese Einstellungen probieren. Sie können immer Ihre Aktionen rückgängig machen. Wenn Sie bereit sind, klicken Sie auf die Schaltfläche OK. Das bearbeitete Bild ist jetzt im Dokument enthalten."
    },
   {
        "id": "UsageInstructions/SavePrintDownload.htm", 
        "title": "Dokument speichern, runterladen, drucken", 
        "body": "Dokument speichern, herunterladen, drucken Speichern Standardmäßig speichert der Online-Dokumenteneditor Ihre Datei während der Bearbeitung automatisch alle 2 Sekunden, um Datenverluste im Falle eines unerwarteten Progammabsturzes zu verhindern. Wenn Sie die Datei im Schnellmodus co-editieren, fordert der Timer 25 Mal pro Sekunde Aktualisierungen an und speichert vorgenommene Änderungen. Wenn Sie die Datei im Modus Strikt co-editieren, werden Änderungen automatisch alle 10 Minuten gespeichert. Sie können den bevorzugten Co-Modus nach Belieben auswählen oder die Funktion AutoSpeichern auf der Seite Erweiterte Einstellungen deaktivieren. Aktuelles Dokument manuell im aktuellen Format im aktuellen Verzeichnis speichern: Verwenden Sie das Symbol Speichern im linken Bereich der Kopfzeile des Editors oder drücken Sie die Tasten STRG+S oder wechseln Sie in der oberen Menüleiste in die Registerkarte Datei und wählen Sie die Option Speichern. Um Datenverluste durch ein unerwartetes Schließen des Programms zu verhindern, können Sie in der Desktop-Version die Option AutoWiederherstellen auf der Seite Erweiterte Einstellungen aktivieren. In der Desktop-Version können Sie das Dokument unter einem anderen Namen, an einem neuen Speicherort oder in einem anderen Format speichern. Klicken Sie in der oberen Menüleiste auf die Registerkarte Datei. Wählen Sie die Option Speichern als. Wählen Sie das gewünschte Format aus: DOCX, ODT, RTF, TXT, PDF, PDF/A, HTML, FB2, EPUB, DOCXF, OFORM. Sie können die Option Dokumentenvorlage (DOTX oder OTT) auswählen. Herunterladen In der Online-Version können Sie das daraus resultierende Dokument auf der Festplatte Ihres Computers speichern. Klicken Sie in der oberen Menüleiste auf die Registerkarte Datei. Wählen Sie die Option Herunterladen als. Wählen Sie das gewünschte Format aus: DOCX, PDF, ODT, TXT, DOTX, PDF/A, OTT, RTF, HTML, FB2, EPUB, DOCXF, OFORM. Kopie speichern In der Online-Version können Sie die eine Kopie der Datei in Ihrem Portal speichern. Klicken Sie in der oberen Menüleiste auf die Registerkarte Datei. Wählen Sie die Option Kopie speichern als. Wählen Sie das gewünschte Format aus: DOCX, PDF, ODT, TXT, DOTX, PDF/A, OTT, RTF, HTML, FB2, EPUB, DOCXF, OFORM. Wählen Sie den gewünschten Speicherort auf dem Portal aus und klicken Sie Speichern. Drucken Aktuelles Dokument drucken klicken Sie auf das Symbol Drucken im linken Bereich der Kopfzeile des Editors oder nutzen Sie die Tastenkombination STRG+P oder wechseln Sie in der oberen Menüleiste in die Registerkarte Datei und wählen Sie die Option Drucken. Der Firefox-Browser ermöglicht das Drucken, ohne das Dokument zuerst als PDF-Datei herunterzuladen. Es ist auch möglich, eine ausgewählte Textpassage mit der Option Auswahl drucken aus dem Kontextmenü sowohl im Modus Bearbeiten als auch im Modus Anzeigen (Klicken Sie mit der rechten Maustaste und wählen Sie die Option Auswahl drucken). In der Desktop-Version wird die Datei direkt gedruckt. In der Online-Version wird basierend auf dem Dokument eine PDF-Datei erstellt. Diese können Sie öffnen und drucken oder auf der Festplatte des Computers oder einem Wechseldatenträger speichern und später drucken. Einige Browser (z. B. Chrome und Opera) unterstützen Direktdruck."
    },
   {
        "id": "UsageInstructions/SectionBreaks.htm", 
        "title": "Abschnittsumbrüche einfügen", 
        "body": "Im Dokumenteneditor Mithilfe von Abschnittsumbrüchen können Sie ein anderes Layout oder eine andere Formatierung für bestimmten Abschnitte in Ihrem Dokument festlegen. Sie können beispielsweise Kopf- und Fußzeilen, Seitennummerierungen, Fußnotenformate, Ränder, Größe, Ausrichtung oder Spaltennummer für jeden einzelnen Abschnitt individuell festlegen. Ein eingefügter Abschnittswechsel definiert die Formatierung des vorangegangenen Abschnitts. Einfügen eines Abschnittsumbruchs an der aktuellen Cursorposition: Klicken Sie in der oberen Menüleiste auf das Symbol Umbrüche in den Registerkarten Einfügen oder Layout. Wählen Sie das Untermenü Abschnittsumbruch einfügen. Wählen Sie den gewünschten Umbruch: Nächste Seite - um auf der nächsten Seite einen neuen Abschnitt zu beginnen Fortlaufend - um auf der aktuellen Seite einen neuen Abschnitt beginnen Gerade Seite - um auf der nächsten geraden Seite einen neuen Abschnitt zu beginnen Ungerade Seite - um auf der nächsten ungeraden Seite einen neuen Abschnitt zu beginnen Abschnittswechsel werden in Ihrem Dokument durch eine doppelt gepunktete Linie angezeigt: Wenn die eingefügten Abschnittsumbrüche nicht angezeigt werden, klicken Sie in der Registerkarte Startseite auf das Symbol . Um einen Abschnittsumbruch zu entfernen, wählen Sie diesen mit der Maus aus und drücken Sie die Taste ENTF. Da ein Abschnittsumbruch die Formatierung des vorherigen Abschnitts definiert, wird durch das Löschen des Abschnittsumbruch auch die Formatierung des vorangegangenen Abschnitts entfernt. Ein solcher Abschnitt wird dann entsprechend der Formatierung des nachfolgenden Abschnitts formatiert."
    },
   {
        "id": "UsageInstructions/SetOutlineLevel.htm", 
        "title": "Gliederungsebene konfigurieren", 
        "body": "Im Dokumenteneditor eine Gliederungsebene ist die Absatzebene in der Dokumentstruktur. Folgende Ebenen stehen zur Verfügung: Basictext, Ebene 1 - Ebene 9. Die Gliederungsebene kann auf verschiedene Arten festgelegt werden, z. B. mithilfe von Überschriftenstilen: Wenn Sie einem Absatz einen Überschriftenstil (Überschrift 1 - Überschrift 9) zuweisen, erhält er die entsprechende Gliederungsstufe. Wenn Sie einem Absatz mithilfe der erweiterten Absatzeinstellungen eine Ebene zuweisen, erhält der Absatz nur die Strukturebene, während sein Stil unverändert bleibt. Die Gliederungsebene kann auch im Navigationsbereich links über die Kontextmenüoptionen geändert werden. Um die Gliederungsebene zu ändern: Drücken Sie die rechte Maustaste auf dem Text und wählen Sie die Option Absatz - Erweiterte Einstellungen im Kontextmenü aus oder verwenden Sie die Option Erweiterte Einstellungen anzeigen im rechten Navigationsbereich. Öffnen Sie das Feld Absatz - Erweiterte Einstellungen, dann öffnen Sie die Registerkarte Einzüge und Abstände. Wählen Sie die gewünschte Gliederungsebene im Dropdown-Menü Gliederungsebene aus. Klicken Sie OK an."
    },
   {
        "id": "UsageInstructions/SetPageParameters.htm", 
        "title": "Seitenparameter festlegen", 
        "body": "Um das Seitenlayout im Dokumenteneditor zu ändern, d. h. Seitenausrichtung und Seitengröße festzulegen, die Ränder anzupassen und Spalten einzufügen, verwenden Sie die entsprechenden Symbole auf der Registerkarte Layout der oberen Symbolleiste. Alle festgelegten Parameter werden auf das gesamte Dokument angewendet. Wie Sie für einzelnen Teile Ihres Dokuments unterschiedliche Seitenränder, Ausrichtungen, Größen oder Spaltenanzahlen festlegen, lesen Sie bitte auf dieser Seite nach. Seitenausrichtung Die aktuelle Seitenausrichtung ändern Sie mit einem Klick auf das Symbol Seitenausrichtung. Die Standardeinstellung ist Hochformat. Diese kann auf das Querformat umgewechselt werden. Seitengröße Sie können das A4-Standardformat ändern, indem Sie das Symbol Größe anklicken und das gewünschte Format aus der Liste auswählen. Die verfügbaren Formate sind: US Letter (21,59 cm x 27,94 cm) US Legal (21,59 cm x 35,56 cm) A4 (21 cm x 29,7 cm) A5 (14,81cm x 20,99cm) B5 (17,6cm x 25,01cm) Umschlag #10 (10,48 cm x 24,13 cm) Umschlag DL (11,01 cm x 22,01 cm) Tabloid (27,94 cm x 43,17 cm) AЗ (29,7 cm x 42,01 cm) Tabloid Übergröße (30,48 cm x 45,71 cm) ROC 16K (19,68 cm x 27,3 cm) Umschlag Choukei 3 (11,99 cm x 23,49 cm) Super B/A3 (33,02 cm x 48,25 cm) Sie können die Seitengröße auch individuell festlegen, indem Sie die Option Benutzerdefinierte Seitengröße aus der Liste auswählen. Das Fenster Seitengröße öffnet sich und Sie können die gewünschte Voreinstellung auswählen (US Letter, US Legal, A4, A5, B5, Umschlag #10, Umschlag DL, Tabloid, A3, Tabloid Übergröße, ROC 16K, Umschlag Choukei 3, Super B/A3, A0, A1, A2, A6) oder benutzerdefinierte Werte für Breite und Höhe festlegen. Geben Sie Ihre gewünschten Werte in die Eingabefelder ein oder passen Sie die vorhandenen Werte über die Pfeile an. Wenn Sie fertig sind, klicken Sie auf OK, um die Änderungen anzuwenden. Seitenränder Ändern Sie die Standardränder, d. h. den Abstand zwischen den linken, rechten, oberen und unteren Seitenkanten und dem Absatztext, indem Sie auf das Symbol Ränder klicken und eine der verfügbaren Voreinstellungen auswählen: Normal, US Normal, Schmal, Mittel, Breit. Sie können auch die Option Benutzerdefinierte Seitenränder verwenden, um Ihre eigenen Werte im geöffneten Fenster Ränder einzugeben. Geben Sie Ihre gewünschten Werte für die Oberen, Unteren, Linken und Rechten Seitenränder in die Eingabefelder ein oder passen Sie die vorhandenen Werte über die Pfeile an. Wenn Sie fertig sind, klicken Sie auf OK. Die benutzerdefinierten Ränder werden auf das aktuelle Dokument angewendet. In der Liste Ränder wird die Option letzte benutzerdefinierte Einstellung mit den entsprechenden Parametern angezeigt, so dass Sie diese auf alle gewünschten anderen Dokumente anwenden können. Die Option Bundsteg-Position wird benutzt, um zusätzlichen Raum auf der linken oder oberen Seite des Dokumentes zu erstellen. Die Option Bundsteg-Position ist hilfreich, um sicherzustellen, dass die Buchbindung den Text nicht verdeckt. Im Fenster Ränder geben Sie die notwendige Bundsteg-Position in die Eingabefelder ein und wählen Sie aus, wo diese positioniert werden sollen. Die Option Bundsteg-Position kann nicht benutzt werden, wenn die Option Spiegelränder angeschaltet ist. In der Auswahl ‘Mehrere Seiten’ wählen Sie die Option Gegenüberliegende Seiten aus, um die gegenüberliegenden Seiten eines doppelseitigen Dokumentes anzustellen. Wenn diese Option ausgewählt ist, werden die Optionen Linke und Rechte Ränder zu Inneren und Äusseren Rändern geändert. In der Auswahl ‘Orientierung’ wählen Sie zwischen Hoch- oder Querformat aus. Alle angewendeten Änderungen werden in dem Vorschaufenster angezeigt. Wenn Sie fertig sind, klicken Sie OK. Die benutzerdefinierten Ränder werden auf das aktuelle Dokument angewendet und die Option Benutzerdefiniert als letzte mit den angegebenen Parametern wird in der Liste Ränder angezeigt, sodass Sie sie auf andere Dokumente anwenden können. Sie können die Seitenränder auch manuell ändern, indem Sie die Ränder zwischen den grauen und weißen Bereichen der Lineale verschieben (die grauen Bereiche der Lineale weisen auf Seitenränder hin): Spalten Sie können Ihren Text in zwei oder mehr Spalten aufteilen, klicken Sie dazu auf das Symbol Spalten und wählen Sie die gewünschte Spaltenzahl aus der Liste aus. Folgende Optionen stehen zur Verfügung: Zwei - zwei Spalten mit der gleichen Breite hinzufügen. Drei - drei Spalten mit der gleichen Breite hinzufügen. Links - zwei Spalten hinzufügen: Eine schmale Spalte links und eine breite Spalte rechts. Rechts - zwei Spalten hinzufügen: Eine schmale Spalte rechts und eine breite Spalte links. Wenn Sie die Spalteneinstellungen anpassen wollen, wählen Sie die Option Benutzerdefinierte Spalten aus der Liste aus. Das Fenster Spalten öffnet sich und Sie können die gewünschte Spaltenanzahl und den Abstand zwischen den Spalten festlegen (es ist möglich bis zu 12 Spalten einzufügen). Geben Sie Ihre gewünschten Werte in die Eingabefelder ein oder passen Sie die vorhandenen Werte über die Pfeile an. Aktivieren Sie das Kontrollkästchen Spaltentrennung, um eine vertikale Linie zwischen den Spalten einzufügen. Wenn Sie fertig sind, klicken Sie auf OK, um die Änderungen anzuwenden. Wenn Sie genau festlegen wollen, wo eine neue Spalte beginnt, positionieren Sie den Zeiger vor dem Text, den Sie in eine neue Spalte verschieben wollen, klicken Sie in der oberen Symbolleiste auf das Symbol Umbrüche und wählen Sie die Option Spaltenumbruch einfügen. Der Text wird in die nächste Spalte verschoben. Spaltenumbrüche werden in Ihrem Dokument durch eine gepunktete Linie angezeigt: . Wenn die eingefügten Spaltenumbrüche nicht angezeigt werden, klicken Sie in der Registerkarte Startseite auf das Symbol . Um einen Spaltenumbruch zu entfernen, wählen Sie diesen mit der Maus aus und drücken Sie die Taste ENTF. Um die Spaltenbreite und den Abstand manuell zu ändern, können Sie das horizontale Lineal verwenden. Um Spalten zu entfernen und zu einem normalen einspaltigen Layout zurückzukehren, klicken Sie in der oberen Symbolleiste auf das Symbol Spalten und wählen Sie die Option Eine aus der angezeigten Liste aus."
    },
   {
        "id": "UsageInstructions/SetTabStops.htm", 
        "title": "Tabstopps setzen", 
        "body": "Es ist möglich die Tabstopps im Dokumenteneditor zu verändern, d.h., zu ändern, an welche Position die Schreibmarke vorrückt, wenn Sie die Tabulatortaste auf Ihrer Tastatur drücken. Um die Tabstopps festzulegen, können Sie das horizontale Lineal nutzen: Klicken Sie zum Auswählen des gewünschten Tabstopps auf das Symbol in der oberen linken Ecke des Arbeitsbereichs. Folgende Tabstopparten sind verfügbar: Linksbündig - der Text wird ab der Position des Tabstopps linksbündig ausgerichtet; d.h. der Text verschiebt sich bei der Eingabe nach rechts. Ein solcher Tabstopp wird auf dem horizontalen Lineal durch die Markierung angezeigt. Zentriert - der Text wird an der Tabstoppposition zentriert. Ein solcher Tabstopp wird auf dem horizontalen Lineal durch die Markierung angezeigt. Rechtsbündig - der Text wird ab der Position des Tabstopps rechtsbündig ausgerichtet; d.h. der Text verschiebt sich bei der Eingabe nach links, also rückwärts. Ein solcher Tabstopp wird auf dem horizontalen Lineal durch die Markierung angezeigt. Klicken Sie an der unteren Kante des Lineals auf die Position, an der Sie einen Tabstopp setzen möchten. Ziehen Sie die Markierung nach links oder rechts, um die Position zu ändern. Um den hinzugefügten Tabstopp zu entfernen, ziehen Sie die Markierung aus dem Lineal. Sie können Ihre Tabstopps auch über das Fenster Absatzeigenschaften anpassen. Klicken Sie die rechte Maustaste und wählen Sie die Option Erweiterte Absatzeinstellungen im Kontextmenü aus oder nutzen Sie die Verknüpfung Erweiterte Einstellungen anzeigen in der rechten Seitenleiste, und wechseln Sie im geöffneten Fenster Absatz - Erweiterte Einstellungen in das Dialogfenster Tabulator. Sie können die folgende Parameter festlegen: Tabulatorposition - Festlegen von benutzerdefinierten Tabstopps. Geben Sie den gewünschten Wert in das angezeigte Feld ein, über die Pfeiltasten können Sie den Wert präzise anpassen, klicken Sie anschließend auf Festlegen. Ihre benutzerdefinierte Tabulatorposition wird der Liste im unteren Feld hinzugefügt. Wenn Sie zuvor Tabstopps mithilfe des Lineals hinzugefügt haben, werden alle diese Tabulatorpositionen ebenfalls in der Liste angezeigt. Die Standardeinstellung für Tabulatoren ist auf 1,25 cm festgelegt. Sie können den Wert verkleinern oder vergrößern, nutzen Sie dafür die Pfeiltasten oder geben Sie den gewünschten Wert in das dafür vorgesehene Feld ein. Ausrichtung - legt den gewünschten Ausrichtungstyp für jede der Tabulatorpositionen in der obigen Liste fest. Wählen Sie die gewünschte Tabulatorposition in der Liste aus, wählen Sie die Option Linksbündig, Zentriert oder Rechtsbündig aus der Menüliste aus und klicken sie anschließend auf Festlegen. Leader - ermöglicht die Auswahl eines Zeichens, um für jede Tab-Position einen Leader zu erstellen. Ein Leader ist eine Reihe von Zeichen (Punkte oder Trennzeichen), die den Abstand zwischen den Registerkarten ausfüllen. Wählen Sie die gewünschte Tabulatorposition in der Liste aus, wählen Sie die Art des Leaders aus der Menüliste aus und klicken sie anschließend auf Festlegen. Um Tabstopps aus der Liste zu löschen, wählen Sie einen Tabstopp und drücken Sie Entfernen oder Alle entfernen."
    },
   {
        "id": "UsageInstructions/Speech.htm", 
        "title": "Text laut vorlesen", 
        "body": "ONLYOFFICE Dokumenteneditor hat ein Plugin, das den Text für Sie vorlesen kann. Wählen Sie den vorzulesenden Text aus. Öffnen Sie die Registerkarte Plugins und wählen Sie den Menüpunkt Rede aus. Der Text wird nun vorgelesen."
    },
   {
        "id": "UsageInstructions/SupportSmartArt.htm", 
        "title": "Unterstützung von SmartArt im ONLYOFFICE-Dokumenteneditor", 
        "body": "SmartArt-Grafiken werden verwendet, um eine visuelle Darstellung einer hierarchischen Struktur zu erstellen, indem ein Layout ausgewählt wird, das am besten passt. ONLYOFFICE Dokumenteneditor unterstützt SmartArt-Grafiken, die mit Editoren von Drittanbietern eingefügt wurden. Sie können eine Datei öffnen, die SmartArt enthält, und sie mit den verfügbaren Bearbeitungswerkzeugen als Grafikobjekt bearbeiten. Sobald Sie auf eine SmartArt-Grafik oder ihr Element klicken, werden die folgenden Registerkarten in der rechten Seitenleiste aktiv, um ein Layout anzupassen: Absatzeinstellungen zum Konfigurieren von Einzügen und Abständen, Zeilen- und Seitenumbrüchen, Rahmen und Füllungen, Schriftarten, Tabulatoren und Auffüllungen. Sehen Sie den Abschnitt Absatzformatierung für eine detaillierte Beschreibung jeder Option. Diese Registerkarte wird nur für SmartArt-Elemente aktiv. Formeinstellungen, um die in einem Layout verwendeten Formen zu konfigurieren. Sie können Formen ändern, also auch die Füllung, die Linien, die Größe, den Umbruchstil, die Position, die Gewichte und Pfeile, das Textfeld und den alternativen Text bearbeiten. TextArt-Einstellungen, um den Textartstil zu konfigurieren, der in einer SmartArt-Grafik verwendet wird, um den Text hervorzuheben. Sie können die TextArt-Vorlage, den Fülltyp, die Farbe und die Undurchsichtigkeit, die Strichgröße, die -Farbe und den -Typ ändern. Diese Registerkarte wird nur für SmartArt-Elemente aktiv. Klicken Sie mit der rechten Maustaste auf die SmartArt-Grafik oder ihren Elementrahmen, um auf die folgenden Formatierungsoptionen zuzugreifen: Textumbruch, um festzulegen, wie das Objekt relativ zum Text positioniert wird. Die Option Textumbruch ist nur verfügbar, wenn Sie auf den Grafikrahmen von SmartArt-Elementen klicken. Drehen, um die Drehrichtung für das ausgewählte Element auf einer SmartArt-Grafik auszuwählen: 90° im UZS drehen, Um 90° gegen den Uhrzeigersinn drehen. Die Option Drehen wird nur für SmartArt-Elemente aktiv. Beschriftung einfügen, um ein SmartArt-Grafikelement als Referenz zu kennzeichnen. Erweiterte Einstellungen der Form, um auf zusätzliche Formformatierungsoptionen zuzugreifen. Klicken Sie mit der rechten Maustaste auf ein SmartArt-Grafikelement, um auf die folgenden Textformatierungsoptionen zuzugreifen: Vertikale Ausrichtung, um die Textausrichtung innerhalb des ausgewählten SmartArt-Elements zu wählen: Oben ausrichten, Mittig ausrichten, Unten ausrichten. Textausrichtung, um die Textausrichtung innerhalb des ausgewählten SmartArt-Elements auszuwählen: Horizontal, Text nach unten drehen, Text nach oben drehen. Absatz - Erweiterte Einstellungen, um auf zusätzliche Absatzformatierungsoptionen zuzugreifen."
    },
   {
        "id": "UsageInstructions/Thesaurus.htm", 
        "title": "Wort durch Synonym ersetzen", 
        "body": "Wenn Sie dasselbe Wort mehrmals verwenden oder ein Wort nicht ganz das richtige Wort ist, können Sie im ONLYOFFICE Dokumenteneditor nach Synonymen suchen. Die Antonyme werden auch angezeigt. Wählen Sie das Wort in Ihrem Dokument aus. Öffnen Sie die Registerkarte Plugins und wählen Sie den Menüpunkt Thesaurus aus. Die Synonyme und Antonyme werden in der linken Seitenleiste angezeigt. Klicken Sie auf ein Wort, um das Wort in Ihrem Dokument zu ersetzen."
    },
   {
        "id": "UsageInstructions/Translator.htm", 
        "title": "Text übersetzen", 
        "body": "Sie können Ihr Dokument im Dokumenteneditor in zahlreiche Sprachen übersetzen. Wählen Sie den Text aus, den Sie übersetzen möchten. Öffnen Sie die Registerkarte Plugins und wählen Sie den Menüpunkt Übersetzer aus. Der Übersetzer wird in einer Seitenleiste links angezeigt. Klicken Sie auf das Drop-Down-Menü und wählen Sie die bevorzugte Sprache aus. Der Text wird in die gewünschte Sprache übersetzt. Die Sprache des Ergebnisses ändern: Klicken Sie auf das Drop-Down-Menü und wählen Sie die bevorzugte Sprache aus. Die Übersetzung wird sofort geändert."
    },
   {
        "id": "UsageInstructions/Typograf.htm", 
        "title": "Typografie korrigieren", 
        "body": "Wenn Sie die Typografie in Ihrem Text korrigieren müssen, verwenden Sie das Typograf-Plugin, das automatisch geschützte Leerzeichen platziert und zusätzliche entfernt sowie kleinere Tippfehler korrigiert, korrekte Anführungszeichen einfügt, Bindestriche durch Gedankenstriche ersetzt usw. Öffnen Sie die Registerkarte Plugins und klicken Sie auf Typograf. Klicken Sie auf die Schaltfläche Show advanced settings. Wählen Sie den Ort und die Regeln aus, die Sie auf Ihren Text anwenden möchten. Wählen Sie den Text aus, den Sie korrigieren möchten. Klicken Sie auf die Schaltfläche Correct text. Weitere Informationen zum Typograf-Plugin und seiner Installation finden Sie auf der Plugin-Seite in AppDirectory."
    },
   {
        "id": "UsageInstructions/UseMailMerge.htm", 
        "title": "Serienbrief verwenden", 
        "body": "Diese Option ist nur in der Online-Version verfügbar. Die Funktion Seriendruck wird verwendet, um eine Reihe von Dokumenten zu erstellen, die einen gemeinsamen Inhalt aus einem Textdokument und einige einzelne Komponenten (Variablen wie Namen, Grüße usw.) aus einer Tabelle (z. B. eine Kundenliste). Dies kann nützlich sein, wenn Sie viele personalisierte Briefe erstellen und an die Empfänger senden müssen. Eine Datenquelle vorbereiten und sie in das Hauptdokument laden Eine für den Seriendruck verwendete Datenquelle muss eine .xlsx-Tabelle sein, die in Ihrem Portal gespeichert ist. Öffnen Sie eine vorhandene Tabelle oder erstellen Sie eine neue Tabelle und stellen Sie sicher, dass sie die folgenden Anforderungen erfüllt. Die Tabelle sollte eine Kopfzeile mit den Spaltentiteln haben, da die Werte in der ersten Zelle jeder Spalte Briefvorlagenfelder bezeichnen (d. h. Variablen, die Sie in den Text einfügen können). Jede Spalte sollte eine Reihe von tatsächlichen Werten für eine Variable enthalten. Jede Zeile in der Tabelle sollte einem separaten Datensatz entsprechen (d. h. einer Reihe von Werten, die einem bestimmten Empfänger gehören). Während des Zusammenführungsprozesses wird für jeden Datensatz eine Kopie des Hauptdokuments erstellt und jedes in den Haupttext eingefügte Zusammenführungsfeld wird durch einen tatsächlichen Wert aus der entsprechenden Spalte ersetzt. Wenn Sie Ergebnisse per E-Mail senden möchten, muss die Tabelle auch eine Spalte mit den E-Mail-Adressen der Empfänger enthalten. Öffnen Sie ein vorhandenes Textdokument oder erstellen Sie ein neues Dokument. Es muss den Haupttext enthalten, der für jede Version des zusammengeführten Dokuments gleich ist. Klicken Sie auf das Symbol Serienbrief auf der Registerkarte Startseite der oberen Symbolleiste und wählen Sie den Speicherort der Datenquelle: Aus Datei, Aus URL oder Aus dem Speicher. Wählen Sie die erforderliche Datei aus oder fügen Sie eine URL ein und klicken Sie auf OK. Sobald die Datenquelle geladen ist, ist die Registerkarte Seriendruckeinstellungen in der rechten Seitenleiste verfügbar. Die Empfängerliste überprüfen oder ändern Klicken Sie in der rechten Seitenleiste auf die Schaltfläche Empfängerliste bearbeiten, um das Fenster Serienbriefempfänger zu öffnen, in dem der Inhalt der ausgewählten Datenquelle angezeigt wird. Hier können Sie bei Bedarf neue Informationen hinzufügen bzw. vorhandene Daten bearbeiten oder löschen. Um das Arbeiten mit Daten zu vereinfachen, können Sie die Symbole oben im Fenster verwenden: und - zum Kopieren und Einfügen der kopierten Daten. und - um Aktionen rückgängig zu machen und zu wiederholen. und - um Ihre Daten in einem Zellenbereich in aufsteigender oder absteigender Reihenfolge zu sortieren. - um den Filter für den zuvor ausgewählten Zellenbereich zu aktivieren oder den aktuellen Filter zu entfernen. - um alle angewendeten Filterparameter zu löschen. Weitere Informationen zur Verwendung des Filters finden Sie im Abschnitt Sortieren und Filtern von Daten im Hilfemenü des Tabelleneditors. - um nach einem bestimmten Wert zu suchen und ihn gegebenenfalls durch einen anderen zu ersetzen. Weitere Informationen zur Verwendung des Werkzeugs Suchen und Ersetzen finden Sie im Abschnitt Suchen und Ersetzen von Funktionen im Hilfemenü der Tabellenkalkulation. Wenn Sie alle notwendigen Änderungen durchgeführt haben, klicken Sie auf Speichern &amp; Schließen. Um die Änderungen zu verwerfen, klicken Sie auf Schließen. Einfügen von Serienbrieffeldern und überprüfen der Ergebnisse Positionieren Sie den Mauszeiger im Text des Hauptdokuments an der Stelle an der Sie ein Serienbrieffeld einfügen möchten, klicken Sie in der rechten Seitenleiste auf die Schaltfläche Serienbrieffeld einfügen und wählen Sie das erforderliche Feld aus der Liste aus Die verfügbaren Felder entsprechen den Daten in der ersten Zelle jeder Spalte der ausgewählten Datenquelle. Fügen Sie alle benötigten Felder an beliebiger Stelle im Dokument ein. Aktivieren Sie in der rechten Seitenleiste den Schalter Serienbrieffelder hervorheben, um die eingefügten Felder im Text deutlicher zu kennzeichnen. Aktivieren Sie in der rechten Seitenleiste den Schalter Ergebnisvorschau, um den Dokumenttext mit den aus der Datenquelle eingesetzten tatsächlichen Werten anzuzeigen. Verwenden Sie die Pfeiltasten, um für jeden Datensatz eine Vorschau des zusammengeführten Dokuments anzuzeigen. Um ein eingefügtes Feld zu löschen, deaktivieren sie den Modus Ergebnisvorschau, wählen Sie das entsprechende Feld mit der Maus aus und drücken Sie die Taste Entf auf der Tastatur. Um ein eingefügtes Feld zu ersetzen, deaktivieren sie den Modus Ergebnisvorschau, wählen Sie das entsprechende Feld mit der Maus aus, klicken Sie in der rechten Seitenleiste auf die Schaltfläche Serienbrieffeld einfügen und wählen Sie ein neues Feld aus der Liste aus. Parameter für den Serienbrief festlegen Wählen Sie den Zusammenführungstyp aus. Sie können den Massenversand beginnen oder das Ergebnis als Datei im PDF- oder Docx-Format speichern und es später drucken oder bearbeiten. Wählen Sie die gewünschte Option aus der Liste Zusammenführen als aus: PDF - um ein einzelnes Dokument im PDF-Format zu erstellen, das alle zusammengeführten Kopien enthält, damit Sie diese später drucken können DOCX - um ein einzelnes Dokument im DOCX-Format zu erstellen, das alle zusammengeführten Kopien enthält, damit Sie diese später bearbeiten können E-Mail - um die Ergebnisse als E-Mail an die Empfänger zu senden Die E-Mail-Adressen der Empfänger müssen in der geladenen Datenquelle angegeben werden und Sie müssen mindestens ein E-Mail-Konto im Mail-Modul in Ihrem Portal hinterlegt haben. Wählen Sie die Datensätze aus, auf die Sie die Zusammenführung anwenden möchten: Alle Datensätze - (diese Option ist standardmäßig ausgewählt) - um zusammengeführte Dokumente für alle Datensätze aus der geladenen Datenquelle zu erstellen Aktueller Datensatz - zum Erstellen eines zusammengeführten Dokuments für den aktuell angezeigten Datensatz Von... Bis - um ein zusammengeführtes Dokument für eine Reihe von Datensätzen zu erstellen (in diesem Fall müssen Sie zwei Werte angeben: die Werte für den ersten und den letzten Datensatz im gewünschten Bereich) Es können maximal 100 Empfänger angegeben werden. Wenn Sie mehr als 100 Empfänger in Ihrer Datenquelle haben, führen Sie den Serienbrief schrittweise aus: Geben Sie die Werte von 1 bis 100 ein, warten Sie, bis der Serienbriefprozess abgeschlossen ist, und wiederholen Sie den Vorgang mit den Werten von 101 bis N. Serienbrief abschließen Wenn Sie sich entschieden haben, die Ergebnisse der Zusammenführung als Datei zu speichern, klicken Sie auf die Schaltfläche Herunterladen als, um die Datei an einem beliebigen Ort auf Ihrem PC zu speichern. Sie finden die Datei in Ihrem Standardordner für Downloads. Klicken Sie auf die Schaltfläche Speichern, um die Datei in Ihrem Portal zu speichern. Im Fenster Speichern unter können Sie den Dateinamen ändern und den Ort angeben, an dem Sie die Datei speichern möchten. Sie können auch das Kontrollkästchen Zusammengeführtes Dokument in neuem Tab öffnen aktivieren, um das Ergebnis zu überprüfen, sobald der Serienbriefprozess abgeschlossen ist. Klicken Sie zum Schluss im Fenster Speichern unter auf Speichern. Wenn Sie die Option E-Mail ausgewählt haben, erscheint in der rechten Seitenleiste die Schaltfläche Teilen. Wenn Sie auf die Schaltfläche klicken, öffnet sich das Fenster An E-Mail senden: Wenn Sie mehrere Konten mit Ihrem Mail-Modul verbunden haben, wählen Sie in der Liste Von das E-Mail-Konto aus, das Sie zum Senden der E-Mails verwenden möchten. Wählen Sie in der Liste An das Serienbrieffeld aus, das den E-Mail-Adressen der Empfänger entspricht, falls es nicht automatisch ausgewählt wurde. Geben Sie den Betreff Ihrer Nachricht in die Betreffzeile ein. Wählen sie das Mailformat aus der Liste aus: HTML, als DOCX anhängen oder als PDF anhängen. Wenn eine der beiden letzteren Optionen ausgewählt ist, müssen Sie auch den Dateinamen für Anhänge angeben und die Nachricht eingeben (der Text Ihres Briefes, der an die Empfänger gesendet wird). Klicken Sie auf Senden. Sobald das Mailing abgeschlossen ist, erhalten Sie an die im Feld Von angegebene E-Mail-Adresse eine Benachrichtigung."
    },
   {
        "id": "UsageInstructions/ViewDocInfo.htm", 
        "title": "Dokumenteigenschaften anzeigen", 
        "body": "Um detaillierte Informationen über das aktuelle Dokument im Dokumenteneditor einzusehen, wechseln Sie in die Registerkarte Datei und wählen Sie die Option Dokumentinformationen. Allgemeine Eigenschaften Die Dokumentinformationen enthalten eine Reihe von Dateieigenschaften, die das Dokument beschreiben. Einige dieser Eigenschaften werden automatisch aktualisiert, andere können bearbeitet werden. Speicherort - den Ordner im Modul Dokumente, in dem die Datei gespeichert ist. Besitzer - der Name des Benutzers, der die Datei erstellt hat. Hochgeladen - das Datum und die Uhrzeit, wann die Datei erstellt wurde. Diese Eigenschaften sind nur in der Online-Version verfügbar. Statistiken - die Anzahl der Seiten, Absätze, Wörter, Symbole, Symbole mit Leerzeichen. Titel, Thema, Kommentar - mit diesen Eigenschaften können Sie die Klassifizierung Ihrer Dokumente vereinfachen. Sie können den erforderlichen Text in den Eigenschaftsfeldern angeben. Zuletzt geändert - Datum und Uhrzeit der letzten Änderung der Datei. Zuletzt geändert von - der Name des Benutzers, der die letzte Änderung am Dokument vorgenommen hat. Diese Option ist verfügbar, wenn das Dokument freigegeben wurde und von mehreren Benutzern bearbeitet werden kann. Anwendung - die Anwendung, mit der das Dokument erstellt wurde. Verfasser - die Person, die die Datei erstellt hat. In diesem Feld können Sie den erforderlichen Namen eingeben. Drücken Sie die Eingabetaste, um ein neues Feld hinzuzufügen, in dem Sie einen weiteren Autor angeben können. Wenn Sie die Dateieigenschaften geändert haben, klicken Sie auf die Schaltfläche Anwenden, um die Änderungen anzuwenden. Sie können den Namen des Dokuments direkt über die Oberfläche des Editors ändern. Klicken Sie dazu in der oberen Menüleiste auf die Registerkarte Datei und wählen Sie die Option Umbenennen, geben Sie den neuen Dateinamen an und klicken Sie auf OK. Zugriffsrechte In der Online-Version können Sie die Informationen zu Berechtigungen für die in der Cloud gespeicherten Dateien einsehen. Diese Option steht im schreibgeschützten Modus nicht zur Verfügung. Um einzusehen, wer das Recht hat, das Dokument anzuzeigen oder zu bearbeiten, wählen Sie die Option Zugriffsrechte in der linken Seitenleiste. Sie können die aktuell ausgewählten Zugriffsrechte auch ändern, klicken Sie dazu im Abschnitt Personen mit Berechtigungen auf die Schaltfläche Zugriffsrechte ändern. Versionsverlauf In der Online-Version können Sie den Versionsverlauf für die in der Cloud gespeicherten Dateien einsehen. Diese Option steht im schreibgeschützten Modus nicht zur Verfügung. Um alle Änderungen an diesem Dokument anzuzeigen, wählen Sie die Option Versionsverlauf in der linken Seitenleiste. Sie können den Versionsverlauf auch über das Symbol Versionsverlauf in der Registerkarte Zusammenarbeit öffnen. Sie sehen eine Liste mit allen Dokumentversionen (Hauptänderungen) und Revisionen (geringfügige Änderungen), unter Angabe aller jeweiligen Autoren sowie Erstellungsdatum und -zeit. Für Dokumentversionen wird auch die Versionsnummer angegeben (z.B. Ver. 2). Für eine detaillierte Anzeige der jeweiligen Änderungen in jeder einzelnen Version/Revision können Sie die gewünschte Version anzeigen, indem Sie in der linken Seitenleiste darauf klicken. Die vom Autor der Version/Revision vorgenommenen Änderungen sind mit der Farbe markiert, die neben dem Autorennamen in der linken Seitenleiste angezeigt wird. Über den unterhalb der gewählten Version/Revision angezeigten Link Wiederherstellen gelangen Sie in die jeweilige Version. Um zur aktuellen Version des Dokuments zurückzukehren, klicken Sie oben in der Liste mit Versionen auf Verlauf schließen. Um das Fenster Datei zu schließen und zur Dokumentbearbeitung zurückzukehren, klicken sie auf Menü schließen."
    },
   {
        "id": "UsageInstructions/WordCounter.htm", 
        "title": "Wörter zählen", 
        "body": "Um die genaue Anzahl der Wörter und Symbole mit und ohne Leerzeichen in Ihrem Dokument sowie die Anzahl der Absätze insgesamt zu kennen, verwenden Sie das Wortzähler-Plugin. Öffnen Sie die Registerkarte Plugins und klicken Sie auf die Schaltfläche Wörter und Zeichen zählen. Wählen Sie den Text aus. Bitte beachten Sie, dass die folgenden Elemente nicht in der Wortzahl enthalten sind: Fußnoten-/Endnotensymbole, Zahlen aus nummerierten Listen, Seitenzahlen. Weitere Informationen zum Wortzähler-Plugin und die Installation finden Sie auf der Seite des Plugins im AppDirectory."
    },
   {
        "id": "UsageInstructions/Wordpress.htm", 
        "title": "Dokument in WordPress hochladen", 
        "body": "Sie können Ihre Artikel in Ihrer ONLYOFFICE-Umgebung schreiben und als WordPress-Artikel hochladen. Mit WordPress verbinden Öffnen Sie Ihr Dokument. Öffnen Sie die Registerkarte Plugins und wählen Sie den Menüpunkt WordPress aus. Melden Sie sich in Ihrem WordPress-Konto an und wählen Sie die Seite aus, auf der Sie Ihr Dokument veröffentlichen möchten. Geben Sie den Titel des Artikels ein. Klicken Sie auf Veröffentlichen, um sofort zu veröffentlichen, oder auf Als Entwurf speichern, um später von Ihrer WordPress-Seite oder -Anwendung zu veröffentlichen."
    },
   {
        "id": "UsageInstructions/YouTube.htm", 
        "title": "Video einfügen", 
        "body": "Sie können ein Video im Dokumenteneditor in Ihr Dokument einfügen. Es wird als Bild angezeigt. Durch Doppelklick auf das Bild wird der Videodialog geöffnet. Hier können Sie das Video starten. Kopieren Sie die URL des Videos, das Sie einbetten möchten. (die vollständige Adresse wird in der Adresszeile Ihres Browsers angezeigt) Gehen Sie zu Ihrem Dokument und platzieren Sie den Cursor an der Stelle, an der Sie das Video einfügen möchten. Öffnen Sie die Registerkarte Plugins und wählen Sie den Menüpunkt YouTube aus. Fügen Sie die URL ein und klicken Sie auf OK. Überprüfen Sie, ob Sie das richtige Video eingefügt haben, und klicken Sie auf die Schaltfläche OK unter dem Video. Das Video ist jetzt in Ihrem Dokument eingefügt."
    }
]