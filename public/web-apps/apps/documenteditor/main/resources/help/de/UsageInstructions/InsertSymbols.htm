﻿<!DOCTYPE html>
<html>
<head>
    <title>Symbole und Sonderzeichen einfügen</title>
    <meta charset="utf-8" />
    <meta name="description" content="Symbole und Sonderzeichen einfügen" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Symbole und Sonderzeichen einfügen</h1>
        <p>Während des Arbeitsprozesses im <a target="_blank" href="https://www.onlyoffice.com/de/document-editor.aspx" onclick="onhyperlinkclick(this)"><b>Dokumenteneditor</b></a> wollen Sie ein Symbol einfügen, das sich nicht auf der Tastatur befindet. Um solche Symbole einzufügen, verwenden Sie die Option <span class = "icon icon-insert_symbol_icon"></span> <b>Symbol einfügen</b>:</p>
        <ul>
            <li>positionieren Sie den Textcursor an der Stelle für das Sonderzeichen,</li>
            <li>öffnen Sie die Registerkarte <b>Einfügen</b>,</li>
            <li>
                klicken Sie <div class = "icon icon-insert_symbol_icon"></div> <b>Symbol</b> an,
                <p><img alt="Symbolrandleiste" src="../images/insert_symbol_window.png" /></p>
            </li>
            <li>das Dialogfeld <b>Symbol</b> wird angezeigt, in dem Sie das gewünschte Symbol auswählen können,</li>
            <li>
                <p>öffnen Sie das Dropdown-Menü <b>Bereich</b>, um ein Symbol schnell zu finden. Alle Symbole sind in Gruppen unterteilt, wie z.B. “Währungssymbole” für Währungszeichen,</p>
                <p>falls Sie das gewünschte Symbol nicht finden können, wählen Sie eine andere Schriftart aus. Viele von ihnen haben auch die Sonderzeichen, die es nicht in den Standartsatz gibt,</p>
                <p>Sie können auch das <b>Unicode HEX Wert</b>-Feld verwenden, um den Code einzugeben. Die Codes können Sie in der <b>Zeichentabelle</b> finden,</p>
                <p>verwenden Sie auch die Registerkarte <b>Sonderzeichen</b>, um ein Sonderzeichen auszuwählen,</p>
                <p><img alt="Symbolrandleiste" src="../images/insert_symbol_window2.png" /></p>
                <p>die Symbole, die zuletzt verwendet wurden, befinden sich im Feld <b>Kürzlich verwendete Symbole</b>,</p>
            </li>
            <li>klicken Sie <b>Einfügen</b> an. Das ausgewählte Symbol wird eingefügt.</li>
        </ul>

        <h2>ASCII-Symbole einfügen</h2>
        <p>Man kann auch die ASCII-Tabelle verwenden, um die Zeichen und Symbole einzufügen.</p>
        <p>Drücken und halten Sie die ALT-Taste und verwenden Sie den Ziffernblock, um einen Zeichencode einzugeben.</p>
        <p class="note">Verwenden Sie nur den Ziffernblock. Um den Ziffernblock zu aktivieren, drücken Sie die NumLock-Taste.</p>
        <p>Z.B., um das Paragraphenzeichen (§) einzufügen, drücken und halten Sie die ALT-Taste und geben Sie 789 ein, dann lassen Sie die ALT-Taste los.</p>

        <h2>Symbole per Unicode-Tabelle einfügen</h2>
        <p>Sonstige Symbole und Zeichen befinden sich auch in der Windows-Symboltabelle. Um diese Tabelle zu öffnen:</p>
        <ul>
            <li>geben Sie “Zeichentabelle” in dem Suchfeld ein,</li>
            <li>
                drücken Sie die Windows-Taste+R und geben Sie <code>charmap.exe</code> im Suchfeld ein, dann klicken Sie OK.
                <p><img alt="Symboltabelle einfügen" src="../images/insert_symbols_windows.png" /></p>
            </li>
        </ul>
        <p>Wählen Sie die <b>Zeichensätze</b>, <b>Gruppen</b> und <b>Schriftarten</b> aus. Klicken Sie die gewünschte Zeichen an, dann kopieren und fügen an der gewünschten Stelle ein.</p>
    </div>
</body>
</html>