﻿<!DOCTYPE html>
<html>
	<head>
		<title><PERSON>hr<PERSON><PERSON>, -größe und -farbe festlegen</title>
		<meta charset="utf-8" />
		<meta name="description" content="Change the following text formatting parameters: font type, size, and color" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Schrift<PERSON>, -größe und -farbe festlegen</h1>
            <p>Im <a target="_blank" href="https://www.onlyoffice.com/de/document-editor.aspx" onclick="onhyperlinkclick(this)"><b>Dokumenteneditor</b></a> sie können Schriftart, Größe und Farbe der Schrift mithilfe der entsprechenden Symbole in der Registerkarte <b>Start</b> auf der oberen Symbolleiste festlegen.</p>
			<p class="note">Wenn Sie die Formatierung auf Text anwenden möchten, der bereits im Dokument vorhanden ist, wählen Sie diesen mit der Maus oder <a href="../HelpfulHints/KeyboardShortcuts.htm#textselection" onclick="onhyperlinkclick(this)">mithilfe der Tastatur</a> aus und legen Sie die gewünschte Formatierung fest. Sie können den Mauszeiger auch innerhalb des erforderlichen Wortes platzieren, um die Formatierung nur auf dieses Wort anzuwenden.</p>
			<table>
				<tr>
                    <td width="10%">Schriftart</td>
                    <td width="15%"><div class = "big big-fontfamily"></div></td>
					<td>Wird verwendet, um eine Schriftart aus der Liste mit den verfügbaren Schriftarten zu wählen. <span class="desktopDocumentFeatures">Wenn eine gewünschte Schriftart nicht in der Liste verfügbar ist, können Sie diese runterladen und in Ihrem Betriebssystem speichern. Anschließend steht Ihnen diese Schrift in der <em>Desktop-Version</em> zur Nutzung zur Verfügung.</span></td>
				</tr>
				<tr>
					<td>Schriftgröße</td>
					<td><div class = "icon icon-fontsize"></div></td>
					<td>Über diese Option kann der gewünschte Wert für die Schriftgröße aus der Dropdown-List ausgewählt werden (die Standardwerte sind: 8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 36, 48, 72 und 96). Sie können auch manuell einen Wert in das Feld für die Schriftgröße eingeben und anschließend die <em>Eingabetaste</em> drücken.</td>
				</tr>
                <tr>
                    <td>Schrift vergrößern</td>
                    <td><div class = "icon icon-larger"></div></td>
                    <td>Wird verwendet, um die Schriftgröße mit jedem Klick auf das Symbol um einen Punkt zu vergrößern.</td>
                </tr>
                <tr>
                    <td>Schrift verkleinern</td>
                    <td><div class = "icon icon-smaller"></div></td>
                    <td>Wird verwendet, um die Schriftgröße mit jedem Klick auf das Symbol um einen Punkt zu verkleinern.</td>
                </tr>
				<tr>
					<td>Groß-/Kleinschreibung</td>
					<td><div class = "icon icon-change_case"></div></td>
					<td>Wird verwendet, um die Groß- und Kleinschreibung zu ändern. <em>Ersten Buchstaben im Satz großschreiben</em> - die Buchstaben werden wie in einem Satz geschrieben. <em>Kleinbuchstaben</em> - alle Buchstaben sind klein. <em>GROSSBUCHSTABEN</em> - alle Buchstaben sind groß. <em>Ersten Buchstaben im Wort großschreiben</em> - jedes Wort beginnt mit einem Großbuchstaben. <em>gROSS-/kLEINSCHREIBUNG</em> - kehrt die Groß- und Kleinschreibung des ausgewählten Textes oder des Wortes um, an dem sich der Mauszeiger befindet.</td>
				</tr>
				<tr>
					<td>Texthervorhebungsfarbe</td>
					<td><div class = "icon icon-highlightcolor"></div></td>
					<td>Wird verwendet, um den Hintergrund für einzelne Sätze, Phrasen, Wörter oder sogar Zeichen durch Hinzufügen eines Farbbandes zu markieren, das den Effekt eines Textmarker um den Text herum imitiert. Wählen Sie dazu den gewünschten Text aus und klicken Sie anschließend auf den Abwärtspfeil neben dem Symbol, um eine Farbe auf der Palette auszuwählen (diese Farbeinstellung ist unabhängig vom ausgewählten <b>Farbschema</b> und enthält 16 Farben) - die Farbe wird auf den ausgewählten Text angewendet. Alternativ können Sie zuerst eine Hervorhebungsfarbe auswählen und dann den Text mit der Maus auswählen - der Mauszeiger sieht in diesem Fall so aus <div class = "icon icon-highlight_color_mouse_pointer"></div> und Sie können mehrere verschiedene Teile Ihres Textes nacheinander markieren. Um die Hervorhebung zu beenden, klicken Sie einfach erneut auf das Symbol. Um die Markierungsfarbe zu löschen, klicken Sie auf die Option <b>Keine Füllung</b>. Bei <b>Hervorhebungen</b> werden nur die Zeichen hinterlegt, im Gegensatz zu <a href="../UsageInstructions/BackgroundColor.htm" onclick="onhyperlinkclick(this)"><b>Hintergrundfarbe,</b></a> <div class = "icon icon-backgroundcolor"></div> die auf den gesamten Absatz angewendet wird und auch Leerräume im Absatz farblich hinterlegt.</td>
				</tr>
				<tr>
					<td>Schriftfarbe</td>
					<td><div class = "icon icon-fontcolor"></div></td>
					<td>Wird verwendet, um die Farbe der Buchstaben/Zeichen im Text zu ändern. Standardmäßig wird die Schriftfarbe in einem neuen leeren Dokument automatisch festgelegt, als schwarze Schrift auf weißem Hintergrund. Wenn Sie die Hintergrundfarbe auf schwarz ändern, wechselt die Schriftfarbe automatisch auf weiß, damit sie weiter zu erkennen ist. Um eine andere Farbe auszuwählen, klicken Sie auf den Abwärtspfeil neben dem Symbol und wählen Sie eine Farbe aus den verfügbaren Paletten aus (die angezeigten <b>Designfarben</b> sind abhängig vom ausgewählten <a href="../UsageInstructions/ChangeColorScheme.htm" onclick="onhyperlinkclick(this)">Farbschema</a>. Wenn Sie die Standardschriftfarbe geändert haben, können Sie die Standardfarbe für die ausgewählte Textpassage über die Option <b>Automatisch</b> in der Gruppe Farbpaletten wiederherstellen.</td>
				</tr>
			</table>
			<p class="note">Weitere Informationen zum Umgang mit Farbpaletten finden Sie auf <a href="../UsageInstructions/ChangeColorScheme.htm" onclick="onhyperlinkclick(this)">dieser Seite</a>.</p>
		</div>
	</body>
</html>