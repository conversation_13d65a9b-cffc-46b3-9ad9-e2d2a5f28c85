﻿<!DOCTYPE html>
<html>
	<head>
		<title>Bilder einfügen</title>
		<meta charset="utf-8" />
        <meta name="description" content="Fügen Sie ein Bild in Ihrem Dokument ein und ändern Sie seine Position und Eigenschaften" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
			<div class="search-field">
				<input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
			</div>
			<h1>Bilder einfügen</h1>
			<p>Im <a target="_blank" href="https://www.onlyoffice.com/de/document-editor.aspx" onclick="onhyperlinkclick(this)"><b>Dokumenteneditor</b></a> können Sie Bilder in den gängigsten Formaten in Ihr Dokument einfügen. Die folgenden Bildformate werden unterstützt: <b>BMP</b>, <b>GIF</b>, <b>JPEG</b>, <b>JPG</b>, <b>PNG</b>.</p>
			<h3>Bild einfügen</h3>
			<p>Um ein Bild in den Dokumenttext einzufügen,</p>
			<ol>
				<li>Plazieren Sie den Zeiger an der Stelle, an der das Bild platziert werden soll.</li>
				<li>Wechseln Sie zur Registerkarte <b>Einfügen</b> in der oberen Symbolleiste.</li>
				<li>Klicken Sie auf das <div class = "icon icon-image"></div> <b>Bildsymbol</b> in der oberen Symbolleiste.</li>
				<li>Wählen Sie eine der folgenden Optionen, um das Bild zu laden:
					<ul>
						<li>Die Option <b>Bild aus Datei</b> öffnet das Standarddialogfenster für die Dateiauswahl. Durchsuchen Sie das Festplattenlaufwerk Ihres Computers nach der erforderlichen Date und klicken Sie auf die Schaltfläche <b>Öffnen</b>.
							<p class="note">Im <em>Online-Editor</em> können Sie mehrere Bilder gleichzeitig auswählen.</p>
						</li>
						<li>Die Option <b>Bild von URL</b> öffnet das Fenster, in dem  Sie die erforderliche Bild-Webadresse eingeben und auf die Schaltfläche <b>OK</b> klicken können.</li>
						<li class="onlineDocumentFeatures">Die Option <b>Bild aus Speicher</b> öffnet das Fenster <b>Datenquelle auswählen</b>. Wählen Sie ein in Ihrem Portal gespeichertes Bild aus und klicken Sie auf die Schaltfläche <b>OK</b>.</li>
					</ul>
				</li>
				<li>Sobald das Bild hinzugefügt wurde, können Sie seine Größe, Eigenschaften und Position ändern.</li>
			</ol>
			<p>Es ist auch möglich, dem Bild eine Beschriftung hinzuzufügen. Weitere Informationen zum Arbeiten mit Bildunterschriften finden Sie in <a href="../UsageInstructions/AddCaption.htm" onclick="onhyperlinkclick(this)">diesem Artikel</a>.</p>
			<h3>Die Größe von Bildern ändern und Bilder verschieben</h3>
            <p><span class="big big-moving_image"></span></p>
			<p>Um die Bildgröße zu ändern, ziehen Sie kleine Quadrate <span class = "icon icon-resize_square"></span> an den Rändern. Halten Sie die <b>Umschalttaste</b> gedrückt und ziehen Sie eines der Eckensymbole, um die ursprünglichen Proportionen des ausgewählten Bilds während der Größenänderung beizubehalten.</p>
			<p>Verwenden Sie zum Ändern der Bildposition das Symbol <span class = "icon icon-arrow"></span>, das angezeigt wird, nachdem Sie den Mauszeiger über das Bild bewegt haben. Ziehen Sie das Bild an die gewünschte Position, ohne die Maustaste loszulassen.</p>
			<p>Wenn Sie das Bild verschieben, werden Hilfslinien angezeigt, mit denen Sie das Objekt präzise auf der Seite positionieren können (wenn ein anderer Umbruchstil als Inline ausgewählt ist).</p>
			<p>Um das Bild zu drehen, bewegen Sie den Mauszeiger über den Drehgriff <span class = "icon icon-greencircle"></span> und ziehen Sie ihn im oder gegen den Uhrzeigersinn. Halten Sie die Umschalttaste gedrückt, um den Drehwinkel auf Schritte von 15 Grad zu beschränken.</p>
			<p class="note">
				Die Liste der Tastaturkürzel, die beim Arbeiten mit Objekten verwendet werden können, finden Sie <a href="../HelpfulHints/KeyboardShortcuts.htm#workwithobjects" onclick="onhyperlinkclick(this)">hier</a>.
			</p>
			<hr />
			<h3>Die Bildeinstellungen anpassen</h3>
			<p><img class="floatleft" alt="Registerkarte Bildeinstellungen" src="../images/right_image.png" />Einige der Bildeinstellungen können über die Registerkarte <b>Bildeinstellungen</b> in der rechten Seitenleiste geändert werden. Um es zu aktivieren, klicken Sie auf das Bild und wählen Sie rechts das Symbol <b>Bildeinstellungen</b> <span class = "icon icon-image_settings_icon"></span>. Hier können Sie folgende Eigenschaften ändern:</p>
			<ul style="margin-left: 280px;">
				<li><b>Größe</b> wird verwendet, um die aktuelle <b>Bildbreite</b> und <b>-höhe</b> anzuzeigen. Bei Bedarf können Sie die tatsächliche Bildgröße wiederherstellen, indem Sie auf die Schaltfläche <b>Tatsächliche Größe</b> klicken. Mit der Schaltfläche <b>An Rand anpassen</b> können Sie die Größe des Bilds so ändern, dass es den gesamten Abstand zwischen dem linken und rechten Seitenrand einnimmt.
				<p>Mit der Schaltfläche <b>Zuschneiden</b> können Sie das Bild zuschneiden. Klicken Sie auf die Schaltfläche <b>Zuschneiden</b>, um die Beschneidungsgriffe zu aktivieren, die an den Bildecken und in der Mitte jeder Seite angezeigt werden. Ziehen Sie die Ziehpunkte manuell, um den Zuschneidebereich festzulegen. Sie können den Mauszeiger über den Rand des Zuschneidebereichs bewegen, sodass er zum <span class = "icon icon-arrow"></span> Symbol wird, und den Bereich ziehen.</p>
					<ul>
						<li>Um eine einzelne Seite zuzuschneiden, ziehen Sie den Griff in der Mitte dieser Seite.</li>
						<li>Ziehen Sie einen der Eckgriffe, um zwei benachbarte Seiten gleichzeitig zuzuschneiden.</li>
						<li>Um zwei gegenüberliegende Seiten des Bildes gleichermaßen zuzuschneiden, halten Sie die Strg-Taste gedrückt, wenn Sie den Griff in die Mitte einer dieser Seiten ziehen.</li>
						<li>Um alle Seiten des Bildes gleichmäßig zuzuschneiden, halten Sie die <em>Strg</em>-Taste gedrückt, wenn Sie einen der Eckgriffe ziehen.</li>
					</ul>
					<p>Wenn der Zuschneidebereich angegeben ist, klicken Sie erneut auf die Schaltfläche <b>Zuschneiden</b> oder drücken Sie die <em>Esc</em>-Taste oder klicken Sie auf eine beliebige Stelle außerhalb des Zuschneidebereichs, um die Änderungen zu übernehmen.</p>
					<p>Nachdem der Zuschneidebereich ausgewählt wurde, können Sie auch die Optionen <b>Auf Form zuschneiden</b>, <b>Ausfüllen</b> und <b>Anpassen</b> verwenden, die im Dropdown-Menü <b>Zuschneiden</b> verfügbar sind. Klicken Sie erneut auf die Schaltfläche <b>Zuschneiden</b> und wählen Sie die gewünschte Option aus:</p>
					<ul>
						<li>Wenn Sie die Option <b>Auf Form zuschneiden</b> auswählen, füllt das Bild eine bestimmte Form aus. Sie können eine Form aus der Galerie auswählen, die geöffnet wird, wenn Sie Ihren Mauszeiger über die Option <b>Auf Form zuschneiden</b> bewegen. Sie können weiterhin die Optionen <b>Füllen</b> und <b>Anpassen</b> verwenden, um auszuwählen, wie Ihr Bild der Form entspricht.</li>
						<li>Wenn Sie die Option <b>Füllen</b> auswählen, bleibt der zentrale Teil des Originalbilds erhalten und wird zum Füllen des ausgewählten Zuschneidebereichs verwendet, während andere Teile des Bildes entfernt werden.</li>
						<li>Wenn Sie die Option <b>Anpassen</b> auswählen, wird die Größe des Bilds so angepasst, dass es der Höhe oder Breite des Zuschneidebereichs entspricht. Es werden keine Teile des Originalbilds entfernt, es können jedoch leere Bereiche innerhalb des ausgewählten Zuschneidebereichs angezeigt werden.</li>
					</ul>
				</li>
				<li>Durch <b>Drehen</b> wird das Bild um 90 Grad im oder gegen den Uhrzeigersinn gedreht sowie das Bild horizontal oder vertikal gespiegelt. Klicken Sie auf eine der Schaltflächen:
				<ul>
					<li><div class = "icon icon-rotatecounterclockwise"></div> um das Bild um 90 Grad gegen den Uhrzeigersinn zu drehen</li>
					<li><div class = "icon icon-rotateclockwise"></div> um das Bild um 90 Grad im Uhrzeigersinn zu drehen</li>
					<li><div class = "icon icon-fliplefttoright"></div> um das Bild horizontal zu drehen  (von links nach rechts)</li>
					<li><div class = "icon icon-flipupsidedown"></div> um das Bild vertikal zu drehen (verkehrt herum)</li>
				</ul>
				</li>
				<li>Der <b>Umbruchstil</b> wird verwendet, um einen Textumbruchstil aus den verfügbaren auszuwählen - Inline, Quadrat, Eng, Durch, Oben und Unten, vorne, hinten (weitere Informationen finden Sie in der Beschreibung der erweiterten Einstellungen unten).</li>
				<li><b>Bild ersetzen</b> wird verwendet, um das aktuelle Bild zu ersetzen, das ein anderes <b>aus Datei</b> oder Von URL lädt.</li>
			</ul>
			<p>Einige dieser Optionen finden Sie auch im <b>Kontextmenü</b>. Die Menüoptionen sind:</p>
			<ul style="margin-left: 280px;">
				<li><b>Ausschneiden, Kopieren, Einfügen</b> - Standardoptionen, mit denen ein ausgewählter Text / ein ausgewähltes Objekt ausgeschnitten oder kopiert und eine zuvor ausgeschnittene / kopierte Textpassage oder ein Objekt an die aktuelle Zeigerposition eingefügt wird.</li>
				<li><b>Anordnen</b> wird verwendet, um das ausgewählte Bild in den Vordergrund zu bringen, in den Hintergrund zu senden, vorwärts oder rückwärts zu bewegen sowie Bilder zu gruppieren oder die Gruppierung aufzuheben, um Operationen mit sieben auszuführen von ihnen sofort. Weitere Informationen zum Anordnen von Objekten finden Sie auf <a href="../UsageInstructions/AlignArrangeObjects.htm" onclick="onhyperlinkclick(this)">dieser Seite</a>.</li>
				<li><b>Ausrichten</b> wird verwendet, um das Bild links, in der Mitte, rechts, oben, in der Mitte und unten auszurichten. Weitere Informationen zum Ausrichten von Objekten finden Sie auf <a href="../UsageInstructions/AlignArrangeObjects.htm" onclick="onhyperlinkclick(this)">dieser Seite</a>.</li>
				<li>Der <b>Umbruchstil</b> wird verwendet, um einen Textumbruchstil aus den verfügbaren auszuwählen - inline, quadratisch, eng, durch, oben und unten, vorne, hinten - oder um die <b>Umbruchgrenze zu bearbeiten</b>. Die Option Wrap-Grenze bearbeiten ist nur verfügbar, wenn Sie einen anderen Wrap-Stil als Inline auswählen. Ziehen Sie die Umbruchpunkte, um die Grenze anzupassen. Um einen neuen Umbruchpunkt zu erstellen, klicken Sie auf eine beliebige Stelle auf der roten Linie und ziehen Sie sie an die gewünschte Position. <div class = "big big-wrap_boundary"></div></li>
				<li><b>Drehen</b> wird verwendet, um das Bild um 90 Grad im oder gegen den Uhrzeigersinn zu drehen sowie um das Bild horizontal oder vertikal zu spiegeln.</li>
				<li><b>Zuschneiden</b> wird verwendet, um eine der Zuschneideoptionen anzuwenden: <b>Zuschneiden</b>, <b>Füllen</b> oder <b>Anpassen</b>. Wählen Sie im Untermenü die Option Zuschneiden, ziehen Sie dann die Zuschneidegriffe, um den Zuschneidebereich festzulegen, und klicken Sie im Untermenü erneut auf eine dieser drei Optionen, um die Änderungen zu übernehmen.</li>
				<li>Die <b>Tatsächliche Größe</b> wird verwendet, um die aktuelle Bildgröße in die tatsächliche zu ändern.</li>
				<li><b>Bild ersetzen</b> wird verwendet, um das aktuelle Bild zu ersetzen, das ein anderes <b>aus Datei</b> oder <b>Von URL</b> lädt.</li>
				<li>Mit den <b>Erweiterte Einstellungen des Bildes</b> wird das Fenster "Bild - Erweiterte Einstellungen" geöffnet.</li>
			</ul>
			<p><img class="floatleft" alt="Registerkarte Formeinstellungen" src="../images/right_image_shape.png" /> Wenn das Bild ausgewählt ist, ist rechts auch das Symbol für die <b>Formeinstellungen</b> <span class = "icon icon-shape_settings_icon"></span> verfügbar. Sie können auf dieses Symbol klicken, um die Registerkarte <b>Formeinstellungen</b> in der rechten Seitenleiste zu öffnen und die Form anzupassen. <a href="../UsageInstructions/InsertAutoshapes.htm#shape_stroke" onclick="onhyperlinkclick(this)"><b>Strichart</b></a>, -größe und -farbe sowie den Formtyp ändern, indem Sie im Menü <b>Autoshape</b> ändern eine andere Form auswählen. Die Form des Bildes ändert sich entsprechend.</p>
			<p>Auf der Registerkarte <b>Formeinstellungen</b> können Sie auch die Option <b>Schatten anzeigen</b> verwenden, um dem Bild einen Schatten hinzuzufügen.</p>
			<hr />
			<h3>Die erweiterten Bildeinstellungen anpassen</h3>
			<p>Um die erweiterten Einstellungen des Bildes zu ändern, klicken Sie mit der rechten Maustaste auf das Bild und wählen Sie im Kontextmenü die Option <b>Bild - Erweiterte Einstellungen</b> oder klicken Sie einfach auf den Link <b>Erweiterte Einstellungen anzeigen</b> in der rechten Seitenleiste. Das Fenster mit den Bildeigenschaften wird geöffnet:</p>
			<p><img alt="Bild - Erweiterte Einstellungen: Größe" src="../images/image_properties.png" /></p>
			<p>Die Registerkarte <b>Größe</b> enthält die folgenden Parameter:</p>
			<ul>
				<li><b>Breite</b> und <b>Höhe</b> - Verwenden Sie diese Optionen, um die Bildbreite und / oder -höhe zu ändern. Wenn Sie auf die Schaltfläche <b>Konstante Proportionen</b> <div class = "icon icon-constantproportions"></div> klicken (in diesem Fall sieht es so aus <div class = "icon icon-constantproportionsactivated"></div>), werden Breite und Höhe zusammen geändert, wobei das ursprüngliche Bildseitenverhältnis beibehalten wird. Klicken Sie auf die Schaltfläche <b>Tatsächliche Größe</b>, um die tatsächliche Größe des hinzugefügten Bilds wiederherzustellen.</li>
			</ul>
			<p><img alt="Bild - Erweiterte Einstellungen: Drehen" src="../images/image_properties_4.png" /></p>
			<p>Die Registerkarte <b>Rotation</b> enthält die folgenden Parameter:</p>
			<ul>
				<li><b>Winkel</b> - Verwenden Sie diese Option, um das Bild um einen genau festgelegten Winkel zu drehen. Geben Sie den erforderlichen Wert in Grad in das Feld ein oder passen Sie ihn mit den Pfeilen rechts an.</li>
				<li><b>Spiegeln</b> - Aktivieren Sie das Kontrollkästchen <b>Horizontal</b>, um das Bild horizontal zu spiegeln (von links nach rechts), oder aktivieren Sie das Kontrollkästchen <b>Vertikal</b>, um das Bild vertikal zu spiegeln (verkehrt herum).</li>
			</ul>
			<p><img alt="Bild - Erweiterte Einstellungen: Textumbruch" src="../images/image_properties_1.png" /></p>
			<p>Die Registerkarte <b>Textumbruch</b> enthält die folgenden Parameter:</p>
			<ul>
				<li>
					<b>Umbruchstil</b> - Verwenden Sie diese Option, um die Position des Bilds relativ zum Text zu ändern: Es ist entweder Teil des Textes (falls Sie den Inline-Stil auswählen) oder wird von allen Seiten umgangen (wenn Sie einen auswählen) die anderen Stile).<ul>
						<li><p><div class = "icon icon-wrappingstyle_inline"></div> <b>Inline </b> - Das Bild wird wie ein Zeichen als Teil des Textes betrachtet. Wenn sich der Text bewegt, bewegt sich auch das Bild. In diesem Fall sind die Positionierungsoptionen nicht zugänglich.</p>
							<p>Wenn einer der folgenden Stile ausgewählt ist, kann das Bild unabhängig vom Text verschoben und genau auf der Seite positioniert werden:</p>
						</li>
						<li><p><div class = "icon icon-wrappingstyle_square"></div> <b>Quadratisch</b> - Der Text umschließt das rechteckige Feld, das das Bild begrenzt.</p></li>
						<li><p><div class = "icon icon-wrappingstyle_tight"></div> <b>Eng</b> - Der Text umschließt die eigentlichen Bildkanten.</p></li>
						<li><p><div class = "icon icon-wrappingstyle_through"></div> <b>Durch</b> - Der Text wird um die Bildränder gewickelt und füllt den offenen weißen Bereich innerhalb des Bildes aus. Verwenden Sie die Option <b>Umbruchgrenze bearbeiten</b> im Kontextmenü, damit der Effekt angezeigt wird.</p></li>
						<li><p><div class = "icon icon-wrappingstyle_topandbottom"></div> <b>Oben und unten</b> - der Text befindet sich nur über und unter dem Bild.</p></li>
						<li><p><div class = "icon icon-wrappingstyle_infront"></div> <b>Vorne</b> - das Bild überlappt den Text.</p></li>
						<li><p><div class = "icon icon-wrappingstyle_behind"></div> <b>Dahinter</b> - der Text überlappt das Bild.</p></li>
					</ul>
				</li>
			</ul>
			<p>Wenn Sie den quadratischen, engen, durchgehenden oder oberen und unteren Stil auswählen, können Sie einige zusätzliche Parameter festlegen - <b>Abstand zum Text</b> an allen Seiten (oben, unten, links, rechts).</p>
			<p id="position"><img alt="Bild - Erweiterte Einstellungen: Position" src="../images/image_properties_2.png" /></p>
			<p>Die Registerkarte <b>Position</b> ist nur verfügbar, wenn Sie einen anderen Umbruchstil als Inline auswählen.  Diese Registerkarte enthält die folgenden Parameter, die je nach ausgewähltem Verpackungsstil variieren:</p>
			<ul>
				<li>
					Im <b>horizontalen</b> Bereich können Sie einen der folgenden drei Bildpositionierungstypen auswählen:
					<ul>
						<li><b>Ausrichtung</b> (links, Mitte, rechts) <b>relativ zu</b> Zeichen, Spalte, linkem Rand, Rand, Seite oder rechtem Rand,</li>
						<li><b>Absolute Position</b> gemessen in absoluten Einheiten, d. H. <b>Zentimeter</b> / <b>Punkte</b> / <b>Zoll</b> (abhängig von der auf der Registerkarte <b>Datei</b> -&gt; <b>Erweiterte Einstellungen...</b> angegebenen Option), <b>rechts neben</b> Zeichen, Spalte, linkem Rand, Rand, Seite oder rechtem Rand,</li>
						<li><b>Relative Position</b> gemessen in Prozent relativ zum linken Rand, Rand, Seite oder rechten Rand.</li>
					</ul>
				</li>
				<li>
					Im <b>vertikalen</b> Bereich können Sie einen der folgenden drei Bildpositionierungstypen auswählen:
					<ul>
						<li><b>Ausrichtung</b> (oben, Mitte, unten) <b>relativ zu</b> Linie, Rand, unterem Rand, Absatz, Seite oder oberem Rand,</li>
						<li><b>Absolute Position</b> gemessen in absoluten Einheiten, d. H. <b>Zentimeter</b> / <b>Punkte</b> / <b>Zoll</b> (abhängig von der auf der Registerkarte <b>Datei</b> -&gt; <b>Erweiterte Einstellungen... </b>angegebenen Option) <b>unter</b> Zeile, Rand, unterem Rand, Absatz, Seite oder oberem Rand,</li>
						<li><b>Relative Position</b> gemessen in Prozent <b>relativ zum</b> Rand, unteren Rand, Seite oder oberen Rand.</li>
					</ul>
				</li>
				<li><b>Objekt mit Text verschieben</b> steuert, ob sich das Bild bewegt, während sich der Text, an dem es verankert ist, bewegt.</li>
				<li><b>Überlappungssteuerung</b> zulassen steuert, ob sich zwei Bilder überlappen oder nicht, wenn Sie sie auf der Seite nebeneinander ziehen.</li>
			</ul>
			<p><img alt="Bild - Erweiterte Einstellungen" src="../images/image_properties_3.png" /></p>
			<p>Auf der Registerkarte <b>Alternativer Text</b> können Sie einen <b>Titel</b> und eine <b>Beschreibung</b> angeben, die Personen mit Seh- oder kognitiven Beeinträchtigungen vorgelesen werden, damit sie besser verstehen, welche Informationen im Bild enthalten sind.</p>
		</div>
	</body>
</html>