﻿<!DOCTYPE html>
<html>
	<head>
		<title>Seitenumbrüche einfügen</title>
		<meta charset="utf-8" />
        <meta name="description" content="Fügen Sie einen Seitenumbruch oder halten Sie die Zeilen eines Absatzes zusammen" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Seitenumbrüche einfügen</h1>
			<p>Im <a target="_blank" href="https://www.onlyoffice.com/de/document-editor.aspx" onclick="onhyperlinkclick(this)"><b>Dokumenteneditor</b></a> können Sie einen Seitenumbruch hinzufügen, um eine neue Seite zu starten, eine leere Seite einzufügen und die Paginierungsoptionen anzupassen.</p>
			<p>Um einen Seitenumbruch an der aktuellen Zeigerposition einzufügen, klicken Sie auf das Symbol <span class = "icon icon-pagebreak1"></span> <b>Unterbrechungen</b> auf der Registerkarte <b>Einfügen</b> oder <b>Layout</b> der oberen Symbolleiste oder klicken Sie auf den Pfeil neben diesem Symbol und wählen Sie im Menu die Option <b>Seitenumbruch einfügen</b>. Sie können auch die Tastenkombination Strg + Eingabetaste verwenden.</p>
			<p>Um eine leere Seite einzufügen, klicken Sie auf das Symbol <span class = "icon icon-blankpage"></span> <b>Leere Seite</b> auf der Registerkarte <b>Einfügen</b> der oberen Symbolleiste. Dadurch werden zwei Seitenumbrüche eingefügt, wodurch eine leere Seite erstellt wird.</p>
            <p>Um einen Seitenumbruch vor dem ausgewählten Absatz einzufügen, d. H. um diesen Absatz oben auf einer neuen Seite zu beginnen:</p>
			<ul>
				<li>Klicken Sie mit der rechten Maustaste und wählen Sie im Menü die Option <b>Seitenumbruch vor</b> oder</li>
				<li>Klicken Sie mit der rechten Maustaste, wählen Sie im Menü die Option <b>Absatz - Erweiterte Einstellungen</b> oder verwenden Sie den Link <b>Erweiterte Einstellungen anzeigen</b> in der rechten Seitenleiste, und aktivieren Sie das Kontrollkästchen <b>Seitenumbruch davor</b> auf der Registerkarte Zeilen- und Seitenumbrüche des geöffneten Fensters <b>Absatz - Erweiterte Einstellungen</b>.
				</li>
			</ul>
			<p>Um die Zeilen so zusammenzuhalten, dass nur ganze Absätze auf die neue Seite verschoben werden (d. H. Es gibt keinen Seitenumbruch zwischen den Zeilen innerhalb eines einzelnen Absatzes),</p>
			<ul>
				<li>Klicken Sie mit der rechten Maustaste und wählen Sie  im Menü die Option <b> Linien zusammenhalten</b> oder</li>
				<li>Klicken Sie mit der rechten Maustaste, wählen Sie  im Menü die Option <b>Erweiterte Absatzeinstellungen</b> aus, oder verwenden Sie den Link <b>Erweiterte Einstellungen anzeigen</b> in der rechten Seitenleiste, und aktivieren Sie das Kontrollkästchen <b>Absatz zusammenhalten</b> unter <b>Zeilen- und Seitenumbrüche</b> im geöffneten Fenster <b>Absatz - Erweiterte Einstellungen</b>.</li>
			</ul>
			<p>Auf der Registerkarte Zeilen- und Seitenumbrüche des Fensters <b>Absatz - Erweiterte Einstellungen</b> können Sie zwei weitere Paginierungsoptionen festlegen:</p>
			<ul>
			    <li><b>Mit dem nächsten zusammen</b> - wird verwendet, um einen Seitenumbruch zwischen dem ausgewählten und dem nächsten Absatz zu verhindern.</li>
			    <li><b>Verwaiste Steuerung</b> - ist standardmäßig ausgewählt und wird verwendet, um zu verhindern, dass eine einzelne Zeile des Absatzes (die erste oder letzte) oben oder unten auf der Seite angezeigt wird.</li>
			</ul>
			<p><img alt="Absatz - Erweiterte Einstellungen: Einzüge &amp; Position" src="../images/paradvsettings_breaks.png" /></p>
		</div>
	</body>
</html>