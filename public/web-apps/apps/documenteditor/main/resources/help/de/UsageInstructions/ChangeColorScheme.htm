﻿<!DOCTYPE html>
<html>
	<head>
		<title>Farbschema ändern</title>
		<meta charset="utf-8" />
		<meta name="description" content="<PERSON><PERSON><PERSON><PERSON>, wie man Farbschema in einem Dokument ändert" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Farbschema ändern</h1>
			<p>Farbschemata werden auf das gesamte Dokument angewendet. Im <a href="https://www.onlyoffice.com/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Dokumenteneditor</b></a> können Sie das Erscheinungsbild Ihres Dokuments schnell ändern, da die Farbschemata die Palette <b>Designfarben</b> für verschiedene Dokumentelemente definieren (<a href="../UsageInstructions/FontTypeSizeColor.htm" onclick="onhyperlinkclick(this) ">Schriftart</a>, <a href="../UsageInstructions/BackgroundColor.htm" onclick="onhyperlinkclick(this)">Hintergrund</a>, <a href="../UsageInstructions/InsertTables.htm " onclick="onhyperlinkclick(this)">Tabellen</a>, <a href="../UsageInstructions/InsertAutoshapes.htm" onclick="onhyperlinkclick(this)">AutoFormen</a>, <a href=" ../UsageInstructions/InsertCharts.htm" onclick="onhyperlinkclick(this)">Diagrammen</a>). Wenn Sie einige <b>Designfarben</b> auf die Dokumentelemente angewendet haben und dann ein anderes <b>Farbschema</b> auswählen, ändern sich die angewendeten Farben in Ihrem Dokument entsprechend.</p>
            <p>Um ein Farbschema zu ändern, klicken Sie auf den Abwärtspfeil neben dem Symbol <b>Farbschema ändern</b> <span class = "icon icon-changecolorscheme"></span> in der Registerkarte <b>Startseite</b> auf der Haupt-Symbolleiste und wählen Sie aus den verfügbaren Vorgaben das gewünschte Farbschema aus: <b>New Office</b>, <b>Office</b>, <b>Graustufen</b>, <b>Apex</b>, <b>Aspect</b>, <b>Civic</b>, <b>Concourse</b>, <b>Equity</b>, <b>Flow</b>, <b>Foundry</b>, <b>Median</b>, <b>Metro</b>, <b>Module</b>, <b>Odulent</b>, <b>Oriel</b>, <b>Origin</b>, <b>Paper</b>, <b>Solstice</b>, <b>Technic</b>, <b>Trek</b>, <b>Urban</b>, <b>Verve</b>. Das ausgewählte Farbschema wird in der Liste hervorgehoben.</p>
			<p><img alt="Farbschemata" src="../images/colorscheme.png" /></p>
			<p>Wenn Sie das gewünschte Farbschema ausgewählt haben, können Sie im Fenster Farbpalette die Farben für das jeweilige Dokumentelement auswählen, auf das Sie die Farbe anwenden möchten. Bei den meisten Dokumentelementen können Sie auf das Fenster mit der Farbpalette zugreifen, indem Sie das gewünschte Element markieren und in der rechten Seitenleiste auf das farbige Feld klicken. Für die Schriftfarbe kann dieses Fenster über den Abwärtspfeil neben dem Symbol <b>Schriftfarbe</b> <span class = "icon icon-fontcolor"></span> in der Registerkarte <b>Startseite</b> geöffnet werden. Folgende Farbauswahlmöglichkeiten stehen zur Verfügung:</p>
			<p><img alt="Palette" src="../images/palette.png" /></p>
			<ul>
				<li><b>Designfarben</b> - die Farben, die dem gewählten Farbschema der Tabelle entsprechen.</li>
				<li><b>Standardfarben</b> - die festgelegten Standardfarben. Werden durch das gewählte Farbschema nicht beeinflusst.</li>
				<li>
					<b>Benutzerdefinierte Farbe</b> - klicken Sie auf diese Option, wenn Ihre gewünschte Farbe nicht in der Palette mit verfügbaren Farben enthalten ist. Wählen Sie den gewünschten Farbbereich aus, in dem Sie den vertikalen Farbregler verschieben und die entsprechende Farbe festlegen, in dem Sie den Farbwähler innerhalb des großen quadratischen Farbfelds ziehen. Sobald Sie eine Farbe mit dem Farbwähler bestimmt haben, werden die entsprechenden RGB- und sRGB-Farbwerte in den Feldern auf der rechten Seite angezeigt. Sie können eine Farbe auch anhand des RGB-Farbmodells bestimmen, indem Sie die gewünschten nummerischen Werte in den Feldern <b>R</b>, <b>G</b>, <b>B</b> (Rot, Grün, Blau) festlegen oder den sRGB-Hexadezimalcode in das Feld mit dem <b>#</b>-Zeichen eingeben. Die gewählte Farbe erscheint im Vorschaufeld <b>Neu</b>. Wenn das Objekt vorher mit einer benutzerdefinierten Farbe gefüllt war, wird diese Farbe im Feld <b>Aktuell</b> angezeigt, so dass Sie die Originalfarbe und die Zielfarbe vergleichen könnten. Wenn Sie die Farbe festgelegt haben, klicken Sie auf <b>Hinzufügen</b>.
					<p><img alt="Palette - Benutzerdefinierte Farbe" src="../../../../../../common/main/resources/help/de/images/palette_custom.png" /></p>
					<p>Die benutzerdefinierte Farbe wird auf das ausgewählte Element angewendet und zur Palette <b>Benutzerdefinierte Farbe</b> hinzugefügt.</p>
				</li>
			</ul>
		</div>
	</body>
</html>