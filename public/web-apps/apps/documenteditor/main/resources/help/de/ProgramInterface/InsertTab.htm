﻿<!DOCTYPE html>
<html>
	<head>
		<title>Registerkarte Einfügen</title>
		<meta charset="utf-8" />
        <meta name="description" content="Introducing the Document Editor user interface - Insert tab" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Registerkarte Einfügen</h1>
            <p>Die Registerkarte <b>Einfügen</b> im <a target="_blank" href="https://www.onlyoffice.com/de/document-editor.aspx" onclick="onhyperlinkclick(this)"><b>Dokumenteneditor</b></a> ermöglicht das Hinzufügen einiger Seitenformatierungselemente sowie visueller Objekte und Kommentare.</p>
            <div class="onlineDocumentFeatures">
                <p>Dialogbox Online-Dokumenteneditor:</p>
                <p><img alt="Registerkarte Einfügen" src="../images/interface/inserttab.png" /></p>
            </div>
            <div class="desktopDocumentFeatures">
                <p>Dialogbox Desktop-Dokumenteneditor:</p>
                <p><img alt="Registerkarte Einfügen" src="../images/interface/desktop_inserttab.png" /></p>
            </div>
            <p>Sie können:</p>
            <ul>
                <li>eine <a href="../UsageInstructions/PageBreaks.htm" onclick="onhyperlinkclick(this)">leere Seite</a> einfügen,</li>
                <li><a href="../UsageInstructions/PageBreaks.htm" onclick="onhyperlinkclick(this)">Seitenumbrüche</a>, <a href="../UsageInstructions/SectionBreaks.htm" onclick="onhyperlinkclick(this)">Abschnittsumbrüche</a> und <a href="../UsageInstructions/SetPageParameters.htm#columns" onclick="onhyperlinkclick(this)">Spaltenumbrüche</a> einfügen,</li>
                <li><a href="../UsageInstructions/InsertHeadersFooters.htm" onclick="onhyperlinkclick(this)">Kopf- und Fußzeilen</a> und <a href="../UsageInstructions/InsertPageNumbers.htm" onclick="onhyperlinkclick(this)">Seitenzahlen</a> einfügen,</li>
                <li><a href="../UsageInstructions/InsertTables.htm" onclick="onhyperlinkclick(this)">Tabellen</a>, <a href="../UsageInstructions/InsertImages.htm" onclick="onhyperlinkclick(this)">Bilder</a>, <a href="../UsageInstructions/InsertCharts.htm" onclick="onhyperlinkclick(this)">Diagramme</a> und <a href="../UsageInstructions/InsertAutoshapes.htm" onclick="onhyperlinkclick(this)">Formen</a> einfügen,</li>
                <li><a href="../UsageInstructions/AddHyperlinks.htm" onclick="onhyperlinkclick(this)">Hyperlinks</a> und <a href="../HelpfulHints/CollaborativeEditing.htm#comments" onclick="onhyperlinkclick(this)">Kommentare</a> einfügen,</li>
                <li><a href="../UsageInstructions/InsertTextObjects.htm" onclick="onhyperlinkclick(this)">Textboxen und TextArt Objekte</a>, <a href="../UsageInstructions/InsertEquation.htm" onclick="onhyperlinkclick(this)">Gleichungen</a>, <a href="../UsageInstructions/InsertDropCap.htm" onclick="onhyperlinkclick(this)">Initialbuchstaben</a> und <a href="../UsageInstructions/InsertContentControls.htm" onclick="onhyperlinkclick(this)">Inhaltskontrollen</a> einfügen,</li>
                <li><a href="../UsageInstructions/InsertSmartArt.htm" onclick="onhyperlinkclick(this)">SmartArt-Objekte</a> einfügen.</li>
            </ul>
		</div>
	</body>
</html>