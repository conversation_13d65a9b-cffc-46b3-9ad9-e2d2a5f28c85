﻿<!DOCTYPE html>
<html>
	<head>
		<title>Lesezeichen hinzufügen</title>
		<meta charset="utf-8" />
        <meta name="description" content="Lesezeichen ermöglichen den schnellen Wechsel zu einer bestimmten Position im aktuellen Dokument oder das Hinzufügen eines Links an dieser Position im Dokument" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Lesezeichen hinzufügen</h1>
            <p>Lesezeichen ermöglichen den schnellen Wechsel zu einer bestimmten Position im aktuellen Dokument oder das Hinzufügen eines Links an dieser Position im Dokument.</p>
			<p>Einfügen eines Lesezeichens im <a target="_blank" href="https://www.onlyoffice.com/de/document-editor.aspx" onclick="onhyperlinkclick(this)"><b>Dokumenteneditor</b></a>:</p>
			<ol>
                <li>Legen Sie die Position fest, an der das Lesezeichen eingefügt werden soll:
                <ul>
                    <li>positionieren Sie den Mauszeiger am Beginn der gewünschten Textstelle oder</li>
                    <li>wählen Sie die gewünschte Textpassage aus.</li>
                </ul>
                </li>
                <li>Wechseln Sie in der oberen Symbolleiste auf die Registerkarte <b>Verweise</b>.</li>
                <li>Klicken Sie in der oberen Symbolleiste auf das Symbol <div class = "icon icon-bookmark"></div> <b>Lesezeichen</b>.</li>
				<li>Geben Sie im sich öffnenden Fenster <b>Lesezeichen</b> den <b>Namen des Lesezeichens</b> ein und klicken Sie auf die Schaltfläche <b>Hinzufügen</b> - das Lesezeichen wird der unten angezeigten Lesezeichenliste hinzugefügt.
                <p class="note">Der Name des Lesezeichens sollte mit einem Buchstaben beginnen, er kann jedoch auch Zahlen enthalten. Der Name des Lesezeichens darf keine Leerzeichen enthalten, ein Unterstrich "_" ist jedoch möglich.</p>
                    <p><img alt="Fenster Lesezeichen" src="../images/bookmark_window.png" /></p>
                </li>
			</ol>
            <p>Um zu einem der hinzugefügten Lesezeichen im Dokumenttext zu wechseln:</p>
            <ol>
                <li>Klicken Sie in der oberen Symbolleiste in der Registerkarte <b>Verweise</b> auf das Symbol <div class = "icon icon-bookmark"></div> <b>Lesezeichen</b>.</li>
                <li>Wählen Sie im sich öffnenden Fenster <b>Lesezeichen</b> das Lesezeichen aus, zu dem Sie springen möchten. Um das erforderliche Lesezeichen in der Liste zu finden, können Sie die Liste nach <b>Name</b> oder <b>Position</b> eines Lesezeichens im Dokumenttext sortieren.</li>
                <li>Aktivieren Sie die Option <b>Versteckte Lesezeichen</b>, um ausgeblendete Lesezeichen in der Liste anzuzeigen (z. B. die vom Programm automatisch erstellten Lesezeichen, wenn Sie Verweise auf einen bestimmten Teil des Dokuments hinzufügen. Wenn Sie beispielsweise einen Hyperlink zu einer bestimmten Überschrift innerhalb des Dokuments erstellen, erzeugt der Dokumenteditor automatisch ein ausgeblendetes Lesezeichen für das Ziel dieses Links).</li>
                <li>Klicken Sie auf die Schaltfläche <b>Wechseln zu</b> - der Zeiger wird an der Stelle innerhalb des Dokuments positioniert, an der das ausgewählte Lesezeichen hinzugefügt wurde oder es wird die entsprechende Textpassage ausgewählt.</li>
                <li>
                    Klicken Sie auf die Schaltfläche <b>Link bekommen</b> und ein neues Fenster öffnet sich wo Sie den <b>Copy</b> Schaltfläche drücken koennen um den Link zu der Datei zu kopieren, welches die Buckmarkierungstelle im Dokument spezifiziert.
                    <p><img alt="Fenster Lesezeichen" src="../images/bookmark_window2.png" /></p>    
                    <p class="note">Um den Link mit anderen Benutzern zu teilen, muss auch die entsprechende Zugangskontrolle zu dieser Datei gesetzt werden. Benutzen Sie dazu die <b>Teilen</b> Funktion in die Schaltfläche <b>Zusammenarbeit</b>.
                </li>
                <li>Klicken Sie auf die Schaltfläche <b>Schließen</b>, um das Dialogfenster zu schließen.</li>
            </ol>
            <p>Um ein Lesezeichen zu löschen, wählen Sie den entsprechenden Namen aus der Liste der Lesezeichen aus und klicken Sie auf <b>Löschen</b>.</p>
            <p>Informationen zum Verwenden von Lesezeichen beim Erstellen von Links finden Sie im Abschnitt <a href="../UsageInstructions/AddHyperlinks.htm" onclick="onhyperlinkclick(this)">Hyperlinks hinzufügen</a>.</p>

		</div>
	</body>
</html>