﻿<!DOCTYPE html>
<html>
<head>
    <title>Registerkarte Formulare</title>
    <meta charset="utf-8" />
    <meta name="description" content="Einführung in die Benutzeroberfläche des Dokumenteneditors – Registerkarte Formulare" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Registerkarte Formulare</h1>
        <p class="note">Diese Registerkarte ist nur bei DOCXF-Dateien verfügbar.</p>
        <p>Auf der Registerkarte <b>Formulare</b> können Sie ausfüllbare Formulare wie Vereinbarungen, Anträge oder Umfragen erstellen. Fügen Sie Text- und Formularfelder hinzu, formatieren und konfigurieren Sie sie, um ein ausfüllbares Formular zu erstellen, egal wie komplex es sein muss.</p>
        <div class="onlineDocumentFeatures">
            <p>Dialogbox Online-Dokumenteneditor:</p>
            <p><img alt="Registerkarte Formulare" src="../images/interface/formstab.png" /></p>
         </div>
         <div class="desktopDocumentFeatures">
             <p>Dialogbox Desktop-Dokumenteneditor:</p>
            <p><img alt="Registerkarte Formulare" src="../images/interface/desktop_formstab.png" /></p>
        </div>
        <p>Sie können:</p>
        <ul>
            <li>
                <a href="../UsageInstructions/CreateFillableForms.htm">die folgenden Objekte einfügen und bearbeiten:</a>
                <ul>
                    <li> <a href="../UsageInstructions/CreateFillableForms.htm#textfield">Textfelder</a></li>
                    <li> <a href="../UsageInstructions/CreateFillableForms.htm#combobox">Comboboxen</a></li>
                    <li> <a href="../UsageInstructions/CreateFillableForms.htm#dropdownlist">Drop-Down Liste</a></li>
                    <li> <a href="../UsageInstructions/CreateFillableForms.htm#checkbox">Kontrollkästchen</a></li>
                    <li> <a href="../UsageInstructions/CreateFillableForms.htm#radiobutton">Radiobuttons</a></li>
                    <li> <a href="../UsageInstructions/CreateFillableForms.htm#image">Bilder</a></li>
                    <li> <a href="../UsageInstructions/CreateFillableForms.htm#emailaddress">E-Mail-Adressen</a></li>
                    <li> <a href="../UsageInstructions/CreateFillableForms.htm#phonenumber">Telefonnummern</a></li>
                    <!--<li> <a href="../UsageInstructions/CreateFillableForms.htm#datetime">date and time</a></li>
            <li> <a href="../UsageInstructions/CreateFillableForms.htm#zipcode">zip codes</a></li>
            <li> <a href="../UsageInstructions/CreateFillableForms.htm#creditcard">credit card numbers</a></li>-->
                    <li> <a href="../UsageInstructions/CreateFillableForms.htm#complexfield">komplexe Felder</a></li>
                </ul>
            <li>alle Felder und Hervorhebungseinstellungen löschen,</li>
            <li>durch die Formularfelder mit den Schaltflächen <b>Vorheriges Feld</b> und <b>Nächstes Feld</b> navigieren,</li>
            <li>die resultierenden Formulare in Ihrem Dokument anzeigen,</li>
            <li><a href="../UsageInstructions/CreateFillableForms.htm#managing_roles">Rollen verwalten</a>,</li>
            <li>Formular als ausfüllbare OFORM-Datei speichern.</li>
        </ul>
    </div>
</body>
</html>