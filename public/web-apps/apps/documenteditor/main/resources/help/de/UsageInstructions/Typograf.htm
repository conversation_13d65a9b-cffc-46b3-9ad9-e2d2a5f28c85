﻿<!DOCTYPE html>
<html>
<head>
    <title>Typografie korrigieren</title>
    <meta charset="utf-8" />
    <meta name="description" content="Die Beschreibung des Typograf-Plugins für ONLYOFFICE-Editoren, mit dem die Typografie des Textes korrigiert werden kann" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <link type="text/css" rel="stylesheet" href="../editor.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Typografie korrigieren</h1>
        <p>Wenn Sie die Typografie in Ihrem Text korrigieren müssen, verwenden Sie das <b>Typograf</b>-Plugin, das automatisch geschützte Leerzeichen platziert und zusätzliche entfernt sowie kleinere Tippfehler korrigiert, korrekte Anführungszeichen einfügt, Bindestriche durch Gedankenstriche ersetzt usw.</p>
        <ol>
            <li>Öffnen Sie die Registerkarte <b>Plugins</b> und klicken Sie auf <b>Typograf</b>.</li>
            <li>Klicken Sie auf die Schaltfläche <b>Show advanced settings</b>.</li>
            <li>Wählen Sie den Ort und die Regeln aus, die Sie auf Ihren Text anwenden möchten.</li>
            <li>Wählen Sie den Text aus, den Sie korrigieren möchten.</li>
            <li>Klicken Sie auf die Schaltfläche <b>Correct text</b>.</li>
        </ol>
        <img class="gif" alt="Typograf plugin gif" src="../../images/typograf_plugin.gif" width="600" />
        <p>Weitere Informationen zum Typograf-Plugin und seiner Installation finden Sie auf der <a href="https://www.onlyoffice.com/en/app-directory/typograph">Plugin-Seite</a> in AppDirectory.</p>
    </div>
</body>
</html>