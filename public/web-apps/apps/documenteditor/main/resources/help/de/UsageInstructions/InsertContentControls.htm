﻿<!DOCTYPE html>
<html>
	<head>
		<title>Inhaltssteuerelemente einfügen</title>
		<meta charset="utf-8" />
        <meta name="description" content="Insert content controls to create a form with input fields that can be filled in by other users, or protect some parts of the document from being edited or deleted" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Suche" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Inhaltssteuerelemente einfügen</h1>
            <p>Mit dem ONLYOFFICE <a href="https://www.onlyoffice.com/document-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Dokumenteneditor</b></a> können Sie <b>klassische</b> Inhaltssteuerelemente einfügen, d. h. sie sind vollständig <b>abwärtskompatibel</b> mit Textverarbeitungsprogrammen von Drittanbietern wie Microsoft Word.</p>
            <p>ONLYOFFICE Dokumenteneditor unterstützt die folgenden klassischen Inhaltssteuerelemente: <em>Einfacher Text</em>, <em>Rich-Text</em>, <em>Bild</em>, <em>Kombinationsfeld</em>, <em>Dropdownliste</em>, <em>Datum</em> und <em>Kontrollkästchen</em>.</p>
            <ul>
                <li><em>Einfacher Text</em> ist ein Objekt, das Text enthält, der nicht formatiert werden kann. Einfacher-Text-Inhaltssteuerelemente können nicht mehr als einen Absatz enthalten.</li>
                <li><em>Rich-Text</em> ist ein Objekt, das Text enthält, der formatiert werden kann. Rich-Text-Inhaltssteuerelemente können mehrere Absätze, Listen und Objekte (Bilder, Formen, Tabellen usw.) enthalten.</li>
                <li><em>Bild</em> ist ein Objekt, das ein einzelnes Bild enthält.</li>
                <li><em>Kombinationsfeld</em> ist ein Objekt, das eine Dropdown-Liste mit einer Reihe von Auswahlmöglichkeiten enthält. Es ermöglicht die Auswahl eines der vordefinierten Werte aus der Liste und die Bearbeitung des ausgewählten Werts bei Bedarf.</li>
                <li><em>Dropdownliste</em> ist ein Objekt, das eine Dropdown-Liste mit einer Reihe von Auswahlmöglichkeiten enthält. Es ermöglicht die Auswahl eines der vordefinierten Werte aus der Liste. Der ausgewählte Wert kann nicht bearbeitet werden.</li>
                <li><em>Datum</em> ist ein Objekt, das einen Kalender enthält, der die Auswahl eines Datums ermöglicht.</li>
                <li><em>Kontrollkästchen</em> ist ein Objekt, das die Anzeige von zwei Zuständen ermöglicht: Das Kontrollkästchen ist aktiviert und das Kontrollkästchen ist deaktiviert.</li>
            </ul>
            <h3>Inhaltssteuerelemente hinzufügen</h3>
            <h5>Ein neues Einfacher-Text-Inhaltssteuerelement erstellen</h5>
            <ol>
                <li>Positionieren Sie den Einfügepunkt innerhalb einer Textzeile, in die das Steuerelement eingefügt werden soll<br />oder wählen Sie eine Textpassage aus, die Sie zum Steuerungsinhalt machen wollen.</li>
                <li>Wechseln Sie auf die Registerkarte <b>Einfügen</b> in der oberen Symbolleiste.</li>
                <li>Klicken Sie auf den Pfeil neben dem Symbol <div class = "icon icon-insertccicon"></div> <b>Inhaltssteuerelemente</b>.</li>
                <li>Wählen Sie die Option <b>Einfacher Text</b> aus dem Menü aus.</li>
            </ol>
            <p>Das Inhaltssteuerelement wird am Einfügepunkt innerhalb der vorhandenen Textzeile eingefügt. Ersetzen Sie den Standardtext innerhalb des Inhaltssteuerelements ("Ihr Text hier") durch Ihren eigenen Text: Wählen Sie den Standardtext aus und geben Sie einen neuen Text ein oder kopieren Sie eine Textpassage von einer beliebigen Stelle und fügen Sie sie in das Inhaltssteuerelement ein. Die Einfacher-Text-Inhaltssteuerelemente erlauben keine Zeilenumbrüche und können keine anderen Objekte wie Bilder, Tabellen usw. enthalten.</p>
            <p><img alt="Neues Inhaltssteuerelement für einfachen Text" src="../images/addedcontentcontrol.png" /></p>
            <h5>Ein neues Rich-Text-Inhaltssteuerelement erstellen</h5>
            <ol>
                <li>Positionieren Sie die Einfügemarke am Ende eines Absatzes, nach dem das Steuerelement hinzugefügt werden soll <br />oder wählen Sie einen oder mehrere der vorhandenen Absätze aus, die Sie zum Steuerungsinhalt machen wollen.</li>
                <li>Wechseln Sie auf die Registerkarte <b>Einfügen</b> in der oberen Symbolleiste.</li>
                <li>Klicken Sie auf den Pfeil neben dem Symbol <div class = "icon icon-insertccicon"></div> <b>Inhaltssteuerelemente</b>.</li>
                <li>Wählen Sie die Option <b>Rich Text</b> aus dem Menü aus.</li>
            </ol>
            <p>Das Steuerelement wird in einen neuen Absatz eingefügt. Ersetzen Sie den Standardtext innerhalb des Steuerelements ("Ihr Text hier") durch Ihren eigenen: Wählen Sie den Standardtext aus und geben Sie einen neuen Text ein oder kopieren Sie eine Textpassage von einer beliebigen Stelle und fügen Sie sie in das Inhaltssteuerelement ein. Rich-Text-Inhaltssteuerelemente ermöglichen das Hinzufügen von Zeilenumbrüchen, d. h. sie können mehrere Absätze sowie einige Objekte wie Bilder, Tabellen, andere Inhaltssteuerelemente usw. enthalten.</p>
            <p><img alt="Inhaltssteuerelement für Rich-Text:" src="../images/richtextcontentcontrol.png" /></p>
            <h5>Ein neues Bild-Inhaltssteuerelement erstellen</h5>
            <ol>
                <li>Positionieren Sie die Einfügemarke innerhalb einer Textzeile, in der das Steuerelement hinzugefügt werden soll.</li>
                <li>Wechseln Sie zur Registerkarte <b>Einfügen</b> in der oberen Symbolleiste.</li>
                <li>Klicken Sie auf den Pfeil neben dem Symbol <div class = "icon icon-insertccicon"></div> <b>Inhaltssteuerelemente</b>.</li>
                <li>Wählen Sie die Option <b>Bild</b> aus dem Menü - das Inhaltssteuerelement wird am Einfügepunkt eingefügt.</li>
                <li>Klicken Sie auf das Bildsymbol <div class = "icon icon-image_settings_icon"></div> in der Schaltfläche über dem Rand der Inhaltssteuerung - ein Standard-Dateiauswahlfenster wird geöffnet. Wählen Sie ein auf Ihrem Computer gespeichertes Bild aus und klicken Sie auf <b>Öffnen</b>.</li>
            </ol>
            <p>Das ausgewählte Bild wird im Inhaltssteuerelement angezeigt. Um das Bild zu ersetzen, klicken Sie auf das Bildsymbol <span class = "icon icon-image_settings_icon"></span> in der Schaltfläche über dem Rand der Inhaltssteuerung und wählen Sie ein anderes Bild aus.</p>
            <p><span class = "big big-picturecontentcontrol"></span></p>
            <h5>Ein neues Kombinationsfeld- oder Dropdownliste-Inhaltssteuerelement erstellen</h5>
            <p>Die Inhaltssteuerelemente <em>Kombinationsfeld</em> und <em>Dropdownliste</em> enthalten eine Dropdownliste mit einer Reihe von Auswahlmöglichkeiten. Sie können fast auf die gleiche Weise erstellt werden. Der Hauptunterschied zwischen ihnen besteht darin, dass der ausgewählte Wert in der Dropdownliste nicht bearbeitet werden kann, während der ausgewählte Wert im Kombinationsfeld ersetzt werden kann.</p>
            <ol>
                <li>Positionieren Sie die Einfügemarke innerhalb einer Textzeile, in der das Steuerelement hinzugefügt werden soll.</li>
                <li>Wechseln Sie zur Registerkarte <b>Einfügen</b> in der oberen Symbolleiste.</li>
                <li>Klicken Sie auf den Pfeil neben dem Symbol <div class = "icon icon-insertccicon"></div> <b>Inhaltssteuerelemente</b>.</li>
                <li>Wählen Sie die Option <b>Kombinationsfeld</b> oder <b>Dropdownliste</b> aus dem Menü - das Steuerelement wird am Einfügepunkt eingefügt.</li>
                <li>Klicken Sie mit der rechten Maustaste auf das hinzugefügte Steuerelement und wählen Sie im Kontextmenü die Option <b>Einstellungen des Inhaltssteuerelements</b>.</li>
                <li>
                    Wechseln Sie im geöffneten Fenster <b>Einstellungen des Inhaltssteuerelements</b> je nach ausgewähltem Inhaltssteuerungstyp auf die Registerkarte <b>Kombinationsfeld</b> oder <b>Dropdownliste</b>.
                    <p><img alt="Combo box settings window" src="../images/combo_box_settings.png" /></p>
                </li>
                <li>
                    Um ein neues Listenelement hinzuzufügen, klicken Sie auf die Schaltfläche <b>Hinzufügen</b> und füllen Sie die verfügbaren Felder im geöffneten Fenster aus:
                    <p><img alt="Combo box - adding value" src="../images/comboboxaddvalue.png" /></p>
                    <ol>
                        <li>Geben Sie im Feld <b>Anzeigename</b> den erforderlichen Text an, z. B. <em>Ja</em>, <em>Nein</em>, <em>Anderes</em>. Dieser Text wird im Inhaltssteuerelement innerhalb des Dokuments angezeigt.</li>
                        <li>Standardmäßig entspricht der Text im Feld <b>Wert</b> dem Text, der im Feld <b>Anzeigename</b> eingegeben wurde. Wenn Sie den Text im Feld <b>Wert</b> bearbeiten möchten, beachten Sie, dass der eingegebene Wert für jeden Artikel eindeutig sein muss.</li>
                        <li>Klicken Sie auf die Schaltfläche <b>OK</b>.</li>
                    </ol>
                </li>
                <li>Sie können die Listenelemente mit den Schaltflächen <b>Bearbeiten</b> oder <b>Löschen</b> auf der rechten Seite bearbeiten oder löschen oder die Reihenfolge der Elemente mit den Schaltflächen <b>Aufwärts</b> und <b>Unten</b> ändern.</li>
                <li>Wenn alle erforderlichen Optionen festgelegt sind, klicken Sie auf die Schaltfläche <b>OK</b>, um die Einstellungen zu speichern und das Fenster zu schließen.</li>
            </ol>
            <p><img alt="New combo box content control" src="../images/comboboxcontentcontrol.png" /></p>
            <p>Sie können auf die Pfeilschaltfläche im rechten Teil des hinzugefügten Inhaltssteuerelements <b>Kombinationsfeld</b> oder <b>Dropdownliste</b> klicken, um die Elementliste zu öffnen und das gewünschte Element auszuwählen. Sobald das erforderliche Element aus dem <b>Kombinationsfeld</b> ausgewählt wurde, können Sie den angezeigten Text bearbeiten, indem Sie ihn ganz oder teilweise durch Ihren Text ersetzen. Die <b>Dropdownliste</b> erlaubt keine Bearbeitung des ausgewählten Elements.</p>
            <p><img alt="Combo box content control" src="../images/comboboxcontentcontrol2.png" /></p>
            <h5>Ein neues Datum-Inhaltssteuerelement erstellen</h5>
            <ol>
                <li>Positionieren Sie die Einfügemarke innerhalb des Textes, wo die Inhaltssteuerung hinzugefügt werden soll.</li>
                <li>Wechseln Sie zur Registerkarte <b>Einfügen</b> in der oberen Symbolleiste.</li>
                <li>Klicken Sie auf den Pfeil neben dem Symbol <div class = "icon icon-insertccicon"></div> <b>Inhaltssteuerelemente</b>.</li>
                <li>Wählen Sie die Option <b>Datum</b> aus dem Menü - das Inhaltssteuerelement mit dem aktuellen Datum wird an der Einfügemarke eingefügt.</li>
                <li>Klicken Sie mit der rechten Maustaste auf die hinzugefügte Inhaltssteuerung und wählen Sie im Kontextmenü die Option <b>Einstellungen des Inhaltssteuerelements</b>.</li>
                <li>Wechseln Sie im geöffneten Fenster <b>Einstellungen des Inhaltssteuerelements</b> auf die Registerkarte <b>Datumsformat</b>.
                    <p><img alt="Date settings window" src="../images/datesettings.png" /></p>
                </li>
                <li>Wählen Sie die erforderliche <b>Sprache</b> und wählen Sie das erforderliche Datumsformat in der Liste <b>Datum wie folgt anzeigen</b> aus.</li>
                <li>Klicken Sie auf die Schaltfläche <b>OK</b>, um die Einstellungen zu speichern und das Fenster zu schließen.</li>
            </ol>
            <p><span class = "big big-datecontentcontrol"></span></p>
            <p>Sie können auf die Pfeilschaltfläche im rechten Teil des hinzugefügten Inhaltssteuerelements <b>Datum</b> klicken, um den Kalender zu öffnen und das erforderliche Datum auszuwählen.</p>
            <p><img alt="Date content control" src="../images/datecontentcontrol2.png" /></p>
            <h5>Ein neues Kontrollkästchen-Inhaltssteuerelement erstellen</h5>
            <ol>
                <li>Positionieren Sie die Einfügemarke innerhalb der Textzeile, in der das Inhaltssteuerelement hinzugefügt werden soll.</li>
                <li>Wechseln Sie zur Registerkarte <b>Einfügen</b> in der oberen Symbolleiste.</li>
                <li>Klicken Sie auf den Pfeil neben dem Symbol <div class = "icon icon-insertccicon"></div> <b>Inhaltssteuerelemente</b>.</li>
                <li>Wählen Sie die Option <b>Kontrollkästchen</b> aus dem Menü - das Inhaltssteuerelement wird am Einfügepunkt eingefügt.</li>
                <li>Klicken Sie mit der rechten Maustaste auf die hinzugefügte Inhaltssteuerung und wählen Sie im Kontextmenü die Option <b>Einstellungen des Inhaltssteuerelements</b>.</li>
                <li>
                    Wechseln Sie im geöffneten Fenster <b>Einstellungen des Inhaltssteuerelements</b> auf die Registerkarte <b>Kontrollkästchen</b>.
                    <p><img alt="Check box settings window" src="../images/checkbox_settings.png" /></p>
                </li>
                <li>Klicken Sie auf die Schaltfläche <b>Häkchen-Symbol</b>, um das erforderliche Symbol für das ausgewählte Kontrollkästchen festzulegen, oder auf das <b>Nicht aktiviertes Häkchen</b>, um auszuwählen, wie das deaktivierte Kontrollkästchen aussehen soll. Das Fenster <b>Symbol</b> wird geöffnet. Weitere Informationen zum Arbeiten mit Symbolen finden Sie in <a href="../UsageInstructions/InsertSymbols.htm" onclick="onhyperlinkclick(this)">diesem Artikel</a>.</li>
                <li>Wenn die Symbole festgelegt sind, klicken Sie auf die Schaltfläche <b>OK</b>, um die Einstellungen zu speichern und das Fenster zu schließen.</li>
            </ol>
            <p>Das hinzugefügte Kontrollkästchen wird im deaktivierten Modus angezeigt.</p>
            <p><span class = "icon icon-checkboxcontentcontrol"></span></p>
            <p>Wenn Sie auf das hinzugefügte Kontrollkästchen klicken, wird es mit dem in der Liste <b>Häkchen-Symbol</b> ausgewählten Symbol markiert.</p>
            <p><span class = "icon icon-checkboxcontentcontrol2"></span></p>

            <p class="note">Der Rahmen des Inhaltssteuerelements ist nur sichtbar, wenn das Steuerelement ausgewählt ist. Die Ränder erscheinen nicht auf einer gedruckten Version.</p>

            <h3>Inhaltssteuerelemente verschieben</h3>
            <p>Steuerelemente können an eine andere Stelle im Dokument <b>verschoben</b> werden: Klicken Sie auf die Schaltfläche links neben dem Rahmen des Steuerelements, um das Steuerelement auszuwählen, und ziehen Sie es bei gedrückter Maustaste an die gewünschte Position.</p>
            <p><img alt="Inhaltssteuerelemente verschieben" src="../images/movecontentcontrol.png" /></p>
            <p>Sie können Inhaltssteuerelemente auch <b>kopieren und einfügen</b>: Wählen Sie das entsprechende Steuerelement aus und verwenden Sie die Tastenkombinationen <b>STRG+C/STRG+V</b>.</p>

            <h3>Bearbeiten von Einfacher-Text- und Rich-Text-Inhaltssteuerelementen</h3>
            <p>Text in einfachen Text- und Rich-Text-Inhaltssteuerelementen kann mithilfe der Symbole in der oberen Symbolleiste formatiert werden: Sie können den <a href="../UsageInstructions/FontTypeSizeColor.htm" onclick="onhyperlinkclick(this)">Schriftart, -größe, -farbe</a> anpassen, <a href="../UsageInstructions/DecorationStyles.htm" onclick="onhyperlinkclick(this)">Dekorationsstile</a> und <a href="../UsageInstructions/FormattingPresets.htm" onclick="onhyperlinkclick(this)">Formatierungsvorlagen</a> anwenden. Es ist auch möglich, das Fenster <b>Absatz – Erweiterte Einstellungen</b> zu verwenden, auf das Sie über das Kontextmenü oder die rechte Seitenleiste zugreifen können, um die Texteigenschaften zu ändern. Text in Rich-Text-Inhaltssteuerelementen kann wie normaler Text formatiert werden, d. h. Sie können <a href="../UsageInstructions/LineSpacing.htm" onclick="onhyperlinkclick(this)">Zeilenabstand</a> festlegen, <a href="../UsageInstructions/ParagraphIndents.htm" onclick="onhyperlinkclick(this)">Absatzeinzüge</a> anpassen, <a href="../UsageInstructions/SetTabStops.htm" onclick="onhyperlinkclick(this)">Tabstopps</a> anpassen usw.</p>

            <h3>Einstellungen für Inhaltssteuerelemente ändern</h3>
            <p>Unabhängig davon, welche Art von Inhaltssteuerung ausgewählt ist, können Sie die Inhaltssteuerungseinstellungen in den Abschnitten <b>Allgemein</b> und <b>Sperrung</b> des Fensters <b>Einstellungen des Inhaltssteuerelements</b> ändern.</p>
            <p>Um die Einstellungen der Inhaltssteuerung zu öffnen, können Sie auf folgende Weise vorgehen:</p>
            <ul>
                <li>Wählen Sie das gewünschte Inhaltssteuerelement aus und klicken Sie auf den Pfeil neben dem Symbol <div class = "icon icon-insertccicon"></div> <b>Inhaltssteuerelemente</b> in der oberen Symbolleiste und wählen Sie dann die Option <b>Einstellungen Inhaltssteuerelemente</b> aus dem Menü aus.</li>
                <li>Klicken Sie mit der rechten Maustaste auf eine beliebige Stelle im Inhaltssteuerelement und nutzen Sie die Option <b>Einstellungen Steuerungselement</b> im Kontextmenü.</li>
            </ul>
            <p>Im sich nun öffnenden Fenstern können Sie die folgenden Parameter festlegen:</p>
            <p><img alt="Fenster Einstellungen für Inhaltssteuerelemente" src="../images/ccsettingswindow.png" /></p>
            <ul>
                <li>Legen Sie in den entsprechenden Feldern <b>Titel</b> oder <b>Tag</b> des Steuerelements fest. Der Titel wird angezeigt, wenn das Steuerelement im Dokument ausgewählt wird. Tags werden verwendet, um Inhaltssteuerelemente zu identifizieren, damit Sie im Code darauf verweisen können.</li>
                <li>Wählen Sie aus, ob Sie Steuerelemente mit einem <b>Begrenzungsrahmen</b> anzeigen möchten oder nicht. Wählen Sie die Option <b>Keine</b>, um das Steuerelement ohne Begrenzungsrahmen anzuzeigen. Über die Option <b>Begrenzungsrahmen</b> können Sie die <b>Feldfarbe</b> im untenstehenden Feld auswählen. Klicken Sie auf die Schaltfläche <b>Auf alle anwenden</b>, um die festgelegten <b>Darstellungseinstellungen</b> auf alle Inhaltssteuerelemente im Dokument anzuwenden.</li>
            </ul>
            <p>Im Abschnitt <b>Sperrung</b> können Sie das Inhaltssteuerelement mit der entsprechenden Option vor ungewolltem Löschen oder Bearbeiten schützen:</p>
            <p><img alt="Content Control settings window - Locking" src="../images/ccsettingswindow2.png" /></p>
            <ul>
                <li><b>Inhaltssteuerelement kann nicht gelöscht werden</b> - Aktivieren Sie dieses Kontrollkästchen, um ein Löschen des Steuerelements zu verhindern.</li>
                <li><b>Inhaltssteuerelement kann nicht bearbeitet werden</b> - Aktivieren Sie dieses Kontrollkästchen, um ein Bearbeiten des Steuerelements zu verhindern.</li>
            </ul>
            <p>Für bestimmte Arten von Inhaltssteuerelementen ist auch die dritte Registerkarte verfügbar, die die spezifischen Einstellungen für den ausgewählten Inhaltssteuerelementtyp enthält: <em>Kombinationsfeld</em>, <em>Dropdownliste</em>, <em>Datum</em>, <em>Kontrollkästchen</em>. Diese Einstellungen werden oben in den Abschnitten zum Hinzufügen der entsprechenden Inhaltssteuerelemente beschrieben.</p>
            <p>Klicken Sie im Fenster Einstellungen auf <b>OK</b>, um die Änderungen zu bestätigen.</p>
            <p>Es ist auch möglich, Inhaltssteuerelemente mit einer bestimmten Farbe hervorzuheben. Inhaltssteuerelemente farblich hervorheben:</p>
            <ol>
                <li>Klicken Sie auf die Schaltfläche links neben dem Rahmen des Steuerelements, um das Steuerelement auszuwählen.</li>
                <li>Klicken Sie auf der oberen Symbolleiste auf den Pfeil neben dem Symbol <div class = "icon icon-insertccicon"></div> <b>Inhaltssteuerelemente</b>.</li>
                <li>Wählen Sie die Option <b>Einstellungen hervorheben</b> aus dem Menü aus.</li>
                <li>Wählen Sie die gewünschte Farbe aus den verfügbaren Paletten aus: <b>Themenfarben</b>, <b>Standardfarben</b> oder neue <b>benutzerdefinierte Farbe</b> angeben. Um zuvor angewendete Farbmarkierungen zu entfernen, verwenden Sie die Option <b>Keine Markierung</b>.</li>
            </ol>
            <p>Die ausgewählten Hervorhebungsoptionen werden auf alle Inhaltssteuerelemente im Dokument angewendet.</p>
            <h3>Inhaltssteuerelemente entfernen</h3>
            <p>Um ein Steuerelement zu entfernen ohne den Inhalt zu löschen, klicken Sie auf das entsprechende Steuerelement und gehen Sie vor wie folgt:</p>
            <ul>
                <li>Klicken Sie auf den Pfeil neben dem Symbol <div class = "icon icon-insertccicon"></div> <b>Inhaltssteuerelemente</b> in der oberen Symbolleiste und wählen Sie dann die Option <b>Steuerelement entfernen</b> aus dem Menü aus.</li>
                <li>Klicken Sie mit der rechten Maustaste auf das Steuerelement und wählen Sie die Option <b>Steuerungselement entfernen</b> im Kontextmenü aus.</li>
            </ul>
            <p>Um ein Steuerelement einschließlich Inhalt zu entfernen, wählen Sie das entsprechende Steuerelement aus und drücken Sie die Taste <b>Löschen</b> auf der Tastatur.</p>

        </div>
	</body>
</html>