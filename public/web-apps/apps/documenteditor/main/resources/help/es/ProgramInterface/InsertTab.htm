﻿<!DOCTYPE html>
<html>
	<head>
		<title>Pestaña Insertar</title>
		<meta charset="utf-8" />
        <meta name="description" content="Introducing the Document Editor user interface - Insert tab" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Buscar" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Pestaña Insertar</h1>
            <p>La pestaña de <b>Insertar</b> le permite añadir elementos para editar, así como objetos visuales y comentarios.</p>
            <div class="onlineDocumentFeatures">
                <p>Editor de documentos en línea:</p>
                <p><img alt="Pestaña Insertar" src="../images/interface/inserttab.png" /></p>
            </div>
            <div class="desktopDocumentFeatures">
                <p>Editor de documentos de escritorio:</p>
                <p><img alt="Pestaña Insertar" src="../images/interface/desktop_inserttab.png" /></p>
            </div>
            <p>Al usar esta pestaña podrás:</p>
            <ul>
                <li>insertar una <a href="../UsageInstructions/PageBreaks.htm" onclick="onhyperlinkclick(this)">página en blanco</a>,</li>
                <li>insertar <a href="../UsageInstructions/PageBreaks.htm" onclick="onhyperlinkclick(this)">saltos de página</a>, <a href="../UsageInstructions/SectionBreaks.htm" onclick="onhyperlinkclick(this)">saltos de sección</a> y <a href="../UsageInstructions/SetPageParameters.htm#columns" onclick="onhyperlinkclick(this)">saltos de columnas</a>,</li>
                <li>insertar <a href="../UsageInstructions/InsertHeadersFooters.htm" onclick="onhyperlinkclick(this)">encabezados y pies de página</a> y <a href="../UsageInstructions/InsertPageNumbers.htm" onclick="onhyperlinkclick(this)">números de página</a>,</li>
                <li>insertar <a href="../UsageInstructions/InsertTables.htm" onclick="onhyperlinkclick(this)">tablas</a>, <a href="../UsageInstructions/InsertImages.htm" onclick="onhyperlinkclick(this)"> imágenes </a>, <a href="../UsageInstructions/InsertCharts.htm" onclick="onhyperlinkclick(this)">gráficos</a>, <a href="../UsageInstructions/InsertAutoshapes.htm" onclick="onhyperlinkclick(this)">formas</a>,</li>
                <li>insertar <a href="../UsageInstructions/AddHyperlinks.htm" onclick="onhyperlinkclick(this)">hyperlinks</a>, <a href="../HelpfulHints/CollaborativeEditing.htm#comments" onclick="onhyperlinkclick(this)">comentarios</a>,</li>
                <li>insertar <a href="../UsageInstructions/InsertTextObjects.htm" onclick="onhyperlinkclick(this)">cuadros de texto y Cuadros para Objetos de Arte</a>, <a href="../UsageInstructions/InsertEquation.htm" onclick="onhyperlinkclick(this)">ecuaciones</a>, <a href="../UsageInstructions/InsertDropCap.htm" onclick="onhyperlinkclick(this)">mayúsculas olvidadas</a>, <a href="../UsageInstructions/InsertContentControls.htm" onclick="onhyperlinkclick(this)">controles de contenido</a>.</li>
            </ul>
		</div>
	</body>
</html>