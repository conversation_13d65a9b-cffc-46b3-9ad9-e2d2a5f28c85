﻿<!DOCTYPE html>
<html>
	<head>
		<title>Pestaña de referencias</title>
		<meta charset="utf-8" />
        <meta name="description" content="Introducing the Document Editor user interface - References tab" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Buscar" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Pestaña de referencias</h1>
            <p>La pestaña de <b>Referencias</b> permite gestionar diferentes tipos de referencias: añadir y actualizar una tabla de contenidos, crear y editar notas en el pie de página, insertar hipervínculos.</p>
            <div class="onlineDocumentFeatures">
                <p>Editor de documentos en línea:</p>
                <p><img alt="Pestaña de referencias" src="../images/interface/referencestab.png" /></p>
            </div>
            <div class="desktopDocumentFeatures">
                <p>Editor de documentos de escritorio:</p>
                <p><img alt="Pestaña de referencias" src="../images/interface/desktop_referencestab.png" /></p>
            </div>
            <p>Al usar esta pestaña podrás:</p>
            <ul>
                <li>Crear y actualizar automáticamente <a href="../UsageInstructions/CreateTableOfContents.htm" onclick="onhyperlinkclick(this)">tablas de contenidos</a>,</li>
                <li>Insertar <a href="../UsageInstructions/InsertFootnotes.htm" onclick="onhyperlinkclick(this)">pies de página</a></li>
                <li>Insertar <a href="../UsageInstructions/AddHyperlinks.htm" onclick="onhyperlinkclick(this)">hiperenlaces</a></li>
                <li>Añadir <a href="../UsageInstructions/InsertBookmarks.htm" onclick="onhyperlinkclick(this)">marcadores</a>.</li>
            </ul>
		</div>
	</body>
</html>