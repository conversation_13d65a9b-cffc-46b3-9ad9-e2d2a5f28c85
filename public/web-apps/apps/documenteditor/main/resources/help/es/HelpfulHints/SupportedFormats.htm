﻿<!DOCTYPE html>
<html>
	<head>
		<title>Formatos Soportados de Documentos Electrónicos</title>
		<meta charset="utf-8" />
		<meta name="description" content="The list of document formats supported by Document Editor" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Buscar" type="text" onkeypress="doSearch(event)">
        </div>
		<h1>Formatos Soportados de Documentos Electrónicos</h1>
			<p>Documentos electrónicos representan uno de los archivos infórmaticos más comúnmente utilizados. Gracias a un nivel alto de desarrollo de las redes infórmaticas actuales es más conveniente distribuir documentos de forma electrónica. Debido a una variedad de dispositivos usados para presentación de documentos existen muchos formatos de archivos patentados y abiertos. <b>Document Editor</b> soporta los formatos más populares.</p>
            <p class="note">Mientras usted sube o abra el archivo para edición, se convertirá al formato Office Open XML (DOCX). Se hace para acelerar el procesamiento de archivos y aumentar la interoperabilidad.</p>
            <p>La siguiente tabla contiene los formatos que pueden abrirse para su visualización y/o edición.</p>
            <table>
				<tr>
					<td><b>Formatos</b></td>
					<td><b>Descripción</b></td>
					<td>Ver de forma nativa</td>
                    <td>Ver después de la conversión a OOXML</td>
					<td>Editar de forma nativa</td>
                    <td>Editar después de la conversión a OOXML</td>
				</tr>
                <tr>
                    <td>DjVu</td>
                    <td>Formato de archivo diseñado principalmente para almacenar los documentos escaneados, especialmente para tales que contienen una combinación de texto, imágenes y fotografías</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td></td>
                </tr>
				<tr>
					<td>DOC</td>
					<td>Extensión de archivo para los documentos de texto creados con Microsoft Word</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
					<td>+</td>
				</tr>
                <tr>
                    <td>DOCM</td>
                    <td>Macro-Enabled Microsoft Word Document<br /> Extensión de archivo para documentos generados por Microsoft Word 2007 o versiones superiores con la capacidad de ejecutar macros</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                </tr>
				<tr>
                    <td>DOCX</td>
					<td>Office Open XML<br />Formato de archivo desarrollado por Microsoft basado en XML, comprimido usando la tecnología ZIP se usa para presentación de hojas de cálculo, gráficos, presentaciones y documentos de texto</td>
					<td>+</td>
                    <td></td>
					<td>+</td>
                    <td></td>
				</tr>
                <tr>
                    <td>DOCXF</td>
                    <td>Un formato para crear, editar y colaborar en una plantilla de formulario.</td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                </tr>
                <tr>
                    <td>DOTX</td>
                    <td>Plantilla de documento Word Open XML<br />Formato de archivo comprimido, basado en XML, desarrollado por Microsoft para plantillas de documentos de texto. Una plantilla DOTX contiene ajustes de formato o estilos, entre otros y se puede usar para crear múltiples documentos con el mismo formato.</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                </tr>
                <tr>
                    <td>EPUB</td>
                    <td>Electronic Publication<br />Estándar abierto y gratuito para libros electrónicos creado por el Foro Internacional de Publicación Digital (International Digital Publishing Forum)</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td></td>
                </tr>
                <tr>
                    <td>FB2</td>
                    <td>Una extensión de ebook que permite leer libros en el ordenador o en dispositivos móviles</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                </tr>
                <tr>
                    <td>HTML</td>
                    <td>HyperText Markup Language<br />Lenguaje de marcado principal para páginas web</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                </tr>
				<tr>
					<td>ODT</td>
					<td>Formato de los archivos de texto OpenDocument, un estándar abierto para documentos electrónicos</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
					<td>+</td>
				</tr>
                <tr>
                    <td>OFORM</td>
                    <td>Un formato para rellenar un formulario. Los campos del formulario se pueden rellenar pero los usuarios no pueden cambiar el formato o los parámetros de los elementos del formulario*.</td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                </tr>
                <tr>
                    <td>OTT</td>
                    <td>Plantilla de documento OpenDocument<br />Formato de archivo OpenDocument para plantillas de documentos de texto. Una plantilla OTT contiene ajustes de formato o estilos, entre otros y se puede usar para crear múltiples documentos con el mismo formato.</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                </tr>
                <tr>
                    <td>PDF</td>
                    <td>Formato de documento portátil<br />Es un formato de archivo que se usa para la representación de documentos de manera independiente a la aplicación software, hardware, y sistemas operativos</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td></td>
                </tr>
                <tr>
                    <td>PDF/A</td>
                    <td>Formato de documento portátil / A<br />Una versión ISO estandarizada del Formato de Documento Portátil (PDF por sus siglas en inglés) especializada para su uso en el archivo y la preservación a largo plazo de documentos electrónicos.</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td></td>
                </tr>
				<tr>
					<td>RTF</td>
					<td>Rich Text Format<br />Formato de archivos de documentos desarrollado por Microsoft para intercambio de documentos entre plataformas</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
					<td>+</td>
				</tr>
				<tr>
					<td>TXT</td>
					<td>Extensión de archivo para archivos de texto que normalmente contiene un formateo mínimo</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
					<td>+</td>
				</tr>
                <tr>
                    <td>XML</td>
                    <td>Extensible Markup Language (XML).<br />Un lenguaje de marcado simple y flexible que deriva del SGML (ISO 8879) y está diseñado para almacenar y transmitir datos.</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                </tr>
                <tr>
                    <td>XPS</td>
                    <td>Open XML Paper Specification<br />Formato de documento abierto de diseño fijo desarrollado por Microsoft</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td></td>
                </tr>
            </table>
            <p class="note"><b>*Nota</b>: el formato OFORM es un formato que se utiliza para rellenar un formulario. Por lo tanto, solo se pueden editar los campos del formulario.</p>
            <p>La siguiente tabla contiene los formatos en los que se puede descargar un documento desde el menú <b>Archivo</b> -> <b>Descargar como</b>.</p>
            <table>
                <tr>
                    <td><b>Formato de entrada</b></td>
                    <td><b>Puede descargarse como</b></td>
                </tr>
                <tr>
                    <td>DjVu</td>
                    <td>DjVu, PDF</td>
                </tr>
                <tr>
                    <td>DOC</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
                </tr>
                <tr>
                    <td>DOCM</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
                </tr>
                <tr>
                    <td>DOCX</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
                </tr>
                <tr>
                    <td>DOCXF</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
                </tr>
                <tr>
                    <td>DOTX</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
                </tr>
                <tr>
                    <td>EPUB</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
                </tr>
                <tr>
                    <td>FB2</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
                </tr>
                <tr>
                    <td>HTML</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
                </tr>
                <tr>
                    <td>ODT</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
                </tr>
                <tr>
                    <td>OFORM</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
                </tr>
                <tr>
                    <td>OTT</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
                </tr>
                <tr>
                    <td>PDF</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, OFORM, PDF, RTF, TXT</td>
                </tr>
                <tr>
                    <td>PDF/A</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, OFORM, PDF, RTF, TXT</td>
                </tr>
                <tr>
                    <td>RTF</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
                </tr>
                <tr>
                    <td>TXT</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
                </tr>
                <tr>
                    <td>XML</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT</td>
                </tr>
                <tr>
                    <td>XPS</td>
                    <td>DOCX, DOCXF, DOTX, EPUB, FB2, HTML, ODT, OFORM, OTT, PDF, PDF/A, RTF, TXT, XPS</td>
                </tr>
            </table>
            <p>También puede consultar la matriz de conversión en la página web <a href="https://api.onlyoffice.com/editors/conversionapi#text-matrix" target="_blank" onclick="onhyperlinkclick(this)"><b>api.onlyoffice.com</b></a> para ver la posibilidad de convertir sus documentos a los formatos de archivo más conocidos.</p>
		</div>
	</body>
</html>