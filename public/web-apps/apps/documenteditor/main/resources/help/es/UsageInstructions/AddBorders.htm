﻿<!DOCTYPE html>
<html>
	<head>
		<title>A<PERSON><PERSON> bordes</title>
		<meta charset="utf-8" />
		<meta name="description" content="Add borders to your document selecting their style" />
		<link type="text/css" rel="stylesheet" href="../editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Buscar" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Añadir bordes</h1>
			<p>Para añadir bordes a un párrafo, una página o a un documento,</p>
			<ol>
				<li>Ponga el cursor en el párrafo que usted necesita, o elija varios párrafos usando el ratón, o todo el texto pulsando combinación de teclas <b>Ctrl+A</b>,</li>
				<li>haga clic derecho y elija la opción <b>Ajustes de párrafo avanzados</b> en el menú, o use el enlace <b>Mostrar ajustes avanzados</b> en la barra derecha lateral,</li>
				<li>pase a la sección <b>Bordes y relleno</b> en la ventana <b>Párrafo - ajustes avanzados</b>,</li>
				<li>establezca el valor necesario para <b>Tamaño de borde</b> y elija <b>Color de borde</b>,</li>
				<li>pulse un diagrama disponible o use botones para seleccionar bordes y aplique el estilo seleccionado,</li>
				<li>pulse el botón <b>OK</b>.</li>
			</ol>
			<p><img alt="Ajustes avanzados de párrafo - bordes y relleno" src="../images/paradvsettings_borders.png" /></p>
			<p>Después de añadir los bordes puede fijar los <b>márgenes</b>, es decir, la distancia entre los bordes <b>derecho</b>, <b>izquierdo</b>, <b>superior</b>, <b>inferior</b> y el texto de párrafo dentro ellos.</p>
			<p>Para establecer los valores necesarios, cambie a la pestaña <b>Espaciados internos</b> en la ventana <b>Párrafo - ajustes avanzados</b>:</p>
			<p><img alt="Ajustes avanzados de párrafo - márgenes" src="../images/paradvsettings_margins.png" /></p>
		</div>
	</body>
</html>