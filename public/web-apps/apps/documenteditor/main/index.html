<!DOCTYPE html>
<html>
<head>
    <title>ONLYOFFICE Document Editor</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=IE8"/>
    <meta name="description" content="" />
    <meta name="keywords" content="" />
    <link rel="icon" href="resources/img/favicon.ico" type="image/x-icon" />

    <!-- splash -->

    <style type="text/css">
        .content-theme-dark {
            --skeleton-canvas-content-background: #3a3a3a;
            --skeleton-canvas-page-border: #2a2a2a;
            --skeleton-canvas-line: rgba(255,255,255,.05);
        }

        .loadmask {
            left: 0;
            top: 0;
            position: absolute;
            height: 100%;
            width: 100%;
            overflow: hidden;
            border: none;
            background: #e2e2e2;
            background: var(--canvas-background, #e2e2e2);
            z-index: 1002;
        }

        .loadmask > .brendpanel {
            width: 100%;
            min-height: 28px;
            background: #446995;
            background: var(--toolbar-header-document, #446995);
        }

        .loadmask > .brendpanel > div {
            display: flex;
            align-items: center;
            height: 28px;
        }

        .loadmask > .brendpanel .spacer {
            margin-left: auto;
        }

        .loadmask > .brendpanel .loading-logo {
            padding: 0 24px 0 12px;
            max-width: 200px;
            height: 20px;
        }

        .loadmask > .brendpanel .loading-logo > img {
            display: inline-block;
            max-width: 100px;
            max-height: 20px;
            opacity: 0;
        }

        .loadmask > .brendpanel .rect {
            vertical-align: middle;
            width: 50px;
            height: 12px;
            border-radius: 3px;
            margin: 0 10px;
            background: rgba(255,255,255,.2);
            background: var(--highlight-header-button-hover, rgba(255,255,255,.2));
        }

        .loadmask > .brendpanel .circle {
            vertical-align: middle;
            width: 20px;
            height: 20px;
            border-radius: 20px;
            margin: 0 10px;
            background: rgba(255,255,255,.2);
            background: var(--highlight-header-button-hover, rgba(255,255,255,.2));
        }

        .loadmask > .sktoolbar {
            background: #f1f1f1;
            background: var(--background-toolbar, #f1f1f1);
            border-bottom: 1px solid #cbcbcb;
            border-bottom: var(--scaled-one-px-value, 1px) solid var(--border-toolbar, #cbcbcb);
            height: 46px;
            padding: 10px 6px;
            box-sizing: content-box;
        }

        .loadmask > .sktoolbar ul {
            margin: 0;
            padding: 0;
            white-space: nowrap;
            position: relative;
        }

        .loadmask > .sktoolbar li {
            background: #d8dadc;
            background: var(--highlight-button-hover, #d8dadc);
            border-radius: 3px;
            width: 20px;
            height: 20px;
            display: inline-block;
            margin-right: 6px;
        }

        .loadmask > .sktoolbar li.space {
            background: none;
            width: 0;
        }

        .loadmask > .sktoolbar li.fat {
            position: absolute;
            right: 0;
            top: 0;
            bottom: 0;
            left: 587px;
            width: inherit;
            height: 44px;
        }

        .loadmask > .placeholder {
            background: #fff;
            background: var(--skeleton-canvas-content-background, var(--canvas-content-background, #fff));
            width: 794px;
            margin: 46px auto;
            height: 100%;
            border: 1px solid #bbbec2;
            border: var(--scaled-one-px-value, 1px) solid var(--skeleton-canvas-page-border, var(--canvas-page-border, #bbbec2));
            padding-top: 50px;
        }

        .loadmask > .placeholder > .line {
            height: 15px;
            margin: 30px 80px;
            background: rgba(0,0,0,.05);
            background: var(--skeleton-canvas-line, rgba(0,0,0,.05));
            overflow: hidden;
            position: relative;

            -webkit-animation: flickerAnimation 2s infinite ease-in-out;
            -moz-animation: flickerAnimation 2s infinite ease-in-out;
            -o-animation: flickerAnimation 2s infinite ease-in-out;
            animation: flickerAnimation 2s infinite ease-in-out;
        }

        @keyframes flickerAnimation {
            0%   { opacity:0.5; }
            50%  { opacity:1; }
            100% { opacity:0.5; }
        }
        @-o-keyframes flickerAnimation{
            0%   { opacity:0.5; }
            50%  { opacity:1; }
            100% { opacity:0.5; }
        }
        @-moz-keyframes flickerAnimation{
            0%   { opacity:0.5; }
            50%  { opacity:1; }
            100% { opacity:0.5; }
        }
        @-webkit-keyframes flickerAnimation{
            0%   { opacity:0.5; }
            50%  { opacity:1; }
            100% { opacity:0.5; }
        }
    </style>

    <script>
        // don't add zoom for mobile devices
        // if (!(/android|avantgo|playbook|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od|ad)|iris|kindle|lge |maemo|midp|mmp|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|symbian|treo|up\.(browser|link)|vodafone|wap|windows (ce|phone)|xda|xiino/i.test(navigator.userAgent || navigator.vendor || window.opera)))
        //    document.getElementsByTagName('html')[0].setAttribute('style', 'zoom: ' + 1 / (window.devicePixelRatio < 2 ? window.devicePixelRatio : window.devicePixelRatio / 2) + ';');

        var userAgent = navigator.userAgent.toLowerCase(),
            check = function(regex){ return regex.test(userAgent); },
            stopLoading = false;
        if (!check(/opera/) && (check(/msie/) || check(/trident/))) {
            var m = /msie (\d+\.\d+)/.exec(userAgent);
            if (m && parseFloat(m[1]) < 10.0) {
                document.write('<div class="app-error-panel">' +
                                '<div class="message-block">' +
                                    '<div class="message-inner">' +
                                        '<div class="title">Your browser is not supported.</div>' +
                                        '<div class="text">Sorry, Document Editor is currently only supported in the latest versions of the Chrome, Firefox, Safari or Internet Explorer web browsers.</div>' +
                                    '</div>' +
                                '</div></div>');
                stopLoading = true;
            }
        } else
        if (check(/windows\snt/i)) {
            var re = /chrome\/(\d+)/i.exec(userAgent);
            if (!!re && !!re[1] && !(re[1] > 49)) {
                setTimeout(function () {
                    document.getElementsByTagName('html')[0].className += "winxp";
                },0);
            }
        }

        function getUrlParams() {
            var e,
                a = /\+/g,  // Regex for replacing addition symbol with a space
                r = /([^&=]+)=?([^&]*)/g,
                d = function (s) { return decodeURIComponent(s.replace(a, " ")); },
                q = window.location.search.substring(1),
                urlParams = {};

            while (e = r.exec(q))
                urlParams[d(e[1])] = d(e[2]);

            return urlParams;
        }

        function encodeUrlParam(str) {
            return str.replace(/"/g, '&quot;')
                    .replace(/'/g, '&#39;')
                    .replace(/</g, '&lt;')
                    .replace(/>/g, '&gt;');
        }

        var params = getUrlParams(),
            lang = (params["lang"] || 'en').split(/[\-\_]/)[0],
            logo = params["headerlogo"] ? encodeUrlParam(params["headerlogo"]) : null,
            logoDark = params["headerlogodark"] ? encodeUrlParam(params["headerlogodark"]) : null;

        window.frameEditorId = params["frameEditorId"];
        window.parentOrigin = params["parentOrigin"];
    </script>

    <link rel="stylesheet" type="text/css" href="../../../apps/documenteditor/main/resources/css/app.css">
    <script>

+function init_themes() {
    if ( localStorage.getItem("ui-theme-use-system") == '1' ) {
        localStorage.removeItem("ui-theme-id");
    }

    var objtheme = localStorage.getItem("ui-theme");
    if ( typeof(objtheme) == 'string' &&
            objtheme.startsWith("{") && objtheme.endsWith("}") )
    {
        objtheme = JSON.parse(objtheme);
    }

    var ui_theme_name = objtheme && typeof(objtheme) == 'object' ? objtheme.id :
        typeof(objtheme) == 'string' ? objtheme : localStorage.getItem("ui-theme-id");

    if ( !!ui_theme_name ) {
        if ( !!objtheme && !!objtheme.colors ) {
            var colors = [];
            for ( var c in objtheme.colors ) {
                colors.push('--' + c + ':' + objtheme.colors[c]);
            }

            var style = document.createElement('style');
            style.type = 'text/css';
            style.innerHTML = '.' + ui_theme_name + '{'+ colors.join(';') +';}';
            document.getElementsByTagName('head')[0].appendChild(style);

            window.currentLoaderTheme = objtheme;
        }
    }
}();
</script>
    <script>

if ( window.AscDesktopEditor ) {
    window.desktop = window.AscDesktopEditor;
    desktop.features = {};
    window.native_message_cmd = [];

    window.on_native_message = function (cmd, param) {
        if ( /window:features/.test(cmd) ) {
            var obj = JSON.parse(param);
            if ( obj.singlewindow !== undefined ) {
                desktop.features.singlewindow = obj.singlewindow;
            }
        } else
            window.native_message_cmd[cmd] = param;
    }

    if ( !!window.RendererProcessVariable ) {
        desktop.theme = window.RendererProcessVariable.theme;
    }

    window.desktop.execCommand('webapps:entry', (window.features && JSON.stringify(window.features)) || '');
}

</script>
</head>
<body>
    <script>

function checkScaling() {
    var matches = {
        'pixel-ratio__1_25': "screen and (-webkit-min-device-pixel-ratio: 1.25) and (-webkit-max-device-pixel-ratio: 1.49), " +
                                "screen and (min-resolution: 1.25dppx) and (max-resolution: 1.49dppx)",
        'pixel-ratio__1_5': "screen and (-webkit-min-device-pixel-ratio: 1.5) and (-webkit-max-device-pixel-ratio: 1.74), " +
                                "screen and (min-resolution: 1.5dppx) and (max-resolution: 1.74dppx)",
        'pixel-ratio__1_75': "screen and (-webkit-min-device-pixel-ratio: 1.75) and (-webkit-max-device-pixel-ratio: 1.99), " +
                                "screen and (min-resolution: 1.75dppx) and (max-resolution: 1.99dppx)",
    };

    for (var c in matches) {
        if ( window.matchMedia(matches[c]).matches ) {
            document.body.classList.add(c);
            break;
        }
    }

    if ( !window.matchMedia("screen and (-webkit-device-pixel-ratio: 1.5)," +
                            "screen and (-webkit-device-pixel-ratio: 1)," +
                            "screen and (-webkit-device-pixel-ratio: 2)").matches )
    {
        // don't add zoom for mobile devices
        // if (!(/android|avantgo|playbook|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od|ad)|iris|kindle|lge |maemo|midp|mmp|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|symbian|treo|up\.(browser|link)|vodafone|wap|windows (ce|phone)|xda|xiino/i.test(navigator.userAgent || navigator.vendor || window.opera)))
        //     document.getElementsByTagName('html')[0].setAttribute('style', 'zoom: ' + (1 / window.devicePixelRatio) + ';');
    }
}

checkScaling();

var params = (function() {
    var e,
        a = /\+/g,  // Regex for replacing addition symbol with a space
        r = /([^&=]+)=?([^&]*)/g,
        d = function (s) { return decodeURIComponent(s.replace(a, " ")); },
        q = window.location.search.substring(1),
        urlParams = {};

    while (e = r.exec(q))
        urlParams[d(e[1])] = d(e[2]);

    return urlParams;
})();

var checkLocalStorage = (function () {
    try {
        var storage = window['localStorage'];
        return true;
    }
    catch(e) {
        return false;
    }
})();

if ( window.desktop ) {
    var theme = desktop.theme

    if ( theme ) {
        if ( !theme.id && !!theme.type ) {
            if ( theme.type == 'dark' ) theme.id = 'theme-dark'; else
            if ( theme.type == 'light' ) theme.id = 'theme-classic-light';
        }

        if ( theme.id ) {
            if ( theme.id == 'theme-system' ) {
                localStorage.setItem("ui-theme-use-system", "1");
                localStorage.removeItem("ui-theme-id");
                delete params.uitheme;
            } else {
                localStorage.setItem("ui-theme-id", theme.id);
                localStorage.removeItem("ui-theme-use-system");
            }

            localStorage.removeItem("ui-theme");
        }
    }
}

if ( !!params.uitheme && checkLocalStorage && !localStorage.getItem("ui-theme-id") ) {
    // const _t = params.uitheme.match(/([\w-]+)/g);

    if ( params.uitheme == 'default-dark' )
        params.uitheme = 'theme-dark';
    else
    if ( params.uitheme == 'default-light' )
        params.uitheme = 'theme-classic-light';

    localStorage.removeItem("ui-theme");
}

var ui_theme_name = checkLocalStorage && localStorage.getItem("ui-theme-id") ? localStorage.getItem("ui-theme-id") : params.uitheme;
var ui_theme_type;
if ( !ui_theme_name ) {
    if ( (window.desktop && desktop.theme && desktop.theme.system == 'dark') ||
            (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) )
    {
        ui_theme_name = 'theme-dark';
        ui_theme_type = 'dark';
        checkLocalStorage && localStorage.removeItem("ui-theme");
    }
}
if ( !!ui_theme_name ) {
    document.body.classList.add(ui_theme_name);
}

if ( checkLocalStorage ) {
    let current_theme = localStorage.getItem("ui-theme");
    if ( !!current_theme && /type":\s*"dark/.test(current_theme) || ui_theme_type == 'dark' ) {
        document.body.classList.add("theme-type-dark");

        let content_theme = localStorage.getItem("content-theme");
        if ( content_theme == 'dark' ) {
            document.body.classList.add("content-theme-dark");
        }
    }
}
</script>

    <div id="loading-mask" class="loadmask"><div class="brendpanel" style="display: none;"><div><div class="loading-logo"><img src="../../../apps/common/main/resources/img/header/header-logo_s.svg"></div><div class="spacer"></div><div class="circle"></div></div><div><span class="rect"></span><span class="rect"></span><span class="rect"></span><span class="rect"></span><span class="rect"></span><span class="rect"></span></div></div><div class="sktoolbar" style="display: none;"><ul><li class="compact" style="width: 30px;display: none;"></li><li class="compact space" style="display: none;"></li><li class="compact" style="width: 90px;display: none;"></li><li class="not-compact" style="width: 63px;"></li><li class="space"></li><li style="width: 212px;"></li><li class="space"></li><li style="width: 200px;"></li><li class="space"></li><li style="width: 63px;"></li><li class="fat"></li></ul><ul><li class="compact" style="width: 30px;display: none;"></li><li class="compact space" style="display: none;"></li><li class="compact" style="width: 90px;display: none;"></li><li class="not-compact" style="width: 63px;"></li><li class="space"></li><li style="width: 212px;"></li><li class="space"></li><li style="width: 200px;"></li><li class="space"></li><li style="width: 63px;"></li></ul></div><div class="placeholder" style="display: none;"><div class="line"></div><div class="line"></div><div class="line"></div><div class="line"></div><div class="line"></div><div class="line"></div><div class="line"></div><div class="line"></div><div class="line"></div><div class="line"></div><div class="line"></div><div class="line"></div><div class="line"></div><div class="line"></div><div class="line"></div><div class="line"></div><div class="line"></div><div class="line"></div><div class="line"></div><div class="line"></div></div></div>
    <div id="viewport"></div>

    <script>
        if ( window.desktop ) {
            var logo = document.getElementsByClassName('loading-logo');
            if ( !!logo && logo.length ) {
                logo[0].setAttribute('style','display:none;');
            }
        }

        var params = getUrlParams(),
            notoolbar = params["toolbar"] == 'false',
            compact = params["compact"] == 'true',
            view = params["mode"] == 'view',
            visible = true;

        (compact || view || notoolbar) && document.querySelector('.brendpanel > :nth-child(2)').remove();

        if (compact || view) {
            if (notoolbar) {
                document.querySelector('.brendpanel > :nth-child(1)').remove();
                visible = false;
            } else
                document.querySelector('.brendpanel > :nth-child(1)').style.height = '32px';
        } else if (notoolbar) {
            document.querySelector('.brendpanel > :nth-child(1)').style.height = '28px';
        }

        if (compact) {
            document.querySelectorAll('.not-compact').forEach(function(item){
                item.remove();
            });
            document.querySelectorAll('.compact').forEach(function(item){
                item.style.display = 'inline-block';
            });
            document.querySelector('.fat').style.left = '655px';
        }

        visible && (document.querySelector('.brendpanel').style.display = 'block');
        !(view && (params["toolbar"] !== 'true') || notoolbar) && (document.querySelector('.sktoolbar').style.display = 'block');
        view && (document.querySelector('.placeholder').style.marginTop = '19px');
        document.querySelector('.placeholder').style.display = 'block';

        if (stopLoading) {
            document.body.removeChild(document.getElementById('loading-mask'));
        } else {
            var elem = document.querySelector('.loading-logo img');
            if (elem) {
                (logo || logoDark) && elem.setAttribute('src', /theme-(?:[a-z]+-)?dark(?:-[a-z]*)?/.test(document.body.className) ? logoDark || logo : logo || logoDark);
                elem.style.opacity = 1;
            }
        }
    </script>

    <script>
        window.requireTimeourError = function(){
            var reqerr;

            if ( lang == 'de')      reqerr = 'Die Verbindung ist zu langsam, einige Komponenten konnten nicht geladen werden. Aktualisieren Sie bitte die Seite.';
            else if ( lang == 'es') reqerr = 'La conexión es muy lenta, algunos de los componentes no han podido cargar. Por favor recargue la página.';
            else if ( lang == 'fr') reqerr = 'La connexion est trop lente, certains des composants n\'ons pas pu être chargé. Veuillez recharger la page.';
            else if ( lang == 'ru') reqerr = 'Слишком медленное соединение, не удается загрузить некоторые компоненты. Пожалуйста, обновите страницу.';
            else if ( lang == 'tr') reqerr = 'Bağlantı çok yavaş, bileşenlerin bazıları yüklenemedi. Lütfen sayfayı yenileyin.';
            else reqerr = 'The connection is too slow, some of the components could not be loaded. Please reload the page.';

            return reqerr;
        };

        var requireTimeoutID = setTimeout(function(){
            window.alert(window.requireTimeourError());
            window.location.reload();
        }, 30000);

        var require = {
            waitSeconds: 30,
            callback: function(){
                clearTimeout(requireTimeoutID);
            }
        };
    </script>

    <svg xmlns="http://www.w3.org/2000/svg" width="28" height="60"><symbol id="svg-icon-crypted" viewBox="0 0 20 20"><path fill-rule="evenodd" clip-rule="evenodd" d="M10 3.0005c-3.0001 1.999-6 1.9997-6 1.9997v3.5c0 1.9999.5628 7.4765 5.9996 8.4999C15.4364 15.9767 16 10.5001 16 8.5002v-3.5s-2.9999-.0007-6-1.9997zm4.0232 4.9648l-1.0464-.9301-3.5495 3.9932-2.4792-2.066-.8962 1.0756 3.5208 2.934 4.4505-5.0067z" fill="#fff"/></symbol><symbol id="svg-icon-users" viewBox="0 0 28 20"><path fill-rule="evenodd" clip-rule="evenodd" d="M27 10c0 4.9706-4.0294 9-9 9s-9-4.0294-9-9 4.0294-9 9-9 9 4.0294 9 9zm1 0c0 5.5228-4.4772 10-10 10-2.6253 0-5.0143-1.0116-6.7984-2.6664C10.2212 17.7622 9.1384 18 8 18c-4.4183 0-8-3.5817-8-8s3.5817-8 8-8c1.1384 0 2.2212.2378 3.2016.6664C12.9857 1.0116 15.3747 0 18 0c5.5228 0 10 4.4771 10 10zM10.4512 3.4412A6.9851 6.9851 0 008 3c-3.866 0-7 3.134-7 7 0 1.5218.4856 2.9302 1.3103 4.0786a10.8167 10.8167 0 011.1237-.7596C4.5435 12.6664 6.1317 12 8 12c.0672 0 .134.0009.2006.0026a9.9847 9.9847 0 01-.1513-1.003A2.7009 2.7009 0 018 11c-1.6569 0-3-1.3431-3-3s1.3431-3 3-3c.426 0 .8314.0888 1.1985.249a10.0216 10.0216 0 011.2527-1.8078zM8.767 6.1524A1.9939 1.9939 0 008 6c-1.1046 0-2 .8954-2 2s.8954 2 2 2c0-1.3635.2729-2.6632.767-3.8476zm-.3043 6.8632A6.9148 6.9148 0 008 13c-1.6317 0-3.0435.5836-4.059 1.181a9.8046 9.8046 0 00-.9906.6668C4.2242 16.1743 6.0157 17 8 17a6.9835 6.9835 0 002.4512-.4412 9.9832 9.9832 0 01-1.9885-3.5432z"/></symbol></svg>
    <svg aria-hidden="true" style="position:absolute;width:0;height:0" xmlns="http://www.w3.org/2000/svg" overflow="hidden"><symbol id="svg-icon-accentBorderCallout1" viewBox="0 0 20 20"><path d="M17 6h-6v7h6V6zm1-1v9h-8V5h8zM8 5h1v9H8v-3.715l-4.631 5.052-.737-.676 5.369-5.857V4.999z"/></symbol><symbol id="svg-icon-accentBorderCallout2" viewBox="0 0 20 20"><path d="M18 6h-6v7h6V6zm1-1v9h-8V5h8zM9 5h1v9H9v-4H5.297l-2.858 5.239-.878-.479 3.142-5.761H9v-4z"/></symbol><symbol id="svg-icon-accentBorderCallout3" viewBox="0 0 20 20"><path d="M17 6h-6v7h6V6zm1-1v9h-8V5h8zM8 5h1v9H8v-4H4v3.27l3.325 2.85-.651.759-3.675-3.15v-4.73h5v-4z"/></symbol><symbol id="svg-icon-accentCallout1" viewBox="0 0 20 20"><path d="M12 5h-2v1h2V5zm-1 2h-1v2h1V7zm6 0h1v2h-1V7zm-6 3h-1v2h1v-2zm6 0h1v2h-1v-2zm-5 4v-1h-2v1h2zm1-1h2v1h-2v-1zm5 1v-1h-2v1h2zm-5-9h2v1h-2V5zm5 0h-2v1h2V5zM8 5h1v9H8v-3.715l-4.631 5.052-.737-.676 5.369-5.857V4.999z"/></symbol><symbol id="svg-icon-accentCallout2" viewBox="0 0 20 20"><path d="M9 5h1v9H9v-4H5.297l-2.858 5.239-.878-.479 3.142-5.761H9v-4zM13 5h-2v1h2V5zm-1 2h-1v2h1V7zm6 0h1v2h-1V7zm-6 3h-1v2h1v-2zm6 0h1v2h-1v-2zm-5 4v-1h-2v1h2zm1-1h2v1h-2v-1zm5 1v-1h-2v1h2zm-5-9h2v1h-2V5zm5 0h-2v1h2V5z"/></symbol><symbol id="svg-icon-accentCallout3" viewBox="0 0 20 20"><path d="M8 4h1v9H8V9H4v3.293l3.354 3.353-.707.707-3.646-3.646V8h5V4zM12 4h-2v1h2V4zm-1 2h-1v2h1V6zm6 0h1v2h-1V6zm-6 3h-1v2h1V9zm6 0h1v2h-1V9zm-5 4v-1h-2v1h2zm1-1h2v1h-2v-1zm5 1v-1h-2v1h2zm-5-9h2v1h-2V4zm5 0h-2v1h2V4z"/></symbol><symbol id="svg-icon-actionButtonBackPrevious" viewBox="0 0 20 20"><path d="M17 17H3V3h14v14zm1 1V2H2v16h16z"/><path d="M14 15V5l-9 5 9 5zm-1-1.699L7.059 10 13 6.7v6.601z"/></symbol><symbol id="svg-icon-actionButtonBeginning" viewBox="0 0 20 20"><path d="M17 3H3v14h14V3zm1-1v16H2V2h16z"/><path d="M15 5v10l-8-5 8-5zm-1 1.804L8.887 10 14 13.196V6.804zM5 5h1v10H5V5z"/></symbol><symbol id="svg-icon-actionButtonBlank" viewBox="0 0 20 20"><path d="M17 3H3v14h14V3zm1-1v16H2V2h16z"/></symbol><symbol id="svg-icon-actionButtonDocument" viewBox="0 0 20 20"><path d="M17 3H3v14h14V3zm1-1v16H2V2h16z"/><path d="M11 5H6v10h8V8l-3-3zm0 1.5L12.5 8H11V6.5zm2 7.5H7V6h3v3h3v5z"/></symbol><symbol id="svg-icon-actionButtonEnd" viewBox="0 0 20 20"><path d="M3 17h14V3H3v14zm-1 1V2h16v16H2z"/><path d="M5 15V5l8 5-8 5zm1-1.804L11.113 10 6 6.804v6.392zM15 15h-1V5h1v10z"/></symbol><symbol id="svg-icon-actionButtonForwardNext" viewBox="0 0 20 20"><path fill="none" stroke="var(--color1, #444)" stroke-miterlimit="10" d="M17.5 17.5v-15h-15v15h15z"/><path fill="none" stroke="var(--color1, #444)" stroke-miterlimit="10" d="M13.97 10L6.5 5.85v8.3L13.97 10z"/><path d="M3 3h14v14H3V3zM2 2v16h16V2H2z"/><path d="M6 5v10l9-5-9-5zm1 1.7l5.941 3.3L7 13.301V6.7z"/></symbol><symbol id="svg-icon-actionButtonHelp" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M17 3H3v14h14V3zm1-1v16H2V2h16z"/><path fill-rule="evenodd" d="M8.8088 11.2612v-.5335c-.0062-.4474.0923-.8888.2857-1.2803.0952-.3201.476-.6402.952-1.067.3457-.1982.6406-.492.8568-.8535.1715-.2029.2728-.4677.2856-.7469a.8214.8214 0 00-.0848-.441c-.0682-.1337-.1716-.2405-.296-.3058a1.1204 1.1204 0 00-.399-.259 1.0273 1.0273 0 00-.4578-.061c-.768.0778-1.5133.332-2.1897.7468L7 4.8596c.956-.6066 2.044-.9023 3.1417-.8536.7416-.0466 1.4766.178 2.0944.6402.2514.2317.4512.526.5834.8594.1322.3333.1932.6966.1783 1.061.0186.4829-.0801.9623-.2856 1.387-.3358.5411-.7549 1.0108-1.2377 1.387-.2915.1962-.5494.4492-.7616.7469a.8851.8851 0 00-.1754.3478.954.954 0 00-.015.3991v.4268H8.8088zm-.1904 2.4539a1.186 1.186 0 01.0574-.4627 1.0862 1.0862 0 01.2282-.3908c.116-.1159.2517-.204.399-.259a1.028 1.028 0 01.4579-.0611.9444.9444 0 01.4096.0738c.1308.0549.2505.1387.352.2463.1182.0993.2142.2282.2803.3764.0661.1481.1005.3115.1005.4771-.0096.3637-.1464.7085-.3808.9602a1.0369 1.0369 0 01-.352.2463.9444.9444 0 01-.4096.0738 1.028 1.028 0 01-.4579-.0611 1.1189 1.1189 0 01-.399-.259 1.3243 1.3243 0 01-.231-.4471 1.4259 1.4259 0 01-.0546-.5131z"/></symbol><symbol id="svg-icon-actionButtonHome" viewBox="0 0 20 20"><path d="M17 3H3v14h14V3zm1-1v16H2V2h16z"/><path d="M12 14v-3H8v3h1v-2h2v2h1z"/><path d="M5 9v6h10V5h-2v2l-3-2.5L5 9zm9 1v4H6v-4h8zm-.162-1H6.495l3.53-3.177L13.838 9z"/></symbol><symbol id="svg-icon-actionButtonInformation" viewBox="0 0 20 20"><path d="M17 3v14H3V3h14zM2 2v16h16V2H2z"/><path d="M10 15c2.761 0 5-2.239 5-5s-2.239-5-5-5-5 2.239-5 5 2.239 5 5 5zm6-5c0 3.314-2.686 6-6 6s-6-2.686-6-6 2.686-6 6-6 6 2.686 6 6z"/><path d="M11.007 7.017l-1.999-.009-.009.995 1.999.009.009-.995zM8 9h3v2h1v1H8v-1h1v-1H8V9z"/></symbol><symbol id="svg-icon-actionButtonMovie" viewBox="0 0 20 20"><path d="M17 3H3v14h14V3zm1-1v16H2V2h16z"/><path d="M14 7v1h-1V7H5v4h1v-1h1v3h6v-2h1v1h1V7h-1zM6 9V8h4v1H6zm6 3H8v-2h3V8h1v4zm2-2h-1V9h1v1z"/></symbol><symbol id="svg-icon-actionButtonReturn" viewBox="0 0 20 20"><path d="M17 3H3v14h14V3zm1-1v16H2V2h16z"/><path d="M12.5 5L16 9h-2v1.5c0 2.485-2.015 4.5-4.5 4.5S5 12.985 5 10.5V9h3v1.5c0 .828.672 1.5 1.5 1.5s1.5-.672 1.5-1.5V9H9l3.5-4zm.5 4V8h1l-1.5-1.5L11 8h1v2.5c0 1.381-1.119 2.5-2.5 2.5S7 11.881 7 10.5V10H6v.5C6 12.433 7.567 14 9.5 14s3.5-1.567 3.5-3.5V9z"/></symbol><symbol id="svg-icon-actionButtonSound" viewBox="0 0 20 20"><path d="M17 3H3v14h14V3zm1-1v16H2V2h16z"/><path d="M8 8H5v4h3l3 3V5L8 8zm2 4.5L8.5 11H6V9h2.5L10 7.5v5zM15 9h-3v1h3V9zM13.556 4L12 5.556l.707.707 1.556-1.556L13.556 4zM14.263 14.556L12.707 13l-.707.707 1.556 1.556.707-.707z"/></symbol><symbol id="svg-icon-arc" viewBox="0 0 20 20"><path d="M3.491 4.163C2.91 4.095 2.403 4.058 2 4.039v-1.04c.432.02.98.058 1.611.132 1.582.185 3.701.594 5.827 1.491s4.279 2.291 5.9 4.455C16.815 11.049 17.828 13.632 18 17h-1.035c-.171-3.152-1.119-5.517-2.453-7.299-1.482-1.979-3.465-3.274-5.474-4.121S5.012 4.342 3.492 4.163z"/></symbol><symbol id="svg-icon-bentArrow" viewBox="0 0 20 20"><path d="M13 8v2l3.333-2.5L13 5v2H6c-1.105 0-2 .895-2 2v7h1v-6c0-1.105.895-2 2-2h6zm-1-5l6 4.5-6 4.5V9H7c-.552 0-1 .448-1 1v7H3V9c0-1.657 1.343-3 3-3h6V3z"/></symbol><symbol id="svg-icon-bentConnector5" viewBox="0 0 20 20"><path d="M9 5H2V4h8v11h8v1H9V5z"/></symbol><symbol id="svg-icon-bentConnector5WithArrow" viewBox="0 0 20 20"><path d="M2 4h6v11h7.793l-1.147 1.146.707.707 2.353-2.354-2.353-2.354-.707.707 1.147 1.146H9v-11H2v1z"/></symbol><symbol id="svg-icon-bentConnector5WithTwoArrows" viewBox="0 0 20 20"><path d="M4.646 1.646l.707.707-1.646 1.646H10v11h6.293l-1.146-1.146.707-.707 2.353 2.353-2.353 2.354-.707-.707 1.146-1.147H9v-11H3.707l1.646 1.646-.707.707-2.854-2.854 2.854-2.854z"/></symbol><symbol id="svg-icon-bentUpArrow" viewBox="0 0 20 20"><path d="M14 8v9H3v-3h8V8H8l4.5-5L17 8h-3zm-1 8V7h1.755L12.5 4.495 10.245 7H12v8H4v1h9z"/></symbol><symbol id="svg-icon-bevel" viewBox="0 0 20 20"><path d="M14.293 15H5.707l-2 2h12.586l-2-2zM5 14.293V5.707l-2-2v12.586l2-2zM14.293 5l2-2H3.707l2 2h8.586zm.707.707v8.586l2 2V3.707l-2 2zM2 2h16v16H2V2zm12 12V6H6v8h8z"/></symbol><symbol id="svg-icon-blockArc" viewBox="0 0 20 20"><path d="M17.938 13c.041.328.062.661.062 1h-4c0-2.209-1.791-4-4-4s-4 1.791-4 4H2c0-.339.021-.672.062-1C2.554 9.054 5.92 6 10 6s7.446 3.054 7.938 7zM3.071 13H5.1c.463-2.282 2.481-4 4.9-4s4.437 1.718 4.9 4h2.029C16.444 9.608 13.526 7 10 7s-6.444 2.608-6.929 6z"/></symbol><symbol id="svg-icon-borderCallout1" viewBox="0 0 20 20"><path d="M9 5h9v9H8v-3.715l-4.631 5.052-.737-.676 5.369-5.857V4.999h1zm0 8h8V6H9v7z"/></symbol><symbol id="svg-icon-borderCallout2" viewBox="0 0 20 20"><path d="M10 5h9v9H9v-4H5.297l-2.858 5.239-.878-.479 3.142-5.761H9v-4h1zm0 8h8V6h-8v7z"/></symbol><symbol id="svg-icon-borderCallout3" viewBox="0 0 20 20"><path d="M9 5h9v9H8v-4H4v3.27l3.325 2.85-.651.759-3.675-3.15v-4.73h5v-4h1zm0 8h8V6H9v7z"/></symbol><symbol id="svg-icon-bracePair" viewBox="0 0 20 20"><path d="M13 16.125c.438-.171.81-.493 1.057-.916s.355-.922.307-1.418v-1.556c-.042-.457.049-.918.261-1.318s.533-.721.921-.918c-.388-.198-.71-.518-.921-.918s-.303-.86-.261-1.318V6.305c0-1.361-.454-2.139-1.364-2.431l.182-.875c.695.202 1.324.606 1.818 1.167.382.636.573 1.383.546 2.139v1.458c0 1.167.545 1.75 1.454 1.75v.972c-.909 0-1.454.583-1.454 1.75v1.458c.027.756-.163 1.502-.546 2.139-.507.543-1.132.944-1.818 1.167L13 16.124zM7.4 3.9c-.482.176-.891.507-1.163.942S5.847 5.79 5.9 6.3v1.6c.046.471-.054.944-.287 1.355s-.586.741-1.013.945c.427.203.781.533 1.013.945s.333.885.287 1.355V14c0 1.4.5 2.2 1.5 2.5l-.2.9a4.2965 4.2965 0 01-2-1.2 3.7976 3.7976 0 01-.6-2.2v-1.5c0-1.2-.6-1.8-1.6-1.8v-1c1 0 1.6-.6 1.6-1.8V6.4c-.03-.777.179-1.545.6-2.2a4.8006 4.8006 0 012-1.2l.2.9z"/></symbol><symbol id="svg-icon-callout1" viewBox="0 0 20 20"><path d="M8 5h1v1H8V5zm0 2h1v2H8v1h1v2H8v-1.715l-4.631 5.052-.737-.676 5.369-5.857V6.999zm0 6v1h1v-1H8zm2-8h2v1h-2V5zm8 2h-1v2h1V7zm0 3h-1v2h1v-2zm-6 3v1h-2v-1h2zm3 0h-2v1h2v-1zm3 0v1h-2v-1h2zm-3-8h-2v1h2V5zm1 0h2v1h-2V5z"/></symbol><symbol id="svg-icon-callout2" viewBox="0 0 20 20"><path d="M9 5h1v1H9V5zm0 2h1v2H9V7zm0 3H5.297l-2.858 5.239-.878-.479 3.142-5.761H9v1zm0 0h1v2H9v-2zm0 3h1v1H9v-1zm2-8h2v1h-2V5zm8 2h-1v2h1V7zm0 3h-1v2h1v-2zm-6 3v1h-2v-1h2zm3 0h-2v1h2v-1zm3 0v1h-2v-1h2zm-3-8h-2v1h2V5zm1 0h2v1h-2V5z"/></symbol><symbol id="svg-icon-callout3" viewBox="0 0 20 20"><path d="M8 4h1v1H8V4zm0 2h1v2H8v1H4v3.293l3.354 3.353-.707.707-3.646-3.646V8h5V6zm0 3v2h1V9H8zm0 4v-1h1v1H8zm2-9h2v1h-2V4zm8 2h-1v2h1V6zm0 3h-1v2h1V9zm-6 3v1h-2v-1h2zm3 0h-2v1h2v-1zm3 0v1h-2v-1h2zm-3-8h-2v1h2V4zm1 0h2v1h-2V4z"/></symbol><symbol id="svg-icon-can" viewBox="0 0 20 20"><path d="M10 17c1.228 0 2.321-.253 3.07-.622.813-.401.93-.769.93-.878V5c-.912.607-2.364 1-4 1s-3.088-.393-4-1v10.5c0 .109.117.477.93.878.749.369 1.842.622 3.07.622zM6 3.5c0 .108.113.474.912.873C7.651 4.742 8.741 5 10 5s2.35-.257 3.088-.627c.798-.399.912-.766.912-.873 0-.109-.117-.477-.93-.878C12.321 2.253 11.228 2 10 2s-2.321.252-3.07.622c-.813.401-.93.769-.93.878zm-1 0C5 2.119 7.239 1 10 1c2.694 0 4.89 1.065 4.996 2.399.003.034.004.067.004.101v12c0 1.4-2.3 2.5-5 2.5s-5-1.1-5-2.5v-12z"/></symbol><symbol id="svg-icon-chevron" viewBox="0 0 20 20"><path d="M3 15l5.891-5.5L3 4h8l6 5.5-6 5.5H3zM5.536 5l4.82 4.5-4.82 4.5h5.075l4.909-4.5L10.611 5H5.536z"/></symbol><symbol id="svg-icon-chord" viewBox="0 0 20 20"><path d="M7.333 3.231l9.436 9.435c-.464 1.076-1.185 2.022-2.098 2.755s-1.993 1.232-3.144 1.452c-1.151.22-2.338.156-3.458-.189s-2.139-.957-2.968-1.786c-.829-.828-1.442-1.847-1.786-2.967s-.409-2.307-.189-3.458c.22-1.151.719-2.23 1.452-3.144s1.68-1.634 2.756-2.098zM7.538 2c-1.614.57-3.012 1.625-4.003 3.02S2.007 8.082 2 9.794c0 1.951.695 3.839 1.96 5.324a8.2057 8.2057 0 0010.513 1.685 8.2043 8.2043 0 003.526-4.445L7.537 2z"/></symbol><symbol id="svg-icon-circularArrow" viewBox="0 0 20 20"><path d="M13 11.4h-2.108l2.608 3.825 2.608-3.825H14V9c0-2.761-2.239-5-5-5S4 6.239 4 9v6h1V9c0-2.209 1.791-4 4-4s4 1.791 4 4v2.4zm5-1L13.5 17 9 10.4h3V9c0-1.657-1.343-3-3-3S6 7.343 6 9v7H3V9c0-3.314 2.686-6 6-6s6 2.686 6 6v1.4h3z"/></symbol><symbol id="svg-icon-cloud" viewBox="0 0 20 20"><path d="M12.001 7.224l.749-.192c.079-.02.163-.031.251-.031.552 0 1 .448 1 1v.018l-.02 1.174 1.162-.166c.117-.017.236-.025.359-.025 1.381 0 2.5 1.119 2.5 2.5s-1.119 2.5-2.5 2.5c-.511 0-.983-.152-1.377-.413l-.963-.637-.493 1.044c-.562 1.188-1.769 2.007-3.167 2.007-1.118 0-2.114-.523-2.756-1.343l-.646-.824-.793.683c-.351.302-.805.484-1.304.484-1.105 0-2-.895-2-2 0-.707.366-1.329.924-1.686l.796-.51-.464-.824a1.9847 1.9847 0 01-.256-.98c0-1.105.895-2 2-2 .117 0 .231.01.342.029l.695.119.346-.615c.515-.917 1.495-1.533 2.618-1.533 1.128 0 2.113.623 2.626 1.548l.375.676zM16 8.035a3.5779 3.5779 0 00-1 0V8c0-.208-.032-.409-.091-.598C14.655 6.59 13.896 6 13 6c-.172 0-.34.022-.499.063C11.819 4.833 10.507 4 9 4c-1.498 0-2.804.824-3.49 2.043A3.061 3.061 0 005 6C3.343 6 2 7.343 2 9c0 .535.14 1.037.385 1.471C1.552 11.004 1 11.937 1 13c0 1.657 1.343 3 3 3 .648 0 1.248-.205 1.738-.555.075-.054.148-.111.218-.171.086.109.176.214.271.315C7.047 16.457 8.21 17 9.499 17c1.44 0 2.722-.676 3.545-1.728.205-.262.382-.547.526-.851.275.182.578.327.901.426.325.1.671.154 1.029.154 1.933 0 3.5-1.567 3.5-3.5 0-1.763-1.304-3.222-3-3.465z"/></symbol><symbol id="svg-icon-cloudCallout" viewBox="0 0 20 20"><path d="M5 15v-1H4v2h2v-1H5zM3 17v-1H2v2h2v-1H3zM14.613 7.795l.689.437c.422.268.698.736.698 1.268 0 .828-.672 1.5-1.5 1.5-.232 0-.448-.052-.642-.143l-.75-.356-.488.672c-.365.503-.955.827-1.62.827-.695 0-1.307-.353-1.667-.895L8.5 9.853l-.833 1.252C7.307 11.647 6.694 12 6 12c-1.105 0-2-.895-2-2 0-.752.414-1.408 1.032-1.751l1.128-.625-.886-.937A.9954.9954 0 015.001 6c0-.552.448-1 1-1 .056 0 .111.005.163.013l.748.122.32-.687C7.63 3.591 8.498 3 9.501 3c1.03 0 1.917.623 2.3 1.518l.361.843.871-.286c.146-.048.303-.074.469-.074.828 0 1.5.672 1.5 1.5 0 .189-.035.368-.097.533l-.29.762zM3.764 8A2.9903 2.9903 0 003 10c0 1.657 1.343 3 3 3 .675 0 1.299-.223 1.8-.6.273-.205.51-.456.7-.741.19.285.427.536.7.741.501.377 1.125.6 1.8.6.999 0 1.885-.489 2.43-1.24.324.154.687.24 1.07.24 1.381 0 2.5-1.119 2.5-2.5 0-.889-.464-1.669-1.162-2.112.105-.276.162-.575.162-.888C16 5.119 14.881 4 13.5 4c-.273 0-.535.044-.781.124C12.185 2.875 10.944 2 9.5 2c-1.406 0-2.619.83-3.175 2.026A2.046 2.046 0 006 4c-1.105 0-2 .895-2 2a1.9967 1.9967 0 00.548 1.375c-.295.164-.56.376-.783.625z"/></symbol><symbol id="svg-icon-corner" viewBox="0 0 20 20"><path d="M10 11h8v7H2V2h8v9zm7 1H9V3H3v14h14v-5z"/></symbol><symbol id="svg-icon-cube" viewBox="0 0 20 20"><path d="M6 3L2 7v11h11l4-4V3H6zm.5 1h9l-3 3h-9l3-3zM3 17V8h9v9H3zm10-.5V8l3-3v8.5l-3 3z"/></symbol><symbol id="svg-icon-curvedConnector3" viewBox="0 0 20 20"><path d="M6 4h3.5c1.235 0 2.421.909 2.792 2.171.389 1.323-.131 2.9-2.002 4.235-1.63 1.164-1.859 2.337-1.623 3.139.254.863 1.068 1.454 1.833 1.454H14v1h-3.5c-1.235 0-2.421-.909-2.792-2.171-.389-1.323.131-2.899 2.002-4.235 1.63-1.164 1.859-2.337 1.623-3.14-.254-.863-1.068-1.454-1.833-1.454H6v-1z"/></symbol><symbol id="svg-icon-curvedConnector3WithArrow" viewBox="0 0 20 20"><path d="M9.5 4H6v1h3.5c.765 0 1.579.591 1.833 1.454.236.802.006 1.975-1.623 3.14-1.87 1.336-2.391 2.913-2.002 4.235C8.079 15.091 9.265 16 10.5 16h2.793l-1.146 1.147.707.707 2.353-2.354-2.353-2.353-.707.707L13.293 15H10.5c-.765 0-1.579-.591-1.833-1.454-.236-.802-.006-1.976 1.623-3.139 1.87-1.336 2.391-2.913 2.002-4.235-.371-1.262-1.557-2.171-2.792-2.171z"/></symbol><symbol id="svg-icon-curvedConnector3WithTwoArrows" viewBox="0 0 20 20"><path d="M7.646 1.646l.707.707-1.646 1.646H9.5c1.235 0 2.421.909 2.792 2.171.389 1.323-.131 2.9-2.002 4.235-1.63 1.164-1.859 2.337-1.623 3.139.254.863 1.068 1.454 1.833 1.454h2.793l-1.146-1.146.707-.707 2.353 2.353-2.353 2.354-.707-.707 1.146-1.147H10.5c-1.235 0-2.421-.909-2.792-2.171-.389-1.323.131-2.899 2.002-4.235 1.63-1.164 1.859-2.337 1.623-3.14-.254-.863-1.068-1.454-1.833-1.454H6.707l1.646 1.646-.707.707-2.854-2.854 2.854-2.854z"/></symbol><symbol id="svg-icon-curvedDownArrow" viewBox="0 0 20 20"><path d="M2 14l.022-.521c.104-2.449.913-4.923 1.965-6.295 1.034-1.349 2.364-2.183 3.534-2.183h4c.464 0 .937.2 1.369.492.439.296.878.715 1.284 1.22.776.967 1.461 2.289 1.795 3.788H18l-4 4.5-4-4.5h2.019c-.031-.984-.291-2.103-.783-2.98-.524-.934-1.26-1.52-2.214-1.52-.417 0-.804.199-1.172.611-.374.419-.701 1.029-.971 1.762-.539 1.462-.808 3.782-.857 5.145l-.017.482H2.001zm4.983-7.913c-.666.201-1.473.754-2.202 1.705-.85 1.109-1.546 3.203-1.725 5.208h1.991c.091-1.368.371-3.557.893-4.973.268-.726.609-1.408 1.042-1.94zM11.343 6c.3.303.554.655.765 1.03.624 1.112.914 2.5.914 3.67v.8h-.793L14 13.586l1.815-2.086h-.793v-.65c-.288-1.398-.921-2.632-1.627-3.512-.357-.445-.724-.789-1.063-1.017-.346-.234-.623-.321-.809-.321h-.179z"/></symbol><symbol id="svg-icon-curvedLeftArrow" viewBox="0 0 20 20"><path d="M6 2l.521.022c2.449.104 4.923.913 6.296 1.965C14.165 5.021 15 6.351 15 7.521v4c0 .464-.201.937-.492 1.369-.297.439-.715.878-1.22 1.284-.967.776-2.289 1.461-3.788 1.795V18L5 14l4.5-4v2.019c.984-.031 2.103-.291 2.98-.783.934-.524 1.52-1.26 1.52-2.214 0-.417-.199-.804-.611-1.172-.419-.374-1.029-.701-1.762-.971-1.462-.539-3.782-.808-5.145-.857L6 6.005V2.001zm7.913 4.983c-.201-.666-.754-1.473-1.705-2.202C11.099 3.931 9.005 3.235 7 3.056v1.991c1.368.091 3.557.371 4.973.893.726.268 1.408.609 1.94 1.042zm.087 4.36c-.303.3-.655.554-1.03.765-1.112.624-2.5.914-3.67.914h-.8v-.793L6.414 14 8.5 15.815v-.793h.65c1.398-.288 2.632-.921 3.512-1.627.445-.357.789-.724 1.017-1.063.234-.346.321-.623.321-.809v-.179z"/></symbol><symbol id="svg-icon-curvedRightArrow" viewBox="0 0 20 20"><path d="M14 2l-.521.022c-2.449.104-4.923.913-6.295 1.965-1.349 1.034-2.183 2.364-2.183 3.534v4c0 .464.2.937.492 1.369.296.439.715.878 1.22 1.284.967.776 2.289 1.461 3.788 1.795V18l4.5-4-4.5-4v2.019c-.984-.031-2.103-.291-2.98-.783-.934-.524-1.52-1.26-1.52-2.214 0-.417.199-.804.611-1.172.419-.374 1.029-.701 1.762-.971 1.462-.539 3.782-.808 5.145-.857l.482-.017V2.001zM6.087 6.983c.201-.666.754-1.473 1.705-2.202 1.109-.85 3.203-1.546 5.208-1.725v1.991c-1.368.091-3.557.371-4.973.893-.726.268-1.408.609-1.94 1.042zM6 11.343c.303.3.655.554 1.03.765 1.112.624 2.5.914 3.67.914h.8v-.793L13.586 14 11.5 15.815v-.793h-.65c-1.398-.288-2.632-.921-3.512-1.627-.445-.357-.789-.724-1.017-1.063-.234-.346-.321-.623-.321-.809v-.179z"/></symbol><symbol id="svg-icon-curvedUpArrow" viewBox="0 0 20 20"><path d="M2 6l.022.521c.104 2.449.913 4.923 1.965 6.296C5.021 14.165 6.351 15 7.521 15h4c.464 0 .937-.2 1.369-.492.439-.297.878-.715 1.284-1.22.776-.967 1.461-2.289 1.795-3.788H18L14 5l-4 4.5h2.019c-.031.984-.291 2.103-.783 2.98-.524.934-1.26 1.52-2.214 1.52-.417 0-.804-.199-1.172-.611-.374-.419-.701-1.029-.971-1.762-.539-1.462-.808-3.782-.857-5.145L6.005 6H2.001zm4.983 7.913c-.666-.201-1.473-.754-2.202-1.705C3.931 11.1 3.235 9.005 3.056 7h1.991c.091 1.368.371 3.557.893 4.973.268.726.609 1.408 1.042 1.94zm4.36.087c.3-.303.554-.655.765-1.03.624-1.112.914-2.5.914-3.67v-.8h-.793L14 6.414 15.815 8.5h-.793v.65c-.288 1.398-.921 2.632-1.627 3.512-.357.445-.724.789-1.063 1.017-.346.234-.623.321-.809.321h-.179z"/></symbol><symbol id="svg-icon-decagon" viewBox="0 0 20 20"><path d="M7.214 17h5.572L17 12.786V7.214L12.786 3H7.214L3 7.214v5.572L7.214 17zm5.986 1H6.8L2 13.2V6.8L6.8 2h6.4L18 6.8v6.4L13.2 18z"/><path d="M9 7v6h4V7H9zm3 5h-2V8h2v4zM7 7v1H6v1h1v4h1V7H7z"/></symbol><symbol id="svg-icon-diagStripe" viewBox="0 0 20 20"><path d="M3 12.414L12.414 3H17v.586L3.586 17H3v-4.586zM2 12v6h2L18 4V2h-6L2 12z"/></symbol><symbol id="svg-icon-diamond" viewBox="0 0 20 20"><path d="M10 3.414L16.586 10 10 16.586 3.414 10 10 3.414zM2 10l8 8 8-8-8-8-8 8z"/></symbol><symbol id="svg-icon-dodecagon" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M7.2142 17L3 12.7858V7.2142L7.2142 3h5.5716L17 7.2142v5.5716L12.7858 17H7.2142zM13.2 18H6.8L2 13.2V6.8L6.8 2h6.4L18 6.8v6.4L13.2 18z"/><path fill-rule="evenodd" d="M8 7v1H7v1h1v4h1V7H8zM10 7h3v3h-1V8h-2V7zm1 4v-1h1v1h-1zm0 0v1h2v1h-3v-2h1z"/></symbol><symbol id="svg-icon-donut" viewBox="0 0 20 20"><path d="M10 17c-3.866 0-7-3.134-7-7s3.134-7 7-7 7 3.134 7 7-3.134 7-7 7zm0 1c4.418 0 8-3.582 8-8s-3.582-8-8-8-8 3.582-8 8 3.582 8 8 8z"/><path d="M10 14c-2.209 0-4-1.791-4-4s1.791-4 4-4 4 1.791 4 4-1.791 4-4 4zm0 1c2.761 0 5-2.239 5-5s-2.239-5-5-5-5 2.239-5 5 2.239 5 5 5z"/></symbol><symbol id="svg-icon-doubleWave" viewBox="0 0 20 20"><path d="M3 14.041c.167-.027.334-.041.5-.041.603 0 1.123.202 1.55.433.367.199.724.454 1.029.671.039.028.076.055.113.081.725.515 1.233.815 1.809.815.361 0 .577-.08.737-.174.186-.109.346-.264.594-.521.013-.014.027-.028.041-.042.221-.229.531-.551.932-.8.461-.286 1.01-.463 1.696-.463.688 0 1.232.179 1.683.488.357.245.633.565.817.778.023.027.045.053.066.076.207.237.347.374.524.473.163.091.427.186.911.186.216 0 .502-.107.75-.306.118-.094.197-.186.239-.251.004-.007.008-.013.011-.018V6.961c-.166.026-.333.039-.5.039-.606 0-1.121-.204-1.542-.439-.346-.193-.685-.442-.973-.652-.047-.034-.092-.068-.136-.099-.686-.499-1.202-.809-1.849-.809-.56 0-.803.218-1.293.707l-.026.026c-.51.511-1.266 1.267-2.681 1.267-.688 0-1.232-.179-1.683-.488-.357-.245-.633-.565-.817-.778-.023-.027-.045-.053-.066-.076-.207-.237-.347-.374-.524-.473C4.749 5.095 4.485 5 4.001 5c-.315 0-.61.098-.802.232-.185.129-.198.228-.198.268v8.541zm0 1.018c-.355.08-.711.238-1 .441v-10C2 4.6 3 4 4 4c1.32 0 1.803.557 2.26 1.083C6.669 5.554 7.056 6 8 6c1 0 1.5-.5 2-1s1-1 2-1c1.072 0 1.856.574 2.585 1.108.631.462 1.219.892 1.915.892.164 0 .332-.019.5-.056.345-.075.69-.226 1-.444v10c0 .6-1 1.5-2 1.5-1.32 0-1.803-.557-2.26-1.083-.408-.471-.796-.917-1.74-.917-.984 0-1.452.484-1.927.976C9.582 16.484 9.083 17 8 17c-.984 0-1.765-.556-2.502-1.082C4.837 15.447 4.21 15 3.5 15c-.161 0-.331.021-.5.059z"/></symbol><symbol id="svg-icon-downArrow" viewBox="0 0 20 20"><path d="M8 9H5l5.5 7.333L16 9h-3V3H8v6zm10-1l-7.5 10L3 8h4V2h7v6h4z"/></symbol><symbol id="svg-icon-downArrowCallout" viewBox="0 0 20 20"><path d="M12 10h4V3H5v7h4v4H7.587l2.913 2.648L13.413 14H12v-4zm4 3l-5.5 5L5 13h3v-2H4V2h13v9h-4v2h3z"/></symbol><symbol id="svg-icon-ellipse" viewBox="0 0 20 20"><path d="M10 5c3.9 0 7 2.2 7 5s-3.1 5-7 5-7-2.2-7-5 3.1-5 7-5zm0-1c-4.4 0-8 2.7-8 6s3.6 6 8 6 8-2.7 8-6-3.6-6-8-6z"/></symbol><symbol id="svg-icon-ellipseRibbon" viewBox="0 0 20 20"><path d="M4.566 9l-1.8-3H6v6H2.766l1.8-3zM7 8.436C7.848 8.8 8.897 9 10 9s2.152-.199 3-.564v4.314c0 .1-.082.393-.662.719-.548.308-1.373.531-2.338.531s-1.79-.222-2.338-.531c-.58-.326-.662-.62-.662-.719V8.436zM14 6h3.234l-1.8 3 1.8 3H14V6zm-.024 7H19l-2.4-4L19 5h-7c-.552 0-1 .448-1 1v1.937c-.32.041-.655.063-1 .063s-.68-.022-1-.063V6c0-.552-.448-1-1-1H1l2.4 4L1 13h5.024c.221 1.125 1.917 2 3.976 2s3.754-.875 3.976-2z"/></symbol><symbol id="svg-icon-ellipseRibbon2" viewBox="0 0 20 20"><path d="M15.434 11l1.8 3H14V8h3.234l-1.8 3zM13 11.563c-.848-.364-1.897-.563-3-.563s-2.152.199-3 .563V7.25c0-.1.082-.393.662-.719C8.21 6.223 9.035 6 10 6s1.79.222 2.338.531c.579.326.662.62.662.719v4.313zM6 14H2.766l1.8-3-1.8-3H6v6zm.024-7H1l2.4 4L1 15h7c.552 0 1-.448 1-1v-1.937c.32-.041.655-.063 1-.063s.68.022 1 .063V14c0 .552.448 1 1 1h7l-2.4-4L19 7h-5.024C13.755 5.875 12.059 5 10 5s-3.754.875-3.976 2z"/></symbol><symbol id="svg-icon-flowChartAlternateProcess" viewBox="0 0 20 20"><path d="M14 5H6C4.343 5 3 6.343 3 8v4c0 1.657 1.343 3 3 3h8c1.657 0 3-1.343 3-3V8c0-1.657-1.343-3-3-3zM6 4h8c2.209 0 4 1.791 4 4v4c0 2.209-1.791 4-4 4H6c-2.209 0-4-1.791-4-4V8c0-2.209 1.791-4 4-4z"/></symbol><symbol id="svg-icon-flowChartCollate" viewBox="0 0 20 20"><path d="M5 17l4.5-7L5 3h9l-4.5 7 4.5 7H5zm4.5-8.849L12.168 4H6.831l2.668 4.151zm0 3.698L6.832 16h5.337l-2.668-4.151z"/></symbol><symbol id="svg-icon-flowChartConnector" viewBox="0 0 20 20"><path d="M10 15c2.761 0 5-2.239 5-5s-2.239-5-5-5-5 2.239-5 5 2.239 5 5 5zm6-5c0 3.314-2.686 6-6 6s-6-2.686-6-6 2.686-6 6-6 6 2.686 6 6z"/></symbol><symbol id="svg-icon-flowChartDecision" viewBox="0 0 20 20"><path d="M3 9.5L10.5 5 18 9.5 10.5 14 3 9.5zm1.944 0l5.556 3.334L16.056 9.5 10.5 6.166 4.944 9.5z"/></symbol><symbol id="svg-icon-flowChartDelay" viewBox="0 0 20 20"><path d="M9.999 4c3.252.007 5.976 2.727 6.001 6.005.009 3.285-2.732 5.988-6.001 5.995H4V4h5.999zM10 3H3v14h7c3.813-.007 7.012-3.151 7-7-.027-3.822-3.193-6.993-7-7z"/></symbol><symbol id="svg-icon-flowChartDisplay" viewBox="0 0 20 20"><path d="M7.6 16L4.133 9.5 7.6 3h5.87c.769 0 1.629.524 2.352 1.727.71 1.183 1.179 2.874 1.179 4.773s-.468 3.59-1.179 4.773C15.1 15.476 14.239 16 13.47 16H7.6zM3 9.5L7 17h6.47C15.987 17 18 13.6 18 9.5S15.987 2 13.47 2H7L3 9.5z"/></symbol><symbol id="svg-icon-flowChartDocument" viewBox="0 0 20 20"><path d="M17 5H3v8.679c.307.4.804.93 1.435 1.385.763.55 1.644.936 2.565.936 1.674 0 2.79-.518 4.24-1.192.064-.03.129-.06.194-.09l.03-.014c1.499-.695 3.203-1.485 5.537-1.666V5zm1 9c-2.639 0-4.443.836-6.146 1.625-.077.036-.153.071-.229.106-1.43.665-2.726 1.268-4.626 1.268-2.4 0-4.333-2-5-3v-10h16v10z"/></symbol><symbol id="svg-icon-flowChartExtract" viewBox="0 0 20 20"><path d="M3 16L9.5 3 16 16H3zm1.618-1h9.764L9.5 5.236 4.618 15z"/></symbol><symbol id="svg-icon-flowChartInputOutput" viewBox="0 0 20 20"><path d="M2 16L6.966 4H18l-5.076 12H2zm1.496-1h8.765l4.23-10H7.634L3.496 15z"/></symbol><symbol id="svg-icon-flowChartInternalStorage" viewBox="0 0 20 20"><path d="M16 4H4v12h12V4zm1-1v14H3V3h14z"/><path d="M6 3H5v14h1V3z"/><path d="M17 5H3v1h14V5z"/></symbol><symbol id="svg-icon-flowChartMagneticDisk" viewBox="0 0 20 20"><path d="M15.998 7.985c.382-.162.718-.34 1-.53v6.737c-.049.047-.131.115-.26.199-.371.242-.956.51-1.701.759-1.484.496-3.389.85-4.984.85s-3.501-.354-5-.851c-.495-.164-.924-.337-1.271-.506-.18-.088-.338-.175-.472-.26-.148-.094-.244-.17-.301-.225A.0647.0647 0 003 14.15V7.453c.282.191.618.369 1 .531C5.466 8.607 7.611 9 10 9s4.532-.392 5.998-1.015zM2.439 14.988c.098.08.21.16.336.24.487.308 1.171.607 1.963.87 1.584.525 3.597.902 5.315.902s3.731-.377 5.301-.902c.785-.263 1.459-.562 1.931-.87a3.0184 3.0184 0 00.315-.232c.032-.027.062-.054.09-.082.184-.177.293-.353.308-.524a.561.561 0 000-.082V6.065C17.999 6.043 18 6.022 18 6s-.001-.043-.002-.065v-.027l-.003-.008a1.182 1.182 0 00-.061-.284c-.467-1.371-3.403-2.453-7.084-2.599-.261-.011-.527-.017-.798-.017h-.053c-4.07 0-7.431 1.14-7.935 2.615a1.181 1.181 0 00-.065.37v8.322c0 .103.035.209.101.316.009.014.018.028.028.043.006.009.013.018.02.027.043.058.094.116.154.173.042.04.087.081.136.121zm.564-8.739L3 6.002V6c0-.026.01-.17.292-.419.281-.249.744-.519 1.402-.766C6.003 4.324 7.879 4 10 4h.053c.257 0 .509.005.756.016h.003c1.885.075 3.519.405 4.664.865.575.231.982.477 1.236.703.256.228.285.369.288.403v.02l-.002.028v.212l-.559.378c-1.124.759-3.51 1.374-6.439 1.374-2.93 0-5.317-.616-6.44-1.375l-.557-.376zm-.055 7.843s0 .001.001.002c0 .001.001.002.002.003z"/></symbol><symbol id="svg-icon-flowChartMagneticDrum" viewBox="0 0 20 20"><path d="M12.015 15.998c.162.382.34.718.531 1H5.809c-.047-.049-.115-.131-.199-.26-.242-.371-.51-.956-.759-1.701-.496-1.484-.85-3.389-.85-4.984s.354-3.501.851-5c.164-.495.337-.924.506-1.271.088-.18.175-.338.26-.472.094-.148.17-.244.225-.301A.0647.0647 0 005.851 3h6.697c-.191.282-.369.618-.531 1-.623 1.466-1.016 3.611-1.016 6s.393 4.532 1.015 5.998zM5.012 2.439c-.08.098-.16.21-.24.336-.308.487-.607 1.171-.87 1.963C3.377 6.322 3 8.335 3 10.053s.377 3.731.902 5.301c.262.785.562 1.459.87 1.931a3.0184 3.0184 0 00.232.315c.027.032.054.062.082.09.177.184.353.293.524.308a.561.561 0 00.082 0h8.243c.022.001.043.002.065.002s.043-.001.065-.002h.027l.008-.003c.096-.008.19-.029.284-.061 1.371-.467 2.453-3.403 2.599-7.084.011-.261.017-.527.017-.798v-.053c0-4.07-1.14-7.431-2.615-7.935-.121-.041-.244-.064-.37-.065H5.693c-.103 0-.209.035-.316.101-.014.009-.029.018-.043.028-.009.006-.018.013-.027.02-.058.043-.115.094-.173.154-.04.042-.081.087-.121.136zm8.739.564L14 3c.026 0 .17.01.419.292.249.281.519.744.766 1.402C15.676 6.003 16 7.879 16 10v.053c0 .257-.005.509-.016.756v.003c-.075 1.885-.405 3.519-.865 4.664-.231.575-.477.982-.703 1.236-.228.256-.369.285-.403.288h-.02l-.028-.002h-.212l-.378-.559c-.759-1.124-1.374-3.51-1.374-6.439 0-2.93.616-5.317 1.375-6.44l.376-.557zm-7.843-.055s-.001 0-.002.001c-.001 0-.002.001-.003.002z"/></symbol><symbol id="svg-icon-flowChartMagneticTape" viewBox="0 0 20 20"><path d="M13.829 15H17v1h-7c-3.314 0-6-2.686-6-6s2.686-6 6-6 6 2.686 6 6c0 1.276-.397 2.456-1.075 3.428L13.828 15zm2.497-2c.432-.909.674-1.926.674-3 0-3.866-3.134-7-7-7s-7 3.134-7 7 3.134 7 7 7h8v-3h-2.255c.22-.315.415-.65.581-1z"/></symbol><symbol id="svg-icon-flowChartManualInput" viewBox="0 0 20 20"><path d="M3 9.735L17 5.36V15H3V9.735zM2 9v7h16V4L2 9z"/></symbol><symbol id="svg-icon-flowChartManualOperation" viewBox="0 0 20 20"><path d="M6.721 15L3.388 5h13.196l-3.556 10H6.721zM6 16h7.733L18 4H2l4 12z"/></symbol><symbol id="svg-icon-flowChartMerge" viewBox="0 0 20 20"><path d="M16 3L9.5 16 3 3h13zm-1.618 1H4.618L9.5 13.764 14.382 4z"/></symbol><symbol id="svg-icon-flowChartMultidocument" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M6 5.9977V4h12v8.9897h-2v.9989h-2v.9988c-2.8175 0-4.0474.6344-5.121 1.1882-.8316.4289-1.5695.8095-2.879.8095a4.3045 4.3045 0 01-2.2805-.4385A4.2989 4.2989 0 012 14.9874v-6.992h2V5.9977h2zm1 0h9v5.9932h1v-6.992H7v.9988zm7 1.9977H5v-.9988h10v5.9931h-1V7.9954zm-1 .9989v4.9943a11.1093 11.1093 0 00-4.6 1.2985 4.5042 4.5042 0 01-2.4.6992 3.5038 3.5038 0 01-1.6611-.2779A3.4995 3.4995 0 013 14.6878V8.9943h10z"/></symbol><symbol id="svg-icon-flowChartOffpageConnector" viewBox="0 0 20 20"><path d="M6 9.948l4.5 3.75 4.5-3.75V5H6v4.948zm10 .469L10.5 15 5 10.417V4h11v6.417z"/></symbol><symbol id="svg-icon-flowChartOnlineStorage" viewBox="0 0 20 20"><path d="M5.318 12.682C4.474 11.838 4 10.694 4 9.5s.474-2.338 1.318-3.182S7.307 5 8.5 5h5.964c-.208.278-.397.57-.566.876-.613 1.11-.934 2.357-.934 3.624s.321 2.515.934 3.624c.169.306.358.598.566.876H8.5c-1.193 0-2.338-.474-3.182-1.318zM8.5 4c-1.459 0-2.858.579-3.889 1.611S3 8.041 3 9.5c0 1.459.579 2.858 1.611 3.889S7.041 15 8.5 15H17a6.5024 6.5024 0 01-2.226-2.359c-.531-.962-.809-2.043-.809-3.141A6.5004 6.5004 0 0117 4H8.5z"/></symbol><symbol id="svg-icon-flowChartOr" viewBox="0 0 20 20"><path d="M10 15.981c-3.191-.243-5.738-2.79-5.981-5.981H10v5.981zM4.019 9C4.262 5.809 6.809 3.262 10 3.019V9H4.019zm12.962 1c-.243 3.191-2.79 5.738-5.981 5.981V10h5.981zm0-1H11V3.019c3.191.243 5.738 2.79 5.981 5.981zm1.003 0c-.257-3.909-3.509-7-7.484-7C6.358 2 3 5.358 3 9.5S6.358 17 10.5 17c3.974 0 7.226-3.091 7.484-7H18V9h-.016z"/></symbol><symbol id="svg-icon-flowChartPredefinedProcess" viewBox="0 0 20 20"><path d="M17 5H3v10h14V5zm1-1v12H2V4h16z"/><path d="M5 4H4v12h1V4zM16 4h-1v12h1V4z"/></symbol><symbol id="svg-icon-flowChartPreparation" viewBox="0 0 20 20"><path d="M7.378 14L3.343 9.5 7.378 5h5.244l4.035 4.5-4.035 4.5H7.378zM2 9.5L6.932 15h6.137l4.932-5.5L13.069 4H6.932L2 9.5z"/></symbol><symbol id="svg-icon-flowChartProcess" viewBox="0 0 20 20"><path d="M17 5H3v10h14V5zm1-1v12H2V4h16z"/></symbol><symbol id="svg-icon-flowChartPunchedCard" viewBox="0 0 20 20"><path d="M3 8.448L6.417 5H17v10H3V8.448zm-1-.412V16h16V4H6L2 8.036z"/></symbol><symbol id="svg-icon-flowChartPunchedTape" viewBox="0 0 20 20"><path d="M3 12.594V6.003c.321.163.65.301.972.418C5.001 6.794 6.117 7 7 7c1.45 0 2.503-.565 3.376-1.033.053-.028.105-.057.157-.084.907-.484 1.724-.882 2.967-.882 1.106 0 1.814.219 2.338.501.435.235.78.53 1.162.892v6.57c-.953-.558-2.121-.963-3.5-.963-1.737 0-2.915.589-3.916 1.09l-.032.016c-.992.496-1.808.894-3.053.894-1.391 0-2.068-.232-2.486-.482-.339-.203-.542-.42-.845-.743a12.035 12.035 0 00-.17-.18zM2 13c.127.127.246.254.363.379C3.167 14.238 3.881 15 6.5 15 8 15 9 14.5 10 14s2-1 3.5-1c1.418 0 2.585.503 3.5 1.152.376.266.709.557 1 .848V5.967c-.013-.013-.027-.026-.04-.039C16.973 4.973 15.967 4 13.5 4c-2 0-2.59.547-3.565 1.068C9.045 5.544 8.193 6 7 6c-1.123 0-2.805-.402-4-1.142-.402-.249-.748-.536-1-.858v9z"/></symbol><symbol id="svg-icon-flowChartSort" viewBox="0 0 20 20"><path d="M6.357 10h8.287l-4.143-4.52L6.358 10zM16 10v1l-5.5 6L5 11v-1l5.5-6 5.5 6zm-9.643 1l4.143 4.52L14.643 11H6.356z"/></symbol><symbol id="svg-icon-flowChartSummingJunction" viewBox="0 0 20 20"><path d="M14.729 15.436L10.5 11.207l-4.229 4.229C7.408 16.411 8.885 17 10.5 17s3.092-.589 4.229-1.564zm.707-.707C16.411 13.592 17 12.115 17 10.5s-.589-3.092-1.564-4.229L11.207 10.5l4.229 4.229zm-.707-9.165C13.592 4.589 12.115 4 10.5 4s-3.092.589-4.229 1.564L10.5 9.793l4.229-4.229zm-9.165.707C4.589 7.408 4 8.885 4 10.5s.589 3.092 1.564 4.229L9.793 10.5 5.564 6.271zM18 10.5c0 4.142-3.358 7.5-7.5 7.5S3 14.642 3 10.5C3 6.358 6.358 3 10.5 3S18 6.358 18 10.5z"/></symbol><symbol id="svg-icon-flowChartTerminator" viewBox="0 0 20 20"><path d="M3.934 12.099C3.328 11.531 3 10.774 3 10s.327-1.531.934-2.099C4.542 7.331 5.381 7 6.267 7h7.467c.887 0 1.725.331 2.333.901.606.568.934 1.325.934 2.099s-.327 1.531-.934 2.099c-.608.57-1.447.901-2.333.901H6.267c-.886 0-1.725-.331-2.333-.901zm-.684.729c.8.75 1.885 1.172 3.017 1.172h7.467c1.132 0 2.217-.421 3.017-1.172s1.25-1.767 1.25-2.828-.449-2.078-1.25-2.828S14.866 6 13.734 6H6.267C5.135 6 4.05 6.421 3.25 7.172S2 8.94 2 10c0 1.061.45 2.078 1.25 2.828z"/></symbol><symbol id="svg-icon-foldedCorner" viewBox="0 0 20 20"><path d="M16 12V4H4v12h8v-4h4zm1 0v2l-3 3H3V3h14v9zm-1 1h-3v3h.6l2.4-2.4V13z"/></symbol><symbol id="svg-icon-frame" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M17 3H3v14h14V3zM2 2v16h16V2H2z"/><path fill-rule="evenodd" d="M15 5H5v10h10V5zM4 4v12h12V4H4z"/></symbol><symbol id="svg-icon-halfFrame" viewBox="0 0 20 20"><path d="M5 5v10l-3 3V2h16l-3 3H5zm-1 9.586V4h10.586l1-1H3v12.586l1-1z"/></symbol><symbol id="svg-icon-heart" viewBox="0 0 20 20"><path d="M15.902 10.516l.01-.01c1.451-1.488 1.451-3.912 0-5.4-1.438-1.474-3.758-1.474-5.196 0L10 5.84l-.716-.734c-1.438-1.474-3.758-1.474-5.196 0-1.451 1.488-1.451 3.912 0 5.4l.009.009.002.002L10 16.568l5.902-6.052zM10 18l-6.627-6.796c-1.83-1.877-1.83-4.92 0-6.796 1.773-1.818 4.614-1.875 6.454-.17.059.055.117.111.174.17.057-.058.115-.115.174-.17 1.84-1.705 4.68-1.649 6.454.17 1.83 1.877 1.83 4.92 0 6.796l-.011.011L10.001 18z"/></symbol><symbol id="svg-icon-heptagon" viewBox="0 0 20 20"><path d="M7.003 17l-3.924-5.108 1.399-6.2 5.544-2.575 5.197 2.786 1.685 6.021L13.097 17H7.003zM2 12.129L6.51 18h7.087L18 12.129l-1.933-6.906L10.054 2 3.611 4.993 2 12.13z"/><path d="M11 7H8v1h3v1h1V7h-1zM11 9h-1v2h1V9zM10 11H9v2h1v-2z"/></symbol><symbol id="svg-icon-hexagon" viewBox="0 0 20 20"><path d="M6.531 16l-3.383-6 3.383-6h6.74l3.566 6-3.566 6h-6.74zM2 10l3.947 7h7.893L18 10l-4.16-7H5.947L2 10z"/></symbol><symbol id="svg-icon-homePlate" viewBox="0 0 20 20"><path d="M4 5v9h7.367l4.257-4.5L11.367 5H4zm7.797-1L17 9.5 11.797 15H3V4h8.797z"/></symbol><symbol id="svg-icon-horizontalScroll" viewBox="0 0 20 20"><path d="M4 7c.552 0 1 .448 1 1s-.448 1-1 1-1-.448-1-1 .448-1 1-1zm1-1H4c-1.105 0-2 .895-2 2v8c0 1.105.895 2 2 2s2-.895 2-2h10c1.105 0 2-.895 2-2V5c0-1.105-.895-2-2-2s-2 .895-2 2v1H5zm12-1c0 .552-.448 1-1 1s-1-.448-1-1 .448-1 1-1 1 .448 1 1zm0 2v7c0 .552-.448 1-1 1H6V7h11zM4 10c.364 0 .706-.097 1-.268V16c0 .552-.448 1-1 1s-1-.448-1-1V9.732c.294.17.636.268 1 .268z"/></symbol><symbol id="svg-icon-irregularSeal1" viewBox="0 0 20 20"><path d="M9.199 11.387L11 14.389v-2.237l3.802-.634-2.321-1.161 2.048-2.731L12 8.239V5.608l-1.908 3.179L8 5.997v2H5l2.731 2.048-1.428.952h1.978l-.435 1.74 1.353-1.353zM6 16l1-4H3l3-2-4-3h5V3l3 4 3-5v4.97L17 6l-3 4 4 2-6 1v5l-3-5-3 3z"/></symbol><symbol id="svg-icon-irregularSeal2" viewBox="0 0 20 20"><path d="M7.03 12.617v2.956l2.968-2.99 1.157 1.157-.599-2.395 3.827 1.531L13.132 11h1.566l-1.5-1 1.5-1h-1.174l1.572-3.929-3.095 1.547V5.303l-.658.986-1.342-.671v3L6.74 6.988 7.431 9H4.957l2.479 1.894-1.648 2.133 1.242-.41zM2 8h4.03L5 5l4 2V4l2 1 2-3v3l4-2-2 5h3l-3 2 3 2h-3l2 3-5-2 1 4-3-3-3.97 4v-4L3 15l3.03-3.922L2 8z"/></symbol><symbol id="svg-icon-leftArrow" viewBox="0 0 20 20"><path d="M11 8V5l-7.333 5.5L11 16v-3h6V8h-6zm1 10L2 10.5 12 3v4h6v7h-6v4z"/></symbol><symbol id="svg-icon-leftArrowCallout" viewBox="0 0 20 20"><path d="M10 12v4h7V5h-7v4H6V7.587L3.351 10.5 6 13.413V12h4zm-3 4l-5-5.5L7 5v3h2V4h9v13H9v-4H7v3z"/></symbol><symbol id="svg-icon-leftBrace" viewBox="0 0 20 20"><path d="M11.4 3.9c-.482.176-.891.507-1.163.942S9.847 5.79 9.9 6.3v1.6c.046.471-.054.944-.287 1.355s-.586.741-1.013.945c.427.203.781.533 1.013.945s.333.885.287 1.355V14c0 1.4.5 2.2 1.5 2.5l-.2.9a4.2965 4.2965 0 01-2-1.2 3.7976 3.7976 0 01-.6-2.2v-1.5c0-1.2-.6-1.8-1.6-1.8v-1c1 0 1.6-.6 1.6-1.8V6.4c-.03-.777.179-1.545.6-2.2a4.8006 4.8006 0 012-1.2l.2.9z"/></symbol><symbol id="svg-icon-leftBracket" viewBox="0 0 20 20"><path d="M10.001 16.998a2.0006 2.0006 0 01-2-2V6a2.0006 2.0006 0 012-2H12V3h-1.999c-.398-.014-.794.054-1.164.2s-.706.366-.988.648c-.281.281-.502.618-.647.988s-.214.766-.2 1.164v8.998c-.014.398.054.794.2 1.164s.366.707.647.988.618.502.988.648.766.214 1.164.2H12v-1h-1.999z"/></symbol><symbol id="svg-icon-leftRightArrow" viewBox="0 0 20 20"><path d="M13 13v2.149l3.719-4.649L13 5.851V8H7V5.851L3.281 10.5 7 15.149V13h6zm-5 5l-6-7.5L8 3v4h4V3l6 7.5-6 7.5v-4H8v4z"/></symbol><symbol id="svg-icon-leftRightArrowCallout" viewBox="0 0 20 20"><path d="M8 12H4v.37L2.338 10.5 4 8.63V9h4V4h4v5h4v-.37l1.662 1.87L16 12.37V12h-4v5H8v-5zm-7-1.5L5 15v-2h2v5h6v-5h2v2l4-4.5L15 6v2h-2V3H7v5H5V6l-4 4.5z"/></symbol><symbol id="svg-icon-leftRightUpArrow" viewBox="0 0 20 20"><path d="M10.5 3L14 7h-2v5h3v-2l4 3.5-4 3.5v-2H6v2l-4-3.5L6 10v2h3V7H7l3.5-4zm1.296 3L10.5 4.519 9.204 6H10v7H5v-.796L3.519 13.5 5 14.796V14h11v.796l1.481-1.296L16 12.204V13h-5V6h.796z"/></symbol><symbol id="svg-icon-leftUpArrow" viewBox="0 0 20 20"><path d="M13 12H7v-1.755L4.495 12.5 7 14.755V13h7V6h1.755L13.5 3.495 11.245 6H13v6zM9 7l4.5-5L18 7h-3v7H8v3l-5-4.5L8 8v3h4V7H9z"/></symbol><symbol id="svg-icon-lightningBolt" viewBox="0 0 20 20"><path d="M8.933 3.3l2.133 2.8-1.493.6 4.053 3.9-.96.4 2.347 4-3.307-1.9 1.28-.6-5.227-3.2 1.707-.9-5.227-3.3 4.693-1.8zM9 2L2 5l5.333 3.3-1.707.9 5.227 3.2-1.387.6 8.533 5-3.84-6.5 1.173-.6-3.947-3.8 1.28-.6L8.998 2z"/></symbol><symbol id="svg-icon-line" viewBox="0 0 20 20"><path d="M16.647 17.354l-14-14 .707-.707 14 14-.707.707z"/></symbol><symbol id="svg-icon-lineWithArrow" viewBox="0 0 20 20"><path d="M16 15.293V13h1v4h-4v-1h2.293L2.647 3.354l.707-.707L16 15.293z"/></symbol><symbol id="svg-icon-lineWithTwoArrows" viewBox="0 0 20 20"><path d="M3 3v4h1V4.707L15.293 16H13v1h4v-4h-1v2.293L4.707 4H7V3H3zm1 1z"/></symbol><symbol id="svg-icon-mathDivide" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16 9H5v2h11V9zm1-1v4H4V8h13zM10.5 6a.5.5 0 100-1 .5.5 0 000 1zm1.5-.5c0 .8284-.6716 1.5-1.5 1.5S9 6.3284 9 5.5 9.6716 4 10.5 4s1.5.6716 1.5 1.5zM10.5 15a.5.5 0 10-.0001-1.0001A.5.5 0 0010.5 15zm1.5-.5c0 .8284-.6716 1.5-1.5 1.5S9 15.3284 9 14.5s.6716-1.5 1.5-1.5 1.5.6716 1.5 1.5z"/></symbol><symbol id="svg-icon-mathEqual" viewBox="0 0 20 20"><path d="M16 6H4v2h12V6zm1-1v4H3V5h14zM16 11H4v2h12v-2zm1-1v4H3v-4h14z"/></symbol><symbol id="svg-icon-mathMinus" viewBox="0 0 20 20"><path d="M16 9H4v2h12V9zm1-1v4H3V8h14z"/></symbol><symbol id="svg-icon-mathMultiply" viewBox="0 0 20 20"><path d="M7.717 9.947L4 6.23 6.23 4l3.717 3.717L13.77 4 16 6.23l-3.717 3.717L16 13.77 13.77 16l-3.823-3.717L6.23 16 4 13.77l3.717-3.823zM5.414 6.23l3.707 3.707-3.717 3.823.826.826 3.707-3.707 3.823 3.717.836-.836-3.717-3.823 3.707-3.707-.826-.826-3.823 3.717L6.23 5.414l-.816.816z"/></symbol><symbol id="svg-icon-mathNotEqual" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M3 17h4l1.5385-2H17v-4h-5.3846l.7692-1H17V6h-1.5385L17 4h-4l-1.5385 2H3v4h5.3846l-.7692 1H3v4h1.5385L3 17zm6.1538-8l1.5385-2H4v2h5.1538zm-4.123 7h1.4768l8.4615-11h-1.4767L5.0309 16zm9.6615-9l-1.5385 2H16V7h-1.3077zM16 14H9.3077l1.5385-2H16v2zM4 12h2.8461l-1.5384 2H4v-2z"/></symbol><symbol id="svg-icon-mathPlus" viewBox="0 0 20 20"><path d="M12 12v5H8v-5H3V8h5V3h4v5h5v4h-5zm-1 4v-5h5V9h-5V4H9v5H4v2h5v5h2z"/></symbol><symbol id="svg-icon-moon" viewBox="0 0 20 20"><path d="M15.24 17.68c-.46.133-.922.221-1.381.267C9.331 18.395 5.062 14.724 5 10c0-4.766 4.348-8.399 8.861-7.948.459.046.921.134 1.379.268a8.671 8.671 0 00-1.086.778c-2.011 1.69-3.213 4.245-3.181 6.902-.032 2.656 1.169 5.21 3.179 6.9.34.286.703.547 1.087.78zm-2.473-.706C10.983 15.128 9.945 12.611 9.973 10c-.028-2.612 1.011-5.131 2.798-6.977C9.205 3.198 6.004 6.191 6 9.994c.053 3.787 3.207 6.796 6.767 6.98z"/></symbol><symbol id="svg-icon-noSmoking" viewBox="0 0 20 20"><path d="M15.657 4.343c3.124 3.124 3.124 8.19 0 11.314s-8.189 3.124-11.314 0c-3.124-3.124-3.124-8.19 0-11.314s8.19-3.124 11.314 0zm-1.793 6.693c.356-1.335.011-2.818-1.036-3.864s-2.53-1.392-3.864-1.036l4.9 4.9zm.792.791a4.936 4.936 0 01-.484.931l-6.93-6.93c.296-.196.608-.357.931-.484 1.794-.703 3.913-.33 5.363 1.12s1.823 3.568 1.12 5.363zm-8.52-2.863l4.9 4.901c-1.335.356-2.818.01-3.864-1.036s-1.392-2.53-1.036-3.864zm-.792-.791c-.703 1.794-.33 3.913 1.12 5.363s3.568 1.823 5.363 1.12c.323-.127.635-.288.931-.484l-6.93-6.93a4.995 4.995 0 00-.484.931zm9.606 6.777c2.734-2.734 2.734-7.166 0-9.899s-7.166-2.734-9.899 0-2.734 7.166 0 9.899c2.734 2.734 7.166 2.734 9.899 0z"/></symbol><symbol id="svg-icon-notchedRightArrow" viewBox="0 0 20 20"><path d="M11 13.016H5.168L7.315 10.5 5.168 7.984H11V5.308l5.538 5.192L11 15.692v-2.676zm-8 1h7V18l8-7.5L10 3v3.984H3L6 10.5l-3 3.516z"/></symbol><symbol id="svg-icon-octagon" viewBox="0 0 20 20"><path d="M7.214 17h5.572L17 12.786V7.214L12.786 3H7.214L3 7.214v5.572L7.214 17zm5.986 1H6.8L2 13.2V6.8L6.8 2h6.4L18 6.8v6.4L13.2 18z"/><path d="M8 7h4v6H8V7zm1 2h2V8H9v1zm0 1v2h2v-2H9z"/></symbol><symbol id="svg-icon-parallelogram" viewBox="0 0 20 20"><path d="M16.5 6l-4.2 10H3.5L7.7 6h8.8zM18 5H7L2 17h11l5-12z"/></symbol><symbol id="svg-icon-pentagon" viewBox="0 0 20 20"><path d="M10 3.203l6.811 4.561-2.733 8.235H5.922L3.189 7.764 10 3.203zm8 4.154L10 2 2 7.357 5.2 17h9.6L18 7.357z"/></symbol><symbol id="svg-icon-pie" viewBox="0 0 20 20"><path d="M10 10h8a8.003 8.003 0 01-1.349 4.445c-.879 1.316-2.128 2.341-3.59 2.946s-3.07.764-4.622.455-2.977-1.071-4.096-2.189-1.881-2.544-2.189-4.096c-.309-1.552-.15-3.16.455-4.622s1.631-2.711 2.946-3.59A8.003 8.003 0 0110 2v8zm6.928 1H9V3.072A7.0025 7.0025 0 006.111 4.18c-1.151.769-2.048 1.862-2.578 3.142a6.9992 6.9992 0 001.518 7.628c.979.979 2.226 1.646 3.584 1.916s2.765.132 4.044-.398a7.008 7.008 0 003.142-2.578c.582-.872.959-1.86 1.108-2.889z"/></symbol><symbol id="svg-icon-plaque" viewBox="0 0 20 20"><path d="M6.765 17c-.36-.956-1.024-1.762-1.559-2.334-.616-.659-1.372-1.154-2.206-1.44V6.775c.834-.286 1.591-.781 2.206-1.44.534-.573 1.198-1.379 1.559-2.334h6.47c.36.956 1.024 1.762 1.559 2.334.616.659 1.372 1.154 2.206 1.44v6.451c-.834.287-1.591.781-2.206 1.44-.534.573-1.198 1.379-1.559 2.334h-6.47zM2 14c.937.157 1.804.629 2.475 1.348S5.853 16.996 6 18h8c.147-1.004.854-1.933 1.525-2.652S17.063 14.157 18 14V6c-.938-.157-1.804-.629-2.475-1.348S14.147 3.005 14 2H6c-.147 1.004-.854 1.933-1.525 2.652S2.937 5.843 2 6v8z"/></symbol><symbol id="svg-icon-plus" viewBox="0 0 20 20"><path d="M13 13v5H7v-5H2V7h5V2h6v5h5v6h-5zm-1 4v-5h5V8h-5V3H8v5H3v4h5v5h4z"/></symbol><symbol id="svg-icon-polyline1" viewBox="0 0 20 20"><path d="M7.224 4.929c.155-.38.276-.966.276-1.929h1c0 1.037-.129 1.764-.349 2.306-.226.556-.535.883-.821 1.133-.084.073-.157.134-.221.188-.17.141-.282.235-.383.383-.115.168-.225.436-.225.991 0 .595.073 1.001.177 1.249.1.238.194.264.23.27.074.012.258-.007.581-.23.312-.214.69-.575 1.123-1.104 1.027-1.283 1.981-1.999 2.828-2.244.889-.257 1.647.018 2.092.643.826 1.157.423 3.213-1.255 4.332-1.087.725-1.63 1.351-1.865 1.845-.223.47-.178.84-.028 1.14.162.324.468.604.814.808.182.107.354.181.496.227-.214-.676-.29-1.247-.256-1.722.038-.527.212-.959.506-1.264.295-.307.678-.45 1.056-.45.662 0 1.188.297 1.516.756.319.446.432 1.017.354 1.564-.123.857-.708 1.663-1.735 2.006.206.423.466.891.789 1.408l-.848.53c-.401-.642-.72-1.23-.966-1.766-.036.001-.073.001-.11.001-.373 0-.868-.17-1.308-.428-.45-.265-.926-.672-1.202-1.223-.287-.574-.336-1.267.019-2.016.344-.724 1.051-1.474 2.214-2.249 1.322-.881 1.419-2.325.995-2.918-.179-.251-.484-.413-1.001-.263-.559.162-1.354.695-2.326 1.91l-.003.004c-.466.57-.913 1.008-1.332 1.297-.408.281-.862.467-1.312.392-.488-.081-.807-.437-.988-.87-.177-.424-.254-.981-.254-1.635 0-.695.14-1.177.4-1.556.188-.274.442-.484.637-.645.049-.041.094-.078.134-.113.213-.187.405-.392.554-.757zm5.515 9.975c.739-.214 1.072-.746 1.141-1.224.047-.327-.027-.632-.177-.842-.141-.197-.365-.337-.703-.337-.122 0-.239.044-.335.143-.097.101-.204.294-.229.642-.027.379.046.91.303 1.618z"/></symbol><symbol id="svg-icon-polyline2" viewBox="0 0 20 20"><path d="M12.8 6.1c-.37.348-.628.786-.742 1.264s-.081.975.096 1.436c.564 1.352 2.15 1.283 3.525 1.223.272-.012.537-.023.783-.023.135.02.26.079.357.168s.16.206.182.332v4c-.046.383-.231.74-.525 1.013s-.678.444-1.091.487H3.539c-.135-.02-.26-.079-.357-.168s-.16-.206-.181-.332V8.2L6.447 5h7.323v.3l-.969.8zm3.2 4.911c-.08.003-.162.007-.247.011-.144.006-.297.013-.464.019-.433.014-.924.019-1.406-.039-.971-.117-2.114-.526-2.652-1.815l-.006-.013-.005-.013a3.4169 3.4169 0 01-.134-2.027c.097-.406.266-.787.496-1.131H6.839L4 8.639v6.364h11.324c.187-.028.351-.109.471-.22.114-.106.18-.231.205-.355v-3.414z"/></symbol><symbol id="svg-icon-quadArrow" viewBox="0 0 20 20"><path d="M10.547 1.095l3.513 3.953h-2.013v3h3V6.035L19 9.548l-3.953 3.513v-2.013h-3v3h2.013l-3.513 3.953-3.513-3.953h2.013v-3h-3v2.013L2.094 9.548l3.953-3.513v2.013h3v-3H7.034l3.513-3.953zM9.261 4.047h.787v5h-5V8.26L3.601 9.547l1.447 1.287v-.787h5v5h-.787l1.287 1.447 1.287-1.447h-.787v-5h5v.787l1.447-1.287-1.447-1.287v.787h-5v-5h.787L10.548 2.6 9.261 4.047z"/></symbol><symbol id="svg-icon-quadArrowCallout" viewBox="0 0 20 20"><path d="M7 15h2v-2H7v-2H5v2L2 9.5 5 6v2h2V6h2V4H7l3.5-3L14 4h-2v2h2v2h2V6l3 3.5-3 3.5v-2h-2v2h-2v2h2l-3.5 3L7 15zm3.5 1.683l.797-.683H11v-4h2v-2h4v.297l.683-.797L17 8.703V9h-4V7h-2V3h.297l-.797-.683L9.703 3H10v4H8v2H4v-.297l-.683.797.683.797V10h4v2h2v4h-.297l.797.683z"/></symbol><symbol id="svg-icon-rect" viewBox="0 0 20 20"><path d="M17 5v10H3V5h14zm1-1H2v12h16V4z"/></symbol><symbol id="svg-icon-rect-1" viewBox="0 0 20 20"><path d="M17 5v10H3V5h14zm1-1H2v12h16V4z"/></symbol><symbol id="svg-icon-ribbon" viewBox="0 0 20 20"><path d="M15.434 9l1.8-3H14v6h3.234l-1.8-3zM13 8H7v6h6V8zm6-3l-2.4 4 2.4 4h-5v1c0 .552-.448 1-1 1H7c-.552 0-1-.448-1-1v-1H1l2.4-4L1 5h7c.552 0 1 .448 1 1v1h2V6c0-.552.448-1 1-1h7zM2.766 6l1.8 3-1.8 3H6V6H2.766z"/></symbol><symbol id="svg-icon-ribbon2" viewBox="0 0 20 20"><path d="M13 12V6H7v6h6zM6 8H2.766l1.8 3-1.8 3H6V8zm-5 7l2.4-4L1 7h5V6c0-.552.448-1 1-1h6c.552 0 1 .448 1 1v1h5l-2.4 4 2.4 4h-7c-.552 0-1-.448-1-1v-1H9v1c0 .552-.448 1-1 1H1zm16.234-1l-1.8-3 1.8-3H14v6h3.234z"/></symbol><symbol id="svg-icon-rightArrow" viewBox="0 0 20 20"><path d="M9 13v3l7.333-5.5L9 5v3H3v5h6zM8 3l10 7.5L8 18v-4H2V7h6V3z"/></symbol><symbol id="svg-icon-rightArrowCallout" viewBox="0 0 20 20"><path d="M10 9V5H3v11h7v-4h4v1.413l2.648-2.913L14 7.587V9h-4zm3-4l5 5.5-5 5.5v-3h-2v4H2V4h9v4h2V5z"/></symbol><symbol id="svg-icon-rightBrace" viewBox="0 0 20 20"><path d="M8 16.5c.482-.176.891-.507 1.163-.942s.39-.948.337-1.458v-1.6c-.046-.471.054-.944.287-1.355s.586-.741 1.013-.945c-.427-.203-.781-.533-1.013-.945S9.454 8.37 9.5 7.9V6.4C9.5 5 9 4.2 8 3.9l.2-.9c.764.208 1.457.623 2 1.2.421.655.63 1.423.6 2.2v1.5c0 1.2.6 1.8 1.6 1.8v1c-1 0-1.6.6-1.6 1.8V14c.03.777-.179 1.545-.6 2.2-.558.558-1.245.971-2 1.2l-.2-.9z"/></symbol><symbol id="svg-icon-rightBracket" viewBox="0 0 20 20"><path d="M8.999 3.002H7v1h1.999a2.0006 2.0006 0 012 2V15a2.0006 2.0006 0 01-2 2H7v1h1.999c.398.014.794-.054 1.164-.2a2.9032 2.9032 0 001.636-1.636c.146-.37.214-.766.2-1.164V6.002c.014-.398-.054-.794-.2-1.164a2.9032 2.9032 0 00-1.636-1.636 2.896 2.896 0 00-1.164-.2z"/></symbol><symbol id="svg-icon-round1Rect" viewBox="0 0 20 20"><path d="M15 5c1.105 0 2 .895 2 2v8H3V5h12zM2 4v12h16V7c0-1.657-1.343-3-3-3H2z"/></symbol><symbol id="svg-icon-round2DiagRect" viewBox="0 0 20 20"><path d="M17 5v8c0 1.105-.895 2-2 2H3V7c0-1.105.895-2 2-2h12zM5 4C3.343 4 2 5.343 2 7v9h13c1.657 0 3-1.343 3-3V4H5z"/></symbol><symbol id="svg-icon-round2SameRect" viewBox="0 0 20 20"><path d="M15 5c1.105 0 2 .895 2 2v8H3V7c0-1.105.895-2 2-2h10zM5 4C3.343 4 2 5.343 2 7v9h16V7c0-1.657-1.343-3-3-3H5z"/></symbol><symbol id="svg-icon-roundRect" viewBox="0 0 20 20"><path d="M15 5c1.105 0 2 .895 2 2v6c0 1.105-.895 2-2 2H5c-1.105 0-2-.895-2-2V7c0-1.105.895-2 2-2h10zM5 4C3.343 4 2 5.343 2 7v6c0 1.657 1.343 3 3 3h10c1.657 0 3-1.343 3-3V7c0-1.657-1.343-3-3-3H5z"/></symbol><symbol id="svg-icon-rtTriangle" viewBox="0 0 20 20"><path d="M4 5.4L14.6 16H4V5.4zM3 3v14h14L3 3z"/></symbol><symbol id="svg-icon-smileyFace" viewBox="0 0 20 20"><path d="M10 16.5c3.59 0 6.5-2.91 6.5-6.5S13.59 3.5 10 3.5 3.5 6.41 3.5 10s2.91 6.5 6.5 6.5zm7.5-6.5c0 4.142-3.358 7.5-7.5 7.5S2.5 14.142 2.5 10c0-4.142 3.358-7.5 7.5-7.5s7.5 3.358 7.5 7.5z"/><path d="M7.5 7c-.276 0-.5.224-.5.5s.*********.5-.224.5-.5-.224-.5-.5-.5zM6 7.5C6 6.672 6.672 6 7.5 6S9 6.672 9 7.5 8.328 9 7.5 9 6 8.328 6 7.5zM12.5 7c-.276 0-.5.224-.5.5s.*********.5-.224.5-.5-.224-.5-.5-.5zm-1.5.5c0-.828.672-1.5 1.5-1.5s1.5.672 1.5 1.5S13.328 9 12.5 9 11 8.328 11 7.5zM5.416 12c.772 1.766 2.534 3 4.584 3s3.812-1.234 4.584-3h-1.119c-.692 1.196-1.984 2-3.465 2s-2.773-.804-3.465-2H5.416z"/></symbol><symbol id="svg-icon-snip1Rect" viewBox="0 0 20 20"><path d="M3 5v10h14V8.414L13.586 5H3zm11-1l4 4v8H2V4h12z"/></symbol><symbol id="svg-icon-snip2DiagRect" viewBox="0 0 20 20"><path d="M6.414 15H17V8.414L13.586 5H3v6.586L6.414 15zM6 16l-4-4V4h12l4 4v8H6z"/></symbol><symbol id="svg-icon-snip2SameRect" viewBox="0 0 20 20"><path d="M6.414 5h7.172L17 8.414V15H3V8.414L6.414 5zM14 4H6L2 8v8h16V8l-4-4z"/></symbol><symbol id="svg-icon-snipRoundRect" viewBox="0 0 20 20"><path d="M3 7c0-1.105.895-2 2-2h8.586L17 8.414V15H3V7zm11-3H5C3.343 4 2 5.343 2 7v9h16V8l-4-4z"/></symbol><symbol id="svg-icon-spline" viewBox="0 0 20 20"><path d="M2.471 12.168L2 12l-.471-.168.001-.002.002-.005.006-.016.023-.062c.02-.053.049-.131.087-.229.076-.196.188-.475.331-.809.286-.667.701-1.561 1.213-2.457.51-.893 1.129-1.811 1.831-2.512.692-.692 1.532-1.24 2.479-1.24.776 0 1.418.196 1.945.562.521.362.895.867 1.181 1.43.455.897.728 2.037.993 *************.118.495.179.738.344 1.375.746 2.651 1.505 3.588.731.902 1.832 1.534 3.698 1.534v1c-2.135 0-3.534-.744-4.474-1.904-.912-1.126-1.354-2.6-1.698-3.975-.068-.272-.132-.539-.195-.8-.265-1.104-.503-2.096-.899-2.876-.238-.468-.516-.823-.86-1.062-.338-.235-.775-.383-1.374-.383-.553 0-1.151.327-1.771.947-.611.611-1.18 1.443-1.669 2.301-.488.853-.885 1.71-1.162 2.355-.138.322-.245.59-.318.777-.036.093-.064.166-.082.216l-.02.056-.005.013-.001.004z"/></symbol><symbol id="svg-icon-star4" viewBox="0 0 20 20"><path d="M12.5 7.5l5.5 2-5.5 2-2 5.5-2-5.5-5.5-2 5.5-2 2-5.5 2 5.5zm2.574 2L11.72 8.28 10.5 4.926 9.28 8.28 5.926 9.5l3.354 1.22 1.22 3.354 1.22-3.354 3.354-1.22z"/></symbol><symbol id="svg-icon-star5" viewBox="0 0 20 20"><path d="M12.5 7.997h6l-5 3.503 2 6-5-3.5-5 3.5 2-6-5-3.503h6L10.5 2l2 5.997zm2.83 1h-3.551L10.5 5.161 9.221 8.997H5.67l3.015 2.112-1.279 3.837L10.5 12.78l3.094 2.166-1.279-3.837 3.015-2.112z"/></symbol><symbol id="svg-icon-star6" viewBox="0 0 20 20"><path d="M8 12H5.174l2.143-2.5L5.174 7H8l2.5-3.333L13 7h2.826l-2.143 2.5 2.143 2.5h-3.38l-2.021 3.233L8 12zm-5 1h4.5l3 4 2.5-4h5l-3-3.5L18 6h-4.5l-3-4-3 4H3l3 3.5L3 13z"/></symbol><symbol id="svg-icon-star7" viewBox="0 0 20 20"><path d="M10.5 13.321l2.747 1.717-.337-2.697 3.129-1.173-2.224-1.854 1.169-3.116-3.021.378L10.5 4.017 9.037 6.576l-3.02-.378 1.169 3.116-2.224 1.854 3.129 1.173-.337 2.697 2.747-1.717zM6.5 17l.5-4-4-1.5L6 9 4.5 5l4 .5 2-3.5 2 3.5 4-.5L15 9l3 2.5-4 1.5.5 4-4-2.5-4 2.5z"/></symbol><symbol id="svg-icon-star8" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M13 4l-2.5-3L8 4H5v3L2 9.5 5 12v3h3l2.5 3 2.5-3h3v-3l3-2.5L16 7V4h-3zm2 1h-2.4684L10.5 2.562 8.4684 5H6v2.4684L3.562 9.5 6 11.5316V14h2.4684L10.5 16.438 12.5316 14H15v-2.4684L17.438 9.5 15 7.4684V5z"/><path fill-rule="evenodd" d="M9 7v5h3V7H9zm2 4h-1v-1h1v1zm0-2h-1V8h1v1z"/></symbol><symbol id="svg-icon-star10" viewBox="0 0 20 20"><path d="M8.413 14.607L10 16.462l1.587-1.855 2.419.616.189-2.498 2.231-.76-1.355-1.966 1.355-1.966-2.231-.76-.189-2.498-2.419.616L10 3.536 8.413 5.391l-2.42-.616-.189 2.498-2.231.76 1.355 1.966-1.355 1.966 2.231.76.189 2.498 2.42-.616zm-3.327 1.88l-.229-3.027L2 12.487 3.714 10 2 7.514l2.857-.973.229-3.027 2.971.757L10 2.001l1.943 2.27 2.971-.757.229 3.027L18 7.514 16.286 10 18 12.487l-2.857.973-.229 3.027-2.971-.757L10 18l-1.943-2.27-2.971.757z"/><path d="M10 8v5h3V8h-3zm2 4h-1V9h1v3zM8 8v1H7v1h1v3h1V8H8z"/></symbol><symbol id="svg-icon-star12" viewBox="0 0 20 20"><path d="M10.5 1l1.54 2.753 2.71-1.614-.043 3.154 3.154-.043-1.614 2.71L19 9.5l-2.753 1.54 1.614 2.71-3.154-.043.043 3.154-2.71-1.614L10.5 18l-1.54-2.753-2.71 1.614.043-3.154-3.154.043 1.614-2.71L2 9.5l2.753-1.54-1.614-2.71 3.154.043-.043-3.154 2.71 1.614L10.5 1zm3.226 2.913l-2.057 1.225L10.5 3.049 9.331 5.138 7.274 3.913l.032 2.394-2.394-.032 1.225 2.057-2.089 1.169 2.089 1.169-1.225 2.057 2.394-.032-.032 2.394 2.057-1.225 1.169 2.089 1.169-2.089 2.057 1.225-.032-2.394 2.394.032-1.225-2.057 2.089-1.169-2.089-1.169 1.225-2.057-2.394.032.032-2.394z"/><path d="M8 7v1H7v1h1v3h1V7H8zM12.682 7.318c.183.183.296.424.318.682v1h-1V8h-2V7h2c.258.023.499.135.682.318zM11 10V9h1v1h-1zm0 0v1h2v1h-3v-2h1z"/></symbol><symbol id="svg-icon-star16" viewBox="0 0 20 20"><path d="M12.26 2.932l2.49-.793.558 2.553 2.553.558-.793 2.49L19 9.5l-1.932 1.76.793 2.49-2.553.558-.558 2.553-2.49-.793L10.5 18l-1.76-1.932-2.49.793-.558-2.553-2.553-.558.793-2.49L2 9.5l1.932-1.76-.793-2.49 2.553-.558.558-2.553 2.49.793L10.5 1l1.76 1.932zm1.748.493l-2.055.654L10.5 2.485 9.047 4.079l-2.055-.654-.461 2.107-2.107.461.654 2.055-1.594 1.453 1.594 1.453-.654 2.055 2.107.461.461 2.107 2.055-.654 1.453 1.594 1.452-1.594 2.055.654.461-2.107 2.107-.461-.654-2.055 1.594-1.453-1.594-1.453.654-2.055-2.107-.461-.461-2.107z"/><path d="M8 7v1H7v1h1v3h1V7H8zM13 8V7h-2c-.258.023-.499.135-.682.318S10.022 7.742 10 8v4h3V9h-2V8h2zm-1 2v1h-1v-1h1z"/></symbol><symbol id="svg-icon-star24" viewBox="0 0 20 20"><path d="M12.26 2.932l2.49-.793.558 2.553 2.553.558-.793 2.49L19 9.5l-1.932 1.76.793 2.49-2.553.558-.558 2.553-2.49-.793L10.5 18l-1.76-1.932-2.49.793-.558-2.553-2.553-.558.793-2.49L2 9.5l1.932-1.76-.793-2.49 2.553-.558.558-2.553 2.49.793L10.5 1l1.76 1.932zm1.748.493l-2.055.654L10.5 2.485 9.047 4.079l-2.055-.654-.461 2.107-2.107.461.654 2.055-1.594 1.453 1.594 1.453-.654 2.055 2.107.461.461 2.107 2.055-.654 1.453 1.594 1.452-1.594 2.055.654.461-2.107 2.107-.461-.654-2.055 1.594-1.453-1.594-1.453.654-2.055-2.107-.461-.461-2.107z"/><path d="M9.682 7.318c.183.183.295.424.318.682v1H9V8H7V7h2c.258.023.499.135.682.318zM8 10V9h1v1H8zm0 0v1h2v1H7v-2h1zM13 7v2h-1V7h-1v3h2v2h1V7h-1z"/></symbol><symbol id="svg-icon-star32" viewBox="0 0 20 20"><path d="M12.26 2.932l2.49-.793.558 2.553 2.553.558-.793 2.49L19 9.5l-1.932 1.76.793 2.49-2.553.558-.558 2.553-2.49-.793L10.5 18l-1.76-1.932-2.49.793-.558-2.553-2.553-.558.793-2.49L2 9.5l1.932-1.76-.793-2.49 2.553-.558.558-2.553 2.49.793L10.5 1l1.76 1.932zm1.748.493l-2.055.654L10.5 2.485 9.047 4.079l-2.055-.654-.461 2.107-2.107.461.654 2.055-1.594 1.453 1.594 1.453-.654 2.055 2.107.461.461 2.107 2.055-.654 1.453 1.594 1.452-1.594 2.055.654.461-2.107 2.107-.461-.654-2.055 1.594-1.453-1.594-1.453.654-2.055-2.107-.461-.461-2.107z"/><path d="M13.682 7.318c.183.183.296.424.318.682v1h-1V8h-2V7h2c.258.023.499.135.682.318zM12 10V9h1v1h-1zm0 0v1h2v1h-3v-2h1zM7 7v1h2v1H7v1h2v1H7v1h3V7H7z"/></symbol><symbol id="svg-icon-stripedRightArrow" viewBox="0 0 20 20"><path d="M11 12v2.692L16.538 9.5 11 4.308V7H7v5h4zM10 2l8 7.5-8 7.5v-4H6V6h4V2zM5 6H4v7h1V6zM3 6H2v7h1V6z"/></symbol><symbol id="svg-icon-sun" viewBox="0 0 20 20"><path d="M10 6.136c.764 0 1.511.227 2.147.651s1.131 1.028 1.423 1.734c.292.706.369 1.483.22 2.232s-.517 1.438-1.057 1.978c-.54.54-1.229.908-1.978 1.057s-1.526.073-2.232-.22c-.706-.292-1.309-.788-1.734-1.423s-.651-1.382-.651-2.147c0-1.025.407-2.007 1.132-2.732s1.707-1.132 2.732-1.132zM10 5c-.989 0-1.956.293-2.778.843S5.759 7.173 5.38 8.087c-.378.914-.477 1.919-.285 2.889s.669 1.861 1.368 2.56a5.0002 5.0002 0 007.693-.759c.55-.823.843-1.789.843-2.778 0-1.326-.527-2.598-1.464-3.536s-2.209-1.464-3.536-1.464zM10 2L8.5 3.5h3L10 2zM16.5 3.5h-2l2 2v-2zM3.5 3.5v2l2-2h-2zM10 18l1.5-1.5h-3L10 18zM18 10l-1.5-1.5v3L18 10zM2 10l1.5 1.5v-3L2 10zM3.5 16.5h2l-2-2v2zM16.5 16.5v-2l-2 2h2z"/></symbol><symbol id="svg-icon-teardrop" viewBox="0 0 20 20"><path d="M10 17.998c-2.113-.028-4.132-.879-5.626-2.373S2.028 12.112 2 9.999c.028-2.113.879-4.132 2.374-5.626S7.887 2.027 10 2h8v8.959c-.213 1.961-1.152 3.77-2.633 5.073S11.972 18.036 10 17.998zM3 9.999a7.1078 7.1078 0 007.013 7l-.013 1 .019-1a6.8935 6.8935 0 004.687-1.717 6.887 6.887 0 002.293-4.378V3.001h-6.993A7.105 7.105 0 005.08 5.082a7.1078 7.1078 0 00-2.081 4.919z"/></symbol><symbol id="svg-icon-textRect" viewBox="0 0 20 20"><path d="M17 4v11H3V4h14zm1-1H2v13h16V3z"/><path d="M15 6h-4v1h4V6zM15 9h-4v1h4V9zM15 12H5v1h10v-1zM9.947 10.276l-.894.447-.362-.724H6.309l-.362.724-.894-.447L7.5 5.382l2.447 4.894zM8.191 9L7.5 7.618 6.809 9h1.382z"/></symbol><symbol id="svg-icon-trapezoid" viewBox="0 0 20 20"><path d="M13.3 5l3.3 11H3.4L6.7 5h6.6zm.7-1H6L2 17h16L14 4z"/></symbol><symbol id="svg-icon-triangle" viewBox="0 0 20 20"><path d="M10 5l6.187 10H3.814l6.187-10zm0-2L2 16h16L10 3z"/></symbol><symbol id="svg-icon-upArrow" viewBox="0 0 20 20"><path d="M13 11h3l-5.5-7.333L5 11h3v6h5v-6zM3 12l7.5-10L18 12h-4v6H7v-6H3z"/></symbol><symbol id="svg-icon-upArrowCallout" viewBox="0 0 20 20"><path d="M9 10H5v7h11v-7h-4V6h1.413L10.5 3.351 7.587 6H9v4zM5 7l5.5-5L16 7h-3v2h4v9H4V9h4V7H5z"/></symbol><symbol id="svg-icon-upDownArrow" viewBox="0 0 20 20"><path d="M7 13H4.851L9.5 16.719 14.149 13H12V7h2.149L9.5 3.281 4.851 7H7v6zM2 8l7.5-6L17 8h-4v4h4l-7.5 6L2 12h4V8H2z"/></symbol><symbol id="svg-icon-uturnArrow" viewBox="0 0 20 20"><path d="M13 10.4h-2.108l2.608 3.825 2.608-3.825H14V8c0-2.761-2.239-5-5-5S4 5.239 4 8v9h1V8c0-2.209 1.791-4 4-4s4 1.791 4 4v2.4zm5-1L13.5 16 9 9.4h3V8c0-1.657-1.343-3-3-3S6 6.343 6 8v10H3V8c0-3.314 2.686-6 6-6s6 2.686 6 6v1.4h3z"/></symbol><symbol id="svg-icon-verticalScroll" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M7 4c0-.5523.4477-1 1-1s1 .4477 1 1-.4477 1-1 1-1-.4477-1-1zM6 5V4c0-1.1046.8954-2 2-2h8c1.1046 0 2 .8954 2 2s-.8954 2-2 2v10c0 1.1046-.8954 2-2 2H5c-1.1046 0-2-.8954-2-2s.8954-2 2-2h1V5zM5 17c.5523 0 1-.4477 1-1s-.4477-1-1-1-1 .4477-1 1 .4477 1 1 1zm5-13c0 .3643-.0974.7058-.2676 1H16c.5523 0 1-.4477 1-1s-.4477-1-1-1H9.7324c.1702.2942.2676.6357.2676 1zM7 6v11h7c.5523 0 1-.4477 1-1V6H7z"/></symbol><symbol id="svg-icon-wave" viewBox="0 0 20 20"><path d="M17 12.594V6.003c-.321.163-.65.301-.972.418C14.999 6.794 13.883 7 13 7c-1.45 0-2.503-.565-3.376-1.033-.053-.028-.105-.057-.157-.084-.907-.484-1.724-.882-2.967-.882-1.106 0-1.814.219-2.338.501-.435.235-.78.53-1.162.892v6.57c.953-.558 2.121-.963 3.5-.963 1.736 0 2.915.589 3.916 1.09l.032.016c.992.496 1.808.894 3.053.894 1.391 0 2.068-.232 2.486-.482.339-.203.542-.42.845-.743.053-.057.109-.117.169-.18zM18 13c-.127.127-.246.254-.363.379C16.833 14.238 16.119 15 13.5 15c-1.5 0-2.5-.5-3.5-1s-2-1-3.5-1c-1.418 0-2.585.503-3.5 1.152-.376.266-.709.557-1 .848V5.967c.013-.013.027-.026.04-.039C3.027 4.973 4.033 4 6.5 4c2 0 2.59.547 3.565 1.068.89.476 1.742.932 2.935.932 1.123 0 2.805-.402 4-1.142.402-.249.748-.536 1-.858v9z"/></symbol><symbol id="svg-icon-wedgeEllipseCallout" viewBox="0 0 20 20"><path d="M8 13.053v1.889l1.954-1.535h.346c1.453 0 3.185-.46 4.527-1.339 1.329-.87 2.172-2.07 2.172-3.569 0-1.16-.629-2.26-1.799-3.107-1.172-.848-2.836-1.393-4.701-1.393-1.827 0-3.49.627-4.682 1.534-1.213.924-1.818 2.036-1.818 2.966 0 1.044.388 1.745 1.003 2.363.513.515 1.13.927 1.844 1.404.231.154.471.315.722.488l.431.298zM7 17v-3.422c-.215-.149-.433-.294-.651-.44C4.662 12.012 3 10.903 3 8.5 3 5.804 6.4 3 10.5 3S18 5.389 18 8.5c0 3.871-4.433 5.907-7.7 5.907L7 17z"/></symbol><symbol id="svg-icon-wedgeRectCallout" viewBox="0 0 20 20"><path d="M5.468 13L7.5 15.438 9.532 13H16V4H4v9h1.468zM7.5 17L5 14H3V3h14v11h-7l-2.5 3z"/></symbol><symbol id="svg-icon-wedgeRoundRectCallout" viewBox="0 0 20 20"><path d="M7.468 13L9.5 15.438 11.532 13H14c1.105 0 2-.895 2-2V6c0-1.105-.895-2-2-2H6c-1.105 0-2 .895-2 2v5c0 1.105.895 2 2 2h1.468zM9.5 17L7 14H6c-1.657 0-3-1.343-3-3V6c0-1.657 1.343-3 3-3h8c1.657 0 3 1.343 3 3v5c0 1.657-1.343 3-3 3h-2l-2.5 3z"/></symbol></svg>
    <svg aria-hidden="true" style="position:absolute;width:0;height:0" xmlns="http://www.w3.org/2000/svg" overflow="hidden"><symbol id="chart-column-normal" viewBox="0 0 40 40"><path fill-rule="evenodd" clip-rule="evenodd" d="M33 6h-6v28h6V6zm-13 8h6v20h-6V14zm-1 6h-6v14h6V20zm-7 6H6v8h6v-8z"/></symbol><symbol id="chart-column-stack" viewBox="0 0 40 40"><path fill-rule="evenodd" clip-rule="evenodd" d="M33 17h-6v17h6V17zm-13 5h6v12h-6V22zm-1 4h-6v8h6v-8zm-7 3H6v5h6v-5z"/><path opacity=".4" fill-rule="evenodd" clip-rule="evenodd" d="M33 6h-6v11h6V6zm-13 8h6v8h-6v-8zm-1 6h-6v6h6v-6zm-7 5H6v4h6v-4z"/></symbol><symbol id="chart-column-pstack" viewBox="0 0 40 40"><path fill-rule="evenodd" clip-rule="evenodd" d="M33 14h-6v20h6V14zm-13 2h6v18h-6V16zm-1 2h-6v16h6V18zm-7 3H6v13h6V21z"/><path opacity=".4" fill-rule="evenodd" clip-rule="evenodd" d="M12 6H6v15h6V6zm7 0h-6v12h6V6zm1 0h6v10h-6V6zm13 0h-6v8h6V6z"/></symbol><symbol id="chart-column-3d-normal" viewBox="0 0 40 40"><path fill-rule="evenodd" clip-rule="evenodd" d="M23 34l4 1V8l-4-1v27zm-1-1l-4-1V11l4 1v21zm-9-3l4 1V17l-4-1v14zm-5-2l4 1v-8l-4-1v8z"/><path opacity=".6" fill-rule="evenodd" clip-rule="evenodd" d="M31 7l-4-1-4 1 4 1 4-1zm-9 3l1 .25v1.5L22 12l-4-1 4-1zm-4 5.25L17 15l-4 1 4 1 1-.25v-1.5zM12 19l1 .25v1.5L12 21l-4-1 4-1z"/><path opacity=".4" d="M31 7l-4 1v27l4-1V7z"/></symbol><symbol id="chart-column-3d-stack" viewBox="0 0 40 40"><path opacity=".2" d="M27 7l4-1v10l-4 1V7z"/><path opacity=".6" fill-rule="evenodd" clip-rule="evenodd" d="M23 6l4-1 4 1-4 1-4-1zm8 10l-4 1v17l4-1V16zm-5 4h-3v12h3V20zm-8-10l4-1 1 .25v1.5L22 11l-4-1zm-1 4l-4 1 4 1 1-.25v-1.5L17 14zm-9 5l4-1 1 .25v1.5L12 20l-4-1z"/><path opacity=".4" fill-rule="evenodd" clip-rule="evenodd" d="M27 7l-4-1v10l4 1V7zm-5 4l-4-1v10l4 1V11zm-10 9l-4-1v4l4 1v-4zm1-5l4 1v7l-4-1v-7z"/><path fill-rule="evenodd" clip-rule="evenodd" d="M23 16l4 1v17l-4-1V16zm-1 5l-4-1v11l4 1V21zm-9 1l4 1v7l-4-1v-7zm-5 1l4 1v4l-4-1v-4z"/></symbol><symbol id="chart-column-3d-pstack" viewBox="0 0 40 40"><path fill-rule="evenodd" clip-rule="evenodd" d="M24 35l4 1V17l-4-1v19zm-1-1l-4-1V18l4 1v15zm-9-3l4 1V21l-4-1v11zm-5-2l4 1v-7l-4-1v7z"/><path opacity=".4" fill-rule="evenodd" clip-rule="evenodd" d="M13 6L9 5v17l4 1V6zm1 1l4 1v13l-4-1V7zm9 3l-4-1v9l4 1v-9zm5 2l-4-1v5l4 1v-5z"/><path opacity=".6" fill-rule="evenodd" clip-rule="evenodd" d="M9 5l4 1 4-1-4-1-4 1zm9 3l-4-1 4-1 4 1-4 1zm1 1l4 1 4-1-4-1-4 1zm9 3l-4-1 4-1 4 1-4 1zm0 5l4-1v19l-4 1V17z"/><path opacity=".2" d="M28 12l4-1v5l-4 1v-5z"/></symbol><symbol id="chart-column-3d-normal-per" viewBox="0 0 40 40"><path d="M24.0002 6l4 1v24L25 30V17l-.9998-.2383V6zM19 10l4 1v5.5l-2-.5-2 .5V10zM14 14l4 1v1.7617L17 17v3.25L16 20l-2 .5V14z"/><path opacity=".2" d="M21 18l4-1v18l-4 1V18z"/><path opacity=".4" fill-rule="evenodd" clip-rule="evenodd" d="M32 6l-4 1v24l4-1V6zM17.0002 17l4 1v18l-4-1V17zm-5 4l4 1v12l-4-1V21zM11 26l-4-1v6l4 1v-6z"/><path id="lighter max_2" opacity=".2" fill-rule="evenodd" clip-rule="evenodd" d="M23.9999 6l4-1 4 1-4 1-4-1zm-4.9998 4l4-1 4 1-4 1-4-1zM18 13l-4 1 4 1 4-1-4-1zm7 4l-4-1-4 1 4 1 4-1zm-9 3l1 .25v1.5L16 22l-4-1 4-1zm-5 4l1 .25v1.5L11 26l-4-1 4-1z"/></symbol><symbol id="chart-line-normal" viewBox="0 0 40 40"><path d="M7 7h1v16.2929l8.4586-8.4586 8.9759 6.9813 6.6892-7.6449.7526.6586-7.3108 8.3551-9.024-7.0187L8 24.7071V33h26v1H7V7z"/><path opacity=".6" d="M32.8536 20.1464L24.5 11.7929 8 28.2929v1.4142l16.5-16.5 7.6465 7.6465.7071-.7072z"/></symbol><symbol id="chart-line-stack" viewBox="0 0 40 40"><path d="M7 7h1v21.3866l8.4732-7.5317 7.9209 5.9407 7.7015-10.5897.8088.5882-8.2985 11.4103-8.0791-6.0593L8 29.7245V33h26v1H7V7z"/><path opacity=".6" d="M23.5427 18.1002l9.2643-7.2055-.614-.7894-8.7357 6.7944-7.0408-4.0233L8 21.2929v1.4142l8.5835-8.5835 6.9592 3.9766z"/></symbol><symbol id="chart-line-pstack" viewBox="0 0 40 40"><path d="M7 7h1v21.2929l7.4363-7.4363 8.9417 5.9611 7.7176-10.6118.8088.5882-8.2824 11.3882-9.0583-6.0389L8 29.7071V33h26v1H7V7z"/><path opacity=".6" fill-rule="evenodd" clip-rule="evenodd" d="M33 10H8V9h25v1z"/></symbol><symbol id="chart-line-normal-marker" viewBox="0 0 40 40"><path opacity=".6" d="M23 11h3v2.2929L31.7071 19H34v3h-3v-2.2929L25.2929 14h-1.5858L9 28.7071V31H6v-3h2.2929L23 13.2929V11z"/><path d="M8 7H7v16H6v3h1v2h1v-2h1v-2.2929L15.7071 17h1.9071L24 21.9668V24h3v-2.455L31.8519 16H34v-3h-3v2.455L26.1481 21h-1.7623L18 16.0332V14h-3v2.2929L8.2929 23H8V7zM7 34v-3h1v2h26v1H7z"/></symbol><symbol id="chart-line-stack-marker" viewBox="0 0 40 40"><path opacity=".6" d="M34 9h-3v2.0332L24.6142 16H22v.067l-4-2.2857V12h-3v2.2929L8.2929 21H6v3h3v-2.2929L15.7071 15H18v-.067l4 2.2857V19h3v-2.0332L31.3858 12H34V9z"/><path d="M8 7H7v14h1V7zM7 28v-4h1v4h.4349L15 22.1644V20h3v2l5.3333 4h1.6394L31 17.7124V15h3v3h-1.9727L26 26.2876V29h-3v-2l-5.3333-4h-2.1016L9 28.8356V31H8v2h26v1H7v-3H6v-3h1z"/></symbol><symbol id="chart-line-pstack-marker" viewBox="0 0 40 40"><path d="M8 7H7v1h1V7zM7 28V11h1v17h.2929L14 22.2929V20h3v1.8991L23.1514 26h1.8213L31 17.7124V15h3v3h-1.9727L26 26.2876V29h-3v-1.8991L16.8486 23h-2.1415L9 28.7071V31H8v2h26v1H7v-3H6v-3h1z"/><path opacity=".6" d="M9 8H6v3h3v-1h5v1h3v-1h6v1h3v-1h5v1h3V8h-3v1h-5V8h-3v1h-6V8h-3v1H9V8z"/></symbol><symbol id="chart-line-3d" viewBox="0 0 40 40"><path d="M8 5v23h31v1H7.7071l-5.8535 5.8536-.7071-.7071L7 28.2929V5h1z"/><path d="M12 19L2 24.5l8-.5 11.5 14L32 26l-8 4.5L12 19z"/><path opacity=".6" d="M24 7c-4 3.5-11.6 9.6-12 10l11.0277-4.9868 6 10h6L38 18l-6-1-8-10z"/></symbol><symbol id="chart-pie-normal" viewBox="0 0 40 40"><path d="M5 20a15.0003 15.0003 0 009.2597 13.8582 15.0002 15.0002 0 0020.4521-10.9318A15.0002 15.0002 0 0020 5v15H5z"/><path opacity=".4" d="M20 5a15.0004 15.0004 0 00-13.8582 9.2598A14.9995 14.9995 0 005 20h15V5z"/></symbol><symbol id="chart-pie-doughnut" viewBox="0 0 40 40"><path d="M5 20a15.0003 15.0003 0 009.2597 13.8582 15.0002 15.0002 0 0020.4521-10.9318A15.0002 15.0002 0 0020 5v6a8.9996 8.9996 0 018.8271 10.7558 9.0011 9.0011 0 01-2.4631 4.6082 9.0007 9.0007 0 01-13.8472-1.3639A8.9994 8.9994 0 0111 20H5z"/><path opacity=".4" d="M20 5a15.0004 15.0004 0 00-13.8582 9.2598A14.9995 14.9995 0 005 20h6a8.9995 8.9995 0 012.636-6.364A9.0015 9.0015 0 0120 11V5z"/></symbol><symbol id="chart-pie-3d-normal" viewBox="0 0 40 40"><path opacity=".4" fill-rule="evenodd" clip-rule="evenodd" d="M31.3137 9.2218C34.3143 11.2847 36 14.0826 36 17v5c0 2.9174-1.6857 5.7153-4.6863 7.7782C28.3131 31.8411 24.2435 33 20 33c-4.2435 0-8.3131-1.1589-11.3137-3.2218C5.6857 27.7153 4 24.9174 4 22v-5c0-2.9174 1.6857-5.7153 4.6863-7.7782C11.6869 7.159 15.7565 6 20 6c4.2435 0 8.3131 1.159 11.3137 3.2218z"/><path d="M4 17c0 2.1756.9384 4.3023 2.6965 6.1113 1.758 1.8089 4.2569 3.2188 7.1806 4.0514 2.9236.8325 6.1406 1.0504 9.2443.6259 3.1037-.4244 5.9547-1.472 8.1923-3.0104 2.2376-1.5384 3.7615-3.4984 4.3789-5.6322.6173-2.1338.3005-4.3455-.9105-6.3555-1.211-2.01-3.2618-3.728-5.893-4.9367C26.2579 6.6451 23.1645 6 20 6v11H4z"/></symbol><symbol id="chart-bar-normal" viewBox="0 0 40 40"><path fill-rule="evenodd" clip-rule="evenodd" d="M15 6H6v6h9V6zM6 27v6h28v-6H6zm0-14h14v6H6v-6zm20 7H6v6h20v-6z"/></symbol><symbol id="chart-bar-stack" viewBox="0 0 40 40"><path fill-rule="evenodd" clip-rule="evenodd" d="M11 6H6v6h5V6zm3 7H6v6h8v-6zm-8 7h12v6H6v-6zm17 7H6v6h17v-6z"/><path opacity=".4" fill-rule="evenodd" clip-rule="evenodd" d="M15 6h-4v6h4V6zm5 7h-6v6h6v-6zm-2 7h8v6h-8v-6zm16 7H23v6h11v-6z"/></symbol><symbol id="chart-bar-pstack" viewBox="0 0 40 40"><path fill-rule="evenodd" clip-rule="evenodd" d="M20 6H6v6h14V6zm2 7H6v6h16v-6zM6 20h18v6H6v-6zm20 7H6v6h20v-6z"/><path opacity=".4" fill-rule="evenodd" clip-rule="evenodd" d="M34 6H20v6h14V6zm0 7H22v6h12v-6zm-10 7h10v6H24v-6zm10 7h-8v6h8v-6z"/></symbol><symbol id="chart-bar-3d-normal" viewBox="0 0 40 40"><path fill-rule="evenodd" clip-rule="evenodd" d="M20 9L8 5v5l12 4V9zM8 11l15 5v5L8 16v-5zm18 12L8 17v5l18 6v-5zm3 7L8 23v5l21 7v-5z"/><path opacity=".6" fill-rule="evenodd" clip-rule="evenodd" d="M24 8l-4 1v5l4-1V8zm-1 8l4-1v5l-4 1v-5zm7 6l-4 1v5l4-1v-5zm3 7l-4 1v5l4-1v-5z"/><path opacity=".4" d="M12 4l12 4-4 1L8 5l4-1z"/></symbol><symbol id="chart-bar-3d-stack" viewBox="0 0 40 40"><path fill-rule="evenodd" clip-rule="evenodd" d="M11 6L8 5v5l3 1V6zM8 16v-5l6 2v5l-6-2zm9 4l-9-3v5l9 3v-5zm-9 3v5l12 4v-5L8 23z"/><path opacity=".6" fill-rule="evenodd" clip-rule="evenodd" d="M15 5l-3-1-4 1 3 1 4-1zm5 4l4-1v5l-4 1V9zm7 6l-4 1v5l4-1v-5zm-1 8l4-1v5l-4 1v-5zm3 7l4-1v5l-4 1v-5z"/><path opacity=".4" fill-rule="evenodd" clip-rule="evenodd" d="M20 9l-9-3v5l9 3V9zm-6 4l9 3v5l-9-3v-5zm12 10l-9-3v5l9 3v-5zm3 7l-9-3v5l9 3v-5z"/><path opacity=".2" d="M15 5l9 3-4 1-9-3 4-1z"/></symbol><symbol id="chart-bar-3d-pstack" viewBox="0 0 40 40"><path fill-rule="evenodd" clip-rule="evenodd" d="M14 7L8 5v5l6 2V7zm-6 4l9 3v5l-9-3v-5zm12 15v-5L8 17v5l12 4zm3 2L8 23v5l15 5v-5z"/><path opacity=".4" fill-rule="evenodd" clip-rule="evenodd" d="M29 12L14 7v5l15 5v-5zm-12 2l12 4v5l-12-4v-5zm12 10l-9-3v5l9 3v-5zm0 6l-6-2v5l6 2v-5z"/><path opacity=".6" fill-rule="evenodd" clip-rule="evenodd" d="M18 6l-6-2-4 1 6 2 4-1zm11 6l4-1v5l-4 1v-5zm4 5l-4 1v5l4-1v-5zm-4 7l4-1v5l-4 1v-5zm0 6l4-1v5l-4 1v-5z"/><path opacity=".2" d="M18 6l15 5-4 1-15-5 4-1z"/></symbol><symbol id="chart-area-normal" viewBox="0 0 40 40"><path d="M15.5 24l4 5L34 14v20H6l9.5-10z"/><path opacity=".4" fill-rule="evenodd" clip-rule="evenodd" d="M19.5 29l-4-5L6 34l9.5-19 5 7h5.7667L19.5 29z"/></symbol><symbol id="chart-area-stack" viewBox="0 0 40 40"><path d="M15.5 24l4 5L34 14v20H6l9.5-10z"/><path opacity=".4" d="M15.5 17l4 5L34 7v7L19.5 29l-4-5L6 34v-7l9.5-10z"/></symbol><symbol id="chart-area-pstack" viewBox="0 0 40 40"><path d="M15.5 24l4 5L34 14v20H6l9.5-10z"/><path opacity=".4" d="M6 6h28v8L19.5 29l-4-5L6 34V6z"/></symbol><symbol id="chart-point-normal" viewBox="0 0 40 40"><path d="M8 7H7v27h27v-1H8V7z"/><circle opacity=".6" cx="11.5" cy="11.5" r="1.5"/><circle opacity=".6" cx="23.5" cy="26.5" r="1.5"/><circle opacity=".6" cx="17.5" cy="23.5" r="1.5"/><circle opacity=".6" cx="29.5" cy="15.5" r="1.5"/><circle cx="19.5" cy="17.5" r="1.5"/><circle cx="26.5" cy="21.5" r="1.5"/><circle cx="32.5" cy="29.5" r="1.5"/><circle cx="13.5" cy="29.5" r="1.5"/></symbol><symbol id="chart-point-smooth-marker"><path d="M8 7H7v27h27v-1H8V7z"/><path d="M17.0422 21.072c-1.4489 2.5453-2.357 5.6039-2.8243 8.1106.466.2545.7821.749.7821 1.3174 0 .8284-.6716 1.5-1.5 1.5s-1.5-.6716-1.5-1.5c0-.7363.5305-1.3487 1.2302-1.4758.481-2.5916 1.4209-5.773 2.9429-8.4469.5451-.9576 1.1724-1.8618 1.8879-2.6525A1.5006 1.5006 0 0118 17.5c0-.8284.6716-1.5 1.5-1.5.2327 0 .4531.053.6497.1476 1.2584-.7851 2.7105-1.2269 4.3743-1.147 2.8108.1349 4.5842 1.772 5.8098 4.0085.0546-.006.11-.0091.1662-.0091.8284 0 1.5.6716 1.5 1.5 0 .466-.2125.8824-.546 1.1575.5057 1.6085.8618 3.4889 1.1029 5.0801.1329.8772.2321 1.6755.3025 2.3057.6548.161 1.1406.7521 1.1406 1.4567 0 .8284-.6716 1.5-1.5 1.5s-1.5-.6716-1.5-1.5c0-.6011.3536-1.1196.8641-1.3589a59.0252 59.0252 0 00-.296-2.2537c-.2384-1.5735-.5826-3.3733-1.0546-4.8875L30.5 22c-.8284 0-1.5-.6716-1.5-1.5 0-.4112.1655-.7837.4334-1.0547-1.1207-2.0288-2.6408-3.3346-4.9574-3.4459-1.3415-.0644-2.5406.2642-3.6081.8841.0849.1881.1321.3968.1321.6165 0 .8284-.6716 1.5-1.5 1.5a1.4931 1.4931 0 01-.8338-.2529c-.598.6856-1.1382 1.4714-1.624 2.3249z"/><path opacity=".6" d="M31.2474 10.8008C31.6972 10.5419 32 10.0563 32 9.5c0-.8284-.6716-1.5-1.5-1.5S29 8.6716 29 9.5c0 .748.5475 1.3681 1.2635 1.4815.0634.3967.1314.8662.1948 1.391.2018 1.6689.3556 3.8822.1727 6.0861-.1837 2.213-.7029 4.3609-1.8025 5.9436-.8745 1.2588-2.1316 2.1844-3.957 2.4894C24.6381 26.3663 24.1118 26 23.5 26c-.611 0-1.1367.3653-1.3705.8894-3.9415-.6477-6.6479-4.0442-8.4323-7.613-.9825-1.965-1.6601-3.9348-2.0922-5.4164a35.2205 35.2205 0 01-.3002-1.094c.418-.2662.6952-.7337.6952-1.266 0-.8284-.6716-1.5-1.5-1.5S9 10.6716 9 11.5c0 .771.5816 1.4061 1.33 1.4905.0861.3361.1906.7229.315 1.1495.4429 1.5184 1.1403 3.5486 2.1578 5.5836 1.8259 3.6519 4.7678 7.4785 9.2486 8.1671.1719.639.7553 1.1093 1.4486 1.1093.6928 0 1.2759-.4697 1.4483-1.108 2.1362-.3336 3.6573-1.4162 4.7015-2.9192 1.2452-1.7923 1.7879-4.1444 1.9777-6.4314.1907-2.2962.0299-4.5828-.1764-6.2889a37.9826 37.9826 0 00-.2037-1.4517z"/></symbol><symbol id="chart-point-smooth"><path d="M8 7H7v27h27v-1H8V7z"/><path opacity=".6" fill-rule="evenodd" clip-rule="evenodd" d="M29.6498 24.9728C28.3834 26.7956 26.4157 28 23.5 28c-5.3054 0-8.6813-4.2447-10.6972-8.2764-1.0175-2.035-1.7149-4.0652-2.1578-5.5836a34.4085 34.4085 0 01-.4836-1.8394 24.105 24.105 0 01-.1148-.5189 16.1182 16.1182 0 01-.0282-.1389l-.0072-.0367-.0019-.0099-.0005-.0027c0-.0003-.0002-.0014.4912-.0935l.4914-.0922.0004.0018.0014.0075.0062.0319c.0057.0286.0144.0719.0262.1287.0237.1137.06.2819.1099.4967a33.487 33.487 0 00.4695 1.7856c.4321 1.4816 1.1097 3.4514 2.0922 5.4164C15.6813 23.2447 18.8054 27 23.5 27c2.59 0 4.2501-1.0456 5.3285-2.5978 1.0996-1.5827 1.6188-3.7306 1.8025-5.9436.1829-2.2038.0291-4.4172-.1727-6.0861a36.0596 36.0596 0 00-.2996-2.0109 25.7525 25.7525 0 00-.1064-.5602 12.5254 12.5254 0 00-.0302-.1456l-.0078-.0362-.0018-.0087-.0005-.0019.488-.109c.488-.1088.4881-.1085.4881-.1083l.0009.004.0024.0108.0088.041c.0076.0356.0185.0878.0322.1554.0274.1353.0658.3323.1106.5824a36.979 36.979 0 01.3081 2.0672c.2063 1.7061.3671 3.9928.1764 6.2889-.1898 2.287-.7325 4.6391-1.9777 6.4314z"/><path fill-rule="evenodd" clip-rule="evenodd" d="M17.0422 21.072c-1.7288 3.0371-2.6877 6.805-3.0466 9.4941l-.9912-.1323c.3698-2.7704 1.3567-6.6731 3.1687-9.8565 1.8069-3.1744 4.5165-5.7608 8.3509-5.5767 3.3296.1598 5.2035 2.4275 6.4356 5.3024.777 1.8131 1.2841 4.3677 1.5973 6.4346a60.1725 60.1725 0 01.3388 2.6394c.0354.3349.0611.6052.0779.7921.0084.0934.0146.1661.0187.2156l.0046.0567.0012.0148.0003.0039.0001.0011c0 .0001 0 .0004-.4985.0388l-.4985.0384-.0014-.0173-.0044-.0535a27.9333 27.9333 0 00-.0181-.2089 49.3827 49.3827 0 00-.0764-.7764 59.036 59.036 0 00-.3331-2.5949c-.3118-2.0581-.8047-4.5035-1.5277-6.1904-1.1708-2.7321-2.8008-4.5649-5.5644-4.6976-3.274-.1572-5.7 2.0265-7.4338 5.0726z"/></symbol><symbol id="chart-point-line-marker"><path d="M8 7H7v27h27v-1H8V7z"/><path d="M22 17.5c0 .276-.0746.5347-.2046.7568l8.9998 10.6362-.6159-4.9274C29.505 23.8188 29 23.2184 29 22.5c0-.8284.6716-1.5 1.5-1.5s1.5.6716 1.5 1.5c0 .587-.3372 1.0952-.8284 1.3416l1.0332 8.2654-11.1727-13.2041A1.4979 1.4979 0 0120.5 19a1.5 1.5 0 01-.3257-.0355l-5.4331 8.693c.1633.2401.2588.5302.2588.8425 0 .8284-.6716 1.5-1.5 1.5s-1.5-.6716-1.5-1.5.6716-1.5 1.5-1.5c.1506 0 .296.0222.4332.0635l5.3932-8.6292A1.4936 1.4936 0 0119 17.5c0-.8284.6716-1.5 1.5-1.5s1.5.6716 1.5 1.5z"/><path opacity=".6" d="M11.5797 11.5413A1.4952 1.4952 0 0012 10.5c0-.8284-.6716-1.5-1.5-1.5S9 9.6716 9 10.5s.6716 1.5 1.5 1.5c.0627 0 .1246-.0039.1853-.0113l7.225 14.45L28.5216 12.598l-3.2029 14.4128C24.5758 27.1003 24 27.7329 24 28.5c0 .8284.6716 1.5 1.5 1.5s1.5-.6716 1.5-1.5c0-.5365-.2816-1.0071-.7051-1.2723l3.3864-15.2385C30.4242 11.8997 31 11.2671 31 10.5c0-.8284-.6716-1.5-1.5-1.5s-1.5.6716-1.5 1.5c0 .3019.0892.5829.2426.8183l-10.1529 13.243-6.51-13.02z"/></symbol><symbol id="chart-point-line"><path d="M8 7H7v27h27v-1H8V7z"/><path opacity=".6" fill-rule="evenodd" clip-rule="evenodd" d="M28.5216 12.598L17.9103 26.4387l-7.8575-15.7151.8944-.4472 7.1425 14.2849 11.0135-14.3655.8849.4127-4 18-.9762-.217 3.5097-15.7935z"/><path fill-rule="evenodd" clip-rule="evenodd" d="M20.4391 16.654l10.3561 12.239-.7913-6.331.9922-.124 1.2087 9.669-11.6439-13.761-7.1369 11.419-.848-.53 7.8631-12.581z"/></symbol><symbol id="chart-stock-normal" viewBox="0 0 40 40"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 7H7v27h27v-1H8V7zm15 5h-1v4h-2v4h2v4h1v-4h2v-4h-2v-4zM13 27h-2v-7h2v-4h1v4h2v7h-2v4h-1v-4zm18-16h-2v7h2v4h1v-4h2v-7h-2V7h-1v4z"/></symbol><symbol id="chart-combo-bar-line" viewBox="0 0 40 40"><path opacity=".4" fill-rule="evenodd" clip-rule="evenodd" d="M20 10h6v24h-6V10zm-1 4h-6v20h6V14zm-7 12H6v8h6v-8zm21-5h-6v13h6V21z"/><path d="M5 26.5L15.5 16l8 8 9-9 1 1-10 10-8-8L6 27.5l-1-1z"/><path d="M5 5h1v29h29v1H5V5z"/></symbol><symbol id="chart-combo-bar-line-sec" viewBox="0 0 40 40"><path opacity=".4" fill-rule="evenodd" clip-rule="evenodd" d="M20 10h6v24h-6V10zm-1 4h-6v20h6V14zm-7 12H6v8h6v-8zm21-5h-6v13h6V21z"/><path d="M5 26.5L15.5 16l8 8 9-9 1 1-10 10-8-8L6 27.5l-1-1z"/><path d="M5 5h1v29h29v1H5V5z"/><path d="M34 32h1v1h-1zM34 30h1v1h-1zM34 28h1v1h-1zM34 26h1v1h-1zM34 24h1v1h-1zM34 22h1v1h-1zM34 20h1v1h-1zM34 18h1v1h-1zM34 16h1v1h-1zM34 14h1v1h-1zM34 12h1v1h-1zM34 10h1v1h-1zM34 8h1v1h-1zM34 6h1v1h-1z"/></symbol><symbol id="chart-combo-area-bar" viewBox="0 0 40 40"><path d="M5 5h1v29h29v1H5V5z"/><path opacity=".4" d="M6 20.5l9.5-9.5 8 8L33 9.5V34H6V20.5z"/><path fill-rule="evenodd" clip-rule="evenodd" d="M19 19h-6v15h6V19zm-7 8H6v7h6v-7zm8-4h6v11h-6V23zm13 3h-6v8h6v-8z"/></symbol><symbol id="chart-combo-custom" viewBox="0 0 40 40"><path opacity=".4" fill-rule="evenodd" clip-rule="evenodd" d="M26 10h-6v24h4v-.5l2-2V10zm7 14.5V21h-6v9.5l6-6zM13 14h6v20h-6V14zM6 26h6v8H6v-8z"/><path d="M5 26.5L15.5 16l8 8 9-9 1 1-10 10-8-8L6 27.5l-1-1z"/><path d="M5 5h1v29h18v1H5V5z"/><path fill-rule="evenodd" clip-rule="evenodd" d="M34 25l3 3 2-2v-1l-2-2h-1l-2 2zm-9 12v-3l8-8 3 3-8 8h-3z"/></symbol><symbol id="chart-spark-line" viewBox="0 0 40 40"><path fill-rule="evenodd" clip-rule="evenodd" d="M9.8636 21.8725l5.9 5.9 5-11 4.9184 4.9185 5.9574-16.0392 1.8749.6964-7.0426 18.9608-5.0816-5.0815-5 11-6.1-6.1-4.8478 8.3733-1.7308-1.0021 6.152-10.6262z"/></symbol><defs><clipPath id="clip0"><path d="M0 0h40v40H0V0z" fill="#fff"/></clipPath></defs><symbol id="chart-spark-column" viewBox="0 0 40 40"><path fill-rule="evenodd" clip-rule="evenodd" d="M26 6h-6v28h6V6zm-13 8h6v20h-6V14zm20 4h-6v16h6V18zm-21 2H6v14h6V20z"/></symbol><symbol id="chart-spark-win" viewBox="0 0 40 40"><path fill-rule="evenodd" clip-rule="evenodd" d="M6 7h6v13H6V7zm7 0h6v13h-6V7zm20 0h-6v13h6V7z"/><path opacity=".4" d="M20 20h6v13h-6z"/></symbol><symbol id="chart-surface-normal" viewBox="0 0 40 40"><path fill-rule="evenodd" clip-rule="evenodd" d="M12 6v9.0117l-6 13h2.293l-3.6465 3.6464.707.7071 4.3538-4.3535H16.5l10.5 6 3.7242-9.0001H36v-1h-4.862L33 19.5117l-10.5-11.5-9.5 6.3334V6h-1zm5.837 18.3331l1.3015-5.8068 4.4488 4.6038c-.8699.4071-1.7692.7155-2.6813.8905-1.1837.227-2.1465.3492-3.069.3125zm-1.22.8705l-.4053 1.8081H7.5629l2.7154-5.8834c.2254.2579.4988.5594.8063.8761.8627.8883 2.0441 1.9492 3.2139 2.4648.8244.3633 1.5779.6007 2.3185.7344zm.2189-.9767c-.6682-.1168-1.3586-.331-2.1341-.6728-.9818-.4327-2.0504-1.3718-2.8998-2.2464a21.8886 21.8886 0 01-.9306-1.0225l7.3108-2.0644-1.3463 6.0061zm.7791 1.0963l.0435.0025c1.0822.06 2.172-.0806 3.4358-.323 1.0942-.2099 2.177-.6015 3.2193-1.121l4.0582 4.1995-1.8616 4.4989-9.3271-5.3298.4319-1.9271zm7.6124-1.9349l3.5613 3.6853 3.0443-7.357-1.0369-1.1356c-.1724.209-.3773.45-.609.7092-1.3609 1.5224-3.0759 3.0029-4.9597 4.0981zm4.8873-5.5536l-7.3855-8.0889-3.2851 7.6576 5.0725 5.2493c1.8721-1.0475 3.5725-2.516 4.9249-4.0289.264-.2953.4915-.5658.6732-.7891zm-8.4979-8.0323l-3.1287 7.293-7.2998 2.0612 1.6013-3.4695 8.8272-5.8847z"/><g opacity=".4"><path d="M19.1385 18.5263l-1.3015 5.8068c.9225.0367 1.8853-.0855 3.069-.3125.9121-.175 1.8114-.4834 2.6813-.8905l-4.4488-4.6038zM14.7018 23.5541c.7755.3418 1.4659.556 2.1341.6728l1.3463-6.0061-7.3108 2.0644c.2407.2817.56.641.9306 1.0225.8494.8746 1.918 1.8137 2.8998 2.2464zM22.7292 9.7458l7.3855 8.0889a22.4468 22.4468 0 01-.6732.7891c-1.3524 1.5129-3.0528 2.9814-4.9249 4.0289l-5.0725-5.2493 3.2851-7.6576zM18.4881 17.0954l3.1287-7.293-8.8272 5.8847-1.6013 3.4695 7.2998-2.0612z"/></g></symbol><symbol id="chart-surface-wireframe" viewBox="0 0 40 40"><g opacity=".4"><path d="M24.4393 24.0117H17.909l-.2242 1h7.7208l-.9663-1zM29.6419 25.0117h-2.8457l-.9663-1h4.2258l-.4138 1zM16.66 25.0117l.2242-1h-3.8838l.0001-4.3277-1 .2824-.0001 4.3378-2.7075 2.7075h1.4142l2-2H16.66zM12.0005 18.9273l1-.2824v-3.0983l-.2109.1405-.7891 1.7099v1.5303z"/></g><path fill-rule="evenodd" clip-rule="evenodd" d="M12 15.0117V6h1v8.3451l9.5-6.3334 10.5 11.5-1.862 4.4999H36v1h-5.2758L27 34.0117l-10.5-6H9.7073l-4.3537 4.3535-.7071-.7071 3.6466-3.6464H6l6-13zm7.1385 3.5146l-1.9554 8.724 9.3271 5.3298 1.8616-4.4989-9.2333-9.5549zm-.6504-1.4309l3.1287-7.293-8.8272 5.8847-1.6013 3.4695 7.2998-2.0612zm4.2411-7.3496l-3.2851 7.6576 9.3446 9.6702 3.0443-7.357-9.1038-9.9708zM7.5629 27.0117l3.074-6.6603 7.5453-2.1306-1.9705 8.7909H7.5629z"/></symbol><symbol id="chart-contour-normal" viewBox="0 0 40 40"><path fill-rule="evenodd" clip-rule="evenodd" d="M7 7v27h27V7H7zm12.9994 1H8v12h11.9994V8zm1 0v12h4.9064c.3152-.5969.6174-1.2101.9097-1.8282.3812-.8058.7484-1.6267 1.1062-2.4266.6042-1.3511 1.1818-2.6423 1.7539-3.6982.4593-.8478.9389-1.5918 1.4614-2.1289C31.66 9.3804 32.2762 9 32.9999 9L33 8H20.9994zm4.3556 13h-4.3556v4.8163c.7845-.4183 1.5019-1.0117 2.1687-1.7366.8058-.8761 1.5261-1.9327 2.1869-3.0797zm-4.3556 5.9284c1.0939-.4853 2.0543-1.2472 2.9047-2.1717.986-1.072 1.8385-2.3772 2.5987-3.7567H33v12H20.9994v-6.0716zm-1-.6886V21H8v.875l.316.237.0464.0345c.0414.0306.1033.0759.184.134.1612.1162.3974.2831.6946.4843.5949.4028 1.4316.9413 2.3991 1.4846 1.9563 1.0985 4.3673 2.17 6.3799 2.2509.7038.0283 1.3615-.0642 1.9794-.2605zm0 1.0419V33H8v-9.8807c.1662.1192.3967.2815.6804.4736.6116.4141 1.4725.9683 2.4701 1.5285 1.974 1.1084 4.563 2.287 6.8293 2.3781.7163.0288 1.3882-.0497 2.0196-.2178zM33 20h-5.9673c.2375-.4648.4659-.9336.6868-1.4006.4044-.8549.7808-1.6973 1.1414-2.504.5897-1.3196 1.1369-2.5439 1.6939-3.572.4449-.8211.8727-1.4697 1.299-1.908.4258-.4378.7999-.6155 1.1462-.6155V20z"/><g opacity=".4"><path d="M19.9994 20H8V8h11.9994v12zM20.9994 8v12h4.9064c.3152-.5969.6174-1.2101.9097-1.8282.3812-.8058.7484-1.6267 1.1062-2.4266.6042-1.3511 1.1818-2.6423 1.7539-3.6982.4593-.8478.9389-1.5918 1.4614-2.1289C31.66 9.3804 32.2762 9 32.9999 9L33 8H20.9994zM8 21.875l.316.237.0464.0345c.0414.0306.1033.0759.184.134.1612.1162.3974.2831.6946.4843.5949.4028 1.4316.9413 2.3991 1.4846 1.9563 1.0985 4.3673 2.17 6.3799 2.2509.7038.0283 1.3615-.0642 1.9794-.2605V21H8v.875zM20.9994 21h4.3556c-.6608 1.147-1.3811 2.2036-2.1869 3.0797-.6668.7249-1.3842 1.3183-2.1687 1.7366V21z"/></g></symbol><symbol id="chart-contour-wireframe" viewBox="0 0 40 40"><path fill-rule="evenodd" clip-rule="evenodd" d="M7 7v27h27V7H7zm12.9994 1H8v12h11.9994V8zm1 0v12h4.9064c.3152-.5969.6174-1.2101.9097-1.8282.3812-.8058.7484-1.6267 1.1062-2.4266.6042-1.3511 1.1818-2.6423 1.7539-3.6982.4593-.8478.9389-1.5918 1.4614-2.1289C31.66 9.3804 32.2762 9 32.9999 9L33 8H20.9994zm4.3556 13h-4.3556v4.8163c.7845-.4183 1.5019-1.0117 2.1687-1.7366.8058-.8761 1.5261-1.9327 2.1869-3.0797zm-4.3556 5.9284c1.0939-.4853 2.0543-1.2472 2.9047-2.1717.986-1.072 1.8385-2.3772 2.5987-3.7567H33v12H20.9994v-6.0716zm-1-.6886V21H8v.8716l.3047.2319.0113.0085.0464.0345c.0414.0306.1033.0759.184.134.1612.1162.3974.2831.6946.4843.5949.4028 1.4316.9413 2.3991 1.4846 1.9563 1.0985 4.3673 2.17 6.3799 2.2509.7038.0283 1.3615-.0642 1.9794-.2605zm0 1.0419V33H8v-9.8807c.1662.1192.3967.2815.6804.4736.6116.4141 1.4725.9683 2.4701 1.5285 1.974 1.1084 4.563 2.287 6.8293 2.3781.7163.0288 1.3882-.0497 2.0196-.2178zM33 20h-5.9673c.2375-.4648.4659-.9336.6868-1.4006.4044-.8549.7808-1.6973 1.1414-2.504.5897-1.3196 1.1369-2.5439 1.6939-3.572.4449-.8211.8727-1.4697 1.299-1.908.4258-.4378.7999-.6155 1.1462-.6155V20z"/></symbol></svg>
    <svg aria-hidden="true" style="position:absolute;width:0;height:0" xmlns="http://www.w3.org/2000/svg" overflow="hidden"><symbol id="svg-icon-accent" viewBox="0 0 20 20"><path d="M8.459 15.146c-.435 0-.816-.105-1.143-.315-.327-.21-.58-.52-.761-.93-.181-.41-.271-.91-.271-1.501 0-.59.06-1.16.183-1.707A7.107 7.107 0 017 9.17c.23-.469.506-.879.828-1.23a3.676 3.676 0 011.099-.82 2.98 2.98 0 011.31-.294c.362 0 .674.069.938.205.269.137.49.318.667.542.18.22.322.454.424.703h.074l.468-1.303h.93L12.04 15h-.974l.22-1.553h-.059c-.22.293-.47.57-.754.828a3.81 3.81 0 01-.93.63 2.48 2.48 0 01-1.084.242zm.395-1.01c.376 0 .747-.137 1.114-.41a4.08 4.08 0 00.996-1.121 5.731 5.731 0 00.68-1.597 5.74 5.74 0 00.198-1.472c0-.503-.134-.908-.402-1.216-.264-.312-.625-.468-1.084-.468-.327 0-.635.083-.923.249a2.69 2.69 0 00-.784.68 4.41 4.41 0 00-.593 1.011c-.166.381-.295.796-.388 1.246-.088.449-.132.91-.132 1.384 0 .561.115.989.344 1.282.23.288.554.432.974.432zm3.472-8.628a.606.606 0 01-.425-.154c-.112-.103-.168-.249-.168-.44 0-.214.063-.405.19-.57.132-.172.322-.257.572-.257.19 0 .332.051.424.154.093.102.14.239.14.41 0 .264-.076.474-.228.63a.7.7 0 01-.505.227zm-2.68 0a.626.626 0 01-.44-.154c-.108-.103-.161-.249-.161-.44 0-.214.063-.405.19-.57.127-.172.313-.257.557-.257a.63.63 0 01.322.073c.083.044.144.11.183.198.044.083.066.18.066.293 0 .264-.073.474-.22.63a.677.677 0 01-.498.227z"/></symbol><symbol id="svg-icon-bracket" viewBox="0 0 20 20"><path d="M5.847 14l3.494-4.13-1.743-3.897h1.23l1.29 3.032 2.445-3.032h1.414L10.623 9.92l1.86 4.08h-1.245l-1.406-3.193L7.26 14H5.847zM14 18v-1.159c.382-.011.708-.076.977-.193.268-.112.473-.288.612-.53.145-.234.217-.542.217-.923V12.65c0-.588.137-1.058.411-1.41.28-.354.7-.583 1.264-.69v-.1c-.574-.112-.998-.345-1.272-.697-.268-.353-.403-.82-.403-1.403V5.797c0-.387-.07-.7-.21-.94a1.234 1.234 0 00-.611-.53c-.27-.118-.597-.18-.985-.185V3c.64.006 1.191.106 1.651.302.46.196.811.493 1.054.89.248.393.373.89.373 1.496V8.24c0 .403.072.728.217.974.144.24.359.417.643.53.29.111.643.167 1.062.167v1.16c-.419 0-.773.055-1.062.167a1.264 1.264 0 00-.643.53c-.145.24-.218.556-.218.948v2.604c0 .593-.129 1.089-.387 1.486a2.347 2.347 0 01-1.078.89c-.46.202-.997.303-1.612.303z"/><path d="M6 18c-.64-.006-1.194-.11-1.659-.31a2.256 2.256 0 01-1.062-.891c-.243-.398-.364-.896-.364-1.495v-2.57c0-.398-.073-.717-.217-.957a1.252 1.252 0 00-.644-.538c-.284-.112-.635-.168-1.054-.168V9.912c.419-.006.77-.062 1.054-.168.284-.112.499-.288.644-.529.144-.24.217-.56.217-.957V5.67c0-.6.129-1.095.387-1.487a2.348 2.348 0 011.078-.89C4.845 3.098 5.385 3 6 3v1.142a2.69 2.69 0 00-.984.193 1.32 1.32 0 00-.62.538c-.14.24-.21.551-.21.932v2.528c0 .582-.137 1.05-.41 1.403-.275.352-.696.585-1.264.697v.1c.578.107 1.002.337 1.271.69.269.352.403.822.403 1.41v2.579c0 .38.07.688.21.924.139.24.343.417.612.529.274.112.604.17.992.176V18z"/></symbol><symbol id="svg-icon-fraction" viewBox="0 0 20 20"><path d="M3 17L17.142 2.858l-.707-.707L2.293 16.293 3 17zm-.434-8l2.795-3.316-1.37-3.141h1.206l.944 2.309 1.822-2.309h1.365L6.633 5.736 8.109 9H6.885L5.854 6.58 3.926 9h-1.36zm8.334 9.889c-.195 0-.353-.012-.474-.035a2.876 2.876 0 01-.352-.088v-.944c.106.024.22.045.346.065.121.023.254.035.398.035.332 0 .614-.104.844-.31.234-.208.457-.5.668-.88l.375-.673-.978-6.516h1.171l.417 3.234c.035.239.062.495.082.768.019.273.033.533.04.78.012.245.018.45.018.615h.035a49.4 49.4 0 01.229-.528c.097-.227.2-.463.31-.709.114-.25.221-.469.323-.656l1.828-3.504h1.26l-4.085 7.523a5.194 5.194 0 01-.68 1.014 2.39 2.39 0 01-.79.604 2.251 2.251 0 01-.985.205z"/></symbol><symbol id="svg-icon-function" viewBox="0 0 20 20"><path d="M3.48 15.146c-.487 0-.917-.048-1.288-.146a4.472 4.472 0 01-.96-.366v-1.128c.26.156.586.303.982.44.4.136.81.204 1.23.204.415 0 .76-.056 1.033-.168.278-.112.486-.266.623-.461.141-.2.212-.433.212-.696 0-.21-.046-.39-.14-.542a1.47 1.47 0 00-.439-.454 8.426 8.426 0 00-.805-.506 6.3 6.3 0 01-.953-.637 2.4 2.4 0 01-.593-.703 1.959 1.959 0 01-.198-.901c0-.459.113-.857.337-1.194.225-.337.54-.598.945-.784.41-.185.889-.278 1.436-.278.483 0 .93.056 1.34.169.41.107.764.232 1.062.373l-.395.974a6.514 6.514 0 00-.872-.337 3.801 3.801 0 00-1.135-.153c-.46 0-.825.105-1.099.314-.268.21-.403.494-.403.85 0 .186.04.352.117.498.083.147.225.293.425.44.2.141.474.307.82.498.338.195.64.395.909.6.273.2.488.432.644.696.161.259.242.579.242.96 0 .522-.124.966-.373 1.332-.25.362-.606.638-1.07.828-.459.186-1.003.278-1.633.278zM7.444 15L9.15 6.973h1.223L8.668 15H7.444zm2.732-9.434a.73.73 0 01-.49-.168c-.128-.117-.191-.28-.191-.49 0-.162.031-.313.095-.455a.79.79 0 01.747-.469c.215 0 .376.057.483.169.108.112.162.268.162.469 0 .288-.084.517-.25.688-.166.171-.351.256-.556.256zm.41 9.434l1.722-8.064h1.076l-.19 1.545h.015c.185-.253.407-.51.666-.769.264-.263.569-.483.916-.659.351-.18.75-.27 1.194-.27.43 0 .798.08 1.105.24.308.157.545.387.71.69.167.297.25.659.25 1.083 0 .225-.017.452-.051.682a9.69 9.69 0 01-.11.615L16.819 15h-1.296l1.084-5.068c.044-.22.078-.408.103-.564a2.39 2.39 0 00.043-.44c0-.342-.092-.6-.278-.776-.185-.176-.459-.264-.82-.264-.357 0-.732.122-1.128.366-.395.245-.762.63-1.099 1.158-.337.522-.598 1.206-.783 2.05L11.89 15h-1.304z"/></symbol><symbol id="svg-icon-integral" viewBox="0 0 20 20"><path d="M5.86 17a3.094 3.094 0 01-.86-.125v-1.018c.089.035.2.069.333.103.134.039.278.059.434.059.449 0 .769-.166.96-.498.19-.328.286-.743.286-1.246V4.717c0-.971.194-1.667.58-2.087.391-.42.911-.63 1.56-.63.151 0 .305.012.46.037.16.02.29.048.387.088v1.003c-.089-.04-.2-.073-.333-.103a1.742 1.742 0 00-.434-.05c-.297 0-.533.077-.706.233a1.295 1.295 0 00-.36.638 3.436 3.436 0 00-.107.893v9.514c0 .992-.204 1.697-.613 2.117-.41.42-.938.63-1.587.63zm3.54-3l3.029-3.58-1.511-3.377h1.066l1.118 2.628 2.12-2.628h1.225l-2.907 3.421L15.152 14h-1.08l-1.218-2.768L10.626 14H9.401z"/></symbol><symbol id="svg-icon-largeOperator" viewBox="0 0 20 20"><path d="M3 16v-1l3.457-4.888L3 6V5h7v1H4.5l3.349 4.098L4.5 15H10v1H3zm7.4-2l3.029-3.58-1.511-3.377h1.066l1.118 2.628 2.12-2.628h1.225l-2.907 3.421L16.152 14h-1.08l-1.218-2.768L11.626 14h-1.225z"/></symbol><symbol id="svg-icon-limAndLog" viewBox="0 0 20 20"><path d="M1.437 15l2.27-10.637h1.135L2.579 15H1.437zm2.855 0l1.593-7.492h1.141L5.434 15H4.292zm2.55-8.805a.682.682 0 01-.458-.157c-.119-.11-.178-.262-.178-.458 0-.15.03-.292.089-.424a.738.738 0 01.697-.438c.2 0 .351.053.451.158.1.105.15.25.15.437 0 .27-.077.484-.232.643a.71.71 0 01-.52.24zM7.133 15l1.593-7.492h.93l-.178 1.449h.068c.164-.237.362-.476.595-.718.232-.246.501-.45.806-.615.31-.169.661-.253 1.053-.253.529 0 .93.153 1.203.458.274.305.433.722.479 1.251h.048c.182-.278.4-.547.656-.807.255-.264.545-.48.868-.649a2.328 2.328 0 011.08-.253c.579 0 1.026.157 1.34.472.314.314.472.77.472 1.367 0 .214-.014.417-.041.608-.023.192-.06.388-.11.588L17.025 15h-1.149l1.012-4.717c.04-.205.07-.383.089-.533.023-.15.034-.294.034-.43 0-.306-.082-.548-.246-.725-.164-.178-.417-.267-.759-.267-.242 0-.492.066-.752.198-.26.133-.513.333-.759.602-.241.264-.46.6-.656 1.005a6.403 6.403 0 00-.472 1.429L12.636 15H11.5l.998-4.696c.041-.21.07-.39.09-.54a2.79 2.79 0 00.033-.397c0-.323-.075-.576-.225-.759-.15-.186-.397-.28-.739-.28-.241 0-.494.064-.758.192a2.56 2.56 0 00-.76.601 4.46 4.46 0 00-.676 1.046c-.205.424-.37.934-.492 1.531L8.275 15H7.133z"/></symbol><symbol id="svg-icon-matrix" viewBox="0 0 20 20"><path d="M2 1v18h4v-1H3V2h3V1H2zm16 0h-4v1h3v16h-3v1h4V1z"/><path d="M14.053 10.713v5.567h-.857v-3.593c0-.152.002-.294.007-.425l.012-.393.026-.381c-.09.08-.189.165-.299.254-.11.089-.226.173-.349.254l-.692.488-.419-.596 1.695-1.175h.876zm-6-7V9.28h-.857V5.688c0-.153.002-.295.007-.426l.012-.393.026-.381c-.09.08-.189.165-.299.254-.11.089-.226.173-.349.254l-.692.488-.419-.596 1.695-1.175h.876zm-.806 12.669c-.664 0-1.162-.246-1.492-.737-.33-.495-.495-1.214-.495-2.158 0-.927.159-1.637.476-2.133.318-.499.821-.748 1.511-.748.664 0 1.164.245 1.498.736.339.49.508 1.206.508 2.145 0 .923-.159 1.636-.476 2.14-.318.503-.827.755-1.53.755zm0-.762c.39 0 .675-.173.857-.52.182-.352.273-.89.273-1.613 0-.72-.091-1.252-.273-1.6-.182-.35-.468-.526-.857-.526-.38 0-.662.175-.844.527-.178.347-.267.884-.267 1.612 0 .715.09 1.248.267 1.6.178.347.459.52.844.52zm6-6.238c-.664 0-1.162-.246-1.492-.736-.33-.496-.495-1.215-.495-2.159 0-.926.159-1.637.476-2.133.318-.499.821-.749 1.511-.749.665 0 1.164.246 1.498.737.339.49.508 1.206.508 2.145 0 .923-.159 1.636-.476 2.14-.317.503-.827.755-1.53.755zm0-.762c.39 0 .675-.173.857-.52.182-.352.273-.89.273-1.613 0-.72-.091-1.252-.273-1.6-.182-.35-.468-.526-.857-.526-.38 0-.662.175-.844.527-.178.347-.267.884-.267 1.612 0 .715.09 1.248.267 1.6.178.347.459.52.844.52z"/></symbol><symbol id="svg-icon-operator" viewBox="0 0 20 20"><path d="M3 14h14v1H3v-1zm0 2h14v1H3v-1zm2.575-3l.154-.762 5.31-9.939h1.267l1.15 9.954-.154.747H5.575zm1.604-1.106h5.04l-.601-5.61c-.03-.25-.056-.493-.08-.733a47.61 47.61 0 01-.089-1.333c-.01-.21-.014-.407-.014-.593-.113.278-.232.557-.36.835-.126.278-.263.566-.41.864-.146.293-.307.606-.483.938L7.18 11.894z"/></symbol><symbol id="svg-icon-radical" viewBox="0 0 20 20"><path d="M9.4 14l3.029-3.58-1.511-3.377h1.066l1.118 2.628 2.12-2.628h1.225l-2.907 3.421L15.152 14h-1.08l-1.218-2.768L10.626 14H9.401z"/><path d="M5.544 16.102L3.5 10.375H2.167v-.96h2.11l1.75 4.981L10.05 3H17v1h-6.34l-4.2 12.102h-.916z"/></symbol><symbol id="svg-icon-script" viewBox="0 0 20 20"><path d="M6.905 15.146c-.6 0-1.118-.12-1.553-.358a2.484 2.484 0 01-1.003-1.04c-.23-.454-.345-.996-.345-1.626 0-.65.093-1.287.279-1.912.19-.63.461-1.199.813-1.707A4.19 4.19 0 016.363 7.28a3.152 3.152 0 011.67-.454c.737 0 1.296.166 1.677.498.38.327.571.779.571 1.355 0 .405-.095.779-.285 1.12-.186.343-.474.64-.865.894-.385.25-.876.445-1.472.586-.59.137-1.291.205-2.102.205H5.28l-.03.293a6.65 6.65 0 00-.007.3c0 .645.156 1.15.469 1.517.312.361.781.542 1.406.542.386 0 .75-.054 1.092-.161a8.72 8.72 0 001.083-.44v1.047a8.019 8.019 0 01-1.09.41 4.842 4.842 0 01-1.297.154zm-1.45-4.658h.175c.625 0 1.2-.053 1.722-.16.522-.113.942-.294 1.26-.543a1.23 1.23 0 00.475-1.01.887.887 0 00-.278-.674c-.185-.176-.469-.264-.85-.264-.336 0-.668.105-.996.315-.327.205-.625.505-.893.9-.264.396-.469.875-.615 1.436zM11.644 9l2.096-2.487-1.028-2.356h.905l.708 1.732 1.366-1.732h1.024l-2.021 2.395L15.8 9h-.918l-.774-1.815L12.664 9h-1.02z"/></symbol><symbol id="svg-icon-symbols" viewBox="0 0 20 20"><path d="M9.993 5.222c-.806 0-1.475.154-2.007.462a2.81 2.81 0 00-1.18 1.303c-.258.567-.388 1.243-.388 2.03 0 .692.086 1.335.257 1.925.176.586.454 1.133.835 1.641.38.503.881.977 1.501 1.42V15H4.72v-1.099h2.747a6.264 6.264 0 01-1.201-1.208 5.796 5.796 0 01-.85-1.64 6.503 6.503 0 01-.315-2.08c0-.987.193-1.842.579-2.564a4.025 4.025 0 011.67-1.685c.732-.4 1.613-.6 2.644-.6 1.05 0 1.94.197 2.673.593.732.39 1.29.947 1.67 1.67.38.722.571 1.58.571 2.57 0 .767-.105 1.463-.315 2.088a5.747 5.747 0 01-.842 1.655 5.833 5.833 0 01-1.201 1.201h2.74V15h-4.293v-.996c.63-.44 1.135-.913 1.516-1.421s.657-1.057.828-1.648a6.75 6.75 0 00.264-1.919c0-.79-.132-1.47-.396-2.036a2.83 2.83 0 00-1.201-1.304c-.532-.302-1.204-.454-2.014-.454z"/></symbol></svg>
    <svg style="position:absolute;width:0;height:0" xmlns="http://www.w3.org/2000/svg" overflow="hidden"><symbol id="svg-icon-Point" viewBox="0 0 20 28"><g id="Point"><path id="Ellipse 121 (Stroke)" fill-rule="evenodd" clip-rule="evenodd" d="M9.5 17c1.3807 0 2.5-1.1193 2.5-2.5S10.8807 12 9.5 12 7 13.1193 7 14.5 8.1193 17 9.5 17zm0 1c1.933 0 3.5-1.567 3.5-3.5S11.433 11 9.5 11 6 12.567 6 14.5 7.567 18 9.5 18z"/></g></symbol><symbol id="svg-icon-StartPoint" viewBox="20 0 20 28"><g id="StartPoint"><path id="Union" fill-rule="evenodd" clip-rule="evenodd" d="M32 14.5c0 1.3807-1.1193 2.5-2.5 2.5S27 15.8807 27 14.5s1.1193-2.5 2.5-2.5 2.5 1.1193 2.5 2.5zm1 0c0 1.933-1.567 3.5-3.5 3.5S26 16.433 26 14.5s1.567-3.5 3.5-3.5 3.5 1.567 3.5 3.5zM29.5 18h.5v2h-1v-2h.5zm.5 4v2h-1v-2h1zm0 6v-2h-1v2h1z"/></g></symbol><symbol id="svg-icon-MiddlePoint" viewBox="40 0 20 28"><g id="MiddlePoint"><path id="Union_2" fill-rule="evenodd" clip-rule="evenodd" d="M49 2h1v2h-1V2zm.5 15c1.3807 0 2.5-1.1193 2.5-2.5S50.8807 12 49.5 12 47 13.1193 47 14.5s1.1193 2.5 2.5 2.5zm0 1c1.933 0 3.5-1.567 3.5-3.5S51.433 11 49.5 11h.5v-1h-1v1h.5c-1.933 0-3.5 1.567-3.5 3.5s1.567 3.5 3.5 3.5zm0 0H49v2h1v-2h-.5zM50 6h-1v2h1V6zm0 16v2h-1v-2h1zm0 6v-2h-1v2h1z"/></g></symbol><symbol id="svg-icon-EndPoint" viewBox="60 0 20 28"><g id="EndPoint"><path id="Union_3" fill-rule="evenodd" clip-rule="evenodd" d="M70 2h-1v2h1V2zm2 12.5c0 1.3807-1.1193 2.5-2.5 2.5S67 15.8807 67 14.5s1.1193-2.5 2.5-2.5 2.5 1.1193 2.5 2.5zm1 0c0 1.933-1.567 3.5-3.5 3.5S66 16.433 66 14.5s1.567-3.5 3.5-3.5 3.5 1.567 3.5 3.5zM69.5 11H69v-1h1v1h-.5zM69 6h1v2h-1V6z"/></g></symbol></svg>
    <script>
/*
 * (c) Copyright Ascensio System SIA 2010-2019
 *
 * This program is a free software product. You can redistribute it and/or
 * modify it under the terms of the GNU Affero General Public License (AGPL)
 * version 3 as published by the Free Software Foundation. In accordance with
 * Section 7(a) of the GNU AGPL its Section 15 shall be amended to the effect
 * that Ascensio System SIA expressly excludes the warranty of non-infringement
 * of any third-party rights.
 *
 * This program is distributed WITHOUT ANY WARRANTY; without even the implied
 * warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR  PURPOSE. For
 * details, see the GNU AGPL at: http://www.gnu.org/licenses/agpl-3.0.html
 *
 * You can contact Ascensio System SIA at 20A-12 Ernesta Birznieka-Upisha
 * street, Riga, Latvia, EU, LV-1050.
 *
 * The  interactive user interfaces in modified source and object code versions
 * of the Program must display Appropriate Legal Notices, as required under
 * Section 5 of the GNU AGPL version 3.
 *
 * Pursuant to Section 7(b) of the License you must retain the original Product
 * logo when distributing the program. Pursuant to Section 7(e) we decline to
 * grant you any rights under trademark law for use of our trademarks.
 *
 * All the Product's GUI elements, including illustrations and icon sets, as
 * well as technical writing content are licensed under the terms of the
 * Creative Commons Attribution-ShareAlike 4.0 International. See the License
 * terms at http://creativecommons.org/licenses/by-sa/4.0/legalcode
 *
 */

"use strict";
(function (window, undefined) {
	var supportedScaleValues = [1, 1.25, 1.5, 1.75, 2];
	if (window["AscDesktopEditor"] && window["AscDesktopEditor"]["GetSupportedScaleValues"])
		supportedScaleValues = window["AscDesktopEditor"]["GetSupportedScaleValues"]();

	// uncomment to debug all scales
	//supportedScaleValues = [];

	var isCorrectApplicationScaleEnabled = (function(){

		if (supportedScaleValues.length === 0)
			return false;

		var userAgent = navigator.userAgent.toLowerCase();
		var isAndroid = (userAgent.indexOf("android") > -1);
		var isIE = (userAgent.indexOf("msie") > -1 || userAgent.indexOf("trident") > -1 || userAgent.indexOf("edge") > -1);
		var isChrome = !isIE && (userAgent.indexOf("chrome") > -1);
		var isOperaOld = (!!window.opera);
		var isMobile = /android|avantgo|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od|ad)|iris|kindle|lge |maemo|midp|mmp|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|symbian|treo|up\.(browser|link)|vodafone|wap|windows (ce|phone)|xda|xiino/i.test(navigator.userAgent || navigator.vendor || window.opera);

		if (isAndroid || !isChrome || isOperaOld || isMobile || !document || !document.firstElementChild || !document.body)
			return false;

		return true;

	})();

	window['AscCommon'] = window['AscCommon'] || {};
	window['AscCommon'].checkDeviceScale = function()
	{
		var retValue = {
			zoom: 1,
			devicePixelRatio: window.devicePixelRatio,
			applicationPixelRatio: window.devicePixelRatio,
			correct : false
		};

		if (!isCorrectApplicationScaleEnabled)
			return retValue;

		var systemScaling = window.devicePixelRatio;
		var bestIndex = 0;
		var bestDistance = Math.abs(supportedScaleValues[0] - systemScaling);
		var currentDistance = 0;
		for (var i = 1, len = supportedScaleValues.length; i < len; i++)
		{
			if (true)
			{
				// это "подстройка под интерфейс" - после убирания этого в общий код - удалить
				if (Math.abs(supportedScaleValues[i] - systemScaling) > 0.0001)
				{
					if (supportedScaleValues[i] > (systemScaling - 0.0001))
						break;
				}
			}

			currentDistance = Math.abs(supportedScaleValues[i] - systemScaling);
			if (currentDistance < (bestDistance - 0.0001))
			{
				bestDistance = currentDistance;
				bestIndex = i;
			}
		}

		retValue.applicationPixelRatio = supportedScaleValues[bestIndex];
		if (Math.abs(retValue.devicePixelRatio - retValue.applicationPixelRatio) > 0.01)
		{
			retValue.zoom = retValue.devicePixelRatio / retValue.applicationPixelRatio;
			retValue.correct = true;
		}
		return retValue;
	};

	var oldZoomValue = 1;
	window['AscCommon'].correctApplicationScale = function(zoomValue)
	{
		if (!zoomValue.correct && Math.abs(zoomValue.zoom - oldZoomValue) < 0.0001)
			return;
		oldZoomValue = zoomValue.zoom;
		var firstElemStyle = document.firstElementChild.style;
		if (Math.abs(oldZoomValue - 1) < 0.001)
			firstElemStyle.zoom = "normal";
		else
			firstElemStyle.zoom = 1.0 / oldZoomValue;
	};
})(window);

</script>
    <script data-main="app" src="../../../vendor/requirejs/require.js"></script>

</body>
</html>