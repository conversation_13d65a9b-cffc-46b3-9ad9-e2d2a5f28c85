{"Common.Controllers.Chat.notcriticalErrorTitle": "Avviso", "Common.Controllers.Chat.textEnterMessage": "Inserisci il tuo messaggio qui", "Common.Controllers.ExternalDiagramEditor.textAnonymous": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalDiagramEditor.textClose": "<PERSON><PERSON>", "Common.Controllers.ExternalDiagramEditor.warningText": "L'oggetto è disabilitato perché un altro utente lo sta modificando.", "Common.Controllers.ExternalDiagramEditor.warningTitle": "Avviso", "Common.Controllers.ExternalMergeEditor.textAnonymous": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalMergeEditor.textClose": "<PERSON><PERSON>", "Common.Controllers.ExternalMergeEditor.warningText": "L'oggetto è disabilitato perché un altro utente lo sta modificando.", "Common.Controllers.ExternalMergeEditor.warningTitle": "Avviso", "Common.Controllers.ExternalOleEditor.textAnonymous": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalOleEditor.textClose": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalOleEditor.warningText": "L'oggetto è disabilitato perché è stato modificato da un altro utente.", "Common.Controllers.ExternalOleEditor.warningTitle": "Avvertimento", "Common.Controllers.History.notcriticalErrorTitle": "Avviso", "Common.Controllers.ReviewChanges.textAcceptBeforeCompare": "Al fine di confrontare i documenti, tutte le modifiche rilevate verranno considerate accettate. Vuoi continuare?", "Common.Controllers.ReviewChanges.textAtLeast": "<PERSON><PERSON>", "Common.Controllers.ReviewChanges.textAuto": "auto", "Common.Controllers.ReviewChanges.textBaseline": "Linea guida", "Common.Controllers.ReviewChanges.textBold": "Grassetto", "Common.Controllers.ReviewChanges.textBreakBefore": "Anteponi Interruzione di pagina", "Common.Controllers.ReviewChanges.textCaps": "<PERSON><PERSON>", "Common.Controllers.ReviewChanges.textCenter": "Allinea al centro", "Common.Controllers.ReviewChanges.textChar": "<PERSON><PERSON>", "Common.Controllers.ReviewChanges.textChart": "Grafico", "Common.Controllers.ReviewChanges.textColor": "Colore caratteri", "Common.Controllers.ReviewChanges.textContextual": "Non aggiungere intervallo tra paragrafi dello stesso stile", "Common.Controllers.ReviewChanges.textDeleted": "<b>Eliminato:</b>", "Common.Controllers.ReviewChanges.textDStrikeout": "Barrato do<PERSON>", "Common.Controllers.ReviewChanges.textEquation": "Equazione", "Common.Controllers.ReviewChanges.textExact": "Esatto", "Common.Controllers.ReviewChanges.textFirstLine": "Prima riga", "Common.Controllers.ReviewChanges.textFontSize": "Dimensione carattere", "Common.Controllers.ReviewChanges.textFormatted": "Formattato", "Common.Controllers.ReviewChanges.textHighlight": "Colore evidenziatore", "Common.Controllers.ReviewChanges.textImage": "<PERSON><PERSON><PERSON><PERSON>", "Common.Controllers.ReviewChanges.textIndentLeft": "Rientro a sinistra", "Common.Controllers.ReviewChanges.textIndentRight": "Rientro a destra", "Common.Controllers.ReviewChanges.textInserted": "<b>Inserito:</b>", "Common.Controllers.ReviewChanges.textItalic": "Corsivo", "Common.Controllers.ReviewChanges.textJustify": "Allineamento giustificato", "Common.Controllers.ReviewChanges.textKeepLines": "<PERSON><PERSON><PERSON> assieme le righe", "Common.Controllers.ReviewChanges.textKeepNext": "Mantieni con il successivo", "Common.Controllers.ReviewChanges.textLeft": "Allinea a sinistra", "Common.Controllers.ReviewChanges.textLineSpacing": "Line Spacing: ", "Common.Controllers.ReviewChanges.textMultiple": "multiplo", "Common.Controllers.ReviewChanges.textNoBreakBefore": "Nessuna interruzione di pagina prima", "Common.Controllers.ReviewChanges.textNoContextual": "Aggiungi intervallo tra paragrafi dello stesso stile", "Common.Controllers.ReviewChanges.textNoKeepLines": "Non tenere insieme le linee", "Common.Controllers.ReviewChanges.textNoKeepNext": "Non tenere dal prossimo", "Common.Controllers.ReviewChanges.textNot": "Non", "Common.Controllers.ReviewChanges.textNoWidow": "Non controllare righe isolate", "Common.Controllers.ReviewChanges.textNum": "Modifica numerazione", "Common.Controllers.ReviewChanges.textOff": "{0} non utilizza più rileva modifiche.", "Common.Controllers.ReviewChanges.textOffGlobal": "{0} ha disattivato la funzione di rilevamento delle modifiche per tutti.", "Common.Controllers.ReviewChanges.textOn": "{0} sta utiliz<PERSON>do ora Rileva modifiche.", "Common.Controllers.ReviewChanges.textOnGlobal": "{0} ha abilitato rileva modifiche per tutti.", "Common.Controllers.ReviewChanges.textParaDeleted": "<b><PERSON><PERSON><PERSON> eliminato</b> ", "Common.Controllers.ReviewChanges.textParaFormatted": "Paragrafo formattato", "Common.Controllers.ReviewChanges.textParaInserted": "<b>Paragrafo inserito</b> ", "Common.Controllers.ReviewChanges.textParaMoveFromDown": "<b><PERSON><PERSON><PERSON> in basso:</b>", "Common.Controllers.ReviewChanges.textParaMoveFromUp": "<b><PERSON><PERSON><PERSON> in alto:</b>", "Common.Controllers.ReviewChanges.textParaMoveTo": "<b>Spostato:</b>", "Common.Controllers.ReviewChanges.textPosition": "Posizione", "Common.Controllers.ReviewChanges.textRight": "Allinea a destra", "Common.Controllers.ReviewChanges.textShape": "Forma", "Common.Controllers.ReviewChanges.textShd": "Colore sfondo", "Common.Controllers.ReviewChanges.textShow": "Mostra modifiche a", "Common.Controllers.ReviewChanges.textSmallCaps": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Controllers.ReviewChanges.textSpacing": "Spaziatura", "Common.Controllers.ReviewChanges.textSpacingAfter": "Spaziatura dopo", "Common.Controllers.ReviewChanges.textSpacingBefore": "Spaziatura prima", "Common.Controllers.ReviewChanges.textStrikeout": "Barrato", "Common.Controllers.ReviewChanges.textSubScript": "<PERSON><PERSON><PERSON>", "Common.Controllers.ReviewChanges.textSuperScript": "Apice", "Common.Controllers.ReviewChanges.textTableChanged": "<b>Impostazioni tabella modificate</b>", "Common.Controllers.ReviewChanges.textTableRowsAdd": "<b><PERSON><PERSON><PERSON> tabella aggiunte</b>", "Common.Controllers.ReviewChanges.textTableRowsDel": "<b><PERSON><PERSON><PERSON> ta<PERSON> eliminate</b>", "Common.Controllers.ReviewChanges.textTabs": "Modifica Schede", "Common.Controllers.ReviewChanges.textTitleComparison": "Impostazioni di confronto", "Common.Controllers.ReviewChanges.textUnderline": "Sottolineato", "Common.Controllers.ReviewChanges.textUrl": "Incolla l'URL di un documento", "Common.Controllers.ReviewChanges.textWidow": "Controllare righe isolate", "Common.Controllers.ReviewChanges.textWord": "livello di parola", "Common.define.chartData.textArea": "Area", "Common.define.chartData.textAreaStacked": "Area impilata", "Common.define.chartData.textAreaStackedPer": "Area impilata al 100%", "Common.define.chartData.textBar": "A barre", "Common.define.chartData.textBarNormal": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textBarNormal3d": "Colonna 3D raggruppata", "Common.define.chartData.textBarNormal3dPerspective": "Colonna in 3D", "Common.define.chartData.textBarStacked": "Colonna in pila", "Common.define.chartData.textBarStacked3d": "Colonna 3D impilata", "Common.define.chartData.textBarStackedPer": "Colonna in pila 100%", "Common.define.chartData.textBarStackedPer3d": "Colonna 3D al 100% con impilamento", "Common.define.chartData.textCharts": "<PERSON><PERSON>", "Common.define.chartData.textColumn": "Istogramma", "Common.define.chartData.textCombo": "Combinato", "Common.define.chartData.textComboAreaBar": "Area in pila - colonna raggruppata", "Common.define.chartData.textComboBarLine": "<PERSON>onna rag<PERSON> - riga", "Common.define.chartData.textComboBarLineSecondary": "<PERSON><PERSON>na rag<PERSON> - linea sull'asse secondario", "Common.define.chartData.textComboCustom": "Combinazione personalizzata", "Common.define.chartData.textDoughnut": "Grafico a doppia ciambella", "Common.define.chartData.textHBarNormal": "Barra raggruppata", "Common.define.chartData.textHBarNormal3d": "Barra 3D raggruppata", "Common.define.chartData.textHBarStacked": "Barra in pila", "Common.define.chartData.textHBarStacked3d": "Barra 3D impilata", "Common.define.chartData.textHBarStackedPer": "Barra in pila al 100%", "Common.define.chartData.textHBarStackedPer3d": "Barra 3D 100% impilata", "Common.define.chartData.textLine": "A linee", "Common.define.chartData.textLine3d": "Linea in 3D", "Common.define.chartData.textLineMarker": "Linea con pennarelli", "Common.define.chartData.textLineStacked": "Linea impilata", "Common.define.chartData.textLineStackedMarker": "Linea impilata con pennarelli", "Common.define.chartData.textLineStackedPer": "Linea impilata al 100%", "Common.define.chartData.textLineStackedPerMarker": "Linea impilata al 100% con pennarelli", "Common.define.chartData.textPie": "A torta", "Common.define.chartData.textPie3d": "Grafico a torta in 3D", "Common.define.chartData.textPoint": "XY (A dispersione)", "Common.define.chartData.textScatter": "Grafico a dispersione", "Common.define.chartData.textScatterLine": "Grafico a dispersione con linee rette", "Common.define.chartData.textScatterLineMarker": "Grafico a dispersione con linee rette e pennarelli", "Common.define.chartData.textScatterSmooth": "Grafico a dispersione con linee leggere", "Common.define.chartData.textScatterSmoothMarker": "Grafico a dispersione con linee e indicatori", "Common.define.chartData.textStock": "Azionario", "Common.define.chartData.textSurface": "Superficie", "Common.define.smartArt.textBalance": "Equilibri", "Common.define.smartArt.textEquation": "Equazione", "Common.define.smartArt.textFunnel": "Imbuto", "Common.define.smartArt.textList": "Elenco", "Common.define.smartArt.textMatrix": "<PERSON><PERSON>", "Common.define.smartArt.textOther": "Altro", "Common.define.smartArt.textPicture": "<PERSON><PERSON><PERSON><PERSON>", "Common.Translation.textMoreButton": "più", "Common.Translation.warnFileLocked": "Non puoi modificare questo file perché è in fase di modifica in un'altra applicazione.", "Common.Translation.warnFileLockedBtnEdit": "Crea copia", "Common.Translation.warnFileLockedBtnView": "‎Aperto per la visualizzazione‎", "Common.UI.ButtonColored.textAutoColor": "Automatico", "Common.UI.ButtonColored.textNewColor": "Aggiungi Colore personalizzato", "Common.UI.Calendar.textApril": "<PERSON>e", "Common.UI.Calendar.textAugust": "Agosto", "Common.UI.Calendar.textDecember": "Dicembre", "Common.UI.Calendar.textFebruary": "<PERSON><PERSON><PERSON>", "Common.UI.Calendar.textJanuary": "Gennaio", "Common.UI.Calendar.textJuly": "<PERSON><PERSON><PERSON>", "Common.UI.Calendar.textJune": "<PERSON><PERSON><PERSON>", "Common.UI.Calendar.textMarch": "<PERSON><PERSON>", "Common.UI.Calendar.textMay": "Maggio", "Common.UI.Calendar.textMonths": "mesi", "Common.UI.Calendar.textNovember": "Novembre", "Common.UI.Calendar.textOctober": "Ottobre", "Common.UI.Calendar.textSeptember": "Settembre", "Common.UI.Calendar.textShortApril": "Apr", "Common.UI.Calendar.textShortAugust": "Ago", "Common.UI.Calendar.textShortDecember": "Dic", "Common.UI.Calendar.textShortFebruary": "Feb", "Common.UI.Calendar.textShortFriday": "Ven", "Common.UI.Calendar.textShortJanuary": "Gen", "Common.UI.Calendar.textShortJuly": "<PERSON>g", "Common.UI.Calendar.textShortJune": "<PERSON><PERSON>", "Common.UI.Calendar.textShortMarch": "Mar", "Common.UI.Calendar.textShortMay": "Mag", "Common.UI.Calendar.textShortMonday": "<PERSON>n", "Common.UI.Calendar.textShortNovember": "Novembre", "Common.UI.Calendar.textShortOctober": "<PERSON><PERSON>", "Common.UI.Calendar.textShortSaturday": "<PERSON>b", "Common.UI.Calendar.textShortSeptember": "Set", "Common.UI.Calendar.textShortSunday": "Dom", "Common.UI.Calendar.textShortThursday": "Gio", "Common.UI.Calendar.textShortTuesday": "Mar", "Common.UI.Calendar.textShortWednesday": "<PERSON><PERSON>", "Common.UI.Calendar.textYears": "<PERSON><PERSON>", "Common.UI.ComboBorderSize.txtNoBorders": "<PERSON><PERSON><PERSON> bordo", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "<PERSON><PERSON><PERSON> bordo", "Common.UI.ComboDataView.emptyComboText": "<PERSON><PERSON><PERSON> stile", "Common.UI.ExtendedColorDialog.addButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textCurrent": "Attuale", "Common.UI.ExtendedColorDialog.textHexErr": "Il valore inserito non è corretto.<br>Inserisci un valore tra 000000 e FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "Nuovo", "Common.UI.ExtendedColorDialog.textRGBErr": "Il valore inserito non è corretto.<br>Inserisci un valore numerico tra 0 e 255.", "Common.UI.HSBColorPicker.textNoColor": "Nessun colore", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "Nascondi password", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "Mostra password", "Common.UI.SearchBar.textFind": "<PERSON><PERSON><PERSON>", "Common.UI.SearchBar.tipCloseSearch": "Chiudi la ricerca", "Common.UI.SearchBar.tipNextResult": "Successivo", "Common.UI.SearchBar.tipOpenAdvancedSettings": "Aprire le impostazioni avanzate", "Common.UI.SearchBar.tipPreviousResult": "Precedente", "Common.UI.SearchDialog.textHighlight": "<PERSON><PERSON><PERSON><PERSON> ris<PERSON>ati", "Common.UI.SearchDialog.textMatchCase": "Sensibile al maiuscolo/minuscolo", "Common.UI.SearchDialog.textReplaceDef": "Inserisci testo sostitutivo", "Common.UI.SearchDialog.textSearchStart": "Inserisci il tuo testo qui", "Common.UI.SearchDialog.textTitle": "Trova e sostituisci", "Common.UI.SearchDialog.textTitle2": "<PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.textWholeWords": "Solo parole intere", "Common.UI.SearchDialog.txtBtnHideReplace": "Nascondi Sostituzione", "Common.UI.SearchDialog.txtBtnReplace": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.txtBtnReplaceAll": "Sostit<PERSON><PERSON><PERSON> tutto", "Common.UI.SynchronizeTip.textDontShow": "Non mostrare più questo messaggio", "Common.UI.SynchronizeTip.textSynchronize": "Il documento è stato modificato da un altro utente.<br><PERSON><PERSON><PERSON> per salvare le modifiche e ricaricare gli aggiornamenti.", "Common.UI.ThemeColorPalette.textRecentColors": "Colori recenti", "Common.UI.ThemeColorPalette.textStandartColors": "Colori standard", "Common.UI.ThemeColorPalette.textThemeColors": "Colori del tema", "Common.UI.Themes.txtThemeClassicLight": "<PERSON> leggera", "Common.UI.Themes.txtThemeContrastDark": "Contrasto scuro", "Common.UI.Themes.txtThemeDark": "<PERSON><PERSON>", "Common.UI.Themes.txtThemeLight": "Chiaro", "Common.UI.Themes.txtThemeSystem": "Uguale al sistema", "Common.UI.Window.cancelButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.Window.closeButtonText": "<PERSON><PERSON>", "Common.UI.Window.noButtonText": "No", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "Conferma", "Common.UI.Window.textDontShow": "Non mostrare più questo messaggio", "Common.UI.Window.textError": "Errore", "Common.UI.Window.textInformation": "Informazioni", "Common.UI.Window.textWarning": "Avviso", "Common.UI.Window.yesButtonText": "Sì", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Shift", "Common.Views.About.txtAddress": "indirizzo: ", "Common.Views.About.txtLicensee": "LICENZIATARIO", "Common.Views.About.txtLicensor": "CONCEDENTE", "Common.Views.About.txtMail": "email: ", "Common.Views.About.txtPoweredBy": "Con tecnologia", "Common.Views.About.txtTel": "tel.: ", "Common.Views.About.txtVersion": "Versione ", "Common.Views.AutoCorrectDialog.textAdd": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textApplyText": "Applica durante la digitazione", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Correzione automatica di testo", "Common.Views.AutoCorrectDialog.textAutoFormat": "Formattazione automatica durante la scrittura", "Common.Views.AutoCorrectDialog.textBulleted": "Elenchi puntati automatici", "Common.Views.AutoCorrectDialog.textBy": "Di", "Common.Views.AutoCorrectDialog.textDelete": "Elimina", "Common.Views.AutoCorrectDialog.textDoubleSpaces": "Aggiungi punto con doppio spazio", "Common.Views.AutoCorrectDialog.textFLCells": "Rendere maiuscola la prima lettera delle celle della tabella", "Common.Views.AutoCorrectDialog.textFLSentence": "Rendere maiuscola la prima lettera di frasi", "Common.Views.AutoCorrectDialog.textHyperlink": "Internet e percorsi di rete con i collegamenti ipertestuali", "Common.Views.AutoCorrectDialog.textHyphens": "‎<PERSON><PERSON><PERSON><PERSON> (--) con t<PERSON><PERSON> (-)‎", "Common.Views.AutoCorrectDialog.textMathCorrect": "Correzione automatica matematica", "Common.Views.AutoCorrectDialog.textNumbered": "Elenchi numerati automatici", "Common.Views.AutoCorrectDialog.textQuotes": "‎\"Citazioni dritte\" con \"citazioni intelligenti\"‎", "Common.Views.AutoCorrectDialog.textRecognized": "Funzioni riconosciute", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "Le seguenti espressioni sono espressioni matematiche riconosciute. Non verranno automaticamente scritte in corsivo.", "Common.Views.AutoCorrectDialog.textReplace": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textReplaceText": "Sostituisci testo con quello digitato", "Common.Views.AutoCorrectDialog.textReplaceType": "Sostituisci testo con quello digitato", "Common.Views.AutoCorrectDialog.textReset": "Reimposta", "Common.Views.AutoCorrectDialog.textResetAll": "Ripristina valori predefiniti", "Common.Views.AutoCorrectDialog.textRestore": "R<PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textTitle": "Correzione automatica", "Common.Views.AutoCorrectDialog.textWarnAddRec": "Le funzioni riconosciute possono contenere soltanto lettere da A a Z, maiuscole o minuscole.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "‎Qualsiasi espressione aggiunta verrà rimossa e quelle rimosse verranno ripristinate. Vuoi continuare?‎", "Common.Views.AutoCorrectDialog.warnReplace": "La voce di correzione automatica per %1 esiste già. Vuoi sostituirla?", "Common.Views.AutoCorrectDialog.warnReset": "Tutte le correzioni automaticche aggiunte verranno rimosse e quelle modificate verranno ripristinate ai valori originali. Vuoi continuare?‎", "Common.Views.AutoCorrectDialog.warnRestore": "La voce di correzione automatica per %1 verrà reimpostata al valore originale. Vuoi continuare?", "Common.Views.Chat.textSend": "Invia", "Common.Views.Comments.mniAuthorAsc": "Autore dalla A alla Z", "Common.Views.Comments.mniAuthorDesc": "Autore dalla Z alla A", "Common.Views.Comments.mniDateAsc": "<PERSON><PERSON> vecchio", "Common.Views.Comments.mniDateDesc": "<PERSON><PERSON> recente", "Common.Views.Comments.mniFilterGroups": "Filtra per gruppo", "Common.Views.Comments.mniPositionAsc": "Dall'alto", "Common.Views.Comments.mniPositionDesc": "<PERSON> basso", "Common.Views.Comments.textAdd": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textAddComment": "Aggiungi commento", "Common.Views.Comments.textAddCommentToDoc": "Aggiungi commento al documento", "Common.Views.Comments.textAddReply": "Aggiungi risposta", "Common.Views.Comments.textAll": "<PERSON><PERSON>", "Common.Views.Comments.textAnonym": "Ospite", "Common.Views.Comments.textCancel": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textClose": "<PERSON><PERSON>", "Common.Views.Comments.textClosePanel": "<PERSON><PERSON><PERSON> commenti", "Common.Views.Comments.textComments": "Commenti", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "Inserisci commento qui", "Common.Views.Comments.textHintAddComment": "Aggiungi commento", "Common.Views.Comments.textOpenAgain": "Apri di nuovo", "Common.Views.Comments.textReply": "Rispondi", "Common.Views.Comments.textResolve": "Risolvere", "Common.Views.Comments.textResolved": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textSort": "Ordinare commenti", "Common.Views.Comments.textViewResolved": "Non sei autorizzato a riaprire il commento", "Common.Views.Comments.txtEmpty": "Non ci sono commenti nel documento.", "Common.Views.CopyWarningDialog.textDontShow": "Non mostrare più questo messaggio", "Common.Views.CopyWarningDialog.textMsg": "Le azioni di copia, taglia e incolla utilizzando i pulsanti della barra degli strumenti dell'editor e le azioni del menu di scelta rapida verranno eseguite solo all'interno di questa scheda dell'editor. <br><br>Per copiare o incollare in o da applicazioni esterne alla scheda dell'editor, utilizzare le seguenti combinazioni di tasti:", "Common.Views.CopyWarningDialog.textTitle": "Funzioni copia/taglia/incolla", "Common.Views.CopyWarningDialog.textToCopy": "per copiare", "Common.Views.CopyWarningDialog.textToCut": "per tagliare", "Common.Views.CopyWarningDialog.textToPaste": "per incollare", "Common.Views.DocumentAccessDialog.textLoading": "Caricamento in corso...", "Common.Views.DocumentAccessDialog.textTitle": "Impostazioni di condivisione", "Common.Views.ExternalDiagramEditor.textTitle": "Editor di grafici", "Common.Views.ExternalEditor.textClose": "<PERSON><PERSON>", "Common.Views.ExternalEditor.textSave": "Salva ed esci", "Common.Views.ExternalMergeEditor.textTitle": "Destinatari Stampa unione", "Common.Views.ExternalOleEditor.textTitle": "Editor di fogli di calcolo", "Common.Views.Header.labelCoUsersDescr": "Utenti che stanno modificando il file:", "Common.Views.Header.textAddFavorite": "Segna come preferito", "Common.Views.Header.textAdvSettings": "Impostazioni avanzate", "Common.Views.Header.textBack": "Apri percorso file", "Common.Views.Header.textCompactView": "Nascondi barra degli strumenti", "Common.Views.Header.textHideLines": "Nascondi righelli", "Common.Views.Header.textHideStatusBar": "Nascondi barra di stato", "Common.Views.Header.textRemoveFavorite": "Rimuovi dai preferiti", "Common.Views.Header.textShare": "Condi<PERSON><PERSON>", "Common.Views.Header.textZoom": "Ingrandimento", "Common.Views.Header.tipAccessRights": "Gestisci i diritti di accesso al documento", "Common.Views.Header.tipDownload": "Scarica file", "Common.Views.Header.tipGoEdit": "Modifica il file corrente", "Common.Views.Header.tipPrint": "Stampa file", "Common.Views.Header.tipRedo": "R<PERSON><PERSON><PERSON>", "Common.Views.Header.tipSave": "<PERSON><PERSON>", "Common.Views.Header.tipSearch": "Cerca", "Common.Views.Header.tipUndo": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipUsers": "Visualizzare gli utenti", "Common.Views.Header.tipViewSettings": "Mostra impostazioni", "Common.Views.Header.tipViewUsers": "Mostra gli utenti e gestisci i diritti di accesso al documento", "Common.Views.Header.txtAccessRights": "Modifica diritti di accesso", "Common.Views.Header.txtRename": "Rinomina", "Common.Views.History.textCloseHistory": "<PERSON>udi cronologia", "Common.Views.History.textHide": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.History.textHideAll": "Nascondi le modifiche dettagliate", "Common.Views.History.textRestore": "R<PERSON><PERSON><PERSON>", "Common.Views.History.textShow": "Espandi", "Common.Views.History.textShowAll": "Mostra modifiche dettagliate", "Common.Views.History.textVer": "ver.", "Common.Views.ImageFromUrlDialog.textUrl": "Incolla l'URL di un'immagine:", "Common.Views.ImageFromUrlDialog.txtEmpty": "Campo obbligatorio", "Common.Views.ImageFromUrlDialog.txtNotUrl": "Il formato URL richiesto è \"http://www.example.com\"", "Common.Views.InsertTableDialog.textInvalidRowsCols": "Specifica il numero di righe e colonne valido.", "Common.Views.InsertTableDialog.txtColumns": "Numero di colonne", "Common.Views.InsertTableDialog.txtMaxText": "Il valore massimo di questo campo è {0}.", "Common.Views.InsertTableDialog.txtMinText": "Il valore minimo di questo campo è {0}.", "Common.Views.InsertTableDialog.txtRows": "Numero di righe", "Common.Views.InsertTableDialog.txtTitle": "Dimensioni tabella", "Common.Views.InsertTableDialog.txtTitleSplit": "Dividi cella", "Common.Views.LanguageDialog.labelSelect": "Seleziona la lingua del documento", "Common.Views.OpenDialog.closeButtonText": "<PERSON><PERSON>", "Common.Views.OpenDialog.txtEncoding": "Codifica", "Common.Views.OpenDialog.txtIncorrectPwd": "Password errata", "Common.Views.OpenDialog.txtOpenFile": "Immettere la password per aprire il file", "Common.Views.OpenDialog.txtPassword": "Password", "Common.Views.OpenDialog.txtPreview": "Anteprima", "Common.Views.OpenDialog.txtProtected": "Una volta inserita la password e aperto il file, verrà ripristinata la password corrente sul file.", "Common.Views.OpenDialog.txtTitle": "Seleziona parametri %1", "Common.Views.OpenDialog.txtTitleProtected": "File protetto", "Common.Views.PasswordDialog.txtDescription": "Impostare una password per proteggere questo documento", "Common.Views.PasswordDialog.txtIncorrectPwd": "La password di conferma non corrisponde", "Common.Views.PasswordDialog.txtPassword": "Password", "Common.Views.PasswordDialog.txtRepeat": "Ripeti password", "Common.Views.PasswordDialog.txtTitle": "Imposta password", "Common.Views.PasswordDialog.txtWarning": "Importante: una volta persa o dimenticata, la password non potrà più essere recuperata. Conservalo in un luogo sicuro.", "Common.Views.PluginDlg.textLoading": "Caricamento", "Common.Views.Plugins.groupCaption": "Plugin", "Common.Views.Plugins.strPlugins": "Plugin", "Common.Views.Plugins.textClosePanel": "Chiudi plugin", "Common.Views.Plugins.textLoading": "Caricamento", "Common.Views.Plugins.textStart": "Inizia", "Common.Views.Plugins.textStop": "Termina", "Common.Views.Protection.hintAddPwd": "Crittografa con password", "Common.Views.Protection.hintDelPwd": "Elimina password", "Common.Views.Protection.hintPwd": "Modifica o rimuovi password", "Common.Views.Protection.hintSignature": "Aggiungi firma digitale o riga di firma", "Common.Views.Protection.txtAddPwd": "Aggiungi password", "Common.Views.Protection.txtChangePwd": "Modifica password", "Common.Views.Protection.txtDeletePwd": "Elimina password", "Common.Views.Protection.txtEncrypt": "Crittografare", "Common.Views.Protection.txtInvisibleSignature": "Aggiungi firma digitale", "Common.Views.Protection.txtSignature": "Firma", "Common.Views.Protection.txtSignatureLine": "Aggiungi riga di firma", "Common.Views.RenameDialog.textName": "Nome del file", "Common.Views.RenameDialog.txtInvalidName": "Il nome del file non può contenere nessuno dei seguenti caratteri:", "Common.Views.ReviewChanges.hintNext": "Alla modifica successiva", "Common.Views.ReviewChanges.hintPrev": "Alla modifica precedente", "Common.Views.ReviewChanges.mniFromFile": "Documento da File", "Common.Views.ReviewChanges.mniFromStorage": "Documento da spazio di archiviazione", "Common.Views.ReviewChanges.mniFromUrl": "Documento da URL", "Common.Views.ReviewChanges.mniSettings": "Impostazioni di confronto", "Common.Views.ReviewChanges.strFast": "Rapido", "Common.Views.ReviewChanges.strFastDesc": "co-editing in teampo reale. <PERSON>tte le modifiche vengono salvate automaticamente.", "Common.Views.ReviewChanges.strStrict": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.strStrictDesc": "Usa il pulsante 'Salva' per sincronizzare le tue modifiche con quelle effettuate da altri.", "Common.Views.ReviewChanges.textEnable": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.textWarnTrackChanges": "Le revisioni verranno attivate per tutti gli utenti con accesso completo. La prossima volta che qualcuno aprirà il documento, le revisioni rimarranno attive.", "Common.Views.ReviewChanges.textWarnTrackChangesTitle": "Abilitare le revisioni per tutti?", "Common.Views.ReviewChanges.tipAcceptCurrent": "Accetta la modifica corrente", "Common.Views.ReviewChanges.tipCoAuthMode": "<PERSON>mposta modalità co-editing", "Common.Views.ReviewChanges.tipCommentRem": "Rimuovi i commenti", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Rimuovi i commenti correnti", "Common.Views.ReviewChanges.tipCommentResolve": "Risolvere i commenti", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Risolvere i commenti presenti", "Common.Views.ReviewChanges.tipCompare": "Confronta il documento corrente con un altro", "Common.Views.ReviewChanges.tipHistory": "Mostra Cronologia versioni", "Common.Views.ReviewChanges.tipRejectCurrent": "Annulla la modifica attuale", "Common.Views.ReviewChanges.tipReview": "<PERSON><PERSON><PERSON> camb<PERSON>i", "Common.Views.ReviewChanges.tipReviewView": "Selezionare il modo in cui si desidera visualizzare le modifiche", "Common.Views.ReviewChanges.tipSetDocLang": "Imposta lingua del documento", "Common.Views.ReviewChanges.tipSetSpelling": "Controllo ortografico", "Common.Views.ReviewChanges.tipSharing": "Gestisci i diritti di accesso al documento", "Common.Views.ReviewChanges.txtAccept": "Accetta", "Common.Views.ReviewChanges.txtAcceptAll": "Accetta tutte le modifiche", "Common.Views.ReviewChanges.txtAcceptChanges": "Accetta modifiche", "Common.Views.ReviewChanges.txtAcceptCurrent": "Accetta la modifica corrente", "Common.Views.ReviewChanges.txtChat": "Cha<PERSON>", "Common.Views.ReviewChanges.txtClose": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtCoAuthMode": "Modalità di co-editing", "Common.Views.ReviewChanges.txtCommentRemAll": "Rimuovi tutti i commenti", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Rimuovi i commenti correnti", "Common.Views.ReviewChanges.txtCommentRemMy": "Rimuovi i miei commenti", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Rimuovi i miei commenti attuali", "Common.Views.ReviewChanges.txtCommentRemove": "Elimina", "Common.Views.ReviewChanges.txtCommentResolve": "Risolvere", "Common.Views.ReviewChanges.txtCommentResolveAll": "Risolvere tutti i commenti", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Risolvere i commenti presenti", "Common.Views.ReviewChanges.txtCommentResolveMy": "Risolvere i miei commenti", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "‎Risolvere i miei commenti presenti", "Common.Views.ReviewChanges.txtCompare": "Confronta", "Common.Views.ReviewChanges.txtDocLang": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtEditing": "Modifica", "Common.Views.ReviewChanges.txtFinal": "<PERSON><PERSON> le modifiche accettate {0}", "Common.Views.ReviewChanges.txtFinalCap": "Finale", "Common.Views.ReviewChanges.txtHistory": "Cronologia delle versioni", "Common.Views.ReviewChanges.txtMarkup": "<PERSON><PERSON> le modifiche {0}", "Common.Views.ReviewChanges.txtMarkupCap": "Markup e balloons", "Common.Views.ReviewChanges.txtMarkupSimple": "<PERSON><PERSON> cambiam<PERSON>i {0}<br><PERSON><PERSON><PERSON><PERSON><PERSON> le notifiche balloons", "Common.Views.ReviewChanges.txtMarkupSimpleCap": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtNext": "Successivo", "Common.Views.ReviewChanges.txtOff": "Disattiva per me", "Common.Views.ReviewChanges.txtOffGlobal": "Disattiva per me e per tutti", "Common.Views.ReviewChanges.txtOn": "Attiva per me", "Common.Views.ReviewChanges.txtOnGlobal": "Attiva per me e per tutti", "Common.Views.ReviewChanges.txtOriginal": "<PERSON><PERSON> le modifiche rifiutate {0}", "Common.Views.ReviewChanges.txtOriginalCap": "Originale", "Common.Views.ReviewChanges.txtPrev": "Precedente", "Common.Views.ReviewChanges.txtPreview": "Anteprima", "Common.Views.ReviewChanges.txtReject": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectAll": "<PERSON><PERSON><PERSON> tutte le modifiche", "Common.Views.ReviewChanges.txtRejectChanges": "<PERSON><PERSON><PERSON> modific<PERSON>", "Common.Views.ReviewChanges.txtRejectCurrent": "Annulla la modifica attuale", "Common.Views.ReviewChanges.txtSharing": "Condivisione", "Common.Views.ReviewChanges.txtSpelling": "Controllo ortografico", "Common.Views.ReviewChanges.txtTurnon": "<PERSON><PERSON><PERSON> camb<PERSON>i", "Common.Views.ReviewChanges.txtView": "Modalità di visualizzazione", "Common.Views.ReviewChangesDialog.textTitle": "<PERSON><PERSON><PERSON> mod<PERSON>", "Common.Views.ReviewChangesDialog.txtAccept": "Accetta", "Common.Views.ReviewChangesDialog.txtAcceptAll": "Accetta tutte le modifiche", "Common.Views.ReviewChangesDialog.txtAcceptCurrent": "Accetta la modifica corrente", "Common.Views.ReviewChangesDialog.txtNext": "Alla modifica successiva", "Common.Views.ReviewChangesDialog.txtPrev": "Alla modifica precedente", "Common.Views.ReviewChangesDialog.txtReject": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChangesDialog.txtRejectAll": "<PERSON><PERSON><PERSON> tutte le modifiche", "Common.Views.ReviewChangesDialog.txtRejectCurrent": "Annulla la modifica attuale", "Common.Views.ReviewPopover.textAdd": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textAddReply": "Aggiungi risposta", "Common.Views.ReviewPopover.textCancel": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textClose": "<PERSON><PERSON>", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textFollowMove": "<PERSON><PERSON><PERSON> mossa", "Common.Views.ReviewPopover.textMention": "+mention fornirà l'accesso al documento e invierà un'e-mail", "Common.Views.ReviewPopover.textMentionNotify": "+mention avviserà l'utente via e-mail", "Common.Views.ReviewPopover.textOpenAgain": "Apri di nuovo", "Common.Views.ReviewPopover.textReply": "Rispondi", "Common.Views.ReviewPopover.textResolve": "Risolvere", "Common.Views.ReviewPopover.textViewResolved": "Non sei autorizzato a riaprire il commento", "Common.Views.ReviewPopover.txtAccept": "Accettare", "Common.Views.ReviewPopover.txtDeleteTip": "Eliminare", "Common.Views.ReviewPopover.txtEditTip": "Modificare", "Common.Views.ReviewPopover.txtReject": "Rifiutare", "Common.Views.SaveAsDlg.textLoading": "Caricamento", "Common.Views.SaveAsDlg.textTitle": "Cartella di salvataggio", "Common.Views.SearchPanel.textCaseSensitive": "Sensibile al maiuscolo/minuscolo", "Common.Views.SearchPanel.textCloseSearch": "Chiudi la ricerca", "Common.Views.SearchPanel.textFind": "<PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textFindAndReplace": "Trova e sostituisci", "Common.Views.SearchPanel.textMatchUsingRegExp": "Corrispondenza usando espressioni regolari", "Common.Views.SearchPanel.textNoMatches": "<PERSON>ess<PERSON> corrispondenza", "Common.Views.SearchPanel.textNoSearchResults": "<PERSON>essun risultato di ricerca", "Common.Views.SearchPanel.textReplace": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textReplaceAll": "Sostit<PERSON><PERSON><PERSON> tutto", "Common.Views.SearchPanel.textReplaceWith": "Sostituire con", "Common.Views.SearchPanel.textSearchAgain": "{0}Esegui una nuova ricerca{1} per ottenere risultati accurati.", "Common.Views.SearchPanel.textSearchHasStopped": "La ricerca è stata interrotta", "Common.Views.SearchPanel.textSearchResults": "Risultati della ricerca: {0}/{1}", "Common.Views.SearchPanel.textTooManyResults": "Ci sono troppi risultati per essere mostrati qui", "Common.Views.SearchPanel.textWholeWords": "Solo parole intere", "Common.Views.SearchPanel.tipNextResult": "Successivo", "Common.Views.SearchPanel.tipPreviousResult": "Precedente", "Common.Views.SelectFileDlg.textLoading": "Caricamento", "Common.Views.SelectFileDlg.textTitle": "Seleziona Sorgente Dati", "Common.Views.SignDialog.textBold": "Grassetto", "Common.Views.SignDialog.textCertificate": "Certificato", "Common.Views.SignDialog.textChange": "Cambia", "Common.Views.SignDialog.textInputName": "Inserisci nome firmatario", "Common.Views.SignDialog.textItalic": "Corsivo", "Common.Views.SignDialog.textNameError": "Il nome firmatario non può essere vuoto.", "Common.Views.SignDialog.textPurpose": "Motivo della firma del documento", "Common.Views.SignDialog.textSelect": "Seleziona", "Common.Views.SignDialog.textSelectImage": "Seleziona Immagine", "Common.Views.SignDialog.textSignature": "La firma appare come", "Common.Views.SignDialog.textTitle": "Firma Documento", "Common.Views.SignDialog.textUseImage": "oppure clicca 'Scegli immagine' per utilizzare un'immagine come firma", "Common.Views.SignDialog.textValid": "Valido dal %1 al %2", "Common.Views.SignDialog.tipFontName": "<PERSON><PERSON> carattere", "Common.Views.SignDialog.tipFontSize": "Dimensione carattere", "Common.Views.SignSettingsDialog.textAllowComment": "Consenti al firmatario di aggiungere commenti nella finestra di dialogo della firma", "Common.Views.SignSettingsDialog.textInfoEmail": "E-mail", "Common.Views.SignSettingsDialog.textInfoName": "Nome", "Common.Views.SignSettingsDialog.textInfoTitle": "<PERSON><PERSON> del Firmatario", "Common.Views.SignSettingsDialog.textInstructions": "Istruzioni per i Firmatari", "Common.Views.SignSettingsDialog.textShowDate": "Mostra la data nella riga di Firma", "Common.Views.SignSettingsDialog.textTitle": "Impostazioni firma", "Common.Views.SignSettingsDialog.txtEmpty": "Campo obbligatorio", "Common.Views.SymbolTableDialog.textCharacter": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textCode": "valore Unicode HEX", "Common.Views.SymbolTableDialog.textCopyright": "Segno di copyright", "Common.Views.SymbolTableDialog.textDCQuote": "<PERSON><PERSON> virgolette alte doppie", "Common.Views.SymbolTableDialog.textDOQuote": "<PERSON>i virgolette alte doppie", "Common.Views.SymbolTableDialog.textEllipsis": "<PERSON>si oriz<PERSON>", "Common.Views.SymbolTableDialog.textEmDash": "Lineetta emme", "Common.Views.SymbolTableDialog.textEmSpace": "Spazio emme", "Common.Views.SymbolTableDialog.textEnDash": "Lineetta enne", "Common.Views.SymbolTableDialog.textEnSpace": "Spazio enne", "Common.Views.SymbolTableDialog.textFont": "Tipo di carattere", "Common.Views.SymbolTableDialog.textNBHyphen": "Trattino senza interruzioni", "Common.Views.SymbolTableDialog.textNBSpace": "Spazio senza interruzioni", "Common.Views.SymbolTableDialog.textPilcrow": "Piede di mosca", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 spazio emme", "Common.Views.SymbolTableDialog.textRange": "Intervallo", "Common.Views.SymbolTableDialog.textRecent": "Simboli usati di recente", "Common.Views.SymbolTableDialog.textRegistered": "Firma registarta", "Common.Views.SymbolTableDialog.textSCQuote": "<PERSON><PERSON> virgolette alte singole", "Common.Views.SymbolTableDialog.textSection": "Sezione firma", "Common.Views.SymbolTableDialog.textShortcut": "Tasto di scelta rapida", "Common.Views.SymbolTableDialog.textSHyphen": "<PERSON><PERSON><PERSON> morbido", "Common.Views.SymbolTableDialog.textSOQuote": "<PERSON>i virgolette alte singole", "Common.Views.SymbolTableDialog.textSpecial": "Caratteri speciali", "Common.Views.SymbolTableDialog.textSymbols": "Simboli", "Common.Views.SymbolTableDialog.textTitle": "Simbolo", "Common.Views.SymbolTableDialog.textTradeMark": "Simbolo del marchio", "Common.Views.UserNameDialog.textDontShow": "‎Non chiedere di nuovo‎", "Common.Views.UserNameDialog.textLabel": "Etichetta:", "Common.Views.UserNameDialog.textLabelError": "L'etichetta non deve essere vuota.", "DE.Controllers.LeftMenu.leavePageText": "Tutte le modifiche non salvate nel documento verranno perse.<br> <PERSON><PERSON><PERSON> \"<PERSON><PERSON><PERSON>\" e poi \"Salva\" per salvarle. Clicca \"OK\" per annullare tutte le modifiche non salvate.", "DE.Controllers.LeftMenu.newDocumentTitle": "Documento senza nome", "DE.Controllers.LeftMenu.notcriticalErrorTitle": "Avviso", "DE.Controllers.LeftMenu.requestEditRightsText": "Richiesta di autorizzazione di modifica...", "DE.Controllers.LeftMenu.textLoadHistory": "Caricamento Cronologia in corso....", "DE.Controllers.LeftMenu.textNoTextFound": "I dati da cercare non sono stati trovati. Modifica i parametri di ricerca.", "DE.Controllers.LeftMenu.textReplaceSkipped": "La sostituzione è stata effettuata. {0} casi sono stati saltati.", "DE.Controllers.LeftMenu.textReplaceSuccess": "La ricerca è stata effettuata. Casi sostituiti: {0}", "DE.Controllers.LeftMenu.txtCompatible": "Il documento verrà salvato nel nuovo formato questo consentirà di utilizzare tutte le funzionalità dell'editor, ma potrebbe influire sul layout del documento. <br> Utilizzare l'opzione \"Compatibilità\" nelle impostazioni avanzate se si desidera rendere i file compatibili con le versioni precedenti di MS Word.", "DE.Controllers.LeftMenu.txtUntitled": "<PERSON>za titolo", "DE.Controllers.LeftMenu.warnDownloadAs": "Se continui a salvare in questo formato tutte le funzioni eccetto il testo andranno perse.<br>Sei sicuro di voler continuare?", "DE.Controllers.LeftMenu.warnDownloadAsPdf": "Il tuo {0} verrà convertito in un formato modificabile. Questo potrebbe richiedere del tempo. Il documento risultante sarà ottimizzato per consentirti di modificare il testo, quindi potrebbe non apparire esattamente come l'originale {0}, soprattutto se il file originale conteneva molti elementi grafici.", "DE.Controllers.LeftMenu.warnDownloadAsRTF": "Se continui a salvare in questo formato, parte della formattazione potrebbe andare persa.<br>Sei sicuro di voler continuare?", "DE.Controllers.LeftMenu.warnReplaceString": "{0} non è un carattere speciale valido per il campo della sostituzione.", "DE.Controllers.Main.applyChangesTextText": "Caricamento delle modifiche in corso...", "DE.Controllers.Main.applyChangesTitleText": "Caricamento delle modifiche", "DE.Controllers.Main.convertationTimeoutText": "È stato superato il tempo limite della conversione.", "DE.Controllers.Main.criticalErrorExtText": "Clicca su \"OK\" per ritornare all'elenco dei documenti.", "DE.Controllers.Main.criticalErrorTitle": "Errore", "DE.Controllers.Main.downloadErrorText": "Scaricamento fallito.", "DE.Controllers.Main.downloadMergeText": "Scaricamento in corso...", "DE.Controllers.Main.downloadMergeTitle": "Scaricamento", "DE.Controllers.Main.downloadTextText": "Scaricamento del documento in corso...", "DE.Controllers.Main.downloadTitleText": "Scaricamento del documento", "DE.Controllers.Main.errorAccessDeny": "Stai tentando di eseguire un'azione per la quale non disponi di permessi sufficienti.<br>Si prega di contattare l'amministratore del Server dei Documenti.", "DE.Controllers.Main.errorBadImageUrl": "URL dell'immagine errato", "DE.Controllers.Main.errorCoAuthoringDisconnect": "Connessione al server persa. Il documento non può essere modificato in questo momento.", "DE.Controllers.Main.errorComboSeries": "Per creare un grafico a combinazione, seleziona almeno due serie di dati.", "DE.Controllers.Main.errorCompare": "La funzione Confronta documenti non è disponibile durante la modifica in co-editing. ", "DE.Controllers.Main.errorConnectToServer": "Impossibile salvare il documento. Verifica le impostazioni di connessione o contatta il tuo amministratore.<br>Quando fai clic sul pulsante 'OK', ti verrà chiesto di scaricare il documento.", "DE.Controllers.Main.errorDatabaseConnection": "Errore esterno. <br> Errore di connessione al database. Si prega di contattare l'assistenza nel caso in cui l'errore persista.", "DE.Controllers.Main.errorDataEncrypted": "Le modifiche crittografate sono state ricevute, non possono essere decifrate.", "DE.Controllers.Main.errorDataRange": "Intervallo di dati non corretto.", "DE.Controllers.Main.errorDefaultMessage": "Codice errore: %1", "DE.Controllers.Main.errorDirectUrl": "Si prega di verificare il link al documento. <br>Quest<PERSON> collegamento deve essere un collegamento diretto al file da scaricare.", "DE.Controllers.Main.errorEditingDownloadas": "Si è verificato un errore durante il lavoro con il documento.<br>Utilizza l'opzione 'Scaricare come' per salvare la copia di backup del file sul disco rigido del computer.", "DE.Controllers.Main.errorEditingSaveas": "Si è verificato un errore durante il lavoro con il documento.<br>Utilizza l'opzione 'Salvare come ...' per salvare la copia di backup del file sul disco rigido del computer.", "DE.Controllers.Main.errorEmailClient": "Non è stato trovato nessun client di posta elettronica.", "DE.Controllers.Main.errorEmptyTOC": "Inizia a creare un sommario applicando uno stile di intestazione dalla galleria Stili al testo selezionato.", "DE.Controllers.Main.errorFilePassProtect": "Il file è protetto da password e non può essere aperto.", "DE.Controllers.Main.errorFileSizeExceed": "La dimensione del file supera la limitazione impostata per il tuo server.<br>Per i dettagli, contatta l'amministratore del Document server.", "DE.Controllers.Main.errorForceSave": "Si è verificato un errore durante il salvataggio del file. Utilizza l'opzione 'Scaricare come' per salvare il file sul disco rigido del computer o riprova più tardi.", "DE.Controllers.Main.errorKeyEncrypt": "Descrittore di chiave scon<PERSON>uto", "DE.Controllers.Main.errorKeyExpire": "Descrittore di chiave scaduto", "DE.Controllers.Main.errorLoadingFont": "I caratteri non sono caricati.<br>Si prega di contattare il tuo amministratore di Document Server.", "DE.Controllers.Main.errorMailMergeLoadFile": "Caricamento del documento non riuscito. Si prega di selezionare un altro file.", "DE.Controllers.Main.errorMailMergeSaveFile": "Unione non riuscita", "DE.Controllers.Main.errorNoTOC": "Non è presente alcun indice da aggiornare. Puoi inserirne uno dalla scheda Riferimenti.", "DE.Controllers.Main.errorProcessSaveResult": "Salvataggio non riuscito", "DE.Controllers.Main.errorServerVersion": "La versione dell'editor è stata aggiornata. La pagina verrà ricaricata per applicare le modifiche.", "DE.Controllers.Main.errorSessionAbsolute": "La sessione di modifica del documento è scaduta. Si prega di ricaricare la pagina.", "DE.Controllers.Main.errorSessionIdle": "È passato troppo tempo dall'ultima modifica apportata al documento. Si prega di ricaricare la pagina.", "DE.Controllers.Main.errorSessionToken": "La connessione al server è stata interrotta. Si prega di ricaricare la pagina.", "DE.Controllers.Main.errorSetPassword": "Impossibile impostare la password.", "DE.Controllers.Main.errorStockChart": "<PERSON>ighe ordinate in modo errato. Per creare un grafico azionario posizionare i dati sul foglio nel seguente ordine:<br> prezzo di apertura, prezzo massimo, prezzo minimo, prezzo di chiusura.", "DE.Controllers.Main.errorSubmit": "Invio fallito.", "DE.Controllers.Main.errorTextFormWrongFormat": "Il valore inserito non corrisponde al formato del campo.", "DE.Controllers.Main.errorToken": "Il token di sicurezza del documento non è stato creato correttamente.<br>Si prega di contattare l'amministratore del Server dei Documenti.", "DE.Controllers.Main.errorTokenExpire": "Il token di sicurezza del documento è scaduto.<br>Si prega di contattare l'amministratore del Server dei Documenti.", "DE.Controllers.Main.errorUpdateVersion": "La versione del file è stata modificata. La pagina verrà ricaricata.", "DE.Controllers.Main.errorUpdateVersionOnDisconnect": "La connessione Internet è stata ripristinata e la versione del file è stata modificata.<br>Prima di poter continuare a lavorare, devi scaricare il file o copiarne il contenuto per assicurarti che nulla vada perso, quindi ricarica questa pagina.", "DE.Controllers.Main.errorUserDrop": "Impossibile accedere al file in questo momento.", "DE.Controllers.Main.errorUsersExceed": "È stato superato il numero di utenti consentito dal piano tariffario", "DE.Controllers.Main.errorViewerDisconnect": "La connessione è stata persa. È ancora possibile visualizzare il documento, <br> ma non sarà possibile scaricarlo o stamparlo fino a quando la connessione non sarà ripristinata e la pagina sarà ricaricata.", "DE.Controllers.Main.leavePageText": "Ci sono delle modifiche non salvate in questo documento. Clicca su 'Rimani in questa pagina', poi su 'Salva' per salvarle. Clicca su 'Esci da questa pagina' per scartare tutte le modifiche non salvate.", "DE.Controllers.Main.leavePageTextOnClose": "Tutte le modifiche non salvate nel documento verranno perse.<br> <PERSON><PERSON><PERSON> \"<PERSON><PERSON><PERSON>\" e poi \"Salva\" per salvarle. Clicca \"OK\" per annullare tutte le modifiche non salvate.", "DE.Controllers.Main.loadFontsTextText": "Caricamento dei dati in corso...", "DE.Controllers.Main.loadFontsTitleText": "Caricamento dei dati", "DE.Controllers.Main.loadFontTextText": "Caricamento dei dati in corso...", "DE.Controllers.Main.loadFontTitleText": "Caricamento dei dati", "DE.Controllers.Main.loadImagesTextText": "Caricamento delle immagini in corso...", "DE.Controllers.Main.loadImagesTitleText": "Caricamento delle immagini", "DE.Controllers.Main.loadImageTextText": "Caricamento dell'immagine in corso...", "DE.Controllers.Main.loadImageTitleText": "Caricamento dell'immagine", "DE.Controllers.Main.loadingDocumentTextText": "Caricamento del documento in corso...", "DE.Controllers.Main.loadingDocumentTitleText": "Caricamento del documento", "DE.Controllers.Main.mailMergeLoadFileText": "Caricamento origine dati in corso...", "DE.Controllers.Main.mailMergeLoadFileTitle": "Caricamento origine dati", "DE.Controllers.Main.notcriticalErrorTitle": "Avviso", "DE.Controllers.Main.openErrorText": "Si è verificato un errore durante l'apertura del file", "DE.Controllers.Main.openTextText": "Apertura del documento in corso...", "DE.Controllers.Main.openTitleText": "Apertura del documento", "DE.Controllers.Main.printTextText": "Stampa del documento in corso...", "DE.Controllers.Main.printTitleText": "Stampa del documento", "DE.Controllers.Main.reloadButtonText": "Ricarica pagina", "DE.Controllers.Main.requestEditFailedMessageText": "Qualcuno sta modificando questo documento in questo momento. Si prega di provare più tardi.", "DE.Controllers.Main.requestEditFailedTitleText": "Accesso negato", "DE.Controllers.Main.saveErrorText": "Si è verificato un errore durante il salvataggio del file", "DE.Controllers.Main.saveErrorTextDesktop": "Questo file non può essere salvato o creato. <br>I possibili motivi sono: <br>1. Il file è di sola lettura. <br>2. Il file è in fase di modifica da parte di altri utenti. <br>3. Il disco è pieno oppure è danneggiato.", "DE.Controllers.Main.saveTextText": "Salvataggio del documento in corso...", "DE.Controllers.Main.saveTitleText": "Salvataggio del documento", "DE.Controllers.Main.scriptLoadError": "La connessione è troppo lenta, alcuni componenti non possono essere caricati. Si prega di ricaricare la pagina.", "DE.Controllers.Main.sendMergeText": "Invio unione in corso...", "DE.Controllers.Main.sendMergeTitle": "Invio unione", "DE.Controllers.Main.splitDividerErrorText": "Il numero di righe deve essere un divisore di %1.", "DE.Controllers.Main.splitMaxColsErrorText": "Il numero di colonne deve essere inferiore a %1.", "DE.Controllers.Main.splitMaxRowsErrorText": "Il numero di righe deve essere inferiore a %1.", "DE.Controllers.Main.textAnonymous": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.textApplyAll": "Applica a tutte le equazioni", "DE.Controllers.Main.textBuyNow": "Visita il sito web", "DE.Controllers.Main.textChangesSaved": "Tutte le modifiche sono state salvate", "DE.Controllers.Main.textClose": "<PERSON><PERSON>", "DE.Controllers.Main.textCloseTip": "Clicca su per chiudere la notifica", "DE.Controllers.Main.textContactUs": "Contatta il team di vendite", "DE.Controllers.Main.textContinue": "Continua", "DE.Controllers.Main.textConvertEquation": "Questa equazione è stata creata con una vecchia versione dell'editor di equazioni che non è più supportata.Per modific<PERSON>, convertire l'equazione nel formato ML di Office Math.<br>Convertire ora?", "DE.Controllers.Main.textCustomLoader": "Si prega di notare che, in base ai termini della licenza, non si ha il diritto di modificare il caricatore.<br>Si prega di contattare il nostro reparto vendite per ottenere un preventivo.", "DE.Controllers.Main.textDisconnect": "Connessione persa", "DE.Controllers.Main.textGuest": "Ospite", "DE.Controllers.Main.textHasMacros": "Il file contiene macro automatiche. <br> Vuoi eseguire le macro?", "DE.Controllers.Main.textLearnMore": "Per saperne di più", "DE.Controllers.Main.textLoadingDocument": "Caricamento del documento", "DE.Controllers.Main.textLongName": "Si prega di immettere un nome che contenga meno di 128 caratteri.", "DE.Controllers.Main.textNoLicenseTitle": "Limite di licenza raggiunto", "DE.Controllers.Main.textPaidFeature": "Funzionalità a pagamento", "DE.Controllers.Main.textReconnect": "Connessione ripristinata", "DE.Controllers.Main.textRemember": "Ricorda la mia scelta per tutti i file", "DE.Controllers.Main.textRememberMacros": "Ricordare la mia scelta per tutte le macro", "DE.Controllers.Main.textRenameError": "Il nome utente non può essere vuoto.", "DE.Controllers.Main.textRenameLabel": "Immettere un nome da utilizzare per la collaborazione", "DE.Controllers.Main.textRequestMacros": "Una macro effettua una richiesta all'URL. Vuoi consentire la richiesta al %1?", "DE.Controllers.Main.textShape": "Forma", "DE.Controllers.Main.textStrict": "Modalità Rigorosa", "DE.Controllers.Main.textText": "<PERSON><PERSON>", "DE.Controllers.Main.textTryUndoRedo": "Le funzioni Annulla/Ripristina sono disabilitate per la Modalità di Co-editing Veloce.<br><PERSON><PERSON><PERSON> il pulsante 'Modalità Rigorosa' per passare alla Modalità di Co-editing Rigorosa per poter modificare il file senza l'interferenza di altri utenti e inviare le modifiche solamente dopo averle salvate. Puoi passare da una modalità all'altra di co-editing utilizzando le Impostazioni avanzate dell'editor.", "DE.Controllers.Main.textTryUndoRedoWarn": "Le funzioni Annulla/Ripeti sono disattivate nella modalità rapida in modifica collaborativa.", "DE.Controllers.Main.textUndo": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.titleLicenseExp": "La licenza è scaduta", "DE.Controllers.Main.titleServerVersion": "L'editor è stato aggiornato", "DE.Controllers.Main.titleUpdateVersion": "Versione Modificata", "DE.Controllers.Main.txtAbove": "Sopra", "DE.Controllers.Main.txtArt": "Il tuo testo qui", "DE.Controllers.Main.txtBasicShapes": "Forme di base", "DE.Controllers.Main.txtBelow": "al di sotto", "DE.Controllers.Main.txtBookmarkError": "Errore! Segnalibro non definito.", "DE.Controllers.Main.txtButtons": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtCallouts": "Chiamate", "DE.Controllers.Main.txtCharts": "<PERSON><PERSON>", "DE.Controllers.Main.txtChoose": "<PERSON><PERSON><PERSON> un oggetto", "DE.Controllers.Main.txtClickToLoad": "Clicca per caricare l'immagine", "DE.Controllers.Main.txtCurrentDocument": "Documento Corrente", "DE.Controllers.Main.txtDiagramTitle": "<PERSON><PERSON> gra<PERSON>o", "DE.Controllers.Main.txtEditingMode": "Imposta la modalità di modifica...", "DE.Controllers.Main.txtEndOfFormula": "Fine inaspettata della formula", "DE.Controllers.Main.txtEnterDate": "Inserisci una data", "DE.Controllers.Main.txtErrorLoadHistory": "Caricamento della cronologia non riuscito", "DE.Controllers.Main.txtEvenPage": "<PERSON>gina pari", "DE.Controllers.Main.txtFiguredArrows": "Frecce figurate", "DE.Controllers.Main.txtFirstPage": "Prima Pagina", "DE.Controllers.Main.txtFooter": "Piè di pagina", "DE.Controllers.Main.txtFormulaNotInTable": "La formula non in tabella", "DE.Controllers.Main.txtHeader": "Intestazione", "DE.Controllers.Main.txtHyperlink": "Collegamento ipertestuale", "DE.Controllers.Main.txtIndTooLarge": "Indice troppo grande", "DE.Controllers.Main.txtLines": "Linee", "DE.Controllers.Main.txtMainDocOnly": "Errore! Solo documento principale.", "DE.Controllers.Main.txtMath": "Matematica", "DE.Controllers.Main.txtMissArg": "<PERSON><PERSON><PERSON> mancante", "DE.Controllers.Main.txtMissOperator": "Operatore mancante", "DE.Controllers.Main.txtNeedSynchronize": "Ci sono aggiornamenti disponibili", "DE.Controllers.Main.txtNone": "<PERSON><PERSON>", "DE.Controllers.Main.txtNoTableOfContents": "Non ci sono titoli nel documento. Applicare uno stile di titolo al testo in modo che appaia nel sommario.", "DE.Controllers.Main.txtNoTableOfFigures": "Nessuna voce della tabella delle cifre trovata.", "DE.Controllers.Main.txtNoText": "Errore! Nessuno stile specificato per il testo nel documento.", "DE.Controllers.Main.txtNotInTable": "Non è in Tabella", "DE.Controllers.Main.txtNotValidBookmark": "Errore! Non è un riferimento personale valido per i segnalibri.", "DE.Controllers.Main.txtOddPage": "<PERSON><PERSON>a dispari", "DE.Controllers.Main.txtOnPage": "sulla pagina", "DE.Controllers.Main.txtRectangles": "Rettangoli", "DE.Controllers.Main.txtSameAsPrev": "come in precedenza", "DE.Controllers.Main.txtSection": "-Sezione", "DE.Controllers.Main.txtSeries": "Serie", "DE.Controllers.Main.txtShape_accentBorderCallout1": "Callout Linea con bordo e barra in risalto", "DE.Controllers.Main.txtShape_accentBorderCallout2": "Callout Linea piegata con bordo e barra in risalto", "DE.Controllers.Main.txtShape_accentBorderCallout3": "Callout Doppia linea piegata con barra e bordo in risalto", "DE.Controllers.Main.txtShape_accentCallout1": "Callout Linea con barra in risalto", "DE.Controllers.Main.txtShape_accentCallout2": "Callout Linea piegata con barra in risalto", "DE.Controllers.Main.txtShape_accentCallout3": "Callout Doppia linea piegata con barra in risalto", "DE.Controllers.Main.txtShape_actionButtonBackPrevious": "Indietro o Pulsante Precedente", "DE.Controllers.Main.txtShape_actionButtonBeginning": "Pulsante di Inizio", "DE.Controllers.Main.txtShape_actionButtonBlank": "Pulsan<PERSON> Vuoto", "DE.Controllers.Main.txtShape_actionButtonDocument": "Pulsante Documento", "DE.Controllers.Main.txtShape_actionButtonEnd": "Pulsante Fine", "DE.Controllers.Main.txtShape_actionButtonForwardNext": "Pulsante Avanti o Successivo", "DE.Controllers.Main.txtShape_actionButtonHelp": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_actionButtonHome": "Pulsante Home", "DE.Controllers.Main.txtShape_actionButtonInformation": "Pulsante Informazioni", "DE.Controllers.Main.txtShape_actionButtonMovie": "Pulsante Film", "DE.Controllers.Main.txtShape_actionButtonReturn": "Pulsante Invio", "DE.Controllers.Main.txtShape_actionButtonSound": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_arc": "Arco", "DE.Controllers.Main.txtShape_bentArrow": "<PERSON><PERSON><PERSON> piegata", "DE.Controllers.Main.txtShape_bentConnector5": "<PERSON><PERSON><PERSON> a gomito", "DE.Controllers.Main.txtShape_bentConnector5WithArrow": "<PERSON><PERSON><PERSON> freccia a gomito", "DE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "<PERSON><PERSON><PERSON> a doppia freccia a gomito", "DE.Controllers.Main.txtShape_bentUpArrow": "<PERSON><PERSON><PERSON> curva in alto", "DE.Controllers.Main.txtShape_bevel": "Smussat<PERSON>", "DE.Controllers.Main.txtShape_blockArc": "<PERSON>o a tutto sesto", "DE.Controllers.Main.txtShape_borderCallout1": "Callout <PERSON>a", "DE.Controllers.Main.txtShape_borderCallout2": "Callout Linea piegata", "DE.Controllers.Main.txtShape_borderCallout3": "Callout Doppia linea piegata", "DE.Controllers.Main.txtShape_bracePair": "<PERSON><PERSON><PERSON> parentesi graffa", "DE.Controllers.Main.txtShape_callout1": "Callout Linea senza bordo", "DE.Controllers.Main.txtShape_callout2": "Callout Linea piegata senza bordo ", "DE.Controllers.Main.txtShape_callout3": "Callout Doppia linea piegata senza bordo", "DE.Controllers.Main.txtShape_can": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_chevron": "freccia a Gallone", "DE.Controllers.Main.txtShape_chord": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_circularArrow": "<PERSON><PERSON><PERSON> circolare", "DE.Controllers.Main.txtShape_cloud": "Nuvola", "DE.Controllers.Main.txtShape_cloudCallout": "Cloud Callout", "DE.Controllers.Main.txtShape_corner": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_cube": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_curvedConnector3": "<PERSON><PERSON><PERSON> curvo", "DE.Controllers.Main.txtShape_curvedConnector3WithArrow": "<PERSON><PERSON><PERSON> a freccia curva", "DE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "<PERSON><PERSON><PERSON> a doppia freccia curva", "DE.Controllers.Main.txtShape_curvedDownArrow": "<PERSON><PERSON><PERSON> curva in basso", "DE.Controllers.Main.txtShape_curvedLeftArrow": "Freccia curva a sinistra", "DE.Controllers.Main.txtShape_curvedRightArrow": "Freccia curva a destra", "DE.Controllers.Main.txtShape_curvedUpArrow": "<PERSON><PERSON><PERSON> curva in alto", "DE.Controllers.Main.txtShape_decagon": "Decagono", "DE.Controllers.Main.txtShape_diagStripe": "<PERSON><PERSON><PERSON> diagonale", "DE.Controllers.Main.txtShape_diamond": "Diamante", "DE.Controllers.Main.txtShape_dodecagon": "dodecagono", "DE.Controllers.Main.txtShape_donut": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_doubleWave": "<PERSON><PERSON><PERSON> onda", "DE.Controllers.Main.txtShape_downArrow": "Freccia in giù", "DE.Controllers.Main.txtShape_downArrowCallout": "Callout <PERSON> in basso", "DE.Controllers.Main.txtShape_ellipse": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_ellipseRibbon": "Nastro curvo in basso", "DE.Controllers.Main.txtShape_ellipseRibbon2": "Nastro curvato in alto", "DE.Controllers.Main.txtShape_flowChartAlternateProcess": "Diagramma di flusso: processo alternativo", "DE.Controllers.Main.txtShape_flowChartCollate": "Diagramma di flusso: Fascicolo", "DE.Controllers.Main.txtShape_flowChartConnector": "Diagramma di flusso: <PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_flowChartDecision": "Diagramma di flusso: Decisione", "DE.Controllers.Main.txtShape_flowChartDelay": "Diagramma di flusso: <PERSON><PERSON>", "DE.Controllers.Main.txtShape_flowChartDisplay": "Diagramma di flusso: Visualizza", "DE.Controllers.Main.txtShape_flowChartDocument": "Diagramma di flusso: Documento", "DE.Controllers.Main.txtShape_flowChartExtract": "Diagramma di flusso: Estrai", "DE.Controllers.Main.txtShape_flowChartInputOutput": "Diagramma di flusso: <PERSON><PERSON>", "DE.Controllers.Main.txtShape_flowChartInternalStorage": "Diagramma di flusso: Memoria interna", "DE.Controllers.Main.txtShape_flowChartMagneticDisk": "Diagramma di flusso: Disco magnetico", "DE.Controllers.Main.txtShape_flowChartMagneticDrum": "Diagramma di flusso: Memoria ad accesso diretto", "DE.Controllers.Main.txtShape_flowChartMagneticTape": "Diagramma di flusso: Memoria ad accesso sequenziale", "DE.Controllers.Main.txtShape_flowChartManualInput": "Diagramma di flusso: Input manuale", "DE.Controllers.Main.txtShape_flowChartManualOperation": "Diagramma di flusso: Operazione manuale", "DE.Controllers.Main.txtShape_flowChartMerge": "Diagramma di flusso: Unione", "DE.Controllers.Main.txtShape_flowChartMultidocument": "Diagramma di flusso: Multidocumento", "DE.Controllers.Main.txtShape_flowChartOffpageConnector": "Diagramma di flusso: <PERSON><PERSON><PERSON> fuori pagina", "DE.Controllers.Main.txtShape_flowChartOnlineStorage": "Diagramma di flusso: <PERSON><PERSON> sal<PERSON>i", "DE.Controllers.Main.txtShape_flowChartOr": "Diagramma di flusso: O", "DE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Diagramma di flusso: Elaborazione predefinita", "DE.Controllers.Main.txtShape_flowChartPreparation": "Diagramma di flusso: Preparazione", "DE.Controllers.Main.txtShape_flowChartProcess": "Diagramma di flusso: Elaborazione", "DE.Controllers.Main.txtShape_flowChartPunchedCard": "Diagramma di flusso: <PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_flowChartPunchedTape": "Diagramma di flusso: Nastro perforato", "DE.Controllers.Main.txtShape_flowChartSort": "Diagramma di flusso: Ordinamento", "DE.Controllers.Main.txtShape_flowChartSummingJunction": "Diagramma di flusso: Giunzione di somma", "DE.Controllers.Main.txtShape_flowChartTerminator": "Diagramma di flusso: <PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_foldedCorner": "angolo ripiegato", "DE.Controllers.Main.txtShape_frame": "Cornice", "DE.Controllers.Main.txtShape_halfFrame": "Mezza Cornice", "DE.Controllers.Main.txtShape_heart": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_heptagon": "Ettagono", "DE.Controllers.Main.txtShape_hexagon": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_homePlate": "Pentagono", "DE.Controllers.Main.txtShape_horizontalScroll": "Scorrimento orizzontale", "DE.Controllers.Main.txtShape_irregularSeal1": "Esplosione 1", "DE.Controllers.Main.txtShape_irregularSeal2": "Esplosione 2", "DE.Controllers.Main.txtShape_leftArrow": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_leftArrowCallout": "Callout <PERSON><PERSON><PERSON> a sinistra", "DE.Controllers.Main.txtShape_leftBrace": "Parentesi graffa aperta", "DE.Controllers.Main.txtShape_leftBracket": "Parentesi quadra aperta", "DE.Controllers.Main.txtShape_leftRightArrow": "Freccia bidirezionale sinistra destra", "DE.Controllers.Main.txtShape_leftRightArrowCallout": "Callout <PERSON><PERSON><PERSON> bidirezionane sinistra destra", "DE.Controllers.Main.txtShape_leftRightUpArrow": "Freccia tridirezionale sinistra destra alto", "DE.Controllers.Main.txtShape_leftUpArrow": "<PERSON><PERSON>cia in alto a sinistra", "DE.Controllers.Main.txtShape_lightningBolt": "Fulmine", "DE.Controllers.Main.txtShape_line": "Linea", "DE.Controllers.Main.txtShape_lineWithArrow": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_lineWithTwoArrows": "<PERSON><PERSON><PERSON> doppia", "DE.Controllers.Main.txtShape_mathDivide": "Divisione", "DE.Controllers.Main.txtShape_mathEqual": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_mathMinus": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_mathMultiply": "Moltiplicazione", "DE.Controllers.Main.txtShape_mathNotEqual": "Non uguale", "DE.Controllers.Main.txtShape_mathPlus": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_moon": "Luna", "DE.Controllers.Main.txtShape_noSmoking": "Simbolo \"No\"", "DE.Controllers.Main.txtShape_notchedRightArrow": "<PERSON><PERSON>cia dentellata a destra ", "DE.Controllers.Main.txtShape_octagon": "Ottagono", "DE.Controllers.Main.txtShape_parallelogram": "Parallelogram<PERSON>", "DE.Controllers.Main.txtShape_pentagon": "Pentagono", "DE.Controllers.Main.txtShape_pie": "A torta", "DE.Controllers.Main.txtShape_plaque": "Firma", "DE.Controllers.Main.txtShape_plus": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_polyline1": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_polyline2": "Forma libera", "DE.Controllers.Main.txtShape_quadArrow": "Freccia a incrocio", "DE.Controllers.Main.txtShape_quadArrowCallout": "Callout <PERSON><PERSON> a incrocio", "DE.Controllers.Main.txtShape_rect": "Rettangolo", "DE.Controllers.Main.txtShape_ribbon": "<PERSON>stro inclinato in basso", "DE.Controllers.Main.txtShape_ribbon2": "Nastro inclinato in alto", "DE.Controllers.Main.txtShape_rightArrow": "<PERSON><PERSON><PERSON> destra", "DE.Controllers.Main.txtShape_rightArrowCallout": "Callout <PERSON><PERSON><PERSON> a destra", "DE.Controllers.Main.txtShape_rightBrace": "Parentesi graffa chiusa", "DE.Controllers.Main.txtShape_rightBracket": "Parentesi quadra chiusa", "DE.Controllers.Main.txtShape_round1Rect": "Rettangolo ad angolo singolo smussato", "DE.Controllers.Main.txtShape_round2DiagRect": "Rettangolo ad angolo diagonale smussato", "DE.Controllers.Main.txtShape_round2SameRect": "<PERSON><PERSON><PERSON><PERSON> smussato dallo stesso lato", "DE.Controllers.Main.txtShape_roundRect": "Rettangolo ad angoli smussati", "DE.Controllers.Main.txtShape_rtTriangle": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_smileyFace": "Faccia sorridente", "DE.Controllers.Main.txtShape_snip1Rect": "Ritaglia rettangolo ad angolo singolo", "DE.Controllers.Main.txtShape_snip2DiagRect": "Ritaglia rettangolo ad angolo diagonale", "DE.Controllers.Main.txtShape_snip2SameRect": "<PERSON><PERSON>lo smussato dallo stesso lato", "DE.Controllers.Main.txtShape_snipRoundRect": "Ritaglia e smussa singolo angolo retta<PERSON>lo", "DE.Controllers.Main.txtShape_spline": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_star10": "Stella a 10 punte", "DE.Controllers.Main.txtShape_star12": "Stella a 12 punte", "DE.Controllers.Main.txtShape_star16": "Stella a 16 punte", "DE.Controllers.Main.txtShape_star24": "Stella a 24 punte", "DE.Controllers.Main.txtShape_star32": "Stella a 32 punte", "DE.Controllers.Main.txtShape_star4": "Stella a 4 punte", "DE.Controllers.Main.txtShape_star5": "Stella a 5 punte", "DE.Controllers.Main.txtShape_star6": "Stella a 6 punte", "DE.Controllers.Main.txtShape_star7": "Stella a 7 punte", "DE.Controllers.Main.txtShape_star8": "Stella a 8 punte", "DE.Controllers.Main.txtShape_stripedRightArrow": "Freccia a strisce verso destra ", "DE.Controllers.Main.txtShape_sun": "Sole", "DE.Controllers.Main.txtShape_teardrop": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_textRect": "Casella di testo", "DE.Controllers.Main.txtShape_trapezoid": "Trapezio", "DE.Controllers.Main.txtShape_triangle": "Triangolo", "DE.Controllers.Main.txtShape_upArrow": "Freccia su", "DE.Controllers.Main.txtShape_upArrowCallout": "Callout <PERSON> in alto", "DE.Controllers.Main.txtShape_upDownArrow": "Freccia bidirezionale su giù", "DE.Controllers.Main.txtShape_uturnArrow": "Freccia a inversione", "DE.Controllers.Main.txtShape_verticalScroll": "Scorrimento verticale", "DE.Controllers.Main.txtShape_wave": "On<PERSON>", "DE.Controllers.Main.txtShape_wedgeEllipseCallout": "Callout <PERSON>", "DE.Controllers.Main.txtShape_wedgeRectCallout": "Callout <PERSON>", "DE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Callout <PERSON><PERSON>", "DE.Controllers.Main.txtStarsRibbons": "Stelle e nastri", "DE.Controllers.Main.txtStyle_Caption": "Didascalia", "DE.Controllers.Main.txtStyle_endnote_text": "Inser<PERSON>ci testo nota di chiusura", "DE.Controllers.Main.txtStyle_footnote_text": "Nota a piè di pagina", "DE.Controllers.Main.txtStyle_Heading_1": "Titolo 1", "DE.Controllers.Main.txtStyle_Heading_2": "Titolo 2", "DE.Controllers.Main.txtStyle_Heading_3": "Titolo 3", "DE.Controllers.Main.txtStyle_Heading_4": "Titolo 4", "DE.Controllers.Main.txtStyle_Heading_5": "Titolo 5", "DE.Controllers.Main.txtStyle_Heading_6": "Titolo 6", "DE.Controllers.Main.txtStyle_Heading_7": "Titolo 7", "DE.Controllers.Main.txtStyle_Heading_8": "Titolo 8", "DE.Controllers.Main.txtStyle_Heading_9": "Titolo 9", "DE.Controllers.Main.txtStyle_Intense_Quote": "Citazione profonda", "DE.Controllers.Main.txtStyle_List_Paragraph": "Elenco <PERSON>fo", "DE.Controllers.Main.txtStyle_No_Spacing": "<PERSON>za spazi", "DE.Controllers.Main.txtStyle_Normal": "Normale", "DE.Controllers.Main.txtStyle_Quote": "Cita", "DE.Controllers.Main.txtStyle_Subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtStyle_Title": "<PERSON><PERSON>", "DE.Controllers.Main.txtSyntaxError": "Errore di sintassi", "DE.Controllers.Main.txtTableInd": "L'indice della tabella non può essere zero", "DE.Controllers.Main.txtTableOfContents": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtTableOfFigures": "‎Indice delle figure‎", "DE.Controllers.Main.txtTOCHeading": "Tabella del contenuto con intestazione", "DE.Controllers.Main.txtTooLarge": "Numero troppo grande per essere formattato", "DE.Controllers.Main.txtTypeEquation": "Digitare un'equazione qui.", "DE.Controllers.Main.txtUndefBookmark": "Segnalibro indefinito", "DE.Controllers.Main.txtXAxis": "Asse <PERSON>", "DE.Controllers.Main.txtYAxis": "<PERSON><PERSON>", "DE.Controllers.Main.txtZeroDivide": "Diviso Zero", "DE.Controllers.Main.unknownErrorText": "<PERSON><PERSON><PERSON> sconos<PERSON>.", "DE.Controllers.Main.unsupportedBrowserErrorText": "Il tuo browser non è supportato.", "DE.Controllers.Main.uploadDocExtMessage": "Formato documento sconosciuto.", "DE.Controllers.Main.uploadDocFileCountMessage": "Nessun documento caricato.", "DE.Controllers.Main.uploadDocSizeMessage": "Il limite massimo delle dimensioni del documento è stato superato.", "DE.Controllers.Main.uploadImageExtMessage": "Formato immagine sconosciuto.", "DE.Controllers.Main.uploadImageFileCountMessage": "Nessuna immagine caricata.", "DE.Controllers.Main.uploadImageSizeMessage": "L'immagine è troppo grande. La dimensione massima è 25 MB.", "DE.Controllers.Main.uploadImageTextText": "Caricamento immagine in corso...", "DE.Controllers.Main.uploadImageTitleText": "Caricamento dell'immagine", "DE.Controllers.Main.waitText": "Per favore, attendi...", "DE.Controllers.Main.warnBrowserIE9": "L'applicazione è poco compatibile con IE9. Usa IE10 o più recente", "DE.Controllers.Main.warnBrowserZoom": "Le impostazioni correnti di zoom del tuo browser non sono completamente supportate. Per favore, ritorna allo zoom predefinito premendo Ctrl+0.", "DE.Controllers.Main.warnLicenseExceeded": "Hai raggiunto il limite per le connessioni simultanee agli editor %1. Questo documento verrà aperto in sola lettura.<br>Contatta l’amministratore per saperne di più.", "DE.Controllers.Main.warnLicenseExp": "La tua licenza è scaduta.<br>Si prega di aggiornare la licenza e ricaricare la pagina.", "DE.Controllers.Main.warnLicenseLimitedNoAccess": "Licenza scaduta. <br>Non puoi modificare il documento.</br>Per favore, contatta l'amministratore", "DE.Controllers.Main.warnLicenseLimitedRenewed": "La licenza deve essere rinnovata. <br>Hai un accesso limitato alla funzionalità di modifica dei documenti.<br>Contatta l'amministratore per ottenere l'accesso completo", "DE.Controllers.Main.warnLicenseUsersExceeded": "Hai raggiunto il limite per gli utenti con accesso agli editor %1. Contatta l’amministratore per saperne di più.", "DE.Controllers.Main.warnNoLicense": "Hai raggiunto il limite per le connessioni simultanee agli editor %1. Questo documento verrà aperto in sola lettura.<br>Contatta il team di vendita di %1 per i termini di aggiornamento personali.", "DE.Controllers.Main.warnNoLicenseUsers": "Hai raggiunto il limite per gli utenti con accesso agli editor %1. Contatta il team di vendita di %1 per i termini di aggiornamento personali.", "DE.Controllers.Main.warnProcessRightsChange": "Ti è stato negato il diritto di modificare il file.", "DE.Controllers.Navigation.txtBeginning": "Inizio del documento", "DE.Controllers.Navigation.txtGotoBeginning": "Vai all'inizio del documento", "DE.Controllers.Search.notcriticalErrorTitle": "Avviso", "DE.Controllers.Search.textNoTextFound": "Impossibile trovare i dati che stavi cercando. Ti preghiamo di modificare le opzioni di ricerca.", "DE.Controllers.Search.textReplaceSkipped": "La sostituzione è stata effettuata. {0} ricorrenze sono state saltate.", "DE.Controllers.Search.textReplaceSuccess": "La ricerca è stata effettuata. {0} occorrenze sono state sostituite", "DE.Controllers.Search.warnReplaceString": "{0} non è un carattere speciale valido per la casella Sostituire con.", "DE.Controllers.Statusbar.textDisconnect": "<b>Connessione persa</b><br>Tentativo di connessione in corso. Si prega di controllare le impostazioni di connessione.", "DE.Controllers.Statusbar.textHasChanges": "Sono state rilevate nuove modifiche", "DE.Controllers.Statusbar.textSetTrackChanges": "Sei in modalità traccia cambiamenti", "DE.Controllers.Statusbar.textTrackChanges": "Il documento è aperto in modalità Traccia Revisioni attivata", "DE.Controllers.Statusbar.tipReview": "<PERSON><PERSON><PERSON> camb<PERSON>i", "DE.Controllers.Statusbar.zoomText": "Zoom {0}%", "DE.Controllers.Toolbar.confirmAddFontName": "Il carattere che vuoi salvare non è accessibile su questo dispositivo.<br>Lo stile di testo sarà visualizzato usando uno dei caratteri di sistema, il carattere salvato sarà usato quando accessibile.<br>Vuoi continuare?", "DE.Controllers.Toolbar.dataUrl": "Incollare un URL dati", "DE.Controllers.Toolbar.notcriticalErrorTitle": "Avviso", "DE.Controllers.Toolbar.textAccent": "Accenti", "DE.Controllers.Toolbar.textBracket": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.textEmptyImgUrl": "Specifica URL immagine.", "DE.Controllers.Toolbar.textEmptyMMergeUrl": "Devi specificare l'URL.", "DE.Controllers.Toolbar.textFontSizeErr": "Il valore inserito non è corretto.<br>Inserisci un valore numerico compreso tra 1 e 300", "DE.Controllers.Toolbar.textFraction": "Frazioni", "DE.Controllers.Toolbar.textFunction": "Funzioni", "DE.Controllers.Toolbar.textGroup": "Gruppo", "DE.Controllers.Toolbar.textInsert": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.textIntegral": "Integrali", "DE.Controllers.Toolbar.textLargeOperator": "Operatori di grandi dimensioni", "DE.Controllers.Toolbar.textLimitAndLog": "Limiti e Logaritmi", "DE.Controllers.Toolbar.textMatrix": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.textOperator": "Operatori", "DE.Controllers.Toolbar.textRadical": "<PERSON><PERSON>", "DE.Controllers.Toolbar.textRecentlyUsed": "Usati di recente", "DE.Controllers.Toolbar.textScript": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.textSymbols": "Simboli", "DE.Controllers.Toolbar.textTabForms": "Forme‎", "DE.Controllers.Toolbar.textWarning": "Avviso", "DE.Controllers.Toolbar.txtAccent_Accent": "Acuto", "DE.Controllers.Toolbar.txtAccent_ArrowD": "<PERSON><PERSON><PERSON>-Sinistra in alto", "DE.Controllers.Toolbar.txtAccent_ArrowL": "Freccia verso sinistra sopra", "DE.Controllers.Toolbar.txtAccent_ArrowR": "Freccia a destra alta", "DE.Controllers.Toolbar.txtAccent_Bar": "A barre", "DE.Controllers.Toolbar.txtAccent_BarBot": "Barra inferiore", "DE.Controllers.Toolbar.txtAccent_BarTop": "barra sopra", "DE.Controllers.Toolbar.txtAccent_BorderBox": "Formula racchiusa (con segnaposto)", "DE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Formula racchiusa (Esempio)", "DE.Controllers.Toolbar.txtAccent_Check": "Controlla", "DE.Controllers.Toolbar.txtAccent_CurveBracketBot": "sottoparentesi", "DE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Overbrace", "DE.Controllers.Toolbar.txtAccent_Custom_1": "Vettore A", "DE.Controllers.Toolbar.txtAccent_Custom_2": "ABC con barra superiore", "DE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y con barra sopra", "DE.Controllers.Toolbar.txtAccent_DDDot": "Punto triplo", "DE.Controllers.Toolbar.txtAccent_DDot": "<PERSON><PERSON><PERSON> punto", "DE.Controllers.Toolbar.txtAccent_Dot": "Dot", "DE.Controllers.Toolbar.txtAccent_DoubleBar": "Doppia barra superiore", "DE.Controllers.Toolbar.txtAccent_Grave": "Grave", "DE.Controllers.Toolbar.txtAccent_GroupBot": "Raggruppa<PERSON> carattere sotto", "DE.Controllers.Toolbar.txtAccent_GroupTop": "Raggruppamento carattere sopra", "DE.Controllers.Toolbar.txtAccent_HarpoonL": "Arpione verso sinistra sopra", "DE.Controllers.Toolbar.txtAccent_HarpoonR": "Arpione verso destra sopra", "DE.Controllers.Toolbar.txtAccent_Hat": "Cir<PERSON>fle<PERSON>", "DE.Controllers.Toolbar.txtAccent_Smile": "Accento Breve", "DE.Controllers.Toolbar.txtAccent_Tilde": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Angle": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Parentesi con separatori", "DE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Parentesi con separatori", "DE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Parentesi quadra singola", "DE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Parentesi quadra singola", "DE.Controllers.Toolbar.txtBracket_Curve": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Parentesi con separatori", "DE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Parentesi quadra singola", "DE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Parentesi quadra singola", "DE.Controllers.Toolbar.txtBracket_Custom_1": "Casi (due condizioni)", "DE.Controllers.Toolbar.txtBracket_Custom_2": "Casi (tre condizioni)", "DE.Controllers.Toolbar.txtBracket_Custom_3": "<PERSON>mp<PERSON>", "DE.Controllers.Toolbar.txtBracket_Custom_4": "<PERSON>mp<PERSON>", "DE.Controllers.Toolbar.txtBracket_Custom_5": "Esempio di casi", "DE.Controllers.Toolbar.txtBracket_Custom_6": "Coefficiente binomiale", "DE.Controllers.Toolbar.txtBracket_Custom_7": "Coefficiente binomiale", "DE.Controllers.Toolbar.txtBracket_Line": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Parentesi quadra singola", "DE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Parentesi quadra singola", "DE.Controllers.Toolbar.txtBracket_LineDouble": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Parentesi quadra singola", "DE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Parentesi quadra singola", "DE.Controllers.Toolbar.txtBracket_LowLim": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Parentesi quadra singola", "DE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Parentesi quadra singola", "DE.Controllers.Toolbar.txtBracket_Round": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Parentesi con separatori", "DE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Parentesi quadra singola", "DE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Parentesi quadra singola", "DE.Controllers.Toolbar.txtBracket_Square": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Square_CloseClose": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Parentesi quadra singola", "DE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Parentesi quadra singola", "DE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_SquareDouble": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Parentesi quadra singola", "DE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Parentesi quadra singola", "DE.Controllers.Toolbar.txtBracket_UppLim": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Parentesi quadra singola", "DE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Parentesi quadra singola", "DE.Controllers.Toolbar.txtFractionDiagonal": "Frazione obliqua", "DE.Controllers.Toolbar.txtFractionDifferential_1": "Differenziale", "DE.Controllers.Toolbar.txtFractionDifferential_2": "Differenziale", "DE.Controllers.Toolbar.txtFractionDifferential_3": "Differenziale", "DE.Controllers.Toolbar.txtFractionDifferential_4": "Differential", "DE.Controllers.Toolbar.txtFractionHorizontal": "Frazione lineare", "DE.Controllers.Toolbar.txtFractionPi_2": "Pi diviso 2", "DE.Controllers.Toolbar.txtFractionSmall": "Frazione piccola", "DE.Controllers.Toolbar.txtFractionVertical": "Frazione impilata", "DE.Controllers.Toolbar.txtFunction_1_Cos": "Funzione coseno inversa", "DE.Controllers.Toolbar.txtFunction_1_Cosh": "Funzione coseno iperbolica inversa", "DE.Controllers.Toolbar.txtFunction_1_Cot": "Funzione cotangente inversa", "DE.Controllers.Toolbar.txtFunction_1_Coth": "Funzione cotangente iperbolica inversa", "DE.Controllers.Toolbar.txtFunction_1_Csc": "Funzione cosecante inversa", "DE.Controllers.Toolbar.txtFunction_1_Csch": "Funzione cosecante iperbolica inversa ", "DE.Controllers.Toolbar.txtFunction_1_Sec": "Funzione secante inversa", "DE.Controllers.Toolbar.txtFunction_1_Sech": "Funziono secante iperbolica inversa", "DE.Controllers.Toolbar.txtFunction_1_Sin": "Funzione seno inversa", "DE.Controllers.Toolbar.txtFunction_1_Sinh": "Funzione seno iperbolica inversa", "DE.Controllers.Toolbar.txtFunction_1_Tan": "Funzione tangente inversa", "DE.Controllers.Toolbar.txtFunction_1_Tanh": "Funzione tangente iperbolica inversa", "DE.Controllers.Toolbar.txtFunction_Cos": "Funzione Coseno", "DE.Controllers.Toolbar.txtFunction_Cosh": "Funzione coseno iperbolica", "DE.Controllers.Toolbar.txtFunction_Cot": "Funzione Cotangente", "DE.Controllers.Toolbar.txtFunction_Coth": "Funzione cotangente iperbolica", "DE.Controllers.Toolbar.txtFunction_Csc": "Funzione cosecante", "DE.Controllers.Toolbar.txtFunction_Csch": "Funzione cosecante iperbolica", "DE.Controllers.Toolbar.txtFunction_Custom_1": "<PERSON><PERSON> the<PERSON>", "DE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "DE.Controllers.Toolbar.txtFunction_Custom_3": "Formula Tangente", "DE.Controllers.Toolbar.txtFunction_Sec": "Funzione secante", "DE.Controllers.Toolbar.txtFunction_Sech": "Funzione secante iperbolica inversa", "DE.Controllers.Toolbar.txtFunction_Sin": "Funzione seno", "DE.Controllers.Toolbar.txtFunction_Sinh": "Funzione seno iperbolica", "DE.Controllers.Toolbar.txtFunction_Tan": "Funzione tangente", "DE.Controllers.Toolbar.txtFunction_Tanh": "Funzione tangente iperbolica", "DE.Controllers.Toolbar.txtIntegral": "Integrale", "DE.Controllers.Toolbar.txtIntegral_dtheta": "Differenziale theta", "DE.Controllers.Toolbar.txtIntegral_dx": "Differenziale x", "DE.Controllers.Toolbar.txtIntegral_dy": "Differenziale y", "DE.Controllers.Toolbar.txtIntegralCenterSubSup": "Integrale", "DE.Controllers.Toolbar.txtIntegralDouble": "Doppio integrale", "DE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Doppio integrale", "DE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Doppio integrale", "DE.Controllers.Toolbar.txtIntegralOriented": "Integrazione di contorno", "DE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Integrazione di contorno", "DE.Controllers.Toolbar.txtIntegralOrientedDouble": "Superficie Integrale", "DE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Superficie Integrale", "DE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Superficie Integrale", "DE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Integrazione di contorno", "DE.Controllers.Toolbar.txtIntegralOrientedTriple": "Volume Integrale", "DE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Volume Integrale", "DE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Volume Integrale", "DE.Controllers.Toolbar.txtIntegralSubSup": "Integrale", "DE.Controllers.Toolbar.txtIntegralTriple": "<PERSON><PERSON> Integrale", "DE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "<PERSON><PERSON> Integrale", "DE.Controllers.Toolbar.txtIntegralTripleSubSup": "<PERSON><PERSON> Integrale", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Cuneo", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Cuneo", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Cuneo", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Cuneo", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Cuneo", "DE.Controllers.Toolbar.txtLargeOperator_CoProd": "Co-<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Co-<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Co-<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Co-<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Co-<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Somma", "DE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Somma", "DE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Somma", "DE.Controllers.Toolbar.txtLargeOperator_Custom_4": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Unione", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Intersection": "Intersezione", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Intersezione", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Intersezione", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Intersezione", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Intersezione", "DE.Controllers.Toolbar.txtLargeOperator_Prod": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Sum": "Somma", "DE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Somma", "DE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Somma", "DE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Somma", "DE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Somma", "DE.Controllers.Toolbar.txtLargeOperator_Union": "Unione", "DE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Unione", "DE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Unione", "DE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Unione", "DE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Unione", "DE.Controllers.Toolbar.txtLimitLog_Custom_1": "Esempio limite", "DE.Controllers.Toolbar.txtLimitLog_Custom_2": "Esemp<PERSON>", "DE.Controllers.Toolbar.txtLimitLog_Lim": "Limite", "DE.Controllers.Toolbar.txtLimitLog_Ln": "Logaritmo Naturale", "DE.Controllers.Toolbar.txtLimitLog_Log": "Logaritmo", "DE.Controllers.Toolbar.txtLimitLog_LogBase": "Logaritmo", "DE.Controllers.Toolbar.txtLimitLog_Max": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtLimitLog_Min": "Minimo", "DE.Controllers.Toolbar.txtMarginsH": "I margini superiore e inferiore sono troppo alti per una determinata altezza di pagina", "DE.Controllers.Toolbar.txtMarginsW": "I margini sinistro e destro sono troppo larghi per una determinata larghezza di pagina", "DE.Controllers.Toolbar.txtMatrix_1_2": "1x2 <PERSON><PERSON>", "DE.Controllers.Toolbar.txtMatrix_1_3": "1x3 <PERSON><PERSON>", "DE.Controllers.Toolbar.txtMatrix_2_1": "2x1 <PERSON><PERSON>", "DE.Controllers.Toolbar.txtMatrix_2_2": "2x2 <PERSON><PERSON>", "DE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "<PERSON><PERSON> vuota con parentesi", "DE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "<PERSON><PERSON> vuota con parentesi", "DE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "<PERSON><PERSON> vuota con parentesi", "DE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "<PERSON><PERSON> vuota con parentesi", "DE.Controllers.Toolbar.txtMatrix_2_3": "2x3 <PERSON><PERSON>", "DE.Controllers.Toolbar.txtMatrix_3_1": "3x1 <PERSON><PERSON>", "DE.Controllers.Toolbar.txtMatrix_3_2": "3x2 <PERSON><PERSON>", "DE.Controllers.Toolbar.txtMatrix_3_3": "3x3 <PERSON><PERSON>", "DE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Punti di base", "DE.Controllers.Toolbar.txtMatrix_Dots_Center": "Punti linea mediana", "DE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "<PERSON><PERSON><PERSON> diagonali", "DE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "Punti verticali", "DE.Controllers.Toolbar.txtMatrix_Flat_Round": "<PERSON><PERSON> sparsa", "DE.Controllers.Toolbar.txtMatrix_Flat_Square": "<PERSON><PERSON> sparsa", "DE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2 Matrice di identità", "DE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "3x3 Matrice di identità", "DE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3 Matrice di identità", "DE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 Matrice di identità", "DE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "<PERSON><PERSON><PERSON>-<PERSON><PERSON> in basso", "DE.Controllers.Toolbar.txtOperator_ArrowD_Top": "<PERSON><PERSON><PERSON>-Sinistra in alto", "DE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Freccia verso sinistra sotto", "DE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Freccia verso sinistra sopra", "DE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Freccia a destra bassa", "DE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Freccia a destra alta", "DE.Controllers.Toolbar.txtOperator_ColonEquals": "Due punti uguali", "DE.Controllers.Toolbar.txtOperator_Custom_1": "Rendimenti", "DE.Controllers.Toolbar.txtOperator_Custom_2": "Rendimenti delta", "DE.Controllers.Toolbar.txtOperator_Definition": "Uguale a Per definizione", "DE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta uguale a", "DE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "<PERSON><PERSON><PERSON>-<PERSON><PERSON> in basso", "DE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "<PERSON><PERSON><PERSON>-Sinistra in alto", "DE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Freccia verso sinistra sotto", "DE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Freccia verso sinistra sopra", "DE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Freccia a destra bassa", "DE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Freccia a destra alta", "DE.Controllers.Toolbar.txtOperator_EqualsEquals": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtOperator_MinusEquals": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtOperator_PlusEquals": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "<PERSON><PERSON><PERSON> con", "DE.Controllers.Toolbar.txtRadicalCustom_1": "Radicale", "DE.Controllers.Toolbar.txtRadicalCustom_2": "Radical", "DE.Controllers.Toolbar.txtRadicalRoot_2": "Radice quadrata con Grado", "DE.Controllers.Toolbar.txtRadicalRoot_3": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtRadicalRoot_n": "Radicale con grado", "DE.Controllers.Toolbar.txtRadicalSqrt": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtScriptCustom_1": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtScriptCustom_2": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtScriptCustom_3": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtScriptCustom_4": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtScriptSub": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtScriptSubSup": "Pedice-Apice", "DE.Controllers.Toolbar.txtScriptSubSupLeft": "Pedice-Apice sinistro", "DE.Controllers.Toolbar.txtScriptSup": "Apice", "DE.Controllers.Toolbar.txtSymbol_about": "Approssimativamente", "DE.Controllers.Toolbar.txtSymbol_additional": "Complemento", "DE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "DE.Controllers.Toolbar.txtSymbol_alpha": "Alfa", "DE.Controllers.Toolbar.txtSymbol_approx": "<PERSON>uasi uguale a", "DE.Controllers.Toolbar.txtSymbol_ast": "Operatore asterisco", "DE.Controllers.Toolbar.txtSymbol_beta": "Beta", "DE.Controllers.Toolbar.txtSymbol_beth": "Bet", "DE.Controllers.Toolbar.txtSymbol_bullet": "Operatore elenco puntato", "DE.Controllers.Toolbar.txtSymbol_cap": "Intersezione", "DE.Controllers.Toolbar.txtSymbol_cbrt": "<PERSON>dice cubica", "DE.Controllers.Toolbar.txtSymbol_cdots": "Ellissi orizzontale di linea mediana", "DE.Controllers.Toolbar.txtSymbol_celsius": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "DE.Controllers.Toolbar.txtSymbol_cong": "Approssimativamente uguale a", "DE.Controllers.Toolbar.txtSymbol_cup": "Unione", "DE.Controllers.Toolbar.txtSymbol_ddots": "<PERSON><PERSON> diagonale in basso a destra", "DE.Controllers.Toolbar.txtSymbol_degree": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_delta": "Delta", "DE.Controllers.Toolbar.txtSymbol_div": "Segno di divisione", "DE.Controllers.Toolbar.txtSymbol_downarrow": "Freccia in giù", "DE.Controllers.Toolbar.txtSymbol_emptyset": "Insieme vuoto", "DE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "DE.Controllers.Toolbar.txtSymbol_equals": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_equiv": "Identico a", "DE.Controllers.Toolbar.txtSymbol_eta": "Eta", "DE.Controllers.Toolbar.txtSymbol_exists": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_factorial": "Fattoriale", "DE.Controllers.Toolbar.txtSymbol_fahrenheit": "Gradi Fah<PERSON>he<PERSON>", "DE.Controllers.Toolbar.txtSymbol_forall": "Per tutti", "DE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "DE.Controllers.Toolbar.txtSymbol_geq": "Maggiore o uguale a", "DE.Controllers.Toolbar.txtSymbol_gg": "Molto più grande di", "DE.Controllers.Toolbar.txtSymbol_greater": "Più grande di", "DE.Controllers.Toolbar.txtSymbol_in": "Elemento Di", "DE.Controllers.Toolbar.txtSymbol_inc": "Incremento", "DE.Controllers.Toolbar.txtSymbol_infinity": "Infinito", "DE.Controllers.Toolbar.txtSymbol_iota": "Iota", "DE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "DE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "DE.Controllers.Toolbar.txtSymbol_leftarrow": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_leftrightarrow": "<PERSON><PERSON>cia sinistra-destra", "DE.Controllers.Toolbar.txtSymbol_leq": "Minore o uguale a ", "DE.Controllers.Toolbar.txtSymbol_less": "Meno di", "DE.Controllers.Toolbar.txtSymbol_ll": "Molto meno di", "DE.Controllers.Toolbar.txtSymbol_minus": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_mp": "<PERSON>o più", "DE.Controllers.Toolbar.txtSymbol_mu": "Mu", "DE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "DE.Controllers.Toolbar.txtSymbol_neq": "<PERSON><PERSON><PERSON> <PERSON>", "DE.Controllers.Toolbar.txtSymbol_ni": "<PERSON><PERSON><PERSON> come <PERSON>", "DE.Controllers.Toolbar.txtSymbol_not": "Segno no", "DE.Controllers.Toolbar.txtSymbol_notexists": "Non esiste", "DE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "DE.Controllers.Toolbar.txtSymbol_o": "Omicron", "DE.Controllers.Toolbar.txtSymbol_omega": "Omega", "DE.Controllers.Toolbar.txtSymbol_partial": "Differenziale Parziale", "DE.Controllers.Toolbar.txtSymbol_percent": "Percent<PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_phi": "Phi", "DE.Controllers.Toolbar.txtSymbol_pi": "Pi", "DE.Controllers.Toolbar.txtSymbol_plus": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_pm": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_propto": "Proporzionale a", "DE.Controllers.Toolbar.txtSymbol_psi": "Psi", "DE.Controllers.Toolbar.txtSymbol_qdrt": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_qed": "Fine della dimostrazione", "DE.Controllers.Toolbar.txtSymbol_rddots": "<PERSON><PERSON> diagonale in alto a destra", "DE.Controllers.Toolbar.txtSymbol_rho": "Rho", "DE.Controllers.Toolbar.txtSymbol_rightarrow": "<PERSON><PERSON><PERSON> destra", "DE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "DE.Controllers.Toolbar.txtSymbol_sqrt": "Simbolo Radicale", "DE.Controllers.Toolbar.txtSymbol_tau": "Tau", "DE.Controllers.Toolbar.txtSymbol_therefore": "Dunque", "DE.Controllers.Toolbar.txtSymbol_theta": "Theta", "DE.Controllers.Toolbar.txtSymbol_times": "Segno di moltiplicazione", "DE.Controllers.Toolbar.txtSymbol_uparrow": "Freccia su", "DE.Controllers.Toolbar.txtSymbol_upsilon": "Upsilon", "DE.Controllers.Toolbar.txtSymbol_varepsilon": "Variante Epsilon", "DE.Controllers.Toolbar.txtSymbol_varphi": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_varpi": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_varrho": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_varsigma": "Variante Sigma", "DE.Controllers.Toolbar.txtSymbol_vartheta": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_vdots": "Ellissi verticale", "DE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "DE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "DE.Controllers.Viewport.textFitPage": "<PERSON>tta alla pagina", "DE.Controllers.Viewport.textFitWidth": "<PERSON><PERSON> alla larghezza", "DE.Controllers.Viewport.txtDarkMode": "Modalità scura", "DE.Views.AddNewCaptionLabelDialog.textLabel": "Etichetta:", "DE.Views.AddNewCaptionLabelDialog.textLabelError": "L'etichetta non deve essere vuota.", "DE.Views.BookmarksDialog.textAdd": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.BookmarksDialog.textBookmarkName": "Nome segnalibro", "DE.Views.BookmarksDialog.textClose": "<PERSON><PERSON>", "DE.Views.BookmarksDialog.textCopy": "Copia", "DE.Views.BookmarksDialog.textDelete": "Elimina", "DE.Views.BookmarksDialog.textGetLink": "<PERSON><PERSON><PERSON> colle<PERSON>", "DE.Views.BookmarksDialog.textGoto": "Vai a", "DE.Views.BookmarksDialog.textHidden": "Segna<PERSON><PERSON> nascosti", "DE.Views.BookmarksDialog.textLocation": "<PERSON><PERSON><PERSON>", "DE.Views.BookmarksDialog.textName": "Nome", "DE.Views.BookmarksDialog.textSort": "Ordina per", "DE.Views.BookmarksDialog.textTitle": "Se<PERSON><PERSON><PERSON>", "DE.Views.BookmarksDialog.txtInvalidName": "il nome del Segnalibro può contenere solo lettere, numeri e underscore, e dovrebbe iniziare con la lettera", "DE.Views.CaptionDialog.textAdd": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.CaptionDialog.textAfter": "<PERSON><PERSON>", "DE.Views.CaptionDialog.textBefore": "Prima", "DE.Views.CaptionDialog.textCaption": "Didascalia", "DE.Views.CaptionDialog.textChapter": "Il capitolo inizia con lo stile", "DE.Views.CaptionDialog.textChapterInc": "Includi il numero del capitolo", "DE.Views.CaptionDialog.textColon": "due punti", "DE.Views.CaptionDialog.textDash": "t<PERSON><PERSON>", "DE.Views.CaptionDialog.textDelete": "Elimina", "DE.Views.CaptionDialog.textEquation": "Equazione", "DE.Views.CaptionDialog.textExamples": "Esempi: <PERSON>bella 2-A, <PERSON><PERSON>gine 1.IV", "DE.Views.CaptionDialog.textExclude": "Escludere l'etichetta dalla didascalia", "DE.Views.CaptionDialog.textFigure": "<PERSON><PERSON><PERSON>", "DE.Views.CaptionDialog.textHyphen": "lineetta d'unione", "DE.Views.CaptionDialog.textInsert": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.CaptionDialog.textLabel": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.CaptionDialog.textLongDash": "trat<PERSON> lungo", "DE.Views.CaptionDialog.textNumbering": "Numerazione", "DE.Views.CaptionDialog.textPeriod": "punto", "DE.Views.CaptionDialog.textSeparator": "<PERSON><PERSON> separatore", "DE.Views.CaptionDialog.textTable": "<PERSON><PERSON>", "DE.Views.CaptionDialog.textTitle": "Inser<PERSON>ci <PERSON>", "DE.Views.CellsAddDialog.textCol": "Colonne", "DE.Views.CellsAddDialog.textDown": "Sotto il cursore", "DE.Views.CellsAddDialog.textLeft": "A sinistra", "DE.Views.CellsAddDialog.textRight": "A destra", "DE.Views.CellsAddDialog.textRow": "<PERSON><PERSON><PERSON>", "DE.Views.CellsAddDialog.textTitle": "Inserisci al<PERSON>ni", "DE.Views.CellsAddDialog.textUp": "Sopra il cursore", "DE.Views.ChartSettings.textAdvanced": "Mostra impostazioni avanzate", "DE.Views.ChartSettings.textChartType": "Cambia tipo di grafico", "DE.Views.ChartSettings.textDown": "<PERSON><PERSON><PERSON>", "DE.Views.ChartSettings.textEditData": "Modifica dati", "DE.Views.ChartSettings.textHeight": "Altezza", "DE.Views.ChartSettings.textLeft": "A sinistra", "DE.Views.ChartSettings.textOriginalSize": "Dimensione reale", "DE.Views.ChartSettings.textPerspective": "Prospettiva", "DE.Views.ChartSettings.textRight": "A destra", "DE.Views.ChartSettings.textSize": "Dimensione", "DE.Views.ChartSettings.textStyle": "Stile", "DE.Views.ChartSettings.textUndock": "Disancora dal pannello", "DE.Views.ChartSettings.textUp": "Verso l'alto", "DE.Views.ChartSettings.textWidth": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ChartSettings.textWrap": "Stile di disposizione testo", "DE.Views.ChartSettings.txtBehind": "<PERSON><PERSON> al testo", "DE.Views.ChartSettings.txtInFront": "<PERSON><PERSON><PERSON> al testo", "DE.Views.ChartSettings.txtInline": "In linea con il testo", "DE.Views.ChartSettings.txtSquare": "Quadrato", "DE.Views.ChartSettings.txtThrough": "All'interno", "DE.Views.ChartSettings.txtTight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.ChartSettings.txtTitle": "Grafico", "DE.Views.ChartSettings.txtTopAndBottom": "Sopra e sotto", "DE.Views.ControlSettingsDialog.strGeneral": "Generale", "DE.Views.ControlSettingsDialog.textAdd": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.ControlSettingsDialog.textAppearance": "Aspetto", "DE.Views.ControlSettingsDialog.textApplyAll": "Applica a tutti", "DE.Views.ControlSettingsDialog.textBox": "Rettangolo di selezione", "DE.Views.ControlSettingsDialog.textChange": "Modifica", "DE.Views.ControlSettingsDialog.textCheckbox": "Casella di controllo", "DE.Views.ControlSettingsDialog.textChecked": "Simbolo Controllato", "DE.Views.ControlSettingsDialog.textColor": "Colore", "DE.Views.ControlSettingsDialog.textCombobox": "<PERSON>lla combinata", "DE.Views.ControlSettingsDialog.textDate": "Formato data", "DE.Views.ControlSettingsDialog.textDelete": "Elimina", "DE.Views.ControlSettingsDialog.textDisplayName": "Visualizza nome", "DE.Views.ControlSettingsDialog.textDown": "<PERSON><PERSON><PERSON>", "DE.Views.ControlSettingsDialog.textDropDown": "Elenco a discesa", "DE.Views.ControlSettingsDialog.textFormat": "Visualizza la data in questo modo", "DE.Views.ControlSettingsDialog.textLang": "<PERSON><PERSON>", "DE.Views.ControlSettingsDialog.textLock": "Blocca", "DE.Views.ControlSettingsDialog.textName": "<PERSON><PERSON>", "DE.Views.ControlSettingsDialog.textNone": "<PERSON><PERSON><PERSON>", "DE.Views.ControlSettingsDialog.textPlaceholder": "Se<PERSON><PERSON>o", "DE.Views.ControlSettingsDialog.textShowAs": "<PERSON>ra come", "DE.Views.ControlSettingsDialog.textSystemColor": "Sistema", "DE.Views.ControlSettingsDialog.textTag": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ControlSettingsDialog.textTitle": "Impostazioni di controllo del contenuto", "DE.Views.ControlSettingsDialog.textUnchecked": "simbolo non controllato", "DE.Views.ControlSettingsDialog.textUp": "Verso l'alto", "DE.Views.ControlSettingsDialog.textValue": "Valore", "DE.Views.ControlSettingsDialog.tipChange": "Cambia simbolo", "DE.Views.ControlSettingsDialog.txtLockDelete": "Il controllo del contenuto non può essere eliminato", "DE.Views.ControlSettingsDialog.txtLockEdit": "I contenuti non possono essere modificati", "DE.Views.CrossReferenceDialog.textAboveBelow": "Sopra/Sotto", "DE.Views.CrossReferenceDialog.textBookmark": "Se<PERSON><PERSON><PERSON>", "DE.Views.CrossReferenceDialog.textBookmarkText": "‎Testo del segnalibro‎", "DE.Views.CrossReferenceDialog.textCaption": "Didascalia‎ intera", "DE.Views.CrossReferenceDialog.textEmpty": "Il riferimento della richiesta è vuoto.", "DE.Views.CrossReferenceDialog.textEndnote": "Nota di chiusura", "DE.Views.CrossReferenceDialog.textEndNoteNum": "Numero nota di chiusura", "DE.Views.CrossReferenceDialog.textEndNoteNumForm": "Numero nota di chiusura (formattato)", "DE.Views.CrossReferenceDialog.textEquation": "Equazione", "DE.Views.CrossReferenceDialog.textFigure": "<PERSON><PERSON><PERSON>", "DE.Views.CrossReferenceDialog.textFootnote": "Nota a piè di pagina", "DE.Views.CrossReferenceDialog.textHeading": "‎Intestazione‎", "DE.Views.CrossReferenceDialog.textHeadingNum": "Numero dell'intestazione", "DE.Views.CrossReferenceDialog.textHeadingNumFull": "Numero dell'intestazione (pieno contesto)", "DE.Views.CrossReferenceDialog.textHeadingNumNo": "Numero dell'intestazione (senza contesto)", "DE.Views.CrossReferenceDialog.textHeadingText": "Testo dell'intestazione", "DE.Views.CrossReferenceDialog.textIncludeAbove": "‎<PERSON><PERSON><PERSON> sopra/sotto‎", "DE.Views.CrossReferenceDialog.textInsert": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.CrossReferenceDialog.textInsertAs": "‎Inserisci come collegamento ipertestuale‎", "DE.Views.CrossReferenceDialog.textLabelNum": "Solo etichetta e numero", "DE.Views.CrossReferenceDialog.textNoteNum": "‎Numero nota a piè di pagina‎", "DE.Views.CrossReferenceDialog.textNoteNumForm": "‎Numero nota a piè di pagina (formattato)‎", "DE.Views.CrossReferenceDialog.textOnlyCaption": "Inserisci solo il testo della didascalia", "DE.Views.CrossReferenceDialog.textPageNum": "Numero di Pagina", "DE.Views.CrossReferenceDialog.textParagraph": "‎Elemento numerato‎", "DE.Views.CrossReferenceDialog.textParaNum": "Numero del paragrafo", "DE.Views.CrossReferenceDialog.textParaNumFull": "Numero del paragrafo (pieno contesto)", "DE.Views.CrossReferenceDialog.textParaNumNo": "Numero del paragrafo (senza contesto)", "DE.Views.CrossReferenceDialog.textSeparate": "Separare i numeri con", "DE.Views.CrossReferenceDialog.textTable": "<PERSON><PERSON>", "DE.Views.CrossReferenceDialog.textText": "Testo del paragrafo", "DE.Views.CrossReferenceDialog.textWhich": "‎Per quale didascalia‎", "DE.Views.CrossReferenceDialog.textWhichBookmark": "‎Per quale segnalibro‎", "DE.Views.CrossReferenceDialog.textWhichEndnote": "‎Per quale nota di chiusura‎", "DE.Views.CrossReferenceDialog.textWhichHeading": "‎Per quale intestazione", "DE.Views.CrossReferenceDialog.textWhichNote": "‎Per quale nota a piè di pagina‎", "DE.Views.CrossReferenceDialog.textWhichPara": "‎Per quale articolo numerato‎", "DE.Views.CrossReferenceDialog.txtReference": "Inserisci riferimento a", "DE.Views.CrossReferenceDialog.txtTitle": "‎Riferimento incrociato‎", "DE.Views.CrossReferenceDialog.txtType": "‎Tipo di riferimento‎", "DE.Views.CustomColumnsDialog.textColumns": "Numero di colonne", "DE.Views.CustomColumnsDialog.textSeparator": "Divisore Colonna", "DE.Views.CustomColumnsDialog.textSpacing": "spaziatura fra le colonne", "DE.Views.CustomColumnsDialog.textTitle": "Colonne", "DE.Views.DateTimeDialog.confirmDefault": "Imposta formato predefinito per {0}: \"{1}\"", "DE.Views.DateTimeDialog.textDefault": "Imposta come predefinito", "DE.Views.DateTimeDialog.textFormat": "Formati", "DE.Views.DateTimeDialog.textLang": "<PERSON><PERSON>", "DE.Views.DateTimeDialog.textUpdate": "Aggiorna automaticamente", "DE.Views.DateTimeDialog.txtTitle": "Data e ora", "DE.Views.DocProtection.hintProtectDoc": "Proteggi documento", "DE.Views.DocProtection.txtProtectDoc": "Proteggi documento", "DE.Views.DocumentHolder.aboveText": "Al di sopra", "DE.Views.DocumentHolder.addCommentText": "Aggiungi commento", "DE.Views.DocumentHolder.advancedDropCapText": "Impostazioni di capolettera", "DE.Views.DocumentHolder.advancedFrameText": "Impostazioni avanzate della cornice", "DE.Views.DocumentHolder.advancedParagraphText": "Impostazioni avanzate del paragrafo", "DE.Views.DocumentHolder.advancedTableText": "Impostazioni avanzate della tabella", "DE.Views.DocumentHolder.advancedText": "Impostazioni avanzate", "DE.Views.DocumentHolder.alignmentText": "Allineamento", "DE.Views.DocumentHolder.belowText": "Al di sotto", "DE.Views.DocumentHolder.breakBeforeText": "Anteponi Interruzione di pagina", "DE.Views.DocumentHolder.bulletsText": "Elenchi puntati e Numerati", "DE.Views.DocumentHolder.cellAlignText": "Allineamento verticale cella", "DE.Views.DocumentHolder.cellText": "Cella", "DE.Views.DocumentHolder.centerText": "Al centro", "DE.Views.DocumentHolder.chartText": "Impostazioni avanzate grafico", "DE.Views.DocumentHolder.columnText": "<PERSON>onna", "DE.Views.DocumentHolder.deleteColumnText": "Elimina colonna", "DE.Views.DocumentHolder.deleteRowText": "Elimina riga", "DE.Views.DocumentHolder.deleteTableText": "<PERSON><PERSON> tabella", "DE.Views.DocumentHolder.deleteText": "Elimina", "DE.Views.DocumentHolder.direct270Text": "Ruota testo verso l'alto", "DE.Views.DocumentHolder.direct90Text": "Ruota testo verso il basso", "DE.Views.DocumentHolder.directHText": "Orizzontale", "DE.Views.DocumentHolder.directionText": "<PERSON><PERSON><PERSON> del testo", "DE.Views.DocumentHolder.editChartText": "Modifica dati", "DE.Views.DocumentHolder.editFooterText": "Modifica piè di pagina", "DE.Views.DocumentHolder.editHeaderText": "Modifica intestazione", "DE.Views.DocumentHolder.editHyperlinkText": "Modifica collegamento ipertestuale", "DE.Views.DocumentHolder.guestText": "Ospite", "DE.Views.DocumentHolder.hyperlinkText": "Collegamento ipertestuale", "DE.Views.DocumentHolder.ignoreAllSpellText": "<PERSON><PERSON><PERSON> tutto", "DE.Views.DocumentHolder.ignoreSpellText": "Ignora", "DE.Views.DocumentHolder.imageText": "Impostazioni avanzate dell'immagine", "DE.Views.DocumentHolder.insertColumnLeftText": "Colonna a sinistra", "DE.Views.DocumentHolder.insertColumnRightText": "Colonna a destra", "DE.Views.DocumentHolder.insertColumnText": "Inserisci colonna", "DE.Views.DocumentHolder.insertRowAboveText": "Riga sopra", "DE.Views.DocumentHolder.insertRowBelowText": "Riga sotto", "DE.Views.DocumentHolder.insertRowText": "Inserisci riga", "DE.Views.DocumentHolder.insertText": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.keepLinesText": "<PERSON><PERSON><PERSON> assieme le righe", "DE.Views.DocumentHolder.langText": "Seleziona lingua", "DE.Views.DocumentHolder.leftText": "A sinistra", "DE.Views.DocumentHolder.loadSpellText": "Caricamento varianti in corso...", "DE.Views.DocumentHolder.mergeCellsText": "Unisci celle", "DE.Views.DocumentHolder.moreText": "<PERSON>ù varianti...", "DE.Views.DocumentHolder.noSpellVariantsText": "Nessuna variante", "DE.Views.DocumentHolder.notcriticalErrorTitle": "Avvertimento", "DE.Views.DocumentHolder.originalSizeText": "Dimensione reale", "DE.Views.DocumentHolder.paragraphText": "Paragrafo", "DE.Views.DocumentHolder.removeHyperlinkText": "Elimina collegamento ipertestuale", "DE.Views.DocumentHolder.rightText": "A destra", "DE.Views.DocumentHolder.rowText": "Riga", "DE.Views.DocumentHolder.saveStyleText": "Crea nuovo stile", "DE.Views.DocumentHolder.selectCellText": "Seleziona cella", "DE.Views.DocumentHolder.selectColumnText": "Seleziona colonna", "DE.Views.DocumentHolder.selectRowText": "Seleziona riga", "DE.Views.DocumentHolder.selectTableText": "<PERSON><PERSON><PERSON><PERSON> tabella", "DE.Views.DocumentHolder.selectText": "Seleziona", "DE.Views.DocumentHolder.shapeText": "Impostazioni avanzate forma", "DE.Views.DocumentHolder.spellcheckText": "Controllo ortografia", "DE.Views.DocumentHolder.splitCellsText": "Divisione cella in corso...", "DE.Views.DocumentHolder.splitCellTitleText": "Dividi cella", "DE.Views.DocumentHolder.strDelete": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.strDetails": "Dettagli firma", "DE.Views.DocumentHolder.strSetup": "Impostazioni firma", "DE.Views.DocumentHolder.strSign": "Firma", "DE.Views.DocumentHolder.styleText": "Formattazione come stile", "DE.Views.DocumentHolder.tableText": "<PERSON><PERSON>", "DE.Views.DocumentHolder.textAccept": "‎Accetta modifica‎", "DE.Views.DocumentHolder.textAlign": "Allinea", "DE.Views.DocumentHolder.textArrange": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textArrangeBack": "<PERSON><PERSON><PERSON> in secondo piano", "DE.Views.DocumentHolder.textArrangeBackward": "Porta indietro", "DE.Views.DocumentHolder.textArrangeForward": "Porta avanti", "DE.Views.DocumentHolder.textArrangeFront": "<PERSON>a in primo piano", "DE.Views.DocumentHolder.textCells": "Celle", "DE.Views.DocumentHolder.textCol": "Elimina l'intera colonna", "DE.Views.DocumentHolder.textContentControls": "<PERSON><PERSON>", "DE.Views.DocumentHolder.textContinueNumbering": "Continua la numerazione", "DE.Views.DocumentHolder.textCopy": "Copia", "DE.Views.DocumentHolder.textCrop": "Rita<PERSON>", "DE.Views.DocumentHolder.textCropFill": "Riempimento", "DE.Views.DocumentHolder.textCropFit": "Adatta", "DE.Views.DocumentHolder.textCut": "Taglia", "DE.Views.DocumentHolder.textDistributeCols": "Distribuisci colonne", "DE.Views.DocumentHolder.textDistributeRows": "Distribuisci righe", "DE.Views.DocumentHolder.textEditControls": "Impostazioni di controllo del contenuto", "DE.Views.DocumentHolder.textEditPoints": "Modifica punti", "DE.Views.DocumentHolder.textEditWrapBoundary": "Modifica bordi disposizione testo", "DE.Views.DocumentHolder.textFlipH": "Capovolgi orizzontalmente", "DE.Views.DocumentHolder.textFlipV": "Capovolgi verticalmente", "DE.Views.DocumentHolder.textFollow": "<PERSON><PERSON><PERSON> mossa", "DE.Views.DocumentHolder.textFromFile": "Da file", "DE.Views.DocumentHolder.textFromStorage": "Da spazio di archiviazione", "DE.Views.DocumentHolder.textFromUrl": "Da URL", "DE.Views.DocumentHolder.textJoinList": "Iscriviti alla lista precedente", "DE.Views.DocumentHolder.textLeft": "Sposta celle a sinistra", "DE.Views.DocumentHolder.textNest": "<PERSON><PERSON> nidificata", "DE.Views.DocumentHolder.textNextPage": "Pagina successiva", "DE.Views.DocumentHolder.textNumberingValue": "Valore di numerazione", "DE.Views.DocumentHolder.textPaste": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textPrevPage": "<PERSON><PERSON><PERSON>e", "DE.Views.DocumentHolder.textRefreshField": "Aggiorna campo", "DE.Views.DocumentHolder.textReject": "‎Rifiu<PERSON> modifica‎", "DE.Views.DocumentHolder.textRemCheckBox": "<PERSON><PERSON><PERSON><PERSON> casella di controllo", "DE.Views.DocumentHolder.textRemComboBox": "Rimuovi cella combinata", "DE.Views.DocumentHolder.textRemDropdown": "Rimuovi menù a discesa", "DE.Views.DocumentHolder.textRemField": "Elimina il campo del Testo", "DE.Views.DocumentHolder.textRemove": "Elimina", "DE.Views.DocumentHolder.textRemoveControl": "Rimuovi il controllo del contenuto", "DE.Views.DocumentHolder.textRemPicture": "<PERSON><PERSON>e", "DE.Views.DocumentHolder.textRemRadioBox": "Rimuovi pulsante dell'opzione", "DE.Views.DocumentHolder.textReplace": "Sostituisci immagine", "DE.Views.DocumentHolder.textRotate": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textRotate270": "Ruota di 90 ° in senso antiorario", "DE.Views.DocumentHolder.textRotate90": "Ruota di 90 ° in senso orario", "DE.Views.DocumentHolder.textRow": "Elimina l'intera riga", "DE.Views.DocumentHolder.textSeparateList": "Elenco separato", "DE.Views.DocumentHolder.textSettings": "Impostazioni", "DE.Views.DocumentHolder.textSeveral": "Alcune righe/colonne", "DE.Views.DocumentHolder.textShapeAlignBottom": "<PERSON><PERSON>a in basso", "DE.Views.DocumentHolder.textShapeAlignCenter": "Allinea al centro", "DE.Views.DocumentHolder.textShapeAlignLeft": "Allinea a sinistra", "DE.Views.DocumentHolder.textShapeAlignMiddle": "Allinea in mezzo", "DE.Views.DocumentHolder.textShapeAlignRight": "Allinea a destra", "DE.Views.DocumentHolder.textShapeAlignTop": "<PERSON><PERSON>a in alto", "DE.Views.DocumentHolder.textStartNewList": "Inizia nuovo elenco", "DE.Views.DocumentHolder.textStartNumberingFrom": "Imposta il valore numerico", "DE.Views.DocumentHolder.textTitleCellsRemove": "Elimina celle", "DE.Views.DocumentHolder.textTOC": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textTOCSettings": "Impostazioni sommario", "DE.Views.DocumentHolder.textUndo": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textUpdateAll": "Aggiorna intera tabella", "DE.Views.DocumentHolder.textUpdatePages": "Aggiorna solo numeri di pagina", "DE.Views.DocumentHolder.textUpdateTOC": "Aggiorna sommario", "DE.Views.DocumentHolder.textWrap": "Stile di disposizione testo", "DE.Views.DocumentHolder.tipIsLocked": "Questo elemento è attualmente in fase di modifica da un altro utente.", "DE.Views.DocumentHolder.toDictionaryText": "Aggiungi al Dizionario", "DE.Views.DocumentHolder.txtAddBottom": "Aggiungi bordo inferiore", "DE.Views.DocumentHolder.txtAddFractionBar": "Aggiungi barra di frazione", "DE.Views.DocumentHolder.txtAddHor": "Aggiungi linea orizzontale", "DE.Views.DocumentHolder.txtAddLB": "Aggiungi linea inferiore sinistra", "DE.Views.DocumentHolder.txtAddLeft": "Aggiungi bordo sinistro", "DE.Views.DocumentHolder.txtAddLT": "Aggiungi linea superiore sinistra", "DE.Views.DocumentHolder.txtAddRight": "Agg<PERSON>ngi bordo destro", "DE.Views.DocumentHolder.txtAddTop": "Aggiungi bordo superiore", "DE.Views.DocumentHolder.txtAddVer": "Aggiungi linea verticale", "DE.Views.DocumentHolder.txtAlignToChar": "Allinea al carattere", "DE.Views.DocumentHolder.txtBehind": "<PERSON><PERSON> al testo", "DE.Views.DocumentHolder.txtBorderProps": "Proprietà bordo", "DE.Views.DocumentHolder.txtBottom": "In basso", "DE.Views.DocumentHolder.txtColumnAlign": "Allineamento colonna", "DE.Views.DocumentHolder.txtDecreaseArg": "Diminuisci dimensione argomento", "DE.Views.DocumentHolder.txtDeleteArg": "Elimina argomento", "DE.Views.DocumentHolder.txtDeleteBreak": "Elimina interruzione manuale", "DE.Views.DocumentHolder.txtDeleteChars": "Elimina i caratteri racchiusi", "DE.Views.DocumentHolder.txtDeleteCharsAndSeparators": "Elimina caratteri e separatori inclusi", "DE.Views.DocumentHolder.txtDeleteEq": "Elimina equazione", "DE.Views.DocumentHolder.txtDeleteGroupChar": "<PERSON>mina char", "DE.Views.DocumentHolder.txtDeleteRadical": "Elimina radicale", "DE.Views.DocumentHolder.txtDistribHor": "Distribuisci orizzontalmente", "DE.Views.DocumentHolder.txtDistribVert": "Distribuisci verticalmente", "DE.Views.DocumentHolder.txtEmpty": "(<PERSON><PERSON><PERSON>)", "DE.Views.DocumentHolder.txtFractionLinear": "Modifica a frazione lineare", "DE.Views.DocumentHolder.txtFractionSkewed": "Modifica a frazione obliqua", "DE.Views.DocumentHolder.txtFractionStacked": "Modifica a frazione impilata", "DE.Views.DocumentHolder.txtGroup": "Raggruppa", "DE.Views.DocumentHolder.txtGroupCharOver": "Char sul testo", "DE.Views.DocumentHolder.txtGroupCharUnder": "Char sotto il testo", "DE.Views.DocumentHolder.txtHideBottom": "Nascondi il bordo inferiore", "DE.Views.DocumentHolder.txtHideBottomLimit": "Nascondi il limite inferiore", "DE.Views.DocumentHolder.txtHideCloseBracket": "Nascondi parentesi chiusa", "DE.Views.DocumentHolder.txtHideDegree": "Nascondi grado", "DE.Views.DocumentHolder.txtHideHor": "Nascondi linea orizzontale", "DE.Views.DocumentHolder.txtHideLB": "Nascondi linea inferiore sinistra", "DE.Views.DocumentHolder.txtHideLeft": "Nascondi bordo sinistro", "DE.Views.DocumentHolder.txtHideLT": "Nascondi linea superiore sinistra", "DE.Views.DocumentHolder.txtHideOpenBracket": "Nascondi parentesi aperta", "DE.Views.DocumentHolder.txtHidePlaceholder": "Nascondi segnaposto", "DE.Views.DocumentHolder.txtHideRight": "Nascondi bordo destro", "DE.Views.DocumentHolder.txtHideTop": "Nascondi bordo superiore", "DE.Views.DocumentHolder.txtHideTopLimit": "Nascondi limite superiore", "DE.Views.DocumentHolder.txtHideVer": "Nascondi linea verticale", "DE.Views.DocumentHolder.txtIncreaseArg": "Aumenta dimensione argomento", "DE.Views.DocumentHolder.txtInFront": "<PERSON><PERSON><PERSON> al testo", "DE.Views.DocumentHolder.txtInline": "In linea con il testo", "DE.Views.DocumentHolder.txtInsertArgAfter": "Inserisci argomento dopo", "DE.Views.DocumentHolder.txtInsertArgBefore": "Inserisci argomento prima", "DE.Views.DocumentHolder.txtInsertBreak": "Inserisci interruzione manuale", "DE.Views.DocumentHolder.txtInsertCaption": "Inser<PERSON>ci <PERSON>", "DE.Views.DocumentHolder.txtInsertEqAfter": "Inserisci Equazione dopo", "DE.Views.DocumentHolder.txtInsertEqBefore": "Inserisci Equazione prima", "DE.Views.DocumentHolder.txtKeepTextOnly": "<PERSON><PERSON><PERSON> solo il testo", "DE.Views.DocumentHolder.txtLimitChange": "Modifica posizione dei limiti", "DE.Views.DocumentHolder.txtLimitOver": "Limite sul testo", "DE.Views.DocumentHolder.txtLimitUnder": "Limite sotto il testo", "DE.Views.DocumentHolder.txtMatchBrackets": "Adatta le parentesi all'altezza dell'argomento", "DE.Views.DocumentHolder.txtMatrixAlign": "Allineamento Matrice", "DE.Views.DocumentHolder.txtOverbar": "Barra sopra al testo", "DE.Views.DocumentHolder.txtOverwriteCells": "Sovrascrivi celle", "DE.Views.DocumentHolder.txtPasteSourceFormat": "Mantieni la formattazione sorgente", "DE.Views.DocumentHolder.txtPressLink": "Premi {0} e clicca sul collegamento", "DE.Views.DocumentHolder.txtPrintSelection": "Stampa Selezione", "DE.Views.DocumentHolder.txtRemFractionBar": "Rimuovi la barra di frazione", "DE.Views.DocumentHolder.txtRemLimit": "Rimuovi limite", "DE.Views.DocumentHolder.txtRemoveAccentChar": "R<PERSON>uovi accento carattere", "DE.Views.DocumentHolder.txtRemoveBar": "<PERSON><PERSON> barra", "DE.Views.DocumentHolder.txtRemoveWarning": "Vuoi rimuovere questa firma?<br>Non può essere annullata.", "DE.Views.DocumentHolder.txtRemScripts": "Rimuovi gli script", "DE.Views.DocumentHolder.txtRemSubscript": "Elimina pedice", "DE.Views.DocumentHolder.txtRemSuperscript": "Elimina apice", "DE.Views.DocumentHolder.txtScriptsAfter": "Script dopo il testo", "DE.Views.DocumentHolder.txtScriptsBefore": "Script prima del testo", "DE.Views.DocumentHolder.txtShowBottomLimit": "Mostra limite inferiore", "DE.Views.DocumentHolder.txtShowCloseBracket": "Mostra parentesi quadra di chiusura", "DE.Views.DocumentHolder.txtShowDegree": "Mostra grado", "DE.Views.DocumentHolder.txtShowOpenBracket": "Mostra parentesi quadra di apertura", "DE.Views.DocumentHolder.txtShowPlaceholder": "Mostra segna<PERSON>o", "DE.Views.DocumentHolder.txtShowTopLimit": "Mostra limite superiore", "DE.Views.DocumentHolder.txtSquare": "Quadrato", "DE.Views.DocumentHolder.txtStretchBrackets": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.txtThrough": "All'interno", "DE.Views.DocumentHolder.txtTight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.txtTop": "In alto", "DE.Views.DocumentHolder.txtTopAndBottom": "Sopra e sotto", "DE.Views.DocumentHolder.txtUnderbar": "<PERSON>a sotto al testo", "DE.Views.DocumentHolder.txtUngroup": "Separa", "DE.Views.DocumentHolder.txtWarnUrl": "C<PERSON>care questo link può essere dannoso per il tuo dispositivo e i dati.<br>Sei sicuro di voler continuare?", "DE.Views.DocumentHolder.updateStyleText": "Aggiorna %1 stile", "DE.Views.DocumentHolder.vertAlignText": "Allineamento verticale", "DE.Views.DropcapSettingsAdvanced.strBorders": "Bordi e riempimento", "DE.Views.DropcapSettingsAdvanced.strDropcap": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.strMargins": "<PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textAlign": "Allineamento", "DE.Views.DropcapSettingsAdvanced.textAtLeast": "<PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textAuto": "Auto", "DE.Views.DropcapSettingsAdvanced.textBackColor": "Colore sfondo", "DE.Views.DropcapSettingsAdvanced.textBorderColor": "Colore bordo", "DE.Views.DropcapSettingsAdvanced.textBorderDesc": "Clicca sul diagramma o utilizza i pulsanti per selezionare i bordi", "DE.Views.DropcapSettingsAdvanced.textBorderWidth": "Dimensioni bordo", "DE.Views.DropcapSettingsAdvanced.textBottom": "In basso", "DE.Views.DropcapSettingsAdvanced.textCenter": "Al centro", "DE.Views.DropcapSettingsAdvanced.textColumn": "<PERSON>onna", "DE.Views.DropcapSettingsAdvanced.textDistance": "<PERSON><PERSON><PERSON> dal testo", "DE.Views.DropcapSettingsAdvanced.textExact": "Esatto", "DE.Views.DropcapSettingsAdvanced.textFlow": "Cornice dinamica", "DE.Views.DropcapSettingsAdvanced.textFont": "Tipo di carattere", "DE.Views.DropcapSettingsAdvanced.textFrame": "Cornice", "DE.Views.DropcapSettingsAdvanced.textHeight": "Altezza", "DE.Views.DropcapSettingsAdvanced.textHorizontal": "Orizzontale", "DE.Views.DropcapSettingsAdvanced.textInline": "Cornice in linea", "DE.Views.DropcapSettingsAdvanced.textInMargin": "<PERSON><PERSON> margine", "DE.Views.DropcapSettingsAdvanced.textInText": "<PERSON><PERSON> testo", "DE.Views.DropcapSettingsAdvanced.textLeft": "A sinistra", "DE.Views.DropcapSettingsAdvanced.textMargin": "<PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textMove": "Sposta col testo", "DE.Views.DropcapSettingsAdvanced.textNone": "<PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textPage": "<PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textParagraph": "Paragrafo", "DE.Views.DropcapSettingsAdvanced.textParameters": "Parametri", "DE.Views.DropcapSettingsAdvanced.textPosition": "Posizione", "DE.Views.DropcapSettingsAdvanced.textRelative": "Rispetto a", "DE.Views.DropcapSettingsAdvanced.textRight": "A destra", "DE.Views.DropcapSettingsAdvanced.textRowHeight": "Altezza righe", "DE.Views.DropcapSettingsAdvanced.textTitle": "Capolettera - Impostazioni avanzate", "DE.Views.DropcapSettingsAdvanced.textTitleFrame": "Cornice - Impostazioni avanzate", "DE.Views.DropcapSettingsAdvanced.textTop": "In alto", "DE.Views.DropcapSettingsAdvanced.textVertical": "Verticale", "DE.Views.DropcapSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.tipFontName": "Tipo di carattere", "DE.Views.DropcapSettingsAdvanced.txtNoBorders": "<PERSON><PERSON><PERSON> bordo", "DE.Views.EditListItemDialog.textDisplayName": "Visualizza nome", "DE.Views.EditListItemDialog.textNameError": "Visualizza nome non può essere vuoto.", "DE.Views.EditListItemDialog.textValue": "Valore", "DE.Views.EditListItemDialog.textValueError": "Un elemento con lo stesso valore esiste già.", "DE.Views.FileMenu.btnBackCaption": "Apri percorso file", "DE.Views.FileMenu.btnCloseMenuCaption": "<PERSON><PERSON> il <PERSON>ù", "DE.Views.FileMenu.btnCreateNewCaption": "<PERSON>rea una nuova didascalia", "DE.Views.FileMenu.btnDownloadCaption": "Scarica come", "DE.Views.FileMenu.btnExitCaption": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenu.btnFileOpenCaption": "Apr<PERSON>", "DE.Views.FileMenu.btnHelpCaption": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenu.btnHistoryCaption": "Cronologia delle versioni", "DE.Views.FileMenu.btnInfoCaption": "Informazioni documento", "DE.Views.FileMenu.btnPrintCaption": "Stampa", "DE.Views.FileMenu.btnProtectCaption": "<PERSON>tegg<PERSON>", "DE.Views.FileMenu.btnRecentFilesCaption": "<PERSON>i recenti", "DE.Views.FileMenu.btnRenameCaption": "Rinomina in corso", "DE.Views.FileMenu.btnReturnCaption": "Torna al documento", "DE.Views.FileMenu.btnRightsCaption": "<PERSON><PERSON><PERSON> di accesso", "DE.Views.FileMenu.btnSaveAsCaption": "<PERSON>va come", "DE.Views.FileMenu.btnSaveCaption": "<PERSON><PERSON>", "DE.Views.FileMenu.btnSaveCopyAsCaption": "Salva copia come", "DE.Views.FileMenu.btnSettingsCaption": "Impostazioni avanzate", "DE.Views.FileMenu.btnToEditCaption": "Modifica documento", "DE.Views.FileMenu.textDownload": "Scarica", "DE.Views.FileMenuPanels.CreateNew.txtBlank": "Documento Vuoto", "DE.Views.FileMenuPanels.CreateNew.txtCreateNew": "Crea nuovo", "DE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Applica", "DE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Aggiungi Autore", "DE.Views.FileMenuPanels.DocumentInfo.txtAddText": "Aggiu<PERSON>i testo", "DE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Applicazione", "DE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "Autore", "DE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Modifica diritti di accesso", "DE.Views.FileMenuPanels.DocumentInfo.txtComment": "Commento", "DE.Views.FileMenuPanels.DocumentInfo.txtCreated": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtFastWV": "Visualizzazione Web veloce", "DE.Views.FileMenuPanels.DocumentInfo.txtLoading": "Caricamento in corso...", "DE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Ultima modifica di", "DE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Ultima modifica", "DE.Views.FileMenuPanels.DocumentInfo.txtNo": "No", "DE.Views.FileMenuPanels.DocumentInfo.txtOwner": "Proprietario", "DE.Views.FileMenuPanels.DocumentInfo.txtPages": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtPageSize": "Dimensione pagina", "DE.Views.FileMenuPanels.DocumentInfo.txtParagraphs": "Paragrafi", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfProducer": "Produttore PDF", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfTagged": "PDF con tag", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfVer": "‎Versione PDF‎", "DE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtRights": "Persone che hanno diritti", "DE.Views.FileMenuPanels.DocumentInfo.txtSpaces": "Caratteri compresi spazi", "DE.Views.FileMenuPanels.DocumentInfo.txtStatistics": "Statistiche", "DE.Views.FileMenuPanels.DocumentInfo.txtSubject": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtSymbols": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtTags": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtTitle": "<PERSON><PERSON> documento", "DE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Caricato", "DE.Views.FileMenuPanels.DocumentInfo.txtWords": "Parole", "DE.Views.FileMenuPanels.DocumentInfo.txtYes": "Sì", "DE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Modifica diritti di accesso", "DE.Views.FileMenuPanels.DocumentRights.txtRights": "Persone che hanno diritti", "DE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Avviso", "DE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "con Password", "DE.Views.FileMenuPanels.ProtectDoc.strProtect": "Proteggi documento", "DE.Views.FileMenuPanels.ProtectDoc.strSignature": "con Firma", "DE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Modifica documento", "DE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "La modifica eliminerà le firme dal documento.<br>V<PERSON><PERSON> continuare?", "DE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "Questo documento è protetto con password", "DE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "Questo documento necessita di essere firmato", "DE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Le firme valide sono state aggiunte al documento. Il documento è protetto dalla modifica.", "DE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Alcune delle firme digitali preseti nel documento non sono valide o non possono essere verificate. Il documento è protetto dalla modifica.", "DE.Views.FileMenuPanels.ProtectDoc.txtView": "Mostra firme", "DE.Views.FileMenuPanels.Settings.okButtonText": "Applica", "DE.Views.FileMenuPanels.Settings.strCoAuthMode": "Modalità di co-editing", "DE.Views.FileMenuPanels.Settings.strFast": "Rapido", "DE.Views.FileMenuPanels.Settings.strFontRender": "Suggerimento per i caratteri", "DE.Views.FileMenuPanels.Settings.strIgnoreWordsInUPPERCASE": "Ignora le parole in MAIUSCOLO", "DE.Views.FileMenuPanels.Settings.strIgnoreWordsWithNumbers": "Ignora le parole con i numeri", "DE.Views.FileMenuPanels.Settings.strMacrosSettings": "Impostazioni macro", "DE.Views.FileMenuPanels.Settings.strPasteButton": "Mostra il pulsante opzioni Incolla quando il contenuto viene incollato", "DE.Views.FileMenuPanels.Settings.strShowChanges": "Evidenzia modifiche di collaborazione in tempo reale", "DE.Views.FileMenuPanels.Settings.strShowComments": "Mostrare i commenti nel testo", "DE.Views.FileMenuPanels.Settings.strShowOthersChanges": "Mostrare le modifiche degli altri utenti", "DE.Views.FileMenuPanels.Settings.strShowResolvedComments": "Mostrare i commenti risolti", "DE.Views.FileMenuPanels.Settings.strStrict": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.strTheme": "Tema dell'interfaccia", "DE.Views.FileMenuPanels.Settings.strUnit": "Unità di misura", "DE.Views.FileMenuPanels.Settings.strZoom": "Valore di zoom predefinito", "DE.Views.FileMenuPanels.Settings.text10Minutes": "Ogni 10 minuti", "DE.Views.FileMenuPanels.Settings.text30Minutes": "Ogni 30 minuti", "DE.Views.FileMenuPanels.Settings.text5Minutes": "Ogni 5 minuti", "DE.Views.FileMenuPanels.Settings.text60Minutes": "Ogni ora", "DE.Views.FileMenuPanels.Settings.textAlignGuides": "Guide di allineamento", "DE.Views.FileMenuPanels.Settings.textAutoRecover": "Recupero automatico", "DE.Views.FileMenuPanels.Settings.textAutoSave": "Salvataggio automatico", "DE.Views.FileMenuPanels.Settings.textDisabled": "Disattivat<PERSON>", "DE.Views.FileMenuPanels.Settings.textForceSave": "Salvare versioni intermedie", "DE.Views.FileMenuPanels.Settings.textMinute": "Ogni minuto", "DE.Views.FileMenuPanels.Settings.textOldVersions": "Rendi i file compatibili con le versioni precedenti di MS Word quando vengono salvati come DOCX", "DE.Views.FileMenuPanels.Settings.txtAll": "<PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.txtAutoCorrect": "Opzioni di correzione automatica ...", "DE.Views.FileMenuPanels.Settings.txtCacheMode": "Modalità cache predefinita", "DE.Views.FileMenuPanels.Settings.txtChangesBalloons": "Mostrare tramite clic su balloons", "DE.Views.FileMenuPanels.Settings.txtChangesTip": "Mostrare tramite suggerimenti al passaggio del mouse", "DE.Views.FileMenuPanels.Settings.txtCm": "Centimetro", "DE.Views.FileMenuPanels.Settings.txtCollaboration": "Collaborazione", "DE.Views.FileMenuPanels.Settings.txtDarkMode": "Attivare la modalità scura di documenti", "DE.Views.FileMenuPanels.Settings.txtEditingSaving": "Modifica e salvataggio", "DE.Views.FileMenuPanels.Settings.txtFastTip": "Co-editing in tempo reale. <PERSON><PERSON> le modifiche vengono salvate automaticamente", "DE.Views.FileMenuPanels.Settings.txtFitPage": "<PERSON>tta alla pagina", "DE.Views.FileMenuPanels.Settings.txtFitWidth": "<PERSON><PERSON> alla larghezza", "DE.Views.FileMenuPanels.Settings.txtHieroglyphs": "Geroglifici", "DE.Views.FileMenuPanels.Settings.txtInch": "Pollice", "DE.Views.FileMenuPanels.Settings.txtLast": "Ultime", "DE.Views.FileMenuPanels.Settings.txtMac": "come su OS X", "DE.Views.FileMenuPanels.Settings.txtNative": "Nativo", "DE.Views.FileMenuPanels.Settings.txtNone": "Nessuna", "DE.Views.FileMenuPanels.Settings.txtProofing": "Correzione", "DE.Views.FileMenuPanels.Settings.txtPt": "Punt<PERSON>", "DE.Views.FileMenuPanels.Settings.txtRunMacros": "<PERSON><PERSON><PERSON> tutto", "DE.Views.FileMenuPanels.Settings.txtRunMacrosDesc": "Abilita tutte le macro senza una notifica", "DE.Views.FileMenuPanels.Settings.txtShowTrackChanges": "Mostrare le modifiche durante le revisioni", "DE.Views.FileMenuPanels.Settings.txtSpellCheck": "Controllo ortografico", "DE.Views.FileMenuPanels.Settings.txtStopMacros": "<PERSON><PERSON><PERSON><PERSON> tutto", "DE.Views.FileMenuPanels.Settings.txtStopMacrosDesc": "Disabilita tutte le macro senza notifica", "DE.Views.FileMenuPanels.Settings.txtStrictTip": "Utilizza il pulsante \"Salvare\" per sincronizzare le modifiche apportate da te e da altri", "DE.Views.FileMenuPanels.Settings.txtUseAltKey": "Utilizzare il tasto Alt per navigare nell'interfaccia utente usando la tastiera", "DE.Views.FileMenuPanels.Settings.txtUseOptionKey": "Utilizzare il tasto Opzione per navigare nell'interfaccia utente usando la tastiera", "DE.Views.FileMenuPanels.Settings.txtWarnMacros": "Mostra notifica", "DE.Views.FileMenuPanels.Settings.txtWarnMacrosDesc": "Disabilita tutte le macro con notifica", "DE.Views.FileMenuPanels.Settings.txtWin": "come su Windows", "DE.Views.FileMenuPanels.Settings.txtWorkspace": "Spazio di lavoro", "DE.Views.FormSettings.textAlways": "Sempre", "DE.Views.FormSettings.textAspect": "Blocca proporzioni", "DE.Views.FormSettings.textAtLeast": "Almeno", "DE.Views.FormSettings.textAuto": "Automatico", "DE.Views.FormSettings.textAutofit": "adattare automaticamente", "DE.Views.FormSettings.textBackgroundColor": "Colore di sfondo", "DE.Views.FormSettings.textCheckbox": "Casella di controllo", "DE.Views.FormSettings.textColor": "Colore bordo", "DE.Views.FormSettings.textComb": "Combinazione di caratteri", "DE.Views.FormSettings.textCombobox": "<PERSON>lla combinata", "DE.Views.FormSettings.textComplex": "Campo complesso", "DE.Views.FormSettings.textConnected": "Campi collegati", "DE.Views.FormSettings.textDelete": "Elimina", "DE.Views.FormSettings.textDigits": "Cifre", "DE.Views.FormSettings.textDisconnect": "<PERSON><PERSON><PERSON>", "DE.Views.FormSettings.textDropDown": "Menù a discesca", "DE.Views.FormSettings.textExact": "Esattamente", "DE.Views.FormSettings.textField": "‎Campo di testo‎", "DE.Views.FormSettings.textFixed": "Dimensione di campo fissa", "DE.Views.FormSettings.textFormat": "Formato", "DE.Views.FormSettings.textFormatSymbols": "Simboli consentiti", "DE.Views.FormSettings.textFromFile": "Da file", "DE.Views.FormSettings.textFromStorage": "Da spazio di archiviazione", "DE.Views.FormSettings.textFromUrl": "Da URL", "DE.Views.FormSettings.textGroupKey": "Chiave di gruppo", "DE.Views.FormSettings.textImage": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.FormSettings.textKey": "Chiave", "DE.Views.FormSettings.textLock": "Blocca", "DE.Views.FormSettings.textMaxChars": "‎Limite caratteri‎", "DE.Views.FormSettings.textMulti": "Campo con molte righe", "DE.Views.FormSettings.textNever": "<PERSON>", "DE.Views.FormSettings.textNoBorder": "<PERSON><PERSON> bordo", "DE.Views.FormSettings.textNone": "<PERSON><PERSON><PERSON>", "DE.Views.FormSettings.textPlaceholder": "Se<PERSON><PERSON>o", "DE.Views.FormSettings.textRadiobox": "Pulsante opzione", "DE.Views.FormSettings.textRequired": "<PERSON><PERSON>", "DE.Views.FormSettings.textScale": "Quando scalare", "DE.Views.FormSettings.textSelectImage": "Seleziona Immagine", "DE.Views.FormSettings.textTag": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.FormSettings.textTip": "Suggerimento", "DE.Views.FormSettings.textTipAdd": "Aggiungi un nuovo valore", "DE.Views.FormSettings.textTipDelete": "Elimina valore", "DE.Views.FormSettings.textTipDown": "Sposta verso il basso", "DE.Views.FormSettings.textTipUp": "Sposta verso l'alto", "DE.Views.FormSettings.textTooBig": "L'immagine è troppo grande", "DE.Views.FormSettings.textTooSmall": "L'immagine è troppo piccola", "DE.Views.FormSettings.textUnlock": "S<PERSON><PERSON>ca", "DE.Views.FormSettings.textValue": "Opzioni di valore", "DE.Views.FormSettings.textWidth": "‎Larg<PERSON>zza cella‎", "DE.Views.FormsTab.capBtnCheckBox": "Casella di controllo", "DE.Views.FormsTab.capBtnComboBox": "<PERSON>lla combinata", "DE.Views.FormsTab.capBtnComplex": "Campo complesso", "DE.Views.FormsTab.capBtnDownloadForm": "Scarica come oform", "DE.Views.FormsTab.capBtnDropDown": "Menù a discesca", "DE.Views.FormsTab.capBtnEmail": "Indirizzo email", "DE.Views.FormsTab.capBtnImage": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.FormsTab.capBtnNext": "Campo successivo", "DE.Views.FormsTab.capBtnPhone": "Numero di telefono", "DE.Views.FormsTab.capBtnPrev": "Campo <PERSON>e", "DE.Views.FormsTab.capBtnRadioBox": "Pulsante opzione", "DE.Views.FormsTab.capBtnSaveForm": "<PERSON><PERSON><PERSON> come oform", "DE.Views.FormsTab.capBtnSubmit": "‎Invia‎", "DE.Views.FormsTab.capBtnText": "‎Campo di testo‎", "DE.Views.FormsTab.capBtnView": "Visualizza modulo", "DE.Views.FormsTab.textClear": "Campi liberi", "DE.Views.FormsTab.textClearFields": "‎Cancella tutti i campi‎", "DE.Views.FormsTab.textCreateForm": "Aggiungi campi e crea un documento OFORM compilabile", "DE.Views.FormsTab.textGotIt": "Capito", "DE.Views.FormsTab.textHighlight": "Impostazioni evidenziazione", "DE.Views.FormsTab.textNoHighlight": "Nessuna evidenziazione", "DE.Views.FormsTab.textRequired": "Compila tutti i campi richiesti per inviare il modulo.", "DE.Views.FormsTab.textSubmited": "<PERSON><PERSON><PERSON> inviato con successo", "DE.Views.FormsTab.tipCheckBox": "Inser<PERSON>ci casella di controllo", "DE.Views.FormsTab.tipComboBox": "Inserisci una cella combinata", "DE.Views.FormsTab.tipComplexField": "Inserisci campo complesso", "DE.Views.FormsTab.tipDownloadForm": "Scaricare un file come un documento OFORM compilabile", "DE.Views.FormsTab.tipDropDown": "Inser<PERSON>ci lista in basso espandibile", "DE.Views.FormsTab.tipEmailField": " Inserisci indirizzo email", "DE.Views.FormsTab.tipImageField": "Inserisci immagine", "DE.Views.FormsTab.tipNextForm": "Vai al campo successivo", "DE.Views.FormsTab.tipPhoneField": "Inserisci numero di telefono", "DE.Views.FormsTab.tipPrevForm": "Vai al campo precedente", "DE.Views.FormsTab.tipRadioBox": "Inserisci pulsante di opzione", "DE.Views.FormsTab.tipSaveForm": "Salvare un file come documento OFORM compilabile", "DE.Views.FormsTab.tipSubmit": "Invia al modulo", "DE.Views.FormsTab.tipTextField": "Inserisci il campo di testo", "DE.Views.FormsTab.tipViewForm": "Visualizza modulo", "DE.Views.FormsTab.txtUntitled": "<PERSON>za titolo", "DE.Views.HeaderFooterSettings.textBottomCenter": "In basso al centro", "DE.Views.HeaderFooterSettings.textBottomLeft": "In basso a sinistra", "DE.Views.HeaderFooterSettings.textBottomPage": "Fondo pagina", "DE.Views.HeaderFooterSettings.textBottomRight": "In basso a destra", "DE.Views.HeaderFooterSettings.textDiffFirst": "Diversi per la prima pagina", "DE.Views.HeaderFooterSettings.textDiffOdd": "Diversi per pagine pari e dispari", "DE.Views.HeaderFooterSettings.textFrom": "Inizia da", "DE.Views.HeaderFooterSettings.textHeaderFromBottom": "<PERSON><PERSON> di pagina dal basso", "DE.Views.HeaderFooterSettings.textHeaderFromTop": "Intestazione all'inizio", "DE.Views.HeaderFooterSettings.textInsertCurrent": "Inserisci nella posizione attuale", "DE.Views.HeaderFooterSettings.textOptions": "Opzioni", "DE.Views.HeaderFooterSettings.textPageNum": "Inserisci numero di pagina", "DE.Views.HeaderFooterSettings.textPageNumbering": "Numerazione pagina", "DE.Views.HeaderFooterSettings.textPosition": "Posizione", "DE.Views.HeaderFooterSettings.textPrev": "Continua dalla selezione precedente", "DE.Views.HeaderFooterSettings.textSameAs": "Collega al <PERSON>e", "DE.Views.HeaderFooterSettings.textTopCenter": "In alto al centro", "DE.Views.HeaderFooterSettings.textTopLeft": "In alto a sinistra", "DE.Views.HeaderFooterSettings.textTopPage": "Inizio pagina", "DE.Views.HeaderFooterSettings.textTopRight": "In alto a destra", "DE.Views.HyperlinkSettingsDialog.textDefault": "Frammento di testo selezionato", "DE.Views.HyperlinkSettingsDialog.textDisplay": "Visualizza", "DE.Views.HyperlinkSettingsDialog.textExternal": "Collegamento esterno", "DE.Views.HyperlinkSettingsDialog.textInternal": "Inserisci nel documento", "DE.Views.HyperlinkSettingsDialog.textTitle": "Impostazioni collegamento ipertestuale", "DE.Views.HyperlinkSettingsDialog.textTooltip": "Testo del suggerimento", "DE.Views.HyperlinkSettingsDialog.textUrl": "Collega a", "DE.Views.HyperlinkSettingsDialog.txtBeginning": "Inizio del documento", "DE.Views.HyperlinkSettingsDialog.txtBookmarks": "Se<PERSON><PERSON><PERSON>", "DE.Views.HyperlinkSettingsDialog.txtEmpty": "Campo obbligatorio", "DE.Views.HyperlinkSettingsDialog.txtHeadings": "Intestazioni", "DE.Views.HyperlinkSettingsDialog.txtNotUrl": "Il formato URL richiesto è \"http://www.example.com\"", "DE.Views.HyperlinkSettingsDialog.txtSizeLimit": "Questo campo è limitato a 2083 caratteri", "DE.Views.ImageSettings.textAdvanced": "Mostra impostazioni avanzate", "DE.Views.ImageSettings.textCrop": "Rita<PERSON>", "DE.Views.ImageSettings.textCropFill": "Riempimento", "DE.Views.ImageSettings.textCropFit": "Adatta", "DE.Views.ImageSettings.textCropToShape": "Ritaglia forma", "DE.Views.ImageSettings.textEdit": "Modifica", "DE.Views.ImageSettings.textEditObject": "Modifica oggetto", "DE.Views.ImageSettings.textFitMargins": "Adatta al margine", "DE.Views.ImageSettings.textFlip": "Capovolgere", "DE.Views.ImageSettings.textFromFile": "Da file", "DE.Views.ImageSettings.textFromStorage": "Da spazio di archiviazione", "DE.Views.ImageSettings.textFromUrl": "Da URL", "DE.Views.ImageSettings.textHeight": "Altezza", "DE.Views.ImageSettings.textHint270": "Ruota di 90 ° in senso antiorario", "DE.Views.ImageSettings.textHint90": "Ruota di 90 ° in senso orario", "DE.Views.ImageSettings.textHintFlipH": "Capovolgi orizzontalmente", "DE.Views.ImageSettings.textHintFlipV": "Capovolgi verticalmente", "DE.Views.ImageSettings.textInsert": "Sostituisci immagine", "DE.Views.ImageSettings.textOriginalSize": "Dimensione reale", "DE.Views.ImageSettings.textRecentlyUsed": "Usati di recente", "DE.Views.ImageSettings.textRotate90": "Ruota di 90°", "DE.Views.ImageSettings.textRotation": "Rotazione", "DE.Views.ImageSettings.textSize": "Dimensione", "DE.Views.ImageSettings.textWidth": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ImageSettings.textWrap": "Stile di disposizione testo", "DE.Views.ImageSettings.txtBehind": "<PERSON><PERSON> al testo", "DE.Views.ImageSettings.txtInFront": "<PERSON><PERSON><PERSON> al testo", "DE.Views.ImageSettings.txtInline": "In linea con il testo", "DE.Views.ImageSettings.txtSquare": "Quadrato", "DE.Views.ImageSettings.txtThrough": "All'interno", "DE.Views.ImageSettings.txtTight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.ImageSettings.txtTopAndBottom": "Sopra e sotto", "DE.Views.ImageSettingsAdvanced.strMargins": "Spaziatura interna", "DE.Views.ImageSettingsAdvanced.textAbsoluteWH": "Assoluto", "DE.Views.ImageSettingsAdvanced.textAlignment": "Allineamento", "DE.Views.ImageSettingsAdvanced.textAlt": "Testo alternativo", "DE.Views.ImageSettingsAdvanced.textAltDescription": "Descrizione", "DE.Views.ImageSettingsAdvanced.textAltTip": "La rappresentazione alternativa testuale basata delle informazioni sull'oggetto visivo, che verrà letta alle persone con disabilità visive o cognitive per aiutarle a capire meglio quali informazioni ci sono nell'immagine, nella forma automatica, nel grafico o nella tabella.", "DE.Views.ImageSettingsAdvanced.textAltTitle": "<PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textAngle": "<PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textArrows": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textAspectRatio": "Blocca proporzioni", "DE.Views.ImageSettingsAdvanced.textAutofit": "Adatta", "DE.Views.ImageSettingsAdvanced.textBeginSize": "Dimensioni inizio", "DE.Views.ImageSettingsAdvanced.textBeginStyle": "<PERSON><PERSON> inizio", "DE.Views.ImageSettingsAdvanced.textBelow": "al di sotto", "DE.Views.ImageSettingsAdvanced.textBevel": "Smussat<PERSON>", "DE.Views.ImageSettingsAdvanced.textBottom": "In basso", "DE.Views.ImageSettingsAdvanced.textBottomMargin": "Margine inferiore", "DE.Views.ImageSettingsAdvanced.textBtnWrap": "Disposizione testo", "DE.Views.ImageSettingsAdvanced.textCapType": "Tipo estremità", "DE.Views.ImageSettingsAdvanced.textCenter": "Al centro", "DE.Views.ImageSettingsAdvanced.textCharacter": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textColumn": "<PERSON>onna", "DE.Views.ImageSettingsAdvanced.textDistance": "<PERSON><PERSON><PERSON> dal testo", "DE.Views.ImageSettingsAdvanced.textEndSize": "Dimensione finale", "DE.Views.ImageSettingsAdvanced.textEndStyle": "Stile finale", "DE.Views.ImageSettingsAdvanced.textFlat": "Uniforme", "DE.Views.ImageSettingsAdvanced.textFlipped": "Capovolto", "DE.Views.ImageSettingsAdvanced.textHeight": "Altezza", "DE.Views.ImageSettingsAdvanced.textHorizontal": "Orizzontale", "DE.Views.ImageSettingsAdvanced.textHorizontally": "Orizzontalmente", "DE.Views.ImageSettingsAdvanced.textJoinType": "Tipo giunzione", "DE.Views.ImageSettingsAdvanced.textKeepRatio": "Proporzioni costanti", "DE.Views.ImageSettingsAdvanced.textLeft": "A sinistra", "DE.Views.ImageSettingsAdvanced.textLeftMargin": "<PERSON><PERSON>e sinistro", "DE.Views.ImageSettingsAdvanced.textLine": "Linea", "DE.Views.ImageSettingsAdvanced.textLineStyle": "Stile linea", "DE.Views.ImageSettingsAdvanced.textMargin": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textMiter": "Acuto", "DE.Views.ImageSettingsAdvanced.textMove": "Sposta oggetto con testo", "DE.Views.ImageSettingsAdvanced.textOptions": "Opzioni", "DE.Views.ImageSettingsAdvanced.textOriginalSize": "Dimensione reale", "DE.Views.ImageSettingsAdvanced.textOverlap": "Consenti sovrapposizione", "DE.Views.ImageSettingsAdvanced.textPage": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textParagraph": "Paragrafo", "DE.Views.ImageSettingsAdvanced.textPosition": "Posizione", "DE.Views.ImageSettingsAdvanced.textPositionPc": "Posizione relativa", "DE.Views.ImageSettingsAdvanced.textRelative": "rispetto a", "DE.Views.ImageSettingsAdvanced.textRelativeWH": "Relativo", "DE.Views.ImageSettingsAdvanced.textResizeFit": "Ridimensiona forma per adattarla al testo", "DE.Views.ImageSettingsAdvanced.textRight": "A destra", "DE.Views.ImageSettingsAdvanced.textRightMargin": "<PERSON><PERSON><PERSON> destro", "DE.Views.ImageSettingsAdvanced.textRightOf": "a destra di", "DE.Views.ImageSettingsAdvanced.textRotation": "Rotazione", "DE.Views.ImageSettingsAdvanced.textRound": "Rotondo", "DE.Views.ImageSettingsAdvanced.textShape": "Impostazioni forma", "DE.Views.ImageSettingsAdvanced.textSize": "Dimensione", "DE.Views.ImageSettingsAdvanced.textSquare": "Quadrato", "DE.Views.ImageSettingsAdvanced.textTextBox": "Casella di testo", "DE.Views.ImageSettingsAdvanced.textTitle": "Immagine - Impostazioni avanzate", "DE.Views.ImageSettingsAdvanced.textTitleChart": "Grafico - Impostazioni avanzate", "DE.Views.ImageSettingsAdvanced.textTitleShape": "Forma - Impostazioni avanzate", "DE.Views.ImageSettingsAdvanced.textTop": "In alto", "DE.Views.ImageSettingsAdvanced.textTopMargin": "Margine superiore", "DE.Views.ImageSettingsAdvanced.textVertical": "Verticale", "DE.Views.ImageSettingsAdvanced.textVertically": "Verticalmente", "DE.Views.ImageSettingsAdvanced.textWeightArrows": "Spessori e frecce", "DE.Views.ImageSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textWrap": "Stile di disposizione testo", "DE.Views.ImageSettingsAdvanced.textWrapBehindTooltip": "<PERSON><PERSON> al testo", "DE.Views.ImageSettingsAdvanced.textWrapInFrontTooltip": "<PERSON><PERSON><PERSON> al testo", "DE.Views.ImageSettingsAdvanced.textWrapInlineTooltip": "In linea con il testo", "DE.Views.ImageSettingsAdvanced.textWrapSquareTooltip": "Quadrato", "DE.Views.ImageSettingsAdvanced.textWrapThroughTooltip": "All'interno", "DE.Views.ImageSettingsAdvanced.textWrapTightTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textWrapTopbottomTooltip": "Sopra e sotto", "DE.Views.LeftMenu.tipAbout": "Informazioni su", "DE.Views.LeftMenu.tipChat": "Cha<PERSON>", "DE.Views.LeftMenu.tipComments": "Commenti", "DE.Views.LeftMenu.tipNavigation": "Navigazione", "DE.Views.LeftMenu.tipOutline": "Intestazioni", "DE.Views.LeftMenu.tipPageThumbnails": "Miniature delle pagine", "DE.Views.LeftMenu.tipPlugins": "Plugin", "DE.Views.LeftMenu.tipSearch": "Ricerca", "DE.Views.LeftMenu.tipSupport": "Feedback & Supporto", "DE.Views.LeftMenu.tipTitles": "<PERSON><PERSON>", "DE.Views.LeftMenu.txtDeveloper": "MODALITÀ SVILUPPATORE", "DE.Views.LeftMenu.txtEditor": "Document Editor", "DE.Views.LeftMenu.txtLimit": "‎<PERSON><PERSON>‎", "DE.Views.LeftMenu.txtTrial": "Modalità di prova", "DE.Views.LeftMenu.txtTrialDev": "Prova Modalità sviluppatore", "DE.Views.LineNumbersDialog.textAddLineNumbering": "Aggiungi la numerazione delle righe", "DE.Views.LineNumbersDialog.textApplyTo": "Applica le modifiche a ", "DE.Views.LineNumbersDialog.textContinuous": "Continua", "DE.Views.LineNumbersDialog.textCountBy": "‎Conta da", "DE.Views.LineNumbersDialog.textDocument": "<PERSON>tto il documento", "DE.Views.LineNumbersDialog.textForward": "Dal punto in avanti", "DE.Views.LineNumbersDialog.textFromText": "<PERSON>o", "DE.Views.LineNumbersDialog.textNumbering": "<PERSON><PERSON><PERSON> numerati", "DE.Views.LineNumbersDialog.textRestartEachPage": "Ricomincia a ogni pagina", "DE.Views.LineNumbersDialog.textRestartEachSection": "Ricomincia a ogni sezione", "DE.Views.LineNumbersDialog.textSection": "Sezione attiva", "DE.Views.LineNumbersDialog.textStartAt": "Inizia da", "DE.Views.LineNumbersDialog.textTitle": "Linea con numeri", "DE.Views.LineNumbersDialog.txtAutoText": "Auto", "DE.Views.Links.capBtnAddText": "Aggiu<PERSON>i testo", "DE.Views.Links.capBtnBookmarks": "Se<PERSON><PERSON><PERSON>", "DE.Views.Links.capBtnCaption": "Didascalia", "DE.Views.Links.capBtnContentsUpdate": "Aggiorna tabella", "DE.Views.Links.capBtnCrossRef": "‎Riferimento incrociato‎", "DE.Views.Links.capBtnInsContents": "<PERSON><PERSON><PERSON>", "DE.Views.Links.capBtnInsFootnote": "Nota a piè di pagina", "DE.Views.Links.capBtnInsLink": "Collegamento ipertestuale", "DE.Views.Links.capBtnTOF": "‎Indice delle figure‎", "DE.Views.Links.confirmDeleteFootnotes": "Vuoi eliminare tutte le note a piè di pagina?", "DE.Views.Links.confirmReplaceTOF": "Vuoi sostituire la tabella delle figure selezionata?", "DE.Views.Links.mniConvertNote": "<PERSON><PERSON><PERSON> tutte le Note", "DE.Views.Links.mniDelFootnote": "Elimina tutte le note a piè di pagina", "DE.Views.Links.mniInsEndnote": "Inserisci nota di chiusura", "DE.Views.Links.mniInsFootnote": "Inserisci nota a piè di pagina", "DE.Views.Links.mniNoteSettings": "Impostazioni delle note", "DE.Views.Links.textContentsRemove": "<PERSON><PERSON>", "DE.Views.Links.textContentsSettings": "Impostazioni", "DE.Views.Links.textConvertToEndnotes": "Converti tutte le note a piè di pagina in note di chiusura", "DE.Views.Links.textConvertToFootnotes": "Converti tutte le note di chiusura in note a piè di pagina", "DE.Views.Links.textGotoEndnote": "Vai alle note di chiusura", "DE.Views.Links.textGotoFootnote": "Passa alle note a piè di pagina", "DE.Views.Links.textSwapNotes": "‎Scambia note a piè di pagina e di chiusura‎", "DE.Views.Links.textUpdateAll": "Aggiorna intera tabella", "DE.Views.Links.textUpdatePages": "Aggiorna solo numeri di pagina", "DE.Views.Links.tipAddText": "Includere l'intestazione nell'indice", "DE.Views.Links.tipBookmarks": "Crea un segnalibro", "DE.Views.Links.tipCaption": "Inser<PERSON>ci <PERSON>", "DE.Views.Links.tipContents": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Links.tipContentsUpdate": "Aggiorna sommario", "DE.Views.Links.tipCrossRef": "Inserisci riferimento incrociato", "DE.Views.Links.tipInsertHyperlink": "Aggiungi collegamento ipertestuale", "DE.Views.Links.tipNotes": "Inserisci o modifica Note a piè di pagina", "DE.Views.Links.tipTableFigures": "In<PERSON><PERSON><PERSON> la tabella delle figure", "DE.Views.Links.tipTableFiguresUpdate": "‎Aggiorna indice delle figure‎", "DE.Views.Links.titleUpdateTOF": "‎Aggiorna indice delle figure‎", "DE.Views.Links.txtDontShowTof": "Non mostrare nell'indice", "DE.Views.Links.txtLevel": "<PERSON><PERSON>", "DE.Views.ListSettingsDialog.textAuto": "Automatico", "DE.Views.ListSettingsDialog.textCenter": "Al centro", "DE.Views.ListSettingsDialog.textLeft": "A sinistra", "DE.Views.ListSettingsDialog.textLevel": "<PERSON><PERSON>", "DE.Views.ListSettingsDialog.textPreview": "Anteprima", "DE.Views.ListSettingsDialog.textRight": "A destra", "DE.Views.ListSettingsDialog.txtAlign": "Allineamento", "DE.Views.ListSettingsDialog.txtBullet": "Elenco puntato", "DE.Views.ListSettingsDialog.txtColor": "Colore", "DE.Views.ListSettingsDialog.txtFont": "Carattere e simbolo.", "DE.Views.ListSettingsDialog.txtLikeText": "Come un testo", "DE.Views.ListSettingsDialog.txtNewBullet": "Nuovo elenco puntato", "DE.Views.ListSettingsDialog.txtNone": "<PERSON><PERSON><PERSON>", "DE.Views.ListSettingsDialog.txtSize": "Dimensioni", "DE.Views.ListSettingsDialog.txtSymbol": "Simbolo", "DE.Views.ListSettingsDialog.txtTitle": "Impostazioni elenco", "DE.Views.ListSettingsDialog.txtType": "Tipo", "DE.Views.MailMergeEmailDlg.filePlaceholder": "PDF", "DE.Views.MailMergeEmailDlg.okButtonText": "Invia", "DE.Views.MailMergeEmailDlg.subjectPlaceholder": "<PERSON><PERSON>", "DE.Views.MailMergeEmailDlg.textAttachDocx": "Allega come DOCX", "DE.Views.MailMergeEmailDlg.textAttachPdf": "Allega come PDF", "DE.Views.MailMergeEmailDlg.textFileName": "Nome del file", "DE.Views.MailMergeEmailDlg.textFormat": "Formato mail", "DE.Views.MailMergeEmailDlg.textFrom": "Da", "DE.Views.MailMergeEmailDlg.textHTML": "HTML", "DE.Views.MailMergeEmailDlg.textMessage": "Messaggio", "DE.Views.MailMergeEmailDlg.textSubject": "Linea soggetto", "DE.Views.MailMergeEmailDlg.textTitle": "Invia a e-mail", "DE.Views.MailMergeEmailDlg.textTo": "A", "DE.Views.MailMergeEmailDlg.textWarning": "Avviso!", "DE.Views.MailMergeEmailDlg.textWarningMsg": "Si prega di notare che l'invio non può essere interrotto una volta che si fa clic sul pulsante 'Invia'.", "DE.Views.MailMergeSettings.downloadMergeTitle": "Unione", "DE.Views.MailMergeSettings.errorMailMergeSaveFile": "Unione non riuscita", "DE.Views.MailMergeSettings.notcriticalErrorTitle": "Avviso", "DE.Views.MailMergeSettings.textAddRecipients": "Per prima cosa aggiungi dei destinatari alla lista", "DE.Views.MailMergeSettings.textAll": "Tutti le registrazioni", "DE.Views.MailMergeSettings.textCurrent": "Registrazione corrente", "DE.Views.MailMergeSettings.textDataSource": "Origine dati", "DE.Views.MailMergeSettings.textDocx": "Docx", "DE.Views.MailMergeSettings.textDownload": "Scarica", "DE.Views.MailMergeSettings.textEditData": "Modificare la lista dei destinatari", "DE.Views.MailMergeSettings.textEmail": "Email", "DE.Views.MailMergeSettings.textFrom": "Da", "DE.Views.MailMergeSettings.textGoToMail": "<PERSON><PERSON>' all<PERSON>", "DE.Views.MailMergeSettings.textHighlight": "Evidenziare i campi unione", "DE.Views.MailMergeSettings.textInsertField": "Inserisci campo unione", "DE.Views.MailMergeSettings.textMaxRecepients": "Massimo 100 destinatari.", "DE.Views.MailMergeSettings.textMerge": "Unisci", "DE.Views.MailMergeSettings.textMergeFields": "Unisci campi", "DE.Views.MailMergeSettings.textMergeTo": "Unisci a", "DE.Views.MailMergeSettings.textPdf": "PDF", "DE.Views.MailMergeSettings.textPortal": "<PERSON><PERSON>", "DE.Views.MailMergeSettings.textPreview": "Anteprima dei risultati", "DE.Views.MailMergeSettings.textReadMore": "Ulteriori informazioni", "DE.Views.MailMergeSettings.textSendMsg": "Tutti i messaggi di posta elettronica sono pronti e saranno inviati in poco tempo.<br>La velocità di invio dipende dal tuo servizio mail.<br>Puoi continuare a lavorare con il documento o chiuderlo. Al termine dell'operazione, ti verrà recapitata una notifica all'indirizzo email di registrazione.", "DE.Views.MailMergeSettings.textTo": "A", "DE.Views.MailMergeSettings.txtFirst": "Al primo record", "DE.Views.MailMergeSettings.txtFromToError": "Il valore \"Da\" deve essere minore del valore \"A\"", "DE.Views.MailMergeSettings.txtLast": "All'ultimo record", "DE.Views.MailMergeSettings.txtNext": "Al record successivo", "DE.Views.MailMergeSettings.txtPrev": "al record precedente", "DE.Views.MailMergeSettings.txtUntitled": "<PERSON>za titolo", "DE.Views.MailMergeSettings.warnProcessMailMerge": "Avvio unione non riuscito", "DE.Views.Navigation.strNavigate": "Intestazioni", "DE.Views.Navigation.txtClosePanel": "<PERSON><PERSON> intestazioni", "DE.Views.Navigation.txtCollapse": "Co<PERSON><PERSON><PERSON> tutto", "DE.Views.Navigation.txtDemote": "Retrocedere", "DE.Views.Navigation.txtEmpty": "Non ci sono titoli nel documento.<br>Applica uno stile di titolo al testo in modo che appaia nel sommario.", "DE.Views.Navigation.txtEmptyItem": "Intestazione vuota", "DE.Views.Navigation.txtEmptyViewer": "Non ci sono titoli nel documento.", "DE.Views.Navigation.txtExpand": "<PERSON><PERSON><PERSON> tutto", "DE.Views.Navigation.txtExpandToLevel": "<PERSON><PERSON><PERSON> al <PERSON>llo", "DE.Views.Navigation.txtFontSize": "Dimensione carattere", "DE.Views.Navigation.txtHeadingAfter": "Nuova intestazione dopo", "DE.Views.Navigation.txtHeadingBefore": "Nuova intestazione prima", "DE.Views.Navigation.txtLarge": "Grande", "DE.Views.Navigation.txtMedium": "Medio", "DE.Views.Navigation.txtNewHeading": "<PERSON>uovo sottoti<PERSON>lo", "DE.Views.Navigation.txtPromote": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Navigation.txtSelect": "<PERSON><PERSON><PERSON> contenuto", "DE.Views.Navigation.txtSettings": "Impostazioni di intestazione", "DE.Views.Navigation.txtSmall": "<PERSON><PERSON><PERSON>", "DE.Views.Navigation.txtWrapHeadings": "Adattare intestazioni lunghe", "DE.Views.NoteSettingsDialog.textApply": "Applica", "DE.Views.NoteSettingsDialog.textApplyTo": "Applica modifiche a", "DE.Views.NoteSettingsDialog.textContinue": "Continua", "DE.Views.NoteSettingsDialog.textCustom": "<PERSON><PERSON><PERSON>", "DE.Views.NoteSettingsDialog.textDocEnd": "‎Fine del documento‎", "DE.Views.NoteSettingsDialog.textDocument": "<PERSON>tto il documento", "DE.Views.NoteSettingsDialog.textEachPage": "Ricomincia a ogni pagina", "DE.Views.NoteSettingsDialog.textEachSection": "Ricomincia a ogni sezione", "DE.Views.NoteSettingsDialog.textEndnote": "Nota di chiusura", "DE.Views.NoteSettingsDialog.textFootnote": "Nota a piè di pagina", "DE.Views.NoteSettingsDialog.textFormat": "Formato", "DE.Views.NoteSettingsDialog.textInsert": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.NoteSettingsDialog.textLocation": "<PERSON><PERSON><PERSON>", "DE.Views.NoteSettingsDialog.textNumbering": "Numerazione", "DE.Views.NoteSettingsDialog.textNumFormat": "Formato numero", "DE.Views.NoteSettingsDialog.textPageBottom": "Fondo pagina", "DE.Views.NoteSettingsDialog.textSectEnd": "‎Fine sezione‎", "DE.Views.NoteSettingsDialog.textSection": "Sezione attiva", "DE.Views.NoteSettingsDialog.textStart": "Inizia da", "DE.Views.NoteSettingsDialog.textTextBottom": "Sotto al testo", "DE.Views.NoteSettingsDialog.textTitle": "Impostazioni delle note", "DE.Views.NotesRemoveDialog.textEnd": "Elimina tutte le note di chiusura", "DE.Views.NotesRemoveDialog.textFoot": "Elimina tutte le note a piè di pagina", "DE.Views.NotesRemoveDialog.textTitle": "Elimina Note", "DE.Views.PageMarginsDialog.notcriticalErrorTitle": "Avviso", "DE.Views.PageMarginsDialog.textBottom": "In basso", "DE.Views.PageMarginsDialog.textGutter": "<PERSON><PERSON>e interno", "DE.Views.PageMarginsDialog.textGutterPosition": "Posizione margine interno", "DE.Views.PageMarginsDialog.textInside": "<PERSON><PERSON>", "DE.Views.PageMarginsDialog.textLandscape": "Orizzontale", "DE.Views.PageMarginsDialog.textLeft": "A sinistra", "DE.Views.PageMarginsDialog.textMirrorMargins": "<PERSON><PERSON><PERSON> speculari", "DE.Views.PageMarginsDialog.textMultiplePages": "<PERSON><PERSON> pagine", "DE.Views.PageMarginsDialog.textNormal": "Normale", "DE.Views.PageMarginsDialog.textOrientation": "Orientamento", "DE.Views.PageMarginsDialog.textOutside": "<PERSON><PERSON>", "DE.Views.PageMarginsDialog.textPortrait": "Verticale", "DE.Views.PageMarginsDialog.textPreview": "Anteprima", "DE.Views.PageMarginsDialog.textRight": "A destra", "DE.Views.PageMarginsDialog.textTitle": "<PERSON><PERSON><PERSON>", "DE.Views.PageMarginsDialog.textTop": "In alto", "DE.Views.PageMarginsDialog.txtMarginsH": "I margini superiore e inferiore sono troppo alti per una determinata altezza di pagina", "DE.Views.PageMarginsDialog.txtMarginsW": "I margini sinistro e destro sono troppo larghi per una determinata larghezza di pagina", "DE.Views.PageSizeDialog.textHeight": "Altezza", "DE.Views.PageSizeDialog.textPreset": "Preimpostazione", "DE.Views.PageSizeDialog.textTitle": "Dimensione pagina", "DE.Views.PageSizeDialog.textWidth": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.PageSizeDialog.txtCustom": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.PageThumbnails.textClosePanel": "<PERSON>udi anteprima pagina", "DE.Views.PageThumbnails.textHighlightVisiblePart": "Evidenziare la parte visibile della pagina", "DE.Views.PageThumbnails.textPageThumbnails": "Miniature delle pagine", "DE.Views.PageThumbnails.textThumbnailsSettings": "Impostazioni anteprime", "DE.Views.PageThumbnails.textThumbnailsSize": "Dimenzione anteprime", "DE.Views.ParagraphSettings.strIndent": "R<PERSON>ri", "DE.Views.ParagraphSettings.strIndentsLeftText": "A sinistra", "DE.Views.ParagraphSettings.strIndentsRightText": "A destra", "DE.Views.ParagraphSettings.strIndentsSpecial": "Speciale", "DE.Views.ParagraphSettings.strLineHeight": "Interlinea", "DE.Views.ParagraphSettings.strParagraphSpacing": "Spaziatura del paragrafo", "DE.Views.ParagraphSettings.strSomeParagraphSpace": "Non aggiungere intervallo tra paragrafi dello stesso stile", "DE.Views.ParagraphSettings.strSpacingAfter": "<PERSON><PERSON>", "DE.Views.ParagraphSettings.strSpacingBefore": "Prima", "DE.Views.ParagraphSettings.textAdvanced": "Mostra impostazioni avanzate", "DE.Views.ParagraphSettings.textAt": "A", "DE.Views.ParagraphSettings.textAtLeast": "<PERSON><PERSON>", "DE.Views.ParagraphSettings.textAuto": "Multipla", "DE.Views.ParagraphSettings.textBackColor": "Colore sfondo", "DE.Views.ParagraphSettings.textExact": "Esatto", "DE.Views.ParagraphSettings.textFirstLine": "Prima riga", "DE.Views.ParagraphSettings.textHanging": "In sospensione", "DE.Views.ParagraphSettings.textNoneSpecial": "(nessuna)", "DE.Views.ParagraphSettings.txtAutoText": "Auto", "DE.Views.ParagraphSettingsAdvanced.noTabs": "Le schede specificate appariranno in questo campo", "DE.Views.ParagraphSettingsAdvanced.strAllCaps": "<PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.strBorders": "Bordi e riempimento", "DE.Views.ParagraphSettingsAdvanced.strBreakBefore": "Anteponi Interruzione di pagina", "DE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "Barrato do<PERSON>", "DE.Views.ParagraphSettingsAdvanced.strIndent": "R<PERSON>ri", "DE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "A sinistra", "DE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Interlinea", "DE.Views.ParagraphSettingsAdvanced.strIndentsOutlinelevel": "<PERSON><PERSON> di struttura", "DE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "A destra", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "<PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "Prima", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Speciale", "DE.Views.ParagraphSettingsAdvanced.strKeepLines": "<PERSON><PERSON><PERSON> assieme le righe", "DE.Views.ParagraphSettingsAdvanced.strKeepNext": "Mantieni con il successivo", "DE.Views.ParagraphSettingsAdvanced.strMargins": "Spaziatura interna", "DE.Views.ParagraphSettingsAdvanced.strOrphan": "Controllo righe isolate", "DE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Tipo di carattere", "DE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Rientri e spaziatura", "DE.Views.ParagraphSettingsAdvanced.strParagraphLine": "Interruzioni di riga e di pagina", "DE.Views.ParagraphSettingsAdvanced.strParagraphPosition": "Posizionamento", "DE.Views.ParagraphSettingsAdvanced.strSmallCaps": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.strSomeParagraphSpace": "Non aggiungere intervallo tra paragrafi dello stesso stile", "DE.Views.ParagraphSettingsAdvanced.strSpacing": "Spaziatura", "DE.Views.ParagraphSettingsAdvanced.strStrike": "Barrato", "DE.Views.ParagraphSettingsAdvanced.strSubscript": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.strSuperscript": "Apice", "DE.Views.ParagraphSettingsAdvanced.strSuppressLineNumbers": "‎Sopprimi i numeri di riga‎", "DE.Views.ParagraphSettingsAdvanced.strTabs": "Tabulazione", "DE.Views.ParagraphSettingsAdvanced.textAlign": "Allineamento", "DE.Views.ParagraphSettingsAdvanced.textAll": "<PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textAtLeast": "<PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textAuto": "Multiplo", "DE.Views.ParagraphSettingsAdvanced.textBackColor": "Colore sfondo", "DE.Views.ParagraphSettingsAdvanced.textBodyText": "Corpo del testo", "DE.Views.ParagraphSettingsAdvanced.textBorderColor": "Colore bordo", "DE.Views.ParagraphSettingsAdvanced.textBorderDesc": "Clicca sul diagramma o utilizza i pulsanti per selezionare i bordi e applicare lo stile selezionato ad essi", "DE.Views.ParagraphSettingsAdvanced.textBorderWidth": "Dimensioni bordo", "DE.Views.ParagraphSettingsAdvanced.textBottom": "In basso", "DE.Views.ParagraphSettingsAdvanced.textCentered": "Centrato", "DE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Spaziatura caratteri", "DE.Views.ParagraphSettingsAdvanced.textContext": "Contestuali", "DE.Views.ParagraphSettingsAdvanced.textContextDiscret": "Contestuali e discrezionali", "DE.Views.ParagraphSettingsAdvanced.textContextHistDiscret": "Contestuali, storici e discrezionali", "DE.Views.ParagraphSettingsAdvanced.textContextHistorical": "Contestuali e storici", "DE.Views.ParagraphSettingsAdvanced.textDefault": "Scheda predefinita", "DE.Views.ParagraphSettingsAdvanced.textDiscret": "Discrezionali", "DE.Views.ParagraphSettingsAdvanced.textEffects": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textExact": "Esatto", "DE.Views.ParagraphSettingsAdvanced.textFirstLine": "Prima riga", "DE.Views.ParagraphSettingsAdvanced.textHanging": "Sospensione", "DE.Views.ParagraphSettingsAdvanced.textHistorical": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textHistoricalDiscret": "Storici e discrezionali", "DE.Views.ParagraphSettingsAdvanced.textJustified": "Giustificato", "DE.Views.ParagraphSettingsAdvanced.textLeader": "Leader", "DE.Views.ParagraphSettingsAdvanced.textLeft": "A sinistra", "DE.Views.ParagraphSettingsAdvanced.textLevel": "<PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textLigatures": "Legature", "DE.Views.ParagraphSettingsAdvanced.textNone": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(nessuna)", "DE.Views.ParagraphSettingsAdvanced.textOpenType": "Caratteristiche OpenType", "DE.Views.ParagraphSettingsAdvanced.textPosition": "Posizione", "DE.Views.ParagraphSettingsAdvanced.textRemove": "Elimina", "DE.Views.ParagraphSettingsAdvanced.textRemoveAll": "<PERSON><PERSON> tutto", "DE.Views.ParagraphSettingsAdvanced.textRight": "A destra", "DE.Views.ParagraphSettingsAdvanced.textSet": "Specifica", "DE.Views.ParagraphSettingsAdvanced.textSpacing": "Spaziatura", "DE.Views.ParagraphSettingsAdvanced.textStandard": "Solo standard", "DE.Views.ParagraphSettingsAdvanced.textStandardContext": "Standard e contestuali", "DE.Views.ParagraphSettingsAdvanced.textStandardContextDiscret": "Standard, contestuali e discrezionali", "DE.Views.ParagraphSettingsAdvanced.textStandardContextHist": "Standard, contestuali e discrezionali", "DE.Views.ParagraphSettingsAdvanced.textStandardDiscret": "Standard e discrezionali", "DE.Views.ParagraphSettingsAdvanced.textStandardHistDiscret": "Standard, storici e discrezionali", "DE.Views.ParagraphSettingsAdvanced.textStandardHistorical": "Standard e storici", "DE.Views.ParagraphSettingsAdvanced.textTabCenter": "Al centro", "DE.Views.ParagraphSettingsAdvanced.textTabLeft": "A sinistra", "DE.Views.ParagraphSettingsAdvanced.textTabPosition": "Posizione della scheda", "DE.Views.ParagraphSettingsAdvanced.textTabRight": "A destra", "DE.Views.ParagraphSettingsAdvanced.textTitle": "Paragrafo - Impostazioni avanzate", "DE.Views.ParagraphSettingsAdvanced.textTop": "In alto", "DE.Views.ParagraphSettingsAdvanced.tipAll": "Imposta bordo esterno e tutte le linee interne", "DE.Views.ParagraphSettingsAdvanced.tipBottom": "Imposta solo bordo inferiore", "DE.Views.ParagraphSettingsAdvanced.tipInner": "Imposta solo linee interne orizzontali", "DE.Views.ParagraphSettingsAdvanced.tipLeft": "<PERSON>mpo<PERSON> solo bordo sinistro", "DE.Views.ParagraphSettingsAdvanced.tipNone": "Non impostare bordi", "DE.Views.ParagraphSettingsAdvanced.tipOuter": "<PERSON>mpo<PERSON> solo bordi esterni", "DE.Views.ParagraphSettingsAdvanced.tipRight": "<PERSON><PERSON><PERSON> solo bordo destro", "DE.Views.ParagraphSettingsAdvanced.tipTop": "Imposta solo bordo superiore", "DE.Views.ParagraphSettingsAdvanced.txtAutoText": "Auto", "DE.Views.ParagraphSettingsAdvanced.txtNoBorders": "<PERSON><PERSON><PERSON> bordo", "DE.Views.ProtectDialog.textComments": "Commenti", "DE.Views.ProtectDialog.txtIncorrectPwd": "La password di conferma non corrisponde", "DE.Views.ProtectDialog.txtOptional": "opzionale", "DE.Views.ProtectDialog.txtPassword": "Password", "DE.Views.ProtectDialog.txtProtect": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ProtectDialog.txtRepeat": "<PERSON><PERSON><PERSON> la password", "DE.Views.ProtectDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ProtectDialog.txtWarning": "Importante: una volta persa o dimenticata, la password non potrà più essere recuperata. Conservalo in un luogo sicuro.", "DE.Views.RightMenu.txtChartSettings": "Impostazioni grafico", "DE.Views.RightMenu.txtFormSettings": "‎Impostazioni modulo‎", "DE.Views.RightMenu.txtHeaderFooterSettings": "Impostazioni intestazione e piè di pagina", "DE.Views.RightMenu.txtImageSettings": "Impostazioni immagine", "DE.Views.RightMenu.txtMailMergeSettings": "Impostazioni Stampa unione", "DE.Views.RightMenu.txtParagraphSettings": "Impostazioni paragrafo", "DE.Views.RightMenu.txtShapeSettings": "Impostazioni forma", "DE.Views.RightMenu.txtSignatureSettings": "Impostazioni della Firma", "DE.Views.RightMenu.txtTableSettings": "Impostazioni tabella", "DE.Views.RightMenu.txtTextArtSettings": "Impostazioni Text Art", "DE.Views.ShapeSettings.strBackground": "Colore sfondo", "DE.Views.ShapeSettings.strChange": "Modifica forma automatica", "DE.Views.ShapeSettings.strColor": "Colore", "DE.Views.ShapeSettings.strFill": "Riempimento", "DE.Views.ShapeSettings.strForeground": "Colore primo piano", "DE.Views.ShapeSettings.strPattern": "<PERSON><PERSON>", "DE.Views.ShapeSettings.strShadow": "Mostra ombra", "DE.Views.ShapeSettings.strSize": "Dimensione", "DE.Views.ShapeSettings.strStroke": "Linea", "DE.Views.ShapeSettings.strTransparency": "Opacità", "DE.Views.ShapeSettings.strType": "Tipo", "DE.Views.ShapeSettings.textAdvanced": "Mostra impostazioni avanzate", "DE.Views.ShapeSettings.textAngle": "<PERSON><PERSON>", "DE.Views.ShapeSettings.textBorderSizeErr": "Il valore inserito non è corretto.<br>Inserisci un valore tra 0 pt e 1584 pt.", "DE.Views.ShapeSettings.textColor": "Colore di riempimento", "DE.Views.ShapeSettings.textDirection": "Direzione", "DE.Views.ShapeSettings.textEmptyPattern": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.textFlip": "Capovolgere", "DE.Views.ShapeSettings.textFromFile": "Da file", "DE.Views.ShapeSettings.textFromStorage": "Da spazio di archiviazione", "DE.Views.ShapeSettings.textFromUrl": "Da URL", "DE.Views.ShapeSettings.textGradient": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.textGradientFill": "Riempimento sfumato", "DE.Views.ShapeSettings.textHint270": "Ruota di 90 ° in senso antiorario", "DE.Views.ShapeSettings.textHint90": "Ruota di 90 ° in senso orario", "DE.Views.ShapeSettings.textHintFlipH": "Capovolgi orizzontalmente", "DE.Views.ShapeSettings.textHintFlipV": "Capovolgi verticalmente", "DE.Views.ShapeSettings.textImageTexture": "Immagine o trama", "DE.Views.ShapeSettings.textLinear": "Lineare", "DE.Views.ShapeSettings.textNoFill": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.textPatternFill": "<PERSON><PERSON>", "DE.Views.ShapeSettings.textPosition": "Posizione", "DE.Views.ShapeSettings.textRadial": "Radiale", "DE.Views.ShapeSettings.textRecentlyUsed": "Usati di recente", "DE.Views.ShapeSettings.textRotate90": "Ruota di 90°", "DE.Views.ShapeSettings.textRotation": "Rotazione", "DE.Views.ShapeSettings.textSelectImage": "Seleziona immagine", "DE.Views.ShapeSettings.textSelectTexture": "Seleziona", "DE.Views.ShapeSettings.textStretch": "Estendi", "DE.Views.ShapeSettings.textStyle": "Stile", "DE.Views.ShapeSettings.textTexture": "Da trama", "DE.Views.ShapeSettings.textTile": "Tela", "DE.Views.ShapeSettings.textWrap": "Stile di disposizione testo", "DE.Views.ShapeSettings.tipAddGradientPoint": "‎Aggiungi punto di sfumatura‎", "DE.Views.ShapeSettings.tipRemoveGradientPoint": "R<PERSON><PERSON><PERSON> punto sfumatura", "DE.Views.ShapeSettings.txtBehind": "<PERSON><PERSON> al testo", "DE.Views.ShapeSettings.txtBrownPaper": "<PERSON>ta da pacchi", "DE.Views.ShapeSettings.txtCanvas": "Tela", "DE.Views.ShapeSettings.txtCarton": "Cartone", "DE.Views.ShapeSettings.txtDarkFabric": "<PERSON><PERSON> scuro", "DE.Views.ShapeSettings.txtGrain": "Grano", "DE.Views.ShapeSettings.txtGranite": "<PERSON><PERSON>", "DE.Views.ShapeSettings.txtGreyPaper": "<PERSON>ta grigia", "DE.Views.ShapeSettings.txtInFront": "<PERSON><PERSON><PERSON> al testo", "DE.Views.ShapeSettings.txtInline": "In linea con il testo", "DE.Views.ShapeSettings.txtKnit": "A maglia", "DE.Views.ShapeSettings.txtLeather": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.txtNoBorders": "Nessuna linea", "DE.Views.ShapeSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.txtSquare": "Quadrato", "DE.Views.ShapeSettings.txtThrough": "All'interno", "DE.Views.ShapeSettings.txtTight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.txtTopAndBottom": "Sopra e sotto", "DE.Views.ShapeSettings.txtWood": "<PERSON><PERSON>", "DE.Views.SignatureSettings.notcriticalErrorTitle": "Avviso", "DE.Views.SignatureSettings.strDelete": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.SignatureSettings.strDetails": "Dettagli firma", "DE.Views.SignatureSettings.strInvalid": "Firme non valide", "DE.Views.SignatureSettings.strRequested": "<PERSON><PERSON><PERSON>", "DE.Views.SignatureSettings.strSetup": "Impostazioni firma", "DE.Views.SignatureSettings.strSign": "Firma", "DE.Views.SignatureSettings.strSignature": "Firma", "DE.Views.SignatureSettings.strSigner": "Firmatario", "DE.Views.SignatureSettings.strValid": "Firme valide", "DE.Views.SignatureSettings.txtContinueEditing": "Modifica comunque", "DE.Views.SignatureSettings.txtEditWarning": "La modifica eliminerà le firme dal documento.<br>V<PERSON><PERSON> continuare?", "DE.Views.SignatureSettings.txtRemoveWarning": "Vuoi rimuovere questa firma?<br>Non può essere annullata.", "DE.Views.SignatureSettings.txtRequestedSignatures": "Questo documento necessita di essere firmato", "DE.Views.SignatureSettings.txtSigned": "Le firme valide sono state aggiunte al documento. Il documento è protetto dalla modifica.", "DE.Views.SignatureSettings.txtSignedInvalid": "Alcune delle firme digitali preseti nel documento non sono valide o non possono essere verificate. Il documento è protetto dalla modifica.", "DE.Views.Statusbar.goToPageText": "Va' alla pagina", "DE.Views.Statusbar.pageIndexText": "Pagina {0} di {1}", "DE.Views.Statusbar.tipFitPage": "<PERSON>tta alla pagina", "DE.Views.Statusbar.tipFitWidth": "<PERSON><PERSON> alla larghezza", "DE.Views.Statusbar.tipHandTool": "Strumento Mano", "DE.Views.Statusbar.tipSelectTool": "Strumento di selezione", "DE.Views.Statusbar.tipSetLang": "Imposta lingua del testo", "DE.Views.Statusbar.tipZoomFactor": "Ingrandimento", "DE.Views.Statusbar.tipZoomIn": "Zoom avanti", "DE.Views.Statusbar.tipZoomOut": "Zoom indietro", "DE.Views.Statusbar.txtPageNumInvalid": "Numero pagina non valido", "DE.Views.StyleTitleDialog.textHeader": "Crea nuovo stile", "DE.Views.StyleTitleDialog.textNextStyle": "Stile paragrafo successivo", "DE.Views.StyleTitleDialog.textTitle": "Title", "DE.Views.StyleTitleDialog.txtEmpty": "Campo obbligatorio", "DE.Views.StyleTitleDialog.txtNotEmpty": "Il campo non deve essere vuoto", "DE.Views.StyleTitleDialog.txtSameAs": "Come il nuovo stile creato", "DE.Views.TableFormulaDialog.textBookmark": "<PERSON>olla segnalibro", "DE.Views.TableFormulaDialog.textFormat": "Formato numero", "DE.Views.TableFormulaDialog.textFormula": "Formula", "DE.Views.TableFormulaDialog.textInsertFunction": "Incolla funzione", "DE.Views.TableFormulaDialog.textTitle": "Impostazioni della formula", "DE.Views.TableOfContentsSettings.strAlign": "Numeri di pagina allineati a destra", "DE.Views.TableOfContentsSettings.strFullCaption": "‎Includi etichetta e numero‎", "DE.Views.TableOfContentsSettings.strLinks": "Formato Sommario come collegamenti", "DE.Views.TableOfContentsSettings.strLinksOF": "Formato l'indice delle figure‎ come collegamenti", "DE.Views.TableOfContentsSettings.strShowPages": "Mostra numeri di pagina", "DE.Views.TableOfContentsSettings.textBuildTable": "<PERSON><PERSON>", "DE.Views.TableOfContentsSettings.textBuildTableOF": "‎Costruisci l'Indice delle figure‎", "DE.Views.TableOfContentsSettings.textEquation": "Equazione", "DE.Views.TableOfContentsSettings.textFigure": "<PERSON><PERSON><PERSON>", "DE.Views.TableOfContentsSettings.textLeader": "Leader", "DE.Views.TableOfContentsSettings.textLevel": "<PERSON><PERSON>", "DE.Views.TableOfContentsSettings.textLevels": "<PERSON><PERSON>", "DE.Views.TableOfContentsSettings.textNone": "<PERSON><PERSON><PERSON>", "DE.Views.TableOfContentsSettings.textRadioCaption": "Didascalia", "DE.Views.TableOfContentsSettings.textRadioLevels": "<PERSON><PERSON> di struttura", "DE.Views.TableOfContentsSettings.textRadioStyle": "Stile", "DE.Views.TableOfContentsSettings.textRadioStyles": "Stili selezionati", "DE.Views.TableOfContentsSettings.textStyle": "Stile", "DE.Views.TableOfContentsSettings.textStyles": "Stili", "DE.Views.TableOfContentsSettings.textTable": "<PERSON><PERSON>", "DE.Views.TableOfContentsSettings.textTitle": "<PERSON><PERSON><PERSON>", "DE.Views.TableOfContentsSettings.textTitleTOF": "‎Indice delle figure‎", "DE.Views.TableOfContentsSettings.txtCentered": "Centrato", "DE.Views.TableOfContentsSettings.txtClassic": "Classico", "DE.Views.TableOfContentsSettings.txtCurrent": "Attuale", "DE.Views.TableOfContentsSettings.txtDistinctive": "Distintivo", "DE.Views.TableOfContentsSettings.txtFormal": "Formale", "DE.Views.TableOfContentsSettings.txtModern": "Moderno", "DE.Views.TableOfContentsSettings.txtOnline": "In linea", "DE.Views.TableOfContentsSettings.txtSimple": "Semplice", "DE.Views.TableOfContentsSettings.txtStandard": "Standard", "DE.Views.TableSettings.deleteColumnText": "Elimina colonna", "DE.Views.TableSettings.deleteRowText": "Elimina riga", "DE.Views.TableSettings.deleteTableText": "<PERSON><PERSON> tabella", "DE.Views.TableSettings.insertColumnLeftText": "Inserisci colonna a sinistra", "DE.Views.TableSettings.insertColumnRightText": "Inserisci colonna a destra", "DE.Views.TableSettings.insertRowAboveText": "Inserisci riga sopra", "DE.Views.TableSettings.insertRowBelowText": "Inser<PERSON>ci riga sotto", "DE.Views.TableSettings.mergeCellsText": "Unisci celle", "DE.Views.TableSettings.selectCellText": "Seleziona cella", "DE.Views.TableSettings.selectColumnText": "Seleziona colonna", "DE.Views.TableSettings.selectRowText": "Seleziona riga", "DE.Views.TableSettings.selectTableText": "<PERSON><PERSON><PERSON><PERSON> tabella", "DE.Views.TableSettings.splitCellsText": "Divisione cella in corso...", "DE.Views.TableSettings.splitCellTitleText": "Dividi cella", "DE.Views.TableSettings.strRepeatRow": "Ripeti come riga di intestazione in ogni pagina", "DE.Views.TableSettings.textAddFormula": "Aggiungi formula", "DE.Views.TableSettings.textAdvanced": "Mostra impostazioni avanzate", "DE.Views.TableSettings.textBackColor": "Colore sfondo", "DE.Views.TableSettings.textBanded": "A strisce", "DE.Views.TableSettings.textBorderColor": "Colore", "DE.Views.TableSettings.textBorders": "<PERSON><PERSON> bordo", "DE.Views.TableSettings.textCellSize": "Dimensioni di righe e colonne", "DE.Views.TableSettings.textColumns": "Colonne", "DE.Views.TableSettings.textConvert": "Convertire tabella in testo", "DE.Views.TableSettings.textDistributeCols": "Distribuisci colonne", "DE.Views.TableSettings.textDistributeRows": "Distribuisci righe", "DE.Views.TableSettings.textEdit": "Righe e colonne", "DE.Views.TableSettings.textEmptyTemplate": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.textFirst": "Prima", "DE.Views.TableSettings.textHeader": "Intestazione", "DE.Views.TableSettings.textHeight": "Altezza", "DE.Views.TableSettings.textLast": "Ultima", "DE.Views.TableSettings.textRows": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.textSelectBorders": "Seleziona i bordi che desideri modificare applicando lo stile scelto sopra", "DE.Views.TableSettings.textTemplate": "Seleziona da modello", "DE.Views.TableSettings.textTotal": "Totale", "DE.Views.TableSettings.textWidth": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.TableSettings.tipAll": "Imposta bordo esterno e tutte le linee interne", "DE.Views.TableSettings.tipBottom": "Imposta solo bordo esterno inferiore", "DE.Views.TableSettings.tipInner": "<PERSON><PERSON><PERSON> solo linee interne", "DE.Views.TableSettings.tipInnerHor": "Imposta solo linee interne orizzontali", "DE.Views.TableSettings.tipInnerVert": "<PERSON>mpo<PERSON> solo linee interne verticali", "DE.Views.TableSettings.tipLeft": "Imposta solo bordo esterno sinistro", "DE.Views.TableSettings.tipNone": "Non impostare bordi", "DE.Views.TableSettings.tipOuter": "<PERSON>mpo<PERSON> solo bordi esterni", "DE.Views.TableSettings.tipRight": "Impo<PERSON> solo bordo esterno destro", "DE.Views.TableSettings.tipTop": "Imposta solo bordo esterno superiore", "DE.Views.TableSettings.txtGroupTable_Custom": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.TableSettings.txtNoBorders": "<PERSON><PERSON><PERSON> bordo", "DE.Views.TableSettings.txtTable_Accent": "Accento", "DE.Views.TableSettings.txtTable_Colorful": "Colorato", "DE.Views.TableSettings.txtTable_Dark": "<PERSON><PERSON>", "DE.Views.TableSettings.txtTable_GridTable": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.txtTable_Light": "Chiaro", "DE.Views.TableSettings.txtTable_ListTable": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.txtTable_PlainTable": "<PERSON><PERSON> semplice", "DE.Views.TableSettings.txtTable_TableGrid": "<PERSON><PERSON> griglia", "DE.Views.TableSettingsAdvanced.textAlign": "Allineamento", "DE.Views.TableSettingsAdvanced.textAlignment": "Allineamento", "DE.Views.TableSettingsAdvanced.textAllowSpacing": "Consenti spaziatura tra celle", "DE.Views.TableSettingsAdvanced.textAlt": "Testo alternativo", "DE.Views.TableSettingsAdvanced.textAltDescription": "Descrizione", "DE.Views.TableSettingsAdvanced.textAltTip": "La rappresentazione alternativa testuale basata sulle informazioni dell'oggetto visivo, verrà letta alle persone con disabilità visive o cognitive per aiutarle a capire meglio quali informazioni ci sono nell'immagine, nella forma automatica, nel grafico e nella tabella.", "DE.Views.TableSettingsAdvanced.textAltTitle": "<PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textAnchorText": "<PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textAutofit": "Adatta automaticamente al contenuto", "DE.Views.TableSettingsAdvanced.textBackColor": "Sfondo cella", "DE.Views.TableSettingsAdvanced.textBelow": "al di sotto", "DE.Views.TableSettingsAdvanced.textBorderColor": "Colore bordo", "DE.Views.TableSettingsAdvanced.textBorderDesc": "Clicca sul diagramma o utilizza i pulsanti per selezionare i bordi e applicare lo stile selezionato ad essi", "DE.Views.TableSettingsAdvanced.textBordersBackgroung": "Bordi e sfondo", "DE.Views.TableSettingsAdvanced.textBorderWidth": "Dimensioni bordo", "DE.Views.TableSettingsAdvanced.textBottom": "In basso", "DE.Views.TableSettingsAdvanced.textCellOptions": "Opzioni della cella", "DE.Views.TableSettingsAdvanced.textCellProps": "Cella", "DE.Views.TableSettingsAdvanced.textCellSize": "Dimensioni cella", "DE.Views.TableSettingsAdvanced.textCenter": "Al centro", "DE.Views.TableSettingsAdvanced.textCenterTooltip": "Al centro", "DE.Views.TableSettingsAdvanced.textCheckMargins": "Utilizza margini predefiniti", "DE.Views.TableSettingsAdvanced.textDefaultMargins": "Margini predefiniti delle celle", "DE.Views.TableSettingsAdvanced.textDistance": "<PERSON><PERSON><PERSON> dal testo", "DE.Views.TableSettingsAdvanced.textHorizontal": "Orizzontale", "DE.Views.TableSettingsAdvanced.textIndLeft": "Rientro da sinistra", "DE.Views.TableSettingsAdvanced.textLeft": "A sinistra", "DE.Views.TableSettingsAdvanced.textLeftTooltip": "A sinistra", "DE.Views.TableSettingsAdvanced.textMargin": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textMargins": "Margini cella", "DE.Views.TableSettingsAdvanced.textMeasure": "<PERSON><PERSON><PERSON> in", "DE.Views.TableSettingsAdvanced.textMove": "Sposta oggetto con testo", "DE.Views.TableSettingsAdvanced.textOnlyCells": "Solo per celle selezionate", "DE.Views.TableSettingsAdvanced.textOptions": "Opzioni", "DE.Views.TableSettingsAdvanced.textOverlap": "Consenti sovrapposizione", "DE.Views.TableSettingsAdvanced.textPage": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textPosition": "Posizione", "DE.Views.TableSettingsAdvanced.textPrefWidth": "<PERSON><PERSON><PERSON><PERSON>ita", "DE.Views.TableSettingsAdvanced.textPreview": "Anteprima", "DE.Views.TableSettingsAdvanced.textRelative": "rispetto a", "DE.Views.TableSettingsAdvanced.textRight": "A destra", "DE.Views.TableSettingsAdvanced.textRightOf": "a destra di", "DE.Views.TableSettingsAdvanced.textRightTooltip": "A destra", "DE.Views.TableSettingsAdvanced.textTable": "<PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textTableBackColor": "<PERSON><PERSON><PERSON> tabella", "DE.Views.TableSettingsAdvanced.textTablePosition": "Posizione della tabella", "DE.Views.TableSettingsAdvanced.textTableSize": "Dimensioni tabella", "DE.Views.TableSettingsAdvanced.textTitle": "Tabella - Impostazioni avanzate", "DE.Views.TableSettingsAdvanced.textTop": "In alto", "DE.Views.TableSettingsAdvanced.textVertical": "Verticale", "DE.Views.TableSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textWidthSpaces": "Spessori e spazi", "DE.Views.TableSettingsAdvanced.textWrap": "Disposizione testo", "DE.Views.TableSettingsAdvanced.textWrapNoneTooltip": "Tabella in linea", "DE.Views.TableSettingsAdvanced.textWrapParallelTooltip": "<PERSON><PERSON> din<PERSON>", "DE.Views.TableSettingsAdvanced.textWrappingStyle": "Stile di disposizione testo", "DE.Views.TableSettingsAdvanced.textWrapText": "<PERSON><PERSON><PERSON> testo", "DE.Views.TableSettingsAdvanced.tipAll": "Imposta bordo esterno e tutte le linee interne", "DE.Views.TableSettingsAdvanced.tipCellAll": "<PERSON>mpo<PERSON> solo bordi per celle interne", "DE.Views.TableSettingsAdvanced.tipCellInner": "Imposta solo linee orizzontali e verticali per celle interne", "DE.Views.TableSettingsAdvanced.tipCellOuter": "Imposta solo bordi esterni per celle interne", "DE.Views.TableSettingsAdvanced.tipInner": "<PERSON><PERSON><PERSON> solo linee interne", "DE.Views.TableSettingsAdvanced.tipNone": "Non impostare bordi", "DE.Views.TableSettingsAdvanced.tipOuter": "<PERSON>mpo<PERSON> solo bordi esterni", "DE.Views.TableSettingsAdvanced.tipTableOuterCellAll": "Imposta bordi esterni e bordi per tutte le celle interne", "DE.Views.TableSettingsAdvanced.tipTableOuterCellInner": "Imposta bordi esterni e linee verticali e orizzontali per celle interne", "DE.Views.TableSettingsAdvanced.tipTableOuterCellOuter": "Imposta bordi esterni di tabella e bordi esterni per celle interne", "DE.Views.TableSettingsAdvanced.txtCm": "Centimetro", "DE.Views.TableSettingsAdvanced.txtInch": "Pollice", "DE.Views.TableSettingsAdvanced.txtNoBorders": "<PERSON><PERSON><PERSON> bordo", "DE.Views.TableSettingsAdvanced.txtPercent": "Percento", "DE.Views.TableSettingsAdvanced.txtPt": "Punt<PERSON>", "DE.Views.TableToTextDialog.textEmpty": "‎È necessario digitare un carattere per il separatore personalizzato.‎", "DE.Views.TableToTextDialog.textNested": "Convertire le tabelle nidificate", "DE.Views.TableToTextDialog.textOther": "Altro", "DE.Views.TableToTextDialog.textPara": "Segni di paragrafo", "DE.Views.TableToTextDialog.textSemicolon": "Virgole", "DE.Views.TableToTextDialog.textSeparator": "Separare il testo con", "DE.Views.TableToTextDialog.textTab": "Schede", "DE.Views.TableToTextDialog.textTitle": "Convertire tabella in testo", "DE.Views.TextArtSettings.strColor": "Colore", "DE.Views.TextArtSettings.strFill": "Riempimento", "DE.Views.TextArtSettings.strSize": "Dimensione", "DE.Views.TextArtSettings.strStroke": "Linea", "DE.Views.TextArtSettings.strTransparency": "Opacità", "DE.Views.TextArtSettings.strType": "Tipo", "DE.Views.TextArtSettings.textAngle": "<PERSON><PERSON>", "DE.Views.TextArtSettings.textBorderSizeErr": "Il valore inserito non è corretto.<br>Inserisci un valore tra 0 pt e 1584 pt.", "DE.Views.TextArtSettings.textColor": "Colore di riempimento", "DE.Views.TextArtSettings.textDirection": "Direzione", "DE.Views.TextArtSettings.textGradient": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.TextArtSettings.textGradientFill": "Riempimento sfumato", "DE.Views.TextArtSettings.textLinear": "Lineare", "DE.Views.TextArtSettings.textNoFill": "<PERSON><PERSON><PERSON>", "DE.Views.TextArtSettings.textPosition": "Posizione", "DE.Views.TextArtSettings.textRadial": "Radiale", "DE.Views.TextArtSettings.textSelectTexture": "Seleziona", "DE.Views.TextArtSettings.textStyle": "Stile", "DE.Views.TextArtSettings.textTemplate": "<PERSON><PERSON>", "DE.Views.TextArtSettings.textTransform": "Trasformazione", "DE.Views.TextArtSettings.tipAddGradientPoint": "‎Aggiungi punto di sfumatura‎", "DE.Views.TextArtSettings.tipRemoveGradientPoint": "R<PERSON><PERSON><PERSON> punto sfumatura", "DE.Views.TextArtSettings.txtNoBorders": "Nessuna linea", "DE.Views.TextToTableDialog.textAutofit": "Modo di autofit", "DE.Views.TextToTableDialog.textColumns": "Colonne", "DE.Views.TextToTableDialog.textContents": "Autofit ai contenuti", "DE.Views.TextToTableDialog.textEmpty": "‎È necessario digitare un carattere per il separatore personalizzato.‎", "DE.Views.TextToTableDialog.textFixed": "Larg<PERSON><PERSON> di colonna fissa", "DE.Views.TextToTableDialog.textOther": "Altro", "DE.Views.TextToTableDialog.textPara": "Paragrafi", "DE.Views.TextToTableDialog.textRows": "<PERSON><PERSON><PERSON>", "DE.Views.TextToTableDialog.textSemicolon": "Virgole", "DE.Views.TextToTableDialog.textSeparator": "Separare il testo in", "DE.Views.TextToTableDialog.textTab": "Schede", "DE.Views.TextToTableDialog.textTableSize": "Dimensione di tabella", "DE.Views.TextToTableDialog.textTitle": "Convertire testo in tabella", "DE.Views.TextToTableDialog.textWindow": "Autofit alla finestra", "DE.Views.TextToTableDialog.txtAutoText": "Automatico", "DE.Views.Toolbar.capBtnAddComment": "Aggiungi commento", "DE.Views.Toolbar.capBtnBlankPage": "<PERSON><PERSON><PERSON> vuota", "DE.Views.Toolbar.capBtnColumns": "Colonne", "DE.Views.Toolbar.capBtnComment": "Commento", "DE.Views.Toolbar.capBtnDateTime": "Data e ora", "DE.Views.Toolbar.capBtnInsChart": "Grafico", "DE.Views.Toolbar.capBtnInsControls": "Controlli del contenuto", "DE.Views.Toolbar.capBtnInsDropcap": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.capBtnInsEquation": "Equazione", "DE.Views.Toolbar.capBtnInsHeader": "Intestazione/Piè di pagina", "DE.Views.Toolbar.capBtnInsImage": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.capBtnInsPagebreak": "Interruzione di pagina", "DE.Views.Toolbar.capBtnInsShape": "Forma", "DE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "DE.Views.Toolbar.capBtnInsSymbol": "Simbolo", "DE.Views.Toolbar.capBtnInsTable": "<PERSON><PERSON>", "DE.Views.Toolbar.capBtnInsTextart": "Text Art", "DE.Views.Toolbar.capBtnInsTextbox": "Casella di testo", "DE.Views.Toolbar.capBtnLineNumbers": "Linea con numeri", "DE.Views.Toolbar.capBtnMargins": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.capBtnPageOrient": "Orientamento", "DE.Views.Toolbar.capBtnPageSize": "Dimensione", "DE.Views.Toolbar.capBtnWatermark": "Filigrana", "DE.Views.Toolbar.capImgAlign": "Allinea", "DE.Views.Toolbar.capImgBackward": "Porta indietro", "DE.Views.Toolbar.capImgForward": "Porta avanti", "DE.Views.Toolbar.capImgGroup": "Gruppo", "DE.Views.Toolbar.capImgWrapping": "Disposizione", "DE.Views.Toolbar.mniCapitalizeWords": "<PERSON><PERSON><PERSON> in maiuscolo ogni parola", "DE.Views.Toolbar.mniCustomTable": "Inserisci tabella personalizzata", "DE.Views.Toolbar.mniDrawTable": "Disegna <PERSON>bella", "DE.Views.Toolbar.mniEditControls": "Impostazioni di controllo", "DE.Views.Toolbar.mniEditDropCap": "Impostazioni capolettera", "DE.Views.Toolbar.mniEditFooter": "Modifica piè di pagina", "DE.Views.Toolbar.mniEditHeader": "Modifica intestazione", "DE.Views.Toolbar.mniEraseTable": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.mniFromFile": "Dal file", "DE.Views.Toolbar.mniFromStorage": "<PERSON><PERSON>'archivio", "DE.Views.Toolbar.mniFromUrl": "Dall'URL", "DE.Views.Toolbar.mniHiddenBorders": "<PERSON><PERSON> tabella nascosti", "DE.Views.Toolbar.mniHiddenChars": "Caratteri non stampabili", "DE.Views.Toolbar.mniHighlightControls": "Impostazioni evidenziazione", "DE.Views.Toolbar.mniImageFromFile": "Imma<PERSON>e da file", "DE.Views.Toolbar.mniImageFromStorage": "Immagine dallo spazio di archiviazione", "DE.Views.Toolbar.mniImageFromUrl": "<PERSON><PERSON><PERSON><PERSON> da URL", "DE.Views.Toolbar.mniInsertSSE": "Inserire foglio di calcolo", "DE.Views.Toolbar.mniLowerCase": "minuscolo", "DE.Views.Toolbar.mniRemoveFooter": "Rimuovere piè di pagina", "DE.Views.Toolbar.mniRemoveHeader": "Rimuovere intestazione", "DE.Views.Toolbar.mniSentenceCase": "Sentenza della frase", "DE.Views.Toolbar.mniTextToTable": "Convertire testo in tabella", "DE.Views.Toolbar.mniToggleCase": "mAIUSCOLO mINUSCOLO", "DE.Views.Toolbar.mniUpperCase": "MAIUSCOLO", "DE.Views.Toolbar.strMenuNoFill": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textAutoColor": "Automatico", "DE.Views.Toolbar.textBold": "Grassetto", "DE.Views.Toolbar.textBottom": "In basso: ", "DE.Views.Toolbar.textChangeLevel": "Cambia livello elenco", "DE.Views.Toolbar.textCheckboxControl": "Casella di controllo", "DE.Views.Toolbar.textColumnsCustom": "Colonne <PERSON>", "DE.Views.Toolbar.textColumnsLeft": "A sinistra", "DE.Views.Toolbar.textColumnsOne": "Uno", "DE.Views.Toolbar.textColumnsRight": "A destra", "DE.Views.Toolbar.textColumnsThree": "Tre", "DE.Views.Toolbar.textColumnsTwo": "Due", "DE.Views.Toolbar.textComboboxControl": "<PERSON>lla combinata", "DE.Views.Toolbar.textContinuous": "Continua", "DE.Views.Toolbar.textContPage": "Pagina continua", "DE.Views.Toolbar.textCustomLineNumbers": "Opzioni di numerazione delle righe", "DE.Views.Toolbar.textDateControl": "Data", "DE.Views.Toolbar.textDropdownControl": "Elenco a discesa", "DE.Views.Toolbar.textEditWatermark": "<PERSON>ligrana <PERSON>", "DE.Views.Toolbar.textEvenPage": "<PERSON>gina pari", "DE.Views.Toolbar.textInMargin": "<PERSON><PERSON> margine", "DE.Views.Toolbar.textInsColumnBreak": "Inserisci interruzione di colonna", "DE.Views.Toolbar.textInsertPageCount": "Inserisci numero delle pagine", "DE.Views.Toolbar.textInsertPageNumber": "Inserisci numero di pagina", "DE.Views.Toolbar.textInsPageBreak": "Inserisci interruzione di pagina", "DE.Views.Toolbar.textInsSectionBreak": "Inserisci interruzione di sezione", "DE.Views.Toolbar.textInText": "<PERSON><PERSON> testo", "DE.Views.Toolbar.textItalic": "Corsivo", "DE.Views.Toolbar.textLandscape": "Orizzontale", "DE.Views.Toolbar.textLeft": "Sinistra:", "DE.Views.Toolbar.textListSettings": "Impostazioni elenco", "DE.Views.Toolbar.textMarginsLast": "Ultima personalizzazione", "DE.Views.Toolbar.textMarginsModerate": "Moderare", "DE.Views.Toolbar.textMarginsNarrow": "Stret<PERSON>", "DE.Views.Toolbar.textMarginsNormal": "Normale", "DE.Views.Toolbar.textMarginsUsNormal": "Normale USA", "DE.Views.Toolbar.textMarginsWide": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textNewColor": "Aggiungi Colore personalizzato", "DE.Views.Toolbar.textNextPage": "Pagina successiva", "DE.Views.Toolbar.textNoHighlight": "Nessuna evidenziazione", "DE.Views.Toolbar.textNone": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textOddPage": "<PERSON><PERSON>a dispari", "DE.Views.Toolbar.textPageMarginsCustom": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textPageSizeCustom": "Dimensioni pagina personalizzate", "DE.Views.Toolbar.textPictureControl": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textPlainControl": "Inserisci il controllo del contenuto in testo normale", "DE.Views.Toolbar.textPortrait": "Verticale", "DE.Views.Toolbar.textRemoveControl": "Rimuovi il controllo del contenuto", "DE.Views.Toolbar.textRemWatermark": "R<PERSON><PERSON>vi filigrana", "DE.Views.Toolbar.textRestartEachPage": "Ricomincia a ogni pagina", "DE.Views.Toolbar.textRestartEachSection": "Ricomincia a ogni sezione", "DE.Views.Toolbar.textRichControl": "RTF", "DE.Views.Toolbar.textRight": "Destra:", "DE.Views.Toolbar.textStrikeout": "Barrato", "DE.Views.Toolbar.textStyleMenuDelete": "<PERSON>mina stile", "DE.Views.Toolbar.textStyleMenuDeleteAll": "Elimina tutti gli stili personalizzati", "DE.Views.Toolbar.textStyleMenuNew": "Nuovo stile da selezione", "DE.Views.Toolbar.textStyleMenuRestore": "Ripristina ai valori predefiniti", "DE.Views.Toolbar.textStyleMenuRestoreAll": "Rip<PERSON>ina tutti gli stili predefiniti", "DE.Views.Toolbar.textStyleMenuUpdate": "Aggiorna da selezione", "DE.Views.Toolbar.textSubscript": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textSuperscript": "Apice", "DE.Views.Toolbar.textSuppressForCurrentParagraph": "‎Sopprimi per il paragrafo corrente‎", "DE.Views.Toolbar.textTabCollaboration": "Collaborazione", "DE.Views.Toolbar.textTabFile": "File", "DE.Views.Toolbar.textTabHome": "Home", "DE.Views.Toolbar.textTabInsert": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textTabLayout": "Layout di <PERSON>gina", "DE.Views.Toolbar.textTabLinks": "R<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textTabProtect": "Protezione", "DE.Views.Toolbar.textTabReview": "Revisione", "DE.Views.Toolbar.textTabView": "Visualizza", "DE.Views.Toolbar.textTitleError": "Errore", "DE.Views.Toolbar.textToCurrent": "Alla posizione corrente", "DE.Views.Toolbar.textTop": "in alto:", "DE.Views.Toolbar.textUnderline": "Sottolineato", "DE.Views.Toolbar.tipAlignCenter": "Allinea al centro", "DE.Views.Toolbar.tipAlignJust": "Giustificato", "DE.Views.Toolbar.tipAlignLeft": "Allinea a sinistra", "DE.Views.Toolbar.tipAlignRight": "Allinea a destra", "DE.Views.Toolbar.tipBack": "Indietro", "DE.Views.Toolbar.tipBlankPage": "Inserisci pagina vuota", "DE.Views.Toolbar.tipChangeCase": "Cambia caso", "DE.Views.Toolbar.tipChangeChart": "Cambia tipo di grafico", "DE.Views.Toolbar.tipClearStyle": "Cancella stile", "DE.Views.Toolbar.tipColorSchemas": "Cambia combinazione colori", "DE.Views.Toolbar.tipColumns": "Inserisci colonne", "DE.Views.Toolbar.tipControls": "Inserisci i controlli del contenuto", "DE.Views.Toolbar.tipCopy": "Copia", "DE.Views.Toolbar.tipCopyStyle": "Copia stile", "DE.Views.Toolbar.tipCut": "Tagliare", "DE.Views.Toolbar.tipDateTime": "Inserisci data e ora correnti", "DE.Views.Toolbar.tipDecFont": "Riduci dimensione caratteri", "DE.Views.Toolbar.tipDecPrLeft": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>", "DE.Views.Toolbar.tipDropCap": "<PERSON>ser<PERSON><PERSON> capolettera", "DE.Views.Toolbar.tipEditHeader": "Modifica intestazione o piè di pagina", "DE.Views.Toolbar.tipFontColor": "Colore caratteri", "DE.Views.Toolbar.tipFontName": "Tipo di carattere", "DE.Views.Toolbar.tipFontSize": "Dimensione carattere", "DE.Views.Toolbar.tipHighlightColor": "Colore evidenziatore", "DE.Views.Toolbar.tipImgAlign": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipImgGroup": "Raggruppa oggetti", "DE.Views.Toolbar.tipImgWrapping": "<PERSON><PERSON><PERSON> testo", "DE.Views.Toolbar.tipIncFont": "Aumenta dimensione caratteri ", "DE.Views.Toolbar.tipIncPrLeft": "Aumenta rientro", "DE.Views.Toolbar.tipInsertChart": "Inserisci grafico", "DE.Views.Toolbar.tipInsertEquation": "Inserisci Equazione", "DE.Views.Toolbar.tipInsertImage": "Inserisci immagine", "DE.Views.Toolbar.tipInsertNum": "Inserisci numero di pagina", "DE.Views.Toolbar.tipInsertShape": "Inserisci forma automatica", "DE.Views.Toolbar.tipInsertSymbol": "Inserisci Simbolo", "DE.Views.Toolbar.tipInsertTable": "<PERSON><PERSON><PERSON><PERSON> tabella", "DE.Views.Toolbar.tipInsertText": "<PERSON><PERSON><PERSON><PERSON> casella di testo", "DE.Views.Toolbar.tipInsertTextArt": "Inserisci Text Art", "DE.Views.Toolbar.tipLineNumbers": "‎Mostra numeri di riga‎", "DE.Views.Toolbar.tipLineSpace": "Interlinea paragrafo", "DE.Views.Toolbar.tipMailRecepients": "Stampa unione", "DE.Views.Toolbar.tipMarkers": "<PERSON><PERSON><PERSON> puntati", "DE.Views.Toolbar.tipMarkersArrow": "Punti elenco a freccia", "DE.Views.Toolbar.tipMarkersCheckmark": "Punti elenco a segno di spunta", "DE.Views.Toolbar.tipMarkersDash": "Punti elenco a trattino", "DE.Views.Toolbar.tipMarkersFRhombus": "Punti elenco a rombo pieno", "DE.Views.Toolbar.tipMarkersFRound": "Punti elenco rotondi pieni", "DE.Views.Toolbar.tipMarkersFSquare": "Punti elenco quadrati pieni", "DE.Views.Toolbar.tipMarkersHRound": "Punti elenco rotondi vuoti", "DE.Views.Toolbar.tipMarkersStar": "Punti elenco a stella", "DE.Views.Toolbar.tipMultiLevelNumbered": "Elenco numerato a livelli multipli", "DE.Views.Toolbar.tipMultilevels": "Struttura", "DE.Views.Toolbar.tipMultiLevelSymbols": "Simboli puntati a livelli multipli", "DE.Views.Toolbar.tipMultiLevelVarious": "Punti numerati a livelli multipli", "DE.Views.Toolbar.tipNumbers": "<PERSON><PERSON><PERSON> numerati", "DE.Views.Toolbar.tipPageBreak": "Inserisci interruzione di pagina o di sezione", "DE.Views.Toolbar.tipPageMargins": "<PERSON><PERSON>i della pagina", "DE.Views.Toolbar.tipPageOrient": "Orientamento pagina", "DE.Views.Toolbar.tipPageSize": "Dimensione pagina", "DE.Views.Toolbar.tipParagraphStyle": "<PERSON><PERSON> paragrafo", "DE.Views.Toolbar.tipPaste": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipPrColor": "Colore sfondo di paragrafo", "DE.Views.Toolbar.tipPrint": "Stampa", "DE.Views.Toolbar.tipRedo": "R<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipSave": "<PERSON><PERSON>", "DE.Views.Toolbar.tipSaveCoauth": "Salva i tuoi cambiamenti per renderli disponibili agli altri utenti.", "DE.Views.Toolbar.tipSelectAll": "Se<PERSON><PERSON><PERSON><PERSON> tutto", "DE.Views.Toolbar.tipSendBackward": "Porta indietro", "DE.Views.Toolbar.tipSendForward": "Porta avanti", "DE.Views.Toolbar.tipShowHiddenChars": "Caratteri non stampabili", "DE.Views.Toolbar.tipSynchronize": "Il documento è stato modificato da un altro utente. Clicca per salvare le modifiche e ricaricare gli aggiornamenti.", "DE.Views.Toolbar.tipUndo": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipWatermark": "Modifica filigrana", "DE.Views.Toolbar.txtDistribHor": "Distribuisci orizzontalmente", "DE.Views.Toolbar.txtDistribVert": "Distribuisci verticalmente", "DE.Views.Toolbar.txtMarginAlign": "Allinea al margine", "DE.Views.Toolbar.txtObjectsAlign": "Allinea oggetti selezionati", "DE.Views.Toolbar.txtPageAlign": "Allinea alla pagina", "DE.Views.Toolbar.txtScheme1": "Ufficio", "DE.Views.Toolbar.txtScheme10": "Luna", "DE.Views.Toolbar.txtScheme11": "Metro", "DE.Views.Toolbar.txtScheme12": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.txtScheme13": "<PERSON><PERSON>", "DE.Views.Toolbar.txtScheme14": "Oriel", "DE.Views.Toolbar.txtScheme15": "Satellite", "DE.Views.Toolbar.txtScheme16": "Carta", "DE.Views.Toolbar.txtScheme17": "Solstizio", "DE.Views.Toolbar.txtScheme18": "Tecnologia", "DE.Views.Toolbar.txtScheme19": "Terra", "DE.Views.Toolbar.txtScheme2": "Scala di grigi", "DE.Views.Toolbar.txtScheme20": "Tramonto", "DE.Views.Toolbar.txtScheme21": "Verve", "DE.Views.Toolbar.txtScheme22": "Nuovo ufficio", "DE.Views.Toolbar.txtScheme3": "Vertice", "DE.Views.Toolbar.txtScheme4": "Aspetto", "DE.Views.Toolbar.txtScheme5": "Civico", "DE.Views.Toolbar.txtScheme6": "<PERSON><PERSON>", "DE.Views.Toolbar.txtScheme7": "Azionario", "DE.Views.Toolbar.txtScheme8": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.txtScheme9": "Fonderia", "DE.Views.ViewTab.textAlwaysShowToolbar": "Mostra sempre barra degli strumenti ", "DE.Views.ViewTab.textDarkDocument": "Documento scuro", "DE.Views.ViewTab.textFitToPage": "<PERSON>tta alla pagina", "DE.Views.ViewTab.textFitToWidth": "<PERSON><PERSON> alla larghezza", "DE.Views.ViewTab.textInterfaceTheme": "Tema dell'interfaccia", "DE.Views.ViewTab.textNavigation": "Navigazione", "DE.Views.ViewTab.textOutline": "Intestazioni", "DE.Views.ViewTab.textRulers": "<PERSON><PERSON><PERSON>", "DE.Views.ViewTab.textStatusBar": "Barra di stato", "DE.Views.ViewTab.textZoom": "Zoom", "DE.Views.ViewTab.tipDarkDocument": "Documento scuro", "DE.Views.ViewTab.tipFitToPage": "<PERSON>tta alla pagina", "DE.Views.ViewTab.tipFitToWidth": "<PERSON><PERSON> alla larghezza", "DE.Views.ViewTab.tipHeadings": "Intestazioni", "DE.Views.ViewTab.tipInterfaceTheme": "Tema dell'interfaccia", "DE.Views.WatermarkSettingsDialog.textAuto": "Auto", "DE.Views.WatermarkSettingsDialog.textBold": "Grassetto", "DE.Views.WatermarkSettingsDialog.textColor": "Colore del testo", "DE.Views.WatermarkSettingsDialog.textDiagonal": "Diagonale", "DE.Views.WatermarkSettingsDialog.textFont": "Tipo di carattere", "DE.Views.WatermarkSettingsDialog.textFromFile": "Da file", "DE.Views.WatermarkSettingsDialog.textFromStorage": "Da spazio di archiviazione", "DE.Views.WatermarkSettingsDialog.textFromUrl": "Da URL", "DE.Views.WatermarkSettingsDialog.textHor": "Orizzontale", "DE.Views.WatermarkSettingsDialog.textImageW": "Immagine filigrana", "DE.Views.WatermarkSettingsDialog.textItalic": "Corsivo", "DE.Views.WatermarkSettingsDialog.textLanguage": "<PERSON><PERSON>", "DE.Views.WatermarkSettingsDialog.textLayout": "Layout di <PERSON>gina", "DE.Views.WatermarkSettingsDialog.textNone": "<PERSON><PERSON><PERSON>", "DE.Views.WatermarkSettingsDialog.textScale": "Ridimensiona", "DE.Views.WatermarkSettingsDialog.textSelect": "Seleziona Immagine", "DE.Views.WatermarkSettingsDialog.textStrikeout": "Barrato", "DE.Views.WatermarkSettingsDialog.textText": "<PERSON><PERSON>", "DE.Views.WatermarkSettingsDialog.textTextW": "Testo filigrana", "DE.Views.WatermarkSettingsDialog.textTitle": "Impostazioni Filigrana", "DE.Views.WatermarkSettingsDialog.textTransparency": "Semitrasparente", "DE.Views.WatermarkSettingsDialog.textUnderline": "Sottolineato", "DE.Views.WatermarkSettingsDialog.tipFontName": "<PERSON><PERSON> carattere", "DE.Views.WatermarkSettingsDialog.tipFontSize": "Dimensione carattere"}