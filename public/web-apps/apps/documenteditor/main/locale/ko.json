{"Common.Controllers.Chat.notcriticalErrorTitle": "경고", "Common.Controllers.Chat.textEnterMessage": "여기에 메시지를 입력하십시오", "Common.Controllers.ExternalDiagramEditor.textAnonymous": "익명", "Common.Controllers.ExternalDiagramEditor.textClose": "닫기", "Common.Controllers.ExternalDiagramEditor.warningText": "다른 사용자가 편집 중이므로 개체를 사용할 수 없습니다.", "Common.Controllers.ExternalDiagramEditor.warningTitle": "경고", "Common.Controllers.ExternalMergeEditor.textAnonymous": "익명", "Common.Controllers.ExternalMergeEditor.textClose": "닫기", "Common.Controllers.ExternalMergeEditor.warningText": "다른 사용자가 편집 중이므로 개체를 사용할 수 없습니다.", "Common.Controllers.ExternalMergeEditor.warningTitle": "경고", "Common.Controllers.History.notcriticalErrorTitle": "경고", "Common.Controllers.ReviewChanges.textAcceptBeforeCompare": "문서를 비교하기 위해 추적된 모든 변경 내역이 수락된 것으로 간주됩니다. 계속하시겠습니까?", "Common.Controllers.ReviewChanges.textAtLeast": "적어도", "Common.Controllers.ReviewChanges.textAuto": "auto", "Common.Controllers.ReviewChanges.textBaseline": "Baseline", "Common.Controllers.ReviewChanges.textBold": "Bold", "Common.Controllers.ReviewChanges.textBreakBefore": "현재 단락 앞에서 페이지 나누기", "Common.Controllers.ReviewChanges.textCaps": "모든 대문자", "Common.Controllers.ReviewChanges.textCenter": "센터 정렬", "Common.Controllers.ReviewChanges.textChar": "문자레벨", "Common.Controllers.ReviewChanges.textChart": "차트", "Common.Controllers.ReviewChanges.textColor": "글꼴 색", "Common.Controllers.ReviewChanges.textContextual": "같은 스타일의 단락 사이에 공백 삽입 안 함", "Common.Controllers.ReviewChanges.textDeleted": "<b> 삭제됨 : </b>", "Common.Controllers.ReviewChanges.textDStrikeout": "이중 취소선", "Common.Controllers.ReviewChanges.textEquation": "수식", "Common.Controllers.ReviewChanges.textExact": "정확히", "Common.Controllers.ReviewChanges.textFirstLine": "첫 번째 줄", "Common.Controllers.ReviewChanges.textFontSize": "글자 크기", "Common.Controllers.ReviewChanges.textFormatted": "서식 지정 됨", "Common.Controllers.ReviewChanges.textHighlight": "색상 강조 표시", "Common.Controllers.ReviewChanges.textImage": "이미지", "Common.Controllers.ReviewChanges.textIndentLeft": "들여 쓰기 왼쪽", "Common.Controllers.ReviewChanges.textIndentRight": "들여 쓰기", "Common.Controllers.ReviewChanges.textInserted": "<b> 삽입 됨 : </b>", "Common.Controllers.ReviewChanges.textItalic": "Italic", "Common.Controllers.ReviewChanges.textJustify": "양쪽 맞춤", "Common.Controllers.ReviewChanges.textKeepLines": "현재 단락을 나누지 않음", "Common.Controllers.ReviewChanges.textKeepNext": "현재 단락과 다음 단락을 항상 같은 페이지에 배치", "Common.Controllers.ReviewChanges.textLeft": "왼쪽 맞춤", "Common.Controllers.ReviewChanges.textLineSpacing": "줄 간격 :", "Common.Controllers.ReviewChanges.textMultiple": "배수", "Common.Controllers.ReviewChanges.textNoBreakBefore": "현재 단락 앞에서 페이지 나누기 없음", "Common.Controllers.ReviewChanges.textNoContextual": "같은 스타일의 단락 사이 간격 추가", "Common.Controllers.ReviewChanges.textNoKeepLines": "현재 단락을 나누지 마십시오", "Common.Controllers.ReviewChanges.textNoKeepNext": "현재 단락과 다음 단락을 항상 같은 페이지에 배치하지 마십시오", "Common.Controllers.ReviewChanges.textNot": "Not", "Common.Controllers.ReviewChanges.textNoWidow": "위젯 컨트롤 없음", "Common.Controllers.ReviewChanges.textNum": "번호 매기기 변경", "Common.Controllers.ReviewChanges.textOff": "더이상 {0} 변경 내용 조회가 불가합니다. ", "Common.Controllers.ReviewChanges.textOffGlobal": "{0} 변경 내용 추적 기능이 비활성화 되었습니다.", "Common.Controllers.ReviewChanges.textOn": "{0} 추적을 사용하기 시작했습니다.", "Common.Controllers.ReviewChanges.textOnGlobal": "{0} 변경 내용 추적 기능이 활성화 되었습니다.", "Common.Controllers.ReviewChanges.textParaDeleted": "<b>단락이 삭제되었습니다</b>", "Common.Controllers.ReviewChanges.textParaFormatted": "단락 서식 지정", "Common.Controllers.ReviewChanges.textParaInserted": "<b>단락이 삽입되었습니다</b>", "Common.Controllers.ReviewChanges.textParaMoveFromDown": "<b>아래로 이동</b>", "Common.Controllers.ReviewChanges.textParaMoveFromUp": "<b>위로 이동:</b>", "Common.Controllers.ReviewChanges.textParaMoveTo": "<b>이동:<b>", "Common.Controllers.ReviewChanges.textPosition": "위치", "Common.Controllers.ReviewChanges.textRight": "오른쪽 정렬", "Common.Controllers.ReviewChanges.textShape": "도형", "Common.Controllers.ReviewChanges.textShd": "배경색", "Common.Controllers.ReviewChanges.textShow": "변경 사항 표시", "Common.Controllers.ReviewChanges.textSmallCaps": "작은 대문자", "Common.Controllers.ReviewChanges.textSpacing": "간격", "Common.Controllers.ReviewChanges.textSpacingAfter": "간격 뒤", "Common.Controllers.ReviewChanges.textSpacingBefore": "간격 앞", "Common.Controllers.ReviewChanges.textStrikeout": "취소선", "Common.Controllers.ReviewChanges.textSubScript": "아래 첨자", "Common.Controllers.ReviewChanges.textSuperScript": "Superscript", "Common.Controllers.ReviewChanges.textTableChanged": "<b> 설정이 변경되었습니다 </ b>", "Common.Controllers.ReviewChanges.textTableRowsAdd": "<b>표 행 삽입</b>", "Common.Controllers.ReviewChanges.textTableRowsDel": "<b>표 행 삭제</b>", "Common.Controllers.ReviewChanges.textTabs": "탭 변경", "Common.Controllers.ReviewChanges.textTitleComparison": "설정 비교", "Common.Controllers.ReviewChanges.textUnderline": "밑줄", "Common.Controllers.ReviewChanges.textUrl": "문서의 URL 링크 붙여 넣기", "Common.Controllers.ReviewChanges.textWidow": "Widow control", "Common.Controllers.ReviewChanges.textWord": "단어 수준", "Common.define.chartData.textArea": "영역", "Common.define.chartData.textAreaStacked": "누적 영역형", "Common.define.chartData.textAreaStackedPer": "100% 누적 영역형", "Common.define.chartData.textBar": "막대", "Common.define.chartData.textBarNormal": "묶은 세로 막대형", "Common.define.chartData.textBarNormal3d": "3차원 묶은 세로 막대", "Common.define.chartData.textBarNormal3dPerspective": "3차원 세로 막대", "Common.define.chartData.textBarStacked": "누적 세로 막대형", "Common.define.chartData.textBarStacked3d": "3차원 누적 세로 막대형", "Common.define.chartData.textBarStackedPer": "100% 누적 세로 막대형", "Common.define.chartData.textBarStackedPer3d": "3차원 100 % 누적 세로 막 대형", "Common.define.chartData.textCharts": "차트", "Common.define.chartData.textColumn": "열", "Common.define.chartData.textCombo": "콤보", "Common.define.chartData.textComboAreaBar": "누적 영역형 - 묶은 세로 막대형", "Common.define.chartData.textComboBarLine": "묶은 세로 막대형 - 꺾은선형", "Common.define.chartData.textComboBarLineSecondary": "묶은 세로 막대형 - 꺾은선형,보조 축", "Common.define.chartData.textComboCustom": "맞춤 조합", "Common.define.chartData.textDoughnut": "도넛", "Common.define.chartData.textHBarNormal": "묶은 가로 막대형", "Common.define.chartData.textHBarNormal3d": "3차원 집합 막대", "Common.define.chartData.textHBarStacked": "누적 가로 막대형", "Common.define.chartData.textHBarStacked3d": "3차원 누적 가로 막대형", "Common.define.chartData.textHBarStackedPer": "100％ 누적 막대형", "Common.define.chartData.textHBarStackedPer3d": "3차원 100 % 기준 누적 가로 막 대형", "Common.define.chartData.textLine": "선", "Common.define.chartData.textLine3d": "3차원 꺾은 선형", "Common.define.chartData.textLineMarker": "마커 라인", "Common.define.chartData.textLineStacked": "누적 꺾은 선형", "Common.define.chartData.textLineStackedMarker": "표식이 있는 누적 꺾은 선형", "Common.define.chartData.textLineStackedPer": "100 % 기준 누적 꺾은 선형", "Common.define.chartData.textLineStackedPerMarker": "표식이 있는 100 % 기준 누적 꺾은 선형", "Common.define.chartData.textPie": "부분 원형", "Common.define.chartData.textPie3d": "3차원 원형", "Common.define.chartData.textPoint": "XY (분산형)", "Common.define.chartData.textScatter": "분산형", "Common.define.chartData.textScatterLine": "직선이 있는 분산형", "Common.define.chartData.textScatterLineMarker": "직선 및 표식이 있는 분산형", "Common.define.chartData.textScatterSmooth": "곡선이 있는 분산형", "Common.define.chartData.textScatterSmoothMarker": "곡선 및 표식이 있는 분산형", "Common.define.chartData.textStock": "주식형", "Common.define.chartData.textSurface": "표면", "Common.Translation.warnFileLocked": "파일이 다른 응용 프로그램에서 편집 중입니다. 편집을 계속하고 사본으로 저장할 수 있습니다.", "Common.Translation.warnFileLockedBtnEdit": "복사본 만들기", "Common.Translation.warnFileLockedBtnView": "미리보기", "Common.UI.ButtonColored.textAutoColor": "자동", "Common.UI.ButtonColored.textNewColor": "사용자 정의 색상 추가", "Common.UI.Calendar.textApril": "4월", "Common.UI.Calendar.textAugust": "8월", "Common.UI.Calendar.textDecember": "12월", "Common.UI.Calendar.textFebruary": "2월", "Common.UI.Calendar.textJanuary": "1월", "Common.UI.Calendar.textJuly": "7월", "Common.UI.Calendar.textJune": "6월", "Common.UI.Calendar.textMarch": "3월", "Common.UI.Calendar.textMay": "5월", "Common.UI.Calendar.textMonths": "개월", "Common.UI.Calendar.textNovember": "11월", "Common.UI.Calendar.textOctober": "10월", "Common.UI.Calendar.textSeptember": "9월", "Common.UI.Calendar.textShortApril": "4.", "Common.UI.Calendar.textShortAugust": "8.", "Common.UI.Calendar.textShortDecember": "12.", "Common.UI.Calendar.textShortFebruary": "2.", "Common.UI.Calendar.textShortFriday": "Fr", "Common.UI.Calendar.textShortJanuary": "1.", "Common.UI.Calendar.textShortJuly": "7.", "Common.UI.Calendar.textShortJune": "6.", "Common.UI.Calendar.textShortMarch": "3.", "Common.UI.Calendar.textShortMay": "5월", "Common.UI.Calendar.textShortMonday": "월", "Common.UI.Calendar.textShortNovember": "11.", "Common.UI.Calendar.textShortOctober": "10.", "Common.UI.Calendar.textShortSaturday": "토", "Common.UI.Calendar.textShortSeptember": "9월", "Common.UI.Calendar.textShortSunday": "일", "Common.UI.Calendar.textShortThursday": "Th", "Common.UI.Calendar.textShortTuesday": "화", "Common.UI.Calendar.textShortWednesday": "우리", "Common.UI.Calendar.textYears": "년", "Common.UI.ComboBorderSize.txtNoBorders": "테두리 없음", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "테두리 없음", "Common.UI.ComboDataView.emptyComboText": "스타일 없음", "Common.UI.ExtendedColorDialog.addButtonText": "Add", "Common.UI.ExtendedColorDialog.textCurrent": "현재", "Common.UI.ExtendedColorDialog.textHexErr": "입력 한 값이 잘못되었습니다. <br> 000000에서 FFFFFF 사이의 값을 입력하십시오.", "Common.UI.ExtendedColorDialog.textNew": "New", "Common.UI.ExtendedColorDialog.textRGBErr": "입력 한 값이 잘못되었습니다. <br> 0에서 255 사이의 숫자 값을 입력하십시오.", "Common.UI.HSBColorPicker.textNoColor": "색상 없음", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "비밀번호 숨기기", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "비밀번호 표시", "Common.UI.SearchDialog.textHighlight": "결과 강조 표시", "Common.UI.SearchDialog.textMatchCase": "대소 문자를 구분합니다", "Common.UI.SearchDialog.textReplaceDef": "대체 텍스트 입력", "Common.UI.SearchDialog.textSearchStart": "여기에 텍스트를 입력하십시오", "Common.UI.SearchDialog.textTitle": "찾기 및 바꾸기", "Common.UI.SearchDialog.textTitle2": "찾기", "Common.UI.SearchDialog.textWholeWords": "전체 단어 만", "Common.UI.SearchDialog.txtBtnHideReplace": "바꾸기 숨기기", "Common.UI.SearchDialog.txtBtnReplace": "Replace", "Common.UI.SearchDialog.txtBtnReplaceAll": "모두 바꾸기", "Common.UI.SynchronizeTip.textDontShow": "이 메시지를 다시 표시하지 않음", "Common.UI.SynchronizeTip.textSynchronize": "다른 사용자가 문서를 변경했습니다. <br> 클릭하여 변경 사항을 저장하고 업데이트를 다시로드하십시오.", "Common.UI.ThemeColorPalette.textStandartColors": "표준 색상", "Common.UI.ThemeColorPalette.textThemeColors": "테마 색", "Common.UI.Themes.txtThemeClassicLight": "전통적인 밝은 색상", "Common.UI.Themes.txtThemeDark": "어두운", "Common.UI.Themes.txtThemeLight": "밝은", "Common.UI.Window.cancelButtonText": "취소", "Common.UI.Window.closeButtonText": "닫기", "Common.UI.Window.noButtonText": "No", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "확인", "Common.UI.Window.textDontShow": "이 메시지를 다시 표시하지 않음", "Common.UI.Window.textError": "오류", "Common.UI.Window.textInformation": "정보", "Common.UI.Window.textWarning": "경고", "Common.UI.Window.yesButtonText": "예", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Views.About.txtAddress": "주소 :", "Common.Views.About.txtLicensee": "LICENSEE", "Common.Views.About.txtLicensor": "LICENSOR", "Common.Views.About.txtMail": "email :", "Common.Views.About.txtPoweredBy": "Powered by", "Common.Views.About.txtTel": "tel .:", "Common.Views.About.txtVersion": "버전", "Common.Views.AutoCorrectDialog.textAdd": "추가", "Common.Views.AutoCorrectDialog.textApplyText": "입력과 동시에 적용", "Common.Views.AutoCorrectDialog.textAutoCorrect": "자동 고침", "Common.Views.AutoCorrectDialog.textAutoFormat": "입력 할 때 자동 서식", "Common.Views.AutoCorrectDialog.textBulleted": "자동 글머리 기호 목록", "Common.Views.AutoCorrectDialog.textBy": "작성", "Common.Views.AutoCorrectDialog.textDelete": "삭제", "Common.Views.AutoCorrectDialog.textDoubleSpaces": "더블 스페이스로 마침표 추가", "Common.Views.AutoCorrectDialog.textFLCells": "표 셀의 첫 글자를 대문자로", "Common.Views.AutoCorrectDialog.textFLSentence": "영어 문장의 첫 글자를 대문자로", "Common.Views.AutoCorrectDialog.textHyperlink": "네트워크 경로 하이퍼링크", "Common.Views.AutoCorrectDialog.textHyphens": "하이픈(--)과 대시(—)", "Common.Views.AutoCorrectDialog.textMathCorrect": "수식 자동 고침", "Common.Views.AutoCorrectDialog.textNumbered": "자동 번호 매기기 목록", "Common.Views.AutoCorrectDialog.textQuotes": "\"직선 따옴표\" 및 \"곱게 따옴표\"", "Common.Views.AutoCorrectDialog.textRecognized": "인식된 함수", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "다음 표현식은 인식 된 수식입니다. 자동으로 이탤릭체로 될 수는 없습니다.", "Common.Views.AutoCorrectDialog.textReplace": "바꾸기", "Common.Views.AutoCorrectDialog.textReplaceText": "입력시 바꿈", "Common.Views.AutoCorrectDialog.textReplaceType": "입력시 텍스트 바꿈", "Common.Views.AutoCorrectDialog.textReset": "재설정", "Common.Views.AutoCorrectDialog.textResetAll": "기본값을 재설정", "Common.Views.AutoCorrectDialog.textRestore": "복구", "Common.Views.AutoCorrectDialog.textTitle": "자동 고침", "Common.Views.AutoCorrectDialog.textWarnAddRec": "인식되는 함수는 대소 A ~ Z까지의 문자만을 포함해야합니다.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "추가한 모든 표현식이 삭제되고 삭제된 표현식이 복원됩니다. 계속하시겠습니까?", "Common.Views.AutoCorrectDialog.warnReplace": "%1에 대한 자동 고침 항목이 이미 있습니다. 교체하시겠습니까?", "Common.Views.AutoCorrectDialog.warnReset": "추가한 모든 자동 고침이 삭제되고 변경된 자동 수정이 원래 값으로 복원됩니다. 계속하시겠습니까?", "Common.Views.AutoCorrectDialog.warnRestore": "%1의 자동 고침 항목이 원래 값으로 재설정됩니다. 계속하시겠습니까?", "Common.Views.Chat.textSend": "보내기", "Common.Views.Comments.mniAuthorAsc": "A에서 Z까지 작성자", "Common.Views.Comments.mniAuthorDesc": "Z에서 A까지 작성자", "Common.Views.Comments.mniDateAsc": "가장 오래된", "Common.Views.Comments.mniDateDesc": "최신", "Common.Views.Comments.mniFilterGroups": "그룹별 필터링", "Common.Views.Comments.mniPositionAsc": "위에서 부터", "Common.Views.Comments.mniPositionDesc": "아래로 부터", "Common.Views.Comments.textAdd": "추가", "Common.Views.Comments.textAddComment": "덧글 추가", "Common.Views.Comments.textAddCommentToDoc": "문서에 설명 추가", "Common.Views.Comments.textAddReply": "답장 추가", "Common.Views.Comments.textAll": "모두", "Common.Views.Comments.textAnonym": "손님", "Common.Views.Comments.textCancel": "취소", "Common.Views.Comments.textClose": "닫기", "Common.Views.Comments.textClosePanel": "코멘트 닫기", "Common.Views.Comments.textComments": "Comments", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "여기에 의견을 입력하십시오", "Common.Views.Comments.textHintAddComment": "덧글 추가", "Common.Views.Comments.textOpenAgain": "다시 열기", "Common.Views.Comments.textReply": "Reply", "Common.Views.Comments.textResolve": "해결", "Common.Views.Comments.textResolved": "해결됨", "Common.Views.Comments.textSort": "코멘트 분류", "Common.Views.Comments.textViewResolved": "코멘트를 다시 열 수 있는 권한이 없습니다", "Common.Views.Comments.txtEmpty": "문서에 코멘트가 없습니다", "Common.Views.CopyWarningDialog.textDontShow": "이 메시지를 다시 표시하지 않음", "Common.Views.CopyWarningDialog.textMsg": "편집기 도구 모음 단추 및 컨텍스트 메뉴 작업을 사용하여 복사, 잘라 내기 및 붙여 넣기 작업은이 편집기 탭 내에서만 수행됩니다. <br> <br> 외부 응용 프로그램으로 복사하거나 붙여 넣으려면 편집기 탭은 다음과 같은 키보드 조합을 사용합니다 : ", "Common.Views.CopyWarningDialog.textTitle": "작업 복사, 잘라 내기 및 붙여 넣기", "Common.Views.CopyWarningDialog.textToCopy": "복사", "Common.Views.CopyWarningDialog.textToCut": "잘라 내기", "Common.Views.CopyWarningDialog.textToPaste": "붙여 넣기", "Common.Views.DocumentAccessDialog.textLoading": "로드 중 ...", "Common.Views.DocumentAccessDialog.textTitle": "공유 설정", "Common.Views.ExternalDiagramEditor.textTitle": "차트 편집기", "Common.Views.ExternalMergeEditor.textTitle": "편지 병합받는 사람", "Common.Views.Header.labelCoUsersDescr": "파일을 편집 중인 사용자:", "Common.Views.Header.textAddFavorite": "즐겨찾기에 추가", "Common.Views.Header.textAdvSettings": "고급 설정", "Common.Views.Header.textBack": "파일 위치 열기", "Common.Views.Header.textCompactView": "보기 컴팩트 도구 모음", "Common.Views.Header.textHideLines": "눈금자 숨기기", "Common.Views.Header.textHideStatusBar": "상태 표시 줄 숨기기", "Common.Views.Header.textRemoveFavorite": "즐겨찾기 제거", "Common.Views.Header.textZoom": "확대/축소", "Common.Views.Header.tipAccessRights": "문서 액세스 권한 관리", "Common.Views.Header.tipDownload": "파일을 다운로드", "Common.Views.Header.tipGoEdit": "현재 파일 편집", "Common.Views.Header.tipPrint": "파일 출력", "Common.Views.Header.tipRedo": "다시 실행", "Common.Views.Header.tipSave": "저장", "Common.Views.Header.tipUndo": "실행 취소", "Common.Views.Header.tipViewSettings": "보기 설정", "Common.Views.Header.tipViewUsers": "사용자보기 및 문서 액세스 권한 관리", "Common.Views.Header.txtAccessRights": "액세스 권한 변경", "Common.Views.Header.txtRename": "이름 바꾸기", "Common.Views.History.textCloseHistory": "기록 닫기", "Common.Views.History.textHide": "Collapse", "Common.Views.History.textHideAll": "자세한 변경 사항 숨기기", "Common.Views.History.textRestore": "복원", "Common.Views.History.textShow": "확장", "Common.Views.History.textShowAll": "자세한 변경 사항 표시", "Common.Views.History.textVer": "ver.", "Common.Views.ImageFromUrlDialog.textUrl": "이미지 URL 붙여 넣기 :", "Common.Views.ImageFromUrlDialog.txtEmpty": "이 입력란은 필수 항목", "Common.Views.ImageFromUrlDialog.txtNotUrl": "이 필드는 \"http://www.example.com\"형식의 URL이어야합니다.", "Common.Views.InsertTableDialog.textInvalidRowsCols": "유효한 행 및 열 수를 지정해야합니다.", "Common.Views.InsertTableDialog.txtColumns": "열 수", "Common.Views.InsertTableDialog.txtMaxText": "이 필드의 최대 값은 {0}입니다.", "Common.Views.InsertTableDialog.txtMinText": "이 필드의 최소값은 {0}입니다.", "Common.Views.InsertTableDialog.txtRows": "행 수", "Common.Views.InsertTableDialog.txtTitle": "표 크기", "Common.Views.InsertTableDialog.txtTitleSplit": "셀 분할", "Common.Views.LanguageDialog.labelSelect": "문서 언어 선택", "Common.Views.OpenDialog.closeButtonText": "파일 닫기", "Common.Views.OpenDialog.txtEncoding": "인코딩", "Common.Views.OpenDialog.txtIncorrectPwd": "비밀번호가 맞지 않음", "Common.Views.OpenDialog.txtOpenFile": "파일을 열려면 암호를 입력하십시오.", "Common.Views.OpenDialog.txtPassword": "비밀번호", "Common.Views.OpenDialog.txtPreview": "미리보기", "Common.Views.OpenDialog.txtProtected": "암호를 입력하고 파일을 열면 파일의 현재 암호가 재설정됩니다.", "Common.Views.OpenDialog.txtTitle": "% 1 옵션 선택", "Common.Views.OpenDialog.txtTitleProtected": "보호 된 파일", "Common.Views.PasswordDialog.txtDescription": "문서 보호용 비밀번호를 세팅하세요", "Common.Views.PasswordDialog.txtIncorrectPwd": "확인 비밀번호가 같지 않음", "Common.Views.PasswordDialog.txtPassword": "암호", "Common.Views.PasswordDialog.txtRepeat": "비밀번호 반복", "Common.Views.PasswordDialog.txtTitle": "비밀번호 설정", "Common.Views.PasswordDialog.txtWarning": "주의: 암호를 잊으면 복구할 수 없습니다. 암호는 대/소문자를 구분합니다. 이 코드를 안전한 곳에 보관하세요.", "Common.Views.PluginDlg.textLoading": "로드 중", "Common.Views.Plugins.groupCaption": "플러그인", "Common.Views.Plugins.strPlugins": "플러그인", "Common.Views.Plugins.textLoading": "로드 중", "Common.Views.Plugins.textStart": "시작", "Common.Views.Plugins.textStop": "정지", "Common.Views.Protection.hintAddPwd": "비밀번호로 암호화", "Common.Views.Protection.hintPwd": "비밀번호 변경 또는 삭제", "Common.Views.Protection.hintSignature": "디지털 서명 또는 서명 라인을 추가 ", "Common.Views.Protection.txtAddPwd": "비밀번호 추가", "Common.Views.Protection.txtChangePwd": "비밀번호를 변경", "Common.Views.Protection.txtDeletePwd": "비밀번호 삭제", "Common.Views.Protection.txtEncrypt": "암호화", "Common.Views.Protection.txtInvisibleSignature": "디지털 서명을 추가", "Common.Views.Protection.txtSignature": "서명", "Common.Views.Protection.txtSignatureLine": "서명란 추가", "Common.Views.RenameDialog.textName": "파일 이름", "Common.Views.RenameDialog.txtInvalidName": "파일 이름에 다음 문자를 포함 할 수 없습니다 :", "Common.Views.ReviewChanges.hintNext": "다음 변경 사항", "Common.Views.ReviewChanges.hintPrev": "이전 변경으로", "Common.Views.ReviewChanges.mniFromFile": "파일로 불러오기", "Common.Views.ReviewChanges.mniFromStorage": "스토리지에서 불러오기", "Common.Views.ReviewChanges.mniFromUrl": "URL로 부터 불러오기", "Common.Views.ReviewChanges.mniSettings": "설정 비교", "Common.Views.ReviewChanges.strFast": "빠르게", "Common.Views.ReviewChanges.strFastDesc": "실시간 공동 편집. 모든 변경사항들은 자동적으로 저장됨.", "Common.Views.ReviewChanges.strStrict": "엄격한", "Common.Views.ReviewChanges.strStrictDesc": "\"저장\" 버튼을 사용하여 귀하와 다른 사람들이 변경한 사항을 동기화하십시오.", "Common.Views.ReviewChanges.textEnable": "활성", "Common.Views.ReviewChanges.textWarnTrackChanges": "승인된 사용자를 위해 변경 내용 추적 기능이 활성중입니다. 다음 사용자가 문서를 열면 변경 내용 추적 기능이 활성 상태로 유지됩니다.", "Common.Views.ReviewChanges.textWarnTrackChangesTitle": "모든 사용자에게 변경 내용 추적 기능을 적용 하시겠습니까?", "Common.Views.ReviewChanges.tipAcceptCurrent": "현재 변경 내용 적용", "Common.Views.ReviewChanges.tipCoAuthMode": "협력 편집 모드 세팅", "Common.Views.ReviewChanges.tipCommentRem": "코멘트 삭제", "Common.Views.ReviewChanges.tipCommentRemCurrent": "현재 코멘트 삭제", "Common.Views.ReviewChanges.tipCommentResolve": "코멘트를 해결된 것으로 표시", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "현 코멘트를 해결된 것으로 표시", "Common.Views.ReviewChanges.tipCompare": "현재 문서를 다른 문서와 비교", "Common.Views.ReviewChanges.tipHistory": "버전 표시", "Common.Views.ReviewChanges.tipRejectCurrent": "현재 변경 거부", "Common.Views.ReviewChanges.tipReview": "변경 내용 추적", "Common.Views.ReviewChanges.tipReviewView": "변경사항이 표시될 모드 선택", "Common.Views.ReviewChanges.tipSetDocLang": "문서 언어 설정", "Common.Views.ReviewChanges.tipSetSpelling": "맞춤법 검사", "Common.Views.ReviewChanges.tipSharing": "문서 액세스 권한 관리", "Common.Views.ReviewChanges.txtAccept": "수락", "Common.Views.ReviewChanges.txtAcceptAll": "모든 변경 내용 적용", "Common.Views.ReviewChanges.txtAcceptChanges": "변경 접수", "Common.Views.ReviewChanges.txtAcceptCurrent": "현재 변경 내용 적용", "Common.Views.ReviewChanges.txtChat": "채팅", "Common.Views.ReviewChanges.txtClose": "닫기", "Common.Views.ReviewChanges.txtCoAuthMode": "공동 편집 모드", "Common.Views.ReviewChanges.txtCommentRemAll": "모든 코멘트 삭제", "Common.Views.ReviewChanges.txtCommentRemCurrent": "현재 코멘트 삭제", "Common.Views.ReviewChanges.txtCommentRemMy": "내 코멘트 삭제", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "내 현재 댓글 삭제", "Common.Views.ReviewChanges.txtCommentRemove": "삭제", "Common.Views.ReviewChanges.txtCommentResolve": "해결", "Common.Views.ReviewChanges.txtCommentResolveAll": "모든 코멘트를 해결된 것으로 표시", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "현 코멘트를 해결된 것으로 표시", "Common.Views.ReviewChanges.txtCommentResolveMy": "내 코멘트를 해결된 것을 표시", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "내 코멘트를 해결된 것으로 표시", "Common.Views.ReviewChanges.txtCompare": "비교", "Common.Views.ReviewChanges.txtDocLang": "언어", "Common.Views.ReviewChanges.txtEditing": "편집", "Common.Views.ReviewChanges.txtFinal": "모든 변경 접수됨 {0}", "Common.Views.ReviewChanges.txtFinalCap": "최종", "Common.Views.ReviewChanges.txtHistory": "버전 기록", "Common.Views.ReviewChanges.txtMarkup": "모든 변경{0}", "Common.Views.ReviewChanges.txtMarkupCap": "마크업 과 풍선", "Common.Views.ReviewChanges.txtMarkupSimple": "모든 변경사항{0}<br>풍선 없음", "Common.Views.ReviewChanges.txtMarkupSimpleCap": "표시된 변경 사항만", "Common.Views.ReviewChanges.txtNext": "다음 변경 사항", "Common.Views.ReviewChanges.txtOff": "나만 표시 안함", "Common.Views.ReviewChanges.txtOffGlobal": "나와 모두 표시 안함", "Common.Views.ReviewChanges.txtOn": "나만 표시", "Common.Views.ReviewChanges.txtOnGlobal": "나와 모두 표시", "Common.Views.ReviewChanges.txtOriginal": "모든 변경 거부됨 {0}", "Common.Views.ReviewChanges.txtOriginalCap": "오리지널", "Common.Views.ReviewChanges.txtPrev": "이전 변경으로", "Common.Views.ReviewChanges.txtPreview": "미리보기", "Common.Views.ReviewChanges.txtReject": "거부", "Common.Views.ReviewChanges.txtRejectAll": "모든 변경 사항 거부", "Common.Views.ReviewChanges.txtRejectChanges": "변경 거부", "Common.Views.ReviewChanges.txtRejectCurrent": "현재 변경 거부", "Common.Views.ReviewChanges.txtSharing": "공유", "Common.Views.ReviewChanges.txtSpelling": "맞춤법 검사", "Common.Views.ReviewChanges.txtTurnon": "변경 내용 추적", "Common.Views.ReviewChanges.txtView": "디스플레이 모드", "Common.Views.ReviewChangesDialog.textTitle": "변경사항 다시보기", "Common.Views.ReviewChangesDialog.txtAccept": "수락", "Common.Views.ReviewChangesDialog.txtAcceptAll": "모든 변경 내용 적용", "Common.Views.ReviewChangesDialog.txtAcceptCurrent": "현재 변경 내용 적용", "Common.Views.ReviewChangesDialog.txtNext": "다음 변경 사항", "Common.Views.ReviewChangesDialog.txtPrev": "이전 변경으로", "Common.Views.ReviewChangesDialog.txtReject": "거부", "Common.Views.ReviewChangesDialog.txtRejectAll": "모든 변경 사항 거부", "Common.Views.ReviewChangesDialog.txtRejectCurrent": "현재 변경 거부", "Common.Views.ReviewPopover.textAdd": "추가", "Common.Views.ReviewPopover.textAddReply": "댓글추가", "Common.Views.ReviewPopover.textCancel": "취소", "Common.Views.ReviewPopover.textClose": "닫기", "Common.Views.ReviewPopover.textEdit": "확인", "Common.Views.ReviewPopover.textFollowMove": "이동", "Common.Views.ReviewPopover.textMention": "+이 내용은 이 문서에 접근시 이메일을 통해 전달됩니다.", "Common.Views.ReviewPopover.textMentionNotify": "+이 내용은 사용자에게 이메일을 통해서 알려집니다.", "Common.Views.ReviewPopover.textOpenAgain": "다시 열기", "Common.Views.ReviewPopover.textReply": "댓글", "Common.Views.ReviewPopover.textResolve": "해결", "Common.Views.ReviewPopover.textViewResolved": "코멘트를 다시 열 수 있는 권한이 없습니다", "Common.Views.ReviewPopover.txtAccept": "동의", "Common.Views.ReviewPopover.txtDeleteTip": "삭제", "Common.Views.ReviewPopover.txtEditTip": "편집", "Common.Views.ReviewPopover.txtReject": "거부", "Common.Views.SaveAsDlg.textLoading": "로드 중", "Common.Views.SaveAsDlg.textTitle": "저장 폴더", "Common.Views.SelectFileDlg.textLoading": "로드 중", "Common.Views.SelectFileDlg.textTitle": "데이터 소스 선택", "Common.Views.SignDialog.textBold": "볼드체", "Common.Views.SignDialog.textCertificate": "인증", "Common.Views.SignDialog.textChange": "변경", "Common.Views.SignDialog.textInputName": "서명자 성함을 입력하세요", "Common.Views.SignDialog.textItalic": "이탈릭", "Common.Views.SignDialog.textNameError": "서명자의 이름은 비워둘 수 없습니다.", "Common.Views.SignDialog.textPurpose": "이 문서에 서명하는 목적", "Common.Views.SignDialog.textSelect": "선택", "Common.Views.SignDialog.textSelectImage": "이미지 선택", "Common.Views.SignDialog.textSignature": "서명은 처럼 보임", "Common.Views.SignDialog.textTitle": "서명문서", "Common.Views.SignDialog.textUseImage": "또는 서명으로 그림을 사용하려면 '이미지 선택'을 클릭", "Common.Views.SignDialog.textValid": "%1에서 %2까지 유효", "Common.Views.SignDialog.tipFontName": "폰트명", "Common.Views.SignDialog.tipFontSize": "글꼴 크기", "Common.Views.SignSettingsDialog.textAllowComment": "서명 대화창에 서명자의 코멘트 추가 허용", "Common.Views.SignSettingsDialog.textInfoEmail": "이메일", "Common.Views.SignSettingsDialog.textInfoName": "이름", "Common.Views.SignSettingsDialog.textInfoTitle": "서명자 타이틀", "Common.Views.SignSettingsDialog.textInstructions": "서명자용 지침", "Common.Views.SignSettingsDialog.textShowDate": "서명라인에 서명 날짜를 보여주세요", "Common.Views.SignSettingsDialog.textTitle": "서명 셋업", "Common.Views.SignSettingsDialog.txtEmpty": "이 입력란은 필수 항목", "Common.Views.SymbolTableDialog.textCharacter": "문자", "Common.Views.SymbolTableDialog.textCode": "유니코드 HEX 값", "Common.Views.SymbolTableDialog.textCopyright": "저작권 표시", "Common.Views.SymbolTableDialog.textDCQuote": "큰 따옴표 닫기", "Common.Views.SymbolTableDialog.textDOQuote": "큰 따옴표 (왼쪽)", "Common.Views.SymbolTableDialog.textEllipsis": "말줄임표", "Common.Views.SymbolTableDialog.textEmDash": "Em 대시", "Common.Views.SymbolTableDialog.textEmSpace": "Em 공백", "Common.Views.SymbolTableDialog.textEnDash": "En 대시", "Common.Views.SymbolTableDialog.textEnSpace": "En 공백", "Common.Views.SymbolTableDialog.textFont": "글꼴", "Common.Views.SymbolTableDialog.textNBHyphen": "줄 바꿈없는 하이픈", "Common.Views.SymbolTableDialog.textNBSpace": "줄 바꿈 없는 공백", "Common.Views.SymbolTableDialog.textPilcrow": "단락기호", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 칸", "Common.Views.SymbolTableDialog.textRange": "범위", "Common.Views.SymbolTableDialog.textRecent": "최근 사용한 기호", "Common.Views.SymbolTableDialog.textRegistered": "등록된 서명", "Common.Views.SymbolTableDialog.textSCQuote": "작은 따옴표 닫기", "Common.Views.SymbolTableDialog.textSection": "섹션 기호", "Common.Views.SymbolTableDialog.textShortcut": "단축키", "Common.Views.SymbolTableDialog.textSHyphen": "소프트 하이픈", "Common.Views.SymbolTableDialog.textSOQuote": "작은 따옴표 (왼쪽)", "Common.Views.SymbolTableDialog.textSpecial": "특수 문자", "Common.Views.SymbolTableDialog.textSymbols": "기호", "Common.Views.SymbolTableDialog.textTitle": "기호", "Common.Views.SymbolTableDialog.textTradeMark": "로고기호", "Common.Views.UserNameDialog.textDontShow": "다시 표시하지 않음", "Common.Views.UserNameDialog.textLabel": "라벨:", "Common.Views.UserNameDialog.textLabelError": "라벨은 비워 둘 수 없습니다.", "DE.Controllers.LeftMenu.leavePageText": "이 문서에 저장되지 않은 모든 변경 사항이 손실됩니다. <br>\"취소\"를 클릭한 다음 \"저장\"을 클릭하여 저장하십시오. 저장되지 않은 모든 변경 사항을 취소하려면 \"확인\"을 클릭하십시오.", "DE.Controllers.LeftMenu.newDocumentTitle": "Unnamed document", "DE.Controllers.LeftMenu.notcriticalErrorTitle": "경고", "DE.Controllers.LeftMenu.requestEditRightsText": "편집 권한 요청 중 ...", "DE.Controllers.LeftMenu.textLoadHistory": "버전 기록 로드 중...", "DE.Controllers.LeftMenu.textNoTextFound": "검색 한 데이터를 찾을 수 없습니다. 검색 옵션을 조정하십시오.", "DE.Controllers.LeftMenu.textReplaceSkipped": "대체가 이루어졌습니다. {0} 건은 건너 뛰었습니다.", "DE.Controllers.LeftMenu.textReplaceSuccess": "검색이 완료되었습니다. 발생 횟수가 대체되었습니다 : {0}", "DE.Controllers.LeftMenu.txtCompatible": "문서가 새 형식으로 저장됩니다. 모든 편집기 기능을 사용할 수 있지만 문서 레이아웃에 영향을 줄 수 있습니다. <br>파일을 이전 버전의 MS Word와 호환되도록 하려면 고급 설정에서 \"호환성\" 옵션을 사용하십시오.", "DE.Controllers.LeftMenu.txtUntitled": "제목없음", "DE.Controllers.LeftMenu.warnDownloadAs": "이 형식으로 저장을 계속하면 텍스트를 제외한 모든 기능이 손실됩니다. 계속 하시겠습니까?", "DE.Controllers.LeftMenu.warnDownloadAsPdf": "{0}(이)가 편집 가능한 형식으로 변환됩니다. 시간이 다소 소요될 수 있습니다. 완성된 문서는 텍스트를 편집할 수 있도록 최적화되므로 특히 원본 파일에 많은 그래픽이 포함된 경우 원본 {0}와/과 완전히 같지 않을 수 있습니다.", "DE.Controllers.LeftMenu.warnDownloadAsRTF": "이 형식으로 계속 저장하면 일부 형식이 손실될 수 있습니다. <br>계속하시겠습니까?", "DE.Controllers.LeftMenu.warnReplaceString": "{0}은/는 대체 필드에 유효한 특수 문자가 아닙니다.", "DE.Controllers.Main.applyChangesTextText": "변경로드 중 ...", "DE.Controllers.Main.applyChangesTitleText": "변경 내용로드 중", "DE.Controllers.Main.convertationTimeoutText": "전환 시간 초과를 초과했습니다.", "DE.Controllers.Main.criticalErrorExtText": "문서 목록으로 돌아가려면 \"OK\"를 누르십시오.", "DE.Controllers.Main.criticalErrorTitle": "오류", "DE.Controllers.Main.downloadErrorText": "다운로드하지 못했습니다.", "DE.Controllers.Main.downloadMergeText": "다운로드 중 ...", "DE.Controllers.Main.downloadMergeTitle": "다운로드 중", "DE.Controllers.Main.downloadTextText": "문서 다운로드 중 ...", "DE.Controllers.Main.downloadTitleText": "문서 다운로드 중", "DE.Controllers.Main.errorAccessDeny": "권한이없는 작업을 수행하려고합니다. <br> 문서 관리자에게 문의하십시오.", "DE.Controllers.Main.errorBadImageUrl": "이미지 URL이 잘못되었습니다.", "DE.Controllers.Main.errorCoAuthoringDisconnect": "서버 연결이 끊어졌습니다. 지금 문서를 편집 할 수 없습니다.", "DE.Controllers.Main.errorComboSeries": "혼합형 차트를 만들려면 최소 2 개의 데이터를 선택합니다.", "DE.Controllers.Main.errorCompare": "공동 편집 시 \"문서 비교\" 기능을 사용할 수 없습니다.", "DE.Controllers.Main.errorConnectToServer": "문서를 저장할 수 없습니다. 연결 설정을 확인하거나 관리자에게 문의하세요. <br>\"확인\" 버튼을 클릭하면 문서를 다운로드하라는 메시지가 표시됩니다.", "DE.Controllers.Main.errorDatabaseConnection": "외부 오류. <br> 데이터베이스 연결 오류입니다. 오류가 계속 발생하면 지원부에 문의하십시오.", "DE.Controllers.Main.errorDataEncrypted": "암호화 변경 사항이 수신되었으며 해독할 수 없습니다.", "DE.Controllers.Main.errorDataRange": "잘못된 참조 대상 입니다.", "DE.Controllers.Main.errorDefaultMessage": "오류 코드 : % 1", "DE.Controllers.Main.errorDirectUrl": "문서에 대한 링크를 확인하십시오. <br>이 링크는 다운로드할 파일에 대한 직접 링크여야 합니다.", "DE.Controllers.Main.errorEditingDownloadas": "문서를 처리하는 동안 오류가 발생했습니다. <br>\"다른 이름으로 다운로드\" 옵션을 사용하여 파일의 백업 사본을 컴퓨터의 하드 드라이브에 저장하십시오.", "DE.Controllers.Main.errorEditingSaveas": "문서를 사용하는 동안 오류가 발생했습니다. <br>파일의 백업 사본을 컴퓨터의 하드 드라이브에 저장하려면 \"다른 이름으로 저장...\" 옵션을 사용하십시오.", "DE.Controllers.Main.errorEmailClient": "이메일 클라이언트를 찾을 수 없습니다.", "DE.Controllers.Main.errorFilePassProtect": "문서가 암호로 보호되어 있습니다.", "DE.Controllers.Main.errorFileSizeExceed": "이 파일은 이 호스트의 크기 제한을 초과합니다.<br> 자세한 내용은 파일 서비스 호스트의 관리자에게 문의하십시오.", "DE.Controllers.Main.errorForceSave": "파일 저장중 문제 발생됨. 컴퓨터 하드 드라이브에 파일을 저장하려면 '로 다운로드' 옵션을 사용 또는 나중에 다시 시도하세요.", "DE.Controllers.Main.errorKeyEncrypt": "알 수없는 키 설명자", "DE.Controllers.Main.errorKeyExpire": "키 설명자가 만료되었습니다", "DE.Controllers.Main.errorLoadingFont": "글꼴 불러오기에 실패하였습니다.<br>문서 시스템 관리자에게 문의하세요.", "DE.Controllers.Main.errorMailMergeLoadFile": "문서의 읽기에 실패했습니다. 다른 파일을 선택하십시오.", "DE.Controllers.Main.errorMailMergeSaveFile": "병합하지 못했습니다.", "DE.Controllers.Main.errorProcessSaveResult": "저장하지 못했습니다.", "DE.Controllers.Main.errorServerVersion": "편집기 버전이 업데이트되었습니다. 페이지가 다시로드되어 변경 사항이 적용됩니다.", "DE.Controllers.Main.errorSessionAbsolute": "문서 편집 세션이 만료되었습니다. 페이지를 새로 고침하십시오.", "DE.Controllers.Main.errorSessionIdle": "문서가 오랫동안 편집되지 않았습니다. 페이지를 새로 고침하십시오.", "DE.Controllers.Main.errorSessionToken": "서버에 대한 연결이 중단되었습니다. 페이지를 새로 고침하십시오.", "DE.Controllers.Main.errorSetPassword": "비밀번호를 재설정할 수 없습니다.", "DE.Controllers.Main.errorStockChart": "잘못된 행 순서. 주식형 차트를 작성하려면 다음 순서로 시트에 데이터를 배치하십시오 : <br> 개시 가격, 최대 가격, 최소 가격, 마감 가격.", "DE.Controllers.Main.errorSubmit": "전송실패", "DE.Controllers.Main.errorToken": "문서 보안 토큰이 올바르게 구성되지 않았습니다. <br> Document Server 관리자에게 문의하십시오.", "DE.Controllers.Main.errorTokenExpire": "문서 보안 토큰이 만료되었습니다. <br> Document Server 관리자에게 문의하십시오.", "DE.Controllers.Main.errorUpdateVersion": "파일 버전이 변경되었습니다. 페이지가 다시로드됩니다.", "DE.Controllers.Main.errorUpdateVersionOnDisconnect": "네트워크 연결이 복원되었습니다. 파일 버전이 변경되었습니다. .<br>계속 작업하기 전에 파일을 다운로드하거나 파일 내용을 복사하여 손실된 항목이 없는지 확인한 다음 이 페이지를 다시 로드해야 합니다.", "DE.Controllers.Main.errorUserDrop": "파일에 지금 액세스 할 수 없습니다.", "DE.Controllers.Main.errorUsersExceed": "가격 책정 계획에서 허용 한 사용자 수가 초과되었습니다", "DE.Controllers.Main.errorViewerDisconnect": "연결이 끊어졌습니다. 문서를 볼 수는 <br> <br>하지만 연결이 복원 될 때까지 다운로드하거나 인쇄 할 수 없습니다.", "DE.Controllers.Main.leavePageText": "이 문서에 변경 사항을 저장하지 않았습니다. \"이 페이지에 유지\"를 클릭한 다음 \"저장\"을 클릭하여 저장합니다. 저장하지 않은 모든 변경 사항을 취소하려면 \"이 페이지에서 나가기\"를 클릭하십시오.", "DE.Controllers.Main.leavePageTextOnClose": "이 문서에 저장되지 않은 모든 변경 사항이 손실됩니다. <br>\"취소\"를 클릭한 다음 \"저장\"을 클릭하여 저장하십시오. 저장되지 않은 모든 변경 사항을 취소하려면 \"확인\"을 클릭하십시오.", "DE.Controllers.Main.loadFontsTextText": "데이터로드 중 ...", "DE.Controllers.Main.loadFontsTitleText": "데이터로드 중", "DE.Controllers.Main.loadFontTextText": "데이터로드 중 ...", "DE.Controllers.Main.loadFontTitleText": "데이터로드 중", "DE.Controllers.Main.loadImagesTextText": "이미지로드 중 ...", "DE.Controllers.Main.loadImagesTitleText": "이미지로드 중", "DE.Controllers.Main.loadImageTextText": "이미지로드 중 ...", "DE.Controllers.Main.loadImageTitleText": "이미지로드 중", "DE.Controllers.Main.loadingDocumentTextText": "문서로드 중 ...", "DE.Controllers.Main.loadingDocumentTitleText": "문서로드 중", "DE.Controllers.Main.mailMergeLoadFileText": "데이터 소스로드 중 ...", "DE.Controllers.Main.mailMergeLoadFileTitle": "데이터 소스로드 중", "DE.Controllers.Main.notcriticalErrorTitle": "경고", "DE.Controllers.Main.openErrorText": "파일을 여는 동안 오류가 발생했습니다.", "DE.Controllers.Main.openTextText": "문서 열기 중 ...", "DE.Controllers.Main.openTitleText": "문서 열기", "DE.Controllers.Main.printTextText": "문서 인쇄 중 ...", "DE.Controllers.Main.printTitleText": "문서 인쇄 중", "DE.Controllers.Main.reloadButtonText": "페이지 새로 고침", "DE.Controllers.Main.requestEditFailedMessageText": "누군가이 문서를 지금 편집하고 있습니다. 나중에 다시 시도하십시오.", "DE.Controllers.Main.requestEditFailedTitleText": "액세스가 거부되었습니다", "DE.Controllers.Main.saveErrorText": "파일을 저장하는 동안 오류가 발생했습니다.", "DE.Controllers.Main.saveErrorTextDesktop": "이 파일을 저장하거나 생성할 수 없습니다. <br>가능한 이유는 다음과 같습니다. <br> 1. 파일이 읽기 전용입니다. <br> 2. 다른 사용자가 파일을 편집 중입니다. <br> 3. 디스크가 가득 찼거나 손상되었습니다.", "DE.Controllers.Main.saveTextText": "문서 저장 중 ...", "DE.Controllers.Main.saveTitleText": "문서 저장 중", "DE.Controllers.Main.scriptLoadError": "연결 속도가 느려, 일부 요소들이 로드되지 않았습니다. 페이지를 다시 새로 고침해주세요.", "DE.Controllers.Main.sendMergeText": "Sending Merge ...", "DE.Controllers.Main.sendMergeTitle": "Sending Merge", "DE.Controllers.Main.splitDividerErrorText": "행 수는 % 1의 제수 여야합니다.", "DE.Controllers.Main.splitMaxColsErrorText": "열 수가 % 1보다 작아야합니다.", "DE.Controllers.Main.splitMaxRowsErrorText": "행 수가 % 1보다 적어야합니다.", "DE.Controllers.Main.textAnonymous": "익명", "DE.Controllers.Main.textApplyAll": "모든 방정식에 적용", "DE.Controllers.Main.textBuyNow": "웹 사이트 방문", "DE.Controllers.Main.textChangesSaved": "모든 변경 사항이 저장되었습니다", "DE.Controllers.Main.textClose": "닫기", "DE.Controllers.Main.textCloseTip": "도움말을 닫으려면 클릭하십시오", "DE.Controllers.Main.textContactUs": "영업 담당자에게 문의", "DE.Controllers.Main.textConvertEquation": "방정식은 더 이상 지원되지 않는 이전 버전의 방정식 편집기를 사용하여 생성되었습니다. 편집하려면 수식을 Office Math ML 형식으로 변환하세요. <br>지금 변환하시겠습니까?", "DE.Controllers.Main.textCustomLoader": "라이센스 조건에 따라 교체할 권한이 없습니다. <br>견적은 당사 영업부에 문의해 주십시오.", "DE.Controllers.Main.textDisconnect": "네트워크 연결 끊김", "DE.Controllers.Main.textGuest": "게스트", "DE.Controllers.Main.textHasMacros": "파일에 자동 매크로가 포함되어 있습니다. <br> 매크로를 실행 하시겠습니까?", "DE.Controllers.Main.textLearnMore": "자세히", "DE.Controllers.Main.textLoadingDocument": "문서로드 중", "DE.Controllers.Main.textLongName": "128자 미만의 이름을 입력하세요.", "DE.Controllers.Main.textNoLicenseTitle": "ONLYOFFICE 연결 제한", "DE.Controllers.Main.textPaidFeature": "유료기능", "DE.Controllers.Main.textReconnect": "연결이 복원되었습니다", "DE.Controllers.Main.textRemember": "모든 파일에 대한 선택 사항을 기억하기", "DE.Controllers.Main.textRenameError": "사용자 이름은 비워둘 수 없습니다.", "DE.Controllers.Main.textRenameLabel": "협업에 사용할 이름을 입력합니다", "DE.Controllers.Main.textShape": "도형", "DE.Controllers.Main.textStrict": "엄격 모드", "DE.Controllers.Main.textText": "본문", "DE.Controllers.Main.textTryUndoRedo": "Fast co-editing mode 에서는 실행 취소 / 다시 실행 기능이 비활성화됩니다. <br>\"Strict co-editing mode \"버튼을 클릭하면 엄격한 공동 편집 모드로 전환되어 파일을 편집 할 수 있습니다. 다른 사용자가 방해를해서 저장 한 후에 만 ​​변경 사항을 보내면됩니다. 편집자 고급 설정을 사용하여 공동 편집 모드간에 전환 할 수 있습니다. ", "DE.Controllers.Main.textTryUndoRedoWarn": "빠른 공동 편집 모드에서 실행 취소 / 다시 실행 기능을 사용할 수 없습니다.", "DE.Controllers.Main.titleLicenseExp": "라이센스 만료", "DE.Controllers.Main.titleServerVersion": "편집기가 업데이트되었습니다.", "DE.Controllers.Main.titleUpdateVersion": "버전이 변경되었습니다.", "DE.Controllers.Main.txtAbove": "위", "DE.Controllers.Main.txtArt": "여기에 귀하의 텍스트", "DE.Controllers.Main.txtBasicShapes": "기본 도형", "DE.Controllers.Main.txtBelow": "아래", "DE.Controllers.Main.txtBookmarkError": "오류! 즐겨찾기가 정의되지 않음", "DE.Controllers.Main.txtButtons": "Buttons", "DE.Controllers.Main.txtCallouts": "설명선", "DE.Controllers.Main.txtCharts": "Charts", "DE.Controllers.Main.txtChoose": "아이템 선택", "DE.Controllers.Main.txtClickToLoad": "이미지를 읽으려면 여기를 클릭하세요", "DE.Controllers.Main.txtCurrentDocument": "현재 문서", "DE.Controllers.Main.txtDiagramTitle": "차트 제목", "DE.Controllers.Main.txtEditingMode": "편집 모드 설정 ...", "DE.Controllers.Main.txtEndOfFormula": "수식의 예기치 않은 종료", "DE.Controllers.Main.txtEnterDate": "미주 날짜", "DE.Controllers.Main.txtErrorLoadHistory": "기록로드 실패", "DE.Controllers.Main.txtEvenPage": "짝수 페이지", "DE.Controllers.Main.txtFiguredArrows": "블록 화살표", "DE.Controllers.Main.txtFirstPage": "첫 페이지", "DE.Controllers.Main.txtFooter": "꼬리말", "DE.Controllers.Main.txtFormulaNotInTable": "\n표에없는 수식", "DE.Controllers.Main.txtHeader": "머리글", "DE.Controllers.Main.txtHyperlink": "하이퍼 링크", "DE.Controllers.Main.txtIndTooLarge": "색인이 너무 큽니다", "DE.Controllers.Main.txtLines": "Lines", "DE.Controllers.Main.txtMainDocOnly": "오류! 주요 문서만 해당됨.", "DE.Controllers.Main.txtMath": "수학", "DE.Controllers.Main.txtMissArg": "인수가 없습니다", "DE.Controllers.Main.txtMissOperator": "연산자가 없습니다", "DE.Controllers.Main.txtNeedSynchronize": "업데이트가 있습니다.", "DE.Controllers.Main.txtNone": "없음", "DE.Controllers.Main.txtNoTableOfContents": "이 문서에는 제목이 없습니다. 목차에 나타나도록 제목 스타일을 텍스트에 적용합니다.", "DE.Controllers.Main.txtNoTableOfFigures": "목차 항목을 찾을 수 없습니다.", "DE.Controllers.Main.txtNoText": "오류! 문서에 지정된 스타일의 텍스트가 없습니다.", "DE.Controllers.Main.txtNotInTable": "표에 없음", "DE.Controllers.Main.txtNotValidBookmark": "오류! 북마크 링크 형식이 잘못되었습니다!", "DE.Controllers.Main.txtOddPage": "홀수 페이지", "DE.Controllers.Main.txtOnPage": "페이지상", "DE.Controllers.Main.txtRectangles": "사각형", "DE.Controllers.Main.txtSameAsPrev": "이전과 동일", "DE.Controllers.Main.txtSection": "섹션", "DE.Controllers.Main.txtSeries": "Series", "DE.Controllers.Main.txtShape_accentBorderCallout1": "설명선 1 (테두리 강조)", "DE.Controllers.Main.txtShape_accentBorderCallout2": "설명선 2 (테두리 강조)", "DE.Controllers.Main.txtShape_accentBorderCallout3": "설명선 3 (테두리 강조)", "DE.Controllers.Main.txtShape_accentCallout1": "설명선 1 (강조선)", "DE.Controllers.Main.txtShape_accentCallout2": "설명선 2 (강조선)", "DE.Controllers.Main.txtShape_accentCallout3": "설명선 3 (강조선)", "DE.Controllers.Main.txtShape_actionButtonBackPrevious": "되돌리기 또는 이전 버튼", "DE.Controllers.Main.txtShape_actionButtonBeginning": "시작 버튼", "DE.Controllers.Main.txtShape_actionButtonBlank": "공백 버튼", "DE.Controllers.Main.txtShape_actionButtonDocument": "문서버튼", "DE.Controllers.Main.txtShape_actionButtonEnd": "종료버튼", "DE.Controllers.Main.txtShape_actionButtonForwardNext": "다음 버튼", "DE.Controllers.Main.txtShape_actionButtonHelp": "도움말 버튼", "DE.Controllers.Main.txtShape_actionButtonHome": "홈 버튼", "DE.Controllers.Main.txtShape_actionButtonInformation": "상세정보 버튼", "DE.Controllers.Main.txtShape_actionButtonMovie": "동영상 버튼", "DE.Controllers.Main.txtShape_actionButtonReturn": "뒤로가기 버튼", "DE.Controllers.Main.txtShape_actionButtonSound": "소리 버튼", "DE.Controllers.Main.txtShape_arc": "원호", "DE.Controllers.Main.txtShape_bentArrow": "화살표: 굽음", "DE.Controllers.Main.txtShape_bentConnector5": "연결선: 꺾임", "DE.Controllers.Main.txtShape_bentConnector5WithArrow": "연결선: 꺾인 화살표", "DE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "연결선: 꺾인 양쪽 화살표", "DE.Controllers.Main.txtShape_bentUpArrow": "화살표: 위로 굽음", "DE.Controllers.Main.txtShape_bevel": "액자", "DE.Controllers.Main.txtShape_blockArc": "막힌 원호", "DE.Controllers.Main.txtShape_borderCallout1": "설명선 1", "DE.Controllers.Main.txtShape_borderCallout2": "설명선 2", "DE.Controllers.Main.txtShape_borderCallout3": "설명선 3", "DE.Controllers.Main.txtShape_bracePair": "양쪽 중괄호", "DE.Controllers.Main.txtShape_callout1": "설명선 1 (테두리없음)", "DE.Controllers.Main.txtShape_callout2": "설명선 2 (테두리없음)", "DE.Controllers.Main.txtShape_callout3": "설명선 3 (테두리없음)", "DE.Controllers.Main.txtShape_can": "원통형", "DE.Controllers.Main.txtShape_chevron": "쉐브론", "DE.Controllers.Main.txtShape_chord": "현", "DE.Controllers.Main.txtShape_circularArrow": "화살표: 원형", "DE.Controllers.Main.txtShape_cloud": "클라우드", "DE.Controllers.Main.txtShape_cloudCallout": "생각풍선: 구름 모양", "DE.Controllers.Main.txtShape_corner": "L도형", "DE.Controllers.Main.txtShape_cube": "정육면체", "DE.Controllers.Main.txtShape_curvedConnector3": "연결선: 구부러짐", "DE.Controllers.Main.txtShape_curvedConnector3WithArrow": "연결선: 구부러진 화살표", "DE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "연결선: 구부러진 양쪽 화살표", "DE.Controllers.Main.txtShape_curvedDownArrow": "화살표: 아래로 구불어 짐", "DE.Controllers.Main.txtShape_curvedLeftArrow": "화살표: 왼쪽으로 구불어 짐", "DE.Controllers.Main.txtShape_curvedRightArrow": "화살표: 오른쪽으로 구불어 짐", "DE.Controllers.Main.txtShape_curvedUpArrow": "화살표: 위로 구불어 짐", "DE.Controllers.Main.txtShape_decagon": "십각형", "DE.Controllers.Main.txtShape_diagStripe": "대각선 줄무늬", "DE.Controllers.Main.txtShape_diamond": "다이아몬드", "DE.Controllers.Main.txtShape_dodecagon": "12각형", "DE.Controllers.Main.txtShape_donut": "도넛", "DE.Controllers.Main.txtShape_doubleWave": "이중 물결", "DE.Controllers.Main.txtShape_downArrow": "화살표: 아래쪽", "DE.Controllers.Main.txtShape_downArrowCallout": "설명선: 아래쪽 화살표", "DE.Controllers.Main.txtShape_ellipse": "타원형", "DE.Controllers.Main.txtShape_ellipseRibbon": "리본: 아래로 구불어지고 기울어짐 ", "DE.Controllers.Main.txtShape_ellipseRibbon2": "리본: 위로 구불어지고 기울어짐 ", "DE.Controllers.Main.txtShape_flowChartAlternateProcess": "순서도: 대체 프로세스", "DE.Controllers.Main.txtShape_flowChartCollate": "순서도: 일치", "DE.Controllers.Main.txtShape_flowChartConnector": "순서도: 연결 연산자", "DE.Controllers.Main.txtShape_flowChartDecision": "순서도: 결정", "DE.Controllers.Main.txtShape_flowChartDelay": "순서도: 지연", "DE.Controllers.Main.txtShape_flowChartDisplay": "순서도: 표시", "DE.Controllers.Main.txtShape_flowChartDocument": "순서도: 문서", "DE.Controllers.Main.txtShape_flowChartExtract": "순서도: 추출", "DE.Controllers.Main.txtShape_flowChartInputOutput": "순서도: 데이터", "DE.Controllers.Main.txtShape_flowChartInternalStorage": "순서도: 내부 스토리지", "DE.Controllers.Main.txtShape_flowChartMagneticDisk": "순서도: 디스크", "DE.Controllers.Main.txtShape_flowChartMagneticDrum": "순서도: 스토리지에 직접 접근", "DE.Controllers.Main.txtShape_flowChartMagneticTape": "순서도: 순차 접근 스토리지", "DE.Controllers.Main.txtShape_flowChartManualInput": "순서도: 수동 입력", "DE.Controllers.Main.txtShape_flowChartManualOperation": "순서도: 수동조작", "DE.Controllers.Main.txtShape_flowChartMerge": "순서도: 병합", "DE.Controllers.Main.txtShape_flowChartMultidocument": "순서도: 다중문서", "DE.Controllers.Main.txtShape_flowChartOffpageConnector": "순서도: 페이지 외부 커넥터", "DE.Controllers.Main.txtShape_flowChartOnlineStorage": "순서도: 저장된 데이터", "DE.Controllers.Main.txtShape_flowChartOr": "순서도: 또는", "DE.Controllers.Main.txtShape_flowChartPredefinedProcess": "순서도: 미리 정의된 흐름", "DE.Controllers.Main.txtShape_flowChartPreparation": "순서도: 준비", "DE.Controllers.Main.txtShape_flowChartProcess": "순서도: 프로세스", "DE.Controllers.Main.txtShape_flowChartPunchedCard": "순서도: 카드", "DE.Controllers.Main.txtShape_flowChartPunchedTape": "순서도: 천공된 종이 테이프", "DE.Controllers.Main.txtShape_flowChartSort": "순서도: 정렬", "DE.Controllers.Main.txtShape_flowChartSummingJunction": "순서도: 합계 노드", "DE.Controllers.Main.txtShape_flowChartTerminator": "순서도: 종료", "DE.Controllers.Main.txtShape_foldedCorner": "접힌 모서리", "DE.Controllers.Main.txtShape_frame": "프레임", "DE.Controllers.Main.txtShape_halfFrame": "1/2 액자", "DE.Controllers.Main.txtShape_heart": "하트모양", "DE.Controllers.Main.txtShape_heptagon": "칠각형", "DE.Controllers.Main.txtShape_hexagon": "육각형", "DE.Controllers.Main.txtShape_homePlate": "오각형", "DE.Controllers.Main.txtShape_horizontalScroll": "두루마리 모양: 가로로 말림", "DE.Controllers.Main.txtShape_irregularSeal1": "폭발: 8pt", "DE.Controllers.Main.txtShape_irregularSeal2": "폭발: 14pt", "DE.Controllers.Main.txtShape_leftArrow": "화살표: 왼쪽", "DE.Controllers.Main.txtShape_leftArrowCallout": "설명선: 왼쪽 화살표", "DE.Controllers.Main.txtShape_leftBrace": "왼쪽 중괄호", "DE.Controllers.Main.txtShape_leftBracket": "왼쪽 대괄호", "DE.Controllers.Main.txtShape_leftRightArrow": "선 화살표 : 양방향", "DE.Controllers.Main.txtShape_leftRightArrowCallout": "설명선: 왼쪽 및 오른쪽 화살표", "DE.Controllers.Main.txtShape_leftRightUpArrow": "화살표: 왼쪽/위쪽", "DE.Controllers.Main.txtShape_leftUpArrow": "화살표: 왼쪽", "DE.Controllers.Main.txtShape_lightningBolt": "번개", "DE.Controllers.Main.txtShape_line": "선", "DE.Controllers.Main.txtShape_lineWithArrow": "화살표", "DE.Controllers.Main.txtShape_lineWithTwoArrows": "선 화살표: 양방향", "DE.Controllers.Main.txtShape_mathDivide": "분할", "DE.Controllers.Main.txtShape_mathEqual": "등호", "DE.Controllers.Main.txtShape_mathMinus": "마이너스", "DE.Controllers.Main.txtShape_mathMultiply": "곱셈", "DE.Controllers.Main.txtShape_mathNotEqual": "부등호", "DE.Controllers.Main.txtShape_mathPlus": "덧셈", "DE.Controllers.Main.txtShape_moon": "달모양", "DE.Controllers.Main.txtShape_noSmoking": "\"없음\" 기호", "DE.Controllers.Main.txtShape_notchedRightArrow": "화살표: 오른쪽 톱니 모양", "DE.Controllers.Main.txtShape_octagon": "팔각형", "DE.Controllers.Main.txtShape_parallelogram": "평행 사변형", "DE.Controllers.Main.txtShape_pentagon": "오각형", "DE.Controllers.Main.txtShape_pie": "부분 원형", "DE.Controllers.Main.txtShape_plaque": "배지", "DE.Controllers.Main.txtShape_plus": "덧셈", "DE.Controllers.Main.txtShape_polyline1": "자유형: 자유 곡선", "DE.Controllers.Main.txtShape_polyline2": "자유형: 도형", "DE.Controllers.Main.txtShape_quadArrow": "화살표: 왼쪽/오른쪽/위쪽/아래쪽", "DE.Controllers.Main.txtShape_quadArrowCallout": "설명선: 왼쪽/오른쪽/위쪽/아래쪽", "DE.Controllers.Main.txtShape_rect": "사각형", "DE.Controllers.Main.txtShape_ribbon": "리본: 아래로 기울어짐", "DE.Controllers.Main.txtShape_ribbon2": "리본: 위로 구불어짐", "DE.Controllers.Main.txtShape_rightArrow": "화살표: 오른쪽", "DE.Controllers.Main.txtShape_rightArrowCallout": "설명선: 오른쪽 화살표", "DE.Controllers.Main.txtShape_rightBrace": "오른쪽 중괄호", "DE.Controllers.Main.txtShape_rightBracket": "오른쪽 대괄호", "DE.Controllers.Main.txtShape_round1Rect": "사각형: 둥근 한쪽 모서리", "DE.Controllers.Main.txtShape_round2DiagRect": "사각형: 둥근 대각선 방향 모서리", "DE.Controllers.Main.txtShape_round2SameRect": "사각형: 둥근 위쪽 모서리", "DE.Controllers.Main.txtShape_roundRect": "사각형: 둥근 모서리", "DE.Controllers.Main.txtShape_rtTriangle": "직각 삼각형", "DE.Controllers.Main.txtShape_smileyFace": "웃는 얼굴", "DE.Controllers.Main.txtShape_snip1Rect": "사각형: 잘린 한쪽 모서리", "DE.Controllers.Main.txtShape_snip2DiagRect": "사각형: 잘린 대각선 방향 모서리", "DE.Controllers.Main.txtShape_snip2SameRect": "사각형: 잘린 양쪽 모서리", "DE.Controllers.Main.txtShape_snipRoundRect": "사각형: 한쪽은 둥글고 한쪽은 짤린 모서리", "DE.Controllers.Main.txtShape_spline": "곡선", "DE.Controllers.Main.txtShape_star10": "별: 꼭짓점 10개", "DE.Controllers.Main.txtShape_star12": "별: 꼭짓점 12개", "DE.Controllers.Main.txtShape_star16": "별: 꼭짓점 16개", "DE.Controllers.Main.txtShape_star24": "별: 꼭짓점 24개", "DE.Controllers.Main.txtShape_star32": "별: 꼭짓점 32개", "DE.Controllers.Main.txtShape_star4": "별: 꼭짓점 4개", "DE.Controllers.Main.txtShape_star5": "별: 꼭짓점 5개", "DE.Controllers.Main.txtShape_star6": "별: 꼭짓점 6개", "DE.Controllers.Main.txtShape_star7": "별: 꼭짓점 7개", "DE.Controllers.Main.txtShape_star8": "별: 꼭짓점 8개", "DE.Controllers.Main.txtShape_stripedRightArrow": "줄무늬 오른쪽 화살표", "DE.Controllers.Main.txtShape_sun": "해모양", "DE.Controllers.Main.txtShape_teardrop": "눈물 방울", "DE.Controllers.Main.txtShape_textRect": "텍스트 상자", "DE.Controllers.Main.txtShape_trapezoid": "사다리꼴", "DE.Controllers.Main.txtShape_triangle": "삼각형", "DE.Controllers.Main.txtShape_upArrow": "화살표: 위쪽", "DE.Controllers.Main.txtShape_upArrowCallout": "설명선: 위쪽 화살표", "DE.Controllers.Main.txtShape_upDownArrow": "화살표: 위쪽/아래쪽", "DE.Controllers.Main.txtShape_uturnArrow": "화살표: U자형", "DE.Controllers.Main.txtShape_verticalScroll": "두루마리 모양: 세로로 말림", "DE.Controllers.Main.txtShape_wave": "물결", "DE.Controllers.Main.txtShape_wedgeEllipseCallout": "말풍선: 타원형", "DE.Controllers.Main.txtShape_wedgeRectCallout": "말풍선: 사각형", "DE.Controllers.Main.txtShape_wedgeRoundRectCallout": "말풍선: 모서리가 둥근 사각형", "DE.Controllers.Main.txtStarsRibbons": "별 및 현수막", "DE.Controllers.Main.txtStyle_Caption": "참조", "DE.Controllers.Main.txtStyle_endnote_text": "미주 텍스트", "DE.Controllers.Main.txtStyle_footnote_text": "꼬리말 글", "DE.Controllers.Main.txtStyle_Heading_1": "제목 1", "DE.Controllers.Main.txtStyle_Heading_2": "제목 2", "DE.Controllers.Main.txtStyle_Heading_3": "제목 3", "DE.Controllers.Main.txtStyle_Heading_4": "제목 4", "DE.Controllers.Main.txtStyle_Heading_5": "제목 5", "DE.Controllers.Main.txtStyle_Heading_6": "제목 6", "DE.Controllers.Main.txtStyle_Heading_7": "제목 7", "DE.Controllers.Main.txtStyle_Heading_8": "제목 8", "DE.Controllers.Main.txtStyle_Heading_9": "제목 9", "DE.Controllers.Main.txtStyle_Intense_Quote": "강렬한 견적", "DE.Controllers.Main.txtStyle_List_Paragraph": "단락 목록", "DE.Controllers.Main.txtStyle_No_Spacing": "간격 없음", "DE.Controllers.Main.txtStyle_Normal": "표준", "DE.Controllers.Main.txtStyle_Quote": "Quote", "DE.Controllers.Main.txtStyle_Subtitle": "자막", "DE.Controllers.Main.txtStyle_Title": "제목", "DE.Controllers.Main.txtSyntaxError": "구문 오류", "DE.Controllers.Main.txtTableInd": "테이블 인덱스는 0일 수 없습니다.", "DE.Controllers.Main.txtTableOfContents": "콘텐츠 테이블", "DE.Controllers.Main.txtTableOfFigures": "목차", "DE.Controllers.Main.txtTOCHeading": "TOC 제목", "DE.Controllers.Main.txtTooLarge": "숫자가 너무 커서 형식을 지정할 수 없습니다", "DE.Controllers.Main.txtTypeEquation": "여기에 방정식을 입력합니다.", "DE.Controllers.Main.txtUndefBookmark": "정의되지 않은 책갈피", "DE.Controllers.Main.txtXAxis": "X 축", "DE.Controllers.Main.txtYAxis": "Y 축", "DE.Controllers.Main.txtZeroDivide": "0으로 나누기", "DE.Controllers.Main.unknownErrorText": "알 수없는 오류.", "DE.Controllers.Main.unsupportedBrowserErrorText": "사용중인 브라우저가 지원되지 않습니다.", "DE.Controllers.Main.uploadDocExtMessage": "알 수 없는 파일 형식입니다.", "DE.Controllers.Main.uploadDocFileCountMessage": "업로드 된 문서가 없습니다.", "DE.Controllers.Main.uploadDocSizeMessage": "최대 문서 크기 제한을 초과했습니다.", "DE.Controllers.Main.uploadImageExtMessage": "알 수없는 이미지 형식입니다.", "DE.Controllers.Main.uploadImageFileCountMessage": "이미지가 업로드되지 않았습니다.", "DE.Controllers.Main.uploadImageSizeMessage": "이미지 크기 제한을 초과했습니다.", "DE.Controllers.Main.uploadImageTextText": "이미지 업로드 중 ...", "DE.Controllers.Main.uploadImageTitleText": "이미지 업로드 중", "DE.Controllers.Main.waitText": "잠시만 기다려주세요...", "DE.Controllers.Main.warnBrowserIE9": "응용 프로그램의 기능이 IE9에서 부족합니다. IE10 이상을 사용하십시오.", "DE.Controllers.Main.warnBrowserZoom": "브라우저의 현재 확대/축소 설정이 완전히 지원되지 않습니다. Ctrl + 0을 눌러 기본 확대 / 축소로 재설정하십시오.", "DE.Controllers.Main.warnLicenseExceeded": "귀하의 시스템은 동시에 연결을 편집하는 %1명의 편집자에게 도달했습니다. 이 문서는 보기 모드에서만 열 수 있습니다. <br> 자세한 내용은 관리자에게 문의하십시오.", "DE.Controllers.Main.warnLicenseExp": "귀하의 라이센스가 만료되었습니다. <br> 라이센스를 업데이트하고 페이지를 새로 고침하십시오.", "DE.Controllers.Main.warnLicenseLimitedNoAccess": "라이센스가 만료되었습니다.<br>더 이상 파일을 수정할 수 있는 권한이 없습니다.<br> 관리자에게 문의하세요.", "DE.Controllers.Main.warnLicenseLimitedRenewed": "라이센스를 갱신해야합니다. <br> 문서 편집 기능에 대한 액세스가 제한되어 있습니다. <br> 전체 액세스 권한을 얻으려면 관리자에게 문의하십시오", "DE.Controllers.Main.warnLicenseUsersExceeded": "편집자 사용자 한도인 %1명에 도달했습니다. 자세한 내용은 관리자에게 문의하십시오.", "DE.Controllers.Main.warnNoLicense": "이 버전의 %1 편집자에게는 문서 서버에 대한 동시 연결에 대한 특정 제한 사항이 있습니다. <br> 더 많은 정보가 필요하면 현재 라이센스를 업그레이드하거나 상용 소프트웨어를 구입하십시오.", "DE.Controllers.Main.warnNoLicenseUsers": "ONLYOFFICE 편집자의이 버전은 동시 사용자에게 일정한 제한이 있습니다. <br> 더 필요한 것이 있으면 현재 라이센스를 업그레이드하거나 상용 라이센스를 구입하십시오.", "DE.Controllers.Main.warnProcessRightsChange": "파일 편집 권한이 거부되었습니다.", "DE.Controllers.Navigation.txtBeginning": "문서의 시작", "DE.Controllers.Navigation.txtGotoBeginning": "문서의 시작점으로 이동", "DE.Controllers.Statusbar.textDisconnect": "<b>연결이 끊어졌습니다</b><br>연결을 시도하는 중입니다.", "DE.Controllers.Statusbar.textHasChanges": "새로운 변경 내역이 조회되었습니다", "DE.Controllers.Statusbar.textSetTrackChanges": "변경 내용 추적 모드 사용중", "DE.Controllers.Statusbar.textTrackChanges": "변경 내용 추적 기능이 활성화된 상태에서 문서가 열립니다.", "DE.Controllers.Statusbar.tipReview": "변경 내용 추적", "DE.Controllers.Statusbar.zoomText": "확대/축소 {0} %", "DE.Controllers.Toolbar.confirmAddFontName": "저장하려는 글꼴을 현재 장치에서 사용할 수 없습니다. <br> 시스템 글꼴 중 하나를 사용하여 텍스트 스타일을 표시하고 저장된 글꼴을 사용할 때 사용할 수 있습니다. <br> 계속 하시겠습니까? ", "DE.Controllers.Toolbar.dataUrl": "데이터 URL 붙여넣기", "DE.Controllers.Toolbar.notcriticalErrorTitle": "경고", "DE.Controllers.Toolbar.textAccent": "Accents", "DE.Controllers.Toolbar.textBracket": "대괄호", "DE.Controllers.Toolbar.textEmptyImgUrl": "이미지 URL을 지정해야합니다.", "DE.Controllers.Toolbar.textEmptyMMergeUrl": "URL을 지정해야 합니다.", "DE.Controllers.Toolbar.textFontSizeErr": "입력 한 값이 잘못되었습니다. <br> 1 ~ 300 사이의 숫자 값을 입력하십시오.", "DE.Controllers.Toolbar.textFraction": "Fractions", "DE.Controllers.Toolbar.textFunction": "Functions", "DE.Controllers.Toolbar.textGroup": "그룹", "DE.Controllers.Toolbar.textInsert": "삽입", "DE.Controllers.Toolbar.textIntegral": "Integrals", "DE.Controllers.Toolbar.textLargeOperator": "Large Operators", "DE.Controllers.Toolbar.textLimitAndLog": "한계 및 로그 수", "DE.Controllers.Toolbar.textMatrix": "Matrices", "DE.Controllers.Toolbar.textOperator": "연산자", "DE.Controllers.Toolbar.textRadical": "Radicals", "DE.Controllers.Toolbar.textRecentlyUsed": "최근 사용된", "DE.Controllers.Toolbar.textScript": "스크립트", "DE.Controllers.Toolbar.textSymbols": "Symbols", "DE.Controllers.Toolbar.textTabForms": "폼", "DE.Controllers.Toolbar.textWarning": "경고", "DE.Controllers.Toolbar.txtAccent_Accent": "Acute", "DE.Controllers.Toolbar.txtAccent_ArrowD": "오른쪽 위 왼쪽 화살표", "DE.Controllers.Toolbar.txtAccent_ArrowL": "왼쪽 위 화살표", "DE.Controllers.Toolbar.txtAccent_ArrowR": "오른쪽 위 화살표 위", "DE.Controllers.Toolbar.txtAccent_Bar": "Bar", "DE.Controllers.Toolbar.txtAccent_BarBot": "Underbar", "DE.Controllers.Toolbar.txtAccent_BarTop": "Overbar", "DE.Controllers.Toolbar.txtAccent_BorderBox": "상자가있는 수식 (자리 표시 자 포함)", "DE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "상자화 된 수식 (예)", "DE.Controllers.Toolbar.txtAccent_Check": "확인", "DE.Controllers.Toolbar.txtAccent_CurveBracketBot": "아래쪽 중괄호", "DE.Controllers.Toolbar.txtAccent_CurveBracketTop": "위쪽 중괄호", "DE.Controllers.Toolbar.txtAccent_Custom_1": "벡터 A", "DE.Controllers.Toolbar.txtAccent_Custom_2": "ABC With Overbar", "DE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y Overbar", "DE.Controllers.Toolbar.txtAccent_DDDot": "트리플 도트", "DE.Controllers.Toolbar.txtAccent_DDot": "Double Dot", "DE.Controllers.Toolbar.txtAccent_Dot": "Dot", "DE.Controllers.Toolbar.txtAccent_DoubleBar": "Double Overbar", "DE.Controllers.Toolbar.txtAccent_Grave": "Grave", "DE.Controllers.Toolbar.txtAccent_GroupBot": "아래의 문자 그룹화", "DE.Controllers.Toolbar.txtAccent_GroupTop": "위의 그룹화 문자", "DE.Controllers.Toolbar.txtAccent_HarpoonL": "Leftwards Harpoon Above", "DE.Controllers.Toolbar.txtAccent_HarpoonR": "Rightwards Harpoon Above", "DE.Controllers.Toolbar.txtAccent_Hat": "Hat", "DE.Controllers.Toolbar.txtAccent_Smile": "Breve", "DE.Controllers.Toolbar.txtAccent_Tilde": "물결표", "DE.Controllers.Toolbar.txtBracket_Angle": "대괄호", "DE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "구분 기호가있는 대괄호", "DE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "구분 기호가있는 대괄호", "DE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "단일 대괄호", "DE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "단일 브래킷", "DE.Controllers.Toolbar.txtBracket_Curve": "대괄호", "DE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "구분 기호가있는 대괄호", "DE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "단일 대괄호", "DE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "단일 브래킷", "DE.Controllers.Toolbar.txtBracket_Custom_1": "사례 (두 조건)", "DE.Controllers.Toolbar.txtBracket_Custom_2": "사례 (세 조건)", "DE.Controllers.Toolbar.txtBracket_Custom_3": "Stack Object", "DE.Controllers.Toolbar.txtBracket_Custom_4": "Stack Object", "DE.Controllers.Toolbar.txtBracket_Custom_5": "사례 사례", "DE.Controllers.Toolbar.txtBracket_Custom_6": "Binomial Coefficient", "DE.Controllers.Toolbar.txtBracket_Custom_7": "Binomial Coefficient", "DE.Controllers.Toolbar.txtBracket_Line": "대괄호", "DE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "단일 대괄호", "DE.Controllers.Toolbar.txtBracket_Line_OpenNone": "단일 브래킷", "DE.Controllers.Toolbar.txtBracket_LineDouble": "대괄호", "DE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "단일 대괄호", "DE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "단일 대괄호", "DE.Controllers.Toolbar.txtBracket_LowLim": "대괄호", "DE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "단일 대괄호", "DE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "단일 브래킷", "DE.Controllers.Toolbar.txtBracket_Round": "Bracket", "DE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "구분 기호가있는 대괄호", "DE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "단일 대괄호", "DE.Controllers.Toolbar.txtBracket_Round_OpenNone": "단일 브래킷", "DE.Controllers.Toolbar.txtBracket_Square": "대괄호", "DE.Controllers.Toolbar.txtBracket_Square_CloseClose": "대괄호", "DE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "대괄호", "DE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "단일 대괄호", "DE.Controllers.Toolbar.txtBracket_Square_OpenNone": "단일 대괄호", "DE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "대괄호", "DE.Controllers.Toolbar.txtBracket_SquareDouble": "대괄호", "DE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "단일 대괄호", "DE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "단일 대괄호", "DE.Controllers.Toolbar.txtBracket_UppLim": "대괄호", "DE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "단일 대괄호", "DE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "단일 대괄호", "DE.Controllers.Toolbar.txtFractionDiagonal": "Skewed Fraction", "DE.Controllers.Toolbar.txtFractionDifferential_1": "Differential", "DE.Controllers.Toolbar.txtFractionDifferential_2": "Differential", "DE.Controllers.Toolbar.txtFractionDifferential_3": "Differential", "DE.Controllers.Toolbar.txtFractionDifferential_4": "Differential", "DE.Controllers.Toolbar.txtFractionHorizontal": "선형 분수", "DE.Controllers.Toolbar.txtFractionPi_2": "Pi Over 2", "DE.Controllers.Toolbar.txtFractionSmall": "Small Fraction", "DE.Controllers.Toolbar.txtFractionVertical": "Stacked Fraction", "DE.Controllers.Toolbar.txtFunction_1_Cos": "역 코사인 함수", "DE.Controllers.Toolbar.txtFunction_1_Cosh": "쌍곡선 역 코사인 함수", "DE.Controllers.Toolbar.txtFunction_1_Cot": "역 코탄젠트 함수", "DE.Controllers.Toolbar.txtFunction_1_Coth": "쌍곡선 역 코탄젠트 함수", "DE.Controllers.Toolbar.txtFunction_1_Csc": "Inverse Cosecant Function", "DE.Controllers.Toolbar.txtFunction_1_Csch": "쌍곡선 반전 Cosecant 함수", "DE.Controllers.Toolbar.txtFunction_1_Sec": "역 분개 함수", "DE.Controllers.Toolbar.txtFunction_1_Sech": "쌍곡선 역 보조 함수", "DE.Controllers.Toolbar.txtFunction_1_Sin": "역 사인 함수", "DE.Controllers.Toolbar.txtFunction_1_Sinh": "쌍곡선 역 사인 함수", "DE.Controllers.Toolbar.txtFunction_1_Tan": "역 탄젠트 함수", "DE.Controllers.Toolbar.txtFunction_1_Tanh": "쌍곡선 역 탄젠트 함수", "DE.Controllers.Toolbar.txtFunction_Cos": "코사인 함수", "DE.Controllers.Toolbar.txtFunction_Cosh": "쌍곡선 코사인 함수", "DE.Controllers.Toolbar.txtFunction_Cot": "Cotangent Function", "DE.Controllers.Toolbar.txtFunction_Coth": "쌍곡선 코탄 센트 함수", "DE.Controllers.Toolbar.txtFunction_Csc": "Cosecant 함수", "DE.Controllers.Toolbar.txtFunction_Csch": "쌍곡선 보조 함수", "DE.Controllers.Toolbar.txtFunction_Custom_1": "Sine theta", "DE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "DE.Controllers.Toolbar.txtFunction_Custom_3": "Tangent formula", "DE.Controllers.Toolbar.txtFunction_Sec": "Secant 함수", "DE.Controllers.Toolbar.txtFunction_Sech": "쌍곡선 시컨트 함수", "DE.Controllers.Toolbar.txtFunction_Sin": "사인 함수", "DE.Controllers.Toolbar.txtFunction_Sinh": "쌍곡선 사인 함수", "DE.Controllers.Toolbar.txtFunction_Tan": "Tangent Function", "DE.Controllers.Toolbar.txtFunction_Tanh": "쌍곡선 탄젠트 함수", "DE.Controllers.Toolbar.txtIntegral": "Integral", "DE.Controllers.Toolbar.txtIntegral_dtheta": "Differential theta", "DE.Controllers.Toolbar.txtIntegral_dx": "Differential x", "DE.Controllers.Toolbar.txtIntegral_dy": "Differential y", "DE.Controllers.Toolbar.txtIntegralCenterSubSup": "Integral", "DE.Controllers.Toolbar.txtIntegralDouble": "Double Integral", "DE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Double Integral", "DE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Double Integral", "DE.Controllers.Toolbar.txtIntegralOriented": "Contour Integral", "DE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Contour Integral", "DE.Controllers.Toolbar.txtIntegralOrientedDouble": "표면 적분", "DE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "표면 적분", "DE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Surface Integral", "DE.Controllers.Toolbar.txtIntegralOrientedSubSup": "윤곽선 적분", "DE.Controllers.Toolbar.txtIntegralOrientedTriple": "볼륨 정수", "DE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "볼륨 정수", "DE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "볼륨 정수", "DE.Controllers.Toolbar.txtIntegralSubSup": "Integral", "DE.Controllers.Toolbar.txtIntegralTriple": "Triple Integral", "DE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Triple Integral", "DE.Controllers.Toolbar.txtIntegralTripleSubSup": "Triple Integral", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Wedge", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Wedge", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Wedge", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Wedge", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Wedge", "DE.Controllers.Toolbar.txtLargeOperator_CoProd": "Co-Product", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Co-Product", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Co-Product", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Co-Product", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Co-Product", "DE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Summation", "DE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Summation", "DE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Summation", "DE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Product", "DE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Union", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Intersection": "교차점", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "교차점", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "교차점", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "교차점", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "교차점", "DE.Controllers.Toolbar.txtLargeOperator_Prod": "Product", "DE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Product", "DE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Product", "DE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Product", "DE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Product", "DE.Controllers.Toolbar.txtLargeOperator_Sum": "Summation", "DE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Summation", "DE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Summation", "DE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Summation", "DE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Summation", "DE.Controllers.Toolbar.txtLargeOperator_Union": "Union", "DE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Union", "DE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Union", "DE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Union", "DE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Union", "DE.Controllers.Toolbar.txtLimitLog_Custom_1": "Limit Example", "DE.Controllers.Toolbar.txtLimitLog_Custom_2": "최대 예제", "DE.Controllers.Toolbar.txtLimitLog_Lim": "제한", "DE.Controllers.Toolbar.txtLimitLog_Ln": "자연 로그", "DE.Controllers.Toolbar.txtLimitLog_Log": "로그", "DE.Controllers.Toolbar.txtLimitLog_LogBase": "로그", "DE.Controllers.Toolbar.txtLimitLog_Max": "최대", "DE.Controllers.Toolbar.txtLimitLog_Min": "Minimum", "DE.Controllers.Toolbar.txtMarginsH": "주어진 페이지 높이에 대해 위쪽 및 아래쪽 여백이 너무 높습니다.", "DE.Controllers.Toolbar.txtMarginsW": "왼쪽 및 오른쪽 여백이 주어진 페이지 폭에 비해 너무 넓습니다.", "DE.Controllers.Toolbar.txtMatrix_1_2": "1x2 Empty Matrix", "DE.Controllers.Toolbar.txtMatrix_1_3": "1x3 Empty Matrix", "DE.Controllers.Toolbar.txtMatrix_2_1": "2x1 빈 행렬", "DE.Controllers.Toolbar.txtMatrix_2_2": "2x2 빈 행렬", "DE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "괄호가있는 빈 행렬", "DE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "대괄호가있는 빈 행렬", "DE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "괄호가있는 빈 행렬", "DE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "괄호가있는 빈 행렬", "DE.Controllers.Toolbar.txtMatrix_2_3": "2x3 빈 행렬", "DE.Controllers.Toolbar.txtMatrix_3_1": "3x1 빈 행렬", "DE.Controllers.Toolbar.txtMatrix_3_2": "3x2 빈 행렬", "DE.Controllers.Toolbar.txtMatrix_3_3": "3x3 빈 행렬", "DE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Baseline Dots", "DE.Controllers.Toolbar.txtMatrix_Dots_Center": "Midline Dots", "DE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "대각선 점", "DE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "수직 점", "DE.Controllers.Toolbar.txtMatrix_Flat_Round": "스파 스 매트릭스", "DE.Controllers.Toolbar.txtMatrix_Flat_Square": "스파 스 매트릭스", "DE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2 Identity Matrix", "DE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "3x3 Identity Matrix", "DE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3 Identity Matrix", "DE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 Identity Matrix", "DE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "아래 오른쪽 화살표", "DE.Controllers.Toolbar.txtOperator_ArrowD_Top": "오른쪽 위 왼쪽 화살표", "DE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "왼쪽 아래쪽 화살표", "DE.Controllers.Toolbar.txtOperator_ArrowL_Top": "왼쪽 위 화살표", "DE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "오른쪽 아래 화살표", "DE.Controllers.Toolbar.txtOperator_ArrowR_Top": "오른쪽 위 화살표 위", "DE.Controllers.Toolbar.txtOperator_ColonEquals": "콜론 균등", "DE.Controllers.Toolbar.txtOperator_Custom_1": "수익률", "DE.Controllers.Toolbar.txtOperator_Custom_2": "Delta Yields", "DE.Controllers.Toolbar.txtOperator_Definition": "정의에 따라 같음", "DE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta Equal To", "DE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "아래 오른쪽 화살표", "DE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "오른쪽 위 왼쪽 화살표", "DE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "왼쪽 아래쪽 화살표", "DE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "왼쪽 위 화살표 위", "DE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "오른쪽 아래 화살표", "DE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "오른쪽 위 화살표 위", "DE.Controllers.Toolbar.txtOperator_EqualsEquals": "Equal Equal", "DE.Controllers.Toolbar.txtOperator_MinusEquals": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtOperator_PlusEquals": "Plus Equal", "DE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "측정 기준", "DE.Controllers.Toolbar.txtRadicalCustom_1": "Radical", "DE.Controllers.Toolbar.txtRadicalCustom_2": "Radical", "DE.Controllers.Toolbar.txtRadicalRoot_2": "학위가있는 제곱근", "DE.Controllers.Toolbar.txtRadicalRoot_3": "Cubic Root", "DE.Controllers.Toolbar.txtRadicalRoot_n": "학위가있는 급진파", "DE.Controllers.Toolbar.txtRadicalSqrt": "Square Root", "DE.Controllers.Toolbar.txtScriptCustom_1": "스크립트", "DE.Controllers.Toolbar.txtScriptCustom_2": "스크립트", "DE.Controllers.Toolbar.txtScriptCustom_3": "스크립트", "DE.Controllers.Toolbar.txtScriptCustom_4": "스크립트", "DE.Controllers.Toolbar.txtScriptSub": "Subscript", "DE.Controllers.Toolbar.txtScriptSubSup": "Subscript-Superscript", "DE.Controllers.Toolbar.txtScriptSubSupLeft": "LeftSubscript-Superscript", "DE.Controllers.Toolbar.txtScriptSup": "Superscript", "DE.Controllers.Toolbar.txtSymbol_about": "대략", "DE.Controllers.Toolbar.txtSymbol_additional": "Complement", "DE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "DE.Controllers.Toolbar.txtSymbol_alpha": "Alpha", "DE.Controllers.Toolbar.txtSymbol_approx": "거의 동일", "DE.Controllers.Toolbar.txtSymbol_ast": "별표 연산자", "DE.Controllers.Toolbar.txtSymbol_beta": "베타", "DE.Controllers.Toolbar.txtSymbol_beth": "Bet", "DE.Controllers.Toolbar.txtSymbol_bullet": "글 머리 기호 연산자", "DE.Controllers.Toolbar.txtSymbol_cap": "교차점", "DE.Controllers.Toolbar.txtSymbol_cbrt": "큐브 루트", "DE.Controllers.Toolbar.txtSymbol_cdots": "중간 말줄임표", "DE.Controllers.Toolbar.txtSymbol_celsius": "섭씨도", "DE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "DE.Controllers.Toolbar.txtSymbol_cong": "대략 같음", "DE.Controllers.Toolbar.txtSymbol_cup": "Union", "DE.Controllers.Toolbar.txtSymbol_ddots": "오른쪽 아래 대각선 줄임표", "DE.Controllers.Toolbar.txtSymbol_degree": "도", "DE.Controllers.Toolbar.txtSymbol_delta": "Delta", "DE.Controllers.Toolbar.txtSymbol_div": "Division Sign", "DE.Controllers.Toolbar.txtSymbol_downarrow": "화살표: 아래쪽", "DE.Controllers.Toolbar.txtSymbol_emptyset": "빈 세트", "DE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "DE.Controllers.Toolbar.txtSymbol_equals": "Equal", "DE.Controllers.Toolbar.txtSymbol_equiv": "동일 함", "DE.Controllers.Toolbar.txtSymbol_eta": "Eta", "DE.Controllers.Toolbar.txtSymbol_exists": "존재 함", "DE.Controllers.Toolbar.txtSymbol_factorial": "Factorial", "DE.Controllers.Toolbar.txtSymbol_fahrenheit": "화씨", "DE.Controllers.Toolbar.txtSymbol_forall": "모두에게", "DE.Controllers.Toolbar.txtSymbol_gamma": "감마", "DE.Controllers.Toolbar.txtSymbol_geq": "크거나 같음", "DE.Controllers.Toolbar.txtSymbol_gg": "훨씬 더 큼", "DE.Controllers.Toolbar.txtSymbol_greater": "Greater Than", "DE.Controllers.Toolbar.txtSymbol_in": "Element Of", "DE.Controllers.Toolbar.txtSymbol_inc": "Increment", "DE.Controllers.Toolbar.txtSymbol_infinity": "Infinity", "DE.Controllers.Toolbar.txtSymbol_iota": "Iota", "DE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "DE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "DE.Controllers.Toolbar.txtSymbol_leftarrow": "화살표: 왼쪽", "DE.Controllers.Toolbar.txtSymbol_leftrightarrow": "왼쪽 / 오른쪽 화살표", "DE.Controllers.Toolbar.txtSymbol_leq": "보다 작거나 같음", "DE.Controllers.Toolbar.txtSymbol_less": "Less Than", "DE.Controllers.Toolbar.txtSymbol_ll": "훨씬 적습니다", "DE.Controllers.Toolbar.txtSymbol_minus": "Minus", "DE.Controllers.Toolbar.txtSymbol_mp": "Minus Plus", "DE.Controllers.Toolbar.txtSymbol_mu": "Mu", "DE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "DE.Controllers.Toolbar.txtSymbol_neq": "같지 않음", "DE.Controllers.Toolbar.txtSymbol_ni": "회원으로 포함", "DE.Controllers.Toolbar.txtSymbol_not": "Not Sign", "DE.Controllers.Toolbar.txtSymbol_notexists": "존재하지 않습니다", "DE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "DE.Controllers.Toolbar.txtSymbol_o": "Omicron", "DE.Controllers.Toolbar.txtSymbol_omega": "Omega", "DE.Controllers.Toolbar.txtSymbol_partial": "부분 미분", "DE.Controllers.Toolbar.txtSymbol_percent": "백분율", "DE.Controllers.Toolbar.txtSymbol_phi": "Phi", "DE.Controllers.Toolbar.txtSymbol_pi": "Pi", "DE.Controllers.Toolbar.txtSymbol_plus": "Plus", "DE.Controllers.Toolbar.txtSymbol_pm": "Plus Minus", "DE.Controllers.Toolbar.txtSymbol_propto": "Proportional To", "DE.Controllers.Toolbar.txtSymbol_psi": "Psi", "DE.Controllers.Toolbar.txtSymbol_qdrt": "네 번째 루트", "DE.Controllers.Toolbar.txtSymbol_qed": "End of Proof", "DE.Controllers.Toolbar.txtSymbol_rddots": "오른쪽 위 대각선 줄임표", "DE.Controllers.Toolbar.txtSymbol_rho": "Rho", "DE.Controllers.Toolbar.txtSymbol_rightarrow": "화살표: 오른쪽", "DE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "DE.Controllers.Toolbar.txtSymbol_sqrt": "Radical Sign", "DE.Controllers.Toolbar.txtSymbol_tau": "Tau", "DE.Controllers.Toolbar.txtSymbol_therefore": "그러므로", "DE.Controllers.Toolbar.txtSymbol_theta": "Theta", "DE.Controllers.Toolbar.txtSymbol_times": "곱셈 기호", "DE.Controllers.Toolbar.txtSymbol_uparrow": "화살표: 위쪽", "DE.Controllers.Toolbar.txtSymbol_upsilon": "Upsilon", "DE.Controllers.Toolbar.txtSymbol_varepsilon": "Epsilon Variant", "DE.Controllers.Toolbar.txtSymbol_varphi": "<PERSON>", "DE.Controllers.Toolbar.txtSymbol_varpi": "<PERSON>", "DE.Controllers.Toolbar.txtSymbol_varrho": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_varsigma": "Sigma Variant", "DE.Controllers.Toolbar.txtSymbol_vartheta": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_vdots": "세로 줄임표", "DE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "DE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "DE.Controllers.Viewport.textFitPage": "페이지에 맞춤", "DE.Controllers.Viewport.textFitWidth": "너비에 맞춤", "DE.Controllers.Viewport.txtDarkMode": "다크 모드", "DE.Views.AddNewCaptionLabelDialog.textLabel": "라벨:", "DE.Views.AddNewCaptionLabelDialog.textLabelError": "라벨은 비워 둘 수 없습니다.", "DE.Views.BookmarksDialog.textAdd": "추가", "DE.Views.BookmarksDialog.textBookmarkName": "즐겨찾기명", "DE.Views.BookmarksDialog.textClose": "닫기", "DE.Views.BookmarksDialog.textCopy": "복사", "DE.Views.BookmarksDialog.textDelete": "삭제", "DE.Views.BookmarksDialog.textGetLink": "링크 가져오기", "DE.Views.BookmarksDialog.textGoto": "이동", "DE.Views.BookmarksDialog.textHidden": "숨겨진 북마크", "DE.Views.BookmarksDialog.textLocation": "위치", "DE.Views.BookmarksDialog.textName": "이름", "DE.Views.BookmarksDialog.textSort": "정렬 기준", "DE.Views.BookmarksDialog.textTitle": "즐겨 찾기", "DE.Views.BookmarksDialog.txtInvalidName": "즐겨찾기 명은 문자, 숫자 및 밑줄만 포함할 수 있으며 문자로 시작해야 합니다.", "DE.Views.CaptionDialog.textAdd": "라벨추가", "DE.Views.CaptionDialog.textAfter": "이후", "DE.Views.CaptionDialog.textBefore": "이전", "DE.Views.CaptionDialog.textCaption": "참조", "DE.Views.CaptionDialog.textChapter": "스타일로 챕터를 시작", "DE.Views.CaptionDialog.textChapterInc": "챕터번호 포함", "DE.Views.CaptionDialog.textColon": "콜론", "DE.Views.CaptionDialog.textDash": "대시", "DE.Views.CaptionDialog.textDelete": "라벨제거", "DE.Views.CaptionDialog.textEquation": "수식", "DE.Views.CaptionDialog.textExamples": "예: 표 2-A, 이미지 1.IV", "DE.Views.CaptionDialog.textExclude": "라벨을 캡션에서 제외", "DE.Views.CaptionDialog.textFigure": "숫자", "DE.Views.CaptionDialog.textHyphen": "하이픈", "DE.Views.CaptionDialog.textInsert": "삽입", "DE.Views.CaptionDialog.textLabel": "라벨", "DE.Views.CaptionDialog.textLongDash": "긴대시", "DE.Views.CaptionDialog.textNumbering": "번호 매기기", "DE.Views.CaptionDialog.textPeriod": "기간", "DE.Views.CaptionDialog.textSeparator": "구분 기호 사용", "DE.Views.CaptionDialog.textTable": "표", "DE.Views.CaptionDialog.textTitle": "캡션 삽입", "DE.Views.CellsAddDialog.textCol": "열", "DE.Views.CellsAddDialog.textDown": "커서 아래", "DE.Views.CellsAddDialog.textLeft": "왼쪽 유지", "DE.Views.CellsAddDialog.textRight": "오른쪽으로", "DE.Views.CellsAddDialog.textRow": "행", "DE.Views.CellsAddDialog.textTitle": "개별 삽입", "DE.Views.CellsAddDialog.textUp": "커서위에", "DE.Views.ChartSettings.textAdvanced": "고급 설정 표시", "DE.Views.ChartSettings.textChartType": "차트 유형 변경", "DE.Views.ChartSettings.textEditData": "데이터 편집", "DE.Views.ChartSettings.textHeight": "높이", "DE.Views.ChartSettings.textOriginalSize": "실제 크기", "DE.Views.ChartSettings.textSize": "크기", "DE.Views.ChartSettings.textStyle": "스타일", "DE.Views.ChartSettings.textUndock": "패널에서 도킹 해제", "DE.Views.ChartSettings.textWidth": "너비", "DE.Views.ChartSettings.textWrap": "포장 스타일", "DE.Views.ChartSettings.txtBehind": "텍스트 뒤", "DE.Views.ChartSettings.txtInFront": "텍스트 앞에", "DE.Views.ChartSettings.txtInline": "텍스트에 맞춰", "DE.Views.ChartSettings.txtSquare": "Square", "DE.Views.ChartSettings.txtThrough": "통해", "DE.Views.ChartSettings.txtTight": "Tight", "DE.Views.ChartSettings.txtTitle": "차트", "DE.Views.ChartSettings.txtTopAndBottom": "상단 및 하단", "DE.Views.ControlSettingsDialog.strGeneral": "일반", "DE.Views.ControlSettingsDialog.textAdd": "추가", "DE.Views.ControlSettingsDialog.textAppearance": "표시", "DE.Views.ControlSettingsDialog.textApplyAll": "모두에 적용", "DE.Views.ControlSettingsDialog.textBox": "바운딩박스", "DE.Views.ControlSettingsDialog.textChange": "편집", "DE.Views.ControlSettingsDialog.textCheckbox": "체크박스", "DE.Views.ControlSettingsDialog.textChecked": "체크표시", "DE.Views.ControlSettingsDialog.textColor": "색상", "DE.Views.ControlSettingsDialog.textCombobox": "콤보박스", "DE.Views.ControlSettingsDialog.textDate": "날짜 형식", "DE.Views.ControlSettingsDialog.textDelete": "삭제", "DE.Views.ControlSettingsDialog.textDisplayName": "표시이름", "DE.Views.ControlSettingsDialog.textDown": "아래로", "DE.Views.ControlSettingsDialog.textDropDown": "드롭 다운 메뉴", "DE.Views.ControlSettingsDialog.textFormat": "날짜 형식", "DE.Views.ControlSettingsDialog.textLang": "언어", "DE.Views.ControlSettingsDialog.textLock": "잠그기", "DE.Views.ControlSettingsDialog.textName": "제목", "DE.Views.ControlSettingsDialog.textNone": "없음", "DE.Views.ControlSettingsDialog.textPlaceholder": "대체표시", "DE.Views.ControlSettingsDialog.textShowAs": "표시", "DE.Views.ControlSettingsDialog.textSystemColor": "시스템", "DE.Views.ControlSettingsDialog.textTag": "꼬리표", "DE.Views.ControlSettingsDialog.textTitle": "콘텐트 제어 세팅", "DE.Views.ControlSettingsDialog.textUnchecked": "선택하지 않은 기호", "DE.Views.ControlSettingsDialog.textUp": "위", "DE.Views.ControlSettingsDialog.textValue": "값", "DE.Views.ControlSettingsDialog.tipChange": "기호변경", "DE.Views.ControlSettingsDialog.txtLockDelete": "콘텐트 제어가 삭제될 수 없슴", "DE.Views.ControlSettingsDialog.txtLockEdit": "콘텐트가 편집될 수 없슴", "DE.Views.CrossReferenceDialog.textAboveBelow": "상/하", "DE.Views.CrossReferenceDialog.textBookmark": "즐겨찾기", "DE.Views.CrossReferenceDialog.textBookmarkText": "즐겨찾기 텍스트", "DE.Views.CrossReferenceDialog.textCaption": "전체 캡션", "DE.Views.CrossReferenceDialog.textEmpty": "요청한 참조가 비어 있습니다.", "DE.Views.CrossReferenceDialog.textEndnote": "미주", "DE.Views.CrossReferenceDialog.textEndNoteNum": "미주번호", "DE.Views.CrossReferenceDialog.textEndNoteNumForm": "미주번호 (형식지정)", "DE.Views.CrossReferenceDialog.textEquation": "방정식", "DE.Views.CrossReferenceDialog.textFigure": "숫자", "DE.Views.CrossReferenceDialog.textFootnote": "각주", "DE.Views.CrossReferenceDialog.textHeading": "제목", "DE.Views.CrossReferenceDialog.textHeadingNum": "제목번호", "DE.Views.CrossReferenceDialog.textHeadingNumFull": "제목번호 (전체)", "DE.Views.CrossReferenceDialog.textHeadingNumNo": "제목 번호 (내용 없음)", "DE.Views.CrossReferenceDialog.textHeadingText": "제목 텍스트", "DE.Views.CrossReferenceDialog.textIncludeAbove": "위/아래 포함", "DE.Views.CrossReferenceDialog.textInsert": "삽입", "DE.Views.CrossReferenceDialog.textInsertAs": "하이퍼링크로 삽입", "DE.Views.CrossReferenceDialog.textLabelNum": "라벨과 번호 만", "DE.Views.CrossReferenceDialog.textNoteNum": "각주번호", "DE.Views.CrossReferenceDialog.textNoteNumForm": "각주번호 (형식지정)", "DE.Views.CrossReferenceDialog.textOnlyCaption": "캡션 텍스트만", "DE.Views.CrossReferenceDialog.textPageNum": "페이지 번호", "DE.Views.CrossReferenceDialog.textParagraph": "번호가 붙여진 항목", "DE.Views.CrossReferenceDialog.textParaNum": "번호 매기기", "DE.Views.CrossReferenceDialog.textParaNumFull": "단락 번호 (전체 문맥)", "DE.Views.CrossReferenceDialog.textParaNumNo": "단락번호 (문맥없음)", "DE.Views.CrossReferenceDialog.textSeparate": "숫자로 구분", "DE.Views.CrossReferenceDialog.textTable": "표", "DE.Views.CrossReferenceDialog.textText": "단락 텍스트", "DE.Views.CrossReferenceDialog.textWhich": "캡션 참조", "DE.Views.CrossReferenceDialog.textWhichBookmark": "책갈피 참조", "DE.Views.CrossReferenceDialog.textWhichEndnote": "미주 참조", "DE.Views.CrossReferenceDialog.textWhichHeading": "제목 참조", "DE.Views.CrossReferenceDialog.textWhichNote": "각주 참조", "DE.Views.CrossReferenceDialog.textWhichPara": "참조", "DE.Views.CrossReferenceDialog.txtReference": "참조 삽입", "DE.Views.CrossReferenceDialog.txtTitle": "상호 참조", "DE.Views.CrossReferenceDialog.txtType": "참조유형", "DE.Views.CustomColumnsDialog.textColumns": "열 수", "DE.Views.CustomColumnsDialog.textSeparator": "열 구분선", "DE.Views.CustomColumnsDialog.textSpacing": "열 사이의 간격", "DE.Views.CustomColumnsDialog.textTitle": "열", "DE.Views.DateTimeDialog.confirmDefault": "{0} 기본 형식을 설정 : \"{1}\"", "DE.Views.DateTimeDialog.textDefault": "디폴트 설정", "DE.Views.DateTimeDialog.textFormat": "형식", "DE.Views.DateTimeDialog.textLang": "언어", "DE.Views.DateTimeDialog.textUpdate": "자동 업데이트", "DE.Views.DateTimeDialog.txtTitle": "날짜 및 시간", "DE.Views.DocProtection.hintProtectDoc": "문서 보호", "DE.Views.DocProtection.txtProtectDoc": "문서 보호", "DE.Views.DocumentHolder.aboveText": "위", "DE.Views.DocumentHolder.addCommentText": "주석 추가", "DE.Views.DocumentHolder.advancedDropCapText": "드롭 캡 설정", "DE.Views.DocumentHolder.advancedFrameText": "프레임 고급 설정", "DE.Views.DocumentHolder.advancedParagraphText": "단락 고급 설정", "DE.Views.DocumentHolder.advancedTableText": "표 고급 설정", "DE.Views.DocumentHolder.advancedText": "고급 설정", "DE.Views.DocumentHolder.alignmentText": "정렬", "DE.Views.DocumentHolder.belowText": "Below", "DE.Views.DocumentHolder.breakBeforeText": "현재 단락 앞에서 페이지 나누기", "DE.Views.DocumentHolder.bulletsText": "글 머리 기호 및 번호 매기기", "DE.Views.DocumentHolder.cellAlignText": "셀 수직 정렬", "DE.Views.DocumentHolder.cellText": "셀", "DE.Views.DocumentHolder.centerText": "Center", "DE.Views.DocumentHolder.chartText": "차트 고급 설정", "DE.Views.DocumentHolder.columnText": "Column", "DE.Views.DocumentHolder.deleteColumnText": "열 삭제", "DE.Views.DocumentHolder.deleteRowText": "Delete Row", "DE.Views.DocumentHolder.deleteTableText": "테이블 삭제", "DE.Views.DocumentHolder.deleteText": "삭제", "DE.Views.DocumentHolder.direct270Text": "텍스트 회전", "DE.Views.DocumentHolder.direct90Text": "텍스트 아래로 회전", "DE.Views.DocumentHolder.directHText": "Horizontal", "DE.Views.DocumentHolder.directionText": "텍스트 방향", "DE.Views.DocumentHolder.editChartText": "데이터 편집", "DE.Views.DocumentHolder.editFooterText": "바닥 글 편집", "DE.Views.DocumentHolder.editHeaderText": "머리글 편집", "DE.Views.DocumentHolder.editHyperlinkText": "하이퍼 링크 편집", "DE.Views.DocumentHolder.guestText": "Guest", "DE.Views.DocumentHolder.hyperlinkText": "하이퍼 링크", "DE.Views.DocumentHolder.ignoreAllSpellText": "모두 무시", "DE.Views.DocumentHolder.ignoreSpellText": "무시", "DE.Views.DocumentHolder.imageText": "이미지 고급 설정", "DE.Views.DocumentHolder.insertColumnLeftText": "왼쪽 열", "DE.Views.DocumentHolder.insertColumnRightText": "오른쪽 열", "DE.Views.DocumentHolder.insertColumnText": "열 삽입", "DE.Views.DocumentHolder.insertRowAboveText": "행 위", "DE.Views.DocumentHolder.insertRowBelowText": "행 아래", "DE.Views.DocumentHolder.insertRowText": "행 삽입", "DE.Views.DocumentHolder.insertText": "삽입", "DE.Views.DocumentHolder.keepLinesText": "현재 단락을 나누지 않음", "DE.Views.DocumentHolder.langText": "언어 선택", "DE.Views.DocumentHolder.leftText": "왼쪽", "DE.Views.DocumentHolder.loadSpellText": "로드 변형 ...", "DE.Views.DocumentHolder.mergeCellsText": "셀 병합", "DE.Views.DocumentHolder.moreText": "추가 변형 ...", "DE.Views.DocumentHolder.noSpellVariantsText": "변형 없음", "DE.Views.DocumentHolder.notcriticalErrorTitle": "경고", "DE.Views.DocumentHolder.originalSizeText": "실제 크기", "DE.Views.DocumentHolder.paragraphText": "단락", "DE.Views.DocumentHolder.removeHyperlinkText": "하이퍼 링크 제거", "DE.Views.DocumentHolder.rightText": "오른쪽", "DE.Views.DocumentHolder.rowText": "행", "DE.Views.DocumentHolder.saveStyleText": "새 스타일 만들기", "DE.Views.DocumentHolder.selectCellText": "셀 선택", "DE.Views.DocumentHolder.selectColumnText": "열 선택", "DE.Views.DocumentHolder.selectRowText": "행 선택", "DE.Views.DocumentHolder.selectTableText": "표 선택", "DE.Views.DocumentHolder.selectText": "선택", "DE.Views.DocumentHolder.shapeText": "모양 고급 설정", "DE.Views.DocumentHolder.spellcheckText": "맞춤법 검사", "DE.Views.DocumentHolder.splitCellsText": "셀 분할 ...", "DE.Views.DocumentHolder.splitCellTitleText": "셀 분할", "DE.Views.DocumentHolder.strDelete": "서명 삭제", "DE.Views.DocumentHolder.strDetails": "서명 상세", "DE.Views.DocumentHolder.strSetup": "서명 셋업", "DE.Views.DocumentHolder.strSign": "서명", "DE.Views.DocumentHolder.styleText": "스타일로 서식 지정", "DE.Views.DocumentHolder.tableText": "테이블", "DE.Views.DocumentHolder.textAccept": "변경 수락", "DE.Views.DocumentHolder.textAlign": "정렬", "DE.Views.DocumentHolder.textArrange": "정렬", "DE.Views.DocumentHolder.textArrangeBack": "맨 뒤로 보내기", "DE.Views.DocumentHolder.textArrangeBackward": "뒤로 이동", "DE.Views.DocumentHolder.textArrangeForward": "앞으로 이동", "DE.Views.DocumentHolder.textArrangeFront": "포 그라운드로 가져 오기", "DE.Views.DocumentHolder.textCells": "셀", "DE.Views.DocumentHolder.textCol": "전체 열 삭제", "DE.Views.DocumentHolder.textContentControls": "콘텐트 제어", "DE.Views.DocumentHolder.textContinueNumbering": "계속 번호 매기기", "DE.Views.DocumentHolder.textCopy": "복사", "DE.Views.DocumentHolder.textCrop": "자르기", "DE.Views.DocumentHolder.textCropFill": "채우기", "DE.Views.DocumentHolder.textCropFit": "맞춤", "DE.Views.DocumentHolder.textCut": "잘라 내기", "DE.Views.DocumentHolder.textDistributeCols": "컬럼 배포", "DE.Views.DocumentHolder.textDistributeRows": "행 배포", "DE.Views.DocumentHolder.textEditControls": "콘텐트 제어 세팅", "DE.Views.DocumentHolder.textEditPoints": "꼭지점 수정", "DE.Views.DocumentHolder.textEditWrapBoundary": "둘러싸 기 경계 편집", "DE.Views.DocumentHolder.textFlipH": "좌우대칭", "DE.Views.DocumentHolder.textFlipV": "상하대칭", "DE.Views.DocumentHolder.textFollow": "이동", "DE.Views.DocumentHolder.textFromFile": "파일로부터", "DE.Views.DocumentHolder.textFromStorage": "스토리지로 부터", "DE.Views.DocumentHolder.textFromUrl": "URL로부터", "DE.Views.DocumentHolder.textJoinList": "이전 목록에 추가", "DE.Views.DocumentHolder.textLeft": "셀을 왼쪽으로 이동", "DE.Views.DocumentHolder.textNest": "네스트 테이블", "DE.Views.DocumentHolder.textNextPage": "다음 페이지", "DE.Views.DocumentHolder.textNumberingValue": "번호", "DE.Views.DocumentHolder.textPaste": "붙여 넣기", "DE.Views.DocumentHolder.textPrevPage": "이전 페이지", "DE.Views.DocumentHolder.textRefreshField": "필드 새로고침", "DE.Views.DocumentHolder.textReject": "변경 거부", "DE.Views.DocumentHolder.textRemCheckBox": "체크박스 제거", "DE.Views.DocumentHolder.textRemComboBox": "콤보박스 제거", "DE.Views.DocumentHolder.textRemDropdown": "드랍박스 제거", "DE.Views.DocumentHolder.textRemField": "텍스트 필드 제거", "DE.Views.DocumentHolder.textRemove": "삭제", "DE.Views.DocumentHolder.textRemoveControl": "콘텐트 제어 삭제", "DE.Views.DocumentHolder.textRemPicture": "이미지 제거", "DE.Views.DocumentHolder.textRemRadioBox": "라디오버튼 제거", "DE.Views.DocumentHolder.textReplace": "이미지 바꾸기", "DE.Views.DocumentHolder.textRotate": "회전", "DE.Views.DocumentHolder.textRotate270": "왼쪽으로 90도 회전", "DE.Views.DocumentHolder.textRotate90": "오른쪽으로 90도 회전", "DE.Views.DocumentHolder.textRow": "전체 행 삭제", "DE.Views.DocumentHolder.textSeparateList": "목록 분리", "DE.Views.DocumentHolder.textSettings": "설정", "DE.Views.DocumentHolder.textSeveral": "여러 행/열", "DE.Views.DocumentHolder.textShapeAlignBottom": "아래쪽 정렬", "DE.Views.DocumentHolder.textShapeAlignCenter": "정렬 중심", "DE.Views.DocumentHolder.textShapeAlignLeft": "왼쪽 정렬", "DE.Views.DocumentHolder.textShapeAlignMiddle": "가운데 정렬", "DE.Views.DocumentHolder.textShapeAlignRight": "오른쪽 정렬", "DE.Views.DocumentHolder.textShapeAlignTop": "정렬", "DE.Views.DocumentHolder.textStartNewList": "새 목록 시작", "DE.Views.DocumentHolder.textStartNumberingFrom": "숫자 값 설정", "DE.Views.DocumentHolder.textTitleCellsRemove": "셀 삭제", "DE.Views.DocumentHolder.textTOC": "콘텍츠 테이블", "DE.Views.DocumentHolder.textTOCSettings": "콘텐츠 테이블 세팅", "DE.Views.DocumentHolder.textUndo": "실행 취소", "DE.Views.DocumentHolder.textUpdateAll": "전체 테이블을 새로고침하세요", "DE.Views.DocumentHolder.textUpdatePages": "페이지 번호만 새로고침하세요", "DE.Views.DocumentHolder.textUpdateTOC": "콘텐트 테이블 새로고침", "DE.Views.DocumentHolder.textWrap": "배치 스타일", "DE.Views.DocumentHolder.tipIsLocked": "이 요소는 현재 다른 사용자가 편집 중입니다.", "DE.Views.DocumentHolder.toDictionaryText": "사용자 정의 사전에 추가", "DE.Views.DocumentHolder.txtAddBottom": "아래쪽 테두리 추가", "DE.Views.DocumentHolder.txtAddFractionBar": "분수 막대 추가", "DE.Views.DocumentHolder.txtAddHor": "가로선 추가", "DE.Views.DocumentHolder.txtAddLB": "왼쪽 하단 추가", "DE.Views.DocumentHolder.txtAddLeft": "왼쪽 테두리 추가", "DE.Views.DocumentHolder.txtAddLT": "왼쪽 상단 줄 추가", "DE.Views.DocumentHolder.txtAddRight": "오른쪽 테두리 추가", "DE.Views.DocumentHolder.txtAddTop": "상단 테두리 추가", "DE.Views.DocumentHolder.txtAddVer": "세로선 추가", "DE.Views.DocumentHolder.txtAlignToChar": "문자에 정렬", "DE.Views.DocumentHolder.txtBehind": "텍스트 뒤", "DE.Views.DocumentHolder.txtBorderProps": "테두리 속성", "DE.Views.DocumentHolder.txtBottom": "Bottom", "DE.Views.DocumentHolder.txtColumnAlign": "열 정렬", "DE.Views.DocumentHolder.txtDecreaseArg": "인수 크기 감소", "DE.Views.DocumentHolder.txtDeleteArg": "인수 삭제", "DE.Views.DocumentHolder.txtDeleteBreak": "수동페이지 나누기 삭제", "DE.Views.DocumentHolder.txtDeleteChars": "둘러싸인 문자 삭제", "DE.Views.DocumentHolder.txtDeleteCharsAndSeparators": "둘러싸는 문자 및 구분 기호 삭제", "DE.Views.DocumentHolder.txtDeleteEq": "수식 삭제", "DE.Views.DocumentHolder.txtDeleteGroupChar": "문자 삭제", "DE.Views.DocumentHolder.txtDeleteRadical": "급진파 삭제", "DE.Views.DocumentHolder.txtDistribHor": "수평 분포", "DE.Views.DocumentHolder.txtDistribVert": "수직 분포", "DE.Views.DocumentHolder.txtEmpty": "(없음)", "DE.Views.DocumentHolder.txtFractionLinear": "선형 분수로 변경", "DE.Views.DocumentHolder.txtFractionSkewed": "기울어 진 분수로 변경", "DE.Views.DocumentHolder.txtFractionStacked": "누적 분율로 변경", "DE.Views.DocumentHolder.txtGroup": "그룹", "DE.Views.DocumentHolder.txtGroupCharOver": "텍스트를 덮는 문자", "DE.Views.DocumentHolder.txtGroupCharUnder": "문자 아래의 문자", "DE.Views.DocumentHolder.txtHideBottom": "아래쪽 경계선 숨기기", "DE.Views.DocumentHolder.txtHideBottomLimit": "하단 제한 숨기기", "DE.Views.DocumentHolder.txtHideCloseBracket": "닫는 대괄호 숨기기", "DE.Views.DocumentHolder.txtHideDegree": "학위 숨기기", "DE.Views.DocumentHolder.txtHideHor": "가로선 숨기기", "DE.Views.DocumentHolder.txtHideLB": "왼쪽 하단 줄 숨기기", "DE.Views.DocumentHolder.txtHideLeft": "왼쪽 테두리 숨기기", "DE.Views.DocumentHolder.txtHideLT": "왼쪽 상단 줄 숨기기", "DE.Views.DocumentHolder.txtHideOpenBracket": "여는 대괄호 숨기기", "DE.Views.DocumentHolder.txtHidePlaceholder": "자리 표시 자 숨기기", "DE.Views.DocumentHolder.txtHideRight": "오른쪽 테두리 숨기기", "DE.Views.DocumentHolder.txtHideTop": "위쪽 테두리 숨기기", "DE.Views.DocumentHolder.txtHideTopLimit": "상한 숨기기", "DE.Views.DocumentHolder.txtHideVer": "수직선 숨기기", "DE.Views.DocumentHolder.txtIncreaseArg": "인수 크기 늘리기", "DE.Views.DocumentHolder.txtInFront": "텍스트 앞에", "DE.Views.DocumentHolder.txtInline": "텍스트에 맞춰", "DE.Views.DocumentHolder.txtInsertArgAfter": "뒤에 인수를 삽입하십시오.", "DE.Views.DocumentHolder.txtInsertArgBefore": "앞에 인수를 삽입하십시오", "DE.Views.DocumentHolder.txtInsertBreak": "수동 페이지 나누기 삽입", "DE.Views.DocumentHolder.txtInsertCaption": "캡션 삽입", "DE.Views.DocumentHolder.txtInsertEqAfter": "뒤에 수식을 삽입하십시오.", "DE.Views.DocumentHolder.txtInsertEqBefore": "이전에 수식 삽입", "DE.Views.DocumentHolder.txtKeepTextOnly": "텍스트 만 유지", "DE.Views.DocumentHolder.txtLimitChange": "제한 위치 변경", "DE.Views.DocumentHolder.txtLimitOver": "텍스트 제한", "DE.Views.DocumentHolder.txtLimitUnder": "텍스트 아래에서 제한", "DE.Views.DocumentHolder.txtMatchBrackets": "대괄호를 인수 높이에 대응", "DE.Views.DocumentHolder.txtMatrixAlign": "매트릭스 정렬", "DE.Views.DocumentHolder.txtOverbar": "텍스트 위에 가로 막기", "DE.Views.DocumentHolder.txtOverwriteCells": "셀에 덮어쓰기", "DE.Views.DocumentHolder.txtPasteSourceFormat": "소스 포맷을 유지하세요", "DE.Views.DocumentHolder.txtPressLink": "{0} 키를 누르고 링크를 클릭합니다.", "DE.Views.DocumentHolder.txtPrintSelection": "선택 항목 인쇄", "DE.Views.DocumentHolder.txtRemFractionBar": "분수 막대 제거", "DE.Views.DocumentHolder.txtRemLimit": "제한 제거", "DE.Views.DocumentHolder.txtRemoveAccentChar": "액센트 문자 제거", "DE.Views.DocumentHolder.txtRemoveBar": "막대 제거", "DE.Views.DocumentHolder.txtRemoveWarning": "이 서명을 삭제하시겠습니까?<br>이 작업은 취소할 수 없습니다.", "DE.Views.DocumentHolder.txtRemScripts": "스크립트 제거", "DE.Views.DocumentHolder.txtRemSubscript": "아래 첨자 제거", "DE.Views.DocumentHolder.txtRemSuperscript": "위 첨자 제거", "DE.Views.DocumentHolder.txtScriptsAfter": "텍스트 뒤의 스크립트", "DE.Views.DocumentHolder.txtScriptsBefore": "텍스트 앞의 스크립트", "DE.Views.DocumentHolder.txtShowBottomLimit": "하단 제한 표시", "DE.Views.DocumentHolder.txtShowCloseBracket": "닫는 괄호 표시", "DE.Views.DocumentHolder.txtShowDegree": "학위 표시", "DE.Views.DocumentHolder.txtShowOpenBracket": "여는 대괄호 표시", "DE.Views.DocumentHolder.txtShowPlaceholder": "Show placeholder", "DE.Views.DocumentHolder.txtShowTopLimit": "상한 표시", "DE.Views.DocumentHolder.txtSquare": "Square", "DE.Views.DocumentHolder.txtStretchBrackets": "스트레치 괄호", "DE.Views.DocumentHolder.txtThrough": "통해", "DE.Views.DocumentHolder.txtTight": "꽉", "DE.Views.DocumentHolder.txtTop": "Top", "DE.Views.DocumentHolder.txtTopAndBottom": "상단 및 하단", "DE.Views.DocumentHolder.txtUnderbar": "텍스트 아래에 바", "DE.Views.DocumentHolder.txtUngroup": "그룹 해제", "DE.Views.DocumentHolder.txtWarnUrl": "이 링크는 장치와 데이터에 손상을 줄 수 있습니다. <br> 계속하시겠습니까?", "DE.Views.DocumentHolder.updateStyleText": "%1 스타일 업데이트", "DE.Views.DocumentHolder.vertAlignText": "Vertical Alignment", "DE.Views.DropcapSettingsAdvanced.strBorders": "테두리 및 채우기", "DE.Views.DropcapSettingsAdvanced.strDropcap": "드롭 캡", "DE.Views.DropcapSettingsAdvanced.strMargins": "여백", "DE.Views.DropcapSettingsAdvanced.textAlign": "정렬", "DE.Views.DropcapSettingsAdvanced.textAtLeast": "적어도", "DE.Views.DropcapSettingsAdvanced.textAuto": "Auto", "DE.Views.DropcapSettingsAdvanced.textBackColor": "배경색", "DE.Views.DropcapSettingsAdvanced.textBorderColor": "테두리 색상", "DE.Views.DropcapSettingsAdvanced.textBorderDesc": "다이어그램을 클릭하거나 단추를 사용하여 테두리를 선택하십시오", "DE.Views.DropcapSettingsAdvanced.textBorderWidth": "테두리 크기", "DE.Views.DropcapSettingsAdvanced.textBottom": "Bottom", "DE.Views.DropcapSettingsAdvanced.textCenter": "Center", "DE.Views.DropcapSettingsAdvanced.textColumn": "Column", "DE.Views.DropcapSettingsAdvanced.textDistance": "텍스트 간격", "DE.Views.DropcapSettingsAdvanced.textExact": "정확히", "DE.Views.DropcapSettingsAdvanced.textFlow": "흐름 프레임", "DE.Views.DropcapSettingsAdvanced.textFont": "글꼴", "DE.Views.DropcapSettingsAdvanced.textFrame": "프레임", "DE.Views.DropcapSettingsAdvanced.textHeight": "높이", "DE.Views.DropcapSettingsAdvanced.textHorizontal": "Horizontal", "DE.Views.DropcapSettingsAdvanced.textInline": "인라인 프레임", "DE.Views.DropcapSettingsAdvanced.textInMargin": "여백 있음", "DE.Views.DropcapSettingsAdvanced.textInText": "텍스트에서", "DE.Views.DropcapSettingsAdvanced.textLeft": "왼쪽", "DE.Views.DropcapSettingsAdvanced.textMargin": "여백", "DE.Views.DropcapSettingsAdvanced.textMove": "텍스트와 함께 이동", "DE.Views.DropcapSettingsAdvanced.textNone": "없음", "DE.Views.DropcapSettingsAdvanced.textPage": "페이지", "DE.Views.DropcapSettingsAdvanced.textParagraph": "단락", "DE.Views.DropcapSettingsAdvanced.textParameters": "매개 변수", "DE.Views.DropcapSettingsAdvanced.textPosition": "위치", "DE.Views.DropcapSettingsAdvanced.textRelative": "상대적", "DE.Views.DropcapSettingsAdvanced.textRight": "오른쪽", "DE.Views.DropcapSettingsAdvanced.textRowHeight": "행의 높이", "DE.Views.DropcapSettingsAdvanced.textTitle": "드롭 캡 - 고급 설정", "DE.Views.DropcapSettingsAdvanced.textTitleFrame": "프레임 - 고급 설정", "DE.Views.DropcapSettingsAdvanced.textTop": "Top", "DE.Views.DropcapSettingsAdvanced.textVertical": "Vertical", "DE.Views.DropcapSettingsAdvanced.textWidth": "너비", "DE.Views.DropcapSettingsAdvanced.tipFontName": "글꼴", "DE.Views.DropcapSettingsAdvanced.txtNoBorders": "테두리 없음", "DE.Views.EditListItemDialog.textDisplayName": "표시이름", "DE.Views.EditListItemDialog.textNameError": "표시이름은 비워둘 수 없습니다.", "DE.Views.EditListItemDialog.textValue": "값", "DE.Views.EditListItemDialog.textValueError": "동일한 값을 가진 항목이 이미 존재합니다.", "DE.Views.FileMenu.btnBackCaption": "파일 위치 열기", "DE.Views.FileMenu.btnCloseMenuCaption": "메뉴 닫기", "DE.Views.FileMenu.btnCreateNewCaption": "새로 만들기", "DE.Views.FileMenu.btnDownloadCaption": "다운로드 방법", "DE.Views.FileMenu.btnExitCaption": "닫기", "DE.Views.FileMenu.btnFileOpenCaption": "열기", "DE.Views.FileMenu.btnHelpCaption": "Help", "DE.Views.FileMenu.btnHistoryCaption": "버전 기록", "DE.Views.FileMenu.btnInfoCaption": "문서 정보", "DE.Views.FileMenu.btnPrintCaption": "인쇄", "DE.Views.FileMenu.btnProtectCaption": "보호", "DE.Views.FileMenu.btnRecentFilesCaption": "최근 열기", "DE.Views.FileMenu.btnRenameCaption": "<PERSON><PERSON>", "DE.Views.FileMenu.btnReturnCaption": "문서로 돌아 가기", "DE.Views.FileMenu.btnRightsCaption": "액세스 권한", "DE.Views.FileMenu.btnSaveAsCaption": "다른 이름으로 저장", "DE.Views.FileMenu.btnSaveCaption": "저장", "DE.Views.FileMenu.btnSaveCopyAsCaption": "다른 이름으로 저장", "DE.Views.FileMenu.btnSettingsCaption": "고급 설정", "DE.Views.FileMenu.btnToEditCaption": "문서 편집", "DE.Views.FileMenu.textDownload": "다운로드", "DE.Views.FileMenuPanels.CreateNew.txtBlank": "빈문서", "DE.Views.FileMenuPanels.CreateNew.txtCreateNew": "새로 만들기", "DE.Views.FileMenuPanels.DocumentInfo.okButtonText": "적용", "DE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "작성자추가", "DE.Views.FileMenuPanels.DocumentInfo.txtAddText": "텍스트추가", "DE.Views.FileMenuPanels.DocumentInfo.txtAppName": "어플리케이션", "DE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "작성자", "DE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "액세스 권한 변경", "DE.Views.FileMenuPanels.DocumentInfo.txtComment": "코멘트", "DE.Views.FileMenuPanels.DocumentInfo.txtCreated": "생성되었습니다", "DE.Views.FileMenuPanels.DocumentInfo.txtFastWV": "패스트 웹 뷰", "DE.Views.FileMenuPanels.DocumentInfo.txtLoading": "로드 중 ...", "DE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "최종 편집자", "DE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "최종 편집", "DE.Views.FileMenuPanels.DocumentInfo.txtNo": "아니오", "DE.Views.FileMenuPanels.DocumentInfo.txtOwner": "소유자", "DE.Views.FileMenuPanels.DocumentInfo.txtPages": "Pages", "DE.Views.FileMenuPanels.DocumentInfo.txtPageSize": "페이지 크기", "DE.Views.FileMenuPanels.DocumentInfo.txtParagraphs": "단락", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfTagged": "태그드 PDF", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfVer": "PDF 버전", "DE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "위치", "DE.Views.FileMenuPanels.DocumentInfo.txtRights": "권한이있는 사람", "DE.Views.FileMenuPanels.DocumentInfo.txtSpaces": "공백이있는 기호", "DE.Views.FileMenuPanels.DocumentInfo.txtStatistics": "통계", "DE.Views.FileMenuPanels.DocumentInfo.txtSubject": "제목", "DE.Views.FileMenuPanels.DocumentInfo.txtSymbols": "심볼", "DE.Views.FileMenuPanels.DocumentInfo.txtTitle": "문서 제목", "DE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "업로드 되었습니다", "DE.Views.FileMenuPanels.DocumentInfo.txtWords": "단어", "DE.Views.FileMenuPanels.DocumentInfo.txtYes": "예", "DE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "액세스 권한 변경", "DE.Views.FileMenuPanels.DocumentRights.txtRights": "권한이있는 사람", "DE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "경고", "DE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "비밀번호로", "DE.Views.FileMenuPanels.ProtectDoc.strProtect": "문서 보호", "DE.Views.FileMenuPanels.ProtectDoc.strSignature": "서명으로", "DE.Views.FileMenuPanels.ProtectDoc.txtEdit": "문서 편집", "DE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "편집하면 문서의 서명이 삭제됩니다. <br>계속하시겠습니까?", "DE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "이 문서는 비밀번호로 보호된 적이 있슴", "DE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "이 문서는 서명되어야 합니다.", "DE.Views.FileMenuPanels.ProtectDoc.txtSigned": "문서에 유효한 서명이 추가되었습니다. 문서가 보호되어 편집할 수 없습니다.", "DE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "문서의 일부 디지털 서명이 유효하지 않거나 확인할 수 없습니다. 문서가 보호되어 편집할 수 없습니다.", "DE.Views.FileMenuPanels.ProtectDoc.txtView": "서명 보기", "DE.Views.FileMenuPanels.Settings.okButtonText": "적용", "DE.Views.FileMenuPanels.Settings.strCoAuthMode": "공동 편집 모드", "DE.Views.FileMenuPanels.Settings.strFast": "Fast", "DE.Views.FileMenuPanels.Settings.strFontRender": "글꼴 힌트", "DE.Views.FileMenuPanels.Settings.strMacrosSettings": "매크로 설정", "DE.Views.FileMenuPanels.Settings.strPasteButton": "내용을 붙여넣을 때 \"붙여넣기 옵션\" 표시", "DE.Views.FileMenuPanels.Settings.strShowChanges": "실시간 협업 변경 사항", "DE.Views.FileMenuPanels.Settings.strStrict": "Strict", "DE.Views.FileMenuPanels.Settings.strTheme": "인터페이스 테마", "DE.Views.FileMenuPanels.Settings.strUnit": "측정 단위", "DE.Views.FileMenuPanels.Settings.strZoom": "기본 확대/축소 값", "DE.Views.FileMenuPanels.Settings.text10Minutes": "매 10 분마다", "DE.Views.FileMenuPanels.Settings.text30Minutes": "매 30 분마다", "DE.Views.FileMenuPanels.Settings.text5Minutes": "매 5 분마다", "DE.Views.FileMenuPanels.Settings.text60Minutes": "매시간", "DE.Views.FileMenuPanels.Settings.textAlignGuides": "정렬 가이드", "DE.Views.FileMenuPanels.Settings.textAutoRecover": "Autorecover", "DE.Views.FileMenuPanels.Settings.textAutoSave": "자동 저장", "DE.Views.FileMenuPanels.Settings.textDisabled": "Disabled", "DE.Views.FileMenuPanels.Settings.textForceSave": "모든 기록 버전을 서버에 저장", "DE.Views.FileMenuPanels.Settings.textMinute": "Every Minute", "DE.Views.FileMenuPanels.Settings.textOldVersions": "DOCX로 저장할 때 이전 버전의 MS Word와 호환되도록 파일을 만드십시오", "DE.Views.FileMenuPanels.Settings.txtAll": "모두보기", "DE.Views.FileMenuPanels.Settings.txtAutoCorrect": "자동 고침 옵션...", "DE.Views.FileMenuPanels.Settings.txtCacheMode": "사전 설정 캐시 모드", "DE.Views.FileMenuPanels.Settings.txtChangesBalloons": "풍선을 클릭하여 표시", "DE.Views.FileMenuPanels.Settings.txtChangesTip": "표시할 툴팁 위로 마우스를 가져갑니다.", "DE.Views.FileMenuPanels.Settings.txtCm": "센티미터", "DE.Views.FileMenuPanels.Settings.txtDarkMode": "문서 다크 모드 켜기", "DE.Views.FileMenuPanels.Settings.txtFitPage": "페이지에 맞춤", "DE.Views.FileMenuPanels.Settings.txtFitWidth": "너비에 맞춤", "DE.Views.FileMenuPanels.Settings.txtInch": "인치", "DE.Views.FileMenuPanels.Settings.txtLast": "마지막보기", "DE.Views.FileMenuPanels.Settings.txtMac": "as OS X", "DE.Views.FileMenuPanels.Settings.txtNative": "Native", "DE.Views.FileMenuPanels.Settings.txtNone": "보기 없음", "DE.Views.FileMenuPanels.Settings.txtProofing": "보정", "DE.Views.FileMenuPanels.Settings.txtPt": "Point", "DE.Views.FileMenuPanels.Settings.txtRunMacros": "모두 활성화", "DE.Views.FileMenuPanels.Settings.txtRunMacrosDesc": "알림 없이 모든 매크로 활성화", "DE.Views.FileMenuPanels.Settings.txtSpellCheck": "맞춤법 검사", "DE.Views.FileMenuPanels.Settings.txtStopMacros": "모두 비활성화", "DE.Views.FileMenuPanels.Settings.txtStopMacrosDesc": "모든 매크로 비활성화하라는 메시지", "DE.Views.FileMenuPanels.Settings.txtWarnMacros": "알림 표시", "DE.Views.FileMenuPanels.Settings.txtWarnMacrosDesc": "모든 매크로를 비활성화하라는 메시지", "DE.Views.FileMenuPanels.Settings.txtWin": "Windows로", "DE.Views.FormSettings.textAlways": "항상", "DE.Views.FormSettings.textAspect": "가로 세로 비율 잠금", "DE.Views.FormSettings.textAutofit": "자동조정", "DE.Views.FormSettings.textBackgroundColor": "배경색", "DE.Views.FormSettings.textCheckbox": "체크박스", "DE.Views.FormSettings.textColor": "테두리 색상", "DE.Views.FormSettings.textComb": "문자 조합", "DE.Views.FormSettings.textCombobox": "콤보박스", "DE.Views.FormSettings.textConnected": "연결된 필드", "DE.Views.FormSettings.textDelete": "삭제", "DE.Views.FormSettings.textDisconnect": "연결해제", "DE.Views.FormSettings.textDropDown": "드롭다운", "DE.Views.FormSettings.textField": "텍스트 필드", "DE.Views.FormSettings.textFixed": "필드 크기 고정", "DE.Views.FormSettings.textFromFile": "파일로 부터", "DE.Views.FormSettings.textFromStorage": "스토리지로 부터", "DE.Views.FormSettings.textFromUrl": "URL로 부터", "DE.Views.FormSettings.textGroupKey": "그룹 키", "DE.Views.FormSettings.textImage": "이미지", "DE.Views.FormSettings.textKey": "키", "DE.Views.FormSettings.textLock": "잠금", "DE.Views.FormSettings.textMaxChars": "문자제한", "DE.Views.FormSettings.textMulti": "다중 필드", "DE.Views.FormSettings.textNever": "절대", "DE.Views.FormSettings.textNoBorder": "테두리 없음", "DE.Views.FormSettings.textPlaceholder": "대체표시", "DE.Views.FormSettings.textRadiobox": "라디오 버튼", "DE.Views.FormSettings.textRequired": "필수", "DE.Views.FormSettings.textScale": "확대/축소 시기", "DE.Views.FormSettings.textSelectImage": "이미지 선택", "DE.Views.FormSettings.textTip": "팁", "DE.Views.FormSettings.textTipAdd": "새 값을 추가", "DE.Views.FormSettings.textTipDelete": "값삭제", "DE.Views.FormSettings.textTipDown": "아래로 이동", "DE.Views.FormSettings.textTipUp": "위로 이동", "DE.Views.FormSettings.textTooBig": "이미지가 너무 큽니다", "DE.Views.FormSettings.textTooSmall": "이미지가 너무 작습니다", "DE.Views.FormSettings.textUnlock": "잠금해제", "DE.Views.FormSettings.textValue": "값 옵션", "DE.Views.FormSettings.textWidth": "셀 너비", "DE.Views.FormsTab.capBtnCheckBox": "체크박스", "DE.Views.FormsTab.capBtnComboBox": "콤보박스", "DE.Views.FormsTab.capBtnDropDown": "드롭다운", "DE.Views.FormsTab.capBtnImage": "이미지", "DE.Views.FormsTab.capBtnNext": "다음 필드", "DE.Views.FormsTab.capBtnPrev": "이전 필드", "DE.Views.FormsTab.capBtnRadioBox": "라디오 버튼", "DE.Views.FormsTab.capBtnSaveForm": "템플릿으로 저장", "DE.Views.FormsTab.capBtnSubmit": "전송", "DE.Views.FormsTab.capBtnText": "텍스트 필드", "DE.Views.FormsTab.capBtnView": "양식 보기", "DE.Views.FormsTab.textClear": "필드 지우기", "DE.Views.FormsTab.textClearFields": "모든 필드 지우기", "DE.Views.FormsTab.textCreateForm": "필드를 추가하여 작성 가능한 OFORM 문서 작성", "DE.Views.FormsTab.textGotIt": "취득", "DE.Views.FormsTab.textHighlight": "강조 설정", "DE.Views.FormsTab.textNoHighlight": "강조 표시되지 않음", "DE.Views.FormsTab.textRequired": "양식을 보내려면 모든 필수 필드를 채우십시오.", "DE.Views.FormsTab.textSubmited": "폼 전송 성공", "DE.Views.FormsTab.tipCheckBox": "체크박스 삽입", "DE.Views.FormsTab.tipComboBox": "콤보박스 삽입", "DE.Views.FormsTab.tipDropDown": "드롭다운 목록 삽입", "DE.Views.FormsTab.tipImageField": "이미지 삽입", "DE.Views.FormsTab.tipNextForm": "다음 필드로 이동", "DE.Views.FormsTab.tipPrevForm": "이전 필드로 이동", "DE.Views.FormsTab.tipRadioBox": "라디오버튼 삽입", "DE.Views.FormsTab.tipSaveForm": "채우기 형식 문서로 저장", "DE.Views.FormsTab.tipSubmit": "전송폼", "DE.Views.FormsTab.tipTextField": "텍스트 필드 삽입", "DE.Views.FormsTab.tipViewForm": "양식 보기", "DE.Views.FormsTab.txtUntitled": "이름없음", "DE.Views.HeaderFooterSettings.textBottomCenter": "하단 중앙", "DE.Views.HeaderFooterSettings.textBottomLeft": "왼쪽 하단", "DE.Views.HeaderFooterSettings.textBottomPage": "페이지 끝", "DE.Views.HeaderFooterSettings.textBottomRight": "오른쪽 하단", "DE.Views.HeaderFooterSettings.textDiffFirst": "첫 페이지를 다르게 지정", "DE.Views.HeaderFooterSettings.textDiffOdd": "다른 홀수 및 짝수 페이지", "DE.Views.HeaderFooterSettings.textFrom": "시작 시간", "DE.Views.HeaderFooterSettings.textHeaderFromBottom": "하단에서 바닥 글", "DE.Views.HeaderFooterSettings.textHeaderFromTop": "머리글을 맨 위부터", "DE.Views.HeaderFooterSettings.textInsertCurrent": "현재 위치로 삽입", "DE.Views.HeaderFooterSettings.textOptions": "옵션", "DE.Views.HeaderFooterSettings.textPageNum": "페이지 번호 삽입", "DE.Views.HeaderFooterSettings.textPageNumbering": "페이지 넘버링", "DE.Views.HeaderFooterSettings.textPosition": "위치", "DE.Views.HeaderFooterSettings.textPrev": "이전 섹션에서 계속하기", "DE.Views.HeaderFooterSettings.textSameAs": "이전 링크", "DE.Views.HeaderFooterSettings.textTopCenter": "Top Center", "DE.Views.HeaderFooterSettings.textTopLeft": "왼쪽 상단", "DE.Views.HeaderFooterSettings.textTopPage": "페이지 시작", "DE.Views.HeaderFooterSettings.textTopRight": "오른쪽 상단", "DE.Views.HyperlinkSettingsDialog.textDefault": "선택한 텍스트 조각", "DE.Views.HyperlinkSettingsDialog.textDisplay": "표시", "DE.Views.HyperlinkSettingsDialog.textExternal": "외부 링크", "DE.Views.HyperlinkSettingsDialog.textInternal": "문서의 현재 위치", "DE.Views.HyperlinkSettingsDialog.textTitle": "하이퍼 링크 설정", "DE.Views.HyperlinkSettingsDialog.textTooltip": "스크린팁 텍스트", "DE.Views.HyperlinkSettingsDialog.textUrl": "링크 대상", "DE.Views.HyperlinkSettingsDialog.txtBeginning": "문서의 시작", "DE.Views.HyperlinkSettingsDialog.txtBookmarks": "즐겨 찾기", "DE.Views.HyperlinkSettingsDialog.txtEmpty": "이 입력란은 필수 항목입니다.", "DE.Views.HyperlinkSettingsDialog.txtHeadings": "제목", "DE.Views.HyperlinkSettingsDialog.txtNotUrl": "이 필드는 \"http://www.example.com\"형식의 URL이어야합니다.", "DE.Views.HyperlinkSettingsDialog.txtSizeLimit": "이 필드는 2083 자로 제한되어 있습니다", "DE.Views.ImageSettings.textAdvanced": "고급 설정 표시", "DE.Views.ImageSettings.textCrop": "자르기", "DE.Views.ImageSettings.textCropFill": "채우기", "DE.Views.ImageSettings.textCropFit": "맞춤", "DE.Views.ImageSettings.textCropToShape": "도형에 맞게 자르기", "DE.Views.ImageSettings.textEdit": "편집", "DE.Views.ImageSettings.textEditObject": "개체 편집", "DE.Views.ImageSettings.textFitMargins": "여백에 맞추기", "DE.Views.ImageSettings.textFlip": "대칭", "DE.Views.ImageSettings.textFromFile": "파일로부터", "DE.Views.ImageSettings.textFromStorage": "스토리지로 부터", "DE.Views.ImageSettings.textFromUrl": "From URL", "DE.Views.ImageSettings.textHeight": "높이", "DE.Views.ImageSettings.textHint270": "왼쪽으로 90도 회전", "DE.Views.ImageSettings.textHint90": "오른쪽으로 90도 회전", "DE.Views.ImageSettings.textHintFlipH": "좌우대칭", "DE.Views.ImageSettings.textHintFlipV": "상하대칭", "DE.Views.ImageSettings.textInsert": "이미지 바꾸기", "DE.Views.ImageSettings.textOriginalSize": "실제 크기", "DE.Views.ImageSettings.textRecentlyUsed": "최근 사용된", "DE.Views.ImageSettings.textRotate90": "90도 회전", "DE.Views.ImageSettings.textRotation": "회전", "DE.Views.ImageSettings.textSize": "크기", "DE.Views.ImageSettings.textWidth": "너비", "DE.Views.ImageSettings.textWrap": "포장 스타일", "DE.Views.ImageSettings.txtBehind": "텍스트 뒤", "DE.Views.ImageSettings.txtInFront": "텍스트 앞에", "DE.Views.ImageSettings.txtInline": "텍스트에 맞춰", "DE.Views.ImageSettings.txtSquare": "Square", "DE.Views.ImageSettings.txtThrough": "통해", "DE.Views.ImageSettings.txtTight": "Tight", "DE.Views.ImageSettings.txtTopAndBottom": "상단 및 하단", "DE.Views.ImageSettingsAdvanced.strMargins": "텍스트 채우기", "DE.Views.ImageSettingsAdvanced.textAbsoluteWH": "Absolute", "DE.Views.ImageSettingsAdvanced.textAlignment": "정렬", "DE.Views.ImageSettingsAdvanced.textAlt": "대체 텍스트", "DE.Views.ImageSettingsAdvanced.textAltDescription": "설명", "DE.Views.ImageSettingsAdvanced.textAltTip": "시각적 개체 정보의 교체는 텍스트 표현을 기반으로 하며 시각 또는 인지 장애가 있는 사람들이 이미지, 자동 모양, 차트 또는 표에 포함된 정보를 더 잘 이해할 수 있도록 읽어줍니다.", "DE.Views.ImageSettingsAdvanced.textAltTitle": "Title", "DE.Views.ImageSettingsAdvanced.textAngle": "각도", "DE.Views.ImageSettingsAdvanced.textArrows": "화살표", "DE.Views.ImageSettingsAdvanced.textAspectRatio": "가로 세로 비율 고정", "DE.Views.ImageSettingsAdvanced.textAutofit": "자동조정", "DE.Views.ImageSettingsAdvanced.textBeginSize": "크기 시작", "DE.Views.ImageSettingsAdvanced.textBeginStyle": "스타일 시작", "DE.Views.ImageSettingsAdvanced.textBelow": "below", "DE.Views.ImageSettingsAdvanced.textBevel": "<PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textBottom": "Bottom", "DE.Views.ImageSettingsAdvanced.textBottomMargin": "Bottom Margin", "DE.Views.ImageSettingsAdvanced.textBtnWrap": "텍스트 줄 바꿈", "DE.Views.ImageSettingsAdvanced.textCapType": "Cap Type", "DE.Views.ImageSettingsAdvanced.textCenter": "Center", "DE.Views.ImageSettingsAdvanced.textCharacter": "Character", "DE.Views.ImageSettingsAdvanced.textColumn": "Column", "DE.Views.ImageSettingsAdvanced.textDistance": "텍스트 간격", "DE.Views.ImageSettingsAdvanced.textEndSize": "최종 크기", "DE.Views.ImageSettingsAdvanced.textEndStyle": "끝 스타일", "DE.Views.ImageSettingsAdvanced.textFlat": "Flat", "DE.Views.ImageSettingsAdvanced.textFlipped": "대칭됨", "DE.Views.ImageSettingsAdvanced.textHeight": "높이", "DE.Views.ImageSettingsAdvanced.textHorizontal": "Horizontal", "DE.Views.ImageSettingsAdvanced.textHorizontally": "수평", "DE.Views.ImageSettingsAdvanced.textJoinType": "조인 유형", "DE.Views.ImageSettingsAdvanced.textKeepRatio": "상수 비율", "DE.Views.ImageSettingsAdvanced.textLeft": "왼쪽", "DE.Views.ImageSettingsAdvanced.textLeftMargin": "왼쪽 여백", "DE.Views.ImageSettingsAdvanced.textLine": "Line", "DE.Views.ImageSettingsAdvanced.textLineStyle": "선 스타일", "DE.Views.ImageSettingsAdvanced.textMargin": "여백", "DE.Views.ImageSettingsAdvanced.textMiter": "연귀", "DE.Views.ImageSettingsAdvanced.textMove": "텍스트가있는 객체 이동", "DE.Views.ImageSettingsAdvanced.textOptions": "옵션", "DE.Views.ImageSettingsAdvanced.textOriginalSize": "실제 크기", "DE.Views.ImageSettingsAdvanced.textOverlap": "중복 허용", "DE.Views.ImageSettingsAdvanced.textPage": "페이지", "DE.Views.ImageSettingsAdvanced.textParagraph": "단락", "DE.Views.ImageSettingsAdvanced.textPosition": "위치", "DE.Views.ImageSettingsAdvanced.textPositionPc": "상대 위치", "DE.Views.ImageSettingsAdvanced.textRelative": "상대적", "DE.Views.ImageSettingsAdvanced.textRelativeWH": "상대적", "DE.Views.ImageSettingsAdvanced.textResizeFit": "텍스트에 맞게 모양 조정", "DE.Views.ImageSettingsAdvanced.textRight": "오른쪽", "DE.Views.ImageSettingsAdvanced.textRightMargin": "오른쪽 여백", "DE.Views.ImageSettingsAdvanced.textRightOf": "오른쪽에", "DE.Views.ImageSettingsAdvanced.textRotation": "회전", "DE.Views.ImageSettingsAdvanced.textRound": "Round", "DE.Views.ImageSettingsAdvanced.textShape": "도형 설정", "DE.Views.ImageSettingsAdvanced.textSize": "크기", "DE.Views.ImageSettingsAdvanced.textSquare": "Square", "DE.Views.ImageSettingsAdvanced.textTextBox": "텍스트 상자", "DE.Views.ImageSettingsAdvanced.textTitle": "이미지 - 고급 설정", "DE.Views.ImageSettingsAdvanced.textTitleChart": "차트 - 고급 설정", "DE.Views.ImageSettingsAdvanced.textTitleShape": "모양 - 고급 설정", "DE.Views.ImageSettingsAdvanced.textTop": "Top", "DE.Views.ImageSettingsAdvanced.textTopMargin": "Top Margin", "DE.Views.ImageSettingsAdvanced.textVertical": "Vertical", "DE.Views.ImageSettingsAdvanced.textVertically": "세로", "DE.Views.ImageSettingsAdvanced.textWeightArrows": "가중치 및 화살표", "DE.Views.ImageSettingsAdvanced.textWidth": "너비", "DE.Views.ImageSettingsAdvanced.textWrap": "포장 스타일", "DE.Views.ImageSettingsAdvanced.textWrapBehindTooltip": "텍스트 뒤", "DE.Views.ImageSettingsAdvanced.textWrapInFrontTooltip": "텍스트 앞에", "DE.Views.ImageSettingsAdvanced.textWrapInlineTooltip": "텍스트에 맞춰", "DE.Views.ImageSettingsAdvanced.textWrapSquareTooltip": "Square", "DE.Views.ImageSettingsAdvanced.textWrapThroughTooltip": "통해", "DE.Views.ImageSettingsAdvanced.textWrapTightTooltip": "Tight", "DE.Views.ImageSettingsAdvanced.textWrapTopbottomTooltip": "상단 및 하단", "DE.Views.LeftMenu.tipAbout": "About", "DE.Views.LeftMenu.tipChat": "채팅", "DE.Views.LeftMenu.tipComments": "Comments", "DE.Views.LeftMenu.tipNavigation": "네비게이션", "DE.Views.LeftMenu.tipPlugins": "플러그인", "DE.Views.LeftMenu.tipSearch": "검색", "DE.Views.LeftMenu.tipSupport": "피드백 및 지원", "DE.Views.LeftMenu.tipTitles": "제목", "DE.Views.LeftMenu.txtDeveloper": "개발자 모드", "DE.Views.LeftMenu.txtLimit": "접근제한", "DE.Views.LeftMenu.txtTrial": "시험 모드", "DE.Views.LeftMenu.txtTrialDev": "개발자 모드 시도", "DE.Views.LineNumbersDialog.textAddLineNumbering": "행 번호를 추가", "DE.Views.LineNumbersDialog.textApplyTo": "변경 사항 적용", "DE.Views.LineNumbersDialog.textContinuous": "계속", "DE.Views.LineNumbersDialog.textCountBy": "줄 번호 증가", "DE.Views.LineNumbersDialog.textDocument": "전체 문서", "DE.Views.LineNumbersDialog.textForward": "Point 앞으로", "DE.Views.LineNumbersDialog.textFromText": "텍스트로 부터", "DE.Views.LineNumbersDialog.textNumbering": "번호 매기기", "DE.Views.LineNumbersDialog.textRestartEachPage": "모든 페이지 다시 시작", "DE.Views.LineNumbersDialog.textRestartEachSection": "각 섹션 다시 시작", "DE.Views.LineNumbersDialog.textSection": "현재 섹션", "DE.Views.LineNumbersDialog.textStartAt": "시작", "DE.Views.LineNumbersDialog.textTitle": "행번호", "DE.Views.LineNumbersDialog.txtAutoText": "자동", "DE.Views.Links.capBtnAddText": "텍스트추가", "DE.Views.Links.capBtnBookmarks": "즐겨찾기", "DE.Views.Links.capBtnCaption": "참조", "DE.Views.Links.capBtnContentsUpdate": "표 업데이트", "DE.Views.Links.capBtnCrossRef": "상호 참조", "DE.Views.Links.capBtnInsContents": "콘텍츠 테이블", "DE.Views.Links.capBtnInsFootnote": "각주", "DE.Views.Links.capBtnInsLink": "하이퍼 링크", "DE.Views.Links.capBtnTOF": "목차", "DE.Views.Links.confirmDeleteFootnotes": "모든 각주를 삭제 하시겠습니까?", "DE.Views.Links.confirmReplaceTOF": "선택한 목차를 바꾸시겠습니까?", "DE.Views.Links.mniConvertNote": "모든 메모 변환", "DE.Views.Links.mniDelFootnote": "모든 메모 삭제", "DE.Views.Links.mniInsEndnote": "미주 삽입", "DE.Views.Links.mniInsFootnote": "각주 삽입", "DE.Views.Links.mniNoteSettings": "메모 설정", "DE.Views.Links.textContentsRemove": "콘텐츠 테이블을 지우세요", "DE.Views.Links.textContentsSettings": "세팅", "DE.Views.Links.textConvertToEndnotes": "모든 각주를 미주로 변환", "DE.Views.Links.textConvertToFootnotes": "모든 미주를 각주로 변환", "DE.Views.Links.textGotoEndnote": "미주로 이동", "DE.Views.Links.textGotoFootnote": "각주로 이동", "DE.Views.Links.textSwapNotes": "각주와 미주 바꾸기", "DE.Views.Links.textUpdateAll": "전체 테이블을 새로고침하세요", "DE.Views.Links.textUpdatePages": "페이지 번호만 업테이트 하세요", "DE.Views.Links.tipBookmarks": "책갈피 만들기", "DE.Views.Links.tipCaption": "캡션 삽입", "DE.Views.Links.tipContents": "콘텐트 테이블 삽입", "DE.Views.Links.tipContentsUpdate": "콘텐트 테이블 새로고침", "DE.Views.Links.tipCrossRef": "상호 참조 삽입", "DE.Views.Links.tipInsertHyperlink": "하이퍼 링크 추가", "DE.Views.Links.tipNotes": "각주 삽입 또는 편집", "DE.Views.Links.tipTableFigures": "목차 삽입", "DE.Views.Links.tipTableFiguresUpdate": "도표 업데이트", "DE.Views.Links.titleUpdateTOF": "도표 업데이트", "DE.Views.ListSettingsDialog.textAuto": "자동", "DE.Views.ListSettingsDialog.textCenter": "가운데", "DE.Views.ListSettingsDialog.textLeft": "왼쪽", "DE.Views.ListSettingsDialog.textLevel": "레벨", "DE.Views.ListSettingsDialog.textPreview": "미리보기", "DE.Views.ListSettingsDialog.textRight": "오른쪽", "DE.Views.ListSettingsDialog.txtAlign": "맞춤", "DE.Views.ListSettingsDialog.txtBullet": "글머리 기호", "DE.Views.ListSettingsDialog.txtColor": "색상", "DE.Views.ListSettingsDialog.txtFont": "글꼴 및 기호", "DE.Views.ListSettingsDialog.txtLikeText": "텍스트처럼", "DE.Views.ListSettingsDialog.txtNewBullet": "새로운 글머리 기호", "DE.Views.ListSettingsDialog.txtNone": "없음", "DE.Views.ListSettingsDialog.txtSize": "크기", "DE.Views.ListSettingsDialog.txtSymbol": "기호", "DE.Views.ListSettingsDialog.txtTitle": "목록설정", "DE.Views.ListSettingsDialog.txtType": "유형", "DE.Views.MailMergeEmailDlg.filePlaceholder": "PDF", "DE.Views.MailMergeEmailDlg.okButtonText": "보내기", "DE.Views.MailMergeEmailDlg.subjectPlaceholder": "테마", "DE.Views.MailMergeEmailDlg.textAttachDocx": "DOCX로 첨부", "DE.Views.MailMergeEmailDlg.textAttachPdf": "PDF로 첨부", "DE.Views.MailMergeEmailDlg.textFileName": "파일 이름", "DE.Views.MailMergeEmailDlg.textFormat": "메일 형식", "DE.Views.MailMergeEmailDlg.textFrom": "보낸 사람", "DE.Views.MailMergeEmailDlg.textHTML": "HTML", "DE.Views.MailMergeEmailDlg.textMessage": "메시지", "DE.Views.MailMergeEmailDlg.textSubject": "Subject Line", "DE.Views.MailMergeEmailDlg.textTitle": "이메일로 보내기", "DE.Views.MailMergeEmailDlg.textTo": "받는 사람", "DE.Views.MailMergeEmailDlg.textWarning": "경고!", "DE.Views.MailMergeEmailDlg.textWarningMsg": "일단 '보내기'버튼을 클릭하면 메일 링을 중지 할 수 없습니다.", "DE.Views.MailMergeSettings.downloadMergeTitle": "병합", "DE.Views.MailMergeSettings.errorMailMergeSaveFile": "병합하지 못했습니다.", "DE.Views.MailMergeSettings.notcriticalErrorTitle": "경고", "DE.Views.MailMergeSettings.textAddRecipients": "먼저 목록에 수신자를 추가하십시오", "DE.Views.MailMergeSettings.textAll": "모든 레코드", "DE.Views.MailMergeSettings.textCurrent": "현재 레코드", "DE.Views.MailMergeSettings.textDataSource": "데이터 소스", "DE.Views.MailMergeSettings.textDocx": "Docx", "DE.Views.MailMergeSettings.textDownload": "다운로드", "DE.Views.MailMergeSettings.textEditData": "받는 사람 목록 편집", "DE.Views.MailMergeSettings.textEmail": "Email", "DE.Views.MailMergeSettings.textFrom": "보낸 사람", "DE.Views.MailMergeSettings.textGoToMail": "메일로 이동", "DE.Views.MailMergeSettings.textHighlight": "병합 필드 강조 표시", "DE.Views.MailMergeSettings.textInsertField": "병합 필드 삽입", "DE.Views.MailMergeSettings.textMaxRecepients": "최대 100 명의 수신자.", "DE.Views.MailMergeSettings.textMerge": "병합", "DE.Views.MailMergeSettings.textMergeFields": "<PERSON><PERSON>", "DE.Views.MailMergeSettings.textMergeTo": "Merge to", "DE.Views.MailMergeSettings.textPdf": "PDF", "DE.Views.MailMergeSettings.textPortal": "저장", "DE.Views.MailMergeSettings.textPreview": "결과 미리보기", "DE.Views.MailMergeSettings.textReadMore": "Read more", "DE.Views.MailMergeSettings.textSendMsg": "모든 메일 메시지가 준비되어 있으며 일정 시간 내에 발송됩니다. <br> 우편 발송 속도는 메일 서비스에 따라 다릅니다. <br> 문서 작업을 계속하거나 닫을 수 있습니다 . 작업이 끝나면 등록 이메일 주소로 알림이 전송됩니다. ", "DE.Views.MailMergeSettings.textTo": "받는 사람", "DE.Views.MailMergeSettings.txtFirst": "처음 녹화", "DE.Views.MailMergeSettings.txtFromToError": "\"사작\"의 값은 \"완료\"의 값보다 작아야 합니다.", "DE.Views.MailMergeSettings.txtLast": "마지막 기록", "DE.Views.MailMergeSettings.txtNext": "다음 레코드로", "DE.Views.MailMergeSettings.txtPrev": "이전 레코드로", "DE.Views.MailMergeSettings.txtUntitled": "제목미정", "DE.Views.MailMergeSettings.warnProcessMailMerge": "병합 시작 실패", "DE.Views.Navigation.txtCollapse": "모두 접기", "DE.Views.Navigation.txtDemote": "강등", "DE.Views.Navigation.txtEmpty": "문서에 제목이 없습니다. <br>텍스트에 제목 스타일을 적용하여 목차에 표시되도록 합니다.", "DE.Views.Navigation.txtEmptyItem": "머리말 없슴", "DE.Views.Navigation.txtEmptyViewer": "문서에 제목이 없습니다. ", "DE.Views.Navigation.txtExpand": "모두 확장", "DE.Views.Navigation.txtExpandToLevel": "레벨로 확장하기", "DE.Views.Navigation.txtHeadingAfter": "뒤에 신규 머리글 ", "DE.Views.Navigation.txtHeadingBefore": "전에 신규 머리글 ", "DE.Views.Navigation.txtNewHeading": "신규 서브헤딩", "DE.Views.Navigation.txtPromote": "승급", "DE.Views.Navigation.txtSelect": "콘텐트 선택", "DE.Views.NoteSettingsDialog.textApply": "적용", "DE.Views.NoteSettingsDialog.textApplyTo": "변경 사항 적용", "DE.Views.NoteSettingsDialog.textContinue": "연속", "DE.Views.NoteSettingsDialog.textCustom": "사용자 정의 표시", "DE.Views.NoteSettingsDialog.textDocEnd": "문서의 마지막", "DE.Views.NoteSettingsDialog.textDocument": "전체 문서", "DE.Views.NoteSettingsDialog.textEachPage": "각 페이지 다시 시작", "DE.Views.NoteSettingsDialog.textEachSection": "각 섹션 다시 시작", "DE.Views.NoteSettingsDialog.textEndnote": "미주", "DE.Views.NoteSettingsDialog.textFootnote": "각주", "DE.Views.NoteSettingsDialog.textFormat": "Format", "DE.Views.NoteSettingsDialog.textInsert": "삽입", "DE.Views.NoteSettingsDialog.textLocation": "위치", "DE.Views.NoteSettingsDialog.textNumbering": "번호 매기기", "DE.Views.NoteSettingsDialog.textNumFormat": "숫자 형식", "DE.Views.NoteSettingsDialog.textPageBottom": "페이지 하단", "DE.Views.NoteSettingsDialog.textSectEnd": "섹션의 끝", "DE.Views.NoteSettingsDialog.textSection": "현재 섹션", "DE.Views.NoteSettingsDialog.textStart": "시작 시간", "DE.Views.NoteSettingsDialog.textTextBottom": "텍스트 아래에", "DE.Views.NoteSettingsDialog.textTitle": "메모 설정", "DE.Views.NotesRemoveDialog.textEnd": "모든 미주 삭제", "DE.Views.NotesRemoveDialog.textFoot": "모든 각주 삭제", "DE.Views.NotesRemoveDialog.textTitle": "메모 삭제", "DE.Views.PageMarginsDialog.notcriticalErrorTitle": "경고", "DE.Views.PageMarginsDialog.textBottom": "Bottom", "DE.Views.PageMarginsDialog.textGutter": "홈", "DE.Views.PageMarginsDialog.textGutterPosition": "홈위치", "DE.Views.PageMarginsDialog.textInside": "내부", "DE.Views.PageMarginsDialog.textLandscape": "수평", "DE.Views.PageMarginsDialog.textLeft": "왼쪽", "DE.Views.PageMarginsDialog.textMirrorMargins": "좌우 대칭의 여백", "DE.Views.PageMarginsDialog.textMultiplePages": "여러 페이지", "DE.Views.PageMarginsDialog.textNormal": "표준", "DE.Views.PageMarginsDialog.textOrientation": "방향", "DE.Views.PageMarginsDialog.textOutside": "외부", "DE.Views.PageMarginsDialog.textPortrait": "세로", "DE.Views.PageMarginsDialog.textPreview": "미리보기", "DE.Views.PageMarginsDialog.textRight": "오른쪽", "DE.Views.PageMarginsDialog.textTitle": "여백", "DE.Views.PageMarginsDialog.textTop": "Top", "DE.Views.PageMarginsDialog.txtMarginsH": "주어진 페이지 높이에 대해 위쪽 및 아래쪽 여백이 너무 높습니다.", "DE.Views.PageMarginsDialog.txtMarginsW": "왼쪽 및 오른쪽 여백이 주어진 페이지 너비에 비해 너무 넓습니다.", "DE.Views.PageSizeDialog.textHeight": "높이", "DE.Views.PageSizeDialog.textPreset": "미리 설정된", "DE.Views.PageSizeDialog.textTitle": "페이지 크기", "DE.Views.PageSizeDialog.textWidth": "너비", "DE.Views.PageSizeDialog.txtCustom": "사용자 정의", "DE.Views.PageThumbnails.textClosePanel": "페이지 썸네일 닫기", "DE.Views.PageThumbnails.textHighlightVisiblePart": "페이지에서 보이는 부분 강조 표시", "DE.Views.PageThumbnails.textPageThumbnails": "페이지 썸네일", "DE.Views.PageThumbnails.textThumbnailsSettings": "썸네일 설정", "DE.Views.PageThumbnails.textThumbnailsSize": "썸네일 크기", "DE.Views.ParagraphSettings.strIndent": "들여쓰기", "DE.Views.ParagraphSettings.strIndentsLeftText": "왼쪽", "DE.Views.ParagraphSettings.strIndentsRightText": "오른쪽", "DE.Views.ParagraphSettings.strIndentsSpecial": "첫줄", "DE.Views.ParagraphSettings.strLineHeight": "줄 간격", "DE.Views.ParagraphSettings.strParagraphSpacing": "단락 간격", "DE.Views.ParagraphSettings.strSomeParagraphSpace": "같은 스타일의 단락 사이에 공백 삽입 안 함", "DE.Views.ParagraphSettings.strSpacingAfter": "이후", "DE.Views.ParagraphSettings.strSpacingBefore": "단락 앞", "DE.Views.ParagraphSettings.textAdvanced": "고급 설정 표시", "DE.Views.ParagraphSettings.textAt": "At", "DE.Views.ParagraphSettings.textAtLeast": "적어도", "DE.Views.ParagraphSettings.textAuto": "배수", "DE.Views.ParagraphSettings.textBackColor": "배경색", "DE.Views.ParagraphSettings.textExact": "정확히", "DE.Views.ParagraphSettings.textFirstLine": "첫 번째 줄", "DE.Views.ParagraphSettings.textHanging": "둘째 줄 이하", "DE.Views.ParagraphSettings.textNoneSpecial": "(없음)", "DE.Views.ParagraphSettings.txtAutoText": "Auto", "DE.Views.ParagraphSettingsAdvanced.noTabs": "지정된 탭이이 필드에 나타납니다", "DE.Views.ParagraphSettingsAdvanced.strAllCaps": "모든 대문자", "DE.Views.ParagraphSettingsAdvanced.strBorders": "테두리 및 채우기", "DE.Views.ParagraphSettingsAdvanced.strBreakBefore": "현재 단락 앞에서 페이지 나누기", "DE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "이중 취소선", "DE.Views.ParagraphSettingsAdvanced.strIndent": "들여쓰기", "DE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "Left", "DE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "줄 간격", "DE.Views.ParagraphSettingsAdvanced.strIndentsOutlinelevel": "개요 수준", "DE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "오른쪽", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "이후", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "단락 앞", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "첫줄", "DE.Views.ParagraphSettingsAdvanced.strKeepLines": "현재 단락을 나누지 않음", "DE.Views.ParagraphSettingsAdvanced.strKeepNext": "현재 단락과 다음 단락을 항상 같은 페이지에 배치", "DE.Views.ParagraphSettingsAdvanced.strMargins": "Paddings", "DE.Views.ParagraphSettingsAdvanced.strOrphan": "고아 컨트롤", "DE.Views.ParagraphSettingsAdvanced.strParagraphFont": "글꼴", "DE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "들여쓰기 및 간격", "DE.Views.ParagraphSettingsAdvanced.strParagraphLine": "줄 바꿈 및 페이지 나누기", "DE.Views.ParagraphSettingsAdvanced.strParagraphPosition": "게재 위치", "DE.Views.ParagraphSettingsAdvanced.strSmallCaps": "작은 대문자", "DE.Views.ParagraphSettingsAdvanced.strSomeParagraphSpace": "같은 스타일의 단락 사이에 공백 삽입 안 함", "DE.Views.ParagraphSettingsAdvanced.strSpacing": "간격", "DE.Views.ParagraphSettingsAdvanced.strStrike": "취소선", "DE.Views.ParagraphSettingsAdvanced.strSubscript": "Subscript", "DE.Views.ParagraphSettingsAdvanced.strSuperscript": "Superscript", "DE.Views.ParagraphSettingsAdvanced.strSuppressLineNumbers": "줄 번호 중지", "DE.Views.ParagraphSettingsAdvanced.strTabs": "탭", "DE.Views.ParagraphSettingsAdvanced.textAlign": "정렬", "DE.Views.ParagraphSettingsAdvanced.textAtLeast": "최소", "DE.Views.ParagraphSettingsAdvanced.textAuto": "배수", "DE.Views.ParagraphSettingsAdvanced.textBackColor": "배경색", "DE.Views.ParagraphSettingsAdvanced.textBodyText": "기본 텍스트", "DE.Views.ParagraphSettingsAdvanced.textBorderColor": "Border Color", "DE.Views.ParagraphSettingsAdvanced.textBorderDesc": "다이어그램을 클릭하거나 단추를 사용하여 테두리를 선택하고 선택한 스타일을 적용", "DE.Views.ParagraphSettingsAdvanced.textBorderWidth": "테두리 크기", "DE.Views.ParagraphSettingsAdvanced.textBottom": "Bottom", "DE.Views.ParagraphSettingsAdvanced.textCentered": "가운데", "DE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "문자 간격", "DE.Views.ParagraphSettingsAdvanced.textDefault": "기본 탭", "DE.Views.ParagraphSettingsAdvanced.textEffects": "효과", "DE.Views.ParagraphSettingsAdvanced.textExact": "고정", "DE.Views.ParagraphSettingsAdvanced.textFirstLine": "첫 번째 줄", "DE.Views.ParagraphSettingsAdvanced.textHanging": "둘째 줄 이하", "DE.Views.ParagraphSettingsAdvanced.textJustified": "균등분할", "DE.Views.ParagraphSettingsAdvanced.textLeader": "리더", "DE.Views.ParagraphSettingsAdvanced.textLeft": "왼쪽", "DE.Views.ParagraphSettingsAdvanced.textLevel": "레벨", "DE.Views.ParagraphSettingsAdvanced.textNone": "없음", "DE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(없음)", "DE.Views.ParagraphSettingsAdvanced.textPosition": "위치", "DE.Views.ParagraphSettingsAdvanced.textRemove": "삭제", "DE.Views.ParagraphSettingsAdvanced.textRemoveAll": "모두 제거", "DE.Views.ParagraphSettingsAdvanced.textRight": "오른쪽", "DE.Views.ParagraphSettingsAdvanced.textSet": "지정", "DE.Views.ParagraphSettingsAdvanced.textSpacing": "간격", "DE.Views.ParagraphSettingsAdvanced.textTabCenter": "Center", "DE.Views.ParagraphSettingsAdvanced.textTabLeft": "왼쪽", "DE.Views.ParagraphSettingsAdvanced.textTabPosition": "탭 위치", "DE.Views.ParagraphSettingsAdvanced.textTabRight": "오른쪽", "DE.Views.ParagraphSettingsAdvanced.textTitle": "단락 - 고급 설정", "DE.Views.ParagraphSettingsAdvanced.textTop": "Top", "DE.Views.ParagraphSettingsAdvanced.tipAll": "바깥쪽 테두리 및 안쪽 테두리", "DE.Views.ParagraphSettingsAdvanced.tipBottom": "아래쪽 테두리", "DE.Views.ParagraphSettingsAdvanced.tipInner": "안쪽 가로 테두리", "DE.Views.ParagraphSettingsAdvanced.tipLeft": "왼쪽 테두리", "DE.Views.ParagraphSettingsAdvanced.tipNone": "테두리 없음 설정", "DE.Views.ParagraphSettingsAdvanced.tipOuter": "바깥쪽 테두리", "DE.Views.ParagraphSettingsAdvanced.tipRight": "오른쪽 테두리", "DE.Views.ParagraphSettingsAdvanced.tipTop": "위쪽 테두리", "DE.Views.ParagraphSettingsAdvanced.txtAutoText": "자동", "DE.Views.ParagraphSettingsAdvanced.txtNoBorders": "테두리 없음", "DE.Views.ProtectDialog.txtProtect": "보호", "DE.Views.ProtectDialog.txtTitle": "보호", "DE.Views.RightMenu.txtChartSettings": "차트 설정", "DE.Views.RightMenu.txtFormSettings": "폼 설정", "DE.Views.RightMenu.txtHeaderFooterSettings": "머리글 및 바닥 글 설정", "DE.Views.RightMenu.txtImageSettings": "이미지 설정", "DE.Views.RightMenu.txtMailMergeSettings": "편지 병합 설정", "DE.Views.RightMenu.txtParagraphSettings": "단락 설정", "DE.Views.RightMenu.txtShapeSettings": "도형 설정", "DE.Views.RightMenu.txtSignatureSettings": "서명 세팅", "DE.Views.RightMenu.txtTableSettings": "표 설정", "DE.Views.RightMenu.txtTextArtSettings": "텍스트 아트 설정", "DE.Views.ShapeSettings.strBackground": "배경색", "DE.Views.ShapeSettings.strChange": "도형 변경", "DE.Views.ShapeSettings.strColor": "Color", "DE.Views.ShapeSettings.strFill": "채우기", "DE.Views.ShapeSettings.strForeground": "전경색", "DE.Views.ShapeSettings.strPattern": "패턴", "DE.Views.ShapeSettings.strShadow": "음영 표시", "DE.Views.ShapeSettings.strSize": "크기", "DE.Views.ShapeSettings.strStroke": "선", "DE.Views.ShapeSettings.strTransparency": "투명도", "DE.Views.ShapeSettings.strType": "유형", "DE.Views.ShapeSettings.textAdvanced": "고급 설정 표시", "DE.Views.ShapeSettings.textAngle": "각도", "DE.Views.ShapeSettings.textBorderSizeErr": "입력 한 값이 잘못되었습니다. <br> 0 ~ 1584pt 사이의 값을 입력하십시오.", "DE.Views.ShapeSettings.textColor": "색상 채우기", "DE.Views.ShapeSettings.textDirection": "Direction", "DE.Views.ShapeSettings.textEmptyPattern": "패턴 없음", "DE.Views.ShapeSettings.textFlip": "대칭", "DE.Views.ShapeSettings.textFromFile": "파일로부터", "DE.Views.ShapeSettings.textFromStorage": "스토리지로 부터", "DE.Views.ShapeSettings.textFromUrl": "URL로부터", "DE.Views.ShapeSettings.textGradient": "Gradient Points", "DE.Views.ShapeSettings.textGradientFill": "그라데이션 채우기", "DE.Views.ShapeSettings.textHint270": "왼쪽으로 90도 회전", "DE.Views.ShapeSettings.textHint90": "오른쪽으로 90도 회전", "DE.Views.ShapeSettings.textHintFlipH": "좌우대칭", "DE.Views.ShapeSettings.textHintFlipV": "상하대칭", "DE.Views.ShapeSettings.textImageTexture": "그림 또는 질감", "DE.Views.ShapeSettings.textLinear": "선형", "DE.Views.ShapeSettings.textNoFill": "채우기 없음", "DE.Views.ShapeSettings.textPatternFill": "패턴", "DE.Views.ShapeSettings.textPosition": "위치", "DE.Views.ShapeSettings.textRadial": "방사형", "DE.Views.ShapeSettings.textRecentlyUsed": "최근 사용된", "DE.Views.ShapeSettings.textRotate90": "90도 회전", "DE.Views.ShapeSettings.textRotation": "회전", "DE.Views.ShapeSettings.textSelectImage": "그림선택", "DE.Views.ShapeSettings.textSelectTexture": "선택", "DE.Views.ShapeSettings.textStretch": "늘이기", "DE.Views.ShapeSettings.textStyle": "스타일", "DE.Views.ShapeSettings.textTexture": "텍스처에서", "DE.Views.ShapeSettings.textTile": "타일", "DE.Views.ShapeSettings.textWrap": "배치 스타일", "DE.Views.ShapeSettings.tipAddGradientPoint": "그라데이션 포인트 추가", "DE.Views.ShapeSettings.tipRemoveGradientPoint": "그라데이션 포인트 제거", "DE.Views.ShapeSettings.txtBehind": "텍스트 뒤", "DE.Views.ShapeSettings.txtBrownPaper": "갈색 종이", "DE.Views.ShapeSettings.txtCanvas": "<PERSON><PERSON>", "DE.Views.ShapeSettings.txtCarton": "<PERSON><PERSON>", "DE.Views.ShapeSettings.txtDarkFabric": "<PERSON> Fab<PERSON>", "DE.Views.ShapeSettings.txtGrain": "Grain", "DE.Views.ShapeSettings.txtGranite": "Granite", "DE.Views.ShapeSettings.txtGreyPaper": "회색 용지", "DE.Views.ShapeSettings.txtInFront": "텍스트 앞에", "DE.Views.ShapeSettings.txtInline": "텍스트에 맞춰", "DE.Views.ShapeSettings.txtKnit": "K<PERSON><PERSON>", "DE.Views.ShapeSettings.txtLeather": "가죽", "DE.Views.ShapeSettings.txtNoBorders": "No Line", "DE.Views.ShapeSettings.txtPapyrus": "파피루스", "DE.Views.ShapeSettings.txtSquare": "Square", "DE.Views.ShapeSettings.txtThrough": "통과", "DE.Views.ShapeSettings.txtTight": "꽉", "DE.Views.ShapeSettings.txtTopAndBottom": "상단 및 하단", "DE.Views.ShapeSettings.txtWood": "<PERSON>", "DE.Views.SignatureSettings.notcriticalErrorTitle": "경고", "DE.Views.SignatureSettings.strDelete": "서명 삭제", "DE.Views.SignatureSettings.strDetails": "서명 상세", "DE.Views.SignatureSettings.strInvalid": "잘못된 서명", "DE.Views.SignatureSettings.strRequested": "요청 서명", "DE.Views.SignatureSettings.strSetup": "서명 셋업", "DE.Views.SignatureSettings.strSign": "서명", "DE.Views.SignatureSettings.strSignature": "서명", "DE.Views.SignatureSettings.strSigner": "서명자", "DE.Views.SignatureSettings.strValid": "유효 서명", "DE.Views.SignatureSettings.txtContinueEditing": "무조건 편집", "DE.Views.SignatureSettings.txtEditWarning": "편집하면 문서의 서명이 삭제됩니다. <br>계속하시겠습니까?", "DE.Views.SignatureSettings.txtRemoveWarning": "이 서명을 삭제하시겠습니까?<br>이 작업은 취소할 수 없습니다.", "DE.Views.SignatureSettings.txtRequestedSignatures": "이 문서는 서명되어야 합니다.", "DE.Views.SignatureSettings.txtSigned": "문서에 유효한 서명이 추가되었습니다. 문서가 보호되어 편집할 수 없습니다.", "DE.Views.SignatureSettings.txtSignedInvalid": "문서의 일부 디지털 서명이 유효하지 않거나 확인할 수 없습니다. 문서가 보호되어 편집할 수 없습니다.", "DE.Views.Statusbar.goToPageText": "페이지로 이동", "DE.Views.Statusbar.pageIndexText": "{1}의 페이지 {0}", "DE.Views.Statusbar.tipFitPage": "페이지에 맞춤", "DE.Views.Statusbar.tipFitWidth": "너비에 맞춤", "DE.Views.Statusbar.tipHandTool": "손도구", "DE.Views.Statusbar.tipSelectTool": "도구 선택", "DE.Views.Statusbar.tipSetLang": "텍스트 언어 설정", "DE.Views.Statusbar.tipZoomFactor": "확대/축소", "DE.Views.Statusbar.tipZoomIn": "확대", "DE.Views.Statusbar.tipZoomOut": "축소", "DE.Views.Statusbar.txtPageNumInvalid": "페이지 번호가 잘못되었습니다.", "DE.Views.StyleTitleDialog.textHeader": "새 스타일 만들기", "DE.Views.StyleTitleDialog.textNextStyle": "다음 단락 스타일", "DE.Views.StyleTitleDialog.textTitle": "제목", "DE.Views.StyleTitleDialog.txtEmpty": "이 입력란은 필수 항목", "DE.Views.StyleTitleDialog.txtNotEmpty": "필드가 비어 있어서는 안됩니다.", "DE.Views.StyleTitleDialog.txtSameAs": "새로 생성된 스타일과 동일하게", "DE.Views.TableFormulaDialog.textBookmark": "책갈피 붙여넣기", "DE.Views.TableFormulaDialog.textFormat": "숫자 형식", "DE.Views.TableFormulaDialog.textFormula": "수식", "DE.Views.TableFormulaDialog.textInsertFunction": "함수 붙여넣기", "DE.Views.TableFormulaDialog.textTitle": "수식설정", "DE.Views.TableOfContentsSettings.strAlign": "오른쪽 정렬 페이지 번호", "DE.Views.TableOfContentsSettings.strFullCaption": "라벨과 번호를 포함", "DE.Views.TableOfContentsSettings.strLinks": "콘텐츠 테이블을 포맷하세요", "DE.Views.TableOfContentsSettings.strLinksOF": "목차 형식을 링크로 변경", "DE.Views.TableOfContentsSettings.strShowPages": "페이지 번호를 보여주세요", "DE.Views.TableOfContentsSettings.textBuildTable": "콘텐츠 테이블을 작성하세요", "DE.Views.TableOfContentsSettings.textBuildTableOF": "목차 폼 만들기", "DE.Views.TableOfContentsSettings.textEquation": "방정식", "DE.Views.TableOfContentsSettings.textFigure": "숫자", "DE.Views.TableOfContentsSettings.textLeader": "리더", "DE.Views.TableOfContentsSettings.textLevel": "레벨", "DE.Views.TableOfContentsSettings.textLevels": "레벨들", "DE.Views.TableOfContentsSettings.textNone": "없음", "DE.Views.TableOfContentsSettings.textRadioCaption": "참조", "DE.Views.TableOfContentsSettings.textRadioLevels": "개요 수준", "DE.Views.TableOfContentsSettings.textRadioStyle": "스타일", "DE.Views.TableOfContentsSettings.textRadioStyles": "선택 스타일", "DE.Views.TableOfContentsSettings.textStyle": "스타일", "DE.Views.TableOfContentsSettings.textStyles": "스타일들", "DE.Views.TableOfContentsSettings.textTable": "표", "DE.Views.TableOfContentsSettings.textTitle": "콘텍츠 테이블", "DE.Views.TableOfContentsSettings.textTitleTOF": "목차", "DE.Views.TableOfContentsSettings.txtCentered": "가운데", "DE.Views.TableOfContentsSettings.txtClassic": "클래식", "DE.Views.TableOfContentsSettings.txtCurrent": "현재", "DE.Views.TableOfContentsSettings.txtDistinctive": "고유한", "DE.Views.TableOfContentsSettings.txtFormal": "공식", "DE.Views.TableOfContentsSettings.txtModern": "모던", "DE.Views.TableOfContentsSettings.txtOnline": "온라인", "DE.Views.TableOfContentsSettings.txtSimple": "간단한", "DE.Views.TableOfContentsSettings.txtStandard": "기준", "DE.Views.TableSettings.deleteColumnText": "열 삭제", "DE.Views.TableSettings.deleteRowText": "행 삭제", "DE.Views.TableSettings.deleteTableText": "테이블 삭제", "DE.Views.TableSettings.insertColumnLeftText": "왼쪽에 열 삽입", "DE.Views.TableSettings.insertColumnRightText": "오른쪽 열 삽입", "DE.Views.TableSettings.insertRowAboveText": "Insert Row Above", "DE.Views.TableSettings.insertRowBelowText": "아래에 행 삽입", "DE.Views.TableSettings.mergeCellsText": "셀 병합", "DE.Views.TableSettings.selectCellText": "셀 선택", "DE.Views.TableSettings.selectColumnText": "열 선택", "DE.Views.TableSettings.selectRowText": "행 선택", "DE.Views.TableSettings.selectTableText": "표 선택", "DE.Views.TableSettings.splitCellsText": "셀 분할 ...", "DE.Views.TableSettings.splitCellTitleText": "셀 분할", "DE.Views.TableSettings.strRepeatRow": "각 페이지 상단의 헤더 행으로 반복", "DE.Views.TableSettings.textAddFormula": "수식추가", "DE.Views.TableSettings.textAdvanced": "고급 설정 표시", "DE.Views.TableSettings.textBackColor": "배경색", "DE.Views.TableSettings.textBanded": "줄무늬", "DE.Views.TableSettings.textBorderColor": "Color", "DE.Views.TableSettings.textBorders": "테두리 스타일", "DE.Views.TableSettings.textCellSize": "행/열 크기", "DE.Views.TableSettings.textColumns": "열", "DE.Views.TableSettings.textConvert": "표를 문자로 변환", "DE.Views.TableSettings.textDistributeCols": "컬럼 배포", "DE.Views.TableSettings.textDistributeRows": "행 배포", "DE.Views.TableSettings.textEdit": "행 및 열", "DE.Views.TableSettings.textEmptyTemplate": "템플릿 없음", "DE.Views.TableSettings.textFirst": "First", "DE.Views.TableSettings.textHeader": "머리글", "DE.Views.TableSettings.textHeight": "높이", "DE.Views.TableSettings.textLast": "Last", "DE.Views.TableSettings.textRows": "행", "DE.Views.TableSettings.textSelectBorders": "위에서 선택한 스타일 적용을 변경하려는 테두리 선택", "DE.Views.TableSettings.textTemplate": "템플릿에서 선택", "DE.Views.TableSettings.textTotal": "요약 행", "DE.Views.TableSettings.textWidth": "너비", "DE.Views.TableSettings.tipAll": "바깥쪽 테두리 및 안쪽 테두리", "DE.Views.TableSettings.tipBottom": "바깥 아래쪽 테두리", "DE.Views.TableSettings.tipInner": "내부 라인 만 설정", "DE.Views.TableSettings.tipInnerHor": "안쪽 가로 테두리", "DE.Views.TableSettings.tipInnerVert": "세로 내부 선만 설정", "DE.Views.TableSettings.tipLeft": "바깥 왼쪽 테두리", "DE.Views.TableSettings.tipNone": "테두리 없음 설정", "DE.Views.TableSettings.tipOuter": "바깥쪽 테두리", "DE.Views.TableSettings.tipRight": "바깥 오른쪽 테두리", "DE.Views.TableSettings.tipTop": "바깥 위쪽 테두리", "DE.Views.TableSettings.txtNoBorders": "테두리 없음", "DE.Views.TableSettings.txtTable_Accent": "강조", "DE.Views.TableSettings.txtTable_Colorful": "화려한", "DE.Views.TableSettings.txtTable_Dark": "어두운", "DE.Views.TableSettings.txtTable_GridTable": "그리드 테이블", "DE.Views.TableSettings.txtTable_Light": "밝은", "DE.Views.TableSettings.txtTable_ListTable": "테이블목록", "DE.Views.TableSettings.txtTable_PlainTable": "일반표", "DE.Views.TableSettings.txtTable_TableGrid": "테이블 그리드", "DE.Views.TableSettingsAdvanced.textAlign": "정렬", "DE.Views.TableSettingsAdvanced.textAlignment": "정렬", "DE.Views.TableSettingsAdvanced.textAllowSpacing": "셀 사이의 간격", "DE.Views.TableSettingsAdvanced.textAlt": "대체 텍스트", "DE.Views.TableSettingsAdvanced.textAltDescription": "설명", "DE.Views.TableSettingsAdvanced.textAltTip": "시각적 개체 정보의 교체는 텍스트 표현을 기반으로 하며 시각 또는 인지 장애가 있는 사람들이 이미지, 자동 모양, 차트 또는 표에 포함된 정보를 더 잘 이해할 수 있도록 읽어줍니다.", "DE.Views.TableSettingsAdvanced.textAltTitle": "Title", "DE.Views.TableSettingsAdvanced.textAnchorText": "텍스트", "DE.Views.TableSettingsAdvanced.textAutofit": "내용에 맞게 자동으로 크기 조정", "DE.Views.TableSettingsAdvanced.textBackColor": "셀 배경", "DE.Views.TableSettingsAdvanced.textBelow": "아래", "DE.Views.TableSettingsAdvanced.textBorderColor": "테두리 색상", "DE.Views.TableSettingsAdvanced.textBorderDesc": "다이어그램을 클릭하거나 단추를 사용하여 테두리를 선택하고 선택한 스타일을 적용", "DE.Views.TableSettingsAdvanced.textBordersBackgroung": "테두리 및 배경", "DE.Views.TableSettingsAdvanced.textBorderWidth": "테두리 크기", "DE.Views.TableSettingsAdvanced.textBottom": "Bottom", "DE.Views.TableSettingsAdvanced.textCellOptions": "셀 옵션", "DE.Views.TableSettingsAdvanced.textCellProps": "셀", "DE.Views.TableSettingsAdvanced.textCellSize": "셀 크기", "DE.Views.TableSettingsAdvanced.textCenter": "Center", "DE.Views.TableSettingsAdvanced.textCenterTooltip": "Center", "DE.Views.TableSettingsAdvanced.textCheckMargins": "기본 여백 사용", "DE.Views.TableSettingsAdvanced.textDefaultMargins": "기본 셀 여백", "DE.Views.TableSettingsAdvanced.textDistance": "텍스트 간격", "DE.Views.TableSettingsAdvanced.textHorizontal": "Horizontal", "DE.Views.TableSettingsAdvanced.textIndLeft": "왼쪽에서 들여 쓰기", "DE.Views.TableSettingsAdvanced.textLeft": "왼쪽", "DE.Views.TableSettingsAdvanced.textLeftTooltip": "왼쪽", "DE.Views.TableSettingsAdvanced.textMargin": "여백", "DE.Views.TableSettingsAdvanced.textMargins": "셀 여백", "DE.Views.TableSettingsAdvanced.textMeasure": "측정", "DE.Views.TableSettingsAdvanced.textMove": "텍스트가있는 객체 이동", "DE.Views.TableSettingsAdvanced.textOnlyCells": "선택한 셀만 해당", "DE.Views.TableSettingsAdvanced.textOptions": "옵션", "DE.Views.TableSettingsAdvanced.textOverlap": "중복 허용", "DE.Views.TableSettingsAdvanced.textPage": "페이지", "DE.Views.TableSettingsAdvanced.textPosition": "위치", "DE.Views.TableSettingsAdvanced.textPrefWidth": "기본 너비", "DE.Views.TableSettingsAdvanced.textPreview": "미리보기", "DE.Views.TableSettingsAdvanced.textRelative": "상대적", "DE.Views.TableSettingsAdvanced.textRight": "오른쪽", "DE.Views.TableSettingsAdvanced.textRightOf": "오른쪽에", "DE.Views.TableSettingsAdvanced.textRightTooltip": "오른쪽", "DE.Views.TableSettingsAdvanced.textTable": "표", "DE.Views.TableSettingsAdvanced.textTableBackColor": "표 배경", "DE.Views.TableSettingsAdvanced.textTablePosition": "표 위치", "DE.Views.TableSettingsAdvanced.textTableSize": "표 크기", "DE.Views.TableSettingsAdvanced.textTitle": "표 - 고급 설정", "DE.Views.TableSettingsAdvanced.textTop": "Top", "DE.Views.TableSettingsAdvanced.textVertical": "Vertical", "DE.Views.TableSettingsAdvanced.textWidth": "너비", "DE.Views.TableSettingsAdvanced.textWidthSpaces": "너비 및 공백", "DE.Views.TableSettingsAdvanced.textWrap": "텍스트 줄 바꿈", "DE.Views.TableSettingsAdvanced.textWrapNoneTooltip": "인라인 테이블", "DE.Views.TableSettingsAdvanced.textWrapParallelTooltip": "흐름 표", "DE.Views.TableSettingsAdvanced.textWrappingStyle": "포장 스타일", "DE.Views.TableSettingsAdvanced.textWrapText": "텍스트 줄 바꾸기", "DE.Views.TableSettingsAdvanced.tipAll": "바깥쪽 테두리 및 안쪽 테두리", "DE.Views.TableSettingsAdvanced.tipCellAll": "내부 셀만 테두리 설정", "DE.Views.TableSettingsAdvanced.tipCellInner": "내부 셀만 수직선과 수평선 설정", "DE.Views.TableSettingsAdvanced.tipCellOuter": "내부 셀 전용 외곽선 설정", "DE.Views.TableSettingsAdvanced.tipInner": "내부 라인 만 설정", "DE.Views.TableSettingsAdvanced.tipNone": "테두리 없음 설정", "DE.Views.TableSettingsAdvanced.tipOuter": "바깥쪽 테두리", "DE.Views.TableSettingsAdvanced.tipTableOuterCellAll": "모든 내부 셀에 테두리 및 테두리 설정", "DE.Views.TableSettingsAdvanced.tipTableOuterCellInner": "내부 셀의 외부 테두리 및 수직 및 수평선 설정", "DE.Views.TableSettingsAdvanced.tipTableOuterCellOuter": "내부 셀에 대한 테이블 바깥 쪽 테두리 및 바깥 쪽 테두리 설정", "DE.Views.TableSettingsAdvanced.txtCm": "센티미터", "DE.Views.TableSettingsAdvanced.txtInch": "인치", "DE.Views.TableSettingsAdvanced.txtNoBorders": "테두리 없음", "DE.Views.TableSettingsAdvanced.txtPercent": "Percent", "DE.Views.TableSettingsAdvanced.txtPt": "Point", "DE.Views.TableToTextDialog.textEmpty": "하나 이상의 맞춤 구분자를 입력해야 합니다.", "DE.Views.TableToTextDialog.textNested": "중첩 테이블의 변환", "DE.Views.TableToTextDialog.textOther": "기타", "DE.Views.TableToTextDialog.textPara": "단락기호", "DE.Views.TableToTextDialog.textSemicolon": "세미콜론", "DE.Views.TableToTextDialog.textSeparator": "텍스트로 구분", "DE.Views.TableToTextDialog.textTab": "탭", "DE.Views.TableToTextDialog.textTitle": "표를 문자로 변환", "DE.Views.TextArtSettings.strColor": "Color", "DE.Views.TextArtSettings.strFill": "채우기", "DE.Views.TextArtSettings.strSize": "크기", "DE.Views.TextArtSettings.strStroke": "선", "DE.Views.TextArtSettings.strTransparency": "투명도", "DE.Views.TextArtSettings.strType": "유형", "DE.Views.TextArtSettings.textAngle": "각도", "DE.Views.TextArtSettings.textBorderSizeErr": "입력 한 값이 잘못되었습니다. <br> 0 ~ 1584pt 사이의 값을 입력하십시오.", "DE.Views.TextArtSettings.textColor": "색상 채우기", "DE.Views.TextArtSettings.textDirection": "Direction", "DE.Views.TextArtSettings.textGradient": "Gradient Points", "DE.Views.TextArtSettings.textGradientFill": "그라데이션 채우기", "DE.Views.TextArtSettings.textLinear": "선형", "DE.Views.TextArtSettings.textNoFill": "채우기 없음", "DE.Views.TextArtSettings.textPosition": "위치", "DE.Views.TextArtSettings.textRadial": "방사형", "DE.Views.TextArtSettings.textSelectTexture": "선택", "DE.Views.TextArtSettings.textStyle": "스타일", "DE.Views.TextArtSettings.textTemplate": "템플릿", "DE.Views.TextArtSettings.textTransform": "변형", "DE.Views.TextArtSettings.tipAddGradientPoint": "그라데이션 포인트 추가", "DE.Views.TextArtSettings.tipRemoveGradientPoint": "그라데이션 포인트 제거", "DE.Views.TextArtSettings.txtNoBorders": "No Line", "DE.Views.TextToTableDialog.textAutofit": "열너비 자동조정", "DE.Views.TextToTableDialog.textColumns": "열", "DE.Views.TextToTableDialog.textContents": "열 너비를 콘텐츠에 맞게 자동 조정", "DE.Views.TextToTableDialog.textEmpty": "하나 이상의 맞춤 구분자를 입력해야 합니다.", "DE.Views.TextToTableDialog.textFixed": "열 너비 고정", "DE.Views.TextToTableDialog.textOther": "기타", "DE.Views.TextToTableDialog.textPara": "단락", "DE.Views.TextToTableDialog.textRows": "행", "DE.Views.TextToTableDialog.textSemicolon": "세미콜론", "DE.Views.TextToTableDialog.textSeparator": "분리된 텍스트", "DE.Views.TextToTableDialog.textTab": "탭", "DE.Views.TextToTableDialog.textTableSize": "표 크기", "DE.Views.TextToTableDialog.textTitle": "문자를 테이블로 변환", "DE.Views.TextToTableDialog.textWindow": "열 너비를 창에 맞게 자동 조정", "DE.Views.TextToTableDialog.txtAutoText": "자동", "DE.Views.Toolbar.capBtnAddComment": "코멘트 달기", "DE.Views.Toolbar.capBtnBlankPage": "빈 페이지", "DE.Views.Toolbar.capBtnColumns": "열", "DE.Views.Toolbar.capBtnComment": "댓글", "DE.Views.Toolbar.capBtnDateTime": "날짜 및 시간", "DE.Views.Toolbar.capBtnInsChart": "차트", "DE.Views.Toolbar.capBtnInsControls": "콘텐트 제어", "DE.Views.Toolbar.capBtnInsDropcap": "드롭 캡", "DE.Views.Toolbar.capBtnInsEquation": "수식", "DE.Views.Toolbar.capBtnInsHeader": "머리말/꼬리말", "DE.Views.Toolbar.capBtnInsImage": "그림", "DE.Views.Toolbar.capBtnInsPagebreak": "나누기", "DE.Views.Toolbar.capBtnInsShape": "쉐이프", "DE.Views.Toolbar.capBtnInsSymbol": "기호", "DE.Views.Toolbar.capBtnInsTable": "테이블", "DE.Views.Toolbar.capBtnInsTextart": "텍스트 아트", "DE.Views.Toolbar.capBtnInsTextbox": "텍스트 상자", "DE.Views.Toolbar.capBtnLineNumbers": "행번호", "DE.Views.Toolbar.capBtnMargins": "여백", "DE.Views.Toolbar.capBtnPageOrient": "오리엔테이션", "DE.Views.Toolbar.capBtnPageSize": "크기", "DE.Views.Toolbar.capBtnWatermark": "워터마크", "DE.Views.Toolbar.capImgAlign": "정렬", "DE.Views.Toolbar.capImgBackward": "뒤로 이동", "DE.Views.Toolbar.capImgForward": "앞으로 이동", "DE.Views.Toolbar.capImgGroup": "그룹", "DE.Views.Toolbar.capImgWrapping": "포장", "DE.Views.Toolbar.mniCapitalizeWords": "각 단어의 첫글자를 대문자로", "DE.Views.Toolbar.mniCustomTable": "사용자 정의 테이블 삽입", "DE.Views.Toolbar.mniDrawTable": "표그리기", "DE.Views.Toolbar.mniEditControls": "제어 세팅", "DE.Views.Toolbar.mniEditDropCap": "드롭 캡 설정", "DE.Views.Toolbar.mniEditFooter": "바닥 글 편집", "DE.Views.Toolbar.mniEditHeader": "머리글 편집", "DE.Views.Toolbar.mniEraseTable": "표삭제", "DE.Views.Toolbar.mniFromFile": "파일로 부터", "DE.Views.Toolbar.mniFromStorage": "스토리지로 부터", "DE.Views.Toolbar.mniFromUrl": "URL로 부터", "DE.Views.Toolbar.mniHiddenBorders": "숨겨진 테이블 테두리", "DE.Views.Toolbar.mniHiddenChars": "인쇄되지 않는 문자", "DE.Views.Toolbar.mniHighlightControls": "강조 설정", "DE.Views.Toolbar.mniImageFromFile": "그림 파일에서", "DE.Views.Toolbar.mniImageFromStorage": "스토리지에서 불러오기", "DE.Views.Toolbar.mniImageFromUrl": "URL에서 그림", "DE.Views.Toolbar.mniLowerCase": "소문자", "DE.Views.Toolbar.mniSentenceCase": "문장의 첫 글자를 대문자로", "DE.Views.Toolbar.mniTextToTable": "문자를 테이블로 변환", "DE.Views.Toolbar.mniToggleCase": "대/소문자 전환", "DE.Views.Toolbar.mniUpperCase": "대문자", "DE.Views.Toolbar.strMenuNoFill": "채우기 없음", "DE.Views.Toolbar.textAutoColor": "자동", "DE.Views.Toolbar.textBold": "Bold", "DE.Views.Toolbar.textBottom": "Bottom :", "DE.Views.Toolbar.textChangeLevel": "목록 수준 변경", "DE.Views.Toolbar.textCheckboxControl": "체크박스", "DE.Views.Toolbar.textColumnsCustom": "사용자 정의 열", "DE.Views.Toolbar.textColumnsLeft": "왼쪽", "DE.Views.Toolbar.textColumnsOne": "하나", "DE.Views.Toolbar.textColumnsRight": "오른쪽", "DE.Views.Toolbar.textColumnsThree": "3", "DE.Views.Toolbar.textColumnsTwo": "2", "DE.Views.Toolbar.textComboboxControl": "콤보박스", "DE.Views.Toolbar.textContinuous": "계속", "DE.Views.Toolbar.textContPage": "연속 페이지", "DE.Views.Toolbar.textCustomLineNumbers": "행 번호 옵션", "DE.Views.Toolbar.textDateControl": "날짜", "DE.Views.Toolbar.textDropdownControl": "드롭 다운 메뉴", "DE.Views.Toolbar.textEditWatermark": "사용자 정의 워터마크", "DE.Views.Toolbar.textEvenPage": "짝수 페이지", "DE.Views.Toolbar.textInMargin": "여백 있음", "DE.Views.Toolbar.textInsColumnBreak": "열 구분 삽입", "DE.Views.Toolbar.textInsertPageCount": "페이지 수 삽입", "DE.Views.Toolbar.textInsertPageNumber": "페이지 번호 삽입", "DE.Views.Toolbar.textInsPageBreak": "페이지 나누기 삽입", "DE.Views.Toolbar.textInsSectionBreak": "섹션 나누기 삽입", "DE.Views.Toolbar.textInText": "텍스트에서", "DE.Views.Toolbar.textItalic": "Italic", "DE.Views.Toolbar.textLandscape": "Landscape", "DE.Views.Toolbar.textLeft": "왼쪽 :", "DE.Views.Toolbar.textListSettings": "목록설정", "DE.Views.Toolbar.textMarginsLast": "마지막 사용자 정의", "DE.Views.Toolbar.textMarginsModerate": "보통", "DE.Views.Toolbar.textMarginsNarrow": "좁다", "DE.Views.Toolbar.textMarginsNormal": "표준", "DE.Views.Toolbar.textMarginsUsNormal": "US 표준", "DE.Views.Toolbar.textMarginsWide": "Wide", "DE.Views.Toolbar.textNewColor": "새로운 사용자 정의 색 추가", "DE.Views.Toolbar.textNextPage": "다음 페이지", "DE.Views.Toolbar.textNoHighlight": "강조 표시되지 않음", "DE.Views.Toolbar.textNone": "없음", "DE.Views.Toolbar.textOddPage": "홀수 페이지", "DE.Views.Toolbar.textPageMarginsCustom": "사용자 정의 여백", "DE.Views.Toolbar.textPageSizeCustom": "사용자 정의 페이지 크기", "DE.Views.Toolbar.textPictureControl": "그림", "DE.Views.Toolbar.textPlainControl": "일반 텍스트", "DE.Views.Toolbar.textPortrait": "Portrait", "DE.Views.Toolbar.textRemoveControl": "콘텐트 컨트롤 삭제", "DE.Views.Toolbar.textRemWatermark": "워터마크 제거", "DE.Views.Toolbar.textRestartEachPage": "모든 페이지 다시 시작", "DE.Views.Toolbar.textRestartEachSection": "각 섹션 다시 시작", "DE.Views.Toolbar.textRichControl": "리치 텍스트", "DE.Views.Toolbar.textRight": "오른쪽 :", "DE.Views.Toolbar.textStrikeout": "취소선", "DE.Views.Toolbar.textStyleMenuDelete": "스타일 삭제", "DE.Views.Toolbar.textStyleMenuDeleteAll": "모든 사용자 정의 스타일 삭제", "DE.Views.Toolbar.textStyleMenuNew": "선택 항목의 새 스타일", "DE.Views.Toolbar.textStyleMenuRestore": "기본값으로 복원", "DE.Views.Toolbar.textStyleMenuRestoreAll": "모두 기본 스타일로 복원", "DE.Views.Toolbar.textStyleMenuUpdate": "선택 항목에서 업데이트", "DE.Views.Toolbar.textSubscript": "Subscript", "DE.Views.Toolbar.textSuperscript": "Superscript", "DE.Views.Toolbar.textSuppressForCurrentParagraph": "이 단락에서 해제", "DE.Views.Toolbar.textTabCollaboration": "협업", "DE.Views.Toolbar.textTabFile": "파일", "DE.Views.Toolbar.textTabHome": "홈", "DE.Views.Toolbar.textTabInsert": "삽입", "DE.Views.Toolbar.textTabLayout": "레이아웃", "DE.Views.Toolbar.textTabLinks": "참조", "DE.Views.Toolbar.textTabProtect": "보호", "DE.Views.Toolbar.textTabReview": "다시보기", "DE.Views.Toolbar.textTabView": "뷰", "DE.Views.Toolbar.textTitleError": "오류", "DE.Views.Toolbar.textToCurrent": "현재 위치로", "DE.Views.Toolbar.textTop": "Top :", "DE.Views.Toolbar.textUnderline": "밑줄", "DE.Views.Toolbar.tipAlignCenter": "Align Center", "DE.Views.Toolbar.tipAlignJust": "Justified", "DE.Views.Toolbar.tipAlignLeft": "왼쪽 정렬", "DE.Views.Toolbar.tipAlignRight": "오른쪽 정렬", "DE.Views.Toolbar.tipBack": "뒤로", "DE.Views.Toolbar.tipBlankPage": "빈 페이지 삽입", "DE.Views.Toolbar.tipChangeCase": "대소문자 변경", "DE.Views.Toolbar.tipChangeChart": "차트 유형 변경", "DE.Views.Toolbar.tipClearStyle": "스타일 지우기", "DE.Views.Toolbar.tipColorSchemas": "Change Color Scheme", "DE.Views.Toolbar.tipColumns": "열 삽입", "DE.Views.Toolbar.tipControls": "컨텐츠 컨트롤 추가", "DE.Views.Toolbar.tipCopy": "복사", "DE.Views.Toolbar.tipCopyStyle": "스타일 복사", "DE.Views.Toolbar.tipDateTime": "현재 날짜 시간 삽입", "DE.Views.Toolbar.tipDecFont": "글꼴 크기 작게", "DE.Views.Toolbar.tipDecPrLeft": "들여 쓰기 감소", "DE.Views.Toolbar.tipDropCap": "드롭 캡 삽입", "DE.Views.Toolbar.tipEditHeader": "머리글 또는 바닥 글 편집", "DE.Views.Toolbar.tipFontColor": "글꼴 색", "DE.Views.Toolbar.tipFontName": "글꼴", "DE.Views.Toolbar.tipFontSize": "글꼴 크기", "DE.Views.Toolbar.tipHighlightColor": "Highlight Color", "DE.Views.Toolbar.tipImgAlign": "오브젝트 정렬", "DE.Views.Toolbar.tipImgGroup": "그룹 오브젝트", "DE.Views.Toolbar.tipImgWrapping": "텍스트 줄 바꾸기", "DE.Views.Toolbar.tipIncFont": "증가 글꼴 크기", "DE.Views.Toolbar.tipIncPrLeft": "들여 쓰기 늘리기", "DE.Views.Toolbar.tipInsertChart": "차트 삽입", "DE.Views.Toolbar.tipInsertEquation": "수식 삽입", "DE.Views.Toolbar.tipInsertImage": "그림 삽입", "DE.Views.Toolbar.tipInsertNum": "페이지 번호 삽입", "DE.Views.Toolbar.tipInsertShape": "도형 삽입", "DE.Views.Toolbar.tipInsertSymbol": "기호 삽입", "DE.Views.Toolbar.tipInsertTable": "표 삽입", "DE.Views.Toolbar.tipInsertText": "텍스트 상자 삽입", "DE.Views.Toolbar.tipInsertTextArt": "텍스트 아트 삽입", "DE.Views.Toolbar.tipLineNumbers": "행 번호 표시", "DE.Views.Toolbar.tipLineSpace": "단락 줄 간격", "DE.Views.Toolbar.tipMailRecepients": "편지 병합", "DE.Views.Toolbar.tipMarkers": "Bullets", "DE.Views.Toolbar.tipMarkersArrow": "화살 글머리 기호", "DE.Views.Toolbar.tipMarkersCheckmark": "체크 표시 글머리 기호", "DE.Views.Toolbar.tipMarkersDash": "대시 글머리 기호", "DE.Views.Toolbar.tipMarkersFRhombus": "채워진 마름모 글머리 기호", "DE.Views.Toolbar.tipMarkersFRound": "채워진 원형 글머리 기호", "DE.Views.Toolbar.tipMarkersFSquare": "채워진 사각형 글머리 기호", "DE.Views.Toolbar.tipMarkersHRound": "빈 원형 글머리 기호", "DE.Views.Toolbar.tipMarkersStar": "별 글머리 기호", "DE.Views.Toolbar.tipMultiLevelNumbered": "멀티-레벨 번호 글머리 기호", "DE.Views.Toolbar.tipMultilevels": "다중 레벨 목록", "DE.Views.Toolbar.tipMultiLevelSymbols": "멀티-레벨 기호 글머리 기호", "DE.Views.Toolbar.tipMultiLevelVarious": "멀티-레벨 여러 번호 글머리 기호", "DE.Views.Toolbar.tipNumbers": "번호 매기기", "DE.Views.Toolbar.tipPageBreak": "페이지 또는 섹션 나누기 삽입", "DE.Views.Toolbar.tipPageMargins": "페이지 여백", "DE.Views.Toolbar.tipPageOrient": "페이지 방향", "DE.Views.Toolbar.tipPageSize": "페이지 크기", "DE.Views.Toolbar.tipParagraphStyle": "단락 스타일", "DE.Views.Toolbar.tipPaste": "붙여 넣기", "DE.Views.Toolbar.tipPrColor": "단락 배경색", "DE.Views.Toolbar.tipPrint": "인쇄", "DE.Views.Toolbar.tipRedo": "Redo", "DE.Views.Toolbar.tipSave": "저장", "DE.Views.Toolbar.tipSaveCoauth": "다른 사용자가 볼 수 있도록 변경 사항을 저장하십시오.", "DE.Views.Toolbar.tipSendBackward": "뒤로 이동", "DE.Views.Toolbar.tipSendForward": "앞으로 이동", "DE.Views.Toolbar.tipShowHiddenChars": "인쇄되지 않는 문자", "DE.Views.Toolbar.tipSynchronize": "다른 사용자가 문서를 변경했습니다. 변경 사항을 저장하고 업데이트를 다시로드하려면 클릭하십시오.", "DE.Views.Toolbar.tipUndo": "실행 취소", "DE.Views.Toolbar.tipWatermark": "워터마크 수정", "DE.Views.Toolbar.txtDistribHor": "수평 분포", "DE.Views.Toolbar.txtDistribVert": "수직 분포", "DE.Views.Toolbar.txtMarginAlign": "여백정렬", "DE.Views.Toolbar.txtObjectsAlign": "선택한 개체 정렬", "DE.Views.Toolbar.txtPageAlign": "페이지 정렬", "DE.Views.Toolbar.txtScheme1": "Office", "DE.Views.Toolbar.txtScheme10": "중앙값", "DE.Views.Toolbar.txtScheme11": "Metro", "DE.Views.Toolbar.txtScheme12": "모듈", "DE.Views.Toolbar.txtScheme13": "호화 로움", "DE.Views.Toolbar.txtScheme14": "Oriel", "DE.Views.Toolbar.txtScheme15": "Origin", "DE.Views.Toolbar.txtScheme16": "용지", "DE.Views.Toolbar.txtScheme17": "지점", "DE.Views.Toolbar.txtScheme18": "기술", "DE.Views.Toolbar.txtScheme19": "트레킹", "DE.Views.Toolbar.txtScheme2": "Grayscale", "DE.Views.Toolbar.txtScheme20": "Urban", "DE.Views.Toolbar.txtScheme21": "Verve", "DE.Views.Toolbar.txtScheme22": "신규 오피스", "DE.Views.Toolbar.txtScheme3": "Apex", "DE.Views.Toolbar.txtScheme4": "Aspect", "DE.Views.Toolbar.txtScheme5": "Civic", "DE.Views.Toolbar.txtScheme6": "Concourse", "DE.Views.Toolbar.txtScheme7": "Equity", "DE.Views.Toolbar.txtScheme8": "흐름", "DE.Views.Toolbar.txtScheme9": "주조", "DE.Views.ViewTab.textAlwaysShowToolbar": "항상 도구 모음 표시", "DE.Views.ViewTab.textDarkDocument": "다크 문서", "DE.Views.ViewTab.textFitToPage": "페이지에 맞춤", "DE.Views.ViewTab.textFitToWidth": "너비에 맞춤", "DE.Views.ViewTab.textInterfaceTheme": "인터페이스 테마", "DE.Views.ViewTab.textNavigation": "네비게이션", "DE.Views.ViewTab.textRulers": "자", "DE.Views.ViewTab.textStatusBar": "상태 바", "DE.Views.ViewTab.textZoom": "확대 / 축소", "DE.Views.WatermarkSettingsDialog.textAuto": "자동", "DE.Views.WatermarkSettingsDialog.textBold": "굵게", "DE.Views.WatermarkSettingsDialog.textColor": "글꼴색", "DE.Views.WatermarkSettingsDialog.textDiagonal": "대각선", "DE.Views.WatermarkSettingsDialog.textFont": "글꼴", "DE.Views.WatermarkSettingsDialog.textFromFile": "파일로 부터", "DE.Views.WatermarkSettingsDialog.textFromStorage": "스토리지로 부터", "DE.Views.WatermarkSettingsDialog.textFromUrl": "URL로 부터", "DE.Views.WatermarkSettingsDialog.textHor": "수평", "DE.Views.WatermarkSettingsDialog.textImageW": "워터마크 이미지", "DE.Views.WatermarkSettingsDialog.textItalic": "기울림꼴", "DE.Views.WatermarkSettingsDialog.textLanguage": "언어", "DE.Views.WatermarkSettingsDialog.textLayout": "레이아웃", "DE.Views.WatermarkSettingsDialog.textNone": "없음", "DE.Views.WatermarkSettingsDialog.textScale": "크기", "DE.Views.WatermarkSettingsDialog.textSelect": "이미지 선택", "DE.Views.WatermarkSettingsDialog.textStrikeout": "취소선", "DE.Views.WatermarkSettingsDialog.textText": "텍스트", "DE.Views.WatermarkSettingsDialog.textTextW": "텍스트 워터마크", "DE.Views.WatermarkSettingsDialog.textTitle": "워커마크 설정", "DE.Views.WatermarkSettingsDialog.textTransparency": "투명한", "DE.Views.WatermarkSettingsDialog.textUnderline": "밑줄", "DE.Views.WatermarkSettingsDialog.tipFontName": "글꼴이름", "DE.Views.WatermarkSettingsDialog.tipFontSize": "글꼴 크기"}