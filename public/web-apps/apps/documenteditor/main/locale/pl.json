{"Common.Controllers.Chat.notcriticalErrorTitle": "Ostrzeżenie", "Common.Controllers.Chat.textEnterMessage": "Wprowadź swoją wiadomość tutaj", "Common.Controllers.ExternalDiagramEditor.textAnonymous": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalDiagramEditor.textClose": "Zamknij", "Common.Controllers.ExternalDiagramEditor.warningText": "Obiekt jest wył<PERSON><PERSON><PERSON>, ponieważ jest edytowany przez innego użytkownika.", "Common.Controllers.ExternalDiagramEditor.warningTitle": "Ostrzeżenie", "Common.Controllers.ExternalMergeEditor.textAnonymous": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalMergeEditor.textClose": "Zamknij", "Common.Controllers.ExternalMergeEditor.warningText": "Obiekt jest wył<PERSON><PERSON><PERSON>, ponieważ jest edytowany przez innego użytkownika.", "Common.Controllers.ExternalMergeEditor.warningTitle": "Ostrzeżenie", "Common.Controllers.ExternalOleEditor.textAnonymous": "<PERSON><PERSON><PERSON><PERSON>", "Common.Controllers.ExternalOleEditor.textClose": "Zamknij", "Common.Controllers.ExternalOleEditor.warningText": "Obiekt jest wył<PERSON><PERSON><PERSON>, ponieważ jest edytowany przez innego użytkownika.", "Common.Controllers.ExternalOleEditor.warningTitle": "Ostrzeżenie", "Common.Controllers.History.notcriticalErrorTitle": "Ostrzeżenie", "Common.Controllers.ReviewChanges.textAcceptBeforeCompare": "W celu porównania dokumentów wszystkie śledzone w nich zmiany będą uważane za zaakceptowane. <PERSON><PERSON> ch<PERSON> kont<PERSON>uowa<PERSON>?", "Common.Controllers.ReviewChanges.textAtLeast": "Co najmniej", "Common.Controllers.ReviewChanges.textAuto": "Automatyczny", "Common.Controllers.ReviewChanges.textBaseline": "<PERSON><PERSON> bazowa", "Common.Controllers.ReviewChanges.textBold": "Pogrubione", "Common.Controllers.ReviewChanges.textBreakBefore": "Przerwanie strony przed", "Common.Controllers.ReviewChanges.textCaps": "Wszystkie duże litery", "Common.Controllers.ReviewChanges.textCenter": "Wyrównaj do środka", "Common.Controllers.ReviewChanges.textChar": "Według znaków", "Common.Controllers.ReviewChanges.textChart": "<PERSON><PERSON><PERSON>", "Common.Controllers.ReviewChanges.textColor": "<PERSON><PERSON>", "Common.Controllers.ReviewChanges.textContextual": "Nie dodawaj odstępu między akapitami tego samego stylu", "Common.Controllers.ReviewChanges.textDeleted": "<b>Usunięte:</b>", "Common.Controllers.ReviewChanges.textDStrikeout": "Podwójne przekreślenie", "Common.Controllers.ReviewChanges.textEquation": "Równanie", "Common.Controllers.ReviewChanges.textExact": "Dokładnie", "Common.Controllers.ReviewChanges.textFirstLine": "<PERSON><PERSON><PERSON> w<PERSON>", "Common.Controllers.ReviewChanges.textFontSize": "Rozmiar <PERSON>ki", "Common.Controllers.ReviewChanges.textFormatted": "Sformatowany", "Common.Controllers.ReviewChanges.textHighlight": "<PERSON><PERSON>", "Common.Controllers.ReviewChanges.textImage": "<PERSON><PERSON><PERSON>", "Common.Controllers.ReviewChanges.textIndentLeft": "Wcięcie w lewo", "Common.Controllers.ReviewChanges.textIndentRight": "Wcięcie w prawo", "Common.Controllers.ReviewChanges.textInserted": "<b>W<PERSON>wione:</b>", "Common.Controllers.ReviewChanges.textItalic": "Ku<PERSON>ywa", "Common.Controllers.ReviewChanges.textJustify": "Wyjustuj do szerokości", "Common.Controllers.ReviewChanges.textKeepLines": "<PERSON><PERSON><PERSON>aj wiersze razem", "Common.Controllers.ReviewChanges.textKeepNext": "<PERSON><PERSON><PERSON><PERSON>", "Common.Controllers.ReviewChanges.textLeft": "Wyrównaj do lewej", "Common.Controllers.ReviewChanges.textLineSpacing": "Rozstaw wierszy:", "Common.Controllers.ReviewChanges.textMultiple": "Mnożnik", "Common.Controllers.ReviewChanges.textNoBreakBefore": "Żadna strona nie łamie się wcześniej", "Common.Controllers.ReviewChanges.textNoContextual": "Dodaj odstęp między akapitami tego samego stylu", "Common.Controllers.ReviewChanges.textNoKeepLines": "<PERSON>e trzymaj linii razem", "Common.Controllers.ReviewChanges.textNoKeepNext": "Nie zbliżaj się do następnego", "Common.Controllers.ReviewChanges.textNot": "<PERSON><PERSON>", "Common.Controllers.ReviewChanges.textNoWidow": "Brak kontroli okna", "Common.Controllers.ReviewChanges.textNum": "Zmień numerację", "Common.Controllers.ReviewChanges.textOff": "{0} nie korzysta już z trybu śledzenia zmian.", "Common.Controllers.ReviewChanges.textOffGlobal": "{0} wyłączonych trybów śledzenia zmian dla wszystkich.", "Common.Controllers.ReviewChanges.textOn": "{0} używa teraz trybu śledzenia zmian.", "Common.Controllers.ReviewChanges.textOnGlobal": "{0} włączonych trybów śledzenia zmian dla wszystkich.", "Common.Controllers.ReviewChanges.textParaDeleted": "<b><PERSON><PERSON><PERSON><PERSON> akapit</b>", "Common.Controllers.ReviewChanges.textParaFormatted": "Sformatowany akapit", "Common.Controllers.ReviewChanges.textParaInserted": "<b><PERSON><PERSON><PERSON><PERSON></b>", "Common.Controllers.ReviewChanges.textParaMoveFromDown": "<b>Prz<PERSON><PERSON>ę<PERSON> w dół:</b>", "Common.Controllers.ReviewChanges.textParaMoveFromUp": "<b>Prz<PERSON><PERSON>ęto w górę:</b>", "Common.Controllers.ReviewChanges.textParaMoveTo": "<b><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>:</b>", "Common.Controllers.ReviewChanges.textPosition": "<PERSON><PERSON><PERSON>ja", "Common.Controllers.ReviewChanges.textRight": "Wyrównaj do prawej", "Common.Controllers.ReviewChanges.textShape": "Kształt", "Common.Controllers.ReviewChanges.textShd": "<PERSON><PERSON>", "Common.Controllers.ReviewChanges.textShow": "Pokaż zmiany:", "Common.Controllers.ReviewChanges.textSmallCaps": "Małe litery", "Common.Controllers.ReviewChanges.textSpacing": "Rozstaw", "Common.Controllers.ReviewChanges.textSpacingAfter": "Rozstaw po", "Common.Controllers.ReviewChanges.textSpacingBefore": "Rozstaw przed", "Common.Controllers.ReviewChanges.textStrikeout": "Skreślenie", "Common.Controllers.ReviewChanges.textSubScript": "<PERSON><PERSON><PERSON>", "Common.Controllers.ReviewChanges.textSuperScript": "<PERSON><PERSON><PERSON>", "Common.Controllers.ReviewChanges.textTableChanged": "<b>Ustawienia tabeli zmienione</b>", "Common.Controllers.ReviewChanges.textTableRowsAdd": "<b><PERSON><PERSON><PERSON><PERSON> dodane do tabeli</b>", "Common.Controllers.ReviewChanges.textTableRowsDel": "<b><PERSON><PERSON><PERSON><PERSON> tabeli <PERSON></b>", "Common.Controllers.ReviewChanges.textTabs": "Zmień zakładki", "Common.Controllers.ReviewChanges.textTitleComparison": "Opcje porównania", "Common.Controllers.ReviewChanges.textUnderline": "Podkreślenie", "Common.Controllers.ReviewChanges.textUrl": "Wklej adres URL dokumentu", "Common.Controllers.ReviewChanges.textWidow": "<PERSON><PERSON><PERSON><PERSON>", "Common.Controllers.ReviewChanges.textWord": "Według słów ", "Common.define.chartData.textArea": "Warstwowy", "Common.define.chartData.textAreaStacked": "Skumulowany warstwowy", "Common.define.chartData.textAreaStackedPer": "100% skumulowany warstwowy", "Common.define.chartData.textBar": "Słupkowy", "Common.define.chartData.textBarNormal": "Kolumnowy grupowany", "Common.define.chartData.textBarNormal3d": "Kolumnowy grupowany 3D", "Common.define.chartData.textBarNormal3dPerspective": "Kolumnowy 3D", "Common.define.chartData.textBarStacked": "Skumulowany kolumnowy", "Common.define.chartData.textBarStacked3d": "Skumulowany kolumnowy 3D", "Common.define.chartData.textBarStackedPer": "100% skumulowany kolumnowy", "Common.define.chartData.textBarStackedPer3d": "100% skumulowany kolumnowy 3D", "Common.define.chartData.textCharts": "Wykresy", "Common.define.chartData.textColumn": "Kolumnowy", "Common.define.chartData.textCombo": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textComboAreaBar": "Skumulowany warstwowy - kolumnowy grupowany", "Common.define.chartData.textComboBarLine": "Kolumnowy grupowany - liniowy", "Common.define.chartData.textComboBarLineSecondary": "Kolumnowy grupowany - liniowy na osi pomocniczej", "Common.define.chartData.textComboCustom": "Niestandardowy złożony", "Common.define.chartData.textDoughnut": "Pierś<PERSON>nio<PERSON>", "Common.define.chartData.textHBarNormal": "Słupkowy grupowany", "Common.define.chartData.textHBarNormal3d": "Słupkowy grupowany 3D", "Common.define.chartData.textHBarStacked": "Skumulowany słupkowy", "Common.define.chartData.textHBarStacked3d": "Skumulowany słupkowy 3D", "Common.define.chartData.textHBarStackedPer": "100% skumulowany słupkowy", "Common.define.chartData.textHBarStackedPer3d": "100% skumulowany słupkowy 3D", "Common.define.chartData.textLine": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textLine3d": "Liniowy 3D", "Common.define.chartData.textLineMarker": "Liniowy ze znacznikami", "Common.define.chartData.textLineStacked": "Skumulowany liniowy", "Common.define.chartData.textLineStackedMarker": "Skumulowany liniowy ze znacznikami", "Common.define.chartData.textLineStackedPer": "100% skumulowany liniowy", "Common.define.chartData.textLineStackedPerMarker": "100% skumulowany liniowy ze znacznikami", "Common.define.chartData.textPie": "Kołowy", "Common.define.chartData.textPie3d": "Kołowy 3D", "Common.define.chartData.textPoint": "XY (Punktowy)", "Common.define.chartData.textScatter": "Punktowy", "Common.define.chartData.textScatterLine": "Punktowy z prostymi liniami", "Common.define.chartData.textScatterLineMarker": "Punktowy z prostymi liniami i znacznikami", "Common.define.chartData.textScatterSmooth": "Punktowy z wygładzonymi liniami", "Common.define.chartData.textScatterSmoothMarker": "Punktowy z wygładzonymi liniami i znacznikami", "Common.define.chartData.textStock": "Zbiory", "Common.define.chartData.textSurface": "Powierzchnia", "Common.Translation.textMoreButton": "<PERSON><PERSON><PERSON><PERSON>j", "Common.Translation.warnFileLocked": "<PERSON><PERSON> mo<PERSON><PERSON><PERSON> edyt<PERSON> tego pliku, p<PERSON><PERSON><PERSON><PERSON> jest on edytowany w innej aplikacji.", "Common.Translation.warnFileLockedBtnEdit": "Utwórz kopię", "Common.Translation.warnFileLockedBtnView": "Otwarte do oglądania", "Common.UI.ButtonColored.textAutoColor": "Automatyczny", "Common.UI.ButtonColored.textNewColor": "Nowy niestandardowy kolor", "Common.UI.Calendar.textApril": "Kwiecień", "Common.UI.Calendar.textAugust": "Sierpień", "Common.UI.Calendar.textDecember": "Grudzień", "Common.UI.Calendar.textFebruary": "<PERSON><PERSON>", "Common.UI.Calendar.textJanuary": "Styczeń", "Common.UI.Calendar.textJuly": "Lipiec", "Common.UI.Calendar.textJune": "Czerwiec", "Common.UI.Calendar.textMarch": "Marzec", "Common.UI.Calendar.textMay": "Maj", "Common.UI.Calendar.textMonths": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.Calendar.textNovember": "Listopad", "Common.UI.Calendar.textOctober": "Październik", "Common.UI.Calendar.textSeptember": "Wrzesień", "Common.UI.Calendar.textShortApril": "K<PERSON>", "Common.UI.Calendar.textShortAugust": "<PERSON><PERSON>", "Common.UI.Calendar.textShortDecember": "Gru", "Common.UI.Calendar.textShortFebruary": "Lut", "Common.UI.Calendar.textShortFriday": "Pią", "Common.UI.Calendar.textShortJanuary": "Sty", "Common.UI.Calendar.textShortJuly": "Lip", "Common.UI.Calendar.textShortJune": "<PERSON><PERSON>", "Common.UI.Calendar.textShortMarch": "Mar", "Common.UI.Calendar.textShortMay": "Maj", "Common.UI.Calendar.textShortMonday": "Pon", "Common.UI.Calendar.textShortNovember": "<PERSON><PERSON>", "Common.UI.Calendar.textShortOctober": "<PERSON><PERSON>", "Common.UI.Calendar.textShortSaturday": "Sob", "Common.UI.Calendar.textShortSeptember": "Wrz", "Common.UI.Calendar.textShortSunday": "<PERSON><PERSON>", "Common.UI.Calendar.textShortThursday": "Czw", "Common.UI.Calendar.textShortTuesday": "Wto", "Common.UI.Calendar.textShortWednesday": "<PERSON><PERSON>", "Common.UI.Calendar.textYears": "lata", "Common.UI.ComboBorderSize.txtNoBorders": "Bez k<PERSON>ędzi", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "Bez k<PERSON>ędzi", "Common.UI.ComboDataView.emptyComboText": "Brak styli", "Common.UI.ExtendedColorDialog.addButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textCurrent": "Obecny", "Common.UI.ExtendedColorDialog.textHexErr": "Wprowadzon<PERSON> wartość jest nieprawidłowa.<br>W<PERSON><PERSON><PERSON><PERSON> wartość w zakresie od 000000 do FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "Nowy", "Common.UI.ExtendedColorDialog.textRGBErr": "Wprowadzona wartość jest nieprawidłowa.<br><PERSON><PERSON><PERSON><PERSON><PERSON> wartość liczbową w zakresie od 0 do 255.", "Common.UI.HSBColorPicker.textNoColor": "Brak koloru", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "<PERSON><PERSON><PERSON> hasło", "Common.UI.SearchBar.textFind": "Znajdź", "Common.UI.SearchBar.tipCloseSearch": "Zamknij wyszukiwanie", "Common.UI.SearchBar.tipNextResult": "Następny wynik", "Common.UI.SearchBar.tipPreviousResult": "Wcześniejszy wynik", "Common.UI.SearchDialog.textHighlight": "Podświetl wyniki", "Common.UI.SearchDialog.textMatchCase": "Rozróżniana wielkość liter", "Common.UI.SearchDialog.textReplaceDef": "Wprowadź tekst zastępczy", "Common.UI.SearchDialog.textSearchStart": "Wprowadź tekst tutaj", "Common.UI.SearchDialog.textTitle": "Znajdź i zamień", "Common.UI.SearchDialog.textTitle2": "Znajdź", "Common.UI.SearchDialog.textWholeWords": "Tylko całe słowa", "Common.UI.SearchDialog.txtBtnHideReplace": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.txtBtnReplace": "Zamień", "Common.UI.SearchDialog.txtBtnReplaceAll": "Zamień wszystko", "Common.UI.SynchronizeTip.textDontShow": "<PERSON>e pokazuj tej wiadomości ponownie", "Common.UI.SynchronizeTip.textSynchronize": "Dokument został zmieniony przez innego użytkownika.<br><PERSON><PERSON><PERSON> k<PERSON>, aby zap<PERSON>ć swoje zmiany i ponownie załadować zmiany.", "Common.UI.ThemeColorPalette.textRecentColors": "Ostatnie kolory", "Common.UI.ThemeColorPalette.textStandartColors": "<PERSON><PERSON><PERSON>e", "Common.UI.ThemeColorPalette.textThemeColors": "<PERSON><PERSON><PERSON> moty<PERSON>", "Common.UI.Themes.txtThemeClassicLight": "Klasyczny jasny", "Common.UI.Themes.txtThemeDark": "Ciemny", "Common.UI.Themes.txtThemeLight": "<PERSON><PERSON><PERSON>", "Common.UI.Window.cancelButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.Window.closeButtonText": "Zamknij", "Common.UI.Window.noButtonText": "<PERSON><PERSON>", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "Potwierdzenie", "Common.UI.Window.textDontShow": "<PERSON>e pokazuj tej wiadomości ponownie", "Common.UI.Window.textError": "Błąd", "Common.UI.Window.textInformation": "Informacja", "Common.UI.Window.textWarning": "Ostrzeżenie", "Common.UI.Window.yesButtonText": "Tak", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Shift", "Common.Views.About.txtAddress": "adres:", "Common.Views.About.txtLicensee": "LICENCJOBIORCA", "Common.Views.About.txtLicensor": "LICENCJODAWCA", "Common.Views.About.txtMail": "e-mail:", "Common.Views.About.txtPoweredBy": "zasilany przez", "Common.Views.About.txtTel": "tel.:", "Common.Views.About.txtVersion": "<PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textAdd": "<PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textApplyText": "Zastosuj podcz<PERSON> pisania", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Autokorekta tekstu", "Common.Views.AutoCorrectDialog.textAutoFormat": "Formatuj automatycznie podczas pisania", "Common.Views.AutoCorrectDialog.textBulleted": "Listy punktowane automatycznie", "Common.Views.AutoCorrectDialog.textBy": "Na", "Common.Views.AutoCorrectDialog.textDelete": "Usuń", "Common.Views.AutoCorrectDialog.textFLSentence": "Zamień pierwszą literę w zdaniach na wielką", "Common.Views.AutoCorrectDialog.textHyperlink": "Ścieżki internetowe i sieciowe z hiperłączami", "Common.Views.AutoCorrectDialog.textHyphens": "Łączniki (--) na pauzy (—)", "Common.Views.AutoCorrectDialog.textMathCorrect": "Autokorekta matematyczna", "Common.Views.AutoCorrectDialog.textNumbered": "Listy numerowane automatycznie", "Common.Views.AutoCorrectDialog.textQuotes": "Cudzysłowy \"proste\" na \"drukarskie\"", "Common.Views.AutoCorrectDialog.textRecognized": "Rozpoznawane funkcje", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "Następujące wyrażenia są rozpoznawanymi wyrażeniami matematycznymi. Nie będą one automatycznie pisane kursywą.", "Common.Views.AutoCorrectDialog.textReplace": "Zamień", "Common.Views.AutoCorrectDialog.textReplaceText": "Zamień podczas pisania", "Common.Views.AutoCorrectDialog.textReplaceType": "Zamień tekst podczas pisania", "Common.Views.AutoCorrectDialog.textReset": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textResetAll": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ustawienia domyślne", "Common.Views.AutoCorrectDialog.textRestore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textTitle": "Autokorekta", "Common.Views.AutoCorrectDialog.textWarnAddRec": "Rozpoznawane funkcje muszą zawierać tylko litery od A do Z, wielkie lub małe.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "Każde dodane wyrażenie zostanie usunięte, a usunięte zostaną przywrócone. <PERSON><PERSON><PERSON> k<PERSON>?", "Common.Views.AutoCorrectDialog.warnReplace": "Wpis autokorekty dla %1 już istnieje. <PERSON><PERSON> ch<PERSON>z go wymienić?", "Common.Views.AutoCorrectDialog.warnReset": "Wszelkie dodane autokorekty zostaną usunięte, a zmienione zostaną przywrócone oryginalne wartości. <PERSON><PERSON> ch<PERSON> kont<PERSON>?", "Common.Views.AutoCorrectDialog.warnRestore": "Wpis autokorekty dla %1 zostanie zresetowany do pierwotnej wartości. <PERSON><PERSON> ch<PERSON>z kontynuować?", "Common.Views.Chat.textSend": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.mniAuthorAsc": "Autor <PERSON>", "Common.Views.Comments.mniAuthorDesc": "Autor <PERSON>", "Common.Views.Comments.mniDateAsc": "<PERSON>d starych do nowych", "Common.Views.Comments.mniDateDesc": "<PERSON>d nowych do starych", "Common.Views.Comments.mniPositionAsc": "Z góry", "Common.Views.Comments.mniPositionDesc": "Z dołu", "Common.Views.Comments.textAdd": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textAddComment": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textAddCommentToDoc": "Dodaj komentarz do", "Common.Views.Comments.textAddReply": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textAll": "Wszystkie", "Common.Views.Comments.textAnonym": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textCancel": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textClose": "Zamknij", "Common.Views.Comments.textClosePanel": "Zamknij komentarze", "Common.Views.Comments.textComments": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "Wprowadź twój komentarz tutaj", "Common.Views.Comments.textHintAddComment": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textOpenAgain": "Otwórz ponownie", "Common.Views.Comments.textReply": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolve": "Roz<PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolved": "Rozwiązany", "Common.Views.Comments.textSort": "Sort<PERSON>j komentarze", "Common.Views.Comments.textViewResolved": "Nie masz uprawnień do ponownego otwarcia komentarza", "Common.Views.Comments.txtEmpty": "Nie ma komentarzy w tym dokumencie.", "Common.Views.CopyWarningDialog.textDontShow": "<PERSON>e pokazuj tej wiadomości ponownie", "Common.Views.CopyWarningDialog.textMsg": "<PERSON><PERSON><PERSON><PERSON>, wycinanie i wklejanie za pomocą przycisków edytora i działań menu kontekstowego zostanie przeprowadzone tylko w tej karcie edytora.<br><br><PERSON><PERSON> s<PERSON><PERSON><PERSON>ć lub w<PERSON><PERSON> do lub z aplikacji poza kartą edytora, użyj następujących kombinacji klawiszy:", "Common.Views.CopyWarningDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON>, Wytnij i Wklej", "Common.Views.CopyWarningDialog.textToCopy": "dla kopiowania", "Common.Views.CopyWarningDialog.textToCut": "dla w<PERSON><PERSON>", "Common.Views.CopyWarningDialog.textToPaste": "do wklejenia", "Common.Views.DocumentAccessDialog.textLoading": "Ładowanie...", "Common.Views.DocumentAccessDialog.textTitle": "Ustawienia udostępniania", "Common.Views.ExternalDiagramEditor.textTitle": "<PERSON><PERSON><PERSON>", "Common.Views.ExternalMergeEditor.textTitle": "Odbiorcy korespondencji seryjnej", "Common.Views.ExternalOleEditor.textTitle": "<PERSON><PERSON><PERSON>", "Common.Views.Header.labelCoUsersDescr": "Użytkownicy obecnie edytujący plik:", "Common.Views.Header.textAddFavorite": "Dodaj do ulubionych", "Common.Views.Header.textAdvSettings": "Ustawi<PERSON>e", "Common.Views.Header.textBack": "Otwórz lokalizację pliku", "Common.Views.Header.textCompactView": "Ukryj pasek narzędzi", "Common.Views.Header.textHideLines": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textHideStatusBar": "<PERSON><PERSON><PERSON><PERSON> pasek stanu", "Common.Views.Header.textRemoveFavorite": "Usuń z ulubionych", "Common.Views.Header.textShare": "Udostępnij", "Common.Views.Header.textZoom": "Powiększenie", "Common.Views.Header.tipAccessRights": "Zarządzaj prawami dostępu do dokumentu", "Common.Views.Header.tipDownload": "Pobierz plik", "Common.Views.Header.tipGoEdit": "Edytuj bieżący plik", "Common.Views.Header.tipPrint": "<PERSON><PERSON><PERSON> plik", "Common.Views.Header.tipRedo": "<PERSON>yk<PERSON><PERSON> pono<PERSON>ie", "Common.Views.Header.tipSave": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipSearch": "Szukaj", "Common.Views.Header.tipUndo": "Cof<PERSON>j", "Common.Views.Header.tipUsers": "Zobacz użytkowników", "Common.Views.Header.tipViewSettings": "Wyświ<PERSON>l us<PERSON>wienia", "Common.Views.Header.tipViewUsers": "Wyświetl użytkowników i zarządzaj prawami dostępu do dokumentu", "Common.Views.Header.txtAccessRights": "Zmień prawa dostępu", "Common.Views.Header.txtRename": "Zmień nazwę", "Common.Views.History.textCloseHistory": "Zamknij historię", "Common.Views.History.textHide": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.History.textHideAll": "Ukryj szczegółowe zmiany", "Common.Views.History.textRestore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.History.textShow": "Rozszerz", "Common.Views.History.textShowAll": "Pokaż szczegółowe zmiany", "Common.Views.History.textVer": "ver.", "Common.Views.ImageFromUrlDialog.textUrl": "Wklej link URL do obrazu:", "Common.Views.ImageFromUrlDialog.txtEmpty": "To pole jest wymagane", "Common.Views.ImageFromUrlDialog.txtNotUrl": "To pole powinno by<PERSON> adresem URL w formacie \"http://www.example.com\"", "Common.Views.InsertTableDialog.textInvalidRowsCols": "Musisz podać liczby wierszy i kolumn.", "Common.Views.InsertTableDialog.txtColumns": "<PERSON><PERSON><PERSON><PERSON> kolumn", "Common.Views.InsertTableDialog.txtMaxText": "<PERSON><PERSON><PERSON><PERSON><PERSON> dla tego pola to {0}", "Common.Views.InsertTableDialog.txtMinText": "<PERSON><PERSON><PERSON> dla tego pola to {0}", "Common.Views.InsertTableDialog.txtRows": "Liczba wierszy", "Common.Views.InsertTableDialog.txtTitle": "Rozmiar tablicy", "Common.Views.InsertTableDialog.txtTitleSplit": "Podziel komórkę", "Common.Views.LanguageDialog.labelSelect": "Wybierz język dokumentu", "Common.Views.OpenDialog.closeButtonText": "Zamknij plik", "Common.Views.OpenDialog.txtEncoding": "Kodowanie", "Common.Views.OpenDialog.txtIncorrectPwd": "<PERSON><PERSON><PERSON> jest nieprawidłowe.", "Common.Views.OpenDialog.txtOpenFile": "<PERSON><PERSON><PERSON><PERSON><PERSON> hasło, aby otworzyć plik", "Common.Views.OpenDialog.txtPassword": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtPreview": "Podgląd", "Common.Views.OpenDialog.txtProtected": "Po wprowadzeniu hasła i otwarciu pliku bieżące hasło do pliku zostanie zresetowane", "Common.Views.OpenDialog.txtTitle": "Wybierz %1 opcji", "Common.Views.OpenDialog.txtTitleProtected": "Plik chroniony", "Common.Views.PasswordDialog.txtDescription": "Ustaw hasło aby zabezpieczyć ten dokument", "Common.Views.PasswordDialog.txtIncorrectPwd": "<PERSON><PERSON> nie są takie same", "Common.Views.PasswordDialog.txtPassword": "<PERSON><PERSON><PERSON>", "Common.Views.PasswordDialog.txtRepeat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.PasswordDialog.txtTitle": "Ustaw hasło", "Common.Views.PasswordDialog.txtWarning": "Uwaga: <PERSON><PERSON><PERSON> zapomnisz lub zgubisz hasło, nie będzie możliwości odzyskania go. Zapisz go i nikomu nie udostępniaj.", "Common.Views.PluginDlg.textLoading": "Ładowanie", "Common.Views.Plugins.groupCaption": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Plugins.strPlugins": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Plugins.textLoading": "Ładowanie", "Common.Views.Plugins.textStart": "Start", "Common.Views.Plugins.textStop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Protection.hintAddPwd": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Protection.hintPwd": "<PERSON><PERSON><PERSON> lub usuń hasło", "Common.Views.Protection.hintSignature": "Dodaj podpis cyfrowy lub wiersz do podpisu", "Common.Views.Protection.txtAddPwd": "<PERSON><PERSON><PERSON>", "Common.Views.Protection.txtChangePwd": "<PERSON><PERSON><PERSON> hasło", "Common.Views.Protection.txtDeletePwd": "<PERSON><PERSON><PERSON>o", "Common.Views.Protection.txtEncrypt": "Szyfruj", "Common.Views.Protection.txtInvisibleSignature": "<PERSON><PERSON><PERSON> c<PERSON>y", "Common.Views.Protection.txtSignature": "Sygnatura", "Common.Views.Protection.txtSignatureLine": "Dodaj linię do podpisu", "Common.Views.RenameDialog.textName": "Nazwa pliku", "Common.Views.RenameDialog.txtInvalidName": "Nazwa pliku nie może zawierać żadnego z następujących znaków:", "Common.Views.ReviewChanges.hintNext": "Do następnej zmiany", "Common.Views.ReviewChanges.hintPrev": "Do poprzedniej zmiany", "Common.Views.ReviewChanges.mniFromFile": "Dokument z pliku", "Common.Views.ReviewChanges.mniFromStorage": "Dokument z magazynu", "Common.Views.ReviewChanges.mniFromUrl": "Dokument z adresu URL", "Common.Views.ReviewChanges.mniSettings": "Opcje porównania", "Common.Views.ReviewChanges.strFast": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.strFastDesc": "Współedycja w czasie rzeczywistym. Wszystkie zmiany są zapisywane automatycznie.", "Common.Views.ReviewChanges.strStrict": "Ścisły", "Common.Views.ReviewChanges.strStrictDesc": "Użyj przycisku „Zapisz”, aby zsynchronizować zmiany wprowadzane przez Ciebie i innych.", "Common.Views.ReviewChanges.textEnable": "Włącz", "Common.Views.ReviewChanges.textWarnTrackChanges": "Śledzenie zmian będzie włączone dla wszystkich użytkowników posiadających pełen dostęp. Następnym razem, gdy k<PERSON>ś otworzy dokument, opcja śledzenia zmian będzie włączona.", "Common.Views.ReviewChanges.textWarnTrackChangesTitle": "W<PERSON><PERSON><PERSON><PERSON>ć śledzenie zmian dla wszystkich?", "Common.Views.ReviewChanges.tipAcceptCurrent": "Zaakceptuj bieżącą zmianę", "Common.Views.ReviewChanges.tipCoAuthMode": "Ustaw tryb współedycji", "Common.Views.ReviewChanges.tipCommentRem": "Usuń komentarze", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Usuń aktualne komentarze", "Common.Views.ReviewChanges.tipCommentResolve": "Rozwiąż komentarze", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Rozwiąż bieżące komentarze", "Common.Views.ReviewChanges.tipCompare": "Porównaj obecny dokument z innym", "Common.Views.ReviewChanges.tipHistory": "Pokaż historię we<PERSON>ji", "Common.Views.ReviewChanges.tipRejectCurrent": "Odrzuć bieżącą zmianę", "Common.Views.ReviewChanges.tipReview": "Śledzenie zmian", "Common.Views.ReviewChanges.tipReviewView": "<PERSON><PERSON><PERSON><PERSON> tryb, w kt<PERSON>rym mają być wyświetlane zmiany", "Common.Views.ReviewChanges.tipSetDocLang": "Ustaw język dokumentu", "Common.Views.ReviewChanges.tipSetSpelling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>i", "Common.Views.ReviewChanges.tipSharing": "Zarządzaj prawami dostępu do dokumentu", "Common.Views.ReviewChanges.txtAccept": "Ak<PERSON>pt<PERSON>j", "Common.Views.ReviewChanges.txtAcceptAll": "Zaakceptuj wszystkie zmiany", "Common.Views.ReviewChanges.txtAcceptChanges": "Zaak<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtAcceptCurrent": "Zaakceptuj bieżącą zmianę", "Common.Views.ReviewChanges.txtChat": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtClose": "Zamknij", "Common.Views.ReviewChanges.txtCoAuthMode": "Tryb współtworzenia", "Common.Views.ReviewChanges.txtCommentRemAll": "Usuń wszystkie komentarze", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Usuń aktualne komentarze", "Common.Views.ReviewChanges.txtCommentRemMy": "Usuń moje komentarze", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Usuń moje bieżące komentarze", "Common.Views.ReviewChanges.txtCommentRemove": "Usuń", "Common.Views.ReviewChanges.txtCommentResolve": "Roz<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentResolveAll": "Rozwiąż wszystkie komentarze", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Rozwiąż bieżące komentarze", "Common.Views.ReviewChanges.txtCommentResolveMy": "Rozwiąż moje komentarze", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Rozwiąż moje bieżące komentarze", "Common.Views.ReviewChanges.txtCompare": "Porównaj", "Common.Views.ReviewChanges.txtDocLang": "Język", "Common.Views.ReviewChanges.txtEditing": "Redagowanie", "Common.Views.ReviewChanges.txtFinal": "Wszystkie zmiany zostały zaakceptowane {0}", "Common.Views.ReviewChanges.txtFinalCap": "Ostateczny", "Common.Views.ReviewChanges.txtHistory": "<PERSON> wersji", "Common.Views.ReviewChanges.txtMarkup": "Wszystkie zmiany {0}", "Common.Views.ReviewChanges.txtMarkupCap": "Poprawki i objaśnienia", "Common.Views.ReviewChanges.txtMarkupSimple": "Wszystkich zmian {0}<br><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtMarkupSimpleCap": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtNext": "Do następnej zmiany", "Common.Views.ReviewChanges.txtOff": "Wyłącz dla mnie", "Common.Views.ReviewChanges.txtOffGlobal": "Wyłącz dla mnie i wszystkich", "Common.Views.ReviewChanges.txtOn": "Włącz dla mnie", "Common.Views.ReviewChanges.txtOnGlobal": "Włącz dla mnie i wszystkich", "Common.Views.ReviewChanges.txtOriginal": "Wszystkie zmiany zostały odrzucone {0}", "Common.Views.ReviewChanges.txtOriginalCap": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtPrev": "Do poprzedniej zmiany", "Common.Views.ReviewChanges.txtPreview": "Podgląd", "Common.Views.ReviewChanges.txtReject": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectAll": "Odrzuć wszystkie zmiany", "Common.Views.ReviewChanges.txtRejectChanges": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectCurrent": "Odrzuć bieżącą zmianę", "Common.Views.ReviewChanges.txtSharing": "Dostęp współdzielony", "Common.Views.ReviewChanges.txtSpelling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>i", "Common.Views.ReviewChanges.txtTurnon": "Śledzenie zmian", "Common.Views.ReviewChanges.txtView": "<PERSON><PERSON> w<PERSON>świ<PERSON>", "Common.Views.ReviewChangesDialog.textTitle": "Przejrzyj zmiany", "Common.Views.ReviewChangesDialog.txtAccept": "Ak<PERSON>pt<PERSON>j", "Common.Views.ReviewChangesDialog.txtAcceptAll": "Zaakceptuj wszystkie zmiany", "Common.Views.ReviewChangesDialog.txtAcceptCurrent": "Zaakceptuj bieżącą zmianę", "Common.Views.ReviewChangesDialog.txtNext": "Do następnej zmiany", "Common.Views.ReviewChangesDialog.txtPrev": "Do poprzedniej zmiany", "Common.Views.ReviewChangesDialog.txtReject": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChangesDialog.txtRejectAll": "Odrzuć wszystkie zmiany", "Common.Views.ReviewChangesDialog.txtRejectCurrent": "Odrzuć bieżącą zmianę", "Common.Views.ReviewPopover.textAdd": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textAddReply": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textCancel": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textClose": "Zamknij", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textFollowMove": "Śledź ruch", "Common.Views.ReviewPopover.textMention": "+wzmianka zapewni użytkownikowi dostęp do pliku i wyśle e-maila", "Common.Views.ReviewPopover.textMentionNotify": "+wzmianka powiadomi użytkownika e-mailem", "Common.Views.ReviewPopover.textOpenAgain": "Otwórz ponownie", "Common.Views.ReviewPopover.textReply": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textResolve": "Roz<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textViewResolved": "Nie masz uprawnień do ponownego otwarcia komentarza", "Common.Views.ReviewPopover.txtAccept": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.txtDeleteTip": "Usuń", "Common.Views.ReviewPopover.txtEditTip": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.txtReject": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SaveAsDlg.textLoading": "Ładowanie", "Common.Views.SaveAsDlg.textTitle": "Folder do zapisu", "Common.Views.SearchPanel.textCaseSensitive": "Rozróżnienie wielkich i małych liter", "Common.Views.SearchPanel.textCloseSearch": "Zamknij wyszukiwanie", "Common.Views.SearchPanel.textFind": "Znajdź", "Common.Views.SearchPanel.textFindAndReplace": "Znajdź i zamień", "Common.Views.SearchPanel.textNoMatches": "Brak dopasowań", "Common.Views.SearchPanel.textNoSearchResults": "Brak wyników wyszukiwania", "Common.Views.SearchPanel.textReplace": "Zamień", "Common.Views.SearchPanel.textReplaceAll": "Zamień wszystko", "Common.Views.SearchPanel.textTooManyResults": "Jest zbyt dużo wyników, aby je tutaj wyświ<PERSON>", "Common.Views.SearchPanel.textWholeWords": "Tylko całe słowa", "Common.Views.SearchPanel.tipNextResult": "Następny wynik", "Common.Views.SearchPanel.tipPreviousResult": "Poprzedni wynik", "Common.Views.SelectFileDlg.textLoading": "Ładowanie", "Common.Views.SelectFileDlg.textTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textBold": "Pogrubienie", "Common.Views.SignDialog.textCertificate": "Certy<PERSON><PERSON>", "Common.Views.SignDialog.textChange": "Zmień", "Common.Views.SignDialog.textInputName": "Wpisz imię i nazwisko osoby podpisującej", "Common.Views.SignDialog.textItalic": "Ku<PERSON>ywa", "Common.Views.SignDialog.textNameError": "Imię i nazwisko osoby podpisującej nie może być puste.", "Common.Views.SignDialog.textPurpose": "Cel podpisywania tego dokumentu", "Common.Views.SignDialog.textSelect": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textSelectImage": "<PERSON><PERSON><PERSON><PERSON> obraz", "Common.Views.SignDialog.textSignature": "<PERSON><PERSON> wyglą<PERSON> podpis:", "Common.Views.SignDialog.textTitle": "Podpisz dokument", "Common.Views.SignDialog.textUseImage": "lub k<PERSON><PERSON><PERSON> \"<PERSON><PERSON><PERSON><PERSON> obraz\", aby <PERSON><PERSON><PERSON><PERSON> obrazu jako pod<PERSON>u", "Common.Views.SignDialog.textValid": "Ważny od %1 do %2", "Common.Views.SignDialog.tipFontName": "Nazwa czcionki", "Common.Views.SignDialog.tipFontSize": "Rozmiar <PERSON>ki", "Common.Views.SignSettingsDialog.textAllowComment": "Zezwól podpisującemu na dodawanie komentarza w oknie podpisu", "Common.Views.SignSettingsDialog.textInfoEmail": "E-mail", "Common.Views.SignSettingsDialog.textInfoName": "Nazwa", "Common.Views.SignSettingsDialog.textInfoTitle": "Stanowisko osoby podpisującej", "Common.Views.SignSettingsDialog.textInstructions": "Instrukcje dla osoby podpisującej", "Common.Views.SignSettingsDialog.textShowDate": "Pokaż datę wykonania podpisu", "Common.Views.SignSettingsDialog.textTitle": "Konfiguracja podpisu", "Common.Views.SignSettingsDialog.txtEmpty": "To pole jest wymagane", "Common.Views.SymbolTableDialog.textCharacter": "Znak", "Common.Views.SymbolTableDialog.textCode": "Nazwa Unicode", "Common.Views.SymbolTableDialog.textCopyright": "Znak zastrzeżenia prawa autorskiego", "Common.Views.SymbolTableDialog.textDCQuote": "Podwójny cudzysłów zamykający", "Common.Views.SymbolTableDialog.textDOQuote": "Podwójny cudzysłów otwierający", "Common.Views.SymbolTableDialog.textEllipsis": "Wielokropek", "Common.Views.SymbolTableDialog.textEmDash": "<PERSON><PERSON>", "Common.Views.SymbolTableDialog.textEmSpace": "Długa spacja", "Common.Views.SymbolTableDialog.textEnDash": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textEnSpace": "Krótka spacja", "Common.Views.SymbolTableDialog.textFont": "Czcionka", "Common.Views.SymbolTableDialog.textNBHyphen": "Łącznik nierozdzielający", "Common.Views.SymbolTableDialog.textNBSpace": "Spacja nierozdzielająca", "Common.Views.SymbolTableDialog.textPilcrow": "Aka<PERSON>", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 d<PERSON>ug<PERSON><PERSON>ji", "Common.Views.SymbolTableDialog.textRange": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textRecent": "Ostatnio używane symbole", "Common.Views.SymbolTableDialog.textRegistered": "Zastrzeżony znak towarowy", "Common.Views.SymbolTableDialog.textSCQuote": "Pojedynczy cudzysłów zamykający", "Common.Views.SymbolTableDialog.textSection": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textShortcut": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "Common.Views.SymbolTableDialog.textSHyphen": "Łącznik miękki", "Common.Views.SymbolTableDialog.textSOQuote": "Pojedynczy cudzysłów otwierający", "Common.Views.SymbolTableDialog.textSpecial": "<PERSON><PERSON><PERSON> s<PERSON>", "Common.Views.SymbolTableDialog.textSymbols": "Symbole", "Common.Views.SymbolTableDialog.textTitle": "Symbole", "Common.Views.SymbolTableDialog.textTradeMark": "Znak towarowy", "Common.Views.UserNameDialog.textDontShow": "<PERSON><PERSON> pytaj mnie ponownie", "Common.Views.UserNameDialog.textLabel": "Etykieta:", "Common.Views.UserNameDialog.textLabelError": "Etykieta nie może być pusta.", "DE.Controllers.LeftMenu.leavePageText": "Wszystkie niezapisane zmiany w tym dokumencie zostaną utracone. Kliknij przycisk \"Anuluj\", a następnie \"Zapisz\", aby je zapisa<PERSON>. Kliknij \"OK\", aby usun<PERSON><PERSON> wszystkie niezapisane zmiany.", "DE.Controllers.LeftMenu.newDocumentTitle": "Nienazwany dokument", "DE.Controllers.LeftMenu.notcriticalErrorTitle": "Ostrzeżenie", "DE.Controllers.LeftMenu.requestEditRightsText": "Żądanie praw do edycji...", "DE.Controllers.LeftMenu.textLoadHistory": "Wczytywanie historii wersji...", "DE.Controllers.LeftMenu.textNoTextFound": "<PERSON><PERSON> znale<PERSON>, k<PERSON><PERSON><PERSON><PERSON> s<PERSON>sz. <PERSON><PERSON><PERSON> dos<PERSON>uj opcje wyszukiwania.", "DE.Controllers.LeftMenu.textReplaceSkipped": "Zastąpiono. {0} zdarzenia zostały pominięte.", "DE.Controllers.LeftMenu.textReplaceSuccess": "Wyszukiwanie zakończone. Zastąpiono {0}", "DE.Controllers.LeftMenu.txtCompatible": "Dokument zostanie zapisany w nowym formacie. <PERSON><PERSON><PERSON><PERSON><PERSON> to korzystanie ze wszystkich funkcji edytora, ale może wpłynąć na układ dokumentu.<br><PERSON><PERSON><PERSON><PERSON> op<PERSON><PERSON> \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\" w us<PERSON><PERSON><PERSON><PERSON>, je<PERSON><PERSON>, aby pliki były kompatybilne ze starszymi wersjami MS Word.", "DE.Controllers.LeftMenu.txtUntitled": "Bez Nazwy", "DE.Controllers.LeftMenu.warnDownloadAs": "<PERSON><PERSON>li kontynuujesz zapisywanie w tym formacie, wszystkie funkcje oprócz tekstu zostaną utracone.<br><PERSON><PERSON> na pewno chcesz kontynuować?", "DE.Controllers.LeftMenu.warnDownloadAsRTF": "<PERSON><PERSON><PERSON> będ<PERSON><PERSON> kontynuować zapisywanie w tym formacie, c<PERSON><PERSON><PERSON>ć formatowania może zostać utracona.<br><PERSON><PERSON> na pewno chcesz kontynuować?", "DE.Controllers.Main.applyChangesTextText": "Zała<PERSON>j zmiany...", "DE.Controllers.Main.applyChangesTitleText": "Zała<PERSON>j <PERSON>mian<PERSON>", "DE.Controllers.Main.convertationTimeoutText": "Przekroczono limit czasu konwersji.", "DE.Controllers.Main.criticalErrorExtText": "<PERSON><PERSON><PERSON><PERSON><PERSON> \"OK\", aby p<PERSON><PERSON><PERSON><PERSON><PERSON> do listy dokumentów.", "DE.Controllers.Main.criticalErrorTitle": "Błąd", "DE.Controllers.Main.downloadErrorText": "Pobieranie ni<PERSON>.", "DE.Controllers.Main.downloadMergeText": "Pobieranie...", "DE.Controllers.Main.downloadMergeTitle": "Pobieranie", "DE.Controllers.Main.downloadTextText": "Pobieranie dokumentu...", "DE.Controllers.Main.downloadTitleText": "Pobieranie dokumentu", "DE.Controllers.Main.errorAccessDeny": "Próbujesz wykonać działanie, na które nie masz uprawnień.<br><PERSON><PERSON><PERSON> skontaktować się z <PERSON>em serwera dokumentów.", "DE.Controllers.Main.errorBadImageUrl": "Adres URL obrazu jest błędny", "DE.Controllers.Main.errorCoAuthoringDisconnect": "Połączenie z serwerem zostało utracone. Nie można teraz edytować dokumentu.", "DE.Controllers.Main.errorComboSeries": "<PERSON>by utworz<PERSON>ć wykres złożony, wybierz co najmniej dwie serie danych.", "DE.Controllers.Main.errorCompare": "Funkcja porównaj dokumenty nie jest dostępna podczas wspólnej edycji.", "DE.Controllers.Main.errorConnectToServer": "Nie można zapisać dokumentu. Sprawdź ustawienia połączenia lub skontaktuj się z <PERSON>em.<br>Po kliknięciu przycisku \"OK\" zostanie wyświetlony monit o pobranie dokumentu.", "DE.Controllers.Main.errorDatabaseConnection": "Błąd zewnętrzny.<br>Błąd połączenia z bazą danych. W przypadku wystąpienia błędu należy skontaktować się z pomocą techniczną.", "DE.Controllers.Main.errorDataEncrypted": "<PERSON><PERSON><PERSON><PERSON><PERSON> z<PERSON>, nie można ich odszyfrować.", "DE.Controllers.Main.errorDataRange": "Błędny zakres danych.", "DE.Controllers.Main.errorDefaultMessage": "Kod błędu: %1", "DE.Controllers.Main.errorDirectUrl": "Sprawdź link do dokumentu.<br>Ten link musi być bezpośrednim linkiem do pliku do pobrania.", "DE.Controllers.Main.errorEditingDownloadas": "Wystąpił błąd podczas pracy z dokumentem.<br><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> „Pobierz jako”, aby zapisać kopię zapasową pliku na dysku twardym komputera.", "DE.Controllers.Main.errorEditingSaveas": "Wystąpił błąd podczas pracy z dokumentem.<br><PERSON><PERSON><PERSON><PERSON> op<PERSON><PERSON> „Zapisz kopię jako...”, aby zap<PERSON>ć kopię zapasową pliku na dysku twardym komputera.", "DE.Controllers.Main.errorEmailClient": "Nie znaleziono klienta poczty e-mail.", "DE.Controllers.Main.errorFilePassProtect": "Dokument jest chroniony hasłem i nie można go otworzyć.", "DE.Controllers.Main.errorFileSizeExceed": "Rozmiar pliku przekracza ustalony limit dla twojego serwera.<br><PERSON><PERSON><PERSON> skontaktowa<PERSON> z administratorem twojego Serwera Dokumentów w celu uzyskania szczegółowych informacji.", "DE.Controllers.Main.errorForceSave": "Wystąpił błąd podczas zapisywania pliku. Użyj opcji „Pobierz jako”, aby zapisać plik na dysku twardym komputera lub spróbuj ponownie później.", "DE.Controllers.Main.errorKeyEncrypt": "Nieznany deskryptor klu<PERSON>a", "DE.Controllers.Main.errorKeyExpire": "Okres ważności deskryptora klucza wygasł", "DE.Controllers.Main.errorLoadingFont": "Czcionki nie zostały załadowane.<br>Skontaktuj się z administratorem usługi Document Server.", "DE.Controllers.Main.errorMailMergeLoadFile": "Ładowanie dokumentu nie powiodło się. Wybierz inny plik.", "DE.Controllers.Main.errorMailMergeSaveFile": "Scalanie nie powiodło się.", "DE.Controllers.Main.errorProcessSaveResult": "Zapisywanie nie powiodło się.", "DE.Controllers.Main.errorServerVersion": "Wersja edytora została zaktualizowana. Strona zostanie ponownie załadowana, aby z<PERSON><PERSON><PERSON><PERSON> z<PERSON>.", "DE.Controllers.Main.errorSessionAbsolute": "Sesja edycji dokumentu wygasła. Proszę ponownie załadować stronę.", "DE.Controllers.Main.errorSessionIdle": "Dokument nie był edytowany przez długi czas. Proszę ponownie załadować stronę.", "DE.Controllers.Main.errorSessionToken": "Połączenie z serwerem zostało przerwane. Proszę ponownie załadować stronę.", "DE.Controllers.Main.errorSetPassword": "<PERSON><PERSON> m<PERSON><PERSON><PERSON> hasła", "DE.Controllers.Main.errorStockChart": "Nieprawidłowa kolejność wierszy. <PERSON><PERSON> zbu<PERSON><PERSON> wykres akcji, umie<PERSON><PERSON> dane na arkuszu w następującej kolejności:<br> cena <PERSON><PERSON>, cena ma<PERSON>, cena <PERSON>, cena zam<PERSON>.", "DE.Controllers.Main.errorSubmit": "Przesyłanie nie powiodło się.", "DE.Controllers.Main.errorToken": "Token bezpieczeństwa dokumentu jest nieprawidłowo uformowany.<br>Prosimy o kontakt z <PERSON>em serwera dokumentów.", "DE.Controllers.Main.errorTokenExpire": "Token zabezpieczeń dokumentu wygasł.<br>Prosimy o kontakt z <PERSON>em serwera dokumentów.", "DE.Controllers.Main.errorUpdateVersion": "Wersja pliku została zmieniona. Strona zostanie ponownie załadowana.", "DE.Controllers.Main.errorUpdateVersionOnDisconnect": " Połączenie z internetem zostało odzyskane, a wersja pliku uległa zmianie.<br><PERSON><PERSON><PERSON> b<PERSON> mógł kont<PERSON> pracę, mus<PERSON>z pobrać plik albo skopiować jego <PERSON>, aby <PERSON><PERSON><PERSON>, że nic nie zostało utracone, a następnie odświeżyć stronę.", "DE.Controllers.Main.errorUserDrop": "Nie można uzyskać dostępu do tego pliku.", "DE.Controllers.Main.errorUsersExceed": "Przekroczono liczbę dozwolonych użytkowników określonych przez plan cenowy wersji", "DE.Controllers.Main.errorViewerDisconnect": "Połączenie zostało utracone. Nadal moż<PERSON><PERSON> dokument,<br>ale nie będziesz mógł pobrać ani wydrukować dokumentu do momentu przywrócenia połączenia i odświeżenia strony.", "DE.Controllers.Main.leavePageText": "Masz niezapisane zmiany w tym dokumencie. Kliknij przycisk \"Zostań na tej stronie\", a następnie \"Zapisz\", aby je zapisa<PERSON>. Kliknij przycisk \"Pozostaw tę stronę\", aby usun<PERSON>ć wszystkie niezapisane zmiany.", "DE.Controllers.Main.leavePageTextOnClose": "Wszystkie niezapisane zmiany w tym dokumencie zostaną utracone. Kliknij przycisk \"Anuluj\", a następnie \"Zapisz\", aby je zapisa<PERSON>. Kliknij \"OK\", aby usun<PERSON><PERSON> wszystkie niezapisane zmiany.", "DE.Controllers.Main.loadFontsTextText": "Ładowanie danych...", "DE.Controllers.Main.loadFontsTitleText": "Łado<PERSON><PERSON> danych", "DE.Controllers.Main.loadFontTextText": "Ładowanie danych...", "DE.Controllers.Main.loadFontTitleText": "Łado<PERSON><PERSON> danych", "DE.Controllers.Main.loadImagesTextText": "Ładowanie obrazów...", "DE.Controllers.Main.loadImagesTitleText": "Ładowanie obrazów", "DE.Controllers.Main.loadImageTextText": "Ładowanie obrazu...", "DE.Controllers.Main.loadImageTitleText": "Ładowanie obrazu", "DE.Controllers.Main.loadingDocumentTextText": "Ładowanie dokumentu...", "DE.Controllers.Main.loadingDocumentTitleText": "Ładowanie dokumentu", "DE.Controllers.Main.mailMergeLoadFileText": "Ładowanie źródła danych", "DE.Controllers.Main.mailMergeLoadFileTitle": "Ładowanie źródła danych", "DE.Controllers.Main.notcriticalErrorTitle": "Ostrzeżenie", "DE.Controllers.Main.openErrorText": "Wys<PERSON>ą<PERSON>ł błąd podczas otwierania pliku.", "DE.Controllers.Main.openTextText": "Otwieranie dokumentu...", "DE.Controllers.Main.openTitleText": "Otwieranie dokumentu", "DE.Controllers.Main.printTextText": "Drukowanie dokumentu...", "DE.Controllers.Main.printTitleText": "Drukowanie dokumentu", "DE.Controllers.Main.reloadButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.requestEditFailedMessageText": "Ktoś właśnie edytuje ten dokument. Proszę spróbuj później.", "DE.Controllers.Main.requestEditFailedTitleText": "Odmo<PERSON> dos<PERSON>ępu", "DE.Controllers.Main.saveErrorText": "Wys<PERSON>ą<PERSON>ł błąd podczas zapisywania pliku.", "DE.Controllers.Main.saveErrorTextDesktop": "<PERSON>e można zapisać lub utwo<PERSON><PERSON>ć tego pliku.<br>Możliwe przyczyny to: <br>1. <PERSON>lik jest tylko do odczytu. <br>2. Plik jest edytowany przez innych użytkowników. <br>3. <PERSON><PERSON><PERSON> jest pełny lub uszkodzony.", "DE.Controllers.Main.saveTextText": "Zapisywanie dokumentu...", "DE.Controllers.Main.saveTitleText": "Zapisywanie dokumentu", "DE.Controllers.Main.scriptLoadError": "Połączenie jest zbyt wolne, niektóre komponenty mogą być niezaładowane. Prosimy o<PERSON><PERSON><PERSON> stronę.", "DE.Controllers.Main.sendMergeText": "Wysyłanie korespondencji seryjnej...", "DE.Controllers.Main.sendMergeTitle": "Wysyłanie korespondencji seryjnej...", "DE.Controllers.Main.splitDividerErrorText": "Liczba wierszy musi być dzielnikiem %1.", "DE.Controllers.Main.splitMaxColsErrorText": "Liczba kolumn musi być mniejsza niż %1.", "DE.Controllers.Main.splitMaxRowsErrorText": "Liczba wierszy musi być mniejsza niż %1.", "DE.Controllers.Main.textAnonymous": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.textApplyAll": "Zastosuj do wszystkich równań", "DE.Controllers.Main.textBuyNow": "Od<PERSON><PERSON><PERSON> stronę web", "DE.Controllers.Main.textChangesSaved": "Wszystkie zmiany zapisane", "DE.Controllers.Main.textClose": "Zamknij", "DE.Controllers.Main.textCloseTip": "Kliknij, żeby zamknąć wskazówkę", "DE.Controllers.Main.textContactUs": "Skontaktuj się z działem sprzedaży", "DE.Controllers.Main.textConvertEquation": "To równanie zostało utworzone za pomocą starej wersji edytora równań, kt<PERSON>ra nie jest już obsługiwana. <PERSON><PERSON> je edyt<PERSON>, przekonwertuj równanie na format Office Math ML.<br>Przekonwertować teraz?", "DE.Controllers.Main.textCustomLoader": "<PERSON><PERSON><PERSON><PERSON>, że zgodnie z warunkami licencji nie jesteś uprawniony do zmiany ładowania. <br> W celu uzyskania wyceny prosimy o kontakt z naszym Działem Sprzedaży.", "DE.Controllers.Main.textDisconnect": "Połączenie zostało utracone", "DE.Controllers.Main.textGuest": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.textHasMacros": "Plik zawiera automatyczne makra. <br> <PERSON><PERSON><PERSON>z uruchomić makra?", "DE.Controllers.Main.textLearnMore": "Dowiedz się więcej", "DE.Controllers.Main.textLoadingDocument": "Ładowanie dokumentu", "DE.Controllers.Main.textLongName": "Wpisz nazwę krótszą niż 128 znaków.", "DE.Controllers.Main.textNoLicenseTitle": "Osiągnięto limit licencji", "DE.Controllers.Main.textPaidFeature": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.textReconnect": "Połączenie przywrócone", "DE.Controllers.Main.textRemember": "Zapamiętaj mój wybór dla wszystkich plików", "DE.Controllers.Main.textRenameError": "Nazwa użytkownika nie może być pusta.", "DE.Controllers.Main.textRenameLabel": "Wpisz nazwę, która ma być używana do współpracy", "DE.Controllers.Main.textShape": "Kształt", "DE.Controllers.Main.textStrict": "<PERSON><PERSON>", "DE.Controllers.Main.textText": "Tekst", "DE.Controllers.Main.textTryUndoRedo": "Funkcje Cofnij/Ponów są wyłączone w trybie \"<PERSON><PERSON>b<PERSON>\" współtworzenia.<br><PERSON><PERSON><PERSON><PERSON> przy<PERSON> \"Tryb ścisły\", aby przejść do trybu ścisłego edytowania, aby edytować plik bez ingerencji innych użytkowników i wysyłać zmiany tylko po zapisaniu. Możesz przełączać się między trybami współtworzenia, używając edytora Ustawienia zaawansowane.", "DE.Controllers.Main.textTryUndoRedoWarn": "Funkcje Cofnij/Ponów są wyłączone w trybie Szybkim współtworzenia.", "DE.Controllers.Main.titleLicenseExp": "Upłynął okres ważności licencji", "DE.Controllers.Main.titleServerVersion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.titleUpdateVersion": "<PERSON><PERSON>ja uległa zmianie", "DE.Controllers.Main.txtAbove": "Powyżej", "DE.Controllers.Main.txtArt": "Twój tekst tutaj", "DE.Controllers.Main.txtBasicShapes": "Kształty podstawowe", "DE.Controllers.Main.txtBelow": "poni<PERSON><PERSON>", "DE.Controllers.Main.txtBookmarkError": "Błąd! Nie zdefiniowano zakładki", "DE.Controllers.Main.txtButtons": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtCallouts": "Objaśnienia", "DE.Controllers.Main.txtCharts": "Wykresy", "DE.Controllers.Main.txtChoose": "Wybierz element", "DE.Controllers.Main.txtClickToLoad": "<PERSON><PERSON><PERSON><PERSON>, aby załado<PERSON>ć obraz", "DE.Controllers.Main.txtCurrentDocument": "Aktualny dokument", "DE.Controllers.Main.txtDiagramTitle": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtEditingMode": "Ustaw tryb edycji...", "DE.Controllers.Main.txtEndOfFormula": "Nieoczekiwany koniec formuły", "DE.Controllers.Main.txtEnterDate": "<PERSON><PERSON>z datę", "DE.Controllers.Main.txtErrorLoadHistory": "Ładowanie historii nie powiodło się", "DE.Controllers.Main.txtEvenPage": "<PERSON><PERSON><PERSON><PERSON> strona", "DE.Controllers.Main.txtFiguredArrows": "Strz<PERSON>ł<PERSON>", "DE.Controllers.Main.txtFirstPage": "<PERSON><PERSON><PERSON> strona", "DE.Controllers.Main.txtFooter": "Stopka", "DE.Controllers.Main.txtFormulaNotInTable": "Formuła nie w tabeli", "DE.Controllers.Main.txtHeader": "Nagłówek", "DE.Controllers.Main.txtHyperlink": "Link", "DE.Controllers.Main.txtIndTooLarge": "<PERSON><PERSON><PERSON> jest za du<PERSON>y", "DE.Controllers.Main.txtLines": "<PERSON><PERSON>", "DE.Controllers.Main.txtMainDocOnly": "Błąd! Tylko dokument główny.", "DE.Controllers.Main.txtMath": "Matematyczne", "DE.Controllers.Main.txtMissArg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> argument", "DE.Controllers.Main.txtMissOperator": "Brak operatora", "DE.Controllers.Main.txtNeedSynchronize": "Masz aktualizacje", "DE.Controllers.Main.txtNone": "Brak", "DE.Controllers.Main.txtNoTableOfContents": "W dokumencie nie ma nagłówków. Zastosuj styl nagłówka do tekstu, aby pojawił się w spisie treści.", "DE.Controllers.Main.txtNoTableOfFigures": "Nie znaleziono żadnych pozycji na liście ilustracji.", "DE.Controllers.Main.txtNoText": "Błąd! Brak tekstu o określonym stylu w dokumencie.", "DE.Controllers.Main.txtNotInTable": "<PERSON>e ma w tabeli", "DE.Controllers.Main.txtNotValidBookmark": "Błąd! Nieprawidłowe odniesienie do zakładki.", "DE.Controllers.Main.txtOddPage": "<PERSON><PERSON><PERSON><PERSON><PERSON> strona", "DE.Controllers.Main.txtOnPage": "na stronie", "DE.Controllers.Main.txtRectangles": "Prostokąty", "DE.Controllers.Main.txtSameAsPrev": "<PERSON><PERSON> sam jak poprzednio", "DE.Controllers.Main.txtSection": "-<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtSeries": "Serie", "DE.Controllers.Main.txtShape_accentBorderCallout1": "Objaśnienie: linia z obramowaniem i paskiem wyróżniającym", "DE.Controllers.Main.txtShape_accentBorderCallout2": "Objaśnienie: wygięta linia z obramowaniem i paskiem wyróżniającym", "DE.Controllers.Main.txtShape_accentBorderCallout3": "Objaśnienie: podwójna wygięta linia z obramowaniem i paskiem wyróżniającym", "DE.Controllers.Main.txtShape_accentCallout1": "Objaśnienie: linia z paskiem wyróżniającym", "DE.Controllers.Main.txtShape_accentCallout2": "Objaśnienie: wygięta linia z paskiem wyróżniającym", "DE.Controllers.Main.txtShape_accentCallout3": "Objaśnienie: podwójna wygięta linia z paskiem wyróżniającym", "DE.Controllers.Main.txtShape_actionButtonBackPrevious": "Przycisk powrotu", "DE.Controllers.Main.txtShape_actionButtonBeginning": "Przycisk rozpoczęcia", "DE.Controllers.Main.txtShape_actionButtonBlank": "Pusty przycisk", "DE.Controllers.Main.txtShape_actionButtonDocument": "Przycisk dokument", "DE.Controllers.Main.txtShape_actionButtonEnd": "Przycisk zakończenia ", "DE.Controllers.Main.txtShape_actionButtonForwardNext": "Przycisk następny", "DE.Controllers.Main.txtShape_actionButtonHelp": "Przycisk pomocy", "DE.Controllers.Main.txtShape_actionButtonHome": "Przycisk dom", "DE.Controllers.Main.txtShape_actionButtonInformation": "Przycisk informacji", "DE.Controllers.Main.txtShape_actionButtonMovie": "Przycisk wideo", "DE.Controllers.Main.txtShape_actionButtonReturn": "Przycisk powrotu", "DE.Controllers.Main.txtShape_actionButtonSound": "Przycisk dźwięku", "DE.Controllers.Main.txtShape_arc": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_bentArrow": "Strzałka: wygięta", "DE.Controllers.Main.txtShape_bentConnector5": "Łącznik: łamany", "DE.Controllers.Main.txtShape_bentConnector5WithArrow": "Łącznik: łamany ze strzałką", "DE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Łącznik: łamany z podwójną strzałką", "DE.Controllers.Main.txtShape_bentUpArrow": "Strzałka: wygięta w górę", "DE.Controllers.Main.txtShape_bevel": "Prostokąt: ze skosem", "DE.Controllers.Main.txtShape_blockArc": "<PERSON><PERSON> blokowy", "DE.Controllers.Main.txtShape_borderCallout1": "Objaśnienie: linia", "DE.Controllers.Main.txtShape_borderCallout2": "Objaśnienie: wygięta linia", "DE.Controllers.Main.txtShape_borderCallout3": "Objaśnienie: podwójna wygięta linia", "DE.Controllers.Main.txtShape_bracePair": "Para nawiasów klamrowych", "DE.Controllers.Main.txtShape_callout1": "Objaśnienie: linia bez obramowania", "DE.Controllers.Main.txtShape_callout2": "Objaśnienie: wygięta linia bez obramowania", "DE.Controllers.Main.txtShape_callout3": "Objaśnienie: podwójna wygięta linia bez obramowania", "DE.Controllers.Main.txtShape_can": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_chevron": "Strzałka: pagon", "DE.Controllers.Main.txtShape_chord": "Odcinek koła", "DE.Controllers.Main.txtShape_circularArrow": "Strzałka: kolista", "DE.Controllers.Main.txtShape_cloud": "Chmurka", "DE.Controllers.Main.txtShape_cloudCallout": "Dymek <PERSON>śli: chmurka", "DE.Controllers.Main.txtShape_corner": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_cube": "S<PERSON>ś<PERSON>", "DE.Controllers.Main.txtShape_curvedConnector3": "Łącznik: zakrzywiony", "DE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Łącznik: zakrzywiony ze strzałką", "DE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Łącznik: zakrzywiony z podwójną strzałką", "DE.Controllers.Main.txtShape_curvedDownArrow": "Strzałka: zakrzywiona w dół", "DE.Controllers.Main.txtShape_curvedLeftArrow": "Strzałka: zakrzywiona w lewo", "DE.Controllers.Main.txtShape_curvedRightArrow": "Strzałka: zakrzywiona w prawo", "DE.Controllers.Main.txtShape_curvedUpArrow": "Strzałka: zakrzywiona w górę", "DE.Controllers.Main.txtShape_decagon": "Dziesięciokąt", "DE.Controllers.Main.txtShape_diagStripe": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_diamond": "Romb", "DE.Controllers.Main.txtShape_dodecagon": "Dwunastoką<PERSON>", "DE.Controllers.Main.txtShape_donut": "Okrąg: pusty", "DE.Controllers.Main.txtShape_doubleWave": "Podwójna fala", "DE.Controllers.Main.txtShape_downArrow": "Strzałka: w dół", "DE.Controllers.Main.txtShape_downArrowCallout": "Objaśnienie: strzałka w dół", "DE.Controllers.Main.txtShape_ellipse": "Elipsa", "DE.Controllers.Main.txtShape_ellipseRibbon": "Wstęga: zakrzywiona i nachylona w dół", "DE.Controllers.Main.txtShape_ellipseRibbon2": "Wstęga: zakrzywiona i nachylona w górę", "DE.Controllers.Main.txtShape_flowChartAlternateProcess": "Schemat blokowy: proces alternatywny", "DE.Controllers.Main.txtShape_flowChartCollate": "Schemat blokowy: zestawienie", "DE.Controllers.Main.txtShape_flowChartConnector": "Schemat blokowy: łącznik", "DE.Controllers.Main.txtShape_flowChartDecision": "Schemat blokowy: decyzja", "DE.Controllers.Main.txtShape_flowChartDelay": "Schemat blokowy: opóźnienie", "DE.Controllers.Main.txtShape_flowChartDisplay": "Schemat blokowy: ekran", "DE.Controllers.Main.txtShape_flowChartDocument": "Schemat blokowy: dokument", "DE.Controllers.Main.txtShape_flowChartExtract": "Schemat blokowy: wyodrębnianie", "DE.Controllers.Main.txtShape_flowChartInputOutput": "Schemat blokowy: decyzja", "DE.Controllers.Main.txtShape_flowChartInternalStorage": "Schemat blokowy: p<PERSON><PERSON><PERSON> wewnętrzna", "DE.Controllers.Main.txtShape_flowChartMagneticDisk": "Schemat blokowy: dysk magnetyczny", "DE.Controllers.Main.txtShape_flowChartMagneticDrum": "Schemat blokowy: pamięć o dostępie bezpośrednim", "DE.Controllers.Main.txtShape_flowChartMagneticTape": "Schemat blokowy: pamięć o dostępie sekwencyjnym", "DE.Controllers.Main.txtShape_flowChartManualInput": "Schemat blokowy: ręczne wprowadzenie danych", "DE.Controllers.Main.txtShape_flowChartManualOperation": "Schemat blokowy: operacja ręczna", "DE.Controllers.Main.txtShape_flowChartMerge": "Schemat blokowy: scalanie", "DE.Controllers.Main.txtShape_flowChartMultidocument": "Schemat blokowy: wiele dokumentów", "DE.Controllers.Main.txtShape_flowChartOffpageConnector": "Schemat blokowy: łącznik międzystronicowy", "DE.Controllers.Main.txtShape_flowChartOnlineStorage": "Schemat blokowy: przechowywane dane", "DE.Controllers.Main.txtShape_flowChartOr": "Schemat blokowy: lub", "DE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Schemat blokowy: proces uprzednio zdefiniowany", "DE.Controllers.Main.txtShape_flowChartPreparation": "Schemat blokowy: przygotowanie", "DE.Controllers.Main.txtShape_flowChartProcess": "Schemat blokowy: proces", "DE.Controllers.Main.txtShape_flowChartPunchedCard": "Schemat blokowy: karta", "DE.Controllers.Main.txtShape_flowChartPunchedTape": "Schemat blokowy: taśma dziurkowana", "DE.Controllers.Main.txtShape_flowChartSort": "Schemat blokowy: sortowanie", "DE.Controllers.Main.txtShape_flowChartSummingJunction": "Schemat blokowy: operacja sumowania", "DE.Controllers.Main.txtShape_flowChartTerminator": "<PERSON>hemat blokowy: terminator", "DE.Controllers.Main.txtShape_foldedCorner": "Prostokąt: zagięty narożnik", "DE.Controllers.Main.txtShape_frame": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_halfFrame": "Połowa ramki", "DE.Controllers.Main.txtShape_heart": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_heptagon": "Siedmiokąt", "DE.Controllers.Main.txtShape_hexagon": "Sześciokąt", "DE.Controllers.Main.txtShape_homePlate": "Pięcioką<PERSON>", "DE.Controllers.Main.txtShape_horizontalScroll": "Zwój: poziomy", "DE.Controllers.Main.txtShape_irregularSeal1": "Eksplozja 1", "DE.Controllers.Main.txtShape_irregularSeal2": "Eksplozja 2", "DE.Controllers.Main.txtShape_leftArrow": "Lewa strzałka", "DE.Controllers.Main.txtShape_leftArrowCallout": "Objaśnienie: strzałka w lewo", "DE.Controllers.Main.txtShape_leftBrace": "<PERSON><PERSON><PERSON> otwierający", "DE.Controllers.Main.txtShape_leftBracket": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_leftRightArrow": "Strzałka: w lewo i w prawo", "DE.Controllers.Main.txtShape_leftRightArrowCallout": "Objaśnienie: strzałka w lewo i w prawo", "DE.Controllers.Main.txtShape_leftRightUpArrow": "Strzałka: w lewo wprawo i w górę", "DE.Controllers.Main.txtShape_leftUpArrow": "Strzałka: w lewo i w górę", "DE.Controllers.Main.txtShape_lightningBolt": "Błyskawica", "DE.Controllers.Main.txtShape_line": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_lineWithArrow": "Strzałka", "DE.Controllers.Main.txtShape_lineWithTwoArrows": "Strzałka liniowa: podwójna", "DE.Controllers.Main.txtShape_mathDivide": "Znak dzielenia", "DE.Controllers.Main.txtShape_mathEqual": "Równy", "DE.Controllers.Main.txtShape_mathMinus": "Znak minus", "DE.Controllers.Main.txtShape_mathMultiply": "Znak mnożenia", "DE.Controllers.Main.txtShape_mathNotEqual": "Różny od", "DE.Controllers.Main.txtShape_mathPlus": "Plus", "DE.Controllers.Main.txtShape_moon": "Księżyc", "DE.Controllers.Main.txtShape_noSmoking": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_notchedRightArrow": "Strzałka: w prawo z wcięciem", "DE.Controllers.Main.txtShape_octagon": "Ośmiokąt", "DE.Controllers.Main.txtShape_parallelogram": "Równoległobok", "DE.Controllers.Main.txtShape_pentagon": "Pięcioką<PERSON>", "DE.Controllers.Main.txtShape_pie": "Kołowy", "DE.Controllers.Main.txtShape_plaque": "Plakietka", "DE.Controllers.Main.txtShape_plus": "Plus", "DE.Controllers.Main.txtShape_polyline1": "Dowolny kształt: bazgroły", "DE.Controllers.Main.txtShape_polyline2": "Dowolny kształt: kształt", "DE.Controllers.Main.txtShape_quadArrow": "Strzałka: w cztery strony", "DE.Controllers.Main.txtShape_quadArrowCallout": "Objaśnienie: strzałka w cztery strony", "DE.Controllers.Main.txtShape_rect": "Prostokąt", "DE.Controllers.Main.txtShape_ribbon": "Wstęga: nachylona w dół", "DE.Controllers.Main.txtShape_ribbon2": "Wstęga: nachylona w górę", "DE.Controllers.Main.txtShape_rightArrow": "Strzałka: w prawo", "DE.Controllers.Main.txtShape_rightArrowCallout": "Objaśnienie: strzałka w prawo", "DE.Controllers.Main.txtShape_rightBrace": "<PERSON><PERSON><PERSON>rowy zamykający", "DE.Controllers.Main.txtShape_rightBracket": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_round1Rect": "Prostokąt: jeden zaokrąglony róg", "DE.Controllers.Main.txtShape_round2DiagRect": "Prostokąt: zaokrąglone rogi po przekątnej", "DE.Controllers.Main.txtShape_round2SameRect": "Prostokąt: zaokrąglone rogi u góry", "DE.Controllers.Main.txtShape_roundRect": "Prostokąt: zaokrąglone rogi", "DE.Controllers.Main.txtShape_rtTriangle": "Trójkąt równoramienny", "DE.Controllers.Main.txtShape_smileyFace": "Uśmiechnię<PERSON> buźka", "DE.Controllers.Main.txtShape_snip1Rect": "Prostokąt: jeden <PERSON> róg ", "DE.Controllers.Main.txtShape_snip2DiagRect": "Prostokąt: ścięte rogi po przekątnej", "DE.Controllers.Main.txtShape_snip2SameRect": "Prostokąt: ścięte rogi u góry", "DE.Controllers.Main.txtShape_snipRoundRect": "Prostokąt: zaokrąglony róg i ścięty róg u góry", "DE.Controllers.Main.txtShape_spline": "Krzywa", "DE.Controllers.Main.txtShape_star10": "Gwiazda 10-<PERSON><PERSON>na", "DE.Controllers.Main.txtShape_star12": "Gwiazda 12-<PERSON><PERSON>na", "DE.Controllers.Main.txtShape_star16": "Gwiazda 16-<PERSON><PERSON>na", "DE.Controllers.Main.txtShape_star24": "Gwiazda 24-<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_star32": "Gwiazda 32-<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_star4": "Gwiazda 4-ramienna", "DE.Controllers.Main.txtShape_star5": "Gwiazda 5-ramienna", "DE.Controllers.Main.txtShape_star6": "Gwiazda 6-<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_star7": "Gwiazda 7-<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_star8": "Gwiazda 8-<PERSON><PERSON>na", "DE.Controllers.Main.txtShape_stripedRightArrow": "Strzałka: prążkowana w prawo", "DE.Controllers.Main.txtShape_sun": "Sł<PERSON>cz<PERSON>", "DE.Controllers.Main.txtShape_teardrop": "Ł<PERSON>", "DE.Controllers.Main.txtShape_textRect": "Pole tekstowe", "DE.Controllers.Main.txtShape_trapezoid": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_triangle": "Trójkąt równoramienny", "DE.Controllers.Main.txtShape_upArrow": "Strzałka: w górę", "DE.Controllers.Main.txtShape_upArrowCallout": "Objaśnienie: strzałka w górę", "DE.Controllers.Main.txtShape_upDownArrow": "Strzałka: w górę i w dół", "DE.Controllers.Main.txtShape_uturnArrow": "Strzałka: zawracanie", "DE.Controllers.Main.txtShape_verticalScroll": "Zwój: pionowy", "DE.Controllers.Main.txtShape_wave": "Fala", "DE.Controllers.Main.txtShape_wedgeEllipseCallout": "Dymek mowy: owalny", "DE.Controllers.Main.txtShape_wedgeRectCallout": "Dymek mowy: prostokąt", "DE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Dymek mowy: prostokąt z zaokrąglonymi rogami", "DE.Controllers.Main.txtStarsRibbons": "Gwiazdy i wstążki", "DE.Controllers.Main.txtStyle_Caption": "Podpis", "DE.Controllers.Main.txtStyle_endnote_text": "Tekst przypisu końcowego", "DE.Controllers.Main.txtStyle_footnote_text": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtStyle_Heading_1": "Nagłówek 1", "DE.Controllers.Main.txtStyle_Heading_2": "Nagłówek 2", "DE.Controllers.Main.txtStyle_Heading_3": "Nagłówek 3", "DE.Controllers.Main.txtStyle_Heading_4": "Nagłówek 4", "DE.Controllers.Main.txtStyle_Heading_5": "Nagłówek 5", "DE.Controllers.Main.txtStyle_Heading_6": "Nagłówek 6", "DE.Controllers.Main.txtStyle_Heading_7": "Nagłówek 7", "DE.Controllers.Main.txtStyle_Heading_8": "Nagłówek 8", "DE.Controllers.Main.txtStyle_Heading_9": "Nagłówek 9", "DE.Controllers.Main.txtStyle_Intense_Quote": "Intensywny cytat", "DE.Controllers.Main.txtStyle_List_Paragraph": "Lista akapitów", "DE.Controllers.Main.txtStyle_No_Spacing": "Brak przerw", "DE.Controllers.Main.txtStyle_Normal": "Normalny", "DE.Controllers.Main.txtStyle_Quote": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtStyle_Subtitle": "Podtytuł", "DE.Controllers.Main.txtStyle_Title": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtSyntaxError": "Błąd składni", "DE.Controllers.Main.txtTableInd": "Indeks tabeli nie może by<PERSON>wy", "DE.Controllers.Main.txtTableOfContents": "<PERSON><PERSON> tre<PERSON>", "DE.Controllers.Main.txtTableOfFigures": "<PERSON>pis ilust<PERSON>", "DE.Controllers.Main.txtTOCHeading": "Tytuł spisu treści", "DE.Controllers.Main.txtTooLarge": "Numer jest za duży do sformatowania", "DE.Controllers.Main.txtTypeEquation": "Wpisz tutaj równanie.", "DE.Controllers.Main.txtUndefBookmark": "Niezdefiniowana zakładka", "DE.Controllers.Main.txtXAxis": "Oś X", "DE.Controllers.Main.txtYAxis": "<PERSON><PERSON>", "DE.Controllers.Main.txtZeroDivide": "Dzielenie przez zero", "DE.Controllers.Main.unknownErrorText": "Nieznany błąd.", "DE.Controllers.Main.unsupportedBrowserErrorText": "Twoja przeglądarka nie jest wspierana.", "DE.Controllers.Main.uploadDocExtMessage": "Nieznany format dokumentu.", "DE.Controllers.Main.uploadDocFileCountMessage": "Nie przesłano żadnych dokumentów.", "DE.Controllers.Main.uploadDocSizeMessage": "Przekroczono maksymalny rozmiar dokumentu.", "DE.Controllers.Main.uploadImageExtMessage": "Nieznany format obrazu.", "DE.Controllers.Main.uploadImageFileCountMessage": "<PERSON><PERSON>.", "DE.Controllers.Main.uploadImageSizeMessage": "Obraz jest za duży. Maksymalny rozmiar to 25 MB.", "DE.Controllers.Main.uploadImageTextText": "Wysyłanie obrazu...", "DE.Controllers.Main.uploadImageTitleText": "Wysyłanie obrazu", "DE.Controllers.Main.waitText": "<PERSON><PERSON><PERSON> cze<PERSON>...", "DE.Controllers.Main.warnBrowserIE9": "Aplikacja ma małe możliwości w IE9. Użyj przeglądarki IE10 lub nowszej.", "DE.Controllers.Main.warnBrowserZoom": "Aktualne ustawienie powiększenia przeglądarki nie jest w pełni obsługiwane. Zresetuj domyślny zoom, naciskając Ctrl + 0.", "DE.Controllers.Main.warnLicenseExceeded": "Ta wersja edytorów ONLYOFFICE ma pewne ograniczenia dla użytkowników.Dokument zostanie otwarty tylko do odczytu.<b><PERSON><PERSON><PERSON> potrzebujesz więcej, roz<PERSON>ż zakupienie licencji komercyjnej.", "DE.Controllers.Main.warnLicenseExp": "Twoja licencja wygasła.<br>Zaktualizuj licencję i odśwież stronę.", "DE.Controllers.Main.warnLicenseLimitedNoAccess": "Licencja wygasła.<br><PERSON><PERSON> <PERSON>sz <PERSON>tę<PERSON> do edycji dokumentu.<br><PERSON><PERSON><PERSON> skontaktować się ze swoim administratorem.", "DE.Controllers.Main.warnLicenseLimitedRenewed": "Licencja musi zostać odnowiona.<br><PERSON><PERSON> dostęp do edycji dokumentu.<br>Skontaktuj się ze swoim administratorem, aby uzyskać pełny dostęp.", "DE.Controllers.Main.warnLicenseUsersExceeded": "Osiągnąłeś limit dla użytkownia. Skontaktuj się z <PERSON>em, aby dowiedzieć się więcej.", "DE.Controllers.Main.warnNoLicense": "Osiągnięto limit jednoczesnych połączeń z edytorami %1. Ten dokument zostanie otwarty tylko do przeglądania.<br>Skontaktuj się z zespołem sprzedaży %1 w celu uzyskania osobistych warunków uaktualnienia.", "DE.Controllers.Main.warnNoLicenseUsers": "Osiągnąłeś limit dla użytkownika. Skontaktuj się z zespołem sprzedaży %1 w celu uzyskania osobistych warunków aktualizacji.", "DE.Controllers.Main.warnProcessRightsChange": "<PERSON>e masz prawa edytować tego pliku.", "DE.Controllers.Navigation.txtBeginning": "Początek dokumentu", "DE.Controllers.Navigation.txtGotoBeginning": "Przejdź na początek dokumentu", "DE.Controllers.Search.notcriticalErrorTitle": "Ostrzeżenie", "DE.Controllers.Search.textNoTextFound": "<PERSON><PERSON> znale<PERSON>, k<PERSON><PERSON><PERSON><PERSON> s<PERSON>sz. <PERSON><PERSON><PERSON> dos<PERSON>uj opcje wyszukiwania.", "DE.Controllers.Search.textReplaceSkipped": "Zastąpiono. {0} zdarzenia zostały pominięte.", "DE.Controllers.Statusbar.textHasChanges": "Nowe zmiany zostały wyśledzone", "DE.Controllers.Statusbar.textSetTrackChanges": "Je<PERSON>ś w trybie śledzenia zmian", "DE.Controllers.Statusbar.textTrackChanges": "Dokument jest otwarty przy włączonym trybie śledzenia zmian", "DE.Controllers.Statusbar.tipReview": "Śledzenie zmian", "DE.Controllers.Statusbar.zoomText": "Powięks<PERSON><PERSON> {0}%", "DE.Controllers.Toolbar.confirmAddFontName": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON><PERSON> z<PERSON>, nie jest dostępna na bieżącym urządzeniu.<br>Styl tekstu zostanie wyświetlony przy użyciu jednej z czcionek systemowych, a zapisana czcionka będzie używana, jeśli będzie dostępna.<br><PERSON><PERSON> ch<PERSON> kont<PERSON>uo<PERSON>?", "DE.Controllers.Toolbar.dataUrl": "W<PERSON>j adres URL danych", "DE.Controllers.Toolbar.notcriticalErrorTitle": "Ostrzeżenie", "DE.Controllers.Toolbar.textAccent": "Akcenty", "DE.Controllers.Toolbar.textBracket": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.textEmptyImgUrl": "<PERSON><PERSON><PERSON> podać adres URL obrazu.", "DE.Controllers.Toolbar.textEmptyMMergeUrl": "<PERSON><PERSON><PERSON>ć adres URL.", "DE.Controllers.Toolbar.textFontSizeErr": "Wp<PERSON><PERSON><PERSON><PERSON> wartość jest nieprawidłowa.<br>W<PERSON><PERSON><PERSON><PERSON> wartość numeryczną w zakresie od 1 do 300", "DE.Controllers.Toolbar.textFraction": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.textFunction": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.textGroup": "Grupa", "DE.Controllers.Toolbar.textInsert": "Wstaw", "DE.Controllers.Toolbar.textIntegral": "Całki", "DE.Controllers.Toolbar.textLargeOperator": "Duże operatory", "DE.Controllers.Toolbar.textLimitAndLog": "Limity i algorytmy", "DE.Controllers.Toolbar.textMatrix": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.textOperator": "Operatory", "DE.Controllers.Toolbar.textRadical": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.textRecentlyUsed": "Ostatnio używane", "DE.Controllers.Toolbar.textScript": "Pisma", "DE.Controllers.Toolbar.textSymbols": "Symbole", "DE.Controllers.Toolbar.textTabForms": "Formularze", "DE.Controllers.Toolbar.textWarning": "Ostrzeżenie", "DE.Controllers.Toolbar.txtAccent_Accent": "Ostry", "DE.Controllers.Toolbar.txtAccent_ArrowD": "Strzałka w lewo - w prawo powyżej", "DE.Controllers.Toolbar.txtAccent_ArrowL": "Lewa strzałka powyżej", "DE.Controllers.Toolbar.txtAccent_ArrowR": "Strzałka w prawo powyżej", "DE.Controllers.Toolbar.txtAccent_Bar": "Słupkowy", "DE.Controllers.Toolbar.txtAccent_BarBot": " Kreska na dole", "DE.Controllers.Toolbar.txtAccent_BarTop": "Kreska od góry", "DE.Controllers.Toolbar.txtAccent_BorderBox": "Formuła w ramce (z wypełnieniem)", "DE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Forma zamknięta (przykład)", "DE.Controllers.Toolbar.txtAccent_Check": "Sprawdź", "DE.Controllers.Toolbar.txtAccent_CurveBracketBot": "<PERSON><PERSON><PERSON> k<PERSON> na dole", "DE.Controllers.Toolbar.txtAccent_CurveBracketTop": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtAccent_Custom_1": "Wektor A", "DE.Controllers.Toolbar.txtAccent_Custom_2": "ABC Z Nadwymiarem", "DE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y z cechą góry", "DE.Controllers.Toolbar.txtAccent_DDDot": "Wielokropek", "DE.Controllers.Toolbar.txtAccent_DDot": "Podwójna kropka", "DE.Controllers.Toolbar.txtAccent_Dot": "Kropka", "DE.Controllers.Toolbar.txtAccent_DoubleBar": "Podwójna kreska od góry", "DE.Controllers.Toolbar.txtAccent_Grave": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtAccent_GroupBot": "Grupowanie znaków poniżej", "DE.Controllers.Toolbar.txtAccent_GroupTop": "Grupowanie znaków powyżej", "DE.Controllers.Toolbar.txtAccent_HarpoonL": "Ha<PERSON>un w lewo do góry", "DE.Controllers.Toolbar.txtAccent_HarpoonR": "Prawy Harpun Powyżej", "DE.Controllers.Toolbar.txtAccent_Hat": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtAccent_Smile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtAccent_Tilde": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Angle": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Klamry z separatorami", "DE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Klamry z separatorami", "DE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Pojedyncza klamra", "DE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Pojedyncza klamra", "DE.Controllers.Toolbar.txtBracket_Curve": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Klamry z separatorami", "DE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Pojedyncza klamra", "DE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Pojedyncza klamra", "DE.Controllers.Toolbar.txtBracket_Custom_1": "<PERSON><PERSON><PERSON><PERSON><PERSON> (dwa warunki)", "DE.Controllers.Toolbar.txtBracket_Custom_2": "<PERSON><PERSON><PERSON><PERSON><PERSON> (trzy warunki)", "DE.Controllers.Toolbar.txtBracket_Custom_3": "Obiekt stosu", "DE.Controllers.Toolbar.txtBracket_Custom_4": "Obiekt stosu", "DE.Controllers.Toolbar.txtBracket_Custom_5": "Przykłady przypadków", "DE.Controllers.Toolbar.txtBracket_Custom_6": "Współczynnik dwumianowy", "DE.Controllers.Toolbar.txtBracket_Custom_7": "Współczynnik dwumianowy", "DE.Controllers.Toolbar.txtBracket_Line": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Pojedyncza klamra", "DE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Pojedyncza klamra", "DE.Controllers.Toolbar.txtBracket_LineDouble": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Pojedyncza klamra", "DE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Pojedyncza klamra", "DE.Controllers.Toolbar.txtBracket_LowLim": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Pojedyncza klamra", "DE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Pojedyncza klamra", "DE.Controllers.Toolbar.txtBracket_Round": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Klamry z separatorami", "DE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Pojedyncza klamra", "DE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Pojedyńcza klamra", "DE.Controllers.Toolbar.txtBracket_Square": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Square_CloseClose": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Pojedyncza klamra", "DE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Pojedyncza klamra", "DE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_SquareDouble": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Pojedyncza klamra", "DE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Pojedyncza klamra", "DE.Controllers.Toolbar.txtBracket_UppLim": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Pojedyncza klamra", "DE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Pojedyncza klamra", "DE.Controllers.Toolbar.txtFractionDiagonal": "Skrzywiona frakcja", "DE.Controllers.Toolbar.txtFractionDifferential_1": "Zróżnicowany", "DE.Controllers.Toolbar.txtFractionDifferential_2": "Zróżnicowany", "DE.Controllers.Toolbar.txtFractionDifferential_3": "Zróżnicowany", "DE.Controllers.Toolbar.txtFractionDifferential_4": "Zróżnicowany", "DE.Controllers.Toolbar.txtFractionHorizontal": "Ułamek liniowy", "DE.Controllers.Toolbar.txtFractionPi_2": "Pi ponad 2", "DE.Controllers.Toolbar.txtFractionSmall": "Mała frakcja", "DE.Controllers.Toolbar.txtFractionVertical": "Sku<PERSON>lowany fragment", "DE.Controllers.Toolbar.txtFunction_1_Cos": "Funkcja Inverse Cosine", "DE.Controllers.Toolbar.txtFunction_1_Cosh": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtFunction_1_Cot": "Funkcja Inverse Cotangent", "DE.Controllers.Toolbar.txtFunction_1_Coth": "Hiper<PERSON><PERSON>na <PERSON> Inverse Cotangent", "DE.Controllers.Toolbar.txtFunction_1_Csc": "Funkcja Odwrotna Cosecans", "DE.Controllers.Toolbar.txtFunction_1_Csch": "Hiperboliczny arcus cosecans", "DE.Controllers.Toolbar.txtFunction_1_Sec": "<PERSON>d<PERSON><PERSON><PERSON>na", "DE.Controllers.Toolbar.txtFunction_1_Sech": "Hiperboliczna odwrotna funkcja se<PERSON>na", "DE.Controllers.Toolbar.txtFunction_1_Sin": "Funkcja Inverse Sine", "DE.Controllers.Toolbar.txtFunction_1_Sinh": "Funkcja odwrotnej sinusoidy hiperbolicznej", "DE.Controllers.Toolbar.txtFunction_1_Tan": "<PERSON><PERSON><PERSON><PERSON><PERSON> arcus tangens", "DE.Controllers.Toolbar.txtFunction_1_Tanh": "<PERSON><PERSON>ja hiperboliczna odwrotna", "DE.Controllers.Toolbar.txtFunction_Cos": "<PERSON><PERSON><PERSON> cosinus", "DE.Controllers.Toolbar.txtFunction_Cosh": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtFunction_Cot": "Funkcja ctg", "DE.Controllers.Toolbar.txtFunction_Coth": "Cotangens hiperboliczny", "DE.Controllers.Toolbar.txtFunction_Csc": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtFunction_Csch": "Funkcja hipotonii hiperbola", "DE.Controllers.Toolbar.txtFunction_Custom_1": "Sinus theta", "DE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "DE.Controllers.Toolbar.txtFunction_Custom_3": "Wzór styczny", "DE.Controllers.Toolbar.txtFunction_Sec": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtFunction_Sech": "<PERSON>er<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtFunction_Sin": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtFunction_Sinh": "<PERSON><PERSON>ja hiperboliczna <PERSON>", "DE.Controllers.Toolbar.txtFunction_Tan": "Tangens", "DE.Controllers.Toolbar.txtFunction_Tanh": "Tangens hiperboliczny", "DE.Controllers.Toolbar.txtIntegral": "Całka", "DE.Controllers.Toolbar.txtIntegral_dtheta": "Mechanizm różnicowy", "DE.Controllers.Toolbar.txtIntegral_dx": "Mechanizm różnicowy x", "DE.Controllers.Toolbar.txtIntegral_dy": "Mechanizm różnicowy y", "DE.Controllers.Toolbar.txtIntegralCenterSubSup": "Całka", "DE.Controllers.Toolbar.txtIntegralDouble": "Całka podwójna", "DE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Całka podwójna", "DE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Całka podwójna", "DE.Controllers.Toolbar.txtIntegralOriented": "Całka krzywej", "DE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Całka krzywej", "DE.Controllers.Toolbar.txtIntegralOrientedDouble": "Integral powierzchni", "DE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Integral powierzchni", "DE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Integral powierzchni", "DE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Całka krzywej", "DE.Controllers.Toolbar.txtIntegralOrientedTriple": "Integralna objętość", "DE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Integralna objętość", "DE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Integralna objętość", "DE.Controllers.Toolbar.txtIntegralSubSup": "Całka", "DE.Controllers.Toolbar.txtIntegralTriple": "Potr<PERSON>jny integral", "DE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Potr<PERSON>jny integral", "DE.Controllers.Toolbar.txtIntegralTripleSubSup": "Potr<PERSON>jny integral", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Zak<PERSON><PERSON>j", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Zak<PERSON><PERSON>j", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Zak<PERSON><PERSON>j", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Zak<PERSON><PERSON>j", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Zak<PERSON><PERSON>j", "DE.Controllers.Toolbar.txtLargeOperator_CoProd": "Współprodukt", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Współprodukt", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Współprodukt", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Współprodukt", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Współprodukt", "DE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Podsumowanie", "DE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Podsumowanie", "DE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Podsumowanie", "DE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Produkt", "DE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Unia", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Intersection": "Przecię<PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Przecię<PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Przecię<PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Przecię<PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Przecię<PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Prod": "Produkt", "DE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Produkt", "DE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Produkt", "DE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Produkt", "DE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Produkt", "DE.Controllers.Toolbar.txtLargeOperator_Sum": "Podsumowanie", "DE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Podsumowanie", "DE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Podsumowanie", "DE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Podsumowanie", "DE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Podsumowanie", "DE.Controllers.Toolbar.txtLargeOperator_Union": "Unia", "DE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Unia", "DE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Unia", "DE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Unia", "DE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Unia", "DE.Controllers.Toolbar.txtLimitLog_Custom_1": "Przykładowy limit", "DE.Controllers.Toolbar.txtLimitLog_Custom_2": "Maksymalny przykład", "DE.Controllers.Toolbar.txtLimitLog_Lim": "Limit", "DE.Controllers.Toolbar.txtLimitLog_Ln": "Logarytm naturalny", "DE.Controllers.Toolbar.txtLimitLog_Log": "Logarytm", "DE.Controllers.Toolbar.txtLimitLog_LogBase": "Logarytm", "DE.Controllers.Toolbar.txtLimitLog_Max": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLimitLog_Min": "Minimum", "DE.Controllers.Toolbar.txtMarginsH": "Górne i dolne marginesy są za wysokie dla danej wysokości strony", "DE.Controllers.Toolbar.txtMarginsW": "Lewe i prawe marginesy są zbyt szerokie dla danej szerokości strony", "DE.Controllers.Toolbar.txtMatrix_1_2": "1x2 Pusta macierz", "DE.Controllers.Toolbar.txtMatrix_1_3": "1x3 Pusta mac<PERSON>z", "DE.Controllers.Toolbar.txtMatrix_2_1": "2x1 Pusta mac<PERSON>z", "DE.Controllers.Toolbar.txtMatrix_2_2": "2x2 Pusta macierz", "DE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "Pusta macierz z nawiasami", "DE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Pusta macierz z nawiasami", "DE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "Pusta macierz z nawiasami", "DE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "Pusta macierz z nawiasami", "DE.Controllers.Toolbar.txtMatrix_2_3": "2x3 Pusta mac<PERSON>z", "DE.Controllers.Toolbar.txtMatrix_3_1": "3x1 Pusta macierz", "DE.Controllers.Toolbar.txtMatrix_3_2": "3x2 Pusta macierz", "DE.Controllers.Toolbar.txtMatrix_3_3": "3x3 Pusta mac<PERSON>z", "DE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Kropki bazowe", "DE.Controllers.Toolbar.txtMatrix_Dots_Center": "<PERSON><PERSON> pomo<PERSON>", "DE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Punkty po przekątnej", "DE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtMatrix_Flat_Round": "Rozrzedzona matryca", "DE.Controllers.Toolbar.txtMatrix_Flat_Square": "Rozrzedzona matryca", "DE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2 Macierz jednostkowa", "DE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "3x3 Macierz jednostkowa", "DE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3 Macierz jednostkowa", "DE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 Macierz jednostkowa", "DE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Strzałka w lewo - w prawo powyżej", "DE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Strzałka w lewo - w prawo powyżej", "DE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Lewa strzałka poniżej", "DE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Lewa strzałka powyżej", "DE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Strzałka w prawo powyżej", "DE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Strzałka w prawo powyżej", "DE.Controllers.Toolbar.txtOperator_ColonEquals": "Dwukropek jest równy", "DE.Controllers.Toolbar.txtOperator_Custom_1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtOperator_Custom_2": "Delta wyjścia", "DE.Controllers.Toolbar.txtOperator_Definition": "<PERSON><PERSON><PERSON> def<PERSON>", "DE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta równa się", "DE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Strzałka w lewo - w prawo powyżej", "DE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Strzałka w lewo - w prawo powyżej", "DE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Lewa strzałka poniżej", "DE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Lewa strzałka powyżej", "DE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Strzałka w prawo Poniżej", "DE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Strzałka w prawo powyżej", "DE.Controllers.Toolbar.txtOperator_EqualsEquals": "Równy równy", "DE.Controllers.Toolbar.txtOperator_MinusEquals": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtOperator_PlusEquals": "Plus Równy", "DE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "Zmierzone przez", "DE.Controllers.Toolbar.txtRadicalCustom_1": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtRadicalCustom_2": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtRadicalRoot_2": "Pierwiastek drugiego <PERSON>", "DE.Controllers.Toolbar.txtRadicalRoot_3": "Pierwiastek sześcienny", "DE.Controllers.Toolbar.txtRadicalRoot_n": "Pierwiastek ze stopniem", "DE.Controllers.Toolbar.txtRadicalSqrt": "Pierwiastek kwadratowy", "DE.Controllers.Toolbar.txtScriptCustom_1": "Pi<PERSON>", "DE.Controllers.Toolbar.txtScriptCustom_2": "Pi<PERSON>", "DE.Controllers.Toolbar.txtScriptCustom_3": "Pi<PERSON>", "DE.Controllers.Toolbar.txtScriptCustom_4": "Pi<PERSON>", "DE.Controllers.Toolbar.txtScriptSub": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtScriptSubSup": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtScriptSubSupLeft": "Le<PERSON> indeks dolny", "DE.Controllers.Toolbar.txtScriptSup": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_about": "W przybliżeniu", "DE.Controllers.Toolbar.txtSymbol_additional": "Uzupełnienie", "DE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "DE.Controllers.Toolbar.txtSymbol_alpha": "Alfa", "DE.Controllers.Toolbar.txtSymbol_approx": "Prawie równe do", "DE.Controllers.Toolbar.txtSymbol_ast": "Operator <PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_beta": "Beta", "DE.Controllers.Toolbar.txtSymbol_beth": "Bet", "DE.Controllers.Toolbar.txtSymbol_bullet": "Symbol kuli", "DE.Controllers.Toolbar.txtSymbol_cap": "Przecię<PERSON>", "DE.Controllers.Toolbar.txtSymbol_cbrt": "Pierwiastek sześcienny", "DE.Controllers.Toolbar.txtSymbol_cdots": "Poziome kropki w środku", "DE.Controllers.Toolbar.txtSymbol_celsius": "<PERSON><PERSON>lcjusza", "DE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "DE.Controllers.Toolbar.txtSymbol_cong": "W przybliżeniu równe", "DE.Controllers.Toolbar.txtSymbol_cup": "Unia", "DE.Controllers.Toolbar.txtSymbol_ddots": "Przekątny wielokropek w dół w prawo", "DE.Controllers.Toolbar.txtSymbol_degree": "<PERSON>nie", "DE.Controllers.Toolbar.txtSymbol_delta": "Delta", "DE.Controllers.Toolbar.txtSymbol_div": "Znak dzielenia", "DE.Controllers.Toolbar.txtSymbol_downarrow": "Strzałka w dół", "DE.Controllers.Toolbar.txtSymbol_emptyset": "<PERSON><PERSON><PERSON> zestaw", "DE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "DE.Controllers.Toolbar.txtSymbol_equals": "Równy", "DE.Controllers.Toolbar.txtSymbol_equiv": "Identyczny do", "DE.Controllers.Toolbar.txtSymbol_eta": "Eta", "DE.Controllers.Toolbar.txtSymbol_exists": "<PERSON> jest", "DE.Controllers.Toolbar.txtSymbol_factorial": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_fahrenheit": "Stopnie Fahrenheit", "DE.Controllers.Toolbar.txtSymbol_forall": "<PERSON><PERSON> wszystkich", "DE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "DE.Controllers.Toolbar.txtSymbol_geq": "<PERSON><PERSON><PERSON><PERSON><PERSON> lub równy niż", "DE.Controllers.Toolbar.txtSymbol_gg": "Dużo wię<PERSON>zy niż", "DE.Controllers.Toolbar.txtSymbol_greater": "<PERSON><PERSON><PERSON><PERSON><PERSON>ż", "DE.Controllers.Toolbar.txtSymbol_in": "Element", "DE.Controllers.Toolbar.txtSymbol_inc": "Przyrost", "DE.Controllers.Toolbar.txtSymbol_infinity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_iota": "odr<PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "DE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "DE.Controllers.Toolbar.txtSymbol_leftarrow": "Lewa strzałka", "DE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Lewa-Prawa strzałka", "DE.Controllers.Toolbar.txtSymbol_leq": "Mniejszy lub równy niż", "DE.Controllers.Toolbar.txtSymbol_less": "Mniejszy niż", "DE.Controllers.Toolbar.txtSymbol_ll": "Dużo mniejszy niż", "DE.Controllers.Toolbar.txtSymbol_minus": "Minus", "DE.Controllers.Toolbar.txtSymbol_mp": "Minus Plus", "DE.Controllers.Toolbar.txtSymbol_mu": "Mu", "DE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "DE.Controllers.Toolbar.txtSymbol_neq": "Różny od", "DE.Controllers.Toolbar.txtSymbol_ni": "Zawiera jako c<PERSON>k", "DE.Controllers.Toolbar.txtSymbol_not": "Znak negacji", "DE.Controllers.Toolbar.txtSymbol_notexists": "Tam nie ma", "DE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "DE.Controllers.Toolbar.txtSymbol_o": "Omicron", "DE.Controllers.Toolbar.txtSymbol_omega": "Omega", "DE.Controllers.Toolbar.txtSymbol_partial": "Częściowe różnice", "DE.Controllers.Toolbar.txtSymbol_percent": "Procentowo", "DE.Controllers.Toolbar.txtSymbol_phi": "Fi", "DE.Controllers.Toolbar.txtSymbol_pi": "Pi", "DE.Controllers.Toolbar.txtSymbol_plus": "Plus", "DE.Controllers.Toolbar.txtSymbol_pm": "Plus minus", "DE.Controllers.Toolbar.txtSymbol_propto": "Proporcjonalny do", "DE.Controllers.Toolbar.txtSymbol_psi": "Psi", "DE.Controllers.Toolbar.txtSymbol_qdrt": "Pierwiastek poczwórny", "DE.Controllers.Toolbar.txtSymbol_qed": "Koniec dowodu", "DE.Controllers.Toolbar.txtSymbol_rddots": "Przekątny wielokropek w górę w prawo", "DE.Controllers.Toolbar.txtSymbol_rho": "Rho", "DE.Controllers.Toolbar.txtSymbol_rightarrow": "Strzałka w prawo", "DE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "DE.Controllers.Toolbar.txtSymbol_sqrt": "Znak <PERSON>", "DE.Controllers.Toolbar.txtSymbol_tau": "Tau", "DE.Controllers.Toolbar.txtSymbol_therefore": "W związku z tym", "DE.Controllers.Toolbar.txtSymbol_theta": "Theta", "DE.Controllers.Toolbar.txtSymbol_times": "Znak mnożenia", "DE.Controllers.Toolbar.txtSymbol_uparrow": "Strzałka w górę", "DE.Controllers.Toolbar.txtSymbol_upsilon": "Ypsilon", "DE.Controllers.Toolbar.txtSymbol_varepsilon": "Epsilon (opcja)", "DE.Controllers.Toolbar.txtSymbol_varphi": "Phi <PERSON>", "DE.Controllers.Toolbar.txtSymbol_varpi": "Pi <PERSON>", "DE.Controllers.Toolbar.txtSymbol_varrho": "Wariant Rho", "DE.Controllers.Toolbar.txtSymbol_varsigma": "Wariacja Sigma", "DE.Controllers.Toolbar.txtSymbol_vartheta": "Wariant Theta", "DE.Controllers.Toolbar.txtSymbol_vdots": "Pionowe wyskakiwanie", "DE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "DE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "DE.Controllers.Viewport.textFitPage": "Dopasuj do strony", "DE.Controllers.Viewport.textFitWidth": "Dopasuj do szerokości", "DE.Controllers.Viewport.txtDarkMode": "Ciemny motyw", "DE.Views.AddNewCaptionLabelDialog.textLabel": "Etykieta:", "DE.Views.AddNewCaptionLabelDialog.textLabelError": "Etykieta nie może być pusta.", "DE.Views.BookmarksDialog.textAdd": "<PERSON><PERSON><PERSON>", "DE.Views.BookmarksDialog.textBookmarkName": "Nazwa zakładki", "DE.Views.BookmarksDialog.textClose": "Zamknij", "DE.Views.BookmarksDialog.textCopy": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.BookmarksDialog.textDelete": "Usuń", "DE.Views.BookmarksDialog.textGetLink": "Uzyskać link", "DE.Views.BookmarksDialog.textGoto": "Przejdź", "DE.Views.BookmarksDialog.textHidden": "Ukryte zakładki", "DE.Views.BookmarksDialog.textLocation": "Lokalizacja", "DE.Views.BookmarksDialog.textName": "Nazwa", "DE.Views.BookmarksDialog.textSort": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.BookmarksDialog.textTitle": "Zakład<PERSON>", "DE.Views.BookmarksDialog.txtInvalidName": "Nazwa zakładki powinna zaczynać się literą i zawierać może ona tylko litery, liczby i znaki podkreślenia ", "DE.Views.CaptionDialog.textAdd": "<PERSON><PERSON><PERSON>", "DE.Views.CaptionDialog.textAfter": "po", "DE.Views.CaptionDialog.textBefore": "<PERSON><PERSON><PERSON>", "DE.Views.CaptionDialog.textCaption": "Podpis", "DE.Views.CaptionDialog.textChapter": "Rozpocznij rozdział stylem", "DE.Views.CaptionDialog.textChapterInc": "Dołącz numer rozdziału", "DE.Views.CaptionDialog.textColon": "dwukropek", "DE.Views.CaptionDialog.textDash": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.CaptionDialog.textDelete": "<PERSON><PERSON><PERSON>yk<PERSON>", "DE.Views.CaptionDialog.textEquation": "Równanie", "DE.Views.CaptionDialog.textExamples": "Przykłady: Tabela 2-A, Rysunek IV-1", "DE.Views.CaptionDialog.textExclude": "Wyklucz etykietę z podpisu", "DE.Views.CaptionDialog.textFigure": "Rysunek", "DE.Views.CaptionDialog.textHyphen": "łącznik", "DE.Views.CaptionDialog.textInsert": "Wstaw", "DE.Views.CaptionDialog.textLabel": "Etykieta", "DE.Views.CaptionDialog.textLongDash": "myślnik", "DE.Views.CaptionDialog.textNumbering": "Format", "DE.Views.CaptionDialog.textPeriod": "kropka", "DE.Views.CaptionDialog.textSeparator": "Użyj separatora", "DE.Views.CaptionDialog.textTable": "<PERSON><PERSON><PERSON>", "DE.Views.CaptionDialog.textTitle": "Wstaw podpis", "DE.Views.CellsAddDialog.textCol": "<PERSON><PERSON><PERSON>", "DE.Views.CellsAddDialog.textDown": "Pod kursorem", "DE.Views.CellsAddDialog.textLeft": "<PERSON>", "DE.Views.CellsAddDialog.textRight": "Do prawej", "DE.Views.CellsAddDialog.textRow": "<PERSON><PERSON><PERSON>", "DE.Views.CellsAddDialog.textTitle": "Wstaw kilka", "DE.Views.CellsAddDialog.textUp": "<PERSON><PERSON> k<PERSON>", "DE.Views.ChartSettings.textAdvanced": "Po<PERSON>ż ustawienia zaawansowane", "DE.Views.ChartSettings.textChartType": "Zmień typ wykresu", "DE.Views.ChartSettings.textEditData": "<PERSON><PERSON><PERSON><PERSON> dane", "DE.Views.ChartSettings.textHeight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.ChartSettings.textOriginalSize": "Rzeczywisty rozmiar", "DE.Views.ChartSettings.textSize": "Rozmiar", "DE.Views.ChartSettings.textStyle": "<PERSON><PERSON>", "DE.Views.ChartSettings.textUndock": "Odepnij od panelu", "DE.Views.ChartSettings.textWidth": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.ChartSettings.textWrap": "Styl zawijania", "DE.Views.ChartSettings.txtBehind": "<PERSON>a", "DE.Views.ChartSettings.txtInFront": "Z przodu", "DE.Views.ChartSettings.txtInline": "W tekście", "DE.Views.ChartSettings.txtSquare": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.ChartSettings.txtThrough": "<PERSON><PERSON><PERSON>", "DE.Views.ChartSettings.txtTight": "szczelnie", "DE.Views.ChartSettings.txtTitle": "<PERSON><PERSON><PERSON>", "DE.Views.ChartSettings.txtTopAndBottom": "Góra i dół", "DE.Views.ControlSettingsDialog.strGeneral": "Ogólne", "DE.Views.ControlSettingsDialog.textAdd": "<PERSON><PERSON><PERSON>", "DE.Views.ControlSettingsDialog.textAppearance": "Wygląd", "DE.Views.ControlSettingsDialog.textApplyAll": "Zast<PERSON><PERSON><PERSON>", "DE.Views.ControlSettingsDialog.textBox": "Z ograniczającą ramką", "DE.Views.ControlSettingsDialog.textChange": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ControlSettingsDialog.textCheckbox": "Pole wyboru", "DE.Views.ControlSettingsDialog.textChecked": "Symbol zaznaczenia", "DE.Views.ControlSettingsDialog.textColor": "<PERSON><PERSON>", "DE.Views.ControlSettingsDialog.textCombobox": "Pole kombi", "DE.Views.ControlSettingsDialog.textDate": "Format daty", "DE.Views.ControlSettingsDialog.textDelete": "Usuń", "DE.Views.ControlSettingsDialog.textDisplayName": "<PERSON><PERSON>ś<PERSON><PERSON><PERSON> na<PERSON>wa", "DE.Views.ControlSettingsDialog.textDown": "<PERSON> dół", "DE.Views.ControlSettingsDialog.textDropDown": "Lista rozwijana", "DE.Views.ControlSettingsDialog.textFormat": "Wyświetl datę w ten sposób", "DE.Views.ControlSettingsDialog.textLang": "Język", "DE.Views.ControlSettingsDialog.textLock": "Blokowanie", "DE.Views.ControlSettingsDialog.textName": "<PERSON><PERSON><PERSON>", "DE.Views.ControlSettingsDialog.textNone": "Brak", "DE.Views.ControlSettingsDialog.textPlaceholder": "Symbol zastępczy", "DE.Views.ControlSettingsDialog.textShowAs": "Po<PERSON>ż jako", "DE.Views.ControlSettingsDialog.textSystemColor": "Systemowy", "DE.Views.ControlSettingsDialog.textTag": "Tag", "DE.Views.ControlSettingsDialog.textTitle": "Ustawienia kontroli treści", "DE.Views.ControlSettingsDialog.textUnchecked": "Symbol odznaczenia", "DE.Views.ControlSettingsDialog.textUp": "W górę", "DE.Views.ControlSettingsDialog.textValue": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ControlSettingsDialog.tipChange": "Zmień symbol", "DE.Views.ControlSettingsDialog.txtLockDelete": "<PERSON><PERSON> można usun<PERSON> kontroli treści", "DE.Views.ControlSettingsDialog.txtLockEdit": "<PERSON><PERSON><PERSON><PERSON> nie może być edytowana", "DE.Views.CrossReferenceDialog.textAboveBelow": "'wyżej/\"niżej'", "DE.Views.CrossReferenceDialog.textBookmark": "Zakładka", "DE.Views.CrossReferenceDialog.textBookmarkText": "Tekst zakładki", "DE.Views.CrossReferenceDialog.textCaption": "Cały podpis", "DE.Views.CrossReferenceDialog.textEmpty": "Odwołanie do żądania jest puste.", "DE.Views.CrossReferenceDialog.textEndnote": "Przypis <PERSON>", "DE.Views.CrossReferenceDialog.textEndNoteNum": "Numer przypisu końcowego", "DE.Views.CrossReferenceDialog.textEndNoteNumForm": "Numer przypisu końcowego (sformatowany)", "DE.Views.CrossReferenceDialog.textEquation": "Równanie", "DE.Views.CrossReferenceDialog.textFigure": "Rysunek", "DE.Views.CrossReferenceDialog.textFootnote": "Przypis dolny", "DE.Views.CrossReferenceDialog.textHeading": "Nagłówek", "DE.Views.CrossReferenceDialog.textHeadingNum": "Poziom nagłówka", "DE.Views.CrossReferenceDialog.textHeadingNumFull": "Poziom nagłówka (pełen kontekst)", "DE.Views.CrossReferenceDialog.textHeadingNumNo": "Poziom nagłówka (bez kontekstu)", "DE.Views.CrossReferenceDialog.textHeadingText": "Tekst nagłówka", "DE.Views.CrossReferenceDialog.textIncludeAbove": "Dołącz wyraz \"powy<PERSON>ej\" lub \"poniżej\"", "DE.Views.CrossReferenceDialog.textInsert": "Wstaw", "DE.Views.CrossReferenceDialog.textInsertAs": "Wstaw jako <PERSON>", "DE.Views.CrossReferenceDialog.textLabelNum": "Tylko etykieta i numer", "DE.Views.CrossReferenceDialog.textNoteNum": "Numer przypisu dolnego", "DE.Views.CrossReferenceDialog.textNoteNumForm": "Numer przypisu dolnego (sformatowany)", "DE.Views.CrossReferenceDialog.textOnlyCaption": "Tylko tekst podpisu", "DE.Views.CrossReferenceDialog.textPageNum": "Numer strony", "DE.Views.CrossReferenceDialog.textParagraph": "Element numerowany", "DE.Views.CrossReferenceDialog.textParaNum": "<PERSON><PERSON><PERSON>", "DE.Views.CrossReferenceDialog.textParaNumFull": "<PERSON><PERSON><PERSON> (pełen kontekst)", "DE.Views.CrossReferenceDialog.textParaNumNo": "<PERSON><PERSON><PERSON> (bez kontekstu)", "DE.Views.CrossReferenceDialog.textSeparate": "Oddziel liczby znakiem", "DE.Views.CrossReferenceDialog.textTable": "<PERSON><PERSON><PERSON>", "DE.Views.CrossReferenceDialog.textText": "Tekst akapitu", "DE.Views.CrossReferenceDialog.textWhich": "Dla którego podpisu", "DE.Views.CrossReferenceDialog.textWhichBookmark": "Dla której zakładki", "DE.Views.CrossReferenceDialog.textWhichEndnote": "Dla którego przypisu końcowego", "DE.Views.CrossReferenceDialog.textWhichHeading": "Dla którego nagłówka", "DE.Views.CrossReferenceDialog.textWhichNote": "Dla którego przypisu", "DE.Views.CrossReferenceDialog.textWhichPara": "Dla którego z numerowanych elementów", "DE.Views.CrossReferenceDialog.txtReference": "Wstaw odsyłacz do", "DE.Views.CrossReferenceDialog.txtTitle": "Odsyłacz", "DE.Views.CrossReferenceDialog.txtType": "Typ odsyłacza", "DE.Views.CustomColumnsDialog.textColumns": "<PERSON><PERSON><PERSON><PERSON> kolumn", "DE.Views.CustomColumnsDialog.textSeparator": "Podział kolumn", "DE.Views.CustomColumnsDialog.textSpacing": "Przerwa między kolumnami", "DE.Views.CustomColumnsDialog.textTitle": "<PERSON><PERSON><PERSON>", "DE.Views.DateTimeDialog.confirmDefault": "Ustaw domyślny format dla {0}: \"{1}\"", "DE.Views.DateTimeDialog.textDefault": "Ustaw jako <PERSON>", "DE.Views.DateTimeDialog.textFormat": "Dostę<PERSON><PERSON> formaty", "DE.Views.DateTimeDialog.textLang": "Język", "DE.Views.DateTimeDialog.textUpdate": "Aktualizuj automatycznie", "DE.Views.DateTimeDialog.txtTitle": "Data i czas", "DE.Views.DocProtection.hintProtectDoc": "Chroń dokument", "DE.Views.DocProtection.txtProtectDoc": "Chroń dokument", "DE.Views.DocumentHolder.aboveText": "Powyżej", "DE.Views.DocumentHolder.addCommentText": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.advancedDropCapText": "Inicjały Ustawienia", "DE.Views.DocumentHolder.advancedFrameText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> us<PERSON>", "DE.Views.DocumentHolder.advancedParagraphText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> us<PERSON> akapitu", "DE.Views.DocumentHolder.advancedTableText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> us<PERSON> tabeli", "DE.Views.DocumentHolder.advancedText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.alignmentText": "Wyrównanie", "DE.Views.DocumentHolder.belowText": "Poniżej", "DE.Views.DocumentHolder.breakBeforeText": "Przerwanie strony przed", "DE.Views.DocumentHolder.bulletsText": "Punktory i numeracja", "DE.Views.DocumentHolder.cellAlignText": "Wyrównanie pionowe komórki", "DE.Views.DocumentHolder.cellText": "Komórka", "DE.Views.DocumentHolder.centerText": "Środek", "DE.Views.DocumentHolder.chartText": "<PERSON><PERSON><PERSON><PERSON><PERSON>e ustawienia wykresu", "DE.Views.DocumentHolder.columnText": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.deleteColumnText": "<PERSON><PERSON>ń kolumnę", "DE.Views.DocumentHolder.deleteRowText": "<PERSON><PERSON><PERSON> wiersz", "DE.Views.DocumentHolder.deleteTableText": "<PERSON><PERSON><PERSON> ta<PERSON>", "DE.Views.DocumentHolder.deleteText": "Usuń", "DE.Views.DocumentHolder.direct270Text": "Obróć tekst w górę", "DE.Views.DocumentHolder.direct90Text": "Obróć tekst w dół", "DE.Views.DocumentHolder.directHText": "Poziomy", "DE.Views.DocumentHolder.directionText": "Kierunek tekstu", "DE.Views.DocumentHolder.editChartText": "<PERSON><PERSON><PERSON><PERSON> dane", "DE.Views.DocumentHolder.editFooterText": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.editHeaderText": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.editHyperlinkText": "Edytuj link", "DE.Views.DocumentHolder.guestText": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.hyperlinkText": "Link", "DE.Views.DocumentHolder.ignoreAllSpellText": "Ignoruj wszystko", "DE.Views.DocumentHolder.ignoreSpellText": "Ignoruj", "DE.Views.DocumentHolder.imageText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> us<PERSON> obrazu", "DE.Views.DocumentHolder.insertColumnLeftText": "<PERSON><PERSON><PERSON> lewa", "DE.Views.DocumentHolder.insertColumnRightText": "<PERSON><PERSON><PERSON> p<PERSON>a", "DE.Views.DocumentHolder.insertColumnText": "Wstaw kolumnę", "DE.Views.DocumentHolder.insertRowAboveText": "Wiersz powyżej", "DE.Views.DocumentHolder.insertRowBelowText": "Wiersz poniżej", "DE.Views.DocumentHolder.insertRowText": "Wstaw wiersz", "DE.Views.DocumentHolder.insertText": "Wstaw", "DE.Views.DocumentHolder.keepLinesText": "<PERSON><PERSON><PERSON>aj wiersze razem", "DE.Views.DocumentHolder.langText": "<PERSON><PERSON>bierz język", "DE.Views.DocumentHolder.leftText": "<PERSON><PERSON>", "DE.Views.DocumentHolder.loadSpellText": "Ładowanie wariantów...", "DE.Views.DocumentHolder.mergeCellsText": "Scal komórki", "DE.Views.DocumentHolder.moreText": "<PERSON><PERSON><PERSON><PERSON>j wariantów...", "DE.Views.DocumentHolder.noSpellVariantsText": "Brak wariantów", "DE.Views.DocumentHolder.notcriticalErrorTitle": "Ostrzeżenie", "DE.Views.DocumentHolder.originalSizeText": "Rzeczywisty rozmiar", "DE.Views.DocumentHolder.paragraphText": "Aka<PERSON>", "DE.Views.DocumentHolder.removeHyperlinkText": "Usuń link", "DE.Views.DocumentHolder.rightText": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.rowText": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.saveStyleText": "Utwórz nowy styl", "DE.Views.DocumentHolder.selectCellText": "Wybierz komórkę", "DE.Views.DocumentHolder.selectColumnText": "<PERSON><PERSON><PERSON><PERSON> kol<PERSON>", "DE.Views.DocumentHolder.selectRowText": "<PERSON><PERSON><PERSON><PERSON> w<PERSON>z", "DE.Views.DocumentHolder.selectTableText": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.selectText": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.shapeText": "Zaawansowane ustawienia kształtu", "DE.Views.DocumentHolder.spellcheckText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>i", "DE.Views.DocumentHolder.splitCellsText": "Podziel komórkę...", "DE.Views.DocumentHolder.splitCellTitleText": "Podziel komórkę", "DE.Views.DocumentHolder.strDelete": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.strDetails": "Szczegóły sygnatury", "DE.Views.DocumentHolder.strSetup": "Konfiguracja podpisu", "DE.Views.DocumentHolder.strSign": "Podpisz", "DE.Views.DocumentHolder.styleText": "Formatowanie jako <PERSON>", "DE.Views.DocumentHolder.tableText": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textAccept": "Zatwierdź zmianę", "DE.Views.DocumentHolder.textAlign": "Wyrównaj", "DE.Views.DocumentHolder.textArrange": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textArrangeBack": "Wyślij do tła", "DE.Views.DocumentHolder.textArrangeBackward": "Przenieś do tyłu", "DE.Views.DocumentHolder.textArrangeForward": "Przenieś do przodu", "DE.Views.DocumentHolder.textArrangeFront": "Przejdź na pierwszy plan", "DE.Views.DocumentHolder.textCells": "Komórki", "DE.Views.DocumentHolder.textCol": "Usuń całą kolumnę", "DE.Views.DocumentHolder.textContentControls": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textContinueNumbering": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> numerowanie", "DE.Views.DocumentHolder.textCopy": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textCrop": "Przytnij", "DE.Views.DocumentHolder.textCropFill": "Wypełnij", "DE.Views.DocumentHolder.textCropFit": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textCut": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textDistributeCols": "Rozłóż kolumny", "DE.Views.DocumentHolder.textDistributeRows": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textEditControls": "Ustawienia kontroli treści", "DE.Views.DocumentHolder.textEditWrapBoundary": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>", "DE.Views.DocumentHolder.textFlipH": "Odwróć w poziomie", "DE.Views.DocumentHolder.textFlipV": "Odwróć w pionie", "DE.Views.DocumentHolder.textFollow": "Śledź ruch", "DE.Views.DocumentHolder.textFromFile": "Z pliku", "DE.Views.DocumentHolder.textFromStorage": "Z magazynu", "DE.Views.DocumentHolder.textFromUrl": "Z adresu URL", "DE.Views.DocumentHolder.textJoinList": "Dołącz do poprzedniej listy", "DE.Views.DocumentHolder.textLeft": "Przesuń komórki w lewo", "DE.Views.DocumentHolder.textNest": "Wstaw jako zagnieżdżoną tabelę", "DE.Views.DocumentHolder.textNextPage": "<PERSON><PERSON><PERSON><PERSON><PERSON> strona", "DE.Views.DocumentHolder.textNumberingValue": "Początkowa wartość", "DE.Views.DocumentHolder.textPaste": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textPrevPage": "Poprzednia strona", "DE.Views.DocumentHolder.textRefreshField": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pole", "DE.Views.DocumentHolder.textReject": "Cofnij <PERSON>ę", "DE.Views.DocumentHolder.textRemCheckBox": "<PERSON><PERSON><PERSON> pole wyboru", "DE.Views.DocumentHolder.textRemComboBox": "<PERSON><PERSON><PERSON> pole kombi", "DE.Views.DocumentHolder.textRemDropdown": "Usuń listę rozwijaną", "DE.Views.DocumentHolder.textRemField": "<PERSON><PERSON><PERSON> pole tekstowe", "DE.Views.DocumentHolder.textRemove": "Usuń", "DE.Views.DocumentHolder.textRemoveControl": "Usuń kontrolę treści", "DE.Views.DocumentHolder.textRemPicture": "<PERSON><PERSON><PERSON> obraz", "DE.Views.DocumentHolder.textRemRadioBox": "Usuń przełącznik", "DE.Views.DocumentHolder.textReplace": "Zamień obraz", "DE.Views.DocumentHolder.textRotate": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textRotate270": "<PERSON><PERSON><PERSON><PERSON><PERSON> w lewo o 90° ", "DE.Views.DocumentHolder.textRotate90": "Obróć w prawo o 90°", "DE.Views.DocumentHolder.textRow": "Usuń cały wiersz", "DE.Views.DocumentHolder.textSeparateList": "Oddzielna lista ", "DE.Views.DocumentHolder.textSettings": "Ustawienia", "DE.Views.DocumentHolder.textSeveral": "<PERSON><PERSON>a w<PERSON>/kolumn ", "DE.Views.DocumentHolder.textShapeAlignBottom": "Wyrównaj do dołu", "DE.Views.DocumentHolder.textShapeAlignCenter": "Wyrównaj do środka", "DE.Views.DocumentHolder.textShapeAlignLeft": "Wyrównaj do lewej", "DE.Views.DocumentHolder.textShapeAlignMiddle": "Wyrównaj do środka", "DE.Views.DocumentHolder.textShapeAlignRight": "Wyrównaj do prawej", "DE.Views.DocumentHolder.textShapeAlignTop": "Wyrównaj do góry", "DE.Views.DocumentHolder.textStartNewList": "Rozpocznij nową listę", "DE.Views.DocumentHolder.textStartNumberingFrom": "Ustaw wartość numerowania", "DE.Views.DocumentHolder.textTitleCellsRemove": "Usuń komórki", "DE.Views.DocumentHolder.textTOC": "<PERSON><PERSON> tre<PERSON>", "DE.Views.DocumentHolder.textTOCSettings": "Ustawienia tabeli zawartości", "DE.Views.DocumentHolder.textUndo": "Cof<PERSON>j", "DE.Views.DocumentHolder.textUpdateAll": "Odśwież całą tabelę", "DE.Views.DocumentHolder.textUpdatePages": "Odśwież wyłącznie numery stron", "DE.Views.DocumentHolder.textUpdateTOC": "Od<PERSON><PERSON><PERSON>ż tabelę zawart<PERSON>", "DE.Views.DocumentHolder.textWrap": "Styl zawijania", "DE.Views.DocumentHolder.tipIsLocked": "Ten element jest obecnie edytowany przez innego użytkownika.", "DE.Views.DocumentHolder.toDictionaryText": "Dodaj do słownika", "DE.Views.DocumentHolder.txtAddBottom": "Dodaj dolną krawędź", "DE.Views.DocumentHolder.txtAddFractionBar": "<PERSON><PERSON> pasek ułamka", "DE.Views.DocumentHolder.txtAddHor": "Dodaj poziomą linie", "DE.Views.DocumentHolder.txtAddLB": "<PERSON><PERSON><PERSON> lewy dolny wiersz", "DE.Views.DocumentHolder.txtAddLeft": "<PERSON><PERSON><PERSON> k<PERSON>", "DE.Views.DocumentHolder.txtAddLT": "<PERSON><PERSON><PERSON> lewy górny w<PERSON>z", "DE.Views.DocumentHolder.txtAddRight": "Dodaj p<PERSON>ą krawędź", "DE.Views.DocumentHolder.txtAddTop": "Dodaj górną krawędź", "DE.Views.DocumentHolder.txtAddVer": "Dodaj pionowy wiersz", "DE.Views.DocumentHolder.txtAlignToChar": "Wyrównaj do znaku", "DE.Views.DocumentHolder.txtBehind": "<PERSON>a", "DE.Views.DocumentHolder.txtBorderProps": "Ustawienia obra<PERSON>ia", "DE.Views.DocumentHolder.txtBottom": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.txtColumnAlign": "Wyrównanie kolumny", "DE.Views.DocumentHolder.txtDecreaseArg": "Zmniejsz rozmiar argumentu", "DE.Views.DocumentHolder.txtDeleteArg": "<PERSON><PERSON><PERSON> argument", "DE.Views.DocumentHolder.txtDeleteBreak": "Usuń ręczną przerwę", "DE.Views.DocumentHolder.txtDeleteChars": "Usuń zamknięte znaki", "DE.Views.DocumentHolder.txtDeleteCharsAndSeparators": "Usuń zamknięte znaki i separatory", "DE.Views.DocumentHolder.txtDeleteEq": "Usuń równanie", "DE.Views.DocumentHolder.txtDeleteGroupChar": "Usuń znak", "DE.Views.DocumentHolder.txtDeleteRadical": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.txtDistribHor": "Rozdziel poziomo", "DE.Views.DocumentHolder.txtDistribVert": "Rozdziel pionowo", "DE.Views.DocumentHolder.txtEmpty": "(<PERSON><PERSON><PERSON>)", "DE.Views.DocumentHolder.txtFractionLinear": "Zmień na ułamek liniowy", "DE.Views.DocumentHolder.txtFractionSkewed": "Zmienić na ukośny prosty ułamek", "DE.Views.DocumentHolder.txtFractionStacked": "Zmień na ułożone ułamki", "DE.Views.DocumentHolder.txtGroup": "Grupa", "DE.Views.DocumentHolder.txtGroupCharOver": "Znak nad tekstem", "DE.Views.DocumentHolder.txtGroupCharUnder": "Znak pod tekstem", "DE.Views.DocumentHolder.txtHideBottom": "Ukryj dolną krawędź", "DE.Views.DocumentHolder.txtHideBottomLimit": "Ukryj dolny limit", "DE.Views.DocumentHolder.txtHideCloseBracket": "Ukryj uchwyt zamykający", "DE.Views.DocumentHolder.txtHideDegree": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.txtHideHor": "<PERSON><PERSON><PERSON><PERSON> poziomy w<PERSON>z", "DE.Views.DocumentHolder.txtHideLB": "<PERSON><PERSON><PERSON><PERSON> lewy dolny wiersz", "DE.Views.DocumentHolder.txtHideLeft": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>ęd<PERSON>", "DE.Views.DocumentHolder.txtHideLT": "<PERSON><PERSON><PERSON><PERSON> lewy górny wiersz", "DE.Views.DocumentHolder.txtHideOpenBracket": "Ukryj uchwyt otwierający", "DE.Views.DocumentHolder.txtHidePlaceholder": "Ukryj <PERSON> zastępczy", "DE.Views.DocumentHolder.txtHideRight": "Uk<PERSON>j prawą krawędź", "DE.Views.DocumentHolder.txtHideTop": "Ukryj górną krawędź", "DE.Views.DocumentHolder.txtHideTopLimit": "Ukryj górny limit", "DE.Views.DocumentHolder.txtHideVer": "Ukryj pionowy wiersz", "DE.Views.DocumentHolder.txtIncreaseArg": "Zwiększ rozmiar argumentu", "DE.Views.DocumentHolder.txtInFront": "Z przodu", "DE.Views.DocumentHolder.txtInline": "W tekście", "DE.Views.DocumentHolder.txtInsertArgAfter": "Wstaw argument po", "DE.Views.DocumentHolder.txtInsertArgBefore": "Wstaw argument przed", "DE.Views.DocumentHolder.txtInsertBreak": "Wstaw ręczną przerwę", "DE.Views.DocumentHolder.txtInsertCaption": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.txtInsertEqAfter": "Wstaw równanie po", "DE.Views.DocumentHolder.txtInsertEqBefore": "Wstaw równanie przed", "DE.Views.DocumentHolder.txtKeepTextOnly": "Zachowaj tylko tekst", "DE.Views.DocumentHolder.txtLimitChange": "Zmień lokalizację limitu", "DE.Views.DocumentHolder.txtLimitOver": "Ograniczenie tekstu", "DE.Views.DocumentHolder.txtLimitUnder": "Limit w tekście", "DE.Views.DocumentHolder.txtMatchBrackets": "Dopasuj nawiasy do wysokości argumentu", "DE.Views.DocumentHolder.txtMatrixAlign": "Wyrównanie macierzy", "DE.Views.DocumentHolder.txtOverbar": "Pasek nad tekstem", "DE.Views.DocumentHolder.txtOverwriteCells": "Wymień zawartość komórki", "DE.Views.DocumentHolder.txtPasteSourceFormat": "Zachowaj formatowanie źródłowe", "DE.Views.DocumentHolder.txtPressLink": "Naciśnij {0} i kliknij w link", "DE.Views.DocumentHolder.txtPrintSelection": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.txtRemFractionBar": "<PERSON><PERSON><PERSON> belkę ułamka", "DE.Views.DocumentHolder.txtRemLimit": "Usuń limit", "DE.Views.DocumentHolder.txtRemoveAccentChar": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "DE.Views.DocumentHolder.txtRemoveBar": "Us<PERSON>ń pasek", "DE.Views.DocumentHolder.txtRemoveWarning": "<PERSON><PERSON> ch<PERSON> us<PERSON> ten podpis? <br> <PERSON><PERSON> można tego co<PERSON>.", "DE.Views.DocumentHolder.txtRemScripts": "Usuń indeksy", "DE.Views.DocumentHolder.txtRemSubscript": "Usuń indeks dolny", "DE.Views.DocumentHolder.txtRemSuperscript": "Usuń górny indeks", "DE.Views.DocumentHolder.txtScriptsAfter": "Pismo po tekście", "DE.Views.DocumentHolder.txtScriptsBefore": "Pismo przed tekstem", "DE.Views.DocumentHolder.txtShowBottomLimit": "Pokaż dolny limit", "DE.Views.DocumentHolder.txtShowCloseBracket": "Pokaż klamrę zamyk<PERSON>ącą", "DE.Views.DocumentHolder.txtShowDegree": "Pokaż stopień", "DE.Views.DocumentHolder.txtShowOpenBracket": "Pokaż klamrę otwierającą", "DE.Views.DocumentHolder.txtShowPlaceholder": "Pokaż symbol zastępczy", "DE.Views.DocumentHolder.txtShowTopLimit": "Pokaż górny limit", "DE.Views.DocumentHolder.txtSquare": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.txtStretchBrackets": "Uchwyty rozciągające", "DE.Views.DocumentHolder.txtThrough": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.txtTight": "szczelnie", "DE.Views.DocumentHolder.txtTop": "Góra", "DE.Views.DocumentHolder.txtTopAndBottom": "Góra i dół", "DE.Views.DocumentHolder.txtUnderbar": "Pasek pod tekstem", "DE.Views.DocumentHolder.txtUngroup": "Rozgrupuj", "DE.Views.DocumentHolder.txtWarnUrl": "Kliknięcie tego linku może być szkodliwe dla urządzenia i danych.<br><PERSON><PERSON> na pewno chcesz kontynuować?", "DE.Views.DocumentHolder.updateStyleText": "Aktualizuj %1 styl", "DE.Views.DocumentHolder.vertAlignText": "Wyrównaj pionowo", "DE.Views.DropcapSettingsAdvanced.strBorders": "Obramowania i wypełnienie", "DE.Views.DropcapSettingsAdvanced.strDropcap": "Inicjały", "DE.Views.DropcapSettingsAdvanced.strMargins": "Mar<PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textAlign": "Wyrównanie", "DE.Views.DropcapSettingsAdvanced.textAtLeast": "Co najmniej", "DE.Views.DropcapSettingsAdvanced.textAuto": "Automatyczny", "DE.Views.DropcapSettingsAdvanced.textBackColor": "<PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textBorderColor": "<PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textBorderDesc": "Kliknij na diagram lub użyj przycisków, aby wy<PERSON>ć obramowania", "DE.Views.DropcapSettingsAdvanced.textBorderWidth": "Roz<PERSON>r <PERSON>", "DE.Views.DropcapSettingsAdvanced.textBottom": "<PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textCenter": "Środek", "DE.Views.DropcapSettingsAdvanced.textColumn": "<PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textDistance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> od tekstu", "DE.Views.DropcapSettingsAdvanced.textExact": "Dokładnie", "DE.Views.DropcapSettingsAdvanced.textFlow": "Ramka przepływu", "DE.Views.DropcapSettingsAdvanced.textFont": "Czcionka", "DE.Views.DropcapSettingsAdvanced.textFrame": "<PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textHeight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textHorizontal": "Poziomy", "DE.Views.DropcapSettingsAdvanced.textInline": "Ramka liniowa", "DE.Views.DropcapSettingsAdvanced.textInMargin": "W marginesie", "DE.Views.DropcapSettingsAdvanced.textInText": "W tekście", "DE.Views.DropcapSettingsAdvanced.textLeft": "<PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textMargin": "Mar<PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textMove": "Poruszaj się tekstem", "DE.Views.DropcapSettingsAdvanced.textNone": "Żaden", "DE.Views.DropcapSettingsAdvanced.textPage": "Strona", "DE.Views.DropcapSettingsAdvanced.textParagraph": "Aka<PERSON>", "DE.Views.DropcapSettingsAdvanced.textParameters": "Parametry", "DE.Views.DropcapSettingsAdvanced.textPosition": "<PERSON><PERSON><PERSON>ja", "DE.Views.DropcapSettingsAdvanced.textRelative": "Względny do", "DE.Views.DropcapSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textRowHeight": "Wysokość w wierszach", "DE.Views.DropcapSettingsAdvanced.textTitle": "Inicjały - Ustawienia zaawansowane", "DE.Views.DropcapSettingsAdvanced.textTitleFrame": "<PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textTop": "Góra", "DE.Views.DropcapSettingsAdvanced.textVertical": "<PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.tipFontName": "Nazwa czcionki", "DE.Views.DropcapSettingsAdvanced.txtNoBorders": "Bez k<PERSON>ędzi", "DE.Views.EditListItemDialog.textDisplayName": "<PERSON><PERSON>ś<PERSON><PERSON><PERSON> na<PERSON>wa", "DE.Views.EditListItemDialog.textNameError": "Wyświetlana nazwa nie może być pusta.", "DE.Views.EditListItemDialog.textValue": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.EditListItemDialog.textValueError": "Element o tej samej wartości już istnieje.", "DE.Views.FileMenu.btnBackCaption": "Otwórz lokalizację pliku", "DE.Views.FileMenu.btnCloseMenuCaption": "Zamknij menu", "DE.Views.FileMenu.btnCreateNewCaption": "Utwórz nowy", "DE.Views.FileMenu.btnDownloadCaption": "<PERSON><PERSON><PERSON> jako", "DE.Views.FileMenu.btnExitCaption": "Wyjdź", "DE.Views.FileMenu.btnFileOpenCaption": "Otwórz", "DE.Views.FileMenu.btnHelpCaption": "Pomoc", "DE.Views.FileMenu.btnHistoryCaption": "<PERSON> wersji", "DE.Views.FileMenu.btnInfoCaption": "Informacje o dokumencie", "DE.Views.FileMenu.btnPrintCaption": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenu.btnProtectCaption": "Zabezpiecz", "DE.Views.FileMenu.btnRecentFilesCaption": "Otwórz ostatnie", "DE.Views.FileMenu.btnRenameCaption": "Zmień nazwę", "DE.Views.FileMenu.btnReturnCaption": "Powrót do dokumentu", "DE.Views.FileMenu.btnRightsCaption": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenu.btnSaveAsCaption": "<PERSON>ap<PERSON>z jako", "DE.Views.FileMenu.btnSaveCaption": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenu.btnSaveCopyAsCaption": "Zapisz kopię jako", "DE.Views.FileMenu.btnSettingsCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenu.btnToEditCaption": "Edytuj dokument", "DE.Views.FileMenu.textDownload": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.CreateNew.txtBlank": "<PERSON><PERSON>y dokument", "DE.Views.FileMenuPanels.CreateNew.txtCreateNew": "Utwórz nowy", "DE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Zatwierdź", "DE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Dodaj autora", "DE.Views.FileMenuPanels.DocumentInfo.txtAddText": "<PERSON><PERSON>j te<PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Aplikacja", "DE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "Autor", "DE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Zmień prawa dostępu", "DE.Views.FileMenuPanels.DocumentInfo.txtComment": "Komentarz", "DE.Views.FileMenuPanels.DocumentInfo.txtCreated": "Utworzono", "DE.Views.FileMenuPanels.DocumentInfo.txtLoading": "Ładowanie...", "DE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Ostatnio zmodyfikowany przez", "DE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Ostatnio zmodyfikowany", "DE.Views.FileMenuPanels.DocumentInfo.txtNo": "<PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtOwner": "Właściciel", "DE.Views.FileMenuPanels.DocumentInfo.txtPages": "Strony", "DE.Views.FileMenuPanels.DocumentInfo.txtPageSize": "<PERSON><PERSON><PERSON><PERSON> strony", "DE.Views.FileMenuPanels.DocumentInfo.txtParagraphs": "Akapity", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfTagged": "Oznacz PDF", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfVer": "Wersja PDF", "DE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Lokalizacja", "DE.Views.FileMenuPanels.DocumentInfo.txtRights": "<PERSON><PERSON><PERSON>, które mają prawa", "DE.Views.FileMenuPanels.DocumentInfo.txtSpaces": "Znaki ze spacjami", "DE.Views.FileMenuPanels.DocumentInfo.txtStatistics": "Statystyki", "DE.Views.FileMenuPanels.DocumentInfo.txtSubject": "<PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtSymbols": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtTitle": "Nazwa", "DE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Przesłany", "DE.Views.FileMenuPanels.DocumentInfo.txtWords": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtYes": "Tak", "DE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Zmień prawa dostępu", "DE.Views.FileMenuPanels.DocumentRights.txtRights": "<PERSON><PERSON><PERSON>, które mają prawa", "DE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Ostrzeżenie", "DE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "<PERSON>a p<PERSON><PERSON><PERSON> hasła", "DE.Views.FileMenuPanels.ProtectDoc.strProtect": "Chroń dokument", "DE.Views.FileMenuPanels.ProtectDoc.strSignature": "Z s<PERSON>gnaturą", "DE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Edytuj dokument", "DE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "Edycja spowoduje usunięcie podpisów z dokumentu. <br> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>?", "DE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "Ten dokument został zabezpieczony hasłem", "DE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "Ten dokument musi zostać podpisany.", "DE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Do dokumentu dodano prawidłowe podpisy. Dokument jest chroniony przed edycją.", "DE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Niektóre podpisy cyfrowe w dokumencie są nieprawidłowe lub nie można ich zweryfikować. Dokument jest chroniony przed edycją.", "DE.Views.FileMenuPanels.ProtectDoc.txtView": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.okButtonText": "Zatwierdź", "DE.Views.FileMenuPanels.Settings.strCoAuthMode": "Tryb współtworzenia", "DE.Views.FileMenuPanels.Settings.strFast": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.strFontRender": "Podpowiedź czcionki", "DE.Views.FileMenuPanels.Settings.strIgnoreWordsInUPPERCASE": "Ignoruj słowa pisane WIELKIMI LITERAMI", "DE.Views.FileMenuPanels.Settings.strIgnoreWordsWithNumbers": "Ignoruj słowa z liczbami", "DE.Views.FileMenuPanels.Settings.strMacrosSettings": "Ustawienia Makr", "DE.Views.FileMenuPanels.Settings.strPasteButton": "Pokaż przycisk opcji wklejania po wklejeniu zawartości", "DE.Views.FileMenuPanels.Settings.strShowChanges": "Zmiany w czasie rzeczywistym podczas współtworzenia", "DE.Views.FileMenuPanels.Settings.strStrict": "Ścisły", "DE.Views.FileMenuPanels.Settings.strTheme": "Motyw interfejsu", "DE.Views.FileMenuPanels.Settings.strUnit": "Jednostka miary", "DE.Views.FileMenuPanels.Settings.strZoom": "Domyślna wartość powiększenia", "DE.Views.FileMenuPanels.Settings.text10Minutes": "Każde 10 minut", "DE.Views.FileMenuPanels.Settings.text30Minutes": "Każde 30 minut", "DE.Views.FileMenuPanels.Settings.text5Minutes": "Każde 5 minut", "DE.Views.FileMenuPanels.Settings.text60Minutes": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.textAlignGuides": "Prowadnice wyrównania", "DE.Views.FileMenuPanels.Settings.textAutoRecover": "Automatyczne odzyskiwanie", "DE.Views.FileMenuPanels.Settings.textAutoSave": "Automatyczny zapis", "DE.Views.FileMenuPanels.Settings.textDisabled": "Wyłączony", "DE.Views.FileMenuPanels.Settings.textForceSave": "Zapisywanie wersji p<PERSON>", "DE.Views.FileMenuPanels.Settings.textMinute": "Każda minuta", "DE.Views.FileMenuPanels.Settings.textOldVersions": "Zachowaj kompatybilność ze starszymi wersjami programu MS Word podczas zapisywania jako DOCX", "DE.Views.FileMenuPanels.Settings.txtAll": "Pokaż wszystkie", "DE.Views.FileMenuPanels.Settings.txtAutoCorrect": "Opcje Autokorekty...", "DE.Views.FileMenuPanels.Settings.txtCacheMode": "Domyślny tryb pamięci podręcznej", "DE.Views.FileMenuPanels.Settings.txtChangesBalloons": "Pokaż po kliknięciu w objaśnieniach", "DE.Views.FileMenuPanels.Settings.txtChangesTip": "Pokaż po najechaniu na podpowiedzi", "DE.Views.FileMenuPanels.Settings.txtCm": "Centymetr", "DE.Views.FileMenuPanels.Settings.txtCollaboration": "Współpraca", "DE.Views.FileMenuPanels.Settings.txtDarkMode": "Włącz tryb ciemny dla dokumentu", "DE.Views.FileMenuPanels.Settings.txtFitPage": "Dopasuj do strony", "DE.Views.FileMenuPanels.Settings.txtFitWidth": "Dopasuj do szerokości", "DE.Views.FileMenuPanels.Settings.txtInch": "<PERSON>", "DE.Views.FileMenuPanels.Settings.txtLast": "Pokaż ostatni", "DE.Views.FileMenuPanels.Settings.txtMac": "jak OS X", "DE.Views.FileMenuPanels.Settings.txtNative": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.txtNone": "<PERSON>c nie pokazuj", "DE.Views.FileMenuPanels.Settings.txtProofing": "Sprawdzanie", "DE.Views.FileMenuPanels.Settings.txtPt": "<PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.txtRunMacros": "Włącz wszystkie", "DE.Views.FileMenuPanels.Settings.txtRunMacrosDesc": "Włącz wszystkie makra bez powiadomienia", "DE.Views.FileMenuPanels.Settings.txtSpellCheck": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>i", "DE.Views.FileMenuPanels.Settings.txtStopMacros": "Wyłącz wszystkie", "DE.Views.FileMenuPanels.Settings.txtStopMacrosDesc": "Wyłącz wszystkie makra bez powiadomienia", "DE.Views.FileMenuPanels.Settings.txtStrictTip": "Użyj przycisku \"Zapisz\", aby <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, które ty i inni dokonują", "DE.Views.FileMenuPanels.Settings.txtWarnMacros": "Pokaż powiadomienie", "DE.Views.FileMenuPanels.Settings.txtWarnMacrosDesc": "Wyłącz wszystkie makra z powiadomieniem", "DE.Views.FileMenuPanels.Settings.txtWin": "jak <PERSON>", "DE.Views.FileMenuPanels.Settings.txtWorkspace": "<PERSON><PERSON><PERSON> rob<PERSON>", "DE.Views.FormSettings.textAlways": "<PERSON><PERSON><PERSON>", "DE.Views.FormSettings.textAspect": "Zachowaj proporcje", "DE.Views.FormSettings.textAtLeast": "Co najmniej", "DE.Views.FormSettings.textAuto": "Automatyczny", "DE.Views.FormSettings.textAutofit": "Autodopasowanie", "DE.Views.FormSettings.textBackgroundColor": "<PERSON><PERSON>", "DE.Views.FormSettings.textCheckbox": "Pole wyboru", "DE.Views.FormSettings.textColor": "<PERSON><PERSON>", "DE.Views.FormSettings.textComb": "Połącz symbole", "DE.Views.FormSettings.textCombobox": "Pole kombi", "DE.Views.FormSettings.textConnected": "Pola połączone", "DE.Views.FormSettings.textDelete": "Usuń", "DE.Views.FormSettings.textDisconnect": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.FormSettings.textDropDown": "Lista rozwijana", "DE.Views.FormSettings.textExact": "Dokładnie", "DE.Views.FormSettings.textField": "Pole tekstowe", "DE.Views.FormSettings.textFixed": "Pole stałego rozmiaru", "DE.Views.FormSettings.textFromFile": "Z pliku", "DE.Views.FormSettings.textFromStorage": "Z magazynu", "DE.Views.FormSettings.textFromUrl": "Z adresu URL", "DE.Views.FormSettings.textGroupKey": "Klucz grupy", "DE.Views.FormSettings.textImage": "<PERSON><PERSON><PERSON>", "DE.Views.FormSettings.textKey": "<PERSON><PERSON>cz", "DE.Views.FormSettings.textLock": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.FormSettings.textMaxChars": "Limit znaków", "DE.Views.FormSettings.textMulti": "Pole wielowierszowe", "DE.Views.FormSettings.textNever": "nigdy", "DE.Views.FormSettings.textNoBorder": "Brak obramowania", "DE.Views.FormSettings.textPlaceholder": "Symbol zastępczy", "DE.Views.FormSettings.textRadiobox": "Przełącznik", "DE.Views.FormSettings.textRequired": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.FormSettings.textScale": "<PERSON><PERSON><PERSON>", "DE.Views.FormSettings.textSelectImage": "<PERSON><PERSON><PERSON><PERSON> obraz", "DE.Views.FormSettings.textTag": "Tag", "DE.Views.FormSettings.textTip": "Podpowiedź", "DE.Views.FormSettings.textTipAdd": "<PERSON><PERSON><PERSON>", "DE.Views.FormSettings.textTipDelete": "<PERSON><PERSON><PERSON>", "DE.Views.FormSettings.textTipDown": "Przenieś w dół", "DE.Views.FormSettings.textTipUp": "Przenieś do góry", "DE.Views.FormSettings.textTooBig": "<PERSON><PERSON><PERSON> jest za duży", "DE.Views.FormSettings.textTooSmall": "<PERSON><PERSON><PERSON> jest za mały", "DE.Views.FormSettings.textUnlock": "O<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.FormSettings.textValue": "<PERSON><PERSON><PERSON>", "DE.Views.FormSettings.textWidth": "Szerokość komórki", "DE.Views.FormsTab.capBtnCheckBox": "Pole wyboru", "DE.Views.FormsTab.capBtnComboBox": "Pole kombi", "DE.Views.FormsTab.capBtnDropDown": "Lista rozwijana", "DE.Views.FormsTab.capBtnImage": "<PERSON><PERSON><PERSON>", "DE.Views.FormsTab.capBtnNext": "<PERSON><PERSON><PERSON><PERSON><PERSON> pole", "DE.Views.FormsTab.capBtnPrev": "<PERSON><PERSON><PERSON><PERSON> pole", "DE.Views.FormsTab.capBtnRadioBox": "Przełącznik", "DE.Views.FormsTab.capBtnSaveForm": "Zapisz jako <PERSON>rz", "DE.Views.FormsTab.capBtnSubmit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.FormsTab.capBtnText": "Pole tekstowe", "DE.Views.FormsTab.capBtnView": "Zobacz formularz", "DE.Views.FormsTab.textClear": "Wyczyść pola", "DE.Views.FormsTab.textClearFields": "Wyczyść wszystkie pola", "DE.Views.FormsTab.textCreateForm": "Dodaj pola i utwórz dokument OFORM z możliwością wypełnienia", "DE.Views.FormsTab.textGotIt": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.FormsTab.textHighlight": "Ustawienia wyróżniania", "DE.Views.FormsTab.textNoHighlight": "Brak wyróżnienia", "DE.Views.FormsTab.textRequired": "Wypełnij wszystkie wymagane pola, aby wys<PERSON><PERSON> formularz.", "DE.Views.FormsTab.textSubmited": "Formularz przesłany pomyślnie", "DE.Views.FormsTab.tipCheckBox": "Wstaw pole wyboru", "DE.Views.FormsTab.tipComboBox": "Wstaw pole kombi", "DE.Views.FormsTab.tipDropDown": "Wstaw listę rozwijaną", "DE.Views.FormsTab.tipImageField": "Wstaw obraz", "DE.Views.FormsTab.tipNextForm": "Przejdź do następnego pola", "DE.Views.FormsTab.tipPrevForm": "Przejdź do poprzedniego pola", "DE.Views.FormsTab.tipRadioBox": "Wstaw przycisk opcji", "DE.Views.FormsTab.tipSaveForm": "Zapisz plik jako wypełnialny dokument OFORM", "DE.Views.FormsTab.tipSubmit": "Prześlij formularz", "DE.Views.FormsTab.tipTextField": "Wstaw pole tekstowe", "DE.Views.FormsTab.tipViewForm": "Zobacz formularz", "DE.Views.FormsTab.txtUntitled": "Bez Nazwy", "DE.Views.HeaderFooterSettings.textBottomCenter": "Dolny środek", "DE.Views.HeaderFooterSettings.textBottomLeft": "Lewy dolny", "DE.Views.HeaderFooterSettings.textBottomPage": "<PERSON><PERSON><PERSON>", "DE.Views.HeaderFooterSettings.textBottomRight": "<PERSON><PERSON><PERSON> dolny", "DE.Views.HeaderFooterSettings.textDiffFirst": "<PERSON><PERSON> pier<PERSON> strona", "DE.Views.HeaderFooterSettings.textDiffOdd": "Różne dziwne i parzyste strony", "DE.Views.HeaderFooterSettings.textFrom": "Rozpocznij od", "DE.Views.HeaderFooterSettings.textHeaderFromBottom": "Stopka z dołu", "DE.Views.HeaderFooterSettings.textHeaderFromTop": "Nagłówek z góry", "DE.Views.HeaderFooterSettings.textInsertCurrent": "Wstaw do aktualnej pozycji", "DE.Views.HeaderFooterSettings.textOptions": "<PERSON><PERSON><PERSON>", "DE.Views.HeaderFooterSettings.textPageNum": "Wstaw numer strony", "DE.Views.HeaderFooterSettings.textPageNumbering": "Numerowanie Strony", "DE.Views.HeaderFooterSettings.textPosition": "<PERSON><PERSON><PERSON>ja", "DE.Views.HeaderFooterSettings.textPrev": "Kontynuuj z poprzedniej sekcji", "DE.Views.HeaderFooterSettings.textSameAs": "Odnośnik do poprzedniego", "DE.Views.HeaderFooterSettings.textTopCenter": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.HeaderFooterSettings.textTopLeft": "Lewy górny", "DE.Views.HeaderFooterSettings.textTopPage": "<PERSON><PERSON><PERSON> strony", "DE.Views.HeaderFooterSettings.textTopRight": "Prawy górny", "DE.Views.HyperlinkSettingsDialog.textDefault": "Wybrany fragment tekstu", "DE.Views.HyperlinkSettingsDialog.textDisplay": "Po<PERSON><PERSON>", "DE.Views.HyperlinkSettingsDialog.textExternal": "Link zew<PERSON>ętrzny", "DE.Views.HyperlinkSettingsDialog.textInternal": "Miejsce w tym dokumencie", "DE.Views.HyperlinkSettingsDialog.textTitle": "Ustawienia linków", "DE.Views.HyperlinkSettingsDialog.textTooltip": "Tekst wskazówki na ekranie", "DE.Views.HyperlinkSettingsDialog.textUrl": "Link do", "DE.Views.HyperlinkSettingsDialog.txtBeginning": "Początek dokumentu", "DE.Views.HyperlinkSettingsDialog.txtBookmarks": "Zakład<PERSON>", "DE.Views.HyperlinkSettingsDialog.txtEmpty": "To pole jest wymagane", "DE.Views.HyperlinkSettingsDialog.txtHeadings": "Nagłówki", "DE.Views.HyperlinkSettingsDialog.txtNotUrl": "To pole powinno by<PERSON> adresem URL w formacie \"http://www.example.com\"", "DE.Views.HyperlinkSettingsDialog.txtSizeLimit": "To pole jest ogranicz<PERSON> do 2083 znaków", "DE.Views.ImageSettings.textAdvanced": "Po<PERSON>ż ustawienia zaawansowane", "DE.Views.ImageSettings.textCrop": "Przytnij", "DE.Views.ImageSettings.textCropFill": "Wypełnij", "DE.Views.ImageSettings.textCropFit": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ImageSettings.textCropToShape": "Przytnij do kształtu", "DE.Views.ImageSettings.textEdit": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ImageSettings.textEditObject": "Ed<PERSON><PERSON>j obiekt", "DE.Views.ImageSettings.textFitMargins": "Dopasuj do marginesu", "DE.Views.ImageSettings.textFlip": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.ImageSettings.textFromFile": "Z pliku", "DE.Views.ImageSettings.textFromStorage": "Z magazynu", "DE.Views.ImageSettings.textFromUrl": "Z adresu URL", "DE.Views.ImageSettings.textHeight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.ImageSettings.textHint270": "<PERSON><PERSON><PERSON><PERSON><PERSON> w lewo o 90° ", "DE.Views.ImageSettings.textHint90": "Obróć w prawo o 90°", "DE.Views.ImageSettings.textHintFlipH": "Odwróć w poziomie", "DE.Views.ImageSettings.textHintFlipV": "Odwróć w pionie", "DE.Views.ImageSettings.textInsert": "Zamień obraz", "DE.Views.ImageSettings.textOriginalSize": "Rzeczywisty rozmiar", "DE.Views.ImageSettings.textRecentlyUsed": "Ostatnio używane", "DE.Views.ImageSettings.textRotate90": "O<PERSON><PERSON>óć o 90°", "DE.Views.ImageSettings.textRotation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.ImageSettings.textSize": "Rozmiar", "DE.Views.ImageSettings.textWidth": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.ImageSettings.textWrap": "Styl zawijania", "DE.Views.ImageSettings.txtBehind": "<PERSON>a", "DE.Views.ImageSettings.txtInFront": "Z przodu", "DE.Views.ImageSettings.txtInline": "W tekście", "DE.Views.ImageSettings.txtSquare": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.ImageSettings.txtThrough": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettings.txtTight": "szczelnie", "DE.Views.ImageSettings.txtTopAndBottom": "Góra i dół", "DE.Views.ImageSettingsAdvanced.strMargins": "Wypełnienie tekstem", "DE.Views.ImageSettingsAdvanced.textAbsoluteWH": "Bezwzględny", "DE.Views.ImageSettingsAdvanced.textAlignment": "Wyrównanie", "DE.Views.ImageSettingsAdvanced.textAlt": "Tekst alternatywny", "DE.Views.ImageSettingsAdvanced.textAltDescription": "Opis", "DE.Views.ImageSettingsAdvanced.textAltTip": "Alternatywna tekstowa reprezentacja informacji o obiekcie wizualnym, która zostanie odczytana osobom z zaburzeniami wzroku lub poznawczego, aby pomóc im lepiej zrozumieć, jakie informacje zawiera obraz, autoks<PERSON><PERSON><PERSON>t, diagram lub tabela.", "DE.Views.ImageSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textAngle": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textArrows": "Strz<PERSON>ł<PERSON>", "DE.Views.ImageSettingsAdvanced.textAspectRatio": "Zablokuj współczynnik kształtu", "DE.Views.ImageSettingsAdvanced.textAutofit": "Autodopasowanie", "DE.Views.ImageSettingsAdvanced.textBeginSize": "Początkowy rozmiar", "DE.Views.ImageSettingsAdvanced.textBeginStyle": "Styl p<PERSON>z<PERSON>kowy", "DE.Views.ImageSettingsAdvanced.textBelow": "Poniżej", "DE.Views.ImageSettingsAdvanced.textBevel": "U<PERSON>", "DE.Views.ImageSettingsAdvanced.textBottom": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textBottomMargin": "Dolny margines", "DE.Views.ImageSettingsAdvanced.textBtnWrap": "Zawijanie tekstu", "DE.Views.ImageSettingsAdvanced.textCapType": "<PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textCenter": "Środek", "DE.Views.ImageSettingsAdvanced.textCharacter": "Znak", "DE.Views.ImageSettingsAdvanced.textColumn": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textDistance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> od tekstu", "DE.Views.ImageSettingsAdvanced.textEndSize": "Rozmiar k<PERSON>ńcowy", "DE.Views.ImageSettingsAdvanced.textEndStyle": "Styl końcowy", "DE.Views.ImageSettingsAdvanced.textFlat": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textFlipped": "Odwrócony ", "DE.Views.ImageSettingsAdvanced.textHeight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textHorizontal": "Poziomy", "DE.Views.ImageSettingsAdvanced.textHorizontally": "Poziomo ", "DE.Views.ImageSettingsAdvanced.textJoinType": "Dołącz typ", "DE.Views.ImageSettingsAdvanced.textKeepRatio": "Stałe proporcje", "DE.Views.ImageSettingsAdvanced.textLeft": "<PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textLeftMargin": "Lewy margines", "DE.Views.ImageSettingsAdvanced.textLine": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textLineStyle": "<PERSON><PERSON> w<PERSON>", "DE.Views.ImageSettingsAdvanced.textMargin": "Mar<PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textMiter": "prosty", "DE.Views.ImageSettingsAdvanced.textMove": "Przesuń obiekt z tekstem", "DE.Views.ImageSettingsAdvanced.textOptions": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textOriginalSize": "Rzeczywisty rozmiar", "DE.Views.ImageSettingsAdvanced.textOverlap": "<PERSON><PERSON><PERSON><PERSON> nakładk<PERSON>", "DE.Views.ImageSettingsAdvanced.textPage": "Strona", "DE.Views.ImageSettingsAdvanced.textParagraph": "Aka<PERSON>", "DE.Views.ImageSettingsAdvanced.textPosition": "<PERSON><PERSON><PERSON>ja", "DE.Views.ImageSettingsAdvanced.textPositionPc": "Względna pozycja", "DE.Views.ImageSettingsAdvanced.textRelative": "względny do", "DE.Views.ImageSettingsAdvanced.textRelativeWH": "Względny", "DE.Views.ImageSettingsAdvanced.textResizeFit": "Dopasuj rozmiar kształtu do tekstu", "DE.Views.ImageSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textRightMargin": "Prawy margines", "DE.Views.ImageSettingsAdvanced.textRightOf": "na prawo od", "DE.Views.ImageSettingsAdvanced.textRotation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textRound": "Zaokrąglij", "DE.Views.ImageSettingsAdvanced.textShape": "Ustawienia kształtu", "DE.Views.ImageSettingsAdvanced.textSize": "Rozmiar", "DE.Views.ImageSettingsAdvanced.textSquare": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textTextBox": "Pole tekstowe", "DE.Views.ImageSettingsAdvanced.textTitle": "Obraz - <PERSON><PERSON><PERSON><PERSON>wane us<PERSON>wi<PERSON>", "DE.Views.ImageSettingsAdvanced.textTitleChart": "Wykres - <PERSON><PERSON><PERSON><PERSON>wane us<PERSON>wi<PERSON>", "DE.Views.ImageSettingsAdvanced.textTitleShape": "Kształt - Zaawansowane ustawienia", "DE.Views.ImageSettingsAdvanced.textTop": "Góra", "DE.Views.ImageSettingsAdvanced.textTopMargin": "<PERSON><PERSON><PERSON> górny", "DE.Views.ImageSettingsAdvanced.textVertical": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textVertically": "<PERSON><PERSON><PERSON> ", "DE.Views.ImageSettingsAdvanced.textWeightArrows": "Wagi i strzałki", "DE.Views.ImageSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textWrap": "Styl zawijania", "DE.Views.ImageSettingsAdvanced.textWrapBehindTooltip": "<PERSON>a", "DE.Views.ImageSettingsAdvanced.textWrapInFrontTooltip": "Z przodu", "DE.Views.ImageSettingsAdvanced.textWrapInlineTooltip": "W tekście", "DE.Views.ImageSettingsAdvanced.textWrapSquareTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textWrapThroughTooltip": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textWrapTightTooltip": "szczelnie", "DE.Views.ImageSettingsAdvanced.textWrapTopbottomTooltip": "Góra i dół", "DE.Views.LeftMenu.tipAbout": "O programie", "DE.Views.LeftMenu.tipChat": "<PERSON><PERSON><PERSON>", "DE.Views.LeftMenu.tipComments": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.LeftMenu.tipNavigation": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.LeftMenu.tipOutline": "Nagłówki", "DE.Views.LeftMenu.tipPlugins": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.LeftMenu.tipSearch": "Szukaj", "DE.Views.LeftMenu.tipSupport": "Opinie i wsparcie", "DE.Views.LeftMenu.tipTitles": "Tytuły", "DE.Views.LeftMenu.txtDeveloper": "TRYB DEWELOPERA", "DE.Views.LeftMenu.txtEditor": "Edytor <PERSON>", "DE.Views.LeftMenu.txtLimit": "Ograniczony dostęp", "DE.Views.LeftMenu.txtTrial": "PRÓBNY TRYB", "DE.Views.LeftMenu.txtTrialDev": "Próbny tryb programisty", "DE.Views.LineNumbersDialog.textAddLineNumbering": "Dodaj numerac<PERSON> w<PERSON>", "DE.Views.LineNumbersDialog.textApplyTo": "Zastosuj do", "DE.Views.LineNumbersDialog.textContinuous": "Ciągłe", "DE.Views.LineNumbersDialog.textCountBy": "Numeruj co", "DE.Views.LineNumbersDialog.textDocument": "Cały dokument", "DE.Views.LineNumbersDialog.textForward": "Od bieżącego miejsca", "DE.Views.LineNumbersDialog.textFromText": "<PERSON><PERSON>", "DE.Views.LineNumbersDialog.textNumbering": "Numerowanie", "DE.Views.LineNumbersDialog.textRestartEachPage": "Roz<PERSON>cz<PERSON>j nową stronę", "DE.Views.LineNumbersDialog.textRestartEachSection": "Rozpocznij nową sekcję", "DE.Views.LineNumbersDialog.textSection": "Ta sekcja", "DE.Views.LineNumbersDialog.textStartAt": "Rozpocznij od", "DE.Views.LineNumbersDialog.textTitle": "<PERSON>ume<PERSON> w<PERSON>zy", "DE.Views.LineNumbersDialog.txtAutoText": "Automatyczny", "DE.Views.Links.capBtnAddText": "<PERSON><PERSON>j te<PERSON>", "DE.Views.Links.capBtnBookmarks": "Zakładka", "DE.Views.Links.capBtnCaption": "Podpis", "DE.Views.Links.capBtnContentsUpdate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.Links.capBtnCrossRef": "Odsyłacz", "DE.Views.Links.capBtnInsContents": "<PERSON><PERSON> tre<PERSON>", "DE.Views.Links.capBtnInsFootnote": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Links.capBtnInsLink": "Link", "DE.Views.Links.capBtnTOF": "<PERSON>pis ilust<PERSON>", "DE.Views.Links.confirmDeleteFootnotes": "<PERSON><PERSON> ch<PERSON>z usunąć wszystkie przypisy?", "DE.Views.Links.confirmReplaceTOF": "<PERSON><PERSON> ch<PERSON>z zastąpić wybraną tabelę figur?", "DE.Views.Links.mniConvertNote": "Konwertowanie przypisów", "DE.Views.Links.mniDelFootnote": "Usuń wszystkie przypisy", "DE.Views.Links.mniInsEndnote": "Wstaw przypis końcowy", "DE.Views.Links.mniInsFootnote": "Wstaw przypis", "DE.Views.Links.mniNoteSettings": "Ustawienia notatek", "DE.Views.Links.textContentsRemove": "Us<PERSON>ń tabelę zawartości", "DE.Views.Links.textContentsSettings": "Ustawienia", "DE.Views.Links.textConvertToEndnotes": "Konwertuj wszystkie przypisy dolne na przypisy końcowe", "DE.Views.Links.textConvertToFootnotes": "Konwertuj wszystkie przypisy końcowe na przypisy dolne", "DE.Views.Links.textGotoEndnote": "idź do przypisów końcowych", "DE.Views.Links.textGotoFootnote": "Idź do przypisów", "DE.Views.Links.textSwapNotes": "Zamień miejscami przypisy dolne z przypisami końcowymi", "DE.Views.Links.textUpdateAll": "Odśwież całą tabelę", "DE.Views.Links.textUpdatePages": "Odśwież wyłącznie numery stron", "DE.Views.Links.tipBookmarks": "Utw<PERSON><PERSON>", "DE.Views.Links.tipCaption": "<PERSON><PERSON><PERSON>", "DE.Views.Links.tipContents": "<PERSON><PERSON> tre<PERSON>", "DE.Views.Links.tipContentsUpdate": "Od<PERSON><PERSON><PERSON>ż tabelę zawart<PERSON>", "DE.Views.Links.tipCrossRef": "Wstaw odsyłacz", "DE.Views.Links.tipInsertHyperlink": "Dodaj link", "DE.Views.Links.tipNotes": "Wstawianie lub edytowanie przypisów", "DE.Views.Links.tipTableFigures": "Wstaw spis ilustracji", "DE.Views.Links.tipTableFiguresUpdate": "Odśwież listę ilustracji", "DE.Views.Links.titleUpdateTOF": "Odśwież listę ilustracji", "DE.Views.Links.txtLevel": "Poziom", "DE.Views.ListSettingsDialog.textAuto": "Automatyczne", "DE.Views.ListSettingsDialog.textCenter": "Do Środka", "DE.Views.ListSettingsDialog.textLeft": "<PERSON>", "DE.Views.ListSettingsDialog.textLevel": "Poziom", "DE.Views.ListSettingsDialog.textPreview": "Podgląd", "DE.Views.ListSettingsDialog.textRight": "Do <PERSON>", "DE.Views.ListSettingsDialog.txtAlign": "Wyrównanie", "DE.Views.ListSettingsDialog.txtBullet": "Znak punktora", "DE.Views.ListSettingsDialog.txtColor": "<PERSON><PERSON>", "DE.Views.ListSettingsDialog.txtFont": "Czcionka i Symbole", "DE.Views.ListSettingsDialog.txtLikeText": "Jak tekst", "DE.Views.ListSettingsDialog.txtNewBullet": "Nowy znacznik", "DE.Views.ListSettingsDialog.txtNone": "Brak", "DE.Views.ListSettingsDialog.txtSize": "Rozmiar", "DE.Views.ListSettingsDialog.txtSymbol": "Symbole", "DE.Views.ListSettingsDialog.txtTitle": "Ustawienia listy", "DE.Views.ListSettingsDialog.txtType": "<PERSON><PERSON>", "DE.Views.MailMergeEmailDlg.filePlaceholder": "PDF", "DE.Views.MailMergeEmailDlg.okButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.MailMergeEmailDlg.subjectPlaceholder": "Motyw", "DE.Views.MailMergeEmailDlg.textAttachDocx": "Załącz jako DOCX", "DE.Views.MailMergeEmailDlg.textAttachPdf": "Załącz jako PDF", "DE.Views.MailMergeEmailDlg.textFileName": "Nazwa pliku", "DE.Views.MailMergeEmailDlg.textFormat": "Format poczty elektronicznej", "DE.Views.MailMergeEmailDlg.textFrom": "Od", "DE.Views.MailMergeEmailDlg.textHTML": "HTML", "DE.Views.MailMergeEmailDlg.textMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.MailMergeEmailDlg.textSubject": "<PERSON><PERSON><PERSON> tematu", "DE.Views.MailMergeEmailDlg.textTitle": "Wyślij do e-maila", "DE.Views.MailMergeEmailDlg.textTo": "Do", "DE.Views.MailMergeEmailDlg.textWarning": "Uwaga!", "DE.Views.MailMergeEmailDlg.textWarningMsg": "<PERSON><PERSON><PERSON><PERSON>, że po kliknięciu przycisku \"W<PERSON><PERSON><PERSON><PERSON>\" nie można zatrzymać wysyłania wiadomości.", "DE.Views.MailMergeSettings.downloadMergeTitle": "<PERSON><PERSON><PERSON>", "DE.Views.MailMergeSettings.errorMailMergeSaveFile": "Scalanie nie powiodło się.", "DE.Views.MailMergeSettings.notcriticalErrorTitle": "Ostrzeżenie", "DE.Views.MailMergeSettings.textAddRecipients": "Najpierw dodaj do listy odbiorców", "DE.Views.MailMergeSettings.textAll": "Wszystkie rekordy", "DE.Views.MailMergeSettings.textCurrent": "Obecny rekord", "DE.Views.MailMergeSettings.textDataSource": "Źródło danych", "DE.Views.MailMergeSettings.textDocx": "Docx", "DE.Views.MailMergeSettings.textDownload": "<PERSON><PERSON><PERSON>", "DE.Views.MailMergeSettings.textEditData": "Edyt<PERSON>j <PERSON>ę odbiorców", "DE.Views.MailMergeSettings.textEmail": "E-mail", "DE.Views.MailMergeSettings.textFrom": "Od", "DE.Views.MailMergeSettings.textGoToMail": "Idź do <PERSON>", "DE.Views.MailMergeSettings.textHighlight": "Podświetl scalone pola", "DE.Views.MailMergeSettings.textInsertField": "<PERSON><PERSON><PERSON> scalone pole", "DE.Views.MailMergeSettings.textMaxRecepients": "Maksymalnie 100 odbiorców.", "DE.Views.MailMergeSettings.textMerge": "<PERSON><PERSON>", "DE.Views.MailMergeSettings.textMergeFields": "Scal pola", "DE.Views.MailMergeSettings.textMergeTo": "Scal do", "DE.Views.MailMergeSettings.textPdf": "PDF", "DE.Views.MailMergeSettings.textPortal": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.MailMergeSettings.textPreview": "Wyniki podglądu", "DE.Views.MailMergeSettings.textReadMore": "Czytaj więcej", "DE.Views.MailMergeSettings.textSendMsg": "Wszystkie wiadomości są gotowe i zostaną wysłane w ciągu pewnego czasu.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wysyłki zależy od usługi poczty.<br>Mo<PERSON><PERSON>z kontynuować pracę z dokumentem lub zamknąć go. Po zakończeniu operacji powiadomienie zostanie wysłane na twój rejestracyjny adres e-mail.", "DE.Views.MailMergeSettings.textTo": "Do", "DE.Views.MailMergeSettings.txtFirst": "Do pierwszego rekordu", "DE.Views.MailMergeSettings.txtFromToError": "<PERSON><PERSON><PERSON><PERSON> \"Od\" musi być mniejsza niż wartość \"Do\"", "DE.Views.MailMergeSettings.txtLast": "Do ostatniego rekordu", "DE.Views.MailMergeSettings.txtNext": "Do następnego rekordu", "DE.Views.MailMergeSettings.txtPrev": "Do poprzedniego rekordu", "DE.Views.MailMergeSettings.txtUntitled": "Niezatytułowany", "DE.Views.MailMergeSettings.warnProcessMailMerge": "Rozpoczęcie scalania nie powiodło się", "DE.Views.Navigation.strNavigate": "Nagłówki", "DE.Views.Navigation.txtCollapse": "Zwiń wszystko", "DE.Views.Navigation.txtDemote": "Zdegrad<PERSON><PERSON>", "DE.Views.Navigation.txtEmpty": "W dokumencie nie ma nagłówków. <br> Zastos<PERSON>j styl nagłówka do tekstu, aby pojawił się w spisie treści.", "DE.Views.Navigation.txtEmptyItem": "Pusty nagłówek", "DE.Views.Navigation.txtEmptyViewer": "W dokumencie nie ma nagłówków.", "DE.Views.Navigation.txtExpand": "Rozwiń wszystko", "DE.Views.Navigation.txtExpandToLevel": "Rozwiń do poziomu", "DE.Views.Navigation.txtFontSize": "Rozmiar <PERSON>ki", "DE.Views.Navigation.txtHeadingAfter": "Nowy nagłówek po", "DE.Views.Navigation.txtHeadingBefore": "Nowy nagłówek przed", "DE.Views.Navigation.txtLarge": "<PERSON><PERSON><PERSON>", "DE.Views.Navigation.txtMedium": "Średni", "DE.Views.Navigation.txtNewHeading": "Nowy podtytuł", "DE.Views.Navigation.txtPromote": "<PERSON>mować", "DE.Views.Navigation.txtSelect": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Navigation.txtSettings": "Ustawienia nagłówków", "DE.Views.Navigation.txtSmall": "Ma<PERSON><PERSON>", "DE.Views.NoteSettingsDialog.textApply": "Zatwierdź", "DE.Views.NoteSettingsDialog.textApplyTo": "Zatwierdź zmiany do", "DE.Views.NoteSettingsDialog.textContinue": "Ciągły", "DE.Views.NoteSettingsDialog.textCustom": "Znak niestandardowy", "DE.Views.NoteSettingsDialog.textDocEnd": "Koniec dokumentu", "DE.Views.NoteSettingsDialog.textDocument": "Cały dokument", "DE.Views.NoteSettingsDialog.textEachPage": "Uruchom ponownie każdą stronę", "DE.Views.NoteSettingsDialog.textEachSection": "Uruchom ponownie każdą sekcję", "DE.Views.NoteSettingsDialog.textEndnote": "Przypisy końcowe", "DE.Views.NoteSettingsDialog.textFootnote": "Ruchoma tablica", "DE.Views.NoteSettingsDialog.textFormat": "Formatowanie", "DE.Views.NoteSettingsDialog.textInsert": "Wstaw", "DE.Views.NoteSettingsDialog.textLocation": "Lokalizacja", "DE.Views.NoteSettingsDialog.textNumbering": "Numeracja", "DE.Views.NoteSettingsDialog.textNumFormat": "Format numeru", "DE.Views.NoteSettingsDialog.textPageBottom": "<PERSON><PERSON><PERSON>", "DE.Views.NoteSettingsDialog.textSectEnd": "Koniec se<PERSON>ji", "DE.Views.NoteSettingsDialog.textSection": "Aktualna <PERSON>", "DE.Views.NoteSettingsDialog.textStart": "Zacznij w", "DE.Views.NoteSettingsDialog.textTextBottom": "Poniżej tekstu", "DE.Views.NoteSettingsDialog.textTitle": "Ustawienia notatek", "DE.Views.NotesRemoveDialog.textEnd": "Usuń wszystkie przypisy końcowe", "DE.Views.NotesRemoveDialog.textFoot": "Usuń wszystkie przypisy", "DE.Views.NotesRemoveDialog.textTitle": "Usuń przypisy", "DE.Views.PageMarginsDialog.notcriticalErrorTitle": "Ostrzeżenie", "DE.Views.PageMarginsDialog.textBottom": "<PERSON><PERSON><PERSON>", "DE.Views.PageMarginsDialog.textGutter": "Wiążący", "DE.Views.PageMarginsDialog.textGutterPosition": "Pozycja marginesu na oprawę", "DE.Views.PageMarginsDialog.textInside": "Wewnę<PERSON><PERSON><PERSON>", "DE.Views.PageMarginsDialog.textLandscape": "Pozioma", "DE.Views.PageMarginsDialog.textLeft": "<PERSON><PERSON>", "DE.Views.PageMarginsDialog.textMirrorMargins": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.PageMarginsDialog.textMultiplePages": "<PERSON><PERSON>e stron", "DE.Views.PageMarginsDialog.textNormal": "Normalny", "DE.Views.PageMarginsDialog.textOrientation": "Orientacja", "DE.Views.PageMarginsDialog.textOutside": "Zewnętrzne", "DE.Views.PageMarginsDialog.textPortrait": "<PERSON><PERSON><PERSON>", "DE.Views.PageMarginsDialog.textPreview": "Podgląd", "DE.Views.PageMarginsDialog.textRight": "<PERSON><PERSON><PERSON>", "DE.Views.PageMarginsDialog.textTitle": "Mar<PERSON><PERSON><PERSON>", "DE.Views.PageMarginsDialog.textTop": "Góra", "DE.Views.PageMarginsDialog.txtMarginsH": "Górne i dolne marginesy są za wysokie dla danej wysokości strony", "DE.Views.PageMarginsDialog.txtMarginsW": "Lewe i prawe marginesy są zbyt szerokie dla danej szerokości strony", "DE.Views.PageSizeDialog.textHeight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.PageSizeDialog.textPreset": "Zaprogramowane", "DE.Views.PageSizeDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON> strony", "DE.Views.PageSizeDialog.textWidth": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.PageSizeDialog.txtCustom": "Niestandardowy", "DE.Views.PageThumbnails.textPageThumbnails": "Miniaturki strony", "DE.Views.PageThumbnails.textThumbnailsSettings": "Ustawienia miniaturek", "DE.Views.PageThumbnails.textThumbnailsSize": "Rozmiar miniaturek", "DE.Views.ParagraphSettings.strIndent": "<PERSON><PERSON>ę<PERSON>", "DE.Views.ParagraphSettings.strIndentsLeftText": "<PERSON>", "DE.Views.ParagraphSettings.strIndentsRightText": "Z prawej", "DE.Views.ParagraphSettings.strIndentsSpecial": "Specjalne", "DE.Views.ParagraphSettings.strLineHeight": "Rozstaw wierszy", "DE.Views.ParagraphSettings.strParagraphSpacing": "Odstępy akapitu", "DE.Views.ParagraphSettings.strSomeParagraphSpace": "Nie dodawaj odstępu między akapitami tego samego stylu", "DE.Views.ParagraphSettings.strSpacingAfter": "Po", "DE.Views.ParagraphSettings.strSpacingBefore": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettings.textAdvanced": "Po<PERSON>ż ustawienia zaawansowane", "DE.Views.ParagraphSettings.textAt": "W", "DE.Views.ParagraphSettings.textAtLeast": "Co najmniej", "DE.Views.ParagraphSettings.textAuto": "Mnożnik", "DE.Views.ParagraphSettings.textBackColor": "<PERSON><PERSON>", "DE.Views.ParagraphSettings.textExact": "Dokładnie", "DE.Views.ParagraphSettings.textFirstLine": "<PERSON><PERSON><PERSON> w<PERSON>", "DE.Views.ParagraphSettings.textHanging": "Wysunięcie", "DE.Views.ParagraphSettings.textNoneSpecial": "(brak)", "DE.Views.ParagraphSettings.txtAutoText": "Automatyczny", "DE.Views.ParagraphSettingsAdvanced.noTabs": "W tym polu zostaną wyświetlone określone karty", "DE.Views.ParagraphSettingsAdvanced.strAllCaps": "Wszystkie duże litery", "DE.Views.ParagraphSettingsAdvanced.strBorders": "Obramowania i wypełnienie", "DE.Views.ParagraphSettingsAdvanced.strBreakBefore": "<PERSON><PERSON><PERSON><PERSON> strony przed", "DE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "Podwójne przekreślenie", "DE.Views.ParagraphSettingsAdvanced.strIndent": "<PERSON><PERSON>ę<PERSON>", "DE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "<PERSON>", "DE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Interlinia", "DE.Views.ParagraphSettingsAdvanced.strIndentsOutlinelevel": "Poziom konspektu", "DE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "Z prawej", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "po", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Specjalne", "DE.Views.ParagraphSettingsAdvanced.strKeepLines": "<PERSON><PERSON><PERSON>aj wiersze razem", "DE.Views.ParagraphSettingsAdvanced.strKeepNext": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.strMargins": "Wewnętrzne pola", "DE.Views.ParagraphSettingsAdvanced.strOrphan": "Zakaz bękartów", "DE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Czcionka", "DE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Wcięcia i odstępy", "DE.Views.ParagraphSettingsAdvanced.strParagraphLine": "Podział wierszy i stron", "DE.Views.ParagraphSettingsAdvanced.strParagraphPosition": "Umieszczenie", "DE.Views.ParagraphSettingsAdvanced.strSmallCaps": "Małe litery", "DE.Views.ParagraphSettingsAdvanced.strSomeParagraphSpace": "Nie dodawaj odstępu między akapitami tego samego stylu", "DE.Views.ParagraphSettingsAdvanced.strSpacing": "Odstępy", "DE.Views.ParagraphSettingsAdvanced.strStrike": "Przekreślenie", "DE.Views.ParagraphSettingsAdvanced.strSubscript": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.strSuperscript": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.strSuppressLineNumbers": "Ukryj numerac<PERSON> w<PERSON>zy", "DE.Views.ParagraphSettingsAdvanced.strTabs": "Karta", "DE.Views.ParagraphSettingsAdvanced.textAlign": "Wyrównanie", "DE.Views.ParagraphSettingsAdvanced.textAll": "Wszystkie", "DE.Views.ParagraphSettingsAdvanced.textAtLeast": "Co najmniej", "DE.Views.ParagraphSettingsAdvanced.textAuto": "Mnożnik", "DE.Views.ParagraphSettingsAdvanced.textBackColor": "<PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textBodyText": "Tekst podstawowy", "DE.Views.ParagraphSettingsAdvanced.textBorderColor": "<PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textBorderDesc": "Kliknij na diagram lub użyj przycisków, aby wybrać granice i zastosuj do nich wybrany styl", "DE.Views.ParagraphSettingsAdvanced.textBorderWidth": "Roz<PERSON>r <PERSON>", "DE.Views.ParagraphSettingsAdvanced.textBottom": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textCentered": "Wyśrodkowane", "DE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Rozstaw znaków", "DE.Views.ParagraphSettingsAdvanced.textDefault": "Domyślna zakładka", "DE.Views.ParagraphSettingsAdvanced.textEffects": "Efekty", "DE.Views.ParagraphSettingsAdvanced.textExact": "Dokładnie", "DE.Views.ParagraphSettingsAdvanced.textFirstLine": "<PERSON><PERSON><PERSON> w<PERSON>", "DE.Views.ParagraphSettingsAdvanced.textHanging": "Wysunięcie", "DE.Views.ParagraphSettingsAdvanced.textJustified": "Wyjustowany", "DE.Views.ParagraphSettingsAdvanced.textLeader": "<PERSON><PERSON> linii", "DE.Views.ParagraphSettingsAdvanced.textLeft": "<PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textLevel": "Poziom", "DE.Views.ParagraphSettingsAdvanced.textLigatures": "Ligatury", "DE.Views.ParagraphSettingsAdvanced.textNone": "Brak", "DE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(brak)", "DE.Views.ParagraphSettingsAdvanced.textPosition": "Położenie", "DE.Views.ParagraphSettingsAdvanced.textRemove": "Usuń", "DE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Us<PERSON>ń wszystko", "DE.Views.ParagraphSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textSet": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textSpacing": "Odstępy", "DE.Views.ParagraphSettingsAdvanced.textStandard": "Tylko standardowy", "DE.Views.ParagraphSettingsAdvanced.textTabCenter": "Środek", "DE.Views.ParagraphSettingsAdvanced.textTabLeft": "<PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textTabPosition": "Pozycja karty", "DE.Views.ParagraphSettingsAdvanced.textTabRight": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textTitle": "Akapit - Ustawienia zaawansowane", "DE.Views.ParagraphSettingsAdvanced.textTop": "Góra", "DE.Views.ParagraphSettingsAdvanced.tipAll": "Ustaw krawędź zewnętrzną i wszystkie wewnętrzne linie", "DE.Views.ParagraphSettingsAdvanced.tipBottom": "Ustaw tylko dolną krawędź", "DE.Views.ParagraphSettingsAdvanced.tipInner": "<PERSON><PERSON><PERSON><PERSON> tylko poziome linie wewnętrzne", "DE.Views.ParagraphSettingsAdvanced.tipLeft": "Ustaw tylko lewą krawędź", "DE.Views.ParagraphSettingsAdvanced.tipNone": "Wyłącz krawędzie", "DE.Views.ParagraphSettingsAdvanced.tipOuter": "Ustaw tylko obramowanie zewnętrzne", "DE.Views.ParagraphSettingsAdvanced.tipRight": "Ustaw tylko obramowanie prawej krawędzi", "DE.Views.ParagraphSettingsAdvanced.tipTop": "Ustaw tylko górną krawędź", "DE.Views.ParagraphSettingsAdvanced.txtAutoText": "Automatyczny", "DE.Views.ParagraphSettingsAdvanced.txtNoBorders": "Bez k<PERSON>ędzi", "DE.Views.RightMenu.txtChartSettings": "Ustawienia wykresu", "DE.Views.RightMenu.txtFormSettings": "Ustawienia formularza", "DE.Views.RightMenu.txtHeaderFooterSettings": "Ustawienia nagłówka i stopki", "DE.Views.RightMenu.txtImageSettings": "Ustawi<PERSON> obrazu", "DE.Views.RightMenu.txtMailMergeSettings": "Ustawienia korespondencji seryjnej", "DE.Views.RightMenu.txtParagraphSettings": "Ustawienia akapitu", "DE.Views.RightMenu.txtShapeSettings": "Ustawienia kształtu", "DE.Views.RightMenu.txtSignatureSettings": "Ustawienia podpisów", "DE.Views.RightMenu.txtTableSettings": "<PERSON><PERSON><PERSON><PERSON> tabeli", "DE.Views.RightMenu.txtTextArtSettings": "Ustawienia tekstu", "DE.Views.ShapeSettings.strBackground": "<PERSON><PERSON>", "DE.Views.ShapeSettings.strChange": "Zmień kształt", "DE.Views.ShapeSettings.strColor": "<PERSON><PERSON>", "DE.Views.ShapeSettings.strFill": "Wypełnij", "DE.Views.ShapeSettings.strForeground": "<PERSON><PERSON>", "DE.Views.ShapeSettings.strPattern": "Wzór", "DE.Views.ShapeSettings.strShadow": "Pokaż cień ", "DE.Views.ShapeSettings.strSize": "Rozmiar", "DE.Views.ShapeSettings.strStroke": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.strTransparency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.strType": "<PERSON><PERSON>", "DE.Views.ShapeSettings.textAdvanced": "Po<PERSON>ż ustawienia zaawansowane", "DE.Views.ShapeSettings.textAngle": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.textBorderSizeErr": "Wprowadzona wartość jest nieprawidłowa.<br><PERSON><PERSON><PERSON><PERSON><PERSON> wartość w zakresie od 0 do 1584 pt.", "DE.Views.ShapeSettings.textColor": "<PERSON><PERSON> wypełnienia", "DE.Views.ShapeSettings.textDirection": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.textEmptyPattern": "Brak wzorca", "DE.Views.ShapeSettings.textFlip": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.textFromFile": "Z pliku", "DE.Views.ShapeSettings.textFromStorage": "Z magazynu", "DE.Views.ShapeSettings.textFromUrl": "Z adresu URL", "DE.Views.ShapeSettings.textGradient": "<PERSON><PERSON>", "DE.Views.ShapeSettings.textGradientFill": "Wypełnienie gradientem", "DE.Views.ShapeSettings.textHint270": "<PERSON><PERSON><PERSON><PERSON><PERSON> w lewo o 90° ", "DE.Views.ShapeSettings.textHint90": "Obróć w prawo o 90°", "DE.Views.ShapeSettings.textHintFlipH": "Odwróć w poziomie", "DE.Views.ShapeSettings.textHintFlipV": "Odwróć w pionie", "DE.Views.ShapeSettings.textImageTexture": "<PERSON><PERSON>z lub teks<PERSON>", "DE.Views.ShapeSettings.textLinear": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.textNoFill": "Brak wypełnienia", "DE.Views.ShapeSettings.textPatternFill": "Wzór", "DE.Views.ShapeSettings.textPosition": "<PERSON><PERSON><PERSON>ja", "DE.Views.ShapeSettings.textRadial": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.textRecentlyUsed": "Ostatnio używane", "DE.Views.ShapeSettings.textRotate90": "O<PERSON><PERSON>óć o 90°", "DE.Views.ShapeSettings.textRotation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.textSelectImage": "<PERSON><PERSON><PERSON>rz zd<PERSON>", "DE.Views.ShapeSettings.textSelectTexture": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.textStretch": "Roz<PERSON>ą<PERSON><PERSON>", "DE.Views.ShapeSettings.textStyle": "<PERSON><PERSON>", "DE.Views.ShapeSettings.textTexture": "Z tekstury", "DE.Views.ShapeSettings.textTile": "Płytka", "DE.Views.ShapeSettings.textWrap": "Styl zawijania", "DE.Views.ShapeSettings.tipAddGradientPoint": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.tipRemoveGradientPoint": "Usuń punkt gradientu", "DE.Views.ShapeSettings.txtBehind": "<PERSON>a", "DE.Views.ShapeSettings.txtBrownPaper": "Brązowy papier", "DE.Views.ShapeSettings.txtCanvas": "Płótno", "DE.Views.ShapeSettings.txtCarton": "<PERSON><PERSON>", "DE.Views.ShapeSettings.txtDarkFabric": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.txtGrain": "Ziarno", "DE.Views.ShapeSettings.txtGranite": "Granit", "DE.Views.ShapeSettings.txtGreyPaper": "<PERSON><PERSON><PERSON> papier", "DE.Views.ShapeSettings.txtInFront": "Z przodu", "DE.Views.ShapeSettings.txtInline": "W tekście", "DE.Views.ShapeSettings.txtKnit": "Szydełkowanie", "DE.Views.ShapeSettings.txtLeather": "Skórzany", "DE.Views.ShapeSettings.txtNoBorders": "<PERSON><PERSON>", "DE.Views.ShapeSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.txtSquare": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.txtThrough": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.txtTight": "szczelnie", "DE.Views.ShapeSettings.txtTopAndBottom": "Góra i dół", "DE.Views.ShapeSettings.txtWood": "Drew<PERSON>", "DE.Views.SignatureSettings.notcriticalErrorTitle": "Ostrzeżenie", "DE.Views.SignatureSettings.strDelete": "<PERSON><PERSON><PERSON>", "DE.Views.SignatureSettings.strDetails": "Szczegóły sygnatury", "DE.Views.SignatureSettings.strInvalid": "Nieprawidłowe podpisy", "DE.Views.SignatureSettings.strRequested": "Żądane podpisy", "DE.Views.SignatureSettings.strSetup": "Konfiguracja podpisu", "DE.Views.SignatureSettings.strSign": "Podpisz", "DE.Views.SignatureSettings.strSignature": "Sygnatura", "DE.Views.SignatureSettings.strSigner": "Podpisujący", "DE.Views.SignatureSettings.strValid": "Ważne podpisy", "DE.Views.SignatureSettings.txtContinueEditing": "<PERSON><PERSON><PERSON><PERSON> mimo w<PERSON>ko", "DE.Views.SignatureSettings.txtEditWarning": "Edycja spowoduje usunięcie podpisów z dokumentu. <br> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>?", "DE.Views.SignatureSettings.txtRemoveWarning": "<PERSON><PERSON> ch<PERSON> us<PERSON> ten podpis? <br> <PERSON><PERSON> można tego co<PERSON>.", "DE.Views.SignatureSettings.txtRequestedSignatures": "Ten dokument musi zostać podpisany.", "DE.Views.SignatureSettings.txtSigned": "Do dokumentu dodano prawidłowe podpisy. Dokument jest chroniony przed edycją.", "DE.Views.SignatureSettings.txtSignedInvalid": "Niektóre podpisy cyfrowe w dokumencie są nieprawidłowe lub nie można ich zweryfikować. Dokument jest chroniony przed edycją.", "DE.Views.Statusbar.goToPageText": "Idź do strony", "DE.Views.Statusbar.pageIndexText": "Strona {0} z {1}", "DE.Views.Statusbar.tipFitPage": "Dopasuj do strony", "DE.Views.Statusbar.tipFitWidth": "Dopasuj do szerokości", "DE.Views.Statusbar.tipSelectTool": "<PERSON><PERSON>bierz narzędzie", "DE.Views.Statusbar.tipSetLang": "Ustaw język tekstu", "DE.Views.Statusbar.tipZoomFactor": "Powiększenie", "DE.Views.Statusbar.tipZoomIn": "Powię<PERSON><PERSON>", "DE.Views.Statusbar.tipZoomOut": "Po<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Statusbar.txtPageNumInvalid": "Błędny numer strony", "DE.Views.StyleTitleDialog.textHeader": "Utwórz nowy styl", "DE.Views.StyleTitleDialog.textNextStyle": "Następny styl akapitu", "DE.Views.StyleTitleDialog.textTitle": "<PERSON><PERSON><PERSON>", "DE.Views.StyleTitleDialog.txtEmpty": "To pole jest wymagane", "DE.Views.StyleTitleDialog.txtNotEmpty": "Pole nie może by<PERSON> puste", "DE.Views.StyleTitleDialog.txtSameAs": "<PERSON><PERSON> sam, jak stwo<PERSON><PERSON> nowy styl", "DE.Views.TableFormulaDialog.textBookmark": "<PERSON><PERSON><PERSON> zakładk<PERSON>", "DE.Views.TableFormulaDialog.textFormat": "Format liczby", "DE.Views.TableFormulaDialog.textFormula": "Formuła", "DE.Views.TableFormulaDialog.textInsertFunction": "<PERSON><PERSON><PERSON>", "DE.Views.TableFormulaDialog.textTitle": "Ustawienia formuł", "DE.Views.TableOfContentsSettings.strAlign": "Numery stron wyrównaj do prawej", "DE.Views.TableOfContentsSettings.strFullCaption": "Dołącz etykietę i numer", "DE.Views.TableOfContentsSettings.strLinks": "Użyj hiperłączy zamiast numerów stron", "DE.Views.TableOfContentsSettings.strLinksOF": "Formatuj ta<PERSON> liczb jako linki", "DE.Views.TableOfContentsSettings.strShowPages": "Pokaż numery stron", "DE.Views.TableOfContentsSettings.textBuildTable": "Zbuduj spis treści z", "DE.Views.TableOfContentsSettings.textBuildTableOF": "Zbuduj tabelę liczb z", "DE.Views.TableOfContentsSettings.textEquation": "Równanie", "DE.Views.TableOfContentsSettings.textFigure": "Rysunek", "DE.Views.TableOfContentsSettings.textLeader": "Znaki wiodące tabulacji", "DE.Views.TableOfContentsSettings.textLevel": "Poziom", "DE.Views.TableOfContentsSettings.textLevels": "Poziomy", "DE.Views.TableOfContentsSettings.textNone": "Brak", "DE.Views.TableOfContentsSettings.textRadioCaption": "Podpis", "DE.Views.TableOfContentsSettings.textRadioLevels": "Pokaż poziomy", "DE.Views.TableOfContentsSettings.textRadioStyle": "<PERSON><PERSON>", "DE.Views.TableOfContentsSettings.textRadioStyles": "Wybierz style", "DE.Views.TableOfContentsSettings.textStyle": "<PERSON><PERSON>", "DE.Views.TableOfContentsSettings.textStyles": "Format", "DE.Views.TableOfContentsSettings.textTable": "<PERSON><PERSON><PERSON>", "DE.Views.TableOfContentsSettings.textTitle": "<PERSON><PERSON> tre<PERSON>", "DE.Views.TableOfContentsSettings.textTitleTOF": "<PERSON>pis ilust<PERSON>", "DE.Views.TableOfContentsSettings.txtCentered": "Wyśrodkowane", "DE.Views.TableOfContentsSettings.txtClassic": "Klasyczny", "DE.Views.TableOfContentsSettings.txtCurrent": "Obecny", "DE.Views.TableOfContentsSettings.txtDistinctive": "Charakterystyczny", "DE.Views.TableOfContentsSettings.txtFormal": "Oficjalny", "DE.Views.TableOfContentsSettings.txtModern": "Nowoczesny", "DE.Views.TableOfContentsSettings.txtOnline": "Online", "DE.Views.TableOfContentsSettings.txtSimple": "Prosty", "DE.Views.TableOfContentsSettings.txtStandard": "Standardowy", "DE.Views.TableSettings.deleteColumnText": "<PERSON><PERSON>ń kolumnę", "DE.Views.TableSettings.deleteRowText": "<PERSON><PERSON><PERSON> wiersz", "DE.Views.TableSettings.deleteTableText": "<PERSON><PERSON><PERSON> ta<PERSON>", "DE.Views.TableSettings.insertColumnLeftText": "Wstaw kolumnę z lewej", "DE.Views.TableSettings.insertColumnRightText": "Wstaw kolumnę z prawej", "DE.Views.TableSettings.insertRowAboveText": "Wstaw wiersz powyżej", "DE.Views.TableSettings.insertRowBelowText": "Wstaw wiersz poniżej", "DE.Views.TableSettings.mergeCellsText": "Scal komórki", "DE.Views.TableSettings.selectCellText": "Wybierz komórkę", "DE.Views.TableSettings.selectColumnText": "<PERSON><PERSON><PERSON><PERSON> kol<PERSON>", "DE.Views.TableSettings.selectRowText": "<PERSON><PERSON><PERSON><PERSON> w<PERSON>z", "DE.Views.TableSettings.selectTableText": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.TableSettings.splitCellsText": "Podziel komórkę...", "DE.Views.TableSettings.splitCellTitleText": "Podziel komórkę", "DE.Views.TableSettings.strRepeatRow": "Powtarzaj jako wiersz nagłówka u góry każdej strony", "DE.Views.TableSettings.textAddFormula": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.textAdvanced": "Po<PERSON>ż ustawienia zaawansowane", "DE.Views.TableSettings.textBackColor": "<PERSON><PERSON>", "DE.Views.TableSettings.textBanded": "Na przemian", "DE.Views.TableSettings.textBorderColor": "<PERSON><PERSON>", "DE.Views.TableSettings.textBorders": "Style obramowań", "DE.Views.TableSettings.textCellSize": "Rozmiary wierszy i kolumn", "DE.Views.TableSettings.textColumns": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.textConvert": "Konwertuj tabelę na tekst", "DE.Views.TableSettings.textDistributeCols": "Rozłóż kolumny", "DE.Views.TableSettings.textDistributeRows": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.TableSettings.textEdit": "Wiersze i Kolumny", "DE.Views.TableSettings.textEmptyTemplate": "Brak szablonów", "DE.Views.TableSettings.textFirst": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.textHeader": "Nagłówek", "DE.Views.TableSettings.textHeight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.TableSettings.textLast": "Ostatni", "DE.Views.TableSettings.textRows": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.textSelectBorders": "<PERSON><PERSON><PERSON><PERSON>, które chcesz zmienić stosując styl wybrany powyżej", "DE.Views.TableSettings.textTemplate": "Wybierz z szablonu", "DE.Views.TableSettings.textTotal": "Wszystkie", "DE.Views.TableSettings.textWidth": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.TableSettings.tipAll": "Ustaw krawędź zewnętrzną i wszystkie wewnętrzne linie", "DE.Views.TableSettings.tipBottom": "Ustaw tylko obramowanie dolnej krawędzi", "DE.Views.TableSettings.tipInner": "Us<PERSON>wić tylko linie wewnętrzne", "DE.Views.TableSettings.tipInnerHor": "<PERSON><PERSON><PERSON><PERSON> tylko poziome linie wewnętrzne", "DE.Views.TableSettings.tipInnerVert": "Ustaw tylko wewnętrzne pionowe linie", "DE.Views.TableSettings.tipLeft": "Ustaw tylko obramowanie lewej krawędzi", "DE.Views.TableSettings.tipNone": "Wyłącz krawędzie", "DE.Views.TableSettings.tipOuter": "Ustaw tylko obramowanie zewnętrzne", "DE.Views.TableSettings.tipRight": "Ustaw tylko obramowanie prawej krawędzi", "DE.Views.TableSettings.tipTop": "Ustaw tylko obramowanie górnej krawędzi", "DE.Views.TableSettings.txtNoBorders": "Bez k<PERSON>ędzi", "DE.Views.TableSettings.txtTable_Accent": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.txtTable_Colorful": "kolo<PERSON>y", "DE.Views.TableSettings.txtTable_Dark": "Ciemny", "DE.Views.TableSettings.txtTable_GridTable": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.txtTable_Light": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.txtTable_ListTable": "<PERSON><PERSON><PERSON> listy", "DE.Views.TableSettings.txtTable_PlainTable": "Zwykła tabela", "DE.Views.TableSettings.txtTable_TableGrid": "Tabela - siatka", "DE.Views.TableSettingsAdvanced.textAlign": "Wyrównanie", "DE.Views.TableSettingsAdvanced.textAlignment": "Wyrównanie", "DE.Views.TableSettingsAdvanced.textAllowSpacing": "Rozstaw między komórkami", "DE.Views.TableSettingsAdvanced.textAlt": "Tekst alternatywny", "DE.Views.TableSettingsAdvanced.textAltDescription": "Opis", "DE.Views.TableSettingsAdvanced.textAltTip": "Alternatywna, tekstowa prezentacja wizualnych informacji o obiektach, która będzie czytana osobom z wadami wzroku lub zmysłu poznawczego, aby pomóc im lepiej zrozumieć, jakie informacje znajdują się na obrazie, ksz<PERSON>łcie, wykresie lub tabeli.", "DE.Views.TableSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textAnchorText": "Tekst", "DE.Views.TableSettingsAdvanced.textAutofit": "Automatycznie zmień rozmiar, aby pasował do zawartości", "DE.Views.TableSettingsAdvanced.textBackColor": "Tło komórki", "DE.Views.TableSettingsAdvanced.textBelow": "Poniżej", "DE.Views.TableSettingsAdvanced.textBorderColor": "<PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textBorderDesc": "Kliknij na diagram lub użyj przycisków, aby wybrać granice i zastosuj do nich wybrany styl", "DE.Views.TableSettingsAdvanced.textBordersBackgroung": "Obramowania i tła", "DE.Views.TableSettingsAdvanced.textBorderWidth": "Roz<PERSON>r <PERSON>", "DE.Views.TableSettingsAdvanced.textBottom": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textCellOptions": "Opcje komórki", "DE.Views.TableSettingsAdvanced.textCellProps": "Komórka", "DE.Views.TableSettingsAdvanced.textCellSize": "Rozmiar komórki", "DE.Views.TableSettingsAdvanced.textCenter": "Środek", "DE.Views.TableSettingsAdvanced.textCenterTooltip": "Środek", "DE.Views.TableSettingsAdvanced.textCheckMargins": "Użyj domyślnych marginesów", "DE.Views.TableSettingsAdvanced.textDefaultMargins": "Domyślne marginesy komórki", "DE.Views.TableSettingsAdvanced.textDistance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> od tekstu", "DE.Views.TableSettingsAdvanced.textHorizontal": "Poziomy", "DE.Views.TableSettingsAdvanced.textIndLeft": "Wcięcie od lewej", "DE.Views.TableSettingsAdvanced.textLeft": "<PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textLeftTooltip": "<PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textMargin": "Mar<PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textMargins": "Marginesy komórki", "DE.Views.TableSettingsAdvanced.textMeasure": "Zmierz w", "DE.Views.TableSettingsAdvanced.textMove": "Przesuń obiekt z tekstem", "DE.Views.TableSettingsAdvanced.textOnlyCells": "<PERSON><PERSON>o dla wybranych komórek", "DE.Views.TableSettingsAdvanced.textOptions": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textOverlap": "<PERSON><PERSON><PERSON><PERSON> nakładk<PERSON>", "DE.Views.TableSettingsAdvanced.textPage": "Strona", "DE.Views.TableSettingsAdvanced.textPosition": "<PERSON><PERSON><PERSON>ja", "DE.Views.TableSettingsAdvanced.textPrefWidth": "<PERSON>alecan<PERSON>", "DE.Views.TableSettingsAdvanced.textPreview": "Podgląd", "DE.Views.TableSettingsAdvanced.textRelative": "względny do", "DE.Views.TableSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textRightOf": "na prawo od", "DE.Views.TableSettingsAdvanced.textRightTooltip": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textTable": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textTableBackColor": "<PERSON><PERSON><PERSON> tabeli", "DE.Views.TableSettingsAdvanced.textTablePosition": "<PERSON><PERSON><PERSON><PERSON> tabeli", "DE.Views.TableSettingsAdvanced.textTableSize": "Rozmiar tablicy", "DE.Views.TableSettingsAdvanced.textTitle": "Tabel<PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> us<PERSON>", "DE.Views.TableSettingsAdvanced.textTop": "Góra", "DE.Views.TableSettingsAdvanced.textVertical": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textWidthSpaces": "Szerokość i przestrzeń", "DE.Views.TableSettingsAdvanced.textWrap": "Zawijanie tekstu", "DE.Views.TableSettingsAdvanced.textWrapNoneTooltip": "<PERSON>bela liniowa", "DE.Views.TableSettingsAdvanced.textWrapParallelTooltip": "Ruchoma tablica", "DE.Views.TableSettingsAdvanced.textWrappingStyle": "Styl zawijania", "DE.Views.TableSettingsAdvanced.textWrapText": "Zawijaj tekst", "DE.Views.TableSettingsAdvanced.tipAll": "Ustaw krawędź zewnętrzną i wszystkie wewnętrzne linie", "DE.Views.TableSettingsAdvanced.tipCellAll": "Ustaw krawędzie tylko dla komórek wewnę<PERSON>z<PERSON>ch", "DE.Views.TableSettingsAdvanced.tipCellInner": "Ustaw pionowe i poziome linie dla wewnętrznych komórek", "DE.Views.TableSettingsAdvanced.tipCellOuter": "Ustaw krawędzie zewnętrzne tylko dla wewnętrznych komórek", "DE.Views.TableSettingsAdvanced.tipInner": "Us<PERSON>wić tylko linie wewnętrzne", "DE.Views.TableSettingsAdvanced.tipNone": "Wyłącz krawędzie", "DE.Views.TableSettingsAdvanced.tipOuter": "Ustaw tylko obramowanie zewnętrzne", "DE.Views.TableSettingsAdvanced.tipTableOuterCellAll": "Ustaw krawędź zewnętrzną i obramowanie dla wszystkich wewnętrznych komórek", "DE.Views.TableSettingsAdvanced.tipTableOuterCellInner": "Ustaw linie zewnętrzne i linie pionowe i poziome dla komórek wewnętrznych", "DE.Views.TableSettingsAdvanced.tipTableOuterCellOuter": "Zestaw stołowych zewnętrznej granicy i na granicy zewnętrznej do wewnętrznej komórki", "DE.Views.TableSettingsAdvanced.txtCm": "Centymetr", "DE.Views.TableSettingsAdvanced.txtInch": "<PERSON>", "DE.Views.TableSettingsAdvanced.txtNoBorders": "Bez k<PERSON>ędzi", "DE.Views.TableSettingsAdvanced.txtPercent": "Procent", "DE.Views.TableSettingsAdvanced.txtPt": "<PERSON><PERSON>", "DE.Views.TableToTextDialog.textEmpty": "Musisz wpisać znak dla niestandardowego separatora.", "DE.Views.TableToTextDialog.textNested": "Konwertuj zagnieżdżone tabele", "DE.Views.TableToTextDialog.textOther": "<PERSON><PERSON>", "DE.Views.TableToTextDialog.textPara": "<PERSON><PERSON><PERSON>", "DE.Views.TableToTextDialog.textSemicolon": "Średniki", "DE.Views.TableToTextDialog.textSeparator": "Separator", "DE.Views.TableToTextDialog.textTab": "Tabulacja", "DE.Views.TableToTextDialog.textTitle": "Konwertuj tabelę na tekst", "DE.Views.TextArtSettings.strColor": "<PERSON><PERSON>", "DE.Views.TextArtSettings.strFill": "Wypełnij", "DE.Views.TextArtSettings.strSize": "Rozmiar", "DE.Views.TextArtSettings.strStroke": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.TextArtSettings.strTransparency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.TextArtSettings.strType": "<PERSON><PERSON>", "DE.Views.TextArtSettings.textAngle": "<PERSON><PERSON><PERSON>", "DE.Views.TextArtSettings.textBorderSizeErr": "Wprowadzona wartość jest nieprawidłowa.<br><PERSON><PERSON><PERSON><PERSON><PERSON> wartość w zakresie od 0 do 1584 pt.", "DE.Views.TextArtSettings.textColor": "<PERSON><PERSON> wypełnienia", "DE.Views.TextArtSettings.textDirection": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.TextArtSettings.textGradient": "<PERSON><PERSON>", "DE.Views.TextArtSettings.textGradientFill": "Wypełnienie gradientem", "DE.Views.TextArtSettings.textLinear": "<PERSON><PERSON><PERSON>", "DE.Views.TextArtSettings.textNoFill": "Brak wypełnienia", "DE.Views.TextArtSettings.textPosition": "Położenie", "DE.Views.TextArtSettings.textRadial": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.TextArtSettings.textSelectTexture": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.TextArtSettings.textStyle": "<PERSON><PERSON>", "DE.Views.TextArtSettings.textTemplate": "Szablon", "DE.Views.TextArtSettings.textTransform": "Przekształcenie", "DE.Views.TextArtSettings.tipAddGradientPoint": "<PERSON><PERSON><PERSON>", "DE.Views.TextArtSettings.tipRemoveGradientPoint": "Usuń punkt gradientu", "DE.Views.TextArtSettings.txtNoBorders": "<PERSON><PERSON>", "DE.Views.TextToTableDialog.textAutofit": "Zachowanie autodopasowania", "DE.Views.TextToTableDialog.textColumns": "<PERSON><PERSON><PERSON>", "DE.Views.TextToTableDialog.textContents": "Automatycznie dopasuj do zawartości", "DE.Views.TextToTableDialog.textEmpty": "Musisz wpisać znak dla niestandardowego separatora.", "DE.Views.TextToTableDialog.textFixed": "Stałą szerokość kolumny", "DE.Views.TextToTableDialog.textOther": "<PERSON><PERSON>", "DE.Views.TextToTableDialog.textPara": "Akapity", "DE.Views.TextToTableDialog.textRows": "<PERSON><PERSON><PERSON>", "DE.Views.TextToTableDialog.textSemicolon": "Średniki", "DE.Views.TextToTableDialog.textSeparator": "Separator te<PERSON>", "DE.Views.TextToTableDialog.textTab": "Tabulacja", "DE.Views.TextToTableDialog.textTableSize": "<PERSON><PERSON><PERSON><PERSON> tabeli", "DE.Views.TextToTableDialog.textTitle": "Konwertuj tekst na tabelę", "DE.Views.TextToTableDialog.textWindow": "Automatycznie dopasuj do okna", "DE.Views.TextToTableDialog.txtAutoText": "Automatyczny", "DE.Views.Toolbar.capBtnAddComment": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.capBtnBlankPage": "<PERSON><PERSON><PERSON> strona", "DE.Views.Toolbar.capBtnColumns": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.capBtnComment": "Komentarz", "DE.Views.Toolbar.capBtnDateTime": "Data i czas", "DE.Views.Toolbar.capBtnInsChart": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.capBtnInsControls": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.capBtnInsDropcap": "Inicjały", "DE.Views.Toolbar.capBtnInsEquation": "Równanie", "DE.Views.Toolbar.capBtnInsHeader": "Nagłówek stopka", "DE.Views.Toolbar.capBtnInsImage": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.capBtnInsPagebreak": "Przerwy", "DE.Views.Toolbar.capBtnInsShape": "Kształt", "DE.Views.Toolbar.capBtnInsSymbol": "Symbole", "DE.Views.Toolbar.capBtnInsTable": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.capBtnInsTextart": "Text Art", "DE.Views.Toolbar.capBtnInsTextbox": "Tekst", "DE.Views.Toolbar.capBtnLineNumbers": "<PERSON>ume<PERSON> w<PERSON>zy", "DE.Views.Toolbar.capBtnMargins": "Mar<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.capBtnPageOrient": "Orientacja", "DE.Views.Toolbar.capBtnPageSize": "Rozmiar", "DE.Views.Toolbar.capBtnWatermark": "Znak wodny", "DE.Views.Toolbar.capImgAlign": "Wyrównaj", "DE.Views.Toolbar.capImgBackward": "Przenieś do tyłu", "DE.Views.Toolbar.capImgForward": "Przenieś do przodu", "DE.Views.Toolbar.capImgGroup": "Grupa", "DE.Views.Toolbar.capImgWrapping": "Zawijanie", "DE.Views.Toolbar.mniCapitalizeWords": "Jak Nazwy W<PERSON>ne", "DE.Views.Toolbar.mniCustomTable": "Wstaw tabelę niestandardową", "DE.Views.Toolbar.mniDrawTable": "<PERSON><PERSON><PERSON><PERSON> tabeli", "DE.Views.Toolbar.mniEditControls": "Ustawienia sterowania", "DE.Views.Toolbar.mniEditDropCap": "Inicjały Ustawienia", "DE.Views.Toolbar.mniEditFooter": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.mniEditHeader": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.mniEraseTable": "<PERSON><PERSON><PERSON><PERSON> ta<PERSON>", "DE.Views.Toolbar.mniFromFile": "Z pliku", "DE.Views.Toolbar.mniFromStorage": "Z magazynu", "DE.Views.Toolbar.mniFromUrl": "Z adresu URL", "DE.Views.Toolbar.mniHiddenBorders": "<PERSON><PERSON><PERSON><PERSON> o<PERSON> tabeli", "DE.Views.Toolbar.mniHiddenChars": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.mniHighlightControls": "<PERSON><PERSON>", "DE.Views.Toolbar.mniImageFromFile": "Obraz z pliku", "DE.Views.Toolbar.mniImageFromStorage": "Obraz z magazynu", "DE.Views.Toolbar.mniImageFromUrl": "Obraz z URL", "DE.Views.Toolbar.mniInsertSSE": "Wstaw arkusz kalkulacyjny", "DE.Views.Toolbar.mniLowerCase": "małe litery", "DE.Views.Toolbar.mniRemoveFooter": "<PERSON><PERSON><PERSON> stopkę", "DE.Views.Toolbar.mniRemoveHeader": "Us<PERSON>ń nagłowek", "DE.Views.Toolbar.mniSentenceCase": "Jak w zdaniu", "DE.Views.Toolbar.mniTextToTable": "Konwertuj tekst na tabelę", "DE.Views.Toolbar.mniToggleCase": "zAMIANA nA mAŁE/wIELKIE", "DE.Views.Toolbar.mniUpperCase": "WIELKI LITERY", "DE.Views.Toolbar.strMenuNoFill": "Brak wypełnienia", "DE.Views.Toolbar.textAutoColor": "Automatyczny", "DE.Views.Toolbar.textBold": "Pogrubienie", "DE.Views.Toolbar.textBottom": "Dół:", "DE.Views.Toolbar.textChangeLevel": "Zmień Poziom Listy", "DE.Views.Toolbar.textCheckboxControl": "Pole wyboru", "DE.Views.Toolbar.textColumnsCustom": "Niestandardowe kolumny", "DE.Views.Toolbar.textColumnsLeft": "<PERSON>", "DE.Views.Toolbar.textColumnsOne": "<PERSON><PERSON>", "DE.Views.Toolbar.textColumnsRight": "Z prawej", "DE.Views.Toolbar.textColumnsThree": "Trzy", "DE.Views.Toolbar.textColumnsTwo": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textComboboxControl": "Pole kombi", "DE.Views.Toolbar.textContinuous": "Ciągłe", "DE.Views.Toolbar.textContPage": "Ciągły", "DE.Views.Toolbar.textCustomLineNumbers": "Opcje numeracji w<PERSON>zy", "DE.Views.Toolbar.textDateControl": "Data", "DE.Views.Toolbar.textDropdownControl": "Lista rozwijana", "DE.Views.Toolbar.textEditWatermark": "Własny znak wodny", "DE.Views.Toolbar.textEvenPage": "<PERSON><PERSON><PERSON><PERSON> strona", "DE.Views.Toolbar.textInMargin": "W marginesie", "DE.Views.Toolbar.textInsColumnBreak": "Wstaw podział kolumny", "DE.Views.Toolbar.textInsertPageCount": "Wstaw liczbę stron", "DE.Views.Toolbar.textInsertPageNumber": "Wstaw numer strony", "DE.Views.Toolbar.textInsPageBreak": "Wstaw podział strony", "DE.Views.Toolbar.textInsSectionBreak": "Wstaw podział sekcji", "DE.Views.Toolbar.textInText": "W tekście", "DE.Views.Toolbar.textItalic": "Ku<PERSON>ywa", "DE.Views.Toolbar.textLandscape": "Pozioma", "DE.Views.Toolbar.textLeft": "Lewo:", "DE.Views.Toolbar.textListSettings": "Ustawienia listy", "DE.Views.Toolbar.textMarginsLast": "Ostatni niestandardowy", "DE.Views.Toolbar.textMarginsModerate": "Umiarkowany", "DE.Views.Toolbar.textMarginsNarrow": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textMarginsNormal": "Normalny", "DE.Views.Toolbar.textMarginsUsNormal": "Normalny US", "DE.Views.Toolbar.textMarginsWide": "Szeroki", "DE.Views.Toolbar.textNewColor": "Nowy niestandardowy kolor", "DE.Views.Toolbar.textNextPage": "<PERSON><PERSON><PERSON><PERSON><PERSON> strona", "DE.Views.Toolbar.textNoHighlight": "Brak wyróżnienia", "DE.Views.Toolbar.textNone": "Żaden", "DE.Views.Toolbar.textOddPage": "<PERSON><PERSON><PERSON><PERSON><PERSON> strona", "DE.Views.Toolbar.textPageMarginsCustom": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textPageSizeCustom": "<PERSON>ła<PERSON><PERSON> rozmiar strony", "DE.Views.Toolbar.textPictureControl": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textPlainControl": "Zwykły Tekst", "DE.Views.Toolbar.textPortrait": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textRemoveControl": "Usuń kontrolę treści", "DE.Views.Toolbar.textRemWatermark": "Usuń znak wodny", "DE.Views.Toolbar.textRestartEachPage": "Roz<PERSON>cz<PERSON>j nową stronę", "DE.Views.Toolbar.textRestartEachSection": "Rozpocznij nową sekcję", "DE.Views.Toolbar.textRichControl": "Tekst sformatowany", "DE.Views.Toolbar.textRight": "Prawo:", "DE.Views.Toolbar.textStrikeout": "Skreślenie", "DE.Views.Toolbar.textStyleMenuDelete": "Usuń styl", "DE.Views.Toolbar.textStyleMenuDeleteAll": "Usuń wszystkie niestandardowe style", "DE.Views.Toolbar.textStyleMenuNew": "Nowy styl od wyboru", "DE.Views.Toolbar.textStyleMenuRestore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textStyleMenuRestoreAll": "Przywróć wszystko do domyślnych stylów", "DE.Views.Toolbar.textStyleMenuUpdate": "Aktualizuj z wyboru", "DE.Views.Toolbar.textSubscript": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textSuperscript": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textSuppressForCurrentParagraph": "Pomiń w bieżącym akapicie", "DE.Views.Toolbar.textTabCollaboration": "Współpraca", "DE.Views.Toolbar.textTabFile": "Plik", "DE.Views.Toolbar.textTabHome": "Narzędzia główne", "DE.Views.Toolbar.textTabInsert": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textTabLayout": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textTabLinks": "Odwołania", "DE.Views.Toolbar.textTabProtect": "Ochrona", "DE.Views.Toolbar.textTabReview": "Przegląd", "DE.Views.Toolbar.textTabView": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textTitleError": "Błąd", "DE.Views.Toolbar.textToCurrent": "Do aktualnej pozycji", "DE.Views.Toolbar.textTop": "Góra:", "DE.Views.Toolbar.textUnderline": "Podkreślenie", "DE.Views.Toolbar.tipAlignCenter": "Wyrównaj do środka", "DE.Views.Toolbar.tipAlignJust": "Wyjustowany", "DE.Views.Toolbar.tipAlignLeft": "Wyrównaj do lewej", "DE.Views.Toolbar.tipAlignRight": "Wyrównaj do prawej", "DE.Views.Toolbar.tipBack": "Powró<PERSON>", "DE.Views.Toolbar.tipBlankPage": "Wstaw pustą stronę", "DE.Views.Toolbar.tipChangeCase": "Zmień wielkość liter", "DE.Views.Toolbar.tipChangeChart": "Zmień typ wykresu", "DE.Views.Toolbar.tipClearStyle": "Wy<PERSON><PERSON><PERSON><PERSON> style", "DE.Views.Toolbar.tipColorSchemas": "Zmień schemat kolorów", "DE.Views.Toolbar.tipColumns": "Wstaw kolumny", "DE.Views.Toolbar.tipControls": "Wstaw kontrolki treści", "DE.Views.Toolbar.tipCopy": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipCopyStyle": "<PERSON><PERSON><PERSON><PERSON> styl", "DE.Views.Toolbar.tipCut": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipDateTime": "Wstaw aktualną datę i godzinę", "DE.Views.Toolbar.tipDecFont": "Zmniejsz rozmiar czcionki", "DE.Views.Toolbar.tipDecPrLeft": "Zmniejsz wcięcie", "DE.Views.Toolbar.tipDropCap": "Wstaw dużą pierwszą literę", "DE.Views.Toolbar.tipEditHeader": "Edytuj nagłówek lub stopkę", "DE.Views.Toolbar.tipFontColor": "<PERSON><PERSON>", "DE.Views.Toolbar.tipFontName": "Czcionka", "DE.Views.Toolbar.tipFontSize": "Rozmiar <PERSON>ki", "DE.Views.Toolbar.tipHighlightColor": "<PERSON><PERSON>", "DE.Views.Toolbar.tipImgAlign": "Wyrównaj obiekty", "DE.Views.Toolbar.tipImgGroup": "Grupuj obiekty", "DE.Views.Toolbar.tipImgWrapping": "Zawijaj tekst", "DE.Views.Toolbar.tipIncFont": "Zwiększ rozmiar czcionki", "DE.Views.Toolbar.tipIncPrLeft": "Zwięks<PERSON> wcięcie", "DE.Views.Toolbar.tipInsertChart": "Wstaw wykres", "DE.Views.Toolbar.tipInsertEquation": "Wstaw równanie", "DE.Views.Toolbar.tipInsertImage": "Wstaw obraz", "DE.Views.Toolbar.tipInsertNum": "Wstaw numer strony", "DE.Views.Toolbar.tipInsertShape": "Wstaw kształt", "DE.Views.Toolbar.tipInsertSymbol": "Wstaw symbol", "DE.Views.Toolbar.tipInsertTable": "Wstaw tabelę", "DE.Views.Toolbar.tipInsertText": "Wstaw pole tekstowe", "DE.Views.Toolbar.tipInsertTextArt": "Wstaw tekst", "DE.Views.Toolbar.tipLineNumbers": "Pokaż numery wierszy", "DE.Views.Toolbar.tipLineSpace": "Rozstaw wierszy akapitu", "DE.Views.Toolbar.tipMailRecepients": "Korespondencja <PERSON>a", "DE.Views.Toolbar.tipMarkers": "Lista punktowa", "DE.Views.Toolbar.tipMultilevels": "Lista wielopoziomowa", "DE.Views.Toolbar.tipNumbers": "Lista numeryczna", "DE.Views.Toolbar.tipPageBreak": "Wstawianie strony lub podziału sekcji", "DE.Views.Toolbar.tipPageMargins": "<PERSON><PERSON><PERSON>y strony", "DE.Views.Toolbar.tipPageOrient": "<PERSON><PERSON><PERSON> s<PERSON>", "DE.Views.Toolbar.tipPageSize": "<PERSON><PERSON><PERSON><PERSON> strony", "DE.Views.Toolbar.tipParagraphStyle": "<PERSON><PERSON>", "DE.Views.Toolbar.tipPaste": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipPrColor": "<PERSON><PERSON> t<PERSON> akapit<PERSON>", "DE.Views.Toolbar.tipPrint": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipRedo": "Ponów", "DE.Views.Toolbar.tipSave": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipSaveCoauth": "Zapisz swoje zmiany, aby inni użytkownicy mogli je zobaczyć.", "DE.Views.Toolbar.tipSelectAll": "<PERSON><PERSON><PERSON><PERSON> wsyzstko", "DE.Views.Toolbar.tipSendBackward": "Przenieś do tyłu", "DE.Views.Toolbar.tipSendForward": "Przenieś do przodu", "DE.Views.Toolbar.tipShowHiddenChars": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipSynchronize": "Dokument został zmieniony przez innego użytkownika. Kliknij, aby zapisać swoje zmiany i ponownie załadować zmiany.", "DE.Views.Toolbar.tipUndo": "Cof<PERSON>j", "DE.Views.Toolbar.tipWatermark": "Edytuj znak wodny", "DE.Views.Toolbar.txtDistribHor": "Rozdziel poziomo", "DE.Views.Toolbar.txtDistribVert": "Rozdziel pionowo", "DE.Views.Toolbar.txtMarginAlign": "Wyrównaj do marginesów", "DE.Views.Toolbar.txtObjectsAlign": "Wyrównaj zaznaczone obiekty", "DE.Views.Toolbar.txtPageAlign": "Wyrównaj do strony", "DE.Views.Toolbar.txtScheme1": "Biuro", "DE.Views.Toolbar.txtScheme10": "Mediana", "DE.Views.Toolbar.txtScheme11": "Metro", "DE.Views.Toolbar.txtScheme12": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.txtScheme13": "Zasob<PERSON>", "DE.Views.Toolbar.txtScheme14": "Oriel", "DE.Views.Toolbar.txtScheme15": "Początek", "DE.Views.Toolbar.txtScheme16": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.txtScheme17": "Przesilenie", "DE.Views.Toolbar.txtScheme18": "Techniczny", "DE.Views.Toolbar.txtScheme19": "Trek", "DE.Views.Toolbar.txtScheme2": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.txtScheme20": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.txtScheme21": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.txtScheme22": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "DE.Views.Toolbar.txtScheme3": "Apex", "DE.Views.Toolbar.txtScheme4": "Aspekt", "DE.Views.Toolbar.txtScheme5": "Obywatelski", "DE.Views.Toolbar.txtScheme6": "Zbiegowisko", "DE.Views.Toolbar.txtScheme7": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.txtScheme8": "Przepływ", "DE.Views.Toolbar.txtScheme9": "Odlewnia", "DE.Views.ViewTab.textAlwaysShowToolbar": "Zawsze pokazuj pasek narzędzi", "DE.Views.ViewTab.textFitToPage": "Dopasuj do strony", "DE.Views.ViewTab.textFitToWidth": "Dopasuj do szerokości", "DE.Views.ViewTab.textInterfaceTheme": "Motyw interfejsu", "DE.Views.ViewTab.textNavigation": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ViewTab.textOutline": "Nagłówki", "DE.Views.ViewTab.textRulers": "Zasady", "DE.Views.ViewTab.textStatusBar": "Pasek stanu", "DE.Views.ViewTab.textZoom": "Powiększenie", "DE.Views.WatermarkSettingsDialog.textAuto": "Automatyczny", "DE.Views.WatermarkSettingsDialog.textBold": "Pogrubienie", "DE.Views.WatermarkSettingsDialog.textColor": "<PERSON><PERSON>", "DE.Views.WatermarkSettingsDialog.textDiagonal": "Przekątna", "DE.Views.WatermarkSettingsDialog.textFont": "Czcionka", "DE.Views.WatermarkSettingsDialog.textFromFile": "Z pliku", "DE.Views.WatermarkSettingsDialog.textFromStorage": "Z magazynu", "DE.Views.WatermarkSettingsDialog.textFromUrl": "Z adresu URL", "DE.Views.WatermarkSettingsDialog.textHor": "Poziomy", "DE.Views.WatermarkSettingsDialog.textImageW": "Znak wodny obrazu", "DE.Views.WatermarkSettingsDialog.textItalic": "Ku<PERSON>ywa", "DE.Views.WatermarkSettingsDialog.textLanguage": "Język", "DE.Views.WatermarkSettingsDialog.textLayout": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.WatermarkSettingsDialog.textNone": "Brak", "DE.Views.WatermarkSettingsDialog.textScale": "<PERSON><PERSON><PERSON>", "DE.Views.WatermarkSettingsDialog.textSelect": "<PERSON><PERSON><PERSON><PERSON> obraz", "DE.Views.WatermarkSettingsDialog.textStrikeout": "Przekreślenie", "DE.Views.WatermarkSettingsDialog.textText": "Tekst", "DE.Views.WatermarkSettingsDialog.textTextW": "Tekstowy znak wodny", "DE.Views.WatermarkSettingsDialog.textTitle": "Ustawienia znaków wodnych", "DE.Views.WatermarkSettingsDialog.textTransparency": "Półprzezroczysty", "DE.Views.WatermarkSettingsDialog.textUnderline": "Podkreślenie", "DE.Views.WatermarkSettingsDialog.tipFontName": "Nazwa czcionki", "DE.Views.WatermarkSettingsDialog.tipFontSize": "Rozmiar <PERSON>ki"}