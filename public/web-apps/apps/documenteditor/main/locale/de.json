{"Common.Controllers.Chat.notcriticalErrorTitle": "Achtung", "Common.Controllers.Chat.textEnterMessage": "<PERSON><PERSON><PERSON> Si<PERSON> Ihre Nachricht hier ein", "Common.Controllers.ExternalDiagramEditor.textAnonymous": "Anonym", "Common.Controllers.ExternalDiagramEditor.textClose": "Schließen", "Common.Controllers.ExternalDiagramEditor.warningText": "Das Objekt ist deaktiviert, weil es momentan von einem anderen <PERSON>utzer bearbeitet wird.", "Common.Controllers.ExternalDiagramEditor.warningTitle": "Achtung", "Common.Controllers.ExternalMergeEditor.textAnonymous": "Anonym", "Common.Controllers.ExternalMergeEditor.textClose": "Schließen", "Common.Controllers.ExternalMergeEditor.warningText": "Das Objekt ist deaktiviert, weil es momentan von einem anderen <PERSON>utzer bearbeitet wird.", "Common.Controllers.ExternalMergeEditor.warningTitle": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalOleEditor.textAnonymous": "Anonym", "Common.Controllers.ExternalOleEditor.textClose": "Schließen", "Common.Controllers.ExternalOleEditor.warningText": "Das Objekt ist deaktiviert, weil es momentan von einem anderen <PERSON>utzer bearbeitet wird.", "Common.Controllers.ExternalOleEditor.warningTitle": "Achtung", "Common.Controllers.History.notcriticalErrorTitle": "Achtung", "Common.Controllers.ReviewChanges.textAcceptBeforeCompare": "Um Dokumente zu vergleichen, gelten alle nachverfolgten Änderungen als akzeptiert. Möchten Sie weiter machen?", "Common.Controllers.ReviewChanges.textAtLeast": "Mindestens", "Common.Controllers.ReviewChanges.textAuto": "Automatisch", "Common.Controllers.ReviewChanges.textBaseline": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Controllers.ReviewChanges.textBold": "<PERSON><PERSON>", "Common.Controllers.ReviewChanges.textBreakBefore": "Seitenumbruch oberhalb", "Common.Controllers.ReviewChanges.textCaps": "Alle Großbuchstaben", "Common.Controllers.ReviewChanges.textCenter": "<PERSON><PERSON><PERSON>", "Common.Controllers.ReviewChanges.textChar": "<PERSON><PERSON><PERSON>-<PERSON><PERSON>e", "Common.Controllers.ReviewChanges.textChart": "Diagramm", "Common.Controllers.ReviewChanges.textColor": "Schriftfarbe", "Common.Controllers.ReviewChanges.textContextual": "Kein Abstand zwischen Absätzen gleicher Formatierung", "Common.Controllers.ReviewChanges.textDeleted": "<b>Gelöscht:</b>", "Common.Controllers.ReviewChanges.textDStrikeout": "Doppeltes Durchstreichen", "Common.Controllers.ReviewChanges.textEquation": "Gleichung", "Common.Controllers.ReviewChanges.textExact": "<PERSON><PERSON>", "Common.Controllers.ReviewChanges.textFirstLine": "<PERSON><PERSON><PERSON>", "Common.Controllers.ReviewChanges.textFontSize": "Schriftgrad", "Common.Controllers.ReviewChanges.textFormatted": "<PERSON><PERSON><PERSON>", "Common.Controllers.ReviewChanges.textHighlight": "Texthervorhebungsfarbe", "Common.Controllers.ReviewChanges.textImage": "Bild", "Common.Controllers.ReviewChanges.textIndentLeft": "Einzug links", "Common.Controllers.ReviewChanges.textIndentRight": "Einzug rechts", "Common.Controllers.ReviewChanges.textInserted": "<b>Eingefügt:</b>", "Common.Controllers.ReviewChanges.textItalic": "<PERSON><PERSON><PERSON>", "Common.Controllers.ReviewChanges.textJustify": "Zielgruppengerecht ausrichten", "Common.Controllers.ReviewChanges.textKeepLines": "Absatz zusammenhalten", "Common.Controllers.ReviewChanges.textKeepNext": "Absätze nicht trennen", "Common.Controllers.ReviewChanges.textLeft": "Linksbündig ausrichten", "Common.Controllers.ReviewChanges.textLineSpacing": "Zeilenabstand:", "Common.Controllers.ReviewChanges.textMultiple": "<PERSON><PERSON><PERSON><PERSON>", "Common.Controllers.ReviewChanges.textNoBreakBefore": "Keinen Seitenumbruch vorher", "Common.Controllers.ReviewChanges.textNoContextual": "Intervall zwischen den Absätzen im gleichen Stil hinzufügen", "Common.Controllers.ReviewChanges.textNoKeepLines": "Halten Sie Linien nicht zusammen", "Common.Controllers.ReviewChanges.textNoKeepNext": "Mit der nächsten nicht halten", "Common.Controllers.ReviewChanges.textNot": "<PERSON><PERSON>", "Common.Controllers.ReviewChanges.textNoWidow": "<PERSON><PERSON> \"Widow-Control\"", "Common.Controllers.ReviewChanges.textNum": "Nummerierung ändern", "Common.Controllers.ReviewChanges.textOff": "{0} verwendet die Nachverfolgung von Änderungen nicht mehr.", "Common.Controllers.ReviewChanges.textOffGlobal": "{0} hat die Nachverfolgung von Änderungen für alle deaktiviert.", "Common.Controllers.ReviewChanges.textOn": "{0} verwendet jetzt die Nachverfolgung von Änderungen.", "Common.Controllers.ReviewChanges.textOnGlobal": "{0} hat die Nachverfolgung von Änderungen für alle aktiviert.", "Common.Controllers.ReviewChanges.textParaDeleted": "<b><PERSON><PERSON><PERSON>z <PERSON></b>", "Common.Controllers.ReviewChanges.textParaFormatted": "Absatz ist formatiert", "Common.Controllers.ReviewChanges.textParaInserted": "<b><PERSON><PERSON><PERSON><PERSON> e<PERSON>fügt</b>", "Common.Controllers.ReviewChanges.textParaMoveFromDown": "<b>Nach unten verschoben</b>", "Common.Controllers.ReviewChanges.textParaMoveFromUp": "<b><PERSON>ch oben verschoben</b>", "Common.Controllers.ReviewChanges.textParaMoveTo": "<b>Vers<PERSON>ben</b>", "Common.Controllers.ReviewChanges.textPosition": "Position", "Common.Controllers.ReviewChanges.textRight": "Rechtsbündig ausrichten", "Common.Controllers.ReviewChanges.textShape": "Form", "Common.Controllers.ReviewChanges.textShd": "Hintergrundfarbe", "Common.Controllers.ReviewChanges.textShow": "Änderungen anzeigen:", "Common.Controllers.ReviewChanges.textSmallCaps": "Kapitälchen", "Common.Controllers.ReviewChanges.textSpacing": "Abstand", "Common.Controllers.ReviewChanges.textSpacingAfter": "Abstand nach", "Common.Controllers.ReviewChanges.textSpacingBefore": "Abstand vor", "Common.Controllers.ReviewChanges.textStrikeout": "Durchgestrichen", "Common.Controllers.ReviewChanges.textSubScript": "Tiefgestellt", "Common.Controllers.ReviewChanges.textSuperScript": "Hochgestellt", "Common.Controllers.ReviewChanges.textTableChanged": "<b>Tabelleneinstellungen</b>", "Common.Controllers.ReviewChanges.textTableRowsAdd": "<b> Tabellenzeilen hinzugefügt </b>", "Common.Controllers.ReviewChanges.textTableRowsDel": "<b>Tabellenzeilen gel<PERSON>t</b>", "Common.Controllers.ReviewChanges.textTabs": "Registerkarten ä<PERSON>n", "Common.Controllers.ReviewChanges.textTitleComparison": "Vergleichseinstellungen", "Common.Controllers.ReviewChanges.textUnderline": "<PERSON><PERSON><PERSON><PERSON>", "Common.Controllers.ReviewChanges.textUrl": "Dokument-URL einfügen", "Common.Controllers.ReviewChanges.textWidow": "<PERSON>", "Common.Controllers.ReviewChanges.textWord": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textArea": "Fläche", "Common.define.chartData.textAreaStacked": "Gestapelte Fläche", "Common.define.chartData.textAreaStackedPer": "100% Gestapelte Fläche", "Common.define.chartData.textBar": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textBarNormal": "Gruppierte Säule", "Common.define.chartData.textBarNormal3d": "Gruppierte 3D-Säule", "Common.define.chartData.textBarNormal3dPerspective": "3D-Säule", "Common.define.chartData.textBarStacked": "Gestapelte Säulen", "Common.define.chartData.textBarStacked3d": "Gestapelte 3D-Säule", "Common.define.chartData.textBarStackedPer": "100% Gestapelte Säule", "Common.define.chartData.textBarStackedPer3d": "3-D 100% Gestapelte Säule", "Common.define.chartData.textCharts": "Diagramme", "Common.define.chartData.textColumn": "<PERSON>lt<PERSON>", "Common.define.chartData.textCombo": "Verbund", "Common.define.chartData.textComboAreaBar": "Gestapelte Flächen/Gruppierte Säulen", "Common.define.chartData.textComboBarLine": "Gruppierte Säulen - Linie", "Common.define.chartData.textComboBarLineSecondary": "Gruppierte Säulen/Linien auf der Sekundärachse", "Common.define.chartData.textComboCustom": "Benutzerdefinierte Kombination", "Common.define.chartData.textDoughnut": "Ring", "Common.define.chartData.textHBarNormal": "Gruppierte Balken", "Common.define.chartData.textHBarNormal3d": "Gruppierte 3D-Balken", "Common.define.chartData.textHBarStacked": "Gestapelte Balken", "Common.define.chartData.textHBarStacked3d": "Gestapelte 3D-Balken", "Common.define.chartData.textHBarStackedPer": "100% Gestapelte Balken", "Common.define.chartData.textHBarStackedPer3d": "3-D 100% Gestapelte Balken", "Common.define.chartData.textLine": "<PERSON><PERSON>", "Common.define.chartData.textLine3d": "3D-<PERSON><PERSON>", "Common.define.chartData.textLineMarker": "<PERSON><PERSON> mit Datenpunkten", "Common.define.chartData.textLineStacked": "Gestapelte Linie", "Common.define.chartData.textLineStackedMarker": "Gestapelte Linie mit Datenpunkten", "Common.define.chartData.textLineStackedPer": "100% Gestapelte Linie", "Common.define.chartData.textLineStackedPerMarker": "100% Gestapelte Linie mit Datenpunkten", "Common.define.chartData.textPie": "Kreis", "Common.define.chartData.textPie3d": "3D-Kreis", "Common.define.chartData.textPoint": "<PERSON><PERSON> (XY)", "Common.define.chartData.textScatter": "Punkte", "Common.define.chartData.textScatterLine": "Punkte mit geraden Linien", "Common.define.chartData.textScatterLineMarker": "Punkte mit geraden Linien und Datenpunkten", "Common.define.chartData.textScatterSmooth": "Punkte mit interpolierten Linien", "Common.define.chartData.textScatterSmoothMarker": "Punkte mit interpolierten Linien und Datenpunkten", "Common.define.chartData.textStock": "<PERSON><PERSON>", "Common.define.chartData.textSurface": "Oberfläche", "Common.define.smartArt.textAccentedPicture": "Bild mit Akzenten", "Common.define.smartArt.textAccentProcess": "Akzentprozess", "Common.define.smartArt.textAlternatingFlow": "Alternierender Fluss", "Common.define.smartArt.textAlternatingHexagons": "Alternierende Sechsecke", "Common.define.smartArt.textAlternatingPictureBlocks": "Alternierende Bildblöcke", "Common.define.smartArt.textAlternatingPictureCircles": "Alternierende Bildblöcke", "Common.define.smartArt.textArchitectureLayout": "Architekturlayout", "Common.define.smartArt.textArrowRibbon": "Pfeilband", "Common.define.smartArt.textAscendingPictureAccentProcess": "Aufsteigender Prozess mit Bildakzenten", "Common.define.smartArt.textBalance": "Kontostand", "Common.define.smartArt.textBasicBendingProcess": "Einfacher umgebrochener Prozess", "Common.define.smartArt.textBasicBlockList": "Einfache Blockliste", "Common.define.smartArt.textBasicChevronProcess": "Einfacher Chevronprozess", "Common.define.smartArt.textBasicCycle": "Einfacher Kreis", "Common.define.smartArt.textBasicMatrix": "Einfache Matrix", "Common.define.smartArt.textBasicPie": "Einfaches Kreisdiagramm", "Common.define.smartArt.textBasicProcess": "Einfacher Prozess", "Common.define.smartArt.textBasicPyramid": "Einfache Pyramide", "Common.define.smartArt.textBasicRadial": "Einfaches Radial", "Common.define.smartArt.textBasicTarget": "Einfaches Ziel", "Common.define.smartArt.textBasicTimeline": "Einfache Zeitachse", "Common.define.smartArt.textBasicVenn": "Einfaches Venn", "Common.define.smartArt.textBendingPictureAccentList": "Umgebrochene Bildakzentliste", "Common.define.smartArt.textBendingPictureBlocks": "Umgebrochene Bildblöcke", "Common.define.smartArt.textBendingPictureCaption": "Umgebrochene Bildbeschriftung", "Common.define.smartArt.textBendingPictureCaptionList": "Umgebrochene Bildbeschriftungsliste", "Common.define.smartArt.textBendingPictureSemiTranparentText": "Umgebrochener halbtransparenter Bildtext", "Common.define.smartArt.textBlockCycle": "Blockkreis", "Common.define.smartArt.textBubblePictureList": "Blasenbildliste", "Common.define.smartArt.textCaptionedPictures": "Bilder mit Beschriftungen", "Common.define.smartArt.textChevronAccentProcess": "Chevronakzentprozess", "Common.define.smartArt.textChevronList": "Chevronliste", "Common.define.smartArt.textCircleAccentTimeline": "Zeitachse mit Kreisakzent", "Common.define.smartArt.textCircleArrowProcess": "Kreisförmiger Pfeilprozess", "Common.define.smartArt.textCirclePictureHierarchy": "Bilderhierarchie mit Kreisakzent", "Common.define.smartArt.textCircleProcess": "Kreisprozess", "Common.define.smartArt.textCircleRelationship": "Kreisbeziehung", "Common.define.smartArt.textCircularBendingProcess": "Kreisförmiger umgebrochener Prozess", "Common.define.smartArt.textCircularPictureCallout": "Bildlegende mit Kreisakzent", "Common.define.smartArt.textClosedChevronProcess": "Geschlossener Chevronprozess", "Common.define.smartArt.textContinuousArrowProcess": "Fortlaufender Pfeilprozess", "Common.define.smartArt.textContinuousBlockProcess": "Fortlaufender Blockprozess", "Common.define.smartArt.textContinuousCycle": "Fortlaufender Kreis", "Common.define.smartArt.textContinuousPictureList": "Fortlaufende Bildliste", "Common.define.smartArt.textConvergingArrows": "Zusammenlaufende Pfeile", "Common.define.smartArt.textConvergingRadial": "Zusammenlaufendes Radial", "Common.define.smartArt.textConvergingText": "Zusammenlaufender Text", "Common.define.smartArt.textCounterbalanceArrows": "Gegengewichtspfeile", "Common.define.smartArt.textCycle": "Z<PERSON><PERSON>", "Common.define.smartArt.textCycleMatrix": "Kreismatrix", "Common.define.smartArt.textDescendingBlockList": "Absteigende Blockliste", "Common.define.smartArt.textDescendingProcess": "<PERSON><PERSON><PERSON><PERSON><PERSON> Prozess", "Common.define.smartArt.textDetailedProcess": "Detaillierter Prozess", "Common.define.smartArt.textDivergingArrows": "Auseinanderlaufende Pfeile", "Common.define.smartArt.textDivergingRadial": "Auseinanderlaufendes Radial", "Common.define.smartArt.textEquation": "Gleichung", "Common.define.smartArt.textFramedTextPicture": "Umrahmte Textgrafik", "Common.define.smartArt.textFunnel": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textGear": "Zahnrad", "Common.define.smartArt.textGridMatrix": "Rastermatrix", "Common.define.smartArt.textGroupedList": "Gruppierte Liste", "Common.define.smartArt.textHalfCircleOrganizationChart": "Halbkreisorganigramm", "Common.define.smartArt.textHexagonCluster": "Sechseck-Cluster", "Common.define.smartArt.textHexagonRadial": "Sechseck Radial", "Common.define.smartArt.textHierarchy": "Hierarchie", "Common.define.smartArt.textHierarchyList": "Hierarchieliste", "Common.define.smartArt.textHorizontalBulletList": "Horizontale Aufzählungsliste", "Common.define.smartArt.textHorizontalHierarchy": "Horizontale Hierarchie", "Common.define.smartArt.textHorizontalLabeledHierarchy": "Horizontal beschriftete Hierarchie", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "Horizontale Hierarchie mit mehreren Ebenen", "Common.define.smartArt.textHorizontalOrganizationChart": "Horizontales Organigramm", "Common.define.smartArt.textHorizontalPictureList": "Horizontale Bildliste", "Common.define.smartArt.textIncreasingArrowProcess": "Wachsender Pfeil-Prozess", "Common.define.smartArt.textIncreasingCircleProcess": "Wachsender Kreis-Prozess", "Common.define.smartArt.textInterconnectedBlockProcess": "Vernetzter Blockprozess", "Common.define.smartArt.textInterconnectedRings": "Verbundene Ringe", "Common.define.smartArt.textInvertedPyramid": "Umgekehrte Pyramide", "Common.define.smartArt.textLabeledHierarchy": "Beschriftete Hierarchie", "Common.define.smartArt.textLinearVenn": "Lineares Venn", "Common.define.smartArt.textLinedList": "Liste mit Linien", "Common.define.smartArt.textList": "Liste", "Common.define.smartArt.textMatrix": "Matrix", "Common.define.smartArt.textMultidirectionalCycle": "Kreis mit mehreren Richtungen", "Common.define.smartArt.textNameAndTitleOrganizationChart": "Organigramm mit Name und Titel", "Common.define.smartArt.textNestedTarget": "Geschachteltes Ziel", "Common.define.smartArt.textNondirectionalCycle": "Richtungsloser Kreis", "Common.define.smartArt.textOpposingArrows": "Entgegengesetzte Pfeile", "Common.define.smartArt.textOpposingIdeas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textOrganizationChart": "Organigramm", "Common.define.smartArt.textOther": "Sonstiges", "Common.define.smartArt.textPhasedProcess": "Phasenprozess", "Common.define.smartArt.textPicture": "Bild", "Common.define.smartArt.textPictureAccentBlocks": "Bildakzentblöcke", "Common.define.smartArt.textPictureAccentList": "Bildakzentliste", "Common.define.smartArt.textPictureAccentProcess": "Bildakzentprozess", "Common.define.smartArt.textPictureCaptionList": "Bildbeschriftungsliste", "Common.define.smartArt.textPictureFrame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textPictureGrid": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textPictureLineup": "Bildanordnung", "Common.define.smartArt.textPictureOrganizationChart": "Bildorganigramm", "Common.define.smartArt.textPictureStrips": "Bildstreifen", "Common.define.smartArt.textPieProcess": "Kreisdiagrammprozess", "Common.define.smartArt.textPlusAndMinus": "Plus und Minus", "Common.define.smartArt.textProcess": "Prozess", "Common.define.smartArt.textProcessArrows": "Prozesspfeile", "Common.define.smartArt.textProcessList": "Prozessliste", "Common.define.smartArt.textPyramid": "Pyramide", "Common.define.smartArt.textPyramidList": "Pyramidenliste", "Common.define.smartArt.textRadialCluster": "Radialer Cluster", "Common.define.smartArt.textRadialCycle": "Radialkreis", "Common.define.smartArt.textRadialList": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textRadialPictureList": "Radiale Bildliste", "Common.define.smartArt.textRadialVenn": "Radialvenn", "Common.define.smartArt.textRandomToResultProcess": "Zufallsergebnisprozess", "Common.define.smartArt.textRelationship": "Beziehung", "Common.define.smartArt.textRepeatingBendingProcess": "Wiederholter umgebrochener Prozess", "Common.define.smartArt.textReverseList": "Umgekehrte Liste", "Common.define.smartArt.textSegmentedCycle": "Segmentierter Kreis", "Common.define.smartArt.textSegmentedProcess": "Segment<PERSON>ter Prozess", "Common.define.smartArt.textSegmentedPyramid": "Segmentierte Pyramide", "Common.define.smartArt.textSnapshotPictureList": "Momentaufnahme-Bildliste", "Common.define.smartArt.textSpiralPicture": "Spiralförmige Grafik", "Common.define.smartArt.textSquareAccentList": "Liste mit quadratischen Akzenten", "Common.define.smartArt.textStackedList": "Gestapelte Liste", "Common.define.smartArt.textStackedVenn": "Gestapeltes Venn", "Common.define.smartArt.textStaggeredProcess": "Gestaffelter Prozess", "Common.define.smartArt.textStepDownProcess": "Prozess mit absteigenden Schritten", "Common.define.smartArt.textStepUpProcess": "Prozess mit aufsteigenden Schritten", "Common.define.smartArt.textSubStepProcess": "Unterschrittprozess", "Common.define.smartArt.textTabbedArc": "Registerkartenbogen", "Common.define.smartArt.textTableHierarchy": "Tabellenhierarchie", "Common.define.smartArt.textTableList": "Tabellenliste", "Common.define.smartArt.textTabList": "Registerkartenliste", "Common.define.smartArt.textTargetList": "Zielliste", "Common.define.smartArt.textTextCycle": "Textkreis", "Common.define.smartArt.textThemePictureAccent": "Designbildakzent", "Common.define.smartArt.textThemePictureAlternatingAccent": "Alternierender Designbildakzent", "Common.define.smartArt.textThemePictureGrid": "Designbildraster", "Common.define.smartArt.textTitledMatrix": "Betitelte Matrix", "Common.define.smartArt.textTitledPictureAccentList": "Bildakzentliste mit Titel", "Common.define.smartArt.textTitledPictureBlocks": "Titelbildblöcke", "Common.define.smartArt.textTitlePictureLineup": "Titelbildanordnung", "Common.define.smartArt.textTrapezoidList": "Trapezförmige Liste", "Common.define.smartArt.textUpwardArrow": "<PERSON><PERSON><PERSON> nach oben", "Common.define.smartArt.textVaryingWidthList": "Liste mit variabler Breite", "Common.define.smartArt.textVerticalAccentList": "Liste mit vertikalen Akzenten", "Common.define.smartArt.textVerticalArrowList": "Vertical Arrow List", "Common.define.smartArt.textVerticalBendingProcess": "Vertikaler umgebrochener Prozess", "Common.define.smartArt.textVerticalBlockList": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textVerticalBoxList": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textVerticalBracketList": "Liste mit vertikalen Klammerakzenten", "Common.define.smartArt.textVerticalBulletList": "Vert<PERSON><PERSON> Aufzählung", "Common.define.smartArt.textVerticalChevronList": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textVerticalCircleList": "Liste mit vertikalen Kreisakzenten", "Common.define.smartArt.textVerticalCurvedList": "Liste mit vertikalen Kurven", "Common.define.smartArt.textVerticalEquation": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textVerticalPictureAccentList": "Vertikale Bildakzentliste", "Common.define.smartArt.textVerticalPictureList": "<PERSON><PERSON><PERSON><PERSON>ildl<PERSON>", "Common.define.smartArt.textVerticalProcess": "<PERSON><PERSON><PERSON><PERSON>", "Common.Translation.textMoreButton": "<PERSON><PERSON>", "Common.Translation.tipFileLocked": "Das Dokument ist für die Bearbeitung gesperrt. Sie können Änderungen vornehmen und die Datei später als lokale Kopie speichern.", "Common.Translation.tipFileReadOnly": "Das Dokument ist schreibgeschützt und für die Bearbeitung gesperrt. Sie können Änderungen vornehmen und die lokale Kopie später speichern.", "Common.Translation.warnFileLocked": "Die Datei wird in einer anderen App bearbeitet. Si<PERSON> können die Bearbeitung fortsetzen und die Kopie dieser Datei speichern.", "Common.Translation.warnFileLockedBtnEdit": "<PERSON><PERSON>", "Common.Translation.warnFileLockedBtnView": "Schreibgeschützt öffnen", "Common.UI.ButtonColored.textAutoColor": "Automatisch", "Common.UI.ButtonColored.textNewColor": "Benutzerdefinierte Farbe", "Common.UI.Calendar.textApril": "April", "Common.UI.Calendar.textAugust": "August", "Common.UI.Calendar.textDecember": "Dezember", "Common.UI.Calendar.textFebruary": "<PERSON><PERSON><PERSON>", "Common.UI.Calendar.textJanuary": "<PERSON><PERSON><PERSON>", "Common.UI.Calendar.textJuly": "<PERSON><PERSON>", "Common.UI.Calendar.textJune": "<PERSON><PERSON>", "Common.UI.Calendar.textMarch": "<PERSON><PERSON><PERSON>", "Common.UI.Calendar.textMay": "<PERSON>", "Common.UI.Calendar.textMonths": "Monate", "Common.UI.Calendar.textNovember": "November", "Common.UI.Calendar.textOctober": "Oktober", "Common.UI.Calendar.textSeptember": "September", "Common.UI.Calendar.textShortApril": "Apr", "Common.UI.Calendar.textShortAugust": "Aug", "Common.UI.Calendar.textShortDecember": "<PERSON>z", "Common.UI.Calendar.textShortFebruary": "Feb", "Common.UI.Calendar.textShortFriday": "Fr", "Common.UI.Calendar.textShortJanuary": "Jan", "Common.UI.Calendar.textShortJuly": "Jul", "Common.UI.Calendar.textShortJune": "Jun", "Common.UI.Calendar.textShortMarch": "Mrz", "Common.UI.Calendar.textShortMay": "<PERSON>", "Common.UI.Calendar.textShortMonday": "Mo", "Common.UI.Calendar.textShortNovember": "Nov", "Common.UI.Calendar.textShortOctober": "Okt", "Common.UI.Calendar.textShortSaturday": "Sa", "Common.UI.Calendar.textShortSeptember": "Sep", "Common.UI.Calendar.textShortSunday": "Son", "Common.UI.Calendar.textShortThursday": "Do", "Common.UI.Calendar.textShortTuesday": "Di", "Common.UI.Calendar.textShortWednesday": "<PERSON>", "Common.UI.Calendar.textYears": "Jahre", "Common.UI.ComboBorderSize.txtNoBorders": "<PERSON><PERSON>", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "<PERSON><PERSON>", "Common.UI.ComboDataView.emptyComboText": "Keine Formate", "Common.UI.ExtendedColorDialog.addButtonText": "Hinzufügen", "Common.UI.ExtendedColorDialog.textCurrent": "Aktuell", "Common.UI.ExtendedColorDialog.textHexErr": "Der eingegebene Wert ist falsch.<br>Bitte geben Sie einen Wert zwischen 000000 und FFFFFF ein.", "Common.UI.ExtendedColorDialog.textNew": "<PERSON>eu", "Common.UI.ExtendedColorDialog.textRGBErr": "Der eingegebene Wert ist falsch.<br>Bitte geben Sie einen numerischen Wert zwischen 0 und 255 ein.", "Common.UI.HSBColorPicker.textNoColor": "<PERSON><PERSON>", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "Passwort ausblenden", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "Password anzeigen", "Common.UI.SearchBar.textFind": "<PERSON><PERSON>", "Common.UI.SearchBar.tipCloseSearch": "<PERSON><PERSON> sch<PERSON>ßen", "Common.UI.SearchBar.tipNextResult": "Nächstes Ergebnis", "Common.UI.SearchBar.tipOpenAdvancedSettings": "Erweiterte Einstellungen öffnen", "Common.UI.SearchBar.tipPreviousResult": "Vorheriges Ergebnis", "Common.UI.SearchDialog.textHighlight": "<PERSON><PERSON><PERSON><PERSON><PERSON> her<PERSON>", "Common.UI.SearchDialog.textMatchCase": "Groß-/Kleinschreibung beachten", "Common.UI.SearchDialog.textReplaceDef": "Geben Sie den Ersetzungstext ein", "Common.UI.SearchDialog.textSearchStart": "<PERSON><PERSON><PERSON> den Text hier ein", "Common.UI.SearchDialog.textTitle": "<PERSON><PERSON> und ersetzen", "Common.UI.SearchDialog.textTitle2": "<PERSON><PERSON>", "Common.UI.SearchDialog.textWholeWords": "Nur ganze Wörter", "Common.UI.SearchDialog.txtBtnHideReplace": "Ersetzen verbergen", "Common.UI.SearchDialog.txtBtnReplace": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.txtBtnReplaceAll": "<PERSON>e ersetzen", "Common.UI.SynchronizeTip.textDontShow": "<PERSON><PERSON> nicht mehr anzeigen", "Common.UI.SynchronizeTip.textSynchronize": "Das Dokument wurde von einem anderen Benutzer geändert.<br><PERSON><PERSON> klicken hier, um Ihre Änderungen zu speichern und die Aktualisierungen neu zu laden.", "Common.UI.ThemeColorPalette.textRecentColors": "<PERSON><PERSON><PERSON>lich verwendete Farben", "Common.UI.ThemeColorPalette.textStandartColors": "Standardfarben", "Common.UI.ThemeColorPalette.textThemeColors": "Designfarben", "Common.UI.Themes.txtThemeClassicLight": "Klassisch Hell", "Common.UI.Themes.txtThemeContrastDark": "<PERSON><PERSON><PERSON> Ko<PERSON>ras<PERSON>", "Common.UI.Themes.txtThemeDark": "<PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeLight": "Hell", "Common.UI.Themes.txtThemeSystem": "Wie im System", "Common.UI.Window.cancelButtonText": "Abbrechen", "Common.UI.Window.closeButtonText": "Schließen", "Common.UI.Window.noButtonText": "<PERSON><PERSON>", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "Bestätigung", "Common.UI.Window.textDontShow": "<PERSON><PERSON> nicht mehr anzeigen", "Common.UI.Window.textError": "<PERSON><PERSON>", "Common.UI.Window.textInformation": "Information", "Common.UI.Window.textWarning": "Achtung", "Common.UI.Window.yesButtonText": "<PERSON>a", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textCtrl": "Strg", "Common.Utils.String.textShift": "Umschalt", "Common.Views.About.txtAddress": "<PERSON><PERSON><PERSON>:", "Common.Views.About.txtLicensee": "LIZENZNEHMER", "Common.Views.About.txtLicensor": "LIZENZGEBER", "Common.Views.About.txtMail": "E-Mail-Adresse: ", "Common.Views.About.txtPoweredBy": "<PERSON><PERSON><PERSON><PERSON><PERSON> von", "Common.Views.About.txtTel": "Tel.: ", "Common.Views.About.txtVersion": "Version ", "Common.Views.AutoCorrectDialog.textAdd": "Hinzufügen", "Common.Views.AutoCorrectDialog.textApplyText": "Bei der Eingabe anwenden", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Autokorrektur für Text", "Common.Views.AutoCorrectDialog.textAutoFormat": "Automatisches Formatieren während der Eingabe", "Common.Views.AutoCorrectDialog.textBulleted": "Automatische Aufzählungen", "Common.Views.AutoCorrectDialog.textBy": "Nach", "Common.Views.AutoCorrectDialog.textDelete": "Löschen", "Common.Views.AutoCorrectDialog.textDoubleSpaces": "Punkt mit doppeltem Leerzeichen hinzufügen", "Common.Views.AutoCorrectDialog.textFLCells": "Jede Tabellenzelle mit einem Großbuchstaben beginnen", "Common.Views.AutoCorrectDialog.textFLSentence": "Jeden Satz mit einem Großbuchstaben beginnen", "Common.Views.AutoCorrectDialog.textHyperlink": "Internet- und Netzwerkpfade durch Hyperlinks", "Common.Views.AutoCorrectDialog.textHyphens": "<PERSON><PERSON><PERSON><PERSON> (--) mit Gedankens<PERSON>h (—)", "Common.Views.AutoCorrectDialog.textMathCorrect": "Mathematische Autokorrektur", "Common.Views.AutoCorrectDialog.textNumbered": "Automatische nummerierte Listen", "Common.Views.AutoCorrectDialog.textQuotes": "\"Gerade Anführungszeichen\" mit \"geschweifte Anführungszeichen\"", "Common.Views.AutoCorrectDialog.textRecognized": "Erkannte Funktionen", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "Die folgenden Ausdrücke sind erkannte mathematische Funktionen. Diese werden nicht automatisch kursiviert.", "Common.Views.AutoCorrectDialog.textReplace": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textReplaceText": "Bei der Eingabe ersetzen", "Common.Views.AutoCorrectDialog.textReplaceType": "Text bei der Eingabe ersetzen", "Common.Views.AutoCorrectDialog.textReset": "Z<PERSON>ücksetzen", "Common.Views.AutoCorrectDialog.textResetAll": "Zurücksetzen auf die Standardeinstellungen", "Common.Views.AutoCorrectDialog.textRestore": "Wiederherstellen", "Common.Views.AutoCorrectDialog.textTitle": "Automatische Korrektur", "Common.Views.AutoCorrectDialog.textWarnAddRec": "Erkannte Funktionen sollen nur groß- oder kleingeschriebene Buchstaben von A bis Z beinhalten.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "Alle hinzugefügten Ausdrücke werden entfernt und die gelöschten Ausdrücke werden zurückgestellt. Möchten Sie fortsetzen?", "Common.Views.AutoCorrectDialog.warnReplace": "Es gibt schon einen Autokorrektur-Eintrag für %1. Möchten Sie dieses ersetzen?", "Common.Views.AutoCorrectDialog.warnReset": "Hinzugefügte Autokorrektur wird entfernt und geänderte Autokorrektur wird zurückgestellt. Möchten Sie trotzdem fortsetzen?", "Common.Views.AutoCorrectDialog.warnRestore": "Der Autokorrektur-Eintrag für %1 wird zurückgestellt. Möchten Sie fortsetzen?", "Common.Views.Chat.textSend": "Senden", "Common.Views.Comments.mniAuthorAsc": "<PERSON><PERSON><PERSON><PERSON> (A-Z)", "Common.Views.Comments.mniAuthorDesc": "<PERSON><PERSON><PERSON><PERSON> (Z-A)", "Common.Views.Comments.mniDateAsc": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.mniDateDesc": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.mniFilterGroups": "Nach Gruppe filtern", "Common.Views.Comments.mniPositionAsc": "<PERSON>ben", "Common.Views.Comments.mniPositionDesc": "<PERSON> unten", "Common.Views.Comments.textAdd": "Hinzufügen", "Common.Views.Comments.textAddComment": "Kommentar hinzufügen", "Common.Views.Comments.textAddCommentToDoc": "Kommentar zum Dokument hinzufügen", "Common.Views.Comments.textAddReply": "Antwort hinzufügen", "Common.Views.Comments.textAll": "Alle", "Common.Views.Comments.textAnonym": "Gas<PERSON>", "Common.Views.Comments.textCancel": "Abbrechen", "Common.Views.Comments.textClose": "Schließen", "Common.Views.Comments.textClosePanel": "Kommentare schließen", "Common.Views.Comments.textComments": "Kommentare", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "<PERSON><PERSON><PERSON> Si<PERSON> Ihren Kommentar hier ein", "Common.Views.Comments.textHintAddComment": "Kommentar hinzufügen", "Common.Views.Comments.textOpenAgain": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textReply": "Antworten", "Common.Views.Comments.textResolve": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolved": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textSort": "Kommentare sortieren", "Common.Views.Comments.textViewResolved": "<PERSON>e haben keine Berechtigung, den Kommentar erneut zu öffnen", "Common.Views.Comments.txtEmpty": "Das Dokument enthält keine Kommentare.", "Common.Views.CopyWarningDialog.textDontShow": "<PERSON><PERSON> nicht mehr anzeigen", "Common.Views.CopyWarningDialog.textMsg": "<PERSON><PERSON><PERSON><PERSON>, Ausschneide- und Einfügeaktionen mit den Schaltflächen der Editor-Symbolleiste und Kontextmenü-Aktionen werden nur innerhalb dieser Editor-Registerkarte ausgeführt.<br><br>Zum Kopieren oder Einfügen in oder aus anderen Anwendungen nutzen Sie die folgenden Tastenkombinationen:", "Common.Views.CopyWarningDialog.textTitle": "Funktionen \"Kopieren\", \"Ausschneiden\" und \"Einfügen\"", "Common.Views.CopyWarningDialog.textToCopy": "zum Kopieren", "Common.Views.CopyWarningDialog.textToCut": "zum Ausschneiden", "Common.Views.CopyWarningDialog.textToPaste": "zum Einfügen", "Common.Views.DocumentAccessDialog.textLoading": "Ladevorgang...", "Common.Views.DocumentAccessDialog.textTitle": "Freigabeeinstellungen", "Common.Views.ExternalDiagramEditor.textTitle": "Diagramm bearbeiten", "Common.Views.ExternalEditor.textClose": "Schließen", "Common.Views.ExternalEditor.textSave": "Speichern und beenden", "Common.Views.ExternalMergeEditor.textTitle": "Seriendruckempfänger", "Common.Views.ExternalOleEditor.textTitle": "Editor der Tabellenkalkulationen", "Common.Views.Header.labelCoUsersDescr": "Das Dokument wird gerade von mehreren Benutzern bearbeitet.", "Common.Views.Header.textAddFavorite": "Als Favorit kennzei<PERSON>nen", "Common.Views.Header.textAdvSettings": "Erweiterte Einstellungen", "Common.Views.Header.textBack": "<PERSON>is<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textCompactView": "Symbolleiste ausblenden", "Common.Views.Header.textHideLines": "Lineale verbergen", "Common.Views.Header.textHideStatusBar": "Statusleiste verbergen", "Common.Views.Header.textReadOnly": "Schreibgeschützt", "Common.Views.Header.textRemoveFavorite": "Aus Favoriten entfernen", "Common.Views.Header.textShare": "Freigeben", "Common.Views.Header.textZoom": "Zoom", "Common.Views.Header.tipAccessRights": "Zugriffsrechte für das Dokument verwalten", "Common.Views.Header.tipDownload": "<PERSON><PERSON>", "Common.Views.Header.tipGoEdit": "Aktuelle Datei bearbeiten", "Common.Views.Header.tipPrint": "<PERSON><PERSON> drucken", "Common.Views.Header.tipPrintQuick": "Schnelldruck", "Common.Views.Header.tipRedo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipSave": "Speichern", "Common.Views.Header.tipSearch": "<PERSON><PERSON>", "Common.Views.Header.tipUndo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipUsers": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipViewSettings": "Ansichts-Einstellungen", "Common.Views.Header.tipViewUsers": "Benutzer ansehen und Zugriffsrechte für das Dokument verwalten", "Common.Views.Header.txtAccessRights": "Zugriffsrechte ändern", "Common.Views.Header.txtRename": "Umbenennen", "Common.Views.History.textCloseHistory": "Historie schließen", "Common.Views.History.textHide": "Reduzieren", "Common.Views.History.textHideAll": "Wesentliche Änderungen verbergen", "Common.Views.History.textRestore": "Wiederherstellen", "Common.Views.History.textShow": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.History.textShowAll": "Wesentliche Änderungen anzeigen", "Common.Views.History.textVer": "Ver.", "Common.Views.ImageFromUrlDialog.textUrl": "Bild-URL einfügen:", "Common.Views.ImageFromUrlDialog.txtEmpty": "<PERSON><PERSON> ist erford<PERSON>lich", "Common.Views.ImageFromUrlDialog.txtNotUrl": "<PERSON><PERSON> muss eine URL im Format \"http://www.example.com\" sein", "Common.Views.InsertTableDialog.textInvalidRowsCols": "Sie müssen eine gültige Anzahl der Zeilen und Spalten angeben.", "Common.Views.InsertTableDialog.txtColumns": "<PERSON><PERSON><PERSON>", "Common.Views.InsertTableDialog.txtMaxText": "Der maximale Wert für dieses Feld ist {0}.", "Common.Views.InsertTableDialog.txtMinText": "Der minimale Wert für dieses Feld ist {0}.", "Common.Views.InsertTableDialog.txtRows": "<PERSON><PERSON><PERSON>", "Common.Views.InsertTableDialog.txtTitle": "Größe der Tabelle", "Common.Views.InsertTableDialog.txtTitleSplit": "<PERSON><PERSON> te<PERSON>n", "Common.Views.LanguageDialog.labelSelect": "Sprache des Dokuments wählen", "Common.Views.OpenDialog.closeButtonText": "<PERSON><PERSON> sch<PERSON>n", "Common.Views.OpenDialog.txtEncoding": "Zeichenkodierung", "Common.Views.OpenDialog.txtIncorrectPwd": "Kennwort ist falsch.", "Common.Views.OpenDialog.txtOpenFile": "Kennwort zum Öffnen der Datei eingeben", "Common.Views.OpenDialog.txtPassword": "Kennwort", "Common.Views.OpenDialog.txtPreview": "Vorschau", "Common.Views.OpenDialog.txtProtected": "<PERSON><PERSON>d Sie das Passwort eingegeben und die Datei geöffnet haben, wird das aktuelle Passwort für die Datei zurückgesetzt.", "Common.Views.OpenDialog.txtTitle": "Wähle %1 Optionen", "Common.Views.OpenDialog.txtTitleProtected": "Geschützte Datei", "Common.Views.PasswordDialog.txtDescription": "Legen Sie ein Passwort fest, um dieses Dokument zu schützen", "Common.Views.PasswordDialog.txtIncorrectPwd": "Bestätigungseingabe ist nicht identisch", "Common.Views.PasswordDialog.txtPassword": "Kennwort", "Common.Views.PasswordDialog.txtRepeat": "Kenn<PERSON><PERSON> wiederholen", "Common.Views.PasswordDialog.txtTitle": "Kennwort festlegen", "Common.Views.PasswordDialog.txtWarning": "Vorsicht: <PERSON>n Si<PERSON> das Kennwort verlieren oder vergessen, lässt es sich nicht mehr wiederherstellen. Bewahren Sie es an einem sicheren Ort auf.", "Common.Views.PluginDlg.textLoading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Plugins.groupCaption": "Plugins", "Common.Views.Plugins.strPlugins": "Plugins", "Common.Views.Plugins.textClosePanel": "<PERSON><PERSON><PERSON> s<PERSON>n", "Common.Views.Plugins.textLoading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Plugins.textStart": "Starten", "Common.Views.Plugins.textStop": "<PERSON>den", "Common.Views.Protection.hintAddPwd": "Mit Kennwort verschlüsseln", "Common.Views.Protection.hintDelPwd": "Kennwort löschen", "Common.Views.Protection.hintPwd": "Das Kennwort ändern oder löschen", "Common.Views.Protection.hintSignature": "Digitale Signatur oder Unterschriftenzeile hinzufügen", "Common.Views.Protection.txtAddPwd": "Kennwort hinzufügen", "Common.Views.Protection.txtChangePwd": "Kennwort ändern", "Common.Views.Protection.txtDeletePwd": "Kennwort löschen", "Common.Views.Protection.txtEncrypt": "Verschlüsseln", "Common.Views.Protection.txtInvisibleSignature": "Digitale Signatur hinzufügen", "Common.Views.Protection.txtSignature": "Signatur", "Common.Views.Protection.txtSignatureLine": "Signaturzeile hinzufügen", "Common.Views.RenameDialog.textName": "Dateiname", "Common.Views.RenameDialog.txtInvalidName": "Dieser Dateiname darf keines der folgenden Zeichen enthalten:", "Common.Views.ReviewChanges.hintNext": "Zur nächsten Änderung", "Common.Views.ReviewChanges.hintPrev": "Zur vorherigen Änderung", "Common.Views.ReviewChanges.mniFromFile": "Dokument aus Datei", "Common.Views.ReviewChanges.mniFromStorage": "Dokument aus dem Speicher", "Common.Views.ReviewChanges.mniFromUrl": "Dokument aus URL", "Common.Views.ReviewChanges.mniSettings": "Vergleichseinstellungen", "Common.Views.ReviewChanges.strFast": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.strFastDesc": "Echtzeit-Zusammenbearbeitung. Alle Änderungen werden automatisch gespeichert.", "Common.Views.ReviewChanges.strStrict": "Formal", "Common.Views.ReviewChanges.strStrictDesc": "Verwenden Sie die Schaltfläche \"Speichern\", um die von Ihnen und anderen vorgenommenen Änderungen zu synchronisieren.", "Common.Views.ReviewChanges.textEnable": "Aktivieren", "Common.Views.ReviewChanges.textWarnTrackChanges": "Nachverfolgung von Änderungen wird für alle Benutzer mit dem vollen Zugriff AKTIVIERT und bleibt auch beim nächsten Öffnen des Dokuments aktiv.", "Common.Views.ReviewChanges.textWarnTrackChangesTitle": "Möchten Sie Nachverfolgung von Änderungen für alle aktivieren?", "Common.Views.ReviewChanges.tipAcceptCurrent": "Aktuelle Änderungen annehmen", "Common.Views.ReviewChanges.tipCoAuthMode": "Zusammen-Bearbeitungsmodus einstellen", "Common.Views.ReviewChanges.tipCommentRem": "Kommentare entfernen", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Aktuelle Kommentare entfernen", "Common.Views.ReviewChanges.tipCommentResolve": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Gültige Kommentare lösen", "Common.Views.ReviewChanges.tipCompare": "Das aktuelle Dokument mit einem anderen vergleichen", "Common.Views.ReviewChanges.tipHistory": "Versionshistorie anzeigen", "Common.Views.ReviewChanges.tipRejectCurrent": "Aktuelle Änderungen ablehnen", "Common.Views.ReviewChanges.tipReview": "Nachverfo<PERSON><PERSON> von <PERSON>", "Common.Views.ReviewChanges.tipReviewView": "Wählen Sie den Modus aus, in dem die Änderungen angezeigt werden sollen", "Common.Views.ReviewChanges.tipSetDocLang": "Sprache des Dokumentes festlegen", "Common.Views.ReviewChanges.tipSetSpelling": "Rechtschreibprüfung", "Common.Views.ReviewChanges.tipSharing": "Zugriffsrechte für das Dokument verwalten", "Common.Views.ReviewChanges.txtAccept": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtAcceptAll": "Alle Änderungen annehmen", "Common.Views.ReviewChanges.txtAcceptChanges": "Änderungen annehmen", "Common.Views.ReviewChanges.txtAcceptCurrent": "Aktuelle Änderungen annehmen", "Common.Views.ReviewChanges.txtChat": "Cha<PERSON>", "Common.Views.ReviewChanges.txtClose": "Schließen", "Common.Views.ReviewChanges.txtCoAuthMode": "Modus \"Gemeinsame Bearbeitung\"", "Common.Views.ReviewChanges.txtCommentRemAll": "Alle Kommentare entfernen", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Aktuelle Kommentare entfernen", "Common.Views.ReviewChanges.txtCommentRemMy": "<PERSON>ne Kommentare entfernen", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Meine aktuellen Kommentare entfernen", "Common.Views.ReviewChanges.txtCommentRemove": "Entfernen", "Common.Views.ReviewChanges.txtCommentResolve": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentResolveAll": "Alle Kommentare lösen", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Gültige Kommentare lösen", "Common.Views.ReviewChanges.txtCommentResolveMy": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Meine gültige Kommentare lösen", "Common.Views.ReviewChanges.txtCompare": "Vergleichen", "Common.Views.ReviewChanges.txtDocLang": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtEditing": "Bearbeitung", "Common.Views.ReviewChanges.txtFinal": "Alle Änderungen übernommen {0}", "Common.Views.ReviewChanges.txtFinalCap": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtHistory": "Versionshistorie", "Common.Views.ReviewChanges.txtMarkup": "Alle Änderungen {0}", "Common.Views.ReviewChanges.txtMarkupCap": "<PERSON><PERSON> und Sprechblasen", "Common.Views.ReviewChanges.txtMarkupSimple": "Alle Änderungen {0}<br>Sprechblasen ausblenden", "Common.Views.ReviewChanges.txtMarkupSimpleCap": "Einfaches Markup", "Common.Views.ReviewChanges.txtNext": "Zur nächsten Änderung", "Common.Views.ReviewChanges.txtOff": "DEAKTIVIERT für mich", "Common.Views.ReviewChanges.txtOffGlobal": "DEAKTIVIERT für alle", "Common.Views.ReviewChanges.txtOn": "AKTIVIERT für mich", "Common.Views.ReviewChanges.txtOnGlobal": "AKTIVIERT für alle", "Common.Views.ReviewChanges.txtOriginal": "Alle Änderungen abgelehnt {0}", "Common.Views.ReviewChanges.txtOriginalCap": "Original", "Common.Views.ReviewChanges.txtPrev": "Zur vorherigen Änderung", "Common.Views.ReviewChanges.txtPreview": "Vorschau", "Common.Views.ReviewChanges.txtReject": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectAll": "Alle Änderungen ablehnen", "Common.Views.ReviewChanges.txtRejectChanges": "Änderungen ablehnen", "Common.Views.ReviewChanges.txtRejectCurrent": "Aktuelle Änderungen ablehnen", "Common.Views.ReviewChanges.txtSharing": "Freigabe", "Common.Views.ReviewChanges.txtSpelling": "Rechtschreibprüfung", "Common.Views.ReviewChanges.txtTurnon": "Nachverfo<PERSON><PERSON> von <PERSON>", "Common.Views.ReviewChanges.txtView": "<PERSON><PERSON>ige<PERSON><PERSON>", "Common.Views.ReviewChangesDialog.textTitle": "Änderungen überprüfen", "Common.Views.ReviewChangesDialog.txtAccept": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChangesDialog.txtAcceptAll": "Alle Änderungen annehmen", "Common.Views.ReviewChangesDialog.txtAcceptCurrent": "Aktuelle Änderungen annehmen", "Common.Views.ReviewChangesDialog.txtNext": "Zur nächsten Änderung", "Common.Views.ReviewChangesDialog.txtPrev": "Zur vorherigen Änderung", "Common.Views.ReviewChangesDialog.txtReject": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChangesDialog.txtRejectAll": "Alle Änderungen ablehnen", "Common.Views.ReviewChangesDialog.txtRejectCurrent": "Aktuelle Änderungen ablehnen", "Common.Views.ReviewPopover.textAdd": "Hinzufügen", "Common.Views.ReviewPopover.textAddReply": "Antwort hinzufügen", "Common.Views.ReviewPopover.textCancel": "Abbrechen", "Common.Views.ReviewPopover.textClose": "Schließen", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textFollowMove": "Verschieben nachverfolgen", "Common.Views.ReviewPopover.textMention": "+Erwähnung ermöglicht den Zugriff auf das Dokument und das Senden einer E-Mail", "Common.Views.ReviewPopover.textMentionNotify": "+Erwähnung benachrichtigt den Benutzer per E-Mail", "Common.Views.ReviewPopover.textOpenAgain": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textReply": "Antworten", "Common.Views.ReviewPopover.textResolve": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textViewResolved": "<PERSON>e haben keine Berechtigung, den Kommentar erneut zu öffnen", "Common.Views.ReviewPopover.txtAccept": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.txtDeleteTip": "Löschen", "Common.Views.ReviewPopover.txtEditTip": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.txtReject": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SaveAsDlg.textLoading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SaveAsDlg.textTitle": "Ordner fürs Speichern", "Common.Views.SearchPanel.textCaseSensitive": "Groß-/Kleinschreibung beachten", "Common.Views.SearchPanel.textCloseSearch": "<PERSON><PERSON> sch<PERSON>ßen", "Common.Views.SearchPanel.textContentChanged": "Dokument verändert.", "Common.Views.SearchPanel.textFind": "<PERSON><PERSON>", "Common.Views.SearchPanel.textFindAndReplace": "<PERSON><PERSON> und ersetzen", "Common.Views.SearchPanel.textMatchUsingRegExp": "Über reguläre Ausdrücke abgleichen", "Common.Views.SearchPanel.textNoMatches": "<PERSON><PERSON>", "Common.Views.SearchPanel.textNoSearchResults": "<PERSON><PERSON>", "Common.Views.SearchPanel.textReplace": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textReplaceAll": "<PERSON>e ersetzen", "Common.Views.SearchPanel.textReplaceWith": "Ersetzen durch", "Common.Views.SearchPanel.textSearchAgain": "{0}Neue Suche durchführen{1} für genaue Ergebnisse.", "Common.Views.SearchPanel.textSearchHasStopped": "Suche abgebrochen", "Common.Views.SearchPanel.textSearchResults": "Suchergebnisse: {0}/{1}", "Common.Views.SearchPanel.textTooManyResults": "<PERSON>s gibt zu viele Ergebnisse, um sie hier zu zeigen", "Common.Views.SearchPanel.textWholeWords": "Nur ganze Wörter", "Common.Views.SearchPanel.tipNextResult": "Nächstes Ergebnis", "Common.Views.SearchPanel.tipPreviousResult": "Vorheriges Ergebnis", "Common.Views.SelectFileDlg.textLoading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SelectFileDlg.textTitle": "<PERSON><PERSON><PERSON><PERSON> au<PERSON>w<PERSON>", "Common.Views.SignDialog.textBold": "<PERSON><PERSON>", "Common.Views.SignDialog.textCertificate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textChange": "Ändern", "Common.Views.SignDialog.textInputName": "Name des Signaturgebers eingeben", "Common.Views.SignDialog.textItalic": "<PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textNameError": "Der Name des Signaturgebers darf nicht leer sein.", "Common.Views.SignDialog.textPurpose": "Zweck der Signierung dieses Dokuments", "Common.Views.SignDialog.textSelect": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textSelectImage": "Bild auswählen", "Common.Views.SignDialog.textSignature": "Wie sieht Signatur aus:", "Common.Views.SignDialog.textTitle": "Dokument signieren", "Common.Views.SignDialog.textUseImage": "oder klicken Sie auf \"Bild auswählen\", um ein Bild als Unterschrift zu verwenden", "Common.Views.SignDialog.textValid": "Gültig von% 1 bis% 2", "Common.Views.SignDialog.tipFontName": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.tipFontSize": "Schriftgrad", "Common.Views.SignSettingsDialog.textAllowComment": "Signaturgeber verfügt über die Möglichkeit, einen Kommentar im Signaturdialog hinzuzufügen", "Common.Views.SignSettingsDialog.textDefInstruction": "Überprüfen Si<PERSON>, ob der signierte Inhalt stimmt, bevor <PERSON> dieses Dokument signieren.", "Common.Views.SignSettingsDialog.textInfoEmail": "E-Mail des vorgeschlagenen Unterzeichners", "Common.Views.SignSettingsDialog.textInfoName": "Name", "Common.Views.SignSettingsDialog.textInfoTitle": "Titel des Signatureingebers", "Common.Views.SignSettingsDialog.textInstructions": "Anweisungen für Signaturgeber", "Common.Views.SignSettingsDialog.textShowDate": "Signaturdatum in der Signaturzeile anzeigen", "Common.Views.SignSettingsDialog.textTitle": "Signatureinstellungen", "Common.Views.SignSettingsDialog.txtEmpty": "<PERSON><PERSON> ist erford<PERSON>lich", "Common.Views.SymbolTableDialog.textCharacter": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textCode": "Unicode HEX Wert", "Common.Views.SymbolTableDialog.textCopyright": "Copyrightzeichen", "Common.Views.SymbolTableDialog.textDCQuote": "Doppelte schließende Anführung", "Common.Views.SymbolTableDialog.textDOQuote": "Doppelte offende Anführungszeichen", "Common.Views.SymbolTableDialog.textEllipsis": "Waagerechte Auslassungspunkte", "Common.Views.SymbolTableDialog.textEmDash": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textEmSpace": "Em-Abstand", "Common.Views.SymbolTableDialog.textEnDash": "Halbgeviertstrich", "Common.Views.SymbolTableDialog.textEnSpace": "En-Abstand", "Common.Views.SymbolTableDialog.textFont": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textNBHyphen": "Geschützter Bindestrich", "Common.Views.SymbolTableDialog.textNBSpace": "Geschütztes Leerzeichen", "Common.Views.SymbolTableDialog.textPilcrow": "Absatzzeichen", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4-Em-Abstand", "Common.Views.SymbolTableDialog.textRange": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textRecent": "K<PERSON><PERSON>lich verwendete Symbole", "Common.Views.SymbolTableDialog.textRegistered": "Registered Trade Mark", "Common.Views.SymbolTableDialog.textSCQuote": "Einfache schließendes Anführungszeichen", "Common.Views.SymbolTableDialog.textSection": "Paragraphenzeichen", "Common.Views.SymbolTableDialog.textShortcut": "Tastenkombination", "Common.Views.SymbolTableDialog.textSHyphen": "Weicher Bindestrich", "Common.Views.SymbolTableDialog.textSOQuote": "Einfache offende Anführungszeichen", "Common.Views.SymbolTableDialog.textSpecial": "Sonderzeichen", "Common.Views.SymbolTableDialog.textSymbols": "Symbole", "Common.Views.SymbolTableDialog.textTitle": "Symbol", "Common.Views.SymbolTableDialog.textTradeMark": "Markenzeichen-Symbol", "Common.Views.UserNameDialog.textDontShow": "Nicht mehr anzeigen", "Common.Views.UserNameDialog.textLabel": "Bezeichnung:", "Common.Views.UserNameDialog.textLabelError": "<PERSON><PERSON><PERSON>nung darf nicht leer sein.", "DE.Controllers.LeftMenu.leavePageText": "Alle ungespeicherten Änderungen in diesem Dokument werden verloren.<br> \n<PERSON><PERSON><PERSON> <PERSON><PERSON> auf \"Abbrechen\" und anschließend auf \"Speichern\", um Änderungen zu speichern. \nKlicken Sie auf \"OK\", und alle ungespeicherten Änderungen werden NICHT gespeichert und sind verloren.", "DE.Controllers.LeftMenu.newDocumentTitle": "Unbetiteltes Dokument", "DE.Controllers.LeftMenu.notcriticalErrorTitle": "Achtung", "DE.Controllers.LeftMenu.requestEditRightsText": "Anfrage betreffend die Bearbeitungsberechtigung...", "DE.Controllers.LeftMenu.textLoadHistory": "Versionshistorie wird geladen...", "DE.Controllers.LeftMenu.textNoTextFound": "<PERSON> Daten, nach denen <PERSON> gesucht haben, können nicht gefunden werden. Bitte ändern Sie die Suchparameter.", "DE.Controllers.LeftMenu.textReplaceSkipped": "Der Ersatzvorgang wurde durchgeführt. {0} Vorkommen wurden ausgelassen.", "DE.Controllers.LeftMenu.textReplaceSuccess": "Der Suchvorgang wurde durchgeführt. Vorkommen wurden ersetzt:{0}", "DE.Controllers.LeftMenu.txtCompatible": "Das Dokument wird im neuen Format gespeichert. Es ermöglicht die Verwendung aller Funktionen, kann jedoch das Dokument-Layout beeinflussen.<br>Verwenden Sie die Option 'Kompatibilität' in den erweiterten Einstellungen, wenn Sie die Dateien mit älteren MS Word-Versionen kompatibel machen möchten.", "DE.Controllers.LeftMenu.txtUntitled": "Unbenannt", "DE.Controllers.LeftMenu.warnDownloadAs": "Wenn Si<PERSON> mit dem Speichern in diesem Format fortsetzen, werden alle Objekte außer Text verloren gehen.<br>M<PERSON>chten Sie wirklich fortsetzen?", "DE.Controllers.LeftMenu.warnDownloadAsPdf": "{0} wird in ein bearbeitbares Format umgewandelt. Dies kann eine Weile dauern. Das Ausgabedokument wird so gestaltet, dass Sie den Text bearbeiten können. Es sieht also möglicherweise nicht genau so aus wie die ursprüngliche Datei {0}, besonders wenn sie viele Grafiken enthält.", "DE.Controllers.LeftMenu.warnDownloadAsRTF": "Wenn Sie mit dem Speichern in diesem Format fortsetzen, kann die Formatierung teilweise verloren gehen.<br>Möchten Sie wirklich fortsetzen?", "DE.Controllers.LeftMenu.warnReplaceString": "{0} darf nicht als Sonderzeichen in das Feld \"Ersetzen durch\" eingegeben werden.", "DE.Controllers.Main.applyChangesTextText": "Die Änderungen werden geladen...", "DE.Controllers.Main.applyChangesTitleText": "<PERSON><PERSON> von <PERSON>", "DE.Controllers.Main.confirmMaxChangesSize": "Die Anzahl der Aktionen überschreitet die für Ihren Server festgelegte Grenze.<br><PERSON><PERSON><PERSON> \"Rückgängig\", um Ihre letzte Aktion abzubrechen, oder drücken Sie \"Weiter\", um die Aktion lokal fortzusetzen (Sie müssen die Datei herunterladen oder ihren Inhalt kopieren, um sicherzustellen, dass nichts verloren geht).", "DE.Controllers.Main.convertationTimeoutText": "Zeitüberschreitung bei der Konvertierung.", "DE.Controllers.Main.criticalErrorExtText": "<PERSON><PERSON><PERSON> Sie auf \"OK\", um in die Dokumentenliste zu gelangen.", "DE.Controllers.Main.criticalErrorTitle": "<PERSON><PERSON>", "DE.Controllers.Main.downloadErrorText": "Herunterladen ist fehlgeschlagen.", "DE.Controllers.Main.downloadMergeText": "Wird herunt<PERSON><PERSON><PERSON><PERSON>...", "DE.Controllers.Main.downloadMergeTitle": "Wird <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.downloadTextText": "Dokument wird heruntergeladen...", "DE.Controllers.Main.downloadTitleText": "Herunterladen des Dokuments", "DE.Controllers.Main.errorAccessDeny": "<PERSON><PERSON>, eine Aktion durchzuführen, für die Si<PERSON> keine Rechte haben.<br><PERSON>te wenden Sie sich an Ihren Document Serveradministrator.", "DE.Controllers.Main.errorBadImageUrl": "URL des Bildes ist falsch", "DE.Controllers.Main.errorCoAuthoringDisconnect": "Verbindung zum Server ist verloren gegangen. Das Dokument kann momentan nicht bearbeitet werden.", "DE.Controllers.Main.errorComboSeries": "<PERSON><PERSON>hlen Sie mindestens zwei Datenreihen aus, um ein Verbunddiagramm zu erstellen.", "DE.Controllers.Main.errorCompare": "Vergleich von Dokumenten ist bei der Zusammenarbeit unverfügbar.", "DE.Controllers.Main.errorConnectToServer": "Das Dokument konnte nicht gespeichert werden. Bitte überprüfen Sie die Verbindungseinstellungen oder wenden Si<PERSON> sich an Ihren Administrator.<br><PERSON><PERSON> <PERSON><PERSON> auf die Schaltfläche \"OK\" klicken, werden Sie aufgefordert das Dokument herunterzuladen.", "DE.Controllers.Main.errorDatabaseConnection": "<PERSON><PERSON><PERSON> Fehler.<br><PERSON><PERSON> beim Verbinden zur Datenbank. Bitte wenden Si<PERSON> sich an den Kundendienst, falls der Fehler bestehen bleibt.", "DE.Controllers.Main.errorDataEncrypted": "Änderungen wurden verschlüsselt. Sie können nicht entschlüsselt werden.", "DE.Controllers.Main.errorDataRange": "Falscher Datenbereich.", "DE.Controllers.Main.errorDefaultMessage": "Fehlercode: %1", "DE.Controllers.Main.errorDirectUrl": "Bitte überprüfen Sie den Link zum Dokument.<br><PERSON>ser Link muss ein direkter Link zu der Datei zum Herunterladen sein.", "DE.Controllers.Main.errorEditingDownloadas": "Bei der Arbeit mit dem Dokument ist ein Fehler aufgetreten. <br> Verwenden Sie die Option 'Herunterladen als', um die Sicherungskopie der Datei auf der Festplatte Ihres Computers zu speichern.", "DE.Controllers.Main.errorEditingSaveas": "Bei der Arbeit mit dem Dokument ist ein Fehler aufgetreten. <br> Verwenden Sie die Option \"Speichern als ...\", um die Sicherungskopie der Datei auf der Festplatte Ihres Computers zu speichern.", "DE.Controllers.Main.errorEmailClient": "<PERSON>s wurde kein E-Mail-Client gefunden.", "DE.Controllers.Main.errorEmptyTOC": "Beginnen Sie die Erstellung eines Inhaltsverzeichnisses, indem Sie eine Überschriftenvorlage aus der Galerie von Stilen auf den ausgewählten Text anwenden.", "DE.Controllers.Main.errorFilePassProtect": "Das Dokument ist kennwortgeschützt und kann nicht geöffnet werden.", "DE.Controllers.Main.errorFileSizeExceed": "Die Dateigröße überschreitet die für Ihren Server festgelegte Einschränkung.<br>Weitere Informationen können Sie von Ihrem Document Server-Administrator er<PERSON><PERSON>.", "DE.Controllers.Main.errorForceSave": "<PERSON>im Speichern der Datei ist ein Fehler aufgetreten. Verwenden Sie die Option \"Herunterladen als\", um die Datei auf Ihrer Computerfestplatte zu speichern oder versuchen Sie es später erneut.", "DE.Controllers.Main.errorInconsistentExt": "<PERSON><PERSON> der Datei ist ein Fehler aufgetreten.<br>Der Inhalt der Datei stimmt nicht mit der Dateierweiterung überein.", "DE.Controllers.Main.errorInconsistentExtDocx": "<PERSON><PERSON> der Datei ist ein Fehler aufgetreten.<br>Der Inhalt der Datei entspricht Textdokumenten (z.B. docx), aber die Datei hat die inkonsistente Erweiterung: %1.", "DE.Controllers.Main.errorInconsistentExtPdf": "<PERSON><PERSON> der Datei ist ein Fehler aufgetreten.<br>Der Inhalt der Datei entspricht einem der folgenden Formate: pdf/djvu/xps/oxps, aber die Datei hat die inkonsistente Erweiterung: %1.", "DE.Controllers.Main.errorInconsistentExtPptx": "<PERSON><PERSON> der Datei ist ein Fehler aufgetreten.<br>Der Inhalt der Datei entspricht Präsentationen (z.B. pptx), aber die Datei hat die inkonsistente Erweiterung: %1.", "DE.Controllers.Main.errorInconsistentExtXlsx": "<PERSON><PERSON> der Datei ist ein Fehler aufgetreten.<br>Der Inhalt der Datei entspricht Tabellenkalkulationen (z.B. xlsx), aber die Datei hat die inkonsistente Erweiterung: %1.", "DE.Controllers.Main.errorKeyEncrypt": "Unbekannter Schlüsseldeskriptor", "DE.Controllers.Main.errorKeyExpire": "Der Schlüsseldeskriptor ist abgelaufen", "DE.Controllers.Main.errorLoadingFont": "Schriftarten nicht hochgeladen.<br><PERSON><PERSON> wenden <PERSON> sich an <PERSON><PERSON> von Ihrem Document Server.", "DE.Controllers.Main.errorMailMergeLoadFile": "Fehler beim Laden des Dokuments. Bitte wählen Sie eine andere Datei.", "DE.Controllers.Main.errorMailMergeSaveFile": "Merge ist fehlgeschlagen.", "DE.Controllers.Main.errorNoTOC": "Es gibt kein Inhaltsverzeichnis. Sie können es auf der Registerkarte \"Verweise\" einfügen.", "DE.Controllers.Main.errorPasswordIsNotCorrect": "Das eingegebene Kennwort ist ungültig.<br><PERSON><PERSON><PERSON>, dass die FESTSTELLTASTE nicht aktiviert ist und dass Sie die korrekte Groß-/Kleinschreibung verwenden.", "DE.Controllers.Main.errorProcessSaveResult": "Speichern ist fehlgeschlagen.", "DE.Controllers.Main.errorServerVersion": "Editor-Version wurde aktualisiert. Die Seite wird neu geladen, um die Änderungen zu übernehmen.", "DE.Controllers.Main.errorSessionAbsolute": "Die Bearbeitungssitzung des Dokumentes ist abgelaufen. Laden Sie die Seite neu.", "DE.Controllers.Main.errorSessionIdle": "Das Dokument wurde lange nicht bearbeitet. Laden Sie die Seite neu.", "DE.Controllers.Main.errorSessionToken": "Die Verbindung zum Server wurde unterbrochen. Laden Sie die Seite neu.", "DE.Controllers.Main.errorSetPassword": "Das Passwort konnte nicht festgelegt werden.", "DE.Controllers.Main.errorStockChart": "Falsche Reihenfolge der Zeilen. Um ein Kursdiagramm zu erstellen, ordnen Sie die Daten auf dem Blatt folgendermaßen an:<br> Eröffnungspreis, Höchstpreis, Tiefstpreis, Schlusskurs.", "DE.Controllers.Main.errorSubmit": "<PERSON><PERSON> beim <PERSON>.", "DE.Controllers.Main.errorTextFormWrongFormat": "Der eingegebene Wert stimmt nicht mit dem Format des Feldes überein.", "DE.Controllers.Main.errorToken": "Sicherheitstoken des Dokuments ist nicht korrekt.<br><PERSON><PERSON> sich an Ihren Serveradministrator.", "DE.Controllers.Main.errorTokenExpire": "Sicherheitstoken des Dokuments ist abgelaufen.<br><PERSON><PERSON> sich an Ihren Serveradministrator.", "DE.Controllers.Main.errorUpdateVersion": "Die Dateiversion wurde geändert. Die Seite wird neu geladen.", "DE.Controllers.Main.errorUpdateVersionOnDisconnect": "Die Internetverbindung wurde wiederhergestellt und die Dateiversion wurde geändert.<br><PERSON><PERSON> weiterarbeiten können, müssen Sie die Datei herunterladen oder den Inhalt kopieren, um sicherzustellen, dass nichts verloren geht, und diese Seite anschließend neu laden.", "DE.Controllers.Main.errorUserDrop": "<PERSON><PERSON> auf diese Datei ist möglich.", "DE.Controllers.Main.errorUsersExceed": "Die nach dem Zahlungsplan erlaubte Anzahl der Benutzer ist überschritten", "DE.Controllers.Main.errorViewerDisconnect": "Die Verbindung ist unterbrochen. Man kann das Dokument weiterhin anschauen.<br><PERSON><PERSON> ist aber momentan nicht möglich, es herunterzuladen oder zu drucken bis die Verbindung wiederhergestellt <br>und die Seite neu geladen wird.", "DE.Controllers.Main.leavePageText": "Dieses Dokument enthält ungespeicherte Änderungen. Klicken Sie \"Auf dieser Seite bleiben\" und dann \"Speichern\", um sie zu speichern. Klicken Sie \"Diese Seite verlassen\", um alle nicht gespeicherten Änderungen zu verwerfen.", "DE.Controllers.Main.leavePageTextOnClose": "Alle ungespeicherten Änderungen in diesem Dokument werden verloren.<br> \n<PERSON><PERSON><PERSON> <PERSON><PERSON> auf \"Abbrechen\" und anschließend auf \"Speichern\", um die Änderungen zu speichern. \nKlicken Sie auf den Button \"OK\", und alle Änderungen werden NICHT gespeichert und sind verloren. ", "DE.Controllers.Main.loadFontsTextText": "Daten werden geladen...", "DE.Controllers.Main.loadFontsTitleText": "Daten werden geladen", "DE.Controllers.Main.loadFontTextText": "Daten werden geladen...", "DE.Controllers.Main.loadFontTitleText": "Daten werden geladen", "DE.Controllers.Main.loadImagesTextText": "Bilder werden geladen...", "DE.Controllers.Main.loadImagesTitleText": "Bilder werden geladen", "DE.Controllers.Main.loadImageTextText": "Bild wird geladen...", "DE.Controllers.Main.loadImageTitleText": "Bild wird geladen", "DE.Controllers.Main.loadingDocumentTextText": "Dokument wird geladen...", "DE.Controllers.Main.loadingDocumentTitleText": "Dokument wird geladen...", "DE.Controllers.Main.mailMergeLoadFileText": "Laden der Datenquellen...", "DE.Controllers.Main.mailMergeLoadFileTitle": "Laden der Datenquellen", "DE.Controllers.Main.notcriticalErrorTitle": "Achtung", "DE.Controllers.Main.openErrorText": "<PERSON><PERSON> der Datei ist ein Fehler aufgetreten.", "DE.Controllers.Main.openTextText": "Dokument wird geöffnet", "DE.Controllers.Main.openTitleText": "Das Dokument wird geöffnet", "DE.Controllers.Main.printTextText": "Dokument wird ausgedruckt...", "DE.Controllers.Main.printTitleText": "Drucken des Dokuments", "DE.Controllers.Main.reloadButtonText": "Seite erneut laden", "DE.Controllers.Main.requestEditFailedMessageText": "<PERSON><PERSON> bearbei<PERSON>t dieses Dokument in diesem Moment. Bitte versuchen Sie es später erneut.", "DE.Controllers.Main.requestEditFailedTitleText": "<PERSON><PERSON><PERSON> verweigert", "DE.Controllers.Main.saveErrorText": "<PERSON><PERSON> der Datei ist ein Fehler aufgetreten.", "DE.Controllers.Main.saveErrorTextDesktop": "Diese Datei kann nicht erstellt oder gespeichert werden.<br>Dies ist möglicherweise davon verursacht: <br>1. Die Datei ist schreibgeschützt. <br>2. Die Datei wird von anderen Benutzern bearbeitet. <br>3. Die Festplatte ist voll oder beschädigt.", "DE.Controllers.Main.saveTextText": "Dokument wird gespeichert...", "DE.Controllers.Main.saveTitleText": "Dokument wird gespeichert...", "DE.Controllers.Main.scriptLoadError": "Die Verbindung ist zu langsam, einige der Komponenten konnten nicht geladen werden. Bitte laden Sie die Se<PERSON> erneut.", "DE.Controllers.Main.sendMergeText": "Merge wird versandt...", "DE.Controllers.Main.sendMergeTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.splitDividerErrorText": "Die Zeilenanzahl muss ein Divisor von %1 sein.", "DE.Controllers.Main.splitMaxColsErrorText": "Die Spaltenanzahl muss weniger sein als %1.", "DE.Controllers.Main.splitMaxRowsErrorText": "Die Zeilenanzahl muss weniger als %1 sein.", "DE.Controllers.Main.textAnonymous": "Anonym", "DE.Controllers.Main.textApplyAll": "<PERSON>ür alle Gleichungen verwenden", "DE.Controllers.Main.textBuyNow": "Webseite besuchen", "DE.Controllers.Main.textChangesSaved": "Alle Änderungen gespeichert", "DE.Controllers.Main.textClose": "Schließen", "DE.Controllers.Main.textCloseTip": "<PERSON><PERSON><PERSON>, um den Tipp zu schließen", "DE.Controllers.Main.textContactUs": "Verkaufsteam kontaktieren", "DE.Controllers.Main.textContinue": "Fortsetzen", "DE.Controllers.Main.textConvertEquation": "Diese Gleichung wurde in einer alten Version des Gleichungseditors erstellt, die nicht mehr unterstützt wird. Um die Gleichung zu bearbeiten, konvertieren Sie diese ins Format Office Math ML. <br><PERSON>zt konvertieren?", "DE.Controllers.Main.textCustomLoader": "<PERSON>te beachten Si<PERSON>, dass Si<PERSON> gemäß den Lizenzbedingungen nicht berechtigt sind, den Loader zu wechseln. <br> <PERSON><PERSON> sich an unseren Vertrieb, um ein Angebot zu erhalten.", "DE.Controllers.Main.textDisconnect": "Verbindung wurde unterbrochen", "DE.Controllers.Main.textGuest": "Gas<PERSON>", "DE.Controllers.Main.textHasMacros": "Die Datei beinhaltet automatische Makros.<br><PERSON><PERSON><PERSON><PERSON> Sie Makros ausführen?", "DE.Controllers.Main.textLearnMore": "<PERSON><PERSON> er<PERSON>", "DE.Controllers.Main.textLoadingDocument": "Dokument wird geladen...", "DE.Controllers.Main.textLongName": "Der Name einer Tabellenansicht darf maximal 128 <PERSON><PERSON>chen lang sein.", "DE.Controllers.Main.textNoLicenseTitle": "Lizenzlimit erreicht", "DE.Controllers.Main.textPaidFeature": "Kostenpflichtige Funktion", "DE.Controllers.Main.textReconnect": "Verbindung wurde wiederhergestellt", "DE.Controllers.Main.textRemember": "<PERSON><PERSON>rken", "DE.Controllers.Main.textRememberMacros": "Auswahl für alle Makros speichern", "DE.Controllers.Main.textRenameError": "<PERSON><PERSON><PERSON><PERSON> darf nicht leer sein.", "DE.Controllers.Main.textRenameLabel": "Geben Sie den Namen für Zusammenarbeit ein", "DE.Controllers.Main.textRequestMacros": "Ein Makro stellt eine Anfrage an die URL. Möchten Sie die Anfrage an die %1 zulassen?", "DE.Controllers.Main.textShape": "Form", "DE.Controllers.Main.textStrict": "Formaler Modus", "DE.Controllers.Main.textText": "Text", "DE.Controllers.Main.textTryQuickPrint": "Sie haben Schnelldruck gewählt: Das gesamte Dokument wird auf dem zuletzt gewählten oder dem Standarddrucker gedruckt.<br><PERSON><PERSON> fort<PERSON>hren?", "DE.Controllers.Main.textTryUndoRedo": "Undo/Redo Optionen sind  für den halbformalen Zusammenbearbeitungsmodus deaktiviert.<br><PERSON><PERSON><PERSON> <PERSON><PERSON> auf den Button \"Formaler Modus\", um den formalen Zusammenbearbeitungsmodus zu aktivieren, um die Datei, ohne Störungen anderer Benutzer zu bearbeiten und die Änderungen erst nachdem Sie sie gespeichert haben, zu senden. Sie können zwischen den Zusammenbearbeitungsmodi mit der Hilfe der erweiterten Einstellungen von Editor umschalten.", "DE.Controllers.Main.textTryUndoRedoWarn": "Die Optionen Rückgängig/Wiederholen sind für den halbformalen Zusammenbearbeitungsmodus deaktiviert.", "DE.Controllers.Main.textUndo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.titleLicenseExp": "Lizenz ist abgelaufen", "DE.Controllers.Main.titleServerVersion": "Editor wurde aktualisi<PERSON>", "DE.Controllers.Main.titleUpdateVersion": "Version wurde geändert", "DE.Controllers.Main.txtAbove": "oben", "DE.Controllers.Main.txtArt": "<PERSON><PERSON> den <PERSON> e<PERSON>ben", "DE.Controllers.Main.txtBasicShapes": "Standardformen", "DE.Controllers.Main.txtBelow": "unten", "DE.Controllers.Main.txtBookmarkError": "Fehler! Textmarke nicht definiert.", "DE.Controllers.Main.txtButtons": "Buttons", "DE.Controllers.Main.txtCallouts": "<PERSON><PERSON>", "DE.Controllers.Main.txtCharts": "Diagramme", "DE.Controllers.Main.txtChoose": "<PERSON><PERSON><PERSON>en Si<PERSON> ein Element aus", "DE.Controllers.Main.txtClickToLoad": "<PERSON><PERSON><PERSON>, um das Bild herunterzuladen", "DE.Controllers.Main.txtCurrentDocument": "Aktuelles Dokument", "DE.Controllers.Main.txtDiagramTitle": "Diagrammtitel", "DE.Controllers.Main.txtEditingMode": "Bearbeitungsmodus festlegen...", "DE.Controllers.Main.txtEndOfFormula": "Unerwartetes Ende der Formel", "DE.Controllers.Main.txtEnterDate": "<PERSON><PERSON> e<PERSON>gen", "DE.Controllers.Main.txtErrorLoadHistory": "Laden der Historie ist fehlgeschlagen ", "DE.Controllers.Main.txtEvenPage": "Gerade Seite", "DE.Controllers.Main.txtFiguredArrows": "Geformte Pfeile", "DE.Controllers.Main.txtFirstPage": "Erste Seite", "DE.Controllers.Main.txtFooter": "Fußzeile", "DE.Controllers.Main.txtFormulaNotInTable": "Die Formel steht nicht in einer Tabelle", "DE.Controllers.Main.txtHeader": "Kopfzeile", "DE.Controllers.Main.txtHyperlink": "Hyperlink", "DE.Controllers.Main.txtIndTooLarge": "Zu großer Index", "DE.Controllers.Main.txtLines": "<PERSON><PERSON>", "DE.Controllers.Main.txtMainDocOnly": "Fehler! Nur Hauptdokument.", "DE.Controllers.Main.txtMath": "Mathematik", "DE.Controllers.Main.txtMissArg": "<PERSON><PERSON><PERSON><PERSON> Argument", "DE.Controllers.Main.txtMissOperator": "Fehlender Operator", "DE.Controllers.Main.txtNeedSynchronize": "Änderungen wurden vorgenommen", "DE.Controllers.Main.txtNone": "Kein(e)", "DE.Controllers.Main.txtNoTableOfContents": "Dieses Dokument enthält keine Überschriften. Wenden Sie ein Überschriftenformat auf den Text an, damit es im Inhaltsverzeichnis angezeigt wird.", "DE.Controllers.Main.txtNoTableOfFigures": "Es konnten keine Einträge für ein Abbildungsverzeichnis gefunden werden.", "DE.Controllers.Main.txtNoText": "Fehler! Im Dokument gibt es keinen Text des angegebenen Stils.", "DE.Controllers.Main.txtNotInTable": "<PERSON>cht in Tabelle", "DE.Controllers.Main.txtNotValidBookmark": "Fehler! Ungültiger Lesezeichen-Link.", "DE.Controllers.Main.txtOddPage": "Ungerade Seite", "DE.Controllers.Main.txtOnPage": "auf Seite", "DE.Controllers.Main.txtRectangles": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtSameAsPrev": "Dasselbe wie zuvor", "DE.Controllers.Main.txtSection": "-<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtSeries": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_accentBorderCallout1": "Legende mit Linie 1 (<PERSON><PERSON><PERSON> und Markierungsleiste)", "DE.Controllers.Main.txtShape_accentBorderCallout2": "Legende mit Linie 2 (<PERSON><PERSON><PERSON> und Markierungsleiste)", "DE.Controllers.Main.txtShape_accentBorderCallout3": "Legende mit Linie 3 (<PERSON><PERSON><PERSON> und Markierungsleiste)", "DE.Controllers.Main.txtShape_accentCallout1": "Legende mit Linie 1 (Markierungsleiste)", "DE.Controllers.Main.txtShape_accentCallout2": "Legende mit Linie 2 (Markierungsle<PERSON><PERSON>)", "DE.Controllers.Main.txtShape_accentCallout3": "Legende mit Linie 3 (Markierungsle<PERSON><PERSON>)", "DE.Controllers.Main.txtShape_actionButtonBackPrevious": "Schaltfläche \"Zurück\"", "DE.Controllers.Main.txtShape_actionButtonBeginning": "<PERSON><PERSON> \"Start\"", "DE.Controllers.Main.txtShape_actionButtonBlank": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_actionButtonDocument": "Dokumentschaltfläche", "DE.Controllers.Main.txtShape_actionButtonEnd": "Schaltfläche „Beenden\"", "DE.Controllers.Main.txtShape_actionButtonForwardNext": "Schaltfläche 'Weiter'", "DE.Controllers.Main.txtShape_actionButtonHelp": "Schaltfläche \"Hilfe\"", "DE.Controllers.Main.txtShape_actionButtonHome": "Schaltfläche \"Startseite\"", "DE.Controllers.Main.txtShape_actionButtonInformation": "Schaltfläche \"Informationen\"", "DE.Controllers.Main.txtShape_actionButtonMovie": "Schaltfläche \"Movie\"", "DE.Controllers.Main.txtShape_actionButtonReturn": "Schaltfläche „Zurück\"", "DE.Controllers.Main.txtShape_actionButtonSound": "Schaltfläche \"Ton\"", "DE.Controllers.Main.txtShape_arc": "Bogen", "DE.Controllers.Main.txtShape_bentArrow": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_bentConnector5": "Gewinkelte Verbindung", "DE.Controllers.Main.txtShape_bentConnector5WithArrow": "Gewinkelte Verbindung mit Pfeil", "DE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Gewinkelte Verbindung mit Doppelpfeil", "DE.Controllers.Main.txtShape_bentUpArrow": "Nach oben gebogener Pfeil", "DE.Controllers.Main.txtShape_bevel": "Abschrägung", "DE.Controllers.Main.txtShape_blockArc": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_borderCallout1": "Legende mit Linie 1", "DE.Controllers.Main.txtShape_borderCallout2": "Legende mit Linie 2", "DE.Controllers.Main.txtShape_borderCallout3": "Legende mit Linie 3", "DE.Controllers.Main.txtShape_bracePair": "Geschweifte Klammer links/rechts", "DE.Controllers.Main.txtShape_callout1": "Legende mit Linie 1 (<PERSON><PERSON>)", "DE.Controllers.Main.txtShape_callout2": "Legende mit Linie 2 (<PERSON><PERSON>)", "DE.Controllers.Main.txtShape_callout3": "Legende mit Linie 3 (<PERSON><PERSON>)", "DE.Controllers.Main.txtShape_can": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_chevron": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_chord": "Akkord", "DE.Controllers.Main.txtShape_circularArrow": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_cloud": "Cloud", "DE.Controllers.Main.txtShape_cloudCallout": "<PERSON>e", "DE.Controllers.Main.txtShape_corner": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_cube": "C<PERSON>", "DE.Controllers.Main.txtShape_curvedConnector3": "Gekrümmte Verbindung", "DE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Gekrümmte Verbindung mit Pfeil", "DE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Gekrümmte Verbindung mit Doppelpfeil", "DE.Controllers.Main.txtShape_curvedDownArrow": "Nach unten gekrümmter Pfeil", "DE.Controllers.Main.txtShape_curvedLeftArrow": "Nach links gekrümmter Pfeil", "DE.Controllers.Main.txtShape_curvedRightArrow": "Nach rechts gekrümmter Pfeil", "DE.Controllers.Main.txtShape_curvedUpArrow": "Nach oben gekr<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_decagon": "Zeh<PERSON>", "DE.Controllers.Main.txtShape_diagStripe": "Diagonaler Streifen", "DE.Controllers.Main.txtShape_diamond": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_dodecagon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_donut": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_doubleWave": "Doppelte Welle", "DE.Controllers.Main.txtShape_downArrow": "<PERSON><PERSON><PERSON> nach unten", "DE.Controllers.Main.txtShape_downArrowCallout": "Legende mit Pfeil nach unten", "DE.Controllers.Main.txtShape_ellipse": "Ellipse", "DE.Controllers.Main.txtShape_ellipseRibbon": "Nach unten gekrümmtes Band", "DE.Controllers.Main.txtShape_ellipseRibbon2": "Nach oben gekrümmtes Band", "DE.Controllers.Main.txtShape_flowChartAlternateProcess": "Flussdiagramm: Alternativer Prozess", "DE.Controllers.Main.txtShape_flowChartCollate": "Flussdiagramm: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_flowChartConnector": "Flussdiagramm: Verbindungsstelle", "DE.Controllers.Main.txtShape_flowChartDecision": "Flussdiagramm: Verzweigung", "DE.Controllers.Main.txtShape_flowChartDelay": "Flussdiagramm: Verzögerung", "DE.Controllers.Main.txtShape_flowChartDisplay": "Flussdiagramm: Anzeige", "DE.Controllers.Main.txtShape_flowChartDocument": "Flussdiagramm: Dokument", "DE.Controllers.Main.txtShape_flowChartExtract": "Flussdiagramm: Auszug", "DE.Controllers.Main.txtShape_flowChartInputOutput": "Flussdiagramm: Daten", "DE.Controllers.Main.txtShape_flowChartInternalStorage": "Flussdiagramm: Zentralspeicher", "DE.Controllers.Main.txtShape_flowChartMagneticDisk": "Flussdiagramm: Magnetplattenspeicher", "DE.Controllers.Main.txtShape_flowChartMagneticDrum": "Flussdiagramm: Datenträger mit direktem Zugriff", "DE.Controllers.Main.txtShape_flowChartMagneticTape": "Flussdiagramm: Datenträger mit sequenziellem Zugriff", "DE.Controllers.Main.txtShape_flowChartManualInput": "Flussdiagramm: <PERSON><PERSON>", "DE.Controllers.Main.txtShape_flowChartManualOperation": "Flussdiagramm: <PERSON><PERSON> Verarbeitung", "DE.Controllers.Main.txtShape_flowChartMerge": "Flussdiagramm: Z<PERSON>mmenführen", "DE.Controllers.Main.txtShape_flowChartMultidocument": "Flussdiagramm: <PERSON><PERSON>ere Dokumente", "DE.Controllers.Main.txtShape_flowChartOffpageConnector": "Flussdiagramm: Verbindungsstelle zu einer anderen Seite", "DE.Controllers.Main.txtShape_flowChartOnlineStorage": "Flussdiagramm: Gespeicherte Daten", "DE.Controllers.Main.txtShape_flowChartOr": "Flussdiagramm", "DE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Flussdiagramm: Vordefinierter Prozess", "DE.Controllers.Main.txtShape_flowChartPreparation": "Flussdiagramm: Vorbereitung", "DE.Controllers.Main.txtShape_flowChartProcess": "Flussdiagramm: Pro<PERSON>s", "DE.Controllers.Main.txtShape_flowChartPunchedCard": "Flussdiagramm: <PERSON><PERSON>", "DE.Controllers.Main.txtShape_flowChartPunchedTape": "Flussdiagramm: Lochstreifen", "DE.Controllers.Main.txtShape_flowChartSort": "Flussdiagramm: Sortieren", "DE.Controllers.Main.txtShape_flowChartSummingJunction": "Flussdiagramm: Zusammenführung", "DE.Controllers.Main.txtShape_flowChartTerminator": "Flussdiagramm: Grenzstelle", "DE.Controllers.Main.txtShape_foldedCorner": "Gefal<PERSON><PERSON>", "DE.Controllers.Main.txtShape_frame": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_halfFrame": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_heart": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_heptagon": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_hexagon": "Sechseck", "DE.Controllers.Main.txtShape_homePlate": "Richtungspfeil", "DE.Controllers.Main.txtShape_horizontalScroll": "Horizontaler Bildlauf", "DE.Controllers.Main.txtShape_irregularSeal1": "Explosion 1", "DE.Controllers.Main.txtShape_irregularSeal2": "Explosion 2", "DE.Controllers.Main.txtShape_leftArrow": "Pfeil nach links", "DE.Controllers.Main.txtShape_leftArrowCallout": "Legende mit Pfeil nach links", "DE.Controllers.Main.txtShape_leftBrace": "Geschweifte Klammer links", "DE.Controllers.Main.txtShape_leftBracket": "Runde K<PERSON> links", "DE.Controllers.Main.txtShape_leftRightArrow": "Pfeil nach links und rechts", "DE.Controllers.Main.txtShape_leftRightArrowCallout": "Legende mit Pfeil nach links und rechts", "DE.Controllers.Main.txtShape_leftRightUpArrow": "Pfeil nach links, rechts und oben", "DE.Controllers.Main.txtShape_leftUpArrow": "Pfeil nach links und oben", "DE.Controllers.Main.txtShape_lightningBolt": "Gewitterblitz", "DE.Controllers.Main.txtShape_line": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_lineWithArrow": "Pfeil", "DE.Controllers.Main.txtShape_lineWithTwoArrows": "Doppelpfeil", "DE.Controllers.Main.txtShape_mathDivide": "Division", "DE.Controllers.Main.txtShape_mathEqual": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_mathMinus": "Minus", "DE.Controllers.Main.txtShape_mathMultiply": "Multiplizieren", "DE.Controllers.Main.txtShape_mathNotEqual": "<PERSON><PERSON> gleich", "DE.Controllers.Main.txtShape_mathPlus": "Plus", "DE.Controllers.Main.txtShape_moon": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_noSmoking": "Symbol \"Nein\"", "DE.Controllers.Main.txtShape_notchedRightArrow": "Eingekerbter Pfeil nach rechts", "DE.Controllers.Main.txtShape_octagon": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_parallelogram": "Parallelogramm", "DE.Controllers.Main.txtShape_pentagon": "Richtungspfeil", "DE.Controllers.Main.txtShape_pie": "Kreis", "DE.Controllers.Main.txtShape_plaque": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_plus": "Plus", "DE.Controllers.Main.txtShape_polyline1": "Skizze", "DE.Controllers.Main.txtShape_polyline2": "Freihandform", "DE.Controllers.Main.txtShape_quadArrow": "Pfeil in vier Richtungen", "DE.Controllers.Main.txtShape_quadArrowCallout": "Legende mit Pfeil in vier Richtungen", "DE.Controllers.Main.txtShape_rect": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_ribbon": "Band nach unten", "DE.Controllers.Main.txtShape_ribbon2": "<PERSON> hoch", "DE.Controllers.Main.txtShape_rightArrow": "<PERSON><PERSON><PERSON> nach rechts", "DE.Controllers.Main.txtShape_rightArrowCallout": "Legende mit Pfeil nach rechts", "DE.Controllers.Main.txtShape_rightBrace": "Geschweifte Klammer rechts", "DE.Controllers.Main.txtShape_rightBracket": "<PERSON><PERSON> re<PERSON>s", "DE.Controllers.Main.txtShape_round1Rect": "Eine Ecke des Rechtecks abrunden", "DE.Controllers.Main.txtShape_round2DiagRect": "Diagonal liegende Ecken des Rechtecks abrunden", "DE.Controllers.Main.txtShape_round2SameRect": "Auf der gleichen Seite des Rechtecks liegende Ecken abrunden", "DE.Controllers.Main.txtShape_roundRect": "Rechteck mit runden Ecken", "DE.Controllers.Main.txtShape_rtTriangle": "Rechtwinkliges Dreieck", "DE.Controllers.Main.txtShape_smileyFace": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_snip1Rect": "Eine Ecke des Rechtecks schneiden", "DE.Controllers.Main.txtShape_snip2DiagRect": "Diagonal liegende Ecken des Rechtecks schneiden", "DE.Controllers.Main.txtShape_snip2SameRect": "Ecken des Rechtecks auf der gleichen Seite schneiden", "DE.Controllers.Main.txtShape_snipRoundRect": "Eine Ecke des Rechtecks schneiden und abrunden", "DE.Controllers.Main.txtShape_spline": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_star10": "10-<PERSON><PERSON>", "DE.Controllers.Main.txtShape_star12": "12-<PERSON><PERSON>", "DE.Controllers.Main.txtShape_star16": "16-<PERSON><PERSON>", "DE.Controllers.Main.txtShape_star24": "24-<PERSON><PERSON>", "DE.Controllers.Main.txtShape_star32": "32-<PERSON><PERSON>", "DE.Controllers.Main.txtShape_star4": "4-<PERSON><PERSON>", "DE.Controllers.Main.txtShape_star5": "5-<PERSON><PERSON>", "DE.Controllers.Main.txtShape_star6": "6-<PERSON><PERSON>", "DE.Controllers.Main.txtShape_star7": "7-<PERSON><PERSON>", "DE.Controllers.Main.txtShape_star8": "8-<PERSON><PERSON>", "DE.Controllers.Main.txtShape_stripedRightArrow": "Gestreifter Pfeil nach rechts", "DE.Controllers.Main.txtShape_sun": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_teardrop": "Tropfenförmig", "DE.Controllers.Main.txtShape_textRect": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_trapezoid": "Trapezoid", "DE.Controllers.Main.txtShape_triangle": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_upArrow": "<PERSON><PERSON><PERSON> nach oben", "DE.Controllers.Main.txtShape_upArrowCallout": "Legende mit Pfeil nach oben", "DE.Controllers.Main.txtShape_upDownArrow": "<PERSON><PERSON><PERSON> nach unten", "DE.Controllers.Main.txtShape_uturnArrow": "180-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_verticalScroll": "<PERSON><PERSON><PERSON><PERSON> Bildlau<PERSON>", "DE.Controllers.Main.txtShape_wave": "Welle", "DE.Controllers.Main.txtShape_wedgeEllipseCallout": "<PERSON>e Legende", "DE.Controllers.Main.txtShape_wedgeRectCallout": "Rechteckige Legende", "DE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Abgerundete rechteckige Legende", "DE.Controllers.Main.txtStarsRibbons": "Sterne und Bänder", "DE.Controllers.Main.txtStyle_Caption": "Beschriftung", "DE.Controllers.Main.txtStyle_endnote_text": "Endnotentext", "DE.Controllers.Main.txtStyle_footnote_text": "Fußnotentext", "DE.Controllers.Main.txtStyle_Heading_1": "Überschrift 1", "DE.Controllers.Main.txtStyle_Heading_2": "Überschrift 2", "DE.Controllers.Main.txtStyle_Heading_3": "Überschrift 3", "DE.Controllers.Main.txtStyle_Heading_4": "Überschrift 4", "DE.Controllers.Main.txtStyle_Heading_5": "Überschrift 5", "DE.Controllers.Main.txtStyle_Heading_6": "Überschrift 6", "DE.Controllers.Main.txtStyle_Heading_7": "Überschrift 7", "DE.Controllers.Main.txtStyle_Heading_8": "Überschrift 8", "DE.Controllers.Main.txtStyle_Heading_9": "Überschrift 9", "DE.Controllers.Main.txtStyle_Intense_Quote": "Intensives Zitat", "DE.Controllers.Main.txtStyle_List_Paragraph": "Listenabsatz", "DE.Controllers.Main.txtStyle_No_Spacing": "<PERSON><PERSON>", "DE.Controllers.Main.txtStyle_Normal": "Normal", "DE.Controllers.Main.txtStyle_Quote": "Zitat", "DE.Controllers.Main.txtStyle_Subtitle": "Untertitel", "DE.Controllers.Main.txtStyle_Title": "Titel", "DE.Controllers.Main.txtSyntaxError": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtTableInd": "Tabellenindex darf nicht <PERSON> sein", "DE.Controllers.Main.txtTableOfContents": "Inhaltsverzeichnis", "DE.Controllers.Main.txtTableOfFigures": "Abbildungsverzeichnis", "DE.Controllers.Main.txtTOCHeading": "Inhaltsverzeichnisüberschrift", "DE.Controllers.Main.txtTooLarge": "<PERSON><PERSON><PERSON> zu groß zum Formatieren", "DE.Controllers.Main.txtTypeEquation": "Hier die Gleichung eingeben.", "DE.Controllers.Main.txtUndefBookmark": "Undefiniertes Lesezeichen", "DE.Controllers.Main.txtXAxis": "x-<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtYAxis": "y-<PERSON><PERSON>e", "DE.Controllers.Main.txtZeroDivide": "Nullteilung", "DE.Controllers.Main.unknownErrorText": "Unbek<PERSON><PERSON> Fehler.", "DE.Controllers.Main.unsupportedBrowserErrorText": "<PERSON><PERSON> wird nicht unterstützt.", "DE.Controllers.Main.uploadDocExtMessage": "Unbekanntes Dokumentformat.", "DE.Controllers.Main.uploadDocFileCountMessage": "Keine Dokumente hochgeladen.", "DE.Controllers.Main.uploadDocSizeMessage": "Maximale Dokumentgröße ist überschritten.", "DE.Controllers.Main.uploadImageExtMessage": "Unbekanntes Bildformat.", "DE.Controllers.Main.uploadImageFileCountMessage": "<PERSON><PERSON>d wird hoch<PERSON>aden.", "DE.Controllers.Main.uploadImageSizeMessage": "Die maximal zulässige Bildgröße von 25 MB ist überschritten.", "DE.Controllers.Main.uploadImageTextText": "Das Bild wird hochgeladen...", "DE.Controllers.Main.uploadImageTitleText": "Bild wird hoch<PERSON>aden", "DE.Controllers.Main.waitText": "Bitte warten...", "DE.Controllers.Main.warnBrowserIE9": "Die Applkation hat geringte Fähigkeiten in IE9. Nutzen Sie IE10 oder höher.", "DE.Controllers.Main.warnBrowserZoom": "Die aktuelle Zoom-Einstellung Ihres Webbrowsers wird nicht völlig unterstützt. Bitte stellen Sie die Standardeinstellung mithilfe der Tastenkombination Strg+0 wieder her.", "DE.Controllers.Main.warnLicenseExceeded": "Sie haben das Limit für gleichzeitige Verbindungen in %1-Editoren erreicht. Dieses Dokument wird nur zum Anzeigen geöffnet.<br><PERSON>te wenden Si<PERSON> sich an Ihren Administrator, um weitere Informationen zu erhalten.", "DE.Controllers.Main.warnLicenseExp": "<PERSON>hre Lizenz ist abgelaufen.<br>Bitte aktualisieren Sie Ihre Lizenz und laden Si<PERSON> die Seite neu.", "DE.Controllers.Main.warnLicenseLimitedNoAccess": "Die Lizenz ist abgelaufen.<br>Die Bearbeitungsfunktionen sind nicht verfügbar.<br><PERSON><PERSON> wenden <PERSON> sich an Ihrem Administrator.", "DE.Controllers.Main.warnLicenseLimitedRenewed": "Die Lizenz soll aktualisiert werden.<br>Die Bearbeitungsfunktionen sind eingeschränkt.<br><PERSON>te wenden Si<PERSON> sich an Ihrem Administrator für vollen Zugriff", "DE.Controllers.Main.warnLicenseUsersExceeded": "Sie haben das Benutzerlimit für %1-Editoren erreicht. Bitte wenden Si<PERSON> sich an Ihren Administrator, um weitere Informationen zu erhalten.", "DE.Controllers.Main.warnNoLicense": "Sie haben das Limit für gleichzeitige Verbindungen in %1-Editoren erreicht. Dieses Dokument wird nur zum Anzeigen geöffnet.<br>Bitte kontaktieren Sie unser Verkaufsteam, um persönliche Upgrade-Bedingungen zu erhalten.", "DE.Controllers.Main.warnNoLicenseUsers": "Sie haben das Benutzerlimit für %1-Editoren erreicht. Bitte kontaktieren Sie unser Verkaufsteam, um persönliche Upgrade-Bedingungen zu erhalten.", "DE.Controllers.Main.warnProcessRightsChange": "<PERSON>, die Datei zu bearbeiten, wurde Ihnen verweigert.", "DE.Controllers.Navigation.txtBeginning": "Anfang des Dokuments", "DE.Controllers.Navigation.txtGotoBeginning": "Zum Anfang des Dokuments übergehnen", "DE.Controllers.Print.textMarginsLast": " Benutzerdefiniert als letzte", "DE.Controllers.Print.txtCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Print.txtPrintRangeInvalid": "Ungültiger Druckbereich", "DE.Controllers.Print.txtPrintRangeSingleRange": "<PERSON><PERSON><PERSON> Si<PERSON> entweder eine einzelne Seitenzahl oder einen einzelnen Seitenbereich ein (z. B. 5-12). Oder Sie können in PDF drucken.", "DE.Controllers.Search.notcriticalErrorTitle": "Achtung", "DE.Controllers.Search.textNoTextFound": "<PERSON> Daten, nach denen <PERSON> gesucht haben, können nicht gefunden werden. Bitte ändern Sie die Suchparameter.", "DE.Controllers.Search.textReplaceSkipped": "Der Ersatzvorgang wurde durchgeführt. {0} Vorkommen wurden ausgelassen.", "DE.Controllers.Search.textReplaceSuccess": "Die Suche wurde durchgeführt. {0} Einträge wurden ersetzt", "DE.Controllers.Search.warnReplaceString": "{0} kann als Sonderzeichen für das Feld \"Ersetzen durch\" nicht verwendet werden.", "DE.Controllers.Statusbar.textDisconnect": "<b>Die Verbindung wurde unterbrochen</b><br>Verbindungsversuch... Bitte Verbindungseinstellungen überprüfen.", "DE.Controllers.Statusbar.textHasChanges": "Neue Änderungen wurden zurückverfolgt", "DE.Controllers.Statusbar.textSetTrackChanges": "Nachverfolgung von Änderungen ist aktiv", "DE.Controllers.Statusbar.textTrackChanges": "Das Dokument wird im Modus \"Nachverfolgen von Änderungen\" geöffnet. ", "DE.Controllers.Statusbar.tipReview": "Nachverfo<PERSON><PERSON> von <PERSON>", "DE.Controllers.Statusbar.zoomText": "Zoom {0}%", "DE.Controllers.Toolbar.confirmAddFontName": "<PERSON> Schriftart, die Sie verwenden wollen, ist auf diesem Gerät nicht verfügbar.<br>Der Textstil wird mit einer der Systemschriften angezeigt, die gespeicherte Schriftart wird verwendet, wenn sie verfügbar ist.<br>Wollen Sie fortsetzen?", "DE.Controllers.Toolbar.dataUrl": "Eine URL der Daten einfügen", "DE.Controllers.Toolbar.notcriticalErrorTitle": "Achtung", "DE.Controllers.Toolbar.textAccent": "Akzente", "DE.Controllers.Toolbar.textBracket": "Klammern", "DE.Controllers.Toolbar.textEmptyImgUrl": "Sie müssen eine Bild-URL angeben.", "DE.Controllers.Toolbar.textEmptyMMergeUrl": "Geben Sie URL ein.", "DE.Controllers.Toolbar.textFontSizeErr": "Der eingegebene Wert ist falsch.<br><PERSON><PERSON><PERSON> Sie bitte einen numerischen Wert zwischen 1 und 300 ein.", "DE.Controllers.Toolbar.textFraction": "Bruchteile", "DE.Controllers.Toolbar.textFunction": "Funktionen", "DE.Controllers.Toolbar.textGroup": "Gruppe", "DE.Controllers.Toolbar.textInsert": "Einfügen", "DE.Controllers.Toolbar.textIntegral": "Integrale", "DE.Controllers.Toolbar.textLargeOperator": "Große Operatoren", "DE.Controllers.Toolbar.textLimitAndLog": "Grenzwerte und Logarithmen", "DE.Controllers.Toolbar.textMatrix": "Matrizen", "DE.Controllers.Toolbar.textOperator": "Operatoren", "DE.Controllers.Toolbar.textRadical": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.textRecentlyUsed": "Zuletzt verwendet", "DE.Controllers.Toolbar.textScript": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.textSymbols": "Symbole", "DE.Controllers.Toolbar.textTabForms": "Formulare", "DE.Controllers.Toolbar.textWarning": "Achtung", "DE.Controllers.Toolbar.txtAccent_Accent": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtAccent_ArrowD": "Pfeil nach rechts und links oben", "DE.Controllers.Toolbar.txtAccent_ArrowL": "Pfeil nach links oben", "DE.Controllers.Toolbar.txtAccent_ArrowR": "<PERSON><PERSON><PERSON> nach rechts oben", "DE.Controllers.Toolbar.txtAccent_Bar": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtAccent_BarBot": "Unterstreichung", "DE.Controllers.Toolbar.txtAccent_BarTop": "Überstreichung", "DE.Controllers.Toolbar.txtAccent_BorderBox": "Geschachtelte Formel (mit Platzhalter)", "DE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Geschachtelte Formel (Beispiel)", "DE.Controllers.Toolbar.txtAccent_Check": "Prüfen", "DE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Horizontale geschweifte Klammer (unten)", "DE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Horizontale geschweifte Klammer (oben)", "DE.Controllers.Toolbar.txtAccent_Custom_1": "Vektor A", "DE.Controllers.Toolbar.txtAccent_Custom_2": "ABC mit Überstreichung", "DE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y Mit Überstreichung", "DE.Controllers.Toolbar.txtAccent_DDDot": "Dreifa<PERSON>", "DE.Controllers.Toolbar.txtAccent_DDot": "Doppelpunkt", "DE.Controllers.Toolbar.txtAccent_Dot": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtAccent_DoubleBar": "Doppelte Überstreichung", "DE.Controllers.Toolbar.txtAccent_Grave": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtAccent_GroupBot": "Gruppierungszeichen unten", "DE.Controllers.Toolbar.txtAccent_GroupTop": "Gruppierungszeichen oben", "DE.Controllers.Toolbar.txtAccent_HarpoonL": "Ha<PERSON>une nach links oben", "DE.Controllers.Toolbar.txtAccent_HarpoonR": "Ha<PERSON><PERSON> nach rechts oben", "DE.Controllers.Toolbar.txtAccent_Hat": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtAccent_Smile": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtAccent_Tilde": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Angle": "Klammern", "DE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Klammern mit Trennlinien", "DE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Klammern mit Trennlinien", "DE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "<PERSON><PERSON><PERSON> e<PERSON>", "DE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "<PERSON><PERSON><PERSON> e<PERSON>", "DE.Controllers.Toolbar.txtBracket_Curve": "Klammern", "DE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Klammern mit Trennlinien", "DE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "<PERSON><PERSON><PERSON> e<PERSON>", "DE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "<PERSON><PERSON><PERSON> e<PERSON>", "DE.Controllers.Toolbar.txtBracket_Custom_1": "Fälle (zwei Bedingungen)", "DE.Controllers.Toolbar.txtBracket_Custom_2": "<PERSON>ä<PERSON> (drei Bedingungen)", "DE.Controllers.Toolbar.txtBracket_Custom_3": "Stapelobjekt", "DE.Controllers.Toolbar.txtBracket_Custom_4": "Stapelobjekt", "DE.Controllers.Toolbar.txtBracket_Custom_5": "Fallbeispiele", "DE.Controllers.Toolbar.txtBracket_Custom_6": "Binomialkoeffizient", "DE.Controllers.Toolbar.txtBracket_Custom_7": "Binomialkoeffizient", "DE.Controllers.Toolbar.txtBracket_Line": "Klammern", "DE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "<PERSON><PERSON><PERSON> e<PERSON>", "DE.Controllers.Toolbar.txtBracket_Line_OpenNone": "<PERSON><PERSON><PERSON> e<PERSON>", "DE.Controllers.Toolbar.txtBracket_LineDouble": "Klammern", "DE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "<PERSON><PERSON><PERSON> e<PERSON>", "DE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "<PERSON><PERSON><PERSON> e<PERSON>", "DE.Controllers.Toolbar.txtBracket_LowLim": "Klammern", "DE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "<PERSON><PERSON><PERSON> e<PERSON>", "DE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "<PERSON><PERSON><PERSON> e<PERSON>", "DE.Controllers.Toolbar.txtBracket_Round": "Klammern", "DE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Klammern mit Trennlinien", "DE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "<PERSON><PERSON><PERSON> e<PERSON>", "DE.Controllers.Toolbar.txtBracket_Round_OpenNone": "<PERSON><PERSON><PERSON> e<PERSON>", "DE.Controllers.Toolbar.txtBracket_Square": "Klammern", "DE.Controllers.Toolbar.txtBracket_Square_CloseClose": "Klammern", "DE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "Klammern", "DE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "<PERSON><PERSON><PERSON> e<PERSON>", "DE.Controllers.Toolbar.txtBracket_Square_OpenNone": "<PERSON><PERSON><PERSON> e<PERSON>", "DE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "Klammern", "DE.Controllers.Toolbar.txtBracket_SquareDouble": "Klammern", "DE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "<PERSON><PERSON><PERSON> e<PERSON>", "DE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "<PERSON><PERSON><PERSON> e<PERSON>", "DE.Controllers.Toolbar.txtBracket_UppLim": "Klammern", "DE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "<PERSON><PERSON><PERSON> e<PERSON>", "DE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "<PERSON><PERSON><PERSON> e<PERSON>", "DE.Controllers.Toolbar.txtFractionDiagonal": "Versetzter Bruch mit schrägem Bruchstrich", "DE.Controllers.Toolbar.txtFractionDifferential_1": "Differenzial", "DE.Controllers.Toolbar.txtFractionDifferential_2": "Differenzial", "DE.Controllers.Toolbar.txtFractionDifferential_3": "Differenzial", "DE.Controllers.Toolbar.txtFractionDifferential_4": "Differenzial", "DE.Controllers.Toolbar.txtFractionHorizontal": "Bruch mit schrägem Bruchstrich", "DE.Controllers.Toolbar.txtFractionPi_2": "Pi wird durch 2 dividiert", "DE.Controllers.Toolbar.txtFractionSmall": "<PERSON><PERSON> Bru<PERSON>hl", "DE.Controllers.Toolbar.txtFractionVertical": "<PERSON>ruch mit waagerechtem Bruchstrich", "DE.Controllers.Toolbar.txtFunction_1_Cos": "Umgekehrte Kosinus-Funktion", "DE.Controllers.Toolbar.txtFunction_1_Cosh": "Hyperbolische umgekehrte Kosinus-Funktion", "DE.Controllers.Toolbar.txtFunction_1_Cot": "Umgekehrte Kotangens-Funktion", "DE.Controllers.Toolbar.txtFunction_1_Coth": "Hyperbolische umgekehrte Kotangens-Funktion", "DE.Controllers.Toolbar.txtFunction_1_Csc": "Umgekehrte Kosekansfunktion", "DE.Controllers.Toolbar.txtFunction_1_Csch": "Hyperbolische umgekehrte Kosekans-Funktion", "DE.Controllers.Toolbar.txtFunction_1_Sec": "Umgekehrte Sekans-Funktion", "DE.Controllers.Toolbar.txtFunction_1_Sech": "Hyperbolische umgekehrte Sekans-Funktion", "DE.Controllers.Toolbar.txtFunction_1_Sin": "Umgekehrte Sinus-Funktion", "DE.Controllers.Toolbar.txtFunction_1_Sinh": "Hyperbolische umgekehrte Sinus-Funktion", "DE.Controllers.Toolbar.txtFunction_1_Tan": "Umgekehrte Tangens-Funktion", "DE.Controllers.Toolbar.txtFunction_1_Tanh": "Hyperbolische umgekehrte Tangens-Funktion", "DE.Controllers.Toolbar.txtFunction_Cos": "Kosinusfunktion", "DE.Controllers.Toolbar.txtFunction_Cosh": "Hyperbolische Kosinusfunktion", "DE.Controllers.Toolbar.txtFunction_Cot": "Kotangensfunktion", "DE.Controllers.Toolbar.txtFunction_Coth": "Hyperbolische Kotangensfunktion", "DE.Controllers.Toolbar.txtFunction_Csc": "Kosekansfunktion", "DE.Controllers.Toolbar.txtFunction_Csch": "Hyperbolische Kosekansfunktion", "DE.Controllers.Toolbar.txtFunction_Custom_1": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtFunction_Custom_2": "Kosinus 2x", "DE.Controllers.Toolbar.txtFunction_Custom_3": "Tangensformel", "DE.Controllers.Toolbar.txtFunction_Sec": "Sekans-Funktion", "DE.Controllers.Toolbar.txtFunction_Sech": "Hyperbolische Sekans-Funktion", "DE.Controllers.Toolbar.txtFunction_Sin": "Sinus-Funktion", "DE.Controllers.Toolbar.txtFunction_Sinh": "Hyperbolische Sinus-Funktion", "DE.Controllers.Toolbar.txtFunction_Tan": "Tangens-Funktion", "DE.Controllers.Toolbar.txtFunction_Tanh": "Hyperbolische Tangens-Funktion", "DE.Controllers.Toolbar.txtIntegral": "Integral", "DE.Controllers.Toolbar.txtIntegral_dtheta": "Differenzial Theta", "DE.Controllers.Toolbar.txtIntegral_dx": "Differenzial x", "DE.Controllers.Toolbar.txtIntegral_dy": "Differenzial y", "DE.Controllers.Toolbar.txtIntegralCenterSubSup": "Integral", "DE.Controllers.Toolbar.txtIntegralDouble": "Doppelintegral", "DE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Doppelintegral", "DE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Doppelintegral", "DE.Controllers.Toolbar.txtIntegralOriented": "Konturenintegral", "DE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Konturenintegral", "DE.Controllers.Toolbar.txtIntegralOrientedDouble": "Oberflächenintegral", "DE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Oberflächenintegral", "DE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Oberflächenintegral", "DE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Konturenintegral", "DE.Controllers.Toolbar.txtIntegralOrientedTriple": "Volumenintegral", "DE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Volumenintegral", "DE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Volumenintegral", "DE.Controllers.Toolbar.txtIntegralSubSup": "Integral", "DE.Controllers.Toolbar.txtIntegralTriple": "Dreifaches Integral", "DE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Dreifaches Integral", "DE.Controllers.Toolbar.txtIntegralTripleSubSup": "Dreifaches Integral", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Keil", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Keil", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Keil", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Keil", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Keil", "DE.Controllers.Toolbar.txtLargeOperator_CoProd": "Ko<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Ko<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Ko<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Ko<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Ko<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Summenbildung", "DE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Summenbildung", "DE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Summenbildung", "DE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Produkt", "DE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Vereinigung", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction": "V-förmig", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "V-förmig", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "V-förmig", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "V-förmig", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "V-förmig", "DE.Controllers.Toolbar.txtLargeOperator_Intersection": "Schnittmenge", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Schnittmenge", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Schnittmenge", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Schnittmenge", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Schnittmenge", "DE.Controllers.Toolbar.txtLargeOperator_Prod": "Produkt", "DE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Produkt", "DE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Produkt", "DE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Produkt", "DE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Produkt", "DE.Controllers.Toolbar.txtLargeOperator_Sum": "Summenbildung", "DE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Summenbildung", "DE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Summenbildung", "DE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Summenbildung", "DE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Summenbildung", "DE.Controllers.Toolbar.txtLargeOperator_Union": "Vereinigung", "DE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Vereinigung", "DE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Vereinigung", "DE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Vereinigung", "DE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Vereinigung", "DE.Controllers.Toolbar.txtLimitLog_Custom_1": "Beispiel für Grenzwert", "DE.Controllers.Toolbar.txtLimitLog_Custom_2": "Beispiel für Maximum", "DE.Controllers.Toolbar.txtLimitLog_Lim": "Grenzwert", "DE.Controllers.Toolbar.txtLimitLog_Ln": "Natürlicher Logarithmus", "DE.Controllers.Toolbar.txtLimitLog_Log": "Logarith<PERSON>", "DE.Controllers.Toolbar.txtLimitLog_LogBase": "Logarith<PERSON>", "DE.Controllers.Toolbar.txtLimitLog_Max": "Maximal", "DE.Controllers.Toolbar.txtLimitLog_Min": "Minimal", "DE.Controllers.Toolbar.txtMarginsH": "Die oberen und unteren Ränder sind zu hoch für eingegebene Seitenhöhe", "DE.Controllers.Toolbar.txtMarginsW": "Die Ränder rechts und links sind bei gegebener Seitenbreite zu breit. ", "DE.Controllers.Toolbar.txtMatrix_1_2": "1x2 leere Matrix", "DE.Controllers.Toolbar.txtMatrix_1_3": "1x3 leere Matrix", "DE.Controllers.Toolbar.txtMatrix_2_1": "2x1 leere Matrix", "DE.Controllers.Toolbar.txtMatrix_2_2": "2x2 leere Matrix", "DE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "<PERSON>re Matrix mit Klammern", "DE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "<PERSON>re Matrix mit Klammern", "DE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "<PERSON>re Matrix mit Klammern", "DE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "<PERSON>re Matrix mit Klammern", "DE.Controllers.Toolbar.txtMatrix_2_3": "2x3 leere Matrix", "DE.Controllers.Toolbar.txtMatrix_3_1": "3x1 leere Matrix", "DE.Controllers.Toolbar.txtMatrix_3_2": "3x2 leere Matrix", "DE.Controllers.Toolbar.txtMatrix_3_3": "3x3 leere Matrix", "DE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Grundlinienpunkte", "DE.Controllers.Toolbar.txtMatrix_Dots_Center": "Mittellinienpunkte", "DE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Diagonale Punkte", "DE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtMatrix_Flat_Round": "Dünnbesetzte Matrix", "DE.Controllers.Toolbar.txtMatrix_Flat_Square": "Dünnbesetzte Matrix", "DE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2 Identitätsmatrix", "DE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "3x3 Identitätsmatrix", "DE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3 Identitätsmatrix", "DE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 Identitätsmatrix", "DE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Pfeil nach rechts und links unten", "DE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Pfeil nach rechts und links oben", "DE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "<PERSON><PERSON>il nach links unten", "DE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Pfeil nach links oben", "DE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "<PERSON><PERSON><PERSON> nach rechts unten", "DE.Controllers.Toolbar.txtOperator_ArrowR_Top": "<PERSON><PERSON><PERSON> nach rechts oben", "DE.Controllers.Toolbar.txtOperator_ColonEquals": "Doppelpunkt gleich", "DE.Controllers.Toolbar.txtOperator_Custom_1": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtOperator_Custom_2": "Delta ergibt", "DE.Controllers.Toolbar.txtOperator_Definition": "<PERSON><PERSON><PERSON> gemäß Definition", "DE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta gleich", "DE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Pfeil nach rechts und links unten", "DE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Pfeil nach rechts und links oben", "DE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "<PERSON><PERSON>il nach links unten", "DE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Pfeil nach links oben", "DE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "<PERSON><PERSON><PERSON> nach rechts unten", "DE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "<PERSON><PERSON><PERSON> nach rechts oben", "DE.Controllers.Toolbar.txtOperator_EqualsEquals": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtOperator_MinusEquals": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtOperator_PlusEquals": "Plus Gleich", "DE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "<PERSON><PERSON><PERSON><PERSON> an", "DE.Controllers.Toolbar.txtRadicalCustom_1": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtRadicalCustom_2": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtRadicalRoot_2": "Quadratwurzel mit Grad", "DE.Controllers.Toolbar.txtRadicalRoot_3": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtRadicalRoot_n": "<PERSON><PERSON><PERSON> mit Grad", "DE.Controllers.Toolbar.txtRadicalSqrt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtScriptCustom_1": "S<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtScriptCustom_2": "S<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtScriptCustom_3": "S<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtScriptCustom_4": "S<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtScriptSub": "Tiefgestellt", "DE.Controllers.Toolbar.txtScriptSubSup": "Tiefgestellt-Hochgestellt", "DE.Controllers.Toolbar.txtScriptSubSupLeft": "Hochgestellter/ tiefgestellter Index links", "DE.Controllers.Toolbar.txtScriptSup": "Hochgestellt", "DE.Controllers.Toolbar.txtSymbol_about": "Ungefähr", "DE.Controllers.Toolbar.txtSymbol_additional": "Komplement", "DE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "DE.Controllers.Toolbar.txtSymbol_alpha": "Alpha", "DE.Controllers.Toolbar.txtSymbol_approx": "Fast gleich", "DE.Controllers.Toolbar.txtSymbol_ast": "Stern-Operator", "DE.Controllers.Toolbar.txtSymbol_beta": "Beta", "DE.Controllers.Toolbar.txtSymbol_beth": "Bet", "DE.Controllers.Toolbar.txtSymbol_bullet": "Stichpunktoperator", "DE.Controllers.Toolbar.txtSymbol_cap": "Schnittmenge", "DE.Controllers.Toolbar.txtSymbol_cbrt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_cdots": "Horizontale Ellipse (Mittellinie)", "DE.Controllers.Toolbar.txtSymbol_celsius": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "DE.Controllers.Toolbar.txtSymbol_cong": "Ungefähr gleich ", "DE.Controllers.Toolbar.txtSymbol_cup": "Vereinigung", "DE.Controllers.Toolbar.txtSymbol_ddots": "Diagonale Ellipse nach unten rechts", "DE.Controllers.Toolbar.txtSymbol_degree": "Grad", "DE.Controllers.Toolbar.txtSymbol_delta": "Delta", "DE.Controllers.Toolbar.txtSymbol_div": "Divisionszeichen", "DE.Controllers.Toolbar.txtSymbol_downarrow": "<PERSON><PERSON><PERSON> nach unten", "DE.Controllers.Toolbar.txtSymbol_emptyset": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "DE.Controllers.Toolbar.txtSymbol_equals": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_equiv": "Identisch mit", "DE.Controllers.Toolbar.txtSymbol_eta": "Eta", "DE.Controllers.Toolbar.txtSymbol_exists": "Vorhand<PERSON>", "DE.Controllers.Toolbar.txtSymbol_factorial": "Faktoriell", "DE.Controllers.Toolbar.txtSymbol_fahrenheit": "Grad Fahrenheit", "DE.Controllers.Toolbar.txtSymbol_forall": "<PERSON><PERSON><PERSON> alle", "DE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "DE.Controllers.Toolbar.txtSymbol_geq": "<PERSON><PERSON><PERSON><PERSON><PERSON> als oder gleich wie ", "DE.Controllers.Toolbar.txtSymbol_gg": "Viel g<PERSON>ößer als", "DE.Controllers.Toolbar.txtSymbol_greater": "<PERSON><PERSON><PERSON><PERSON><PERSON> als", "DE.Controllers.Toolbar.txtSymbol_in": "Element", "DE.Controllers.Toolbar.txtSymbol_inc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_infinity": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_iota": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "DE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "DE.Controllers.Toolbar.txtSymbol_leftarrow": "Pfeil nach links", "DE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Pfeil nach rechts und links", "DE.Controllers.Toolbar.txtSymbol_leq": "<PERSON>er als oder gleich", "DE.Controllers.Toolbar.txtSymbol_less": "<PERSON><PERSON>s", "DE.Controllers.Toolbar.txtSymbol_ll": "<PERSON><PERSON> kleiner als", "DE.Controllers.Toolbar.txtSymbol_minus": "Minus", "DE.Controllers.Toolbar.txtSymbol_mp": "Minus Plus", "DE.Controllers.Toolbar.txtSymbol_mu": "Mu", "DE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "DE.Controllers.Toolbar.txtSymbol_neq": "<PERSON><PERSON> gleich", "DE.Controllers.Toolbar.txtSymbol_ni": "Enthält als Element", "DE.Controllers.Toolbar.txtSymbol_not": "Negationszeichen", "DE.Controllers.Toolbar.txtSymbol_notexists": "Nicht vorhanden", "DE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "DE.Controllers.Toolbar.txtSymbol_o": "Omikron", "DE.Controllers.Toolbar.txtSymbol_omega": "Omega", "DE.Controllers.Toolbar.txtSymbol_partial": "Partielles Differenzial", "DE.Controllers.Toolbar.txtSymbol_percent": "Prozentsatz", "DE.Controllers.Toolbar.txtSymbol_phi": "Phi", "DE.Controllers.Toolbar.txtSymbol_pi": "Pi", "DE.Controllers.Toolbar.txtSymbol_plus": "Plus", "DE.Controllers.Toolbar.txtSymbol_pm": "Plus Minus", "DE.Controllers.Toolbar.txtSymbol_propto": "Proportional zu", "DE.Controllers.Toolbar.txtSymbol_psi": "Psi", "DE.Controllers.Toolbar.txtSymbol_qdrt": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_qed": "Ende des Beweises", "DE.Controllers.Toolbar.txtSymbol_rddots": "Horizontale Ellipse nach oben rechts", "DE.Controllers.Toolbar.txtSymbol_rho": "Rho", "DE.Controllers.Toolbar.txtSymbol_rightarrow": "<PERSON><PERSON><PERSON> nach rechts", "DE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "DE.Controllers.Toolbar.txtSymbol_sqrt": "Wurzelzeichen", "DE.Controllers.Toolbar.txtSymbol_tau": "Tau", "DE.Controllers.Toolbar.txtSymbol_therefore": "Folglich", "DE.Controllers.Toolbar.txtSymbol_theta": "Theta", "DE.Controllers.Toolbar.txtSymbol_times": "Multiplikationszeichen", "DE.Controllers.Toolbar.txtSymbol_uparrow": "<PERSON><PERSON><PERSON> nach oben", "DE.Controllers.Toolbar.txtSymbol_upsilon": "Ypsilon", "DE.Controllers.Toolbar.txtSymbol_varepsilon": "Epsilon Variant", "DE.Controllers.Toolbar.txtSymbol_varphi": "<PERSON>", "DE.Controllers.Toolbar.txtSymbol_varpi": "<PERSON>", "DE.Controllers.Toolbar.txtSymbol_varrho": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_varsigma": "Sigma Variant", "DE.Controllers.Toolbar.txtSymbol_vartheta": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_vdots": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "DE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "DE.Controllers.Viewport.textFitPage": "Seite anpassen", "DE.Controllers.Viewport.textFitWidth": "<PERSON><PERSON><PERSON> an<PERSON>en", "DE.Controllers.Viewport.txtDarkMode": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.AddNewCaptionLabelDialog.textLabel": "Bezeichnung:", "DE.Views.AddNewCaptionLabelDialog.textLabelError": "<PERSON><PERSON><PERSON>nung darf nicht leer sein.", "DE.Views.BookmarksDialog.textAdd": "Hinzufügen", "DE.Views.BookmarksDialog.textBookmarkName": "Lesezeichenname", "DE.Views.BookmarksDialog.textClose": "Schließen", "DE.Views.BookmarksDialog.textCopy": "<PERSON><PERSON><PERSON>", "DE.Views.BookmarksDialog.textDelete": "Löschen", "DE.Views.BookmarksDialog.textGetLink": "<PERSON> a<PERSON>", "DE.Views.BookmarksDialog.textGoto": "<PERSON><PERSON><PERSON><PERSON> zu", "DE.Views.BookmarksDialog.textHidden": "Ausgeblendete Lesezeichen", "DE.Views.BookmarksDialog.textLocation": "<PERSON><PERSON>", "DE.Views.BookmarksDialog.textName": "Name", "DE.Views.BookmarksDialog.textSort": "Sortieren nach", "DE.Views.BookmarksDialog.textTitle": "Lesezeichen", "DE.Views.BookmarksDialog.txtInvalidName": "Der Name des Lesezeichens darf nur Buchstaben, Ziffern und Unterstriche enthalten und sollte mit dem Buchstaben beginnen", "DE.Views.CaptionDialog.textAdd": "Hinzufügen", "DE.Views.CaptionDialog.textAfter": "Nach", "DE.Views.CaptionDialog.textBefore": "<PERSON><PERSON><PERSON>", "DE.Views.CaptionDialog.textCaption": "Beschriftung", "DE.Views.CaptionDialog.textChapter": "<PERSON><PERSON><PERSON> beginnt mit Stil", "DE.Views.CaptionDialog.textChapterInc": "Kapitelnummer einschließen", "DE.Views.CaptionDialog.textColon": "Doppelpunkt", "DE.Views.CaptionDialog.textDash": "Gedankenstrich", "DE.Views.CaptionDialog.textDelete": "Löschen", "DE.Views.CaptionDialog.textEquation": "Gleichung", "DE.Views.CaptionDialog.textExamples": "Beispiele: Tabelle 2-A, Bild 1.IV", "DE.Views.CaptionDialog.textExclude": "Bezeichnung aus Beschriftung ausschließen", "DE.Views.CaptionDialog.textFigure": "Abb<PERSON><PERSON><PERSON>", "DE.Views.CaptionDialog.textHyphen": "Bindestrich", "DE.Views.CaptionDialog.textInsert": "Einfügen", "DE.Views.CaptionDialog.textLabel": "Bezeichnung", "DE.Views.CaptionDialog.textLongDash": "langer <PERSON>", "DE.Views.CaptionDialog.textNumbering": "Nummerierung", "DE.Views.CaptionDialog.textPeriod": "<PERSON><PERSON>", "DE.Views.CaptionDialog.textSeparator": "Trennzeichen verwenden", "DE.Views.CaptionDialog.textTable": "<PERSON><PERSON><PERSON>", "DE.Views.CaptionDialog.textTitle": "Beschriftung einfügen", "DE.Views.CellsAddDialog.textCol": "Spalten", "DE.Views.CellsAddDialog.textDown": "<PERSON><PERSON> dem Cursor", "DE.Views.CellsAddDialog.textLeft": "Nach links", "DE.Views.CellsAddDialog.textRight": "<PERSON>ch rechts ", "DE.Views.CellsAddDialog.textRow": "<PERSON><PERSON><PERSON>", "DE.Views.CellsAddDialog.textTitle": "Einfügen: mehrere", "DE.Views.CellsAddDialog.textUp": "<PERSON><PERSON> dem Cursor", "DE.Views.ChartSettings.text3dDepth": "Tiefe (% der Basis)", "DE.Views.ChartSettings.text3dHeight": "Höhe (% der Basis)", "DE.Views.ChartSettings.text3dRotation": "3D-Dr<PERSON>ung", "DE.Views.ChartSettings.textAdvanced": "Erweiterte Einstellungen anzeigen", "DE.Views.ChartSettings.textAutoscale": "Autoskalierung", "DE.Views.ChartSettings.textChartType": "Diagrammtyp ändern", "DE.Views.ChartSettings.textDefault": "Standardmäßige Drehung", "DE.Views.ChartSettings.textDown": "Unten", "DE.Views.ChartSettings.textEditData": "Daten ä<PERSON>n", "DE.Views.ChartSettings.textHeight": "<PERSON><PERSON><PERSON>", "DE.Views.ChartSettings.textLeft": "Links", "DE.Views.ChartSettings.textNarrow": "Blickfeld verengen", "DE.Views.ChartSettings.textOriginalSize": "Tatsächliche Größe", "DE.Views.ChartSettings.textPerspective": "Perspektive", "DE.Views.ChartSettings.textRight": "<PERSON><PERSON><PERSON>", "DE.Views.ChartSettings.textRightAngle": "Rechtwinklige Achsen", "DE.Views.ChartSettings.textSize": "Größe", "DE.Views.ChartSettings.textStyle": "Stil", "DE.Views.ChartSettings.textUndock": "Seitenbereich abdocken", "DE.Views.ChartSettings.textUp": "Aufwärts", "DE.Views.ChartSettings.textWiden": "Blickfeld verbreitern", "DE.Views.ChartSettings.textWidth": "Breite", "DE.Views.ChartSettings.textWrap": "Textumbruch", "DE.Views.ChartSettings.textX": "X-Rotation", "DE.Views.ChartSettings.textY": "Y-Rotation", "DE.Views.ChartSettings.txtBehind": "<PERSON><PERSON> dem <PERSON>", "DE.Views.ChartSettings.txtInFront": "<PERSON><PERSON><PERSON>", "DE.Views.ChartSettings.txtInline": "Inline", "DE.Views.ChartSettings.txtSquare": "<PERSON><PERSON><PERSON>", "DE.Views.ChartSettings.txtThrough": "Durchgehend", "DE.Views.ChartSettings.txtTight": "Passend", "DE.Views.ChartSettings.txtTitle": "Diagramm", "DE.Views.ChartSettings.txtTopAndBottom": "<PERSON><PERSON> und unten", "DE.Views.ControlSettingsDialog.strGeneral": "Allgemein", "DE.Views.ControlSettingsDialog.textAdd": "Hinzufügen", "DE.Views.ControlSettingsDialog.textAppearance": "Darstellung", "DE.Views.ControlSettingsDialog.textApplyAll": "Auf alle anwenden", "DE.Views.ControlSettingsDialog.textBox": "Begrenzungsrahmen", "DE.Views.ControlSettingsDialog.textChange": "<PERSON><PERSON><PERSON>", "DE.Views.ControlSettingsDialog.textCheckbox": "Kontrollkästchen", "DE.Views.ControlSettingsDialog.textChecked": "Häkchen-Symbol ", "DE.Views.ControlSettingsDialog.textColor": "Farbe", "DE.Views.ControlSettingsDialog.textCombobox": "<PERSON><PERSON><PERSON><PERSON>feld", "DE.Views.ControlSettingsDialog.textDate": "Datumsformat", "DE.Views.ControlSettingsDialog.textDelete": "Löschen", "DE.Views.ControlSettingsDialog.textDisplayName": "Anzeigename", "DE.Views.ControlSettingsDialog.textDown": "Unten", "DE.Views.ControlSettingsDialog.textDropDown": "Dropdownliste", "DE.Views.ControlSettingsDialog.textFormat": "Datum wie folgt anzeigen", "DE.Views.ControlSettingsDialog.textLang": "<PERSON><PERSON><PERSON>", "DE.Views.ControlSettingsDialog.textLock": "<PERSON><PERSON><PERSON>", "DE.Views.ControlSettingsDialog.textName": "Titel", "DE.Views.ControlSettingsDialog.textNone": "<PERSON><PERSON>", "DE.Views.ControlSettingsDialog.textPlaceholder": "Platzhalter", "DE.Views.ControlSettingsDialog.textShowAs": "Anzeigen als", "DE.Views.ControlSettingsDialog.textSystemColor": "System", "DE.Views.ControlSettingsDialog.textTag": "Tag", "DE.Views.ControlSettingsDialog.textTitle": "Einstellungen des Inhaltssteuerelements", "DE.Views.ControlSettingsDialog.textUnchecked": "Nicht aktiviertes Häkchen", "DE.Views.ControlSettingsDialog.textUp": "Aufwärts", "DE.Views.ControlSettingsDialog.textValue": "Wert", "DE.Views.ControlSettingsDialog.tipChange": "Symbol ändern", "DE.Views.ControlSettingsDialog.txtLockDelete": "Das Inhaltssteuerelement kann nicht gelöscht werden", "DE.Views.ControlSettingsDialog.txtLockEdit": "Der Inhalt kann nicht bearbeitet werden", "DE.Views.CrossReferenceDialog.textAboveBelow": "Oben/unten", "DE.Views.CrossReferenceDialog.textBookmark": "Lesezeichen", "DE.Views.CrossReferenceDialog.textBookmarkText": "Text des Lesezeichens", "DE.Views.CrossReferenceDialog.textCaption": "Ganze Beschriftung", "DE.Views.CrossReferenceDialog.textEmpty": "Der angeforderte Verweis hat keinen Inhalt.", "DE.Views.CrossReferenceDialog.textEndnote": "Endnote", "DE.Views.CrossReferenceDialog.textEndNoteNum": "<PERSON><PERSON><PERSON>note", "DE.Views.CrossReferenceDialog.textEndNoteNumForm": "<PERSON><PERSON><PERSON> der Endnote (formatiert)", "DE.Views.CrossReferenceDialog.textEquation": "Gleichung", "DE.Views.CrossReferenceDialog.textFigure": "Abb<PERSON><PERSON><PERSON>", "DE.Views.CrossReferenceDialog.textFootnote": "Fußnote", "DE.Views.CrossReferenceDialog.textHeading": "Überschrift", "DE.Views.CrossReferenceDialog.textHeadingNum": "Nummer der Überschrift", "DE.Views.CrossReferenceDialog.textHeadingNumFull": "Nummer der Überschrift (der ganze Kontext)", "DE.Views.CrossReferenceDialog.textHeadingNumNo": "Nummer der Überschrift (kein Kontext)", "DE.Views.CrossReferenceDialog.textHeadingText": "Überschriftentext", "DE.Views.CrossReferenceDialog.textIncludeAbove": "Oben/unten einschließen", "DE.Views.CrossReferenceDialog.textInsert": "Einfügen", "DE.Views.CrossReferenceDialog.textInsertAs": "Als Hyperlink einfügen", "DE.Views.CrossReferenceDialog.textLabelNum": "Nur Bezeichnung und Nummer", "DE.Views.CrossReferenceDialog.textNoteNum": "<PERSON><PERSON>mer der Fußnote", "DE.Views.CrossReferenceDialog.textNoteNumForm": "Nummer der Fußnote (formatiert)", "DE.Views.CrossReferenceDialog.textOnlyCaption": "<PERSON>ur der Text von der Legende", "DE.Views.CrossReferenceDialog.textPageNum": "Seitenn<PERSON>mer", "DE.Views.CrossReferenceDialog.textParagraph": "Nummeriertes Element", "DE.Views.CrossReferenceDialog.textParaNum": "Absatznummer", "DE.Views.CrossReferenceDialog.textParaNumFull": "Absatznummer (der ganze Kontext)", "DE.Views.CrossReferenceDialog.textParaNumNo": "Absatznummer (kein Kontext)", "DE.Views.CrossReferenceDialog.textSeparate": "<PERSON><PERSON>mern trennen mit", "DE.Views.CrossReferenceDialog.textTable": "<PERSON><PERSON><PERSON>", "DE.Views.CrossReferenceDialog.textText": "Text im Absatz", "DE.Views.CrossReferenceDialog.textWhich": "Für welche Beschriftung", "DE.Views.CrossReferenceDialog.textWhichBookmark": "<PERSON><PERSON><PERSON> welches Lesezeichen", "DE.Views.CrossReferenceDialog.textWhichEndnote": "<PERSON><PERSON><PERSON> we<PERSON><PERSON>", "DE.Views.CrossReferenceDialog.textWhichHeading": "Für welche Überschrift", "DE.Views.CrossReferenceDialog.textWhichNote": "<PERSON><PERSON><PERSON> welche <PERSON>", "DE.Views.CrossReferenceDialog.textWhichPara": "Für welches nummeriertes Element", "DE.Views.CrossReferenceDialog.txtReference": "Ver<PERSON>sen auf", "DE.Views.CrossReferenceDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.CrossReferenceDialog.txtType": "Bezugstyp", "DE.Views.CustomColumnsDialog.textColumns": "<PERSON><PERSON><PERSON>", "DE.Views.CustomColumnsDialog.textSeparator": "Spaltenunterteilers", "DE.Views.CustomColumnsDialog.textSpacing": "Abstand zwischen Spalten", "DE.Views.CustomColumnsDialog.textTitle": "Spalten", "DE.Views.DateTimeDialog.confirmDefault": "Standardformat für {0}: \"{1}\" festlegen", "DE.Views.DateTimeDialog.textDefault": "Als Standardeinstellung festlegen", "DE.Views.DateTimeDialog.textFormat": "Formate", "DE.Views.DateTimeDialog.textLang": "<PERSON><PERSON><PERSON>", "DE.Views.DateTimeDialog.textUpdate": "Automatisch aktualisieren", "DE.Views.DateTimeDialog.txtTitle": "Datum & Uhrzeit", "DE.Views.DocProtection.hintProtectDoc": "<PERSON><PERSON> s<PERSON>en", "DE.Views.DocProtection.txtDocProtectedComment": "Das Dokument ist geschützt.<br><PERSON>e können nur Kommentare zu diesem Dokument hinterlassen.", "DE.Views.DocProtection.txtDocProtectedForms": "Das Dokument ist geschützt.<br><PERSON><PERSON> können nur Formulare in diesem Dokument ausfüllen.", "DE.Views.DocProtection.txtDocProtectedTrack": "Das Dokument ist geschützt.<br><PERSON><PERSON> können dieses Dokument bearbeiten, aber alle Änderungen werden nachverfolgt.", "DE.Views.DocProtection.txtDocProtectedView": "Das Dokument ist geschützt.<br><PERSON><PERSON> könne<PERSON> dieses Dokument nur ansehen.", "DE.Views.DocProtection.txtDocUnlockDescription": "<PERSON><PERSON>en Si<PERSON> ein Passwort ein, um den Schutz des Dokuments aufzuheben", "DE.Views.DocProtection.txtProtectDoc": "<PERSON><PERSON> s<PERSON>en", "DE.Views.DocumentHolder.aboveText": "<PERSON><PERSON>", "DE.Views.DocumentHolder.addCommentText": "Kommentar hinzufügen", "DE.Views.DocumentHolder.advancedDropCapText": "Initialformatierung", "DE.Views.DocumentHolder.advancedEquationText": "Einstellungen der Gleichung", "DE.Views.DocumentHolder.advancedFrameText": "Rahmen - Erweiterte Einstellungen", "DE.Views.DocumentHolder.advancedParagraphText": "Absatz - Erweiterte Einstellungen", "DE.Views.DocumentHolder.advancedTableText": "Tabelle - Erweiterte Einstellungen", "DE.Views.DocumentHolder.advancedText": "Erweiterte Einstellungen", "DE.Views.DocumentHolder.alignmentText": "Ausrichtung", "DE.Views.DocumentHolder.allLinearText": "Alle – Linear", "DE.Views.DocumentHolder.allProfText": "Alle – Professionelle", "DE.Views.DocumentHolder.belowText": "Unten", "DE.Views.DocumentHolder.breakBeforeText": "Seitenumbruch oberhalb", "DE.Views.DocumentHolder.bulletsText": "Aufzählung und Nummerierung", "DE.Views.DocumentHolder.cellAlignText": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.cellText": "<PERSON><PERSON>", "DE.Views.DocumentHolder.centerText": "<PERSON><PERSON>", "DE.Views.DocumentHolder.chartText": "Erweiterte Einstellungen des Diagramms", "DE.Views.DocumentHolder.columnText": "<PERSON>lt<PERSON>", "DE.Views.DocumentHolder.currLinearText": "Aktuell – Linear", "DE.Views.DocumentHolder.currProfText": "Aktuell – Professionell", "DE.Views.DocumentHolder.deleteColumnText": "Spalte löschen", "DE.Views.DocumentHolder.deleteRowText": "Zeile löschen", "DE.Views.DocumentHolder.deleteTableText": "<PERSON><PERSON><PERSON> l<PERSON>", "DE.Views.DocumentHolder.deleteText": "Löschen", "DE.Views.DocumentHolder.direct270Text": "Text nach oben drehen", "DE.Views.DocumentHolder.direct90Text": "Text nach unten drehen", "DE.Views.DocumentHolder.directHText": "Horizontal", "DE.Views.DocumentHolder.directionText": "Textausrichtung", "DE.Views.DocumentHolder.editChartText": "Daten ä<PERSON>n", "DE.Views.DocumentHolder.editFooterText": "Fußzeile bearbeiten", "DE.Views.DocumentHolder.editHeaderText": "Kopfzeile bearbeiten", "DE.Views.DocumentHolder.editHyperlinkText": "Hyperlink bearbeiten", "DE.Views.DocumentHolder.eqToInlineText": "Zum Inline wechseln", "DE.Views.DocumentHolder.guestText": "Gas<PERSON>", "DE.Views.DocumentHolder.hyperlinkText": "Hyperlink", "DE.Views.DocumentHolder.ignoreAllSpellText": "Alle auslassen", "DE.Views.DocumentHolder.ignoreSpellText": "Auslassen", "DE.Views.DocumentHolder.imageText": "Erweiterte Einstellungen des Bildes", "DE.Views.DocumentHolder.insertColumnLeftText": "Spalte nach links", "DE.Views.DocumentHolder.insertColumnRightText": "Spalte nach rechts", "DE.Views.DocumentHolder.insertColumnText": "Spalte einfügen", "DE.Views.DocumentHolder.insertRowAboveText": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.insertRowBelowText": "<PERSON><PERSON><PERSON> un<PERSON>", "DE.Views.DocumentHolder.insertRowText": "<PERSON>eile einfügen", "DE.Views.DocumentHolder.insertText": "Einfügen", "DE.Views.DocumentHolder.keepLinesText": "Absatz zusammenhalten", "DE.Views.DocumentHolder.langText": "Sprache wählen", "DE.Views.DocumentHolder.latexText": "LaTeX", "DE.Views.DocumentHolder.leftText": "Links", "DE.Views.DocumentHolder.loadSpellText": "Varianten werden geladen...", "DE.Views.DocumentHolder.mergeCellsText": "<PERSON><PERSON><PERSON> verbinden", "DE.Views.DocumentHolder.moreText": "<PERSON><PERSON>...", "DE.Views.DocumentHolder.noSpellVariantsText": "<PERSON><PERSON>", "DE.Views.DocumentHolder.notcriticalErrorTitle": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.originalSizeText": "Tatsächliche Größe", "DE.Views.DocumentHolder.paragraphText": "Absatz", "DE.Views.DocumentHolder.removeHyperlinkText": "Hyperlink entfernen", "DE.Views.DocumentHolder.rightText": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.rowText": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.saveStyleText": "Neuer Stil erstellen", "DE.Views.DocumentHolder.selectCellText": "<PERSON><PERSON> au<PERSON>wählen", "DE.Views.DocumentHolder.selectColumnText": "Spalte auswählen", "DE.Views.DocumentHolder.selectRowText": "Zeile auswählen", "DE.Views.DocumentHolder.selectTableText": "<PERSON><PERSON><PERSON> auswählen", "DE.Views.DocumentHolder.selectText": "Auswählen", "DE.Views.DocumentHolder.shapeText": "Erweiterte Einstellungen der Form", "DE.Views.DocumentHolder.spellcheckText": "Rechtschreibprüfung", "DE.Views.DocumentHolder.splitCellsText": "<PERSON><PERSON> te<PERSON>...", "DE.Views.DocumentHolder.splitCellTitleText": "<PERSON><PERSON> te<PERSON>n", "DE.Views.DocumentHolder.strDelete": "Signatur entfernen", "DE.Views.DocumentHolder.strDetails": "Signaturdetails", "DE.Views.DocumentHolder.strSetup": "Signatureinrichtung", "DE.Views.DocumentHolder.strSign": "Signieren", "DE.Views.DocumentHolder.styleText": "Formatierung als Formatvorlage", "DE.Views.DocumentHolder.tableText": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textAccept": "Änderung annehmen", "DE.Views.DocumentHolder.textAlign": "Ausrichten", "DE.Views.DocumentHolder.textArrange": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textArrangeBack": "In den Hintergrund senden", "DE.Views.DocumentHolder.textArrangeBackward": "Eine Ebene nach hinten", "DE.Views.DocumentHolder.textArrangeForward": "Eine Ebene nach vorne", "DE.Views.DocumentHolder.textArrangeFront": "In den Vordergrund bringen", "DE.Views.DocumentHolder.textCells": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textCol": "Ganze Spalte löschen", "DE.Views.DocumentHolder.textContentControls": "Inhaltssteuerelement", "DE.Views.DocumentHolder.textContinueNumbering": "Nummerierung fortführen", "DE.Views.DocumentHolder.textCopy": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textCrop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textCropFill": "Ausfüllen", "DE.Views.DocumentHolder.textCropFit": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textCut": "Ausschneiden", "DE.Views.DocumentHolder.textDistributeCols": "Spalten verteilen", "DE.Views.DocumentHolder.textDistributeRows": "<PERSON><PERSON><PERSON> verteilen", "DE.Views.DocumentHolder.textEditControls": "Einstellungen des Inhaltssteuerelements", "DE.Views.DocumentHolder.textEditPoints": "<PERSON><PERSON> bearbeiten", "DE.Views.DocumentHolder.textEditWrapBoundary": "Umbruchsgrenze bearbeiten", "DE.Views.DocumentHolder.textFlipH": "Horizontal kippen", "DE.Views.DocumentHolder.textFlipV": "Vertikal kippen", "DE.Views.DocumentHolder.textFollow": "Verschieben nachverfolgen", "DE.Views.DocumentHolder.textFromFile": "<PERSON><PERSON> Datei", "DE.Views.DocumentHolder.textFromStorage": "Aus dem Speicher", "DE.Views.DocumentHolder.textFromUrl": "Aus URL", "DE.Views.DocumentHolder.textJoinList": "Mit der vorherigen Liste verbinden", "DE.Views.DocumentHolder.textLeft": "Zellen nach links verschieben", "DE.Views.DocumentHolder.textNest": "<PERSON><PERSON><PERSON> s<PERSON>n", "DE.Views.DocumentHolder.textNextPage": "Nächste Seite", "DE.Views.DocumentHolder.textNumberingValue": "Nummerierungswert", "DE.Views.DocumentHolder.textPaste": "Einfügen", "DE.Views.DocumentHolder.textPrevPage": "Vorherige Seite", "DE.Views.DocumentHolder.textRefreshField": "Feld aktualisieren", "DE.Views.DocumentHolder.textReject": "Änderung ablehnen", "DE.Views.DocumentHolder.textRemCheckBox": "Checkbox entfernen", "DE.Views.DocumentHolder.textRemComboBox": "Combobox entfernen", "DE.Views.DocumentHolder.textRemDropdown": "Dropdown entfernen", "DE.Views.DocumentHolder.textRemField": "Textfeld entfernen", "DE.Views.DocumentHolder.textRemove": "Entfernen", "DE.Views.DocumentHolder.textRemoveControl": "Inhaltssteuerelement entfernen", "DE.Views.DocumentHolder.textRemPicture": "Bild entfernen", "DE.Views.DocumentHolder.textRemRadioBox": "Radiobutton entfernen", "DE.Views.DocumentHolder.textReplace": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textRotate": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textRotate270": "Um 90 ° gegen den Uhrzeigersinn drehen", "DE.Views.DocumentHolder.textRotate90": "90° im UZS drehen", "DE.Views.DocumentHolder.textRow": "Ganze Zeile löschen", "DE.Views.DocumentHolder.textSeparateList": "Separate Liste", "DE.Views.DocumentHolder.textSettings": "Einstellungen", "DE.Views.DocumentHolder.textSeveral": "Mehrere Zeilen/Spalten", "DE.Views.DocumentHolder.textShapeAlignBottom": "Unten ausrichten", "DE.Views.DocumentHolder.textShapeAlignCenter": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textShapeAlignLeft": "Linksbündig ausrichten", "DE.Views.DocumentHolder.textShapeAlignMiddle": "<PERSON><PERSON><PERSON> au<PERSON>", "DE.Views.DocumentHolder.textShapeAlignRight": "Rechtsbündig ausrichten", "DE.Views.DocumentHolder.textShapeAlignTop": "<PERSON><PERSON> aus<PERSON>", "DE.Views.DocumentHolder.textStartNewList": "Neue Liste beginnen", "DE.Views.DocumentHolder.textStartNumberingFrom": "Nummerierungswert festlegen", "DE.Views.DocumentHolder.textTitleCellsRemove": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textTOC": "Inhaltsverzeichnis", "DE.Views.DocumentHolder.textTOCSettings": "Einstellungen für das Inhaltverzeichnis", "DE.Views.DocumentHolder.textUndo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textUpdateAll": "Ganze Tabelle aktualisieren", "DE.Views.DocumentHolder.textUpdatePages": "Nur Seitenzahlen aktualisieren", "DE.Views.DocumentHolder.textUpdateTOC": "Das Inhaltsverzeichnis aktualisieren", "DE.Views.DocumentHolder.textWrap": "Textumbruch", "DE.Views.DocumentHolder.tipIsLocked": "Dieses Element wird gerade von einem anderen Benutzer bearbeitet.", "DE.Views.DocumentHolder.toDictionaryText": "Zum Wörterbuch hinzufügen", "DE.Views.DocumentHolder.txtAddBottom": "<PERSON><PERSON>en Rahmen <PERSON>", "DE.Views.DocumentHolder.txtAddFractionBar": "Bruchstrich hinzufügen", "DE.Views.DocumentHolder.txtAddHor": "Horizontale Linie einfügen", "DE.Views.DocumentHolder.txtAddLB": "Linke untere Linie einfügen", "DE.Views.DocumentHolder.txtAddLeft": "<PERSON><PERSON> Ra<PERSON>", "DE.Views.DocumentHolder.txtAddLT": "Linke obere Linie einfügen", "DE.Views.DocumentHolder.txtAddRight": "<PERSON><PERSON><PERSON> Ra<PERSON>", "DE.Views.DocumentHolder.txtAddTop": "Oberem Rahmen <PERSON>", "DE.Views.DocumentHolder.txtAddVer": "<PERSON><PERSON><PERSON><PERSON> Lin<PERSON>", "DE.Views.DocumentHolder.txtAlignToChar": "An einem Zeichen ausrichten", "DE.Views.DocumentHolder.txtBehind": "<PERSON><PERSON> dem <PERSON>", "DE.Views.DocumentHolder.txtBorderProps": "Rahmeneigenschaften", "DE.Views.DocumentHolder.txtBottom": "Unten", "DE.Views.DocumentHolder.txtColumnAlign": "Spaltenausrichtung", "DE.Views.DocumentHolder.txtDecreaseArg": "Argumentgröße reduzieren", "DE.Views.DocumentHolder.txtDeleteArg": "Argument löschen", "DE.Views.DocumentHolder.txtDeleteBreak": "<PERSON>len Umbruch löschen", "DE.Views.DocumentHolder.txtDeleteChars": "Einschlusszeichen löschen", "DE.Views.DocumentHolder.txtDeleteCharsAndSeparators": "Einschlusszeichen und Trennzeichen löschen", "DE.Views.DocumentHolder.txtDeleteEq": "Formel löschen", "DE.Views.DocumentHolder.txtDeleteGroupChar": "Zeichen löschen", "DE.Views.DocumentHolder.txtDeleteRadical": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.txtDistribHor": "Horizontal verteilen", "DE.Views.DocumentHolder.txtDistribVert": "Vertikal verteilen", "DE.Views.DocumentHolder.txtEmpty": "(<PERSON><PERSON>)", "DE.Views.DocumentHolder.txtFractionLinear": "Zu linearer Bruchrechnung ändern", "DE.Views.DocumentHolder.txtFractionSkewed": "<PERSON><PERSON> verzerrter Bruchrechnung ändern", "DE.Views.DocumentHolder.txtFractionStacked": "<PERSON><PERSON> verzerrter Bruchrechnung ändern", "DE.Views.DocumentHolder.txtGroup": "Gruppieren", "DE.Views.DocumentHolder.txtGroupCharOver": "Zeichen über dem Text ", "DE.Views.DocumentHolder.txtGroupCharUnder": "<PERSON><PERSON><PERSON> unter dem Text ", "DE.Views.DocumentHolder.txtHideBottom": "Untere Rahmenlinie verbergen", "DE.Views.DocumentHolder.txtHideBottomLimit": "Untere Grenze verbergen", "DE.Views.DocumentHolder.txtHideCloseBracket": "Schließende Klammer verbergen", "DE.Views.DocumentHolder.txtHideDegree": "Grad verbergen", "DE.Views.DocumentHolder.txtHideHor": "Horizontale Linie verbergen", "DE.Views.DocumentHolder.txtHideLB": "Linke untere Line verbergen", "DE.Views.DocumentHolder.txtHideLeft": "Linker Rand verbergen", "DE.Views.DocumentHolder.txtHideLT": "Linke obere Linie verbergen", "DE.Views.DocumentHolder.txtHideOpenBracket": "Öffnende Klammer verbergen", "DE.Views.DocumentHolder.txtHidePlaceholder": "Platzhalter verbergen", "DE.Views.DocumentHolder.txtHideRight": "Rahmenlinie rechts verbergen", "DE.Views.DocumentHolder.txtHideTop": "<PERSON><PERSON><PERSON><PERSON><PERSON> oben verbergen", "DE.Views.DocumentHolder.txtHideTopLimit": "Obergrenze verbergen", "DE.Views.DocumentHolder.txtHideVer": "<PERSON><PERSON><PERSON><PERSON> Lin<PERSON> verb<PERSON>gen", "DE.Views.DocumentHolder.txtIncreaseArg": "Argumentgröße erh<PERSON>hen", "DE.Views.DocumentHolder.txtInFront": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.txtInline": "Inline", "DE.Views.DocumentHolder.txtInsertArgAfter": "Argument nachher einfügen", "DE.Views.DocumentHolder.txtInsertArgBefore": "Argument vorher einfügen", "DE.Views.DocumentHolder.txtInsertBreak": "<PERSON><PERSON> Umbruch einfügen", "DE.Views.DocumentHolder.txtInsertCaption": "Beschriftung einfügen", "DE.Views.DocumentHolder.txtInsertEqAfter": "Formel nachher einfügen", "DE.Views.DocumentHolder.txtInsertEqBefore": "Formel vorher einfügen", "DE.Views.DocumentHolder.txtKeepTextOnly": "Nur Text beibehalten", "DE.Views.DocumentHolder.txtLimitChange": "Grenzwerten ändern ", "DE.Views.DocumentHolder.txtLimitOver": "Grenzwert über den Text", "DE.Views.DocumentHolder.txtLimitUnder": "Grenzwer<PERSON> unter den Text", "DE.Views.DocumentHolder.txtMatchBrackets": "<PERSON>ckige Klammern an Argumenthöhe anpassen", "DE.Views.DocumentHolder.txtMatrixAlign": "Matrixausrichtung", "DE.Views.DocumentHolder.txtOverbar": "Balken über dem Text", "DE.Views.DocumentHolder.txtOverwriteCells": "Zellen überschreiben", "DE.Views.DocumentHolder.txtPasteSourceFormat": "Ursprüngliche Formatierung beibehalten", "DE.Views.DocumentHolder.txtPressLink": "<PERSON><PERSON><PERSON> {0} und klicken Sie auf den Link", "DE.Views.DocumentHolder.txtPrintSelection": "Auswahl drucken", "DE.Views.DocumentHolder.txtRemFractionBar": "Bruchstrich entfernen", "DE.Views.DocumentHolder.txtRemLimit": "Grenzwert entfernen", "DE.Views.DocumentHolder.txtRemoveAccentChar": "Akzentzeichen entfernen", "DE.Views.DocumentHolder.txtRemoveBar": "<PERSON><PERSON><PERSON> en<PERSON>fernen", "DE.Views.DocumentHolder.txtRemoveWarning": "Möchten Sie diese Signatur wirklich entfernen?<br>Dies kann nicht rückgängig gemacht werden.", "DE.Views.DocumentHolder.txtRemScripts": "Skripts entfernen", "DE.Views.DocumentHolder.txtRemSubscript": "Tiefstellung entfernen", "DE.Views.DocumentHolder.txtRemSuperscript": "Hochstellung entfernen", "DE.Views.DocumentHolder.txtScriptsAfter": "<PERSON><PERSON><PERSON> nach dem Text", "DE.Views.DocumentHolder.txtScriptsBefore": "Scripts vor dem Text", "DE.Views.DocumentHolder.txtShowBottomLimit": "Untere Grenze zeigen", "DE.Views.DocumentHolder.txtShowCloseBracket": "Schließende eckige Klammer anzeigen", "DE.Views.DocumentHolder.txtShowDegree": "Grad anzeigen", "DE.Views.DocumentHolder.txtShowOpenBracket": "Öffnende eckige Klammer anzeigen", "DE.Views.DocumentHolder.txtShowPlaceholder": "Platzhaltertext anzeigen", "DE.Views.DocumentHolder.txtShowTopLimit": "Höchstgrenze anzeigen", "DE.Views.DocumentHolder.txtSquare": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.txtStretchBrackets": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.txtThrough": "Durchgehend", "DE.Views.DocumentHolder.txtTight": "Passend", "DE.Views.DocumentHolder.txtTop": "<PERSON><PERSON>", "DE.Views.DocumentHolder.txtTopAndBottom": "<PERSON><PERSON> und unten", "DE.Views.DocumentHolder.txtUnderbar": "<PERSON><PERSON><PERSON> unter dem <PERSON> ", "DE.Views.DocumentHolder.txtUngroup": "Gruppierung aufheben", "DE.Views.DocumentHolder.txtWarnUrl": "Dieser Link kann für Ihr Gerät und Daten gefährlich sein.<br><PERSON><PERSON>cht<PERSON> Sie wirklich fortsetzen?", "DE.Views.DocumentHolder.unicodeText": "Unicode", "DE.Views.DocumentHolder.updateStyleText": "Format aktualisieren %1", "DE.Views.DocumentHolder.vertAlignText": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.strBorders": "Rahmen & Füllung", "DE.Views.DropcapSettingsAdvanced.strDropcap": "Initialbuchstaben ", "DE.Views.DropcapSettingsAdvanced.strMargins": "<PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textAlign": "Ausrichtung", "DE.Views.DropcapSettingsAdvanced.textAtLeast": "Mindestens", "DE.Views.DropcapSettingsAdvanced.textAuto": "Automatisch", "DE.Views.DropcapSettingsAdvanced.textBackColor": "Hintergrundfarbe", "DE.Views.DropcapSettingsAdvanced.textBorderColor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textBorderDesc": "<PERSON>lick<PERSON> Sie aufs Diagramm oder nutzen Sie die Buttons, um Umrandungen zu wählen", "DE.Views.DropcapSettingsAdvanced.textBorderWidth": "Rahmenstärke", "DE.Views.DropcapSettingsAdvanced.textBottom": "Unten", "DE.Views.DropcapSettingsAdvanced.textCenter": "<PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textColumn": "<PERSON>lt<PERSON>", "DE.Views.DropcapSettingsAdvanced.textDistance": "<PERSON><PERSON><PERSON><PERSON> von <PERSON>", "DE.Views.DropcapSettingsAdvanced.textExact": "<PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textFlow": "Unveran<PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textFont": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textFrame": "<PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textHeight": "<PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textHorizontal": "Horizontal", "DE.Views.DropcapSettingsAdvanced.textInline": "Inlineframe", "DE.Views.DropcapSettingsAdvanced.textInMargin": "<PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textInText": "Im Text", "DE.Views.DropcapSettingsAdvanced.textLeft": "Links", "DE.Views.DropcapSettingsAdvanced.textMargin": "Rand", "DE.Views.DropcapSettingsAdvanced.textMove": "Mit <PERSON> verschieben", "DE.Views.DropcapSettingsAdvanced.textNone": "<PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textPage": "Seite", "DE.Views.DropcapSettingsAdvanced.textParagraph": "Absatz", "DE.Views.DropcapSettingsAdvanced.textParameters": "Parameter", "DE.Views.DropcapSettingsAdvanced.textPosition": "Position", "DE.Views.DropcapSettingsAdvanced.textRelative": "<PERSON><PERSON> auf ", "DE.Views.DropcapSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textRowHeight": "Höhe in Zeilen", "DE.Views.DropcapSettingsAdvanced.textTitle": "Initialbuchstaben - Erweiterte Einstellungen", "DE.Views.DropcapSettingsAdvanced.textTitleFrame": "Rahmen - Erweiterte Einstellungen", "DE.Views.DropcapSettingsAdvanced.textTop": "<PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textVertical": "Vertikal", "DE.Views.DropcapSettingsAdvanced.textWidth": "Breite", "DE.Views.DropcapSettingsAdvanced.tipFontName": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.txtNoBorders": "<PERSON><PERSON>", "DE.Views.EditListItemDialog.textDisplayName": "Anzeigename", "DE.Views.EditListItemDialog.textNameError": "Der Anzeigename darf nicht leer sein.", "DE.Views.EditListItemDialog.textValue": "Wert", "DE.Views.EditListItemDialog.textValueError": "Ein Element mit demselben Wert ist bereits vorhanden.", "DE.Views.FileMenu.btnBackCaption": "<PERSON>is<PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenu.btnCloseMenuCaption": "<PERSON><PERSON> sch<PERSON>ßen", "DE.Views.FileMenu.btnCreateNewCaption": "Neues erstellen", "DE.Views.FileMenu.btnDownloadCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON> als", "DE.Views.FileMenu.btnExitCaption": "Schließen", "DE.Views.FileMenu.btnFileOpenCaption": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenu.btnHelpCaption": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenu.btnHistoryCaption": "Versionshistorie", "DE.Views.FileMenu.btnInfoCaption": "Dokumentinformationen", "DE.Views.FileMenu.btnPrintCaption": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenu.btnProtectCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenu.btnRecentFilesCaption": "Zuletzt benutztes Dokument öffnen", "DE.Views.FileMenu.btnRenameCaption": "Umbenennen", "DE.Views.FileMenu.btnReturnCaption": "Z<PERSON><PERSON> zu dem Dokument", "DE.Views.FileMenu.btnRightsCaption": "Zugriffsrechte", "DE.Views.FileMenu.btnSaveAsCaption": "Speichern als", "DE.Views.FileMenu.btnSaveCaption": "Speichern", "DE.Views.FileMenu.btnSaveCopyAsCaption": "<PERSON><PERSON> speichern als", "DE.Views.FileMenu.btnSettingsCaption": "Erweiterte Einstellungen", "DE.Views.FileMenu.btnToEditCaption": "Dokument bearbeiten", "DE.Views.FileMenu.textDownload": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.CreateNew.txtBlank": "<PERSON>res <PERSON>", "DE.Views.FileMenuPanels.CreateNew.txtCreateNew": "<PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.okButtonText": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtAddText": "Text hinzufügen", "DE.Views.FileMenuPanels.DocumentInfo.txtAppName": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Zugriffsrechte ändern", "DE.Views.FileMenuPanels.DocumentInfo.txtComment": "Kommentar", "DE.Views.FileMenuPanels.DocumentInfo.txtCreated": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtFastWV": "Schnelle Web-Anzeige", "DE.Views.FileMenuPanels.DocumentInfo.txtLoading": "Ladevorgang...", "DE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Zuletzt ge<PERSON><PERSON><PERSON> von", "DE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Zuletzt geändert", "DE.Views.FileMenuPanels.DocumentInfo.txtNo": "<PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtOwner": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtPages": "Seiten", "DE.Views.FileMenuPanels.DocumentInfo.txtPageSize": "Seitengröße", "DE.Views.FileMenuPanels.DocumentInfo.txtParagraphs": "Absätze", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfProducer": "PDF-<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfTagged": "PDF mit Tags", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfVer": "PDF-Version", "DE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Speicherort", "DE.Views.FileMenuPanels.DocumentInfo.txtRights": "Personen mit Berechtigungen", "DE.Views.FileMenuPanels.DocumentInfo.txtSpaces": "Symbole mit Leerzeichen", "DE.Views.FileMenuPanels.DocumentInfo.txtStatistics": "Statistiken", "DE.Views.FileMenuPanels.DocumentInfo.txtSubject": "<PERSON>a", "DE.Views.FileMenuPanels.DocumentInfo.txtSymbols": "Symbole", "DE.Views.FileMenuPanels.DocumentInfo.txtTags": "Tags", "DE.Views.FileMenuPanels.DocumentInfo.txtTitle": "Titel", "DE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Hochgeladen", "DE.Views.FileMenuPanels.DocumentInfo.txtWords": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtYes": "<PERSON>a", "DE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Zugriffsrechte ändern", "DE.Views.FileMenuPanels.DocumentRights.txtRights": "Personen mit Berechtigungen", "DE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "<PERSON><PERSON>", "DE.Views.FileMenuPanels.ProtectDoc.strProtect": "<PERSON><PERSON> s<PERSON>en", "DE.Views.FileMenuPanels.ProtectDoc.strSignature": "<PERSON>t Signatur", "DE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Dokument bearbeiten", "DE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "Die Bearbeitung entfernt Signaturen aus diesem Dokument.<br><PERSON><PERSON>cht<PERSON> Sie trotzdem fortsetzen?", "DE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "Dieses Dokument ist schreibgeschützt.", "DE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "Dieses Dokument muss signiert werden.", "DE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Gültige Signaturen wurden dem Dokument hinzugefügt. Das Dokument ist vor der Bearbeitung geschützt.", "DE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Einige der digitalen Signaturen im Dokument sind ungültig oder konnten nicht verifiziert werden. Das Dokument ist vor der Bearbeitung geschützt.", "DE.Views.FileMenuPanels.ProtectDoc.txtView": "Signaturen anzeigen", "DE.Views.FileMenuPanels.Settings.okButtonText": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.strCoAuthMode": "Modus \"Gemeinsame Bearbeitung\"", "DE.Views.FileMenuPanels.Settings.strFast": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.strFontRender": "Schriftglättung", "DE.Views.FileMenuPanels.Settings.strIgnoreWordsInUPPERCASE": "Wörter in GROSSBUCHSTABEN ignorieren", "DE.Views.FileMenuPanels.Settings.strIgnoreWordsWithNumbers": "W<PERSON>rter mit Zahlen ignorieren", "DE.Views.FileMenuPanels.Settings.strMacrosSettings": "Einstellungen von Ma<PERSON>ros", "DE.Views.FileMenuPanels.Settings.strPasteButton": "Die Schaltfläche Einfügeoptionen beim Einfügen von Inhalten anzeigen", "DE.Views.FileMenuPanels.Settings.strShowChanges": "Änderungen bei der Echtzeit-Zusammenarbeit zeigen", "DE.Views.FileMenuPanels.Settings.strShowComments": "Kommentare im Text anzeigen", "DE.Views.FileMenuPanels.Settings.strShowOthersChanges": "Änderungen von anderen Benutzern anzeigen", "DE.Views.FileMenuPanels.Settings.strShowResolvedComments": "Gelöste Kommentare anzeigen", "DE.Views.FileMenuPanels.Settings.strStrict": "Formal", "DE.Views.FileMenuPanels.Settings.strTheme": "Thema der Benutzeroberfläche", "DE.Views.FileMenuPanels.Settings.strUnit": "Maßeinheit", "DE.Views.FileMenuPanels.Settings.strZoom": "Standard-Zoom-Wert", "DE.Views.FileMenuPanels.Settings.text10Minutes": "Alle 10 Minuten", "DE.Views.FileMenuPanels.Settings.text30Minutes": "Alle 30 Minuten", "DE.Views.FileMenuPanels.Settings.text5Minutes": "Alle 5 Minuten", "DE.Views.FileMenuPanels.Settings.text60Minutes": "Jede Stunde", "DE.Views.FileMenuPanels.Settings.textAlignGuides": "Ausrichtungslinien", "DE.Views.FileMenuPanels.Settings.textAutoRecover": "Automatisches wiederherstellen", "DE.Views.FileMenuPanels.Settings.textAutoSave": "Automatisches speichern", "DE.Views.FileMenuPanels.Settings.textDisabled": "Deaktiviert", "DE.Views.FileMenuPanels.Settings.textForceSave": "<PERSON><PERSON> dem <PERSON> s<PERSON>ichern", "DE.Views.FileMenuPanels.Settings.textMinute": "<PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.textOldVersions": "Die Dateien mit älteren MS Word-Versionen kompatibel machen, wenn sie als DOCX gespeichert werden", "DE.Views.FileMenuPanels.Settings.txtAll": "Alle anzeigen", "DE.Views.FileMenuPanels.Settings.txtAutoCorrect": "Automatische Korrekturoptionen", "DE.Views.FileMenuPanels.Settings.txtCacheMode": "Standard-Cache-Modus", "DE.Views.FileMenuPanels.Settings.txtChangesBalloons": "In Sprechblasen beim Klicken anzeigen", "DE.Views.FileMenuPanels.Settings.txtChangesTip": "In Tipps anzeigen", "DE.Views.FileMenuPanels.Settings.txtCm": "Zentimeter", "DE.Views.FileMenuPanels.Settings.txtCollaboration": "Zusammenarbeit", "DE.Views.FileMenuPanels.Settings.txtDarkMode": "Dunkelmodus aktivieren", "DE.Views.FileMenuPanels.Settings.txtEditingSaving": "Bearbeitung und Speicherung", "DE.Views.FileMenuPanels.Settings.txtFastTip": "Zusammenarbeit in Echtzeit. Alle Änderungen werden automatisch gespeichert", "DE.Views.FileMenuPanels.Settings.txtFitPage": "Seite anpassen", "DE.Views.FileMenuPanels.Settings.txtFitWidth": "<PERSON><PERSON><PERSON> an<PERSON>en", "DE.Views.FileMenuPanels.Settings.txtHieroglyphs": "Hieroglyphen", "DE.Views.FileMenuPanels.Settings.txtInch": "<PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.txtLast": "Letzte anzeigen", "DE.Views.FileMenuPanels.Settings.txtMac": "wie OS X", "DE.Views.FileMenuPanels.Settings.txtNative": "Native", "DE.Views.FileMenuPanels.Settings.txtNone": "<PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.txtProofing": "Rechtschreibprüfung", "DE.Views.FileMenuPanels.Settings.txtPt": "<PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.txtQuickPrint": "Die Schaltfläche Schnelldruck in der Kopfzeile des Editors anzeigen", "DE.Views.FileMenuPanels.Settings.txtQuickPrintTip": "Das Dokument wird auf dem zuletzt ausgewählten oder dem standardmäßigen Drucker gedruckt", "DE.Views.FileMenuPanels.Settings.txtRunMacros": "Alle aktivieren", "DE.Views.FileMenuPanels.Settings.txtRunMacrosDesc": "Alle Makros ohne Benachrichtigung aktivieren", "DE.Views.FileMenuPanels.Settings.txtShowTrackChanges": "Änderungen anzeigen", "DE.Views.FileMenuPanels.Settings.txtSpellCheck": "Rechtschreibprüfung", "DE.Views.FileMenuPanels.Settings.txtStopMacros": "Alle deaktivieren", "DE.Views.FileMenuPanels.Settings.txtStopMacrosDesc": "Alle Makros ohne Benachrichtigung deaktivieren", "DE.Views.FileMenuPanels.Settings.txtStrictTip": "Verwenden Sie die Schaltfläche \"Speichern\", um die vorgenommenen Änderungen zu synchronisieren.", "DE.Views.FileMenuPanels.Settings.txtUseAltKey": "Verwenden Sie die Alt-Taste, um über die Tastatur in der Benutzeroberfläche zu navigieren.", "DE.Views.FileMenuPanels.Settings.txtUseOptionKey": "Verwenden Sie die Option-Taste, um über die Tastatur in der Benutzeroberfläche zu navigieren.", "DE.Views.FileMenuPanels.Settings.txtWarnMacros": "Benachrichtigung anzeigen", "DE.Views.FileMenuPanels.Settings.txtWarnMacrosDesc": "<PERSON>e Makros mit einer Benachrichtigung deaktivieren", "DE.Views.FileMenuPanels.Settings.txtWin": "wie Windows", "DE.Views.FileMenuPanels.Settings.txtWorkspace": "Arbeitsbereich", "DE.Views.FormSettings.textAlways": "Immer", "DE.Views.FormSettings.textAspect": "Seitenverhältnis sperren", "DE.Views.FormSettings.textAtLeast": "Mindestens", "DE.Views.FormSettings.textAuto": "auto", "DE.Views.FormSettings.textAutofit": "Automatisch anpassen", "DE.Views.FormSettings.textBackgroundColor": "Hintergrundfarbe", "DE.Views.FormSettings.textCheckbox": "Kontrollkästchen", "DE.Views.FormSettings.textColor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.FormSettings.textComb": "Zeichenanzahl in Textfeld", "DE.Views.FormSettings.textCombobox": "Combobox", "DE.Views.FormSettings.textComplex": "Komplexes Feld", "DE.Views.FormSettings.textConnected": "Verbundene Felder", "DE.Views.FormSettings.textDelete": "Löschen", "DE.Views.FormSettings.textDigits": "<PERSON><PERSON><PERSON>", "DE.Views.FormSettings.textDisconnect": "Verbindung trennen", "DE.Views.FormSettings.textDropDown": "Dropdown", "DE.Views.FormSettings.textExact": "<PERSON><PERSON>", "DE.Views.FormSettings.textField": "<PERSON><PERSON>", "DE.Views.FormSettings.textFixed": "Feste Feldgröße", "DE.Views.FormSettings.textFormat": "Format", "DE.Views.FormSettings.textFormatSymbols": "Erlaubte Symbole", "DE.Views.FormSettings.textFromFile": "Aus einer Datei", "DE.Views.FormSettings.textFromStorage": "Aus dem Speicher", "DE.Views.FormSettings.textFromUrl": "Aus einer URL", "DE.Views.FormSettings.textGroupKey": "Gruppenschlüssel", "DE.Views.FormSettings.textImage": "Bild", "DE.Views.FormSettings.textKey": "Schlüssel", "DE.Views.FormSettings.textLetters": "Buchstaben", "DE.Views.FormSettings.textLock": "<PERSON><PERSON><PERSON>", "DE.Views.FormSettings.textMask": "Beliebige Maske", "DE.Views.FormSettings.textMaxChars": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.FormSettings.textMulti": "Mehrzeiliges Feld", "DE.Views.FormSettings.textNever": "<PERSON><PERSON>", "DE.Views.FormSettings.textNoBorder": "<PERSON><PERSON>", "DE.Views.FormSettings.textNone": "<PERSON><PERSON>", "DE.Views.FormSettings.textPlaceholder": "Platzhalter", "DE.Views.FormSettings.textRadiobox": "Radiobutton", "DE.Views.FormSettings.textReg": "Regulärer Ausdruck", "DE.Views.FormSettings.textRequired": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.FormSettings.textScale": "<PERSON><PERSON> s<PERSON>", "DE.Views.FormSettings.textSelectImage": "Bild auswählen", "DE.Views.FormSettings.textTag": "Tag", "DE.Views.FormSettings.textTip": "<PERSON><PERSON><PERSON>", "DE.Views.FormSettings.textTipAdd": "Neuen Wert hinzufügen", "DE.Views.FormSettings.textTipDelete": "Den Wert löschen", "DE.Views.FormSettings.textTipDown": "Nach unten bewegen", "DE.Views.FormSettings.textTipUp": "Nach oben bewegen", "DE.Views.FormSettings.textTooBig": "Das Bild ist zu groß", "DE.Views.FormSettings.textTooSmall": "Das Bild ist zu klein", "DE.Views.FormSettings.textUnlock": "Entsperren", "DE.Views.FormSettings.textValue": "<PERSON><PERSON><PERSON>", "DE.Views.FormSettings.textWidth": "Zeilenbreite", "DE.Views.FormsTab.capBtnCheckBox": "Kontrollkästchen", "DE.Views.FormsTab.capBtnComboBox": "Combobox", "DE.Views.FormsTab.capBtnComplex": "Komplexes Feld", "DE.Views.FormsTab.capBtnDownloadForm": "Als OFORM herunt<PERSON>n", "DE.Views.FormsTab.capBtnDropDown": "Dropdown", "DE.Views.FormsTab.capBtnEmail": "E-Mail-Adresse", "DE.Views.FormsTab.capBtnImage": "Bild", "DE.Views.FormsTab.capBtnNext": "Nächstes Feld", "DE.Views.FormsTab.capBtnPhone": "Telefonnummer", "DE.Views.FormsTab.capBtnPrev": "Vorheriges Feld", "DE.Views.FormsTab.capBtnRadioBox": "Radiobutton", "DE.Views.FormsTab.capBtnSaveForm": "Als OFORM speichern", "DE.Views.FormsTab.capBtnSubmit": "Senden", "DE.Views.FormsTab.capBtnText": "<PERSON><PERSON>", "DE.Views.FormsTab.capBtnView": "Formular anzeigen", "DE.Views.FormsTab.textClear": "<PERSON><PERSON>", "DE.Views.FormsTab.textClearFields": "Alle Felder löschen", "DE.Views.FormsTab.textCreateForm": "<PERSON><PERSON> hinzufügen und ausfüllbare OFORM-Date<PERSON> erstellen", "DE.Views.FormsTab.textGotIt": "OK", "DE.Views.FormsTab.textHighlight": "Einstellungen für Hervorhebungen", "DE.Views.FormsTab.textNoHighlight": "<PERSON><PERSON>", "DE.Views.FormsTab.textRequired": "<PERSON>üllen Sie alle erforderlichen Felder aus, um das Formular zu senden.", "DE.Views.FormsTab.textSubmited": "Das Formular wurde erfolgreich versandt", "DE.Views.FormsTab.tipCheckBox": "Checkbox einfügen", "DE.Views.FormsTab.tipComboBox": "Combobox einfügen", "DE.Views.FormsTab.tipComplexField": "Komplexes Feld einfügen", "DE.Views.FormsTab.tipDownloadForm": "Die Datei als ausfüllbares OFORM-Dokument herunterladen", "DE.Views.FormsTab.tipDropDown": "Dropdown-Liste einfügen", "DE.Views.FormsTab.tipEmailField": "E-Mail Adresse einfügen", "DE.Views.FormsTab.tipImageField": "Bild einfügen", "DE.Views.FormsTab.tipNextForm": "Zum nächsten Feld wechseln", "DE.Views.FormsTab.tipPhoneField": "Telefonnummer einfügen", "DE.Views.FormsTab.tipPrevForm": "Zum vorherigen Feld wechseln", "DE.Views.FormsTab.tipRadioBox": "Radiobutton einfügen", "DE.Views.FormsTab.tipSaveForm": "Als eine ausfüllbare OFORM-Datei speichern", "DE.Views.FormsTab.tipSubmit": "Formular senden", "DE.Views.FormsTab.tipTextField": "Textfeld einfügen", "DE.Views.FormsTab.tipViewForm": "Formular anzeigen", "DE.Views.FormsTab.txtUntitled": "Unbenannt", "DE.Views.HeaderFooterSettings.textBottomCenter": "<PERSON>ten zent<PERSON>t", "DE.Views.HeaderFooterSettings.textBottomLeft": "Unten links", "DE.Views.HeaderFooterSettings.textBottomPage": "Seitenende", "DE.Views.HeaderFooterSettings.textBottomRight": "Unten rechts", "DE.Views.HeaderFooterSettings.textDiffFirst": "<PERSON><PERSON><PERSON> Se<PERSON> anders", "DE.Views.HeaderFooterSettings.textDiffOdd": "Untersch. gerade/ungerade Seiten", "DE.Views.HeaderFooterSettings.textFrom": "<PERSON>en mit", "DE.Views.HeaderFooterSettings.textHeaderFromBottom": "<PERSON><PERSON><PERSON><PERSON> von unten", "DE.Views.HeaderFooterSettings.textHeaderFromTop": "Kopfzeile oberhalb", "DE.Views.HeaderFooterSettings.textInsertCurrent": "In aktuelle Position einfügen", "DE.Views.HeaderFooterSettings.textOptions": "Optionen", "DE.Views.HeaderFooterSettings.textPageNum": "Seitenzahl einfügen", "DE.Views.HeaderFooterSettings.textPageNumbering": "Seitennummerierung", "DE.Views.HeaderFooterSettings.textPosition": "Position", "DE.Views.HeaderFooterSettings.textPrev": "Fortsetzen vom vorherigen Abschnitt", "DE.Views.HeaderFooterSettings.textSameAs": "Mit vor<PERSON>iger verknüpfen", "DE.Views.HeaderFooterSettings.textTopCenter": "<PERSON><PERSON>", "DE.Views.HeaderFooterSettings.textTopLeft": "Oben links", "DE.Views.HeaderFooterSettings.textTopPage": "Seitenanfang", "DE.Views.HeaderFooterSettings.textTopRight": "<PERSON><PERSON> rechts", "DE.Views.HyperlinkSettingsDialog.textDefault": "Gewählter Textabschnitt", "DE.Views.HyperlinkSettingsDialog.textDisplay": "Anzeigen", "DE.Views.HyperlinkSettingsDialog.textExternal": "Externer Link", "DE.Views.HyperlinkSettingsDialog.textInternal": "Stelle im Dokument", "DE.Views.HyperlinkSettingsDialog.textTitle": "Hyperlink-Einstellungen", "DE.Views.HyperlinkSettingsDialog.textTooltip": "QuickInfo-Text", "DE.Views.HyperlinkSettingsDialog.textUrl": "Verknüpfen mit", "DE.Views.HyperlinkSettingsDialog.txtBeginning": "Anfang des Dokuments", "DE.Views.HyperlinkSettingsDialog.txtBookmarks": "Lesezeichen", "DE.Views.HyperlinkSettingsDialog.txtEmpty": "<PERSON><PERSON> ist erford<PERSON>lich", "DE.Views.HyperlinkSettingsDialog.txtHeadings": "Überschriften", "DE.Views.HyperlinkSettingsDialog.txtNotUrl": "<PERSON><PERSON> muss eine URL im Format \"http://www.example.com\" sein", "DE.Views.HyperlinkSettingsDialog.txtSizeLimit": "<PERSON><PERSON> soll maximal 2083 Zeichen beinhalten", "DE.Views.ImageSettings.textAdvanced": "Erweiterte Einstellungen anzeigen", "DE.Views.ImageSettings.textCrop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.ImageSettings.textCropFill": "Ausfüllen", "DE.Views.ImageSettings.textCropFit": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettings.textCropToShape": "Auf Form <PERSON>", "DE.Views.ImageSettings.textEdit": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettings.textEditObject": "Objekt bearbeiten", "DE.Views.ImageSettings.textFitMargins": "<PERSON><PERSON><PERSON><PERSON> anpassen", "DE.Views.ImageSettings.textFlip": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettings.textFromFile": "<PERSON><PERSON> Datei", "DE.Views.ImageSettings.textFromStorage": "Aus dem Speicher", "DE.Views.ImageSettings.textFromUrl": "Aus URL", "DE.Views.ImageSettings.textHeight": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettings.textHint270": "Um 90 ° gegen den Uhrzeigersinn drehen", "DE.Views.ImageSettings.textHint90": "90° im UZS drehen", "DE.Views.ImageSettings.textHintFlipH": "Horizontal kippen", "DE.Views.ImageSettings.textHintFlipV": "Vertikal kippen", "DE.Views.ImageSettings.textInsert": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettings.textOriginalSize": "Tatsächliche Größe", "DE.Views.ImageSettings.textRecentlyUsed": "Zuletzt verwendet", "DE.Views.ImageSettings.textRotate90": "90 Grad drehen", "DE.Views.ImageSettings.textRotation": "Rotation", "DE.Views.ImageSettings.textSize": "Größe", "DE.Views.ImageSettings.textWidth": "Breite", "DE.Views.ImageSettings.textWrap": "Textumbruch", "DE.Views.ImageSettings.txtBehind": "<PERSON><PERSON> dem <PERSON>", "DE.Views.ImageSettings.txtInFront": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettings.txtInline": "Inline", "DE.Views.ImageSettings.txtSquare": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettings.txtThrough": "Durchgehend", "DE.Views.ImageSettings.txtTight": "Passend", "DE.Views.ImageSettings.txtTopAndBottom": "<PERSON><PERSON> und unten", "DE.Views.ImageSettingsAdvanced.strMargins": "Textauffüllung", "DE.Views.ImageSettingsAdvanced.textAbsoluteWH": "Absolut", "DE.Views.ImageSettingsAdvanced.textAlignment": "Ausrichtung", "DE.Views.ImageSettingsAdvanced.textAlt": "Alternativer Text", "DE.Views.ImageSettingsAdvanced.textAltDescription": "Beschreibung", "DE.Views.ImageSettingsAdvanced.textAltTip": "Die alternative textbasierte Darstellung der visuellen Objektinformation, die den Menschen  mit geistigen Behinderungen oder Sehbehinderungen vorgelesen wird, um besser verstehen zu können, was gena<PERSON> auf dem Bild, AutoForm, Diagramm oder der Tabelle dargestellt wurde.", "DE.Views.ImageSettingsAdvanced.textAltTitle": "Titel", "DE.Views.ImageSettingsAdvanced.textAngle": "<PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textArrows": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textAspectRatio": "Seitenverhältnis sperren", "DE.Views.ImageSettingsAdvanced.textAutofit": "Automatisch anpassen", "DE.Views.ImageSettingsAdvanced.textBeginSize": "Startgröße", "DE.Views.ImageSettingsAdvanced.textBeginStyle": "Startlinienart", "DE.Views.ImageSettingsAdvanced.textBelow": "unten", "DE.Views.ImageSettingsAdvanced.textBevel": "Schräge Kante", "DE.Views.ImageSettingsAdvanced.textBottom": "Unten", "DE.Views.ImageSettingsAdvanced.textBottomMargin": "Un<PERSON>er Rand", "DE.Views.ImageSettingsAdvanced.textBtnWrap": "Textumbruch", "DE.Views.ImageSettingsAdvanced.textCapType": "Zierbuchstabe", "DE.Views.ImageSettingsAdvanced.textCenter": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textCharacter": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textColumn": "<PERSON>lt<PERSON>", "DE.Views.ImageSettingsAdvanced.textDistance": "Abstand vom Text", "DE.Views.ImageSettingsAdvanced.textEndSize": "Endgröße", "DE.Views.ImageSettingsAdvanced.textEndStyle": "Endlinienart", "DE.Views.ImageSettingsAdvanced.textFlat": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textFlipped": "Gekippt", "DE.Views.ImageSettingsAdvanced.textHeight": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textHorizontal": "Horizontal", "DE.Views.ImageSettingsAdvanced.textHorizontally": "Horizontal", "DE.Views.ImageSettingsAdvanced.textJoinType": "Verknüpfungstyp", "DE.Views.ImageSettingsAdvanced.textKeepRatio": "Seitenverhältnis beibehalten", "DE.Views.ImageSettingsAdvanced.textLeft": "Links", "DE.Views.ImageSettingsAdvanced.textLeftMargin": "<PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textLine": "<PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textLineStyle": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textMargin": "Rand", "DE.Views.ImageSettingsAdvanced.textMiter": "<PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textMove": "Objekt mit Text verschieben", "DE.Views.ImageSettingsAdvanced.textOptions": "Optionen", "DE.Views.ImageSettingsAdvanced.textOriginalSize": "Tatsächliche Größe", "DE.Views.ImageSettingsAdvanced.textOverlap": "Überlappung zulassen", "DE.Views.ImageSettingsAdvanced.textPage": "Seite", "DE.Views.ImageSettingsAdvanced.textParagraph": "Absatz", "DE.Views.ImageSettingsAdvanced.textPosition": "Position", "DE.Views.ImageSettingsAdvanced.textPositionPc": "Relative Position", "DE.Views.ImageSettingsAdvanced.textRelative": "im Bezug auf ", "DE.Views.ImageSettingsAdvanced.textRelativeWH": "Relativ", "DE.Views.ImageSettingsAdvanced.textResizeFit": "Die Form am Text anpassen", "DE.Views.ImageSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textRightMargin": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textRightOf": "rechts von", "DE.Views.ImageSettingsAdvanced.textRotation": "Rotation", "DE.Views.ImageSettingsAdvanced.textRound": "Rund", "DE.Views.ImageSettingsAdvanced.textShape": "Formeinstellungen", "DE.Views.ImageSettingsAdvanced.textSize": "Größe", "DE.Views.ImageSettingsAdvanced.textSquare": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textTextBox": "<PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textTitle": "Bild - Erweiterte Einstellungen", "DE.Views.ImageSettingsAdvanced.textTitleChart": "Diagramm - Erweiterte Einstellungen", "DE.Views.ImageSettingsAdvanced.textTitleShape": "Form - Erweiterte Einstellungen", "DE.Views.ImageSettingsAdvanced.textTop": "<PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textTopMargin": "<PERSON>berer Rand", "DE.Views.ImageSettingsAdvanced.textVertical": "Vertikal", "DE.Views.ImageSettingsAdvanced.textVertically": "Vertikal", "DE.Views.ImageSettingsAdvanced.textWeightArrows": "Stärken & Pfeile", "DE.Views.ImageSettingsAdvanced.textWidth": "Breite", "DE.Views.ImageSettingsAdvanced.textWrap": "Textumbruch", "DE.Views.ImageSettingsAdvanced.textWrapBehindTooltip": "<PERSON><PERSON> dem <PERSON>", "DE.Views.ImageSettingsAdvanced.textWrapInFrontTooltip": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textWrapInlineTooltip": "Inline", "DE.Views.ImageSettingsAdvanced.textWrapSquareTooltip": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textWrapThroughTooltip": "Durchgehend", "DE.Views.ImageSettingsAdvanced.textWrapTightTooltip": "Passend", "DE.Views.ImageSettingsAdvanced.textWrapTopbottomTooltip": "<PERSON><PERSON> und unten", "DE.Views.LeftMenu.tipAbout": "Über das Produkt", "DE.Views.LeftMenu.tipChat": "Cha<PERSON>", "DE.Views.LeftMenu.tipComments": "Kommentare", "DE.Views.LeftMenu.tipNavigation": "Navigation", "DE.Views.LeftMenu.tipOutline": "Überschriften", "DE.Views.LeftMenu.tipPageThumbnails": "Miniaturansichten", "DE.Views.LeftMenu.tipPlugins": "Plugins", "DE.Views.LeftMenu.tipSearch": "<PERSON><PERSON>", "DE.Views.LeftMenu.tipSupport": "Feed<PERSON> und Support", "DE.Views.LeftMenu.tipTitles": "Titel", "DE.Views.LeftMenu.txtDeveloper": "ENTWICKLERMODUS", "DE.Views.LeftMenu.txtEditor": "Dokument Editor", "DE.Views.LeftMenu.txtLimit": "Zugriffseinschränkung", "DE.Views.LeftMenu.txtTrial": "Trial-Modus", "DE.Views.LeftMenu.txtTrialDev": "Testversion für Entwickler-Modus", "DE.Views.LineNumbersDialog.textAddLineNumbering": "Zeilennummer hinzufügen", "DE.Views.LineNumbersDialog.textApplyTo": "Änderungen anwenden", "DE.Views.LineNumbersDialog.textContinuous": "Ununterbrochen", "DE.Views.LineNumbersDialog.textCountBy": "Zählintervall", "DE.Views.LineNumbersDialog.textDocument": "Zum ganzen Dokument", "DE.Views.LineNumbersDialog.textForward": "Bis zum Ende des Dokuments", "DE.Views.LineNumbersDialog.textFromText": "<PERSON><PERSON> dem Text", "DE.Views.LineNumbersDialog.textNumbering": "Nummerierung", "DE.Views.LineNumbersDialog.textRestartEachPage": "Jede Seite neu beginnen", "DE.Views.LineNumbersDialog.textRestartEachSection": "<PERSON>en Abschnitt neu beginnen", "DE.Views.LineNumbersDialog.textSection": "Aktueller Abschnitt", "DE.Views.LineNumbersDialog.textStartAt": "Beginnen mit", "DE.Views.LineNumbersDialog.textTitle": "Zeilennummern", "DE.Views.LineNumbersDialog.txtAutoText": "Automatisch", "DE.Views.Links.capBtnAddText": "Text hinzufügen", "DE.Views.Links.capBtnBookmarks": "Lesezeichen", "DE.Views.Links.capBtnCaption": "Beschriftung", "DE.Views.Links.capBtnContentsUpdate": "Aktualisierung", "DE.Views.Links.capBtnCrossRef": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.Links.capBtnInsContents": "Inhaltsverzeichnis", "DE.Views.Links.capBtnInsFootnote": "Fußnote", "DE.Views.Links.capBtnInsLink": "Hyperlink", "DE.Views.Links.capBtnTOF": "Abbildungsverzeichnis", "DE.Views.Links.confirmDeleteFootnotes": "<PERSON>öchten Sie alle Fußnoten löschen?", "DE.Views.Links.confirmReplaceTOF": "Möchten Sie das ausgewählte Abbildungsverzeichnis wirklich ersetzen?", "DE.Views.Links.mniConvertNote": "Alle Anmerkungen konvertieren", "DE.Views.Links.mniDelFootnote": "Alle Anmerkungen löschen ", "DE.Views.Links.mniInsEndnote": "Endnote einfügen", "DE.Views.Links.mniInsFootnote": "Fußnoten einfügen", "DE.Views.Links.mniNoteSettings": "Hinweise Einstellungen", "DE.Views.Links.textContentsRemove": "Inhaltsverzeichnis entfernen", "DE.Views.Links.textContentsSettings": "Einstellungen", "DE.Views.Links.textConvertToEndnotes": "Alle Fußnoten in Endnoten konvertieren", "DE.Views.Links.textConvertToFootnotes": "Alle Endnoten in Fußnoten konvertieren", "DE.Views.Links.textGotoEndnote": "<PERSON><PERSON>", "DE.Views.Links.textGotoFootnote": "<PERSON><PERSON> übergehen", "DE.Views.Links.textSwapNotes": "Fußnoten und Endnoten wechseln", "DE.Views.Links.textUpdateAll": "Gesamtes Verzeichnis aktualisieren", "DE.Views.Links.textUpdatePages": "Nur Seitenzahlen aktualisieren", "DE.Views.Links.tipAddText": "Überschrift in das Inhaltsverzeichnis einfügen", "DE.Views.Links.tipBookmarks": "Lesezeichen erstellen", "DE.Views.Links.tipCaption": "Beschriftung einfügen", "DE.Views.Links.tipContents": "Inhaltsverzeichnis einfügen", "DE.Views.Links.tipContentsUpdate": "Inhaltsverzeichnis aktualisieren", "DE.Views.Links.tipCrossRef": "Querverweis einfügen", "DE.Views.Links.tipInsertHyperlink": "Hyperlink hinzufügen", "DE.Views.Links.tipNotes": "Fußnoten einfügen oder bearbeiten", "DE.Views.Links.tipTableFigures": "Abbildungsverzeichnis einfügen", "DE.Views.Links.tipTableFiguresUpdate": "Abbildungsverzeichnis aktualisieren", "DE.Views.Links.titleUpdateTOF": "Abbildungsverzeichnis aktualisieren", "DE.Views.Links.txtDontShowTof": "Im Inhaltsverzeichnis nicht anzeigen", "DE.Views.Links.txtLevel": "<PERSON><PERSON><PERSON>", "DE.Views.ListSettingsDialog.textAuto": "Automatisch", "DE.Views.ListSettingsDialog.textCenter": "<PERSON><PERSON>", "DE.Views.ListSettingsDialog.textLeft": "Links", "DE.Views.ListSettingsDialog.textLevel": "<PERSON><PERSON><PERSON>", "DE.Views.ListSettingsDialog.textPreview": "Vorschau", "DE.Views.ListSettingsDialog.textRight": "<PERSON><PERSON><PERSON>", "DE.Views.ListSettingsDialog.txtAlign": "Ausrichtung", "DE.Views.ListSettingsDialog.txtBullet": "Aufzählungszeichen", "DE.Views.ListSettingsDialog.txtColor": "Farbe", "DE.Views.ListSettingsDialog.txtFont": "Schriftart und Symbol", "DE.Views.ListSettingsDialog.txtLikeText": "Wie ein Text", "DE.Views.ListSettingsDialog.txtNewBullet": "Neues Aufzählungszeichen", "DE.Views.ListSettingsDialog.txtNone": "<PERSON><PERSON>", "DE.Views.ListSettingsDialog.txtSize": "Größe", "DE.Views.ListSettingsDialog.txtSymbol": "Symbol", "DE.Views.ListSettingsDialog.txtTitle": "Listeneinstellungen", "DE.Views.ListSettingsDialog.txtType": "<PERSON><PERSON>", "DE.Views.MailMergeEmailDlg.filePlaceholder": "PDF", "DE.Views.MailMergeEmailDlg.okButtonText": "Senden", "DE.Views.MailMergeEmailDlg.subjectPlaceholder": "<PERSON>a", "DE.Views.MailMergeEmailDlg.textAttachDocx": "Als DOCX anhängen", "DE.Views.MailMergeEmailDlg.textAttachPdf": "Als PDF anhängen", "DE.Views.MailMergeEmailDlg.textFileName": "Dateiname", "DE.Views.MailMergeEmailDlg.textFormat": "E-Mail-Format", "DE.Views.MailMergeEmailDlg.textFrom": "<PERSON>", "DE.Views.MailMergeEmailDlg.textHTML": "HTML", "DE.Views.MailMergeEmailDlg.textMessage": "Nachricht", "DE.Views.MailMergeEmailDlg.textSubject": "<PERSON><PERSON><PERSON>", "DE.Views.MailMergeEmailDlg.textTitle": "Per E-Mail senden", "DE.Views.MailMergeEmailDlg.textTo": "<PERSON><PERSON>", "DE.Views.MailMergeEmailDlg.textWarning": "Achtung!", "DE.Views.MailMergeEmailDlg.textWarningMsg": "<PERSON><PERSON> <PERSON><PERSON>, dass Sendung nicht gestoppt werden kann, wenn man auf den <PERSON> Send<PERSON> klickt.", "DE.Views.MailMergeSettings.downloadMergeTitle": "Merging", "DE.Views.MailMergeSettings.errorMailMergeSaveFile": "Merge ist fehlgeschlagen.", "DE.Views.MailMergeSettings.notcriticalErrorTitle": "Achtung", "DE.Views.MailMergeSettings.textAddRecipients": "Einige Empfänger zur Liste hinzufügen", "DE.Views.MailMergeSettings.textAll": "Alle Datensätze ", "DE.Views.MailMergeSettings.textCurrent": "Aktueller Datensatz", "DE.Views.MailMergeSettings.textDataSource": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.MailMergeSettings.textDocx": "Docx", "DE.Views.MailMergeSettings.textDownload": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.MailMergeSettings.textEditData": "Empfängerliste bearbeiten", "DE.Views.MailMergeSettings.textEmail": "E-Email", "DE.Views.MailMergeSettings.textFrom": "<PERSON>", "DE.Views.MailMergeSettings.textGoToMail": "Zu E-Mail übergehen", "DE.Views.MailMergeSettings.textHighlight": "Serienbrief-<PERSON><PERSON>", "DE.Views.MailMergeSettings.textInsertField": "Seriendruckfeld einfügen", "DE.Views.MailMergeSettings.textMaxRecepients": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (max. 100)", "DE.Views.MailMergeSettings.textMerge": "Verbinden", "DE.Views.MailMergeSettings.textMergeFields": "<PERSON><PERSON>", "DE.Views.MailMergeSettings.textMergeTo": "Verbindung mit", "DE.Views.MailMergeSettings.textPdf": "PDF", "DE.Views.MailMergeSettings.textPortal": "Speichern", "DE.Views.MailMergeSettings.textPreview": "Ergebnisvorschau", "DE.Views.MailMergeSettings.textReadMore": "<PERSON><PERSON> lesen", "DE.Views.MailMergeSettings.textSendMsg": "Alle E-Mail-Nachrichten sind bereit und werden versendet. <br> Die Geschwindigkeit des Email-Versands hängt von Ihrem Mail-Dienst ab. Sie können an dem Dokument weiterarbeiten oder es schließen. Nachdem der Email-Versand fertig ist, werden Sie per E-Mail, die Sie bei der Registriering wervendeten, ben<PERSON><PERSON><PERSON><PERSON>.", "DE.Views.MailMergeSettings.textTo": "<PERSON><PERSON>", "DE.Views.MailMergeSettings.txtFirst": "Zum ersten Datensatz", "DE.Views.MailMergeSettings.txtFromToError": "<PERSON> Wert \"<PERSON>\" muss weniger als \"<PERSON><PERSON>\" sein", "DE.Views.MailMergeSettings.txtLast": "Zum letzten Datensatz", "DE.Views.MailMergeSettings.txtNext": "Zum nächsten Datensatz", "DE.Views.MailMergeSettings.txtPrev": "<PERSON>u den vorherigen Rekord", "DE.Views.MailMergeSettings.txtUntitled": "Unbenannt", "DE.Views.MailMergeSettings.warnProcessMailMerge": "Merge ist fehlgeschlagen", "DE.Views.Navigation.strNavigate": "Überschriften", "DE.Views.Navigation.txtClosePanel": "Überschriften schließen", "DE.Views.Navigation.txtCollapse": "Alle einklappen", "DE.Views.Navigation.txtDemote": "Tieferstufen", "DE.Views.Navigation.txtEmpty": "Dieses Dokument enthält keine Überschriften.<br>Wenden Sie ein Überschriftenformat auf den Text an, damit es im Inhaltsverzeichnis angezeigt wird.", "DE.Views.Navigation.txtEmptyItem": "<PERSON><PERSON>", "DE.Views.Navigation.txtEmptyViewer": "Dieses Dokument enthält keine Überschriften.", "DE.Views.Navigation.txtExpand": "Alle ausklappen", "DE.Views.Navigation.txtExpandToLevel": "<PERSON><PERSON> er<PERSON>", "DE.Views.Navigation.txtFontSize": "Schriftgröße", "DE.Views.Navigation.txtHeadingAfter": "Neue Überschrift nach", "DE.Views.Navigation.txtHeadingBefore": "Neue Überschrift vor", "DE.Views.Navigation.txtLarge": "<PERSON><PERSON><PERSON>", "DE.Views.Navigation.txtMedium": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.Navigation.txtNewHeading": "Neue Unterüberschrift", "DE.Views.Navigation.txtPromote": "Höherstufen", "DE.Views.Navigation.txtSelect": "Inhalt auswählen", "DE.Views.Navigation.txtSettings": "Einstellungen von Überschriften", "DE.Views.Navigation.txtSmall": "<PERSON>", "DE.Views.Navigation.txtWrapHeadings": "Lange Überschriften umbrechen", "DE.Views.NoteSettingsDialog.textApply": "<PERSON><PERSON><PERSON>", "DE.Views.NoteSettingsDialog.textApplyTo": "Änderungen anwenden", "DE.Views.NoteSettingsDialog.textContinue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.NoteSettingsDialog.textCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.NoteSettingsDialog.textDocEnd": "Ende des Dokuments", "DE.Views.NoteSettingsDialog.textDocument": "Das ganze Dokument", "DE.Views.NoteSettingsDialog.textEachPage": "Jede Seite neu beginnen", "DE.Views.NoteSettingsDialog.textEachSection": "<PERSON>en Abschnitt neu beginnen", "DE.Views.NoteSettingsDialog.textEndnote": "Endnote", "DE.Views.NoteSettingsDialog.textFootnote": "Fußnote", "DE.Views.NoteSettingsDialog.textFormat": "Format", "DE.Views.NoteSettingsDialog.textInsert": "Einfügen", "DE.Views.NoteSettingsDialog.textLocation": "<PERSON><PERSON>", "DE.Views.NoteSettingsDialog.textNumbering": "Nummerierung", "DE.Views.NoteSettingsDialog.textNumFormat": "Zahlenformat", "DE.Views.NoteSettingsDialog.textPageBottom": "Seitenende", "DE.Views.NoteSettingsDialog.textSectEnd": "Ende des Abschnitts", "DE.Views.NoteSettingsDialog.textSection": "Aktueller Abschnitt", "DE.Views.NoteSettingsDialog.textStart": "Starten", "DE.Views.NoteSettingsDialog.textTextBottom": "Unterhalb des Textes", "DE.Views.NoteSettingsDialog.textTitle": "Hinweise Einstellungen", "DE.Views.NotesRemoveDialog.textEnd": "Alle Endnoten löschen", "DE.Views.NotesRemoveDialog.textFoot": "Alle Fußnoten löschen ", "DE.Views.NotesRemoveDialog.textTitle": "Anmerkungen löschen", "DE.Views.PageMarginsDialog.notcriticalErrorTitle": "Achtung", "DE.Views.PageMarginsDialog.textBottom": "Unten", "DE.Views.PageMarginsDialog.textGutter": "Bundsteg", "DE.Views.PageMarginsDialog.textGutterPosition": "Bundsteg-Position", "DE.Views.PageMarginsDialog.textInside": "Innen", "DE.Views.PageMarginsDialog.textLandscape": "Querformat", "DE.Views.PageMarginsDialog.textLeft": "Left", "DE.Views.PageMarginsDialog.textMirrorMargins": "Gegenüberliegende Seiten", "DE.Views.PageMarginsDialog.textMultiplePages": "<PERSON><PERSON><PERSON>", "DE.Views.PageMarginsDialog.textNormal": "Normal", "DE.Views.PageMarginsDialog.textOrientation": "Ausrichtung", "DE.Views.PageMarginsDialog.textOutside": "Außen", "DE.Views.PageMarginsDialog.textPortrait": "Hochformat", "DE.Views.PageMarginsDialog.textPreview": "Vorschau", "DE.Views.PageMarginsDialog.textRight": "<PERSON><PERSON><PERSON>", "DE.Views.PageMarginsDialog.textTitle": "<PERSON><PERSON><PERSON>", "DE.Views.PageMarginsDialog.textTop": "<PERSON><PERSON>", "DE.Views.PageMarginsDialog.txtMarginsH": "Die oberen und unteren Ränder sind zu hoch für eingegebene Seitenhöhe", "DE.Views.PageMarginsDialog.txtMarginsW": "Die Ränder rechts und links sind bei gegebener Seitenbreite zu breit. ", "DE.Views.PageSizeDialog.textHeight": "<PERSON><PERSON><PERSON>", "DE.Views.PageSizeDialog.textPreset": "Voreinstellung", "DE.Views.PageSizeDialog.textTitle": "Seitenformat", "DE.Views.PageSizeDialog.textWidth": "Breite", "DE.Views.PageSizeDialog.txtCustom": "Benutzerdefinierte", "DE.Views.PageThumbnails.textClosePanel": "Miniaturansichten schließen", "DE.Views.PageThumbnails.textHighlightVisiblePart": "Sichtbaren Teil der Seite hervorheben", "DE.Views.PageThumbnails.textPageThumbnails": "Miniaturansichten", "DE.Views.PageThumbnails.textThumbnailsSettings": "Einstellungen von Miniaturansichten", "DE.Views.PageThumbnails.textThumbnailsSize": "<PERSON><PERSON><PERSON><PERSON> von Miniaturansichten", "DE.Views.ParagraphSettings.strIndent": "Einzüge ", "DE.Views.ParagraphSettings.strIndentsLeftText": "Links", "DE.Views.ParagraphSettings.strIndentsRightText": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettings.strIndentsSpecial": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettings.strLineHeight": "Zeilenabstand", "DE.Views.ParagraphSettings.strParagraphSpacing": "Absatzabstand", "DE.Views.ParagraphSettings.strSomeParagraphSpace": "Kein Abstand zwischen Absätzen gleicher Formatierung", "DE.Views.ParagraphSettings.strSpacingAfter": "Nach", "DE.Views.ParagraphSettings.strSpacingBefore": "<PERSON><PERSON><PERSON> ", "DE.Views.ParagraphSettings.textAdvanced": "Erweiterte Einstellungen anzeigen", "DE.Views.ParagraphSettings.textAt": "Um", "DE.Views.ParagraphSettings.textAtLeast": "Mindestens", "DE.Views.ParagraphSettings.textAuto": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettings.textBackColor": "Hintergrundfarbe", "DE.Views.ParagraphSettings.textExact": "<PERSON><PERSON>", "DE.Views.ParagraphSettings.textFirstLine": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettings.textHanging": "Hängend", "DE.Views.ParagraphSettings.textNoneSpecial": "(kein)", "DE.Views.ParagraphSettings.txtAutoText": "Automatisch", "DE.Views.ParagraphSettingsAdvanced.noTabs": "Die festgelegten Registerkarten werden in diesem Feld erscheinen", "DE.Views.ParagraphSettingsAdvanced.strAllCaps": "Alle Großbuchstaben", "DE.Views.ParagraphSettingsAdvanced.strBorders": "Rahmen & Füllung", "DE.Views.ParagraphSettingsAdvanced.strBreakBefore": "Seitenumbruch oberhalb", "DE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "Doppeltes Durchstreichen", "DE.Views.ParagraphSettingsAdvanced.strIndent": "Einzüge ", "DE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "Links", "DE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Zeilenabstand", "DE.Views.ParagraphSettingsAdvanced.strIndentsOutlinelevel": "Gliederungsebene", "DE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "Nach", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "<PERSON><PERSON><PERSON> ", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.strKeepLines": "Absatz zusammenhalten", "DE.Views.ParagraphSettingsAdvanced.strKeepNext": "Absätze nicht trennen", "DE.Views.ParagraphSettingsAdvanced.strMargins": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.strOrphan": "Absatzkontrolle", "DE.Views.ParagraphSettingsAdvanced.strParagraphFont": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Einzüge und Abstände", "DE.Views.ParagraphSettingsAdvanced.strParagraphLine": "Zeilen- und Seitenumbrüche", "DE.Views.ParagraphSettingsAdvanced.strParagraphPosition": "Positionierung", "DE.Views.ParagraphSettingsAdvanced.strSmallCaps": "Kapitälchen", "DE.Views.ParagraphSettingsAdvanced.strSomeParagraphSpace": "Kein Abstand zwischen Absätzen gleicher Formatierung", "DE.Views.ParagraphSettingsAdvanced.strSpacing": "Abstand", "DE.Views.ParagraphSettingsAdvanced.strStrike": "Durchgestrichen", "DE.Views.ParagraphSettingsAdvanced.strSubscript": "Tiefgestellt", "DE.Views.ParagraphSettingsAdvanced.strSuperscript": "Hochgestellt", "DE.Views.ParagraphSettingsAdvanced.strSuppressLineNumbers": "Zeilennummerierung verbieten", "DE.Views.ParagraphSettingsAdvanced.strTabs": "Tabulatoren", "DE.Views.ParagraphSettingsAdvanced.textAlign": "Ausrichtung", "DE.Views.ParagraphSettingsAdvanced.textAll": "Alle", "DE.Views.ParagraphSettingsAdvanced.textAtLeast": "Mindestens", "DE.Views.ParagraphSettingsAdvanced.textAuto": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textBackColor": "Hintergrundfarbe", "DE.Views.ParagraphSettingsAdvanced.textBodyText": "Standard text", "DE.Views.ParagraphSettingsAdvanced.textBorderColor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textBorderDesc": "<PERSON>lick<PERSON> Sie aufs Diagramm oder nutzen Sie die Buttons, um Umrandungen zu wählen und den gewählten Stil anzuwenden", "DE.Views.ParagraphSettingsAdvanced.textBorderWidth": "Rahmenstärke", "DE.Views.ParagraphSettingsAdvanced.textBottom": "Unten", "DE.Views.ParagraphSettingsAdvanced.textCentered": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Zeichenabstand", "DE.Views.ParagraphSettingsAdvanced.textContext": "Kontextbezogene", "DE.Views.ParagraphSettingsAdvanced.textContextDiscret": "Kontextbezogene und freie", "DE.Views.ParagraphSettingsAdvanced.textContextHistDiscret": "Kontextbezogene, historische und freie", "DE.Views.ParagraphSettingsAdvanced.textContextHistorical": "Kontextbezogene und historische", "DE.Views.ParagraphSettingsAdvanced.textDefault": "Standardregisterkarte", "DE.Views.ParagraphSettingsAdvanced.textDiscret": "Frei<PERSON>", "DE.Views.ParagraphSettingsAdvanced.textEffects": "Effekte", "DE.Views.ParagraphSettingsAdvanced.textExact": "<PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textFirstLine": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textHanging": "Hängend", "DE.Views.ParagraphSettingsAdvanced.textHistorical": "Historische", "DE.Views.ParagraphSettingsAdvanced.textHistoricalDiscret": "Historische und freie", "DE.Views.ParagraphSettingsAdvanced.textJustified": "Blocksatz", "DE.Views.ParagraphSettingsAdvanced.textLeader": "Füllzeichen", "DE.Views.ParagraphSettingsAdvanced.textLeft": "Links", "DE.Views.ParagraphSettingsAdvanced.textLevel": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textLigatures": "Doppelbuchstaben", "DE.Views.ParagraphSettingsAdvanced.textNone": "<PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(kein)", "DE.Views.ParagraphSettingsAdvanced.textOpenType": "OpenType-Funktionen", "DE.Views.ParagraphSettingsAdvanced.textPosition": "Position", "DE.Views.ParagraphSettingsAdvanced.textRemove": "Löschen", "DE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Alle löschen", "DE.Views.ParagraphSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textSet": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textSpacing": "Abstand", "DE.Views.ParagraphSettingsAdvanced.textStandard": "<PERSON><PERSON> standart<PERSON>e", "DE.Views.ParagraphSettingsAdvanced.textStandardContext": "Standartisierte und kontextbezogene", "DE.Views.ParagraphSettingsAdvanced.textStandardContextDiscret": "Standartisierte, kontextbezogene und freie", "DE.Views.ParagraphSettingsAdvanced.textStandardContextHist": "Standartisierte, kontextbezogene und historische", "DE.Views.ParagraphSettingsAdvanced.textStandardDiscret": "Standartisierte und freie", "DE.Views.ParagraphSettingsAdvanced.textStandardHistDiscret": "Standartisierte, historische und freie", "DE.Views.ParagraphSettingsAdvanced.textStandardHistorical": "Standartisierte und historische", "DE.Views.ParagraphSettingsAdvanced.textTabCenter": "<PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textTabLeft": "Links", "DE.Views.ParagraphSettingsAdvanced.textTabPosition": "Tabulatorposition", "DE.Views.ParagraphSettingsAdvanced.textTabRight": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textTitle": "Absatz - Erweiterte Einstellungen", "DE.Views.ParagraphSettingsAdvanced.textTop": "<PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.tipAll": "Äußere Rahmenlinie und alle inneren Linien festlegen", "DE.Views.ParagraphSettingsAdvanced.tipBottom": "Nur untere Rahmenlinie festlegen", "DE.Views.ParagraphSettingsAdvanced.tipInner": "Nur innere horizontale Linien festlegen", "DE.Views.ParagraphSettingsAdvanced.tipLeft": "Nur linke Rahmenlinie festlegen", "DE.Views.ParagraphSettingsAdvanced.tipNone": "<PERSON><PERSON> festlegen", "DE.Views.ParagraphSettingsAdvanced.tipOuter": "Nur äußere Rahmenlinie festlegen", "DE.Views.ParagraphSettingsAdvanced.tipRight": "<PERSON>ur rechte Rahmenlinie festlegen", "DE.Views.ParagraphSettingsAdvanced.tipTop": "Nur obere Rahmenlinie festlegen", "DE.Views.ParagraphSettingsAdvanced.txtAutoText": "Automatisch", "DE.Views.ParagraphSettingsAdvanced.txtNoBorders": "<PERSON><PERSON>", "DE.Views.PrintWithPreview.textMarginsLast": " Benutzerdefiniert als letzte", "DE.Views.PrintWithPreview.textMarginsModerate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.PrintWithPreview.textMarginsNarrow": "<PERSON><PERSON><PERSON>", "DE.Views.PrintWithPreview.textMarginsNormal": "Normal", "DE.Views.PrintWithPreview.textMarginsUsNormal": "Normal (US)", "DE.Views.PrintWithPreview.textMarginsWide": "Breit", "DE.Views.PrintWithPreview.txtAllPages": "Alle Seiten", "DE.Views.PrintWithPreview.txtBottom": "Unten", "DE.Views.PrintWithPreview.txtCurrentPage": "Aktuelle Seite", "DE.Views.PrintWithPreview.txtCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.PrintWithPreview.txtCustomPages": "Benutzerdefinierter Druck", "DE.Views.PrintWithPreview.txtLandscape": "Querformat", "DE.Views.PrintWithPreview.txtLeft": "Links", "DE.Views.PrintWithPreview.txtMargins": "<PERSON><PERSON><PERSON>", "DE.Views.PrintWithPreview.txtOf": "von {0}", "DE.Views.PrintWithPreview.txtPage": "Seite", "DE.Views.PrintWithPreview.txtPageNumInvalid": "Ungültige Seitennummer", "DE.Views.PrintWithPreview.txtPageOrientation": "Seitenausrichtung", "DE.Views.PrintWithPreview.txtPages": "Seiten", "DE.Views.PrintWithPreview.txtPageSize": "Seitengröße", "DE.Views.PrintWithPreview.txtPortrait": "Hochformat", "DE.Views.PrintWithPreview.txtPrint": "<PERSON><PERSON><PERSON>", "DE.Views.PrintWithPreview.txtPrintPdf": "Als PDF-<PERSON><PERSON> d<PERSON>n", "DE.Views.PrintWithPreview.txtPrintRange": "Dr<PERSON>berei<PERSON>", "DE.Views.PrintWithPreview.txtRight": "<PERSON><PERSON><PERSON>", "DE.Views.PrintWithPreview.txtSelection": "Auswahl", "DE.Views.PrintWithPreview.txtTop": "<PERSON><PERSON>", "DE.Views.ProtectDialog.textComments": "Kommentare", "DE.Views.ProtectDialog.textForms": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.ProtectDialog.textReview": "Überarbeitungen", "DE.Views.ProtectDialog.textView": "<PERSON><PERSON> (Schreibgeschützt)", "DE.Views.ProtectDialog.txtAllow": "Nur diese Art der Bearbeitung im Dokument zulassen", "DE.Views.ProtectDialog.txtIncorrectPwd": "Bestätigungseingabe ist nicht identisch", "DE.Views.ProtectDialog.txtOptional": "optional", "DE.Views.ProtectDialog.txtPassword": "Kennwort", "DE.Views.ProtectDialog.txtProtect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.ProtectDialog.txtRepeat": "Kenn<PERSON><PERSON> wiederholen", "DE.Views.ProtectDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.ProtectDialog.txtWarning": "Vorsicht: <PERSON>n Si<PERSON> das Kennwort verlieren oder vergessen, lässt es sich nicht mehr wiederherstellen. Bewahren Sie es an einem sicheren Ort auf.", "DE.Views.RightMenu.txtChartSettings": "Diagrammeinstellungen", "DE.Views.RightMenu.txtFormSettings": "Einstellungen des Formulars", "DE.Views.RightMenu.txtHeaderFooterSettings": "Kopf- und Fußzeileneinstellungen", "DE.Views.RightMenu.txtImageSettings": "Bild-Einstellungen", "DE.Views.RightMenu.txtMailMergeSettings": "Seriendruckeinstellungen ", "DE.Views.RightMenu.txtParagraphSettings": "Absatzeinstellungen", "DE.Views.RightMenu.txtShapeSettings": "Formeinstellungen", "DE.Views.RightMenu.txtSignatureSettings": "Signatureinstellungen", "DE.Views.RightMenu.txtTableSettings": "Tabellen-Einstellungen", "DE.Views.RightMenu.txtTextArtSettings": "TextArt-Einstellungen", "DE.Views.ShapeSettings.strBackground": "Hintergrundfarbe", "DE.Views.ShapeSettings.strChange": "AutoForm ändern", "DE.Views.ShapeSettings.strColor": "Farbe", "DE.Views.ShapeSettings.strFill": "Füllung", "DE.Views.ShapeSettings.strForeground": "Vordergrundfarbe", "DE.Views.ShapeSettings.strPattern": "Muster", "DE.Views.ShapeSettings.strShadow": "<PERSON><PERSON><PERSON> anzeigen", "DE.Views.ShapeSettings.strSize": "Größe", "DE.Views.ShapeSettings.strStroke": "<PERSON><PERSON>", "DE.Views.ShapeSettings.strTransparency": "Undurchsichtigkeit", "DE.Views.ShapeSettings.strType": "<PERSON><PERSON>", "DE.Views.ShapeSettings.textAdvanced": "Erweiterte Einstellungen anzeigen", "DE.Views.ShapeSettings.textAngle": "<PERSON><PERSON>", "DE.Views.ShapeSettings.textBorderSizeErr": "Der eingegebene Wert ist falsch.<br>Bitte geben Sie einen Wert zwischen 0 pt und 1584 pt ein.", "DE.Views.ShapeSettings.textColor": "Farbfüllung", "DE.Views.ShapeSettings.textDirection": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.textEmptyPattern": "<PERSON><PERSON>", "DE.Views.ShapeSettings.textFlip": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.textFromFile": "<PERSON><PERSON> Datei", "DE.Views.ShapeSettings.textFromStorage": "Aus dem Speicher", "DE.Views.ShapeSettings.textFromUrl": "Aus URL", "DE.Views.ShapeSettings.textGradient": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.textGradientFill": "Füllung mit Farbverlauf", "DE.Views.ShapeSettings.textHint270": "Um 90 ° gegen den Uhrzeigersinn drehen", "DE.Views.ShapeSettings.textHint90": "90° im UZS drehen", "DE.Views.ShapeSettings.textHintFlipH": "Horizontal kippen", "DE.Views.ShapeSettings.textHintFlipV": "Vertikal kippen", "DE.Views.ShapeSettings.textImageTexture": "Bild oder Textur", "DE.Views.ShapeSettings.textLinear": "Linear", "DE.Views.ShapeSettings.textNoFill": "<PERSON><PERSON>", "DE.Views.ShapeSettings.textPatternFill": "Muster", "DE.Views.ShapeSettings.textPosition": "Stellung", "DE.Views.ShapeSettings.textRadial": "Radial", "DE.Views.ShapeSettings.textRecentlyUsed": "Zuletzt verwendet", "DE.Views.ShapeSettings.textRotate90": "90 Grad drehen", "DE.Views.ShapeSettings.textRotation": "Rotation", "DE.Views.ShapeSettings.textSelectImage": "Bild auswählen", "DE.Views.ShapeSettings.textSelectTexture": "Auswählen", "DE.Views.ShapeSettings.textStretch": "Ausdehnung", "DE.Views.ShapeSettings.textStyle": "Stil", "DE.Views.ShapeSettings.textTexture": "Aus Textur", "DE.Views.ShapeSettings.textTile": "<PERSON><PERSON>", "DE.Views.ShapeSettings.textWrap": "Textumbruch", "DE.Views.ShapeSettings.tipAddGradientPoint": "Punkt des Farbverlaufs einfügen", "DE.Views.ShapeSettings.tipRemoveGradientPoint": "Punkt des Farbverlaufs entfernen", "DE.Views.ShapeSettings.txtBehind": "<PERSON><PERSON> dem <PERSON>", "DE.Views.ShapeSettings.txtBrownPaper": "Kraftpapier", "DE.Views.ShapeSettings.txtCanvas": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.txtCarton": "<PERSON><PERSON>", "DE.Views.ShapeSettings.txtDarkFabric": "<PERSON>nk<PERSON> Stoff", "DE.Views.ShapeSettings.txtGrain": "<PERSON><PERSON>", "DE.Views.ShapeSettings.txtGranite": "Granit", "DE.Views.ShapeSettings.txtGreyPaper": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.txtInFront": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.txtInline": "Inline", "DE.Views.ShapeSettings.txtKnit": "K<PERSON><PERSON>", "DE.Views.ShapeSettings.txtLeather": "<PERSON><PERSON>", "DE.Views.ShapeSettings.txtNoBorders": "<PERSON><PERSON>", "DE.Views.ShapeSettings.txtPapyrus": "Papyrus", "DE.Views.ShapeSettings.txtSquare": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.txtThrough": "Durchgehend", "DE.Views.ShapeSettings.txtTight": "Passend", "DE.Views.ShapeSettings.txtTopAndBottom": "<PERSON><PERSON> und unten", "DE.Views.ShapeSettings.txtWood": "<PERSON><PERSON>z", "DE.Views.SignatureSettings.notcriticalErrorTitle": "<PERSON><PERSON><PERSON>", "DE.Views.SignatureSettings.strDelete": "Signatur entfernen", "DE.Views.SignatureSettings.strDetails": "Signaturdetails", "DE.Views.SignatureSettings.strInvalid": "Ungültige Signaturen", "DE.Views.SignatureSettings.strRequested": "Angeforderte Signaturen", "DE.Views.SignatureSettings.strSetup": "Signatureinrichtung", "DE.Views.SignatureSettings.strSign": "Signieren", "DE.Views.SignatureSettings.strSignature": "Signatur", "DE.Views.SignatureSettings.strSigner": "Signaturgeber", "DE.Views.SignatureSettings.strValid": "Gültige Signaturen", "DE.Views.SignatureSettings.txtContinueEditing": "Trotzdem bearbeiten", "DE.Views.SignatureSettings.txtEditWarning": "Die Bearbeitung entfernt Signaturen aus diesem Dokument.<br><PERSON><PERSON>cht<PERSON> Sie trotzdem fortsetzen?", "DE.Views.SignatureSettings.txtRemoveWarning": "Möchten Sie diese Signatur wirklich entfernen?<br>Dies kann nicht rückgängig gemacht werden.", "DE.Views.SignatureSettings.txtRequestedSignatures": "Dieses Dokument muss signiert werden.", "DE.Views.SignatureSettings.txtSigned": "Gültige Signaturen wurden dem Dokument hinzugefügt. Das Dokument ist vor der Bearbeitung geschützt.", "DE.Views.SignatureSettings.txtSignedInvalid": "Einige der digitalen Signaturen im Dokument sind ungültig oder konnten nicht verifiziert werden. Das Dokument ist vor der Bearbeitung geschützt.", "DE.Views.Statusbar.goToPageText": "Auf die Seite übergehen", "DE.Views.Statusbar.pageIndexText": "Seite {0} von {1}", "DE.Views.Statusbar.tipFitPage": "Seite anpassen", "DE.Views.Statusbar.tipFitWidth": "<PERSON><PERSON><PERSON> an<PERSON>en", "DE.Views.Statusbar.tipHandTool": "Hand-Werkzeug", "DE.Views.Statusbar.tipSelectTool": "Auswählungstool", "DE.Views.Statusbar.tipSetLang": "Textsprache wählen", "DE.Views.Statusbar.tipZoomFactor": "Zoommodus", "DE.Views.Statusbar.tipZoomIn": "Vergrößern", "DE.Views.Statusbar.tipZoomOut": "Verkleinern", "DE.Views.Statusbar.txtPageNumInvalid": "Ungültige Seitennummer", "DE.Views.Statusbar.txtPages": "Seiten", "DE.Views.Statusbar.txtParagraphs": "Absätze", "DE.Views.Statusbar.txtSpaces": "<PERSON><PERSON><PERSON> mit Leerzeichen", "DE.Views.Statusbar.txtSymbols": "<PERSON><PERSON><PERSON>", "DE.Views.Statusbar.txtWordCount": "<PERSON><PERSON><PERSON>", "DE.Views.Statusbar.txtWords": "<PERSON><PERSON><PERSON>", "DE.Views.StyleTitleDialog.textHeader": "Neuer Stil erstellen", "DE.Views.StyleTitleDialog.textNextStyle": "Nächste Absatz-Formatvorlage", "DE.Views.StyleTitleDialog.textTitle": "Titel", "DE.Views.StyleTitleDialog.txtEmpty": "<PERSON><PERSON> ist erford<PERSON>lich", "DE.Views.StyleTitleDialog.txtNotEmpty": "<PERSON> Feld darf nicht leer sein", "DE.Views.StyleTitleDialog.txtSameAs": "Gleich wie der neu erstellte Stil", "DE.Views.TableFormulaDialog.textBookmark": "Lesezeichen einfügen", "DE.Views.TableFormulaDialog.textFormat": "Zahlenformat", "DE.Views.TableFormulaDialog.textFormula": "Formula", "DE.Views.TableFormulaDialog.textInsertFunction": "Funktion einfügen", "DE.Views.TableFormulaDialog.textTitle": "Formel-Einstellungen", "DE.Views.TableOfContentsSettings.strAlign": "Seitenzahlen rechtsbündig", "DE.Views.TableOfContentsSettings.strFullCaption": "Bezeichnung und Nummer einschließen", "DE.Views.TableOfContentsSettings.strLinks": "Inhaltsverzeichnis als Links formatieren", "DE.Views.TableOfContentsSettings.strLinksOF": "Abbildungsverzeichnis als Links formatieren", "DE.Views.TableOfContentsSettings.strShowPages": "Seitenzahlen anzeigen", "DE.Views.TableOfContentsSettings.textBuildTable": "<PERSON>rst<PERSON>n eines Inhaltsverzeichnisses von", "DE.Views.TableOfContentsSettings.textBuildTableOF": "<PERSON><PERSON>elle Abbildungsverzeichnis aus", "DE.Views.TableOfContentsSettings.textEquation": "Gleichung", "DE.Views.TableOfContentsSettings.textFigure": "Abb<PERSON><PERSON><PERSON>", "DE.Views.TableOfContentsSettings.textLeader": "Füllzeichen", "DE.Views.TableOfContentsSettings.textLevel": "<PERSON><PERSON><PERSON>", "DE.Views.TableOfContentsSettings.textLevels": "<PERSON><PERSON><PERSON>", "DE.Views.TableOfContentsSettings.textNone": "<PERSON><PERSON>", "DE.Views.TableOfContentsSettings.textRadioCaption": "Beschriftung", "DE.Views.TableOfContentsSettings.textRadioLevels": "Gliederungsebenen", "DE.Views.TableOfContentsSettings.textRadioStyle": "Stil", "DE.Views.TableOfContentsSettings.textRadioStyles": "Ausgewählte Formatvorlagen", "DE.Views.TableOfContentsSettings.textStyle": "Formatvorlage", "DE.Views.TableOfContentsSettings.textStyles": "Formatvorlagen", "DE.Views.TableOfContentsSettings.textTable": "<PERSON><PERSON><PERSON>", "DE.Views.TableOfContentsSettings.textTitle": "Inhaltsverzeichnis", "DE.Views.TableOfContentsSettings.textTitleTOF": "Abbildungsverzeichnis", "DE.Views.TableOfContentsSettings.txtCentered": "<PERSON><PERSON><PERSON>", "DE.Views.TableOfContentsSettings.txtClassic": "Klassisch", "DE.Views.TableOfContentsSettings.txtCurrent": "Aktuell", "DE.Views.TableOfContentsSettings.txtDistinctive": "Elegant", "DE.Views.TableOfContentsSettings.txtFormal": "<PERSON>ell", "DE.Views.TableOfContentsSettings.txtModern": "Modern", "DE.Views.TableOfContentsSettings.txtOnline": "Online", "DE.Views.TableOfContentsSettings.txtSimple": "<PERSON><PERSON><PERSON>", "DE.Views.TableOfContentsSettings.txtStandard": "Standard", "DE.Views.TableSettings.deleteColumnText": "Spalte löschen", "DE.Views.TableSettings.deleteRowText": "Zeile löschen", "DE.Views.TableSettings.deleteTableText": "<PERSON><PERSON><PERSON> l<PERSON>", "DE.Views.TableSettings.insertColumnLeftText": "Spalte links einfügen", "DE.Views.TableSettings.insertColumnRightText": "Spalte rechts einfügen", "DE.Views.TableSettings.insertRowAboveText": "Zeile oberhalb einfügen", "DE.Views.TableSettings.insertRowBelowText": "<PERSON><PERSON><PERSON> unterhalb einfügen", "DE.Views.TableSettings.mergeCellsText": "<PERSON><PERSON><PERSON> verbinden", "DE.Views.TableSettings.selectCellText": "<PERSON><PERSON> au<PERSON>wählen", "DE.Views.TableSettings.selectColumnText": "Spalte auswählen", "DE.Views.TableSettings.selectRowText": "Zeile auswählen", "DE.Views.TableSettings.selectTableText": "<PERSON><PERSON><PERSON> auswählen", "DE.Views.TableSettings.splitCellsText": "<PERSON><PERSON> te<PERSON>...", "DE.Views.TableSettings.splitCellTitleText": "<PERSON><PERSON> te<PERSON>n", "DE.Views.TableSettings.strRepeatRow": "Gleiche Kopfzeile auf jeder Seite wiederholen", "DE.Views.TableSettings.textAddFormula": "Formel hinzufügen", "DE.Views.TableSettings.textAdvanced": "Erweiterte Einstellungen anzeigen", "DE.Views.TableSettings.textBackColor": "Hintergrundfarbe", "DE.Views.TableSettings.textBanded": "Gestreift", "DE.Views.TableSettings.textBorderColor": "Farbe", "DE.Views.TableSettings.textBorders": "<PERSON>il des Rahmens", "DE.Views.TableSettings.textCellSize": "Zeilen- und Spaltengröße", "DE.Views.TableSettings.textColumns": "Spalten", "DE.Views.TableSettings.textConvert": "Tabelle in Text umwandeln", "DE.Views.TableSettings.textDistributeCols": "Spalten verteilen", "DE.Views.TableSettings.textDistributeRows": "<PERSON><PERSON><PERSON> verteilen", "DE.Views.TableSettings.textEdit": "Zeilen & Spalten", "DE.Views.TableSettings.textEmptyTemplate": "<PERSON><PERSON>", "DE.Views.TableSettings.textFirst": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.textHeader": "Kopfzeile", "DE.Views.TableSettings.textHeight": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.textLast": "Letzte", "DE.Views.TableSettings.textRows": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.textSelectBorders": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> Rahm<PERSON>, auf die ein anderer Stil angewandt wird", "DE.Views.TableSettings.textTemplate": "Vorlage auswählen", "DE.Views.TableSettings.textTotal": "Insgesamt", "DE.Views.TableSettings.textWidth": "Breite", "DE.Views.TableSettings.tipAll": "Äußere Rahmenlinie und alle inneren Linien festlegen", "DE.Views.TableSettings.tipBottom": "Nur äußere untere Rahmenlinie festlegen", "DE.Views.TableSettings.tipInner": "Nur innere Linien festlegen", "DE.Views.TableSettings.tipInnerHor": "Nur innere horizontale Linien festlegen", "DE.Views.TableSettings.tipInnerVert": "Nur vertikale innere Linien festlegen", "DE.Views.TableSettings.tipLeft": "Nur äußere linke Rahmenlinie festlegen", "DE.Views.TableSettings.tipNone": "<PERSON><PERSON> festlegen", "DE.Views.TableSettings.tipOuter": "Nur äußere Rahmenlinie festlegen", "DE.Views.TableSettings.tipRight": "Nur äußere rechte Rahmenlinie festlegen", "DE.Views.TableSettings.tipTop": "Nur äußere obere Rahmenlinie festlegen", "DE.Views.TableSettings.txtGroupTable_BorderedAndLined": "Umgrenzte und linierte Tabellen", "DE.Views.TableSettings.txtGroupTable_Custom": "Einstellbar", "DE.Views.TableSettings.txtGroupTable_Grid": "Gitternetztabellen", "DE.Views.TableSettings.txtGroupTable_List": "Listentabellen", "DE.Views.TableSettings.txtGroupTable_Plain": "Einfache Tabellen", "DE.Views.TableSettings.txtNoBorders": "<PERSON><PERSON>", "DE.Views.TableSettings.txtTable_Accent": "Akzent", "DE.Views.TableSettings.txtTable_Bordered": "Umgrenzt", "DE.Views.TableSettings.txtTable_BorderedAndLined": "Umgrenzt und liniert", "DE.Views.TableSettings.txtTable_Colorful": "Farbig", "DE.Views.TableSettings.txtTable_Dark": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.txtTable_GridTable": "Gitternetztabelle", "DE.Views.TableSettings.txtTable_Light": "Hell", "DE.Views.TableSettings.txtTable_Lined": "<PERSON><PERSON>", "DE.Views.TableSettings.txtTable_ListTable": "Listentabelle", "DE.Views.TableSettings.txtTable_PlainTable": "Einfache Tabelle", "DE.Views.TableSettings.txtTable_TableGrid": "Ta<PERSON>en<PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textAlign": "Ausrichtung", "DE.Views.TableSettingsAdvanced.textAlignment": "Ausrichtung", "DE.Views.TableSettingsAdvanced.textAllowSpacing": "Abstand zwischen Zellen zulassen", "DE.Views.TableSettingsAdvanced.textAlt": "Alternativer Text", "DE.Views.TableSettingsAdvanced.textAltDescription": "Beschreibung", "DE.Views.TableSettingsAdvanced.textAltTip": "Die alternative textbasierte Darstellung der visuellen Objektinformation, die den Menschen  mit geistigen Behinderungen oder Sehbehinderungen vorgelesen wird, um besser verstehen zu können, was gena<PERSON> auf dem Bild, AutoForm, Diagramm oder der Tabelle dargestellt wurde.", "DE.Views.TableSettingsAdvanced.textAltTitle": "Titel", "DE.Views.TableSettingsAdvanced.textAnchorText": "Text", "DE.Views.TableSettingsAdvanced.textAutofit": "Größe an Inhalt automatisch anpassen", "DE.Views.TableSettingsAdvanced.textBackColor": "Zellenhintergrund", "DE.Views.TableSettingsAdvanced.textBelow": "unten", "DE.Views.TableSettingsAdvanced.textBorderColor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textBorderDesc": "<PERSON>lick<PERSON> Sie aufs Diagramm oder nutzen Sie die Buttons, um Umrandungen zu wählen und den gewählten Stil anzuwenden", "DE.Views.TableSettingsAdvanced.textBordersBackgroung": "Rahmen & Hintergrund", "DE.Views.TableSettingsAdvanced.textBorderWidth": "Rahmenstärke", "DE.Views.TableSettingsAdvanced.textBottom": "Unten", "DE.Views.TableSettingsAdvanced.textCellOptions": "Zellenoptionen", "DE.Views.TableSettingsAdvanced.textCellProps": "<PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textCellSize": "Zellengröße", "DE.Views.TableSettingsAdvanced.textCenter": "<PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textCenterTooltip": "<PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textCheckMargins": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textDefaultMargins": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textDistance": "Abstand vom Text", "DE.Views.TableSettingsAdvanced.textHorizontal": "Horizontal", "DE.Views.TableSettingsAdvanced.textIndLeft": "<PERSON><PERSON><PERSON> von links", "DE.Views.TableSettingsAdvanced.textLeft": "Links", "DE.Views.TableSettingsAdvanced.textLeftTooltip": "Links", "DE.Views.TableSettingsAdvanced.textMargin": "Rand", "DE.Views.TableSettingsAdvanced.textMargins": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textMeasure": "Maßeinheit in", "DE.Views.TableSettingsAdvanced.textMove": "Objekt mit Text verschieben", "DE.Views.TableSettingsAdvanced.textOnlyCells": "Nur für gewählte Zellen", "DE.Views.TableSettingsAdvanced.textOptions": "Optionen", "DE.Views.TableSettingsAdvanced.textOverlap": "Überlappung zulassen", "DE.Views.TableSettingsAdvanced.textPage": "Seite", "DE.Views.TableSettingsAdvanced.textPosition": "Position", "DE.Views.TableSettingsAdvanced.textPrefWidth": "Vorzugsbreite", "DE.Views.TableSettingsAdvanced.textPreview": "Vorschau", "DE.Views.TableSettingsAdvanced.textRelative": "im Bezug auf ", "DE.Views.TableSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textRightOf": "rechts von", "DE.Views.TableSettingsAdvanced.textRightTooltip": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textTable": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textTableBackColor": "Tabellenhintergrund", "DE.Views.TableSettingsAdvanced.textTablePosition": "Tabellenposition", "DE.Views.TableSettingsAdvanced.textTableSize": "Größe der Tabelle", "DE.Views.TableSettingsAdvanced.textTitle": "Tabelle - Erweiterte Einstellungen", "DE.Views.TableSettingsAdvanced.textTop": "<PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textVertical": "Vertikal", "DE.Views.TableSettingsAdvanced.textWidth": "Breite", "DE.Views.TableSettingsAdvanced.textWidthSpaces": "Breite & Abstand", "DE.Views.TableSettingsAdvanced.textWrap": "Textumbruch", "DE.Views.TableSettingsAdvanced.textWrapNoneTooltip": "Inline-Tabelle", "DE.Views.TableSettingsAdvanced.textWrapParallelTooltip": "Flow-<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textWrappingStyle": "Textumbruch", "DE.Views.TableSettingsAdvanced.textWrapText": "Zeilenumbruch", "DE.Views.TableSettingsAdvanced.tipAll": "Äußere Rahmenlinie und alle inneren Linien festlegen", "DE.Views.TableSettingsAdvanced.tipCellAll": "Rahmenlinien nur für innere Zellen festlegen", "DE.Views.TableSettingsAdvanced.tipCellInner": "Horizontale und vertikale Linien nur für innere Zellen festlegen ", "DE.Views.TableSettingsAdvanced.tipCellOuter": "Äußere Rahmenlinien nur für innere Zellen festlegen", "DE.Views.TableSettingsAdvanced.tipInner": "Nur innere Linien festlegen", "DE.Views.TableSettingsAdvanced.tipNone": "<PERSON><PERSON> festlegen", "DE.Views.TableSettingsAdvanced.tipOuter": "Nur äußere Rahmenlinie festlegen", "DE.Views.TableSettingsAdvanced.tipTableOuterCellAll": "Äußere Rahmenlinie und Rahmenlinien für alle inneren Zellen festlegen", "DE.Views.TableSettingsAdvanced.tipTableOuterCellInner": "Äußere Rahmenlinie und vertikale und horizontale Linien für innere Zellen festlegen", "DE.Views.TableSettingsAdvanced.tipTableOuterCellOuter": "Äußere Rahmenlinie der Tabelle und äußere Rahmenlinien für innere Zellen festlegen", "DE.Views.TableSettingsAdvanced.txtCm": "Zentimeter", "DE.Views.TableSettingsAdvanced.txtInch": "<PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.txtNoBorders": "<PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.txtPercent": "Prozent", "DE.Views.TableSettingsAdvanced.txtPt": "<PERSON><PERSON>", "DE.Views.TableToTextDialog.textEmpty": "<PERSON>ür das benutzerdefinierte Trennzeichen muss ein Zeichen eingegeben werden.", "DE.Views.TableToTextDialog.textNested": "Geschachtelte Tabellen konvertieren", "DE.Views.TableToTextDialog.textOther": "Sonstiges", "DE.Views.TableToTextDialog.textPara": "Absatzmarken", "DE.Views.TableToTextDialog.textSemicolon": "Semikolons", "DE.Views.TableToTextDialog.textSeparator": "Text trennen durch:", "DE.Views.TableToTextDialog.textTab": "Tabulatoren", "DE.Views.TableToTextDialog.textTitle": "Tabelle in Text umwandeln", "DE.Views.TextArtSettings.strColor": "Farbe", "DE.Views.TextArtSettings.strFill": "Füllung", "DE.Views.TextArtSettings.strSize": "Größe", "DE.Views.TextArtSettings.strStroke": "<PERSON><PERSON>", "DE.Views.TextArtSettings.strTransparency": "Undurchsichtigkeit", "DE.Views.TextArtSettings.strType": "<PERSON><PERSON>", "DE.Views.TextArtSettings.textAngle": "<PERSON><PERSON>", "DE.Views.TextArtSettings.textBorderSizeErr": "Der eingegebene Wert ist falsch.<br>Bitte geben Sie einen Wert zwischen 0 pt und 1584 pt ein.", "DE.Views.TextArtSettings.textColor": "Farbfüllung", "DE.Views.TextArtSettings.textDirection": "<PERSON><PERSON><PERSON>", "DE.Views.TextArtSettings.textGradient": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.TextArtSettings.textGradientFill": "Füllung mit Farbverlauf", "DE.Views.TextArtSettings.textLinear": "Linear", "DE.Views.TextArtSettings.textNoFill": "<PERSON><PERSON>", "DE.Views.TextArtSettings.textPosition": "Stellung", "DE.Views.TextArtSettings.textRadial": "Radial", "DE.Views.TextArtSettings.textSelectTexture": "Auswählen", "DE.Views.TextArtSettings.textStyle": "Stil", "DE.Views.TextArtSettings.textTemplate": "Vorlage", "DE.Views.TextArtSettings.textTransform": "Transformieren", "DE.Views.TextArtSettings.tipAddGradientPoint": "Punkt des Farbverlaufs einfügen", "DE.Views.TextArtSettings.tipRemoveGradientPoint": "Punkt des Farbverlaufs entfernen", "DE.Views.TextArtSettings.txtNoBorders": "<PERSON><PERSON>", "DE.Views.TextToTableDialog.textAutofit": "Einstellung für Autoanpassen", "DE.Views.TextToTableDialog.textColumns": "Spalten", "DE.Views.TextToTableDialog.textContents": "An Inhalt autoanpassen", "DE.Views.TextToTableDialog.textEmpty": "<PERSON>ür das benutzerdefinierte Trennzeichen muss ein Zeichen eingegeben werden.", "DE.Views.TextToTableDialog.textFixed": "Feste Spaltenbreite", "DE.Views.TextToTableDialog.textOther": "Sonstiges", "DE.Views.TextToTableDialog.textPara": "Absätze", "DE.Views.TextToTableDialog.textRows": "<PERSON><PERSON><PERSON>", "DE.Views.TextToTableDialog.textSemicolon": "Semikolons", "DE.Views.TextToTableDialog.textSeparator": "Text trennen bei:", "DE.Views.TextToTableDialog.textTab": "Tabulatoren", "DE.Views.TextToTableDialog.textTableSize": "Größe der Tabelle", "DE.Views.TextToTableDialog.textTitle": "Text in Tabelle umwandeln", "DE.Views.TextToTableDialog.textWindow": "An Fenster autoanpassen", "DE.Views.TextToTableDialog.txtAutoText": "Automatisch", "DE.Views.Toolbar.capBtnAddComment": "Kommentar Hinzufügen", "DE.Views.Toolbar.capBtnBlankPage": "<PERSON>re Seite", "DE.Views.Toolbar.capBtnColumns": "Spalten", "DE.Views.Toolbar.capBtnComment": "Kommentar", "DE.Views.Toolbar.capBtnDateTime": "Datum & Uhrzeit", "DE.Views.Toolbar.capBtnInsChart": "Diagramm", "DE.Views.Toolbar.capBtnInsControls": "Inhaltssteuerelemente", "DE.Views.Toolbar.capBtnInsDropcap": "Initialbuchstaben ", "DE.Views.Toolbar.capBtnInsEquation": "Gleichung", "DE.Views.Toolbar.capBtnInsHeader": "Kopf- und Fußzeile", "DE.Views.Toolbar.capBtnInsImage": "Bild", "DE.Views.Toolbar.capBtnInsPagebreak": "Umbrüche", "DE.Views.Toolbar.capBtnInsShape": "Form", "DE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "DE.Views.Toolbar.capBtnInsSymbol": "Symbol", "DE.Views.Toolbar.capBtnInsTable": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.capBtnInsTextart": "Text Art", "DE.Views.Toolbar.capBtnInsTextbox": "<PERSON><PERSON>", "DE.Views.Toolbar.capBtnLineNumbers": "Zeilennummern", "DE.Views.Toolbar.capBtnMargins": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.capBtnPageOrient": "Orientierung", "DE.Views.Toolbar.capBtnPageSize": "Größe", "DE.Views.Toolbar.capBtnWatermark": "Wasserzeichen", "DE.Views.Toolbar.capImgAlign": "Ausrichten", "DE.Views.Toolbar.capImgBackward": "Eine Ebene nach hinten", "DE.Views.Toolbar.capImgForward": "Eine Ebene nach vorne", "DE.Views.Toolbar.capImgGroup": "Gruppieren", "DE.Views.Toolbar.capImgWrapping": "Umbruch", "DE.Views.Toolbar.mniCapitalizeWords": "<PERSON><PERSON> im jedem Wort großschreiben", "DE.Views.Toolbar.mniCustomTable": "Benutzerdefinierte Tabelle einfügen", "DE.Views.Toolbar.mniDrawTable": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.mniEditControls": "Steuerelementeinstellungen", "DE.Views.Toolbar.mniEditDropCap": "Initialeinstellungen", "DE.Views.Toolbar.mniEditFooter": "Fußzeile bearbeiten", "DE.Views.Toolbar.mniEditHeader": "Kopfzeile bearbeiten", "DE.Views.Toolbar.mniEraseTable": "<PERSON><PERSON><PERSON> l<PERSON>", "DE.Views.Toolbar.mniFromFile": "<PERSON><PERSON> Datei", "DE.Views.Toolbar.mniFromStorage": "Aus dem Speicher", "DE.Views.Toolbar.mniFromUrl": "Aus einer URL", "DE.Views.Toolbar.mniHiddenBorders": "Ausgeblendete Tabellenrahmen", "DE.Views.Toolbar.mniHiddenChars": "Formatierungszeichen", "DE.Views.Toolbar.mniHighlightControls": "Einstellungen für Hervorhebungen", "DE.Views.Toolbar.mniImageFromFile": "Bild aus Datei", "DE.Views.Toolbar.mniImageFromStorage": "Bild aus dem Speicher", "DE.Views.Toolbar.mniImageFromUrl": "Bild aus URL", "DE.Views.Toolbar.mniInsertSSE": "<PERSON>bell<PERSON> e<PERSON>fügen", "DE.Views.Toolbar.mniLowerCase": "Kleinbuchstaben", "DE.Views.Toolbar.mniRemoveFooter": "Fußzeile entfernen", "DE.Views.Toolbar.mniRemoveHeader": "Kopfzeile entfernen", "DE.Views.Toolbar.mniSentenceCase": "<PERSON><PERSON> im Satz großschreiben.", "DE.Views.Toolbar.mniTextToTable": "Text in Tabelle umwandeln", "DE.Views.Toolbar.mniToggleCase": "gROSS-/kLEINSCHREIBUNG", "DE.Views.Toolbar.mniUpperCase": "GROSSBUCHSTABEN", "DE.Views.Toolbar.strMenuNoFill": "<PERSON><PERSON>", "DE.Views.Toolbar.textAutoColor": "Automatisch", "DE.Views.Toolbar.textBold": "<PERSON><PERSON>", "DE.Views.Toolbar.textBottom": "Unten: ", "DE.Views.Toolbar.textChangeLevel": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textCheckboxControl": "Kontrollkästchen", "DE.Views.Toolbar.textColumnsCustom": "Benutzerdefinierte Spalten", "DE.Views.Toolbar.textColumnsLeft": "Links", "DE.Views.Toolbar.textColumnsOne": "Ein", "DE.Views.Toolbar.textColumnsRight": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textColumnsThree": "<PERSON><PERSON>", "DE.Views.Toolbar.textColumnsTwo": "Zwei", "DE.Views.Toolbar.textComboboxControl": "<PERSON><PERSON><PERSON><PERSON>feld", "DE.Views.Toolbar.textContinuous": "Ununterbrochen", "DE.Views.Toolbar.textContPage": "Fortlaufende Seite", "DE.Views.Toolbar.textCustomLineNumbers": "Zeilennummerierungsoptionen", "DE.Views.Toolbar.textDateControl": "Datum", "DE.Views.Toolbar.textDropdownControl": "Dropdownliste", "DE.Views.Toolbar.textEditWatermark": "Benutzerdefiniertes Wasserzeichen", "DE.Views.Toolbar.textEvenPage": "Gerade Seite", "DE.Views.Toolbar.textInMargin": "<PERSON><PERSON>", "DE.Views.Toolbar.textInsColumnBreak": "Spaltenumbruch einfügen", "DE.Views.Toolbar.textInsertPageCount": "Anzahl der Seiten einfügen", "DE.Views.Toolbar.textInsertPageNumber": "Seitenzahl einfügen", "DE.Views.Toolbar.textInsPageBreak": "Seitenumbruch einfügen", "DE.Views.Toolbar.textInsSectionBreak": "Abschnittsumbruch einfügen", "DE.Views.Toolbar.textInText": "Im Text", "DE.Views.Toolbar.textItalic": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textLandscape": "Querformat", "DE.Views.Toolbar.textLeft": "Links: ", "DE.Views.Toolbar.textListSettings": "Listeneinstellungen", "DE.Views.Toolbar.textMarginsLast": " Benutzerdefiniert als letzte", "DE.Views.Toolbar.textMarginsModerate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textMarginsNarrow": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textMarginsNormal": "Normal", "DE.Views.Toolbar.textMarginsUsNormal": "Normal (US)", "DE.Views.Toolbar.textMarginsWide": "Breit", "DE.Views.Toolbar.textNewColor": "Benutzerdefinierte Farbe", "DE.Views.Toolbar.textNextPage": "Nächste Seite", "DE.Views.Toolbar.textNoHighlight": "<PERSON><PERSON>", "DE.Views.Toolbar.textNone": "<PERSON><PERSON>", "DE.Views.Toolbar.textOddPage": "Ungerade Seite", "DE.Views.Toolbar.textPageMarginsCustom": "Benutzerdefinierte Seitenränder ", "DE.Views.Toolbar.textPageSizeCustom": "Benutzerdefiniertes Seitenformat", "DE.Views.Toolbar.textPictureControl": "Bild", "DE.Views.Toolbar.textPlainControl": "Einfacher Text", "DE.Views.Toolbar.textPortrait": "Hochformat", "DE.Views.Toolbar.textRemoveControl": "Inhaltssteuerelement entfernen", "DE.Views.Toolbar.textRemWatermark": "Wasserzeichen entfernen", "DE.Views.Toolbar.textRestartEachPage": "Jede Seite neu beginnen", "DE.Views.Toolbar.textRestartEachSection": "<PERSON>en Abschnitt neu beginnen", "DE.Views.Toolbar.textRichControl": "Rich-Text", "DE.Views.Toolbar.textRight": "Rechts: ", "DE.Views.Toolbar.textStrikeout": "Durchgestrichen", "DE.Views.Toolbar.textStyleMenuDelete": "<PERSON>il löschen", "DE.Views.Toolbar.textStyleMenuDeleteAll": "Alle benutzerdefinierte Stile löschen ", "DE.Views.Toolbar.textStyleMenuNew": "Neue Formatvorlagen auf der Basis einer Auswahl", "DE.Views.Toolbar.textStyleMenuRestore": "Auf Standard setzen", "DE.Views.Toolbar.textStyleMenuRestoreAll": "Alle Standardformatvorlagen zurücksetzen", "DE.Views.Toolbar.textStyleMenuUpdate": "Aus der Auswahl neu aktualisieren", "DE.Views.Toolbar.textSubscript": "Tiefgestellt", "DE.Views.Toolbar.textSuperscript": "Hochgestellt", "DE.Views.Toolbar.textSuppressForCurrentParagraph": "Im aktuellen Absatz verbieten", "DE.Views.Toolbar.textTabCollaboration": "Zusammenarbeit", "DE.Views.Toolbar.textTabFile": "<PERSON><PERSON>", "DE.Views.Toolbar.textTabHome": "Startseite", "DE.Views.Toolbar.textTabInsert": "Einfügen", "DE.Views.Toolbar.textTabLayout": "Layout", "DE.Views.Toolbar.textTabLinks": "Verweise", "DE.Views.Toolbar.textTabProtect": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textTabReview": "Review", "DE.Views.Toolbar.textTabView": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textTitleError": "<PERSON><PERSON>", "DE.Views.Toolbar.textToCurrent": "An aktueller Position", "DE.Views.Toolbar.textTop": "Oben: ", "DE.Views.Toolbar.textUnderline": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipAlignCenter": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipAlignJust": "Blocksatz", "DE.Views.Toolbar.tipAlignLeft": "Linksbündig ausrichten", "DE.Views.Toolbar.tipAlignRight": "Rechtsbündig ausrichten", "DE.Views.Toolbar.tipBack": "Zurück", "DE.Views.Toolbar.tipBlankPage": "<PERSON>re Seite e<PERSON>legen", "DE.Views.Toolbar.tipChangeCase": "Groß-/Kleinschreibung ändern", "DE.Views.Toolbar.tipChangeChart": "Diagrammtyp ändern", "DE.Views.Toolbar.tipClearStyle": "Formatierung löschen", "DE.Views.Toolbar.tipColorSchemas": "Farbschema ändern", "DE.Views.Toolbar.tipColumns": "Spalten einfügen", "DE.Views.Toolbar.tipControls": "Inhaltssteuerelemente einfügen", "DE.Views.Toolbar.tipCopy": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipCopyStyle": "Format übertragen", "DE.Views.Toolbar.tipCut": "Ausschneiden", "DE.Views.Toolbar.tipDateTime": "Die aktuellen Datum und Uhrzeit einfügen", "DE.Views.Toolbar.tipDecFont": "Schriftart verkleinern", "DE.Views.Toolbar.tipDecPrLeft": "Einzug verkleinern", "DE.Views.Toolbar.tipDropCap": "Initiale einfügen", "DE.Views.Toolbar.tipEditHeader": "Kopf- oder Fußzeile bearbeiten", "DE.Views.Toolbar.tipFontColor": "Schriftfarbe", "DE.Views.Toolbar.tipFontName": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipFontSize": "Schriftgrad", "DE.Views.Toolbar.tipHighlightColor": "Texthervorhebungsfarbe", "DE.Views.Toolbar.tipImgAlign": "Objekte ausrichten", "DE.Views.Toolbar.tipImgGroup": "Objekte gruppieren", "DE.Views.Toolbar.tipImgWrapping": "Textumbruch", "DE.Views.Toolbar.tipIncFont": "Schriftart vergrößern\n ", "DE.Views.Toolbar.tipIncPrLeft": "Einzug vergrößern", "DE.Views.Toolbar.tipInsertChart": "Diagramm einfügen", "DE.Views.Toolbar.tipInsertEquation": "Formel einfügen", "DE.Views.Toolbar.tipInsertHorizontalText": "Horizontales Textfeld einfügen", "DE.Views.Toolbar.tipInsertImage": "Bild einfügen", "DE.Views.Toolbar.tipInsertNum": "Seitenzahl einfügen", "DE.Views.Toolbar.tipInsertShape": "AutoForm einfügen", "DE.Views.Toolbar.tipInsertSmartArt": "SmartArt einfügen", "DE.Views.Toolbar.tipInsertSymbol": "Symbol einfügen", "DE.Views.Toolbar.tipInsertTable": "<PERSON>bell<PERSON> e<PERSON>fügen", "DE.Views.Toolbar.tipInsertText": "Textfeld einfügen", "DE.Views.Toolbar.tipInsertTextArt": "TextArt einfügen", "DE.Views.Toolbar.tipInsertVerticalText": "Vertikales Textfeld einfügen", "DE.Views.Toolbar.tipLineNumbers": "Zeilennummern anzeigen", "DE.Views.Toolbar.tipLineSpace": "Zeilenabstand", "DE.Views.Toolbar.tipMailRecepients": "Serienbrief", "DE.Views.Toolbar.tipMarkers": "Aufzählung", "DE.Views.Toolbar.tipMarkersArrow": "Pfeilförmige Aufzählungszeichen", "DE.Views.Toolbar.tipMarkersCheckmark": "Häkchenaufzählungszeichen", "DE.Views.Toolbar.tipMarkersDash": "Aufzählungszeichen", "DE.Views.Toolbar.tipMarkersFRhombus": "Ausgefüllte karoförmige Aufzählungszeichen", "DE.Views.Toolbar.tipMarkersFRound": "Ausgefüllte runde Aufzählungszeichen", "DE.Views.Toolbar.tipMarkersFSquare": "Ausgefüllte quadratische Aufzählungszeichen", "DE.Views.Toolbar.tipMarkersHRound": "<PERSON><PERSON> runde Aufzählungszeichen", "DE.Views.Toolbar.tipMarkersStar": "Sternförmige Aufzählungszeichen", "DE.Views.Toolbar.tipMultiLevelArticl": "Mehrstufig nummerierte Artikel", "DE.Views.Toolbar.tipMultiLevelChapter": "Mehrstufig nummerierte Kapitel", "DE.Views.Toolbar.tipMultiLevelHeadings": "Mehrstufig nummerierte Überschriften", "DE.Views.Toolbar.tipMultiLevelHeadVarious": "Unterschiedliche mehrstufig nummerierte Überschriften", "DE.Views.Toolbar.tipMultiLevelNumbered": "Nummerierte Liste mit mehreren Ebenen", "DE.Views.Toolbar.tipMultilevels": "Liste mit mehreren Ebenen", "DE.Views.Toolbar.tipMultiLevelSymbols": "Aufzählungsliste mit mehreren Ebenen", "DE.Views.Toolbar.tipMultiLevelVarious": "Kombinierte Liste mit mehreren Ebenen", "DE.Views.Toolbar.tipNumbers": "Nummerierung", "DE.Views.Toolbar.tipPageBreak": "Seiten- oder Abschnittsumbruch einfügen", "DE.Views.Toolbar.tipPageMargins": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipPageOrient": "Seitenausrichtung", "DE.Views.Toolbar.tipPageSize": "Seitenformat", "DE.Views.Toolbar.tipParagraphStyle": "Absatzformat", "DE.Views.Toolbar.tipPaste": "Einfügen", "DE.Views.Toolbar.tipPrColor": "Absatzhintergrundfarbe", "DE.Views.Toolbar.tipPrint": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipRedo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipSave": "Speichern", "DE.Views.Toolbar.tipSaveCoauth": "Speichern Sie die Änderungen, damit die anderen Benutzer sie sehen können.", "DE.Views.Toolbar.tipSelectAll": "Alles auswählen", "DE.Views.Toolbar.tipSendBackward": "Eine Ebene nach hinten", "DE.Views.Toolbar.tipSendForward": "Eine Ebene nach vorne", "DE.Views.Toolbar.tipShowHiddenChars": "Formatierungszeichen", "DE.Views.Toolbar.tipSynchronize": "Das Dokument wurde von einem anderen Benutzer geändert. Bitte speichern Sie Ihre Änderungen und aktualisieren Sie Ihre Seite.", "DE.Views.Toolbar.tipUndo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipWatermark": "Wasserzeichen bearbeiten", "DE.Views.Toolbar.txtDistribHor": "Horizontal verteilen", "DE.Views.Toolbar.txtDistribVert": "Vertikal verteilen", "DE.Views.Toolbar.txtMarginAlign": "<PERSON><PERSON> aus<PERSON>ten", "DE.Views.Toolbar.txtObjectsAlign": "Ausgewählte Objekte ausrichten", "DE.Views.Toolbar.txtPageAlign": "Am Seitenende ausrichten", "DE.Views.Toolbar.txtScheme1": "Larissa", "DE.Views.Toolbar.txtScheme10": "Median", "DE.Views.Toolbar.txtScheme11": "Metro", "DE.Views.Toolbar.txtScheme12": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.txtScheme13": "Lysithea", "DE.Views.Toolbar.txtScheme14": "Nere<PERSON>", "DE.Views.Toolbar.txtScheme15": "Okeanos", "DE.Views.Toolbar.txtScheme16": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.txtScheme17": "Nyad", "DE.Views.Toolbar.txtScheme18": "Ha<PERSON>era", "DE.Views.Toolbar.txtScheme19": "Trek", "DE.Views.Toolbar.txtScheme2": "Graustufe", "DE.Views.Toolbar.txtScheme20": "<PERSON><PERSON>", "DE.Views.Toolbar.txtScheme21": "Telesto", "DE.Views.Toolbar.txtScheme22": "Neues Office", "DE.Views.Toolbar.txtScheme3": "Apex", "DE.Views.Toolbar.txtScheme4": "Aspekt ", "DE.Views.Toolbar.txtScheme5": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.txtScheme6": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.txtScheme7": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.txtScheme8": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.txtScheme9": "Phoebe", "DE.Views.ViewTab.textAlwaysShowToolbar": "Symbolleiste immer anzeigen", "DE.Views.ViewTab.textDarkDocument": "Dunkles Dokument", "DE.Views.ViewTab.textFitToPage": "Seite anpassen", "DE.Views.ViewTab.textFitToWidth": "An Breite anpassen", "DE.Views.ViewTab.textInterfaceTheme": "Thema der Benutzeroberfläche", "DE.Views.ViewTab.textLeftMenu": "<PERSON><PERSON>", "DE.Views.ViewTab.textNavigation": "Navigation", "DE.Views.ViewTab.textOutline": "Überschriften", "DE.Views.ViewTab.textRightMenu": "Rechtes Bedienungsfeld ", "DE.Views.ViewTab.textRulers": "Lineale", "DE.Views.ViewTab.textStatusBar": "Statusleiste", "DE.Views.ViewTab.textZoom": "Zoom", "DE.Views.ViewTab.tipDarkDocument": "Dunkles Dokument", "DE.Views.ViewTab.tipFitToPage": "Seite anpassen", "DE.Views.ViewTab.tipFitToWidth": "An Breite anpassen", "DE.Views.ViewTab.tipHeadings": "Überschriften", "DE.Views.ViewTab.tipInterfaceTheme": "Thema der Benutzeroberfläche", "DE.Views.WatermarkSettingsDialog.textAuto": "Automatisch", "DE.Views.WatermarkSettingsDialog.textBold": "<PERSON><PERSON>", "DE.Views.WatermarkSettingsDialog.textColor": "Textfarbe", "DE.Views.WatermarkSettingsDialog.textDiagonal": "Diagonal", "DE.Views.WatermarkSettingsDialog.textFont": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.WatermarkSettingsDialog.textFromFile": "<PERSON><PERSON> Datei", "DE.Views.WatermarkSettingsDialog.textFromStorage": "Aus dem Speicher", "DE.Views.WatermarkSettingsDialog.textFromUrl": "Aus URL", "DE.Views.WatermarkSettingsDialog.textHor": "Horizontal", "DE.Views.WatermarkSettingsDialog.textImageW": "Bild-Wasserzeichen", "DE.Views.WatermarkSettingsDialog.textItalic": "<PERSON><PERSON><PERSON>", "DE.Views.WatermarkSettingsDialog.textLanguage": "<PERSON><PERSON><PERSON>", "DE.Views.WatermarkSettingsDialog.textLayout": "Layout", "DE.Views.WatermarkSettingsDialog.textNone": "<PERSON><PERSON>", "DE.Views.WatermarkSettingsDialog.textScale": "Maßstab", "DE.Views.WatermarkSettingsDialog.textSelect": "Bild auswählen", "DE.Views.WatermarkSettingsDialog.textStrikeout": "Durchgestrichen", "DE.Views.WatermarkSettingsDialog.textText": "Text", "DE.Views.WatermarkSettingsDialog.textTextW": "Text-Wasserzeichen", "DE.Views.WatermarkSettingsDialog.textTitle": "Wasserzeichen-Einstellungen", "DE.Views.WatermarkSettingsDialog.textTransparency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.WatermarkSettingsDialog.textUnderline": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.WatermarkSettingsDialog.tipFontName": "Schriftartname", "DE.Views.WatermarkSettingsDialog.tipFontSize": "Schriftgrad"}