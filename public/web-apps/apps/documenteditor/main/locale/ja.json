{"Common.Controllers.Chat.notcriticalErrorTitle": "警告", "Common.Controllers.Chat.textEnterMessage": "ここにメッセージを挿入してください。", "Common.Controllers.ExternalDiagramEditor.textAnonymous": "匿名者", "Common.Controllers.ExternalDiagramEditor.textClose": "閉じる", "Common.Controllers.ExternalDiagramEditor.warningText": "他のユーザーが編集しているのためオブジェクトが無効になります。", "Common.Controllers.ExternalDiagramEditor.warningTitle": "警告", "Common.Controllers.ExternalMergeEditor.textAnonymous": "匿名者", "Common.Controllers.ExternalMergeEditor.textClose": "閉じる", "Common.Controllers.ExternalMergeEditor.warningText": "他のユーザーが編集しているのためオブジェクトが無効になります。", "Common.Controllers.ExternalMergeEditor.warningTitle": "警告", "Common.Controllers.ExternalOleEditor.textAnonymous": "匿名", "Common.Controllers.ExternalOleEditor.textClose": "閉じる", "Common.Controllers.ExternalOleEditor.warningText": "他のユーザーが編集しているのためオブジェクトが無効になります。", "Common.Controllers.ExternalOleEditor.warningTitle": "警告", "Common.Controllers.History.notcriticalErrorTitle": "警告", "Common.Controllers.ReviewChanges.textAcceptBeforeCompare": "文書を比較するために、文書内のすべての変更履歴が承認されたと見なされます。続行しますか？", "Common.Controllers.ReviewChanges.textAtLeast": "最小", "Common.Controllers.ReviewChanges.textAuto": "自動", "Common.Controllers.ReviewChanges.textBaseline": "ベースライン", "Common.Controllers.ReviewChanges.textBold": "太字", "Common.Controllers.ReviewChanges.textBreakBefore": "前に改ページ", "Common.Controllers.ReviewChanges.textCaps": "全ての英大文字", "Common.Controllers.ReviewChanges.textCenter": "中央揃え", "Common.Controllers.ReviewChanges.textChar": "文字レベル", "Common.Controllers.ReviewChanges.textChart": "チャート", "Common.Controllers.ReviewChanges.textColor": "フォントの色", "Common.Controllers.ReviewChanges.textContextual": "同じスタイルの場合は、段落間に間隔を追加しません。", "Common.Controllers.ReviewChanges.textDeleted": "<b>削除済み:</b>", "Common.Controllers.ReviewChanges.textDStrikeout": "二重取り消し線", "Common.Controllers.ReviewChanges.textEquation": "方程式\t", "Common.Controllers.ReviewChanges.textExact": "固定値", "Common.Controllers.ReviewChanges.textFirstLine": "最初の行", "Common.Controllers.ReviewChanges.textFontSize": "フォントのサイズ", "Common.Controllers.ReviewChanges.textFormatted": "書式設定済み", "Common.Controllers.ReviewChanges.textHighlight": "ハイライトの色", "Common.Controllers.ReviewChanges.textImage": "画像", "Common.Controllers.ReviewChanges.textIndentLeft": "左インデント", "Common.Controllers.ReviewChanges.textIndentRight": "右インデント", "Common.Controllers.ReviewChanges.textInserted": "<b>挿入済み:</b>", "Common.Controllers.ReviewChanges.textItalic": "イタリック", "Common.Controllers.ReviewChanges.textJustify": "両端揃え", "Common.Controllers.ReviewChanges.textKeepLines": "段落を分割しない", "Common.Controllers.ReviewChanges.textKeepNext": "次の段落と分離しない", "Common.Controllers.ReviewChanges.textLeft": "左揃え", "Common.Controllers.ReviewChanges.textLineSpacing": "行間:", "Common.Controllers.ReviewChanges.textMultiple": "倍数", "Common.Controllers.ReviewChanges.textNoBreakBefore": "前にページ区切りなし", "Common.Controllers.ReviewChanges.textNoContextual": "同じスタイルの段落の間に間隔を追加する", "Common.Controllers.ReviewChanges.textNoKeepLines": "段落を分割する", "Common.Controllers.ReviewChanges.textNoKeepNext": "次の段落と分離する", "Common.Controllers.ReviewChanges.textNot": "ではない", "Common.Controllers.ReviewChanges.textNoWidow": "ウィンドウ制御なし", "Common.Controllers.ReviewChanges.textNum": "番号付けの変更", "Common.Controllers.ReviewChanges.textOff": "{0} は、変更履歴を使用しなくなりました。", "Common.Controllers.ReviewChanges.textOffGlobal": "{0} は全員に変更履歴を無効にしました", "Common.Controllers.ReviewChanges.textOn": "{0} は変更履歴を現在使用しています。", "Common.Controllers.ReviewChanges.textOnGlobal": "{0} は全員に変更履歴を有効にしました。", "Common.Controllers.ReviewChanges.textParaDeleted": "<b>段落が削除されました</b>", "Common.Controllers.ReviewChanges.textParaFormatted": "段落の書式変更済み", "Common.Controllers.ReviewChanges.textParaInserted": "<b>段落が挿入されました</b>", "Common.Controllers.ReviewChanges.textParaMoveFromDown": "<b>下に移動済み：</b>", "Common.Controllers.ReviewChanges.textParaMoveFromUp": "<b>上に移動済み：</b>", "Common.Controllers.ReviewChanges.textParaMoveTo": "<b>移動済み：</b>", "Common.Controllers.ReviewChanges.textPosition": "位置", "Common.Controllers.ReviewChanges.textRight": "右揃え", "Common.Controllers.ReviewChanges.textShape": "図形", "Common.Controllers.ReviewChanges.textShd": "背景色", "Common.Controllers.ReviewChanges.textShow": "での変更点を表示", "Common.Controllers.ReviewChanges.textSmallCaps": "小型英大文字", "Common.Controllers.ReviewChanges.textSpacing": "間隔", "Common.Controllers.ReviewChanges.textSpacingAfter": "の後の行間", "Common.Controllers.ReviewChanges.textSpacingBefore": "の前の行間", "Common.Controllers.ReviewChanges.textStrikeout": "取り消し線", "Common.Controllers.ReviewChanges.textSubScript": "下付き文字", "Common.Controllers.ReviewChanges.textSuperScript": "上付き文字", "Common.Controllers.ReviewChanges.textTableChanged": "<b>テーブル設定が変更されました</b>", "Common.Controllers.ReviewChanges.textTableRowsAdd": "<b>テーブルに行が追加されました</b>", "Common.Controllers.ReviewChanges.textTableRowsDel": "<b>テーブルの行が削除されました</b>", "Common.Controllers.ReviewChanges.textTabs": "タブの変更", "Common.Controllers.ReviewChanges.textTitleComparison": "比較設定", "Common.Controllers.ReviewChanges.textUnderline": "アンダーライン", "Common.Controllers.ReviewChanges.textUrl": "ドキュメントのURLを貼り付け", "Common.Controllers.ReviewChanges.textWidow": "ウインドウ制御", "Common.Controllers.ReviewChanges.textWord": "単語レベル", "Common.define.chartData.textArea": "面グラフ", "Common.define.chartData.textAreaStacked": "積み上げ面", "Common.define.chartData.textAreaStackedPer": "スタック領域 100%", "Common.define.chartData.textBar": "バー", "Common.define.chartData.textBarNormal": "集合縦棒", "Common.define.chartData.textBarNormal3d": "3-D 集合縦棒", "Common.define.chartData.textBarNormal3dPerspective": "3-D 縦棒", "Common.define.chartData.textBarStacked": "積み上げ縦棒", "Common.define.chartData.textBarStacked3d": "3-D 積み上げ縦棒", "Common.define.chartData.textBarStackedPer": "積み上げ縦棒 100% ", "Common.define.chartData.textBarStackedPer3d": "3-D 積み上げ縦棒 100% ", "Common.define.chartData.textCharts": "チャート", "Common.define.chartData.textColumn": "縦棒グラフ", "Common.define.chartData.textCombo": "複合", "Common.define.chartData.textComboAreaBar": "積み上げ面 - 集合縦棒", "Common.define.chartData.textComboBarLine": "集合縦棒 - 線", "Common.define.chartData.textComboBarLineSecondary": "集合縦棒 - 二次軸上の線", "Common.define.chartData.textComboCustom": "カスタム組み合わせ", "Common.define.chartData.textDoughnut": "ドーナツ", "Common.define.chartData.textHBarNormal": "集合横棒", "Common.define.chartData.textHBarNormal3d": "3-D 集合横棒", "Common.define.chartData.textHBarStacked": "積み上げ横棒", "Common.define.chartData.textHBarStacked3d": "3-D 積み上げ横棒", "Common.define.chartData.textHBarStackedPer": "積み上げ横棒 100％", "Common.define.chartData.textHBarStackedPer3d": "3-D 積み上げ横棒 100% ", "Common.define.chartData.textLine": "線", "Common.define.chartData.textLine3d": "3-D 折れ線", "Common.define.chartData.textLineMarker": "マーカー付き折れ線", "Common.define.chartData.textLineStacked": "積み上げ折れ線", "Common.define.chartData.textLineStackedMarker": "マーク付き積み上げ折れ線", "Common.define.chartData.textLineStackedPer": "積み上げ折れ線 100% ", "Common.define.chartData.textLineStackedPerMarker": "マーカー付き 積み上げ折れ線 100% ", "Common.define.chartData.textPie": "円グラフ", "Common.define.chartData.textPie3d": "3-D 円", "Common.define.chartData.textPoint": "XY (散布図)", "Common.define.chartData.textScatter": "散布図", "Common.define.chartData.textScatterLine": "直線付き散布図", "Common.define.chartData.textScatterLineMarker": "マーカーと直線付き散布図", "Common.define.chartData.textScatterSmooth": "平滑線付き散布図", "Common.define.chartData.textScatterSmoothMarker": "マーカーと平滑線付き散布図", "Common.define.chartData.textStock": "株価チャート", "Common.define.chartData.textSurface": "表面", "Common.define.smartArt.textAccentedPicture": "アクセント付きの図", "Common.define.smartArt.textAccentProcess": "アクセント・プロセス", "Common.define.smartArt.textAlternatingFlow": "波型ステップ", "Common.define.smartArt.textAlternatingHexagons": "左右交替積み上げ六角形", "Common.define.smartArt.textAlternatingPictureBlocks": "左右交替積み上げ画像ブロック", "Common.define.smartArt.textAlternatingPictureCircles": "円形付き画像ジグザグ表示", "Common.define.smartArt.textArchitectureLayout": "アーキテクチャ レイアウト", "Common.define.smartArt.textArrowRibbon": "リボン状の矢印", "Common.define.smartArt.textAscendingPictureAccentProcess": "アクセント画像付き上昇ステップ", "Common.define.smartArt.textBalance": "バランス", "Common.define.smartArt.textBasicBendingProcess": "基本蛇行ステップ", "Common.define.smartArt.textBasicBlockList": "カード型リスト", "Common.define.smartArt.textBasicChevronProcess": "プロセス", "Common.define.smartArt.textBasicCycle": "基本の循環", "Common.define.smartArt.textBasicMatrix": "基本マトリックス", "Common.define.smartArt.textBasicPie": "円グラフ", "Common.define.smartArt.textBasicProcess": "基本ステップ", "Common.define.smartArt.textBasicPyramid": "基本ピラミッド", "Common.define.smartArt.textBasicRadial": "基本放射", "Common.define.smartArt.textBasicTarget": "ターゲット", "Common.define.smartArt.textBasicTimeline": "タイムライン", "Common.define.smartArt.textBasicVenn": "基本ベン図", "Common.define.smartArt.textBendingPictureAccentList": "画像付きカード型リスト", "Common.define.smartArt.textBendingPictureBlocks": "自動配置の画像ブロック", "Common.define.smartArt.textBendingPictureCaption": "自動配置の表題付き画像", "Common.define.smartArt.textBendingPictureCaptionList": "自動配置の表題付き画像レイアウト", "Common.define.smartArt.textBendingPictureSemiTranparentText": "自動配置の半透明テキスト付き画像", "Common.define.smartArt.textBlockCycle": "ボックス循環", "Common.define.smartArt.textBubblePictureList": "バブル状画像リスト", "Common.define.smartArt.textCaptionedPictures": "表題付き画像", "Common.define.smartArt.textChevronAccentProcess": "アクセントステップ", "Common.define.smartArt.textChevronList": "プロセス リスト", "Common.define.smartArt.textCircleAccentTimeline": "円形組み合わせタイムライン", "Common.define.smartArt.textCircleArrowProcess": "円形矢印プロセス", "Common.define.smartArt.textCirclePictureHierarchy": "円形画像を使用した階層", "Common.define.smartArt.textCircleProcess": "円形プロセス", "Common.define.smartArt.textCircleRelationship": "円の関連付け", "Common.define.smartArt.textCircularBendingProcess": "円形蛇行ステップ", "Common.define.smartArt.textCircularPictureCallout": "円形画像を使った吹き出し", "Common.define.smartArt.textClosedChevronProcess": "開始点強調型プロセス", "Common.define.smartArt.textContinuousArrowProcess": "大きな矢印のプロセス", "Common.define.smartArt.textContinuousBlockProcess": "矢印と長方形のプロセス", "Common.define.smartArt.textContinuousCycle": "連続性強調循環", "Common.define.smartArt.textContinuousPictureList": "矢印付き画像リスト", "Common.define.smartArt.textConvergingArrows": "内向き矢印", "Common.define.smartArt.textConvergingRadial": "集中", "Common.define.smartArt.textConvergingText": "内向きテキスト", "Common.define.smartArt.textCounterbalanceArrows": "対立とバランスの矢印", "Common.define.smartArt.textCycle": "循環", "Common.define.smartArt.textCycleMatrix": "循環マトリックス", "Common.define.smartArt.textDescendingBlockList": "ブロックの降順リスト", "Common.define.smartArt.textDescendingProcess": "降順プロセス", "Common.define.smartArt.textDetailedProcess": "詳述プロセス", "Common.define.smartArt.textDivergingArrows": "左右逆方向矢印", "Common.define.smartArt.textDivergingRadial": "矢印付き放射", "Common.define.smartArt.textEquation": "数式", "Common.define.smartArt.textFramedTextPicture": "フレームに表示されるテキスト画像", "Common.define.smartArt.textFunnel": "漏斗", "Common.define.smartArt.textGear": "歯車", "Common.define.smartArt.textGridMatrix": "グリッド マトリックス", "Common.define.smartArt.textGroupedList": "グループ リスト", "Common.define.smartArt.textHalfCircleOrganizationChart": "アーチ型線で飾られた組織図", "Common.define.smartArt.textHexagonCluster": "蜂の巣状の六角形", "Common.define.smartArt.textHexagonRadial": "六角形放射", "Common.define.smartArt.textHierarchy": "階層", "Common.define.smartArt.textHierarchyList": "階層リスト", "Common.define.smartArt.textHorizontalBulletList": "横方向箇条書きリスト", "Common.define.smartArt.textHorizontalHierarchy": "横方向階層", "Common.define.smartArt.textHorizontalLabeledHierarchy": "ラベル付き横方向階層", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "複数レベル対応の横方向階層", "Common.define.smartArt.textHorizontalOrganizationChart": "水平方向の組織図", "Common.define.smartArt.textHorizontalPictureList": "横方向画像リスト", "Common.define.smartArt.textIncreasingArrowProcess": "上昇矢印のプロセス", "Common.define.smartArt.textIncreasingCircleProcess": "上昇円プロセス", "Common.define.smartArt.textInterconnectedBlockProcess": "相互接続された長方形のプロセス", "Common.define.smartArt.textInterconnectedRings": "互いにつながったリング", "Common.define.smartArt.textInvertedPyramid": "反転ピラミッド", "Common.define.smartArt.textLabeledHierarchy": "ラベル付き階層", "Common.define.smartArt.textLinearVenn": "横方向ベン図", "Common.define.smartArt.textLinedList": "線区切りリスト", "Common.define.smartArt.textList": "リスト", "Common.define.smartArt.textMatrix": "マトリックス", "Common.define.smartArt.textMultidirectionalCycle": "双方向循環", "Common.define.smartArt.textNameAndTitleOrganizationChart": "氏名/役職名付き組織図", "Common.define.smartArt.textNestedTarget": "包含", "Common.define.smartArt.textNondirectionalCycle": "矢印無し循環", "Common.define.smartArt.textOpposingArrows": "上下逆方向矢印", "Common.define.smartArt.textOpposingIdeas": "対立する案", "Common.define.smartArt.textOrganizationChart": "組織図", "Common.define.smartArt.textOther": "その他", "Common.define.smartArt.textPhasedProcess": "フェーズ プロセス", "Common.define.smartArt.textPicture": "画像", "Common.define.smartArt.textPictureAccentBlocks": "画像アクセントのブロック", "Common.define.smartArt.textPictureAccentList": "画像アクセントのリスト", "Common.define.smartArt.textPictureAccentProcess": "画像アクセントのプロセス", "Common.define.smartArt.textPictureCaptionList": "画像キャプションのリスト", "Common.define.smartArt.textPictureFrame": "フォトフレーム", "Common.define.smartArt.textPictureGrid": "画像グリッド", "Common.define.smartArt.textPictureLineup": "画像ラインアップ", "Common.define.smartArt.textPictureOrganizationChart": "画像付き組織図", "Common.define.smartArt.textPictureStrips": "画像付きラベル", "Common.define.smartArt.textPieProcess": "円グラフのプロセス", "Common.define.smartArt.textPlusAndMinus": "プラスとマイナス", "Common.define.smartArt.textProcess": "プロセス", "Common.define.smartArt.textProcessArrows": "矢印型ステップ", "Common.define.smartArt.textProcessList": "プロセスのリスト", "Common.define.smartArt.textPyramid": "ピラミッド", "Common.define.smartArt.textPyramidList": "ピラミッドのリスト", "Common.define.smartArt.textRadialCluster": "放射ブロック", "Common.define.smartArt.textRadialCycle": "中心付き循環", "Common.define.smartArt.textRadialList": "放射リスト", "Common.define.smartArt.textRadialPictureList": "放射画像リスト", "Common.define.smartArt.textRadialVenn": "放射型ベン図", "Common.define.smartArt.textRandomToResultProcess": "複数案をまとめるステップ", "Common.define.smartArt.textRelationship": "関係", "Common.define.smartArt.textRepeatingBendingProcess": "改行型蛇行ステップ", "Common.define.smartArt.textReverseList": "逆順リスト", "Common.define.smartArt.textSegmentedCycle": "円型循環", "Common.define.smartArt.textSegmentedProcess": "分割ステップ", "Common.define.smartArt.textSegmentedPyramid": "分割ピラミッド", "Common.define.smartArt.textSnapshotPictureList": "スナップショット画像リスト", "Common.define.smartArt.textSpiralPicture": "渦巻き画像", "Common.define.smartArt.textSquareAccentList": "箇条書き記号アクセントのリスト", "Common.define.smartArt.textStackedList": "積み上げリスト", "Common.define.smartArt.textStackedVenn": "包含型ベン図", "Common.define.smartArt.textStaggeredProcess": "段違いステップ", "Common.define.smartArt.textStepDownProcess": "ステップ ダウンのプロセス", "Common.define.smartArt.textStepUpProcess": "ステップアップのプロセス", "Common.define.smartArt.textSubStepProcess": "サブステップのプロセス", "Common.define.smartArt.textTabbedArc": "円弧状タブ", "Common.define.smartArt.textTableHierarchy": "積み木型の階層", "Common.define.smartArt.textTableList": "表型リスト", "Common.define.smartArt.textTabList": "タブ付きリスト", "Common.define.smartArt.textTargetList": "ターゲットのリスト", "Common.define.smartArt.textTextCycle": "テキスト循環", "Common.define.smartArt.textThemePictureAccent": "テーマ画像アクセント", "Common.define.smartArt.textThemePictureAlternatingAccent": "テーマ画像交互のアクセント", "Common.define.smartArt.textThemePictureGrid": "テーマ画像グリッド", "Common.define.smartArt.textTitledMatrix": "タイトル付きマトリックス", "Common.define.smartArt.textTitledPictureAccentList": "画像付き横方向リスト", "Common.define.smartArt.textTitledPictureBlocks": "タイトル付き画像ブロック", "Common.define.smartArt.textTitlePictureLineup": "タイトル付き画像ラインアップ", "Common.define.smartArt.textTrapezoidList": "台形リスト", "Common.define.smartArt.textUpwardArrow": "上向き矢印", "Common.define.smartArt.textVaryingWidthList": "可変幅リスト", "Common.define.smartArt.textVerticalAccentList": "縦方向アクセントのリスト", "Common.define.smartArt.textVerticalArrowList": "縦方向矢印リスト", "Common.define.smartArt.textVerticalBendingProcess": "縦型蛇行ステップ", "Common.define.smartArt.textVerticalBlockList": "縦方向ボックス リスト", "Common.define.smartArt.textVerticalBoxList": "縦方向リスト", "Common.define.smartArt.textVerticalBracketList": "縦方向ブラケット リスト", "Common.define.smartArt.textVerticalBulletList": "縦方向箇条書きリスト", "Common.define.smartArt.textVerticalChevronList": "縦方向プロセス", "Common.define.smartArt.textVerticalCircleList": "縦方向円リスト", "Common.define.smartArt.textVerticalCurvedList": "縦方向カーブのリスト", "Common.define.smartArt.textVerticalEquation": "縦型の数式", "Common.define.smartArt.textVerticalPictureAccentList": "縦方向円形画像リスト", "Common.define.smartArt.textVerticalPictureList": "縦方向画像リスト", "Common.define.smartArt.textVerticalProcess": "縦方向ステップ", "Common.Translation.textMoreButton": "もっと", "Common.Translation.tipFileLocked": "ドキュメントが編集用にロックされています。後で変更し、ローカルコピーとして保存することができます。", "Common.Translation.tipFileReadOnly": "ドキュメントは閲覧用で、編集はロックされています。後で変更し、そのローカルコピーを保存することができます。", "Common.Translation.warnFileLocked": "このファイルは他のアプリで編集されているので、編集できません。", "Common.Translation.warnFileLockedBtnEdit": "コピーを作成する", "Common.Translation.warnFileLockedBtnView": "閲覧するために開く", "Common.UI.ButtonColored.textAutoColor": "自動​", "Common.UI.ButtonColored.textNewColor": "新規カスタムカラーの追加", "Common.UI.Calendar.textApril": "4月", "Common.UI.Calendar.textAugust": "8月", "Common.UI.Calendar.textDecember": "12月", "Common.UI.Calendar.textFebruary": "2月", "Common.UI.Calendar.textJanuary": "1月", "Common.UI.Calendar.textJuly": "7月", "Common.UI.Calendar.textJune": "6月", "Common.UI.Calendar.textMarch": "3月", "Common.UI.Calendar.textMay": "5月", "Common.UI.Calendar.textMonths": "月", "Common.UI.Calendar.textNovember": "11月", "Common.UI.Calendar.textOctober": "10月", "Common.UI.Calendar.textSeptember": "9月", "Common.UI.Calendar.textShortApril": "4月", "Common.UI.Calendar.textShortAugust": "8月", "Common.UI.Calendar.textShortDecember": "12月", "Common.UI.Calendar.textShortFebruary": "2月", "Common.UI.Calendar.textShortFriday": "金", "Common.UI.Calendar.textShortJanuary": "1月", "Common.UI.Calendar.textShortJuly": "7月", "Common.UI.Calendar.textShortJune": "6月", "Common.UI.Calendar.textShortMarch": "3月", "Common.UI.Calendar.textShortMay": "5月", "Common.UI.Calendar.textShortMonday": "月", "Common.UI.Calendar.textShortNovember": "11月", "Common.UI.Calendar.textShortOctober": "10月", "Common.UI.Calendar.textShortSaturday": "土", "Common.UI.Calendar.textShortSeptember": "9月", "Common.UI.Calendar.textShortSunday": "日", "Common.UI.Calendar.textShortThursday": "木", "Common.UI.Calendar.textShortTuesday": "火", "Common.UI.Calendar.textShortWednesday": "水", "Common.UI.Calendar.textYears": "年", "Common.UI.ComboBorderSize.txtNoBorders": "罫線なし", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "罫線なし", "Common.UI.ComboDataView.emptyComboText": "スタイルなし", "Common.UI.ExtendedColorDialog.addButtonText": "追加", "Common.UI.ExtendedColorDialog.textCurrent": "現在", "Common.UI.ExtendedColorDialog.textHexErr": "入力された値が正しくありません。<br>000000〜FFFFFFの数値を入力してください。", "Common.UI.ExtendedColorDialog.textNew": "新しい", "Common.UI.ExtendedColorDialog.textRGBErr": "入力された値が正しくありません。<br>0〜255の数値を入力してください。", "Common.UI.HSBColorPicker.textNoColor": "色なし", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "パスワードを表示しない", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "パスワードを表示する", "Common.UI.SearchBar.textFind": "検索する", "Common.UI.SearchBar.tipCloseSearch": "検索を閉じる", "Common.UI.SearchBar.tipNextResult": "次の結果", "Common.UI.SearchBar.tipOpenAdvancedSettings": "詳細設定を開く", "Common.UI.SearchBar.tipPreviousResult": "前の結果", "Common.UI.SearchDialog.textHighlight": "結果のハイライト", "Common.UI.SearchDialog.textMatchCase": "大文字と小文字の区別", "Common.UI.SearchDialog.textReplaceDef": "代替テキストを挿入する", "Common.UI.SearchDialog.textSearchStart": "テキストをここに挿入してください。", "Common.UI.SearchDialog.textTitle": "検索と置換", "Common.UI.SearchDialog.textTitle2": "検索", "Common.UI.SearchDialog.textWholeWords": "単語全体のみ", "Common.UI.SearchDialog.txtBtnHideReplace": "置換を表示しない", "Common.UI.SearchDialog.txtBtnReplace": "置換する", "Common.UI.SearchDialog.txtBtnReplaceAll": "全てを置き換える", "Common.UI.SynchronizeTip.textDontShow": "今後このメッセージを表示しない", "Common.UI.SynchronizeTip.textSynchronize": "このドキュメントは他のユーザーによって変更されました。クリックして変更を保存し、更新を再読み込みしてください。", "Common.UI.ThemeColorPalette.textRecentColors": "最近使った色", "Common.UI.ThemeColorPalette.textStandartColors": "標準色", "Common.UI.ThemeColorPalette.textThemeColors": "テーマの色", "Common.UI.Themes.txtThemeClassicLight": "明るい(クラシック)", "Common.UI.Themes.txtThemeContrastDark": "ダークコントラスト", "Common.UI.Themes.txtThemeDark": "暗い", "Common.UI.Themes.txtThemeLight": "明るい", "Common.UI.Themes.txtThemeSystem": "システム設定と同じ", "Common.UI.Window.cancelButtonText": "キャンセル", "Common.UI.Window.closeButtonText": "閉じる", "Common.UI.Window.noButtonText": "いいえ", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "確認", "Common.UI.Window.textDontShow": "今後このメッセージを表示しない", "Common.UI.Window.textError": "エラー", "Common.UI.Window.textInformation": "情報", "Common.UI.Window.textWarning": "警告", "Common.UI.Window.yesButtonText": "はい", "Common.Utils.Metric.txtCm": "センチ", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Shift", "Common.Views.About.txtAddress": "アドレス：", "Common.Views.About.txtLicensee": "ライセンシー", "Common.Views.About.txtLicensor": "ライセンサー\t", "Common.Views.About.txtMail": "メール：", "Common.Views.About.txtPoweredBy": "によって提供されています", "Common.Views.About.txtTel": "電話番号：", "Common.Views.About.txtVersion": "バージョン", "Common.Views.AutoCorrectDialog.textAdd": "追加", "Common.Views.AutoCorrectDialog.textApplyText": "入力時に適用する", "Common.Views.AutoCorrectDialog.textAutoCorrect": "テキストオートコレクト", "Common.Views.AutoCorrectDialog.textAutoFormat": "入力時にオートフォーマット", "Common.Views.AutoCorrectDialog.textBulleted": "自動箇条書きリスト", "Common.Views.AutoCorrectDialog.textBy": "幅", "Common.Views.AutoCorrectDialog.textDelete": "削除する", "Common.Views.AutoCorrectDialog.textDoubleSpaces": "スペース2回でピリオドを入力する", "Common.Views.AutoCorrectDialog.textFLCells": "テーブルセルの最初の文字を大文字にする", "Common.Views.AutoCorrectDialog.textFLSentence": "文章の最初の文字を大文字にする", "Common.Views.AutoCorrectDialog.textHyperlink": "ハイパーリンクを使用したインターネットとネットワークの経路", "Common.Views.AutoCorrectDialog.textHyphens": "ハイフン(--)とダッシュ(-)の組み合わせ", "Common.Views.AutoCorrectDialog.textMathCorrect": "数式オートコレクト", "Common.Views.AutoCorrectDialog.textNumbered": "自動番号付けリスト", "Common.Views.AutoCorrectDialog.textQuotes": "左右の区別がない引用符を、区別がある引用符に変更する", "Common.Views.AutoCorrectDialog.textRecognized": "認識された関数", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "以下の式は、認識される数式です。 自動的にイタリック体になることはありません。", "Common.Views.AutoCorrectDialog.textReplace": "置換する", "Common.Views.AutoCorrectDialog.textReplaceText": "入力時に置き換える\n\t", "Common.Views.AutoCorrectDialog.textReplaceType": "入力時にテキストを置き換える", "Common.Views.AutoCorrectDialog.textReset": "リセット", "Common.Views.AutoCorrectDialog.textResetAll": "デフォルト設定にリセットする", "Common.Views.AutoCorrectDialog.textRestore": "復元する", "Common.Views.AutoCorrectDialog.textTitle": "オートコレクト", "Common.Views.AutoCorrectDialog.textWarnAddRec": "認識される関数には、大文字または小文字のAからZまでの文字のみを含める必要があります。", "Common.Views.AutoCorrectDialog.textWarnResetRec": "追加した式はすべて削除され、削除された式が復元されます。 このまま続けますか？", "Common.Views.AutoCorrectDialog.warnReplace": "％1のオートコレクトのエントリはすでに存在します。 取り替えますか？", "Common.Views.AutoCorrectDialog.warnReset": "追加したオートコレクトはすべて削除され、変更されたものは元の値に復元されます。 このまま続けますか？", "Common.Views.AutoCorrectDialog.warnRestore": "％1のオートコレクトエントリは元の値にリセットされます。 続けますか？", "Common.Views.Chat.textSend": "送信", "Common.Views.Comments.mniAuthorAsc": "AからZで作成者を表示する", "Common.Views.Comments.mniAuthorDesc": "ZからAで作成者を表示する", "Common.Views.Comments.mniDateAsc": "最も古い", "Common.Views.Comments.mniDateDesc": "最も新しい", "Common.Views.Comments.mniFilterGroups": "グループでフィルター", "Common.Views.Comments.mniPositionAsc": "上から", "Common.Views.Comments.mniPositionDesc": "下から", "Common.Views.Comments.textAdd": "追加", "Common.Views.Comments.textAddComment": "コメントの追加", "Common.Views.Comments.textAddCommentToDoc": "ドキュメントにコメントの追加", "Common.Views.Comments.textAddReply": "返信の追加", "Common.Views.Comments.textAll": "すべて", "Common.Views.Comments.textAnonym": "ゲスト", "Common.Views.Comments.textCancel": "キャンセル", "Common.Views.Comments.textClose": "閉じる", "Common.Views.Comments.textClosePanel": "コメントを閉じる", "Common.Views.Comments.textComments": "コメント", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "ここにコメントを挿入してください。", "Common.Views.Comments.textHintAddComment": "コメントを追加", "Common.Views.Comments.textOpenAgain": "もう一度開く", "Common.Views.Comments.textReply": "返信する", "Common.Views.Comments.textResolve": "解決する", "Common.Views.Comments.textResolved": "解決済み", "Common.Views.Comments.textSort": "コメントを並べ替える", "Common.Views.Comments.textViewResolved": "コメントを再開する権限がありません", "Common.Views.Comments.txtEmpty": "ドキュメントにはコメントがありません。", "Common.Views.CopyWarningDialog.textDontShow": "今後このメッセージを表示しない", "Common.Views.CopyWarningDialog.textMsg": "エディターツールバーのボタンやコンテキストメニューの操作によるコピー、カット、ペーストの動作は、このエディタータブ内でのみ実行されます。<br><br> エディタータブ以外のアプリケーションとの間でコピーまたは貼り付けを行うには、次のキーボードの組み合わせを使用して下さい:", "Common.Views.CopyWarningDialog.textTitle": "コピー,切り取り,貼り付け", "Common.Views.CopyWarningDialog.textToCopy": "コピーのため", "Common.Views.CopyWarningDialog.textToCut": "切り取りのため", "Common.Views.CopyWarningDialog.textToPaste": "貼り付のため", "Common.Views.DocumentAccessDialog.textLoading": "読み込み中...", "Common.Views.DocumentAccessDialog.textTitle": "共有設定", "Common.Views.ExternalDiagramEditor.textTitle": "チャートのエディタ", "Common.Views.ExternalEditor.textClose": "閉じる", "Common.Views.ExternalEditor.textSave": "保存&終了", "Common.Views.ExternalMergeEditor.textTitle": "差し込み印刷の宛先", "Common.Views.ExternalOleEditor.textTitle": "スプレッドシートエディター", "Common.Views.Header.labelCoUsersDescr": "ファイルを編集しているユーザー：", "Common.Views.Header.textAddFavorite": "お気に入りとしてマークする", "Common.Views.Header.textAdvSettings": "詳細設定", "Common.Views.Header.textBack": "ファイルの場所を開く", "Common.Views.Header.textCompactView": "ツールバーを表示しない", "Common.Views.Header.textHideLines": "ルーラーを表示しない", "Common.Views.Header.textHideStatusBar": "ステータスバーを表示しない", "Common.Views.Header.textReadOnly": "閲覧のみ", "Common.Views.Header.textRemoveFavorite": "お気に入りから削除", "Common.Views.Header.textShare": "共有", "Common.Views.Header.textZoom": "ズーム", "Common.Views.Header.tipAccessRights": "文書のアクセス許可のの管理", "Common.Views.Header.tipDownload": "ファイルをダウンロード", "Common.Views.Header.tipGoEdit": "現在のファイルを編集する", "Common.Views.Header.tipPrint": "ファイルを印刷する", "Common.Views.Header.tipPrintQuick": "クイックプリント", "Common.Views.Header.tipRedo": "やり直し", "Common.Views.Header.tipSave": "保存する", "Common.Views.Header.tipSearch": "検索", "Common.Views.Header.tipUndo": "元に戻す", "Common.Views.Header.tipUsers": "ユーザーを表示する", "Common.Views.Header.tipViewSettings": "表示の設定", "Common.Views.Header.tipViewUsers": "ユーザーとドキュメントのアクセス権限の管理を表示", "Common.Views.Header.txtAccessRights": "アクセス権限の変更", "Common.Views.Header.txtRename": "名前を変更する", "Common.Views.History.textCloseHistory": "履歴を閉じる", "Common.Views.History.textHide": "折りたたみ", "Common.Views.History.textHideAll": "変更の詳細を表示しない", "Common.Views.History.textRestore": "復元する", "Common.Views.History.textShow": "拡張する", "Common.Views.History.textShowAll": "変更の詳細を表示する", "Common.Views.History.textVer": "ver.", "Common.Views.ImageFromUrlDialog.textUrl": "画像URLの貼り付け", "Common.Views.ImageFromUrlDialog.txtEmpty": "この項目は必須です", "Common.Views.ImageFromUrlDialog.txtNotUrl": "このフィールドは「http://www.example.com」の形式のURLである必要があります。", "Common.Views.InsertTableDialog.textInvalidRowsCols": "有効な行と列の数を指定する必要があります。", "Common.Views.InsertTableDialog.txtColumns": "列数", "Common.Views.InsertTableDialog.txtMaxText": "このフィールドの最大値は{0}です。", "Common.Views.InsertTableDialog.txtMinText": "このフィールドの最小値は{0}です。", "Common.Views.InsertTableDialog.txtRows": "行数", "Common.Views.InsertTableDialog.txtTitle": "テーブルのサイズ", "Common.Views.InsertTableDialog.txtTitleSplit": "セルを分割", "Common.Views.LanguageDialog.labelSelect": "ドキュメントの言語の選択", "Common.Views.OpenDialog.closeButtonText": "ファイルを閉じる", "Common.Views.OpenDialog.txtEncoding": "エンコード", "Common.Views.OpenDialog.txtIncorrectPwd": "パスワードが正しくありません。", "Common.Views.OpenDialog.txtOpenFile": "ファイルを開くためにパスワードを入力してください。", "Common.Views.OpenDialog.txtPassword": "パスワード", "Common.Views.OpenDialog.txtPreview": "プレビュー", "Common.Views.OpenDialog.txtProtected": "一度パスワードを入力してファイルを開くと、そのファイルの既存のパスワードがリセットされます。", "Common.Views.OpenDialog.txtTitle": "%1オプションの選択", "Common.Views.OpenDialog.txtTitleProtected": "保護されたファイル", "Common.Views.PasswordDialog.txtDescription": "この文書を保護するためのパスワードを設定してください", "Common.Views.PasswordDialog.txtIncorrectPwd": "先に入力したパスワードと一致しません。", "Common.Views.PasswordDialog.txtPassword": "パスワード", "Common.Views.PasswordDialog.txtRepeat": "パスワードを再入力", "Common.Views.PasswordDialog.txtTitle": "パスワードを設定する", "Common.Views.PasswordDialog.txtWarning": "警告: パスワードを忘れると元に戻せません。安全な場所に記録してください。", "Common.Views.PluginDlg.textLoading": "読み込み中", "Common.Views.Plugins.groupCaption": "プラグイン", "Common.Views.Plugins.strPlugins": "プラグイン", "Common.Views.Plugins.textClosePanel": "プラグインを閉じる", "Common.Views.Plugins.textLoading": "読み込み中", "Common.Views.Plugins.textStart": "開始", "Common.Views.Plugins.textStop": "停止", "Common.Views.Protection.hintAddPwd": "パスワードを使用して暗号化する", "Common.Views.Protection.hintDelPwd": "パスワードを削除する", "Common.Views.Protection.hintPwd": "パスワードを変更か削除する", "Common.Views.Protection.hintSignature": "デジタル署名かデジタル署名行を追加", "Common.Views.Protection.txtAddPwd": "パスワードの追加", "Common.Views.Protection.txtChangePwd": "パスワードを変更する", "Common.Views.Protection.txtDeletePwd": "パスワードを削除する", "Common.Views.Protection.txtEncrypt": "暗号化する", "Common.Views.Protection.txtInvisibleSignature": "デジタル署名を追加", "Common.Views.Protection.txtSignature": "署名", "Common.Views.Protection.txtSignatureLine": "署名欄の追加", "Common.Views.RenameDialog.textName": "ファイル名", "Common.Views.RenameDialog.txtInvalidName": "ファイル名に次の文字を使うことはできません。", "Common.Views.ReviewChanges.hintNext": "次の変更箇所へ", "Common.Views.ReviewChanges.hintPrev": "以前の変更箇所へ", "Common.Views.ReviewChanges.mniFromFile": "ファイルからの文書", "Common.Views.ReviewChanges.mniFromStorage": "ストレージからの文書", "Common.Views.ReviewChanges.mniFromUrl": "URLからの文書", "Common.Views.ReviewChanges.mniSettings": "比較設定", "Common.Views.ReviewChanges.strFast": "高速", "Common.Views.ReviewChanges.strFastDesc": "リアルタイム共同編集モードです。すべての変更は自動的に保存されます。", "Common.Views.ReviewChanges.strStrict": "厳格", "Common.Views.ReviewChanges.strStrictDesc": "あなたや他のユーザーが行った変更を同期するために、[保存]ボタンを使用する", "Common.Views.ReviewChanges.textEnable": "有効にする", "Common.Views.ReviewChanges.textWarnTrackChanges": "変更履歴はフルアクセス権を持つすべてのユーザーに対して有効になります。次に他のユーザーがドキュメントを開いた時にも、変更履歴は有効になっています。", "Common.Views.ReviewChanges.textWarnTrackChangesTitle": "全員に変更履歴を有効しますか？", "Common.Views.ReviewChanges.tipAcceptCurrent": "現在の変更を承諾する", "Common.Views.ReviewChanges.tipCoAuthMode": "共同編集モードを設定する", "Common.Views.ReviewChanges.tipCommentRem": "コメントを削除する", "Common.Views.ReviewChanges.tipCommentRemCurrent": "現在のコメントを削除する", "Common.Views.ReviewChanges.tipCommentResolve": "コメントを解決する", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "現在のコメントを解決する", "Common.Views.ReviewChanges.tipCompare": "現在の文書を別の文書と比較する", "Common.Views.ReviewChanges.tipHistory": "バージョン履歴を表示する", "Common.Views.ReviewChanges.tipRejectCurrent": "現在の変更を拒否する", "Common.Views.ReviewChanges.tipReview": "変更履歴", "Common.Views.ReviewChanges.tipReviewView": "変更内容を表示するモードを選択してください", "Common.Views.ReviewChanges.tipSetDocLang": "文書の言語を設定", "Common.Views.ReviewChanges.tipSetSpelling": "スペルチェック", "Common.Views.ReviewChanges.tipSharing": "文書のアクセス許可のの管理", "Common.Views.ReviewChanges.txtAccept": "承諾", "Common.Views.ReviewChanges.txtAcceptAll": "すべての変更を承諾する", "Common.Views.ReviewChanges.txtAcceptChanges": "変更を承諾する", "Common.Views.ReviewChanges.txtAcceptCurrent": "現在の変更を承諾する", "Common.Views.ReviewChanges.txtChat": "チャット", "Common.Views.ReviewChanges.txtClose": "閉じる", "Common.Views.ReviewChanges.txtCoAuthMode": "共同編集のモード", "Common.Views.ReviewChanges.txtCommentRemAll": "全てのコメントを削除する", "Common.Views.ReviewChanges.txtCommentRemCurrent": "現在のコメントを削除する", "Common.Views.ReviewChanges.txtCommentRemMy": "自分のコメントを削除する", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "自分の今のコメントを削除する", "Common.Views.ReviewChanges.txtCommentRemove": "削除する", "Common.Views.ReviewChanges.txtCommentResolve": "解決する", "Common.Views.ReviewChanges.txtCommentResolveAll": "すべてのコメントを解決する", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "現在のコメントを解決する", "Common.Views.ReviewChanges.txtCommentResolveMy": "自分のコメントを解決する", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "現在の自分のコメントを解決する", "Common.Views.ReviewChanges.txtCompare": "比較", "Common.Views.ReviewChanges.txtDocLang": "言語", "Common.Views.ReviewChanges.txtEditing": "編集", "Common.Views.ReviewChanges.txtFinal": "全ての変更が承認されました{0}", "Common.Views.ReviewChanges.txtFinalCap": "最終版", "Common.Views.ReviewChanges.txtHistory": "バージョン履歴", "Common.Views.ReviewChanges.txtMarkup": "全ての変更{0}", "Common.Views.ReviewChanges.txtMarkupCap": "マークアップとバルーン", "Common.Views.ReviewChanges.txtMarkupSimple": "すべての変更 {0} <br> 吹き出しなし", "Common.Views.ReviewChanges.txtMarkupSimpleCap": "マークアップのみ", "Common.Views.ReviewChanges.txtNext": "次へ", "Common.Views.ReviewChanges.txtOff": "私はオフする", "Common.Views.ReviewChanges.txtOffGlobal": "私と全員オフにする", "Common.Views.ReviewChanges.txtOn": "私はオンにする", "Common.Views.ReviewChanges.txtOnGlobal": "私と全員オンにする", "Common.Views.ReviewChanges.txtOriginal": "全ての変更が拒否されました{0}", "Common.Views.ReviewChanges.txtOriginalCap": "原本", "Common.Views.ReviewChanges.txtPrev": "前回の", "Common.Views.ReviewChanges.txtPreview": "プレビュー", "Common.Views.ReviewChanges.txtReject": "拒否する", "Common.Views.ReviewChanges.txtRejectAll": "すべての変更を拒否する", "Common.Views.ReviewChanges.txtRejectChanges": "変更を拒否", "Common.Views.ReviewChanges.txtRejectCurrent": "現在の変更を拒否する", "Common.Views.ReviewChanges.txtSharing": "共有", "Common.Views.ReviewChanges.txtSpelling": "スペルチェック", "Common.Views.ReviewChanges.txtTurnon": "変更履歴", "Common.Views.ReviewChanges.txtView": "表示モード", "Common.Views.ReviewChangesDialog.textTitle": "変更の確認", "Common.Views.ReviewChangesDialog.txtAccept": "承諾", "Common.Views.ReviewChangesDialog.txtAcceptAll": "すべての変更を承諾する", "Common.Views.ReviewChangesDialog.txtAcceptCurrent": "現在の変更を承諾する", "Common.Views.ReviewChangesDialog.txtNext": "次の変更箇所へ", "Common.Views.ReviewChangesDialog.txtPrev": "以前の変更箇所へ", "Common.Views.ReviewChangesDialog.txtReject": "拒否する", "Common.Views.ReviewChangesDialog.txtRejectAll": "すべての変更を拒否する", "Common.Views.ReviewChangesDialog.txtRejectCurrent": "現在の変更を拒否する", "Common.Views.ReviewPopover.textAdd": "追加", "Common.Views.ReviewPopover.textAddReply": "返信を追加", "Common.Views.ReviewPopover.textCancel": "キャンセル", "Common.Views.ReviewPopover.textClose": "閉じる", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textFollowMove": "移動する", "Common.Views.ReviewPopover.textMention": "+メンションされるユーザーは文書にアクセスのメール通知を取得します", "Common.Views.ReviewPopover.textMentionNotify": "＋メンションされるユーザーはメールで通知されます", "Common.Views.ReviewPopover.textOpenAgain": "もう一度開く", "Common.Views.ReviewPopover.textReply": "返信する", "Common.Views.ReviewPopover.textResolve": "解決する", "Common.Views.ReviewPopover.textViewResolved": "コメントを再開する権限がありません", "Common.Views.ReviewPopover.txtAccept": "同意する", "Common.Views.ReviewPopover.txtDeleteTip": "削除する", "Common.Views.ReviewPopover.txtEditTip": "編集", "Common.Views.ReviewPopover.txtReject": "拒否する", "Common.Views.SaveAsDlg.textLoading": "読み込み中", "Common.Views.SaveAsDlg.textTitle": "保存先のフォルダ", "Common.Views.SearchPanel.textCaseSensitive": "大文字と小文字の区別", "Common.Views.SearchPanel.textCloseSearch": "検索を閉じる", "Common.Views.SearchPanel.textContentChanged": "ドキュメントが変更されました", "Common.Views.SearchPanel.textFind": "検索する", "Common.Views.SearchPanel.textFindAndReplace": "検索して置換する", "Common.Views.SearchPanel.textMatchUsingRegExp": "正規表現によるマッチング", "Common.Views.SearchPanel.textNoMatches": "一致する結果がありません", "Common.Views.SearchPanel.textNoSearchResults": "検索結果は見つかりませんでした", "Common.Views.SearchPanel.textReplace": "置換する", "Common.Views.SearchPanel.textReplaceAll": "全てを置換する", "Common.Views.SearchPanel.textReplaceWith": "置換後の文字列", "Common.Views.SearchPanel.textSearchAgain": "正確な結果を得るために{0}新規検索を行う{1}。", "Common.Views.SearchPanel.textSearchHasStopped": "検索が停止しました", "Common.Views.SearchPanel.textSearchResults": "検索結果：{0}/{1}", "Common.Views.SearchPanel.textTooManyResults": "検索結果が多すぎるため、ここに表示できません", "Common.Views.SearchPanel.textWholeWords": "単語全体のみ", "Common.Views.SearchPanel.tipNextResult": "次の結果", "Common.Views.SearchPanel.tipPreviousResult": "前の結果", "Common.Views.SelectFileDlg.textLoading": "読み込み中", "Common.Views.SelectFileDlg.textTitle": "データソースを選択する", "Common.Views.SignDialog.textBold": "太字", "Common.Views.SignDialog.textCertificate": "証明書", "Common.Views.SignDialog.textChange": "変更する", "Common.Views.SignDialog.textInputName": "署名者の名前を入力", "Common.Views.SignDialog.textItalic": "イタリック", "Common.Views.SignDialog.textNameError": "署名者の名前を空にしておくことはできません。", "Common.Views.SignDialog.textPurpose": "この文書にサインする目的", "Common.Views.SignDialog.textSelect": "選択する", "Common.Views.SignDialog.textSelectImage": "画像を選択する", "Common.Views.SignDialog.textSignature": "署名は次のようになります:", "Common.Views.SignDialog.textTitle": "文書に署名する", "Common.Views.SignDialog.textUseImage": "または「画像を選択」をクリックして、画像を署名として使用します", "Common.Views.SignDialog.textValid": "％1から％2まで有効", "Common.Views.SignDialog.tipFontName": "フォント名", "Common.Views.SignDialog.tipFontSize": "フォントのサイズ", "Common.Views.SignSettingsDialog.textAllowComment": "署名ダイアログで署名者がコメントを追加できるようにする", "Common.Views.SignSettingsDialog.textDefInstruction": "このドキュメントに署名する前に、署名するコンテンツが正しいことを確認してください。", "Common.Views.SignSettingsDialog.textInfoEmail": "署名候補者のメールアドレス", "Common.Views.SignSettingsDialog.textInfoName": "署名候補者", "Common.Views.SignSettingsDialog.textInfoTitle": "署名候補者の役職", "Common.Views.SignSettingsDialog.textInstructions": "署名者への説明", "Common.Views.SignSettingsDialog.textShowDate": "署名欄に署名日を表示する", "Common.Views.SignSettingsDialog.textTitle": "署名の設定", "Common.Views.SignSettingsDialog.txtEmpty": "この項目は必須です", "Common.Views.SymbolTableDialog.textCharacter": "文字", "Common.Views.SymbolTableDialog.textCode": "UnicodeHEX値", "Common.Views.SymbolTableDialog.textCopyright": "著作権マーク", "Common.Views.SymbolTableDialog.textDCQuote": "二重引用符（右）を終了する", "Common.Views.SymbolTableDialog.textDOQuote": "二重の引用符(左）", "Common.Views.SymbolTableDialog.textEllipsis": "水平の省略記号", "Common.Views.SymbolTableDialog.textEmDash": "全角ダッシュ", "Common.Views.SymbolTableDialog.textEmSpace": "全角スペース", "Common.Views.SymbolTableDialog.textEnDash": "半角ダッシュ", "Common.Views.SymbolTableDialog.textEnSpace": "半角スペース", "Common.Views.SymbolTableDialog.textFont": "フォント", "Common.Views.SymbolTableDialog.textNBHyphen": "改行をしないハイフン", "Common.Views.SymbolTableDialog.textNBSpace": "ノーブレークスペース", "Common.Views.SymbolTableDialog.textPilcrow": "段落記号", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4スペース", "Common.Views.SymbolTableDialog.textRange": "範囲", "Common.Views.SymbolTableDialog.textRecent": "最近使用した記号", "Common.Views.SymbolTableDialog.textRegistered": "登録商標マーク", "Common.Views.SymbolTableDialog.textSCQuote": "単一引用符（右）を終了する", "Common.Views.SymbolTableDialog.textSection": "節記号", "Common.Views.SymbolTableDialog.textShortcut": "ショートカットキー", "Common.Views.SymbolTableDialog.textSHyphen": "ソフトハイフン", "Common.Views.SymbolTableDialog.textSOQuote": "単一引用符（左）", "Common.Views.SymbolTableDialog.textSpecial": "特殊文字", "Common.Views.SymbolTableDialog.textSymbols": "記号", "Common.Views.SymbolTableDialog.textTitle": "記号", "Common.Views.SymbolTableDialog.textTradeMark": "商標マーク", "Common.Views.UserNameDialog.textDontShow": "二度と表示しない", "Common.Views.UserNameDialog.textLabel": "ラベル：", "Common.Views.UserNameDialog.textLabelError": "ラベルは空白にできません", "DE.Controllers.LeftMenu.leavePageText": "この文書で保存されていない変更はすべて失われます。<br> 「キャンセル」をクリックし、「保存」をクリックすると、変更が保存されます。「OK」をクリックすると、保存されていないすべての変更が破棄されます。", "DE.Controllers.LeftMenu.newDocumentTitle": "無名のドキュメント", "DE.Controllers.LeftMenu.notcriticalErrorTitle": "警告", "DE.Controllers.LeftMenu.requestEditRightsText": "編集の権限を要求中...", "DE.Controllers.LeftMenu.textLoadHistory": "バリエーションの履歴の読み込み中...", "DE.Controllers.LeftMenu.textNoTextFound": "検索データが見つかりませんでした。検索オプションを変更してください。", "DE.Controllers.LeftMenu.textReplaceSkipped": "置換が行われました。スキップされた発生回数は{0}です。", "DE.Controllers.LeftMenu.textReplaceSuccess": "検索が完了しました。{0}つが置換されました。", "DE.Controllers.LeftMenu.txtCompatible": "ドキュメントは新しい形式で保存されます。 すべてのエディタ機能を使用できますが、ドキュメントのレイアウトに影響する可能性があります。<br>ファイルを古いバージョンのMS Wordと互換性を持たせる場合は、詳細設定の[互換性]オプションをご使用ください。", "DE.Controllers.LeftMenu.txtUntitled": "無題", "DE.Controllers.LeftMenu.warnDownloadAs": "この形式で保存を続けると、テキスト以外のすべての機能が失われます。<br>本当に続行してもよろしいですか？", "DE.Controllers.LeftMenu.warnDownloadAsPdf": "あなたの{0}は編集可能な形式に変換されます。これには時間がかかる場合があります。変換後のドキュメントは、テキストを編集できるように最適化されるため、特に元のファイルに多くのグラフィックが含まれている場合、元の {0} と全く同じようには見えないかもしれません。", "DE.Controllers.LeftMenu.warnDownloadAsRTF": "この形式で保存を続けると、一部の書式が失われる可能性があります。<br>本当に続行しますか？", "DE.Controllers.LeftMenu.warnReplaceString": "{0} は、置換フィールドに有効な特殊文字ではありません。", "DE.Controllers.Main.applyChangesTextText": "変更の読み込み中...", "DE.Controllers.Main.applyChangesTitleText": "変更の読み込み中", "DE.Controllers.Main.confirmMaxChangesSize": "アクションのサイズがサーバーに設定された制限を超えています。<br>「元に戻す」ボタンを押して最後のアクションをキャンセルするか、「続ける」を押してローカルにアクションを維持してください（何も失われないことを確認するために、ファイルをダウンロードするか、その内容をコピーする必要があります）。", "DE.Controllers.Main.convertationTimeoutText": "変換タイムアウトを超過しました。", "DE.Controllers.Main.criticalErrorExtText": "OKボタンを押すとドキュメントリストに戻ることができます。", "DE.Controllers.Main.criticalErrorTitle": "エラー", "DE.Controllers.Main.downloadErrorText": "ダウンロード失敗", "DE.Controllers.Main.downloadMergeText": "ダウンロード中...", "DE.Controllers.Main.downloadMergeTitle": "ダウンロード中", "DE.Controllers.Main.downloadTextText": "ドキュメントのダウンロード中...", "DE.Controllers.Main.downloadTitleText": "ドキュメントのダウンロード中", "DE.Controllers.Main.errorAccessDeny": "権限のない操作を実行しようとしています。<br>ドキュメントサーバーの管理者にご連絡ください。", "DE.Controllers.Main.errorBadImageUrl": "画像のURLが正しくありません", "DE.Controllers.Main.errorCoAuthoringDisconnect": "サーバーとの接続が失われました。現在、文書を編集することができません。", "DE.Controllers.Main.errorComboSeries": "組み合わせチャートを作成するには、最低2つのデータを選択します。", "DE.Controllers.Main.errorCompare": "共同編集中は、ドキュメントの比較機能は使用できません。", "DE.Controllers.Main.errorConnectToServer": "ドキュメントを保存できませんでした。接続設定を確認するか、管理者にお問い合わせください。<br>「OK」ボタンをクリックすると、ドキュメントのダウンロードを促すメッセージが表示されます。", "DE.Controllers.Main.errorDatabaseConnection": "外部エラーです。<br>データベース接続エラーです。この問題は解決しない場合は、サポートにお問い合わせください。", "DE.Controllers.Main.errorDataEncrypted": "暗号化された変更を受信しました。残念ながら解読できません。", "DE.Controllers.Main.errorDataRange": "データ範囲が正しくありません。", "DE.Controllers.Main.errorDefaultMessage": "エラー コード:%1", "DE.Controllers.Main.errorDirectUrl": "文ドキュメントへのリンクを確認してください。<br>このリンクは、ダウンロード用のファイルへの直接リンクである必要があります。", "DE.Controllers.Main.errorEditingDownloadas": "文書の処理中にエラーが発生しました。<br>「名前を付けてダウンロード」オプションを使用して、ファイルのバックアップコピーをコンピューターのハードディスクに保存してください。", "DE.Controllers.Main.errorEditingSaveas": "文書の処理中にエラーが発生しました。<br>「名前を付けてダウンロード」オプションを使用して、ファイルのバックアップコピーをコンピューターのハードディスクに保存してください。", "DE.Controllers.Main.errorEmailClient": "メールクライアントが見つかりませんでした。", "DE.Controllers.Main.errorEmptyTOC": "スタイルギャラリーからの見出しスタイルを選択したテキストに適用して、目次の作成を開始します", "DE.Controllers.Main.errorFilePassProtect": "文書がパスワードで保護されているため開くことができません", "DE.Controllers.Main.errorFileSizeExceed": "ファイルサイズがサーバーで設定された制限を超過しています。<br>Documentサーバー管理者に詳細をお問い合わせください。", "DE.Controllers.Main.errorForceSave": "文書の保存中にエラーが発生しました。「名前を付けてダウンロード」オプションを使用して、ファイルのバックアップコピーをコンピューターのハードディスクに保存するか、後で再試行してください。", "DE.Controllers.Main.errorInconsistentExt": "ファイルを開くときにエラーが発生しました。<br>ファイルの内容がファイルの拡張子と一致しません。", "DE.Controllers.Main.errorInconsistentExtDocx": "ファイルを開くときにエラーが発生しました。<br>ファイルの内容はドキュメント (docx など) に対応していますが、ファイルの拡張子が一致していません: %1", "DE.Controllers.Main.errorInconsistentExtPdf": "ファイルを開くときにエラーが発生しました。<br>ファイルの内容は次のいずれかの形式に対応しています: pdf/djvu/xps/oxps が、ファイルの拡張子が一致していません: %1", "DE.Controllers.Main.errorInconsistentExtPptx": "ファイルを開くときにエラーが発生しました。<br>ファイルの内容はプレゼンテーション (pptx など) に対応していますが、ファイルの拡張子が一致していません: %1", "DE.Controllers.Main.errorInconsistentExtXlsx": "ファイルを開くときにエラーが発生しました。<br>ファイルの内容はスプレッドシート (xlsx など) に対応していますが、ファイルの拡張子が一致していません: %1", "DE.Controllers.Main.errorKeyEncrypt": "不明なキーの記述子", "DE.Controllers.Main.errorKeyExpire": "キー記述子は有効期限が切れました", "DE.Controllers.Main.errorLoadingFont": "フォントが読み込まれていません。<br>ドキュメントサーバーの管理者に連絡してください。", "DE.Controllers.Main.errorMailMergeLoadFile": "ドキュメントの読み込みに失敗しました。別のファイルを選択してください。", "DE.Controllers.Main.errorMailMergeSaveFile": "結合に失敗しました。", "DE.Controllers.Main.errorNoTOC": "更新する目次がありません。「参考文献」タブから挿入できます", "DE.Controllers.Main.errorPasswordIsNotCorrect": "入力されたパスワードが正しくありません。<br>CAPS LOCKキーがオフになっていることを確認し、大文字を正しく使用するようにしてください。", "DE.Controllers.Main.errorProcessSaveResult": "保存に失敗しました", "DE.Controllers.Main.errorServerVersion": "エディターのバージョンが更新されました。 変更を適用するために、ページが再読み込みされます。", "DE.Controllers.Main.errorSessionAbsolute": "ドキュメント編集セッションが終了しました。 ページを再度読み込みしてください。", "DE.Controllers.Main.errorSessionIdle": "このドキュメントは長い間編集されていませんでした。このページを再度読み込みしてください。", "DE.Controllers.Main.errorSessionToken": "サーバーとの接続が中断されました。このページを再度読み込みしてください。", "DE.Controllers.Main.errorSetPassword": "パスワードを設定できませんでした。", "DE.Controllers.Main.errorStockChart": "行の順序が正しくありません。この株価チャートを作成するには、<br>始値、最大値、最小値、終値の順でシートのデータを配置してください。", "DE.Controllers.Main.errorSubmit": "送信に失敗しました。", "DE.Controllers.Main.errorTextFormWrongFormat": "入力された値がフィールドのフォーマットと一致しません。", "DE.Controllers.Main.errorToken": "ドキュメントセキュリティトークンが正しく形成されていません。<br>ドキュメントサーバーの管理者にご連絡ください。", "DE.Controllers.Main.errorTokenExpire": "ドキュメントセキュリティトークンの有効期限が切れています。<br>ドキュメントサーバーの管理者に連絡してください。", "DE.Controllers.Main.errorUpdateVersion": "ファイルのバージョンが変更されました。ページを再読み込みします。", "DE.Controllers.Main.errorUpdateVersionOnDisconnect": "インターネット接続が復旧し、ファイルのバージョンが更新されています。<br>作業を継続する前に、ファイルをダウンロードするか内容をコピーして、変更が消えてしまわないようにしてからページを再読み込みしてください。", "DE.Controllers.Main.errorUserDrop": "現在、このファイルにはアクセスできません。", "DE.Controllers.Main.errorUsersExceed": "料金プランで許可されているユーザー数を超過しました。", "DE.Controllers.Main.errorViewerDisconnect": "接続が切断されました。文書の表示は可能ですが、<br>再度接続されてページが再ロードされるまで、ダウンロードまたは印刷することはできません。", "DE.Controllers.Main.leavePageText": "この文書の保存されていない変更があります。保存するために「このページにとどまる」をクリックし、その後「保存」をクリックしてください。「このページを離れる」をクリックすると、未保存の変更がすべて破棄されます。", "DE.Controllers.Main.leavePageTextOnClose": "この文書で保存されていない変更はすべて失われます。<br> 「キャンセル」をクリックし、「保存」をクリックすると、変更が保存されます。「OK」をクリックすると、保存されていないすべての変更が破棄されます。", "DE.Controllers.Main.loadFontsTextText": "データを読み込んでいます…", "DE.Controllers.Main.loadFontsTitleText": "データを読み込んでいます", "DE.Controllers.Main.loadFontTextText": "データを読み込んでいます…", "DE.Controllers.Main.loadFontTitleText": "データを読み込んでいます", "DE.Controllers.Main.loadImagesTextText": "画像の読み込み中...", "DE.Controllers.Main.loadImagesTitleText": "画像の読み込み中", "DE.Controllers.Main.loadImageTextText": "画像の読み込み中...", "DE.Controllers.Main.loadImageTitleText": "画像の読み込み中", "DE.Controllers.Main.loadingDocumentTextText": "ドキュメントを読み込んでいます…", "DE.Controllers.Main.loadingDocumentTitleText": "ドキュメントを読み込んでいます", "DE.Controllers.Main.mailMergeLoadFileText": "データソースを読み込んでいます...", "DE.Controllers.Main.mailMergeLoadFileTitle": "データソースを読み込んでいます", "DE.Controllers.Main.notcriticalErrorTitle": "警告", "DE.Controllers.Main.openErrorText": "ファイルを読み込み中にエラーが発生しました。", "DE.Controllers.Main.openTextText": "ドキュメントを開いています...", "DE.Controllers.Main.openTitleText": "ドキュメントを開いています", "DE.Controllers.Main.printTextText": "ドキュメント印刷中...", "DE.Controllers.Main.printTitleText": "ドキュメント印刷中", "DE.Controllers.Main.reloadButtonText": "ページを再読み込み", "DE.Controllers.Main.requestEditFailedMessageText": "この文書は他のユーザによって編集されています。後でもう一度お試しください。", "DE.Controllers.Main.requestEditFailedTitleText": "アクセスが拒否されました", "DE.Controllers.Main.saveErrorText": "ファイルを保存中にエラーが発生しました。", "DE.Controllers.Main.saveErrorTextDesktop": "このファイルは作成または保存できません。<br>考えられる理由は次のとおりです：<br>1. 閲覧のみのファイルです。<br>2. ファイルが他のユーザーによって編集されています。<br>3. ディスクが満杯か破損しています。", "DE.Controllers.Main.saveTextText": "ドキュメントを保存しています...", "DE.Controllers.Main.saveTitleText": "ドキュメントの保存中", "DE.Controllers.Main.scriptLoadError": "インターネット接続が遅いため、一部のコンポーネントをロードできませんでした。ページを再読み込みしてください。", "DE.Controllers.Main.sendMergeText": "マージを送信中...", "DE.Controllers.Main.sendMergeTitle": "マージを送信中", "DE.Controllers.Main.splitDividerErrorText": "行数は%1の除数になければなりません。", "DE.Controllers.Main.splitMaxColsErrorText": "列の数は%1より小さくなければなりません。", "DE.Controllers.Main.splitMaxRowsErrorText": "行数は%1より小さくなければなりません。", "DE.Controllers.Main.textAnonymous": "匿名者", "DE.Controllers.Main.textApplyAll": "全ての数式に適用する", "DE.Controllers.Main.textBuyNow": "ウェブサイトにアクセス", "DE.Controllers.Main.textChangesSaved": "全ての変更が保存されました", "DE.Controllers.Main.textClose": "閉じる", "DE.Controllers.Main.textCloseTip": "クリックでヒントを閉じる", "DE.Controllers.Main.textContactUs": "営業部に連絡する", "DE.Controllers.Main.textContinue": "続ける", "DE.Controllers.Main.textConvertEquation": "この数式は、サポートされなくなった古いバージョンの数式エディタで作成されました。 編集するには、方程式をOffice Math ML形式に変換します。<br>今すぐ変換しますか？", "DE.Controllers.Main.textCustomLoader": "ライセンス条項により、ローダーを変更する権利がないことにご注意ください。<br>見積もりについては、弊社営業部門にお問い合わせください。", "DE.Controllers.Main.textDisconnect": "接続が切断されました", "DE.Controllers.Main.textGuest": "ゲスト", "DE.Controllers.Main.textHasMacros": "ファイルには自動マクロが含まれています。<br>マクロを実行しますか？", "DE.Controllers.Main.textLearnMore": "詳細はこちら", "DE.Controllers.Main.textLoadingDocument": "ドキュメントを読み込んでいます", "DE.Controllers.Main.textLongName": "128文字未満の名前を入力してください。", "DE.Controllers.Main.textNoLicenseTitle": "ライセンス制限に達しました。", "DE.Controllers.Main.textPaidFeature": "有料機能", "DE.Controllers.Main.textReconnect": "接続が回復しました", "DE.Controllers.Main.textRemember": "すべてのファイルに自分の選択を記憶させる", "DE.Controllers.Main.textRememberMacros": "すべてのマクロに、この選択を記憶する", "DE.Controllers.Main.textRenameError": "ユーザー名は空にできません。", "DE.Controllers.Main.textRenameLabel": "コラボレーションに使用する名前を入力して下さい。", "DE.Controllers.Main.textRequestMacros": "マクロがURLに対してリクエストを行います。%1へのリクエストを許可しますか？", "DE.Controllers.Main.textShape": "図形", "DE.Controllers.Main.textStrict": "厳格モード", "DE.Controllers.Main.textText": "テキスト", "DE.Controllers.Main.textTryQuickPrint": "クイックプリントが選択されています。ドキュメント全体が、最後に選択したプリンタまたはデフォルトのプリンタで印刷されます。<br>続行しますか?", "DE.Controllers.Main.textTryUndoRedo": "高速共同編集モードでは、元に戻す/やり直し機能は無効になります。<br>「厳格モード」ボタンをクリックすると、他のユーザーの干渉を受けずにファイルを編集し、保存後に変更内容を送信する厳格共同編集モードに切り替わります。共同編集モードの切り替えは、エディタの詳細設定を使用して行うことができます。", "DE.Controllers.Main.textTryUndoRedoWarn": "高速共同編集モードでは、元に戻す/やり直し機能が無効になります。", "DE.Controllers.Main.textUndo": "元に戻す", "DE.Controllers.Main.titleLicenseExp": "ライセンスの有効期限が切れています", "DE.Controllers.Main.titleServerVersion": "編集者が更新されました", "DE.Controllers.Main.titleUpdateVersion": "バージョンが変更されました", "DE.Controllers.Main.txtAbove": "上", "DE.Controllers.Main.txtArt": "ここにテキストを入力", "DE.Controllers.Main.txtBasicShapes": "基本図形", "DE.Controllers.Main.txtBelow": "下", "DE.Controllers.Main.txtBookmarkError": "エラー！ブックマークが定義されていません。", "DE.Controllers.Main.txtButtons": "ボタン", "DE.Controllers.Main.txtCallouts": "吹き出し", "DE.Controllers.Main.txtCharts": "チャート", "DE.Controllers.Main.txtChoose": "アイテムを選択してください", "DE.Controllers.Main.txtClickToLoad": "クリックして画像を読み込む", "DE.Controllers.Main.txtCurrentDocument": "現在の文書", "DE.Controllers.Main.txtDiagramTitle": "チャートのタイトル", "DE.Controllers.Main.txtEditingMode": "編集モードを設定します...", "DE.Controllers.Main.txtEndOfFormula": "予期しない数式の終了", "DE.Controllers.Main.txtEnterDate": "日付を入力してください", "DE.Controllers.Main.txtErrorLoadHistory": "履歴の読み込みに失敗しました。", "DE.Controllers.Main.txtEvenPage": "偶数ページ", "DE.Controllers.Main.txtFiguredArrows": "図形矢印", "DE.Controllers.Main.txtFirstPage": "最初のページ", "DE.Controllers.Main.txtFooter": "フッター", "DE.Controllers.Main.txtFormulaNotInTable": "テーブルにない数式", "DE.Controllers.Main.txtHeader": "ヘッダー", "DE.Controllers.Main.txtHyperlink": "ハイパーリンク", "DE.Controllers.Main.txtIndTooLarge": "インデックスが大きすぎます", "DE.Controllers.Main.txtLines": "線", "DE.Controllers.Main.txtMainDocOnly": "エラー！メイン文書のみ。", "DE.Controllers.Main.txtMath": "数学", "DE.Controllers.Main.txtMissArg": "引数がありません", "DE.Controllers.Main.txtMissOperator": "演算子がありません　", "DE.Controllers.Main.txtNeedSynchronize": "更新があります", "DE.Controllers.Main.txtNone": "なし", "DE.Controllers.Main.txtNoTableOfContents": "ドキュメントに見出しがありません。 目次に表示されるように、テキストに見出しスタイルを適用ください。", "DE.Controllers.Main.txtNoTableOfFigures": "図表のエントリーはありません。", "DE.Controllers.Main.txtNoText": "エラー！文書に指定されたスタイルのテキストがありません。", "DE.Controllers.Main.txtNotInTable": "テーブルにありません", "DE.Controllers.Main.txtNotValidBookmark": "エラー！ブックマークの自己参照が無効です。", "DE.Controllers.Main.txtOddPage": "奇数ページ", "DE.Controllers.Main.txtOnPage": "ページで", "DE.Controllers.Main.txtRectangles": "矩形", "DE.Controllers.Main.txtSameAsPrev": "前と同じ", "DE.Controllers.Main.txtSection": "-セクション", "DE.Controllers.Main.txtSeries": "系列", "DE.Controllers.Main.txtShape_accentBorderCallout1": "線吹き出し１（枠付きと強調線）", "DE.Controllers.Main.txtShape_accentBorderCallout2": "線吹き出し２（枠付きと強調線）", "DE.Controllers.Main.txtShape_accentBorderCallout3": "線吹き出し３（枠付きと強調線）", "DE.Controllers.Main.txtShape_accentCallout1": "線吹き出し１（強調線）", "DE.Controllers.Main.txtShape_accentCallout2": "線吹き出し２（強調線）", "DE.Controllers.Main.txtShape_accentCallout3": "線吹き出し３（強調線）", "DE.Controllers.Main.txtShape_actionButtonBackPrevious": "［戻る］ボタン", "DE.Controllers.Main.txtShape_actionButtonBeginning": "［始めに戻る］ボタン", "DE.Controllers.Main.txtShape_actionButtonBlank": "空白ボタン", "DE.Controllers.Main.txtShape_actionButtonDocument": "文書ボタン", "DE.Controllers.Main.txtShape_actionButtonEnd": "［最後］ボタン", "DE.Controllers.Main.txtShape_actionButtonForwardNext": "［次へ］のボタン", "DE.Controllers.Main.txtShape_actionButtonHelp": "［ヘルプ］ボタン", "DE.Controllers.Main.txtShape_actionButtonHome": "ホームボタン", "DE.Controllers.Main.txtShape_actionButtonInformation": "［情報］ボタン", "DE.Controllers.Main.txtShape_actionButtonMovie": "［ムービー］ボタン", "DE.Controllers.Main.txtShape_actionButtonReturn": "［戻る］ボタン", "DE.Controllers.Main.txtShape_actionButtonSound": "「音」ボタン", "DE.Controllers.Main.txtShape_arc": "円弧", "DE.Controllers.Main.txtShape_bentArrow": "曲げ矢印", "DE.Controllers.Main.txtShape_bentConnector5": "カギ線コネクタ", "DE.Controllers.Main.txtShape_bentConnector5WithArrow": "カギ線矢印コネクター", "DE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "カギ線の二重矢印コネクタ", "DE.Controllers.Main.txtShape_bentUpArrow": "屈折矢印", "DE.Controllers.Main.txtShape_bevel": "斜角", "DE.Controllers.Main.txtShape_blockArc": "アーチ", "DE.Controllers.Main.txtShape_borderCallout1": "線吹き出し１　", "DE.Controllers.Main.txtShape_borderCallout2": "線吹き出し２", "DE.Controllers.Main.txtShape_borderCallout3": "線吹き出し３", "DE.Controllers.Main.txtShape_bracePair": "二重括弧", "DE.Controllers.Main.txtShape_callout1": "線吹き出し１（枠付き無し）", "DE.Controllers.Main.txtShape_callout2": "線吹き出し２（枠付き無し）", "DE.Controllers.Main.txtShape_callout3": "線吹き出し３（枠付き無し）", "DE.Controllers.Main.txtShape_can": "円筒", "DE.Controllers.Main.txtShape_chevron": "シェブロン", "DE.Controllers.Main.txtShape_chord": "コード", "DE.Controllers.Main.txtShape_circularArrow": "円弧の矢印", "DE.Controllers.Main.txtShape_cloud": "クラウド", "DE.Controllers.Main.txtShape_cloudCallout": "雲形吹き出し", "DE.Controllers.Main.txtShape_corner": "角", "DE.Controllers.Main.txtShape_cube": "立方体", "DE.Controllers.Main.txtShape_curvedConnector3": "曲線コネクタ", "DE.Controllers.Main.txtShape_curvedConnector3WithArrow": "曲線矢印コネクタ", "DE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "曲線の二重矢印コネクタ", "DE.Controllers.Main.txtShape_curvedDownArrow": "曲線の下向き矢印", "DE.Controllers.Main.txtShape_curvedLeftArrow": "曲線の左矢印", "DE.Controllers.Main.txtShape_curvedRightArrow": "曲線の右矢印", "DE.Controllers.Main.txtShape_curvedUpArrow": "曲線の上矢印", "DE.Controllers.Main.txtShape_decagon": "十角形", "DE.Controllers.Main.txtShape_diagStripe": "斜めストライプ", "DE.Controllers.Main.txtShape_diamond": "ダイヤモンド", "DE.Controllers.Main.txtShape_dodecagon": "12角形", "DE.Controllers.Main.txtShape_donut": "ドーナツグラフ", "DE.Controllers.Main.txtShape_doubleWave": "二重波", "DE.Controllers.Main.txtShape_downArrow": "下矢印", "DE.Controllers.Main.txtShape_downArrowCallout": "下矢印吹き出し", "DE.Controllers.Main.txtShape_ellipse": "楕円", "DE.Controllers.Main.txtShape_ellipseRibbon": "下に湾曲したリボン", "DE.Controllers.Main.txtShape_ellipseRibbon2": "上に湾曲したリボン", "DE.Controllers.Main.txtShape_flowChartAlternateProcess": "フローチャート：代替処理", "DE.Controllers.Main.txtShape_flowChartCollate": "フローチャート：照合", "DE.Controllers.Main.txtShape_flowChartConnector": "フローチャート：結合子", "DE.Controllers.Main.txtShape_flowChartDecision": "フローチャート：判断", "DE.Controllers.Main.txtShape_flowChartDelay": "フローチャート：遅延", "DE.Controllers.Main.txtShape_flowChartDisplay": "フローチャート：表示", "DE.Controllers.Main.txtShape_flowChartDocument": "フローチャート：文書", "DE.Controllers.Main.txtShape_flowChartExtract": "フローチャート：抜き出し", "DE.Controllers.Main.txtShape_flowChartInputOutput": "フローチャート：データ", "DE.Controllers.Main.txtShape_flowChartInternalStorage": "フローチャート：内部ストレージ", "DE.Controllers.Main.txtShape_flowChartMagneticDisk": "フローチャート：磁気ディスク", "DE.Controllers.Main.txtShape_flowChartMagneticDrum": "フローチャート：直接アクセスストレージ", "DE.Controllers.Main.txtShape_flowChartMagneticTape": "フローチャート：順次アクセス記憶", "DE.Controllers.Main.txtShape_flowChartManualInput": "フローチャート：手操作入力", "DE.Controllers.Main.txtShape_flowChartManualOperation": "フローチャート：手作業", "DE.Controllers.Main.txtShape_flowChartMerge": "フローチャート：融合", "DE.Controllers.Main.txtShape_flowChartMultidocument": "フローチャート：複数文書", "DE.Controllers.Main.txtShape_flowChartOffpageConnector": "フローチャート：他ページ結合子", "DE.Controllers.Main.txtShape_flowChartOnlineStorage": "フローチャート：保存されたデータ", "DE.Controllers.Main.txtShape_flowChartOr": "フローチャート：論理和", "DE.Controllers.Main.txtShape_flowChartPredefinedProcess": "フローチャート：事前定義されたプロセス", "DE.Controllers.Main.txtShape_flowChartPreparation": "フローチャート：準備", "DE.Controllers.Main.txtShape_flowChartProcess": "フローチャート：処理", "DE.Controllers.Main.txtShape_flowChartPunchedCard": "フローチャート：カード", "DE.Controllers.Main.txtShape_flowChartPunchedTape": "フローチャート：せん孔テープ", "DE.Controllers.Main.txtShape_flowChartSort": "フローチャート：並べ替え", "DE.Controllers.Main.txtShape_flowChartSummingJunction": "フローチャート：和接合", "DE.Controllers.Main.txtShape_flowChartTerminator": "フローチャート：ターミネーター", "DE.Controllers.Main.txtShape_foldedCorner": "折り曲げコーナー", "DE.Controllers.Main.txtShape_frame": "フレーム", "DE.Controllers.Main.txtShape_halfFrame": "半フレーム", "DE.Controllers.Main.txtShape_heart": "ハート", "DE.Controllers.Main.txtShape_heptagon": "七角形", "DE.Controllers.Main.txtShape_hexagon": "六角形", "DE.Controllers.Main.txtShape_homePlate": "五角形", "DE.Controllers.Main.txtShape_horizontalScroll": "水平スクロール", "DE.Controllers.Main.txtShape_irregularSeal1": "爆発　１", "DE.Controllers.Main.txtShape_irregularSeal2": "爆発　２", "DE.Controllers.Main.txtShape_leftArrow": "左矢印", "DE.Controllers.Main.txtShape_leftArrowCallout": "左矢印吹き出し", "DE.Controllers.Main.txtShape_leftBrace": "左中括弧", "DE.Controllers.Main.txtShape_leftBracket": "左括弧", "DE.Controllers.Main.txtShape_leftRightArrow": "左右矢印", "DE.Controllers.Main.txtShape_leftRightArrowCallout": "左右矢印吹き出し", "DE.Controllers.Main.txtShape_leftRightUpArrow": "三方向矢印", "DE.Controllers.Main.txtShape_leftUpArrow": "左上矢印", "DE.Controllers.Main.txtShape_lightningBolt": "稲妻", "DE.Controllers.Main.txtShape_line": "線", "DE.Controllers.Main.txtShape_lineWithArrow": "矢印", "DE.Controllers.Main.txtShape_lineWithTwoArrows": "二重矢印", "DE.Controllers.Main.txtShape_mathDivide": "分割", "DE.Controllers.Main.txtShape_mathEqual": "イコール", "DE.Controllers.Main.txtShape_mathMinus": "マイナス", "DE.Controllers.Main.txtShape_mathMultiply": "乗算する", "DE.Controllers.Main.txtShape_mathNotEqual": "等しくない", "DE.Controllers.Main.txtShape_mathPlus": "プラス", "DE.Controllers.Main.txtShape_moon": "月形", "DE.Controllers.Main.txtShape_noSmoking": "\"禁止\"マーク", "DE.Controllers.Main.txtShape_notchedRightArrow": "切り欠き右矢印", "DE.Controllers.Main.txtShape_octagon": "八角形", "DE.Controllers.Main.txtShape_parallelogram": "平行四辺形", "DE.Controllers.Main.txtShape_pentagon": "五角形", "DE.Controllers.Main.txtShape_pie": "円グラフ", "DE.Controllers.Main.txtShape_plaque": "署名する", "DE.Controllers.Main.txtShape_plus": "プラス", "DE.Controllers.Main.txtShape_polyline1": "走り書き", "DE.Controllers.Main.txtShape_polyline2": "フリーフォーム", "DE.Controllers.Main.txtShape_quadArrow": "四方向矢印", "DE.Controllers.Main.txtShape_quadArrowCallout": "四方向矢印の吹き出し", "DE.Controllers.Main.txtShape_rect": "矩形", "DE.Controllers.Main.txtShape_ribbon": "下リボン", "DE.Controllers.Main.txtShape_ribbon2": "上リボン", "DE.Controllers.Main.txtShape_rightArrow": "右矢印", "DE.Controllers.Main.txtShape_rightArrowCallout": "右矢印吹き出し", "DE.Controllers.Main.txtShape_rightBrace": "右中括弧", "DE.Controllers.Main.txtShape_rightBracket": "右大括弧", "DE.Controllers.Main.txtShape_round1Rect": "1つの角を丸めた四角形", "DE.Controllers.Main.txtShape_round2DiagRect": "角丸長方形", "DE.Controllers.Main.txtShape_round2SameRect": "同辺角丸四角形", "DE.Controllers.Main.txtShape_roundRect": "角丸長方形", "DE.Controllers.Main.txtShape_rtTriangle": "直角三角形", "DE.Controllers.Main.txtShape_smileyFace": "笑顔のマーク", "DE.Controllers.Main.txtShape_snip1Rect": "1つの角を切り取った四角形", "DE.Controllers.Main.txtShape_snip2DiagRect": "対角する2つの角を切り取った四角形", "DE.Controllers.Main.txtShape_snip2SameRect": "片側の2つの角を切り取った四角形", "DE.Controllers.Main.txtShape_snipRoundRect": "1つの角を切り取り1つの角を丸めた四角形", "DE.Controllers.Main.txtShape_spline": "曲線", "DE.Controllers.Main.txtShape_star10": "10ポイントスター", "DE.Controllers.Main.txtShape_star12": "12ポイントスター", "DE.Controllers.Main.txtShape_star16": "16ポイントスター", "DE.Controllers.Main.txtShape_star24": "24ポイントスター", "DE.Controllers.Main.txtShape_star32": "32ポイントスター", "DE.Controllers.Main.txtShape_star4": "4ポイントスター", "DE.Controllers.Main.txtShape_star5": "5ポイントスター", "DE.Controllers.Main.txtShape_star6": "6ポイントスター", "DE.Controllers.Main.txtShape_star7": "7ポイントスター", "DE.Controllers.Main.txtShape_star8": "8ポイントスター", "DE.Controllers.Main.txtShape_stripedRightArrow": "ストライプの右矢印", "DE.Controllers.Main.txtShape_sun": "太陽形", "DE.Controllers.Main.txtShape_teardrop": "涙の滴", "DE.Controllers.Main.txtShape_textRect": "テキストボックス", "DE.Controllers.Main.txtShape_trapezoid": "台形", "DE.Controllers.Main.txtShape_triangle": "三角形", "DE.Controllers.Main.txtShape_upArrow": "上矢印", "DE.Controllers.Main.txtShape_upArrowCallout": "上矢印吹き出し", "DE.Controllers.Main.txtShape_upDownArrow": "上下矢印", "DE.Controllers.Main.txtShape_uturnArrow": "U形矢印", "DE.Controllers.Main.txtShape_verticalScroll": "縦スクロール", "DE.Controllers.Main.txtShape_wave": "波", "DE.Controllers.Main.txtShape_wedgeEllipseCallout": "円形吹き出し", "DE.Controllers.Main.txtShape_wedgeRectCallout": "矩形の吹き出し", "DE.Controllers.Main.txtShape_wedgeRoundRectCallout": "角丸長方形の吹き出し", "DE.Controllers.Main.txtStarsRibbons": "スター＆リボン", "DE.Controllers.Main.txtStyle_Caption": "キャプション", "DE.Controllers.Main.txtStyle_endnote_text": "文末脚注のテキスト", "DE.Controllers.Main.txtStyle_footnote_text": "脚注テキスト", "DE.Controllers.Main.txtStyle_Heading_1": "見出し１", "DE.Controllers.Main.txtStyle_Heading_2": "見出し２", "DE.Controllers.Main.txtStyle_Heading_3": "見出し３", "DE.Controllers.Main.txtStyle_Heading_4": "見出し４", "DE.Controllers.Main.txtStyle_Heading_5": "見出し５", "DE.Controllers.Main.txtStyle_Heading_6": "見出し６", "DE.Controllers.Main.txtStyle_Heading_7": "見出し７", "DE.Controllers.Main.txtStyle_Heading_8": "見出し８", "DE.Controllers.Main.txtStyle_Heading_9": "見出し９", "DE.Controllers.Main.txtStyle_Intense_Quote": "強調表示された引用", "DE.Controllers.Main.txtStyle_List_Paragraph": "リスト段落", "DE.Controllers.Main.txtStyle_No_Spacing": "間隔なし", "DE.Controllers.Main.txtStyle_Normal": "正常", "DE.Controllers.Main.txtStyle_Quote": "引用", "DE.Controllers.Main.txtStyle_Subtitle": "サブタイトル", "DE.Controllers.Main.txtStyle_Title": "タイトル", "DE.Controllers.Main.txtSyntaxError": "構文エラー", "DE.Controllers.Main.txtTableInd": "テーブルインデックスをゼロにすることはできません", "DE.Controllers.Main.txtTableOfContents": "目次", "DE.Controllers.Main.txtTableOfFigures": "図表", "DE.Controllers.Main.txtTOCHeading": "目次 見出し", "DE.Controllers.Main.txtTooLarge": "数値が大きすぎて書式設定できません。", "DE.Controllers.Main.txtTypeEquation": "こちらに数式を入力してください", "DE.Controllers.Main.txtUndefBookmark": "未定義のブックマーク", "DE.Controllers.Main.txtXAxis": "X 軸", "DE.Controllers.Main.txtYAxis": "Y軸", "DE.Controllers.Main.txtZeroDivide": "ゼロ除算", "DE.Controllers.Main.unknownErrorText": "不明なエラー", "DE.Controllers.Main.unsupportedBrowserErrorText": "お使いのブラウザはサポートされていません。", "DE.Controllers.Main.uploadDocExtMessage": "不明な文書形式", "DE.Controllers.Main.uploadDocFileCountMessage": "アップロードされた文書がありません。", "DE.Controllers.Main.uploadDocSizeMessage": "文書の最大サイズ制限を超えました", "DE.Controllers.Main.uploadImageExtMessage": "不明な画像形式", "DE.Controllers.Main.uploadImageFileCountMessage": "画像のアップロードはありません。", "DE.Controllers.Main.uploadImageSizeMessage": "画像サイズの上限を超えました。サイズの上限は25MBです。", "DE.Controllers.Main.uploadImageTextText": "画像のアップロード中...", "DE.Controllers.Main.uploadImageTitleText": "画像のアップロード中", "DE.Controllers.Main.waitText": "少々お待ちください...", "DE.Controllers.Main.warnBrowserIE9": "このアプリケーションはIE9では低機能です。IE10以上のバージョンをご使用ください。", "DE.Controllers.Main.warnBrowserZoom": "お使いのブラウザの現在のZoomの設定は完全にはサポートされていません。Ctrl+0を押して、デフォルトのZoomにリセットしてください。", "DE.Controllers.Main.warnLicenseExceeded": "％1エディターへの同時接続の制限に達しました。 このドキュメントは表示専用で開かれます。<br>詳細については、管理者にお問い合わせください。", "DE.Controllers.Main.warnLicenseExp": "ライセンスの有効期限が切れています。<br>ライセンスを更新してページをリロードしてください。", "DE.Controllers.Main.warnLicenseLimitedNoAccess": "ライセンスの有効期限が切れています。<br>ドキュメント編集機能にアクセスできません。<br>管理者にご連絡ください。", "DE.Controllers.Main.warnLicenseLimitedRenewed": "ライセンスを更新する必要があります。<br>ドキュメント編集機能へのアクセスが制限されています。<br>フルアクセスを取得するには、管理者にご連絡ください。", "DE.Controllers.Main.warnLicenseUsersExceeded": "％1エディターのユーザー制限に達しました。 詳細については、管理者にお問い合わせください。", "DE.Controllers.Main.warnNoLicense": "％1エディターへの同時接続の制限に達しました。 このドキュメントは閲覧のみを目的として開かれます。<br>個人的なアップグレード条件については、％1セールスチームにお問い合わせください。", "DE.Controllers.Main.warnNoLicenseUsers": "％1エディターのユーザー制限に達しました。 個人的なアップグレード条件については、％1セールスチームにお問い合わせください。", "DE.Controllers.Main.warnProcessRightsChange": "ファイルを編集する権限を拒否されています。", "DE.Controllers.Navigation.txtBeginning": "文書の先頭", "DE.Controllers.Navigation.txtGotoBeginning": "文書の先頭に移動する", "DE.Controllers.Print.textMarginsLast": "最後に適用した設定", "DE.Controllers.Print.txtCustom": "ユーザー設定", "DE.Controllers.Print.txtPrintRangeInvalid": "無効な印刷範囲", "DE.Controllers.Print.txtPrintRangeSingleRange": "ページ番号のみ、またはページ範囲のみを入力してください（例: 5-12）。または、PDFに印刷することもできます。", "DE.Controllers.Search.notcriticalErrorTitle": " 警告", "DE.Controllers.Search.textNoTextFound": "検索データが見つかりませんでした。他の検索設定を選択してください。", "DE.Controllers.Search.textReplaceSkipped": "置換が行われました。スキップされた発生回数は{0}です。", "DE.Controllers.Search.textReplaceSuccess": "検索が実行されました。{0}発生が置換されました。", "DE.Controllers.Search.warnReplaceString": "{0}は、「置換」ボックスで有効な特殊文字ではありません。", "DE.Controllers.Statusbar.textDisconnect": "<b>接続が切断されました</b><br>接続を試みています。接続設定を確認してください。", "DE.Controllers.Statusbar.textHasChanges": "新しい変更点を追記しました", "DE.Controllers.Statusbar.textSetTrackChanges": "変更履歴モードで編集中です", "DE.Controllers.Statusbar.textTrackChanges": "ドキュメントが変更履歴モードが有効な状態で開かれています", "DE.Controllers.Statusbar.tipReview": "変更履歴", "DE.Controllers.Statusbar.zoomText": "ズーム{0}%", "DE.Controllers.Toolbar.confirmAddFontName": "保存しようとしているフォントを現在のデバイスで使用することができません。<br>システムフォントを使って、テキストのスタイルが表示されます。利用可能になったとき、保存されたフォントが適用されます。<br>続行しますか。", "DE.Controllers.Toolbar.dataUrl": "データのURLを貼り付け", "DE.Controllers.Toolbar.notcriticalErrorTitle": "警告", "DE.Controllers.Toolbar.textAccent": "アクセントカラー", "DE.Controllers.Toolbar.textBracket": "括弧", "DE.Controllers.Toolbar.textEmptyImgUrl": "画像のURLを指定する必要があります。", "DE.Controllers.Toolbar.textEmptyMMergeUrl": "URLを指定してください。", "DE.Controllers.Toolbar.textFontSizeErr": "入力された値が正しくありません。<br>1〜300の数値を入力してください。", "DE.Controllers.Toolbar.textFraction": "分数", "DE.Controllers.Toolbar.textFunction": "関数", "DE.Controllers.Toolbar.textGroup": "グループ", "DE.Controllers.Toolbar.textInsert": "挿入する", "DE.Controllers.Toolbar.textIntegral": "積分", "DE.Controllers.Toolbar.textLargeOperator": "大型演算子", "DE.Controllers.Toolbar.textLimitAndLog": "制限と対数", "DE.Controllers.Toolbar.textMatrix": "行列", "DE.Controllers.Toolbar.textOperator": "演算子", "DE.Controllers.Toolbar.textRadical": "ラジカル", "DE.Controllers.Toolbar.textRecentlyUsed": "最近使用された", "DE.Controllers.Toolbar.textScript": "スクリプト", "DE.Controllers.Toolbar.textSymbols": "記号", "DE.Controllers.Toolbar.textTabForms": "フォーム", "DE.Controllers.Toolbar.textWarning": "警告", "DE.Controllers.Toolbar.txtAccent_Accent": "アキュート", "DE.Controllers.Toolbar.txtAccent_ArrowD": "左右双方向矢印 (上)", "DE.Controllers.Toolbar.txtAccent_ArrowL": "左に矢印 (上)", "DE.Controllers.Toolbar.txtAccent_ArrowR": "右向き矢印 (上)", "DE.Controllers.Toolbar.txtAccent_Bar": "バー", "DE.Controllers.Toolbar.txtAccent_BarBot": "アンダーバー", "DE.Controllers.Toolbar.txtAccent_BarTop": "オーバーライン", "DE.Controllers.Toolbar.txtAccent_BorderBox": "四角囲み数式 (プレースホルダ付き)", "DE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "四角囲み数式 (例)", "DE.Controllers.Toolbar.txtAccent_Check": "チェック", "DE.Controllers.Toolbar.txtAccent_CurveBracketBot": "下括弧", "DE.Controllers.Toolbar.txtAccent_CurveBracketTop": "上括弧", "DE.Controllers.Toolbar.txtAccent_Custom_1": "ベクトルA", "DE.Controllers.Toolbar.txtAccent_Custom_2": "オーバーライン付き ABC", "DE.Controllers.Toolbar.txtAccent_Custom_3": "x XORと上線", "DE.Controllers.Toolbar.txtAccent_DDDot": "トリプルドット", "DE.Controllers.Toolbar.txtAccent_DDot": "複付点", "DE.Controllers.Toolbar.txtAccent_Dot": "点", "DE.Controllers.Toolbar.txtAccent_DoubleBar": "二重オーバーライン", "DE.Controllers.Toolbar.txtAccent_Grave": "グレイヴ", "DE.Controllers.Toolbar.txtAccent_GroupBot": "グループ化文字(下)", "DE.Controllers.Toolbar.txtAccent_GroupTop": "グループ化文字(上)", "DE.Controllers.Toolbar.txtAccent_HarpoonL": "左半矢印(上)", "DE.Controllers.Toolbar.txtAccent_HarpoonR": "右向き半矢印 (上)", "DE.Controllers.Toolbar.txtAccent_Hat": "ハット", "DE.Controllers.Toolbar.txtAccent_Smile": "ブレーヴェ", "DE.Controllers.Toolbar.txtAccent_Tilde": "チルダ", "DE.Controllers.Toolbar.txtBracket_Angle": "括弧", "DE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "括弧と区切り線", "DE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "括弧と区切り線", "DE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "単一括弧", "DE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "単一括弧", "DE.Controllers.Toolbar.txtBracket_Curve": "括弧", "DE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "括弧と区切り線", "DE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "単一括弧", "DE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "単一括弧", "DE.Controllers.Toolbar.txtBracket_Custom_1": "場合分け(条件2つ)", "DE.Controllers.Toolbar.txtBracket_Custom_2": "場合分け (条件 3 つ)", "DE.Controllers.Toolbar.txtBracket_Custom_3": "縦並びオブジェクト", "DE.Controllers.Toolbar.txtBracket_Custom_4": "縦並びオブジェクト", "DE.Controllers.Toolbar.txtBracket_Custom_5": "場合分けの例", "DE.Controllers.Toolbar.txtBracket_Custom_6": "二項係数", "DE.Controllers.Toolbar.txtBracket_Custom_7": "二項係数", "DE.Controllers.Toolbar.txtBracket_Line": "括弧", "DE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "単一括弧", "DE.Controllers.Toolbar.txtBracket_Line_OpenNone": "単一括弧", "DE.Controllers.Toolbar.txtBracket_LineDouble": "括弧", "DE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "単一括弧", "DE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "単一括弧", "DE.Controllers.Toolbar.txtBracket_LowLim": "括弧", "DE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "単一括弧", "DE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "単一括弧", "DE.Controllers.Toolbar.txtBracket_Round": "括弧", "DE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "括弧と区切り線", "DE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "単一括弧", "DE.Controllers.Toolbar.txtBracket_Round_OpenNone": "単一括弧", "DE.Controllers.Toolbar.txtBracket_Square": "括弧", "DE.Controllers.Toolbar.txtBracket_Square_CloseClose": "括弧", "DE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "括弧", "DE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "単一括弧", "DE.Controllers.Toolbar.txtBracket_Square_OpenNone": "単一括弧", "DE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "括弧", "DE.Controllers.Toolbar.txtBracket_SquareDouble": "括弧", "DE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "単一括弧", "DE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "単一括弧", "DE.Controllers.Toolbar.txtBracket_UppLim": "括弧", "DE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "単一括弧", "DE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "単一括弧", "DE.Controllers.Toolbar.txtFractionDiagonal": "分数 (斜め)", "DE.Controllers.Toolbar.txtFractionDifferential_1": "微分", "DE.Controllers.Toolbar.txtFractionDifferential_2": "微分", "DE.Controllers.Toolbar.txtFractionDifferential_3": "微分", "DE.Controllers.Toolbar.txtFractionDifferential_4": "微分", "DE.Controllers.Toolbar.txtFractionHorizontal": "分数 (横)", "DE.Controllers.Toolbar.txtFractionPi_2": "Pi/2", "DE.Controllers.Toolbar.txtFractionSmall": "分数 (小)", "DE.Controllers.Toolbar.txtFractionVertical": "分数 (縦)", "DE.Controllers.Toolbar.txtFunction_1_Cos": "逆余弦関数", "DE.Controllers.Toolbar.txtFunction_1_Cosh": "双曲線逆余弦関数", "DE.Controllers.Toolbar.txtFunction_1_Cot": "逆余接関数", "DE.Controllers.Toolbar.txtFunction_1_Coth": "双曲線逆共接関数", "DE.Controllers.Toolbar.txtFunction_1_Csc": "逆余割関数", "DE.Controllers.Toolbar.txtFunction_1_Csch": "双曲線逆余割関数", "DE.Controllers.Toolbar.txtFunction_1_Sec": "逆正割関数", "DE.Controllers.Toolbar.txtFunction_1_Sech": "双曲線逆正割関数", "DE.Controllers.Toolbar.txtFunction_1_Sin": "逆正弦関数", "DE.Controllers.Toolbar.txtFunction_1_Sinh": "双曲線逆正弦関数", "DE.Controllers.Toolbar.txtFunction_1_Tan": "逆正接関数", "DE.Controllers.Toolbar.txtFunction_1_Tanh": "双曲線逆正接関数", "DE.Controllers.Toolbar.txtFunction_Cos": "余弦関数", "DE.Controllers.Toolbar.txtFunction_Cosh": "双曲線余弦関数", "DE.Controllers.Toolbar.txtFunction_Cot": "余接関数", "DE.Controllers.Toolbar.txtFunction_Coth": "双曲線余接関数", "DE.Controllers.Toolbar.txtFunction_Csc": "余割関数\t", "DE.Controllers.Toolbar.txtFunction_Csch": "双曲線余割関数", "DE.Controllers.Toolbar.txtFunction_Custom_1": "Sin θ", "DE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "DE.Controllers.Toolbar.txtFunction_Custom_3": "正接数式", "DE.Controllers.Toolbar.txtFunction_Sec": "正割関数", "DE.Controllers.Toolbar.txtFunction_Sech": "双曲線正割", "DE.Controllers.Toolbar.txtFunction_Sin": "正弦関数", "DE.Controllers.Toolbar.txtFunction_Sinh": "双曲線正弦", "DE.Controllers.Toolbar.txtFunction_Tan": "正接関数", "DE.Controllers.Toolbar.txtFunction_Tanh": "双曲線正接関数", "DE.Controllers.Toolbar.txtIntegral": "積分", "DE.Controllers.Toolbar.txtIntegral_dtheta": "微分シータ", "DE.Controllers.Toolbar.txtIntegral_dx": "微分x", "DE.Controllers.Toolbar.txtIntegral_dy": "微分y", "DE.Controllers.Toolbar.txtIntegralCenterSubSup": "積分", "DE.Controllers.Toolbar.txtIntegralDouble": "二重積分", "DE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "二重積分", "DE.Controllers.Toolbar.txtIntegralDoubleSubSup": "二重積分", "DE.Controllers.Toolbar.txtIntegralOriented": "周回積分", "DE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "周回積分", "DE.Controllers.Toolbar.txtIntegralOrientedDouble": "面積分", "DE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "面積分", "DE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "面積分", "DE.Controllers.Toolbar.txtIntegralOrientedSubSup": "周回積分", "DE.Controllers.Toolbar.txtIntegralOrientedTriple": "体積積分", "DE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "体積積分", "DE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "体積積分", "DE.Controllers.Toolbar.txtIntegralSubSup": "積分", "DE.Controllers.Toolbar.txtIntegralTriple": "三重積分", "DE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "三重積分", "DE.Controllers.Toolbar.txtIntegralTripleSubSup": "三重積分", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction": "くさび形", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "くさび形", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "くさび形", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "くさび形", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "くさび形", "DE.Controllers.Toolbar.txtLargeOperator_CoProd": "共同製品", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "共同製品", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "共同製品", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "共同製品", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "共同製品", "DE.Controllers.Toolbar.txtLargeOperator_Custom_1": "合計", "DE.Controllers.Toolbar.txtLargeOperator_Custom_2": "合計", "DE.Controllers.Toolbar.txtLargeOperator_Custom_3": "合計", "DE.Controllers.Toolbar.txtLargeOperator_Custom_4": "製品", "DE.Controllers.Toolbar.txtLargeOperator_Custom_5": "和集合", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction": "V字形", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "V字形", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "V字形", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "V字形", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "V字形", "DE.Controllers.Toolbar.txtLargeOperator_Intersection": "交点", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "交点", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "交点", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "交点", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "交点", "DE.Controllers.Toolbar.txtLargeOperator_Prod": "製品", "DE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "製品", "DE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "製品", "DE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "製品", "DE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "製品", "DE.Controllers.Toolbar.txtLargeOperator_Sum": "合計", "DE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "合計", "DE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "合計", "DE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "合計", "DE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "合計", "DE.Controllers.Toolbar.txtLargeOperator_Union": "和集合", "DE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "和集合", "DE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "和集合", "DE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "和集合", "DE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "和集合", "DE.Controllers.Toolbar.txtLimitLog_Custom_1": "制限の例", "DE.Controllers.Toolbar.txtLimitLog_Custom_2": "最大値の例", "DE.Controllers.Toolbar.txtLimitLog_Lim": "限度", "DE.Controllers.Toolbar.txtLimitLog_Ln": "自然対数", "DE.Controllers.Toolbar.txtLimitLog_Log": "対数", "DE.Controllers.Toolbar.txtLimitLog_LogBase": "対数", "DE.Controllers.Toolbar.txtLimitLog_Max": "最大", "DE.Controllers.Toolbar.txtLimitLog_Min": "最小", "DE.Controllers.Toolbar.txtMarginsH": "指定されたページの高さ対して、上下の余白が大きすぎます。", "DE.Controllers.Toolbar.txtMarginsW": "ページ幅に対して左右の余白が広すぎます。", "DE.Controllers.Toolbar.txtMatrix_1_2": "1x2空行列", "DE.Controllers.Toolbar.txtMatrix_1_3": "1x3空行列", "DE.Controllers.Toolbar.txtMatrix_2_1": "2x1 空行列", "DE.Controllers.Toolbar.txtMatrix_2_2": "2x2 空行列", "DE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "括弧付き空行列", "DE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "括弧付き空行列", "DE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "括弧付き空行列", "DE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "括弧付き空行列", "DE.Controllers.Toolbar.txtMatrix_2_3": "2x3 空行列", "DE.Controllers.Toolbar.txtMatrix_3_1": "3x1 空行列", "DE.Controllers.Toolbar.txtMatrix_3_2": "3x2 空行列", "DE.Controllers.Toolbar.txtMatrix_3_3": "3x3 空行列", "DE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "基準線点", "DE.Controllers.Toolbar.txtMatrix_Dots_Center": "ミッドラインドット", "DE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "斜めドット", "DE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "縦向きドット", "DE.Controllers.Toolbar.txtMatrix_Flat_Round": "疎行列", "DE.Controllers.Toolbar.txtMatrix_Flat_Square": "疎行列", "DE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2 単位行列", "DE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "3x3 単位行列", "DE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3 単位行列", "DE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 単位行列", "DE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "左右双方向矢印 (下)", "DE.Controllers.Toolbar.txtOperator_ArrowD_Top": "左右双方向矢印 (上)", "DE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "左に矢印 (下)", "DE.Controllers.Toolbar.txtOperator_ArrowL_Top": "左に矢印 (上)", "DE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "右向き矢印 (下)", "DE.Controllers.Toolbar.txtOperator_ArrowR_Top": "右向き矢印 (上)", "DE.Controllers.Toolbar.txtOperator_ColonEquals": "コロンイコール", "DE.Controllers.Toolbar.txtOperator_Custom_1": "導出", "DE.Controllers.Toolbar.txtOperator_Custom_2": "デルタ収量", "DE.Controllers.Toolbar.txtOperator_Definition": "定義上等しい", "DE.Controllers.Toolbar.txtOperator_DeltaEquals": "デルタ付き等号", "DE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "左右双方向矢印 (下)", "DE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "左右双方向矢印 (上)", "DE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "左に矢印 (下)", "DE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "左に矢印 (上)", "DE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "右向き矢印 (下)", "DE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "右向き矢印 (上)", "DE.Controllers.Toolbar.txtOperator_EqualsEquals": "イコールイコール", "DE.Controllers.Toolbar.txtOperator_MinusEquals": "マイナスイコール", "DE.Controllers.Toolbar.txtOperator_PlusEquals": "プラスイコール", "DE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "によって測定", "DE.Controllers.Toolbar.txtRadicalCustom_1": "ラジカル", "DE.Controllers.Toolbar.txtRadicalCustom_2": "ラジカル", "DE.Controllers.Toolbar.txtRadicalRoot_2": "次数付き平方根", "DE.Controllers.Toolbar.txtRadicalRoot_3": "立方根", "DE.Controllers.Toolbar.txtRadicalRoot_n": "度付きラジカル", "DE.Controllers.Toolbar.txtRadicalSqrt": "平方根", "DE.Controllers.Toolbar.txtScriptCustom_1": "スクリプト", "DE.Controllers.Toolbar.txtScriptCustom_2": "スクリプト", "DE.Controllers.Toolbar.txtScriptCustom_3": "スクリプト", "DE.Controllers.Toolbar.txtScriptCustom_4": "スクリプト", "DE.Controllers.Toolbar.txtScriptSub": "下付き文字", "DE.Controllers.Toolbar.txtScriptSubSup": "下付き文字 - 上付き文字", "DE.Controllers.Toolbar.txtScriptSubSupLeft": "左下付き文字 - 上付き文字", "DE.Controllers.Toolbar.txtScriptSup": "上付き文字", "DE.Controllers.Toolbar.txtSymbol_about": "約", "DE.Controllers.Toolbar.txtSymbol_additional": "補数", "DE.Controllers.Toolbar.txtSymbol_aleph": "アレフ", "DE.Controllers.Toolbar.txtSymbol_alpha": "アルファ", "DE.Controllers.Toolbar.txtSymbol_approx": "にほぼ等しい", "DE.Controllers.Toolbar.txtSymbol_ast": "アスタリスク", "DE.Controllers.Toolbar.txtSymbol_beta": "ベータ", "DE.Controllers.Toolbar.txtSymbol_beth": "ベート", "DE.Controllers.Toolbar.txtSymbol_bullet": "箇条書きの演算子", "DE.Controllers.Toolbar.txtSymbol_cap": "交点", "DE.Controllers.Toolbar.txtSymbol_cbrt": "立方根", "DE.Controllers.Toolbar.txtSymbol_cdots": "水平中央の省略記号", "DE.Controllers.Toolbar.txtSymbol_celsius": "摂氏", "DE.Controllers.Toolbar.txtSymbol_chi": "カイ", "DE.Controllers.Toolbar.txtSymbol_cong": "にほぼ等しい", "DE.Controllers.Toolbar.txtSymbol_cup": "和集合", "DE.Controllers.Toolbar.txtSymbol_ddots": "下右斜めの省略記号", "DE.Controllers.Toolbar.txtSymbol_degree": "度", "DE.Controllers.Toolbar.txtSymbol_delta": "デルタ", "DE.Controllers.Toolbar.txtSymbol_div": "除算記号", "DE.Controllers.Toolbar.txtSymbol_downarrow": "下矢印", "DE.Controllers.Toolbar.txtSymbol_emptyset": "空集合", "DE.Controllers.Toolbar.txtSymbol_epsilon": "イプシロン", "DE.Controllers.Toolbar.txtSymbol_equals": "イコール", "DE.Controllers.Toolbar.txtSymbol_equiv": "と同一", "DE.Controllers.Toolbar.txtSymbol_eta": "エータ", "DE.Controllers.Toolbar.txtSymbol_exists": "存在します\t", "DE.Controllers.Toolbar.txtSymbol_factorial": "階乗", "DE.Controllers.Toolbar.txtSymbol_fahrenheit": "華氏", "DE.Controllers.Toolbar.txtSymbol_forall": "全てに", "DE.Controllers.Toolbar.txtSymbol_gamma": "ガンマ", "DE.Controllers.Toolbar.txtSymbol_geq": "より大きいか等しい", "DE.Controllers.Toolbar.txtSymbol_gg": "よりもはるかに大きい", "DE.Controllers.Toolbar.txtSymbol_greater": "より大きい", "DE.Controllers.Toolbar.txtSymbol_in": "要素", "DE.Controllers.Toolbar.txtSymbol_inc": "増分", "DE.Controllers.Toolbar.txtSymbol_infinity": "無限", "DE.Controllers.Toolbar.txtSymbol_iota": "イオタ", "DE.Controllers.Toolbar.txtSymbol_kappa": "カッパ", "DE.Controllers.Toolbar.txtSymbol_lambda": "ラムダ", "DE.Controllers.Toolbar.txtSymbol_leftarrow": "左矢印", "DE.Controllers.Toolbar.txtSymbol_leftrightarrow": "左右矢印", "DE.Controllers.Toolbar.txtSymbol_leq": "より小さいか等しい", "DE.Controllers.Toolbar.txtSymbol_less": "より小さい", "DE.Controllers.Toolbar.txtSymbol_ll": "よりはるかに小さい", "DE.Controllers.Toolbar.txtSymbol_minus": "マイナス", "DE.Controllers.Toolbar.txtSymbol_mp": "マイナスプラス\t", "DE.Controllers.Toolbar.txtSymbol_mu": "ミュー", "DE.Controllers.Toolbar.txtSymbol_nabla": "ナブラ", "DE.Controllers.Toolbar.txtSymbol_neq": "と等しくない", "DE.Controllers.Toolbar.txtSymbol_ni": "メンバーとして含む", "DE.Controllers.Toolbar.txtSymbol_not": "否定記号", "DE.Controllers.Toolbar.txtSymbol_notexists": "存在しません", "DE.Controllers.Toolbar.txtSymbol_nu": "ニュー", "DE.Controllers.Toolbar.txtSymbol_o": "オミクロン", "DE.Controllers.Toolbar.txtSymbol_omega": "オメガ", "DE.Controllers.Toolbar.txtSymbol_partial": "偏微分", "DE.Controllers.Toolbar.txtSymbol_percent": "パーセンテージ", "DE.Controllers.Toolbar.txtSymbol_phi": "ファイ", "DE.Controllers.Toolbar.txtSymbol_pi": "パイ", "DE.Controllers.Toolbar.txtSymbol_plus": "プラス", "DE.Controllers.Toolbar.txtSymbol_pm": "プラス マイナス", "DE.Controllers.Toolbar.txtSymbol_propto": "に比例", "DE.Controllers.Toolbar.txtSymbol_psi": "プサイ", "DE.Controllers.Toolbar.txtSymbol_qdrt": "四乗根", "DE.Controllers.Toolbar.txtSymbol_qed": "証明終了", "DE.Controllers.Toolbar.txtSymbol_rddots": "斜め(右上)の省略記号", "DE.Controllers.Toolbar.txtSymbol_rho": "ロー", "DE.Controllers.Toolbar.txtSymbol_rightarrow": "右矢印", "DE.Controllers.Toolbar.txtSymbol_sigma": "シグマ", "DE.Controllers.Toolbar.txtSymbol_sqrt": "根号", "DE.Controllers.Toolbar.txtSymbol_tau": "タウ", "DE.Controllers.Toolbar.txtSymbol_therefore": "従って", "DE.Controllers.Toolbar.txtSymbol_theta": "シータ", "DE.Controllers.Toolbar.txtSymbol_times": "乗算記号", "DE.Controllers.Toolbar.txtSymbol_uparrow": "上矢印", "DE.Controllers.Toolbar.txtSymbol_upsilon": "ウプシロン", "DE.Controllers.Toolbar.txtSymbol_varepsilon": "イプシロン (別形)", "DE.Controllers.Toolbar.txtSymbol_varphi": "ファイ (別形)", "DE.Controllers.Toolbar.txtSymbol_varpi": "円周率（別形）", "DE.Controllers.Toolbar.txtSymbol_varrho": "ロー (別形)", "DE.Controllers.Toolbar.txtSymbol_varsigma": "シグマ (別形)", "DE.Controllers.Toolbar.txtSymbol_vartheta": "シータ (別形)", "DE.Controllers.Toolbar.txtSymbol_vdots": "垂直線の省略記号", "DE.Controllers.Toolbar.txtSymbol_xsi": "グザイ", "DE.Controllers.Toolbar.txtSymbol_zeta": "ゼータ", "DE.Controllers.Viewport.textFitPage": "ページに合わせる", "DE.Controllers.Viewport.textFitWidth": "幅を合わせる", "DE.Controllers.Viewport.txtDarkMode": "ダークモード", "DE.Views.AddNewCaptionLabelDialog.textLabel": "ラベル：", "DE.Views.AddNewCaptionLabelDialog.textLabelError": "ラベルは空白にできません", "DE.Views.BookmarksDialog.textAdd": "追加", "DE.Views.BookmarksDialog.textBookmarkName": "ブックマーク名", "DE.Views.BookmarksDialog.textClose": "閉じる", "DE.Views.BookmarksDialog.textCopy": "コピー", "DE.Views.BookmarksDialog.textDelete": "削除する", "DE.Views.BookmarksDialog.textGetLink": "リンクを取得する", "DE.Views.BookmarksDialog.textGoto": "移動する", "DE.Views.BookmarksDialog.textHidden": "隠しブックマーク", "DE.Views.BookmarksDialog.textLocation": "位置", "DE.Views.BookmarksDialog.textName": "名前", "DE.Views.BookmarksDialog.textSort": "並べ替え", "DE.Views.BookmarksDialog.textTitle": "ブックマーク", "DE.Views.BookmarksDialog.txtInvalidName": "ブックマーク名には、文字、数字、アンダースコアのみを使用でき、先頭は文字で始まる必要があります。", "DE.Views.CaptionDialog.textAdd": "ラベルを追加", "DE.Views.CaptionDialog.textAfter": "後に", "DE.Views.CaptionDialog.textBefore": "前", "DE.Views.CaptionDialog.textCaption": "キャプション", "DE.Views.CaptionDialog.textChapter": "章タイトルのスタイル", "DE.Views.CaptionDialog.textChapterInc": "章番号を含める", "DE.Views.CaptionDialog.textColon": "コロン", "DE.Views.CaptionDialog.textDash": "ダッシュ", "DE.Views.CaptionDialog.textDelete": "ラベル削除", "DE.Views.CaptionDialog.textEquation": "方程式\t", "DE.Views.CaptionDialog.textExamples": " 例：表 2-A 、図 1.IV", "DE.Views.CaptionDialog.textExclude": "キャプションからラベルを除外する", "DE.Views.CaptionDialog.textFigure": "図形", "DE.Views.CaptionDialog.textHyphen": "ハイフン", "DE.Views.CaptionDialog.textInsert": "挿入する", "DE.Views.CaptionDialog.textLabel": "ラベル", "DE.Views.CaptionDialog.textLongDash": "長いダッシュ", "DE.Views.CaptionDialog.textNumbering": "ナンバリング", "DE.Views.CaptionDialog.textPeriod": "期間", "DE.Views.CaptionDialog.textSeparator": "セパレーターを使用する", "DE.Views.CaptionDialog.textTable": "テーブル", "DE.Views.CaptionDialog.textTitle": "キャプションの挿入", "DE.Views.CellsAddDialog.textCol": "列", "DE.Views.CellsAddDialog.textDown": "カーソルの下", "DE.Views.CellsAddDialog.textLeft": "左に", "DE.Views.CellsAddDialog.textRight": "右に", "DE.Views.CellsAddDialog.textRow": "行", "DE.Views.CellsAddDialog.textTitle": "複数を挿入する", "DE.Views.CellsAddDialog.textUp": "カーソルより上", "DE.Views.ChartSettings.text3dDepth": "深さ(ベースに対する割合)", "DE.Views.ChartSettings.text3dHeight": "高<PERSON>(ベースに対する割合)", "DE.Views.ChartSettings.text3dRotation": "3D回転", "DE.Views.ChartSettings.textAdvanced": "詳細設定を表示", "DE.Views.ChartSettings.textAutoscale": "自動スケーリング", "DE.Views.ChartSettings.textChartType": "グラフの種類の変更", "DE.Views.ChartSettings.textDefault": "デフォルト回転", "DE.Views.ChartSettings.textDown": "下", "DE.Views.ChartSettings.textEditData": "データの編集", "DE.Views.ChartSettings.textHeight": "高さ", "DE.Views.ChartSettings.textLeft": "左", "DE.Views.ChartSettings.textNarrow": "狭角", "DE.Views.ChartSettings.textOriginalSize": "実際のサイズ", "DE.Views.ChartSettings.textPerspective": "分析観点", "DE.Views.ChartSettings.textRight": "右", "DE.Views.ChartSettings.textRightAngle": "軸の直交", "DE.Views.ChartSettings.textSize": "サイズ", "DE.Views.ChartSettings.textStyle": "スタイル", "DE.Views.ChartSettings.textUndock": "パネルからドッキング解除", "DE.Views.ChartSettings.textUp": "上", "DE.Views.ChartSettings.textWiden": "広角", "DE.Views.ChartSettings.textWidth": "幅", "DE.Views.ChartSettings.textWrap": "折り返しの種類と配置", "DE.Views.ChartSettings.textX": "X 回転", "DE.Views.ChartSettings.textY": "Y 回転", "DE.Views.ChartSettings.txtBehind": "テキストの背後に", "DE.Views.ChartSettings.txtInFront": "テキストの前に", "DE.Views.ChartSettings.txtInline": "テキストに沿って", "DE.Views.ChartSettings.txtSquare": "四角", "DE.Views.ChartSettings.txtThrough": "内部", "DE.Views.ChartSettings.txtTight": "外周", "DE.Views.ChartSettings.txtTitle": "チャート", "DE.Views.ChartSettings.txtTopAndBottom": "上と下", "DE.Views.ControlSettingsDialog.strGeneral": "一般", "DE.Views.ControlSettingsDialog.textAdd": "追加", "DE.Views.ControlSettingsDialog.textAppearance": "外観", "DE.Views.ControlSettingsDialog.textApplyAll": "全てに適用する", "DE.Views.ControlSettingsDialog.textBox": "境界ボックス", "DE.Views.ControlSettingsDialog.textChange": "編集する", "DE.Views.ControlSettingsDialog.textCheckbox": "チェックボックス", "DE.Views.ControlSettingsDialog.textChecked": "［チェックした］記号", "DE.Views.ControlSettingsDialog.textColor": "色", "DE.Views.ControlSettingsDialog.textCombobox": "コンボボックス", "DE.Views.ControlSettingsDialog.textDate": "日付形式", "DE.Views.ControlSettingsDialog.textDelete": "削除する", "DE.Views.ControlSettingsDialog.textDisplayName": "表示名", "DE.Views.ControlSettingsDialog.textDown": "下", "DE.Views.ControlSettingsDialog.textDropDown": "ドロップダウンリスト", "DE.Views.ControlSettingsDialog.textFormat": "日付の表示形式", "DE.Views.ControlSettingsDialog.textLang": "言語", "DE.Views.ControlSettingsDialog.textLock": "ロック", "DE.Views.ControlSettingsDialog.textName": "タイトル", "DE.Views.ControlSettingsDialog.textNone": "なし", "DE.Views.ControlSettingsDialog.textPlaceholder": "プレースホルダ", "DE.Views.ControlSettingsDialog.textShowAs": "として表示する", "DE.Views.ControlSettingsDialog.textSystemColor": "システム", "DE.Views.ControlSettingsDialog.textTag": "タグ", "DE.Views.ControlSettingsDialog.textTitle": "コンテンツコントロール設定", "DE.Views.ControlSettingsDialog.textUnchecked": "［チェックされていない］記号", "DE.Views.ControlSettingsDialog.textUp": "上", "DE.Views.ControlSettingsDialog.textValue": "値", "DE.Views.ControlSettingsDialog.tipChange": "記号の変更", "DE.Views.ControlSettingsDialog.txtLockDelete": "コンテンツコントロールは削除不可です。", "DE.Views.ControlSettingsDialog.txtLockEdit": "コンテンツは編集不可です。", "DE.Views.CrossReferenceDialog.textAboveBelow": "上/下", "DE.Views.CrossReferenceDialog.textBookmark": "ブックマーク", "DE.Views.CrossReferenceDialog.textBookmarkText": "ブックマークのテキスト", "DE.Views.CrossReferenceDialog.textCaption": "キャプション全体", "DE.Views.CrossReferenceDialog.textEmpty": "要求された参照は空です。", "DE.Views.CrossReferenceDialog.textEndnote": "文末脚注", "DE.Views.CrossReferenceDialog.textEndNoteNum": "文末脚注番号", "DE.Views.CrossReferenceDialog.textEndNoteNumForm": "文末脚注番号（フォーマット済み）", "DE.Views.CrossReferenceDialog.textEquation": "方程式\t", "DE.Views.CrossReferenceDialog.textFigure": "図形", "DE.Views.CrossReferenceDialog.textFootnote": "脚注", "DE.Views.CrossReferenceDialog.textHeading": "見出し", "DE.Views.CrossReferenceDialog.textHeadingNum": "見出し番号", "DE.Views.CrossReferenceDialog.textHeadingNumFull": "見出し番号（全文）", "DE.Views.CrossReferenceDialog.textHeadingNumNo": "見出し番号（文脈なし）", "DE.Views.CrossReferenceDialog.textHeadingText": "見出しテキスト", "DE.Views.CrossReferenceDialog.textIncludeAbove": "上/下を含める", "DE.Views.CrossReferenceDialog.textInsert": "挿入する", "DE.Views.CrossReferenceDialog.textInsertAs": "ハイパーリンクとして挿入する", "DE.Views.CrossReferenceDialog.textLabelNum": "ラベルと番号のみ", "DE.Views.CrossReferenceDialog.textNoteNum": "脚注番号", "DE.Views.CrossReferenceDialog.textNoteNumForm": "脚注番号（フォーマット済み）", "DE.Views.CrossReferenceDialog.textOnlyCaption": "キャプションのテキストのみ", "DE.Views.CrossReferenceDialog.textPageNum": "ページ番号", "DE.Views.CrossReferenceDialog.textParagraph": "番号付き項目", "DE.Views.CrossReferenceDialog.textParaNum": "段落番号", "DE.Views.CrossReferenceDialog.textParaNumFull": "段落番号（全文）", "DE.Views.CrossReferenceDialog.textParaNumNo": "段落番号（文脈なし）", "DE.Views.CrossReferenceDialog.textSeparate": "で区切られた数字", "DE.Views.CrossReferenceDialog.textTable": "テーブル", "DE.Views.CrossReferenceDialog.textText": "段落テキスト", "DE.Views.CrossReferenceDialog.textWhich": "どのキャプションに対して", "DE.Views.CrossReferenceDialog.textWhichBookmark": "どのブックマークに対して", "DE.Views.CrossReferenceDialog.textWhichEndnote": "どの文末脚注に対して", "DE.Views.CrossReferenceDialog.textWhichHeading": "どの見出しに対して", "DE.Views.CrossReferenceDialog.textWhichNote": "どの脚注に対して", "DE.Views.CrossReferenceDialog.textWhichPara": "どの番号の項目に対して", "DE.Views.CrossReferenceDialog.txtReference": "に参照を挿入する", "DE.Views.CrossReferenceDialog.txtTitle": "相互参照", "DE.Views.CrossReferenceDialog.txtType": "参照タイプ", "DE.Views.CustomColumnsDialog.textColumns": "列数", "DE.Views.CustomColumnsDialog.textSeparator": "列の区切り線", "DE.Views.CustomColumnsDialog.textSpacing": "列の間隔", "DE.Views.CustomColumnsDialog.textTitle": "列", "DE.Views.DateTimeDialog.confirmDefault": "{0}に既定の形式を設定：\"{1}\"", "DE.Views.DateTimeDialog.textDefault": "デフォルトに設定", "DE.Views.DateTimeDialog.textFormat": "形式", "DE.Views.DateTimeDialog.textLang": "言語", "DE.Views.DateTimeDialog.textUpdate": "自動的に更新", "DE.Views.DateTimeDialog.txtTitle": "日付＆時刻", "DE.Views.DocProtection.hintProtectDoc": "文書を保護する", "DE.Views.DocProtection.txtDocProtectedComment": "文書は保護されています。<br>この文書には、コメントしか挿入できません。", "DE.Views.DocProtection.txtDocProtectedForms": "文書は保護されています。<br>この文書では、フォームにのみ記入することができます。", "DE.Views.DocProtection.txtDocProtectedTrack": "文書は保護されています。<br>この文書を編集することは可能ですが、すべての変更は追跡されます。", "DE.Views.DocProtection.txtDocProtectedView": "ドキュメントが保護されています。<br>このドキュメントは閲覧のみ可能です。", "DE.Views.DocProtection.txtDocUnlockDescription": "パスワードを入力すると、文書の保護が解除されます", "DE.Views.DocProtection.txtProtectDoc": "文書を保護する", "DE.Views.DocumentHolder.aboveText": "上", "DE.Views.DocumentHolder.addCommentText": "コメントの追加", "DE.Views.DocumentHolder.advancedDropCapText": "ドロップキャップの設定", "DE.Views.DocumentHolder.advancedEquationText": "数式設定", "DE.Views.DocumentHolder.advancedFrameText": "フレームの詳細設定", "DE.Views.DocumentHolder.advancedParagraphText": "段落の詳細設定", "DE.Views.DocumentHolder.advancedTableText": "テーブルの詳細設定", "DE.Views.DocumentHolder.advancedText": "詳細設定", "DE.Views.DocumentHolder.alignmentText": "配置", "DE.Views.DocumentHolder.allLinearText": "すべて - 線形", "DE.Views.DocumentHolder.allProfText": "すべて - プロフェッショナル", "DE.Views.DocumentHolder.belowText": "下", "DE.Views.DocumentHolder.breakBeforeText": "前に改ページ", "DE.Views.DocumentHolder.bulletsText": "箇条書きと段落番号", "DE.Views.DocumentHolder.cellAlignText": "セルの縦方向の配置", "DE.Views.DocumentHolder.cellText": "セル", "DE.Views.DocumentHolder.centerText": "中央揃え", "DE.Views.DocumentHolder.chartText": "チャートの詳細設定", "DE.Views.DocumentHolder.columnText": "列", "DE.Views.DocumentHolder.currLinearText": "現在 - 線形", "DE.Views.DocumentHolder.currProfText": "現在 - プロフェッショナル", "DE.Views.DocumentHolder.deleteColumnText": "列の削除", "DE.Views.DocumentHolder.deleteRowText": "行の削除", "DE.Views.DocumentHolder.deleteTableText": "表の削除", "DE.Views.DocumentHolder.deleteText": "削除する", "DE.Views.DocumentHolder.direct270Text": "上にテキストを回転", "DE.Views.DocumentHolder.direct90Text": "下にテキストを回転", "DE.Views.DocumentHolder.directHText": "水平", "DE.Views.DocumentHolder.directionText": "文字の方向", "DE.Views.DocumentHolder.editChartText": "データの編集", "DE.Views.DocumentHolder.editFooterText": "フッターの編集", "DE.Views.DocumentHolder.editHeaderText": "ヘッダーの編集", "DE.Views.DocumentHolder.editHyperlinkText": "ハイパーリンクの編集", "DE.Views.DocumentHolder.eqToInlineText": "内臓に切り替える", "DE.Views.DocumentHolder.guestText": "ゲスト", "DE.Views.DocumentHolder.hyperlinkText": "ハイパーリンク", "DE.Views.DocumentHolder.ignoreAllSpellText": "全てを無視する", "DE.Views.DocumentHolder.ignoreSpellText": "無視する", "DE.Views.DocumentHolder.imageText": "画像の詳細設定", "DE.Views.DocumentHolder.insertColumnLeftText": "左の列", "DE.Views.DocumentHolder.insertColumnRightText": "1 列右", "DE.Views.DocumentHolder.insertColumnText": "列の挿入", "DE.Views.DocumentHolder.insertRowAboveText": "行 (上)", "DE.Views.DocumentHolder.insertRowBelowText": "行(下)", "DE.Views.DocumentHolder.insertRowText": "行の挿入", "DE.Views.DocumentHolder.insertText": "挿入", "DE.Views.DocumentHolder.keepLinesText": "段落を分割しない", "DE.Views.DocumentHolder.langText": "言語の選択", "DE.Views.DocumentHolder.latexText": "LaTeX", "DE.Views.DocumentHolder.leftText": "左", "DE.Views.DocumentHolder.loadSpellText": "バリエーションの読み込み中...", "DE.Views.DocumentHolder.mergeCellsText": "セルの結合", "DE.Views.DocumentHolder.moreText": "その他のバリエーション...", "DE.Views.DocumentHolder.noSpellVariantsText": "バリエーションなし", "DE.Views.DocumentHolder.notcriticalErrorTitle": "警告", "DE.Views.DocumentHolder.originalSizeText": "実際のサイズ", "DE.Views.DocumentHolder.paragraphText": "段落", "DE.Views.DocumentHolder.removeHyperlinkText": "ハイパーリンクの削除", "DE.Views.DocumentHolder.rightText": "右", "DE.Views.DocumentHolder.rowText": "行", "DE.Views.DocumentHolder.saveStyleText": "新しいスタイルの作成", "DE.Views.DocumentHolder.selectCellText": "セルの選択", "DE.Views.DocumentHolder.selectColumnText": "列の選択", "DE.Views.DocumentHolder.selectRowText": "行の選択", "DE.Views.DocumentHolder.selectTableText": "テーブルの選択", "DE.Views.DocumentHolder.selectText": "選択する", "DE.Views.DocumentHolder.shapeText": "図形の詳細設定", "DE.Views.DocumentHolder.spellcheckText": "スペルチェック", "DE.Views.DocumentHolder.splitCellsText": "セルを分割...", "DE.Views.DocumentHolder.splitCellTitleText": "セルを分割", "DE.Views.DocumentHolder.strDelete": "署名の削除", "DE.Views.DocumentHolder.strDetails": "署名の詳細", "DE.Views.DocumentHolder.strSetup": "署名の設定", "DE.Views.DocumentHolder.strSign": "署名する", "DE.Views.DocumentHolder.styleText": "スタイルとしての書式設定", "DE.Views.DocumentHolder.tableText": "テーブル", "DE.Views.DocumentHolder.textAccept": "変更を承諾する", "DE.Views.DocumentHolder.textAlign": "整列", "DE.Views.DocumentHolder.textArrange": "整列", "DE.Views.DocumentHolder.textArrangeBack": "背景へ移動", "DE.Views.DocumentHolder.textArrangeBackward": "背面へ移動", "DE.Views.DocumentHolder.textArrangeForward": "前面へ移動", "DE.Views.DocumentHolder.textArrangeFront": "前景に移動", "DE.Views.DocumentHolder.textCells": "セル", "DE.Views.DocumentHolder.textCol": "列全体を削除", "DE.Views.DocumentHolder.textContentControls": "コンテンツコントロール", "DE.Views.DocumentHolder.textContinueNumbering": "番号付けを続行", "DE.Views.DocumentHolder.textCopy": "コピー", "DE.Views.DocumentHolder.textCrop": "トリミング", "DE.Views.DocumentHolder.textCropFill": "塗りつぶし", "DE.Views.DocumentHolder.textCropFit": "収める", "DE.Views.DocumentHolder.textCut": "切り取り", "DE.Views.DocumentHolder.textDistributeCols": "列の幅を揃える", "DE.Views.DocumentHolder.textDistributeRows": "行の高さを揃える", "DE.Views.DocumentHolder.textEditControls": "コンテンツコントロール設定", "DE.Views.DocumentHolder.textEditPoints": "頂点の編集", "DE.Views.DocumentHolder.textEditWrapBoundary": "折り返し点の編集", "DE.Views.DocumentHolder.textFlipH": "左右に反転", "DE.Views.DocumentHolder.textFlipV": "上下に反転", "DE.Views.DocumentHolder.textFollow": "移動する", "DE.Views.DocumentHolder.textFromFile": "ファイルから", "DE.Views.DocumentHolder.textFromStorage": "ストレージから", "DE.Views.DocumentHolder.textFromUrl": "URLから", "DE.Views.DocumentHolder.textJoinList": "前のリストに結合", "DE.Views.DocumentHolder.textLeft": "セルの左シフト", "DE.Views.DocumentHolder.textNest": "ネスト表", "DE.Views.DocumentHolder.textNextPage": "次のページ", "DE.Views.DocumentHolder.textNumberingValue": "ナンバリング値", "DE.Views.DocumentHolder.textPaste": "貼り付け", "DE.Views.DocumentHolder.textPrevPage": "前のページ", "DE.Views.DocumentHolder.textRefreshField": "フィールドの更新", "DE.Views.DocumentHolder.textReject": "変更を拒否する", "DE.Views.DocumentHolder.textRemCheckBox": "チェックボックスを削除する", "DE.Views.DocumentHolder.textRemComboBox": "コンボボックスを削除する", "DE.Views.DocumentHolder.textRemDropdown": "ドロップダウンリストを削除する", "DE.Views.DocumentHolder.textRemField": "テキストフィールドを削除する", "DE.Views.DocumentHolder.textRemove": "削除する", "DE.Views.DocumentHolder.textRemoveControl": "コンテンツコントロールを削除する", "DE.Views.DocumentHolder.textRemPicture": "画像を削除する", "DE.Views.DocumentHolder.textRemRadioBox": "ラジオボタンの削除", "DE.Views.DocumentHolder.textReplace": "画像を置き換える", "DE.Views.DocumentHolder.textRotate": "回転させる", "DE.Views.DocumentHolder.textRotate270": "反時計回りに90度回転", "DE.Views.DocumentHolder.textRotate90": "時計回りに90度回転", "DE.Views.DocumentHolder.textRow": "行全体を削除", "DE.Views.DocumentHolder.textSeparateList": "別のリスト", "DE.Views.DocumentHolder.textSettings": "設定", "DE.Views.DocumentHolder.textSeveral": "複数の行/列", "DE.Views.DocumentHolder.textShapeAlignBottom": "下揃え", "DE.Views.DocumentHolder.textShapeAlignCenter": "中央揃え", "DE.Views.DocumentHolder.textShapeAlignLeft": "左揃え", "DE.Views.DocumentHolder.textShapeAlignMiddle": "上下中央揃え", "DE.Views.DocumentHolder.textShapeAlignRight": "右揃え", "DE.Views.DocumentHolder.textShapeAlignTop": "上揃え", "DE.Views.DocumentHolder.textStartNewList": "新しいリストを開始する", "DE.Views.DocumentHolder.textStartNumberingFrom": "計数値の設定", "DE.Views.DocumentHolder.textTitleCellsRemove": "セルを削除する", "DE.Views.DocumentHolder.textTOC": "目次", "DE.Views.DocumentHolder.textTOCSettings": "目次設定", "DE.Views.DocumentHolder.textUndo": "元に戻す", "DE.Views.DocumentHolder.textUpdateAll": "テーブル全体の更新", "DE.Views.DocumentHolder.textUpdatePages": "ページ番号のみの更新", "DE.Views.DocumentHolder.textUpdateTOC": "目次を更新する", "DE.Views.DocumentHolder.textWrap": "折り返しの種類と配置", "DE.Views.DocumentHolder.tipIsLocked": "今、この要素が他のユーザーによって編集されています。", "DE.Views.DocumentHolder.toDictionaryText": "辞書に追加", "DE.Views.DocumentHolder.txtAddBottom": "下罫線の追加", "DE.Views.DocumentHolder.txtAddFractionBar": "分数罫の追加", "DE.Views.DocumentHolder.txtAddHor": "水平線の追加", "DE.Views.DocumentHolder.txtAddLB": "左下線の追加", "DE.Views.DocumentHolder.txtAddLeft": "左罫線の追加", "DE.Views.DocumentHolder.txtAddLT": "左上線の追加", "DE.Views.DocumentHolder.txtAddRight": "右罫線の追加", "DE.Views.DocumentHolder.txtAddTop": "上罫線の追加", "DE.Views.DocumentHolder.txtAddVer": "縦線の追加", "DE.Views.DocumentHolder.txtAlignToChar": "文字の整列", "DE.Views.DocumentHolder.txtBehind": "テキストの背後に", "DE.Views.DocumentHolder.txtBorderProps": "罫線の​​プロパティ", "DE.Views.DocumentHolder.txtBottom": "下", "DE.Views.DocumentHolder.txtColumnAlign": "列の配置", "DE.Views.DocumentHolder.txtDecreaseArg": "引数のサイズの縮小", "DE.Views.DocumentHolder.txtDeleteArg": "引数の削除", "DE.Views.DocumentHolder.txtDeleteBreak": "任意指定の改行を削除", "DE.Views.DocumentHolder.txtDeleteChars": "囲み文字の削除", "DE.Views.DocumentHolder.txtDeleteCharsAndSeparators": "囲み文字と区切り文字の削除", "DE.Views.DocumentHolder.txtDeleteEq": "数式の削除", "DE.Views.DocumentHolder.txtDeleteGroupChar": "文字の削除", "DE.Views.DocumentHolder.txtDeleteRadical": "ラジカルを削除する", "DE.Views.DocumentHolder.txtDistribHor": "左右に整列", "DE.Views.DocumentHolder.txtDistribVert": "上下に整列", "DE.Views.DocumentHolder.txtEmpty": "（空白）", "DE.Views.DocumentHolder.txtFractionLinear": "分数(横)に変更", "DE.Views.DocumentHolder.txtFractionSkewed": "分数(斜め)に変更", "DE.Views.DocumentHolder.txtFractionStacked": "分数(縦)に変更\t", "DE.Views.DocumentHolder.txtGroup": "グループ", "DE.Views.DocumentHolder.txtGroupCharOver": "テキストの上の文字", "DE.Views.DocumentHolder.txtGroupCharUnder": "テキストの下の文字", "DE.Views.DocumentHolder.txtHideBottom": "下罫線を表示しない", "DE.Views.DocumentHolder.txtHideBottomLimit": "下極限を表示しない", "DE.Views.DocumentHolder.txtHideCloseBracket": "右括弧を表示しない", "DE.Views.DocumentHolder.txtHideDegree": "次数を表示しない", "DE.Views.DocumentHolder.txtHideHor": "横線を表示しない", "DE.Views.DocumentHolder.txtHideLB": "左(下)の線を表示しない", "DE.Views.DocumentHolder.txtHideLeft": "左罫線を表示しない", "DE.Views.DocumentHolder.txtHideLT": "左(上)の線を表示しない", "DE.Views.DocumentHolder.txtHideOpenBracket": "左括弧を表示しない", "DE.Views.DocumentHolder.txtHidePlaceholder": "プレースホルダを表示しない", "DE.Views.DocumentHolder.txtHideRight": "右罫線を枠線表示しない", "DE.Views.DocumentHolder.txtHideTop": "上罫線を表示しない", "DE.Views.DocumentHolder.txtHideTopLimit": "上極限を表示しない", "DE.Views.DocumentHolder.txtHideVer": "縦線を表示しない", "DE.Views.DocumentHolder.txtIncreaseArg": "引数のサイズの拡大", "DE.Views.DocumentHolder.txtInFront": "テキストの前に", "DE.Views.DocumentHolder.txtInline": "テキストに沿って", "DE.Views.DocumentHolder.txtInsertArgAfter": "の後に引数を挿入", "DE.Views.DocumentHolder.txtInsertArgBefore": "の前に引数を挿入", "DE.Views.DocumentHolder.txtInsertBreak": "任意指定の改行を挿入", "DE.Views.DocumentHolder.txtInsertCaption": "キャプションの挿入", "DE.Views.DocumentHolder.txtInsertEqAfter": "の後に数式を挿入", "DE.Views.DocumentHolder.txtInsertEqBefore": "の前に数式を挿入", "DE.Views.DocumentHolder.txtKeepTextOnly": "テキスト保存のみ", "DE.Views.DocumentHolder.txtLimitChange": "制限位置の変更", "DE.Views.DocumentHolder.txtLimitOver": "テキストの上に制限する", "DE.Views.DocumentHolder.txtLimitUnder": "テキストの下に制限する", "DE.Views.DocumentHolder.txtMatchBrackets": "括弧を引数の高さに合わせる", "DE.Views.DocumentHolder.txtMatrixAlign": "行列の配置", "DE.Views.DocumentHolder.txtOverbar": "テキストの上のバー", "DE.Views.DocumentHolder.txtOverwriteCells": "セルを上書きする", "DE.Views.DocumentHolder.txtPasteSourceFormat": "元の書式付けを保存する", "DE.Views.DocumentHolder.txtPressLink": "{0}キーを押しながらリンクをクリックしてください", "DE.Views.DocumentHolder.txtPrintSelection": "選択範囲の印刷", "DE.Views.DocumentHolder.txtRemFractionBar": "分数線の削除", "DE.Views.DocumentHolder.txtRemLimit": "制限を削除する", "DE.Views.DocumentHolder.txtRemoveAccentChar": "アクセント記号を削除", "DE.Views.DocumentHolder.txtRemoveBar": "上/下線の削除", "DE.Views.DocumentHolder.txtRemoveWarning": "この署名を削除しますか？<br>この操作は元に戻せません。", "DE.Views.DocumentHolder.txtRemScripts": "スクリプトの削除", "DE.Views.DocumentHolder.txtRemSubscript": "下付き文字の削除", "DE.Views.DocumentHolder.txtRemSuperscript": "上付き文字の削除", "DE.Views.DocumentHolder.txtScriptsAfter": "テキストの後のスクリプト", "DE.Views.DocumentHolder.txtScriptsBefore": "テキストの前のスクリプト", "DE.Views.DocumentHolder.txtShowBottomLimit": "下限を表示する", "DE.Views.DocumentHolder.txtShowCloseBracket": "右大括弧を表示", "DE.Views.DocumentHolder.txtShowDegree": "次数を表示", "DE.Views.DocumentHolder.txtShowOpenBracket": "左大括弧の表示", "DE.Views.DocumentHolder.txtShowPlaceholder": "プレースホルダーの表示", "DE.Views.DocumentHolder.txtShowTopLimit": "上限を表示する", "DE.Views.DocumentHolder.txtSquare": "四角", "DE.Views.DocumentHolder.txtStretchBrackets": "括弧の拡大", "DE.Views.DocumentHolder.txtThrough": "内部", "DE.Views.DocumentHolder.txtTight": "外周", "DE.Views.DocumentHolder.txtTop": "トップ", "DE.Views.DocumentHolder.txtTopAndBottom": "上と下", "DE.Views.DocumentHolder.txtUnderbar": "テキストの下のバー", "DE.Views.DocumentHolder.txtUngroup": "グループ化解除", "DE.Views.DocumentHolder.txtWarnUrl": "このリンクをクリックすると、お使いの端末やデータに悪影響を与える可能性があります。<br>本当に続けてよろしいですか？", "DE.Views.DocumentHolder.unicodeText": "Unicode", "DE.Views.DocumentHolder.updateStyleText": "%1スタイルの更新", "DE.Views.DocumentHolder.vertAlignText": "垂直方向の配置", "DE.Views.DropcapSettingsAdvanced.strBorders": "罫線と塗りつぶし", "DE.Views.DropcapSettingsAdvanced.strDropcap": "ドロップキャップ", "DE.Views.DropcapSettingsAdvanced.strMargins": "余白", "DE.Views.DropcapSettingsAdvanced.textAlign": "配置", "DE.Views.DropcapSettingsAdvanced.textAtLeast": "最小", "DE.Views.DropcapSettingsAdvanced.textAuto": "自動", "DE.Views.DropcapSettingsAdvanced.textBackColor": "背景色", "DE.Views.DropcapSettingsAdvanced.textBorderColor": "罫線の色", "DE.Views.DropcapSettingsAdvanced.textBorderDesc": "図表をクリックするか、ボタンで枠を選択します。", "DE.Views.DropcapSettingsAdvanced.textBorderWidth": "罫線のサイズ", "DE.Views.DropcapSettingsAdvanced.textBottom": "下", "DE.Views.DropcapSettingsAdvanced.textCenter": "中央揃え", "DE.Views.DropcapSettingsAdvanced.textColumn": "列", "DE.Views.DropcapSettingsAdvanced.textDistance": "文字列との間隔", "DE.Views.DropcapSettingsAdvanced.textExact": "固定値", "DE.Views.DropcapSettingsAdvanced.textFlow": "フローフレーム", "DE.Views.DropcapSettingsAdvanced.textFont": "フォント", "DE.Views.DropcapSettingsAdvanced.textFrame": "フレーム", "DE.Views.DropcapSettingsAdvanced.textHeight": "高さ", "DE.Views.DropcapSettingsAdvanced.textHorizontal": "水平", "DE.Views.DropcapSettingsAdvanced.textInline": "インラインフレーム", "DE.Views.DropcapSettingsAdvanced.textInMargin": "余白", "DE.Views.DropcapSettingsAdvanced.textInText": "テキスト", "DE.Views.DropcapSettingsAdvanced.textLeft": "左", "DE.Views.DropcapSettingsAdvanced.textMargin": "余白", "DE.Views.DropcapSettingsAdvanced.textMove": "文字列と一緒に移動する", "DE.Views.DropcapSettingsAdvanced.textNone": "なし", "DE.Views.DropcapSettingsAdvanced.textPage": "ページ", "DE.Views.DropcapSettingsAdvanced.textParagraph": "段落", "DE.Views.DropcapSettingsAdvanced.textParameters": "パラメーター", "DE.Views.DropcapSettingsAdvanced.textPosition": "位置", "DE.Views.DropcapSettingsAdvanced.textRelative": "と相対", "DE.Views.DropcapSettingsAdvanced.textRight": "右", "DE.Views.DropcapSettingsAdvanced.textRowHeight": "行の高さ", "DE.Views.DropcapSettingsAdvanced.textTitle": "ドロップキャップの詳細設定", "DE.Views.DropcapSettingsAdvanced.textTitleFrame": "フレーム - 詳細設定", "DE.Views.DropcapSettingsAdvanced.textTop": "トップ", "DE.Views.DropcapSettingsAdvanced.textVertical": "垂直", "DE.Views.DropcapSettingsAdvanced.textWidth": "幅", "DE.Views.DropcapSettingsAdvanced.tipFontName": "フォント", "DE.Views.DropcapSettingsAdvanced.txtNoBorders": "罫線なし", "DE.Views.EditListItemDialog.textDisplayName": "表示名", "DE.Views.EditListItemDialog.textNameError": "表示名は空白にできません。", "DE.Views.EditListItemDialog.textValue": "値", "DE.Views.EditListItemDialog.textValueError": "同じ値の項目がすでに存在します。", "DE.Views.FileMenu.btnBackCaption": "ファイルの場所を開く", "DE.Views.FileMenu.btnCloseMenuCaption": "（←戻る）", "DE.Views.FileMenu.btnCreateNewCaption": "新規作成", "DE.Views.FileMenu.btnDownloadCaption": "名前を付けてダウンロード", "DE.Views.FileMenu.btnExitCaption": "終了", "DE.Views.FileMenu.btnFileOpenCaption": "開く", "DE.Views.FileMenu.btnHelpCaption": "ヘルプ", "DE.Views.FileMenu.btnHistoryCaption": "バージョン履歴", "DE.Views.FileMenu.btnInfoCaption": "ファイル情報", "DE.Views.FileMenu.btnPrintCaption": "印刷", "DE.Views.FileMenu.btnProtectCaption": "保護する", "DE.Views.FileMenu.btnRecentFilesCaption": "最近開いた", "DE.Views.FileMenu.btnRenameCaption": "名前を変更する", "DE.Views.FileMenu.btnReturnCaption": "文書に戻る", "DE.Views.FileMenu.btnRightsCaption": "アクセス許可", "DE.Views.FileMenu.btnSaveAsCaption": "名前を付けて保存", "DE.Views.FileMenu.btnSaveCaption": "保存", "DE.Views.FileMenu.btnSaveCopyAsCaption": "コピーを別名で保存する", "DE.Views.FileMenu.btnSettingsCaption": "詳細設定", "DE.Views.FileMenu.btnToEditCaption": "ドキュメントを編集", "DE.Views.FileMenu.textDownload": "ダウンロード", "DE.Views.FileMenuPanels.CreateNew.txtBlank": "空の文書", "DE.Views.FileMenuPanels.CreateNew.txtCreateNew": "新規作成", "DE.Views.FileMenuPanels.DocumentInfo.okButtonText": "適用する", "DE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "著者を追加", "DE.Views.FileMenuPanels.DocumentInfo.txtAddText": "テキストの追加", "DE.Views.FileMenuPanels.DocumentInfo.txtAppName": "アプリ", "DE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "作成者", "DE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "アクセス許可の変更", "DE.Views.FileMenuPanels.DocumentInfo.txtComment": "コメント", "DE.Views.FileMenuPanels.DocumentInfo.txtCreated": "作成済み", "DE.Views.FileMenuPanels.DocumentInfo.txtFastWV": "Web表示用に最適化", "DE.Views.FileMenuPanels.DocumentInfo.txtLoading": "読み込み中...", "DE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "最終更新者", "DE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "最後の変更", "DE.Views.FileMenuPanels.DocumentInfo.txtNo": "いいえ", "DE.Views.FileMenuPanels.DocumentInfo.txtOwner": "所有者", "DE.Views.FileMenuPanels.DocumentInfo.txtPages": "ページ", "DE.Views.FileMenuPanels.DocumentInfo.txtPageSize": "ページのサイズ", "DE.Views.FileMenuPanels.DocumentInfo.txtParagraphs": "段落", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfProducer": "PDFメーカー", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfTagged": "タグ付きPDF", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfVer": "PDFのバージョン", "DE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "場所", "DE.Views.FileMenuPanels.DocumentInfo.txtRights": "権利を有する者", "DE.Views.FileMenuPanels.DocumentInfo.txtSpaces": "文字数 (スペースを含む)", "DE.Views.FileMenuPanels.DocumentInfo.txtStatistics": "統計", "DE.Views.FileMenuPanels.DocumentInfo.txtSubject": "件名", "DE.Views.FileMenuPanels.DocumentInfo.txtSymbols": "文字数", "DE.Views.FileMenuPanels.DocumentInfo.txtTags": "タグ", "DE.Views.FileMenuPanels.DocumentInfo.txtTitle": "タイトル", "DE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "アップロード済み", "DE.Views.FileMenuPanels.DocumentInfo.txtWords": "単語", "DE.Views.FileMenuPanels.DocumentInfo.txtYes": "はい", "DE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "アクセス許可の変更", "DE.Views.FileMenuPanels.DocumentRights.txtRights": "権利を有する者", "DE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "警告", "DE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "パスワード付きで", "DE.Views.FileMenuPanels.ProtectDoc.strProtect": "文書を保護する", "DE.Views.FileMenuPanels.ProtectDoc.strSignature": "署名付きで", "DE.Views.FileMenuPanels.ProtectDoc.txtEdit": "ドキュメントを編集", "DE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "編集すると、文書から署名が削除されます。<br>続行しますか？", "DE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "このドキュメントはパスワードで保護されています", "DE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "この文書には署名が必要です。", "DE.Views.FileMenuPanels.ProtectDoc.txtSigned": "有効な署名がドキュメントに追加されました。 ドキュメントは編集されないように保護されています。", "DE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "文書のデジタル署名の一部が無効であるか、検証できませんでした。 文書は編集できないように保護されています。", "DE.Views.FileMenuPanels.ProtectDoc.txtView": "署名の表示", "DE.Views.FileMenuPanels.Settings.okButtonText": "適用する", "DE.Views.FileMenuPanels.Settings.strCoAuthMode": "共同編集のモード", "DE.Views.FileMenuPanels.Settings.strFast": "高速", "DE.Views.FileMenuPanels.Settings.strFontRender": "フォントヒンティング", "DE.Views.FileMenuPanels.Settings.strIgnoreWordsInUPPERCASE": "大文字がある言葉を無視する", "DE.Views.FileMenuPanels.Settings.strIgnoreWordsWithNumbers": "数字のある単語は無視する", "DE.Views.FileMenuPanels.Settings.strMacrosSettings": "マクロの設定", "DE.Views.FileMenuPanels.Settings.strPasteButton": "貼り付けるときに[貼り付けオプション]ボタンを表示する", "DE.Views.FileMenuPanels.Settings.strShowChanges": "リアルタイム共同編集の変更表示モード", "DE.Views.FileMenuPanels.Settings.strShowComments": "テキストにコメントを表示する", "DE.Views.FileMenuPanels.Settings.strShowOthersChanges": "他のユーザーの変更点を表示する", "DE.Views.FileMenuPanels.Settings.strShowResolvedComments": "解決済みコメントを表示する", "DE.Views.FileMenuPanels.Settings.strStrict": "厳格", "DE.Views.FileMenuPanels.Settings.strTheme": "インターフェイスのテーマ", "DE.Views.FileMenuPanels.Settings.strUnit": "測定単位", "DE.Views.FileMenuPanels.Settings.strZoom": "デフォルトのズーム値", "DE.Views.FileMenuPanels.Settings.text10Minutes": "10分毎", "DE.Views.FileMenuPanels.Settings.text30Minutes": "30分毎", "DE.Views.FileMenuPanels.Settings.text5Minutes": "5分毎", "DE.Views.FileMenuPanels.Settings.text60Minutes": "1時間毎", "DE.Views.FileMenuPanels.Settings.textAlignGuides": "配置ガイド", "DE.Views.FileMenuPanels.Settings.textAutoRecover": "自動回復", "DE.Views.FileMenuPanels.Settings.textAutoSave": "自動保存", "DE.Views.FileMenuPanels.Settings.textDisabled": "無効", "DE.Views.FileMenuPanels.Settings.textForceSave": "中間バージョンの保存", "DE.Views.FileMenuPanels.Settings.textMinute": "1分毎", "DE.Views.FileMenuPanels.Settings.textOldVersions": "DOCXとして保存する場合は、MS Wordの古いバージョンと互換性のあるファイルにしてください", "DE.Views.FileMenuPanels.Settings.txtAll": "全て表示", "DE.Views.FileMenuPanels.Settings.txtAutoCorrect": "オートコレクト設定", "DE.Views.FileMenuPanels.Settings.txtCacheMode": "デフォルトのキャッシュモード", "DE.Views.FileMenuPanels.Settings.txtChangesBalloons": "バルーンをクリックで表示する", "DE.Views.FileMenuPanels.Settings.txtChangesTip": "ツールチップをクリックで表示する", "DE.Views.FileMenuPanels.Settings.txtCm": "センチ", "DE.Views.FileMenuPanels.Settings.txtCollaboration": "共同編集", "DE.Views.FileMenuPanels.Settings.txtDarkMode": "ドキュメントをダークモードに変更", "DE.Views.FileMenuPanels.Settings.txtEditingSaving": "編集と保存", "DE.Views.FileMenuPanels.Settings.txtFastTip": "リアルタイムの共同編集　すべての変更は自動的に保存されます", "DE.Views.FileMenuPanels.Settings.txtFitPage": "ページに合わせる", "DE.Views.FileMenuPanels.Settings.txtFitWidth": "幅に合わせる", "DE.Views.FileMenuPanels.Settings.txtHieroglyphs": "漢字", "DE.Views.FileMenuPanels.Settings.txtInch": "インチ", "DE.Views.FileMenuPanels.Settings.txtLast": "最後の表示", "DE.Views.FileMenuPanels.Settings.txtMac": "OS Xとして", "DE.Views.FileMenuPanels.Settings.txtNative": "ネイティブ", "DE.Views.FileMenuPanels.Settings.txtNone": "表示なし", "DE.Views.FileMenuPanels.Settings.txtProofing": "校正", "DE.Views.FileMenuPanels.Settings.txtPt": "ポイント", "DE.Views.FileMenuPanels.Settings.txtQuickPrint": "クイックプリントボタンをエディタヘッダーに表示", "DE.Views.FileMenuPanels.Settings.txtQuickPrintTip": "最後に選択した、またはデフォルトのプリンターで印刷されます。", "DE.Views.FileMenuPanels.Settings.txtRunMacros": "全てを有効にする", "DE.Views.FileMenuPanels.Settings.txtRunMacrosDesc": "全てのマクロを有効にして、通知しない", "DE.Views.FileMenuPanels.Settings.txtShowTrackChanges": "変更履歴を表示する", "DE.Views.FileMenuPanels.Settings.txtSpellCheck": "スペルチェック", "DE.Views.FileMenuPanels.Settings.txtStopMacros": "全てを無効にする", "DE.Views.FileMenuPanels.Settings.txtStopMacrosDesc": "全てのマクロを無効にして、通知しない", "DE.Views.FileMenuPanels.Settings.txtStrictTip": "「保存」ボタンを使用して、あなたや他人が行った変更を同期させることができます", "DE.Views.FileMenuPanels.Settings.txtUseAltKey": "キーボードでユーザーインターフェイスで移動するには、Altキーを使用します", "DE.Views.FileMenuPanels.Settings.txtUseOptionKey": "「Option」キーを使用して、キーボードでユーザーインターフェイスで移動します", "DE.Views.FileMenuPanels.Settings.txtWarnMacros": "通知を表示する", "DE.Views.FileMenuPanels.Settings.txtWarnMacrosDesc": "全てのマクロを無効にして、通知する", "DE.Views.FileMenuPanels.Settings.txtWin": "Windowsとして", "DE.Views.FileMenuPanels.Settings.txtWorkspace": "ワークスペース", "DE.Views.FormSettings.textAlways": "常時", "DE.Views.FormSettings.textAspect": "縦横比の固定", "DE.Views.FormSettings.textAtLeast": "最小", "DE.Views.FormSettings.textAuto": "オート", "DE.Views.FormSettings.textAutofit": "自動調整", "DE.Views.FormSettings.textBackgroundColor": "背景色", "DE.Views.FormSettings.textCheckbox": "チェックボックス", "DE.Views.FormSettings.textColor": "罫線の色", "DE.Views.FormSettings.textComb": "文字の組み合わせ", "DE.Views.FormSettings.textCombobox": "コンボボックス", "DE.Views.FormSettings.textComplex": "複合フィールド", "DE.Views.FormSettings.textConnected": "フィールド接続済み", "DE.Views.FormSettings.textDelete": "削除する", "DE.Views.FormSettings.textDigits": "数値", "DE.Views.FormSettings.textDisconnect": "切断する", "DE.Views.FormSettings.textDropDown": "ドロップダウン", "DE.Views.FormSettings.textExact": "固定値", "DE.Views.FormSettings.textField": "テキストフィールド", "DE.Views.FormSettings.textFixed": "固定サイズのフィールド", "DE.Views.FormSettings.textFormat": "フォーマット", "DE.Views.FormSettings.textFormatSymbols": "使用可能なシンボル", "DE.Views.FormSettings.textFromFile": "ファイルから", "DE.Views.FormSettings.textFromStorage": "ストレージから", "DE.Views.FormSettings.textFromUrl": "URLから", "DE.Views.FormSettings.textGroupKey": "グループキー", "DE.Views.FormSettings.textImage": "画像", "DE.Views.FormSettings.textKey": "キー", "DE.Views.FormSettings.textLetters": "文字", "DE.Views.FormSettings.textLock": "ロックする", "DE.Views.FormSettings.textMask": "任意のマスク", "DE.Views.FormSettings.textMaxChars": "文字の制限", "DE.Views.FormSettings.textMulti": "複数行のフィールド", "DE.Views.FormSettings.textNever": "一度もない", "DE.Views.FormSettings.textNoBorder": "罫線なし", "DE.Views.FormSettings.textNone": "なし", "DE.Views.FormSettings.textPlaceholder": "プレースホルダ", "DE.Views.FormSettings.textRadiobox": "ラジオボタン", "DE.Views.FormSettings.textReg": "正規表現", "DE.Views.FormSettings.textRequired": "必須", "DE.Views.FormSettings.textScale": "スケーリングのタイミング", "DE.Views.FormSettings.textSelectImage": "画像を選択する", "DE.Views.FormSettings.textTag": "タグ", "DE.Views.FormSettings.textTip": "ヒント", "DE.Views.FormSettings.textTipAdd": "新しい値を追加する", "DE.Views.FormSettings.textTipDelete": "値を削除する", "DE.Views.FormSettings.textTipDown": "下に移動する", "DE.Views.FormSettings.textTipUp": "上に移動する", "DE.Views.FormSettings.textTooBig": "画像が大きすぎます", "DE.Views.FormSettings.textTooSmall": "画像が小さすぎます", "DE.Views.FormSettings.textUnlock": "ロックを解除する", "DE.Views.FormSettings.textValue": "値のオプション", "DE.Views.FormSettings.textWidth": "セルの幅", "DE.Views.FormsTab.capBtnCheckBox": "チェックボックス", "DE.Views.FormsTab.capBtnComboBox": "コンボボックス", "DE.Views.FormsTab.capBtnComplex": "複合フィールド", "DE.Views.FormsTab.capBtnDownloadForm": "oformとしてダウンロードする", "DE.Views.FormsTab.capBtnDropDown": "ドロップダウン", "DE.Views.FormsTab.capBtnEmail": "メールアドレス", "DE.Views.FormsTab.capBtnImage": "画像", "DE.Views.FormsTab.capBtnNext": "次のフィールド", "DE.Views.FormsTab.capBtnPhone": "電話番号", "DE.Views.FormsTab.capBtnPrev": "前のフィールド", "DE.Views.FormsTab.capBtnRadioBox": "ラジオボタン", "DE.Views.FormsTab.capBtnSaveForm": "OFORMとして保存", "DE.Views.FormsTab.capBtnSubmit": "送信", "DE.Views.FormsTab.capBtnText": "テキストフィールド", "DE.Views.FormsTab.capBtnView": "フォームを表示する", "DE.Views.FormsTab.textClear": "フィールドをクリアする", "DE.Views.FormsTab.textClearFields": "すべてのフィールドをクリアする", "DE.Views.FormsTab.textCreateForm": "フィールドを追加して、記入可能なOFORM文書を作成する", "DE.Views.FormsTab.textGotIt": "OK", "DE.Views.FormsTab.textHighlight": "ハイライト設定", "DE.Views.FormsTab.textNoHighlight": "ハイライト表示なし", "DE.Views.FormsTab.textRequired": "必須事項をすべて入力し、送信してください。", "DE.Views.FormsTab.textSubmited": "フォームの送信成功", "DE.Views.FormsTab.tipCheckBox": "チェックボックスを挿入する", "DE.Views.FormsTab.tipComboBox": "コンボボックスを挿入", "DE.Views.FormsTab.tipComplexField": "複合フィールドを挿入する", "DE.Views.FormsTab.tipDownloadForm": "記入可能なOFORM文書としてファイルをダウンロードする", "DE.Views.FormsTab.tipDropDown": "ドロップダウンリストを挿入", "DE.Views.FormsTab.tipEmailField": "メールアドレスを挿入する", "DE.Views.FormsTab.tipImageField": "画像の挿入", "DE.Views.FormsTab.tipNextForm": "次のフィールドに移動する", "DE.Views.FormsTab.tipPhoneField": "電話番号を挿入する", "DE.Views.FormsTab.tipPrevForm": "前のフィールドに移動する", "DE.Views.FormsTab.tipRadioBox": "ラジオボタンの挿入\t", "DE.Views.FormsTab.tipSaveForm": "ファイルをOFORMの記入式ドキュメントとして保存", "DE.Views.FormsTab.tipSubmit": "フォームを送信", "DE.Views.FormsTab.tipTextField": "テキストフィールドを挿入", "DE.Views.FormsTab.tipViewForm": "フォームを表示する", "DE.Views.FormsTab.txtUntitled": "無題", "DE.Views.HeaderFooterSettings.textBottomCenter": "中央下", "DE.Views.HeaderFooterSettings.textBottomLeft": "左下", "DE.Views.HeaderFooterSettings.textBottomPage": "ページの下部", "DE.Views.HeaderFooterSettings.textBottomRight": "右下", "DE.Views.HeaderFooterSettings.textDiffFirst": "先頭ページのみ別指定\t", "DE.Views.HeaderFooterSettings.textDiffOdd": "奇数/偶数ページ別指定", "DE.Views.HeaderFooterSettings.textFrom": "から開始", "DE.Views.HeaderFooterSettings.textHeaderFromBottom": "下からのフッター位置", "DE.Views.HeaderFooterSettings.textHeaderFromTop": "上からのヘッダー位置", "DE.Views.HeaderFooterSettings.textInsertCurrent": "現在の位置に挿入", "DE.Views.HeaderFooterSettings.textOptions": "オプション", "DE.Views.HeaderFooterSettings.textPageNum": "ページ番号の挿入", "DE.Views.HeaderFooterSettings.textPageNumbering": "ページ番号", "DE.Views.HeaderFooterSettings.textPosition": "位置", "DE.Views.HeaderFooterSettings.textPrev": "前のセクションから継続", "DE.Views.HeaderFooterSettings.textSameAs": "前と同じ​​ヘッダー/フッター", "DE.Views.HeaderFooterSettings.textTopCenter": "上中央", "DE.Views.HeaderFooterSettings.textTopLeft": "左上", "DE.Views.HeaderFooterSettings.textTopPage": "ページの上部", "DE.Views.HeaderFooterSettings.textTopRight": "右上", "DE.Views.HyperlinkSettingsDialog.textDefault": "選択されたテキストフラグメント", "DE.Views.HyperlinkSettingsDialog.textDisplay": "表示する", "DE.Views.HyperlinkSettingsDialog.textExternal": "外部リンク", "DE.Views.HyperlinkSettingsDialog.textInternal": "文書内の場所", "DE.Views.HyperlinkSettingsDialog.textTitle": "ハイパーリンクの設定", "DE.Views.HyperlinkSettingsDialog.textTooltip": "ヒントのテキスト:", "DE.Views.HyperlinkSettingsDialog.textUrl": "リンク先", "DE.Views.HyperlinkSettingsDialog.txtBeginning": "文書の先頭", "DE.Views.HyperlinkSettingsDialog.txtBookmarks": "ブックマーク", "DE.Views.HyperlinkSettingsDialog.txtEmpty": "この項目は必須です", "DE.Views.HyperlinkSettingsDialog.txtHeadings": "見出し", "DE.Views.HyperlinkSettingsDialog.txtNotUrl": "このフィールドは「http://www.example.com」の形式のURLである必要があります。", "DE.Views.HyperlinkSettingsDialog.txtSizeLimit": "このフィールドは最大2083文字に制限されています", "DE.Views.ImageSettings.textAdvanced": "詳細設定を表示", "DE.Views.ImageSettings.textCrop": "トリミング", "DE.Views.ImageSettings.textCropFill": "塗りつぶし", "DE.Views.ImageSettings.textCropFit": "収める", "DE.Views.ImageSettings.textCropToShape": "図形に合わせてトリミング", "DE.Views.ImageSettings.textEdit": "編集する", "DE.Views.ImageSettings.textEditObject": "オブジェクトを編集する", "DE.Views.ImageSettings.textFitMargins": "余白内に収まる", "DE.Views.ImageSettings.textFlip": "反転する", "DE.Views.ImageSettings.textFromFile": "ファイルから", "DE.Views.ImageSettings.textFromStorage": "ストレージから", "DE.Views.ImageSettings.textFromUrl": "URLから", "DE.Views.ImageSettings.textHeight": "高さ", "DE.Views.ImageSettings.textHint270": "反時計回りに90度回転", "DE.Views.ImageSettings.textHint90": "時計回りに90度回転", "DE.Views.ImageSettings.textHintFlipH": "左右に反転", "DE.Views.ImageSettings.textHintFlipV": "上下に反転", "DE.Views.ImageSettings.textInsert": "画像を置き換える", "DE.Views.ImageSettings.textOriginalSize": "実際のサイズ", "DE.Views.ImageSettings.textRecentlyUsed": "最近使用された", "DE.Views.ImageSettings.textRotate90": "90度回転", "DE.Views.ImageSettings.textRotation": "回転", "DE.Views.ImageSettings.textSize": "サイズ", "DE.Views.ImageSettings.textWidth": "幅", "DE.Views.ImageSettings.textWrap": "折り返しの種類と配置", "DE.Views.ImageSettings.txtBehind": "テキストの背後に", "DE.Views.ImageSettings.txtInFront": "テキストの前に", "DE.Views.ImageSettings.txtInline": "テキストに沿って", "DE.Views.ImageSettings.txtSquare": "四角", "DE.Views.ImageSettings.txtThrough": "内部", "DE.Views.ImageSettings.txtTight": "外周", "DE.Views.ImageSettings.txtTopAndBottom": "上と下", "DE.Views.ImageSettingsAdvanced.strMargins": "テキストの埋め込み文字", "DE.Views.ImageSettingsAdvanced.textAbsoluteWH": "アブソリュート", "DE.Views.ImageSettingsAdvanced.textAlignment": "配置", "DE.Views.ImageSettingsAdvanced.textAlt": "代替テキスト", "DE.Views.ImageSettingsAdvanced.textAltDescription": "説明", "DE.Views.ImageSettingsAdvanced.textAltTip": "代替テキストとは、表、図、画像などのオブジェクトが持つ情報の、テキストによる代替表現です。この情報は、視覚や認知機能に障碍があり、オブジェクトを見たり認識したりできない方の役に立ちます。", "DE.Views.ImageSettingsAdvanced.textAltTitle": "タイトル", "DE.Views.ImageSettingsAdvanced.textAngle": "角度", "DE.Views.ImageSettingsAdvanced.textArrows": "矢印", "DE.Views.ImageSettingsAdvanced.textAspectRatio": "縦横比の固定", "DE.Views.ImageSettingsAdvanced.textAutofit": "自動調整", "DE.Views.ImageSettingsAdvanced.textBeginSize": "開始サイズ", "DE.Views.ImageSettingsAdvanced.textBeginStyle": "開始スタイル", "DE.Views.ImageSettingsAdvanced.textBelow": "下", "DE.Views.ImageSettingsAdvanced.textBevel": "斜角", "DE.Views.ImageSettingsAdvanced.textBottom": "下", "DE.Views.ImageSettingsAdvanced.textBottomMargin": "下余白", "DE.Views.ImageSettingsAdvanced.textBtnWrap": "テキストの折り返し\t", "DE.Views.ImageSettingsAdvanced.textCapType": "キャップタイプ", "DE.Views.ImageSettingsAdvanced.textCenter": "中央揃え", "DE.Views.ImageSettingsAdvanced.textCharacter": "文字", "DE.Views.ImageSettingsAdvanced.textColumn": "列", "DE.Views.ImageSettingsAdvanced.textDistance": "文字列との間隔", "DE.Views.ImageSettingsAdvanced.textEndSize": "終了サイズ", "DE.Views.ImageSettingsAdvanced.textEndStyle": "終了スタイル", "DE.Views.ImageSettingsAdvanced.textFlat": "フラット", "DE.Views.ImageSettingsAdvanced.textFlipped": "反転済み", "DE.Views.ImageSettingsAdvanced.textHeight": "高さ", "DE.Views.ImageSettingsAdvanced.textHorizontal": "水平", "DE.Views.ImageSettingsAdvanced.textHorizontally": "水平に", "DE.Views.ImageSettingsAdvanced.textJoinType": "結合の種類", "DE.Views.ImageSettingsAdvanced.textKeepRatio": "一定の比率", "DE.Views.ImageSettingsAdvanced.textLeft": "左", "DE.Views.ImageSettingsAdvanced.textLeftMargin": "左余白", "DE.Views.ImageSettingsAdvanced.textLine": "線", "DE.Views.ImageSettingsAdvanced.textLineStyle": "線のスタイル", "DE.Views.ImageSettingsAdvanced.textMargin": "余白", "DE.Views.ImageSettingsAdvanced.textMiter": "留め継ぎ", "DE.Views.ImageSettingsAdvanced.textMove": "文字列と一緒に移動する", "DE.Views.ImageSettingsAdvanced.textOptions": "オプション", "DE.Views.ImageSettingsAdvanced.textOriginalSize": "実際のサイズ", "DE.Views.ImageSettingsAdvanced.textOverlap": "オーバーラップを許可する", "DE.Views.ImageSettingsAdvanced.textPage": "ページ", "DE.Views.ImageSettingsAdvanced.textParagraph": "段落", "DE.Views.ImageSettingsAdvanced.textPosition": "位置", "DE.Views.ImageSettingsAdvanced.textPositionPc": "相対位置", "DE.Views.ImageSettingsAdvanced.textRelative": "と相対", "DE.Views.ImageSettingsAdvanced.textRelativeWH": "相対的", "DE.Views.ImageSettingsAdvanced.textResizeFit": "テキストに合わせて図形を調整", "DE.Views.ImageSettingsAdvanced.textRight": "右", "DE.Views.ImageSettingsAdvanced.textRightMargin": "右余白", "DE.Views.ImageSettingsAdvanced.textRightOf": "の右に", "DE.Views.ImageSettingsAdvanced.textRotation": "回転", "DE.Views.ImageSettingsAdvanced.textRound": "円い", "DE.Views.ImageSettingsAdvanced.textShape": "図形の設定", "DE.Views.ImageSettingsAdvanced.textSize": "サイズ", "DE.Views.ImageSettingsAdvanced.textSquare": "四角", "DE.Views.ImageSettingsAdvanced.textTextBox": "テキストボックス", "DE.Views.ImageSettingsAdvanced.textTitle": "画像 - 詳細設定", "DE.Views.ImageSettingsAdvanced.textTitleChart": "チャートー詳細設定", "DE.Views.ImageSettingsAdvanced.textTitleShape": "図形 - 詳細設定", "DE.Views.ImageSettingsAdvanced.textTop": "トップ", "DE.Views.ImageSettingsAdvanced.textTopMargin": "上余白", "DE.Views.ImageSettingsAdvanced.textVertical": "垂直", "DE.Views.ImageSettingsAdvanced.textVertically": "縦に", "DE.Views.ImageSettingsAdvanced.textWeightArrows": "太さ&矢印", "DE.Views.ImageSettingsAdvanced.textWidth": "幅", "DE.Views.ImageSettingsAdvanced.textWrap": "折り返しの種類と配置", "DE.Views.ImageSettingsAdvanced.textWrapBehindTooltip": "テキストの背後に", "DE.Views.ImageSettingsAdvanced.textWrapInFrontTooltip": "テキストの前に", "DE.Views.ImageSettingsAdvanced.textWrapInlineTooltip": "テキストに沿って", "DE.Views.ImageSettingsAdvanced.textWrapSquareTooltip": "四角", "DE.Views.ImageSettingsAdvanced.textWrapThroughTooltip": "内部", "DE.Views.ImageSettingsAdvanced.textWrapTightTooltip": "外周", "DE.Views.ImageSettingsAdvanced.textWrapTopbottomTooltip": "上と下", "DE.Views.LeftMenu.tipAbout": "詳細情報", "DE.Views.LeftMenu.tipChat": "チャット", "DE.Views.LeftMenu.tipComments": "コメント", "DE.Views.LeftMenu.tipNavigation": "ナビゲーション", "DE.Views.LeftMenu.tipOutline": "見出し", "DE.Views.LeftMenu.tipPageThumbnails": "ページサムネイル", "DE.Views.LeftMenu.tipPlugins": "プラグイン", "DE.Views.LeftMenu.tipSearch": "検索", "DE.Views.LeftMenu.tipSupport": "フィードバック＆サポート", "DE.Views.LeftMenu.tipTitles": "タイトル", "DE.Views.LeftMenu.txtDeveloper": "開発者モード", "DE.Views.LeftMenu.txtEditor": "ドキュメントエディタ", "DE.Views.LeftMenu.txtLimit": "制限されたアクセス", "DE.Views.LeftMenu.txtTrial": "試用モード", "DE.Views.LeftMenu.txtTrialDev": "試用開発者モード", "DE.Views.LineNumbersDialog.textAddLineNumbering": "行番号を追加する", "DE.Views.LineNumbersDialog.textApplyTo": "に変更を適用する", "DE.Views.LineNumbersDialog.textContinuous": "継続的", "DE.Views.LineNumbersDialog.textCountBy": "行番号の増分", "DE.Views.LineNumbersDialog.textDocument": "全ての文書", "DE.Views.LineNumbersDialog.textForward": "このポイント以降", "DE.Views.LineNumbersDialog.textFromText": "テキストから", "DE.Views.LineNumbersDialog.textNumbering": "ナンバリング", "DE.Views.LineNumbersDialog.textRestartEachPage": "各ページに振り直し", "DE.Views.LineNumbersDialog.textRestartEachSection": "各セクションに振り直し", "DE.Views.LineNumbersDialog.textSection": "現在のセクション", "DE.Views.LineNumbersDialog.textStartAt": "から開始", "DE.Views.LineNumbersDialog.textTitle": "行番号", "DE.Views.LineNumbersDialog.txtAutoText": "自動", "DE.Views.Links.capBtnAddText": "テキスト追加", "DE.Views.Links.capBtnBookmarks": "ブックマーク", "DE.Views.Links.capBtnCaption": "キャプション", "DE.Views.Links.capBtnContentsUpdate": "テーブルの更新", "DE.Views.Links.capBtnCrossRef": "相互参照", "DE.Views.Links.capBtnInsContents": "目次", "DE.Views.Links.capBtnInsFootnote": "脚注", "DE.Views.Links.capBtnInsLink": "ハイパーリンク", "DE.Views.Links.capBtnTOF": "図表", "DE.Views.Links.confirmDeleteFootnotes": "すべての脚注を削除しますか？", "DE.Views.Links.confirmReplaceTOF": "選択した図表を置き換えますか？", "DE.Views.Links.mniConvertNote": "すべてのメモを変換する", "DE.Views.Links.mniDelFootnote": "すべての脚注を削除する", "DE.Views.Links.mniInsEndnote": "文末脚注を挿入", "DE.Views.Links.mniInsFootnote": "フットノートの挿入", "DE.Views.Links.mniNoteSettings": "ノートの設定", "DE.Views.Links.textContentsRemove": "目次の削除", "DE.Views.Links.textContentsSettings": "設定", "DE.Views.Links.textConvertToEndnotes": "すべての脚注を文末脚注に変換する", "DE.Views.Links.textConvertToFootnotes": "すべての文末脚注を脚注に変換する", "DE.Views.Links.textGotoEndnote": "文末脚注に移動する", "DE.Views.Links.textGotoFootnote": "脚注に移動する", "DE.Views.Links.textSwapNotes": "脚注と文末脚注を交換する", "DE.Views.Links.textUpdateAll": "テーブル全体の更新", "DE.Views.Links.textUpdatePages": "ページ番号のみの更新", "DE.Views.Links.tipAddText": "見出しを目次に入れる", "DE.Views.Links.tipBookmarks": "ブックマークの作成", "DE.Views.Links.tipCaption": "キャプションの挿入", "DE.Views.Links.tipContents": "目次を挿入", "DE.Views.Links.tipContentsUpdate": "目次を更新する", "DE.Views.Links.tipCrossRef": "相互参照を挿入", "DE.Views.Links.tipInsertHyperlink": "ハイパーリンクの追加", "DE.Views.Links.tipNotes": "フットノートを挿入または編集", "DE.Views.Links.tipTableFigures": "図表を挿入する", "DE.Views.Links.tipTableFiguresUpdate": "図表を更新する", "DE.Views.Links.titleUpdateTOF": "図表を更新する", "DE.Views.Links.txtDontShowTof": "目次に表示しない", "DE.Views.Links.txtLevel": "レベル", "DE.Views.ListSettingsDialog.textAuto": "自動", "DE.Views.ListSettingsDialog.textCenter": "中央揃え", "DE.Views.ListSettingsDialog.textLeft": "左", "DE.Views.ListSettingsDialog.textLevel": "レベル", "DE.Views.ListSettingsDialog.textPreview": "プレビュー", "DE.Views.ListSettingsDialog.textRight": "右", "DE.Views.ListSettingsDialog.txtAlign": "配置", "DE.Views.ListSettingsDialog.txtBullet": "箇条書き", "DE.Views.ListSettingsDialog.txtColor": "色", "DE.Views.ListSettingsDialog.txtFont": "フォントと記号", "DE.Views.ListSettingsDialog.txtLikeText": "テキストのように", "DE.Views.ListSettingsDialog.txtNewBullet": "新しい行頭文字", "DE.Views.ListSettingsDialog.txtNone": "なし", "DE.Views.ListSettingsDialog.txtSize": "サイズ", "DE.Views.ListSettingsDialog.txtSymbol": "記号", "DE.Views.ListSettingsDialog.txtTitle": "リストの設定", "DE.Views.ListSettingsDialog.txtType": "タイプ", "DE.Views.MailMergeEmailDlg.filePlaceholder": "PDF", "DE.Views.MailMergeEmailDlg.okButtonText": "送信", "DE.Views.MailMergeEmailDlg.subjectPlaceholder": "テーマ", "DE.Views.MailMergeEmailDlg.textAttachDocx": "DOCXとして添付する", "DE.Views.MailMergeEmailDlg.textAttachPdf": "PDFとして添付する", "DE.Views.MailMergeEmailDlg.textFileName": "ファイル名", "DE.Views.MailMergeEmailDlg.textFormat": "メール形式", "DE.Views.MailMergeEmailDlg.textFrom": "差出人", "DE.Views.MailMergeEmailDlg.textHTML": "HTML", "DE.Views.MailMergeEmailDlg.textMessage": "メッセージ", "DE.Views.MailMergeEmailDlg.textSubject": "件名", "DE.Views.MailMergeEmailDlg.textTitle": "メールに送信する", "DE.Views.MailMergeEmailDlg.textTo": "宛先", "DE.Views.MailMergeEmailDlg.textWarning": "警告！", "DE.Views.MailMergeEmailDlg.textWarningMsg": "「送信」ボタンをクリックするとメール送信を中止することはできません。", "DE.Views.MailMergeSettings.downloadMergeTitle": "結合中", "DE.Views.MailMergeSettings.errorMailMergeSaveFile": "結合に失敗しました。", "DE.Views.MailMergeSettings.notcriticalErrorTitle": "警告", "DE.Views.MailMergeSettings.textAddRecipients": "初めに数名の受信者をリストに追加する", "DE.Views.MailMergeSettings.textAll": "全ての記録", "DE.Views.MailMergeSettings.textCurrent": "現在の履歴", "DE.Views.MailMergeSettings.textDataSource": "データソース", "DE.Views.MailMergeSettings.textDocx": "Docx", "DE.Views.MailMergeSettings.textDownload": "ダウンロード", "DE.Views.MailMergeSettings.textEditData": "アドレス帳の編集", "DE.Views.MailMergeSettings.textEmail": "メール", "DE.Views.MailMergeSettings.textFrom": "から", "DE.Views.MailMergeSettings.textGoToMail": "メールに移動", "DE.Views.MailMergeSettings.textHighlight": "差し込みフィールドをハイライト", "DE.Views.MailMergeSettings.textInsertField": "差し込みフィールドの挿入", "DE.Views.MailMergeSettings.textMaxRecepients": "受信者の最大人数は100人です。", "DE.Views.MailMergeSettings.textMerge": "結合", "DE.Views.MailMergeSettings.textMergeFields": "差し込みフィールド", "DE.Views.MailMergeSettings.textMergeTo": "に結合", "DE.Views.MailMergeSettings.textPdf": "PDF", "DE.Views.MailMergeSettings.textPortal": "保存", "DE.Views.MailMergeSettings.textPreview": "プレビューの結果", "DE.Views.MailMergeSettings.textReadMore": "続きを読む", "DE.Views.MailMergeSettings.textSendMsg": "すべてのメールの準備ができました。 しばらくするとメッセージが送信されます。<br>配信速度はメールサーバにより変動します。<br>ドキュメントの作業を続けることも、閉じることもできます。操作終了後、登録したメールアドレスに通知が届きます。", "DE.Views.MailMergeSettings.textTo": "へ", "DE.Views.MailMergeSettings.txtFirst": "最初の記録へ", "DE.Views.MailMergeSettings.txtFromToError": "\"From \"の値は \"To \"の値よりも小さくなければなりません。", "DE.Views.MailMergeSettings.txtLast": "最後の記録へ", "DE.Views.MailMergeSettings.txtNext": "次の記録へ", "DE.Views.MailMergeSettings.txtPrev": "以前の記録へ", "DE.Views.MailMergeSettings.txtUntitled": "無題", "DE.Views.MailMergeSettings.warnProcessMailMerge": "マージの開始に失敗しました", "DE.Views.Navigation.strNavigate": "見出し", "DE.Views.Navigation.txtClosePanel": "見出しを閉じる", "DE.Views.Navigation.txtCollapse": "すべてを折りたたむ", "DE.Views.Navigation.txtDemote": "下げる", "DE.Views.Navigation.txtEmpty": "ドキュメントに見出しがありません。<br>目次に表示されるように、テキストに見出しスタイルを適用ください。", "DE.Views.Navigation.txtEmptyItem": "空白の見出し", "DE.Views.Navigation.txtEmptyViewer": "ドキュメントに見出しがありません。", "DE.Views.Navigation.txtExpand": "すべてを拡張する", "DE.Views.Navigation.txtExpandToLevel": "レベルまで拡張する", "DE.Views.Navigation.txtFontSize": "フォントのサイズ", "DE.Views.Navigation.txtHeadingAfter": "後の新しい見出し", "DE.Views.Navigation.txtHeadingBefore": "前の新しい見出し", "DE.Views.Navigation.txtLarge": "大", "DE.Views.Navigation.txtMedium": "中", "DE.Views.Navigation.txtNewHeading": "新しい小見出し", "DE.Views.Navigation.txtPromote": "促進", "DE.Views.Navigation.txtSelect": "コンテンツの選択", "DE.Views.Navigation.txtSettings": "見出しの設定", "DE.Views.Navigation.txtSmall": "小", "DE.Views.Navigation.txtWrapHeadings": "長い見出しを折り返す", "DE.Views.NoteSettingsDialog.textApply": "適用する", "DE.Views.NoteSettingsDialog.textApplyTo": "変更適用", "DE.Views.NoteSettingsDialog.textContinue": "継続的", "DE.Views.NoteSettingsDialog.textCustom": "カスタムマーク", "DE.Views.NoteSettingsDialog.textDocEnd": "文書の最後", "DE.Views.NoteSettingsDialog.textDocument": "全ての文書", "DE.Views.NoteSettingsDialog.textEachPage": "各ページに振り直し", "DE.Views.NoteSettingsDialog.textEachSection": "各セクションに振り直し", "DE.Views.NoteSettingsDialog.textEndnote": "文末脚注", "DE.Views.NoteSettingsDialog.textFootnote": "脚注", "DE.Views.NoteSettingsDialog.textFormat": "形式", "DE.Views.NoteSettingsDialog.textInsert": "挿入する", "DE.Views.NoteSettingsDialog.textLocation": "位置", "DE.Views.NoteSettingsDialog.textNumbering": "ナンバリング", "DE.Views.NoteSettingsDialog.textNumFormat": "数の書式", "DE.Views.NoteSettingsDialog.textPageBottom": "ページの下部", "DE.Views.NoteSettingsDialog.textSectEnd": "セクションの終わり", "DE.Views.NoteSettingsDialog.textSection": "現在のセクション", "DE.Views.NoteSettingsDialog.textStart": "から開始", "DE.Views.NoteSettingsDialog.textTextBottom": "テキストの下", "DE.Views.NoteSettingsDialog.textTitle": "ノートの設定", "DE.Views.NotesRemoveDialog.textEnd": "すべての文末脚注を削除", "DE.Views.NotesRemoveDialog.textFoot": "すべての文末脚注を削除", "DE.Views.NotesRemoveDialog.textTitle": "メモを削除する", "DE.Views.PageMarginsDialog.notcriticalErrorTitle": "警告", "DE.Views.PageMarginsDialog.textBottom": "下", "DE.Views.PageMarginsDialog.textGutter": "とじしろ", "DE.Views.PageMarginsDialog.textGutterPosition": "とじしろの位置", "DE.Views.PageMarginsDialog.textInside": "内部", "DE.Views.PageMarginsDialog.textLandscape": "横向き", "DE.Views.PageMarginsDialog.textLeft": "左", "DE.Views.PageMarginsDialog.textMirrorMargins": "左右対称の余白", "DE.Views.PageMarginsDialog.textMultiplePages": "複数ページ", "DE.Views.PageMarginsDialog.textNormal": "正常", "DE.Views.PageMarginsDialog.textOrientation": "印刷の向き", "DE.Views.PageMarginsDialog.textOutside": "外面", "DE.Views.PageMarginsDialog.textPortrait": "縦向き", "DE.Views.PageMarginsDialog.textPreview": "プレビュー", "DE.Views.PageMarginsDialog.textRight": "右", "DE.Views.PageMarginsDialog.textTitle": "余白", "DE.Views.PageMarginsDialog.textTop": "トップ", "DE.Views.PageMarginsDialog.txtMarginsH": "指定されたページの高さ対して、上下の余白が大きすぎます。", "DE.Views.PageMarginsDialog.txtMarginsW": "ページ幅に対して左右の余白が広すぎます。", "DE.Views.PageSizeDialog.textHeight": "高さ", "DE.Views.PageSizeDialog.textPreset": "あらかじめ設定された", "DE.Views.PageSizeDialog.textTitle": "ページのサイズ", "DE.Views.PageSizeDialog.textWidth": "幅", "DE.Views.PageSizeDialog.txtCustom": "カスタム", "DE.Views.PageThumbnails.textClosePanel": "ページサムネイルを閉じる", "DE.Views.PageThumbnails.textHighlightVisiblePart": "表示されているページをハイライト", "DE.Views.PageThumbnails.textPageThumbnails": "ページサムネイル", "DE.Views.PageThumbnails.textThumbnailsSettings": "サムネイルの設定", "DE.Views.PageThumbnails.textThumbnailsSize": "サムネイルサイズ", "DE.Views.ParagraphSettings.strIndent": "インデント", "DE.Views.ParagraphSettings.strIndentsLeftText": "左", "DE.Views.ParagraphSettings.strIndentsRightText": "右", "DE.Views.ParagraphSettings.strIndentsSpecial": "特殊", "DE.Views.ParagraphSettings.strLineHeight": "行間", "DE.Views.ParagraphSettings.strParagraphSpacing": "段落間隔", "DE.Views.ParagraphSettings.strSomeParagraphSpace": "同じスタイルの場合は、段落間に間隔を追加しません。", "DE.Views.ParagraphSettings.strSpacingAfter": "後に", "DE.Views.ParagraphSettings.strSpacingBefore": "前", "DE.Views.ParagraphSettings.textAdvanced": "詳細設定を表示", "DE.Views.ParagraphSettings.textAt": "行間", "DE.Views.ParagraphSettings.textAtLeast": "最小", "DE.Views.ParagraphSettings.textAuto": "倍数", "DE.Views.ParagraphSettings.textBackColor": "背景色", "DE.Views.ParagraphSettings.textExact": "固定値", "DE.Views.ParagraphSettings.textFirstLine": "最初の行", "DE.Views.ParagraphSettings.textHanging": "ぶら下げ", "DE.Views.ParagraphSettings.textNoneSpecial": "（なし）", "DE.Views.ParagraphSettings.txtAutoText": "自動", "DE.Views.ParagraphSettingsAdvanced.noTabs": "指定されたタブは、このフィールドに表示されます。", "DE.Views.ParagraphSettingsAdvanced.strAllCaps": "全ての英大文字", "DE.Views.ParagraphSettingsAdvanced.strBorders": "罫線と塗りつぶし", "DE.Views.ParagraphSettingsAdvanced.strBreakBefore": "前に改ページ", "DE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "二重取り消し線", "DE.Views.ParagraphSettingsAdvanced.strIndent": "インデント", "DE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "左", "DE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "行間", "DE.Views.ParagraphSettingsAdvanced.strIndentsOutlinelevel": "アウトラインレベル", "DE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "右", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "後に", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "前", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "特殊", "DE.Views.ParagraphSettingsAdvanced.strKeepLines": "段落を分割しない", "DE.Views.ParagraphSettingsAdvanced.strKeepNext": "次の段落と分離しない", "DE.Views.ParagraphSettingsAdvanced.strMargins": "埋め込み文字", "DE.Views.ParagraphSettingsAdvanced.strOrphan": "改ページ時 1 行残して段落を区切らない", "DE.Views.ParagraphSettingsAdvanced.strParagraphFont": "フォント", "DE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "インデント＆行間隔", "DE.Views.ParagraphSettingsAdvanced.strParagraphLine": "改行＆改ページ", "DE.Views.ParagraphSettingsAdvanced.strParagraphPosition": "位置", "DE.Views.ParagraphSettingsAdvanced.strSmallCaps": "小型英大文字", "DE.Views.ParagraphSettingsAdvanced.strSomeParagraphSpace": "同じスタイルの場合は、段落間に間隔を追加しません。", "DE.Views.ParagraphSettingsAdvanced.strSpacing": "間隔", "DE.Views.ParagraphSettingsAdvanced.strStrike": "取り消し線", "DE.Views.ParagraphSettingsAdvanced.strSubscript": "下付き文字", "DE.Views.ParagraphSettingsAdvanced.strSuperscript": "上付き文字", "DE.Views.ParagraphSettingsAdvanced.strSuppressLineNumbers": "行番号を表示しない", "DE.Views.ParagraphSettingsAdvanced.strTabs": "タブ", "DE.Views.ParagraphSettingsAdvanced.textAlign": "配置", "DE.Views.ParagraphSettingsAdvanced.textAll": "すべて", "DE.Views.ParagraphSettingsAdvanced.textAtLeast": "最小", "DE.Views.ParagraphSettingsAdvanced.textAuto": "倍数", "DE.Views.ParagraphSettingsAdvanced.textBackColor": "背景色", "DE.Views.ParagraphSettingsAdvanced.textBodyText": "基本テキスト", "DE.Views.ParagraphSettingsAdvanced.textBorderColor": "罫線の色", "DE.Views.ParagraphSettingsAdvanced.textBorderDesc": "図表をクリックするか、ボタンで枠を選択し、選択したスタイルを適用します。", "DE.Views.ParagraphSettingsAdvanced.textBorderWidth": "罫線のサイズ", "DE.Views.ParagraphSettingsAdvanced.textBottom": "下", "DE.Views.ParagraphSettingsAdvanced.textCentered": "中央揃え済み", "DE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "文字間隔", "DE.Views.ParagraphSettingsAdvanced.textContext": "コンテキスト合字", "DE.Views.ParagraphSettingsAdvanced.textContextDiscret": "コンテキストおよび随意合字", "DE.Views.ParagraphSettingsAdvanced.textContextHistDiscret": "コンテキスト、履歴および随意合字", "DE.Views.ParagraphSettingsAdvanced.textContextHistorical": "コンテキストおよび履歴合字", "DE.Views.ParagraphSettingsAdvanced.textDefault": "デフォルトのタブ", "DE.Views.ParagraphSettingsAdvanced.textDiscret": "随意合字", "DE.Views.ParagraphSettingsAdvanced.textEffects": "エフェクト", "DE.Views.ParagraphSettingsAdvanced.textExact": "固定値", "DE.Views.ParagraphSettingsAdvanced.textFirstLine": "最初の行", "DE.Views.ParagraphSettingsAdvanced.textHanging": "ぶら下げ", "DE.Views.ParagraphSettingsAdvanced.textHistorical": "歴史的合字", "DE.Views.ParagraphSettingsAdvanced.textHistoricalDiscret": "歴史的合字と随意合字", "DE.Views.ParagraphSettingsAdvanced.textJustified": "両端揃え", "DE.Views.ParagraphSettingsAdvanced.textLeader": "埋め草文字", "DE.Views.ParagraphSettingsAdvanced.textLeft": "左", "DE.Views.ParagraphSettingsAdvanced.textLevel": "レベル", "DE.Views.ParagraphSettingsAdvanced.textLigatures": "合字", "DE.Views.ParagraphSettingsAdvanced.textNone": "なし", "DE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "（なし）", "DE.Views.ParagraphSettingsAdvanced.textOpenType": "OpenTypeフォント特性", "DE.Views.ParagraphSettingsAdvanced.textPosition": "位置", "DE.Views.ParagraphSettingsAdvanced.textRemove": "削除", "DE.Views.ParagraphSettingsAdvanced.textRemoveAll": "全てを削除", "DE.Views.ParagraphSettingsAdvanced.textRight": "右", "DE.Views.ParagraphSettingsAdvanced.textSet": "指定", "DE.Views.ParagraphSettingsAdvanced.textSpacing": "間隔", "DE.Views.ParagraphSettingsAdvanced.textStandard": "標準合字のみ", "DE.Views.ParagraphSettingsAdvanced.textStandardContext": "標準合字およびコンテキスト合字", "DE.Views.ParagraphSettingsAdvanced.textStandardContextDiscret": "標準、コンテキストおよび随意合字", "DE.Views.ParagraphSettingsAdvanced.textStandardContextHist": "標準、コンテキストおよび履歴合字", "DE.Views.ParagraphSettingsAdvanced.textStandardDiscret": "標準および随意合字", "DE.Views.ParagraphSettingsAdvanced.textStandardHistDiscret": "標準、履歴および随意合字", "DE.Views.ParagraphSettingsAdvanced.textStandardHistorical": "標準および履歴合字", "DE.Views.ParagraphSettingsAdvanced.textTabCenter": "中央揃え", "DE.Views.ParagraphSettingsAdvanced.textTabLeft": "左", "DE.Views.ParagraphSettingsAdvanced.textTabPosition": "タブの位置", "DE.Views.ParagraphSettingsAdvanced.textTabRight": "右", "DE.Views.ParagraphSettingsAdvanced.textTitle": "段落 - 詳細設定", "DE.Views.ParagraphSettingsAdvanced.textTop": "トップ", "DE.Views.ParagraphSettingsAdvanced.tipAll": "外枠とすべての内枠の線を設定", "DE.Views.ParagraphSettingsAdvanced.tipBottom": "下罫線のみを設定", "DE.Views.ParagraphSettingsAdvanced.tipInner": "水平方向の内側の線のみを設定", "DE.Views.ParagraphSettingsAdvanced.tipLeft": "左縁だけを設定", "DE.Views.ParagraphSettingsAdvanced.tipNone": "罫線の設定なし", "DE.Views.ParagraphSettingsAdvanced.tipOuter": "外枠の罫線だけを設定", "DE.Views.ParagraphSettingsAdvanced.tipRight": "右罫線だけを設定", "DE.Views.ParagraphSettingsAdvanced.tipTop": "上罫線だけを設定", "DE.Views.ParagraphSettingsAdvanced.txtAutoText": "自動", "DE.Views.ParagraphSettingsAdvanced.txtNoBorders": "罫線なし", "DE.Views.PrintWithPreview.textMarginsLast": "最後に適用した設定", "DE.Views.PrintWithPreview.textMarginsModerate": "中", "DE.Views.PrintWithPreview.textMarginsNarrow": "狭い", "DE.Views.PrintWithPreview.textMarginsNormal": "標準", "DE.Views.PrintWithPreview.textMarginsUsNormal": "ノーマル（アメリカの標準）", "DE.Views.PrintWithPreview.textMarginsWide": "広い", "DE.Views.PrintWithPreview.txtAllPages": "全ページ", "DE.Views.PrintWithPreview.txtBottom": "下", "DE.Views.PrintWithPreview.txtCurrentPage": "現在のページ", "DE.Views.PrintWithPreview.txtCustom": "ユーザー設定", "DE.Views.PrintWithPreview.txtCustomPages": "カスタム印刷", "DE.Views.PrintWithPreview.txtLandscape": "横", "DE.Views.PrintWithPreview.txtLeft": "左", "DE.Views.PrintWithPreview.txtMargins": "余白", "DE.Views.PrintWithPreview.txtOf": "{0}から", "DE.Views.PrintWithPreview.txtPage": "ページ", "DE.Views.PrintWithPreview.txtPageNumInvalid": "ページ番号が正しくありません。", "DE.Views.PrintWithPreview.txtPageOrientation": "印刷の向き", "DE.Views.PrintWithPreview.txtPages": "ページ", "DE.Views.PrintWithPreview.txtPageSize": "ページのサイズ", "DE.Views.PrintWithPreview.txtPortrait": "縦", "DE.Views.PrintWithPreview.txtPrint": "印刷", "DE.Views.PrintWithPreview.txtPrintPdf": "PDFに印刷", "DE.Views.PrintWithPreview.txtPrintRange": "印刷範囲\t", "DE.Views.PrintWithPreview.txtRight": "右", "DE.Views.PrintWithPreview.txtSelection": "選択", "DE.Views.PrintWithPreview.txtTop": "上", "DE.Views.ProtectDialog.textComments": "コメント", "DE.Views.ProtectDialog.textForms": "フォームの入力", "DE.Views.ProtectDialog.textReview": "変更履歴", "DE.Views.ProtectDialog.textView": "変更不可 (閲覧のみ)", "DE.Views.ProtectDialog.txtAllow": "ユーザーに許可する編集の種類を指定する", "DE.Views.ProtectDialog.txtIncorrectPwd": "先に入力したパスワードと一致しません。", "DE.Views.ProtectDialog.txtOptional": "任意", "DE.Views.ProtectDialog.txtPassword": "パスワード", "DE.Views.ProtectDialog.txtProtect": "保護する", "DE.Views.ProtectDialog.txtRepeat": "パスワードを再入力", "DE.Views.ProtectDialog.txtTitle": "保護する", "DE.Views.ProtectDialog.txtWarning": "ご注意：パスワードを紛失したり、忘れたりした場合は、復旧できません。安全な場所に保管してください。", "DE.Views.RightMenu.txtChartSettings": "チャート設定", "DE.Views.RightMenu.txtFormSettings": "フォーム設定", "DE.Views.RightMenu.txtHeaderFooterSettings": "ヘッダーとフッターの設定", "DE.Views.RightMenu.txtImageSettings": "画像の設定", "DE.Views.RightMenu.txtMailMergeSettings": "差し込み印刷の設定", "DE.Views.RightMenu.txtParagraphSettings": "段落の設定", "DE.Views.RightMenu.txtShapeSettings": "図形の設定", "DE.Views.RightMenu.txtSignatureSettings": "署名の設定", "DE.Views.RightMenu.txtTableSettings": "テーブルの設定", "DE.Views.RightMenu.txtTextArtSettings": "テキストアートの設定", "DE.Views.ShapeSettings.strBackground": "背景色", "DE.Views.ShapeSettings.strChange": "オートシェイプの変更", "DE.Views.ShapeSettings.strColor": "色", "DE.Views.ShapeSettings.strFill": "塗りつぶし", "DE.Views.ShapeSettings.strForeground": "前景色", "DE.Views.ShapeSettings.strPattern": "パターン", "DE.Views.ShapeSettings.strShadow": "影を表示する", "DE.Views.ShapeSettings.strSize": "サイズ", "DE.Views.ShapeSettings.strStroke": "線", "DE.Views.ShapeSettings.strTransparency": "不透明度", "DE.Views.ShapeSettings.strType": "タイプ", "DE.Views.ShapeSettings.textAdvanced": "詳細設定を表示", "DE.Views.ShapeSettings.textAngle": "角度", "DE.Views.ShapeSettings.textBorderSizeErr": "入力された値が正しくありません。<br>0〜1584の数値を入力してください。", "DE.Views.ShapeSettings.textColor": "色で塗りつぶし", "DE.Views.ShapeSettings.textDirection": "方向", "DE.Views.ShapeSettings.textEmptyPattern": "パターンなし", "DE.Views.ShapeSettings.textFlip": "反転する", "DE.Views.ShapeSettings.textFromFile": "ファイルから", "DE.Views.ShapeSettings.textFromStorage": "ストレージから", "DE.Views.ShapeSettings.textFromUrl": "URLから", "DE.Views.ShapeSettings.textGradient": "グラデーションポイント", "DE.Views.ShapeSettings.textGradientFill": "塗りつぶし (グラデーション)", "DE.Views.ShapeSettings.textHint270": "反時計回りに90度回転", "DE.Views.ShapeSettings.textHint90": "時計回りに90度回転", "DE.Views.ShapeSettings.textHintFlipH": "左右に反転", "DE.Views.ShapeSettings.textHintFlipV": "上下に反転", "DE.Views.ShapeSettings.textImageTexture": "図またはテクスチャ", "DE.Views.ShapeSettings.textLinear": "線形", "DE.Views.ShapeSettings.textNoFill": "塗りつぶしなし", "DE.Views.ShapeSettings.textPatternFill": "パターン", "DE.Views.ShapeSettings.textPosition": "位置", "DE.Views.ShapeSettings.textRadial": "ラジアル", "DE.Views.ShapeSettings.textRecentlyUsed": "最近使用された", "DE.Views.ShapeSettings.textRotate90": "90度回転", "DE.Views.ShapeSettings.textRotation": "回転", "DE.Views.ShapeSettings.textSelectImage": "画像の選択", "DE.Views.ShapeSettings.textSelectTexture": "選択する", "DE.Views.ShapeSettings.textStretch": "ストレッチ", "DE.Views.ShapeSettings.textStyle": "スタイル", "DE.Views.ShapeSettings.textTexture": "テクスチャから", "DE.Views.ShapeSettings.textTile": "タイル", "DE.Views.ShapeSettings.textWrap": "折り返しの種類と配置", "DE.Views.ShapeSettings.tipAddGradientPoint": "グラデーションポイントを追加する", "DE.Views.ShapeSettings.tipRemoveGradientPoint": "グラデーションポイントを削除する", "DE.Views.ShapeSettings.txtBehind": "テキストの背後に", "DE.Views.ShapeSettings.txtBrownPaper": "クラフト紙", "DE.Views.ShapeSettings.txtCanvas": "キャンバス", "DE.Views.ShapeSettings.txtCarton": "カートン", "DE.Views.ShapeSettings.txtDarkFabric": "ダークファブリック", "DE.Views.ShapeSettings.txtGrain": "粒子", "DE.Views.ShapeSettings.txtGranite": "花崗岩", "DE.Views.ShapeSettings.txtGreyPaper": "グレー紙", "DE.Views.ShapeSettings.txtInFront": "テキストの前に", "DE.Views.ShapeSettings.txtInline": "テキストに沿って", "DE.Views.ShapeSettings.txtKnit": "ニット", "DE.Views.ShapeSettings.txtLeather": "レザー", "DE.Views.ShapeSettings.txtNoBorders": "線なし", "DE.Views.ShapeSettings.txtPapyrus": "パピルス", "DE.Views.ShapeSettings.txtSquare": "四角", "DE.Views.ShapeSettings.txtThrough": "内部", "DE.Views.ShapeSettings.txtTight": "外周", "DE.Views.ShapeSettings.txtTopAndBottom": "上と下", "DE.Views.ShapeSettings.txtWood": "木", "DE.Views.SignatureSettings.notcriticalErrorTitle": "警告", "DE.Views.SignatureSettings.strDelete": "署名の削除", "DE.Views.SignatureSettings.strDetails": "署名の詳細", "DE.Views.SignatureSettings.strInvalid": "無効な署名", "DE.Views.SignatureSettings.strRequested": "要求された署名", "DE.Views.SignatureSettings.strSetup": "署名の設定", "DE.Views.SignatureSettings.strSign": "署名する", "DE.Views.SignatureSettings.strSignature": "署名", "DE.Views.SignatureSettings.strSigner": "署名者", "DE.Views.SignatureSettings.strValid": "有効な署名", "DE.Views.SignatureSettings.txtContinueEditing": "無視して編集する", "DE.Views.SignatureSettings.txtEditWarning": "編集すると、文書から署名が削除されます。<br>続行しますか？", "DE.Views.SignatureSettings.txtRemoveWarning": "この署名を削除しますか？<br>この操作は元に戻せません。", "DE.Views.SignatureSettings.txtRequestedSignatures": "この文書には署名が必要です。", "DE.Views.SignatureSettings.txtSigned": "有効な署名がドキュメントに追加されました。 ドキュメントは編集されないように保護されています。", "DE.Views.SignatureSettings.txtSignedInvalid": "文書のデジタル署名の一部が無効であるか、検証できませんでした。 文書は編集できないように保護されています。", "DE.Views.Statusbar.goToPageText": "ページに移動", "DE.Views.Statusbar.pageIndexText": "{0}/{1} ページ", "DE.Views.Statusbar.tipFitPage": "ページに合わせる", "DE.Views.Statusbar.tipFitWidth": "幅に合わせる", "DE.Views.Statusbar.tipHandTool": "「手のひら」ツール", "DE.Views.Statusbar.tipSelectTool": "選択ツール", "DE.Views.Statusbar.tipSetLang": "テキストの言語を設定", "DE.Views.Statusbar.tipZoomFactor": "ズーム", "DE.Views.Statusbar.tipZoomIn": "ズームイン", "DE.Views.Statusbar.tipZoomOut": "ズームアウト", "DE.Views.Statusbar.txtPageNumInvalid": "ページ番号が正しくありません。", "DE.Views.StyleTitleDialog.textHeader": "新しいスタイルの作成", "DE.Views.StyleTitleDialog.textNextStyle": "次の段落スタイル", "DE.Views.StyleTitleDialog.textTitle": "タイトル", "DE.Views.StyleTitleDialog.txtEmpty": "この項目は必須です", "DE.Views.StyleTitleDialog.txtNotEmpty": "フィールドは空にできません。", "DE.Views.StyleTitleDialog.txtSameAs": "新規作成したスタイルと同じ", "DE.Views.TableFormulaDialog.textBookmark": "ブックマークの貼り付け", "DE.Views.TableFormulaDialog.textFormat": "数の書式", "DE.Views.TableFormulaDialog.textFormula": "数式", "DE.Views.TableFormulaDialog.textInsertFunction": "関数の貼り付け", "DE.Views.TableFormulaDialog.textTitle": "数式設定", "DE.Views.TableOfContentsSettings.strAlign": "ページ番号の右揃え", "DE.Views.TableOfContentsSettings.strFullCaption": "ラベルと番号を含める", "DE.Views.TableOfContentsSettings.strLinks": "目次をリンクとして書式設定する", "DE.Views.TableOfContentsSettings.strLinksOF": "図表をリンクとして書式設定する", "DE.Views.TableOfContentsSettings.strShowPages": "ページ番号の表示", "DE.Views.TableOfContentsSettings.textBuildTable": "目次の作成要素：", "DE.Views.TableOfContentsSettings.textBuildTableOF": "次から図表を作成する", "DE.Views.TableOfContentsSettings.textEquation": "方程式\t", "DE.Views.TableOfContentsSettings.textFigure": "図形", "DE.Views.TableOfContentsSettings.textLeader": "埋め草文字", "DE.Views.TableOfContentsSettings.textLevel": "レベル", "DE.Views.TableOfContentsSettings.textLevels": "レベル", "DE.Views.TableOfContentsSettings.textNone": "なし", "DE.Views.TableOfContentsSettings.textRadioCaption": "キャプション", "DE.Views.TableOfContentsSettings.textRadioLevels": "アウトラインレベル", "DE.Views.TableOfContentsSettings.textRadioStyle": "スタイル", "DE.Views.TableOfContentsSettings.textRadioStyles": "選択されたスタイル", "DE.Views.TableOfContentsSettings.textStyle": "スタイル", "DE.Views.TableOfContentsSettings.textStyles": "スタイル", "DE.Views.TableOfContentsSettings.textTable": "テーブル", "DE.Views.TableOfContentsSettings.textTitle": "目次", "DE.Views.TableOfContentsSettings.textTitleTOF": "図表", "DE.Views.TableOfContentsSettings.txtCentered": "中央揃え済み", "DE.Views.TableOfContentsSettings.txtClassic": "クラシック", "DE.Views.TableOfContentsSettings.txtCurrent": "現在", "DE.Views.TableOfContentsSettings.txtDistinctive": "特徴的", "DE.Views.TableOfContentsSettings.txtFormal": "フォーマル", "DE.Views.TableOfContentsSettings.txtModern": "モダン", "DE.Views.TableOfContentsSettings.txtOnline": "オンライン", "DE.Views.TableOfContentsSettings.txtSimple": "簡単な", "DE.Views.TableOfContentsSettings.txtStandard": "標準", "DE.Views.TableSettings.deleteColumnText": "列の削除", "DE.Views.TableSettings.deleteRowText": "行の削除", "DE.Views.TableSettings.deleteTableText": "表の削除", "DE.Views.TableSettings.insertColumnLeftText": "左に列を挿入", "DE.Views.TableSettings.insertColumnRightText": "右に列を挿入", "DE.Views.TableSettings.insertRowAboveText": "上に行を挿入", "DE.Views.TableSettings.insertRowBelowText": "下に行を挿入", "DE.Views.TableSettings.mergeCellsText": "セルの結合", "DE.Views.TableSettings.selectCellText": "セルの選択", "DE.Views.TableSettings.selectColumnText": "列の選択", "DE.Views.TableSettings.selectRowText": "行の選択", "DE.Views.TableSettings.selectTableText": "テーブルの選択", "DE.Views.TableSettings.splitCellsText": "セルを分割...", "DE.Views.TableSettings.splitCellTitleText": "セルを分割", "DE.Views.TableSettings.strRepeatRow": "各ページの上部に見出し行として繰り返す", "DE.Views.TableSettings.textAddFormula": "式を追加", "DE.Views.TableSettings.textAdvanced": "詳細設定を表示", "DE.Views.TableSettings.textBackColor": "背景色", "DE.Views.TableSettings.textBanded": "縞模様", "DE.Views.TableSettings.textBorderColor": "色", "DE.Views.TableSettings.textBorders": "罫線のスタイル", "DE.Views.TableSettings.textCellSize": "行と列のサイズ", "DE.Views.TableSettings.textColumns": "列", "DE.Views.TableSettings.textConvert": "表を文字に変換する", "DE.Views.TableSettings.textDistributeCols": "列の幅を揃える", "DE.Views.TableSettings.textDistributeRows": "行の高さを揃える", "DE.Views.TableSettings.textEdit": "行/列", "DE.Views.TableSettings.textEmptyTemplate": "テンプレートなし", "DE.Views.TableSettings.textFirst": "最初の", "DE.Views.TableSettings.textHeader": "ヘッダー", "DE.Views.TableSettings.textHeight": "高さ", "DE.Views.TableSettings.textLast": "最後", "DE.Views.TableSettings.textRows": "行", "DE.Views.TableSettings.textSelectBorders": "選択したスタイルを適用する罫線を選択してください。 ", "DE.Views.TableSettings.textTemplate": "テンプレートから選択する", "DE.Views.TableSettings.textTotal": "合計", "DE.Views.TableSettings.textWidth": "幅", "DE.Views.TableSettings.tipAll": "外枠とすべての内枠の線を設定", "DE.Views.TableSettings.tipBottom": "外部の罫線（下）だけを設定", "DE.Views.TableSettings.tipInner": "内側の線のみを設定", "DE.Views.TableSettings.tipInnerHor": "水平方向の内側の線のみを設定", "DE.Views.TableSettings.tipInnerVert": "縦方向の内線のみを設定", "DE.Views.TableSettings.tipLeft": "外部の罫線（左）だけを設定", "DE.Views.TableSettings.tipNone": "罫線の設定なし", "DE.Views.TableSettings.tipOuter": "外枠の罫線だけを設定", "DE.Views.TableSettings.tipRight": "外部の罫線（右）だけを設定", "DE.Views.TableSettings.tipTop": "外部の罫線（上）だけを設定", "DE.Views.TableSettings.txtGroupTable_BorderedAndLined": "境界＆線付き表", "DE.Views.TableSettings.txtGroupTable_Custom": "カスタム", "DE.Views.TableSettings.txtGroupTable_Grid": "グリッド テーブル", "DE.Views.TableSettings.txtGroupTable_List": "リストの表", "DE.Views.TableSettings.txtGroupTable_Plain": "標準の表", "DE.Views.TableSettings.txtNoBorders": "罫線なし", "DE.Views.TableSettings.txtTable_Accent": "アクセント", "DE.Views.TableSettings.txtTable_Bordered": "境界付き", "DE.Views.TableSettings.txtTable_BorderedAndLined": "境界＆線付き", "DE.Views.TableSettings.txtTable_Colorful": "カラフル", "DE.Views.TableSettings.txtTable_Dark": "暗い", "DE.Views.TableSettings.txtTable_GridTable": "グリッドテーブル", "DE.Views.TableSettings.txtTable_Light": "明るい", "DE.Views.TableSettings.txtTable_Lined": "線付き", "DE.Views.TableSettings.txtTable_ListTable": "リスト表", "DE.Views.TableSettings.txtTable_PlainTable": "通常のテーブル", "DE.Views.TableSettings.txtTable_TableGrid": "テーブルの枠線", "DE.Views.TableSettingsAdvanced.textAlign": "配置", "DE.Views.TableSettingsAdvanced.textAlignment": "配置", "DE.Views.TableSettingsAdvanced.textAllowSpacing": "セルの間隔を指定する", "DE.Views.TableSettingsAdvanced.textAlt": "代替テキスト", "DE.Views.TableSettingsAdvanced.textAltDescription": "説明", "DE.Views.TableSettingsAdvanced.textAltTip": "代替テキストとは、表、図、画像などのオブジェクトが持つ情報の、テキストによる代替表現です。この情報は、視覚や認知機能に障碍があり、オブジェクトを見たり認識したりできない方の役に立ちます。", "DE.Views.TableSettingsAdvanced.textAltTitle": "タイトル", "DE.Views.TableSettingsAdvanced.textAnchorText": "テキスト", "DE.Views.TableSettingsAdvanced.textAutofit": "自動的にセルのサイズを変更する", "DE.Views.TableSettingsAdvanced.textBackColor": "セルの背景", "DE.Views.TableSettingsAdvanced.textBelow": "下", "DE.Views.TableSettingsAdvanced.textBorderColor": "罫線の色", "DE.Views.TableSettingsAdvanced.textBorderDesc": "図表をクリックするか、ボタンで枠を選択し、選択したスタイルを適用します。", "DE.Views.TableSettingsAdvanced.textBordersBackgroung": "罫線と背景", "DE.Views.TableSettingsAdvanced.textBorderWidth": "罫線のサイズ", "DE.Views.TableSettingsAdvanced.textBottom": "下", "DE.Views.TableSettingsAdvanced.textCellOptions": "セルのオプション", "DE.Views.TableSettingsAdvanced.textCellProps": "セル", "DE.Views.TableSettingsAdvanced.textCellSize": "セルのサイズ", "DE.Views.TableSettingsAdvanced.textCenter": "中央揃え", "DE.Views.TableSettingsAdvanced.textCenterTooltip": "中央揃え", "DE.Views.TableSettingsAdvanced.textCheckMargins": "既定の余白を使用", "DE.Views.TableSettingsAdvanced.textDefaultMargins": "デフォルトのセルの余白", "DE.Views.TableSettingsAdvanced.textDistance": "文字列との間隔", "DE.Views.TableSettingsAdvanced.textHorizontal": "水平", "DE.Views.TableSettingsAdvanced.textIndLeft": "左端からのインデント", "DE.Views.TableSettingsAdvanced.textLeft": "左", "DE.Views.TableSettingsAdvanced.textLeftTooltip": "左", "DE.Views.TableSettingsAdvanced.textMargin": "余白", "DE.Views.TableSettingsAdvanced.textMargins": "セル内の余白", "DE.Views.TableSettingsAdvanced.textMeasure": "測定", "DE.Views.TableSettingsAdvanced.textMove": "文字列と一緒に移動する", "DE.Views.TableSettingsAdvanced.textOnlyCells": "選択されたセルだけに適応", "DE.Views.TableSettingsAdvanced.textOptions": "オプション", "DE.Views.TableSettingsAdvanced.textOverlap": "オーバーラップを許可する", "DE.Views.TableSettingsAdvanced.textPage": "ページ", "DE.Views.TableSettingsAdvanced.textPosition": "位置", "DE.Views.TableSettingsAdvanced.textPrefWidth": "希望する幅", "DE.Views.TableSettingsAdvanced.textPreview": "プレビュー", "DE.Views.TableSettingsAdvanced.textRelative": "と相対", "DE.Views.TableSettingsAdvanced.textRight": "右", "DE.Views.TableSettingsAdvanced.textRightOf": "の右に", "DE.Views.TableSettingsAdvanced.textRightTooltip": "右", "DE.Views.TableSettingsAdvanced.textTable": "テーブル", "DE.Views.TableSettingsAdvanced.textTableBackColor": "テーブルの背景", "DE.Views.TableSettingsAdvanced.textTablePosition": "テーブルの位置", "DE.Views.TableSettingsAdvanced.textTableSize": "テーブルのサイズ", "DE.Views.TableSettingsAdvanced.textTitle": "テーブル - 詳細設定", "DE.Views.TableSettingsAdvanced.textTop": "トップ", "DE.Views.TableSettingsAdvanced.textVertical": "垂直", "DE.Views.TableSettingsAdvanced.textWidth": "幅", "DE.Views.TableSettingsAdvanced.textWidthSpaces": "幅&スペース", "DE.Views.TableSettingsAdvanced.textWrap": "テキストの折り返し\t", "DE.Views.TableSettingsAdvanced.textWrapNoneTooltip": "インラインテーブル", "DE.Views.TableSettingsAdvanced.textWrapParallelTooltip": "フローテーブル", "DE.Views.TableSettingsAdvanced.textWrappingStyle": "折り返しの種類と配置", "DE.Views.TableSettingsAdvanced.textWrapText": "テキストの折り返し", "DE.Views.TableSettingsAdvanced.tipAll": "外枠とすべての内枠の線を設定", "DE.Views.TableSettingsAdvanced.tipCellAll": "内部セルだけに罫線を設定", "DE.Views.TableSettingsAdvanced.tipCellInner": "内部のセルだけのために縦線と横線を設定", "DE.Views.TableSettingsAdvanced.tipCellOuter": "内側のセルにのみ外枠罫線を設定", "DE.Views.TableSettingsAdvanced.tipInner": "内側の線のみを設定", "DE.Views.TableSettingsAdvanced.tipNone": "罫線の設定なし", "DE.Views.TableSettingsAdvanced.tipOuter": "外枠の罫線だけを設定", "DE.Views.TableSettingsAdvanced.tipTableOuterCellAll": "内部セルの罫線と外部の罫線を設定", "DE.Views.TableSettingsAdvanced.tipTableOuterCellInner": "内側のセルに外枠と縦線・横線を設定", "DE.Views.TableSettingsAdvanced.tipTableOuterCellOuter": "テーブルの外枠の罫線と内部セルの外枠罫線を設定", "DE.Views.TableSettingsAdvanced.txtCm": "センチ", "DE.Views.TableSettingsAdvanced.txtInch": "インチ", "DE.Views.TableSettingsAdvanced.txtNoBorders": "罫線なし", "DE.Views.TableSettingsAdvanced.txtPercent": "パーセント", "DE.Views.TableSettingsAdvanced.txtPt": "ポイント", "DE.Views.TableToTextDialog.textEmpty": "カスタムセパレータの文字を入力する必要があります。", "DE.Views.TableToTextDialog.textNested": "ネストした表の変換", "DE.Views.TableToTextDialog.textOther": "その他", "DE.Views.TableToTextDialog.textPara": "段落記号", "DE.Views.TableToTextDialog.textSemicolon": "セミコロン", "DE.Views.TableToTextDialog.textSeparator": "で文字を区切る", "DE.Views.TableToTextDialog.textTab": "タブ", "DE.Views.TableToTextDialog.textTitle": "表を文字に変換する", "DE.Views.TextArtSettings.strColor": "色", "DE.Views.TextArtSettings.strFill": "塗りつぶし", "DE.Views.TextArtSettings.strSize": "サイズ", "DE.Views.TextArtSettings.strStroke": "線", "DE.Views.TextArtSettings.strTransparency": "不透明度", "DE.Views.TextArtSettings.strType": "タイプ", "DE.Views.TextArtSettings.textAngle": "角度", "DE.Views.TextArtSettings.textBorderSizeErr": "入力された値が正しくありません。<br>0〜1584の数値を入力してください。", "DE.Views.TextArtSettings.textColor": "色で塗りつぶし", "DE.Views.TextArtSettings.textDirection": "方向", "DE.Views.TextArtSettings.textGradient": "グラデーションポイント", "DE.Views.TextArtSettings.textGradientFill": "塗りつぶし (グラデーション)", "DE.Views.TextArtSettings.textLinear": "線形", "DE.Views.TextArtSettings.textNoFill": "塗りつぶしなし", "DE.Views.TextArtSettings.textPosition": "位置", "DE.Views.TextArtSettings.textRadial": "ラジアル", "DE.Views.TextArtSettings.textSelectTexture": "選択する", "DE.Views.TextArtSettings.textStyle": "スタイル", "DE.Views.TextArtSettings.textTemplate": "テンプレート", "DE.Views.TextArtSettings.textTransform": "変換", "DE.Views.TextArtSettings.tipAddGradientPoint": "グラデーションポイントを追加する", "DE.Views.TextArtSettings.tipRemoveGradientPoint": "グラデーションポイントを削除する", "DE.Views.TextArtSettings.txtNoBorders": "線なし", "DE.Views.TextToTableDialog.textAutofit": "自動調整の動作", "DE.Views.TextToTableDialog.textColumns": "列", "DE.Views.TextToTableDialog.textContents": "コンテンツへの自動調整", "DE.Views.TextToTableDialog.textEmpty": "カスタムセパレータの文字を入力する必要があります。", "DE.Views.TextToTableDialog.textFixed": "固定カラム幅", "DE.Views.TextToTableDialog.textOther": "その他", "DE.Views.TextToTableDialog.textPara": "段落", "DE.Views.TextToTableDialog.textRows": "行", "DE.Views.TextToTableDialog.textSemicolon": "セミコロン", "DE.Views.TextToTableDialog.textSeparator": "でテキストを分離", "DE.Views.TextToTableDialog.textTab": "タブ", "DE.Views.TextToTableDialog.textTableSize": "テーブルのサイズ", "DE.Views.TextToTableDialog.textTitle": "文字を表に変換する", "DE.Views.TextToTableDialog.textWindow": "ウインドウへの自動調整", "DE.Views.TextToTableDialog.txtAutoText": "自動", "DE.Views.Toolbar.capBtnAddComment": "コメントを追加", "DE.Views.Toolbar.capBtnBlankPage": "空白ページ", "DE.Views.Toolbar.capBtnColumns": "列", "DE.Views.Toolbar.capBtnComment": "コメント", "DE.Views.Toolbar.capBtnDateTime": "日付＆時刻", "DE.Views.Toolbar.capBtnInsChart": "チャート", "DE.Views.Toolbar.capBtnInsControls": "コンテンツコントロール", "DE.Views.Toolbar.capBtnInsDropcap": "ドロップキャップ", "DE.Views.Toolbar.capBtnInsEquation": "方程式\t", "DE.Views.Toolbar.capBtnInsHeader": "ヘッダー/フッター", "DE.Views.Toolbar.capBtnInsImage": "画像", "DE.Views.Toolbar.capBtnInsPagebreak": "区切り", "DE.Views.Toolbar.capBtnInsShape": "図形", "DE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "DE.Views.Toolbar.capBtnInsSymbol": "記号", "DE.Views.Toolbar.capBtnInsTable": "テーブル", "DE.Views.Toolbar.capBtnInsTextart": "テキストアート", "DE.Views.Toolbar.capBtnInsTextbox": "テキストボックス", "DE.Views.Toolbar.capBtnLineNumbers": "行番号", "DE.Views.Toolbar.capBtnMargins": "余白", "DE.Views.Toolbar.capBtnPageOrient": "印刷の向き", "DE.Views.Toolbar.capBtnPageSize": "サイズ", "DE.Views.Toolbar.capBtnWatermark": "透かし", "DE.Views.Toolbar.capImgAlign": "整列", "DE.Views.Toolbar.capImgBackward": "背面へ移動", "DE.Views.Toolbar.capImgForward": "前面へ移動", "DE.Views.Toolbar.capImgGroup": "グループ", "DE.Views.Toolbar.capImgWrapping": "折り返し", "DE.Views.Toolbar.mniCapitalizeWords": "各単語を大文字にする", "DE.Views.Toolbar.mniCustomTable": "カスタムテーブルの挿入", "DE.Views.Toolbar.mniDrawTable": "罫線を引く", "DE.Views.Toolbar.mniEditControls": "コントロール設定", "DE.Views.Toolbar.mniEditDropCap": "ドロップキャップの設定", "DE.Views.Toolbar.mniEditFooter": "フッターの編集", "DE.Views.Toolbar.mniEditHeader": "ヘッダーの編集", "DE.Views.Toolbar.mniEraseTable": "テーブルの削除", "DE.Views.Toolbar.mniFromFile": "ファイルから", "DE.Views.Toolbar.mniFromStorage": "ストレージから", "DE.Views.Toolbar.mniFromUrl": "URLから", "DE.Views.Toolbar.mniHiddenBorders": "隠しテーブルの罫線", "DE.Views.Toolbar.mniHiddenChars": "非表示文字", "DE.Views.Toolbar.mniHighlightControls": "ハイライト設定", "DE.Views.Toolbar.mniImageFromFile": "ファイルからの画像", "DE.Views.Toolbar.mniImageFromStorage": "ストレージから画像", "DE.Views.Toolbar.mniImageFromUrl": "URLからのファイル", "DE.Views.Toolbar.mniInsertSSE": "スプレッドシートを挿入する", "DE.Views.Toolbar.mniLowerCase": "小文字", "DE.Views.Toolbar.mniRemoveFooter": "フッターの削除", "DE.Views.Toolbar.mniRemoveHeader": "ヘッダーの削除", "DE.Views.Toolbar.mniSentenceCase": "センテンスケース", "DE.Views.Toolbar.mniTextToTable": "文字を表に変換する", "DE.Views.Toolbar.mniToggleCase": "大文字と小文字を入れ替える", "DE.Views.Toolbar.mniUpperCase": "大文字", "DE.Views.Toolbar.strMenuNoFill": "塗りつぶしなし", "DE.Views.Toolbar.textAutoColor": "自動", "DE.Views.Toolbar.textBold": "太字", "DE.Views.Toolbar.textBottom": "下：", "DE.Views.Toolbar.textChangeLevel": "リストラベルの変更", "DE.Views.Toolbar.textCheckboxControl": "チェックボックス", "DE.Views.Toolbar.textColumnsCustom": "カスタム設定の列", "DE.Views.Toolbar.textColumnsLeft": "左", "DE.Views.Toolbar.textColumnsOne": "1", "DE.Views.Toolbar.textColumnsRight": "右", "DE.Views.Toolbar.textColumnsThree": "3", "DE.Views.Toolbar.textColumnsTwo": "2", "DE.Views.Toolbar.textComboboxControl": "コンボボックス", "DE.Views.Toolbar.textContinuous": "継続的", "DE.Views.Toolbar.textContPage": "連続ページ", "DE.Views.Toolbar.textCustomLineNumbers": "行番号オプション", "DE.Views.Toolbar.textDateControl": "日付", "DE.Views.Toolbar.textDropdownControl": "ドロップダウンリスト", "DE.Views.Toolbar.textEditWatermark": "カスタム設定の透かし", "DE.Views.Toolbar.textEvenPage": "偶数ページから開始", "DE.Views.Toolbar.textInMargin": "余白", "DE.Views.Toolbar.textInsColumnBreak": "段区切りの挿入", "DE.Views.Toolbar.textInsertPageCount": "ページ数を挿入", "DE.Views.Toolbar.textInsertPageNumber": "ページ番号の挿入", "DE.Views.Toolbar.textInsPageBreak": "改ページの挿入", "DE.Views.Toolbar.textInsSectionBreak": "セクション区切りの挿入", "DE.Views.Toolbar.textInText": "テキスト", "DE.Views.Toolbar.textItalic": "イタリック", "DE.Views.Toolbar.textLandscape": "横向き", "DE.Views.Toolbar.textLeft": "左：", "DE.Views.Toolbar.textListSettings": "リストの設定", "DE.Views.Toolbar.textMarginsLast": "最後に適用した設定", "DE.Views.Toolbar.textMarginsModerate": "標準", "DE.Views.Toolbar.textMarginsNarrow": "狭い", "DE.Views.Toolbar.textMarginsNormal": "標準", "DE.Views.Toolbar.textMarginsUsNormal": "ノーマル（アメリカの標準）", "DE.Views.Toolbar.textMarginsWide": "広い", "DE.Views.Toolbar.textNewColor": "新規カスタムカラーの追加", "DE.Views.Toolbar.textNextPage": "次のページ", "DE.Views.Toolbar.textNoHighlight": "ハイライト表示なし", "DE.Views.Toolbar.textNone": "なし", "DE.Views.Toolbar.textOddPage": "奇数ページから開始", "DE.Views.Toolbar.textPageMarginsCustom": "カスタム設定の余白", "DE.Views.Toolbar.textPageSizeCustom": "カスタム設定のページサイズ", "DE.Views.Toolbar.textPictureControl": "画像", "DE.Views.Toolbar.textPlainControl": "プレーンテキスト", "DE.Views.Toolbar.textPortrait": "縦向き", "DE.Views.Toolbar.textRemoveControl": "コンテンツコントロールを削除する", "DE.Views.Toolbar.textRemWatermark": "透かしの削除", "DE.Views.Toolbar.textRestartEachPage": "各ページに振り直し", "DE.Views.Toolbar.textRestartEachSection": "各セクションに振り直し", "DE.Views.Toolbar.textRichControl": "リッチテキスト", "DE.Views.Toolbar.textRight": "右：", "DE.Views.Toolbar.textStrikeout": "取り消し線", "DE.Views.Toolbar.textStyleMenuDelete": "スタイルの削除", "DE.Views.Toolbar.textStyleMenuDeleteAll": "カスタム設定のスタイルを全て削除", "DE.Views.Toolbar.textStyleMenuNew": "選択からの新しいスタイル", "DE.Views.Toolbar.textStyleMenuRestore": "デフォルトへの復元", "DE.Views.Toolbar.textStyleMenuRestoreAll": "全てのデフォルトスタイルの復元", "DE.Views.Toolbar.textStyleMenuUpdate": "選択範囲からの更新", "DE.Views.Toolbar.textSubscript": "下付き文字", "DE.Views.Toolbar.textSuperscript": "上付き文字", "DE.Views.Toolbar.textSuppressForCurrentParagraph": "現在の段落を表示しない ", "DE.Views.Toolbar.textTabCollaboration": "共同編集", "DE.Views.Toolbar.textTabFile": "ファイル", "DE.Views.Toolbar.textTabHome": "ホーム", "DE.Views.Toolbar.textTabInsert": "挿入", "DE.Views.Toolbar.textTabLayout": "レイアウト", "DE.Views.Toolbar.textTabLinks": "参考資料", "DE.Views.Toolbar.textTabProtect": "保護", "DE.Views.Toolbar.textTabReview": "レビュー", "DE.Views.Toolbar.textTabView": "表示", "DE.Views.Toolbar.textTitleError": "エラー", "DE.Views.Toolbar.textToCurrent": "現在の場所へ", "DE.Views.Toolbar.textTop": "トップ：", "DE.Views.Toolbar.textUnderline": "アンダーライン", "DE.Views.Toolbar.tipAlignCenter": "中央揃え", "DE.Views.Toolbar.tipAlignJust": "両端揃え", "DE.Views.Toolbar.tipAlignLeft": "左揃え", "DE.Views.Toolbar.tipAlignRight": "右揃え", "DE.Views.Toolbar.tipBack": "戻る", "DE.Views.Toolbar.tipBlankPage": "空白ページの挿入", "DE.Views.Toolbar.tipChangeCase": "大文字小文字を変更", "DE.Views.Toolbar.tipChangeChart": "グラフの種類の変更", "DE.Views.Toolbar.tipClearStyle": "スタイルのクリア", "DE.Views.Toolbar.tipColorSchemas": "配色の変更", "DE.Views.Toolbar.tipColumns": "列の挿入", "DE.Views.Toolbar.tipControls": "コンテンツコントロールの挿入", "DE.Views.Toolbar.tipCopy": "コピー", "DE.Views.Toolbar.tipCopyStyle": "スタイルのコピー", "DE.Views.Toolbar.tipCut": "切り取り", "DE.Views.Toolbar.tipDateTime": "現在の日付と時刻を挿入", "DE.Views.Toolbar.tipDecFont": "フォントサイズを小さくする", "DE.Views.Toolbar.tipDecPrLeft": "インデントを減らす", "DE.Views.Toolbar.tipDropCap": "ドロップキャップの挿入", "DE.Views.Toolbar.tipEditHeader": "ヘッダーまたはフッターの編集", "DE.Views.Toolbar.tipFontColor": "フォントの色", "DE.Views.Toolbar.tipFontName": "フォント", "DE.Views.Toolbar.tipFontSize": "フォントのサイズ", "DE.Views.Toolbar.tipHighlightColor": "ハイライトの色", "DE.Views.Toolbar.tipImgAlign": "オブジェクトを配置する", "DE.Views.Toolbar.tipImgGroup": "オブジェクトをグループ化する", "DE.Views.Toolbar.tipImgWrapping": "テキストの折り返し", "DE.Views.Toolbar.tipIncFont": "フォントサイズの増分", "DE.Views.Toolbar.tipIncPrLeft": "インデントを増やす", "DE.Views.Toolbar.tipInsertChart": "グラフの挿入", "DE.Views.Toolbar.tipInsertEquation": "数式の挿入", "DE.Views.Toolbar.tipInsertHorizontalText": "横書きテキストボックスの挿入", "DE.Views.Toolbar.tipInsertImage": "画像の挿入", "DE.Views.Toolbar.tipInsertNum": "ページ番号の挿入", "DE.Views.Toolbar.tipInsertShape": "オートシェイプの挿入", "DE.Views.Toolbar.tipInsertSmartArt": "SmartArtの挿入", "DE.Views.Toolbar.tipInsertSymbol": "記号の挿入", "DE.Views.Toolbar.tipInsertTable": "表の挿入", "DE.Views.Toolbar.tipInsertText": "テキストボックスの挿入", "DE.Views.Toolbar.tipInsertTextArt": "テキストアートの挿入", "DE.Views.Toolbar.tipInsertVerticalText": "縦書きテキストボックスの挿入", "DE.Views.Toolbar.tipLineNumbers": "行番号を表示する", "DE.Views.Toolbar.tipLineSpace": "段落の行間", "DE.Views.Toolbar.tipMailRecepients": "差し込み印刷", "DE.Views.Toolbar.tipMarkers": "箇条書き", "DE.Views.Toolbar.tipMarkersArrow": "箇条書き（矢印）", "DE.Views.Toolbar.tipMarkersCheckmark": "箇条書き（チェックマーク）", "DE.Views.Toolbar.tipMarkersDash": "「ダッシュ」記号", "DE.Views.Toolbar.tipMarkersFRhombus": "箇条書き（ひし形）", "DE.Views.Toolbar.tipMarkersFRound": "箇条書き（丸）", "DE.Views.Toolbar.tipMarkersFSquare": "箇条書き（四角）", "DE.Views.Toolbar.tipMarkersHRound": "箇条書き（円）", "DE.Views.Toolbar.tipMarkersStar": "箇条書き（星）", "DE.Views.Toolbar.tipMultiLevelArticl": "複数レベルの番号付き記事", "DE.Views.Toolbar.tipMultiLevelChapter": "複数レベルの番号付き文章", "DE.Views.Toolbar.tipMultiLevelHeadings": "複数レベルの番号付き見出し", "DE.Views.Toolbar.tipMultiLevelHeadVarious": "複数レベルの番号付き各種見出し", "DE.Views.Toolbar.tipMultiLevelNumbered": "段落番号付き箇条書き", "DE.Views.Toolbar.tipMultilevels": "複数レベルのリスト", "DE.Views.Toolbar.tipMultiLevelSymbols": "記号付き箇条書き", "DE.Views.Toolbar.tipMultiLevelVarious": "段落番号付き様々な箇条書き", "DE.Views.Toolbar.tipNumbers": "ナンバリング", "DE.Views.Toolbar.tipPageBreak": "ページの挿入またはセクション区切り", "DE.Views.Toolbar.tipPageMargins": "ページ余白", "DE.Views.Toolbar.tipPageOrient": "ページの向き", "DE.Views.Toolbar.tipPageSize": "ページのサイズ", "DE.Views.Toolbar.tipParagraphStyle": "段落のスタイル", "DE.Views.Toolbar.tipPaste": "貼り付け", "DE.Views.Toolbar.tipPrColor": "段落の背景色", "DE.Views.Toolbar.tipPrint": "印刷", "DE.Views.Toolbar.tipRedo": "やり直し", "DE.Views.Toolbar.tipSave": "保存", "DE.Views.Toolbar.tipSaveCoauth": "変更内容を保存して、他のユーザーが確認できるようにします。", "DE.Views.Toolbar.tipSelectAll": "すべて選択", "DE.Views.Toolbar.tipSendBackward": "背面へ移動", "DE.Views.Toolbar.tipSendForward": "前面へ移動", "DE.Views.Toolbar.tipShowHiddenChars": "非表示文字", "DE.Views.Toolbar.tipSynchronize": "このドキュメントは他のユーザーによって変更されました。クリックして変更を保存し、更新を再読み込みしてください。", "DE.Views.Toolbar.tipUndo": "元に戻す", "DE.Views.Toolbar.tipWatermark": "透かしを編集する", "DE.Views.Toolbar.txtDistribHor": "左右に整列", "DE.Views.Toolbar.txtDistribVert": "上下に整列", "DE.Views.Toolbar.txtMarginAlign": "マージンに揃え", "DE.Views.Toolbar.txtObjectsAlign": "選択したオブジェクトを整列する", "DE.Views.Toolbar.txtPageAlign": "ページに揃え", "DE.Views.Toolbar.txtScheme1": "Office", "DE.Views.Toolbar.txtScheme10": "中位数", "DE.Views.Toolbar.txtScheme11": "メトロ", "DE.Views.Toolbar.txtScheme12": "モジュール", "DE.Views.Toolbar.txtScheme13": "オピュレント", "DE.Views.Toolbar.txtScheme14": "オリエル", "DE.Views.Toolbar.txtScheme15": "発生元", "DE.Views.Toolbar.txtScheme16": "紙", "DE.Views.Toolbar.txtScheme17": "ソルスティス", "DE.Views.Toolbar.txtScheme18": "テクニック", "DE.Views.Toolbar.txtScheme19": "トレッキング", "DE.Views.Toolbar.txtScheme2": "グレースケール", "DE.Views.Toolbar.txtScheme20": "アーバン", "DE.Views.Toolbar.txtScheme21": "バーブ", "DE.Views.Toolbar.txtScheme22": "新しいOffice", "DE.Views.Toolbar.txtScheme3": "エイペックス", "DE.Views.Toolbar.txtScheme4": "アスペクト", "DE.Views.Toolbar.txtScheme5": "市民", "DE.Views.Toolbar.txtScheme6": "ビジネス", "DE.Views.Toolbar.txtScheme7": "エクイティ", "DE.Views.Toolbar.txtScheme8": "フロー", "DE.Views.Toolbar.txtScheme9": "ファウンドリー", "DE.Views.ViewTab.textAlwaysShowToolbar": "ツールバーを常に表示する", "DE.Views.ViewTab.textDarkDocument": "ダークドキュメント", "DE.Views.ViewTab.textFitToPage": "ページに合わせる", "DE.Views.ViewTab.textFitToWidth": "幅に合わせる", "DE.Views.ViewTab.textInterfaceTheme": "インターフェイスのテーマ", "DE.Views.ViewTab.textLeftMenu": "左パネル", "DE.Views.ViewTab.textNavigation": "ナビゲーション", "DE.Views.ViewTab.textOutline": "見出し", "DE.Views.ViewTab.textRightMenu": "右パネル", "DE.Views.ViewTab.textRulers": "ルーラー", "DE.Views.ViewTab.textStatusBar": "ステータスバー", "DE.Views.ViewTab.textZoom": "ズーム", "DE.Views.ViewTab.tipDarkDocument": "ダークドキュメント", "DE.Views.ViewTab.tipFitToPage": "ページに合わせる", "DE.Views.ViewTab.tipFitToWidth": "幅に合わせる", "DE.Views.ViewTab.tipHeadings": "見出し", "DE.Views.ViewTab.tipInterfaceTheme": "インターフェースのテーマ", "DE.Views.WatermarkSettingsDialog.textAuto": "自動", "DE.Views.WatermarkSettingsDialog.textBold": "太字", "DE.Views.WatermarkSettingsDialog.textColor": "文字の色", "DE.Views.WatermarkSettingsDialog.textDiagonal": "斜め", "DE.Views.WatermarkSettingsDialog.textFont": "フォント", "DE.Views.WatermarkSettingsDialog.textFromFile": "ファイルから", "DE.Views.WatermarkSettingsDialog.textFromStorage": "ストレージから", "DE.Views.WatermarkSettingsDialog.textFromUrl": "URLから", "DE.Views.WatermarkSettingsDialog.textHor": "水平", "DE.Views.WatermarkSettingsDialog.textImageW": "画像透かし", "DE.Views.WatermarkSettingsDialog.textItalic": "イタリック", "DE.Views.WatermarkSettingsDialog.textLanguage": "言語", "DE.Views.WatermarkSettingsDialog.textLayout": "レイアウト", "DE.Views.WatermarkSettingsDialog.textNone": "なし", "DE.Views.WatermarkSettingsDialog.textScale": "規模", "DE.Views.WatermarkSettingsDialog.textSelect": "画像を選択する", "DE.Views.WatermarkSettingsDialog.textStrikeout": "取り消し線", "DE.Views.WatermarkSettingsDialog.textText": "テキスト", "DE.Views.WatermarkSettingsDialog.textTextW": "テキスト透かし", "DE.Views.WatermarkSettingsDialog.textTitle": "透かし設定", "DE.Views.WatermarkSettingsDialog.textTransparency": "半透明", "DE.Views.WatermarkSettingsDialog.textUnderline": "アンダーライン", "DE.Views.WatermarkSettingsDialog.tipFontName": "フォント名", "DE.Views.WatermarkSettingsDialog.tipFontSize": "フォントのサイズ"}