{"Common.Controllers.Chat.notcriticalErrorTitle": "Aviso", "Common.Controllers.Chat.textEnterMessage": "Insira sua mensagem aqui", "Common.Controllers.ExternalDiagramEditor.textAnonymous": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalDiagramEditor.textClose": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalDiagramEditor.warningText": "O objeto foi desativado porque está a ser editado por outro utilizador.", "Common.Controllers.ExternalDiagramEditor.warningTitle": "Aviso", "Common.Controllers.ExternalMergeEditor.textAnonymous": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalMergeEditor.textClose": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalMergeEditor.warningText": "O objeto foi desativado porque está a ser editado por outro utilizador.", "Common.Controllers.ExternalMergeEditor.warningTitle": "Aviso", "Common.Controllers.ExternalOleEditor.textAnonymous": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalOleEditor.textClose": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalOleEditor.warningText": "O objeto está desativado porque está a ser editado por outro utilizador.", "Common.Controllers.ExternalOleEditor.warningTitle": "Aviso", "Common.Controllers.History.notcriticalErrorTitle": "Aviso", "Common.Controllers.ReviewChanges.textAcceptBeforeCompare": "A fim de comparar os documentos, todas as alterações neles verificadas serão consideradas como tendo sido aceites. Deseja continuar?", "Common.Controllers.ReviewChanges.textAtLeast": "no mínimo", "Common.Controllers.ReviewChanges.textAuto": "auto", "Common.Controllers.ReviewChanges.textBaseline": "Linha base", "Common.Controllers.ReviewChanges.textBold": "Bold", "Common.Controllers.ReviewChanges.textBreakBefore": "Quebra de página antes", "Common.Controllers.ReviewChanges.textCaps": "Tudo em ma<PERSON>", "Common.Controllers.ReviewChanges.textCenter": "Alinhar ao centro", "Common.Controllers.ReviewChanges.textChar": "<PERSON><PERSON><PERSON>", "Common.Controllers.ReviewChanges.textChart": "Gráfico", "Common.Controllers.ReviewChanges.textColor": "Cor do tipo de letra", "Common.Controllers.ReviewChanges.textContextual": "Não adicionar intervalo entre parágrafos do mesmo estilo", "Common.Controllers.ReviewChanges.textDeleted": "<b>Eliminado:</b>", "Common.Controllers.ReviewChanges.textDStrikeout": "<PERSON><PERSON><PERSON>", "Common.Controllers.ReviewChanges.textEquation": "Equação", "Common.Controllers.ReviewChanges.textExact": "exatamente", "Common.Controllers.ReviewChanges.textFirstLine": "Primeira linha", "Common.Controllers.ReviewChanges.textFontSize": "Tamanho do tipo de letra", "Common.Controllers.ReviewChanges.textFormatted": "Formatado", "Common.Controllers.ReviewChanges.textHighlight": "<PERSON>r <PERSON>", "Common.Controllers.ReviewChanges.textImage": "Imagem", "Common.Controllers.ReviewChanges.textIndentLeft": "Avanço à esquerda", "Common.Controllers.ReviewChanges.textIndentRight": "Avanço à direita", "Common.Controllers.ReviewChanges.textInserted": "<b>Inserido:</b>", "Common.Controllers.ReviewChanges.textItalic": "Itálico", "Common.Controllers.ReviewChanges.textJustify": "<PERSON><PERSON><PERSON>", "Common.Controllers.ReviewChanges.textKeepLines": "Manter linhas juntas", "Common.Controllers.ReviewChanges.textKeepNext": "Manter com seguinte", "Common.Controllers.ReviewChanges.textLeft": "Alinhar à esquerda", "Common.Controllers.ReviewChanges.textLineSpacing": "Espaçamento entre linhas:", "Common.Controllers.ReviewChanges.textMultiple": "<PERSON><PERSON><PERSON><PERSON>", "Common.Controllers.ReviewChanges.textNoBreakBefore": "Sem quebra de página antes", "Common.Controllers.ReviewChanges.textNoContextual": "Adicionar intervalo entre parágrafos com o mesmo estilo", "Common.Controllers.ReviewChanges.textNoKeepLines": "<PERSON>ão manter linhas juntas", "Common.Controllers.ReviewChanges.textNoKeepNext": "Não manter com seguinte", "Common.Controllers.ReviewChanges.textNot": "Não", "Common.Controllers.ReviewChanges.textNoWidow": "Não controlar órfãos", "Common.Controllers.ReviewChanges.textNum": "Alterar numeração", "Common.Controllers.ReviewChanges.textOff": "{0} já não está a utilizar O Registo de Alterações.", "Common.Controllers.ReviewChanges.textOffGlobal": "{0} Registar Alterações desativado para todos.", "Common.Controllers.ReviewChanges.textOn": "{0} está agora a utilizar o Registar Alterações.", "Common.Controllers.ReviewChanges.textOnGlobal": "{0} Registar Alterações ativado para todos.", "Common.Controllers.ReviewChanges.textParaDeleted": "<b><PERSON><PERSON><PERSON><PERSON><PERSON> eliminado</b>", "Common.Controllers.ReviewChanges.textParaFormatted": "Parágrafo formatado", "Common.Controllers.ReviewChanges.textParaInserted": "<b>Parágra<PERSON> inserido</b>", "Common.Controllers.ReviewChanges.textParaMoveFromDown": "<b>Movido para baixo:</b>", "Common.Controllers.ReviewChanges.textParaMoveFromUp": "<b>Movido para cima:</b>", "Common.Controllers.ReviewChanges.textParaMoveTo": "<b><PERSON><PERSON><PERSON>:</b>", "Common.Controllers.ReviewChanges.textPosition": "Posição", "Common.Controllers.ReviewChanges.textRight": "Alinhar à direita", "Common.Controllers.ReviewChanges.textShape": "Forma", "Common.Controllers.ReviewChanges.textShd": "Cor de fundo", "Common.Controllers.ReviewChanges.textShow": "Mostrar alterações em", "Common.Controllers.ReviewChanges.textSmallCaps": "Versalet<PERSON>", "Common.Controllers.ReviewChanges.textSpacing": "Espaçamento", "Common.Controllers.ReviewChanges.textSpacingAfter": "Espaçamento depois", "Common.Controllers.ReviewChanges.textSpacingBefore": "Espaçamento antes", "Common.Controllers.ReviewChanges.textStrikeout": "<PERSON><PERSON><PERSON>", "Common.Controllers.ReviewChanges.textSubScript": "Subscrito", "Common.Controllers.ReviewChanges.textSuperScript": "Sobrescrito", "Common.Controllers.ReviewChanges.textTableChanged": "<b>Definições de tabela alteradas</b>", "Common.Controllers.ReviewChanges.textTableRowsAdd": "<b><PERSON><PERSON> de tabela adicionadas</b>", "Common.Controllers.ReviewChanges.textTableRowsDel": "<b><PERSON><PERSON> de tabela eliminadas</b>", "Common.Controllers.ReviewChanges.textTabs": "Alterar separadores", "Common.Controllers.ReviewChanges.textTitleComparison": "Definições de comparação", "Common.Controllers.ReviewChanges.textUnderline": "<PERSON><PERSON><PERSON><PERSON>", "Common.Controllers.ReviewChanges.textUrl": "Colar de um URL", "Common.Controllers.ReviewChanges.textWidow": "Controlo de órfãos", "Common.Controllers.ReviewChanges.textWord": "Nível de palavra", "Common.define.chartData.textArea": "Á<PERSON>", "Common.define.chartData.textAreaStacked": "<PERSON><PERSON> empi<PERSON>", "Common.define.chartData.textAreaStackedPer": "Área 100% alinhada", "Common.define.chartData.textBar": "Barr<PERSON>", "Common.define.chartData.textBarNormal": "Coluna agrupada", "Common.define.chartData.textBarNormal3d": "Coluna 3-D agrupada", "Common.define.chartData.textBarNormal3dPerspective": "Coluna 3-D", "Common.define.chartData.textBarStacked": "Coluna empilhada", "Common.define.chartData.textBarStacked3d": "Coluna 3-D agrupada", "Common.define.chartData.textBarStackedPer": "Coluna 100% alinhada", "Common.define.chartData.textBarStackedPer3d": "Coluna 3-D 100% alinhada", "Common.define.chartData.textCharts": "Grá<PERSON><PERSON>", "Common.define.chartData.textColumn": "Coluna", "Common.define.chartData.textCombo": "Combinação", "Common.define.chartData.textComboAreaBar": "<PERSON><PERSON> empi<PERSON> – coluna agrupada", "Common.define.chartData.textComboBarLine": "Coluna agrupada – linha", "Common.define.chartData.textComboBarLineSecondary": "Coluna agrupada – linha num eixo secundário", "Common.define.chartData.textComboCustom": "Combinação personalizada", "Common.define.chartData.textDoughnut": "Rosca", "Common.define.chartData.textHBarNormal": "Barra Agrupada", "Common.define.chartData.textHBarNormal3d": "Barra 3-D agrupada", "Common.define.chartData.textHBarStacked": "<PERSON><PERSON> empilhada", "Common.define.chartData.textHBarStacked3d": "Barra 3-D agrupada", "Common.define.chartData.textHBarStackedPer": "Barra 100% alinhada", "Common.define.chartData.textHBarStackedPer3d": "Barra 3-D 100% alinhada", "Common.define.chartData.textLine": "<PERSON><PERSON>", "Common.define.chartData.textLine3d": "Linha 3-D", "Common.define.chartData.textLineMarker": "Linha com marcadores", "Common.define.chartData.textLineStacked": "<PERSON><PERSON> em<PERSON>", "Common.define.chartData.textLineStackedMarker": "Linha empilhada com marcadores", "Common.define.chartData.textLineStackedPer": "100% Ali<PERSON><PERSON>", "Common.define.chartData.textLineStackedPerMarker": "Alinhado com 100%", "Common.define.chartData.textPie": "Tarte", "Common.define.chartData.textPie3d": "Tarte 3-D", "Common.define.chartData.textPoint": "XY (gráfico de dispersão)", "Common.define.chartData.textScatter": "Di<PERSON>são", "Common.define.chartData.textScatterLine": "Dispersão com Linhas Retas", "Common.define.chartData.textScatterLineMarker": "Dispersão com Linhas e Marcadores Retos", "Common.define.chartData.textScatterSmooth": "Dispersão com Linhas Suaves", "Common.define.chartData.textScatterSmoothMarker": "Dispersão com Linhas Suaves e Marcadores", "Common.define.chartData.textStock": "Gráfico de ações", "Common.define.chartData.textSurface": "Superfície", "Common.define.smartArt.textArchitectureLayout": "Disposição de arquitetura", "Common.define.smartArt.textEquation": "Equação", "Common.define.smartArt.textFunnel": "Funil", "Common.define.smartArt.textList": "Lista", "Common.define.smartArt.textOther": "Outro", "Common.define.smartArt.textPicture": "Imagem", "Common.Translation.textMoreButton": "<PERSON><PERSON>", "Common.Translation.warnFileLocked": "Não pode editar o ficheiro porque este está a ser editado por outra aplicação.", "Common.Translation.warnFileLockedBtnEdit": "<PERSON><PERSON><PERSON> uma c<PERSON>pia", "Common.Translation.warnFileLockedBtnView": "Abrir para visualizar", "Common.UI.ButtonColored.textAutoColor": "Automático", "Common.UI.ButtonColored.textNewColor": "Adicionar nova cor personalizada", "Common.UI.Calendar.textApril": "Abril", "Common.UI.Calendar.textAugust": "Agosto", "Common.UI.Calendar.textDecember": "Dezembro", "Common.UI.Calendar.textFebruary": "<PERSON><PERSON>", "Common.UI.Calendar.textJanuary": "Janeiro", "Common.UI.Calendar.textJuly": "<PERSON><PERSON>", "Common.UI.Calendar.textJune": "<PERSON><PERSON>", "Common.UI.Calendar.textMarch": "Março", "Common.UI.Calendar.textMay": "<PERSON>", "Common.UI.Calendar.textMonths": "Meses", "Common.UI.Calendar.textNovember": "Novembro", "Common.UI.Calendar.textOctober": "Out<PERSON>ro", "Common.UI.Calendar.textSeptember": "Setembro", "Common.UI.Calendar.textShortApril": "Abr", "Common.UI.Calendar.textShortAugust": "Ago", "Common.UI.Calendar.textShortDecember": "<PERSON>z", "Common.UI.Calendar.textShortFebruary": "<PERSON>v", "Common.UI.Calendar.textShortFriday": "Sex", "Common.UI.Calendar.textShortJanuary": "Jan", "Common.UI.Calendar.textShortJuly": "Jul", "Common.UI.Calendar.textShortJune": "Jun", "Common.UI.Calendar.textShortMarch": "Mar", "Common.UI.Calendar.textShortMay": "<PERSON><PERSON>", "Common.UI.Calendar.textShortMonday": "Seg", "Common.UI.Calendar.textShortNovember": "Nov", "Common.UI.Calendar.textShortOctober": "Out", "Common.UI.Calendar.textShortSaturday": "<PERSON><PERSON><PERSON>", "Common.UI.Calendar.textShortSeptember": "Set", "Common.UI.Calendar.textShortSunday": "Dom", "Common.UI.Calendar.textShortThursday": "<PERSON>ui", "Common.UI.Calendar.textShortTuesday": "<PERSON><PERSON>", "Common.UI.Calendar.textShortWednesday": "<PERSON>ua", "Common.UI.Calendar.textYears": "<PERSON><PERSON>", "Common.UI.ComboBorderSize.txtNoBorders": "Sem contornos", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "Sem contornos", "Common.UI.ComboDataView.emptyComboText": "Sem estilos", "Common.UI.ExtendedColorDialog.addButtonText": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textCurrent": "Atual", "Common.UI.ExtendedColorDialog.textHexErr": "O valor inserido não está correto.<br>Introduza um valor entre 000000 e FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "Novo", "Common.UI.ExtendedColorDialog.textRGBErr": "O valor inserido não está correto.<br>Introduza um valor numérico entre 0 e 255.", "Common.UI.HSBColorPicker.textNoColor": "Sem cor", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "Ocultar palavra-passe", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "Mostrar palavra-passe", "Common.UI.SearchBar.textFind": "Localizar", "Common.UI.SearchBar.tipCloseSearch": "<PERSON><PERSON><PERSON>es<PERSON>", "Common.UI.SearchBar.tipNextResult": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.SearchBar.tipOpenAdvancedSettings": "Abrir opções avançadas", "Common.UI.SearchBar.tipPreviousResult": "Resultado anterior", "Common.UI.SearchDialog.textHighlight": "Destacar resultados", "Common.UI.SearchDialog.textMatchCase": "Diferenciar maiús<PERSON>s de minúsculas", "Common.UI.SearchDialog.textReplaceDef": "Inserir o texto de substituição", "Common.UI.SearchDialog.textSearchStart": "Insira seu texto aqui", "Common.UI.SearchDialog.textTitle": "Localizar e substituir", "Common.UI.SearchDialog.textTitle2": "Localizar", "Common.UI.SearchDialog.textWholeWords": "Palavras inteiras apenas", "Common.UI.SearchDialog.txtBtnHideReplace": "Ocultar substituição", "Common.UI.SearchDialog.txtBtnReplace": "Substituir", "Common.UI.SearchDialog.txtBtnReplaceAll": "Substituir tudo", "Common.UI.SynchronizeTip.textDontShow": "Não exibir esta mensagem novamente", "Common.UI.SynchronizeTip.textSynchronize": "O documento foi alterado por outro utilizador.<br>Clique para guardar as suas alterações e recarregar o documento.", "Common.UI.ThemeColorPalette.textRecentColors": "Cores recentes", "Common.UI.ThemeColorPalette.textStandartColors": "<PERSON><PERSON>", "Common.UI.ThemeColorPalette.textThemeColors": "Cores do tema", "Common.UI.Themes.txtThemeClassicLight": "Clássico claro", "Common.UI.Themes.txtThemeContrastDark": "Contraste escuro", "Common.UI.Themes.txtThemeDark": "Escuro", "Common.UI.Themes.txtThemeLight": "<PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeSystem": "O mesmo do sistema", "Common.UI.Window.cancelButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.Window.closeButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.Window.noButtonText": "Não", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "Confirmação", "Common.UI.Window.textDontShow": "Não exibir esta mensagem novamente", "Common.UI.Window.textError": "Erro", "Common.UI.Window.textInformation": "Informações", "Common.UI.Window.textWarning": "Aviso", "Common.UI.Window.yesButtonText": "<PERSON>m", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Shift", "Common.Views.About.txtAddress": "endereço:", "Common.Views.About.txtLicensee": "LICENÇA", "Common.Views.About.txtLicensor": "LICENCIANTE", "Common.Views.About.txtMail": "e-mail:", "Common.Views.About.txtPoweredBy": "Desenvolvido por", "Common.Views.About.txtTel": "tel.: ", "Common.Views.About.txtVersion": "Vers<PERSON>", "Common.Views.AutoCorrectDialog.textAdd": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textApplyText": "Aplicar ao escrever", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Autocorreção de Texto", "Common.Views.AutoCorrectDialog.textAutoFormat": "Formatação automática ao escrever", "Common.Views.AutoCorrectDialog.textBulleted": "Lista automática com marcas", "Common.Views.AutoCorrectDialog.textBy": "Por", "Common.Views.AutoCorrectDialog.textDelete": "Eliminar", "Common.Views.AutoCorrectDialog.textDoubleSpaces": "Adicionar parágrafo com espaçamento duplo", "Common.Views.AutoCorrectDialog.textFLCells": "Maiúscula na primeira letra das células da tabela", "Common.Views.AutoCorrectDialog.textFLSentence": "Capitalizar a primeira letra das frases", "Common.Views.AutoCorrectDialog.textHyperlink": "Internet e locais de rede com hiperligações", "Common.Views.AutoCorrectDialog.textHyphens": "<PERSON><PERSON><PERSON>s (--) com traço (-)", "Common.Views.AutoCorrectDialog.textMathCorrect": " Correção automática de matemática", "Common.Views.AutoCorrectDialog.textNumbered": "Lista automática com números", "Common.Views.AutoCorrectDialog.textQuotes": "\"Aspas retas\" com \"aspas inteligentes\"", "Common.Views.AutoCorrectDialog.textRecognized": "Funções Reconhecidas", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "As seguintes expressões são expressões matemáticas reconhecidas. Não serão colocadas automaticamente em itálico.", "Common.Views.AutoCorrectDialog.textReplace": "Substituir", "Common.Views.AutoCorrectDialog.textReplaceText": "Substituir à medida que digita", "Common.Views.AutoCorrectDialog.textReplaceType": "Substitua o texto à medida que digita", "Common.Views.AutoCorrectDialog.textReset": "Repor", "Common.Views.AutoCorrectDialog.textResetAll": "Repor para a predefinição", "Common.Views.AutoCorrectDialog.textRestore": "Restaurar", "Common.Views.AutoCorrectDialog.textTitle": "Correção automática", "Common.Views.AutoCorrectDialog.textWarnAddRec": "As funções reconhecidas devem conter apenas as letras de A a Z, maiúsculas ou minúsculas.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "Qualquer expressão que tenha adicionado será removida e as expressões removidas serão restauradas. Quer continuar?", "Common.Views.AutoCorrectDialog.warnReplace": "A correção automática para %1 já existe. Quer substituí-la?", "Common.Views.AutoCorrectDialog.warnReset": "Qualquer correção automática que tenha adicionado será removida e as alterações serão restauradas aos seus valores originais. Quer continuar?", "Common.Views.AutoCorrectDialog.warnRestore": "A correção automática para %1 já existe. Quer substituí-la?", "Common.Views.Chat.textSend": "Enviar", "Common.Views.Comments.mniAuthorAsc": "Autor de A a Z", "Common.Views.Comments.mniAuthorDesc": "Autor Z a A", "Common.Views.Comments.mniDateAsc": "<PERSON><PERSON> anti<PERSON>", "Common.Views.Comments.mniDateDesc": "Novidades", "Common.Views.Comments.mniFilterGroups": "Filtrar por Grupo", "Common.Views.Comments.mniPositionAsc": "De cima", "Common.Views.Comments.mniPositionDesc": "Do fundo", "Common.Views.Comments.textAdd": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textAddComment": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textAddCommentToDoc": "Adicionar comentário ao documento", "Common.Views.Comments.textAddReply": "<PERSON><PERSON><PERSON><PERSON> resposta", "Common.Views.Comments.textAll": "<PERSON><PERSON>", "Common.Views.Comments.textAnonym": "Visitante", "Common.Views.Comments.textCancel": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textClosePanel": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textComments": "Comentários", "Common.Views.Comments.textEdit": "<PERSON><PERSON>", "Common.Views.Comments.textEnterCommentHint": "Introduza o seu comentário aqui", "Common.Views.Comments.textHintAddComment": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textOpenAgain": "Abrir novamente", "Common.Views.Comments.textReply": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolve": "Resolver", "Common.Views.Comments.textResolved": "Resolvido", "Common.Views.Comments.textSort": "Ordenar comentá<PERSON>s", "Common.Views.Comments.textViewResolved": "Não tem permissão para reabrir o comentário", "Common.Views.Comments.txtEmpty": "Não há comentários no documento.", "Common.Views.CopyWarningDialog.textDontShow": "Não exibir esta mensagem novamente", "Common.Views.CopyWarningDialog.textMsg": "As ações copiar, cortar e colar através dos botões da barra de ferramentas ou através do menu de contexto apenas serão executadas neste separador.<br><br>Para copiar ou colar de outras aplicações deve utilizar estas teclas de atalho:", "Common.Views.CopyWarningDialog.textTitle": "Ações <PERSON>, cortar e colar", "Common.Views.CopyWarningDialog.textToCopy": "para copiar", "Common.Views.CopyWarningDialog.textToCut": "para cortar", "Common.Views.CopyWarningDialog.textToPaste": "para Colar", "Common.Views.DocumentAccessDialog.textLoading": "A carregar...", "Common.Views.DocumentAccessDialog.textTitle": "Definições de partilha", "Common.Views.ExternalDiagramEditor.textTitle": "Editor de gráfico", "Common.Views.ExternalEditor.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.ExternalEditor.textSave": "Guardar e sair", "Common.Views.ExternalMergeEditor.textTitle": "Mail Merge Recepients", "Common.Views.ExternalOleEditor.textTitle": "Editor de folhas de cálculo", "Common.Views.Header.labelCoUsersDescr": "Utilizadores que estão a editar o ficheiro:", "Common.Views.Header.textAddFavorite": "Marcar como favorito", "Common.Views.Header.textAdvSettings": "Definições avançadas", "Common.Views.Header.textBack": "Abrir localização", "Common.Views.Header.textCompactView": "Ocultar barra de ferramentas", "Common.Views.Header.textHideLines": "Ocultar r<PERSON>", "Common.Views.Header.textHideStatusBar": "Ocultar barra de estado", "Common.Views.Header.textRemoveFavorite": "Remover dos favoritos", "Common.Views.Header.textShare": "Partilhar", "Common.Views.Header.textZoom": "Ampliação", "Common.Views.Header.tipAccessRights": "<PERSON><PERSON><PERSON> direitos de acesso ao documento", "Common.Views.Header.tipDownload": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipGoEdit": "<PERSON><PERSON> atual", "Common.Views.Header.tipPrint": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipRedo": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipSave": "Guardar", "Common.Views.Header.tipSearch": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipUndo": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipUsers": "Ver utilizadores", "Common.Views.Header.tipViewSettings": "Definições de visualização", "Common.Views.Header.tipViewUsers": "Ver utilizadores e gerir direitos de acesso", "Common.Views.Header.txtAccessRights": "Alterar direitos de acesso", "Common.Views.Header.txtRename": "<PERSON>dar nome", "Common.Views.History.textCloseHistory": "<PERSON><PERSON><PERSON>", "Common.Views.History.textHide": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.History.textHideAll": "Ocultar alterações detalhadas", "Common.Views.History.textRestore": "Restaurar", "Common.Views.History.textShow": "Expandir", "Common.Views.History.textShowAll": "Mostrar alterações detalhadas", "Common.Views.History.textVer": "ver.", "Common.Views.ImageFromUrlDialog.textUrl": "Colar URL de uma imagem:", "Common.Views.ImageFromUrlDialog.txtEmpty": "Este campo é obrigatório", "Common.Views.ImageFromUrlDialog.txtNotUrl": "Este campo deve ser uma URL no formato \"http://www.example.com\"", "Common.Views.InsertTableDialog.textInvalidRowsCols": "Tem que especificar um número válido de linhas e de colunas.", "Common.Views.InsertTableDialog.txtColumns": "Número de <PERSON>nas", "Common.Views.InsertTableDialog.txtMaxText": "O valor máximo para este campo é {0}.", "Common.Views.InsertTableDialog.txtMinText": "O valor mínimo para este campo é {0}.", "Common.Views.InsertTableDialog.txtRows": "Número de l<PERSON>has", "Common.Views.InsertTableDialog.txtTitle": "<PERSON><PERSON><PERSON>", "Common.Views.InsertTableDialog.txtTitleSplit": "<PERSON><PERSON><PERSON>", "Common.Views.LanguageDialog.labelSelect": "Selecionar idioma do documento", "Common.Views.OpenDialog.closeButtonText": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtEncoding": "Codificação", "Common.Views.OpenDialog.txtIncorrectPwd": "Palavra-passe inválida.", "Common.Views.OpenDialog.txtOpenFile": "Introduza a palavra-passe para abrir o ficheiro", "Common.Views.OpenDialog.txtPassword": "Palavra-passe", "Common.Views.OpenDialog.txtPreview": "Pré-visualizar", "Common.Views.OpenDialog.txtProtected": "Assim que introduzir a palavra-passe e abrir o ficheiro, a palavra-passe atual será reposta.", "Common.Views.OpenDialog.txtTitle": "Escolher opções %1", "Common.Views.OpenDialog.txtTitleProtected": "<PERSON><PERSON><PERSON> protegido", "Common.Views.PasswordDialog.txtDescription": "Defina uma palavra-passe para proteger este documento", "Common.Views.PasswordDialog.txtIncorrectPwd": "Disparidade nas palavras-passe introduzidas", "Common.Views.PasswordDialog.txtPassword": "Palavra-passe", "Common.Views.PasswordDialog.txtRepeat": "Repetição de palavra-passe", "Common.Views.PasswordDialog.txtTitle": "Definir palavra-passe", "Common.Views.PasswordDialog.txtWarning": "Aviso: Se perder ou esquecer a palavra-passe, não será possível recuperá-la. Guarde-a num local seguro.", "Common.Views.PluginDlg.textLoading": "A carregar", "Common.Views.Plugins.groupCaption": "Plugins", "Common.Views.Plugins.strPlugins": "Plugins", "Common.Views.Plugins.textClosePanel": "Fechar plugin", "Common.Views.Plugins.textLoading": "A carregar", "Common.Views.Plugins.textStart": "Iniciar", "Common.Views.Plugins.textStop": "<PERSON><PERSON>", "Common.Views.Protection.hintAddPwd": "Cifrar com palavra-passe", "Common.Views.Protection.hintDelPwd": "Eliminar palavra-passe", "Common.Views.Protection.hintPwd": "Alt<PERSON>r ou eliminar palavra-passe", "Common.Views.Protection.hintSignature": "Adicionar assinatura digital ou linha de assinatura", "Common.Views.Protection.txtAddPwd": "Adicionar pala<PERSON>-passe", "Common.Views.Protection.txtChangePwd": "Alterar palavra-passe", "Common.Views.Protection.txtDeletePwd": "Eliminar palavra-passe", "Common.Views.Protection.txtEncrypt": "Cifrar", "Common.Views.Protection.txtInvisibleSignature": "Adicionar assinatura digital", "Common.Views.Protection.txtSignature": "Assinatura", "Common.Views.Protection.txtSignatureLine": "Adicionar linha de assinatura", "Common.Views.RenameDialog.textName": "Nome do ficheiro", "Common.Views.RenameDialog.txtInvalidName": "O nome do ficheiro não pode ter qualquer um dos seguintes caracteres:", "Common.Views.ReviewChanges.hintNext": "Para a próxima alteração", "Common.Views.ReviewChanges.hintPrev": "Para a alteração anterior", "Common.Views.ReviewChanges.mniFromFile": "Documento de um ficheiro", "Common.Views.ReviewChanges.mniFromStorage": "Documento de um armazenamento", "Common.Views.ReviewChanges.mniFromUrl": "Documento de um URL", "Common.Views.ReviewChanges.mniSettings": "Definições de comparação", "Common.Views.ReviewChanges.strFast": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.strFastDesc": "Edição em tempo real. <PERSON><PERSON> as alterações foram guardadas.", "Common.Views.ReviewChanges.strStrict": "Estrito", "Common.Views.ReviewChanges.strStrictDesc": "Utilize o botão 'Guardar' para sincronizar as alterações efetuadas ao documento.", "Common.Views.ReviewChanges.textEnable": "Ativar", "Common.Views.ReviewChanges.textWarnTrackChanges": "O Registo de Alterações estará ligadas para todos os utilizadores com acesso total. A próxima vez que alguém abrir o documento, o Registo Alterações permanecerá ativado.", "Common.Views.ReviewChanges.textWarnTrackChangesTitle": "Ativar rastreio de alterações para todos?", "Common.Views.ReviewChanges.tipAcceptCurrent": "Aceitar alteração atual", "Common.Views.ReviewChanges.tipCoAuthMode": "Definir modo de coedição", "Common.Views.ReviewChanges.tipCommentRem": "Remover comentários", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Remover comentários atuais", "Common.Views.ReviewChanges.tipCommentResolve": "Resolver comentários", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Resolver comentários atuais", "Common.Views.ReviewChanges.tipCompare": "Comparar o documento atual com outro", "Common.Views.ReviewChanges.tipHistory": "<PERSON>rar histó<PERSON>", "Common.Views.ReviewChanges.tipRejectCurrent": "Rejeitar alterações atuais", "Common.Views.ReviewChanges.tipReview": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.tipReviewView": "Selecione o modo em que pretende que as alterações sejam apresentadas", "Common.Views.ReviewChanges.tipSetDocLang": "Definir idioma do documento", "Common.Views.ReviewChanges.tipSetSpelling": "Verificação ortográfica", "Common.Views.ReviewChanges.tipSharing": "<PERSON><PERSON><PERSON> direitos de acesso ao documento", "Common.Views.ReviewChanges.txtAccept": "Aceitar", "Common.Views.ReviewChanges.txtAcceptAll": "Aceitar to<PERSON> as alteraç<PERSON><PERSON>", "Common.Views.ReviewChanges.txtAcceptChanges": "Aceitar alterações", "Common.Views.ReviewChanges.txtAcceptCurrent": "Aceitar alteração atual", "Common.Views.ReviewChanges.txtChat": "Conversa", "Common.Views.ReviewChanges.txtClose": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCoAuthMode": "Modo de co-edição", "Common.Views.ReviewChanges.txtCommentRemAll": "Remover todos os comentários", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Remover comentários atuais", "Common.Views.ReviewChanges.txtCommentRemMy": "Remover os meus comentários", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Remover os meus comentários atuais", "Common.Views.ReviewChanges.txtCommentRemove": "Remover", "Common.Views.ReviewChanges.txtCommentResolve": "Resolver", "Common.Views.ReviewChanges.txtCommentResolveAll": "Resolver todos os comentários", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Resolver comentários atuais", "Common.Views.ReviewChanges.txtCommentResolveMy": "Resolver os meus comentários", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Resolver os meus comentários atuais", "Common.Views.ReviewChanges.txtCompare": "Comparar", "Common.Views.ReviewChanges.txtDocLang": "Idioma", "Common.Views.ReviewChanges.txtEditing": "A editar", "Common.Views.ReviewChanges.txtFinal": "Todas as alterações aceites {0}", "Common.Views.ReviewChanges.txtFinalCap": "Final", "Common.Views.ReviewChanges.txtHistory": "Hist<PERSON><PERSON><PERSON> <PERSON> ve<PERSON>ão", "Common.Views.ReviewChanges.txtMarkup": "<PERSON><PERSON> as alterações {0}", "Common.Views.ReviewChanges.txtMarkupCap": "Marcação e balões", "Common.Views.ReviewChanges.txtMarkupSimple": "<PERSON><PERSON> as alteraç<PERSON><PERSON> {0}<br><PERSON><PERSON> <PERSON> balões", "Common.Views.ReviewChanges.txtMarkupSimpleCap": "Apenas marcação", "Common.Views.ReviewChanges.txtNext": "Para a próxima alteração", "Common.Views.ReviewChanges.txtOff": "Desligado pra mim", "Common.Views.ReviewChanges.txtOffGlobal": "Desligado pra mim e para todos", "Common.Views.ReviewChanges.txtOn": "Ligado para mim", "Common.Views.ReviewChanges.txtOnGlobal": "Ligado para mim e para todos", "Common.Views.ReviewChanges.txtOriginal": "<PERSON><PERSON> as alterações recusadas {0}", "Common.Views.ReviewChanges.txtOriginalCap": "Original", "Common.Views.ReviewChanges.txtPrev": "Para a alteração anterior", "Common.Views.ReviewChanges.txtPreview": "Pré-visualizar", "Common.Views.ReviewChanges.txtReject": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectAll": "<PERSON><PERSON><PERSON><PERSON> as alteraç<PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectChanges": "Rejeitar alterações", "Common.Views.ReviewChanges.txtRejectCurrent": "Rejeitar alteração atual", "Common.Views.ReviewChanges.txtSharing": "Partilhar", "Common.Views.ReviewChanges.txtSpelling": "Verificação ortográfica", "Common.Views.ReviewChanges.txtTurnon": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtView": "Modo de exibição", "Common.Views.ReviewChangesDialog.textTitle": "Rever alter<PERSON>", "Common.Views.ReviewChangesDialog.txtAccept": "Aceitar", "Common.Views.ReviewChangesDialog.txtAcceptAll": "Aceitar to<PERSON> as alteraç<PERSON><PERSON>", "Common.Views.ReviewChangesDialog.txtAcceptCurrent": "Aceitar alteração atual", "Common.Views.ReviewChangesDialog.txtNext": "Para a próxima alteração", "Common.Views.ReviewChangesDialog.txtPrev": "Para a alteração anterior", "Common.Views.ReviewChangesDialog.txtReject": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChangesDialog.txtRejectAll": "<PERSON><PERSON><PERSON><PERSON> as alteraç<PERSON><PERSON>", "Common.Views.ReviewChangesDialog.txtRejectCurrent": "Rejeitar alteração atual", "Common.Views.ReviewPopover.textAdd": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textAddReply": "<PERSON><PERSON><PERSON><PERSON> resposta", "Common.Views.ReviewPopover.textCancel": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textFollowMove": "<PERSON><PERSON><PERSON> movimento", "Common.Views.ReviewPopover.textMention": "+menção disponibiliza o acesso ao documento e envia um e-mail ao utilizador", "Common.Views.ReviewPopover.textMentionNotify": "+menção notifica o utilizador por e-mail", "Common.Views.ReviewPopover.textOpenAgain": "Abrir novamente", "Common.Views.ReviewPopover.textReply": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textResolve": "Resolver", "Common.Views.ReviewPopover.textViewResolved": "Não tem permissão para reabrir o comentário", "Common.Views.ReviewPopover.txtAccept": "Aceitar", "Common.Views.ReviewPopover.txtDeleteTip": "Eliminar", "Common.Views.ReviewPopover.txtEditTip": "<PERSON><PERSON>", "Common.Views.ReviewPopover.txtReject": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SaveAsDlg.textLoading": "A carregar", "Common.Views.SaveAsDlg.textTitle": "Pasta para guardar", "Common.Views.SearchPanel.textCaseSensitive": "Diferenciar maiús<PERSON>s de minúsculas", "Common.Views.SearchPanel.textCloseSearch": "<PERSON><PERSON><PERSON>es<PERSON>", "Common.Views.SearchPanel.textContentChanged": "Documento alterado.", "Common.Views.SearchPanel.textFind": "Localizar", "Common.Views.SearchPanel.textFindAndReplace": "Localizar e substituir", "Common.Views.SearchPanel.textMatchUsingRegExp": "Correspondência através de expressões regulares", "Common.Views.SearchPanel.textNoMatches": "Sem correspondência", "Common.Views.SearchPanel.textNoSearchResults": "Sem resultados de pesquisa", "Common.Views.SearchPanel.textReplace": "Substituir", "Common.Views.SearchPanel.textReplaceAll": "Substituir tudo", "Common.Views.SearchPanel.textReplaceWith": "Substituir com", "Common.Views.SearchPanel.textSearchAgain": "{0}Executar nova pesquisa{1} para resultados precisos.", "Common.Views.SearchPanel.textSearchHasStopped": "A pesquisa parou", "Common.Views.SearchPanel.textSearchResults": "Resultados da pesquisa: {0}/{1}", "Common.Views.SearchPanel.textTooManyResults": "Existem demasiados resultados para poderem ser mostrados aqui", "Common.Views.SearchPanel.textWholeWords": "Palavras inteiras apenas", "Common.Views.SearchPanel.tipNextResult": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.tipPreviousResult": "Resultado anterior", "Common.Views.SelectFileDlg.textLoading": "A carregar", "Common.Views.SelectFileDlg.textTitle": "Selecionar fonte de dados", "Common.Views.SignDialog.textBold": "Negrito", "Common.Views.SignDialog.textCertificate": " Certificado", "Common.Views.SignDialog.textChange": "Alterar", "Common.Views.SignDialog.textInputName": "Inserir nome do assinante", "Common.Views.SignDialog.textItalic": "Itálico", "Common.Views.SignDialog.textNameError": "O nome do assinante não pode estar vazio", "Common.Views.SignDialog.textPurpose": "Objetivo para assinar o documento", "Common.Views.SignDialog.textSelect": "Selecionar", "Common.Views.SignDialog.textSelectImage": "Selecionar imagem", "Common.Views.SignDialog.textSignature": "A assinatura parece ser", "Common.Views.SignDialog.textTitle": "Assinar o documento", "Common.Views.SignDialog.textUseImage": "ou clique \"Selecionar imagem\" para a utilizar como assinatura", "Common.Views.SignDialog.textValid": "Válida de %1 até %2", "Common.Views.SignDialog.tipFontName": "Nome do tipo de letra", "Common.Views.SignDialog.tipFontSize": "Tamanho do tipo de letra", "Common.Views.SignSettingsDialog.textAllowComment": "Permitir ao signatário inserir comentários no diálogo de assinatura", "Common.Views.SignSettingsDialog.textDefInstruction": "Antes de assinar este documento, verifique se o conteúdo que está a assinar está correto.", "Common.Views.SignSettingsDialog.textInfoEmail": "E-mail do assinante sugerido", "Common.Views.SignSettingsDialog.textInfoName": "Assinante sugerido", "Common.Views.SignSettingsDialog.textInfoTitle": "Título do assinante", "Common.Views.SignSettingsDialog.textInstructions": "Instruções para o assinante", "Common.Views.SignSettingsDialog.textShowDate": "Mostrar data na linha de assinatura", "Common.Views.SignSettingsDialog.textTitle": "Definições de Assinatura", "Common.Views.SignSettingsDialog.txtEmpty": "Este campo é obrigatório", "Common.Views.SymbolTableDialog.textCharacter": "Caractere", "Common.Views.SymbolTableDialog.textCode": "Valor Unicode HEX", "Common.Views.SymbolTableDialog.textCopyright": "Assinatura Copyright", "Common.Views.SymbolTableDialog.textDCQuote": "Aspas Duplas de Fechamento", "Common.Views.SymbolTableDialog.textDOQuote": "Aspas de abertura", "Common.Views.SymbolTableDialog.textEllipsis": "Elipse horizontal", "Common.Views.SymbolTableDialog.textEmDash": "Travessão", "Common.Views.SymbolTableDialog.textEmSpace": "Espaço", "Common.Views.SymbolTableDialog.textEnDash": "Travessão", "Common.Views.SymbolTableDialog.textEnSpace": "Espaço", "Common.Views.SymbolTableDialog.textFont": "<PERSON><PERSON><PERSON> de letra", "Common.Views.SymbolTableDialog.textNBHyphen": "Hífen inseparável", "Common.Views.SymbolTableDialog.textNBSpace": "Espaço sem interrupção", "Common.Views.SymbolTableDialog.textPilcrow": "Sinal de antígrafo", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 de espaço (Em)", "Common.Views.SymbolTableDialog.textRange": "Intervalo", "Common.Views.SymbolTableDialog.textRecent": "Símbolos usados recentemente", "Common.Views.SymbolTableDialog.textRegistered": "Sinal Registado", "Common.Views.SymbolTableDialog.textSCQuote": "Aspas de Fechamento", "Common.Views.SymbolTableDialog.textSection": "Sinal de secção", "Common.Views.SymbolTableDialog.textShortcut": "Tecla de atalho", "Common.Views.SymbolTableDialog.textSHyphen": "Hífen virtual", "Common.Views.SymbolTableDialog.textSOQuote": "Apóstrofo de abertura", "Common.Views.SymbolTableDialog.textSpecial": "Caracteres especiais", "Common.Views.SymbolTableDialog.textSymbols": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textTitle": "Símbolo", "Common.Views.SymbolTableDialog.textTradeMark": "Símbolo de Marca Registada.", "Common.Views.UserNameDialog.textDontShow": "Não perguntar novamente", "Common.Views.UserNameDialog.textLabel": "Etiqueta:", "Common.Views.UserNameDialog.textLabelError": "Etiqueta não deve estar em branco.", "DE.Controllers.LeftMenu.leavePageText": "<PERSON><PERSON> as alterações não guardadas serão perdidas.<br>Prima \"Cancelar\" e, depois, prima \"Guardar\". Clique \"Ok\" para descartar as alterações não guardadas.", "DE.Controllers.LeftMenu.newDocumentTitle": "Documento sem nome", "DE.Controllers.LeftMenu.notcriticalErrorTitle": "Aviso", "DE.Controllers.LeftMenu.requestEditRightsText": "Solicitando direitos de edição...", "DE.Controllers.LeftMenu.textLoadHistory": "A carregar o histórico de versões...", "DE.Controllers.LeftMenu.textNoTextFound": "Não foi possível localizar os dados procurados. Por favor ajuste as opções de pesquisa.", "DE.Controllers.LeftMenu.textReplaceSkipped": "A substituição foi realizada. {0} ocorrências foram ignoradas.", "DE.Controllers.LeftMenu.textReplaceSuccess": "A pesquisa foi realizada. Ocorrências substituídas: {0}", "DE.Controllers.LeftMenu.txtCompatible": "O documento será guardado com um novo formato. Desta forma, pode utilizar todas as funcionalidades do editor mas que poderão afetar a disposição do documento.<br>Utilize a opção 'Compatibilidade' nas definições avançadas que quiser tornar este ficheiro compatível com as versões antigas do MS Word.", "DE.Controllers.LeftMenu.txtUntitled": "<PERSON><PERSON> tí<PERSON>lo", "DE.Controllers.LeftMenu.warnDownloadAs": "Se guardar o documento neste formato, perderá todos os atributos com exceção do texto.<br>Tem a certeza de que deseja continuar?", "DE.Controllers.LeftMenu.warnDownloadAsPdf": "O seu {0} será convertido para um formato editável. Isto pode demorar algum tempo. O documento resultante será otimizado para lhe permitir editar o texto, pelo que poderá não ser exatamente igual ao {0} original, especialmente se o ficheiro original contiver muitos gráficos.", "DE.Controllers.LeftMenu.warnDownloadAsRTF": "Se guardar o documento neste formato, perderá alguns dos atributos de formatação.<br>Tem a certeza de que deseja continuar?", "DE.Controllers.LeftMenu.warnReplaceString": "{0} não é um caracter especial válido para o campo de substituição.", "DE.Controllers.Main.applyChangesTextText": "A carregar as alteraç<PERSON>es...", "DE.Controllers.Main.applyChangesTitleText": "A carregar as alterações", "DE.Controllers.Main.convertationTimeoutText": "Excedeu o tempo limite de conversão.", "DE.Controllers.Main.criticalErrorExtText": "Prima \"OK\" para voltar para a lista de documentos.", "DE.Controllers.Main.criticalErrorTitle": "Erro", "DE.Controllers.Main.downloadErrorText": "<PERSON>alha ao <PERSON>.", "DE.Controllers.Main.downloadMergeText": "A descarregar...", "DE.Controllers.Main.downloadMergeTitle": "A descarregar", "DE.Controllers.Main.downloadTextText": "A descarregar documento...", "DE.Controllers.Main.downloadTitleText": "A descarregar documento", "DE.Controllers.Main.errorAccessDeny": "Está a tentar executar uma ação para a qual não tem permissões.<br>Por favor contacte o administrador do servidor de documentos.", "DE.Controllers.Main.errorBadImageUrl": "URL inválido", "DE.Controllers.Main.errorCoAuthoringDisconnect": "Conexão com servidor perdida. O documento não pode ser editado neste momento.", "DE.Controllers.Main.errorComboSeries": "Para criar um gráfico de combinação, selecione pelo menos duas séries de dados.", "DE.Controllers.Main.errorCompare": "A funcionalidade de Comparar Documentos não está disponível enquanto se faz a coedição.", "DE.Controllers.Main.errorConnectToServer": "Não foi possível guardar o documento. Verifique a sua ligação de rede ou contacte o seu administrador.<br>Ao clicar no botão OK, ser-lhe-á pedido para descarregar o documento.", "DE.Controllers.Main.errorDatabaseConnection": "Erro externo.<br><PERSON><PERSON> de conexão ao banco de dados. Entre em contato com o suporte caso o erro persista.", "DE.Controllers.Main.errorDataEncrypted": "Foram recebidas alterações cifradas que não puderam ser decifradas.", "DE.Controllers.Main.errorDataRange": "Intervalo de dados inválido.", "DE.Controllers.Main.errorDefaultMessage": "Código do erro: %1", "DE.Controllers.Main.errorDirectUrl": "Verifique a ligação ao documento.<br><PERSON>e ser uma ligação direta para o ficheiro a descarregar.", "DE.Controllers.Main.errorEditingDownloadas": "Ocorreu um erro ao trabalhar no documento.<br>Utilize a opção 'Descarregar como' para guardar a cópia de segurança do ficheiro numa unidade.", "DE.Controllers.Main.errorEditingSaveas": "Ocorreu um erro ao trabalhar no documento.<br>Utilize a opção 'Descarregar como...' para guardar a cópia de segurança do ficheiro numa unidade.", "DE.Controllers.Main.errorEmailClient": "Não foi possível encontrar nenhum cliente de e-mail.", "DE.Controllers.Main.errorEmptyTOC": "Pode criar um índice remissivo aplicando um estilo ao texto selecionado a partir da galeria de estilos.", "DE.Controllers.Main.errorFilePassProtect": "O ficheiro está protegido por palavra-passe e não pode ser aberto.", "DE.Controllers.Main.errorFileSizeExceed": "O tamanho do documento excede o limite do servidor.<br>Contacte o administrador do servidor de documentos para mais detalhes.", "DE.Controllers.Main.errorForceSave": "Ocorreu um erro ao guardar o ficheiro. Utilize a opção 'Descarregar como' para guardar o ficheiro para uma unidade ou tente novamente mais tarde.", "DE.Controllers.Main.errorKeyEncrypt": "Descritor de chave desconhecido", "DE.Controllers.Main.errorKeyExpire": "Descritor de chave expirado", "DE.Controllers.Main.errorLoadingFont": "Os tipos de letra não foram carregados.<br>Contacte o administrador do servidor de documentos.", "DE.Controllers.Main.errorMailMergeLoadFile": "Não foi possível carregar o documento. Por favor escolha outro ficheiro.", "DE.Controllers.Main.errorMailMergeSaveFile": "Falha ao unir.", "DE.Controllers.Main.errorNoTOC": "Não existem alterações a fazer no índice remissivo. Pode introduzir alterações no separador Referências.", "DE.Controllers.Main.errorPasswordIsNotCorrect": "A palavra-passe que introduziu não está correta.<br>Verifique se a tecla CAPS LOCK está desligada e não se esqueça de utilizar a capitalização correta.", "DE.Controllers.Main.errorProcessSaveResult": "<PERSON><PERSON>ha ao guardar.", "DE.Controllers.Main.errorServerVersion": "A versão do editor foi atualizada. A página será recarregada para aplicar as alterações.", "DE.Controllers.Main.errorSessionAbsolute": "A sessão de edição expirou. Tente recarregar a página.", "DE.Controllers.Main.errorSessionIdle": "Este documento não foi editado durante muito tempo. Tente recarregar a página.", "DE.Controllers.Main.errorSessionToken": "A ligação ao servidor foi interrompida. Tente recarregar a página.", "DE.Controllers.Main.errorSetPassword": "Não foi possível definir a palavra-passe.", "DE.Controllers.Main.errorStockChart": "Ordem de linha inválida. Para criar um gráfico de cotações, coloque os dados na folha pela seguinte ordem:<br>preço de abertura, preço máximo, preço mínimo, preço de fecho.", "DE.Controllers.Main.errorSubmit": "Falha ao submeter.", "DE.Controllers.Main.errorTextFormWrongFormat": "O valor introduzido não corresponde ao formato do campo.", "DE.Controllers.Main.errorToken": "O token de segurança do documento não foi formatado corretamente.<br>Entre em contato com o administrador do Servidor de Documentos.", "DE.Controllers.Main.errorTokenExpire": "O token de segurança do documento expirou.<br>Entre em contato com o administrador do Servidor de Documentos.", "DE.Controllers.Main.errorUpdateVersion": "A versão do ficheiro foi alterada. A página será recarregada.", "DE.Controllers.Main.errorUpdateVersionOnDisconnect": "A ligação foi restaurada e a versão do ficheiro foi alterada.<br><PERSON><PERSON> de poder continuar a trabalhar, é necessário descarregar o ficheiro ou copiar o seu conteúdo para garantir que nada se perde e depois voltar a carregar esta página.", "DE.Controllers.Main.errorUserDrop": "De momento, não é possível aceder ao ficheiro.", "DE.Controllers.Main.errorUsersExceed": "Excedeu o número máximo de utilizadores permitidos pelo seu plano", "DE.Controllers.Main.errorViewerDisconnect": "Ligação perdida. Ainda pode ver o documento mas<br>não o conseguirá descarregar até que a ligação seja restaurada e a página recarregada.", "DE.Controllers.Main.leavePageText": "Este documento tem alterações não guardadas. Clique 'Ficar na página' para que o documento seja guardado automaticamente. Clique 'Sair da página' para rejeitar todas as alterações.", "DE.Controllers.Main.leavePageTextOnClose": "<PERSON><PERSON> as alterações não guardadas serão perdidas.<br>Clique em \"Cancelar\" e depois em \"Guardar\" para as guardar. Clique em \"Ok\" para descartar todas as alterações não guardadas.", "DE.Controllers.Main.loadFontsTextText": "A carregar dados...", "DE.Controllers.Main.loadFontsTitleText": "A carregar dados", "DE.Controllers.Main.loadFontTextText": "A carregar dados...", "DE.Controllers.Main.loadFontTitleText": "A carregar dados", "DE.Controllers.Main.loadImagesTextText": "A carregar imagens...", "DE.Controllers.Main.loadImagesTitleText": "A carregar imagens", "DE.Controllers.Main.loadImageTextText": "A carregar imagem...", "DE.Controllers.Main.loadImageTitleText": "A carregar imagem", "DE.Controllers.Main.loadingDocumentTextText": "A carregar documento...", "DE.Controllers.Main.loadingDocumentTitleText": "A carregar documento", "DE.Controllers.Main.mailMergeLoadFileText": "A carregar origem de dados...", "DE.Controllers.Main.mailMergeLoadFileTitle": "A carregar origem de dados", "DE.Controllers.Main.notcriticalErrorTitle": "Aviso", "DE.Controllers.Main.openErrorText": "Ocorreu um erro ao abrir o ficheiro.", "DE.Controllers.Main.openTextText": "Abrindo documento...", "DE.Controllers.Main.openTitleText": "Abrindo documento", "DE.Controllers.Main.printTextText": "Imprimindo documento...", "DE.Controllers.Main.printTitleText": "Imprimindo documento", "DE.Controllers.Main.reloadButtonText": "Re<PERSON><PERSON><PERSON> p<PERSON>gina", "DE.Controllers.Main.requestEditFailedMessageText": "Alguém está editando este documento neste momento. Tente novamente mais tarde.", "DE.Controllers.Main.requestEditFailedTitleText": "<PERSON><PERSON> recusado", "DE.Controllers.Main.saveErrorText": "Ocorreu um erro ao guardar o ficheiro.", "DE.Controllers.Main.saveErrorTextDesktop": "Este ficheiro não pode ser guardado ou criado.<br>Os motivos podem ser: <br>1. O ficheiro é apenas de leitura. <br>2. O ficheiro está a ser editado por outro utilizador.<br>3. O disco está cheio ou danificado.", "DE.Controllers.Main.saveTextText": "A guardar documento...", "DE.Controllers.Main.saveTitleText": "A guardar documento", "DE.Controllers.Main.scriptLoadError": "A ligação está muito lenta e alguns dos componentes não foram carregados. Tente recarregar a página.", "DE.Controllers.Main.sendMergeText": "A enviar combinação...", "DE.Controllers.Main.sendMergeTitle": "A enviar combinação", "DE.Controllers.Main.splitDividerErrorText": "O número de linhas deve ser um divisor de %1.", "DE.Controllers.Main.splitMaxColsErrorText": "O número de colunas deve ser inferior a %1.", "DE.Controllers.Main.splitMaxRowsErrorText": "O número de linhas deve ser inferior a %1.", "DE.Controllers.Main.textAnonymous": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.textAnyone": "Alguém", "DE.Controllers.Main.textApplyAll": "Aplicar a todas as equações", "DE.Controllers.Main.textBuyNow": "Visitar site", "DE.Controllers.Main.textChangesSaved": "<PERSON><PERSON> as alterações foram guardadas", "DE.Controllers.Main.textClose": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.textCloseTip": "Clique para fechar a dica", "DE.Controllers.Main.textContactUs": "Contacte a equipa comercial", "DE.Controllers.Main.textContinue": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.textConvertEquation": "Esta equação foi criada com uma versão anterior da aplicação e já não é suportada. Para a editar, tem que converter a equação para o formato Office Math ML.<br>Converter agora?", "DE.Controllers.Main.textCustomLoader": "Tenha em conta de que, de acordo com os termos da licença, não tem permissões para alterar o carregador.<br><PERSON>r favor contacte a equipa comercial.", "DE.Controllers.Main.textDisconnect": "A ligação está perdida", "DE.Controllers.Main.textGuest": "Convidado(a)", "DE.Controllers.Main.textHasMacros": "O ficheiro contém macros automáticas.<br><PERSON><PERSON><PERSON> executar as macros?", "DE.Controllers.Main.textLearnMore": "<PERSON><PERSON> mais", "DE.Controllers.Main.textLoadingDocument": "A carregar documento", "DE.Controllers.Main.textLongName": "Introduza um nome com menos de 128 caracteres.", "DE.Controllers.Main.textNoLicenseTitle": "Atingiu o limite da licença", "DE.Controllers.Main.textPaidFeature": "Funcionalidade paga", "DE.Controllers.Main.textReconnect": "A ligação foi reposta", "DE.Controllers.Main.textRemember": "Memorizar a minha escolha", "DE.Controllers.Main.textRememberMacros": "Memor<PERSON>r escolha para todas as macros", "DE.Controllers.Main.textRenameError": "O nome de utilizador não pode estar em branco.", "DE.Controllers.Main.textRenameLabel": "Introduza um nome a ser usado para colaboração", "DE.Controllers.Main.textRequestMacros": "Uma macro faz um pedido de URL. Quer permitir o pedido à %1?", "DE.Controllers.Main.textShape": "Forma", "DE.Controllers.Main.textStrict": "Modo estrito", "DE.Controllers.Main.textText": "Texto", "DE.Controllers.Main.textTryUndoRedo": "As funções Desfazer/<PERSON><PERSON><PERSON> foram desativadas para se poder co-editar o documento.<br>Clique no botão 'Modo estrito' para ativar este modo de edição e editar o ficheiro sem ser incomodado por outros utilizadores enviando apenas as suas alterações assim que terminar e guardar. Pode alternar entre modos de co-edição através das definições avançadas.", "DE.Controllers.Main.textTryUndoRedoWarn": "As funções Desfazer/Refazer estão desativadas no modo de co-edição rápida.", "DE.Controllers.Main.textUndo": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.titleLicenseExp": "Licença expirada", "DE.Controllers.Main.titleServerVersion": "Editor atual<PERSON><PERSON>", "DE.Controllers.Main.titleUpdateVersion": "Versão alterada", "DE.Controllers.Main.txtAbove": "acima", "DE.Controllers.Main.txtArt": "O seu texto aqui", "DE.Controllers.Main.txtBasicShapes": "Formas básicas", "DE.Controllers.Main.txtBelow": "abaixo", "DE.Controllers.Main.txtBookmarkError": "Erro! Marcador não definido.", "DE.Controllers.Main.txtButtons": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtCallouts": "Textos explicativos", "DE.Controllers.Main.txtCharts": "Grá<PERSON><PERSON>", "DE.Controllers.Main.txtChoose": "Escolha um item", "DE.Controllers.Main.txtClickToLoad": "Clique para carregar a imagem", "DE.Controllers.Main.txtCurrentDocument": "Documento atual", "DE.Controllers.Main.txtDiagramTitle": "Título do <PERSON>a", "DE.Controllers.Main.txtEditingMode": "Definir modo de edição...", "DE.Controllers.Main.txtEndOfFormula": "Fim Inesperado da Fórmula", "DE.Controllers.Main.txtEnterDate": "Indique uma data", "DE.Controllers.Main.txtErrorLoadHistory": "Falha ao carregar histórico", "DE.Controllers.Main.txtEvenPage": "Página par", "DE.Controllers.Main.txtFiguredArrows": "Setas figuradas", "DE.Controllers.Main.txtFirstPage": "Primeira página", "DE.Controllers.Main.txtFooter": "Rodapé", "DE.Controllers.Main.txtFormulaNotInTable": "A Fórmula Não Está na Tabela", "DE.Controllers.Main.txtHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtHyperlink": "Hiperligação", "DE.Controllers.Main.txtIndTooLarge": "Índice demasiado grande", "DE.Controllers.Main.txtLines": "<PERSON><PERSON>", "DE.Controllers.Main.txtMainDocOnly": "Erro! Apenas documento principal.", "DE.Controllers.Main.txtMath": "Matemática", "DE.Controllers.Main.txtMissArg": "Argumento em falta", "DE.Controllers.Main.txtMissOperator": "Operador em falta", "DE.Controllers.Main.txtNeedSynchronize": "Você tem atualizações", "DE.Controllers.Main.txtNone": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtNoTableOfContents": "Não existem títulos no documento. Aplique um estilo de título ao texto para que este apareça no índice remissivo.", "DE.Controllers.Main.txtNoTableOfFigures": "Não foi encontrada nenhuma entrada no índice de ilustrações.", "DE.Controllers.Main.txtNoText": "Erro! Não existe texto com este estilo no documento.", "DE.Controllers.Main.txtNotInTable": "Não é uma tabela", "DE.Controllers.Main.txtNotValidBookmark": "Erro! Não é uma auto-referência de marcador válida.", "DE.Controllers.Main.txtOddPage": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtOnPage": "na página", "DE.Controllers.Main.txtRectangles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtSameAsPrev": "Igual à anterior", "DE.Controllers.Main.txtSection": "-Secção", "DE.Controllers.Main.txtSeries": "Série", "DE.Controllers.Main.txtShape_accentBorderCallout1": "Chamada da linha 1 (contorno e barra de destaque)", "DE.Controllers.Main.txtShape_accentBorderCallout2": "Chamada da linha 2 (contorno e barra de destaque)", "DE.Controllers.Main.txtShape_accentBorderCallout3": "Chamada da linha 3 (contorno e barra de destaque)", "DE.Controllers.Main.txtShape_accentCallout1": "Chamada da linha 1 (barra de destaque)", "DE.Controllers.Main.txtShape_accentCallout2": "Chamada da linha 2 (barra de destaque)", "DE.Controllers.Main.txtShape_accentCallout3": "Chamada da linha 3 (barra de destaque)", "DE.Controllers.Main.txtShape_actionButtonBackPrevious": "<PERSON><PERSON><PERSON> ou Anterior", "DE.Controllers.Main.txtShape_actionButtonBeginning": "Botão Início", "DE.Controllers.Main.txtShape_actionButtonBlank": "Botão vazio", "DE.Controllers.Main.txtShape_actionButtonDocument": "Botão Documento", "DE.Controllers.Main.txtShape_actionButtonEnd": "Botão Final", "DE.Controllers.Main.txtShape_actionButtonForwardNext": "Bot<PERSON><PERSON> e/ou Avançar", "DE.Controllers.Main.txtShape_actionButtonHelp": "Botão Ajuda", "DE.Controllers.Main.txtShape_actionButtonHome": "Botão Base", "DE.Controllers.Main.txtShape_actionButtonInformation": "Botão Informação", "DE.Controllers.Main.txtShape_actionButtonMovie": "Botão Filme", "DE.Controllers.Main.txtShape_actionButtonReturn": "Botão de Voltar", "DE.Controllers.Main.txtShape_actionButtonSound": "Botão Som", "DE.Controllers.Main.txtShape_arc": "Arco", "DE.Controllers.Main.txtShape_bentArrow": "Seta curvada", "DE.Controllers.Main.txtShape_bentConnector5": "Conector angular", "DE.Controllers.Main.txtShape_bentConnector5WithArrow": "Conector de seta angular", "DE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Conector de seta dupla angulada", "DE.Controllers.Main.txtShape_bentUpArrow": "Seta para cima dobrada", "DE.Controllers.Main.txtShape_bevel": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_blockArc": "Arco de bloco", "DE.Controllers.Main.txtShape_borderCallout1": "Chamada da linha 1", "DE.Controllers.Main.txtShape_borderCallout2": "Chamada da linha 2", "DE.Controllers.Main.txtShape_borderCallout3": "Chamada da linha 3", "DE.Controllers.Main.txtShape_bracePair": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_callout1": "Chamada da linha 1 (sem contorno)", "DE.Controllers.Main.txtShape_callout2": "Chamada da linha 2 (sem contorno)", "DE.Controllers.Main.txtShape_callout3": "Chamada da linha 3 (sem contorno)", "DE.Controllers.Main.txtShape_can": "Pode", "DE.Controllers.Main.txtShape_chevron": "Divisa", "DE.Controllers.Main.txtShape_chord": "Acorde", "DE.Controllers.Main.txtShape_circularArrow": "Seta circular", "DE.Controllers.Main.txtShape_cloud": "Nuvem", "DE.Controllers.Main.txtShape_cloudCallout": "Texto explicativo na nuvem", "DE.Controllers.Main.txtShape_corner": "Canto", "DE.Controllers.Main.txtShape_cube": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_curvedConnector3": "Conector curvado", "DE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Conector de seta curvada", "DE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Conector de seta dupla curvado", "DE.Controllers.Main.txtShape_curvedDownArrow": "Seta curvada para baixo", "DE.Controllers.Main.txtShape_curvedLeftArrow": "Seta curvada para a esquerda", "DE.Controllers.Main.txtShape_curvedRightArrow": "Seta curvava para a direita", "DE.Controllers.Main.txtShape_curvedUpArrow": "Seta curvada para cima", "DE.Controllers.Main.txtShape_decagon": "Decágono", "DE.Controllers.Main.txtShape_diagStripe": "<PERSON><PERSON><PERSON> diagonal", "DE.Controllers.Main.txtShape_diamond": "Diamante", "DE.Controllers.Main.txtShape_dodecagon": "Dodecágono", "DE.Controllers.Main.txtShape_donut": "Donut", "DE.Controllers.Main.txtShape_doubleWave": "Til", "DE.Controllers.Main.txtShape_downArrow": "Seta para baixo", "DE.Controllers.Main.txtShape_downArrowCallout": "Chamada com seta para baixo", "DE.Controllers.Main.txtShape_ellipse": "Elipse", "DE.Controllers.Main.txtShape_ellipseRibbon": "Faixa curvada para baixo", "DE.Controllers.Main.txtShape_ellipseRibbon2": "Faixa curvada para cima", "DE.Controllers.Main.txtShape_flowChartAlternateProcess": "Fluxograma: Processo alternativo", "DE.Controllers.Main.txtShape_flowChartCollate": "Fluxograma: Agrupar", "DE.Controllers.Main.txtShape_flowChartConnector": "Fluxograma: Conector", "DE.Controllers.Main.txtShape_flowChartDecision": "Fluxograma: Decisão", "DE.Controllers.Main.txtShape_flowChartDelay": "Fluxograma: Atraso", "DE.Controllers.Main.txtShape_flowChartDisplay": "Fluxograma: Exibir", "DE.Controllers.Main.txtShape_flowChartDocument": "Fluxograma: Documento", "DE.Controllers.Main.txtShape_flowChartExtract": "Fluxograma: Extrair", "DE.Controllers.Main.txtShape_flowChartInputOutput": "Fluxograma: <PERSON><PERSON>", "DE.Controllers.Main.txtShape_flowChartInternalStorage": "Fluxograma: Armazenamento interno", "DE.Controllers.Main.txtShape_flowChartMagneticDisk": "Fluxograma: Disco magnético", "DE.Controllers.Main.txtShape_flowChartMagneticDrum": "Fluxograma: Armazenamento de acesso direto", "DE.Controllers.Main.txtShape_flowChartMagneticTape": "Fluxograma: Armazenamento de acesso sequencial", "DE.Controllers.Main.txtShape_flowChartManualInput": "Fluxograma: Entrada manual", "DE.Controllers.Main.txtShape_flowChartManualOperation": "Fluxograma: Operação manual", "DE.Controllers.Main.txtShape_flowChartMerge": "Fluxograma: Unir", "DE.Controllers.Main.txtShape_flowChartMultidocument": "Fluxograma: <PERSON><PERSON><PERSON><PERSON> documentos", "DE.Controllers.Main.txtShape_flowChartOffpageConnector": "Fluxograma: Conector fora da página", "DE.Controllers.Main.txtShape_flowChartOnlineStorage": "Fluxograma: Dad<PERSON> armaz<PERSON>", "DE.Controllers.Main.txtShape_flowChartOr": "Fluxograma: Ou", "DE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Fluxograma: Processo predefinido", "DE.Controllers.Main.txtShape_flowChartPreparation": "Fluxograma: Preparação", "DE.Controllers.Main.txtShape_flowChartProcess": "Fluxograma: Processo", "DE.Controllers.Main.txtShape_flowChartPunchedCard": "Fluxograma: Cartão", "DE.Controllers.Main.txtShape_flowChartPunchedTape": "Fluxograma: <PERSON><PERSON> perfurada", "DE.Controllers.Main.txtShape_flowChartSort": "Fluxograma: Ordenar", "DE.Controllers.Main.txtShape_flowChartSummingJunction": "Fluxograma: Junção de Soma", "DE.Controllers.Main.txtShape_flowChartTerminator": "Fluxograma: Exterminador", "DE.Controllers.Main.txtShape_foldedCorner": "<PERSON><PERSON> do<PERSON>", "DE.Controllers.Main.txtShape_frame": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_halfFrame": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_heart": "Coração", "DE.Controllers.Main.txtShape_heptagon": "<PERSON>pt<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_hexagon": "Hexágono", "DE.Controllers.Main.txtShape_homePlate": "Pentágono", "DE.Controllers.Main.txtShape_horizontalScroll": "Deslocação horizontal", "DE.Controllers.Main.txtShape_irregularSeal1": "Explosão 1", "DE.Controllers.Main.txtShape_irregularSeal2": "Explosão 2", "DE.Controllers.Main.txtShape_leftArrow": "Seta para esquerda", "DE.Controllers.Main.txtShape_leftArrowCallout": "Chamada com seta para a esquerda", "DE.Controllers.Main.txtShape_leftBrace": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_leftBracket": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_leftRightArrow": "Seta para a esquerda e para a direita", "DE.Controllers.Main.txtShape_leftRightArrowCallout": "Chamada com seta para a esquerda e direita", "DE.Controllers.Main.txtShape_leftRightUpArrow": "Seta para cima, para a direita e para a esquerda", "DE.Controllers.Main.txtShape_leftUpArrow": "Seta para a esquerda e para cima", "DE.Controllers.Main.txtShape_lightningBolt": "Relâmpago", "DE.Controllers.Main.txtShape_line": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_lineWithArrow": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_lineWithTwoArrows": "<PERSON>a dupla", "DE.Controllers.Main.txtShape_mathDivide": "Divisão", "DE.Controllers.Main.txtShape_mathEqual": "Igual", "DE.Controllers.Main.txtShape_mathMinus": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_mathMultiply": "Multiplicar", "DE.Controllers.Main.txtShape_mathNotEqual": "Não é igual", "DE.Controllers.Main.txtShape_mathPlus": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_moon": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_noSmoking": "Símbolo \"Não\"", "DE.Controllers.Main.txtShape_notchedRightArrow": "Seta entalhada para a direita", "DE.Controllers.Main.txtShape_octagon": "Octógono", "DE.Controllers.Main.txtShape_parallelogram": "Paralelograma", "DE.Controllers.Main.txtShape_pentagon": "Pentágono", "DE.Controllers.Main.txtShape_pie": "Tarte", "DE.Controllers.Main.txtShape_plaque": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_plus": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_polyline1": "Rabisco", "DE.Controllers.Main.txtShape_polyline2": "Forma livre", "DE.Controllers.Main.txtShape_quadArrow": "<PERSON>a cruzada", "DE.Controllers.Main.txtShape_quadArrowCallout": "Chamada com seta cruzada", "DE.Controllers.Main.txtShape_rect": "Re<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_ribbon": "Faixa para baixo", "DE.Controllers.Main.txtShape_ribbon2": "Faixa para cima", "DE.Controllers.Main.txtShape_rightArrow": "Seta para a direita", "DE.Controllers.Main.txtShape_rightArrowCallout": "Chamada com seta para a direita", "DE.Controllers.Main.txtShape_rightBrace": "Chaveta à Direita", "DE.Controllers.Main.txtShape_rightBracket": "Parê<PERSON><PERSON> direito", "DE.Controllers.Main.txtShape_round1Rect": "Retângulo de Apenas Um Canto Redondo", "DE.Controllers.Main.txtShape_round2DiagRect": "Retângulo Diagonal Redondo", "DE.Controllers.Main.txtShape_round2SameRect": "Retângulo do Mesmo Lado Redondo", "DE.Controllers.Main.txtShape_roundRect": "Retângulo de Cantos Redondos", "DE.Controllers.Main.txtShape_rtTriangle": "Triângulo à Direita", "DE.Controllers.Main.txtShape_smileyFace": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_snip1Rect": "Retângulo de Canto Cortado", "DE.Controllers.Main.txtShape_snip2DiagRect": "Retângulo de Cantos Diagonais Cortados", "DE.Controllers.Main.txtShape_snip2SameRect": "Retân<PERSON>lo de cantos cortados no mesmo lado", "DE.Controllers.Main.txtShape_snipRoundRect": "Retângulo Com Canto Arredondado e Canto Cortado", "DE.Controllers.Main.txtShape_spline": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_star10": "Estrela de 10 pontos", "DE.Controllers.Main.txtShape_star12": "Estrela de 12 pontos", "DE.Controllers.Main.txtShape_star16": "Estrela de 16 pontos", "DE.Controllers.Main.txtShape_star24": "Estrela de 24 pontos", "DE.Controllers.Main.txtShape_star32": "Estrela de 32 pontos", "DE.Controllers.Main.txtShape_star4": "Estrela de 4 pontos", "DE.Controllers.Main.txtShape_star5": "Estrela de 5 pontos", "DE.Controllers.Main.txtShape_star6": "Estrela de 6 pontos", "DE.Controllers.Main.txtShape_star7": "Estrela de 7 pontos", "DE.Controllers.Main.txtShape_star8": "Estrela de 8 pontos", "DE.Controllers.Main.txtShape_stripedRightArrow": "Seta riscada para a direita", "DE.Controllers.Main.txtShape_sun": "Sol", "DE.Controllers.Main.txtShape_teardrop": "Lágrima", "DE.Controllers.Main.txtShape_textRect": "Caixa de texto", "DE.Controllers.Main.txtShape_trapezoid": "Trapé<PERSON>", "DE.Controllers.Main.txtShape_triangle": "Triângulo", "DE.Controllers.Main.txtShape_upArrow": "Seta para cima", "DE.Controllers.Main.txtShape_upArrowCallout": "Chamada com seta para cima", "DE.Controllers.Main.txtShape_upDownArrow": "Seta para cima e para baixo", "DE.Controllers.Main.txtShape_uturnArrow": "Seta em forma de U", "DE.Controllers.Main.txtShape_verticalScroll": "Deslocação vertical", "DE.Controllers.Main.txtShape_wave": "On<PERSON>", "DE.Controllers.Main.txtShape_wedgeEllipseCallout": "Chamada oval", "DE.Controllers.Main.txtShape_wedgeRectCallout": "<PERSON><PERSON> retangular", "DE.Controllers.Main.txtShape_wedgeRoundRectCallout": "<PERSON><PERSON> retangular arredondada", "DE.Controllers.Main.txtStarsRibbons": "Estrelas e arco-íris", "DE.Controllers.Main.txtStyle_Caption": "<PERSON>a", "DE.Controllers.Main.txtStyle_endnote_text": "Texto da nota final", "DE.Controllers.Main.txtStyle_footnote_text": "Texto da nota de rodapé", "DE.Controllers.Main.txtStyle_Heading_1": "Título 1", "DE.Controllers.Main.txtStyle_Heading_2": "Título 2", "DE.Controllers.Main.txtStyle_Heading_3": "Título 3", "DE.Controllers.Main.txtStyle_Heading_4": "Título 4", "DE.Controllers.Main.txtStyle_Heading_5": "Título 5", "DE.Controllers.Main.txtStyle_Heading_6": "Título 6", "DE.Controllers.Main.txtStyle_Heading_7": "Título 7", "DE.Controllers.Main.txtStyle_Heading_8": "Título 8", "DE.Controllers.Main.txtStyle_Heading_9": "Título 9", "DE.Controllers.Main.txtStyle_Intense_Quote": "Citação forte", "DE.Controllers.Main.txtStyle_List_Paragraph": "Parágrafo em lista", "DE.Controllers.Main.txtStyle_No_Spacing": "Sem espaçamento", "DE.Controllers.Main.txtStyle_Normal": "Normal", "DE.Controllers.Main.txtStyle_Quote": "Citar", "DE.Controllers.Main.txtStyle_Subtitle": "Subtítulo", "DE.Controllers.Main.txtStyle_Title": "Titulo", "DE.Controllers.Main.txtSyntaxError": "Erro de Sintaxe", "DE.Controllers.Main.txtTableInd": "O Índice da Tabela Não Pode Ser Zero", "DE.Controllers.Main.txtTableOfContents": "Índice remissivo", "DE.Controllers.Main.txtTableOfFigures": "Tabela de figuras", "DE.Controllers.Main.txtTOCHeading": "Título do índice remissivo", "DE.Controllers.Main.txtTooLarge": "Número <PERSON> para Formatar", "DE.Controllers.Main.txtTypeEquation": "Digite uma equação aqui.", "DE.Controllers.Main.txtUndefBookmark": "Marc<PERSON>Definido", "DE.Controllers.Main.txtXAxis": "Eixo X", "DE.Controllers.Main.txtYAxis": "Eixo Y", "DE.Controllers.Main.txtZeroDivide": "Divisão por zero", "DE.Controllers.Main.unknownErrorText": "<PERSON><PERSON> desconhecido.", "DE.Controllers.Main.unsupportedBrowserErrorText": "<PERSON>u navegador não é suportado.", "DE.Controllers.Main.uploadDocExtMessage": "Formato de documento desconhecido.", "DE.Controllers.Main.uploadDocFileCountMessage": "Nenhum documento foi carregado.", "DE.Controllers.Main.uploadDocSizeMessage": "Excedeu o limite de tamanho para o documento.", "DE.Controllers.Main.uploadImageExtMessage": "Formato desconhecido", "DE.Controllers.Main.uploadImageFileCountMessage": "<PERSON>enhuma imagem foi carregada.", "DE.Controllers.Main.uploadImageSizeMessage": "A imagem é demasiado grande. O tamanho máximo é de 25 MB.", "DE.Controllers.Main.uploadImageTextText": "A carregar imagem...", "DE.Controllers.Main.uploadImageTitleText": "A carregar imagem", "DE.Controllers.Main.waitText": "Aguarde...", "DE.Controllers.Main.warnBrowserIE9": "A aplicação não funciona corretamente com IE9. Deve utilizar IE10 ou superior", "DE.Controllers.Main.warnBrowserZoom": "A definição 'zoom' do seu navegador não é totalmente suportada. Prima Ctrl+0 para repor o valor padrão.", "DE.Controllers.Main.warnLicenseExceeded": "Atingiu o limite de ligações simultâneas a %1 editores. Este documento será aberto no modo de leitura.<br>Contacte o administrador para obter mais de<PERSON>hes.", "DE.Controllers.Main.warnLicenseExp": "A sua licença expirou.<br>Deve atualizar a licença e recarregar a página.", "DE.Controllers.Main.warnLicenseLimitedNoAccess": "Licença expirada.<br>Não pode editar o documento.<br>Contacte o administrador de sistemas.", "DE.Controllers.Main.warnLicenseLimitedRenewed": "Tem que renovar a sua licença.<br>A edição de documentos está limitada.<br>Contacte o administrador de sistemas para obter acesso completo.", "DE.Controllers.Main.warnLicenseUsersExceeded": "Atingiu o limite de %1 editores. Contacte o seu administrador para obter detalhes.", "DE.Controllers.Main.warnNoLicense": "Atingiu o limite de ligações simultâneas a %1 editores. Este documento será aberto no modo de leitura.<br>Contacte a equipa comercial %1 para saber mais sobre os termos de licenciamento.", "DE.Controllers.Main.warnNoLicenseUsers": "Atingiu o limite de %1 editores. Contacte a equipa comercial %1 para obter mais informações.", "DE.Controllers.Main.warnProcessRightsChange": "Você não tem permissões para editar o ficheiro.", "DE.Controllers.Navigation.txtBeginning": "Início do documento", "DE.Controllers.Navigation.txtGotoBeginning": "Ir para o início do documento", "DE.Controllers.Print.txtCustom": "Personalizado", "DE.Controllers.Search.notcriticalErrorTitle": "Aviso", "DE.Controllers.Search.textNoTextFound": "Não foi possível localizar os dados procurados. Ajuste as opções de pesquisa.", "DE.Controllers.Search.textReplaceSkipped": "A substituição foi realizada. {0} ocorrências foram ignoradas.", "DE.Controllers.Search.textReplaceSuccess": "A pesquisa foi concluída. {0} ocorrências foram substituídas", "DE.Controllers.Search.warnReplaceString": "{0} não é um carácter especial válido para a janela Substituir com.", "DE.Controllers.Statusbar.textDisconnect": "<b>Sem ligação</b><br>A tentar ligar. Por favor, verifique as definições da ligação.", "DE.Controllers.Statusbar.textHasChanges": "Novas alterações foram rastreadas", "DE.Controllers.Statusbar.textSetTrackChanges": "Está no modo Registar Alterações", "DE.Controllers.Statusbar.textTrackChanges": "The document is opened with the Track Changes mode enabled", "DE.Controllers.Statusbar.tipReview": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Statusbar.zoomText": "Zoom {0}%", "DE.Controllers.Toolbar.confirmAddFontName": "O tipo de letra que vai guardar não está disponível no dispositivo atual.<br>O estilo de texto será apresentado utilizando um dos tipos de letra do sistema, o tipo de letra guardado será utilizado quando estiver disponível.<br>Quer continuar?", "DE.Controllers.Toolbar.dataUrl": "Colar um URL de dados", "DE.Controllers.Toolbar.notcriticalErrorTitle": "Aviso", "DE.Controllers.Toolbar.textAccent": "Destaques", "DE.Controllers.Toolbar.textBracket": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.textEmptyImgUrl": "Tem que especificar o URL da imagem.", "DE.Controllers.Toolbar.textEmptyMMergeUrl": "Precisa de especificar o URL.", "DE.Controllers.Toolbar.textFontSizeErr": "O valor inserido não está correto.<br>Introduza um valor numérico entre 1 e 300.", "DE.Controllers.Toolbar.textFraction": "Frações", "DE.Controllers.Toolbar.textFunction": "Funções", "DE.Controllers.Toolbar.textGroup": "Grupo", "DE.Controllers.Toolbar.textInsert": "Inserir", "DE.Controllers.Toolbar.textIntegral": "Inteiros", "DE.Controllers.Toolbar.textLargeOperator": "Grandes operadores", "DE.Controllers.Toolbar.textLimitAndLog": "Limites e logaritmos", "DE.Controllers.Toolbar.textMatrix": "Matrizes", "DE.Controllers.Toolbar.textOperator": "Operadores", "DE.Controllers.Toolbar.textRadical": "Radicais", "DE.Controllers.Toolbar.textRecentlyUsed": "Utilizado recentemente", "DE.Controllers.Toolbar.textScript": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.textSymbols": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.textTabForms": "Formulários", "DE.Controllers.Toolbar.textWarning": "Aviso", "DE.Controllers.Toolbar.txtAccent_Accent": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtAccent_ArrowD": "Seta para direita-esquerda acima", "DE.Controllers.Toolbar.txtAccent_ArrowL": "Seta adiante para cima", "DE.Controllers.Toolbar.txtAccent_ArrowR": "Seta para direita acima", "DE.Controllers.Toolbar.txtAccent_Bar": "Barr<PERSON>", "DE.Controllers.Toolbar.txtAccent_BarBot": "Barra inferior", "DE.Controllers.Toolbar.txtAccent_BarTop": "Barra superior", "DE.Controllers.Toolbar.txtAccent_BorderBox": "<PERSON><PERSON><PERSON><PERSON> embal<PERSON> (com Placeholder)", "DE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "F<PERSON>rmula embalada(Exemplo)", "DE.Controllers.Toolbar.txtAccent_Check": "Verificar", "DE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Chave <PERSON>", "DE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Chave Superior", "DE.Controllers.Toolbar.txtAccent_Custom_1": "Vetor A", "DE.Controllers.Toolbar.txtAccent_Custom_2": "ABC com barra superior ", "DE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y com barra superior", "DE.Controllers.Toolbar.txtAccent_DDDot": "Ponto <PERSON>lo", "DE.Controllers.Toolbar.txtAccent_DDot": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtAccent_Dot": "Ponto", "DE.Controllers.Toolbar.txtAccent_DoubleBar": "Barra superior dupla", "DE.Controllers.Toolbar.txtAccent_Grave": "Grave", "DE.Controllers.Toolbar.txtAccent_GroupBot": "Agrupamento de caracteres abaixo", "DE.Controllers.Toolbar.txtAccent_GroupTop": "Agrupamento de caracteres acima", "DE.Controllers.Toolbar.txtAccent_HarpoonL": "Arpão adiante para cima", "DE.Controllers.Toolbar.txtAccent_HarpoonR": "Arpão para direita acima", "DE.Controllers.Toolbar.txtAccent_Hat": "Acento circunflexo", "DE.Controllers.Toolbar.txtAccent_Smile": "Breve", "DE.Controllers.Toolbar.txtAccent_Tilde": "Til", "DE.Controllers.Toolbar.txtBracket_Angle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Parênteses com separadores", "DE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Parênteses com separadores", "DE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Colchete Simples", "DE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Colchete Simples", "DE.Controllers.Toolbar.txtBracket_Curve": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Parênteses com separadores", "DE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Colchete Simples", "DE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Colchete Simples", "DE.Controllers.Toolbar.txtBracket_Custom_1": "Casos (Duas Condições)", "DE.Controllers.Toolbar.txtBracket_Custom_2": "Casos (Três Condições)", "DE.Controllers.Toolbar.txtBracket_Custom_3": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Custom_4": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Custom_5": "Exemplo de casos", "DE.Controllers.Toolbar.txtBracket_Custom_6": "Coeficiente binominal", "DE.Controllers.Toolbar.txtBracket_Custom_7": "Coeficiente binominal", "DE.Controllers.Toolbar.txtBracket_Line": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Colchete Simples", "DE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Colchete Simples", "DE.Controllers.Toolbar.txtBracket_LineDouble": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Colchete Simples", "DE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Colchete Simples", "DE.Controllers.Toolbar.txtBracket_LowLim": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Colchete Simples", "DE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Colchete Simples", "DE.Controllers.Toolbar.txtBracket_Round": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Parênteses com separadores", "DE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Colchete Simples", "DE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Colchete Simples", "DE.Controllers.Toolbar.txtBracket_Square": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Square_CloseClose": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Colchete Simples", "DE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Colchete Simples", "DE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_SquareDouble": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Colchete Simples", "DE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Colchete Simples", "DE.Controllers.Toolbar.txtBracket_UppLim": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Colchete Simples", "DE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Colchete Simples", "DE.Controllers.Toolbar.txtFractionDiagonal": "Fração inclinada", "DE.Controllers.Toolbar.txtFractionDifferential_1": "Diferencial", "DE.Controllers.Toolbar.txtFractionDifferential_2": "Diferencial", "DE.Controllers.Toolbar.txtFractionDifferential_3": "Diferencial", "DE.Controllers.Toolbar.txtFractionDifferential_4": "Diferencial", "DE.Controllers.Toolbar.txtFractionHorizontal": "Fração linear", "DE.Controllers.Toolbar.txtFractionPi_2": "Pi sobre 2", "DE.Controllers.Toolbar.txtFractionSmall": "Fração pequena", "DE.Controllers.Toolbar.txtFractionVertical": "Fração Empilhada", "DE.Controllers.Toolbar.txtFunction_1_Cos": "Função cosseno inverso", "DE.Controllers.Toolbar.txtFunction_1_Cosh": "Função cosseno inverso hiperbólico", "DE.Controllers.Toolbar.txtFunction_1_Cot": "Função cotangente inversa", "DE.Controllers.Toolbar.txtFunction_1_Coth": "Função cotangente inversa hiperbólica", "DE.Controllers.Toolbar.txtFunction_1_Csc": "Função cossecante inversa", "DE.Controllers.Toolbar.txtFunction_1_Csch": "Função cossecante inversa hiperbólica", "DE.Controllers.Toolbar.txtFunction_1_Sec": "Função secante inversa", "DE.Controllers.Toolbar.txtFunction_1_Sech": "Função secante inversa hiperbólica", "DE.Controllers.Toolbar.txtFunction_1_Sin": "Função seno inverso", "DE.Controllers.Toolbar.txtFunction_1_Sinh": "Função seno inverso hiperbólico", "DE.Controllers.Toolbar.txtFunction_1_Tan": "Função tangente inversa", "DE.Controllers.Toolbar.txtFunction_1_Tanh": "Função tangente inversa hiperbólica", "DE.Controllers.Toolbar.txtFunction_Cos": "Função cosseno", "DE.Controllers.Toolbar.txtFunction_Cosh": "Função cosseno hiperbólico", "DE.Controllers.Toolbar.txtFunction_Cot": "Função cotangente", "DE.Controllers.Toolbar.txtFunction_Coth": "Função cotangente hiperbólica", "DE.Controllers.Toolbar.txtFunction_Csc": "Função cossecante", "DE.Controllers.Toolbar.txtFunction_Csch": "Função co-secante hiperbólica", "DE.Controllers.Toolbar.txtFunction_Custom_1": "<PERSON>ta seno", "DE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "DE.Controllers.Toolbar.txtFunction_Custom_3": "<PERSON><PERSON><PERSON><PERSON> da tangente", "DE.Controllers.Toolbar.txtFunction_Sec": "Função secante", "DE.Controllers.Toolbar.txtFunction_Sech": "Função secante hiperbólica", "DE.Controllers.Toolbar.txtFunction_Sin": "Função de seno", "DE.Controllers.Toolbar.txtFunction_Sinh": "Função seno hiperbólico", "DE.Controllers.Toolbar.txtFunction_Tan": "Função da tangente", "DE.Controllers.Toolbar.txtFunction_Tanh": "Função tangente hiperbólica", "DE.Controllers.Toolbar.txtIntegral": "Inteiro", "DE.Controllers.Toolbar.txtIntegral_dtheta": "Teta diferencial", "DE.Controllers.Toolbar.txtIntegral_dx": "Diferencial x", "DE.Controllers.Toolbar.txtIntegral_dy": "Diferencial y", "DE.Controllers.Toolbar.txtIntegralCenterSubSup": "Inteiro", "DE.Controllers.Toolbar.txtIntegralDouble": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtIntegralDoubleSubSup": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtIntegralOriented": "Contorno integral", "DE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Contorno integral", "DE.Controllers.Toolbar.txtIntegralOrientedDouble": "Integral de Superfície", "DE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Integral de Superfície", "DE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Integral de Superfície", "DE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Contorno integral", "DE.Controllers.Toolbar.txtIntegralOrientedTriple": "Integral de Volume", "DE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Integral de Volume", "DE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Integral de Volume", "DE.Controllers.Toolbar.txtIntegralSubSup": "Inteiro", "DE.Controllers.Toolbar.txtIntegralTriple": "Inteiro <PERSON>", "DE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Inteiro <PERSON>", "DE.Controllers.Toolbar.txtIntegralTripleSubSup": "Inteiro <PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Triangular", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Triangular", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Triangular", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Triangular", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Triangular", "DE.Controllers.Toolbar.txtLargeOperator_CoProd": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Custom_1": "So<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Custom_2": "So<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Custom_3": "So<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Produ<PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Custom_5": "União", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Intersection": "Interseção", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Interseção", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Interseção", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Interseção", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Interseção", "DE.Controllers.Toolbar.txtLargeOperator_Prod": "Produ<PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Produ<PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Produ<PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Produ<PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Produ<PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Sum": "So<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "So<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "So<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "So<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "So<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Union": "União", "DE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "União", "DE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "União", "DE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "União", "DE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "União", "DE.Controllers.Toolbar.txtLimitLog_Custom_1": "Exemplo limite", "DE.Controllers.Toolbar.txtLimitLog_Custom_2": "Exemplo máximo", "DE.Controllers.Toolbar.txtLimitLog_Lim": "Limite", "DE.Controllers.Toolbar.txtLimitLog_Ln": "Logaritmo natural", "DE.Controllers.Toolbar.txtLimitLog_Log": "Logaritmo", "DE.Controllers.Toolbar.txtLimitLog_LogBase": "Logaritmo", "DE.Controllers.Toolbar.txtLimitLog_Max": "Máximo", "DE.Controllers.Toolbar.txtLimitLog_Min": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtMarginsH": "As margens superior e inferior são muito grandes para a altura indicada", "DE.Controllers.Toolbar.txtMarginsW": "As margens direita e esquerda são muito grandes para a largura indicada", "DE.Controllers.Toolbar.txtMatrix_1_2": "Matriz vazia 1x2", "DE.Controllers.Toolbar.txtMatrix_1_3": "Matriz vazia 1x3", "DE.Controllers.Toolbar.txtMatrix_2_1": "Matriz vazia 2x1", "DE.Controllers.Toolbar.txtMatrix_2_2": "Matriz vazia 2x2", "DE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "Matriz vazia com parênteses", "DE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Matriz vazia com parênteses", "DE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "Matriz vazia com parênteses", "DE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "Matriz vazia com parênteses", "DE.Controllers.Toolbar.txtMatrix_2_3": "Matriz vazia 2x3", "DE.Controllers.Toolbar.txtMatrix_3_1": "Matriz vazia 3x1", "DE.Controllers.Toolbar.txtMatrix_3_2": "Matriz vazia 3x2", "DE.Controllers.Toolbar.txtMatrix_3_3": "Matriz vazia 3x3", "DE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Pontos da linha base", "DE.Controllers.Toolbar.txtMatrix_Dots_Center": "Pontos de linha média", "DE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Pontos diagonais", "DE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "Pontos verticais", "DE.Controllers.Toolbar.txtMatrix_Flat_Round": "<PERSON><PERSON> dispersa", "DE.Controllers.Toolbar.txtMatrix_Flat_Square": "<PERSON><PERSON> dispersa", "DE.Controllers.Toolbar.txtMatrix_Identity_2": "<PERSON>riz da identidade 2x2", "DE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "Matriz da identidade 3x3", "DE.Controllers.Toolbar.txtMatrix_Identity_3": "Matriz da identidade 3x3", "DE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "Matriz da identidade 3x3", "DE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Seta para direita esquerda abaixo", "DE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Seta para direita-esquerda acima", "DE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Seta à esquerda para baixo", "DE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Seta adiante para cima", "DE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Seta para direita abaixo", "DE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Seta para direita acima", "DE.Controllers.Toolbar.txtOperator_ColonEquals": "Dois-pontos-Sinal de Igual", "DE.Controllers.Toolbar.txtOperator_Custom_1": "Resul<PERSON><PERSON>", "DE.Controllers.Toolbar.txtOperator_Custom_2": "Resultados de Delta", "DE.Controllers.Toolbar.txtOperator_Definition": "Igual a por definição", "DE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta igual a", "DE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Seta para direita esquerda abaixo", "DE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Seta para direita-esquerda acima", "DE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Seta à esquerda para baixo", "DE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Seta adiante para cima", "DE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Seta para direita abaixo", "DE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Seta para direita acima", "DE.Controllers.Toolbar.txtOperator_EqualsEquals": "Sinal de Igual-Sinal de Igual", "DE.Controllers.Toolbar.txtOperator_MinusEquals": "Sinal de Menos-Sinal de Igual", "DE.Controllers.Toolbar.txtOperator_PlusEquals": "Sinal de Mais-Sinal de Igual", "DE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "Medido por", "DE.Controllers.Toolbar.txtRadicalCustom_1": "Radical", "DE.Controllers.Toolbar.txtRadicalCustom_2": "Radical", "DE.Controllers.Toolbar.txtRadicalRoot_2": "Raiz quadrada com grau", "DE.Controllers.Toolbar.txtRadicalRoot_3": "Raiz cúbica", "DE.Controllers.Toolbar.txtRadicalRoot_n": "Radical com grau", "DE.Controllers.Toolbar.txtRadicalSqrt": "Raiz quadrada", "DE.Controllers.Toolbar.txtScriptCustom_1": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtScriptCustom_2": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtScriptCustom_3": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtScriptCustom_4": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtScriptSub": "Subscrito", "DE.Controllers.Toolbar.txtScriptSubSup": "Subscrito-Sobrescrito", "DE.Controllers.Toolbar.txtScriptSubSupLeft": "Subscrito-Sobrescrito Esquerdo", "DE.Controllers.Toolbar.txtScriptSup": "Sobrescrito", "DE.Controllers.Toolbar.txtSymbol_about": "Aproximadamente", "DE.Controllers.Toolbar.txtSymbol_additional": "Complemento", "DE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "DE.Controllers.Toolbar.txtSymbol_alpha": "Alfa", "DE.Controllers.Toolbar.txtSymbol_approx": "Quase igual a", "DE.Controllers.Toolbar.txtSymbol_ast": "Operador de asterisco", "DE.Controllers.Toolbar.txtSymbol_beta": "Beta", "DE.Controllers.Toolbar.txtSymbol_beth": "Aposta", "DE.Controllers.Toolbar.txtSymbol_bullet": "Operador de marcador", "DE.Controllers.Toolbar.txtSymbol_cap": "Interseção", "DE.Controllers.Toolbar.txtSymbol_cbrt": "Raiz cúbica", "DE.Controllers.Toolbar.txtSymbol_cdots": "Reticências horizontais de linha média", "DE.Controllers.Toolbar.txtSymbol_celsius": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "DE.Controllers.Toolbar.txtSymbol_cong": "Aproximadamente igual a", "DE.Controllers.Toolbar.txtSymbol_cup": "União", "DE.Controllers.Toolbar.txtSymbol_ddots": "Reticências diagonal para baixo à direita", "DE.Controllers.Toolbar.txtSymbol_degree": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_delta": "Delta", "DE.Controllers.Toolbar.txtSymbol_div": "Sinal de divisão", "DE.Controllers.Toolbar.txtSymbol_downarrow": "Seta para baixo", "DE.Controllers.Toolbar.txtSymbol_emptyset": "Conjunto vazio", "DE.Controllers.Toolbar.txtSymbol_epsilon": "Epsílon", "DE.Controllers.Toolbar.txtSymbol_equals": "Igual", "DE.Controllers.Toolbar.txtSymbol_equiv": "<PERSON><PERSON><PERSON><PERSON><PERSON> a", "DE.Controllers.Toolbar.txtSymbol_eta": "Eta", "DE.Controllers.Toolbar.txtSymbol_exists": "Existe", "DE.Controllers.Toolbar.txtSymbol_factorial": "Fatorial", "DE.Controllers.Toolbar.txtSymbol_fahrenheit": "<PERSON><PERSON>us Fahrenheit", "DE.Controllers.Toolbar.txtSymbol_forall": "Para todos", "DE.Controllers.Toolbar.txtSymbol_gamma": "Gama", "DE.Controllers.Toolbar.txtSymbol_geq": "Superior a ou igual a", "DE.Controllers.Toolbar.txtSymbol_gg": "Muito superior a", "DE.Controllers.Toolbar.txtSymbol_greater": "Superior a", "DE.Controllers.Toolbar.txtSymbol_in": "Elemento de", "DE.Controllers.Toolbar.txtSymbol_inc": "Incremento", "DE.Controllers.Toolbar.txtSymbol_infinity": "Infinidade", "DE.Controllers.Toolbar.txtSymbol_iota": "Iota", "DE.Controllers.Toolbar.txtSymbol_kappa": "Capa", "DE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "DE.Controllers.Toolbar.txtSymbol_leftarrow": "Seta para esquerda", "DE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Seta esquerda-direita", "DE.Controllers.Toolbar.txtSymbol_leq": "Inferior a ou igual a", "DE.Controllers.Toolbar.txtSymbol_less": "Inferior a", "DE.Controllers.Toolbar.txtSymbol_ll": "Muito inferior a", "DE.Controllers.Toolbar.txtSymbol_minus": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_mp": "Sinal de Menos-Sinal de <PERSON>", "DE.Controllers.Toolbar.txtSymbol_mu": "Mu", "DE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "DE.Controllers.Toolbar.txtSymbol_neq": "Não igual a", "DE.Controllers.Toolbar.txtSymbol_ni": "Contém como membro", "DE.Controllers.Toolbar.txtSymbol_not": "Não entrar", "DE.Controllers.Toolbar.txtSymbol_notexists": "Não existe", "DE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "DE.Controllers.Toolbar.txtSymbol_o": "Omicron", "DE.Controllers.Toolbar.txtSymbol_omega": "Ômega", "DE.Controllers.Toolbar.txtSymbol_partial": "Diferencial parcial", "DE.Controllers.Toolbar.txtSymbol_percent": "Porcentagem", "DE.Controllers.Toolbar.txtSymbol_phi": "Fi", "DE.Controllers.Toolbar.txtSymbol_pi": "Pi", "DE.Controllers.Toolbar.txtSymbol_plus": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_pm": "Sinal de Menos-Sinal de Igual", "DE.Controllers.Toolbar.txtSymbol_propto": "Proporcional a", "DE.Controllers.Toolbar.txtSymbol_psi": "Psi", "DE.Controllers.Toolbar.txtSymbol_qdrt": "Quarta raiz", "DE.Controllers.Toolbar.txtSymbol_qed": "<PERSON><PERSON> da prova", "DE.Controllers.Toolbar.txtSymbol_rddots": "Reticências diagonal direitas para cima", "DE.Controllers.Toolbar.txtSymbol_rho": "Rô", "DE.Controllers.Toolbar.txtSymbol_rightarrow": "Seta para direita", "DE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "DE.Controllers.Toolbar.txtSymbol_sqrt": "Sinal de Radical", "DE.Controllers.Toolbar.txtSymbol_tau": "Tau", "DE.Controllers.Toolbar.txtSymbol_therefore": "Portanto", "DE.Controllers.Toolbar.txtSymbol_theta": "Teta", "DE.Controllers.Toolbar.txtSymbol_times": "Sinal de multiplicação", "DE.Controllers.Toolbar.txtSymbol_uparrow": "Seta para cima", "DE.Controllers.Toolbar.txtSymbol_upsilon": "Ípsilon", "DE.Controllers.Toolbar.txtSymbol_varepsilon": "<PERSON><PERSON><PERSON> de Epsílon", "DE.Controllers.Toolbar.txtSymbol_varphi": "Variante de fi", "DE.Controllers.Toolbar.txtSymbol_varpi": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_varrho": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_varsigma": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_vartheta": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_vdots": "Reticências verticais", "DE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "DE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "DE.Controllers.Viewport.textFitPage": "Ajustar à página", "DE.Controllers.Viewport.textFitWidth": "Ajustar à largura", "DE.Controllers.Viewport.txtDarkMode": "<PERSON><PERSON> es<PERSON>ro", "DE.Views.AddNewCaptionLabelDialog.textLabel": "Etiqueta:", "DE.Views.AddNewCaptionLabelDialog.textLabelError": "Etiqueta não deve estar em branco.", "DE.Views.BookmarksDialog.textAdd": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.BookmarksDialog.textBookmarkName": "Nome do marcador", "DE.Views.BookmarksDialog.textClose": "<PERSON><PERSON><PERSON>", "DE.Views.BookmarksDialog.textCopy": "Copiar", "DE.Views.BookmarksDialog.textDelete": "Eliminar", "DE.Views.BookmarksDialog.textGetLink": "Obter ligação", "DE.Views.BookmarksDialog.textGoto": "<PERSON>r <PERSON>", "DE.Views.BookmarksDialog.textHidden": "Favoritos ocultos", "DE.Views.BookmarksDialog.textLocation": "Localização", "DE.Views.BookmarksDialog.textName": "Nome", "DE.Views.BookmarksDialog.textSort": "Ordenar por", "DE.Views.BookmarksDialog.textTitle": "Marcadores", "DE.Views.BookmarksDialog.txtInvalidName": "Apenas pode utilizar letras, dígitos e sublinhado (_) para o nome do marcador e deve começar com uma letra", "DE.Views.CaptionDialog.textAdd": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.CaptionDialog.textAfter": "<PERSON><PERSON><PERSON>", "DE.Views.CaptionDialog.textBefore": "<PERSON><PERSON>", "DE.Views.CaptionDialog.textCaption": "<PERSON>a", "DE.Views.CaptionDialog.textChapter": "Capítulo inicia com estilo", "DE.Views.CaptionDialog.textChapterInc": "Inclui o número do capítulo", "DE.Views.CaptionDialog.textColon": "<PERSON><PERSON> pontos", "DE.Views.CaptionDialog.textDash": "traço", "DE.Views.CaptionDialog.textDelete": "Eliminar", "DE.Views.CaptionDialog.textEquation": "Equação", "DE.Views.CaptionDialog.textExamples": "Exemplos: Tabela 2-<PERSON>, Imagem 1.IV", "DE.Views.CaptionDialog.textExclude": "Eliminar r<PERSON><PERSON><PERSON> da <PERSON>a", "DE.Views.CaptionDialog.textFigure": "<PERSON><PERSON><PERSON>", "DE.Views.CaptionDialog.textHyphen": "hífen", "DE.Views.CaptionDialog.textInsert": "Inserir", "DE.Views.CaptionDialog.textLabel": "Etiqueta", "DE.Views.CaptionDialog.textLongDash": "traço longo", "DE.Views.CaptionDialog.textNumbering": "Numeração", "DE.Views.CaptionDialog.textPeriod": "<PERSON><PERSON><PERSON>", "DE.Views.CaptionDialog.textSeparator": "Use separador", "DE.Views.CaptionDialog.textTable": "<PERSON><PERSON><PERSON>", "DE.Views.CaptionDialog.textTitle": "<PERSON>ser<PERSON> legenda", "DE.Views.CellsAddDialog.textCol": "Colunas", "DE.Views.CellsAddDialog.textDown": "Abaixo do cursor", "DE.Views.CellsAddDialog.textLeft": "Para a esquerda", "DE.Views.CellsAddDialog.textRight": "Para a direita", "DE.Views.CellsAddDialog.textRow": "<PERSON><PERSON>", "DE.Views.CellsAddDialog.textTitle": "Insira vários", "DE.Views.CellsAddDialog.textUp": "Acima do cursor", "DE.Views.ChartSettings.text3dDepth": "Profundidade (% da base)", "DE.Views.ChartSettings.text3dHeight": "Altura (% da base)", "DE.Views.ChartSettings.text3dRotation": "Rotação 3D", "DE.Views.ChartSettings.textAdvanced": "Mostrar definições avançadas", "DE.Views.ChartSettings.textAutoscale": "Ajuste automático", "DE.Views.ChartSettings.textChartType": "Alterar tipo de gráfico", "DE.Views.ChartSettings.textDefault": "Rotação padrão", "DE.Views.ChartSettings.textDown": "Para baixo", "DE.Views.ChartSettings.textEditData": "<PERSON><PERSON> dad<PERSON>", "DE.Views.ChartSettings.textHeight": "Altura", "DE.Views.ChartSettings.textLeft": "E<PERSON>rda", "DE.Views.ChartSettings.textNarrow": "Campo de visão estreito", "DE.Views.ChartSettings.textOriginalSize": "Tamanho real", "DE.Views.ChartSettings.textPerspective": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ChartSettings.textRight": "<PERSON><PERSON><PERSON>", "DE.Views.ChartSettings.textRightAngle": "Eixos de ângulo reto", "DE.Views.ChartSettings.textSize": "<PERSON><PERSON><PERSON>", "DE.Views.ChartSettings.textStyle": "<PERSON><PERSON><PERSON>", "DE.Views.ChartSettings.textUndock": "Desafixar do painel", "DE.Views.ChartSettings.textUp": "Para cima", "DE.Views.ChartSettings.textWiden": "Ampliar o campo de visão", "DE.Views.ChartSettings.textWidth": "<PERSON><PERSON><PERSON>", "DE.Views.ChartSettings.textWrap": "Estilo de moldagem", "DE.Views.ChartSettings.textX": "Rotação X", "DE.Views.ChartSettings.textY": "Rotação Y", "DE.Views.ChartSettings.txtBehind": "Atrás do texto", "DE.Views.ChartSettings.txtInFront": "À frente do texto", "DE.Views.ChartSettings.txtInline": "Em Linha com o Texto", "DE.Views.ChartSettings.txtSquare": "Quadrado", "DE.Views.ChartSettings.txtThrough": "Através", "DE.Views.ChartSettings.txtTight": "<PERSON><PERSON>", "DE.Views.ChartSettings.txtTitle": "Gráfico", "DE.Views.ChartSettings.txtTopAndBottom": "Cima e baixo", "DE.Views.ControlSettingsDialog.strGeneral": "G<PERSON>", "DE.Views.ControlSettingsDialog.textAdd": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ControlSettingsDialog.textAppearance": "Aparência", "DE.Views.ControlSettingsDialog.textApplyAll": "Aplicar a todos", "DE.Views.ControlSettingsDialog.textBox": "Caixa delimitadora", "DE.Views.ControlSettingsDialog.textChange": "<PERSON><PERSON>", "DE.Views.ControlSettingsDialog.textCheckbox": "Caixa de seleção", "DE.Views.ControlSettingsDialog.textChecked": "<PERSON><PERSON><PERSON><PERSON> marcado", "DE.Views.ControlSettingsDialog.textColor": "Cor", "DE.Views.ControlSettingsDialog.textCombobox": "Caixa de combinação", "DE.Views.ControlSettingsDialog.textDate": "Formato da data", "DE.Views.ControlSettingsDialog.textDelete": "Eliminar", "DE.Views.ControlSettingsDialog.textDisplayName": "Nome de exibição", "DE.Views.ControlSettingsDialog.textDown": "Para baixo", "DE.Views.ControlSettingsDialog.textDropDown": "Lista suspensa", "DE.Views.ControlSettingsDialog.textFormat": "Mostra a data assim", "DE.Views.ControlSettingsDialog.textLang": "Idioma", "DE.Views.ControlSettingsDialog.textLock": "Bloqueio", "DE.Views.ControlSettingsDialog.textName": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ControlSettingsDialog.textNone": "<PERSON><PERSON><PERSON>", "DE.Views.ControlSettingsDialog.textPlaceholder": "Marcador de posição", "DE.Views.ControlSettingsDialog.textShowAs": "Mostrar como", "DE.Views.ControlSettingsDialog.textSystemColor": "Sistema", "DE.Views.ControlSettingsDialog.textTag": "Etiqueta", "DE.Views.ControlSettingsDialog.textTitle": "Definições de controlo de conteúdo", "DE.Views.ControlSettingsDialog.textUnchecked": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ControlSettingsDialog.textUp": "Para cima", "DE.Views.ControlSettingsDialog.textValue": "Valor", "DE.Views.ControlSettingsDialog.tipChange": "<PERSON><PERSON>r s<PERSON>", "DE.Views.ControlSettingsDialog.txtLockDelete": "Controlo de conteúdo não pode ser eliminado", "DE.Views.ControlSettingsDialog.txtLockEdit": "Não é possível editar o conteúdo", "DE.Views.CrossReferenceDialog.textAboveBelow": "Acima/abaixo", "DE.Views.CrossReferenceDialog.textBookmark": "Marcador", "DE.Views.CrossReferenceDialog.textBookmarkText": "Texto do marcador", "DE.Views.CrossReferenceDialog.textCaption": "<PERSON><PERSON>", "DE.Views.CrossReferenceDialog.textEmpty": "A referência do pedido está vazia.", "DE.Views.CrossReferenceDialog.textEndnote": "Nota final", "DE.Views.CrossReferenceDialog.textEndNoteNum": "Número de nota final", "DE.Views.CrossReferenceDialog.textEndNoteNumForm": "Número da nota final (formatado)", "DE.Views.CrossReferenceDialog.textEquation": "Equação", "DE.Views.CrossReferenceDialog.textFigure": "<PERSON><PERSON><PERSON>", "DE.Views.CrossReferenceDialog.textFootnote": "<PERSON><PERSON>", "DE.Views.CrossReferenceDialog.textHeading": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.CrossReferenceDialog.textHeadingNum": "Número do título", "DE.Views.CrossReferenceDialog.textHeadingNumFull": "Número do título (contexto total)", "DE.Views.CrossReferenceDialog.textHeadingNumNo": "Número do título (sem contexto)", "DE.Views.CrossReferenceDialog.textHeadingText": "Texto do título", "DE.Views.CrossReferenceDialog.textIncludeAbove": "Incluir acima/abaixo", "DE.Views.CrossReferenceDialog.textInsert": "Inserir", "DE.Views.CrossReferenceDialog.textInsertAs": "Inserir como hiperligação", "DE.Views.CrossReferenceDialog.textLabelNum": "Apenas etiqueta e número", "DE.Views.CrossReferenceDialog.textNoteNum": "Númer<PERSON> da nota de rodapé", "DE.Views.CrossReferenceDialog.textNoteNumForm": "Número da nota de rodapé (formatado)", "DE.Views.CrossReferenceDialog.textOnlyCaption": "Apenas texto de legenda", "DE.Views.CrossReferenceDialog.textPageNum": "Número de página", "DE.Views.CrossReferenceDialog.textParagraph": "Item numerado", "DE.Views.CrossReferenceDialog.textParaNum": "Número do parágrafo", "DE.Views.CrossReferenceDialog.textParaNumFull": "Número do parágrafo (contexto completo)", "DE.Views.CrossReferenceDialog.textParaNumNo": "Número do parágrafo (sem contexto)", "DE.Views.CrossReferenceDialog.textSeparate": "Separar números com", "DE.Views.CrossReferenceDialog.textTable": "<PERSON><PERSON><PERSON>", "DE.Views.CrossReferenceDialog.textText": "Texto do parágrafo", "DE.Views.CrossReferenceDialog.textWhich": "Para qual legenda", "DE.Views.CrossReferenceDialog.textWhichBookmark": "Para qual favorito", "DE.Views.CrossReferenceDialog.textWhichEndnote": "Para qual nota final", "DE.Views.CrossReferenceDialog.textWhichHeading": "Para que título", "DE.Views.CrossReferenceDialog.textWhichNote": "Para qual nota de rodapé", "DE.Views.CrossReferenceDialog.textWhichPara": "Para qual item numerado", "DE.Views.CrossReferenceDialog.txtReference": "Inserir referência a", "DE.Views.CrossReferenceDialog.txtTitle": "Referência-cruzada", "DE.Views.CrossReferenceDialog.txtType": "Tipo de Referência", "DE.Views.CustomColumnsDialog.textColumns": "Número de <PERSON>nas", "DE.Views.CustomColumnsDialog.textSeparator": "Separador de colunas", "DE.Views.CustomColumnsDialog.textSpacing": "Espaçamento entre colunas", "DE.Views.CustomColumnsDialog.textTitle": "Colunas", "DE.Views.DateTimeDialog.confirmDefault": "Definir formato predefinido para {0}: \"{1}\"", "DE.Views.DateTimeDialog.textDefault": "Definir como predefinido", "DE.Views.DateTimeDialog.textFormat": "Formatos", "DE.Views.DateTimeDialog.textLang": "Idioma", "DE.Views.DateTimeDialog.textUpdate": "Atualizar automaticamente", "DE.Views.DateTimeDialog.txtTitle": "Data e hora", "DE.Views.DocProtection.hintProtectDoc": "Proteger o documento", "DE.Views.DocProtection.txtDocProtectedComment": "O documento está protegido.<br>Apenas pode inserir comentários a este documento.", "DE.Views.DocProtection.txtDocProtectedForms": "O documento está protegido.<br><PERSON>penas pode preencher formulários neste documento.", "DE.Views.DocProtection.txtDocProtectedTrack": "O documento está protegido.<br>Pode editar este documento, mas todas as alterações serão rastreadas.", "DE.Views.DocProtection.txtDocProtectedView": "O documento está protegido.<br>Apenas pode ver este documento.", "DE.Views.DocProtection.txtDocUnlockDescription": "Introduzir uma palavra-passe para desproteger documento", "DE.Views.DocProtection.txtProtectDoc": "Proteger o documento", "DE.Views.DocumentHolder.aboveText": "Acima", "DE.Views.DocumentHolder.addCommentText": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.advancedDropCapText": "Definições de capitulares", "DE.Views.DocumentHolder.advancedEquationText": "Definições de equações", "DE.Views.DocumentHolder.advancedFrameText": "Definições avançadas de moldura", "DE.Views.DocumentHolder.advancedParagraphText": "Definições avançadas de parágrafo", "DE.Views.DocumentHolder.advancedTableText": "Definições avançadas de tabela", "DE.Views.DocumentHolder.advancedText": "Definições avançadas", "DE.Views.DocumentHolder.alignmentText": "Alinhamento", "DE.Views.DocumentHolder.allLinearText": "Tudo - Linear", "DE.Views.DocumentHolder.allProfText": "Tudo - Profissional", "DE.Views.DocumentHolder.belowText": "Abaixo", "DE.Views.DocumentHolder.breakBeforeText": "Quebra de página antes", "DE.Views.DocumentHolder.bulletsText": "Marcadores e numeração", "DE.Views.DocumentHolder.cellAlignText": "Alinhamento vertical da célula", "DE.Views.DocumentHolder.cellText": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.centerText": "Centro", "DE.Views.DocumentHolder.chartText": "Definições avançadas de gráfico", "DE.Views.DocumentHolder.columnText": "Coluna", "DE.Views.DocumentHolder.currLinearText": "Atual - Linear", "DE.Views.DocumentHolder.currProfText": "Atual - Profissional", "DE.Views.DocumentHolder.deleteColumnText": "Eliminar coluna", "DE.Views.DocumentHolder.deleteRowText": "Excluir linha", "DE.Views.DocumentHolder.deleteTableText": "Eliminar tabela", "DE.Views.DocumentHolder.deleteText": "Excluir", "DE.Views.DocumentHolder.direct270Text": "Rodar texto para cima", "DE.Views.DocumentHolder.direct90Text": "Rodar texto para baixo", "DE.Views.DocumentHolder.directHText": "Horizontal", "DE.Views.DocumentHolder.directionText": "Text Direction", "DE.Views.DocumentHolder.editChartText": "<PERSON><PERSON> dad<PERSON>", "DE.Views.DocumentHolder.editFooterText": "<PERSON><PERSON>", "DE.Views.DocumentHolder.editHeaderText": "<PERSON><PERSON>", "DE.Views.DocumentHolder.editHyperlinkText": "<PERSON><PERSON>", "DE.Views.DocumentHolder.eqToInlineText": "Mudar para Embutida", "DE.Views.DocumentHolder.guestText": "Visitante", "DE.Views.DocumentHolder.hyperlinkText": "Hiperligação", "DE.Views.DocumentHolder.ignoreAllSpellText": "<PERSON><PERSON><PERSON> tudo", "DE.Views.DocumentHolder.ignoreSpellText": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.imageText": "Definições avançadas de imagem", "DE.Views.DocumentHolder.insertColumnLeftText": "Coluna esquerda", "DE.Views.DocumentHolder.insertColumnRightText": "Coluna direita", "DE.Views.DocumentHolder.insertColumnText": "Inserir coluna", "DE.Views.DocumentHolder.insertRowAboveText": "<PERSON><PERSON>", "DE.Views.DocumentHolder.insertRowBelowText": "<PERSON><PERSON>", "DE.Views.DocumentHolder.insertRowText": "<PERSON><PERSON><PERSON> linha", "DE.Views.DocumentHolder.insertText": "Inserir", "DE.Views.DocumentHolder.keepLinesText": "Manter linhas juntas", "DE.Views.DocumentHolder.langText": "Selecionar idioma", "DE.Views.DocumentHolder.latexText": "LaTex", "DE.Views.DocumentHolder.leftText": "E<PERSON>rda", "DE.Views.DocumentHolder.loadSpellText": "A carregar variantes...", "DE.Views.DocumentHolder.mergeCellsText": "Mesclar célu<PERSON>", "DE.Views.DocumentHolder.moreText": "Mais variantes...", "DE.Views.DocumentHolder.noSpellVariantsText": "Sem varientes", "DE.Views.DocumentHolder.notcriticalErrorTitle": "Aviso", "DE.Views.DocumentHolder.originalSizeText": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.paragraphText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.removeHyperlinkText": "Remover hiperligação", "DE.Views.DocumentHolder.rightText": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.rowText": "<PERSON><PERSON>", "DE.Views.DocumentHolder.saveStyleText": "Criar novo estilo", "DE.Views.DocumentHolder.selectCellText": "Selecionar célula", "DE.Views.DocumentHolder.selectColumnText": "Selecionar coluna", "DE.Views.DocumentHolder.selectRowText": "Selecionar linha", "DE.Views.DocumentHolder.selectTableText": "Selecionar tabela", "DE.Views.DocumentHolder.selectText": "Selecionar", "DE.Views.DocumentHolder.shapeText": "Definições avançadas de forma", "DE.Views.DocumentHolder.spellcheckText": "Verificação ortográfica", "DE.Views.DocumentHolder.splitCellsText": "<PERSON><PERSON><PERSON>...", "DE.Views.DocumentHolder.splitCellTitleText": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.strDelete": "Remover assinatura", "DE.Views.DocumentHolder.strDetails": "Detalhes da assinatura", "DE.Views.DocumentHolder.strSetup": "Definições de Assinatura", "DE.Views.DocumentHolder.strSign": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.styleText": "Formatting as Style", "DE.Views.DocumentHolder.tableText": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textAccept": "Aceitar Alteração", "DE.Views.DocumentHolder.textAlign": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textArrange": "Dispor", "DE.Views.DocumentHolder.textArrangeBack": "Enviar para plano de fundo", "DE.Views.DocumentHolder.textArrangeBackward": "Mover para trás", "DE.Views.DocumentHolder.textArrangeForward": "Mover para frente", "DE.Views.DocumentHolder.textArrangeFront": "Trazer para primeiro plano", "DE.Views.DocumentHolder.textCells": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textCol": "Eliminar toda a coluna", "DE.Views.DocumentHolder.textContentControls": "Controlo de conteúdo", "DE.Views.DocumentHolder.textContinueNumbering": "Continuar numeração", "DE.Views.DocumentHolder.textCopy": "Copiar", "DE.Views.DocumentHolder.textCrop": "Recortar", "DE.Views.DocumentHolder.textCropFill": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textCropFit": "Ajustar", "DE.Views.DocumentHolder.textCut": "Cortar", "DE.Views.DocumentHolder.textDistributeCols": "Distribuir colunas", "DE.Views.DocumentHolder.textDistributeRows": "Distribuir linhas", "DE.Views.DocumentHolder.textEditControls": "Definições de controlo de conteúdo", "DE.Views.DocumentHolder.textEditPoints": "<PERSON><PERSON>", "DE.Views.DocumentHolder.textEditWrapBoundary": "Editar limites", "DE.Views.DocumentHolder.textFlipH": "Virar horizontalmente", "DE.Views.DocumentHolder.textFlipV": "Virar verticalmente", "DE.Views.DocumentHolder.textFollow": "<PERSON><PERSON><PERSON> movimento", "DE.Views.DocumentHolder.textFromFile": "De um ficheiro", "DE.Views.DocumentHolder.textFromStorage": "De um armazenamento", "DE.Views.DocumentHolder.textFromUrl": "De um URL", "DE.Views.DocumentHolder.textJoinList": "Juntar à lista anterior", "DE.Views.DocumentHolder.textLeft": "Deslocar células para a esquerda", "DE.Views.DocumentHolder.textNest": "<PERSON><PERSON><PERSON> ta<PERSON>a", "DE.Views.DocumentHolder.textNextPage": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textNumberingValue": "Valor de numeração", "DE.Views.DocumentHolder.textPaste": "Colar", "DE.Views.DocumentHolder.textPrevPage": "Página anterior", "DE.Views.DocumentHolder.textRefreshField": "Atualizar o campo", "DE.Views.DocumentHolder.textReject": "Rejeitar Alteração", "DE.Views.DocumentHolder.textRemCheckBox": "Remover caixa de seleção", "DE.Views.DocumentHolder.textRemComboBox": "Remover caixa de combinação", "DE.Views.DocumentHolder.textRemDropdown": "Remover lista suspensa", "DE.Views.DocumentHolder.textRemField": "Remover Campo de Texto", "DE.Views.DocumentHolder.textRemove": "Remover", "DE.Views.DocumentHolder.textRemoveControl": "Remover controlo de conteúdo", "DE.Views.DocumentHolder.textRemPicture": "Remover imagem", "DE.Views.DocumentHolder.textRemRadioBox": "Remover botão de seleção", "DE.Views.DocumentHolder.textReplace": "Substituir imagem", "DE.Views.DocumentHolder.textRotate": "<PERSON><PERSON>", "DE.Views.DocumentHolder.textRotate270": "Rodar 90º à esquerda", "DE.Views.DocumentHolder.textRotate90": "Rodar 90º à direita", "DE.Views.DocumentHolder.textRow": "Eliminar a linha inteira", "DE.Views.DocumentHolder.textSeparateList": "Lista distinta", "DE.Views.DocumentHolder.textSettings": "Definições", "DE.Views.DocumentHolder.textSeveral": "Diversas linhas/colunas", "DE.Views.DocumentHolder.textShapeAlignBottom": "<PERSON><PERSON><PERSON> em baixo", "DE.Views.DocumentHolder.textShapeAlignCenter": "Alinhar ao centro", "DE.Views.DocumentHolder.textShapeAlignLeft": "Alinhar à esquerda", "DE.Views.DocumentHolder.textShapeAlignMiddle": "Alinhar ao centro", "DE.Views.DocumentHolder.textShapeAlignRight": "Alinhar à direita", "DE.Views.DocumentHolder.textShapeAlignTop": "Alinhar em cima", "DE.Views.DocumentHolder.textStartNewList": "Iniciar nova lista", "DE.Views.DocumentHolder.textStartNumberingFrom": "Definir valor de numeração", "DE.Views.DocumentHolder.textTitleCellsRemove": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textTOC": "Índice remissivo", "DE.Views.DocumentHolder.textTOCSettings": "Definições do índice remissivo", "DE.Views.DocumentHolder.textUndo": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textUpdateAll": "<PERSON><PERSON><PERSON><PERSON> toda a tabela", "DE.Views.DocumentHolder.textUpdatePages": "Recarregar apenas o número de páginas", "DE.Views.DocumentHolder.textUpdateTOC": "Recarregar índice remissivo", "DE.Views.DocumentHolder.textWrap": "Estilo de moldagem", "DE.Views.DocumentHolder.tipIsLocked": "Este elemento está a ser editado por outro utilizador.", "DE.Views.DocumentHolder.toDictionaryText": "Adicionar ao dicionário", "DE.Views.DocumentHolder.txtAddBottom": "Adicionar contorno inferior", "DE.Views.DocumentHolder.txtAddFractionBar": "Adicionar barra de fração", "DE.Views.DocumentHolder.txtAddHor": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "DE.Views.DocumentHolder.txtAddLB": "Adicionar linha inferior esquerda", "DE.Views.DocumentHolder.txtAddLeft": "<PERSON><PERSON><PERSON><PERSON> con<PERSON> es<PERSON>", "DE.Views.DocumentHolder.txtAddLT": "Adicionar linha superior esquerda", "DE.Views.DocumentHolder.txtAddRight": "Adicionar contorno direito", "DE.Views.DocumentHolder.txtAddTop": "Adicionar contorno superior", "DE.Views.DocumentHolder.txtAddVer": "Adicionar lin<PERSON>", "DE.Views.DocumentHolder.txtAlignToChar": "<PERSON><PERSON><PERSON> ao car<PERSON>", "DE.Views.DocumentHolder.txtBehind": "Atrás do texto", "DE.Views.DocumentHolder.txtBorderProps": "Propriedades do contorno", "DE.Views.DocumentHolder.txtBottom": "Baixo", "DE.Views.DocumentHolder.txtColumnAlign": "Alinhamento de colunas", "DE.Views.DocumentHolder.txtDecreaseArg": "<PERSON><PERSON><PERSON><PERSON> tamanho do argumento", "DE.Views.DocumentHolder.txtDeleteArg": "Eliminar argumento", "DE.Views.DocumentHolder.txtDeleteBreak": "Eliminar quebra manual", "DE.Views.DocumentHolder.txtDeleteChars": "Eliminar caracteres anexos ", "DE.Views.DocumentHolder.txtDeleteCharsAndSeparators": "Eliminar separadores e caracteres anexos", "DE.Views.DocumentHolder.txtDeleteEq": "Eliminar equação", "DE.Views.DocumentHolder.txtDeleteGroupChar": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.txtDeleteRadical": "Eliminar radical", "DE.Views.DocumentHolder.txtDistribHor": "Distribuir horizontalmente", "DE.Views.DocumentHolder.txtDistribVert": "Distribuir verticalmente", "DE.Views.DocumentHolder.txtEmpty": "(Vazio)", "DE.Views.DocumentHolder.txtFractionLinear": "Alterar para fração linear", "DE.Views.DocumentHolder.txtFractionSkewed": "Alterar para fração inclinada", "DE.Views.DocumentHolder.txtFractionStacked": "Alterar para fração empilhada", "DE.Views.DocumentHolder.txtGroup": "Grupo", "DE.Views.DocumentHolder.txtGroupCharOver": "Caractere sobre texto", "DE.Views.DocumentHolder.txtGroupCharUnder": "Carácter sob texto", "DE.Views.DocumentHolder.txtHideBottom": "Ocultar contorno inferior", "DE.Views.DocumentHolder.txtHideBottomLimit": "Ocultar limite inferior", "DE.Views.DocumentHolder.txtHideCloseBracket": "Ocultar o parêntesis de fecho", "DE.Views.DocumentHolder.txtHideDegree": "Ocultar grau", "DE.Views.DocumentHolder.txtHideHor": "Ocultar linha horizontal", "DE.Views.DocumentHolder.txtHideLB": "Ocultar linha inferior esquerda", "DE.Views.DocumentHolder.txtHideLeft": "O<PERSON>ltar contorno es<PERSON>do", "DE.Views.DocumentHolder.txtHideLT": "Ocultar linha superior esquerda", "DE.Views.DocumentHolder.txtHideOpenBracket": "Ocultar parêntese de abertura", "DE.Views.DocumentHolder.txtHidePlaceholder": "Ocultar espaço reservado", "DE.Views.DocumentHolder.txtHideRight": "Ocultar contorno direito", "DE.Views.DocumentHolder.txtHideTop": "Ocultar contorno superior", "DE.Views.DocumentHolder.txtHideTopLimit": "Ocultar limite superior", "DE.Views.DocumentHolder.txtHideVer": "Ocultar linha vertical", "DE.Views.DocumentHolder.txtIncreaseArg": "Aumentar tamanho do argumento", "DE.Views.DocumentHolder.txtInFront": "À frente do texto", "DE.Views.DocumentHolder.txtInline": "Inline", "DE.Views.DocumentHolder.txtInsertArgAfter": "Inserir argumento após", "DE.Views.DocumentHolder.txtInsertArgBefore": "Inserir argumento antes", "DE.Views.DocumentHolder.txtInsertBreak": "Inserir quebra manual", "DE.Views.DocumentHolder.txtInsertCaption": "<PERSON>ser<PERSON> legenda", "DE.Views.DocumentHolder.txtInsertEqAfter": "Inserir equação a seguir", "DE.Views.DocumentHolder.txtInsertEqBefore": "Inserir equação à frente", "DE.Views.DocumentHolder.txtKeepTextOnly": "<PERSON><PERSON> apenas texto", "DE.Views.DocumentHolder.txtLimitChange": "Alterar localização de limites", "DE.Views.DocumentHolder.txtLimitOver": "Limite sobre o texto", "DE.Views.DocumentHolder.txtLimitUnder": "Limite sob o texto", "DE.Views.DocumentHolder.txtMatchBrackets": "Combinar parênteses com a altura do argumento", "DE.Views.DocumentHolder.txtMatrixAlign": "Alinhamento de matriz", "DE.Views.DocumentHolder.txtOverbar": "Barra por cima do texto", "DE.Views.DocumentHolder.txtOverwriteCells": "<PERSON>stit<PERSON><PERSON>", "DE.Views.DocumentHolder.txtPasteSourceFormat": "Manter formatação original", "DE.Views.DocumentHolder.txtPressLink": "Prima {0} e clique na ligação", "DE.Views.DocumentHolder.txtPrintSelection": "Imprimir <PERSON>", "DE.Views.DocumentHolder.txtRemFractionBar": "Remover barra de fração", "DE.Views.DocumentHolder.txtRemLimit": "Remover limite", "DE.Views.DocumentHolder.txtRemoveAccentChar": "Remover caractere destacado", "DE.Views.DocumentHolder.txtRemoveBar": "Remover barra", "DE.Views.DocumentHolder.txtRemoveWarning": "Quer remover esta assinatura?<br><PERSON><PERSON> não pode ser anulado.", "DE.Views.DocumentHolder.txtRemScripts": "Remover scripts", "DE.Views.DocumentHolder.txtRemSubscript": "Remover subscrito", "DE.Views.DocumentHolder.txtRemSuperscript": "Remover sobrescrito", "DE.Views.DocumentHolder.txtScriptsAfter": "Scripts após o texto", "DE.Views.DocumentHolder.txtScriptsBefore": "Scripts antes do texto", "DE.Views.DocumentHolder.txtShowBottomLimit": "Mostrar limite inferior", "DE.Views.DocumentHolder.txtShowCloseBracket": "Mostrar colchetes de fechamento", "DE.Views.DocumentHolder.txtShowDegree": "<PERSON><PERSON><PERSON> gra<PERSON>", "DE.Views.DocumentHolder.txtShowOpenBracket": "Mostrar chaveta de abertura", "DE.Views.DocumentHolder.txtShowPlaceholder": "Exibir espaço reservado", "DE.Views.DocumentHolder.txtShowTopLimit": "Mostrar limite superior", "DE.Views.DocumentHolder.txtSquare": "Quadrado", "DE.Views.DocumentHolder.txtStretchBrackets": "Esticar colchetes", "DE.Views.DocumentHolder.txtThrough": "Através", "DE.Views.DocumentHolder.txtTight": "<PERSON><PERSON>", "DE.Views.DocumentHolder.txtTop": "Cima", "DE.Views.DocumentHolder.txtTopAndBottom": "Cima e baixo", "DE.Views.DocumentHolder.txtUnderbar": "Barra por baixo do texto", "DE.Views.DocumentHolder.txtUngroup": "Desagrupar", "DE.Views.DocumentHolder.txtWarnUrl": "Clicar nesta ligação pode ser prejudicial ao seu dispositivo e dados.<br>Deseja continuar?", "DE.Views.DocumentHolder.unicodeText": "Unicode", "DE.Views.DocumentHolder.updateStyleText": "Atualizar estilo %1", "DE.Views.DocumentHolder.vertAlignText": "Alinhamento vertical", "DE.Views.DropcapSettingsAdvanced.strBorders": "Contornos e Preenchimento", "DE.Views.DropcapSettingsAdvanced.strDropcap": "Letra capitular", "DE.Views.DropcapSettingsAdvanced.strMargins": "Margens", "DE.Views.DropcapSettingsAdvanced.textAlign": "Alinhamento", "DE.Views.DropcapSettingsAdvanced.textAtLeast": "No mínimo", "DE.Views.DropcapSettingsAdvanced.textAuto": "Automático", "DE.Views.DropcapSettingsAdvanced.textBackColor": "Cor de fundo", "DE.Views.DropcapSettingsAdvanced.textBorderColor": "Cor do contorno", "DE.Views.DropcapSettingsAdvanced.textBorderDesc": "Clique no diagrama ou utilize os botões para selecionar o contorno", "DE.Views.DropcapSettingsAdvanced.textBorderWidth": "Tamanho do contorno", "DE.Views.DropcapSettingsAdvanced.textBottom": "Baixo", "DE.Views.DropcapSettingsAdvanced.textCenter": "Centro", "DE.Views.DropcapSettingsAdvanced.textColumn": "Coluna", "DE.Views.DropcapSettingsAdvanced.textDistance": "Distância do texto", "DE.Views.DropcapSettingsAdvanced.textExact": "Exatamente", "DE.Views.DropcapSettingsAdvanced.textFlow": "Estrutura de fluxo", "DE.Views.DropcapSettingsAdvanced.textFont": "<PERSON><PERSON><PERSON> de letra", "DE.Views.DropcapSettingsAdvanced.textFrame": "<PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textHeight": "Altura", "DE.Views.DropcapSettingsAdvanced.textHorizontal": "Horizontal", "DE.Views.DropcapSettingsAdvanced.textInline": "Moldura embutida", "DE.Views.DropcapSettingsAdvanced.textInMargin": "<PERSON> margem", "DE.Views.DropcapSettingsAdvanced.textInText": "No texto", "DE.Views.DropcapSettingsAdvanced.textLeft": "E<PERSON>rda", "DE.Views.DropcapSettingsAdvanced.textMargin": "Margem", "DE.Views.DropcapSettingsAdvanced.textMove": "Mover com texto", "DE.Views.DropcapSettingsAdvanced.textNone": "<PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textPage": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textParagraph": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textParameters": "Parâmetros", "DE.Views.DropcapSettingsAdvanced.textPosition": "Posição", "DE.Views.DropcapSettingsAdvanced.textRelative": "Relativo para", "DE.Views.DropcapSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textRowHeight": "Altura em linhas", "DE.Views.DropcapSettingsAdvanced.textTitle": "Capitulares - Definições avançadas", "DE.Views.DropcapSettingsAdvanced.textTitleFrame": "Moldura - Definições avançadas", "DE.Views.DropcapSettingsAdvanced.textTop": "Cima", "DE.Views.DropcapSettingsAdvanced.textVertical": "Vertical", "DE.Views.DropcapSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.tipFontName": "<PERSON><PERSON><PERSON> de letra", "DE.Views.DropcapSettingsAdvanced.txtNoBorders": "Sem contornos", "DE.Views.EditListItemDialog.textDisplayName": "Nome de exibição", "DE.Views.EditListItemDialog.textNameError": "O nome de exibição não deve estar vazio.", "DE.Views.EditListItemDialog.textValue": "Valor", "DE.Views.EditListItemDialog.textValueError": "Já existe um item com este mesmo valor.", "DE.Views.FileMenu.btnBackCaption": "Abrir localização", "DE.Views.FileMenu.btnCloseMenuCaption": "Fechar menu", "DE.Views.FileMenu.btnCreateNewCaption": "Criar novo", "DE.Views.FileMenu.btnDownloadCaption": "<PERSON><PERSON><PERSON><PERSON> como", "DE.Views.FileMenu.btnExitCaption": "<PERSON><PERSON>", "DE.Views.FileMenu.btnFileOpenCaption": "Abrir", "DE.Views.FileMenu.btnHelpCaption": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenu.btnHistoryCaption": "Hist<PERSON><PERSON><PERSON> <PERSON> ve<PERSON>ão", "DE.Views.FileMenu.btnInfoCaption": "Informações do documento", "DE.Views.FileMenu.btnPrintCaption": "Imprimir", "DE.Views.FileMenu.btnProtectCaption": "Proteger", "DE.Views.FileMenu.btnRecentFilesCaption": "Abrir recente", "DE.Views.FileMenu.btnRenameCaption": "<PERSON>dar nome", "DE.Views.FileMenu.btnReturnCaption": "Voltar para documento", "DE.Views.FileMenu.btnRightsCaption": "Direitos de acesso", "DE.Views.FileMenu.btnSaveAsCaption": "Guardar como", "DE.Views.FileMenu.btnSaveCaption": "Guardar", "DE.Views.FileMenu.btnSaveCopyAsCaption": "Guardar Cópia como", "DE.Views.FileMenu.btnSettingsCaption": "Definições avançadas", "DE.Views.FileMenu.btnToEditCaption": "Editar documento", "DE.Views.FileMenu.textDownload": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.CreateNew.txtBlank": "Documento em branco", "DE.Views.FileMenuPanels.CreateNew.txtCreateNew": "Criar novo", "DE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Aplicar", "DE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Adicionar autor", "DE.Views.FileMenuPanels.DocumentInfo.txtAddText": "Adicionar texto", "DE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Aplicação", "DE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "Autor", "DE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Alterar direitos de acesso", "DE.Views.FileMenuPanels.DocumentInfo.txtComment": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtCreated": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtFastWV": "Visualização rápida da Web", "DE.Views.FileMenuPanels.DocumentInfo.txtLoading": "A carregar...", "DE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Última modificação por", "DE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Última modificação", "DE.Views.FileMenuPanels.DocumentInfo.txtNo": "Não", "DE.Views.FileMenuPanels.DocumentInfo.txtOwner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtPages": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtPageSize": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtParagraphs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfProducer": "Produtor de <PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfTagged": "PDF marcado", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfVer": "Versão PDF", "DE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Localização", "DE.Views.FileMenuPanels.DocumentInfo.txtRights": "Pessoas que têm direitos", "DE.Views.FileMenuPanels.DocumentInfo.txtSpaces": "Carateres com espaços", "DE.Views.FileMenuPanels.DocumentInfo.txtStatistics": "Estatísticas", "DE.Views.FileMenuPanels.DocumentInfo.txtSubject": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtSymbols": "Carater<PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtTags": "Tags", "DE.Views.FileMenuPanels.DocumentInfo.txtTitle": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Carregado", "DE.Views.FileMenuPanels.DocumentInfo.txtWords": "Palavras", "DE.Views.FileMenuPanels.DocumentInfo.txtYes": "<PERSON>m", "DE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Alterar direitos de acesso", "DE.Views.FileMenuPanels.DocumentRights.txtRights": "Pessoas que têm direitos", "DE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Aviso", "DE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "Com palavra-passe", "DE.Views.FileMenuPanels.ProtectDoc.strProtect": "Proteger o documento", "DE.Views.FileMenuPanels.ProtectDoc.strSignature": "Com assinatura", "DE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Editar documento", "DE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "Se editar este documento, remove as assinaturas.<br>Tem a certeza de que deseja continuar?", "DE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "Este documento está protegido com uma palavra-passe", "DE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "Este documento precisa de ser assinado.", "DE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Assinaturas adicionadas ao documento. Este documento não pode ser editado.", "DE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Algumas das assinaturas digitais são inválidas ou não puderam ser verificadas. Este documento não pode ser editado.", "DE.Views.FileMenuPanels.ProtectDoc.txtView": "Ver assinaturas", "DE.Views.FileMenuPanels.Settings.okButtonText": "Aplicar", "DE.Views.FileMenuPanels.Settings.strCoAuthMode": "Modo de co-edição", "DE.Views.FileMenuPanels.Settings.strFast": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.strFontRender": "Dicas de tipo de letra", "DE.Views.FileMenuPanels.Settings.strIgnoreWordsInUPPERCASE": "Ignorar palavras em MAÍSCULAS", "DE.Views.FileMenuPanels.Settings.strIgnoreWordsWithNumbers": "Ignorar palavras com números", "DE.Views.FileMenuPanels.Settings.strMacrosSettings": "Definições de macros", "DE.Views.FileMenuPanels.Settings.strPasteButton": "Mostrar botão Opções de colagem ao colar conteúdo", "DE.Views.FileMenuPanels.Settings.strShowChanges": "Alterações de colaboração em tempo real", "DE.Views.FileMenuPanels.Settings.strShowComments": "Mostrar comentários no texto", "DE.Views.FileMenuPanels.Settings.strShowOthersChanges": "Mostrar alterações de outros utilizadores", "DE.Views.FileMenuPanels.Settings.strShowResolvedComments": "<PERSON>rar coment<PERSON>rios resolvidos", "DE.Views.FileMenuPanels.Settings.strStrict": "Estrito", "DE.Views.FileMenuPanels.Settings.strTheme": "<PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.strUnit": "Unidade de medida", "DE.Views.FileMenuPanels.Settings.strZoom": "Valor de zoom padrão", "DE.Views.FileMenuPanels.Settings.text10Minutes": "Cada 10 minutos", "DE.Views.FileMenuPanels.Settings.text30Minutes": "Cada 30 minutos", "DE.Views.FileMenuPanels.Settings.text5Minutes": "Cada 5 minutos", "DE.Views.FileMenuPanels.Settings.text60Minutes": "<PERSON><PERSON> hora", "DE.Views.FileMenuPanels.Settings.textAlignGuides": "<PERSON><PERSON><PERSON> <PERSON>", "DE.Views.FileMenuPanels.Settings.textAutoRecover": "Recuperação automática", "DE.Views.FileMenuPanels.Settings.textAutoSave": "Guardar automático", "DE.Views.FileMenuPanels.Settings.textDisabled": "Desativado", "DE.Views.FileMenuPanels.Settings.textForceSave": "Guardar versões intermédias", "DE.Views.FileMenuPanels.Settings.textMinute": "Cada minuto", "DE.Views.FileMenuPanels.Settings.textOldVersions": "Ao guardar como DOCX, tornar ficheiro compatível com versões antigas do MS Word", "DE.Views.FileMenuPanels.Settings.txtAll": "Ver tudo", "DE.Views.FileMenuPanels.Settings.txtAutoCorrect": "Opções de correção automática...", "DE.Views.FileMenuPanels.Settings.txtCacheMode": "Modo de cache predefinido", "DE.Views.FileMenuPanels.Settings.txtChangesBalloons": "Mostrar ao clicar nos balões", "DE.Views.FileMenuPanels.Settings.txtChangesTip": "Mostrar ao sobrepor o cursor nas descrições", "DE.Views.FileMenuPanels.Settings.txtCm": "Centímetro", "DE.Views.FileMenuPanels.Settings.txtCollaboration": "Colaboração", "DE.Views.FileMenuPanels.Settings.txtDarkMode": "Ligar o modo escuro do documento", "DE.Views.FileMenuPanels.Settings.txtEditingSaving": "Editar e Guardar", "DE.Views.FileMenuPanels.Settings.txtFastTip": "Co-edição em tempo real. Todas as alterações são salvas automaticamente", "DE.Views.FileMenuPanels.Settings.txtFitPage": "Ajustar à página", "DE.Views.FileMenuPanels.Settings.txtFitWidth": "Ajustar à largura", "DE.Views.FileMenuPanels.Settings.txtHieroglyphs": "Hierogli<PERSON>s", "DE.Views.FileMenuPanels.Settings.txtInch": "Polegada", "DE.Views.FileMenuPanels.Settings.txtLast": "Ver último", "DE.Views.FileMenuPanels.Settings.txtMac": "como OS X", "DE.Views.FileMenuPanels.Settings.txtNative": "Nativo", "DE.Views.FileMenuPanels.Settings.txtNone": "<PERSON><PERSON>nhum", "DE.Views.FileMenuPanels.Settings.txtProofing": "Correção", "DE.Views.FileMenuPanels.Settings.txtPt": "Ponto", "DE.Views.FileMenuPanels.Settings.txtRunMacros": "Ativar tudo", "DE.Views.FileMenuPanels.Settings.txtRunMacrosDesc": "<PERSON><PERSON><PERSON> todas as macros sem uma notificação", "DE.Views.FileMenuPanels.Settings.txtShowTrackChanges": "Mostrar registo de alterações", "DE.Views.FileMenuPanels.Settings.txtSpellCheck": "Verificação ortográfica", "DE.Views.FileMenuPanels.Settings.txtStopMacros": "Desativar tudo", "DE.Views.FileMenuPanels.Settings.txtStopMacrosDesc": "<PERSON><PERSON><PERSON> to<PERSON> as macros sem uma notificação", "DE.Views.FileMenuPanels.Settings.txtStrictTip": "Utilize o bot<PERSON> \"Guardar\" para sincronizar as alterações efetuadas", "DE.Views.FileMenuPanels.Settings.txtUseAltKey": "Utilize a tecla 'Alt' para navegar na interface através do teclado", "DE.Views.FileMenuPanels.Settings.txtUseOptionKey": "Utilize a tecla 'Opção' para navegar na interface através do teclado", "DE.Views.FileMenuPanels.Settings.txtWarnMacros": "Mostrar notificação", "DE.Views.FileMenuPanels.Settings.txtWarnMacrosDesc": "<PERSON><PERSON><PERSON> todas as macros com uma notificação", "DE.Views.FileMenuPanels.Settings.txtWin": "como Windows", "DE.Views.FileMenuPanels.Settings.txtWorkspace": "<PERSON><PERSON>", "DE.Views.FormSettings.textAlways": "Sempre", "DE.Views.FormSettings.textAnyone": "Alguém", "DE.Views.FormSettings.textAspect": "Bloquear proporção", "DE.Views.FormSettings.textAtLeast": "<PERSON><PERSON>", "DE.Views.FormSettings.textAuto": "Automático", "DE.Views.FormSettings.textAutofit": "Ajuste automático", "DE.Views.FormSettings.textBackgroundColor": "Cor de fundo", "DE.Views.FormSettings.textCheckbox": "Caixa de seleção", "DE.Views.FormSettings.textColor": "Cor do contorno", "DE.Views.FormSettings.textComb": "Conjunto de caracteres", "DE.Views.FormSettings.textCombobox": "Caixa de combinação", "DE.Views.FormSettings.textComplex": "Campo complexo", "DE.Views.FormSettings.textConnected": "Campos ligados", "DE.Views.FormSettings.textDelete": "Eliminar", "DE.Views.FormSettings.textDigits": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.FormSettings.textDisconnect": "Desconectar", "DE.Views.FormSettings.textDropDown": "Suspenso", "DE.Views.FormSettings.textExact": "Exatamente", "DE.Views.FormSettings.textField": "Campo de texto", "DE.Views.FormSettings.textFixed": "Campo de tamanho fixo", "DE.Views.FormSettings.textFormat": "Formato", "DE.Views.FormSettings.textFormatSymbols": "Símbolos permitidos", "DE.Views.FormSettings.textFromFile": "Do fi<PERSON>iro", "DE.Views.FormSettings.textFromStorage": "Do armazenamento", "DE.Views.FormSettings.textFromUrl": "De um URL", "DE.Views.FormSettings.textGroupKey": "Agrupar chave", "DE.Views.FormSettings.textImage": "Imagem", "DE.Views.FormSettings.textKey": "Chave", "DE.Views.FormSettings.textLetters": "Letras", "DE.Views.FormSettings.textLock": "Bloquear", "DE.Views.FormSettings.textMask": "Máscara arbitrária", "DE.Views.FormSettings.textMaxChars": "Limite de caracteres", "DE.Views.FormSettings.textMulti": "Campo com Múltiplas <PERSON>", "DE.Views.FormSettings.textNever": "Nunca", "DE.Views.FormSettings.textNoBorder": "Sem contorno", "DE.Views.FormSettings.textNone": "<PERSON><PERSON><PERSON>", "DE.Views.FormSettings.textPlaceholder": "Marcador de posição", "DE.Views.FormSettings.textRadiobox": "Botão Seleção", "DE.Views.FormSettings.textReg": "Expressão regular", "DE.Views.FormSettings.textRequired": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.FormSettings.textScale": "Quando escalar", "DE.Views.FormSettings.textSelectImage": "Selecionar imagem", "DE.Views.FormSettings.textTag": "Etiqueta", "DE.Views.FormSettings.textTip": "Dica", "DE.Views.FormSettings.textTipAdd": "Adicionar novo valor", "DE.Views.FormSettings.textTipDelete": "Eliminar valor", "DE.Views.FormSettings.textTipDown": "Mover para baixo", "DE.Views.FormSettings.textTipUp": "Mover para cima", "DE.Views.FormSettings.textTooBig": "A imagem é demasiado grande", "DE.Views.FormSettings.textTooSmall": "A imagem é demasiado pequena", "DE.Views.FormSettings.textUnlock": "Desb<PERSON>que<PERSON>", "DE.Views.FormSettings.textValue": "Opções de valor", "DE.Views.FormSettings.textWidth": "<PERSON><PERSON><PERSON> da célula", "DE.Views.FormsTab.capBtnCheckBox": "Caixa de seleção", "DE.Views.FormsTab.capBtnComboBox": "Caixa de combinação", "DE.Views.FormsTab.capBtnComplex": "Campo complexo", "DE.Views.FormsTab.capBtnDownloadForm": "Descarregar como OFORM", "DE.Views.FormsTab.capBtnDropDown": "Suspenso", "DE.Views.FormsTab.capBtnEmail": "Endereço de e-mail", "DE.Views.FormsTab.capBtnImage": "Imagem", "DE.Views.FormsTab.capBtnNext": "Campo <PERSON>te", "DE.Views.FormsTab.capBtnPhone": "Número de telefone", "DE.Views.FormsTab.capBtnPrev": "Campo anterior", "DE.Views.FormsTab.capBtnRadioBox": "Botão Seleção", "DE.Views.FormsTab.capBtnSaveForm": "Guardar como oform", "DE.Views.FormsTab.capBtnSubmit": "Submeter", "DE.Views.FormsTab.capBtnText": "Campo de texto", "DE.Views.FormsTab.capBtnView": "<PERSON>er formul<PERSON>", "DE.Views.FormsTab.textAnyone": "Alguém", "DE.Views.FormsTab.textClear": "Limpar camp<PERSON>", "DE.Views.FormsTab.textClearFields": "Limpar todos os campos", "DE.Views.FormsTab.textCreateForm": "Adicione campos e crie um documento FORM preenchível", "DE.Views.FormsTab.textGotIt": "<PERSON><PERSON><PERSON>", "DE.Views.FormsTab.textHighlight": "Definições de destaque", "DE.Views.FormsTab.textNoHighlight": "Sem realce", "DE.Views.FormsTab.textRequired": "Preencha todos os campos obrigatórios para enviar o formulário.", "DE.Views.FormsTab.textSubmited": "Formulário enviado com sucesso", "DE.Views.FormsTab.tipCheckBox": "Inserir caixa de seleção", "DE.Views.FormsTab.tipComboBox": "Inserir caixa de combinação", "DE.Views.FormsTab.tipComplexField": "Inserir campo complexo", "DE.Views.FormsTab.tipDownloadForm": "Descarregar um ficheiro no formato OFORM", "DE.Views.FormsTab.tipDropDown": "Inserir lista suspensa", "DE.Views.FormsTab.tipEmailField": "Inserir endereço eletrónico", "DE.Views.FormsTab.tipImageField": "Inserir imagem", "DE.Views.FormsTab.tipNextForm": "Ir para o campo seguinte", "DE.Views.FormsTab.tipPhoneField": "Inserir número de telefone", "DE.Views.FormsTab.tipPrevForm": "Ir para o campo anterior", "DE.Views.FormsTab.tipRadioBox": "Inserir botão de seleção", "DE.Views.FormsTab.tipSaveForm": "Guardar um ficheiro como um documento OFORM preenchível", "DE.Views.FormsTab.tipSubmit": "Submeter forma", "DE.Views.FormsTab.tipTextField": "Inserir campo de texto", "DE.Views.FormsTab.tipViewForm": "<PERSON>er formul<PERSON>", "DE.Views.FormsTab.txtUntitled": "<PERSON><PERSON> tí<PERSON>lo", "DE.Views.HeaderFooterSettings.textBottomCenter": "Centro inferior", "DE.Views.HeaderFooterSettings.textBottomLeft": "Esquerda inferior", "DE.Views.HeaderFooterSettings.textBottomPage": "Fundo da página", "DE.Views.HeaderFooterSettings.textBottomRight": "Direita inferior", "DE.Views.HeaderFooterSettings.textDiffFirst": "Primeira página diferente", "DE.Views.HeaderFooterSettings.textDiffOdd": "Páginas pares e ímpares diferentes", "DE.Views.HeaderFooterSettings.textFrom": "Iniciar em", "DE.Views.HeaderFooterSettings.textHeaderFromBottom": "<PERSON><PERSON><PERSON>", "DE.Views.HeaderFooterSettings.textHeaderFromTop": "Cabeçalho em cima", "DE.Views.HeaderFooterSettings.textInsertCurrent": "Inserir na posição atual ", "DE.Views.HeaderFooterSettings.textOptions": "Opções", "DE.Views.HeaderFooterSettings.textPageNum": "Inserir número da página", "DE.Views.HeaderFooterSettings.textPageNumbering": "Numeração de páginas", "DE.Views.HeaderFooterSettings.textPosition": "Posição", "DE.Views.HeaderFooterSettings.textPrev": "Continuar da secção anterior", "DE.Views.HeaderFooterSettings.textSameAs": "Ligação à anterior", "DE.Views.HeaderFooterSettings.textTopCenter": "Superior central", "DE.Views.HeaderFooterSettings.textTopLeft": "Superior esquerdo", "DE.Views.HeaderFooterSettings.textTopPage": "Topo da página", "DE.Views.HeaderFooterSettings.textTopRight": "Superior direito", "DE.Views.HyperlinkSettingsDialog.textDefault": "Fragmento de texto selecionado", "DE.Views.HyperlinkSettingsDialog.textDisplay": "<PERSON><PERSON><PERSON>", "DE.Views.HyperlinkSettingsDialog.textExternal": "Ligação externa", "DE.Views.HyperlinkSettingsDialog.textInternal": "Colocar no Documento", "DE.Views.HyperlinkSettingsDialog.textTitle": "Definições de hiperligação", "DE.Views.HyperlinkSettingsDialog.textTooltip": "Texto de dica de tela:", "DE.Views.HyperlinkSettingsDialog.textUrl": "Ligação a", "DE.Views.HyperlinkSettingsDialog.txtBeginning": "Início do documento", "DE.Views.HyperlinkSettingsDialog.txtBookmarks": "Marcadores", "DE.Views.HyperlinkSettingsDialog.txtEmpty": "Este campo é obrigatório", "DE.Views.HyperlinkSettingsDialog.txtHeadings": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.HyperlinkSettingsDialog.txtNotUrl": "Este campo deve ser uma URL no formato \"http://www.example.com\"", "DE.Views.HyperlinkSettingsDialog.txtSizeLimit": "Este campo está limitado a 2083 caracteres", "DE.Views.ImageSettings.textAdvanced": "Mostrar definições avançadas", "DE.Views.ImageSettings.textCrop": "Recortar", "DE.Views.ImageSettings.textCropFill": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettings.textCropFit": "Ajustar", "DE.Views.ImageSettings.textCropToShape": "Recortar com Forma", "DE.Views.ImageSettings.textEdit": "<PERSON><PERSON>", "DE.Views.ImageSettings.textEditObject": "<PERSON>ar objeto", "DE.Views.ImageSettings.textFitMargins": "Ajustar à margem", "DE.Views.ImageSettings.textFlip": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettings.textFromFile": "De um ficheiro", "DE.Views.ImageSettings.textFromStorage": "De um armazenamento", "DE.Views.ImageSettings.textFromUrl": "De um URL", "DE.Views.ImageSettings.textHeight": "Altura", "DE.Views.ImageSettings.textHint270": "Rodar 90º à esquerda", "DE.Views.ImageSettings.textHint90": "Rodar 90º à direita", "DE.Views.ImageSettings.textHintFlipH": "Virar horizontalmente", "DE.Views.ImageSettings.textHintFlipV": "Virar verticalmente", "DE.Views.ImageSettings.textInsert": "Substituir imagem", "DE.Views.ImageSettings.textOriginalSize": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettings.textRecentlyUsed": "Utilizado recentemente", "DE.Views.ImageSettings.textRotate90": "Rodar 90°", "DE.Views.ImageSettings.textRotation": "Rotação", "DE.Views.ImageSettings.textSize": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettings.textWidth": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettings.textWrap": "Estilo de moldagem", "DE.Views.ImageSettings.txtBehind": "Atrás do texto", "DE.Views.ImageSettings.txtInFront": "À frente do texto", "DE.Views.ImageSettings.txtInline": "Em Linha com o Texto", "DE.Views.ImageSettings.txtSquare": "Quadrado", "DE.Views.ImageSettings.txtThrough": "Através", "DE.Views.ImageSettings.txtTight": "<PERSON><PERSON>", "DE.Views.ImageSettings.txtTopAndBottom": "Cima e baixo", "DE.Views.ImageSettingsAdvanced.strMargins": "Preenchimento de texto", "DE.Views.ImageSettingsAdvanced.textAbsoluteWH": "Absoluto", "DE.Views.ImageSettingsAdvanced.textAlignment": "Alinhamento", "DE.Views.ImageSettingsAdvanced.textAlt": "Texto alternativo", "DE.Views.ImageSettingsAdvanced.textAltDescription": "Descrição", "DE.Views.ImageSettingsAdvanced.textAltTip": "A representação alternativa baseada em texto da informação visual do objeto, que será lida para as pessoas com deficiências visuais ou cognitivas para ajudá-las a entender melhor que informação, forma automática, gráfico ou tabela existe na imagem.", "DE.Views.ImageSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textAngle": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textArrows": "Set<PERSON>", "DE.Views.ImageSettingsAdvanced.textAspectRatio": "Bloquear proporção", "DE.Views.ImageSettingsAdvanced.textAutofit": "Ajuste automático", "DE.Views.ImageSettingsAdvanced.textBeginSize": "<PERSON><PERSON><PERSON> inicial", "DE.Views.ImageSettingsAdvanced.textBeginStyle": "<PERSON><PERSON><PERSON> inici<PERSON>", "DE.Views.ImageSettingsAdvanced.textBelow": "abaixo", "DE.Views.ImageSettingsAdvanced.textBevel": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textBottom": "Baixo", "DE.Views.ImageSettingsAdvanced.textBottomMargin": "Margem inferior", "DE.Views.ImageSettingsAdvanced.textBtnWrap": "<PERSON><PERSON><PERSON> texto", "DE.Views.ImageSettingsAdvanced.textCapType": "<PERSON><PERSON><PERSON> de letra", "DE.Views.ImageSettingsAdvanced.textCenter": "Centro", "DE.Views.ImageSettingsAdvanced.textCharacter": "Caractere", "DE.Views.ImageSettingsAdvanced.textColumn": "Coluna", "DE.Views.ImageSettingsAdvanced.textDistance": "Distância do texto", "DE.Views.ImageSettingsAdvanced.textEndSize": "Tamanho final", "DE.Views.ImageSettingsAdvanced.textEndStyle": "Estilo final", "DE.Views.ImageSettingsAdvanced.textFlat": "Plano", "DE.Views.ImageSettingsAdvanced.textFlipped": "Invertido", "DE.Views.ImageSettingsAdvanced.textHeight": "Altura", "DE.Views.ImageSettingsAdvanced.textHorizontal": "Horizontal", "DE.Views.ImageSettingsAdvanced.textHorizontally": "Horizontalmente", "DE.Views.ImageSettingsAdvanced.textJoinType": "Tipo de junção", "DE.Views.ImageSettingsAdvanced.textKeepRatio": "Proporções constantes", "DE.Views.ImageSettingsAdvanced.textLeft": "E<PERSON>rda", "DE.Views.ImageSettingsAdvanced.textLeftMargin": "Mar<PERSON>m es<PERSON>", "DE.Views.ImageSettingsAdvanced.textLine": "<PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textLineStyle": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textMargin": "Margem", "DE.Views.ImageSettingsAdvanced.textMiter": "Malhete", "DE.Views.ImageSettingsAdvanced.textMove": "Mover objeto com texto", "DE.Views.ImageSettingsAdvanced.textOptions": "Opções", "DE.Views.ImageSettingsAdvanced.textOriginalSize": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textOverlap": "<PERSON><PERSON><PERSON> sobrepo<PERSON>", "DE.Views.ImageSettingsAdvanced.textPage": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textParagraph": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textPosition": "Posição", "DE.Views.ImageSettingsAdvanced.textPositionPc": "Posição relativa", "DE.Views.ImageSettingsAdvanced.textRelative": "relativo para", "DE.Views.ImageSettingsAdvanced.textRelativeWH": "Relativo", "DE.Views.ImageSettingsAdvanced.textResizeFit": "Redimensionar forma para se ajustar ao texto", "DE.Views.ImageSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textRightMargin": "Margem direita", "DE.Views.ImageSettingsAdvanced.textRightOf": "para a direita de", "DE.Views.ImageSettingsAdvanced.textRotation": "Rotação", "DE.Views.ImageSettingsAdvanced.textRound": "Rodada", "DE.Views.ImageSettingsAdvanced.textShape": "Definições de forma", "DE.Views.ImageSettingsAdvanced.textSize": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textSquare": "Quadrado", "DE.Views.ImageSettingsAdvanced.textTextBox": "Caixa de texto", "DE.Views.ImageSettingsAdvanced.textTitle": "Imagem - Definições avançadas", "DE.Views.ImageSettingsAdvanced.textTitleChart": "Gráfico - Definições avançadas", "DE.Views.ImageSettingsAdvanced.textTitleShape": "Forma - Definições avançadas", "DE.Views.ImageSettingsAdvanced.textTop": "Cima", "DE.Views.ImageSettingsAdvanced.textTopMargin": "Margem superior", "DE.Views.ImageSettingsAdvanced.textVertical": "Vertical", "DE.Views.ImageSettingsAdvanced.textVertically": "Verticalmente", "DE.Views.ImageSettingsAdvanced.textWeightArrows": "Pesos e Setas", "DE.Views.ImageSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textWrap": "Estilo de moldagem", "DE.Views.ImageSettingsAdvanced.textWrapBehindTooltip": "Atrás do texto", "DE.Views.ImageSettingsAdvanced.textWrapInFrontTooltip": "À frente do texto", "DE.Views.ImageSettingsAdvanced.textWrapInlineTooltip": "Em Linha com o Texto", "DE.Views.ImageSettingsAdvanced.textWrapSquareTooltip": "Quadrado", "DE.Views.ImageSettingsAdvanced.textWrapThroughTooltip": "Através", "DE.Views.ImageSettingsAdvanced.textWrapTightTooltip": "<PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textWrapTopbottomTooltip": "Cima e baixo", "DE.Views.LeftMenu.tipAbout": "Acerca", "DE.Views.LeftMenu.tipChat": "Cha<PERSON>", "DE.Views.LeftMenu.tipComments": "Comentários", "DE.Views.LeftMenu.tipNavigation": "Navegação", "DE.Views.LeftMenu.tipOutline": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.LeftMenu.tipPageThumbnails": "Miniaturas de páginas", "DE.Views.LeftMenu.tipPlugins": "Plugins", "DE.Views.LeftMenu.tipSearch": "Pesquisa", "DE.Views.LeftMenu.tipSupport": "Feedback e Suporte", "DE.Views.LeftMenu.tipTitles": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.LeftMenu.txtDeveloper": "MODO DE PROGRAMADOR", "DE.Views.LeftMenu.txtEditor": "Editor de Documentos", "DE.Views.LeftMenu.txtLimit": "Limitar o acesso", "DE.Views.LeftMenu.txtTrial": "MODO DE TESTE", "DE.Views.LeftMenu.txtTrialDev": "Versão de Avaliação do Modo de Programador", "DE.Views.LineNumbersDialog.textAddLineNumbering": "Adicionar numeração de linhas", "DE.Views.LineNumbersDialog.textApplyTo": "Aplicar alterações a", "DE.Views.LineNumbersDialog.textContinuous": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.LineNumbersDialog.textCountBy": "Contar por", "DE.Views.LineNumbersDialog.textDocument": "Documento inteiro", "DE.Views.LineNumbersDialog.textForward": "A partir deste ponto", "DE.Views.LineNumbersDialog.textFromText": "De um texto", "DE.Views.LineNumbersDialog.textNumbering": "Numeração", "DE.Views.LineNumbersDialog.textRestartEachPage": "Reiniciar cada página", "DE.Views.LineNumbersDialog.textRestartEachSection": "Reiniciar Em Cada Secção", "DE.Views.LineNumbersDialog.textSection": "Se<PERSON>ção atual", "DE.Views.LineNumbersDialog.textStartAt": "Iniciar em", "DE.Views.LineNumbersDialog.textTitle": "Números de Linhas", "DE.Views.LineNumbersDialog.txtAutoText": "Automático", "DE.Views.Links.capBtnAddText": "Adicionar texto", "DE.Views.Links.capBtnBookmarks": "Marcador", "DE.Views.Links.capBtnCaption": "<PERSON>a", "DE.Views.Links.capBtnContentsUpdate": "<PERSON><PERSON><PERSON><PERSON> tabela", "DE.Views.Links.capBtnCrossRef": "Referência-cruzada", "DE.Views.Links.capBtnInsContents": "Índice remissivo", "DE.Views.Links.capBtnInsFootnote": "<PERSON><PERSON>", "DE.Views.Links.capBtnInsLink": "Hiperligação", "DE.Views.Links.capBtnTOF": "Tabela de figuras", "DE.Views.Links.confirmDeleteFootnotes": "Deseja eliminar todas as notas de rodapé?", "DE.Views.Links.confirmReplaceTOF": "Quer substituir a tabela de figuras selecionada?", "DE.Views.Links.mniConvertNote": "Converter todas as notas", "DE.Views.Links.mniDelFootnote": "Eliminar todas as notas", "DE.Views.Links.mniInsEndnote": "Inserir nota final", "DE.Views.Links.mniInsFootnote": "Inserir nota de rodapé", "DE.Views.Links.mniNoteSettings": "Definições de notas", "DE.Views.Links.textContentsRemove": "Remover índice remissivo", "DE.Views.Links.textContentsSettings": "Definições", "DE.Views.Links.textConvertToEndnotes": "Converter todas as notas de rodapé em notas finais", "DE.Views.Links.textConvertToFootnotes": "Converter todas as notas finais em notas de rodapé", "DE.Views.Links.textGotoEndnote": "Ir para notas finais", "DE.Views.Links.textGotoFootnote": "Ir para notas de rodapé", "DE.Views.Links.textSwapNotes": "<PERSON><PERSON><PERSON> as notas de rodapé pelas notas de fim", "DE.Views.Links.textUpdateAll": "<PERSON><PERSON><PERSON><PERSON> toda a tabela", "DE.Views.Links.textUpdatePages": "Recarregar apenas o número de páginas", "DE.Views.Links.tipAddText": "Incluir título no índice remissivo ", "DE.Views.Links.tipBookmarks": "<PERSON><PERSON>r marcador", "DE.Views.Links.tipCaption": "<PERSON>ser<PERSON> legenda", "DE.Views.Links.tipContents": "Inserir índice remissivo", "DE.Views.Links.tipContentsUpdate": "Recarregar índice remissivo", "DE.Views.Links.tipCrossRef": "Inserir <PERSON><PERSON> cru<PERSON>a", "DE.Views.Links.tipInsertHyperlink": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Links.tipNotes": "Inserir ou editar notas de rodapé", "DE.Views.Links.tipTableFigures": "Inserir tabela de figuras", "DE.Views.Links.tipTableFiguresUpdate": "Recarregar tabela de figuras", "DE.Views.Links.titleUpdateTOF": "Recarregar tabela de figuras", "DE.Views.Links.txtDontShowTof": "Não mostrar no índice remissivo", "DE.Views.Links.txtLevel": "Nível", "DE.Views.ListSettingsDialog.textAuto": "Automático", "DE.Views.ListSettingsDialog.textCenter": "Centro", "DE.Views.ListSettingsDialog.textLeft": "E<PERSON>rda", "DE.Views.ListSettingsDialog.textLevel": "Nível", "DE.Views.ListSettingsDialog.textPreview": "Pré-visualizar", "DE.Views.ListSettingsDialog.textRight": "<PERSON><PERSON><PERSON>", "DE.Views.ListSettingsDialog.txtAlign": "Alinhamento", "DE.Views.ListSettingsDialog.txtBullet": "Marcador", "DE.Views.ListSettingsDialog.txtColor": "Cor", "DE.Views.ListSettingsDialog.txtFont": "Tipo de letra e Símbolo", "DE.Views.ListSettingsDialog.txtLikeText": "Como um texto", "DE.Views.ListSettingsDialog.txtNewBullet": "Nova marca", "DE.Views.ListSettingsDialog.txtNone": "<PERSON><PERSON><PERSON>", "DE.Views.ListSettingsDialog.txtSize": "<PERSON><PERSON><PERSON>", "DE.Views.ListSettingsDialog.txtSymbol": "Símbolo", "DE.Views.ListSettingsDialog.txtTitle": "Definições da lista", "DE.Views.ListSettingsDialog.txtType": "Tipo", "DE.Views.MailMergeEmailDlg.filePlaceholder": "PDF", "DE.Views.MailMergeEmailDlg.okButtonText": "Enviar", "DE.Views.MailMergeEmailDlg.subjectPlaceholder": "<PERSON><PERSON>", "DE.Views.MailMergeEmailDlg.textAttachDocx": "Attach as DOCX", "DE.Views.MailMergeEmailDlg.textAttachPdf": "Attach as PDF", "DE.Views.MailMergeEmailDlg.textFileName": "Nome do ficheiro", "DE.Views.MailMergeEmailDlg.textFormat": "Mail format", "DE.Views.MailMergeEmailDlg.textFrom": "De", "DE.Views.MailMergeEmailDlg.textHTML": "HTML", "DE.Views.MailMergeEmailDlg.textMessage": "Mensagem", "DE.Views.MailMergeEmailDlg.textSubject": "<PERSON><PERSON> de <PERSON>unto", "DE.Views.MailMergeEmailDlg.textTitle": "Send to Email", "DE.Views.MailMergeEmailDlg.textTo": "a", "DE.Views.MailMergeEmailDlg.textWarning": "Aviso!", "DE.Views.MailMergeEmailDlg.textWarningMsg": "Tenha em atenção de que não é possível interromper o envio de e-mail assim que selecionar 'Enviar'", "DE.Views.MailMergeSettings.downloadMergeTitle": "Merging", "DE.Views.MailMergeSettings.errorMailMergeSaveFile": "Falha ao unir.", "DE.Views.MailMergeSettings.notcriticalErrorTitle": "Aviso", "DE.Views.MailMergeSettings.textAddRecipients": "Tem que adicionar alguns destinatários à lista", "DE.Views.MailMergeSettings.textAll": "Todos os registos", "DE.Views.MailMergeSettings.textCurrent": "Current record", "DE.Views.MailMergeSettings.textDataSource": "Fonte de dados", "DE.Views.MailMergeSettings.textDocx": "Docx", "DE.Views.MailMergeSettings.textDownload": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.MailMergeSettings.textEditData": "Editar lista de destinatários", "DE.Views.MailMergeSettings.textEmail": "Email", "DE.Views.MailMergeSettings.textFrom": "De", "DE.Views.MailMergeSettings.textGoToMail": "Go to Mail", "DE.Views.MailMergeSettings.textHighlight": "Highlight merge fields", "DE.Views.MailMergeSettings.textInsertField": "Insert Merge Field", "DE.Views.MailMergeSettings.textMaxRecepients": "Max 100 recepients.", "DE.Views.MailMergeSettings.textMerge": "Unir", "DE.Views.MailMergeSettings.textMergeFields": "<PERSON><PERSON>", "DE.Views.MailMergeSettings.textMergeTo": "Merge to", "DE.Views.MailMergeSettings.textPdf": "PDF", "DE.Views.MailMergeSettings.textPortal": "Guardar", "DE.Views.MailMergeSettings.textPreview": "Resultados da pré-visualização", "DE.Views.MailMergeSettings.textReadMore": "Ler mais", "DE.Views.MailMergeSettings.textSendMsg": "<PERSON><PERSON> as mensagens de e-mail estão prontas e serão enviadas seguidamente.<br>A velocidade do envio depende do seu serviço de correio eletrónico.<br>Pode continuar a trabalhar ou fechar o documento. Terminada a ação, será enviada uma notificação por e-mail para o endereço de e-mail registado.", "DE.Views.MailMergeSettings.textTo": "a", "DE.Views.MailMergeSettings.txtFirst": "To first record", "DE.Views.MailMergeSettings.txtFromToError": "O valor \"De\" deve ser menor do que o valor \"Até\"", "DE.Views.MailMergeSettings.txtLast": "Para último registo", "DE.Views.MailMergeSettings.txtNext": "To next record", "DE.Views.MailMergeSettings.txtPrev": "To previous record", "DE.Views.MailMergeSettings.txtUntitled": "<PERSON><PERSON> tí<PERSON>lo", "DE.Views.MailMergeSettings.warnProcessMailMerge": "Starting merge failed", "DE.Views.Navigation.strNavigate": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Navigation.txtClosePanel": "<PERSON><PERSON><PERSON>", "DE.Views.Navigation.txtCollapse": "<PERSON><PERSON><PERSON><PERSON> tudo", "DE.Views.Navigation.txtDemote": "Re<PERSON><PERSON><PERSON>", "DE.Views.Navigation.txtEmpty": "Não existem títulos no documento.<br>Aplique um estilo de título ao texto para que este apareça no índice remissivo.", "DE.Views.Navigation.txtEmptyItem": "<PERSON><PERSON><PERSON><PERSON> vazio", "DE.Views.Navigation.txtEmptyViewer": "Não existem títulos no documento.", "DE.Views.Navigation.txtExpand": "Expandir tudo", "DE.Views.Navigation.txtExpandToLevel": "Expandir ao nível", "DE.Views.Navigation.txtFontSize": "Tamanho do tipo de letra", "DE.Views.Navigation.txtHeadingAfter": "Novo título <PERSON>", "DE.Views.Navigation.txtHeadingBefore": "Novo título antes", "DE.Views.Navigation.txtLarge": "Grande", "DE.Views.Navigation.txtMedium": "Médio", "DE.Views.Navigation.txtNewHeading": "Novo subtítulo", "DE.Views.Navigation.txtPromote": "Promover", "DE.Views.Navigation.txtSelect": "Selecionar conteúdo", "DE.Views.Navigation.txtSettings": "Definições dos títulos", "DE.Views.Navigation.txtSmall": "Pequeno", "DE.Views.Navigation.txtWrapHeadings": "<PERSON><PERSON><PERSON> t<PERSON><PERSON>", "DE.Views.NoteSettingsDialog.textApply": "Aplicar", "DE.Views.NoteSettingsDialog.textApplyTo": "Aplicar alterações a", "DE.Views.NoteSettingsDialog.textContinue": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.NoteSettingsDialog.textCustom": "Marca personal<PERSON>", "DE.Views.NoteSettingsDialog.textDocEnd": "Fim do documento", "DE.Views.NoteSettingsDialog.textDocument": "Documento inteiro", "DE.Views.NoteSettingsDialog.textEachPage": "Reiniciar cada página", "DE.Views.NoteSettingsDialog.textEachSection": "Reiniciar Em Cada Secção", "DE.Views.NoteSettingsDialog.textEndnote": "Nota final", "DE.Views.NoteSettingsDialog.textFootnote": "<PERSON><PERSON>", "DE.Views.NoteSettingsDialog.textFormat": "Formato", "DE.Views.NoteSettingsDialog.textInsert": "Inserir", "DE.Views.NoteSettingsDialog.textLocation": "Localização", "DE.Views.NoteSettingsDialog.textNumbering": "Numeração", "DE.Views.NoteSettingsDialog.textNumFormat": "Formato de número", "DE.Views.NoteSettingsDialog.textPageBottom": "Fundo da página", "DE.Views.NoteSettingsDialog.textSectEnd": "Fim da secção", "DE.Views.NoteSettingsDialog.textSection": "Se<PERSON>ção atual", "DE.Views.NoteSettingsDialog.textStart": "Iniciar em", "DE.Views.NoteSettingsDialog.textTextBottom": "Abaixo do texto", "DE.Views.NoteSettingsDialog.textTitle": "Definições de notas", "DE.Views.NotesRemoveDialog.textEnd": "Eliminar todas as notas finais", "DE.Views.NotesRemoveDialog.textFoot": "Eliminar to<PERSON> as notas de rodap<PERSON>", "DE.Views.NotesRemoveDialog.textTitle": "<PERSON>minar notas", "DE.Views.PageMarginsDialog.notcriticalErrorTitle": "Aviso", "DE.Views.PageMarginsDialog.textBottom": "Baixo", "DE.Views.PageMarginsDialog.textGutter": "Medianiz", "DE.Views.PageMarginsDialog.textGutterPosition": "Posição da Medianiz", "DE.Views.PageMarginsDialog.textInside": "<PERSON><PERSON> de", "DE.Views.PageMarginsDialog.textLandscape": "Horizontal", "DE.Views.PageMarginsDialog.textLeft": "E<PERSON>rda", "DE.Views.PageMarginsDialog.textMirrorMargins": "Margens espelho", "DE.Views.PageMarginsDialog.textMultiplePages": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.PageMarginsDialog.textNormal": "Normal", "DE.Views.PageMarginsDialog.textOrientation": "Orientação", "DE.Views.PageMarginsDialog.textOutside": "Exterior", "DE.Views.PageMarginsDialog.textPortrait": "Vertical", "DE.Views.PageMarginsDialog.textPreview": "Pré-visualizar", "DE.Views.PageMarginsDialog.textRight": "<PERSON><PERSON><PERSON>", "DE.Views.PageMarginsDialog.textTitle": "Margens", "DE.Views.PageMarginsDialog.textTop": "Cima", "DE.Views.PageMarginsDialog.txtMarginsH": "As margens superior e inferior são muito grandes para a altura indicada", "DE.Views.PageMarginsDialog.txtMarginsW": "As margens direita e esquerda são muito grandes para a largura indicada", "DE.Views.PageSizeDialog.textHeight": "Altura", "DE.Views.PageSizeDialog.textPreset": "Predefinição", "DE.Views.PageSizeDialog.textTitle": "<PERSON><PERSON><PERSON>", "DE.Views.PageSizeDialog.textWidth": "<PERSON><PERSON><PERSON>", "DE.Views.PageSizeDialog.txtCustom": "Personalizado", "DE.Views.PageThumbnails.textClosePanel": "<PERSON><PERSON><PERSON> os thumbnails da página", "DE.Views.PageThumbnails.textHighlightVisiblePart": "Realçar a parte visível da página", "DE.Views.PageThumbnails.textPageThumbnails": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.PageThumbnails.textThumbnailsSettings": "Definições de Thumbnails", "DE.Views.PageThumbnails.textThumbnailsSize": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettings.strIndent": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettings.strIndentsLeftText": "E<PERSON>rda", "DE.Views.ParagraphSettings.strIndentsRightText": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettings.strIndentsSpecial": "Especial", "DE.Views.ParagraphSettings.strLineHeight": "Espaçamento entre linhas", "DE.Views.ParagraphSettings.strParagraphSpacing": "Espaçamento", "DE.Views.ParagraphSettings.strSomeParagraphSpace": "Não adicionar intervalo entre parágrafos do mesmo estilo", "DE.Views.ParagraphSettings.strSpacingAfter": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettings.strSpacingBefore": "<PERSON><PERSON>", "DE.Views.ParagraphSettings.textAdvanced": "Mostrar definições avançadas", "DE.Views.ParagraphSettings.textAt": "Em", "DE.Views.ParagraphSettings.textAtLeast": "<PERSON><PERSON>", "DE.Views.ParagraphSettings.textAuto": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettings.textBackColor": "Cor de fundo", "DE.Views.ParagraphSettings.textExact": "Exatamente", "DE.Views.ParagraphSettings.textFirstLine": "Primeira linha", "DE.Views.ParagraphSettings.textHanging": "Suspensão", "DE.Views.ParagraphSettings.textNoneSpecial": "(nenhum)", "DE.Views.ParagraphSettings.txtAutoText": "Automático", "DE.Views.ParagraphSettingsAdvanced.noTabs": "Os separadores especificados aparecerão neste campo", "DE.Views.ParagraphSettingsAdvanced.strAllCaps": "Tudo em ma<PERSON>", "DE.Views.ParagraphSettingsAdvanced.strBorders": "Contornos e Preenchimento", "DE.Views.ParagraphSettingsAdvanced.strBreakBefore": "Quebra de página antes", "DE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.strIndent": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "E<PERSON>rda", "DE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Espaçamento entre linhas", "DE.Views.ParagraphSettingsAdvanced.strIndentsOutlinelevel": "Nível de contorno", "DE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "<PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Especial", "DE.Views.ParagraphSettingsAdvanced.strKeepLines": "Manter linhas juntas", "DE.Views.ParagraphSettingsAdvanced.strKeepNext": "Manter com seguinte", "DE.Views.ParagraphSettingsAdvanced.strMargins": "Preenchimentos", "DE.Views.ParagraphSettingsAdvanced.strOrphan": "Controlo de órfãos", "DE.Views.ParagraphSettingsAdvanced.strParagraphFont": "<PERSON><PERSON><PERSON> de letra", "DE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Avanços e espaçamento", "DE.Views.ParagraphSettingsAdvanced.strParagraphLine": "Quebras de linha e de p<PERSON>gina", "DE.Views.ParagraphSettingsAdvanced.strParagraphPosition": "Posicionamento", "DE.Views.ParagraphSettingsAdvanced.strSmallCaps": "Versalet<PERSON>", "DE.Views.ParagraphSettingsAdvanced.strSomeParagraphSpace": "Não adicionar intervalo entre parágrafos do mesmo estilo", "DE.Views.ParagraphSettingsAdvanced.strSpacing": "Espaçamento", "DE.Views.ParagraphSettingsAdvanced.strStrike": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.strSubscript": "Subscrito", "DE.Views.ParagraphSettingsAdvanced.strSuperscript": "Sobrescrito", "DE.Views.ParagraphSettingsAdvanced.strSuppressLineNumbers": "Sup<PERSON><PERSON> nú<PERSON> linhas", "DE.Views.ParagraphSettingsAdvanced.strTabs": "Separadores", "DE.Views.ParagraphSettingsAdvanced.textAlign": "Alinhamento", "DE.Views.ParagraphSettingsAdvanced.textAll": "<PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textAtLeast": "<PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textAuto": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textBackColor": "Cor de fundo", "DE.Views.ParagraphSettingsAdvanced.textBodyText": "Texto básico", "DE.Views.ParagraphSettingsAdvanced.textBorderColor": "Cor do contorno", "DE.Views.ParagraphSettingsAdvanced.textBorderDesc": "Clique no diagrama ou utilize os botões para selecionar os contornos e aplicar um estilo", "DE.Views.ParagraphSettingsAdvanced.textBorderWidth": "Tamanho do contorno", "DE.Views.ParagraphSettingsAdvanced.textBottom": "Baixo", "DE.Views.ParagraphSettingsAdvanced.textCentered": "Centrado", "DE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Espaçamento entre caracteres", "DE.Views.ParagraphSettingsAdvanced.textContext": "Contextual", "DE.Views.ParagraphSettingsAdvanced.textContextDiscret": "Contextual e Discricionário", "DE.Views.ParagraphSettingsAdvanced.textContextHistDiscret": "Contextual, Histórico e Discricionário", "DE.Views.ParagraphSettingsAdvanced.textContextHistorical": "Contextual e Histórico", "DE.Views.ParagraphSettingsAdvanced.textDefault": "Separador predefinido", "DE.Views.ParagraphSettingsAdvanced.textDiscret": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textEffects": "Efeitos", "DE.Views.ParagraphSettingsAdvanced.textExact": "Exatamente", "DE.Views.ParagraphSettingsAdvanced.textFirstLine": "Primeira linha", "DE.Views.ParagraphSettingsAdvanced.textHanging": "Suspensão", "DE.Views.ParagraphSettingsAdvanced.textHistorical": "Hist<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textHistoricalDiscret": "Histórico e Discricionário", "DE.Views.ParagraphSettingsAdvanced.textJustified": "Justificado", "DE.Views.ParagraphSettingsAdvanced.textLeader": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textLeft": "E<PERSON>rda", "DE.Views.ParagraphSettingsAdvanced.textLevel": "Nível", "DE.Views.ParagraphSettingsAdvanced.textLigatures": "Ligaduras tipográficas", "DE.Views.ParagraphSettingsAdvanced.textNone": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(nenhum)", "DE.Views.ParagraphSettingsAdvanced.textOpenType": "Funcionalidades do OpenType", "DE.Views.ParagraphSettingsAdvanced.textPosition": "Posição", "DE.Views.ParagraphSettingsAdvanced.textRemove": "Remover", "DE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Remover todos", "DE.Views.ParagraphSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textSet": "Especificar", "DE.Views.ParagraphSettingsAdvanced.textSpacing": "Espaçamento", "DE.Views.ParagraphSettingsAdvanced.textStandard": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textStandardContext": "Padrão e Contextual", "DE.Views.ParagraphSettingsAdvanced.textStandardContextDiscret": "<PERSON><PERSON><PERSON>, Contextual e Discricionário", "DE.Views.ParagraphSettingsAdvanced.textStandardContextHist": "<PERSON><PERSON><PERSON>, Contextual e Histórico", "DE.Views.ParagraphSettingsAdvanced.textStandardDiscret": "Padrão e Discricionário", "DE.Views.ParagraphSettingsAdvanced.textStandardHistDiscret": "Padrão, Histórico e Discricionário", "DE.Views.ParagraphSettingsAdvanced.textStandardHistorical": "Padrão e Histórico", "DE.Views.ParagraphSettingsAdvanced.textTabCenter": "Centro", "DE.Views.ParagraphSettingsAdvanced.textTabLeft": "E<PERSON>rda", "DE.Views.ParagraphSettingsAdvanced.textTabPosition": "Posição do separador", "DE.Views.ParagraphSettingsAdvanced.textTabRight": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textTitle": "Parágrafo - Definições avançadas", "DE.Views.ParagraphSettingsAdvanced.textTop": "Cima", "DE.Views.ParagraphSettingsAdvanced.tipAll": "Definir contorno externo e todas as linhas internas", "DE.Views.ParagraphSettingsAdvanced.tipBottom": "Definir apenas contorno inferior", "DE.Views.ParagraphSettingsAdvanced.tipInner": "Definir apenas linhas internas horizontais", "DE.Views.ParagraphSettingsAdvanced.tipLeft": "Definir apenas contorno esquerdo", "DE.Views.ParagraphSettingsAdvanced.tipNone": "Definir sem contornos", "DE.Views.ParagraphSettingsAdvanced.tipOuter": "Definir apenas contorno externo", "DE.Views.ParagraphSettingsAdvanced.tipRight": "Definir apenas contorno direito", "DE.Views.ParagraphSettingsAdvanced.tipTop": "Definir apenas contorno superior", "DE.Views.ParagraphSettingsAdvanced.txtAutoText": "Automático", "DE.Views.ParagraphSettingsAdvanced.txtNoBorders": "Sem contornos", "DE.Views.PrintWithPreview.txtAllPages": "<PERSON><PERSON> as p<PERSON><PERSON><PERSON>", "DE.Views.PrintWithPreview.txtCustom": "Personalizado", "DE.Views.PrintWithPreview.txtLeft": "E<PERSON>rda", "DE.Views.ProtectDialog.textComments": "Comentários", "DE.Views.ProtectDialog.textForms": "Preenchimento de formulários", "DE.Views.ProtectDialog.textReview": "Alterações rastreadas", "DE.Views.ProtectDialog.textView": "Sem alterações (apenas leitura)", "DE.Views.ProtectDialog.txtAllow": "Permitir apenas este tipo de edição no documento", "DE.Views.ProtectDialog.txtIncorrectPwd": "A palavra-passe de confirmação não é idêntica", "DE.Views.ProtectDialog.txtOptional": "opcional", "DE.Views.ProtectDialog.txtPassword": "Palavra-passe", "DE.Views.ProtectDialog.txtProtect": "Proteger", "DE.Views.ProtectDialog.txtRepeat": "<PERSON><PERSON>r pala<PERSON>ra-passe", "DE.Views.ProtectDialog.txtTitle": "Proteger", "DE.Views.ProtectDialog.txtWarning": "Aviso: se perder ou esquecer a palavra-passe, não será possível recuperá-la. Guarde-a num local seguro.", "DE.Views.RightMenu.txtChartSettings": "Definições de gráfico", "DE.Views.RightMenu.txtFormSettings": "Definições de formulários", "DE.Views.RightMenu.txtHeaderFooterSettings": "Definições de cabeçalho/rodapé", "DE.Views.RightMenu.txtImageSettings": "Definições de imagem", "DE.Views.RightMenu.txtMailMergeSettings": "Definições de e-mail em série", "DE.Views.RightMenu.txtParagraphSettings": "Definições de parágrafo", "DE.Views.RightMenu.txtShapeSettings": "Definições de forma", "DE.Views.RightMenu.txtSignatureSettings": "Definições de assinatura", "DE.Views.RightMenu.txtTableSettings": "Definições da tabela", "DE.Views.RightMenu.txtTextArtSettings": "Definições de texto artístico", "DE.Views.RolesManagerDlg.textAnyone": "Alguém", "DE.Views.SaveFormDlg.textAnyone": "Alguém", "DE.Views.ShapeSettings.strBackground": "Cor de fundo", "DE.Views.ShapeSettings.strChange": "Alterar forma automática", "DE.Views.ShapeSettings.strColor": "Cor", "DE.Views.ShapeSettings.strFill": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.strForeground": "Cor principal", "DE.Views.ShapeSettings.strPattern": "Padrão", "DE.Views.ShapeSettings.strShadow": "Mostrar sombra", "DE.Views.ShapeSettings.strSize": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.strStroke": "<PERSON><PERSON>", "DE.Views.ShapeSettings.strTransparency": "Opacidade", "DE.Views.ShapeSettings.strType": "Tipo", "DE.Views.ShapeSettings.textAdvanced": "Mostrar definições avançadas", "DE.Views.ShapeSettings.textAngle": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.textBorderSizeErr": "O valor inserido não está correto.<br>Introduza um valor entre 0 pt e 1584 pt.", "DE.Views.ShapeSettings.textColor": "<PERSON><PERSON> <PERSON> pre<PERSON>", "DE.Views.ShapeSettings.textDirection": "Direção", "DE.Views.ShapeSettings.textEmptyPattern": "<PERSON><PERSON>", "DE.Views.ShapeSettings.textFlip": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.textFromFile": "De um ficheiro", "DE.Views.ShapeSettings.textFromStorage": "De um armazenamento", "DE.Views.ShapeSettings.textFromUrl": "De um URL", "DE.Views.ShapeSettings.textGradient": "Ponto de gradiente", "DE.Views.ShapeSettings.textGradientFill": "Preenchimento gradiente", "DE.Views.ShapeSettings.textHint270": "Rodar 90º à esquerda", "DE.Views.ShapeSettings.textHint90": "Rodar 90º à direita", "DE.Views.ShapeSettings.textHintFlipH": "Virar horizontalmente", "DE.Views.ShapeSettings.textHintFlipV": "Virar verticalmente", "DE.Views.ShapeSettings.textImageTexture": "<PERSON><PERSON> ou Textura", "DE.Views.ShapeSettings.textLinear": "Linear", "DE.Views.ShapeSettings.textNoFill": "Sem preenchimento", "DE.Views.ShapeSettings.textPatternFill": "Padrão", "DE.Views.ShapeSettings.textPosition": "Posição", "DE.Views.ShapeSettings.textRadial": "Radial", "DE.Views.ShapeSettings.textRecentlyUsed": "Utilizado recentemente", "DE.Views.ShapeSettings.textRotate90": "Rodar 90°", "DE.Views.ShapeSettings.textRotation": "Rotação", "DE.Views.ShapeSettings.textSelectImage": "Selecionar imagem", "DE.Views.ShapeSettings.textSelectTexture": "Selecionar", "DE.Views.ShapeSettings.textStretch": "<PERSON><PERSON>", "DE.Views.ShapeSettings.textStyle": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.textTexture": "De uma textura", "DE.Views.ShapeSettings.textTile": "Lado a lado", "DE.Views.ShapeSettings.textWrap": "Estilo de moldagem", "DE.Views.ShapeSettings.tipAddGradientPoint": "Adicionar ponto de gradiente", "DE.Views.ShapeSettings.tipRemoveGradientPoint": "Remover Ponto de Gradiente", "DE.Views.ShapeSettings.txtBehind": "Atrás do texto", "DE.Views.ShapeSettings.txtBrownPaper": "<PERSON>pel pardo", "DE.Views.ShapeSettings.txtCanvas": "<PERSON><PERSON>", "DE.Views.ShapeSettings.txtCarton": "Cartão", "DE.Views.ShapeSettings.txtDarkFabric": "Tela escura", "DE.Views.ShapeSettings.txtGrain": "Granulação", "DE.Views.ShapeSettings.txtGranite": "<PERSON><PERSON>", "DE.Views.ShapeSettings.txtGreyPaper": "Papel cinza", "DE.Views.ShapeSettings.txtInFront": "À frente do texto", "DE.Views.ShapeSettings.txtInline": "Em Linha com o Texto", "DE.Views.ShapeSettings.txtKnit": "Unir", "DE.Views.ShapeSettings.txtLeather": "<PERSON><PERSON>", "DE.Views.ShapeSettings.txtNoBorders": "<PERSON><PERSON> linha", "DE.Views.ShapeSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.txtSquare": "Quadrado", "DE.Views.ShapeSettings.txtThrough": "Através", "DE.Views.ShapeSettings.txtTight": "<PERSON><PERSON>", "DE.Views.ShapeSettings.txtTopAndBottom": "Cima e baixo", "DE.Views.ShapeSettings.txtWood": "Madeira", "DE.Views.SignatureSettings.notcriticalErrorTitle": "Aviso", "DE.Views.SignatureSettings.strDelete": "Remover assinatura", "DE.Views.SignatureSettings.strDetails": "Detalhes da assinatura", "DE.Views.SignatureSettings.strInvalid": "Assinaturas inválidas", "DE.Views.SignatureSettings.strRequested": "Assinaturas solicitadas", "DE.Views.SignatureSettings.strSetup": "Definições de Assinatura", "DE.Views.SignatureSettings.strSign": "<PERSON><PERSON><PERSON>", "DE.Views.SignatureSettings.strSignature": "Assinatura", "DE.Views.SignatureSettings.strSigner": "<PERSON><PERSON><PERSON>", "DE.Views.SignatureSettings.strValid": "Assinaturas válidas", "DE.Views.SignatureSettings.txtContinueEditing": "<PERSON>ar mesmo assim", "DE.Views.SignatureSettings.txtEditWarning": "Se editar este documento, remove as assinaturas.<br>Tem a certeza de que deseja continuar?", "DE.Views.SignatureSettings.txtRemoveWarning": "Quer remover esta assinatura?<br><PERSON><PERSON> não pode ser anulado.", "DE.Views.SignatureSettings.txtRequestedSignatures": "Este documento precisa de ser assinado.", "DE.Views.SignatureSettings.txtSigned": "Assinaturas adicionadas ao documento. Este documento não pode ser editado.", "DE.Views.SignatureSettings.txtSignedInvalid": "Algumas das assinaturas digitais são inválidas ou não puderam ser verificadas. Este documento não pode ser editado.", "DE.Views.Statusbar.goToPageText": "Ir para a página", "DE.Views.Statusbar.pageIndexText": "Página {0} de {1}", "DE.Views.Statusbar.tipFitPage": "Ajustar à página", "DE.Views.Statusbar.tipFitWidth": "<PERSON><PERSON><PERSON> largura", "DE.Views.Statusbar.tipHandTool": "Ferramenta de mão", "DE.Views.Statusbar.tipSelectTool": "Selecionar ferramenta", "DE.Views.Statusbar.tipSetLang": "Definir idioma do texto", "DE.Views.Statusbar.tipZoomFactor": "Ampliação", "DE.Views.Statusbar.tipZoomIn": "Ampliar", "DE.Views.Statusbar.tipZoomOut": "Reduzir", "DE.Views.Statusbar.txtPageNumInvalid": "Número da página inválido", "DE.Views.StyleTitleDialog.textHeader": "Criar novo estilo", "DE.Views.StyleTitleDialog.textNextStyle": "Estilo do parágrafo se<PERSON>te", "DE.Views.StyleTitleDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.StyleTitleDialog.txtEmpty": "This field is required", "DE.Views.StyleTitleDialog.txtNotEmpty": "Field must not be empty", "DE.Views.StyleTitleDialog.txtSameAs": "Igual ao novo estilo criado", "DE.Views.TableFormulaDialog.textBookmark": "Colar Marcador", "DE.Views.TableFormulaDialog.textFormat": "Formato de número", "DE.Views.TableFormulaDialog.textFormula": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.TableFormulaDialog.textInsertFunction": "Colar Função", "DE.Views.TableFormulaDialog.textTitle": "Definições de fórmulas", "DE.Views.TableOfContentsSettings.strAlign": "Alinhar número das páginas à direita", "DE.Views.TableOfContentsSettings.strFullCaption": "Incluir etiqueta e número", "DE.Views.TableOfContentsSettings.strLinks": "Formatar índice remissivo como ligação", "DE.Views.TableOfContentsSettings.strLinksOF": "Formatar tabela de figuras como ligações", "DE.Views.TableOfContentsSettings.strShowPages": "Mostrar número da página", "DE.Views.TableOfContentsSettings.textBuildTable": "Criar índice remissivo com base em", "DE.Views.TableOfContentsSettings.textBuildTableOF": "Construir tabela de figuras de", "DE.Views.TableOfContentsSettings.textEquation": "Equação", "DE.Views.TableOfContentsSettings.textFigure": "<PERSON><PERSON><PERSON>", "DE.Views.TableOfContentsSettings.textLeader": "<PERSON><PERSON><PERSON>", "DE.Views.TableOfContentsSettings.textLevel": "Nível", "DE.Views.TableOfContentsSettings.textLevels": "Níveis", "DE.Views.TableOfContentsSettings.textNone": "<PERSON><PERSON><PERSON>", "DE.Views.TableOfContentsSettings.textRadioCaption": "<PERSON>a", "DE.Views.TableOfContentsSettings.textRadioLevels": "Níveis de contorno", "DE.Views.TableOfContentsSettings.textRadioStyle": "<PERSON><PERSON><PERSON>", "DE.Views.TableOfContentsSettings.textRadioStyles": "Estilos selecionados", "DE.Views.TableOfContentsSettings.textStyle": "<PERSON><PERSON><PERSON>", "DE.Views.TableOfContentsSettings.textStyles": "Estilos", "DE.Views.TableOfContentsSettings.textTable": "<PERSON><PERSON><PERSON>", "DE.Views.TableOfContentsSettings.textTitle": "Índice remissivo", "DE.Views.TableOfContentsSettings.textTitleTOF": "Tabela de figuras", "DE.Views.TableOfContentsSettings.txtCentered": "Centrado", "DE.Views.TableOfContentsSettings.txtClassic": "Clássico", "DE.Views.TableOfContentsSettings.txtCurrent": "Atual", "DE.Views.TableOfContentsSettings.txtDistinctive": "Distintivo", "DE.Views.TableOfContentsSettings.txtFormal": "Formal", "DE.Views.TableOfContentsSettings.txtModern": "Moderno", "DE.Views.TableOfContentsSettings.txtOnline": "Online", "DE.Views.TableOfContentsSettings.txtSimple": "Simples", "DE.Views.TableOfContentsSettings.txtStandard": "Padrão", "DE.Views.TableSettings.deleteColumnText": "Eliminar coluna", "DE.Views.TableSettings.deleteRowText": "Excluir linha", "DE.Views.TableSettings.deleteTableText": "Eliminar tabela", "DE.Views.TableSettings.insertColumnLeftText": "Inserir coluna à esquerda", "DE.Views.TableSettings.insertColumnRightText": "Inserir coluna à direita", "DE.Views.TableSettings.insertRowAboveText": "<PERSON><PERSON><PERSON> linha acima", "DE.Views.TableSettings.insertRowBelowText": "<PERSON>ser<PERSON> linha a<PERSON>o", "DE.Views.TableSettings.mergeCellsText": "Mesclar célu<PERSON>", "DE.Views.TableSettings.selectCellText": "Selecionar célula", "DE.Views.TableSettings.selectColumnText": "Selecionar coluna", "DE.Views.TableSettings.selectRowText": "Selecionar linha", "DE.Views.TableSettings.selectTableText": "Selecionar tabela", "DE.Views.TableSettings.splitCellsText": "<PERSON><PERSON><PERSON>...", "DE.Views.TableSettings.splitCellTitleText": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.strRepeatRow": "Repetir como linha de cabeçalho em todas as p<PERSON>ginas", "DE.Views.TableSettings.textAddFormula": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.TableSettings.textAdvanced": "Mostrar definições avançadas", "DE.Views.TableSettings.textBackColor": "Cor de fundo", "DE.Views.TableSettings.textBanded": "Em tiras", "DE.Views.TableSettings.textBorderColor": "Cor", "DE.Views.TableSettings.textBorders": "Estilo do contorno", "DE.Views.TableSettings.textCellSize": "<PERSON><PERSON><PERSON> l<PERSON> e de colu<PERSON>", "DE.Views.TableSettings.textColumns": "Colunas", "DE.Views.TableSettings.textConvert": "Converter tabela em texto", "DE.Views.TableSettings.textDistributeCols": "Distribuir colunas", "DE.Views.TableSettings.textDistributeRows": "Distribuir linhas", "DE.Views.TableSettings.textEdit": "Linhas e colunas", "DE.Views.TableSettings.textEmptyTemplate": "Sem modelos", "DE.Views.TableSettings.textFirst": "<PERSON><PERSON>", "DE.Views.TableSettings.textHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.TableSettings.textHeight": "Altura", "DE.Views.TableSettings.textLast": "Última", "DE.Views.TableSettings.textRows": "<PERSON><PERSON>", "DE.Views.TableSettings.textSelectBorders": "Selecione os contornos aos quais pretende aplicar o estilo escolhido", "DE.Views.TableSettings.textTemplate": "Selecionar de um modelo", "DE.Views.TableSettings.textTotal": "Total", "DE.Views.TableSettings.textWidth": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.tipAll": "Definir contorno externo e todas as linhas internas", "DE.Views.TableSettings.tipBottom": "Definir apenas contorno inferior externo", "DE.Views.TableSettings.tipInner": "Definir apenas linhas internas", "DE.Views.TableSettings.tipInnerHor": "Definir apenas linhas internas horizontais", "DE.Views.TableSettings.tipInnerVert": "Definir apenas linhas internas verticais", "DE.Views.TableSettings.tipLeft": "Definir apenas contorno esquerdo externo", "DE.Views.TableSettings.tipNone": "Definir sem contornos", "DE.Views.TableSettings.tipOuter": "Definir apenas contorno externo", "DE.Views.TableSettings.tipRight": "Definir apenas contorno direito externo", "DE.Views.TableSettings.tipTop": "Definir apenas contorno superior externo", "DE.Views.TableSettings.txtGroupTable_BorderedAndLined": "Tabelas Contornadas e Alinhadas", "DE.Views.TableSettings.txtGroupTable_Custom": "Personalizado", "DE.Views.TableSettings.txtGroupTable_Grid": "<PERSON><PERSON><PERSON> grelha", "DE.Views.TableSettings.txtGroupTable_List": "Listar tabelas", "DE.Views.TableSettings.txtGroupTable_Plain": "Tabelas simples", "DE.Views.TableSettings.txtNoBorders": "Sem contornos", "DE.Views.TableSettings.txtTable_Accent": "Destaque", "DE.Views.TableSettings.txtTable_Bordered": "Contornado", "DE.Views.TableSettings.txtTable_BorderedAndLined": "Contornado e Alinhado", "DE.Views.TableSettings.txtTable_Colorful": "Colorido", "DE.Views.TableSettings.txtTable_Dark": "Escuro", "DE.Views.TableSettings.txtTable_GridTable": "Tabela em grelha", "DE.Views.TableSettings.txtTable_Light": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.txtTable_Lined": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.txtTable_ListTable": "Tabela em lista", "DE.Views.TableSettings.txtTable_PlainTable": "Tabela simples", "DE.Views.TableSettings.txtTable_TableGrid": "<PERSON><PERSON><PERSON>a", "DE.Views.TableSettingsAdvanced.textAlign": "Alinhamento", "DE.Views.TableSettingsAdvanced.textAlignment": "Alinhamento", "DE.Views.TableSettingsAdvanced.textAllowSpacing": "Permitir <PERSON> entre células", "DE.Views.TableSettingsAdvanced.textAlt": "Texto alternativo", "DE.Views.TableSettingsAdvanced.textAltDescription": "Descrição", "DE.Views.TableSettingsAdvanced.textAltTip": "A representação alternativa baseada em texto da informação visual do objeto, que será lida para as pessoas com deficiências visuais ou cognitivas para ajudá-las a entender melhor que informação, forma automática, gráfico ou tabela existe na imagem.", "DE.Views.TableSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textAnchorText": "Тexto", "DE.Views.TableSettingsAdvanced.textAutofit": "Redimensionar automaticamente para se ajustar ao conteúdo", "DE.Views.TableSettingsAdvanced.textBackColor": "Plano de fundo da célula", "DE.Views.TableSettingsAdvanced.textBelow": "abaixo", "DE.Views.TableSettingsAdvanced.textBorderColor": "Cor do contorno", "DE.Views.TableSettingsAdvanced.textBorderDesc": "Clique no diagrama ou utilize os botões para selecionar os contornos e aplicar um estilo", "DE.Views.TableSettingsAdvanced.textBordersBackgroung": "Contornos e Fundo", "DE.Views.TableSettingsAdvanced.textBorderWidth": "Tamanho do contorno", "DE.Views.TableSettingsAdvanced.textBottom": "Baixo", "DE.Views.TableSettingsAdvanced.textCellOptions": "Opções de célula", "DE.Views.TableSettingsAdvanced.textCellProps": "<PERSON><PERSON><PERSON><PERSON> da célula", "DE.Views.TableSettingsAdvanced.textCellSize": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textCenter": "Centro", "DE.Views.TableSettingsAdvanced.textCenterTooltip": "Centro", "DE.Views.TableSettingsAdvanced.textCheckMargins": "<PERSON>ar margens pad<PERSON>", "DE.Views.TableSettingsAdvanced.textDefaultMargins": "Margens padrão", "DE.Views.TableSettingsAdvanced.textDistance": "Distância do texto", "DE.Views.TableSettingsAdvanced.textHorizontal": "Horizontal", "DE.Views.TableSettingsAdvanced.textIndLeft": "<PERSON><PERSON><PERSON> da es<PERSON>da", "DE.Views.TableSettingsAdvanced.textLeft": "E<PERSON>rda", "DE.Views.TableSettingsAdvanced.textLeftTooltip": "E<PERSON>rda", "DE.Views.TableSettingsAdvanced.textMargin": "Margem", "DE.Views.TableSettingsAdvanced.textMargins": "Margens da célula", "DE.Views.TableSettingsAdvanced.textMeasure": "Medir em", "DE.Views.TableSettingsAdvanced.textMove": "Mover objeto com texto", "DE.Views.TableSettingsAdvanced.textOnlyCells": "Apenas para as células selecionadas", "DE.Views.TableSettingsAdvanced.textOptions": "Opções", "DE.Views.TableSettingsAdvanced.textOverlap": "<PERSON><PERSON><PERSON> sobrepo<PERSON>", "DE.Views.TableSettingsAdvanced.textPage": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textPosition": "Posição", "DE.Views.TableSettingsAdvanced.textPrefWidth": "<PERSON><PERSON><PERSON> preferida", "DE.Views.TableSettingsAdvanced.textPreview": "Pré-visualizar", "DE.Views.TableSettingsAdvanced.textRelative": "relativo para", "DE.Views.TableSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textRightOf": "para a direita de", "DE.Views.TableSettingsAdvanced.textRightTooltip": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textTable": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textTableBackColor": "Fundo da tabela", "DE.Views.TableSettingsAdvanced.textTablePosition": "Posição da tabela", "DE.Views.TableSettingsAdvanced.textTableSize": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textTitle": "Tabela - Definições avançadas", "DE.Views.TableSettingsAdvanced.textTop": "Cima", "DE.Views.TableSettingsAdvanced.textVertical": "Vertical", "DE.Views.TableSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textWidthSpaces": "Largura e Espaços", "DE.Views.TableSettingsAdvanced.textWrap": "<PERSON><PERSON><PERSON> texto", "DE.Views.TableSettingsAdvanced.textWrapNoneTooltip": "<PERSON><PERSON><PERSON> embutida", "DE.Views.TableSettingsAdvanced.textWrapParallelTooltip": "Tabela de fluxo", "DE.Views.TableSettingsAdvanced.textWrappingStyle": "Estilo de moldagem", "DE.Views.TableSettingsAdvanced.textWrapText": "<PERSON><PERSON><PERSON> texto", "DE.Views.TableSettingsAdvanced.tipAll": "Definir contorno externo e todas as linhas internas", "DE.Views.TableSettingsAdvanced.tipCellAll": "Definir contornos para células internas apenas", "DE.Views.TableSettingsAdvanced.tipCellInner": "Definir linhas verticais e horizontais apenas para células internas", "DE.Views.TableSettingsAdvanced.tipCellOuter": "Definir contornos externos apenas para células internas", "DE.Views.TableSettingsAdvanced.tipInner": "Definir apenas linhas internas", "DE.Views.TableSettingsAdvanced.tipNone": "Definir sem contornos", "DE.Views.TableSettingsAdvanced.tipOuter": "Definir apenas contorno externo", "DE.Views.TableSettingsAdvanced.tipTableOuterCellAll": "Definir contorno externo e contornos para todas as células internas", "DE.Views.TableSettingsAdvanced.tipTableOuterCellInner": "Definir contorno externo e linhas verticais e horizontais para células internas", "DE.Views.TableSettingsAdvanced.tipTableOuterCellOuter": "Definir contorno externo da tabela e contornos externos para células internas", "DE.Views.TableSettingsAdvanced.txtCm": "Centímetro", "DE.Views.TableSettingsAdvanced.txtInch": "Polegada", "DE.Views.TableSettingsAdvanced.txtNoBorders": "Sem contornos", "DE.Views.TableSettingsAdvanced.txtPercent": "Percentagem", "DE.Views.TableSettingsAdvanced.txtPt": "Ponto", "DE.Views.TableToTextDialog.textEmpty": "Deve escrever um carácter para o separador personalizado.", "DE.Views.TableToTextDialog.textNested": "Converter tabelas anin<PERSON>as", "DE.Views.TableToTextDialog.textOther": "Outros", "DE.Views.TableToTextDialog.textPara": "Marcas de parágrafo", "DE.Views.TableToTextDialog.textSemicolon": "Ponto e vírgula", "DE.Views.TableToTextDialog.textSeparator": "Separar texto com", "DE.Views.TableToTextDialog.textTab": "Separadores", "DE.Views.TableToTextDialog.textTitle": "Converter tabela em texto", "DE.Views.TextArtSettings.strColor": "Cor", "DE.Views.TextArtSettings.strFill": "<PERSON><PERSON><PERSON>", "DE.Views.TextArtSettings.strSize": "<PERSON><PERSON><PERSON>", "DE.Views.TextArtSettings.strStroke": "Traço", "DE.Views.TextArtSettings.strTransparency": "Opacidade", "DE.Views.TextArtSettings.strType": "Tipo", "DE.Views.TextArtSettings.textAngle": "<PERSON><PERSON><PERSON>", "DE.Views.TextArtSettings.textBorderSizeErr": "O valor inserido não está correto.<br>Introduza um valor entre 0 pt e 1584 pt.", "DE.Views.TextArtSettings.textColor": "<PERSON><PERSON> <PERSON> pre<PERSON>", "DE.Views.TextArtSettings.textDirection": "Direção", "DE.Views.TextArtSettings.textGradient": "Gradiente", "DE.Views.TextArtSettings.textGradientFill": "Preenchimento gradiente", "DE.Views.TextArtSettings.textLinear": "Linear", "DE.Views.TextArtSettings.textNoFill": "Sem preenchimento", "DE.Views.TextArtSettings.textPosition": "Posição", "DE.Views.TextArtSettings.textRadial": "Radial", "DE.Views.TextArtSettings.textSelectTexture": "Selecionar", "DE.Views.TextArtSettings.textStyle": "<PERSON><PERSON><PERSON>", "DE.Views.TextArtSettings.textTemplate": "<PERSON><PERSON>", "DE.Views.TextArtSettings.textTransform": "Transform", "DE.Views.TextArtSettings.tipAddGradientPoint": "Adicionar ponto de gradiente", "DE.Views.TextArtSettings.tipRemoveGradientPoint": "Remover Ponto de Gradiente", "DE.Views.TextArtSettings.txtNoBorders": "<PERSON><PERSON> linha", "DE.Views.TextToTableDialog.textAutofit": "Comportamento de auto-ajuste", "DE.Views.TextToTableDialog.textColumns": "Colunas", "DE.Views.TextToTableDialog.textContents": "Adaptação automática ao conteúdo", "DE.Views.TextToTableDialog.textEmpty": "Deve escrever um carácter para o separador personalizado.", "DE.Views.TextToTableDialog.textFixed": "Largura fixa da coluna", "DE.Views.TextToTableDialog.textOther": "Outros", "DE.Views.TextToTableDialog.textPara": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.TextToTableDialog.textRows": "<PERSON><PERSON>", "DE.Views.TextToTableDialog.textSemicolon": "Ponto e vírgula", "DE.Views.TextToTableDialog.textSeparator": "Separar texto em", "DE.Views.TextToTableDialog.textTab": "Separadores", "DE.Views.TextToTableDialog.textTableSize": "<PERSON><PERSON><PERSON>", "DE.Views.TextToTableDialog.textTitle": "Converter texto em tabela", "DE.Views.TextToTableDialog.textWindow": "Ajustar automaticamente à janela", "DE.Views.TextToTableDialog.txtAutoText": "Automático", "DE.Views.Toolbar.capBtnAddComment": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.capBtnBlankPage": "Página vazia", "DE.Views.Toolbar.capBtnColumns": "Colunas", "DE.Views.Toolbar.capBtnComment": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.capBtnDateTime": "Data e hora", "DE.Views.Toolbar.capBtnInsChart": "Gráfico", "DE.Views.Toolbar.capBtnInsControls": "Controlos de conteúdo", "DE.Views.Toolbar.capBtnInsDropcap": "Letra capitular", "DE.Views.Toolbar.capBtnInsEquation": "Equação", "DE.Views.Toolbar.capBtnInsHeader": "Cabeçalho/rodapé", "DE.Views.Toolbar.capBtnInsImage": "Imagem", "DE.Views.Toolbar.capBtnInsPagebreak": "Quebras", "DE.Views.Toolbar.capBtnInsShape": "Forma", "DE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "DE.Views.Toolbar.capBtnInsSymbol": "Símbolo", "DE.Views.Toolbar.capBtnInsTable": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.capBtnInsTextart": "Lágrima", "DE.Views.Toolbar.capBtnInsTextbox": "Caixa de texto", "DE.Views.Toolbar.capBtnLineNumbers": "Números de Linhas", "DE.Views.Toolbar.capBtnMargins": "Margens", "DE.Views.Toolbar.capBtnPageOrient": "Orientação", "DE.Views.Toolbar.capBtnPageSize": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.capBtnWatermark": "Marca d'á<PERSON>", "DE.Views.Toolbar.capImgAlign": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.capImgBackward": "Enviar para trás", "DE.Views.Toolbar.capImgForward": "Trazer para a frente", "DE.Views.Toolbar.capImgGroup": "Grupo", "DE.Views.Toolbar.capImgWrapping": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.mniCapitalizeWords": "Capitalizar cada palavra", "DE.Views.Toolbar.mniCustomTable": "Inserir tabela personalizada", "DE.Views.Toolbar.mniDrawTable": "<PERSON><PERSON><PERSON> tabela", "DE.Views.Toolbar.mniEditControls": "Definições de controlo", "DE.Views.Toolbar.mniEditDropCap": "Definições de capitulares", "DE.Views.Toolbar.mniEditFooter": "<PERSON><PERSON>", "DE.Views.Toolbar.mniEditHeader": "<PERSON><PERSON>", "DE.Views.Toolbar.mniEraseTable": "Eliminar tabela", "DE.Views.Toolbar.mniFromFile": "Do fi<PERSON>iro", "DE.Views.Toolbar.mniFromStorage": "Do armazenamento", "DE.Views.Toolbar.mniFromUrl": "De um URL", "DE.Views.Toolbar.mniHiddenBorders": "O<PERSON>ltar contornos da tabela", "DE.Views.Toolbar.mniHiddenChars": "Caracteres não imprimíveis", "DE.Views.Toolbar.mniHighlightControls": "Definições de destaque", "DE.Views.Toolbar.mniImageFromFile": "Imagem de um ficheiro", "DE.Views.Toolbar.mniImageFromStorage": "Imagem de um armazenamento", "DE.Views.Toolbar.mniImageFromUrl": "Imagem de um URL", "DE.Views.Toolbar.mniInsertSSE": "Inserir <PERSON> de Cálculo", "DE.Views.Toolbar.mniLowerCase": "minúscula", "DE.Views.Toolbar.mniRemoveFooter": "Remover rodapé", "DE.Views.Toolbar.mniRemoveHeader": "Remover cabeçalho", "DE.Views.Toolbar.mniSentenceCase": "Maiúscula no Início da frase.", "DE.Views.Toolbar.mniTextToTable": "Converter texto em tabela", "DE.Views.Toolbar.mniToggleCase": "iNVERTER mAIÚSCULAS/mINÚSCULAS", "DE.Views.Toolbar.mniUpperCase": "MAIÚSCULAS", "DE.Views.Toolbar.strMenuNoFill": "Sem preenchimento", "DE.Views.Toolbar.textAutoColor": "Automático", "DE.Views.Toolbar.textBold": "Negrito", "DE.Views.Toolbar.textBottom": "Baixo:", "DE.Views.Toolbar.textChangeLevel": "Alterar nível de lista", "DE.Views.Toolbar.textCheckboxControl": "Caixa de seleção", "DE.Views.Toolbar.textColumnsCustom": "Colunas personalizadas", "DE.Views.Toolbar.textColumnsLeft": "E<PERSON>rda", "DE.Views.Toolbar.textColumnsOne": "<PERSON><PERSON>", "DE.Views.Toolbar.textColumnsRight": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textColumnsThree": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textColumnsTwo": "<PERSON><PERSON>", "DE.Views.Toolbar.textComboboxControl": "Caixa de combinação", "DE.Views.Toolbar.textContinuous": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textContPage": "<PERSON><PERSON>gin<PERSON> contínua", "DE.Views.Toolbar.textCustomLineNumbers": "Opções de numeração de linha", "DE.Views.Toolbar.textDateControl": "Data", "DE.Views.Toolbar.textDropdownControl": "Lista suspensa", "DE.Views.Toolbar.textEditWatermark": "Marca d'água personalizada", "DE.Views.Toolbar.textEvenPage": "Página par", "DE.Views.Toolbar.textInMargin": "<PERSON> margem", "DE.Views.Toolbar.textInsColumnBreak": "Inserir quebra de coluna", "DE.Views.Toolbar.textInsertPageCount": "Inserir número de páginas", "DE.Views.Toolbar.textInsertPageNumber": "Inserir número de página", "DE.Views.Toolbar.textInsPageBreak": "Inserir quebra de página", "DE.Views.Toolbar.textInsSectionBreak": "Inserir quebra de seção", "DE.Views.Toolbar.textInText": "No texto", "DE.Views.Toolbar.textItalic": "Itálico", "DE.Views.Toolbar.textLandscape": "Horizontal", "DE.Views.Toolbar.textLeft": "Esquerda:", "DE.Views.Toolbar.textListSettings": "Definições da lista", "DE.Views.Toolbar.textMarginsLast": "Última personalizada", "DE.Views.Toolbar.textMarginsModerate": "Moderado", "DE.Views.Toolbar.textMarginsNarrow": "Estreita", "DE.Views.Toolbar.textMarginsNormal": "Normal", "DE.Views.Toolbar.textMarginsUsNormal": "US Normal", "DE.Views.Toolbar.textMarginsWide": "Amplo", "DE.Views.Toolbar.textNewColor": "Adicionar nova cor personalizada", "DE.Views.Toolbar.textNextPage": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textNoHighlight": "Sem realce", "DE.Views.Toolbar.textNone": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textOddPage": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textPageMarginsCustom": "Margens personalizadas", "DE.Views.Toolbar.textPageSizeCustom": "<PERSON><PERSON><PERSON> de página personalizado", "DE.Views.Toolbar.textPictureControl": "Imagem", "DE.Views.Toolbar.textPlainControl": "Texto simples", "DE.Views.Toolbar.textPortrait": "Vertical", "DE.Views.Toolbar.textRemoveControl": "Remover controlo de conteúdo", "DE.Views.Toolbar.textRemWatermark": "Remover marca d'água", "DE.Views.Toolbar.textRestartEachPage": "Reiniciar cada página", "DE.Views.Toolbar.textRestartEachSection": "Reiniciar Em Cada Secção", "DE.Views.Toolbar.textRichControl": "Texto simples", "DE.Views.Toolbar.textRight": "Direita:", "DE.Views.Toolbar.textStrikeout": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textStyleMenuDelete": "Delete style", "DE.Views.Toolbar.textStyleMenuDeleteAll": "Delete all custom styles", "DE.Views.Toolbar.textStyleMenuNew": "Novo estilo baseado na seleção", "DE.Views.Toolbar.textStyleMenuRestore": "Repor valores padrão", "DE.Views.Toolbar.textStyleMenuRestoreAll": "Restore all to default styles", "DE.Views.Toolbar.textStyleMenuUpdate": "Atualizar com base na seleção", "DE.Views.Toolbar.textSubscript": "Subscrito", "DE.Views.Toolbar.textSuperscript": "Sobrescrito", "DE.Views.Toolbar.textSuppressForCurrentParagraph": "Suprimir para Parágrafo Atual", "DE.Views.Toolbar.textTabCollaboration": "Colaboração", "DE.Views.Toolbar.textTabFile": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textTabHome": "Base", "DE.Views.Toolbar.textTabInsert": "Inserir", "DE.Views.Toolbar.textTabLayout": "Disposição", "DE.Views.Toolbar.textTabLinks": "Referências", "DE.Views.Toolbar.textTabProtect": "Proteção", "DE.Views.Toolbar.textTabReview": "<PERSON>er", "DE.Views.Toolbar.textTabView": "Visualizar", "DE.Views.Toolbar.textTitleError": "Erro", "DE.Views.Toolbar.textToCurrent": "Para posição atual", "DE.Views.Toolbar.textTop": "Cima:", "DE.Views.Toolbar.textUnderline": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipAlignCenter": "Alinhar ao centro", "DE.Views.Toolbar.tipAlignJust": "Justificado", "DE.Views.Toolbar.tipAlignLeft": "Alinhar à esquerda", "DE.Views.Toolbar.tipAlignRight": "Alinhar à direita", "DE.Views.Toolbar.tipBack": "Voltar", "DE.Views.Toolbar.tipBlankPage": "Inserir página vazia", "DE.Views.Toolbar.tipChangeCase": "Alternar maiúscula/minúscula", "DE.Views.Toolbar.tipChangeChart": "Alterar tipo de gráfico", "DE.Views.Toolbar.tipClearStyle": "<PERSON><PERSON> estilo", "DE.Views.Toolbar.tipColorSchemas": "Alterar esquema de cor", "DE.Views.Toolbar.tipColumns": "Inserir colunas", "DE.Views.Toolbar.tipControls": "Adicionar controlos de conteúdo", "DE.Views.Toolbar.tipCopy": "Copiar", "DE.Views.Toolbar.tipCopyStyle": "<PERSON><PERSON><PERSON> est<PERSON>", "DE.Views.Toolbar.tipCut": "Cortar", "DE.Views.Toolbar.tipDateTime": "Insira a data e hora atuais", "DE.Views.Toolbar.tipDecFont": "Di<PERSON><PERSON>r tamanho do tipo de letra", "DE.Views.Toolbar.tipDecPrLeft": "Diminuir o Recuo", "DE.Views.Toolbar.tipDropCap": "Inserir letra capitular", "DE.Views.Toolbar.tipEditHeader": "Editar cabeçalho e rodapé", "DE.Views.Toolbar.tipFontColor": "Cor do tipo de letra", "DE.Views.Toolbar.tipFontName": "<PERSON><PERSON><PERSON> de letra", "DE.Views.Toolbar.tipFontSize": "Tamanho do tipo de letra", "DE.Views.Toolbar.tipHighlightColor": "<PERSON>r <PERSON>", "DE.Views.Toolbar.tipImgAlign": "<PERSON><PERSON><PERSON> ob<PERSON>", "DE.Views.Toolbar.tipImgGroup": "Agrupar objetos", "DE.Views.Toolbar.tipImgWrapping": "<PERSON><PERSON><PERSON> texto", "DE.Views.Toolbar.tipIncFont": "Aumentar tamanho do tipo de letra", "DE.Views.Toolbar.tipIncPrLeft": "Aumentar recuo", "DE.Views.Toolbar.tipInsertChart": "Inserir g<PERSON>", "DE.Views.Toolbar.tipInsertEquation": "Inserir equação", "DE.Views.Toolbar.tipInsertHorizontalText": "Inserir caixa de texto horizontal", "DE.Views.Toolbar.tipInsertImage": "Inserir imagem", "DE.Views.Toolbar.tipInsertNum": "Inserir número da página", "DE.Views.Toolbar.tipInsertShape": "Inserir forma automática", "DE.Views.Toolbar.tipInsertSymbol": "Inserir sí<PERSON>lo", "DE.Views.Toolbar.tipInsertTable": "<PERSON><PERSON><PERSON> tabela", "DE.Views.Toolbar.tipInsertText": "Inserir caixa de texto", "DE.Views.Toolbar.tipInsertTextArt": "Inserir arte de texto", "DE.Views.Toolbar.tipInsertVerticalText": "Inserir caixa de texto vertical", "DE.Views.Toolbar.tipLineNumbers": "Mostrar número das linhas", "DE.Views.Toolbar.tipLineSpace": "Espaçamento entre linhas do parágrafo", "DE.Views.Toolbar.tipMailRecepients": "Select Recepients", "DE.Views.Toolbar.tipMarkers": "Marcadores", "DE.Views.Toolbar.tipMarkersArrow": "Marcas em <PERSON>a", "DE.Views.Toolbar.tipMarkersCheckmark": "Marcas de verificação", "DE.Views.Toolbar.tipMarkersDash": "Marcadores de traços", "DE.Views.Toolbar.tipMarkersFRhombus": "Listas Rômbicas Preenchidas", "DE.Views.Toolbar.tipMarkersFRound": "Listas Redondas Preenchidas", "DE.Views.Toolbar.tipMarkersFSquare": "Listas Quadradas Preenchidas", "DE.Views.Toolbar.tipMarkersHRound": "Marcas de lista redondas vazias", "DE.Views.Toolbar.tipMarkersStar": "Marcas em estrela", "DE.Views.Toolbar.tipMultiLevelArticl": "Itens numerados de vários níveis", "DE.Views.Toolbar.tipMultiLevelChapter": "Capítulos numerados de vários níveis", "DE.Views.Toolbar.tipMultiLevelHeadings": "Títulos numerados de vários níveis", "DE.Views.Toolbar.tipMultiLevelHeadVarious": "Vários títulos numerados de vários níveis", "DE.Views.Toolbar.tipMultiLevelNumbered": "Listas Multiníveis Numeradas ", "DE.Views.Toolbar.tipMultilevels": "Lista multi-níveis", "DE.Views.Toolbar.tipMultiLevelSymbols": "Listas de Símbolos Multiníveis", "DE.Views.Toolbar.tipMultiLevelVarious": "Várias Listas Multiníveis Numeradas", "DE.Views.Toolbar.tipNumbers": "Numeração", "DE.Views.Toolbar.tipPageBreak": "Inserir página ou quebra de seção", "DE.Views.Toolbar.tipPageMargins": "Margens da página", "DE.Views.Toolbar.tipPageOrient": "Orientação da página", "DE.Views.Toolbar.tipPageSize": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipParagraphStyle": "Estilo do parágrafo", "DE.Views.Toolbar.tipPaste": "Colar", "DE.Views.Toolbar.tipPrColor": "Cor de fundo do parágrafo", "DE.Views.Toolbar.tipPrint": "Imprimir", "DE.Views.Toolbar.tipRedo": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipSave": "Guardar", "DE.Views.Toolbar.tipSaveCoauth": "<PERSON>e as suas alterações para que os outros utilizadores as possam ver.", "DE.Views.Toolbar.tipSelectAll": "Selecionar tudo", "DE.Views.Toolbar.tipSendBackward": "Enviar para trás", "DE.Views.Toolbar.tipSendForward": "Trazer para a frente", "DE.Views.Toolbar.tipShowHiddenChars": "Caracteres não imprimíveis", "DE.Views.Toolbar.tipSynchronize": "O documento foi alterado por outro utilizador. Clique para guardar as suas alterações e recarregar o documento.", "DE.Views.Toolbar.tipUndo": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipWatermark": "Editar marca d'água", "DE.Views.Toolbar.txtDistribHor": "Distribuir horizontalmente", "DE.Views.Toolbar.txtDistribVert": "Distribuir verticalmente", "DE.Views.Toolbar.txtMarginAlign": "<PERSON><PERSON><PERSON>rge<PERSON>", "DE.Views.Toolbar.txtObjectsAlign": "<PERSON><PERSON><PERSON> objetos selecionados", "DE.Views.Toolbar.txtPageAlign": "<PERSON><PERSON><PERSON>ágin<PERSON>", "DE.Views.Toolbar.txtScheme1": "Office", "DE.Views.Toolbar.txtScheme10": "Mediana", "DE.Views.Toolbar.txtScheme11": "Metro", "DE.Views.Toolbar.txtScheme12": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.txtScheme13": "Opulento", "DE.Views.Toolbar.txtScheme14": "Balcão envidraçado", "DE.Views.Toolbar.txtScheme15": "Origem", "DE.Views.Toolbar.txtScheme16": "Papel", "DE.Views.Toolbar.txtScheme17": "<PERSON>st<PERSON><PERSON>", "DE.Views.Toolbar.txtScheme18": "Técnica", "DE.Views.Toolbar.txtScheme19": "Viagem", "DE.Views.Toolbar.txtScheme2": "Escala de cinza", "DE.Views.Toolbar.txtScheme20": "Urbano", "DE.Views.Toolbar.txtScheme21": "Verve", "DE.Views.Toolbar.txtScheme22": "Novo Escritório", "DE.Views.Toolbar.txtScheme3": "Ápice", "DE.Views.Toolbar.txtScheme4": "Aspeto", "DE.Views.Toolbar.txtScheme5": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.txtScheme6": "Concurso", "DE.Views.Toolbar.txtScheme7": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.txtScheme8": "Fluxo", "DE.Views.Toolbar.txtScheme9": "Fundição", "DE.Views.ViewTab.textAlwaysShowToolbar": "Mostrar sempre a barra de ferramentas", "DE.Views.ViewTab.textDarkDocument": "Documento escuro", "DE.Views.ViewTab.textFitToPage": "Ajustar à página", "DE.Views.ViewTab.textFitToWidth": "Ajustar à Largura", "DE.Views.ViewTab.textInterfaceTheme": "<PERSON><PERSON>", "DE.Views.ViewTab.textLeftMenu": "<PERSON><PERSON>", "DE.Views.ViewTab.textNavigation": "Navegação", "DE.Views.ViewTab.textOutline": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ViewTab.textRulers": "Réguas", "DE.Views.ViewTab.textStatusBar": "Barra de estado", "DE.Views.ViewTab.textZoom": "Zoom", "DE.Views.ViewTab.tipDarkDocument": "Documento escuro", "DE.Views.ViewTab.tipFitToPage": "Ajustar à página", "DE.Views.ViewTab.tipFitToWidth": "Ajustar à Largura", "DE.Views.ViewTab.tipHeadings": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ViewTab.tipInterfaceTheme": "<PERSON><PERSON>", "DE.Views.WatermarkSettingsDialog.textAuto": "Automático", "DE.Views.WatermarkSettingsDialog.textBold": "Negrito", "DE.Views.WatermarkSettingsDialog.textColor": "Cor do texto", "DE.Views.WatermarkSettingsDialog.textDiagonal": "Diagonal", "DE.Views.WatermarkSettingsDialog.textFont": "<PERSON><PERSON><PERSON> de letra", "DE.Views.WatermarkSettingsDialog.textFromFile": "De um ficheiro", "DE.Views.WatermarkSettingsDialog.textFromStorage": "De um armazenamento", "DE.Views.WatermarkSettingsDialog.textFromUrl": "De um URL", "DE.Views.WatermarkSettingsDialog.textHor": "Horizontal", "DE.Views.WatermarkSettingsDialog.textImageW": "Marca d'água da imagem", "DE.Views.WatermarkSettingsDialog.textItalic": "Itálico", "DE.Views.WatermarkSettingsDialog.textLanguage": "Idioma", "DE.Views.WatermarkSettingsDialog.textLayout": "Disposição", "DE.Views.WatermarkSettingsDialog.textNone": "<PERSON><PERSON><PERSON>", "DE.Views.WatermarkSettingsDialog.textScale": "Redimensionar", "DE.Views.WatermarkSettingsDialog.textSelect": "Selecionar imagem", "DE.Views.WatermarkSettingsDialog.textStrikeout": "<PERSON><PERSON><PERSON>", "DE.Views.WatermarkSettingsDialog.textText": "Тexto", "DE.Views.WatermarkSettingsDialog.textTextW": "Marca d'água de texto", "DE.Views.WatermarkSettingsDialog.textTitle": "Definições de marcas d'água", "DE.Views.WatermarkSettingsDialog.textTransparency": "Semi-transparente", "DE.Views.WatermarkSettingsDialog.textUnderline": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.WatermarkSettingsDialog.tipFontName": "Nome do tipo de letra", "DE.Views.WatermarkSettingsDialog.tipFontSize": "Tamanho do tipo de letra"}