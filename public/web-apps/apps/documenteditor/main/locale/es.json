{"Common.Controllers.Chat.notcriticalErrorTitle": "Aviso", "Common.Controllers.Chat.textEnterMessage": "Introduzca su mensaje aquí", "Common.Controllers.ExternalDiagramEditor.textAnonymous": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalDiagramEditor.textClose": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalDiagramEditor.warningText": "El objeto está desactivado porque se está editando por otro usuario.", "Common.Controllers.ExternalDiagramEditor.warningTitle": "Aviso", "Common.Controllers.ExternalMergeEditor.textAnonymous": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalMergeEditor.textClose": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalMergeEditor.warningText": "El objeto está desactivado porque se está editando por otro usuario.", "Common.Controllers.ExternalMergeEditor.warningTitle": "Aviso", "Common.Controllers.ExternalOleEditor.textAnonymous": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalOleEditor.textClose": "<PERSON><PERSON><PERSON>", "Common.Controllers.ExternalOleEditor.warningText": "Objeto desactivado porque está siendo editado por otro usuario", "Common.Controllers.ExternalOleEditor.warningTitle": "Advertencia", "Common.Controllers.History.notcriticalErrorTitle": "Aviso", "Common.Controllers.ReviewChanges.textAcceptBeforeCompare": "A fin de comparar los documentos, se considerará que todos los cambios registrados en ellos han sido aceptados. ¿Quieres continuar?", "Common.Controllers.ReviewChanges.textAtLeast": "Por lo menos", "Common.Controllers.ReviewChanges.textAuto": "Auto", "Common.Controllers.ReviewChanges.textBaseline": "Baseline", "Common.Controllers.ReviewChanges.textBold": "Negrita", "Common.Controllers.ReviewChanges.textBreakBefore": "Salto de página antes", "Common.Controllers.ReviewChanges.textCaps": "<PERSON><PERSON><PERSON><PERSON>", "Common.Controllers.ReviewChanges.textCenter": "Alinear al centro", "Common.Controllers.ReviewChanges.textChar": "<PERSON><PERSON>", "Common.Controllers.ReviewChanges.textChart": "Gráfico", "Common.Controllers.ReviewChanges.textColor": "Color de letra", "Common.Controllers.ReviewChanges.textContextual": "No agregue intervalos entre párrafos del mismo estilo", "Common.Controllers.ReviewChanges.textDeleted": "<b>Eliminado:</b>", "Common.Controllers.ReviewChanges.textDStrikeout": "Double strikeout", "Common.Controllers.ReviewChanges.textEquation": "Ecuación", "Common.Controllers.ReviewChanges.textExact": "Exacto", "Common.Controllers.ReviewChanges.textFirstLine": "Primera línea", "Common.Controllers.ReviewChanges.textFontSize": "Tam<PERSON>ño de <PERSON>", "Common.Controllers.ReviewChanges.textFormatted": "Formateado", "Common.Controllers.ReviewChanges.textHighlight": "Color de resaltado", "Common.Controllers.ReviewChanges.textImage": "Imagen", "Common.Controllers.ReviewChanges.textIndentLeft": "<PERSON><PERSON><PERSON>", "Common.Controllers.ReviewChanges.textIndentRight": "<PERSON><PERSON><PERSON> der<PERSON>a", "Common.Controllers.ReviewChanges.textInserted": "<b>Insertado:</b>", "Common.Controllers.ReviewChanges.textItalic": "Cursiva", "Common.Controllers.ReviewChanges.textJustify": "Alinear justificado", "Common.Controllers.ReviewChanges.textKeepLines": "Mantener líneas juntas", "Common.Controllers.ReviewChanges.textKeepNext": "Conservar con el siguiente", "Common.Controllers.ReviewChanges.textLeft": "Alinear a la izquierda", "Common.Controllers.ReviewChanges.textLineSpacing": "Espaciado de línea: ", "Common.Controllers.ReviewChanges.textMultiple": "<PERSON><PERSON><PERSON><PERSON>", "Common.Controllers.ReviewChanges.textNoBreakBefore": "Sin salto de página antes", "Common.Controllers.ReviewChanges.textNoContextual": "Agregar intervalo entre párrafos del mismo estilo", "Common.Controllers.ReviewChanges.textNoKeepLines": "No mantener líneas juntas", "Common.Controllers.ReviewChanges.textNoKeepNext": "No mantener con el siguiente", "Common.Controllers.ReviewChanges.textNot": "No", "Common.Controllers.ReviewChanges.textNoWidow": "No widow control", "Common.Controllers.ReviewChanges.textNum": "Cambiar numeración", "Common.Controllers.ReviewChanges.textOff": "{0} ya no utiliza el seguimiento de cambios.", "Common.Controllers.ReviewChanges.textOffGlobal": "{0} ha deshabilitado el seguimiento de cambios para todos.", "Common.Controllers.ReviewChanges.textOn": "{0} está usando el seguimiento de cambios.", "Common.Controllers.ReviewChanges.textOnGlobal": "{0} ha habilitado el seguimiento de cambios para todos.", "Common.Controllers.ReviewChanges.textParaDeleted": "<b><PERSON><PERSON><PERSON><PERSON> eliminado</b>", "Common.Controllers.ReviewChanges.textParaFormatted": "Paragraph Formatted", "Common.Controllers.ReviewChanges.textParaInserted": "<b><PERSON><PERSON><PERSON><PERSON></b>", "Common.Controllers.ReviewChanges.textParaMoveFromDown": "<b>Bajado:</b>", "Common.Controllers.ReviewChanges.textParaMoveFromUp": "<b>Subido:</b>", "Common.Controllers.ReviewChanges.textParaMoveTo": "<b><PERSON><PERSON><PERSON>:</b>", "Common.Controllers.ReviewChanges.textPosition": "Posición", "Common.Controllers.ReviewChanges.textRight": "Alinear a la derecha", "Common.Controllers.ReviewChanges.textShape": "Forma", "Common.Controllers.ReviewChanges.textShd": "Color de fondo", "Common.Controllers.ReviewChanges.textShow": "Mostrar cambios a:", "Common.Controllers.ReviewChanges.textSmallCaps": "<PERSON><PERSON><PERSON><PERSON> pequeñas", "Common.Controllers.ReviewChanges.textSpacing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Controllers.ReviewChanges.textSpacingAfter": "Espac<PERSON><PERSON>", "Common.Controllers.ReviewChanges.textSpacingBefore": "Espac<PERSON><PERSON> antes", "Common.Controllers.ReviewChanges.textStrikeout": "<PERSON><PERSON><PERSON>", "Common.Controllers.ReviewChanges.textSubScript": "Subíndice", "Common.Controllers.ReviewChanges.textSuperScript": "Sobreíndice", "Common.Controllers.ReviewChanges.textTableChanged": "<b>Se ha cambiado la configuración de la tabla</b>", "Common.Controllers.ReviewChanges.textTableRowsAdd": "<b>Se han agregado filas a la tabla</b>", "Common.Controllers.ReviewChanges.textTableRowsDel": "<b>Se han eliminado filas de la tabla</b>", "Common.Controllers.ReviewChanges.textTabs": "Cambiar tabuladores", "Common.Controllers.ReviewChanges.textTitleComparison": "Ajustes de comparación", "Common.Controllers.ReviewChanges.textUnderline": "Subrayado", "Common.Controllers.ReviewChanges.textUrl": "Pegar la URL de documento", "Common.Controllers.ReviewChanges.textWidow": "Widow control", "Common.Controllers.ReviewChanges.textWord": "<PERSON>vel de palabra", "Common.define.chartData.textArea": "Á<PERSON>", "Common.define.chartData.textAreaStacked": "<PERSON><PERSON> a<PERSON>a", "Common.define.chartData.textAreaStackedPer": "Área apilada 100% ", "Common.define.chartData.textBar": "Barr<PERSON>", "Common.define.chartData.textBarNormal": "<PERSON>umna a<PERSON>", "Common.define.chartData.textBarNormal3d": "Columna 3D agrupada", "Common.define.chartData.textBarNormal3dPerspective": "Columna 3D", "Common.define.chartData.textBarStacked": "<PERSON><PERSON><PERSON> apilada", "Common.define.chartData.textBarStacked3d": "Columna 3D apilada", "Common.define.chartData.textBarStackedPer": "Columna apilada 100%", "Common.define.chartData.textBarStackedPer3d": "Columna 3D apilada 100%", "Common.define.chartData.textCharts": "Grá<PERSON><PERSON>", "Common.define.chartData.textColumn": "Gráfico de columnas", "Common.define.chartData.textCombo": "Combinado", "Common.define.chartData.textComboAreaBar": "<PERSON><PERSON> api<PERSON>a - Columna a<PERSON>", "Common.define.chartData.textComboBarLine": "<PERSON><PERSON>na a<PERSON> <PERSON><PERSON><PERSON>", "Common.define.chartData.textComboBarLineSecondary": "Columna agrupada - Línea en eje secundario", "Common.define.chartData.textComboCustom": "Combinación personalizada", "Common.define.chartData.textDoughnut": "<PERSON><PERSON>", "Common.define.chartData.textHBarNormal": "Barra agrupada", "Common.define.chartData.textHBarNormal3d": "Barra 3D agrupada", "Common.define.chartData.textHBarStacked": "Barra apilada", "Common.define.chartData.textHBarStacked3d": "Barra 3D apilada", "Common.define.chartData.textHBarStackedPer": "Barra apilada 100%", "Common.define.chartData.textHBarStackedPer3d": "Barra 3D apilada 100%", "Common.define.chartData.textLine": "Lín<PERSON>", "Common.define.chartData.textLine3d": "Línea 3D", "Common.define.chartData.textLineMarker": "Línea con marcadores", "Common.define.chartData.textLineStacked": "<PERSON><PERSON><PERSON> apilada", "Common.define.chartData.textLineStackedMarker": "Línea apilada con marcadores", "Common.define.chartData.textLineStackedPer": "Línea apilada 100%", "Common.define.chartData.textLineStackedPerMarker": "Línea apilada al 100% con marcadores ", "Common.define.chartData.textPie": "Gráfico circular", "Common.define.chartData.textPie3d": "Circular 3D", "Common.define.chartData.textPoint": "XY (Dispersión)", "Common.define.chartData.textScatter": "Dispersión", "Common.define.chartData.textScatterLine": "Dispersión con líneas rectas", "Common.define.chartData.textScatterLineMarker": "Dispersión con líneas rectas y marcadores", "Common.define.chartData.textScatterSmooth": "Dispersión con líneas suavizadas", "Common.define.chartData.textScatterSmoothMarker": "Dispersión con líneas suavizadas y marcadores", "Common.define.chartData.textStock": "De cotizaciones", "Common.define.chartData.textSurface": "Superficie", "Common.define.smartArt.textAccentedPicture": "Imagen destacada", "Common.define.smartArt.textAccentProcess": "Proceso destacado", "Common.define.smartArt.textAlternatingFlow": "Flujo alternativo", "Common.define.smartArt.textAlternatingHexagons": "Hexágonos alternados", "Common.define.smartArt.textAlternatingPictureBlocks": "Bloques de imágenes alternativos", "Common.define.smartArt.textAlternatingPictureCircles": "Círculos con imágenes alternativos", "Common.define.smartArt.textArchitectureLayout": "Diseño de arquitectura", "Common.define.smartArt.textArrowRibbon": "<PERSON>inta de flechas", "Common.define.smartArt.textAscendingPictureAccentProcess": "Proceso de imágenes destacadas ascendente", "Common.define.smartArt.textBalance": "<PERSON><PERSON>", "Common.define.smartArt.textBasicBendingProcess": "Proceso curvo básico", "Common.define.smartArt.textBasicBlockList": "Lista de bloques básica", "Common.define.smartArt.textBasicChevronProcess": "Proceso cheurón básico", "Common.define.smartArt.textBasicCycle": "Ciclo básico", "Common.define.smartArt.textBasicMatrix": "<PERSON><PERSON>", "Common.define.smartArt.textBasicPie": "Circular básico", "Common.define.smartArt.textBasicProcess": "Proceso básico", "Common.define.smartArt.textBasicPyramid": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "Common.define.smartArt.textBasicRadial": "Radial básico", "Common.define.smartArt.textBasicTarget": "Objetivo básico", "Common.define.smartArt.textBasicTimeline": "Escala de tiempo básica", "Common.define.smartArt.textBasicVenn": "<PERSON><PERSON>n b<PERSON><PERSON>", "Common.define.smartArt.textBendingPictureAccentList": "Lista destacada con círculos abajo", "Common.define.smartArt.textBendingPictureBlocks": "Bloques de imágenes con cuadro", "Common.define.smartArt.textBendingPictureCaption": "Imágenes con títulos", "Common.define.smartArt.textBendingPictureCaptionList": "Lista de títulos de imágenes", "Common.define.smartArt.textBendingPictureSemiTranparentText": "Imágenes con texto semitransparente", "Common.define.smartArt.textBlockCycle": "Ciclo de bloques", "Common.define.smartArt.textBubblePictureList": "Lista de imágenes con burbujas", "Common.define.smartArt.textCaptionedPictures": "Imágenes con títulos", "Common.define.smartArt.textChevronAccentProcess": "Proceso cheurón destacado", "Common.define.smartArt.textChevronList": "Lista de cheurones", "Common.define.smartArt.textCircleAccentTimeline": "Línea de tiempo con círculos", "Common.define.smartArt.textCircleArrowProcess": "Proceso de círculos con flecha", "Common.define.smartArt.textCirclePictureHierarchy": "Jerarquía con imágenes en círculos", "Common.define.smartArt.textCircleProcess": "Proceso de círculos", "Common.define.smartArt.textCircleRelationship": "Relación de círculo", "Common.define.smartArt.textCircularBendingProcess": "Proceso curvo circular", "Common.define.smartArt.textCircularPictureCallout": "Globo de imagen circular", "Common.define.smartArt.textClosedChevronProcess": "Proceso de cheurón cerrado", "Common.define.smartArt.textContinuousArrowProcess": "Proceso de flechas continuo", "Common.define.smartArt.textContinuousBlockProcess": "Proceso de bloque continuo", "Common.define.smartArt.textContinuousCycle": "<PERSON><PERSON><PERSON> continuo", "Common.define.smartArt.textContinuousPictureList": "Lista de imágenes continua", "Common.define.smartArt.textConvergingArrows": "<PERSON>le<PERSON><PERSON> convergentes", "Common.define.smartArt.textConvergingRadial": "Radial convergente", "Common.define.smartArt.textConvergingText": "Texto convergente", "Common.define.smartArt.textCounterbalanceArrows": "Flechas de contrapeso", "Common.define.smartArt.textCycle": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textCycleMatrix": "<PERSON><PERSON>", "Common.define.smartArt.textDescendingBlockList": "Lista de bloques descendente", "Common.define.smartArt.textDescendingProcess": "Proceso descendente", "Common.define.smartArt.textDetailedProcess": "Proceso detallado", "Common.define.smartArt.textDivergingArrows": "Flechas divergentes", "Common.define.smartArt.textDivergingRadial": "Radial divergente", "Common.define.smartArt.textEquation": "Ecuación", "Common.define.smartArt.textFramedTextPicture": "Imagen de texto enmarcado", "Common.define.smartArt.textFunnel": "Embudo", "Common.define.smartArt.textGear": "Engranaje", "Common.define.smartArt.textGridMatrix": "<PERSON><PERSON>", "Common.define.smartArt.textGroupedList": "Lista agrupada", "Common.define.smartArt.textHalfCircleOrganizationChart": "Organigrama con semicírculos", "Common.define.smartArt.textHexagonCluster": "Grupo de hexágonos", "Common.define.smartArt.textHexagonRadial": "Radial con hexágon<PERSON>", "Common.define.smartArt.textHierarchy": "Jerar<PERSON><PERSON><PERSON>", "Common.define.smartArt.textHierarchyList": "Lista de jerarquías", "Common.define.smartArt.textHorizontalBulletList": "Lista de viñetas horizontal", "Common.define.smartArt.textHorizontalHierarchy": "Jerarquía horizontal", "Common.define.smartArt.textHorizontalLabeledHierarchy": "Jerarquía etiquetada horizontal", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "Jerarquía horizontal de varios niveles", "Common.define.smartArt.textHorizontalOrganizationChart": "Organigrama horizontal", "Common.define.smartArt.textHorizontalPictureList": "Lista horizontal de imágenes", "Common.define.smartArt.textIncreasingArrowProcess": "Proceso de flechas crecientes", "Common.define.smartArt.textIncreasingCircleProcess": "Proceso de círculos crecientes", "Common.define.smartArt.textInterconnectedBlockProcess": "Bloque interconectado", "Common.define.smartArt.textInterconnectedRings": "Anillos interconectados", "Common.define.smartArt.textInvertedPyramid": "Pirámide invertida", "Common.define.smartArt.textLabeledHierarchy": "Jerarquía etiquetada", "Common.define.smartArt.textLinearVenn": "Venn lineal", "Common.define.smartArt.textLinedList": "Lista alineada", "Common.define.smartArt.textList": "Lista", "Common.define.smartArt.textMatrix": "<PERSON><PERSON>", "Common.define.smartArt.textMultidirectionalCycle": "Ciclo multidireccional", "Common.define.smartArt.textNameAndTitleOrganizationChart": "Organigrama con nombres y cargos", "Common.define.smartArt.textNestedTarget": "Objetivo anidado", "Common.define.smartArt.textNondirectionalCycle": "Ciclo sin dirección", "Common.define.smartArt.textOpposingArrows": "Flechas opuestas", "Common.define.smartArt.textOpposingIdeas": "Ideas opuestas", "Common.define.smartArt.textOrganizationChart": "Organigrama", "Common.define.smartArt.textOther": "<PERSON><PERSON>", "Common.define.smartArt.textPhasedProcess": "Proceso en fases", "Common.define.smartArt.textPicture": "Imagen", "Common.define.smartArt.textPictureAccentBlocks": "Imágenes destacadas en bloques", "Common.define.smartArt.textPictureAccentList": "Lista de imágenes destacadas", "Common.define.smartArt.textPictureAccentProcess": "Proceso de imágenes destacadas", "Common.define.smartArt.textPictureCaptionList": "Lista de títulos de imágenes", "Common.define.smartArt.textPictureFrame": "MarcoDeFotos", "Common.define.smartArt.textPictureGrid": "Imágenes en cuadrícula", "Common.define.smartArt.textPictureLineup": "Imágenes en paralelo", "Common.define.smartArt.textPictureOrganizationChart": "Organigrama con imágenes", "Common.define.smartArt.textPictureStrips": "Picture Strips", "Common.define.smartArt.textPieProcess": "Proceso circular", "Common.define.smartArt.textPlusAndMinus": "Más y menos", "Common.define.smartArt.textProcess": "Proceso", "Common.define.smartArt.textProcessArrows": "Flechas de proceso", "Common.define.smartArt.textProcessList": "Lista de procesos", "Common.define.smartArt.textPyramid": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textPyramidList": "Lista en pirámide", "Common.define.smartArt.textRadialCluster": "Diseño radial", "Common.define.smartArt.textRadialCycle": "Ciclo radial", "Common.define.smartArt.textRadialList": "Lista radial", "Common.define.smartArt.textRadialPictureList": "Lista radial con imágenes", "Common.define.smartArt.textRadialVenn": "Venn radial", "Common.define.smartArt.textRandomToResultProcess": "Proceso de azar a resultado", "Common.define.smartArt.textRelationship": "Relación", "Common.define.smartArt.textRepeatingBendingProcess": "Proceso curvo repetitivo", "Common.define.smartArt.textReverseList": "Lista inversa", "Common.define.smartArt.textSegmentedCycle": "Ciclo segmentado", "Common.define.smartArt.textSegmentedProcess": "Proceso segmentado", "Common.define.smartArt.textSegmentedPyramid": "Pirámide segmentada", "Common.define.smartArt.textSnapshotPictureList": "Lista de imágenes instantáneas", "Common.define.smartArt.textSpiralPicture": "Imagen en espiral", "Common.define.smartArt.textSquareAccentList": "Lista de imágenes con cuadrados", "Common.define.smartArt.textStackedList": "Lista apilada", "Common.define.smartArt.textStackedVenn": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textStaggeredProcess": "Proceso escalonado", "Common.define.smartArt.textStepDownProcess": "Proceso de nivel inferior", "Common.define.smartArt.textStepUpProcess": "Proceso de nivel superior", "Common.define.smartArt.textSubStepProcess": "Proceso de pasos secundarios", "Common.define.smartArt.textTabbedArc": "Arco con pestañas", "Common.define.smartArt.textTableHierarchy": "Jerarquía de tabla", "Common.define.smartArt.textTableList": "Lista de tablas", "Common.define.smartArt.textTabList": "Lista de pestañas", "Common.define.smartArt.textTargetList": "Lista de objetivo", "Common.define.smartArt.textTextCycle": "Ciclo de texto", "Common.define.smartArt.textThemePictureAccent": "Imágenes temáticas destacadas", "Common.define.smartArt.textThemePictureAlternatingAccent": "Imágenes temáticas destacadas alternativas", "Common.define.smartArt.textThemePictureGrid": "Imágenes temáticas en cuadrícula", "Common.define.smartArt.textTitledMatrix": "<PERSON><PERSON> con tí<PERSON>", "Common.define.smartArt.textTitledPictureAccentList": "Lista de imágenes destacadas con título", "Common.define.smartArt.textTitledPictureBlocks": "Bloques de imágenes con títulos", "Common.define.smartArt.textTitlePictureLineup": "Serie de imágenes con título", "Common.define.smartArt.textTrapezoidList": "Lista de trapezoides", "Common.define.smartArt.textUpwardArrow": "Flecha arriba", "Common.define.smartArt.textVaryingWidthList": "Lista de ancho variable", "Common.define.smartArt.textVerticalAccentList": "Lista con rectángulos en vertical", "Common.define.smartArt.textVerticalArrowList": "Lista vertical de flechas", "Common.define.smartArt.textVerticalBendingProcess": "Proceso curvo vertical", "Common.define.smartArt.textVerticalBlockList": "Lista de bloques verticales", "Common.define.smartArt.textVerticalBoxList": "Lista vertical de cuadros", "Common.define.smartArt.textVerticalBracketList": "Lista vertical con corchetes", "Common.define.smartArt.textVerticalBulletList": "Lista vertical de viñetas", "Common.define.smartArt.textVerticalChevronList": "Lista vertical de cheurones", "Common.define.smartArt.textVerticalCircleList": "Lista con círculos en vertical", "Common.define.smartArt.textVerticalCurvedList": "Lista curvada vertical", "Common.define.smartArt.textVerticalEquation": "Ecuación vertical", "Common.define.smartArt.textVerticalPictureAccentList": "Lista con círculos a la izquierda", "Common.define.smartArt.textVerticalPictureList": "Lista vertical de imágenes", "Common.define.smartArt.textVerticalProcess": "Proceso vertical", "Common.Translation.textMoreButton": "Más", "Common.Translation.tipFileLocked": "El documento está bloqueado para su edición. Puede hacer cambios y guardarlo como copia local más tarde.", "Common.Translation.tipFileReadOnly": "El archivo es de solo lectura. Para no perder los cambios, guarde el archivo con otro nombre o en otra ubicación.", "Common.Translation.warnFileLocked": "No puede editar este archivo porque está siendo editado en otra aplicación.", "Common.Translation.warnFileLockedBtnEdit": "<PERSON><PERSON>r una copia", "Common.Translation.warnFileLockedBtnView": "Abrir para visualizar", "Common.UI.ButtonColored.textAutoColor": "Automático", "Common.UI.ButtonColored.textNewColor": "Color personalizado", "Common.UI.Calendar.textApril": "Abril", "Common.UI.Calendar.textAugust": "Agosto", "Common.UI.Calendar.textDecember": "Diciembre", "Common.UI.Calendar.textFebruary": "<PERSON><PERSON><PERSON>", "Common.UI.Calendar.textJanuary": "<PERSON><PERSON>", "Common.UI.Calendar.textJuly": "<PERSON>", "Common.UI.Calendar.textJune": "<PERSON><PERSON>", "Common.UI.Calendar.textMarch": "marzo", "Common.UI.Calendar.textMay": "mayo", "Common.UI.Calendar.textMonths": "meses", "Common.UI.Calendar.textNovember": "noviembre", "Common.UI.Calendar.textOctober": "octubre", "Common.UI.Calendar.textSeptember": "septiembre", "Common.UI.Calendar.textShortApril": "Abr", "Common.UI.Calendar.textShortAugust": "Ago", "Common.UI.Calendar.textShortDecember": "Dic.", "Common.UI.Calendar.textShortFebruary": "Feb.", "Common.UI.Calendar.textShortFriday": "Vie.", "Common.UI.Calendar.textShortJanuary": "Jan.", "Common.UI.Calendar.textShortJuly": "Jul.", "Common.UI.Calendar.textShortJune": "Jun.", "Common.UI.Calendar.textShortMarch": "mar.", "Common.UI.Calendar.textShortMay": "mayo", "Common.UI.Calendar.textShortMonday": "lu.", "Common.UI.Calendar.textShortNovember": "nov.", "Common.UI.Calendar.textShortOctober": "oct.", "Common.UI.Calendar.textShortSaturday": "sá.", "Common.UI.Calendar.textShortSeptember": "sept.", "Common.UI.Calendar.textShortSunday": "do.", "Common.UI.Calendar.textShortThursday": "ju.", "Common.UI.Calendar.textShortTuesday": "ma.", "Common.UI.Calendar.textShortWednesday": "mi.", "Common.UI.Calendar.textYears": "<PERSON><PERSON><PERSON>", "Common.UI.ComboBorderSize.txtNoBorders": "<PERSON> bordes", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "<PERSON> bordes", "Common.UI.ComboDataView.emptyComboText": "Sin estilo", "Common.UI.ExtendedColorDialog.addButtonText": "Agregar", "Common.UI.ExtendedColorDialog.textCurrent": "Actual", "Common.UI.ExtendedColorDialog.textHexErr": "El valor introducido es incorrecto.<br>Por favor, introduzca un valor de 000000 a FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "Nuevo", "Common.UI.ExtendedColorDialog.textRGBErr": "El valor introducido es incorrecto.<br><PERSON>r favor, introduzca un valor numérico de 0 a 225.", "Common.UI.HSBColorPicker.textNoColor": "Sin color", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "Ocultar la contraseña", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "Mostrar la contraseña", "Common.UI.SearchBar.textFind": "Buscar", "Common.UI.SearchBar.tipCloseSearch": "<PERSON><PERSON><PERSON>", "Common.UI.SearchBar.tipNextResult": "Resul<PERSON><PERSON> si<PERSON>", "Common.UI.SearchBar.tipOpenAdvancedSettings": "Abrir los ajustes avanzados", "Common.UI.SearchBar.tipPreviousResult": "Resultado anterior", "Common.UI.SearchDialog.textHighlight": "Resaltar resultados", "Common.UI.SearchDialog.textMatchCase": "Sensible a mayúsculas y minúsculas", "Common.UI.SearchDialog.textReplaceDef": "Introduzca el texto de sustitución", "Common.UI.SearchDialog.textSearchStart": "Introduzca su texto aquí", "Common.UI.SearchDialog.textTitle": "Buscar y reemplazar", "Common.UI.SearchDialog.textTitle2": "Encontrar", "Common.UI.SearchDialog.textWholeWords": "<PERSON><PERSON><PERSON> palabras completas", "Common.UI.SearchDialog.txtBtnHideReplace": "Esconder Sustitución", "Common.UI.SearchDialog.txtBtnReplace": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.txtBtnReplaceAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> todo", "Common.UI.SynchronizeTip.textDontShow": "No volver a mostrar este mensaje", "Common.UI.SynchronizeTip.textSynchronize": "El documento ha sido cambiado por otro usuario.<br>Por favor haga clic para guardar sus cambios y recargue las actualizaciones.", "Common.UI.ThemeColorPalette.textRecentColors": "Colores recientes", "Common.UI.ThemeColorPalette.textStandartColors": "Colores estándar", "Common.UI.ThemeColorPalette.textThemeColors": "Colores de tema", "Common.UI.Themes.txtThemeClassicLight": "Clásico claro", "Common.UI.Themes.txtThemeContrastDark": "Contraste oscuro", "Common.UI.Themes.txtThemeDark": "Oscuro", "Common.UI.Themes.txtThemeLight": "<PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeSystem": "Igual que el sistema", "Common.UI.Window.cancelButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.Window.closeButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.Window.noButtonText": "No", "Common.UI.Window.okButtonText": "Aceptar", "Common.UI.Window.textConfirmation": "Confirmación", "Common.UI.Window.textDontShow": "No volver a mostrar este mensaje", "Common.UI.Window.textError": "Error", "Common.UI.Window.textInformation": "Información", "Common.UI.Window.textWarning": "Aviso", "Common.UI.Window.yesButtonText": "Sí", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textCtrl": "Control", "Common.Utils.String.textShift": "Shift", "Common.Views.About.txtAddress": "dirección: ", "Common.Views.About.txtLicensee": "LICENCIATARIO ", "Common.Views.About.txtLicensor": "LICENCIANTE", "Common.Views.About.txtMail": "correo: ", "Common.Views.About.txtPoweredBy": "Desarrollado por", "Common.Views.About.txtTel": "tel.: ", "Common.Views.About.txtVersion": "Versión ", "Common.Views.AutoCorrectDialog.textAdd": "Agregar", "Common.Views.AutoCorrectDialog.textApplyText": "Aplicar mientras escribe", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Autocorrección de texto", "Common.Views.AutoCorrectDialog.textAutoFormat": "Autoformato mientras escribe", "Common.Views.AutoCorrectDialog.textBulleted": "Listas con viñetas automáticas", "Common.Views.AutoCorrectDialog.textBy": "Por", "Common.Views.AutoCorrectDialog.textDelete": "Eliminar", "Common.Views.AutoCorrectDialog.textDoubleSpaces": "<PERSON><PERSON><PERSON> punto con doble espacio", "Common.Views.AutoCorrectDialog.textFLCells": "Poner en mayúsculas la primera letra de las celdas de la tabla", "Common.Views.AutoCorrectDialog.textFLSentence": "Poner en mayúscula la primera letra de una oración", "Common.Views.AutoCorrectDialog.textHyperlink": "Rutas de red e Internet por hipervínculos", "Common.Views.AutoCorrectDialog.textHyphens": "<PERSON><PERSON><PERSON> (--) con gui<PERSON> (—)", "Common.Views.AutoCorrectDialog.textMathCorrect": "Autocorrección matemática", "Common.Views.AutoCorrectDialog.textNumbered": "Listas con numeración automática", "Common.Views.AutoCorrectDialog.textQuotes": "\"Comillas rectas\" con \"comillas tipográficas\"", "Common.Views.AutoCorrectDialog.textRecognized": "Funciones reconocidas", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "Las siguientes expresiones son expresiones matemáticas reconocidas. No se pondrán en cursiva automáticamente.", "Common.Views.AutoCorrectDialog.textReplace": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textReplaceText": "<PERSON><PERSON><PERSON><PERSON><PERSON> escribe", "Common.Views.AutoCorrectDialog.textReplaceType": "Reemplazar texto mientras escribe", "Common.Views.AutoCorrectDialog.textReset": "Restablecer", "Common.Views.AutoCorrectDialog.textResetAll": "Rees<PERSON>cer a valor predeterminado", "Common.Views.AutoCorrectDialog.textRestore": "Restaurar", "Common.Views.AutoCorrectDialog.textTitle": "Autocorrección", "Common.Views.AutoCorrectDialog.textWarnAddRec": "Las funciones reconocidas deben contener solo letras de la A a la Z, mayúsculas o minúsculas.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "Cualquier expresión que haya agregado se eliminará y las eliminadas se restaurarán. ¿Desea continuar?", "Common.Views.AutoCorrectDialog.warnReplace": "La entrada de autocorreción para %1 ya existe. ¿Desea reemplazarla?", "Common.Views.AutoCorrectDialog.warnReset": "Las autocorrecciones que haya agregado se eliminarán y las modificadas recuperarán sus valores originales. ¿Desea continuar?", "Common.Views.AutoCorrectDialog.warnRestore": "La entrada de autocorrección para %1 será restablecida a su valor original. ¿Desea continuar?", "Common.Views.Chat.textSend": "Enviar", "Common.Views.Comments.mniAuthorAsc": "Autor de A a Z", "Common.Views.Comments.mniAuthorDesc": "Autor de Z a A", "Common.Views.Comments.mniDateAsc": "<PERSON>ás antiguo", "Common.Views.Comments.mniDateDesc": "Más reciente", "Common.Views.Comments.mniFilterGroups": "Filtrar por grupo", "Common.Views.Comments.mniPositionAsc": "<PERSON><PERSON>", "Common.Views.Comments.mniPositionDesc": "<PERSON><PERSON>", "Common.Views.Comments.textAdd": "Agregar", "Common.Views.Comments.textAddComment": "Agregar comentario", "Common.Views.Comments.textAddCommentToDoc": "Agregar comentario al documento", "Common.Views.Comments.textAddReply": "Agregar respuesta", "Common.Views.Comments.textAll": "Todo", "Common.Views.Comments.textAnonym": "Visitante", "Common.Views.Comments.textCancel": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textClosePanel": "<PERSON><PERSON>r comentarios", "Common.Views.Comments.textComments": "Comentarios", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "Introduzca su comentario aquí", "Common.Views.Comments.textHintAddComment": "Agregar comentario", "Common.Views.Comments.textOpenAgain": "<PERSON><PERSON>r de nuevo", "Common.Views.Comments.textReply": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolve": "Resolver", "Common.Views.Comments.textResolved": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textSort": "Ordenar comentarios", "Common.Views.Comments.textViewResolved": "No tiene permiso para volver a abrir el documento", "Common.Views.Comments.txtEmpty": "Sin comentarios en el documento", "Common.Views.CopyWarningDialog.textDontShow": "No volver a mostrar este mensaje", "Common.Views.CopyWarningDialog.textMsg": "Se puede realizar las acciones de copiar, cortar y pegar usando los botones en la barra de herramientas y el menú contextual sólo en esta pestaña del editor.<br><br>Si quiere copiar o pegar algo fuera de esta pestaña, usa las combinaciones de teclas siguientes:", "Common.Views.CopyWarningDialog.textTitle": "Acciones de Copiar, Cortar y Pegar", "Common.Views.CopyWarningDialog.textToCopy": "para copiar", "Common.Views.CopyWarningDialog.textToCut": "para cortar", "Common.Views.CopyWarningDialog.textToPaste": "para pegar", "Common.Views.DocumentAccessDialog.textLoading": "Cargando...", "Common.Views.DocumentAccessDialog.textTitle": "Ajustes de uso compartido", "Common.Views.ExternalDiagramEditor.textTitle": "Editor de gráfico", "Common.Views.ExternalEditor.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.ExternalEditor.textSave": "Guardar y salir", "Common.Views.ExternalMergeEditor.textTitle": "Receptores de Fusión de Correo", "Common.Views.ExternalOleEditor.textTitle": "Editor de hojas de cálculo", "Common.Views.Header.labelCoUsersDescr": "Usuarios que están editando el archivo:", "Common.Views.Header.textAddFavorite": "Marcar como favorito", "Common.Views.Header.textAdvSettings": "Configuración avanzada", "Common.Views.Header.textBack": "Abrir ubicación del archivo", "Common.Views.Header.textCompactView": "Esconder barra de herramientas", "Common.Views.Header.textHideLines": "<PERSON><PERSON><PERSON><PERSON> reglas", "Common.Views.Header.textHideStatusBar": "Ocultar barra de estado", "Common.Views.Header.textReadOnly": "Sólo lectura", "Common.Views.Header.textRemoveFavorite": "Eliminar de Favoritos", "Common.Views.Header.textShare": "Compartir", "Common.Views.Header.textZoom": "Ampliación", "Common.Views.Header.tipAccessRights": "Gestionar derechos de acceso al documento", "Common.Views.Header.tipDownload": "Descargar archivo", "Common.Views.Header.tipGoEdit": "Editar archivo actual", "Common.Views.Header.tipPrint": "Imprimir archivo", "Common.Views.Header.tipPrintQuick": "Impresión rápida", "Common.Views.Header.tipRedo": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipSave": "Guardar", "Common.Views.Header.tipSearch": "Búsqueda", "Common.Views.Header.tipUndo": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipUsers": "Ver usuarios", "Common.Views.Header.tipViewSettings": "<PERSON><PERSON> a<PERSON>", "Common.Views.Header.tipViewUsers": "Ver usuarios y administrar derechos de acceso al documento", "Common.Views.Header.txtAccessRights": "Cambiar derechos de acceso", "Common.Views.Header.txtRename": "Renombrar", "Common.Views.History.textCloseHistory": "<PERSON><PERSON><PERSON> historial", "Common.Views.History.textHide": "Plegar", "Common.Views.History.textHideAll": "Ocultar cambios detallados", "Common.Views.History.textRestore": "Restaurar", "Common.Views.History.textShow": "<PERSON><PERSON><PERSON>", "Common.Views.History.textShowAll": "Mostrar cambios detallados", "Common.Views.History.textVer": "ver.", "Common.Views.ImageFromUrlDialog.textUrl": "Pegar URL de imagen:", "Common.Views.ImageFromUrlDialog.txtEmpty": "Este campo es obligatorio", "Common.Views.ImageFromUrlDialog.txtNotUrl": "El campo debe ser URL en el formato \"http://www.example.com\"", "Common.Views.InsertTableDialog.textInvalidRowsCols": "Debe especificar número válido de filas y columnas", "Common.Views.InsertTableDialog.txtColumns": "Número de columnas", "Common.Views.InsertTableDialog.txtMaxText": "El valor máximo para este campo es{0}.", "Common.Views.InsertTableDialog.txtMinText": "El valor mínimo para este campo es {0}.", "Common.Views.InsertTableDialog.txtRows": "Número <PERSON>", "Common.Views.InsertTableDialog.txtTitle": "Tamaño de tabla", "Common.Views.InsertTableDialog.txtTitleSplit": "<PERSON><PERSON><PERSON> celda", "Common.Views.LanguageDialog.labelSelect": "Seleccionar el idioma de documento", "Common.Views.OpenDialog.closeButtonText": "Cerrar archivo", "Common.Views.OpenDialog.txtEncoding": "Codificación", "Common.Views.OpenDialog.txtIncorrectPwd": "La contraseña es incorrecta", "Common.Views.OpenDialog.txtOpenFile": "Escribir la contraseña para abrir el archivo", "Common.Views.OpenDialog.txtPassword": "Contraseña", "Common.Views.OpenDialog.txtPreview": "Vista previa", "Common.Views.OpenDialog.txtProtected": "Una vez que se ha introducido la contraseña y abierto el archivo, la contraseña actual al archivo se restablecerá", "Common.Views.OpenDialog.txtTitle": "Elegir opciones de %1", "Common.Views.OpenDialog.txtTitleProtected": "Archivo protegido", "Common.Views.PasswordDialog.txtDescription": "Establezca una contraseña para proteger", "Common.Views.PasswordDialog.txtIncorrectPwd": "La contraseña de confirmación es", "Common.Views.PasswordDialog.txtPassword": "Contraseña", "Common.Views.PasswordDialog.txtRepeat": "Repita la contraseña", "Common.Views.PasswordDialog.txtTitle": "<PERSON><PERSON><PERSON> contras<PERSON>", "Common.Views.PasswordDialog.txtWarning": "Precaución: Si pierde u olvida su contraseña, no podrá recuperarla. Guárdalo en un lugar seguro.", "Common.Views.PluginDlg.textLoading": "Cargando", "Common.Views.Plugins.groupCaption": "Extensiones", "Common.Views.Plugins.strPlugins": "Plugins", "Common.Views.Plugins.textClosePanel": "Cerrar plugin", "Common.Views.Plugins.textLoading": "Cargando", "Common.Views.Plugins.textStart": "Iniciar", "Common.Views.Plugins.textStop": "Detener", "Common.Views.Protection.hintAddPwd": "Encriptar con contraseña", "Common.Views.Protection.hintDelPwd": "Eliminar contraseña", "Common.Views.Protection.hintPwd": "Cambie o elimine la contraseña", "Common.Views.Protection.hintSignature": "Agregar firma digital o línea de firma", "Common.Views.Protection.txtAddPwd": "Agregar contraseña", "Common.Views.Protection.txtChangePwd": "Cambie la contraseña", "Common.Views.Protection.txtDeletePwd": "Eliminar contraseña", "Common.Views.Protection.txtEncrypt": "Encriptar", "Common.Views.Protection.txtInvisibleSignature": "Agregar firma digital", "Common.Views.Protection.txtSignature": "Firma", "Common.Views.Protection.txtSignatureLine": "Agregar línea de firma", "Common.Views.RenameDialog.textName": "Nombre de archivo", "Common.Views.RenameDialog.txtInvalidName": "El nombre de archivo no debe contener los símbolos siguientes:", "Common.Views.ReviewChanges.hintNext": "Al siguiente cambio", "Common.Views.ReviewChanges.hintPrev": "Al cambio anterior", "Common.Views.ReviewChanges.mniFromFile": "Documento del archivo", "Common.Views.ReviewChanges.mniFromStorage": "Documento de almacenamiento", "Common.Views.ReviewChanges.mniFromUrl": "Documento de la URL", "Common.Views.ReviewChanges.mniSettings": "Ajustes de comparación", "Common.Views.ReviewChanges.strFast": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.strFastDesc": "Co-edición a tiempo real. Todos", "Common.Views.ReviewChanges.strStrict": "Estricto", "Common.Views.ReviewChanges.strStrictDesc": "Use el botón \"Guardar\" para", "Common.Views.ReviewChanges.textEnable": "Habilitar", "Common.Views.ReviewChanges.textWarnTrackChanges": "El seguimiento de cambios se activará para todos los usuarios con acceso total. La próxima vez que alguien abra el documento, el seguimiento de cambios seguirá activado.", "Common.Views.ReviewChanges.textWarnTrackChangesTitle": "¿Habilitar el seguimiento de cambios para todos?", "Common.Views.ReviewChanges.tipAcceptCurrent": "Aceptar cambio actual", "Common.Views.ReviewChanges.tipCoAuthMode": "Establezca el modo de co-edición", "Common.Views.ReviewChanges.tipCommentRem": "Eliminar comentarios", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Eliminar comentarios actuales", "Common.Views.ReviewChanges.tipCommentResolve": "Resolver comentarios", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Resolver los comentarios actuales", "Common.Views.ReviewChanges.tipCompare": "Comparar el documento actual con otro", "Common.Views.ReviewChanges.tipHistory": "Mostrar historial de versiones", "Common.Views.ReviewChanges.tipRejectCurrent": "<PERSON><PERSON><PERSON> camb<PERSON>", "Common.Views.ReviewChanges.tipReview": "Seguimiento a cambios", "Common.Views.ReviewChanges.tipReviewView": "Seleccionar el modo en el que quiere que se presenten los cambios", "Common.Views.ReviewChanges.tipSetDocLang": "Establecer el idioma de documento", "Common.Views.ReviewChanges.tipSetSpelling": "Сorrección ortográfica", "Common.Views.ReviewChanges.tipSharing": "Gestionar derechos de acceso al documento", "Common.Views.ReviewChanges.txtAccept": "Aceptar", "Common.Views.ReviewChanges.txtAcceptAll": "Aceptar todos los cambios", "Common.Views.ReviewChanges.txtAcceptChanges": "Aceptar cambios", "Common.Views.ReviewChanges.txtAcceptCurrent": "Aceptar cambio actual", "Common.Views.ReviewChanges.txtChat": "Cha<PERSON>", "Common.Views.ReviewChanges.txtClose": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCoAuthMode": "Modo de co-edición", "Common.Views.ReviewChanges.txtCommentRemAll": "Eliminar todos los comentarios", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Eliminar comentarios actuales", "Common.Views.ReviewChanges.txtCommentRemMy": "Eliminar mis comentarios", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Eliminar mis actuales comentarios", "Common.Views.ReviewChanges.txtCommentRemove": "Eliminar", "Common.Views.ReviewChanges.txtCommentResolve": "Resolver", "Common.Views.ReviewChanges.txtCommentResolveAll": "Resolver todos los comentarios", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Resolver comentarios actuales", "Common.Views.ReviewChanges.txtCommentResolveMy": "Resolver mis comentarios", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Resolver mis comentarios actuales", "Common.Views.ReviewChanges.txtCompare": "Comparar", "Common.Views.ReviewChanges.txtDocLang": "Idioma", "Common.Views.ReviewChanges.txtEditing": "Edición", "Common.Views.ReviewChanges.txtFinal": "Todos los cambio aceptados {0}", "Common.Views.ReviewChanges.txtFinalCap": "Final", "Common.Views.ReviewChanges.txtHistory": "Historial de versiones", "Common.Views.ReviewChanges.txtMarkup": "Todos los cambios {0}", "Common.Views.ReviewChanges.txtMarkupCap": "Revisiones y globos", "Common.Views.ReviewChanges.txtMarkupSimple": "Todos los cambios {0}<br>Sin globos", "Common.Views.ReviewChanges.txtMarkupSimpleCap": "Sólo revisiones", "Common.Views.ReviewChanges.txtNext": "Al siguiente cambio", "Common.Views.ReviewChanges.txtOff": "Desactivar para mí", "Common.Views.ReviewChanges.txtOffGlobal": "Desactivar para mí y para todos", "Common.Views.ReviewChanges.txtOn": "Activar para mí", "Common.Views.ReviewChanges.txtOnGlobal": "Activar para mí y para todos", "Common.Views.ReviewChanges.txtOriginal": "Todos los cambios rechazados {0}", "Common.Views.ReviewChanges.txtOriginalCap": "Original", "Common.Views.ReviewChanges.txtPrev": "Al cambio anterior", "Common.Views.ReviewChanges.txtPreview": "Vista previa", "Common.Views.ReviewChanges.txtReject": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectAll": "<PERSON><PERSON><PERSON> todos los cambios", "Common.Views.ReviewChanges.txtRejectChanges": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectCurrent": "<PERSON><PERSON><PERSON> Actual", "Common.Views.ReviewChanges.txtSharing": "Compartir", "Common.Views.ReviewChanges.txtSpelling": "Сorrección ortográfica", "Common.Views.ReviewChanges.txtTurnon": "Seguimiento a cambios", "Common.Views.ReviewChanges.txtView": "Modo de visualización", "Common.Views.ReviewChangesDialog.textTitle": "Revisar camb<PERSON>", "Common.Views.ReviewChangesDialog.txtAccept": "Aceptar", "Common.Views.ReviewChangesDialog.txtAcceptAll": "Aceptar todos los cambios", "Common.Views.ReviewChangesDialog.txtAcceptCurrent": "Aceptar cambio actual", "Common.Views.ReviewChangesDialog.txtNext": "Al siguiente cambio", "Common.Views.ReviewChangesDialog.txtPrev": "Al cambio anterior", "Common.Views.ReviewChangesDialog.txtReject": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChangesDialog.txtRejectAll": "<PERSON><PERSON><PERSON> todos los cambios", "Common.Views.ReviewChangesDialog.txtRejectCurrent": "<PERSON><PERSON><PERSON> camb<PERSON>", "Common.Views.ReviewPopover.textAdd": "Agregar", "Common.Views.ReviewPopover.textAddReply": "Agregar respuesta", "Common.Views.ReviewPopover.textCancel": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textEnterComment": "Introduzca su comentario aquí", "Common.Views.ReviewPopover.textFollowMove": "<PERSON><PERSON><PERSON>ov<PERSON>", "Common.Views.ReviewPopover.textMention": "+mención proporcionará acceso al documento y enviará un correo", "Common.Views.ReviewPopover.textMentionNotify": "+mención notificará al usuario por correo", "Common.Views.ReviewPopover.textOpenAgain": "<PERSON><PERSON>r de nuevo", "Common.Views.ReviewPopover.textReply": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textResolve": "Resolver", "Common.Views.ReviewPopover.textViewResolved": "No tiene permiso para volver a abrir el documento", "Common.Views.ReviewPopover.txtAccept": "Aceptar", "Common.Views.ReviewPopover.txtDeleteTip": "Eliminar", "Common.Views.ReviewPopover.txtEditTip": "<PERSON><PERSON>", "Common.Views.ReviewPopover.txtReject": "<PERSON><PERSON><PERSON>", "Common.Views.SaveAsDlg.textLoading": "Cargando", "Common.Views.SaveAsDlg.textTitle": "Carpeta para guardar", "Common.Views.SearchPanel.textCaseSensitive": "<PERSON><PERSON><PERSON><PERSON> <PERSON> de minúsculas", "Common.Views.SearchPanel.textCloseSearch": "<PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textContentChanged": "Se ha cambiado el documento", "Common.Views.SearchPanel.textFind": "Buscar", "Common.Views.SearchPanel.textFindAndReplace": "Buscar y reemplazar", "Common.Views.SearchPanel.textMatchUsingRegExp": "Coincidir utilizando expresiones regulares", "Common.Views.SearchPanel.textNoMatches": "No hay coincidencias", "Common.Views.SearchPanel.textNoSearchResults": "No hay resultados de búsqueda", "Common.Views.SearchPanel.textReplace": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textReplaceAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> todo", "Common.Views.SearchPanel.textReplaceWith": "<PERSON>em<PERSON><PERSON><PERSON> por", "Common.Views.SearchPanel.textSearchAgain": "{0}Realiza nueva búsqueda{1} para obtener resultados precisos.", "Common.Views.SearchPanel.textSearchHasStopped": "La búsqueda se ha detenido", "Common.Views.SearchPanel.textSearchResults": "Resultados de búsqueda: {0}/{1}", "Common.Views.SearchPanel.textTooManyResults": "Hay demasiados resultados para mostrar aquí", "Common.Views.SearchPanel.textWholeWords": "<PERSON><PERSON><PERSON> palabras completas", "Common.Views.SearchPanel.tipNextResult": "Resul<PERSON><PERSON> si<PERSON>", "Common.Views.SearchPanel.tipPreviousResult": "Resultado anterior", "Common.Views.SelectFileDlg.textLoading": "Cargando", "Common.Views.SelectFileDlg.textTitle": "Seleccionar origen de datos", "Common.Views.SignDialog.textBold": "Negrilla", "Common.Views.SignDialog.textCertificate": "Certificar", "Common.Views.SignDialog.textChange": "Cambiar", "Common.Views.SignDialog.textInputName": "Ingresar nombre de quien firma", "Common.Views.SignDialog.textItalic": "Itálica", "Common.Views.SignDialog.textNameError": "El nombre del firmante no debe estar vacío.", "Common.Views.SignDialog.textPurpose": "Propósito al firmar este documento", "Common.Views.SignDialog.textSelect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textSelectImage": "Seleccionar imagen", "Common.Views.SignDialog.textSignature": "La firma se ve como", "Common.Views.SignDialog.textTitle": "Firmar documento", "Common.Views.SignDialog.textUseImage": "o pulsar 'Seleccionar Imagen' para usar una imagen como firma", "Common.Views.SignDialog.textValid": "Válido desde %1 hasta %2", "Common.Views.SignDialog.tipFontName": "Nombre del tipo de letra", "Common.Views.SignDialog.tipFontSize": "Tamaño del tipo de letra", "Common.Views.SignSettingsDialog.textAllowComment": "Permitir al firmante agregar comentarios en el diálogo de firma", "Common.Views.SignSettingsDialog.textDefInstruction": "Antes de firmar este documento, verifique que el contenido que está firmando es correcto.", "Common.Views.SignSettingsDialog.textInfoEmail": "Correo electrónico del firmante sugerido", "Common.Views.SignSettingsDialog.textInfoName": "Firmante sugerido", "Common.Views.SignSettingsDialog.textInfoTitle": "Título del firmante sugerido", "Common.Views.SignSettingsDialog.textInstructions": "Instrucciones para quien firma", "Common.Views.SignSettingsDialog.textShowDate": "Presentar fecha de la firma", "Common.Views.SignSettingsDialog.textTitle": "Configuración de firma", "Common.Views.SignSettingsDialog.txtEmpty": "Este campo es obligatorio", "Common.Views.SymbolTableDialog.textCharacter": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textCode": "Valor HEX de Unicode", "Common.Views.SymbolTableDialog.textCopyright": "Signo de Copyright", "Common.Views.SymbolTableDialog.textDCQuote": "<PERSON><PERSON><PERSON> do<PERSON> de cierre", "Common.Views.SymbolTableDialog.textDOQuote": "<PERSON><PERSON><PERSON> do<PERSON> de apertura", "Common.Views.SymbolTableDialog.textEllipsis": "Elipsis horizontal", "Common.Views.SymbolTableDialog.textEmDash": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textEmSpace": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textEnDash": "<PERSON><PERSON><PERSON> corto", "Common.Views.SymbolTableDialog.textEnSpace": "<PERSON><PERSON><PERSON><PERSON> corto", "Common.Views.SymbolTableDialog.textFont": "Letra ", "Common.Views.SymbolTableDialog.textNBHyphen": "<PERSON><PERSON><PERSON> sin ruptura", "Common.Views.SymbolTableDialog.textNBSpace": "Espacio de no separación", "Common.Views.SymbolTableDialog.textPilcrow": "Signo de antígrafo", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 <PERSON><PERSON>ac<PERSON>", "Common.Views.SymbolTableDialog.textRange": "Ra<PERSON>", "Common.Views.SymbolTableDialog.textRecent": "Símbolos utilizados recientemente", "Common.Views.SymbolTableDialog.textRegistered": "Signo de marca registrada", "Common.Views.SymbolTableDialog.textSCQuote": "Comillas simples de cierre", "Common.Views.SymbolTableDialog.textSection": "Signo de sección", "Common.Views.SymbolTableDialog.textShortcut": "Tecla de método abreviado", "Common.Views.SymbolTableDialog.textSHyphen": "G<PERSON><PERSON>", "Common.Views.SymbolTableDialog.textSOQuote": "Comillas simples de apertura", "Common.Views.SymbolTableDialog.textSpecial": "Caracteres especiales", "Common.Views.SymbolTableDialog.textSymbols": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textTitle": "Símbolo", "Common.Views.SymbolTableDialog.textTradeMark": "Símbolo de marca registrada", "Common.Views.UserNameDialog.textDontShow": "No volver a preguntarme", "Common.Views.UserNameDialog.textLabel": "Etiqueta:", "Common.Views.UserNameDialog.textLabelError": "La etiqueta no debe estar vacía.", "DE.Controllers.DocProtection.txtIsProtectedComment": "El documento está protegido. Solo puede añadir comentarios a este documento.", "DE.Controllers.DocProtection.txtIsProtectedForms": "El documento está protegido. Puede editar este documento, pero todos los cambios serán seguidos.", "DE.Controllers.DocProtection.txtIsProtectedTrack": "El documento está protegido. Puede editar este documento, pero todos los cambios serán rastreados.", "DE.Controllers.DocProtection.txtIsProtectedView": "El documento está protegido. Solo puede ver este documento.", "DE.Controllers.DocProtection.txtWasProtectedComment": "El documento ha sido protegido por otro usuario.\nSolo puede añadir comentarios a este documento.", "DE.Controllers.DocProtection.txtWasProtectedForms": "El documento ha sido protegido por otro usuario.\nSolo puede rellenar formularios en este documento.", "DE.Controllers.DocProtection.txtWasProtectedTrack": "El documento ha sido protegido por otro usuario.\nPuede editar este documento, pero todos los cambios serán seguidos.", "DE.Controllers.DocProtection.txtWasProtectedView": "El documento ha sido protegido por otro usuario.\nSolo puede ver este documento.", "DE.Controllers.DocProtection.txtWasUnprotected": "El documento ha sido desprotegido.", "DE.Controllers.LeftMenu.leavePageText": "Todos los cambios no guardados de este documento se perderán.<br> Pulse \"Cancelar\" después \"Guardar\" para guardarlos. Pulse \"OK\" para deshacer todos los cambios no guardados.", "DE.Controllers.LeftMenu.newDocumentTitle": "Documento sin título", "DE.Controllers.LeftMenu.notcriticalErrorTitle": "Aviso", "DE.Controllers.LeftMenu.requestEditRightsText": "Solicitando derechos de edición...", "DE.Controllers.LeftMenu.textLoadHistory": "Cargando historial de versiones...", "DE.Controllers.LeftMenu.textNoTextFound": "No se puede encontrar los datos que usted busca. Por favor, ajuste los parámetros de búsqueda.", "DE.Controllers.LeftMenu.textReplaceSkipped": "Se ha realizado el reemplazo. {0} ocurrencias fueron saltadas.", "DE.Controllers.LeftMenu.textReplaceSuccess": "La búsqueda se ha realizado. Ocurrencias reemplazadas: {0}", "DE.Controllers.LeftMenu.txtCompatible": "El documento se guardará en el nuevo formato. Permitirá utilizar todas las características del editor, pero podría afectar el diseño del documento.<br>Utilice la opción 'Compatibilidad' de la configuración avanzada si quiere hacer que los archivos sean compatibles con versiones anteriores de MS Word.", "DE.Controllers.LeftMenu.txtUntitled": "Sin título", "DE.Controllers.LeftMenu.warnDownloadAs": "Si sigue guardando en este formato todas las características a excepción del texto se perderán.<br> ¿Está seguro de que quiere continuar?", "DE.Controllers.LeftMenu.warnDownloadAsPdf": "Su {0} se convertirá en un formato editable. Esto puede llevar un tiempo. El documento resultante será optimizado para permitirle editar el texto, por lo que puede que no se vea exactamente como el {0} original, especialmente si el archivo original contenía muchos gráficos.", "DE.Controllers.LeftMenu.warnDownloadAsRTF": "Si Usted sigue guardando en este formato, una parte de formateo puede perderse.<br>¿Está seguro de que desea continuar?", "DE.Controllers.LeftMenu.warnReplaceString": "{0} no es un carácter especial válido para el campo de sustitución.", "DE.Controllers.Main.applyChangesTextText": "Cargando cambios...", "DE.Controllers.Main.applyChangesTitleText": "Cargando cambios", "DE.Controllers.Main.confirmMaxChangesSize": "El tamaño de las acciones excede la limitación establecida para su servidor.<br><PERSON><PERSON> \"Deshacer\" para cancelar su última acción o pulse \"Continuar\" para mantener la acción localmente (debe descargar el archivo o copiar su contenido para asegurarse de que no se pierde nada).", "DE.Controllers.Main.convertationTimeoutText": "Tiempo de conversión está superado.", "DE.Controllers.Main.criticalErrorExtText": "Pulse \"OK\" para regresar al documento.", "DE.Controllers.Main.criticalErrorTitle": "Error", "DE.Controllers.Main.downloadErrorText": "<PERSON><PERSON><PERSON> <PERSON>.", "DE.Controllers.Main.downloadMergeText": "Descargando...", "DE.Controllers.Main.downloadMergeTitle": "Descargando", "DE.Controllers.Main.downloadTextText": "Cargando documento...", "DE.Controllers.Main.downloadTitleText": "Cargando documento", "DE.Controllers.Main.errorAccessDeny": "Usted no tiene permisos para realizar la acción que está intentando hacer.<br> <PERSON>r favor, contacte con el Administrador del Servidor de Documentos.", "DE.Controllers.Main.errorBadImageUrl": "URL de imagen es incorrecto", "DE.Controllers.Main.errorCannotPasteImg": "No es posible pegar esta imagen desde el Portapapeles, pero puede guardarla en su dispositivo e \ninsertarla desde allí, o puede copiar la imagen sin texto y pegarla en el documento.", "DE.Controllers.Main.errorCoAuthoringDisconnect": "Se ha perdido la conexión con servidor. El documento no puede ser editado ahora.", "DE.Controllers.Main.errorComboSeries": "Para crear un gráfico combinado, seleccione al menos dos series de datos.", "DE.Controllers.Main.errorCompare": "La característica de comparación de documentos no está disponible durante la coedición.", "DE.Controllers.Main.errorConnectToServer": "No se consiguió guardar el documento. Por favor, compruebe los ajustes de conexión o póngase en contacto con su administrador.<br>Al hacer clic en el botón 'OK' se le solicitará que descargue el documento.", "DE.Controllers.Main.errorDatabaseConnection": "Error externo.<br>Error de conexión de base de datos. Por favor póngase en contacto con soporte si el error se mantiene.", "DE.Controllers.Main.errorDataEncrypted": "Se han recibido cambios cifrados, ellos no pueden ser descifrados.", "DE.Controllers.Main.errorDataRange": "<PERSON><PERSON>.", "DE.Controllers.Main.errorDefaultMessage": "Código de error: %1", "DE.Controllers.Main.errorDirectUrl": "Por favor, verifique el vínculo al documento.<br><PERSON><PERSON> vínculo debe ser un vínculo directo al archivo para descargar.", "DE.Controllers.Main.errorEditingDownloadas": "Se produjo un error durante el trabajo con el documento.<br>Use la opción 'Descargar como' para guardar la copia de seguridad de este archivo en el disco duro.", "DE.Controllers.Main.errorEditingSaveas": "Se produjo un error durante el trabajo con el documento.<br>Use la opción 'Guardar como...' para guardar la copia de seguridad de este archivo en el disco duro.", "DE.Controllers.Main.errorEmailClient": "No se pudo encontrar ningun cliente de correo", "DE.Controllers.Main.errorEmptyTOC": "Empezar a crear una tabla de contenido aplicando un estilo de título de la galería de estilos para el texto seleccionado.", "DE.Controllers.Main.errorFilePassProtect": "El archivo está protegido por una contraseña y no puede ser abierto.", "DE.Controllers.Main.errorFileSizeExceed": "El tamaño del archivo excede la limitación establecida para su servidor.<br>Por favor, póngase en contacto con el administrador del Servidor de Documentos para obtener más detalles.", "DE.Controllers.Main.errorForceSave": "Se produjo un error al guardar el archivo. Utilice la opción \"Descargar como\" para guardar el archivo en el disco duro o inténtelo de nuevo más tarde.", "DE.Controllers.Main.errorInconsistentExt": "Se ha producido un error al abrir el archivo.<br>El contenido del archivo no coincide con la extensión del mismo.", "DE.Controllers.Main.errorInconsistentExtDocx": "Se ha producido un error al abrir el archivo.<br>El contenido del archivo corresponde a documentos de texto (por ejemplo, docx), pero el archivo tiene extensión inconsistente: %1.", "DE.Controllers.Main.errorInconsistentExtPdf": "Se ha producido un error al abrir el archivo.<br>El contenido del archivo corresponde a uno de los siguientes formatos: pdf/djvu/xps/oxps, pero el archivo tiene extensión inconsistente: %1.", "DE.Controllers.Main.errorInconsistentExtPptx": "Se ha producido un error al abrir el archivo.<br>El contenido del archivo corresponde a presentaciones (por ejemplo, pptx), pero el archivo tiene extensión inconsistente: %1.", "DE.Controllers.Main.errorInconsistentExtXlsx": "Se ha producido un error al abrir el archivo.<br>El contenido del archivo corresponde a hojas de cálculo (por ejemplo, xlsx), pero el archivo tiene extensión inconsistente: %1.", "DE.Controllers.Main.errorKeyEncrypt": "Descriptor de clave desconocido", "DE.Controllers.Main.errorKeyExpire": "Descriptor de clave ha expirado", "DE.Controllers.Main.errorLoadingFont": "Las fuentes no están cargadas.<br><PERSON><PERSON> <PERSON>, póngase en contacto con el administrador del Document Server.", "DE.Controllers.Main.errorMailMergeLoadFile": "La carga del documento ha fallado. Por favor, seleccione un archivo diferente.", "DE.Controllers.Main.errorMailMergeSaveFile": "Error de fusión.", "DE.Controllers.Main.errorNoTOC": "No hay ninguna tabla de contenido para actualizar. Se puede insertar una desde la pestaña Referencias.", "DE.Controllers.Main.errorPasswordIsNotCorrect": "La contraseña que ha proporcionado no es correcta.<br>Verifique que la tecla Bloq Mayús está desactivada y asegúrese de utilizar las mayúsculas correctas.", "DE.Controllers.Main.errorProcessSaveResult": "Problemas al guardar", "DE.Controllers.Main.errorServerVersion": "La versión del editor ha sido actualizada. La página será recargada para aplicar los cambios.", "DE.Controllers.Main.errorSessionAbsolute": "Sesión de editar el documento ha expirado. Por favor, recargue la página.", "DE.Controllers.Main.errorSessionIdle": "El documento no ha sido editado durante bastante tiempo. Por favor, recargue la página.", "DE.Controllers.Main.errorSessionToken": "Conexión al servidor ha sido interrumpido. Por favor, recargue la página.", "DE.Controllers.Main.errorSetPassword": "No se pudo establecer la contraseña.", "DE.Controllers.Main.errorStockChart": "Orden de las filas incorrecto. Para crear un gráfico de cotizaciones introduzca los datos en la hoja  de la forma siguiente:<br> precio de apertura, precio má<PERSON><PERSON>, precio mín<PERSON>, precio de cierre.", "DE.Controllers.Main.errorSubmit": "Error al enviar.", "DE.Controllers.Main.errorTextFormWrongFormat": "El valor introducido no se corresponde con el formato del campo", "DE.Controllers.Main.errorToken": "El token de seguridad de documento tiene un formato incorrecto.<br>Por favor, contacte con el Administrador del Servidor de Documentos.", "DE.Controllers.Main.errorTokenExpire": "El token de seguridad de documento ha sido expirado.<br><PERSON>r favor, contacte con el Administrador del Servidor de Documentos.", "DE.Controllers.Main.errorUpdateVersion": "Se ha cambiado la versión del archivo. La página será actualizada.", "DE.Controllers.Main.errorUpdateVersionOnDisconnect": "Se ha restablecido la conexión a Internet y se ha cambiado la versión del archivo. <br>Para poder seguir trabajando, es necesario descargar el archivo o copiar su contenido para asegurarse de que no se ha perdido nada, y luego volver a cargar esta página.", "DE.Controllers.Main.errorUserDrop": "No se puede acceder al archivo ahora.", "DE.Controllers.Main.errorUsersExceed": "El número de usuarios permitido según su plan de precios fue excedido", "DE.Controllers.Main.errorViewerDisconnect": "Se ha perdido la conexión. Usted todavía puede visualizar el documento,<br> pero no puede descargar o imprimirlo hasta que la conexión sea restaurada y la página esté recargada.", "DE.Controllers.Main.leavePageText": "Hay cambios no guardados en este documento. Haga clic en 'Permanecer en esta página', después 'Guardar' para guardarlos. Haga clic en 'Abandonar esta página' para descartar todos los cambios no guardados.", "DE.Controllers.Main.leavePageTextOnClose": "Todos los cambios no guardados de este documento se perderán.<br> Pulse \"Cancelar\" después \"Guardar\" para guardarlos. Pulse \"OK\" para deshacer todos los cambios no guardados.", "DE.Controllers.Main.loadFontsTextText": "Cargando datos...", "DE.Controllers.Main.loadFontsTitleText": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.loadFontTextText": "Cargando datos...", "DE.Controllers.Main.loadFontTitleText": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.loadImagesTextText": "Cargando imágenes...", "DE.Controllers.Main.loadImagesTitleText": "<PERSON>gan<PERSON>", "DE.Controllers.Main.loadImageTextText": "Cargando imagen...", "DE.Controllers.Main.loadImageTitleText": "Cargando imagen", "DE.Controllers.Main.loadingDocumentTextText": "Cargando documento...", "DE.Controllers.Main.loadingDocumentTitleText": "Cargando documento", "DE.Controllers.Main.mailMergeLoadFileText": "Cargando fuente de datos...", "DE.Controllers.Main.mailMergeLoadFileTitle": "Cargando fuente de datos", "DE.Controllers.Main.notcriticalErrorTitle": "Aviso", "DE.Controllers.Main.openErrorText": "Ha ocurrido un error al abrir el archivo. ", "DE.Controllers.Main.openTextText": "Abriendo documento...", "DE.Controllers.Main.openTitleText": "Abriendo documento", "DE.Controllers.Main.printTextText": "Imprimiendo documento...", "DE.Controllers.Main.printTitleText": "Imprimiendo documento", "DE.Controllers.Main.reloadButtonText": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.requestEditFailedMessageText": "Alguien está editando este documento en este momento. Por favor, inténtelo de nuevo más tarde.", "DE.Controllers.Main.requestEditFailedTitleText": "Acceso denegado", "DE.Controllers.Main.saveErrorText": "Ha ocurrido un error al guardar el archivo. ", "DE.Controllers.Main.saveErrorTextDesktop": "Este archivo no se puede guardar o crear.<br>Las razones posibles son: <br>1. El archivo es sólo para leer. <br>2. El archivo está siendo editado por otros usuarios. <br>3. El disco está lleno o corrupto.", "DE.Controllers.Main.saveTextText": "Guardando documento...", "DE.Controllers.Main.saveTitleText": "Guardando documento", "DE.Controllers.Main.scriptLoadError": "La conexión a Internet es demasiado lenta, no se podía cargar algunos componentes. Por favor, recargue la página.", "DE.Controllers.Main.sendMergeText": "Envío de fusión...", "DE.Controllers.Main.sendMergeTitle": "Envío de fusión", "DE.Controllers.Main.splitDividerErrorText": "El número de filas debe ser un divisor de %1.", "DE.Controllers.Main.splitMaxColsErrorText": "El número de columnas debe ser menos que %1.", "DE.Controllers.Main.splitMaxRowsErrorText": "El número de filas debe ser menos que %1.", "DE.Controllers.Main.textAnonymous": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.textAnyone": "Cualquiera", "DE.Controllers.Main.textApplyAll": "Aplicar a todas las ecuaciones", "DE.Controllers.Main.textBuyNow": "Visitar sitio web", "DE.Controllers.Main.textChangesSaved": "Todos los cambios son guardados", "DE.Controllers.Main.textClose": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.textCloseTip": "Pulse para cerrar el consejo", "DE.Controllers.Main.textContactUs": "Contactar con equipo de ventas", "DE.Controllers.Main.textContinue": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.textConvertEquation": "Esta ecuación fue creada con una versión antigua del editor de ecuaciones que ya no es compatible. Para editarla, convierta la ecuación al formato ML de Office Math.<br>¿Convertir ahora?", "DE.Controllers.Main.textCustomLoader": "Note, por favor, que según los términos de la licencia Usted no tiene derecho a cambiar el cargador.<br>Por favor, póngase en contacto con nuestro Departamento de Ventas para obtener una cotización.", "DE.Controllers.Main.textDisconnect": "Se ha perdido la conexión", "DE.Controllers.Main.textGuest": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.textHasMacros": "El archivo contiene macros automáticas.<br>¿Quiere ejecutar macros?", "DE.Controllers.Main.textLearnMore": "Más información", "DE.Controllers.Main.textLoadingDocument": "Cargando documento", "DE.Controllers.Main.textLongName": "Escriba un nombre que tenga menos de 128 caracteres.", "DE.Controllers.Main.textNoLicenseTitle": "Se ha alcanzado el límite de licencias", "DE.Controllers.Main.textPaidFeature": "Función de pago", "DE.Controllers.Main.textReconnect": "Se ha restablecido la conexión", "DE.Controllers.Main.textRemember": "Recordar mi elección para todos los archivos", "DE.Controllers.Main.textRememberMacros": "Recordar mi elección para todas las macros", "DE.Controllers.Main.textRenameError": "El nombre de usuario no debe estar vacío.", "DE.Controllers.Main.textRenameLabel": "Escriba un nombre que se utilizará para la colaboración", "DE.Controllers.Main.textRequestMacros": "Una macro realiza una solicitud a la URL. ¿Quiere permitir la solicitud al %1?", "DE.Controllers.Main.textShape": "Forma", "DE.Controllers.Main.textStrict": "<PERSON><PERSON> estricto", "DE.Controllers.Main.textText": "Texto", "DE.Controllers.Main.textTryQuickPrint": "Ha seleccionado Impresión rápida: todo el documento se imprimirá en la última impresora seleccionada o predeterminada.<br>¿Desea continuar?", "DE.Controllers.Main.textTryUndoRedo": "Las funciones Anular/<PERSON><PERSON>cer se desactivan para el modo co-edición rápido.<br><PERSON><PERSON> en el botón \"modo estricto\" para cambiar al modo de co-edición estricta para editar el archivo sin la interferencia de otros usuarios y enviar sus cambios sólo después de guardarlos. Se puede cambiar entre los modos de co-edición usando los ajustes avanzados de edición.", "DE.Controllers.Main.textTryUndoRedoWarn": "Las funciones Deshacer/<PERSON><PERSON><PERSON> son desactivados en el modo de co-edición rápido.", "DE.Controllers.Main.textUndo": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.titleLicenseExp": "Licencia ha expirado", "DE.Controllers.Main.titleServerVersion": "Editor ha sido actualizado", "DE.Controllers.Main.titleUpdateVersion": "Versión ha cambiado", "DE.Controllers.Main.txtAbove": "encima", "DE.Controllers.Main.txtArt": "Su texto aquí", "DE.Controllers.Main.txtBasicShapes": "Formas básicas", "DE.Controllers.Main.txtBelow": "de<PERSON>jo", "DE.Controllers.Main.txtBookmarkError": "¡Error! El marcador no se ha definido", "DE.Controllers.Main.txtButtons": "Botones", "DE.Controllers.Main.txtCallouts": "Llamadas", "DE.Controllers.Main.txtCharts": "Grá<PERSON><PERSON>", "DE.Controllers.Main.txtChoose": "<PERSON><PERSON> un item", "DE.Controllers.Main.txtClickToLoad": "Haga clic para cargar la imagen", "DE.Controllers.Main.txtCurrentDocument": "Documento actual", "DE.Controllers.Main.txtDiagramTitle": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtEditingMode": "Establecer el modo de edición...", "DE.Controllers.Main.txtEndOfFormula": "Fin de fórmula inesperado", "DE.Controllers.Main.txtEnterDate": "Introducir una fecha", "DE.Controllers.Main.txtErrorLoadHistory": "Historia de carga falló", "DE.Controllers.Main.txtEvenPage": "Página par", "DE.Controllers.Main.txtFiguredArrows": "<PERSON><PERSON><PERSON><PERSON> figu<PERSON>", "DE.Controllers.Main.txtFirstPage": "Primera página", "DE.Controllers.Main.txtFooter": "Pie de página", "DE.Controllers.Main.txtFormulaNotInTable": "La fórmula no está en la tabla", "DE.Controllers.Main.txtHeader": "Encabezado", "DE.Controllers.Main.txtHyperlink": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtIndTooLarge": "El índice es demasiado grande", "DE.Controllers.Main.txtLines": "Líneas", "DE.Controllers.Main.txtMainDocOnly": "¡Error! Sólo el documento principal.", "DE.Controllers.Main.txtMath": "Matemáticas", "DE.Controllers.Main.txtMissArg": "Falta argumento", "DE.Controllers.Main.txtMissOperator": "Falta operador", "DE.Controllers.Main.txtNeedSynchronize": "Usted tiene actualizaciones", "DE.Controllers.Main.txtNone": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtNoTableOfContents": "No hay títulos en el documento. Aplique un estilo de título al texto para que aparezca en la tabla de contenido.", "DE.Controllers.Main.txtNoTableOfFigures": "No se han encontrado los elementos de tabla de ilustraciones.", "DE.Controllers.Main.txtNoText": "¡Error! No hay texto del estilo especificado en el documento.", "DE.Controllers.Main.txtNotInTable": "No está en la tabla", "DE.Controllers.Main.txtNotValidBookmark": "¡Error! No es una auto-referencia de marcador válida.", "DE.Controllers.Main.txtOddPage": "<PERSON><PERSON>gina impar", "DE.Controllers.Main.txtOnPage": "en la página", "DE.Controllers.Main.txtRectangles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtSameAsPrev": "Igual al Anterior", "DE.Controllers.Main.txtSection": "-Sección", "DE.Controllers.Main.txtSeries": "Serie", "DE.Controllers.Main.txtShape_accentBorderCallout1": "Llamada con línea 1 (borde y barra de énfasis)", "DE.Controllers.Main.txtShape_accentBorderCallout2": "Llamada con línea 2 (borde y barra de é<PERSON>fa<PERSON>)", "DE.Controllers.Main.txtShape_accentBorderCallout3": "Llamada con línea 3 (borde y barra de énfa<PERSON>)", "DE.Controllers.Main.txtShape_accentCallout1": "Llamada con línea 1 (barra de <PERSON>)", "DE.Controllers.Main.txtShape_accentCallout2": "Llamada con línea 2 (bar<PERSON> <PERSON>)", "DE.Controllers.Main.txtShape_accentCallout3": "Llamada con línea 3 (barra <PERSON>)", "DE.Controllers.Main.txtShape_actionButtonBackPrevious": "Atrás o Botón Anterior", "DE.Controllers.Main.txtShape_actionButtonBeginning": "Botón Al inicio", "DE.Controllers.Main.txtShape_actionButtonBlank": "Botón en blanco", "DE.Controllers.Main.txtShape_actionButtonDocument": "Botón Documento", "DE.Controllers.Main.txtShape_actionButtonEnd": "Botón Al fin", "DE.Controllers.Main.txtShape_actionButtonForwardNext": "Adelante o Botón Siguiente", "DE.Controllers.Main.txtShape_actionButtonHelp": "Bo<PERSON><PERSON>", "DE.Controllers.Main.txtShape_actionButtonHome": "Botón Inicio", "DE.Controllers.Main.txtShape_actionButtonInformation": "Botón Información", "DE.Controllers.Main.txtShape_actionButtonMovie": "Botón Vídeo ", "DE.Controllers.Main.txtShape_actionButtonReturn": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_actionButtonSound": "Botón Sonido", "DE.Controllers.Main.txtShape_arc": "Arco", "DE.Controllers.Main.txtShape_bentArrow": "Flecha do<PERSON>", "DE.Controllers.Main.txtShape_bentConnector5": "Conector angular", "DE.Controllers.Main.txtShape_bentConnector5WithArrow": "Conector angular de flecha", "DE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Conector angular de flecha doble", "DE.Controllers.Main.txtShape_bentUpArrow": "Flecha doblada hacia arriba", "DE.Controllers.Main.txtShape_bevel": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_blockArc": "Arco de bloque", "DE.Controllers.Main.txtShape_borderCallout1": "Llamada con línea 1", "DE.Controllers.Main.txtShape_borderCallout2": "Llamada con línea 2", "DE.Controllers.Main.txtShape_borderCallout3": "Llamada con línea 3", "DE.Controllers.Main.txtShape_bracePair": "Llaves", "DE.Controllers.Main.txtShape_callout1": "Llamada con línea 1 (sin borde)", "DE.Controllers.Main.txtShape_callout2": "Llamada con línea 2 (sin borde)", "DE.Controllers.Main.txtShape_callout3": "Llamada con línea 3 (sin borde)", "DE.Controllers.Main.txtShape_can": "Сilindro", "DE.Controllers.Main.txtShape_chevron": "Cheurón", "DE.Controllers.Main.txtShape_chord": "Acorde", "DE.Controllers.Main.txtShape_circularArrow": "Flecha circular", "DE.Controllers.Main.txtShape_cloud": "Nube", "DE.Controllers.Main.txtShape_cloudCallout": "Llamada de nube", "DE.Controllers.Main.txtShape_corner": "Esquina", "DE.Controllers.Main.txtShape_cube": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_curvedConnector3": "Conector curvado", "DE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Conector curvado de flecha", "DE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Conector curvado de flecha doble", "DE.Controllers.Main.txtShape_curvedDownArrow": "Flecha curvada hacia abajo", "DE.Controllers.Main.txtShape_curvedLeftArrow": "Flecha curvada hacia la izquierda", "DE.Controllers.Main.txtShape_curvedRightArrow": "Flecha curvada hacia la derecha", "DE.Controllers.Main.txtShape_curvedUpArrow": "Flecha curvada hacia arriba", "DE.Controllers.Main.txtShape_decagon": "Decágono", "DE.Controllers.Main.txtShape_diagStripe": "<PERSON><PERSON><PERSON> diagonal", "DE.Controllers.Main.txtShape_diamond": "Rombo", "DE.Controllers.Main.txtShape_dodecagon": "Dodecágono", "DE.Controllers.Main.txtShape_donut": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_doubleWave": "Doble onda", "DE.Controllers.Main.txtShape_downArrow": "<PERSON><PERSON>cha a<PERSON>", "DE.Controllers.Main.txtShape_downArrowCallout": "Llamada de flecha hacia abajo", "DE.Controllers.Main.txtShape_ellipse": "Elipse", "DE.Controllers.Main.txtShape_ellipseRibbon": "Cinta curvada hacia abajo", "DE.Controllers.Main.txtShape_ellipseRibbon2": "Cinta curvada hacia arriba", "DE.Controllers.Main.txtShape_flowChartAlternateProcess": "Proceso alternativo", "DE.Controllers.Main.txtShape_flowChartCollate": "Intercalar", "DE.Controllers.Main.txtShape_flowChartConnector": "Conector", "DE.Controllers.Main.txtShape_flowChartDecision": "Decisión", "DE.Controllers.Main.txtShape_flowChartDelay": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_flowChartDisplay": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_flowChartDocument": "Documento", "DE.Controllers.Main.txtShape_flowChartExtract": "Extracto", "DE.Controllers.Main.txtShape_flowChartInputOutput": "Datos", "DE.Controllers.Main.txtShape_flowChartInternalStorage": "Almacenamiento interno", "DE.Controllers.Main.txtShape_flowChartMagneticDisk": "Disco magnético", "DE.Controllers.Main.txtShape_flowChartMagneticDrum": "Almacenamiento de acceso directo", "DE.Controllers.Main.txtShape_flowChartMagneticTape": "Almacenamiento de acceso secuencial", "DE.Controllers.Main.txtShape_flowChartManualInput": "Entrada manual", "DE.Controllers.Main.txtShape_flowChartManualOperation": "Operación manual", "DE.Controllers.Main.txtShape_flowChartMerge": "Combinar", "DE.Controllers.Main.txtShape_flowChartMultidocument": "Multidocumento", "DE.Controllers.Main.txtShape_flowChartOffpageConnector": "Conector fuera de <PERSON>a", "DE.Controllers.Main.txtShape_flowChartOnlineStorage": "Datos almacenados", "DE.Controllers.Main.txtShape_flowChartOr": "Diagrama de flujo: O", "DE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Proceso predefinido", "DE.Controllers.Main.txtShape_flowChartPreparation": "Preparación", "DE.Controllers.Main.txtShape_flowChartProcess": "Proceso", "DE.Controllers.Main.txtShape_flowChartPunchedCard": "Tarjeta", "DE.Controllers.Main.txtShape_flowChartPunchedTape": "Cinta perforada", "DE.Controllers.Main.txtShape_flowChartSort": "Ordenar", "DE.Controllers.Main.txtShape_flowChartSummingJunction": "Y", "DE.Controllers.Main.txtShape_flowChartTerminator": "Terminador", "DE.Controllers.Main.txtShape_foldedCorner": "Esquina doblada", "DE.Controllers.Main.txtShape_frame": "<PERSON>", "DE.Controllers.Main.txtShape_halfFrame": "Medio marco", "DE.Controllers.Main.txtShape_heart": "Corazón", "DE.Controllers.Main.txtShape_heptagon": "<PERSON>pt<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_hexagon": "Hexágono", "DE.Controllers.Main.txtShape_homePlate": "Pentágono", "DE.Controllers.Main.txtShape_horizontalScroll": "Pergamino horizontal", "DE.Controllers.Main.txtShape_irregularSeal1": "Explosión 1", "DE.Controllers.Main.txtShape_irregularSeal2": "Explosión 2", "DE.Controllers.Main.txtShape_leftArrow": "Flecha izquierda", "DE.Controllers.Main.txtShape_leftArrowCallout": "Llamada de flecha a la izquierda", "DE.Controllers.Main.txtShape_leftBrace": "<PERSON><PERSON>r llave", "DE.Controllers.Main.txtShape_leftBracket": "<PERSON><PERSON><PERSON> corchete", "DE.Controllers.Main.txtShape_leftRightArrow": "Flecha izquierda y derecha", "DE.Controllers.Main.txtShape_leftRightArrowCallout": "Llamada de flecha izquierda y derecha", "DE.Controllers.Main.txtShape_leftRightUpArrow": "Flecha izquierda, derecha y arriba", "DE.Controllers.Main.txtShape_leftUpArrow": "Flecha izquierda y arriba", "DE.Controllers.Main.txtShape_lightningBolt": "Rayo", "DE.Controllers.Main.txtShape_line": "Lín<PERSON>", "DE.Controllers.Main.txtShape_lineWithArrow": "Fle<PERSON>", "DE.Controllers.Main.txtShape_lineWithTwoArrows": "<PERSON><PERSON><PERSON> doble", "DE.Controllers.Main.txtShape_mathDivide": "División", "DE.Controllers.Main.txtShape_mathEqual": "Igual", "DE.Controllers.Main.txtShape_mathMinus": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_mathMultiply": "Multiplicar", "DE.Controllers.Main.txtShape_mathNotEqual": "No igual", "DE.Controllers.Main.txtShape_mathPlus": "Más", "DE.Controllers.Main.txtShape_moon": "Luna", "DE.Controllers.Main.txtShape_noSmoking": "Señal de prohibido", "DE.Controllers.Main.txtShape_notchedRightArrow": "Flecha a la derecha con muesca", "DE.Controllers.Main.txtShape_octagon": "Octágono", "DE.Controllers.Main.txtShape_parallelogram": "Paralelogramo", "DE.Controllers.Main.txtShape_pentagon": "Pentágono", "DE.Controllers.Main.txtShape_pie": "Sector del círculo", "DE.Controllers.Main.txtShape_plaque": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_plus": "Más", "DE.Controllers.Main.txtShape_polyline1": "A mano alzada", "DE.Controllers.Main.txtShape_polyline2": "Forma libre", "DE.Controllers.Main.txtShape_quadArrow": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_quadArrowCallout": "Llamada de flecha cu<PERSON>", "DE.Controllers.Main.txtShape_rect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_ribbon": "<PERSON>inta hacia abajo", "DE.Controllers.Main.txtShape_ribbon2": "Cinta hacia arriba", "DE.Controllers.Main.txtShape_rightArrow": "<PERSON><PERSON><PERSON> derecha", "DE.Controllers.Main.txtShape_rightArrowCallout": "Llamada de flecha a la derecha", "DE.Controllers.Main.txtShape_rightBrace": "Ce<PERSON>r llave", "DE.Controllers.Main.txtShape_rightBracket": "<PERSON><PERSON><PERSON> corchete", "DE.Controllers.Main.txtShape_round1Rect": "Redondear rectángulo de esquina sencilla", "DE.Controllers.Main.txtShape_round2DiagRect": "Redondear rectángulo de esquina diagonal", "DE.Controllers.Main.txtShape_round2SameRect": "Redondear rectángulo de esquina del mismo lado", "DE.Controllers.Main.txtShape_roundRect": "Rectángulo con esquinas redondeadas", "DE.Controllers.Main.txtShape_rtTriangle": "Triángulo rectángulo", "DE.Controllers.Main.txtShape_smileyFace": "<PERSON>", "DE.Controllers.Main.txtShape_snip1Rect": "Recortar rectángulo de esquina sencilla", "DE.Controllers.Main.txtShape_snip2DiagRect": "Recortar rectángulo de esquina diagonal", "DE.Controllers.Main.txtShape_snip2SameRect": "Recortar rectángulo de esquina del mismo lado", "DE.Controllers.Main.txtShape_snipRoundRect": "Recortar y redondear rectángulo de esquina sencilla", "DE.Controllers.Main.txtShape_spline": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_star10": "Estrella de 10 puntas", "DE.Controllers.Main.txtShape_star12": "Estrella de 12 puntas", "DE.Controllers.Main.txtShape_star16": "Estrella de 16 puntas", "DE.Controllers.Main.txtShape_star24": "Estrella de 24 puntas", "DE.Controllers.Main.txtShape_star32": "Estrella de 32 puntas", "DE.Controllers.Main.txtShape_star4": "Estrella de 4 puntas", "DE.Controllers.Main.txtShape_star5": "Estrella de 5 puntas", "DE.Controllers.Main.txtShape_star6": "Estrella de 6 puntas", "DE.Controllers.Main.txtShape_star7": "Estrella de 7 puntas", "DE.Controllers.Main.txtShape_star8": "Estrella de 8 puntas", "DE.Controllers.Main.txtShape_stripedRightArrow": "Flecha a la derecha con bandas", "DE.Controllers.Main.txtShape_sun": "Sol", "DE.Controllers.Main.txtShape_teardrop": "Lágrima", "DE.Controllers.Main.txtShape_textRect": "Cuadro de texto", "DE.Controllers.Main.txtShape_trapezoid": "Trapecio", "DE.Controllers.Main.txtShape_triangle": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_upArrow": "Flecha hacia arriba", "DE.Controllers.Main.txtShape_upArrowCallout": "Llamada de flecha hacia arriba", "DE.Controllers.Main.txtShape_upDownArrow": "Flecha hacia arriba y abajo", "DE.Controllers.Main.txtShape_uturnArrow": "Flecha en U", "DE.Controllers.Main.txtShape_verticalScroll": "Pergamino vertical", "DE.Controllers.Main.txtShape_wave": "On<PERSON>", "DE.Controllers.Main.txtShape_wedgeEllipseCallout": "Llamada ovalada", "DE.Controllers.Main.txtShape_wedgeRectCallout": "Llamada rectangular", "DE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Llamada rectangular redondeada", "DE.Controllers.Main.txtStarsRibbons": "Cintas y estrellas", "DE.Controllers.Main.txtStyle_Caption": "Leyenda", "DE.Controllers.Main.txtStyle_endnote_text": "Texto de nota al final", "DE.Controllers.Main.txtStyle_footnote_text": "Texto de nota al pie", "DE.Controllers.Main.txtStyle_Heading_1": "Título 1", "DE.Controllers.Main.txtStyle_Heading_2": "Título 2", "DE.Controllers.Main.txtStyle_Heading_3": "Título 3", "DE.Controllers.Main.txtStyle_Heading_4": "Título 4", "DE.Controllers.Main.txtStyle_Heading_5": "Título 5", "DE.Controllers.Main.txtStyle_Heading_6": "Título 6", "DE.Controllers.Main.txtStyle_Heading_7": "Título 7", "DE.Controllers.Main.txtStyle_Heading_8": "Título 8", "DE.Controllers.Main.txtStyle_Heading_9": "Título 9", "DE.Controllers.Main.txtStyle_Intense_Quote": "<PERSON>ita se<PERSON>cci<PERSON>", "DE.Controllers.Main.txtStyle_List_Paragraph": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtStyle_No_Spacing": "Sin espacio", "DE.Controllers.Main.txtStyle_Normal": "Normal", "DE.Controllers.Main.txtStyle_Quote": "Cita", "DE.Controllers.Main.txtStyle_Subtitle": "Subtítulo", "DE.Controllers.Main.txtStyle_Title": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtSyntaxError": "Error de sintaxis", "DE.Controllers.Main.txtTableInd": "El índice de la tabla no puede ser cero", "DE.Controllers.Main.txtTableOfContents": "Tabla de contenidos", "DE.Controllers.Main.txtTableOfFigures": "Tabla de ilustraciones", "DE.Controllers.Main.txtTOCHeading": "Título TDC", "DE.Controllers.Main.txtTooLarge": "El número es demasiado grande para darle formato", "DE.Controllers.Main.txtTypeEquation": "Escribir una ecuación aquí.", "DE.Controllers.Main.txtUndefBookmark": "Marcador no definido", "DE.Controllers.Main.txtXAxis": "<PERSON><PERSON>", "DE.Controllers.Main.txtYAxis": "<PERSON><PERSON>", "DE.Controllers.Main.txtZeroDivide": "División por cero", "DE.Controllers.Main.unknownErrorText": "Error des<PERSON>.", "DE.Controllers.Main.unsupportedBrowserErrorText": "Su navegador no está soportado.", "DE.Controllers.Main.uploadDocExtMessage": "Formato de documento desconocido", "DE.Controllers.Main.uploadDocFileCountMessage": "No hay documentos subidos", "DE.Controllers.Main.uploadDocSizeMessage": "Límite de tamaño máximo del documento excedido.", "DE.Controllers.Main.uploadImageExtMessage": "Formato de imagen desconocido.", "DE.Controllers.Main.uploadImageFileCountMessage": "Ningunas imágenes cargadas.", "DE.Controllers.Main.uploadImageSizeMessage": "La imagen es demasiado grande. El tamaño máximo es de 25 MB.", "DE.Controllers.Main.uploadImageTextText": "Subiendo imagen...", "DE.Controllers.Main.uploadImageTitleText": "Subiendo imagen", "DE.Controllers.Main.waitText": "Por favor, espere...", "DE.Controllers.Main.warnBrowserIE9": "Este aplicación tiene baja capacidad en IE9. Utilice IE10 o superior", "DE.Controllers.Main.warnBrowserZoom": "La configuración actual de zoom de su navegador no está soportada por completo. Por favor restablezca zoom predeterminado pulsando Ctrl+0.", "DE.Controllers.Main.warnLicenseExceeded": "Usted ha alcanzado el límite de conexiones simultáneas con %1 editores. Este documento se abrirá sólo para su visualización.<br>Por favor, contacte con su administrador para recibir más información.", "DE.Controllers.Main.warnLicenseExp": "Su licencia ha expirado.<br>Por favor, actualice su licencia y después recargue la página.", "DE.Controllers.Main.warnLicenseLimitedNoAccess": "Licencia expirada.<br>No tiene acceso a la funcionalidad de edición de documentos.<br>Por favor, póngase en contacto con su administrador.", "DE.Controllers.Main.warnLicenseLimitedRenewed": "La licencia requiere ser renovada.<br>Tiene un acceso limitado a la funcionalidad de edición de documentos.<br>Por favor, póngase en contacto con su administrador para obtener un acceso completo", "DE.Controllers.Main.warnLicenseUsersExceeded": "Usted ha alcanzado el límite de usuarios para los editores de %1. <PERSON><PERSON> favor, contacte con su administrador para recibir más información.", "DE.Controllers.Main.warnNoLicense": "Usted ha alcanzado el límite de conexiones simultáneas con %1 editores. Este documento se abrirá sólo para su visualización.<br>Contacte con el equipo de ventas de %1 para conocer los términos de actualización personal.", "DE.Controllers.Main.warnNoLicenseUsers": "Usted ha alcanzado el límite de usuarios para los editores de %1. Contacte con el equipo de ventas de %1 para conocer los términos de actualización personal.", "DE.Controllers.Main.warnProcessRightsChange": "El derecho de edición del archivo es denegado", "DE.Controllers.Navigation.txtBeginning": "Principio del documento", "DE.Controllers.Navigation.txtGotoBeginning": "Ir al principio de", "DE.Controllers.Print.textMarginsLast": "Último personalizado", "DE.Controllers.Print.txtCustom": "Personalizado", "DE.Controllers.Print.txtPrintRangeInvalid": "Intervalo de impresión no válido", "DE.Controllers.Print.txtPrintRangeSingleRange": "Introduzca un único número de página o un único intervalo de páginas (por ejemplo, 5-12). O puede imprimir en PDF.", "DE.Controllers.Search.notcriticalErrorTitle": "Advertencia", "DE.Controllers.Search.textNoTextFound": "No se puede encontrar los datos que usted busca. Por favor, ajuste los parámetros de búsqueda.", "DE.Controllers.Search.textReplaceSkipped": "Se ha realizado la sustitución. Se han omitido {0} ocurrencias.", "DE.Controllers.Search.textReplaceSuccess": "Se realizó la búsqueda. {0} ocurrencias se sustituyeron", "DE.Controllers.Search.warnReplaceString": "{0} no es un carácter especial válido para la casilla Reemplazar con.", "DE.Controllers.Statusbar.textDisconnect": "<b>Se ha perdido la conexión</b><br>Intentando conectar. Por favor compruebe la configuración de la conexión.", "DE.Controllers.Statusbar.textHasChanges": "Nuevos cambios han sido encontrado", "DE.Controllers.Statusbar.textSetTrackChanges": "Usted está en el modo de seguimiento de cambios", "DE.Controllers.Statusbar.textTrackChanges": "El documento se abre con el modo de cambio de pista activado", "DE.Controllers.Statusbar.tipReview": "Seguimiento a cambios", "DE.Controllers.Statusbar.zoomText": "Zoom {0}%", "DE.Controllers.Toolbar.confirmAddFontName": "El tipo de letra que usted va a guardar no está disponible en este dispositivo. <br>El estilo de letra se mostrará usando uno de los tipos de letra del dispositivo, el tipo de letra guardado va a usarse cuando esté disponible.<br>¿Desea continuar?", "DE.Controllers.Toolbar.dataUrl": "Pegar una URL de datos", "DE.Controllers.Toolbar.notcriticalErrorTitle": "Aviso", "DE.Controllers.Toolbar.textAccent": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.textBracket": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.textEmptyImgUrl": "Hay que especificar URL de imagen", "DE.Controllers.Toolbar.textEmptyMMergeUrl": "Debe especificar la URL.", "DE.Controllers.Toolbar.textFontSizeErr": "El valor introducido es incorrecto.<br>Por favor, introduzca un valor numérico entre 1 y 300", "DE.Controllers.Toolbar.textFraction": "Fracciones", "DE.Controllers.Toolbar.textFunction": "Funciones", "DE.Controllers.Toolbar.textGroup": "Grupo", "DE.Controllers.Toolbar.textInsert": "Insertar", "DE.Controllers.Toolbar.textIntegral": "Integrales", "DE.Controllers.Toolbar.textLargeOperator": "Operadores grandes", "DE.Controllers.Toolbar.textLimitAndLog": "Límites y logaritmos ", "DE.Controllers.Toolbar.textMatrix": "Matrices", "DE.Controllers.Toolbar.textOperator": "Operadores", "DE.Controllers.Toolbar.textRadical": "Radicales", "DE.Controllers.Toolbar.textRecentlyUsed": "Usados recientemente", "DE.Controllers.Toolbar.textScript": "Letras", "DE.Controllers.Toolbar.textSymbols": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.textTabForms": "Formularios", "DE.Controllers.Toolbar.textWarning": "Aviso", "DE.Controllers.Toolbar.txtAccent_Accent": "<PERSON><PERSON> a<PERSON>", "DE.Controllers.Toolbar.txtAccent_ArrowD": "Flecha derecha-izquierda superior", "DE.Controllers.Toolbar.txtAccent_ArrowL": "Flecha superior hacia izquierda", "DE.Controllers.Toolbar.txtAccent_ArrowR": "Flecha superior hacia derecha", "DE.Controllers.Toolbar.txtAccent_Bar": "Barr<PERSON>", "DE.Controllers.Toolbar.txtAccent_BarBot": "Barra subyacente", "DE.Controllers.Toolbar.txtAccent_BarTop": "Barra superpuesta", "DE.Controllers.Toolbar.txtAccent_BorderBox": "Fórmula encuadrada (con marcador de posición)", "DE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Fórmula en<PERSON> (ejemplo)", "DE.Controllers.Toolbar.txtAccent_Check": "Comprobar", "DE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Llave subyacente", "DE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Llave superpuesta", "DE.Controllers.Toolbar.txtAccent_Custom_1": "Vector A", "DE.Controllers.Toolbar.txtAccent_Custom_2": "ABC con barra superpuesta", "DE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y con barra superpuesta", "DE.Controllers.Toolbar.txtAccent_DDDot": "<PERSON><PERSON> puntos", "DE.Controllers.Toolbar.txtAccent_DDot": "<PERSON><PERSON> puntos", "DE.Controllers.Toolbar.txtAccent_Dot": "Punt<PERSON>", "DE.Controllers.Toolbar.txtAccent_DoubleBar": "Barra doble superpuesta", "DE.Controllers.Toolbar.txtAccent_Grave": "Acento grave", "DE.Controllers.Toolbar.txtAccent_GroupBot": "Carácter de agrupación inferior", "DE.Controllers.Toolbar.txtAccent_GroupTop": "Carácter de agrupación superior", "DE.Controllers.Toolbar.txtAccent_HarpoonL": "Arpón superior hacia izquierdo", "DE.Controllers.Toolbar.txtAccent_HarpoonR": "Arpón superior hacia derecha", "DE.Controllers.Toolbar.txtAccent_Hat": "Circunfle<PERSON>", "DE.Controllers.Toolbar.txtAccent_Smile": "Acento breve", "DE.Controllers.Toolbar.txtAccent_Tilde": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Angle": "Corchetes angulares", "DE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Corchetes angulares con separador", "DE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Corchetes angulares con dos separadores", "DE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Corchete angular de cierre", "DE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Corchete angular de apertura", "DE.Controllers.Toolbar.txtBracket_Curve": "Llaves", "DE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Llaves con separador", "DE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Llave de cierre", "DE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Llave de apertura", "DE.Controllers.Toolbar.txtBracket_Custom_1": "Casos (dos condiciones)", "DE.Controllers.Toolbar.txtBracket_Custom_2": "Casos (tres condiciones)", "DE.Controllers.Toolbar.txtBracket_Custom_3": "Objeto de pila", "DE.Controllers.Toolbar.txtBracket_Custom_4": "Objeto acotado entre paréntesis", "DE.Controllers.Toolbar.txtBracket_Custom_5": "Ejemplo de casos", "DE.Controllers.Toolbar.txtBracket_Custom_6": "Coeficiente de binomio", "DE.Controllers.Toolbar.txtBracket_Custom_7": "Coeficiente binomial en corchetes angulares", "DE.Controllers.Toolbar.txtBracket_Line": "Plecas", "DE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Pleca de cierre", "DE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Pleca de apertura", "DE.Controllers.Toolbar.txtBracket_LineDouble": "<PERSON><PERSON><PERSON> dobles", "DE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Pleca doble de cierre", "DE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Pleca doble de apertura", "DE.Controllers.Toolbar.txtBracket_LowLim": "Corchete inferior", "DE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Corchete inferior de cierre", "DE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Corchete inferior de apertura", "DE.Controllers.Toolbar.txtBracket_Round": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Paréntesis con separador", "DE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Paréntesis de cierre", "DE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Paréntesis de apertura", "DE.Controllers.Toolbar.txtBracket_Square": "Corchetes", "DE.Controllers.Toolbar.txtBracket_Square_CloseClose": "Marcador de posición entre dos corchetes de cierre", "DE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "Corchetes invertidos", "DE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Corchete de cierre", "DE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Corchete de apertura", "DE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "Marcador de posición entre dos corchetes de apertura", "DE.Controllers.Toolbar.txtBracket_SquareDouble": "Corchetes dobles", "DE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Corchete doble de cierre", "DE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Corchete doble de apertura", "DE.Controllers.Toolbar.txtBracket_UppLim": "Corchete de techo", "DE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Corchete de techo de cierre", "DE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Corchete de techo de apertura", "DE.Controllers.Toolbar.txtFractionDiagonal": "Fracción sesgada", "DE.Controllers.Toolbar.txtFractionDifferential_1": "dx sobre dy", "DE.Controllers.Toolbar.txtFractionDifferential_2": "Delta mayúscula y sobre delta mayúscula x", "DE.Controllers.Toolbar.txtFractionDifferential_3": "y parcial sobre x parcial", "DE.Controllers.Toolbar.txtFractionDifferential_4": "Delta y sobre delta x", "DE.Controllers.Toolbar.txtFractionHorizontal": "Fracción lineal", "DE.Controllers.Toolbar.txtFractionPi_2": "Pi dividir a 2", "DE.Controllers.Toolbar.txtFractionSmall": "Fracción pequeña", "DE.Controllers.Toolbar.txtFractionVertical": "Fracción apilada", "DE.Controllers.Toolbar.txtFunction_1_Cos": "Función de coseno inversa", "DE.Controllers.Toolbar.txtFunction_1_Cosh": "Función de coseno inversa hiperbólica", "DE.Controllers.Toolbar.txtFunction_1_Cot": "Función de cotangente inversa", "DE.Controllers.Toolbar.txtFunction_1_Coth": "Función de cotangente inversa hiperbólica", "DE.Controllers.Toolbar.txtFunction_1_Csc": "Función de cosecante inversa", "DE.Controllers.Toolbar.txtFunction_1_Csch": "Función de cosecante inversa hiperbólica", "DE.Controllers.Toolbar.txtFunction_1_Sec": "Función de secante inversa", "DE.Controllers.Toolbar.txtFunction_1_Sech": "Función de secante inversa hiperbólica", "DE.Controllers.Toolbar.txtFunction_1_Sin": "Función de seno inversa", "DE.Controllers.Toolbar.txtFunction_1_Sinh": "Función de seno inversa hiperbólica", "DE.Controllers.Toolbar.txtFunction_1_Tan": "Función de tangente inversa", "DE.Controllers.Toolbar.txtFunction_1_Tanh": "Función de tangente inversa hiperbólica", "DE.Controllers.Toolbar.txtFunction_Cos": "Función de coseno", "DE.Controllers.Toolbar.txtFunction_Cosh": "Función de coseno hiperbólica", "DE.Controllers.Toolbar.txtFunction_Cot": "Función de cotangente", "DE.Controllers.Toolbar.txtFunction_Coth": "Función de cotangente hiperbólica", "DE.Controllers.Toolbar.txtFunction_Csc": "Función de cosecante", "DE.Controllers.Toolbar.txtFunction_Csch": "Función de cosecante hiperbólica", "DE.Controllers.Toolbar.txtFunction_Custom_1": "Seno zeta", "DE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "DE.Controllers.Toolbar.txtFunction_Custom_3": "Fórmula de tangente", "DE.Controllers.Toolbar.txtFunction_Sec": "Función de secante", "DE.Controllers.Toolbar.txtFunction_Sech": "Función de secante hiperbólica", "DE.Controllers.Toolbar.txtFunction_Sin": "Función de seno", "DE.Controllers.Toolbar.txtFunction_Sinh": "Función de seno hiperbólica", "DE.Controllers.Toolbar.txtFunction_Tan": "Función de tangente", "DE.Controllers.Toolbar.txtFunction_Tanh": "Función de tangente hiperbólica", "DE.Controllers.Toolbar.txtIntegral": "Integral", "DE.Controllers.Toolbar.txtIntegral_dtheta": "Diferencial zeta", "DE.Controllers.Toolbar.txtIntegral_dx": "Diferencial x", "DE.Controllers.Toolbar.txtIntegral_dy": "Diferencial y", "DE.Controllers.Toolbar.txtIntegralCenterSubSup": "Integral con límites acotados", "DE.Controllers.Toolbar.txtIntegralDouble": "Integral doble", "DE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Integral doble con límites acotados", "DE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Integral doble con límites", "DE.Controllers.Toolbar.txtIntegralOriented": "Integral de contorno", "DE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Integral de contorno con límites acotados", "DE.Controllers.Toolbar.txtIntegralOrientedDouble": "Integral de superficie", "DE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Integral de superficie con límites acotados", "DE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Integral de superficie con límites", "DE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Integral de contorno con límites", "DE.Controllers.Toolbar.txtIntegralOrientedTriple": "Integral de volumen", "DE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Integral de volumen con límites acotados", "DE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Integral de volumen con límites", "DE.Controllers.Toolbar.txtIntegralSubSup": "Integral con límites", "DE.Controllers.Toolbar.txtIntegralTriple": "Integral triple", "DE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Integral triple con límites acotados", "DE.Controllers.Toolbar.txtIntegralTripleSubSup": "Integral triple con límites", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Y lógico", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Y lógico con límite inferior", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Y lógico con límites", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Y lógico con límite inferior en subíndice", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Y lógico con límites de subíndice/supraíndice", "DE.Controllers.Toolbar.txtLargeOperator_CoProd": "Coproduc<PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Coproducto con límite inferior", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Coproducto con límites", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Coproducto con límite inferior en subíndice", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Coproducto con límites de subíndice/supraíndice", "DE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Sumatoria sobre k de n sobre k", "DE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Sumatoria de i igual a cero a n", "DE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Ejemplo de suma con dos índices", "DE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Ejemplo del producto", "DE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Ejemplo de unión", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction": "O lógico", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "O lógico con límite inferior", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "O lógico con límites", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "O lógico con límite inferior en subíndice", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "O lógico con límites de subíndice/supraíndice", "DE.Controllers.Toolbar.txtLargeOperator_Intersection": "Intersección", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Intersección con límite inferior", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Intersección con límites", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Intersección con límite inferior en subíndice", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Intersección con límites de subíndice/superíndice", "DE.Controllers.Toolbar.txtLargeOperator_Prod": "Producto", "DE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Producto con límite inferior", "DE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Producto con límites", "DE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Producto con límite inferior en subíndice", "DE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Producto con límites de subíndice/superíndice", "DE.Controllers.Toolbar.txtLargeOperator_Sum": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Sumatoria con límite inferior", "DE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Sumatoria con límites", "DE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Sumatoria con límite inferior en subíndice", "DE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Sumatoria con límites de subíndice/supraíndice", "DE.Controllers.Toolbar.txtLargeOperator_Union": "Unión", "DE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Unión con límite inferior", "DE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Unión con límites", "DE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Unión con límite inferior en subíndice", "DE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Unión con límites de subíndice/superíndice", "DE.Controllers.Toolbar.txtLimitLog_Custom_1": "Ejemplo de límite", "DE.Controllers.Toolbar.txtLimitLog_Custom_2": "Ejemplo de máximo", "DE.Controllers.Toolbar.txtLimitLog_Lim": "Límite", "DE.Controllers.Toolbar.txtLimitLog_Ln": "Logaritmo natural", "DE.Controllers.Toolbar.txtLimitLog_Log": "Logaritmo", "DE.Controllers.Toolbar.txtLimitLog_LogBase": "Logaritmo", "DE.Controllers.Toolbar.txtLimitLog_Max": "Máximo", "DE.Controllers.Toolbar.txtLimitLog_Min": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtMarginsH": "Márgenes superior e inferior son demasiado altos para una altura de página determinada ", "DE.Controllers.Toolbar.txtMarginsW": "M<PERSON>rgenes izquierdo y derecho son demasiado grandes para una anchura determinada de la página", "DE.Controllers.Toolbar.txtMatrix_1_2": "Matriz vacía de 1x2", "DE.Controllers.Toolbar.txtMatrix_1_3": "Matriz vacía de 1x3", "DE.Controllers.Toolbar.txtMatrix_2_1": "Matriz vacía de 2x1", "DE.Controllers.Toolbar.txtMatrix_2_2": "Matriz vacía de 2x2", "DE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "Matriz de 2 por 2 vacía entre plecas dobles", "DE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Determinante de 2 por 2 vacío", "DE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "Matriz de 2 por 2 vacía entre paréntesis", "DE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "Matriz de 2 por 2 vacía entre paréntesis", "DE.Controllers.Toolbar.txtMatrix_2_3": "Matriz vacía de 2x3", "DE.Controllers.Toolbar.txtMatrix_3_1": "Matriz vacía de 3x1", "DE.Controllers.Toolbar.txtMatrix_3_2": "Matriz vacía de 3x2", "DE.Controllers.Toolbar.txtMatrix_3_3": "Matriz vacía de 3x3", "DE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Puntos en línea base", "DE.Controllers.Toolbar.txtMatrix_Dots_Center": "Puntos en línea media", "DE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "<PERSON><PERSON><PERSON> diagonales", "DE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "Puntos verticales", "DE.Controllers.Toolbar.txtMatrix_Flat_Round": "<PERSON><PERSON> dispersa entre parén<PERSON>is", "DE.Controllers.Toolbar.txtMatrix_Flat_Square": "<PERSON><PERSON> dispersa entre corchetes", "DE.Controllers.Toolbar.txtMatrix_Identity_2": "Matriz de identidad de 2x2 con ceros", "DE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "Matriz de identidad de 2x2 con celdas en blanco que no están en la diagonal", "DE.Controllers.Toolbar.txtMatrix_Identity_3": "Matriz de identidad de 3x3 con ceros", "DE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "Matriz de identidad de 3x3 con celdas en blanco que no están en la diagonal", "DE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Flecha derecha-izquierda inferior", "DE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Flecha derecha-izquierda superior", "DE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Flecha inferior hacia izquierda", "DE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Flecha superior hacia izquierda", "DE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Flecha inferior hacia derecha", "DE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Flecha superior hacia derecha", "DE.Controllers.Toolbar.txtOperator_ColonEquals": "Dos puntos igual", "DE.Controllers.Toolbar.txtOperator_Custom_1": "Produce", "DE.Controllers.Toolbar.txtOperator_Custom_2": "Produce con delta", "DE.Controllers.Toolbar.txtOperator_Definition": "Igual por definición", "DE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta igual a", "DE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Flecha doble inferior derecha e izquierda", "DE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Flecha doble superior derecha e izquierda", "DE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Flecha inferior hacia izquierda", "DE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Flecha superior hacia izquierda", "DE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Flecha inferior hacia derecha", "DE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Flecha superior hacia derecha", "DE.Controllers.Toolbar.txtOperator_EqualsEquals": "Igual igual", "DE.Controllers.Toolbar.txtOperator_MinusEquals": "Menos igual", "DE.Controllers.Toolbar.txtOperator_PlusEquals": "Más igual", "DE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "Unidad de medida", "DE.Controllers.Toolbar.txtRadicalCustom_1": "Lado derecho de la fórmula cuadrática", "DE.Controllers.Toolbar.txtRadicalCustom_2": "<PERSON><PERSON><PERSON> cuadrada de un cuadrado más b al cuadrado", "DE.Controllers.Toolbar.txtRadicalRoot_2": "<PERSON><PERSON><PERSON> cu<PERSON> con índice", "DE.Controllers.Toolbar.txtRadicalRoot_3": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtRadicalRoot_n": "Radical con índice", "DE.Controllers.Toolbar.txtRadicalSqrt": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtScriptCustom_1": "x subíndice y al cuadrado", "DE.Controllers.Toolbar.txtScriptCustom_2": "e elevado a menos i omega t", "DE.Controllers.Toolbar.txtScriptCustom_3": "x al cuadrado", "DE.Controllers.Toolbar.txtScriptCustom_4": "Y superíndice izquierdo n subíndice izquierdo uno", "DE.Controllers.Toolbar.txtScriptSub": "Subíndice", "DE.Controllers.Toolbar.txtScriptSubSup": "Subíndice/Superíndice", "DE.Controllers.Toolbar.txtScriptSubSupLeft": "Subíndice-superíndice izquierdo", "DE.Controllers.Toolbar.txtScriptSup": "Sobreíndice", "DE.Controllers.Toolbar.txtSymbol_about": "Aproximadamente", "DE.Controllers.Toolbar.txtSymbol_additional": "Complemento", "DE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "DE.Controllers.Toolbar.txtSymbol_alpha": "Alfa", "DE.Controllers.Toolbar.txtSymbol_approx": "Casi igual a", "DE.Controllers.Toolbar.txtSymbol_ast": "Operador asterisco", "DE.Controllers.Toolbar.txtSymbol_beta": "Beta", "DE.Controllers.Toolbar.txtSymbol_beth": "Bet", "DE.Controllers.Toolbar.txtSymbol_bullet": "Operador de viñeta", "DE.Controllers.Toolbar.txtSymbol_cap": "Intersección", "DE.Controllers.Toolbar.txtSymbol_cbrt": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_cdots": "Elipsis horizontal de línea media", "DE.Controllers.Toolbar.txtSymbol_celsius": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "DE.Controllers.Toolbar.txtSymbol_cong": "Aproximadamente igual a", "DE.Controllers.Toolbar.txtSymbol_cup": "Unión", "DE.Controllers.Toolbar.txtSymbol_ddots": "Elipsis en diagonal de derecha a izquierda", "DE.Controllers.Toolbar.txtSymbol_degree": "Grados", "DE.Controllers.Toolbar.txtSymbol_delta": "Delta", "DE.Controllers.Toolbar.txtSymbol_div": "Signo de división", "DE.Controllers.Toolbar.txtSymbol_downarrow": "Flecha hacia abajo", "DE.Controllers.Toolbar.txtSymbol_emptyset": "Conjunto vacío", "DE.Controllers.Toolbar.txtSymbol_epsilon": "Épsilon", "DE.Controllers.Toolbar.txtSymbol_equals": "Igual", "DE.Controllers.Toolbar.txtSymbol_equiv": "Idéntico a", "DE.Controllers.Toolbar.txtSymbol_eta": "Eta", "DE.Controllers.Toolbar.txtSymbol_exists": "Existe", "DE.Controllers.Toolbar.txtSymbol_factorial": "Factorial", "DE.Controllers.Toolbar.txtSymbol_fahrenheit": "Grados Fahrenheit", "DE.Controllers.Toolbar.txtSymbol_forall": "Para todos", "DE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "DE.Controllers.Toolbar.txtSymbol_geq": "Mayor o igual a", "DE.Controllers.Toolbar.txtSymbol_gg": "Mayor que", "DE.Controllers.Toolbar.txtSymbol_greater": "Mayor que", "DE.Controllers.Toolbar.txtSymbol_in": "Elemento de", "DE.Controllers.Toolbar.txtSymbol_inc": "Incremento", "DE.Controllers.Toolbar.txtSymbol_infinity": "Infinito", "DE.Controllers.Toolbar.txtSymbol_iota": "Iota", "DE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "DE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "DE.Controllers.Toolbar.txtSymbol_leftarrow": "Flecha izquierda", "DE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Flecha izquierda-derecha", "DE.Controllers.Toolbar.txtSymbol_leq": "<PERSON>or o igual a", "DE.Controllers.Toolbar.txtSymbol_less": "<PERSON><PERSON> que", "DE.Controllers.Toolbar.txtSymbol_ll": "<PERSON><PERSON> que", "DE.Controllers.Toolbar.txtSymbol_minus": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_mp": "Menos más", "DE.Controllers.Toolbar.txtSymbol_mu": "<PERSON>", "DE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "DE.Controllers.Toolbar.txtSymbol_neq": "No igual a", "DE.Controllers.Toolbar.txtSymbol_ni": "Contiene como miembro", "DE.Controllers.Toolbar.txtSymbol_not": "Signo de negación", "DE.Controllers.Toolbar.txtSymbol_notexists": "No existe", "DE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>", "DE.Controllers.Toolbar.txtSymbol_o": "Ómicron", "DE.Controllers.Toolbar.txtSymbol_omega": "Omega", "DE.Controllers.Toolbar.txtSymbol_partial": "Derivada parcial", "DE.Controllers.Toolbar.txtSymbol_percent": "Po<PERSON>entaj<PERSON>", "DE.Controllers.Toolbar.txtSymbol_phi": "Fi", "DE.Controllers.Toolbar.txtSymbol_pi": "Pi", "DE.Controllers.Toolbar.txtSymbol_plus": "Más", "DE.Controllers.Toolbar.txtSymbol_pm": "<PERSON><PERSON> menos", "DE.Controllers.Toolbar.txtSymbol_propto": "Proporcional a", "DE.Controllers.Toolbar.txtSymbol_psi": "Psi", "DE.Controllers.Toolbar.txtSymbol_qdrt": "<PERSON><PERSON><PERSON>a", "DE.Controllers.Toolbar.txtSymbol_qed": "Lo que era necesario demostrar", "DE.Controllers.Toolbar.txtSymbol_rddots": "Elipsis en diagonal de izquierda a derecha", "DE.Controllers.Toolbar.txtSymbol_rho": "Ro", "DE.Controllers.Toolbar.txtSymbol_rightarrow": "<PERSON><PERSON><PERSON> derecha", "DE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "DE.Controllers.Toolbar.txtSymbol_sqrt": "Signo de radical", "DE.Controllers.Toolbar.txtSymbol_tau": "Tau", "DE.Controllers.Toolbar.txtSymbol_therefore": "Por lo tanto ", "DE.Controllers.Toolbar.txtSymbol_theta": "Zeta", "DE.Controllers.Toolbar.txtSymbol_times": "Signo de multiplicación", "DE.Controllers.Toolbar.txtSymbol_uparrow": "Flecha hacia arriba", "DE.Controllers.Toolbar.txtSymbol_upsilon": "Ípsilon", "DE.Controllers.Toolbar.txtSymbol_varepsilon": "Variante de épsilon", "DE.Controllers.Toolbar.txtSymbol_varphi": "Variante fi", "DE.Controllers.Toolbar.txtSymbol_varpi": "Variante pi", "DE.Controllers.Toolbar.txtSymbol_varrho": "Variante ro", "DE.Controllers.Toolbar.txtSymbol_varsigma": "Variante sigma", "DE.Controllers.Toolbar.txtSymbol_vartheta": "Variante zeta", "DE.Controllers.Toolbar.txtSymbol_vdots": "Elipsis vertical", "DE.Controllers.Toolbar.txtSymbol_xsi": "Csi", "DE.Controllers.Toolbar.txtSymbol_zeta": "<PERSON><PERSON><PERSON>", "DE.Controllers.Viewport.textFitPage": "Ajustar a la página", "DE.Controllers.Viewport.textFitWidth": "Ajustar al ancho", "DE.Controllers.Viewport.txtDarkMode": "<PERSON><PERSON> oscuro", "DE.Views.AddNewCaptionLabelDialog.textLabel": "Etiqueta:", "DE.Views.AddNewCaptionLabelDialog.textLabelError": "La etiqueta no debe estar vacía.", "DE.Views.BookmarksDialog.textAdd": "Agregar", "DE.Views.BookmarksDialog.textBookmarkName": "Nombre de marcador", "DE.Views.BookmarksDialog.textClose": "<PERSON><PERSON><PERSON>", "DE.Views.BookmarksDialog.textCopy": "Copiar ", "DE.Views.BookmarksDialog.textDelete": "Bo<PERSON>r", "DE.Views.BookmarksDialog.textGetLink": "Obtener enlace", "DE.Views.BookmarksDialog.textGoto": "Pasar a", "DE.Views.BookmarksDialog.textHidden": "Marcadores ocultados", "DE.Views.BookmarksDialog.textLocation": "Ubicación", "DE.Views.BookmarksDialog.textName": "Nombre", "DE.Views.BookmarksDialog.textSort": "Ordenar por", "DE.Views.BookmarksDialog.textTitle": "Marcadores", "DE.Views.BookmarksDialog.txtInvalidName": "Nombre de marcador s<PERSON>lo puede contener letras, dí<PERSON><PERSON> y barras bajas y debe comenzar con la letra", "DE.Views.CaptionDialog.textAdd": "Agregar", "DE.Views.CaptionDialog.textAfter": "Después", "DE.Views.CaptionDialog.textBefore": "<PERSON><PERSON>", "DE.Views.CaptionDialog.textCaption": "Leyenda", "DE.Views.CaptionDialog.textChapter": "El capítulo comienza con el estilo", "DE.Views.CaptionDialog.textChapterInc": "Incluya el número de capítulo", "DE.Views.CaptionDialog.textColon": "Colón", "DE.Views.CaptionDialog.textDash": "g<PERSON><PERSON>", "DE.Views.CaptionDialog.textDelete": "Bo<PERSON>r", "DE.Views.CaptionDialog.textEquation": "Ecuación", "DE.Views.CaptionDialog.textExamples": "Ejemplos: Tabla 2-A, Imagen 1.IV", "DE.Views.CaptionDialog.textExclude": "Excluir la etiqueta de la leyenda", "DE.Views.CaptionDialog.textFigure": "<PERSON><PERSON><PERSON>", "DE.Views.CaptionDialog.textHyphen": "g<PERSON><PERSON>", "DE.Views.CaptionDialog.textInsert": "Insertar", "DE.Views.CaptionDialog.textLabel": "<PERSON><PERSON>", "DE.Views.CaptionDialog.textLongDash": "g<PERSON><PERSON> largo", "DE.Views.CaptionDialog.textNumbering": "Numeración", "DE.Views.CaptionDialog.textPeriod": "periodo", "DE.Views.CaptionDialog.textSeparator": "Utilizar separador", "DE.Views.CaptionDialog.textTable": "Tabla", "DE.Views.CaptionDialog.textTitle": "Insertar leyenda", "DE.Views.CellsAddDialog.textCol": "Columnas", "DE.Views.CellsAddDialog.textDown": "Debajo del cursor", "DE.Views.CellsAddDialog.textLeft": "A la izquierda", "DE.Views.CellsAddDialog.textRight": "A la derecha", "DE.Views.CellsAddDialog.textRow": "<PERSON><PERSON>", "DE.Views.CellsAddDialog.textTitle": "Insertar varios", "DE.Views.CellsAddDialog.textUp": "Por encima del cursor", "DE.Views.ChartSettings.text3dDepth": "Profundidad (% de la base)", "DE.Views.ChartSettings.text3dHeight": "Altura (% de la base)", "DE.Views.ChartSettings.text3dRotation": "Rotación 3D", "DE.Views.ChartSettings.textAdvanced": "<PERSON><PERSON> a<PERSON>", "DE.Views.ChartSettings.textAutoscale": "Escalado automático", "DE.Views.ChartSettings.textChartType": "Cambiar tipo de gráfico", "DE.Views.ChartSettings.textDefault": "Rotación por defecto", "DE.Views.ChartSettings.textDown": "Abajo", "DE.Views.ChartSettings.textEditData": "<PERSON><PERSON>", "DE.Views.ChartSettings.textHeight": "Altura", "DE.Views.ChartSettings.textLeft": "Iz<PERSON>erda", "DE.Views.ChartSettings.textNarrow": "Campo de visión estrecho", "DE.Views.ChartSettings.textOriginalSize": "Tamaño real", "DE.Views.ChartSettings.textPerspective": "Perspectiva", "DE.Views.ChartSettings.textRight": "Derecha", "DE.Views.ChartSettings.textRightAngle": "Ejes en ángulo recto", "DE.Views.ChartSettings.textSize": "<PERSON><PERSON><PERSON>", "DE.Views.ChartSettings.textStyle": "<PERSON><PERSON><PERSON>", "DE.Views.ChartSettings.textUndock": "Desacoplar de panel", "DE.Views.ChartSettings.textUp": "Arriba", "DE.Views.ChartSettings.textWiden": "Campo de visión ancho", "DE.Views.ChartSettings.textWidth": "<PERSON><PERSON>", "DE.Views.ChartSettings.textWrap": "Ajuste de texto", "DE.Views.ChartSettings.textX": "Rotación X", "DE.Views.ChartSettings.textY": "Rotación Y", "DE.Views.ChartSettings.txtBehind": "Detrás del texto", "DE.Views.ChartSettings.txtInFront": "Delante del texto", "DE.Views.ChartSettings.txtInline": "En línea con el texto", "DE.Views.ChartSettings.txtSquare": "Cuadrado", "DE.Views.ChartSettings.txtThrough": "A través", "DE.Views.ChartSettings.txtTight": "Estrecho", "DE.Views.ChartSettings.txtTitle": "Gráfico", "DE.Views.ChartSettings.txtTopAndBottom": "Superior e inferior", "DE.Views.ControlSettingsDialog.strGeneral": "General", "DE.Views.ControlSettingsDialog.textAdd": "Agregar", "DE.Views.ControlSettingsDialog.textAppearance": "Aspect<PERSON>", "DE.Views.ControlSettingsDialog.textApplyAll": "Aplicar a todo", "DE.Views.ControlSettingsDialog.textBox": "Cuadro delimitador", "DE.Views.ControlSettingsDialog.textChange": "<PERSON><PERSON>", "DE.Views.ControlSettingsDialog.textCheckbox": "<PERSON><PERSON><PERSON> a marquar", "DE.Views.ControlSettingsDialog.textChecked": "<PERSON><PERSON><PERSON><PERSON> marcado", "DE.Views.ControlSettingsDialog.textColor": "Color", "DE.Views.ControlSettingsDialog.textCombobox": "Cuadro de lista desplegable", "DE.Views.ControlSettingsDialog.textDate": "Formato de la fecha", "DE.Views.ControlSettingsDialog.textDelete": "Bo<PERSON>r", "DE.Views.ControlSettingsDialog.textDisplayName": "Nombre de pantalla", "DE.Views.ControlSettingsDialog.textDown": "Abajo", "DE.Views.ControlSettingsDialog.textDropDown": "Lista desplegable", "DE.Views.ControlSettingsDialog.textFormat": "Mostrar la fecha de esta manera", "DE.Views.ControlSettingsDialog.textLang": "Idioma", "DE.Views.ControlSettingsDialog.textLock": "<PERSON><PERSON><PERSON>", "DE.Views.ControlSettingsDialog.textName": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ControlSettingsDialog.textNone": "<PERSON><PERSON><PERSON>", "DE.Views.ControlSettingsDialog.textPlaceholder": "Marcador de posición", "DE.Views.ControlSettingsDialog.textShowAs": "Mostrar como", "DE.Views.ControlSettingsDialog.textSystemColor": "Sistema", "DE.Views.ControlSettingsDialog.textTag": "Etiqueta", "DE.Views.ControlSettingsDialog.textTitle": "Ajustes de control de contenido", "DE.Views.ControlSettingsDialog.textUnchecked": "Símbolo desactivado", "DE.Views.ControlSettingsDialog.textUp": "Arriba", "DE.Views.ControlSettingsDialog.textValue": "Valor", "DE.Views.ControlSettingsDialog.tipChange": "Cambiar s<PERSON>", "DE.Views.ControlSettingsDialog.txtLockDelete": "El control de contenido no puede", "DE.Views.ControlSettingsDialog.txtLockEdit": "Los contenidos no se pueden editar", "DE.Views.CrossReferenceDialog.textAboveBelow": "Arriba/abajo", "DE.Views.CrossReferenceDialog.textBookmark": "Marcador", "DE.Views.CrossReferenceDialog.textBookmarkText": "<PERSON>ar texto", "DE.Views.CrossReferenceDialog.textCaption": "<PERSON><PERSON><PERSON><PERSON> completo", "DE.Views.CrossReferenceDialog.textEmpty": "La referencia de la solicitud está vacía.", "DE.Views.CrossReferenceDialog.textEndnote": "Nota al Final", "DE.Views.CrossReferenceDialog.textEndNoteNum": "Número de Nota al Final", "DE.Views.CrossReferenceDialog.textEndNoteNumForm": "Número de Nota al Final (formateado)", "DE.Views.CrossReferenceDialog.textEquation": "Ecuación", "DE.Views.CrossReferenceDialog.textFigure": "<PERSON><PERSON><PERSON>", "DE.Views.CrossReferenceDialog.textFootnote": "Nota al pie", "DE.Views.CrossReferenceDialog.textHeading": "Encabezado", "DE.Views.CrossReferenceDialog.textHeadingNum": "Número de Encabezado", "DE.Views.CrossReferenceDialog.textHeadingNumFull": "Número de Encabezado (contexto completo)", "DE.Views.CrossReferenceDialog.textHeadingNumNo": "Número de encabezado (sin contexto)", "DE.Views.CrossReferenceDialog.textHeadingText": "Número de Encabezado", "DE.Views.CrossReferenceDialog.textIncludeAbove": "Incluir arriba/abajo", "DE.Views.CrossReferenceDialog.textInsert": "Insertar", "DE.Views.CrossReferenceDialog.textInsertAs": "Insertar como hipervínculo", "DE.Views.CrossReferenceDialog.textLabelNum": "Solo etiqueta y número", "DE.Views.CrossReferenceDialog.textNoteNum": "Número de nota al pie", "DE.Views.CrossReferenceDialog.textNoteNumForm": "Número de nota al pie (formateado)", "DE.Views.CrossReferenceDialog.textOnlyCaption": "Solo texto de subtítulos", "DE.Views.CrossReferenceDialog.textPageNum": "Número de página", "DE.Views.CrossReferenceDialog.textParagraph": "Ítem numerado", "DE.Views.CrossReferenceDialog.textParaNum": "Número <PERSON>", "DE.Views.CrossReferenceDialog.textParaNumFull": "Númer<PERSON> (contexto completo)", "DE.Views.CrossReferenceDialog.textParaNumNo": "Número de párarfo (sin contexto)", "DE.Views.CrossReferenceDialog.textSeparate": "Separar números con", "DE.Views.CrossReferenceDialog.textTable": "Tabla", "DE.Views.CrossReferenceDialog.textText": "Texto de párrafo", "DE.Views.CrossReferenceDialog.textWhich": "Para qué título", "DE.Views.CrossReferenceDialog.textWhichBookmark": "Para que marcador", "DE.Views.CrossReferenceDialog.textWhichEndnote": "Para qué nota al final", "DE.Views.CrossReferenceDialog.textWhichHeading": "Para qué encabezado", "DE.Views.CrossReferenceDialog.textWhichNote": "Para qué nota al pie", "DE.Views.CrossReferenceDialog.textWhichPara": "Para qué ítem numerado", "DE.Views.CrossReferenceDialog.txtReference": "Insertar referencia a", "DE.Views.CrossReferenceDialog.txtTitle": "Referencia cruzada", "DE.Views.CrossReferenceDialog.txtType": "Tipo de referencia", "DE.Views.CustomColumnsDialog.textColumns": "Número de columnas", "DE.Views.CustomColumnsDialog.textSeparator": "Divisor de columnas", "DE.Views.CustomColumnsDialog.textSpacing": "Espacio entre columnas", "DE.Views.CustomColumnsDialog.textTitle": "Columnas", "DE.Views.DateTimeDialog.confirmDefault": "Establecer formato predeterminado para {0}: \"{1}\"", "DE.Views.DateTimeDialog.textDefault": "Establecer como predeterminado", "DE.Views.DateTimeDialog.textFormat": "Formatos", "DE.Views.DateTimeDialog.textLang": "Idioma", "DE.Views.DateTimeDialog.textUpdate": "Actualizar automáticamente", "DE.Views.DateTimeDialog.txtTitle": "<PERSON><PERSON> y hora", "DE.Views.DocProtection.hintProtectDoc": "Proteger documento", "DE.Views.DocProtection.txtDocProtectedComment": "El documento está protegido.<br><PERSON><PERSON><PERSON> puede insertar comentarios en este documento.", "DE.Views.DocProtection.txtDocProtectedForms": "El documento está protegido.<br><PERSON><PERSON><PERSON> puede rellenar los formularios de este documento.", "DE.Views.DocProtection.txtDocProtectedTrack": "El documento está protegido.<br> <PERSON>uede editar este documento, pero todos los cambios serán revisados.", "DE.Views.DocProtection.txtDocProtectedView": "El documento está protegido.<br><PERSON><PERSON>lo puede visualizar este documento.", "DE.Views.DocProtection.txtDocUnlockDescription": "Introduzca una contraseña para desproteger el documento", "DE.Views.DocProtection.txtProtectDoc": "Proteger documento", "DE.Views.DocProtection.txtUnlockTitle": "Desproteger documento", "DE.Views.DocumentHolder.aboveText": "Encima", "DE.Views.DocumentHolder.addCommentText": "Agregar comentario", "DE.Views.DocumentHolder.advancedDropCapText": "Configuración de Capitalización", "DE.Views.DocumentHolder.advancedEquationText": "Ajustes de la ecuación", "DE.Views.DocumentHolder.advancedFrameText": "<PERSON><PERSON><PERSON><PERSON> a<PERSON> ma<PERSON>o", "DE.Views.DocumentHolder.advancedParagraphText": "<PERSON><PERSON><PERSON>s a<PERSON> párrafo", "DE.Views.DocumentHolder.advancedTableText": "<PERSON><PERSON><PERSON>s a<PERSON>zad<PERSON> de tabla", "DE.Views.DocumentHolder.advancedText": "Configuración avanzada", "DE.Views.DocumentHolder.alignmentText": "Alineación", "DE.Views.DocumentHolder.allLinearText": "Lineal (todos)", "DE.Views.DocumentHolder.allProfText": "Profesional (todos)", "DE.Views.DocumentHolder.belowText": "Abajo", "DE.Views.DocumentHolder.breakBeforeText": "Salto de página antes", "DE.Views.DocumentHolder.bulletsText": "Viñetas y numeración", "DE.Views.DocumentHolder.cellAlignText": "Alineación vertical de celda", "DE.Views.DocumentHolder.cellText": "<PERSON><PERSON>", "DE.Views.DocumentHolder.centerText": "Al centro", "DE.Views.DocumentHolder.chartText": "<PERSON><PERSON><PERSON>s a<PERSON> de gráfico", "DE.Views.DocumentHolder.columnText": "Columna", "DE.Views.DocumentHolder.currLinearText": "Lineal (actual)", "DE.Views.DocumentHolder.currProfText": "Profesional (actual)", "DE.Views.DocumentHolder.deleteColumnText": "Bo<PERSON>r columna", "DE.Views.DocumentHolder.deleteRowText": "<PERSON><PERSON><PERSON> fila", "DE.Views.DocumentHolder.deleteTableText": "Borrar tabla", "DE.Views.DocumentHolder.deleteText": "Bo<PERSON>r", "DE.Views.DocumentHolder.direct270Text": "<PERSON><PERSON>r texto hacia arriba", "DE.Views.DocumentHolder.direct90Text": "<PERSON><PERSON><PERSON> texto hacia abajo", "DE.Views.DocumentHolder.directHText": "Horizontal ", "DE.Views.DocumentHolder.directionText": "Dirección de texto", "DE.Views.DocumentHolder.editChartText": "<PERSON><PERSON>", "DE.Views.DocumentHolder.editFooterText": "Editar pie de página", "DE.Views.DocumentHolder.editHeaderText": "<PERSON><PERSON>", "DE.Views.DocumentHolder.editHyperlinkText": "<PERSON><PERSON>", "DE.Views.DocumentHolder.eqToInlineText": "Cambiar a En línea", "DE.Views.DocumentHolder.guestText": "Visitante", "DE.Views.DocumentHolder.hyperlinkText": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.ignoreAllSpellText": "<PERSON><PERSON><PERSON> todo", "DE.Views.DocumentHolder.ignoreSpellText": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.imageText": "<PERSON><PERSON><PERSON><PERSON> a<PERSON> imagen", "DE.Views.DocumentHolder.insertColumnLeftText": "Columna i<PERSON>erda", "DE.Views.DocumentHolder.insertColumnRightText": "<PERSON><PERSON>na derecha", "DE.Views.DocumentHolder.insertColumnText": "Insertar columna", "DE.Views.DocumentHolder.insertRowAboveText": "Fila de arriba", "DE.Views.DocumentHolder.insertRowBelowText": "<PERSON><PERSON>", "DE.Views.DocumentHolder.insertRowText": "Insertar fila", "DE.Views.DocumentHolder.insertText": "Insertar", "DE.Views.DocumentHolder.keepLinesText": "Mantener líneas juntas", "DE.Views.DocumentHolder.langText": "Seleccionar idioma", "DE.Views.DocumentHolder.latexText": "LaTeX", "DE.Views.DocumentHolder.leftText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.loadSpellText": "Cargando variantes", "DE.Views.DocumentHolder.mergeCellsText": "Unir celdas", "DE.Views.DocumentHolder.moreText": "Más variantes...", "DE.Views.DocumentHolder.noSpellVariantsText": "Sin variantes ", "DE.Views.DocumentHolder.notcriticalErrorTitle": "Advertencia", "DE.Views.DocumentHolder.originalSizeText": "Tamaño real", "DE.Views.DocumentHolder.paragraphText": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.removeHyperlinkText": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.rightText": "Derecho", "DE.Views.DocumentHolder.rowText": "<PERSON><PERSON>", "DE.Views.DocumentHolder.saveStyleText": "<PERSON><PERSON><PERSON> estilo nuevo", "DE.Views.DocumentHolder.selectCellText": "<PERSON><PERSON><PERSON><PERSON><PERSON> celda", "DE.Views.DocumentHolder.selectColumnText": "Seleccionar columna", "DE.Views.DocumentHolder.selectRowText": "Seleccionar fila", "DE.Views.DocumentHolder.selectTableText": "Seleccionar tabla", "DE.Views.DocumentHolder.selectText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.shapeText": "<PERSON><PERSON><PERSON>s a<PERSON> de forma", "DE.Views.DocumentHolder.spellcheckText": "Сorrección ortográfica", "DE.Views.DocumentHolder.splitCellsText": "Di<PERSON><PERSON> celda...", "DE.Views.DocumentHolder.splitCellTitleText": "<PERSON><PERSON><PERSON> celda", "DE.Views.DocumentHolder.strDelete": "Elimine la firma", "DE.Views.DocumentHolder.strDetails": "Detalles de la firma", "DE.Views.DocumentHolder.strSetup": "Preparación de la firma", "DE.Views.DocumentHolder.strSign": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.styleText": "Formateando como Estilo", "DE.Views.DocumentHolder.tableText": "Tabla", "DE.Views.DocumentHolder.textAccept": "Aceptar el cambio", "DE.Views.DocumentHolder.textAlign": "Alinear", "DE.Views.DocumentHolder.textArrange": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textArrangeBack": "Enviar al fondo", "DE.Views.DocumentHolder.textArrangeBackward": "Enviar atrás", "DE.Views.DocumentHolder.textArrangeForward": "<PERSON><PERSON><PERSON> adelante", "DE.Views.DocumentHolder.textArrangeFront": "Traer al primer plano", "DE.Views.DocumentHolder.textCells": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textCol": "Eliminar toda la columna", "DE.Views.DocumentHolder.textContentControls": "Control de contenido", "DE.Views.DocumentHolder.textContinueNumbering": "Continuar numeración", "DE.Views.DocumentHolder.textCopy": "Copiar", "DE.Views.DocumentHolder.textCrop": "Recortar", "DE.Views.DocumentHolder.textCropFill": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textCropFit": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textCut": "Cortar", "DE.Views.DocumentHolder.textDistributeCols": "Distribuir columnas", "DE.Views.DocumentHolder.textDistributeRows": "Distribuir filas", "DE.Views.DocumentHolder.textEditControls": "Los ajustes del control de contenido", "DE.Views.DocumentHolder.textEditPoints": "Modificar puntos", "DE.Views.DocumentHolder.textEditWrapBoundary": "Editar límite de ajuste", "DE.Views.DocumentHolder.textFlipH": "Voltear horizontalmente", "DE.Views.DocumentHolder.textFlipV": "Voltear verticalmente", "DE.Views.DocumentHolder.textFollow": "<PERSON><PERSON><PERSON>ov<PERSON>", "DE.Views.DocumentHolder.textFromFile": "De archivo", "DE.Views.DocumentHolder.textFromStorage": "Desde almacenamiento", "DE.Views.DocumentHolder.textFromUrl": "De URL", "DE.Views.DocumentHolder.textJoinList": "Juntar con lista anterior", "DE.Views.DocumentHolder.textLeft": "Desplazar las celdas hacia la izquierda", "DE.Views.DocumentHolder.textNest": "Tabla nido", "DE.Views.DocumentHolder.textNextPage": "<PERSON><PERSON><PERSON><PERSON> sigu<PERSON>e", "DE.Views.DocumentHolder.textNumberingValue": "Valor de inicio", "DE.Views.DocumentHolder.textPaste": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textPrevPage": "Página anterior", "DE.Views.DocumentHolder.textRefreshField": "Actualice el campo", "DE.Views.DocumentHolder.textReject": "Rechazar el cambio", "DE.Views.DocumentHolder.textRemCheckBox": "Eliminar casilla", "DE.Views.DocumentHolder.textRemComboBox": "Eliminar cuadro combinado", "DE.Views.DocumentHolder.textRemDropdown": "Eliminar lista desplegable", "DE.Views.DocumentHolder.textRemField": "Eliminar campo de texto", "DE.Views.DocumentHolder.textRemove": "Eliminar", "DE.Views.DocumentHolder.textRemoveControl": "Elimine el control de contenido", "DE.Views.DocumentHolder.textRemPicture": "Eliminar imagen", "DE.Views.DocumentHolder.textRemRadioBox": "Eliminar botón de opción", "DE.Views.DocumentHolder.textReplace": "<PERSON><PERSON><PERSON><PERSON><PERSON> imagen", "DE.Views.DocumentHolder.textRotate": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textRotate270": "Girar 90° a la izquierda", "DE.Views.DocumentHolder.textRotate90": "Girar 90° a la derecha", "DE.Views.DocumentHolder.textRow": "Borrar toda la fila", "DE.Views.DocumentHolder.textSeparateList": "Separar lista", "DE.Views.DocumentHolder.textSettings": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textSeveral": "Varias filas/columnas", "DE.Views.DocumentHolder.textShapeAlignBottom": "Alinear en la parte inferior", "DE.Views.DocumentHolder.textShapeAlignCenter": "Alinear al centro", "DE.Views.DocumentHolder.textShapeAlignLeft": "Alinear a la izquierda", "DE.Views.DocumentHolder.textShapeAlignMiddle": "Alinear al medio", "DE.Views.DocumentHolder.textShapeAlignRight": "Alinear a la derecha", "DE.Views.DocumentHolder.textShapeAlignTop": "Alinear en la parte superior", "DE.Views.DocumentHolder.textStartNewList": "Iniciar nueva lista", "DE.Views.DocumentHolder.textStartNumberingFrom": "<PERSON><PERSON><PERSON> valor de inicio", "DE.Views.DocumentHolder.textTitleCellsRemove": "Eliminar celdas", "DE.Views.DocumentHolder.textTOC": "Tabla de contenidos", "DE.Views.DocumentHolder.textTOCSettings": "Ajustes de la tabla de contenidos", "DE.Views.DocumentHolder.textUndo": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textUpdateAll": "Actualice toda la tabla", "DE.Views.DocumentHolder.textUpdatePages": "Actualice solo los números de página", "DE.Views.DocumentHolder.textUpdateTOC": "Actualice la tabla de contenidos", "DE.Views.DocumentHolder.textWrap": "Ajuste de texto", "DE.Views.DocumentHolder.tipIsLocked": "Otro usuario está editando este elemento ahora.", "DE.Views.DocumentHolder.toDictionaryText": "Agregar al diccionario", "DE.Views.DocumentHolder.txtAddBottom": "Agregar borde inferior", "DE.Views.DocumentHolder.txtAddFractionBar": "Agregar barra de fracción", "DE.Views.DocumentHolder.txtAddHor": "Agregar línea horizontal", "DE.Views.DocumentHolder.txtAddLB": "Agregar línea inferior izquierda", "DE.Views.DocumentHolder.txtAddLeft": "Agre<PERSON> borde <PERSON>", "DE.Views.DocumentHolder.txtAddLT": "Agregar línea superior izquierda", "DE.Views.DocumentHolder.txtAddRight": "Agregar borde derecho", "DE.Views.DocumentHolder.txtAddTop": "Agregar borde superior", "DE.Views.DocumentHolder.txtAddVer": "Agregar línea vertical", "DE.Views.DocumentHolder.txtAlignToChar": "Alinear a carácter", "DE.Views.DocumentHolder.txtBehind": "Detrás del texto", "DE.Views.DocumentHolder.txtBorderProps": "Propiedades de borde", "DE.Views.DocumentHolder.txtBottom": "Inferior", "DE.Views.DocumentHolder.txtColumnAlign": "Alineación de columna", "DE.Views.DocumentHolder.txtDecreaseArg": "Decrease argument size", "DE.Views.DocumentHolder.txtDeleteArg": "Eliminar argumento", "DE.Views.DocumentHolder.txtDeleteBreak": "Borrar abertura manual", "DE.Views.DocumentHolder.txtDeleteChars": "<PERSON><PERSON><PERSON> carácteres encerrados", "DE.Views.DocumentHolder.txtDeleteCharsAndSeparators": "Eliminar caracteres encerrados y separadores", "DE.Views.DocumentHolder.txtDeleteEq": "Eliminar ecuación", "DE.Views.DocumentHolder.txtDeleteGroupChar": "Eliminar char", "DE.Views.DocumentHolder.txtDeleteRadical": "Eliminar radical", "DE.Views.DocumentHolder.txtDistribHor": "Distribuir horizontalmente", "DE.Views.DocumentHolder.txtDistribVert": "Distribuir verticalmente", "DE.Views.DocumentHolder.txtEmpty": "(Vacío)", "DE.Views.DocumentHolder.txtFractionLinear": "Cambiar a la fracción lineal", "DE.Views.DocumentHolder.txtFractionSkewed": "Cambiar a la fracción sesgada", "DE.Views.DocumentHolder.txtFractionStacked": "Cambiar a la fracción apilada", "DE.Views.DocumentHolder.txtGroup": "Grupo", "DE.Views.DocumentHolder.txtGroupCharOver": "Char sobre texto", "DE.Views.DocumentHolder.txtGroupCharUnder": "Char debajo de texto", "DE.Views.DocumentHolder.txtHideBottom": "Esconder borde inferior", "DE.Views.DocumentHolder.txtHideBottomLimit": "Esconder límite inferior", "DE.Views.DocumentHolder.txtHideCloseBracket": "Esconder llaves conclusivas", "DE.Views.DocumentHolder.txtHideDegree": "Esconder grado", "DE.Views.DocumentHolder.txtHideHor": "Esconder línea horizontal", "DE.Views.DocumentHolder.txtHideLB": "Esconder línea inferior izquierda ", "DE.Views.DocumentHolder.txtHideLeft": "Esconder borde i<PERSON>o", "DE.Views.DocumentHolder.txtHideLT": "Esconder línea superior izquierda", "DE.Views.DocumentHolder.txtHideOpenBracket": "Esconder llaves de apertura", "DE.Views.DocumentHolder.txtHidePlaceholder": "Esconder marcador de posición", "DE.Views.DocumentHolder.txtHideRight": "Esconder borde derecho", "DE.Views.DocumentHolder.txtHideTop": "Ocultar borde superior", "DE.Views.DocumentHolder.txtHideTopLimit": "Ocultar límite superior", "DE.Views.DocumentHolder.txtHideVer": "Ocultar línea vertical", "DE.Views.DocumentHolder.txtIncreaseArg": "Aumentar el tamaño del argumento", "DE.Views.DocumentHolder.txtInFront": "Delante del texto", "DE.Views.DocumentHolder.txtInline": "En línea con el texto", "DE.Views.DocumentHolder.txtInsertArgAfter": "Insertar argumento después", "DE.Views.DocumentHolder.txtInsertArgBefore": "Insertar argumento antes", "DE.Views.DocumentHolder.txtInsertBreak": "Insertar grieta manual", "DE.Views.DocumentHolder.txtInsertCaption": "Insertar leyenda", "DE.Views.DocumentHolder.txtInsertEqAfter": "Insertar la ecuación después de", "DE.Views.DocumentHolder.txtInsertEqBefore": "Insertar la ecuación antes de", "DE.Views.DocumentHolder.txtKeepTextOnly": "<PERSON><PERSON><PERSON> solo texto", "DE.Views.DocumentHolder.txtLimitChange": "Cambiar ubicación de límites", "DE.Views.DocumentHolder.txtLimitOver": "Límite sobre el texto", "DE.Views.DocumentHolder.txtLimitUnder": "Límite debajo del texto", "DE.Views.DocumentHolder.txtMatchBrackets": "Combinar cochetes a la altura de argumento", "DE.Views.DocumentHolder.txtMatrixAlign": "Alineación de la matriz", "DE.Views.DocumentHolder.txtOverbar": "Barra sobre texto", "DE.Views.DocumentHolder.txtOverwriteCells": "Sobreescribir las celdas", "DE.Views.DocumentHolder.txtPasteSourceFormat": "Mantener el formato original", "DE.Views.DocumentHolder.txtPressLink": "Pulse {0} y haga clic en el enlace", "DE.Views.DocumentHolder.txtPrintSelection": "Imp<PERSON><PERSON>", "DE.Views.DocumentHolder.txtRemFractionBar": "Quitar la barra de fracción", "DE.Views.DocumentHolder.txtRemLimit": "Eliminar límite", "DE.Views.DocumentHolder.txtRemoveAccentChar": "<PERSON><PERSON><PERSON> car<PERSON>cter de acento", "DE.Views.DocumentHolder.txtRemoveBar": "Eliminar barra", "DE.Views.DocumentHolder.txtRemoveWarning": "¿Desea eliminar esta firma?<br>No se puede deshacer.", "DE.Views.DocumentHolder.txtRemScripts": "Eliminar texto", "DE.Views.DocumentHolder.txtRemSubscript": "Eliminar subíndice", "DE.Views.DocumentHolder.txtRemSuperscript": "Eliminar subíndice", "DE.Views.DocumentHolder.txtScriptsAfter": "Letras después de texto", "DE.Views.DocumentHolder.txtScriptsBefore": "Letras antes de texto", "DE.Views.DocumentHolder.txtShowBottomLimit": "Mostrar límite inferior", "DE.Views.DocumentHolder.txtShowCloseBracket": "Mostrar corchete de cierre", "DE.Views.DocumentHolder.txtShowDegree": "Mostrar grado", "DE.Views.DocumentHolder.txtShowOpenBracket": "Mostrar corchete de apertura", "DE.Views.DocumentHolder.txtShowPlaceholder": "Mostrar marcador de posición", "DE.Views.DocumentHolder.txtShowTopLimit": "Mostrar límite superior", "DE.Views.DocumentHolder.txtSquare": "Cuadrado", "DE.Views.DocumentHolder.txtStretchBrackets": "Estirar corchetes", "DE.Views.DocumentHolder.txtThrough": "A través", "DE.Views.DocumentHolder.txtTight": "Estrecho", "DE.Views.DocumentHolder.txtTop": "Superior", "DE.Views.DocumentHolder.txtTopAndBottom": "Superior e inferior", "DE.Views.DocumentHolder.txtUnderbar": "Barra debajo de texto", "DE.Views.DocumentHolder.txtUngroup": "Desagrupar", "DE.Views.DocumentHolder.txtWarnUrl": "Hacer clic en este enlace puede ser perjudicial para su dispositivo y sus datos.<br>¿Está seguro de que quiere continuar?", "DE.Views.DocumentHolder.unicodeText": "Unicode", "DE.Views.DocumentHolder.updateStyleText": "Actualizar estilo %1", "DE.Views.DocumentHolder.vertAlignText": "Alineación vertical", "DE.Views.DropcapSettingsAdvanced.strBorders": "<PERSON><PERSON> y relleno", "DE.Views.DropcapSettingsAdvanced.strDropcap": "Letra capital", "DE.Views.DropcapSettingsAdvanced.strMargins": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textAlign": "Alineación", "DE.Views.DropcapSettingsAdvanced.textAtLeast": "Por lo menos", "DE.Views.DropcapSettingsAdvanced.textAuto": "Auto", "DE.Views.DropcapSettingsAdvanced.textBackColor": "Color de fondo", "DE.Views.DropcapSettingsAdvanced.textBorderColor": "Color de borde", "DE.Views.DropcapSettingsAdvanced.textBorderDesc": "Pulse diagrama o utilice botones para seleccionar bordes", "DE.Views.DropcapSettingsAdvanced.textBorderWidth": "<PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textBottom": "Inferior", "DE.Views.DropcapSettingsAdvanced.textCenter": "Al centro", "DE.Views.DropcapSettingsAdvanced.textColumn": "Columna", "DE.Views.DropcapSettingsAdvanced.textDistance": "Distancia del texto", "DE.Views.DropcapSettingsAdvanced.textExact": "Exacto", "DE.Views.DropcapSettingsAdvanced.textFlow": "<PERSON>", "DE.Views.DropcapSettingsAdvanced.textFont": "Letra ", "DE.Views.DropcapSettingsAdvanced.textFrame": "<PERSON>", "DE.Views.DropcapSettingsAdvanced.textHeight": "Altura", "DE.Views.DropcapSettingsAdvanced.textHorizontal": "Horizontal ", "DE.Views.DropcapSettingsAdvanced.textInline": "<PERSON> flota<PERSON>", "DE.Views.DropcapSettingsAdvanced.textInMargin": "<PERSON> margen", "DE.Views.DropcapSettingsAdvanced.textInText": "En texto", "DE.Views.DropcapSettingsAdvanced.textLeft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textMargin": "Margen", "DE.Views.DropcapSettingsAdvanced.textMove": "<PERSON><PERSON><PERSON><PERSON> con texto", "DE.Views.DropcapSettingsAdvanced.textNone": "<PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textPage": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textParagraph": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textParameters": "Parámetros", "DE.Views.DropcapSettingsAdvanced.textPosition": "Posición", "DE.Views.DropcapSettingsAdvanced.textRelative": "En relación a", "DE.Views.DropcapSettingsAdvanced.textRight": "Derecho", "DE.Views.DropcapSettingsAdvanced.textRowHeight": "Altura en filas", "DE.Views.DropcapSettingsAdvanced.textTitle": "Letra capital - Ajustes avanzados", "DE.Views.DropcapSettingsAdvanced.textTitleFrame": "<PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textTop": "Superior", "DE.Views.DropcapSettingsAdvanced.textVertical": "Vertical", "DE.Views.DropcapSettingsAdvanced.textWidth": "<PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.tipFontName": "<PERSON><PERSON><PERSON> de letra", "DE.Views.DropcapSettingsAdvanced.txtNoBorders": "<PERSON> bordes", "DE.Views.EditListItemDialog.textDisplayName": "Nombre de pantalla", "DE.Views.EditListItemDialog.textNameError": "El nombre de la pantalla no debe estar vacío.", "DE.Views.EditListItemDialog.textValue": "Valor", "DE.Views.EditListItemDialog.textValueError": "Ya existe un elemento con el mismo valor.", "DE.Views.FileMenu.btnBackCaption": "Abrir ubicación del archivo", "DE.Views.FileMenu.btnCloseMenuCaption": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenu.btnCreateNewCaption": "<PERSON><PERSON><PERSON> nuevo", "DE.Views.FileMenu.btnDownloadCaption": "<PERSON><PERSON><PERSON> como", "DE.Views.FileMenu.btnExitCaption": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenu.btnFileOpenCaption": "Abrir", "DE.Views.FileMenu.btnHelpCaption": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenu.btnHistoryCaption": "Historial de las Versiones", "DE.Views.FileMenu.btnInfoCaption": "Info sobre documento", "DE.Views.FileMenu.btnPrintCaption": "Imprimir", "DE.Views.FileMenu.btnProtectCaption": "Proteger", "DE.Views.FileMenu.btnRecentFilesCaption": "Abrir reciente", "DE.Views.FileMenu.btnRenameCaption": "Renombrar", "DE.Views.FileMenu.btnReturnCaption": "Volver a Documento", "DE.Views.FileMenu.btnRightsCaption": "Derechos de acceso", "DE.Views.FileMenu.btnSaveAsCaption": "Guardar como", "DE.Views.FileMenu.btnSaveCaption": "Guardar", "DE.Views.FileMenu.btnSaveCopyAsCaption": "Guardar Copia como", "DE.Views.FileMenu.btnSettingsCaption": "Configuración avanzada", "DE.Views.FileMenu.btnToEditCaption": "Editar documento", "DE.Views.FileMenu.textDownload": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.CreateNew.txtBlank": "Documento en blanco", "DE.Views.FileMenuPanels.CreateNew.txtCreateNew": "<PERSON><PERSON><PERSON> nuevo", "DE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Aplicar", "DE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Agregar autor", "DE.Views.FileMenuPanels.DocumentInfo.txtAddText": "Agregar texto", "DE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Aplicación", "DE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "Autor", "DE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Cambiar derechos de acceso", "DE.Views.FileMenuPanels.DocumentInfo.txtComment": "Comentario", "DE.Views.FileMenuPanels.DocumentInfo.txtCreated": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtFastWV": "Vista rápida de la web", "DE.Views.FileMenuPanels.DocumentInfo.txtLoading": "Cargando...", "DE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Última modificación por", "DE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Última modificación", "DE.Views.FileMenuPanels.DocumentInfo.txtNo": "No", "DE.Views.FileMenuPanels.DocumentInfo.txtOwner": "Propietario", "DE.Views.FileMenuPanels.DocumentInfo.txtPages": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtPageSize": "Tamaño de la página", "DE.Views.FileMenuPanels.DocumentInfo.txtParagraphs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfProducer": "Generador de PDF", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfTagged": "PDF etiquetado", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfVer": "Versión PDF", "DE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Ubicación", "DE.Views.FileMenuPanels.DocumentInfo.txtRights": "Personas que tienen derechos", "DE.Views.FileMenuPanels.DocumentInfo.txtSpaces": "Caracteres con espacios", "DE.Views.FileMenuPanels.DocumentInfo.txtStatistics": "Estadísticas", "DE.Views.FileMenuPanels.DocumentInfo.txtSubject": "<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtSymbols": "Caracteres", "DE.Views.FileMenuPanels.DocumentInfo.txtTags": "Etiquetas", "DE.Views.FileMenuPanels.DocumentInfo.txtTitle": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Subido", "DE.Views.FileMenuPanels.DocumentInfo.txtWords": "Palabras", "DE.Views.FileMenuPanels.DocumentInfo.txtYes": "Sí", "DE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Cambiar derechos de acceso", "DE.Views.FileMenuPanels.DocumentRights.txtRights": "Personas que tienen derechos", "DE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Aviso", "DE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "Con contraseña", "DE.Views.FileMenuPanels.ProtectDoc.strProtect": "Proteger <PERSON>", "DE.Views.FileMenuPanels.ProtectDoc.strSignature": "Con firma", "DE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Editar documento", "DE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "La edición eliminará las firmas del documento.<br>¿Continuar?", "DE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "Este documento ha sido", "DE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "Este documento necesita", "DE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Se han agregado firmas válidas al documento. El documento está protegido contra a la edición.", "DE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Algunas de las firmas digitales del documento no son válidas o no han podido ser verificadas. El documento está protegido frente a la edición.", "DE.Views.FileMenuPanels.ProtectDoc.txtView": "Ver firmas", "DE.Views.FileMenuPanels.Settings.okButtonText": "Aplicar", "DE.Views.FileMenuPanels.Settings.strCoAuthMode": "El modo Co-edición", "DE.Views.FileMenuPanels.Settings.strFast": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.strFontRender": "Hinting", "DE.Views.FileMenuPanels.Settings.strIgnoreWordsInUPPERCASE": "<PERSON><PERSON><PERSON> en MAYÚSCULAS", "DE.Views.FileMenuPanels.Settings.strIgnoreWordsWithNumbers": "<PERSON><PERSON><PERSON> con números", "DE.Views.FileMenuPanels.Settings.strMacrosSettings": "Ajustes de macros", "DE.Views.FileMenuPanels.Settings.strPasteButton": "Mostrar el botón Opciones de pegado cuando se pegue contenido", "DE.Views.FileMenuPanels.Settings.strShowChanges": "Cambios de colaboración en tiempo real", "DE.Views.FileMenuPanels.Settings.strShowComments": "Mostrar comentarios en el texto", "DE.Views.FileMenuPanels.Settings.strShowOthersChanges": "Muestra los cambios de otros usuarios", "DE.Views.FileMenuPanels.Settings.strShowResolvedComments": "Mostrar comentarios resueltos", "DE.Views.FileMenuPanels.Settings.strStrict": "Estricto", "DE.Views.FileMenuPanels.Settings.strTheme": "Tema de interfaz", "DE.Views.FileMenuPanels.Settings.strUnit": "Unidad de medida", "DE.Views.FileMenuPanels.Settings.strZoom": "Valor de zoom predeterminado", "DE.Views.FileMenuPanels.Settings.text10Minutes": "Cada 10 minutos ", "DE.Views.FileMenuPanels.Settings.text30Minutes": "Cada 30 minutos", "DE.Views.FileMenuPanels.Settings.text5Minutes": "Cada 5 minutos", "DE.Views.FileMenuPanels.Settings.text60Minutes": "<PERSON><PERSON> hora", "DE.Views.FileMenuPanels.Settings.textAlignGuides": "Guías de alineación", "DE.Views.FileMenuPanels.Settings.textAutoRecover": "Autorrecuperación", "DE.Views.FileMenuPanels.Settings.textAutoSave": "Guardar automáticamente", "DE.Views.FileMenuPanels.Settings.textDisabled": "Desactivado", "DE.Views.FileMenuPanels.Settings.textForceSave": "Guardar versiones intermedias", "DE.Views.FileMenuPanels.Settings.textMinute": "Cada minuto", "DE.Views.FileMenuPanels.Settings.textOldVersions": "Hacer que los archivos sean compatibles con versiones anteriores de MS Word cuando se guarden como DOCX", "DE.Views.FileMenuPanels.Settings.txtAll": "Ver todo", "DE.Views.FileMenuPanels.Settings.txtAutoCorrect": "Opciones de autocorrección", "DE.Views.FileMenuPanels.Settings.txtCacheMode": "Modo de caché predeterminado", "DE.Views.FileMenuPanels.Settings.txtChangesBalloons": "Mostrar con un clic en los globos", "DE.Views.FileMenuPanels.Settings.txtChangesTip": "Mostrar al mantener el puntero en la información sobre herramientas", "DE.Views.FileMenuPanels.Settings.txtCm": "Centímetro", "DE.Views.FileMenuPanels.Settings.txtCollaboration": "Colaboración", "DE.Views.FileMenuPanels.Settings.txtDarkMode": "Activar el modo oscuro para documentos", "DE.Views.FileMenuPanels.Settings.txtEditingSaving": "Editar y guardar", "DE.Views.FileMenuPanels.Settings.txtFastTip": "Co-edición en tiempo real. Todos los cambios se guardan automáticamente", "DE.Views.FileMenuPanels.Settings.txtFitPage": "Ajustar a la página", "DE.Views.FileMenuPanels.Settings.txtFitWidth": "Ajustar al ancho", "DE.Views.FileMenuPanels.Settings.txtHieroglyphs": "Jeroglíficos", "DE.Views.FileMenuPanels.Settings.txtInch": "Pulgada", "DE.Views.FileMenuPanels.Settings.txtLast": "<PERSON>er últimos", "DE.Views.FileMenuPanels.Settings.txtMac": "como OS X", "DE.Views.FileMenuPanels.Settings.txtNative": "Nativo", "DE.Views.FileMenuPanels.Settings.txtNone": "<PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.txtProofing": "Revisión", "DE.Views.FileMenuPanels.Settings.txtPt": "Punt<PERSON>", "DE.Views.FileMenuPanels.Settings.txtQuickPrint": "Mostrar el botón Impresión Rápida en el encabezado del editor", "DE.Views.FileMenuPanels.Settings.txtQuickPrintTip": "El documento se imprimirá en la última impresora seleccionada o predeterminada", "DE.Views.FileMenuPanels.Settings.txtRunMacros": "Habil<PERSON>r todo", "DE.Views.FileMenuPanels.Settings.txtRunMacrosDesc": "Habilitar todas las macros sin notificación ", "DE.Views.FileMenuPanels.Settings.txtShowTrackChanges": "Mostrar control de cambios", "DE.Views.FileMenuPanels.Settings.txtSpellCheck": "Сorrección ortográfica", "DE.Views.FileMenuPanels.Settings.txtStopMacros": "<PERSON>habili<PERSON> todo", "DE.Views.FileMenuPanels.Settings.txtStopMacrosDesc": "Deshabilitar todas las macros sin notificación", "DE.Views.FileMenuPanels.Settings.txtStrictTip": "Utilice el botón \"Guardar\" para sincronizar los cambios que usted y los demás realicen", "DE.Views.FileMenuPanels.Settings.txtUseAltKey": "Utilice la tecla Alt para navegar por la interfaz de usuario mediante el teclado", "DE.Views.FileMenuPanels.Settings.txtUseOptionKey": "Utilice la tecla Opción para navegar por la interfaz de usuario mediante el teclado", "DE.Views.FileMenuPanels.Settings.txtWarnMacros": "Mostrar notificación", "DE.Views.FileMenuPanels.Settings.txtWarnMacrosDesc": "Deshabilitar todas las macros con notificación", "DE.Views.FileMenuPanels.Settings.txtWin": "como Windows", "DE.Views.FileMenuPanels.Settings.txtWorkspace": "Área de trabajo", "DE.Views.FormSettings.textAlways": "Siempre", "DE.Views.FormSettings.textAnyone": "Cualquiera", "DE.Views.FormSettings.textAspect": "Bloquear relación de aspecto", "DE.Views.FormSettings.textAtLeast": "Al menos", "DE.Views.FormSettings.textAuto": "Automático", "DE.Views.FormSettings.textAutofit": "Autoajustar", "DE.Views.FormSettings.textBackgroundColor": "Color de fondo", "DE.Views.FormSettings.textCheckbox": "<PERSON><PERSON><PERSON>", "DE.Views.FormSettings.textColor": "Color de borde", "DE.Views.FormSettings.textComb": "<PERSON><PERSON><PERSON> de caracteres", "DE.Views.FormSettings.textCombobox": "Cuadro combinado", "DE.Views.FormSettings.textComplex": "Campo complejo", "DE.Views.FormSettings.textConnected": "Campos conectados", "DE.Views.FormSettings.textCreditCard": "<PERSON><PERSON><PERSON><PERSON> de tarjeta de crédito (por ejemplo, 4111-1111-1111-1111)", "DE.Views.FormSettings.textDateField": "Campo Fecha y hora", "DE.Views.FormSettings.textDateFormat": "Mostrar la fecha de esta manera", "DE.Views.FormSettings.textDelete": "Eliminar", "DE.Views.FormSettings.textDigits": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.FormSettings.textDisconnect": "Desconectar", "DE.Views.FormSettings.textDropDown": "Desplegable", "DE.Views.FormSettings.textExact": "Exactamente", "DE.Views.FormSettings.textField": "Campo de texto", "DE.Views.FormSettings.textFillRoles": "¿Quién tiene que rellenar esto?", "DE.Views.FormSettings.textFixed": "Campo de tamaño fijo", "DE.Views.FormSettings.textFormat": "Formato", "DE.Views.FormSettings.textFormatSymbols": "Símbolos permitidos", "DE.Views.FormSettings.textFromFile": "Desde archivo", "DE.Views.FormSettings.textFromStorage": "Desde almacenamiento", "DE.Views.FormSettings.textFromUrl": "Desde URL", "DE.Views.FormSettings.textGroupKey": "Clave de grupo", "DE.Views.FormSettings.textImage": "Imagen", "DE.Views.FormSettings.textKey": "Clave", "DE.Views.FormSettings.textLang": "Idioma", "DE.Views.FormSettings.textLetters": "<PERSON><PERSON>", "DE.Views.FormSettings.textLock": "Bloquear", "DE.Views.FormSettings.textMask": "Máscara arbitraria", "DE.Views.FormSettings.textMaxChars": "Límite de caracteres", "DE.Views.FormSettings.textMulti": "Campo multilínea", "DE.Views.FormSettings.textNever": "Nunca", "DE.Views.FormSettings.textNoBorder": "<PERSON> bordes", "DE.Views.FormSettings.textNone": "<PERSON><PERSON><PERSON>", "DE.Views.FormSettings.textPhone1": "<PERSON><PERSON><PERSON><PERSON> de teléfono (por ejemplo, (*************)", "DE.Views.FormSettings.textPhone2": "<PERSON><PERSON><PERSON><PERSON> de teléfono (por ejemplo, +447911123456)", "DE.Views.FormSettings.textPlaceholder": "Marcador de posición", "DE.Views.FormSettings.textRadiobox": "Botón de opción", "DE.Views.FormSettings.textReg": "Expresión regular", "DE.Views.FormSettings.textRequired": "Necesario", "DE.Views.FormSettings.textScale": "<PERSON>u<PERSON><PERSON> escalar", "DE.Views.FormSettings.textSelectImage": "Seleccionar <PERSON>", "DE.Views.FormSettings.textTag": "Etiqueta", "DE.Views.FormSettings.textTip": "Sugerencia", "DE.Views.FormSettings.textTipAdd": "Agregar valor nuevo", "DE.Views.FormSettings.textTipDelete": "Eliminar valor", "DE.Views.FormSettings.textTipDown": "Mover hacia abajo", "DE.Views.FormSettings.textTipUp": "Mover hacia arriba", "DE.Views.FormSettings.textTooBig": "La imagen es demasiado grande", "DE.Views.FormSettings.textTooSmall": "La imagen es demasiado pequeña", "DE.Views.FormSettings.textUKPassport": "Número de pasaporte británico (por ejemplo, *********)", "DE.Views.FormSettings.textUnlock": "Desb<PERSON>que<PERSON>", "DE.Views.FormSettings.textUSSSN": "SSN de EE.UU. (por ejemplo, ***********)", "DE.Views.FormSettings.textValue": "Opciones de valor", "DE.Views.FormSettings.textWidth": "<PERSON><PERSON>", "DE.Views.FormSettings.textZipCodeUS": "Código postal de EE.UU. (por ejemplo, 92663 o 92663-1234)", "DE.Views.FormsTab.capBtnCheckBox": "<PERSON><PERSON><PERSON>", "DE.Views.FormsTab.capBtnComboBox": "Cuadro combinado", "DE.Views.FormsTab.capBtnComplex": "Campo complejo", "DE.Views.FormsTab.capBtnDownloadForm": "<PERSON><PERSON><PERSON> como oform", "DE.Views.FormsTab.capBtnDropDown": "Lista desplegable", "DE.Views.FormsTab.capBtnEmail": "Dirección de correo electrónico", "DE.Views.FormsTab.capBtnImage": "Imagen", "DE.Views.FormsTab.capBtnManager": "Gestionar roles", "DE.Views.FormsTab.capBtnNext": "Campo siguiente", "DE.Views.FormsTab.capBtnPhone": "Número de teléfono", "DE.Views.FormsTab.capBtnPrev": "Campo anterior", "DE.Views.FormsTab.capBtnRadioBox": "Botón de opción", "DE.Views.FormsTab.capBtnSaveForm": "Guardar como oform", "DE.Views.FormsTab.capBtnSubmit": "Enviar", "DE.Views.FormsTab.capBtnText": "Campo de texto", "DE.Views.FormsTab.capBtnView": "Ver formulario", "DE.Views.FormsTab.capCreditCard": "Tarjeta de crédito", "DE.Views.FormsTab.capDateTime": "<PERSON><PERSON> y hora", "DE.Views.FormsTab.capZipCode": "Código postal", "DE.Views.FormsTab.textAnyone": "Cualquiera", "DE.Views.FormsTab.textClear": "Bo<PERSON>r campos", "DE.Views.FormsTab.textClearFields": "Borrar todos los campos", "DE.Views.FormsTab.textCreateForm": "Agregue campos y cree un documento OFORM rellenable", "DE.Views.FormsTab.textGotIt": "Entiendo", "DE.Views.FormsTab.textHighlight": "Ajustes de resaltado", "DE.Views.FormsTab.textNoHighlight": "No resaltar", "DE.Views.FormsTab.textRequired": "Rellene todos los campos obligatorios para enviar el formulario.", "DE.Views.FormsTab.textSubmited": "El formulario se ha enviado correctamente", "DE.Views.FormsTab.tipCheckBox": "Insertar casilla", "DE.Views.FormsTab.tipComboBox": "Insertar cuadro combinado", "DE.Views.FormsTab.tipComplexField": "Insertar campo complejo", "DE.Views.FormsTab.tipCreditCard": "Insertar el número de tarjeta de crédito", "DE.Views.FormsTab.tipDateTime": "Insertar fecha y hora", "DE.Views.FormsTab.tipDownloadForm": "Descargar el archivo como documento OFORM rellenable", "DE.Views.FormsTab.tipDropDown": "Insertar lista desplegable", "DE.Views.FormsTab.tipEmailField": "Insertar dirección de correo electrónico", "DE.Views.FormsTab.tipFixedText": "Insertar campo de texto fijo", "DE.Views.FormsTab.tipImageField": "Insertar imagen", "DE.Views.FormsTab.tipInlineText": "Insertar campo de texto alineado", "DE.Views.FormsTab.tipManager": "Gestionar roles", "DE.Views.FormsTab.tipNextForm": "Ir al siguiente campo", "DE.Views.FormsTab.tipPhoneField": "Insertar número de teléfono", "DE.Views.FormsTab.tipPrevForm": "Ir al campo anterior", "DE.Views.FormsTab.tipRadioBox": "Insertar botón de opción", "DE.Views.FormsTab.tipSaveForm": "Guardar el archivo como un documento OFORM rellenable", "DE.Views.FormsTab.tipSubmit": "Enviar formulario", "DE.Views.FormsTab.tipTextField": "Insertar campo de texto", "DE.Views.FormsTab.tipViewForm": "Ver formulario", "DE.Views.FormsTab.tipZipCode": "Insertar código postal", "DE.Views.FormsTab.txtFixedDesc": "Insertar campo de texto fijo", "DE.Views.FormsTab.txtFixedText": "<PERSON><PERSON>", "DE.Views.FormsTab.txtInlineDesc": "Insertar campo de texto alineado", "DE.Views.FormsTab.txtInlineText": "<PERSON><PERSON><PERSON>", "DE.Views.FormsTab.txtUntitled": "Sin título", "DE.Views.HeaderFooterSettings.textBottomCenter": "Inferior centro", "DE.Views.HeaderFooterSettings.textBottomLeft": "Inferior izquierdo", "DE.Views.HeaderFooterSettings.textBottomPage": "Final de la página", "DE.Views.HeaderFooterSettings.textBottomRight": "Inferior derecho", "DE.Views.HeaderFooterSettings.textDiffFirst": "Primera página diferente", "DE.Views.HeaderFooterSettings.textDiffOdd": "Páginas impares y pares diferentes", "DE.Views.HeaderFooterSettings.textFrom": "Empezar a", "DE.Views.HeaderFooterSettings.textHeaderFromBottom": "Pie de página desde abajo", "DE.Views.HeaderFooterSettings.textHeaderFromTop": "Encabezado desde arriba", "DE.Views.HeaderFooterSettings.textInsertCurrent": "Introducir en la posición actual", "DE.Views.HeaderFooterSettings.textOptions": "Opciones", "DE.Views.HeaderFooterSettings.textPageNum": "Insertar número de página", "DE.Views.HeaderFooterSettings.textPageNumbering": "Numeración de páginas", "DE.Views.HeaderFooterSettings.textPosition": "Posición", "DE.Views.HeaderFooterSettings.textPrev": "<PERSON><PERSON><PERSON>r desde el anterior", "DE.Views.HeaderFooterSettings.textSameAs": "<PERSON><PERSON><PERSON> con <PERSON>", "DE.Views.HeaderFooterSettings.textTopCenter": "Superior centro", "DE.Views.HeaderFooterSettings.textTopLeft": "Superior izquierdo", "DE.Views.HeaderFooterSettings.textTopPage": "Principio de la página", "DE.Views.HeaderFooterSettings.textTopRight": "Superior derecho", "DE.Views.HyperlinkSettingsDialog.textDefault": "Fragmento de texto seleccionado", "DE.Views.HyperlinkSettingsDialog.textDisplay": "Mostrar", "DE.Views.HyperlinkSettingsDialog.textExternal": "Enlace externo", "DE.Views.HyperlinkSettingsDialog.textInternal": "Lugar del documento", "DE.Views.HyperlinkSettingsDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.HyperlinkSettingsDialog.textTooltip": "Información en pantalla", "DE.Views.HyperlinkSettingsDialog.textUrl": "Enlace a", "DE.Views.HyperlinkSettingsDialog.txtBeginning": "Principio del documento", "DE.Views.HyperlinkSettingsDialog.txtBookmarks": "Marcadores", "DE.Views.HyperlinkSettingsDialog.txtEmpty": "Este campo es obligatorio", "DE.Views.HyperlinkSettingsDialog.txtHeadings": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.HyperlinkSettingsDialog.txtNotUrl": "Este campo debe ser URL en el formato \"http://www.example.com\"", "DE.Views.HyperlinkSettingsDialog.txtSizeLimit": "Este campo está limitado a 2083 caracteres", "DE.Views.ImageSettings.textAdvanced": "<PERSON><PERSON> a<PERSON>", "DE.Views.ImageSettings.textCrop": "Recortar", "DE.Views.ImageSettings.textCropFill": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettings.textCropFit": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettings.textCropToShape": "Recortar a la forma", "DE.Views.ImageSettings.textEdit": "<PERSON><PERSON>", "DE.Views.ImageSettings.textEditObject": "<PERSON>ar objeto", "DE.Views.ImageSettings.textFitMargins": "Ajustar al margen", "DE.Views.ImageSettings.textFlip": "Volteo", "DE.Views.ImageSettings.textFromFile": "De archivo", "DE.Views.ImageSettings.textFromStorage": "Desde almacenamiento", "DE.Views.ImageSettings.textFromUrl": "De URL", "DE.Views.ImageSettings.textHeight": "Altura", "DE.Views.ImageSettings.textHint270": "Girar 90° a la izquierda", "DE.Views.ImageSettings.textHint90": "Girar 90° a la derecha", "DE.Views.ImageSettings.textHintFlipH": "Volteo Horizontal", "DE.Views.ImageSettings.textHintFlipV": "Volteo Vertical", "DE.Views.ImageSettings.textInsert": "<PERSON><PERSON><PERSON><PERSON><PERSON> imagen", "DE.Views.ImageSettings.textOriginalSize": "Tamaño real", "DE.Views.ImageSettings.textRecentlyUsed": "Usados recientemente", "DE.Views.ImageSettings.textRotate90": "Girar 90°", "DE.Views.ImageSettings.textRotation": "Rotación", "DE.Views.ImageSettings.textSize": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettings.textWidth": "<PERSON><PERSON>", "DE.Views.ImageSettings.textWrap": "Ajuste de texto", "DE.Views.ImageSettings.txtBehind": "Detrás del texto", "DE.Views.ImageSettings.txtInFront": "Delante del texto", "DE.Views.ImageSettings.txtInline": "En línea con el texto", "DE.Views.ImageSettings.txtSquare": "Cuadrado", "DE.Views.ImageSettings.txtThrough": "A través", "DE.Views.ImageSettings.txtTight": "Estrecho", "DE.Views.ImageSettings.txtTopAndBottom": "Superior e inferior", "DE.Views.ImageSettingsAdvanced.strMargins": "Espaciado del texto", "DE.Views.ImageSettingsAdvanced.textAbsoluteWH": "Absoluto", "DE.Views.ImageSettingsAdvanced.textAlignment": "Alineación", "DE.Views.ImageSettingsAdvanced.textAlt": "Texto alternativo", "DE.Views.ImageSettingsAdvanced.textAltDescription": "Descripción", "DE.Views.ImageSettingsAdvanced.textAltTip": "Representación de texto alternativa de la información sobre el objeto visual, que se leerá para las personas con deficiencia visual o deterioro cognitivo para ayudarles a entender mejor, que información contiene la imagen, autoforma, gráfica o tabla.", "DE.Views.ImageSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textAngle": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textArrows": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textAspectRatio": "Bloquear relación de aspecto", "DE.Views.ImageSettingsAdvanced.textAutofit": "Autoajustar", "DE.Views.ImageSettingsAdvanced.textBeginSize": "Tam<PERSON>ño inicial", "DE.Views.ImageSettingsAdvanced.textBeginStyle": "<PERSON><PERSON><PERSON> inici<PERSON>", "DE.Views.ImageSettingsAdvanced.textBelow": "abajo", "DE.Views.ImageSettingsAdvanced.textBevel": "Biselado", "DE.Views.ImageSettingsAdvanced.textBottom": "Inferior", "DE.Views.ImageSettingsAdvanced.textBottomMargin": "Margen inferior", "DE.Views.ImageSettingsAdvanced.textBtnWrap": "Ajuste de texto", "DE.Views.ImageSettingsAdvanced.textCapType": "<PERSON><PERSON><PERSON> de remate", "DE.Views.ImageSettingsAdvanced.textCenter": "Al centro", "DE.Views.ImageSettingsAdvanced.textCharacter": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textColumn": "Columna", "DE.Views.ImageSettingsAdvanced.textDistance": "Distancia desde el texto", "DE.Views.ImageSettingsAdvanced.textEndSize": "Tamaño final", "DE.Views.ImageSettingsAdvanced.textEndStyle": "Estilo final", "DE.Views.ImageSettingsAdvanced.textFlat": "Plano", "DE.Views.ImageSettingsAdvanced.textFlipped": "Volteado", "DE.Views.ImageSettingsAdvanced.textHeight": "Altura", "DE.Views.ImageSettingsAdvanced.textHorizontal": "Horizontal ", "DE.Views.ImageSettingsAdvanced.textHorizontally": "Horizontalmente", "DE.Views.ImageSettingsAdvanced.textJoinType": "Tipo de combinación", "DE.Views.ImageSettingsAdvanced.textKeepRatio": "Proporciones constantes", "DE.Views.ImageSettingsAdvanced.textLeft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textLeftMargin": "<PERSON><PERSON> i<PERSON>o", "DE.Views.ImageSettingsAdvanced.textLine": "Lín<PERSON>", "DE.Views.ImageSettingsAdvanced.textLineStyle": "<PERSON>stilo <PERSON>", "DE.Views.ImageSettingsAdvanced.textMargin": "Margen", "DE.Views.ImageSettingsAdvanced.textMiter": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textMove": "<PERSON><PERSON><PERSON>zar objeto con texto", "DE.Views.ImageSettingsAdvanced.textOptions": "Opciones", "DE.Views.ImageSettingsAdvanced.textOriginalSize": "Tamaño real", "DE.Views.ImageSettingsAdvanced.textOverlap": "Superposición", "DE.Views.ImageSettingsAdvanced.textPage": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textParagraph": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textPosition": "Posición", "DE.Views.ImageSettingsAdvanced.textPositionPc": "Posición Relativa", "DE.Views.ImageSettingsAdvanced.textRelative": "en relación a", "DE.Views.ImageSettingsAdvanced.textRelativeWH": "Relativo", "DE.Views.ImageSettingsAdvanced.textResizeFit": "Ajustar tamaño de la forma al texto", "DE.Views.ImageSettingsAdvanced.textRight": "Derecho", "DE.Views.ImageSettingsAdvanced.textRightMargin": "Margen derecho", "DE.Views.ImageSettingsAdvanced.textRightOf": "a la derecha de", "DE.Views.ImageSettingsAdvanced.textRotation": "Rotación", "DE.Views.ImageSettingsAdvanced.textRound": "Redondeado", "DE.Views.ImageSettingsAdvanced.textShape": "Ajustes de forma", "DE.Views.ImageSettingsAdvanced.textSize": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textSquare": "Cuadrado", "DE.Views.ImageSettingsAdvanced.textTextBox": "Cuadro de texto", "DE.Views.ImageSettingsAdvanced.textTitle": "Imagen - <PERSON><PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textTitleChart": "Gráfico - Ajustes <PERSON>", "DE.Views.ImageSettingsAdvanced.textTitleShape": "Forma - <PERSON><PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textTop": "Superior", "DE.Views.ImageSettingsAdvanced.textTopMargin": "Margen superior", "DE.Views.ImageSettingsAdvanced.textVertical": "Vertical", "DE.Views.ImageSettingsAdvanced.textVertically": "Verticalmente", "DE.Views.ImageSettingsAdvanced.textWeightArrows": "Grosores y flechas", "DE.Views.ImageSettingsAdvanced.textWidth": "<PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textWrap": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textWrapBehindTooltip": "Detrás del texto", "DE.Views.ImageSettingsAdvanced.textWrapInFrontTooltip": "Delante del texto", "DE.Views.ImageSettingsAdvanced.textWrapInlineTooltip": "En línea con el texto", "DE.Views.ImageSettingsAdvanced.textWrapSquareTooltip": "Cuadrado", "DE.Views.ImageSettingsAdvanced.textWrapThroughTooltip": "A través", "DE.Views.ImageSettingsAdvanced.textWrapTightTooltip": "Estrecho", "DE.Views.ImageSettingsAdvanced.textWrapTopbottomTooltip": "Superior e inferior", "DE.Views.LeftMenu.tipAbout": "Acerca de", "DE.Views.LeftMenu.tipChat": "Cha<PERSON>", "DE.Views.LeftMenu.tipComments": "Comentarios", "DE.Views.LeftMenu.tipNavigation": "Navegación", "DE.Views.LeftMenu.tipOutline": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.LeftMenu.tipPageThumbnails": "Miniaturas de página", "DE.Views.LeftMenu.tipPlugins": "Plugins", "DE.Views.LeftMenu.tipSearch": "Búsqueda", "DE.Views.LeftMenu.tipSupport": "Feedback y Soporte", "DE.Views.LeftMenu.tipTitles": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.LeftMenu.txtDeveloper": "MODO DE DESARROLLO", "DE.Views.LeftMenu.txtEditor": "Editor de documentos", "DE.Views.LeftMenu.txtLimit": "Limitar acceso", "DE.Views.LeftMenu.txtTrial": "MODO DE PRUEBA", "DE.Views.LeftMenu.txtTrialDev": "Modo de programador de prueba", "DE.Views.LineNumbersDialog.textAddLineNumbering": "Agregar numeración de líneas", "DE.Views.LineNumbersDialog.textApplyTo": "Aplicar cambios a", "DE.Views.LineNumbersDialog.textContinuous": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.LineNumbersDialog.textCountBy": "Contar por", "DE.Views.LineNumbersDialog.textDocument": "Todo el documento", "DE.Views.LineNumbersDialog.textForward": "Este punto adelante", "DE.Views.LineNumbersDialog.textFromText": "Del texto", "DE.Views.LineNumbersDialog.textNumbering": "Numeración", "DE.Views.LineNumbersDialog.textRestartEachPage": "Reiniciar cada página", "DE.Views.LineNumbersDialog.textRestartEachSection": "Reiniciar cada sección", "DE.Views.LineNumbersDialog.textSection": "Sección actual", "DE.Views.LineNumbersDialog.textStartAt": "Empezar en", "DE.Views.LineNumbersDialog.textTitle": "Numeración de líneas", "DE.Views.LineNumbersDialog.txtAutoText": "Auto", "DE.Views.Links.capBtnAddText": "Agregar texto", "DE.Views.Links.capBtnBookmarks": "Marcador", "DE.Views.Links.capBtnCaption": "Leyenda", "DE.Views.Links.capBtnContentsUpdate": "Actualizar la tabla", "DE.Views.Links.capBtnCrossRef": "Referencia cruzada", "DE.Views.Links.capBtnInsContents": "Tabla de contenidos", "DE.Views.Links.capBtnInsFootnote": "Nota a pie de página", "DE.Views.Links.capBtnInsLink": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Links.capBtnTOF": "Tabla de ilustraciones", "DE.Views.Links.confirmDeleteFootnotes": "¿Usted desea eliminar todas", "DE.Views.Links.confirmReplaceTOF": "¿Quiere reemplazar la tabla de ilustraciones seleccionada?", "DE.Views.Links.mniConvertNote": "Convertir todas las notas", "DE.Views.Links.mniDelFootnote": "Eliminar todas las notas", "DE.Views.Links.mniInsEndnote": "Insertar nota al final", "DE.Views.Links.mniInsFootnote": "Insertar nota a pie de página", "DE.Views.Links.mniNoteSettings": "<PERSON><PERSON><PERSON><PERSON> de notas", "DE.Views.Links.textContentsRemove": "Elimine la tabla de contenidos", "DE.Views.Links.textContentsSettings": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Links.textConvertToEndnotes": "Convertir Todas las Notas de Pie a Notas al Final", "DE.Views.Links.textConvertToFootnotes": "Convertir Todas las Notas al Final a Notas de Pie", "DE.Views.Links.textGotoEndnote": "Ir a notas al final", "DE.Views.Links.textGotoFootnote": "Ir a notas a pie de página", "DE.Views.Links.textSwapNotes": "Intercambiar Notas al Pie y Notas al Final", "DE.Views.Links.textUpdateAll": "Actualice toda la tabla", "DE.Views.Links.textUpdatePages": "Actualice solo los números de página", "DE.Views.Links.tipAddText": "Incluir título en la tabla de contenido", "DE.Views.Links.tipBookmarks": "<PERSON><PERSON><PERSON> marcador", "DE.Views.Links.tipCaption": "Insertar leyenda", "DE.Views.Links.tipContents": "Introducir tabla de contenidos", "DE.Views.Links.tipContentsUpdate": "Actualice la tabla de contenidos", "DE.Views.Links.tipCrossRef": "Insertar referencia cruzada", "DE.Views.Links.tipInsertHyperlink": "Agre<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.Links.tipNotes": "Introducir o editar notas a pie de página", "DE.Views.Links.tipTableFigures": "Insertar tabla de ilustraciones", "DE.Views.Links.tipTableFiguresUpdate": "Actualizar la tabla de ilustraciones", "DE.Views.Links.titleUpdateTOF": "Actualizar la tabla de ilustraciones", "DE.Views.Links.txtDontShowTof": "No mostrar en la tabla de contenido", "DE.Views.Links.txtLevel": "<PERSON><PERSON>", "DE.Views.ListSettingsDialog.textAuto": "Automático", "DE.Views.ListSettingsDialog.textCenter": "Al centro", "DE.Views.ListSettingsDialog.textLeft": "Iz<PERSON>erda", "DE.Views.ListSettingsDialog.textLevel": "<PERSON><PERSON>", "DE.Views.ListSettingsDialog.textPreview": "Vista previa", "DE.Views.ListSettingsDialog.textRight": "A la derecha", "DE.Views.ListSettingsDialog.txtAlign": "Alineación", "DE.Views.ListSettingsDialog.txtBullet": "<PERSON><PERSON>ñ<PERSON>", "DE.Views.ListSettingsDialog.txtColor": "Color", "DE.Views.ListSettingsDialog.txtFont": "Fuente y símbolo", "DE.Views.ListSettingsDialog.txtLikeText": "Como un texto", "DE.Views.ListSettingsDialog.txtNewBullet": "Nueva viñeta", "DE.Views.ListSettingsDialog.txtNone": "<PERSON><PERSON><PERSON>", "DE.Views.ListSettingsDialog.txtSize": "<PERSON><PERSON><PERSON>", "DE.Views.ListSettingsDialog.txtSymbol": "Símbolo", "DE.Views.ListSettingsDialog.txtTitle": "Ajustes de lista", "DE.Views.ListSettingsDialog.txtType": "Tipo", "DE.Views.MailMergeEmailDlg.filePlaceholder": "PDF", "DE.Views.MailMergeEmailDlg.okButtonText": "Enviar", "DE.Views.MailMergeEmailDlg.subjectPlaceholder": "<PERSON><PERSON>", "DE.Views.MailMergeEmailDlg.textAttachDocx": "Adjuntar como DOCX", "DE.Views.MailMergeEmailDlg.textAttachPdf": "Adjuntar como PDF", "DE.Views.MailMergeEmailDlg.textFileName": "Nombre de archivo", "DE.Views.MailMergeEmailDlg.textFormat": "Formato", "DE.Views.MailMergeEmailDlg.textFrom": "De", "DE.Views.MailMergeEmailDlg.textHTML": "HTML", "DE.Views.MailMergeEmailDlg.textMessage": "Men<PERSON><PERSON>", "DE.Views.MailMergeEmailDlg.textSubject": "Línea de <PERSON>unto", "DE.Views.MailMergeEmailDlg.textTitle": "Enviar a correo electrónico", "DE.Views.MailMergeEmailDlg.textTo": "Para", "DE.Views.MailMergeEmailDlg.textWarning": "¡Aviso!", "DE.Views.MailMergeEmailDlg.textWarningMsg": "Note, por favor, que no se puede detener el envío, una vez pulsado el botón 'Enviar'.", "DE.Views.MailMergeSettings.downloadMergeTitle": "Fusión", "DE.Views.MailMergeSettings.errorMailMergeSaveFile": "Error de fusión.", "DE.Views.MailMergeSettings.notcriticalErrorTitle": "Aviso", "DE.Views.MailMergeSettings.textAddRecipients": "Primero, agregar algunos destinatarios a la lista ", "DE.Views.MailMergeSettings.textAll": "Todos registros", "DE.Views.MailMergeSettings.textCurrent": "Registro actual", "DE.Views.MailMergeSettings.textDataSource": "Fuente de datos", "DE.Views.MailMergeSettings.textDocx": "Docx", "DE.Views.MailMergeSettings.textDownload": "<PERSON><PERSON><PERSON>", "DE.Views.MailMergeSettings.textEditData": "Editar lista de destinatarios", "DE.Views.MailMergeSettings.textEmail": "<PERSON><PERSON><PERSON>", "DE.Views.MailMergeSettings.textFrom": "De", "DE.Views.MailMergeSettings.textGoToMail": "Siga a Correo", "DE.Views.MailMergeSettings.textHighlight": "Resaltar campos unidos", "DE.Views.MailMergeSettings.textInsertField": "Insertar campo unido", "DE.Views.MailMergeSettings.textMaxRecepients": "Máximo - 100 receptores", "DE.Views.MailMergeSettings.textMerge": "Fusión", "DE.Views.MailMergeSettings.textMergeFields": "Combinar Campos", "DE.Views.MailMergeSettings.textMergeTo": "Fusión en", "DE.Views.MailMergeSettings.textPdf": "PDF", "DE.Views.MailMergeSettings.textPortal": "Guardar", "DE.Views.MailMergeSettings.textPreview": "Vista previa de resultados", "DE.Views.MailMergeSettings.textReadMore": "Más información", "DE.Views.MailMergeSettings.textSendMsg": "Todos los mensajes de correo están listos y se enviarán en breve. <br>La velocidad de envío dependerá de su servicio de correo. <br><PERSON><PERSON><PERSON> continuar trabajando en el documento o cerrarlo. Una vez terminada la operación la notificación se enviará a su dirección de correo de registro.", "DE.Views.MailMergeSettings.textTo": "a", "DE.Views.MailMergeSettings.txtFirst": "Primer campo", "DE.Views.MailMergeSettings.txtFromToError": "El valor \"De\" debe ser menor que el valor \"A\"", "DE.Views.MailMergeSettings.txtLast": "Último campo", "DE.Views.MailMergeSettings.txtNext": "Campo siguente", "DE.Views.MailMergeSettings.txtPrev": "a registro anterior", "DE.Views.MailMergeSettings.txtUntitled": "Sin título", "DE.Views.MailMergeSettings.warnProcessMailMerge": "Imposible empezar fusión", "DE.Views.Navigation.strNavigate": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Navigation.txtClosePanel": "<PERSON><PERSON><PERSON>", "DE.Views.Navigation.txtCollapse": "<PERSON><PERSON>gar todo", "DE.Views.Navigation.txtDemote": "Degradar", "DE.Views.Navigation.txtEmpty": "No hay títulos en el documento.<br>Aplique un estilo de título al texto para que aparezca en la tabla de contenido.", "DE.Views.Navigation.txtEmptyItem": "Encabezado vacío", "DE.Views.Navigation.txtEmptyViewer": "No hay títulos en el documento.", "DE.Views.Navigation.txtExpand": "Expandir todo", "DE.Views.Navigation.txtExpandToLevel": "Expandir a nivel", "DE.Views.Navigation.txtFontSize": "Tamaño de fuente", "DE.Views.Navigation.txtHeadingAfter": "Título nuevo después ", "DE.Views.Navigation.txtHeadingBefore": "<PERSON><PERSON><PERSON><PERSON> nuevo antes", "DE.Views.Navigation.txtLarge": "Grande", "DE.Views.Navigation.txtMedium": "Medio", "DE.Views.Navigation.txtNewHeading": "Subtítulo nuevo", "DE.Views.Navigation.txtPromote": "Promover", "DE.Views.Navigation.txtSelect": "Seleccione contenido", "DE.Views.Navigation.txtSettings": "Ajustes de los títulos", "DE.Views.Navigation.txtSmall": "Pequeño", "DE.Views.Navigation.txtWrapHeadings": "Ajustar tí<PERSON><PERSON> largos", "DE.Views.NoteSettingsDialog.textApply": "Aplicar", "DE.Views.NoteSettingsDialog.textApplyTo": "Aplicar cambios a", "DE.Views.NoteSettingsDialog.textContinue": "Continua", "DE.Views.NoteSettingsDialog.textCustom": "<PERSON><PERSON> personal", "DE.Views.NoteSettingsDialog.textDocEnd": "Final del documento", "DE.Views.NoteSettingsDialog.textDocument": "Todo el documento", "DE.Views.NoteSettingsDialog.textEachPage": "Reiniciar cada página", "DE.Views.NoteSettingsDialog.textEachSection": "Reiniciar cada sección", "DE.Views.NoteSettingsDialog.textEndnote": "Nota al Final", "DE.Views.NoteSettingsDialog.textFootnote": "Nota a pie de página", "DE.Views.NoteSettingsDialog.textFormat": "Formato", "DE.Views.NoteSettingsDialog.textInsert": "Insertar", "DE.Views.NoteSettingsDialog.textLocation": "Ubicación", "DE.Views.NoteSettingsDialog.textNumbering": "Numeración", "DE.Views.NoteSettingsDialog.textNumFormat": "Formato de número", "DE.Views.NoteSettingsDialog.textPageBottom": "Al pie de la página", "DE.Views.NoteSettingsDialog.textSectEnd": "Fin de Sección", "DE.Views.NoteSettingsDialog.textSection": "Sección actual", "DE.Views.NoteSettingsDialog.textStart": "<PERSON><PERSON><PERSON> con", "DE.Views.NoteSettingsDialog.textTextBottom": "Bajo el texto", "DE.Views.NoteSettingsDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON> de notas", "DE.Views.NotesRemoveDialog.textEnd": "Eliminar todas las notas al final", "DE.Views.NotesRemoveDialog.textFoot": "Eliminar todas las notas al pie de página", "DE.Views.NotesRemoveDialog.textTitle": "<PERSON>minar notas", "DE.Views.PageMarginsDialog.notcriticalErrorTitle": "Aviso", "DE.Views.PageMarginsDialog.textBottom": "Inferior", "DE.Views.PageMarginsDialog.textGutter": "<PERSON><PERSON><PERSON>", "DE.Views.PageMarginsDialog.textGutterPosition": "Posición de fijación", "DE.Views.PageMarginsDialog.textInside": "<PERSON><PERSON> de", "DE.Views.PageMarginsDialog.textLandscape": "Horizontal", "DE.Views.PageMarginsDialog.textLeft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.PageMarginsDialog.textMirrorMargins": " <PERSON><PERSON><PERSON><PERSON> ", "DE.Views.PageMarginsDialog.textMultiplePages": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.PageMarginsDialog.textNormal": "Normal", "DE.Views.PageMarginsDialog.textOrientation": "Orientación ", "DE.Views.PageMarginsDialog.textOutside": "Exterior", "DE.Views.PageMarginsDialog.textPortrait": "Vertical", "DE.Views.PageMarginsDialog.textPreview": "Vista previa", "DE.Views.PageMarginsDialog.textRight": "Derecho", "DE.Views.PageMarginsDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.PageMarginsDialog.textTop": "Top", "DE.Views.PageMarginsDialog.txtMarginsH": "Márgenes superior e inferior son demasiado altos para una altura de página determinada ", "DE.Views.PageMarginsDialog.txtMarginsW": "M<PERSON>rgenes izquierdo y derecho son demasiado grandes para una anchura determinada de la página", "DE.Views.PageSizeDialog.textHeight": "Altura", "DE.Views.PageSizeDialog.textPreset": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.PageSizeDialog.textTitle": "Tamaño de página", "DE.Views.PageSizeDialog.textWidth": "<PERSON><PERSON>", "DE.Views.PageSizeDialog.txtCustom": "Personalizado", "DE.Views.PageThumbnails.textClosePanel": "Cerrar las miniaturas de las páginas", "DE.Views.PageThumbnails.textHighlightVisiblePart": "Resaltar la parte visible de la página", "DE.Views.PageThumbnails.textPageThumbnails": "Miniaturas de página", "DE.Views.PageThumbnails.textThumbnailsSettings": "Configuración de las miniaturas", "DE.Views.PageThumbnails.textThumbnailsSize": "Tamaño de las miniaturas", "DE.Views.ParagraphSettings.strIndent": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettings.strIndentsLeftText": "A la izquierda", "DE.Views.ParagraphSettings.strIndentsRightText": "A la derecha", "DE.Views.ParagraphSettings.strIndentsSpecial": "Especial", "DE.Views.ParagraphSettings.strLineHeight": "Espaciado de línea", "DE.Views.ParagraphSettings.strParagraphSpacing": "Espaciado de Párafo ", "DE.Views.ParagraphSettings.strSomeParagraphSpace": "No agregue intervalos entre párrafos del mismo estilo", "DE.Views.ParagraphSettings.strSpacingAfter": "Después", "DE.Views.ParagraphSettings.strSpacingBefore": "<PERSON><PERSON>", "DE.Views.ParagraphSettings.textAdvanced": "<PERSON><PERSON> a<PERSON>", "DE.Views.ParagraphSettings.textAt": "En", "DE.Views.ParagraphSettings.textAtLeast": "Por lo menos", "DE.Views.ParagraphSettings.textAuto": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettings.textBackColor": "Color de fondo", "DE.Views.ParagraphSettings.textExact": "Exacto", "DE.Views.ParagraphSettings.textFirstLine": "Primera línea", "DE.Views.ParagraphSettings.textHanging": "<PERSON><PERSON><PERSON> francesa", "DE.Views.ParagraphSettings.textNoneSpecial": "(ninguno)", "DE.Views.ParagraphSettings.txtAutoText": "Auto", "DE.Views.ParagraphSettingsAdvanced.noTabs": "Las pestañas especificadas aparecerán en este campo", "DE.Views.ParagraphSettingsAdvanced.strAllCaps": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.strBorders": "<PERSON><PERSON> y relleno", "DE.Views.ParagraphSettingsAdvanced.strBreakBefore": "Salto de página antes", "DE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "Doble tachado", "DE.Views.ParagraphSettingsAdvanced.strIndent": "Reti<PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Espaciado de línea", "DE.Views.ParagraphSettingsAdvanced.strIndentsOutlinelevel": "<PERSON><PERSON> de es<PERSON> ", "DE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "Derecho", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "Después", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "<PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Especial", "DE.Views.ParagraphSettingsAdvanced.strKeepLines": "Mantener líneas juntas", "DE.Views.ParagraphSettingsAdvanced.strKeepNext": "Conservar con el siguiente", "DE.Views.ParagraphSettingsAdvanced.strMargins": "Espaciados internos", "DE.Views.ParagraphSettingsAdvanced.strOrphan": "Control de líneas huérfanas", "DE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Letra ", "DE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Sangría y espaciado", "DE.Views.ParagraphSettingsAdvanced.strParagraphLine": "Saltos de línea y saltos de página", "DE.Views.ParagraphSettingsAdvanced.strParagraphPosition": "Ubicación", "DE.Views.ParagraphSettingsAdvanced.strSmallCaps": "<PERSON><PERSON><PERSON><PERSON> pequeñas", "DE.Views.ParagraphSettingsAdvanced.strSomeParagraphSpace": "No agregue intervalos entre párrafos del mismo estilo", "DE.Views.ParagraphSettingsAdvanced.strSpacing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.strStrike": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.strSubscript": "Subíndice", "DE.Views.ParagraphSettingsAdvanced.strSuperscript": "Sobreíndice", "DE.Views.ParagraphSettingsAdvanced.strSuppressLineNumbers": "Suprimir números de línea", "DE.Views.ParagraphSettingsAdvanced.strTabs": "Pestaña", "DE.Views.ParagraphSettingsAdvanced.textAlign": "Alineación", "DE.Views.ParagraphSettingsAdvanced.textAll": "Todo", "DE.Views.ParagraphSettingsAdvanced.textAtLeast": "Por lo menos", "DE.Views.ParagraphSettingsAdvanced.textAuto": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textBackColor": "Color de fondo", "DE.Views.ParagraphSettingsAdvanced.textBodyText": "Texto básico", "DE.Views.ParagraphSettingsAdvanced.textBorderColor": "Color de borde", "DE.Views.ParagraphSettingsAdvanced.textBorderDesc": "Haga clic en diagrama o use botones para seleccionar bordes y aplicar el estilo seleccionado", "DE.Views.ParagraphSettingsAdvanced.textBorderWidth": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textBottom": "Inferior", "DE.Views.ParagraphSettingsAdvanced.textCentered": "Centrado", "DE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Espacia<PERSON> entre caracteres", "DE.Views.ParagraphSettingsAdvanced.textContext": "Contexto", "DE.Views.ParagraphSettingsAdvanced.textContextDiscret": "Contextuales y discrecionales", "DE.Views.ParagraphSettingsAdvanced.textContextHistDiscret": "Contextuales, históricas y discrecionales", "DE.Views.ParagraphSettingsAdvanced.textContextHistorical": "Contextuales e históricas", "DE.Views.ParagraphSettingsAdvanced.textDefault": "Pestaña predeterminada", "DE.Views.ParagraphSettingsAdvanced.textDiscret": "Discrecionalidad", "DE.Views.ParagraphSettingsAdvanced.textEffects": "Efectos", "DE.Views.ParagraphSettingsAdvanced.textExact": "Exactamente", "DE.Views.ParagraphSettingsAdvanced.textFirstLine": "Primera linea", "DE.Views.ParagraphSettingsAdvanced.textHanging": "Suspendido", "DE.Views.ParagraphSettingsAdvanced.textHistorical": "Hist<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textHistoricalDiscret": "Históricas y discrecionales", "DE.Views.ParagraphSettingsAdvanced.textJustified": "Justificado", "DE.Views.ParagraphSettingsAdvanced.textLeader": "Director", "DE.Views.ParagraphSettingsAdvanced.textLeft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textLevel": "<PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textLigatures": "Ligaduras", "DE.Views.ParagraphSettingsAdvanced.textNone": "<PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(ninguno)", "DE.Views.ParagraphSettingsAdvanced.textOpenType": "Características OpenType", "DE.Views.ParagraphSettingsAdvanced.textPosition": "Posición", "DE.Views.ParagraphSettingsAdvanced.textRemove": "Eliminar", "DE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Eliminar todo", "DE.Views.ParagraphSettingsAdvanced.textRight": "Derecho", "DE.Views.ParagraphSettingsAdvanced.textSet": "Especificar", "DE.Views.ParagraphSettingsAdvanced.textSpacing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textStandard": "Solamente estándar", "DE.Views.ParagraphSettingsAdvanced.textStandardContext": "Estándar y contextual", "DE.Views.ParagraphSettingsAdvanced.textStandardContextDiscret": "<PERSON><PERSON><PERSON><PERSON>, contextual y discrecional", "DE.Views.ParagraphSettingsAdvanced.textStandardContextHist": "<PERSON><PERSON><PERSON><PERSON>, contextual e histórico", "DE.Views.ParagraphSettingsAdvanced.textStandardDiscret": "Estándar y discrecional", "DE.Views.ParagraphSettingsAdvanced.textStandardHistDiscret": "Est<PERSON><PERSON>, histórico y discrecional", "DE.Views.ParagraphSettingsAdvanced.textStandardHistorical": "Estándar e histórico", "DE.Views.ParagraphSettingsAdvanced.textTabCenter": "Al centro", "DE.Views.ParagraphSettingsAdvanced.textTabLeft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textTabPosition": "Posición de tab", "DE.Views.ParagraphSettingsAdvanced.textTabRight": "Derecho", "DE.Views.ParagraphSettingsAdvanced.textTitle": "Párrafo - <PERSON><PERSON><PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.textTop": "Superior", "DE.Views.ParagraphSettingsAdvanced.tipAll": "<PERSON>jar borde exterior y todas líneas interiores ", "DE.Views.ParagraphSettingsAdvanced.tipBottom": "<PERSON>jar s<PERSON>lo borde inferior", "DE.Views.ParagraphSettingsAdvanced.tipInner": "Fijar sólo líneas horizontales interiores ", "DE.Views.ParagraphSettingsAdvanced.tipLeft": "<PERSON><PERSON> s<PERSON>lo borde i<PERSON>", "DE.Views.ParagraphSettingsAdvanced.tipNone": "No fijar bordes", "DE.Views.ParagraphSettingsAdvanced.tipOuter": "<PERSON><PERSON> s<PERSON>lo borde <PERSON>", "DE.Views.ParagraphSettingsAdvanced.tipRight": "<PERSON><PERSON> s<PERSON>lo borde derecho", "DE.Views.ParagraphSettingsAdvanced.tipTop": "<PERSON>jar s<PERSON>lo borde superior", "DE.Views.ParagraphSettingsAdvanced.txtAutoText": "Auto", "DE.Views.ParagraphSettingsAdvanced.txtNoBorders": "<PERSON> bordes", "DE.Views.PrintWithPreview.textMarginsLast": "Último personalizado", "DE.Views.PrintWithPreview.textMarginsModerate": "Moderado", "DE.Views.PrintWithPreview.textMarginsNarrow": "Estrecho", "DE.Views.PrintWithPreview.textMarginsNormal": "Normal", "DE.Views.PrintWithPreview.textMarginsUsNormal": "US Normal", "DE.Views.PrintWithPreview.textMarginsWide": "Amplio", "DE.Views.PrintWithPreview.txtAllPages": "Todas las páginas", "DE.Views.PrintWithPreview.txtBottom": "Parte inferior", "DE.Views.PrintWithPreview.txtCurrentPage": "<PERSON><PERSON>gin<PERSON> actual", "DE.Views.PrintWithPreview.txtCustom": "Personalizado", "DE.Views.PrintWithPreview.txtCustomPages": "Impresión personalizada", "DE.Views.PrintWithPreview.txtLandscape": "Horizontal", "DE.Views.PrintWithPreview.txtLeft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.PrintWithPreview.txtMargins": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.PrintWithPreview.txtOf": "de {0}", "DE.Views.PrintWithPreview.txtPage": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.PrintWithPreview.txtPageNumInvalid": "Número de página no válido", "DE.Views.PrintWithPreview.txtPageOrientation": "Orientación de página", "DE.Views.PrintWithPreview.txtPages": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.PrintWithPreview.txtPageSize": "Tamaño de página", "DE.Views.PrintWithPreview.txtPortrait": "Vertical", "DE.Views.PrintWithPreview.txtPrint": "Imprimir", "DE.Views.PrintWithPreview.txtPrintPdf": "Imprimir en PDF", "DE.Views.PrintWithPreview.txtPrintRange": "Intervalo de impresión", "DE.Views.PrintWithPreview.txtRight": "Derecho", "DE.Views.PrintWithPreview.txtSelection": "Selección ", "DE.Views.PrintWithPreview.txtTop": "Parte superior", "DE.Views.ProtectDialog.textComments": "Comentarios", "DE.Views.ProtectDialog.textForms": "<PERSON><PERSON><PERSON> de formularios", "DE.Views.ProtectDialog.textReview": "Cambios realizados", "DE.Views.ProtectDialog.textView": "Sin cambios (Sólo lectura)", "DE.Views.ProtectDialog.txtAllow": "Permit<PERSON> s<PERSON>lo este tipo de edición en el documento", "DE.Views.ProtectDialog.txtIncorrectPwd": "La contraseña de confirmación no es idéntica", "DE.Views.ProtectDialog.txtLimit": "La contraseña está limitada a 15 caracteres", "DE.Views.ProtectDialog.txtOptional": "opcional", "DE.Views.ProtectDialog.txtPassword": "Contraseña", "DE.Views.ProtectDialog.txtProtect": "Proteger", "DE.Views.ProtectDialog.txtRepeat": "Repita la contraseña", "DE.Views.ProtectDialog.txtTitle": "Proteger", "DE.Views.ProtectDialog.txtWarning": "Precaución: Si pierde u olvida su contraseña, no podrá recuperarla. Guárdalo en un lugar seguro.", "DE.Views.RightMenu.txtChartSettings": "Ajustes de gráfico", "DE.Views.RightMenu.txtFormSettings": "Ajustes de formulario", "DE.Views.RightMenu.txtHeaderFooterSettings": "Ajustes de encabezado y pie de página", "DE.Views.RightMenu.txtImageSettings": "<PERSON><PERSON><PERSON><PERSON> imagen", "DE.Views.RightMenu.txtMailMergeSettings": "Ajustes de fusión", "DE.Views.RightMenu.txtParagraphSettings": "<PERSON><PERSON><PERSON><PERSON> de p<PERSON>fo", "DE.Views.RightMenu.txtShapeSettings": "Ajustes de forma", "DE.Views.RightMenu.txtSignatureSettings": "Configuración de firma", "DE.Views.RightMenu.txtTableSettings": "Ajustes de tabla", "DE.Views.RightMenu.txtTextArtSettings": "Ajustes de galería de texto", "DE.Views.RoleDeleteDlg.textLabel": "Para eliminar este rol, es necesario mover los campos asociados a él a otro rol.", "DE.Views.RoleDeleteDlg.textSelect": "Seleccione para el rol de fusión de campo", "DE.Views.RoleDeleteDlg.textTitle": "Eliminar rol", "DE.Views.RoleEditDlg.errNameExists": "Ya existe un rol con ese nombre.", "DE.Views.RoleEditDlg.textEmptyError": "El nombre del rol no debe estar vacío.", "DE.Views.RoleEditDlg.textName": "Nombre del rol", "DE.Views.RoleEditDlg.textNoHighlight": "No resaltar", "DE.Views.RoleEditDlg.txtTitleEdit": "Editar rol", "DE.Views.RoleEditDlg.txtTitleNew": "Crear nuevo rol", "DE.Views.RolesManagerDlg.closeButtonText": "<PERSON><PERSON><PERSON>", "DE.Views.RolesManagerDlg.textAnyone": "Cualquiera", "DE.Views.RolesManagerDlg.textDelete": "Eliminar", "DE.Views.RolesManagerDlg.textDeleteLast": "¿Está seguro de que desea eliminar el rol {0}?<br>Una vez eliminado, se creará el rol predeterminado.", "DE.Views.RolesManagerDlg.textDescription": "Añada roles y establezca el orden en que los rellenadores reciben y firman el documento", "DE.Views.RolesManagerDlg.textDown": "Mover el rol hacia abajo", "DE.Views.RolesManagerDlg.textEdit": "<PERSON><PERSON>", "DE.Views.RolesManagerDlg.textEmpty": "Todavía no se ha creado ningún rol.<br><PERSON><PERSON> al menos un rol y aparecerá en este campo.", "DE.Views.RolesManagerDlg.textNew": "Nuevo", "DE.Views.RolesManagerDlg.textUp": "Mover el rol hacia arriba", "DE.Views.RolesManagerDlg.txtTitle": "Gestionar roles", "DE.Views.RolesManagerDlg.warnCantDelete": "No puede eliminar este rol porque tiene campos asociados.", "DE.Views.RolesManagerDlg.warnDelete": "¿Está seguro de que desea eliminar el rol {0}?", "DE.Views.SaveFormDlg.saveButtonText": "Guardar", "DE.Views.SaveFormDlg.textAnyone": "Cualquiera", "DE.Views.SaveFormDlg.textDescription": "Al guardar en oform, solo los roles con campos se añaden a la lista de relleno", "DE.Views.SaveFormDlg.textEmpty": "No hay roles asociados a este campo.", "DE.Views.SaveFormDlg.textFill": "Lista de relleno", "DE.Views.SaveFormDlg.txtTitle": "Guardar como formulario", "DE.Views.ShapeSettings.strBackground": "Color de fondo", "DE.Views.ShapeSettings.strChange": "Cambiar autoforma", "DE.Views.ShapeSettings.strColor": "Color", "DE.Views.ShapeSettings.strFill": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.strForeground": "Color de primer plano", "DE.Views.ShapeSettings.strPattern": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.strShadow": "Mostrar sombra", "DE.Views.ShapeSettings.strSize": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.strStroke": "Trazo", "DE.Views.ShapeSettings.strTransparency": "Opacidad ", "DE.Views.ShapeSettings.strType": "Tipo", "DE.Views.ShapeSettings.textAdvanced": "<PERSON><PERSON> a<PERSON>", "DE.Views.ShapeSettings.textAngle": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.textBorderSizeErr": "El valor numérico es incorrecto.<br><PERSON>r favor, introduzca un valor de 0 a 1584 puntos.", "DE.Views.ShapeSettings.textColor": "Color de relleno", "DE.Views.ShapeSettings.textDirection": "Dirección ", "DE.Views.ShapeSettings.textEmptyPattern": "<PERSON> patr<PERSON>", "DE.Views.ShapeSettings.textFlip": "Volteo", "DE.Views.ShapeSettings.textFromFile": "De archivo", "DE.Views.ShapeSettings.textFromStorage": "Desde almacenamiento", "DE.Views.ShapeSettings.textFromUrl": "De URL", "DE.Views.ShapeSettings.textGradient": "Puntos de gradiente", "DE.Views.ShapeSettings.textGradientFill": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.textHint270": "Girar 90° a la izquierda", "DE.Views.ShapeSettings.textHint90": "Girar 90° a la derecha", "DE.Views.ShapeSettings.textHintFlipH": "Volteo Horizontal", "DE.Views.ShapeSettings.textHintFlipV": "Volteo Vertical", "DE.Views.ShapeSettings.textImageTexture": "Imagen o textura", "DE.Views.ShapeSettings.textLinear": "Lineal", "DE.Views.ShapeSettings.textNoFill": "<PERSON> relleno", "DE.Views.ShapeSettings.textPatternFill": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.textPosition": "Posición", "DE.Views.ShapeSettings.textRadial": "Radial", "DE.Views.ShapeSettings.textRecentlyUsed": "Usados recientemente", "DE.Views.ShapeSettings.textRotate90": "Girar 90°", "DE.Views.ShapeSettings.textRotation": "Rotación", "DE.Views.ShapeSettings.textSelectImage": "Seleccionar imagen", "DE.Views.ShapeSettings.textSelectTexture": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.textStretch": "<PERSON>st<PERSON><PERSON>", "DE.Views.ShapeSettings.textStyle": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.textTexture": "De textura", "DE.Views.ShapeSettings.textTile": "Mosaico", "DE.Views.ShapeSettings.textWrap": "Ajuste de texto", "DE.Views.ShapeSettings.tipAddGradientPoint": "Agregar punto de degradado", "DE.Views.ShapeSettings.tipRemoveGradientPoint": "Eliminar punto de degradado", "DE.Views.ShapeSettings.txtBehind": "Detrás del texto", "DE.Views.ShapeSettings.txtBrownPaper": "<PERSON><PERSON>", "DE.Views.ShapeSettings.txtCanvas": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.txtCarton": "Cartón", "DE.Views.ShapeSettings.txtDarkFabric": "Tela oscura", "DE.Views.ShapeSettings.txtGrain": "Grano", "DE.Views.ShapeSettings.txtGranite": "<PERSON><PERSON>", "DE.Views.ShapeSettings.txtGreyPaper": "Papel gris", "DE.Views.ShapeSettings.txtInFront": "Delante del texto", "DE.Views.ShapeSettings.txtInline": "En línea con el texto", "DE.Views.ShapeSettings.txtKnit": "Tejid<PERSON>", "DE.Views.ShapeSettings.txtLeather": "<PERSON><PERSON>", "DE.Views.ShapeSettings.txtNoBorders": "Sin línea", "DE.Views.ShapeSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.txtSquare": "Cuadrado", "DE.Views.ShapeSettings.txtThrough": "A través", "DE.Views.ShapeSettings.txtTight": "Estrecho", "DE.Views.ShapeSettings.txtTopAndBottom": "Superior e inferior", "DE.Views.ShapeSettings.txtWood": "<PERSON><PERSON>", "DE.Views.SignatureSettings.notcriticalErrorTitle": "Aviso", "DE.Views.SignatureSettings.strDelete": "Elimine la firma", "DE.Views.SignatureSettings.strDetails": "Detalles de la firma", "DE.Views.SignatureSettings.strInvalid": "<PERSON><PERSON><PERSON> invalidas", "DE.Views.SignatureSettings.strRequested": "<PERSON><PERSON><PERSON>", "DE.Views.SignatureSettings.strSetup": "Preparación de la firma", "DE.Views.SignatureSettings.strSign": "<PERSON><PERSON><PERSON>", "DE.Views.SignatureSettings.strSignature": "Firma", "DE.Views.SignatureSettings.strSigner": "Firmante", "DE.Views.SignatureSettings.strValid": "<PERSON><PERSON><PERSON> valida", "DE.Views.SignatureSettings.txtContinueEditing": "<PERSON><PERSON> de todas maneras", "DE.Views.SignatureSettings.txtEditWarning": "La edición eliminará las firmas del documento.<br>¿Continuar?", "DE.Views.SignatureSettings.txtRemoveWarning": "¿Desea eliminar esta firma?<br> No se puede deshacer.", "DE.Views.SignatureSettings.txtRequestedSignatures": "Este documento necesita", "DE.Views.SignatureSettings.txtSigned": "Se han agregado firmas válidas al documento. El documento está protegido contra a la edición.", "DE.Views.SignatureSettings.txtSignedInvalid": "Algunas de las firmas digitales del documento no son válidas o no han podido ser verificadas. El documento está protegido frente a la edición.", "DE.Views.Statusbar.goToPageText": "<PERSON>r a p<PERSON><PERSON>a", "DE.Views.Statusbar.pageIndexText": "Página {0} de {1}", "DE.Views.Statusbar.tipFitPage": "Ajustar a la página", "DE.Views.Statusbar.tipFitWidth": "Ajustar a ancho", "DE.Views.Statusbar.tipHandTool": "Herramienta manual", "DE.Views.Statusbar.tipSelectTool": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.Statusbar.tipSetLang": "Establecer idioma de texto", "DE.Views.Statusbar.tipZoomFactor": "Ampliación", "DE.Views.Statusbar.tipZoomIn": "Acercar", "DE.Views.Statusbar.tipZoomOut": "<PERSON><PERSON><PERSON>", "DE.Views.Statusbar.txtPageNumInvalid": "Número de página inválido", "DE.Views.Statusbar.txtPages": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Statusbar.txtParagraphs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.Statusbar.txtSpaces": "Símbolos con espacios", "DE.Views.Statusbar.txtSymbols": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Statusbar.txtWordCount": "Recuento de palabras", "DE.Views.Statusbar.txtWords": "Palabras", "DE.Views.StyleTitleDialog.textHeader": "<PERSON><PERSON><PERSON> estilo nuevo", "DE.Views.StyleTitleDialog.textNextStyle": "Est<PERSON> de <PERSON> si<PERSON>", "DE.Views.StyleTitleDialog.textTitle": "Title", "DE.Views.StyleTitleDialog.txtEmpty": "Este campo es obligatorio", "DE.Views.StyleTitleDialog.txtNotEmpty": "El campo no puede estar vacío", "DE.Views.StyleTitleDialog.txtSameAs": "Igual que el nuevo estilo creado", "DE.Views.TableFormulaDialog.textBookmark": "<PERSON><PERSON><PERSON> marcador", "DE.Views.TableFormulaDialog.textFormat": "Formato de número", "DE.Views.TableFormulaDialog.textFormula": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.TableFormulaDialog.textInsertFunction": "<PERSON><PERSON><PERSON>", "DE.Views.TableFormulaDialog.textTitle": "Ajustes de fórmula", "DE.Views.TableOfContentsSettings.strAlign": "<PERSON><PERSON> los números de página a la derecha", "DE.Views.TableOfContentsSettings.strFullCaption": "Incluir etiqueta y número", "DE.Views.TableOfContentsSettings.strLinks": "Formatear tabla de contenido como enlaces", "DE.Views.TableOfContentsSettings.strLinksOF": "Formatear tabla de ilustraciones como enlaces", "DE.Views.TableOfContentsSettings.strShowPages": "Mostrar números de página", "DE.Views.TableOfContentsSettings.textBuildTable": "Crear tabla de contenidos desde", "DE.Views.TableOfContentsSettings.textBuildTableOF": "Generar tabla de ilustraciones a partir de:", "DE.Views.TableOfContentsSettings.textEquation": "Ecuación", "DE.Views.TableOfContentsSettings.textFigure": "<PERSON><PERSON><PERSON>", "DE.Views.TableOfContentsSettings.textLeader": "Director", "DE.Views.TableOfContentsSettings.textLevel": "<PERSON><PERSON>", "DE.Views.TableOfContentsSettings.textLevels": "<PERSON><PERSON><PERSON>", "DE.Views.TableOfContentsSettings.textNone": "Ninguna", "DE.Views.TableOfContentsSettings.textRadioCaption": "Leyenda", "DE.Views.TableOfContentsSettings.textRadioLevels": "<PERSON><PERSON><PERSON> de perfil", "DE.Views.TableOfContentsSettings.textRadioStyle": "<PERSON><PERSON><PERSON>", "DE.Views.TableOfContentsSettings.textRadioStyles": "Seleccione estilos", "DE.Views.TableOfContentsSettings.textStyle": "<PERSON><PERSON><PERSON>", "DE.Views.TableOfContentsSettings.textStyles": "Estilos", "DE.Views.TableOfContentsSettings.textTable": "Tabla", "DE.Views.TableOfContentsSettings.textTitle": "Tabla de contenidos", "DE.Views.TableOfContentsSettings.textTitleTOF": "Tabla de ilustraciones", "DE.Views.TableOfContentsSettings.txtCentered": "Centrado", "DE.Views.TableOfContentsSettings.txtClassic": "Clásico", "DE.Views.TableOfContentsSettings.txtCurrent": "Actual", "DE.Views.TableOfContentsSettings.txtDistinctive": "Distintivo", "DE.Views.TableOfContentsSettings.txtFormal": "Formal", "DE.Views.TableOfContentsSettings.txtModern": "Moderna", "DE.Views.TableOfContentsSettings.txtOnline": "En línea", "DE.Views.TableOfContentsSettings.txtSimple": "Simple", "DE.Views.TableOfContentsSettings.txtStandard": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.TableSettings.deleteColumnText": "Bo<PERSON>r columna", "DE.Views.TableSettings.deleteRowText": "<PERSON><PERSON><PERSON> fila", "DE.Views.TableSettings.deleteTableText": "Borrar tabla", "DE.Views.TableSettings.insertColumnLeftText": "Insertar columna a la izquierda", "DE.Views.TableSettings.insertColumnRightText": "Insertar columna a la derecha", "DE.Views.TableSettings.insertRowAboveText": "Insertar fila arriba", "DE.Views.TableSettings.insertRowBelowText": "Insertar fila abajo", "DE.Views.TableSettings.mergeCellsText": "Unir celdas", "DE.Views.TableSettings.selectCellText": "<PERSON><PERSON><PERSON><PERSON><PERSON> celda", "DE.Views.TableSettings.selectColumnText": "Seleccionar columna", "DE.Views.TableSettings.selectRowText": "Seleccionar fila", "DE.Views.TableSettings.selectTableText": "Seleccionar tabla", "DE.Views.TableSettings.splitCellsText": "Di<PERSON><PERSON> celda...", "DE.Views.TableSettings.splitCellTitleText": "<PERSON><PERSON><PERSON> celda", "DE.Views.TableSettings.strRepeatRow": "Repetir como una fila de encabezado en la parte superior de cada página", "DE.Views.TableSettings.textAddFormula": "Agregar fó<PERSON>ula", "DE.Views.TableSettings.textAdvanced": "<PERSON><PERSON> a<PERSON>", "DE.Views.TableSettings.textBackColor": "Color de fondo", "DE.Views.TableSettings.textBanded": "Con bandas", "DE.Views.TableSettings.textBorderColor": "Color", "DE.Views.TableSettings.textBorders": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.textCellSize": "Tamaño de filas y columnas", "DE.Views.TableSettings.textColumns": "Columnas", "DE.Views.TableSettings.textConvert": "Convertir tabla en texto", "DE.Views.TableSettings.textDistributeCols": "Distribuir columnas", "DE.Views.TableSettings.textDistributeRows": "Distribuir filas", "DE.Views.TableSettings.textEdit": "Filas y columnas", "DE.Views.TableSettings.textEmptyTemplate": "Sin plantillas", "DE.Views.TableSettings.textFirst": "primero", "DE.Views.TableSettings.textHeader": "Encabezado", "DE.Views.TableSettings.textHeight": "Altura", "DE.Views.TableSettings.textLast": "Última", "DE.Views.TableSettings.textRows": "<PERSON><PERSON>", "DE.Views.TableSettings.textSelectBorders": "Seleccione bordes que usted desea cambiar aplicando estilo seleccionado", "DE.Views.TableSettings.textTemplate": "Seleccionar de plantilla", "DE.Views.TableSettings.textTotal": "Total", "DE.Views.TableSettings.textWidth": "<PERSON><PERSON>", "DE.Views.TableSettings.tipAll": "<PERSON>jar borde exterior y todas líneas interiores ", "DE.Views.TableSettings.tipBottom": "<PERSON>jar s<PERSON>lo borde exterior inferior", "DE.Views.TableSettings.tipInner": "Fijar sólo líneas interiores", "DE.Views.TableSettings.tipInnerHor": "Fijar sólo líneas horizontales interiores ", "DE.Views.TableSettings.tipInnerVert": "Fijar sólo líneas verticales interiores", "DE.Views.TableSettings.tipLeft": "<PERSON><PERSON> s<PERSON>lo borde exterior izquierdo", "DE.Views.TableSettings.tipNone": "No fijar bordes", "DE.Views.TableSettings.tipOuter": "<PERSON><PERSON> s<PERSON>lo borde <PERSON>", "DE.Views.TableSettings.tipRight": "<PERSON>jar s<PERSON>lo borde exterior derecho", "DE.Views.TableSettings.tipTop": "<PERSON>jar s<PERSON>lo borde exterior superior", "DE.Views.TableSettings.txtGroupTable_BorderedAndLined": "Tablas con bordes y líneas", "DE.Views.TableSettings.txtGroupTable_Custom": "Personalizado", "DE.Views.TableSettings.txtGroupTable_Grid": "Tablas de cuadrícula", "DE.Views.TableSettings.txtGroupTable_List": "Tablas de lista", "DE.Views.TableSettings.txtGroupTable_Plain": "Tablas sin formato", "DE.Views.TableSettings.txtNoBorders": "<PERSON> bordes", "DE.Views.TableSettings.txtTable_Accent": "Ace<PERSON>", "DE.Views.TableSettings.txtTable_Bordered": "<PERSON> bordes", "DE.Views.TableSettings.txtTable_BorderedAndLined": "Con bordes y líneas", "DE.Views.TableSettings.txtTable_Colorful": "Colorido", "DE.Views.TableSettings.txtTable_Dark": "Oscuro", "DE.Views.TableSettings.txtTable_GridTable": "Tabla de rejilla", "DE.Views.TableSettings.txtTable_Light": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.txtTable_Lined": "Con líneas", "DE.Views.TableSettings.txtTable_ListTable": "Tabla de lista", "DE.Views.TableSettings.txtTable_PlainTable": "Tabla normal", "DE.Views.TableSettings.txtTable_TableGrid": "Cuadrícula de tabla", "DE.Views.TableSettingsAdvanced.textAlign": "Alineación", "DE.Views.TableSettingsAdvanced.textAlignment": "Alineación", "DE.Views.TableSettingsAdvanced.textAllowSpacing": "Espac<PERSON> entre celdas", "DE.Views.TableSettingsAdvanced.textAlt": "Texto alternativo", "DE.Views.TableSettingsAdvanced.textAltDescription": "Descripción", "DE.Views.TableSettingsAdvanced.textAltTip": "Representación de texto alternativa de la información sobre el objeto visual, que se leerá para las personas con deficiencia visual o deterioro cognitivo para ayudarles a entender mejor, que información contiene la imagen, autoforma, gráfica o tabla.", "DE.Views.TableSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textAnchorText": "Texto", "DE.Views.TableSettingsAdvanced.textAutofit": "Cambiar tamaño automáticamente para ajustar a contenido", "DE.Views.TableSettingsAdvanced.textBackColor": "Fondo de celda", "DE.Views.TableSettingsAdvanced.textBelow": "abajo", "DE.Views.TableSettingsAdvanced.textBorderColor": "Color de borde", "DE.Views.TableSettingsAdvanced.textBorderDesc": "Haga clic en diagrama o use botones para seleccionar bordes y aplicar el estilo seleccionado", "DE.Views.TableSettingsAdvanced.textBordersBackgroung": "Bordes y fondo", "DE.Views.TableSettingsAdvanced.textBorderWidth": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textBottom": "Inferior", "DE.Views.TableSettingsAdvanced.textCellOptions": "Opciones de celda", "DE.Views.TableSettingsAdvanced.textCellProps": "<PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textCellSize": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textCenter": "Al centro", "DE.Views.TableSettingsAdvanced.textCenterTooltip": "Al centro", "DE.Views.TableSettingsAdvanced.textCheckMargins": "<PERSON><PERSON> márgenes predeterminados", "DE.Views.TableSettingsAdvanced.textDefaultMargins": "<PERSON><PERSON><PERSON><PERSON> de celda predeterminados", "DE.Views.TableSettingsAdvanced.textDistance": "Distancia desde el texto", "DE.Views.TableSettingsAdvanced.textHorizontal": "Horizontal ", "DE.Views.TableSettingsAdvanced.textIndLeft": "Sangría a la izquierda", "DE.Views.TableSettingsAdvanced.textLeft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textLeftTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textMargin": "Margen", "DE.Views.TableSettingsAdvanced.textMargins": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textMeasure": "medir en", "DE.Views.TableSettingsAdvanced.textMove": "<PERSON><PERSON><PERSON>zar objeto con texto", "DE.Views.TableSettingsAdvanced.textOnlyCells": "Sólo para celdas seleccionadas", "DE.Views.TableSettingsAdvanced.textOptions": "Opciones", "DE.Views.TableSettingsAdvanced.textOverlap": "Superposición", "DE.Views.TableSettingsAdvanced.textPage": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textPosition": "Posición", "DE.Views.TableSettingsAdvanced.textPrefWidth": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textPreview": "Vista previa", "DE.Views.TableSettingsAdvanced.textRelative": "en relación a", "DE.Views.TableSettingsAdvanced.textRight": "Derecho", "DE.Views.TableSettingsAdvanced.textRightOf": "a la derecha de", "DE.Views.TableSettingsAdvanced.textRightTooltip": "Derecho", "DE.Views.TableSettingsAdvanced.textTable": "Tabla", "DE.Views.TableSettingsAdvanced.textTableBackColor": "Fondo de tabla", "DE.Views.TableSettingsAdvanced.textTablePosition": "Posición de tabla", "DE.Views.TableSettingsAdvanced.textTableSize": "Tamaño de tabla", "DE.Views.TableSettingsAdvanced.textTitle": "Tabla - <PERSON><PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textTop": "Superior", "DE.Views.TableSettingsAdvanced.textVertical": "Vertical", "DE.Views.TableSettingsAdvanced.textWidth": "<PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textWidthSpaces": "Ancho y espacios", "DE.Views.TableSettingsAdvanced.textWrap": "Ajuste de texto", "DE.Views.TableSettingsAdvanced.textWrapNoneTooltip": "Tabla en línea", "DE.Views.TableSettingsAdvanced.textWrapParallelTooltip": "Tabla flujo", "DE.Views.TableSettingsAdvanced.textWrappingStyle": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textWrapText": "Ajustar texto", "DE.Views.TableSettingsAdvanced.tipAll": "<PERSON>jar borde exterior y todas líneas interiores ", "DE.Views.TableSettingsAdvanced.tipCellAll": "Fijar bordes sólo para celdas interiores", "DE.Views.TableSettingsAdvanced.tipCellInner": "Fijar líneas verticales y horizontales sólo para celdas interiores", "DE.Views.TableSettingsAdvanced.tipCellOuter": "Fijar bordes exteriores sólo para celdas iferiores", "DE.Views.TableSettingsAdvanced.tipInner": "Fijar sólo líneas interiores", "DE.Views.TableSettingsAdvanced.tipNone": "No fijar bordes", "DE.Views.TableSettingsAdvanced.tipOuter": "<PERSON><PERSON> s<PERSON>lo borde <PERSON>", "DE.Views.TableSettingsAdvanced.tipTableOuterCellAll": "Fijar borde exterior y bordes para todas celdas inferiores", "DE.Views.TableSettingsAdvanced.tipTableOuterCellInner": "Fijar borde exterior y líneas verticales y horizontales para celdas inferiores", "DE.Views.TableSettingsAdvanced.tipTableOuterCellOuter": "Fijar borde de tabla exterior y bordes exteriores para celdas interiores", "DE.Views.TableSettingsAdvanced.txtCm": "Centímetro", "DE.Views.TableSettingsAdvanced.txtInch": "Pulgada", "DE.Views.TableSettingsAdvanced.txtNoBorders": "<PERSON> bordes", "DE.Views.TableSettingsAdvanced.txtPercent": "<PERSON>r ciento", "DE.Views.TableSettingsAdvanced.txtPt": "Punt<PERSON>", "DE.Views.TableToTextDialog.textEmpty": "Debe escribir un carácter para el separador personalizado.", "DE.Views.TableToTextDialog.textNested": "Convertir tablas anidadas", "DE.Views.TableToTextDialog.textOther": "<PERSON><PERSON>", "DE.Views.TableToTextDialog.textPara": "Marcas de pá<PERSON>fo", "DE.Views.TableToTextDialog.textSemicolon": "Signos de punto y coma", "DE.Views.TableToTextDialog.textSeparator": "Separadores", "DE.Views.TableToTextDialog.textTab": "Pestañas", "DE.Views.TableToTextDialog.textTitle": "Convertir tabla en texto", "DE.Views.TextArtSettings.strColor": "Color", "DE.Views.TextArtSettings.strFill": "<PERSON><PERSON><PERSON>", "DE.Views.TextArtSettings.strSize": "<PERSON><PERSON><PERSON>", "DE.Views.TextArtSettings.strStroke": "Gráfico de líneas", "DE.Views.TextArtSettings.strTransparency": "Opacidad ", "DE.Views.TextArtSettings.strType": "Tipo", "DE.Views.TextArtSettings.textAngle": "<PERSON><PERSON><PERSON>", "DE.Views.TextArtSettings.textBorderSizeErr": "El valor numérico es incorrecto.<br><PERSON>r favor, introduzca un valor de 0 a 1584 puntos.", "DE.Views.TextArtSettings.textColor": "Color Fill", "DE.Views.TextArtSettings.textDirection": "Dirección ", "DE.Views.TextArtSettings.textGradient": "Puntos de gradiente", "DE.Views.TextArtSettings.textGradientFill": "<PERSON><PERSON><PERSON>", "DE.Views.TextArtSettings.textLinear": "Lineal", "DE.Views.TextArtSettings.textNoFill": "<PERSON> relleno", "DE.Views.TextArtSettings.textPosition": "Posición", "DE.Views.TextArtSettings.textRadial": "Radial", "DE.Views.TextArtSettings.textSelectTexture": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.TextArtSettings.textStyle": "<PERSON><PERSON><PERSON>", "DE.Views.TextArtSettings.textTemplate": "Plantilla", "DE.Views.TextArtSettings.textTransform": "Transformar", "DE.Views.TextArtSettings.tipAddGradientPoint": "Agregar punto de degradado", "DE.Views.TextArtSettings.tipRemoveGradientPoint": "Eliminar punto de degradado", "DE.Views.TextArtSettings.txtNoBorders": "Sin línea", "DE.Views.TextToTableDialog.textAutofit": "Autoajuste", "DE.Views.TextToTableDialog.textColumns": "Columnas", "DE.Views.TextToTableDialog.textContents": "Autoajustar al contenido", "DE.Views.TextToTableDialog.textEmpty": "Debe escribir un carácter para el separador personalizado.", "DE.Views.TextToTableDialog.textFixed": "<PERSON><PERSON> de columna fijo", "DE.Views.TextToTableDialog.textOther": "<PERSON><PERSON>", "DE.Views.TextToTableDialog.textPara": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.TextToTableDialog.textRows": "<PERSON><PERSON>", "DE.Views.TextToTableDialog.textSemicolon": "Signos de punto y coma", "DE.Views.TextToTableDialog.textSeparator": "Separar texto en", "DE.Views.TextToTableDialog.textTab": "Pestañas", "DE.Views.TextToTableDialog.textTableSize": "Tamaño de tabla", "DE.Views.TextToTableDialog.textTitle": "Convertir texto en tabla", "DE.Views.TextToTableDialog.textWindow": "Autoajustar a la ventana", "DE.Views.TextToTableDialog.txtAutoText": "Auto", "DE.Views.Toolbar.capBtnAddComment": "Agregar comentario", "DE.Views.Toolbar.capBtnBlankPage": "Página en blanco", "DE.Views.Toolbar.capBtnColumns": "Columnas", "DE.Views.Toolbar.capBtnComment": "Comentario", "DE.Views.Toolbar.capBtnDateTime": "<PERSON><PERSON> y hora", "DE.Views.Toolbar.capBtnInsChart": "Diagram", "DE.Views.Toolbar.capBtnInsControls": "Controles de contenido", "DE.Views.Toolbar.capBtnInsDropcap": "Quitar capitlización", "DE.Views.Toolbar.capBtnInsEquation": "Ecuación", "DE.Views.Toolbar.capBtnInsHeader": "Encabezado/Pie de página", "DE.Views.Toolbar.capBtnInsImage": "Imagen", "DE.Views.Toolbar.capBtnInsPagebreak": "Cambios de línea", "DE.Views.Toolbar.capBtnInsShape": "Forma", "DE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "DE.Views.Toolbar.capBtnInsSymbol": "Símbolo", "DE.Views.Toolbar.capBtnInsTable": "Tabla", "DE.Views.Toolbar.capBtnInsTextart": "Galería de texto", "DE.Views.Toolbar.capBtnInsTextbox": "Cuadro de Texto", "DE.Views.Toolbar.capBtnLineNumbers": "Numeración de Líneas", "DE.Views.Toolbar.capBtnMargins": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.capBtnPageOrient": "Orientación", "DE.Views.Toolbar.capBtnPageSize": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.capBtnWatermark": "Marca de agua", "DE.Views.Toolbar.capImgAlign": "Alinear", "DE.Views.Toolbar.capImgBackward": "Enviar atrás", "DE.Views.Toolbar.capImgForward": "Una capa adelante", "DE.Views.Toolbar.capImgGroup": "Agrupar", "DE.Views.Toolbar.capImgWrapping": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.mniCapitalizeWords": "Poner En Mayúsculas Cada Palabra", "DE.Views.Toolbar.mniCustomTable": "Insertar tabla personalizada", "DE.Views.Toolbar.mniDrawTable": "Dibujar tabla", "DE.Views.Toolbar.mniEditControls": "Ajustes de control", "DE.Views.Toolbar.mniEditDropCap": "Ajustes de letra capital", "DE.Views.Toolbar.mniEditFooter": "Editar pie de página", "DE.Views.Toolbar.mniEditHeader": "<PERSON><PERSON>", "DE.Views.Toolbar.mniEraseTable": "Borrar tabla", "DE.Views.Toolbar.mniFromFile": "Desde archivo", "DE.Views.Toolbar.mniFromStorage": "Desde almacenamiento", "DE.Views.Toolbar.mniFromUrl": "Desde URL", "DE.Views.Toolbar.mniHiddenBorders": "Bordes de tabla ocultos", "DE.Views.Toolbar.mniHiddenChars": "Caracteres no imprimibles", "DE.Views.Toolbar.mniHighlightControls": "Ajustes de resaltado", "DE.Views.Toolbar.mniImageFromFile": "Imagen de archivo", "DE.Views.Toolbar.mniImageFromStorage": "Imagen de Almacenamiento", "DE.Views.Toolbar.mniImageFromUrl": "Imagen de URL", "DE.Views.Toolbar.mniInsertSSE": "Insertar hoja de cálculo", "DE.Views.Toolbar.mniLowerCase": "minúsculas", "DE.Views.Toolbar.mniRemoveFooter": "Quitar pie de página", "DE.Views.Toolbar.mniRemoveHeader": "<PERSON><PERSON>ar <PERSON>", "DE.Views.Toolbar.mniSentenceCase": "Tipo oración.", "DE.Views.Toolbar.mniTextToTable": "Convertir texto en tabla", "DE.Views.Toolbar.mniToggleCase": "tIPO iNVERSO", "DE.Views.Toolbar.mniUpperCase": "MAYÚSCULAS", "DE.Views.Toolbar.strMenuNoFill": "<PERSON> relleno", "DE.Views.Toolbar.textAutoColor": "Automático", "DE.Views.Toolbar.textBold": "Negrita", "DE.Views.Toolbar.textBottom": "Inferior: ", "DE.Views.Toolbar.textChangeLevel": "Cambiar nivel de lista", "DE.Views.Toolbar.textCheckboxControl": "<PERSON><PERSON><PERSON> a marquar", "DE.Views.Toolbar.textColumnsCustom": "Columnas personalizadas", "DE.Views.Toolbar.textColumnsLeft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textColumnsOne": "Uno", "DE.Views.Toolbar.textColumnsRight": "Derecho", "DE.Views.Toolbar.textColumnsThree": "Tres", "DE.Views.Toolbar.textColumnsTwo": "<PERSON><PERSON>", "DE.Views.Toolbar.textComboboxControl": "Cuadro de lista desplegable", "DE.Views.Toolbar.textContinuous": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textContPage": "Página continua", "DE.Views.Toolbar.textCustomLineNumbers": "Opciones de numeración de líneas", "DE.Views.Toolbar.textDateControl": "<PERSON><PERSON>", "DE.Views.Toolbar.textDropdownControl": "Lista desplegable", "DE.Views.Toolbar.textEditWatermark": "Marca de agua personalizada", "DE.Views.Toolbar.textEvenPage": "Página par", "DE.Views.Toolbar.textInMargin": "<PERSON> margen", "DE.Views.Toolbar.textInsColumnBreak": "Insertar salto de columna", "DE.Views.Toolbar.textInsertPageCount": "Insertar el número de páginas", "DE.Views.Toolbar.textInsertPageNumber": "Insertar número de página", "DE.Views.Toolbar.textInsPageBreak": "Insertar salto de página", "DE.Views.Toolbar.textInsSectionBreak": "Insertar salto de sección", "DE.Views.Toolbar.textInText": "En texto", "DE.Views.Toolbar.textItalic": "Cursiva", "DE.Views.Toolbar.textLandscape": "Horizontal", "DE.Views.Toolbar.textLeft": "Izquierdo: ", "DE.Views.Toolbar.textListSettings": "Opciones de lista", "DE.Views.Toolbar.textMarginsLast": "último personalizado", "DE.Views.Toolbar.textMarginsModerate": "Moderar", "DE.Views.Toolbar.textMarginsNarrow": "Estrecho", "DE.Views.Toolbar.textMarginsNormal": "Normal", "DE.Views.Toolbar.textMarginsUsNormal": "US Normal", "DE.Views.Toolbar.textMarginsWide": "Amplio", "DE.Views.Toolbar.textNewColor": "Color personalizado", "DE.Views.Toolbar.textNextPage": "<PERSON><PERSON><PERSON><PERSON> sigu<PERSON>e", "DE.Views.Toolbar.textNoHighlight": "No resaltar", "DE.Views.Toolbar.textNone": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textOddPage": "<PERSON><PERSON>gina impar", "DE.Views.Toolbar.textPageMarginsCustom": "<PERSON><PERSON><PERSON><PERSON> personaliza<PERSON>", "DE.Views.Toolbar.textPageSizeCustom": "Tamaño de página personalizado", "DE.Views.Toolbar.textPictureControl": "Imagen", "DE.Views.Toolbar.textPlainControl": "Texto sin formato", "DE.Views.Toolbar.textPortrait": "Vertical", "DE.Views.Toolbar.textRemoveControl": "Eliminar control de contenido", "DE.Views.Toolbar.textRemWatermark": "Quitar marca de agua", "DE.Views.Toolbar.textRestartEachPage": "Reiniciar en cada página", "DE.Views.Toolbar.textRestartEachSection": "Reiniciar en cada sección", "DE.Views.Toolbar.textRichControl": "Texto enriquecido", "DE.Views.Toolbar.textRight": "Derecho: ", "DE.Views.Toolbar.textStrikeout": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textStyleMenuDelete": "Eliminar estilo", "DE.Views.Toolbar.textStyleMenuDeleteAll": "Borrar todos los estilos personalizados", "DE.Views.Toolbar.textStyleMenuNew": "Nuevo estilo de la selección", "DE.Views.Toolbar.textStyleMenuRestore": "Restablecer a predeterminado", "DE.Views.Toolbar.textStyleMenuRestoreAll": "Restaurar todo a estilos predeterminados ", "DE.Views.Toolbar.textStyleMenuUpdate": "Actualizar de la selección", "DE.Views.Toolbar.textSubscript": "Subíndice", "DE.Views.Toolbar.textSuperscript": "Sobreíndice", "DE.Views.Toolbar.textSuppressForCurrentParagraph": "Suprimir del párrafo actual", "DE.Views.Toolbar.textTabCollaboration": "Colaboración", "DE.Views.Toolbar.textTabFile": "Archivo", "DE.Views.Toolbar.textTabHome": "<PERSON><PERSON>o", "DE.Views.Toolbar.textTabInsert": "Insertar", "DE.Views.Toolbar.textTabLayout": "Diseño", "DE.Views.Toolbar.textTabLinks": "Referencias", "DE.Views.Toolbar.textTabProtect": "Protección", "DE.Views.Toolbar.textTabReview": "Rev<PERSON><PERSON>", "DE.Views.Toolbar.textTabView": "Vista", "DE.Views.Toolbar.textTitleError": "Error", "DE.Views.Toolbar.textToCurrent": "A la posición actual", "DE.Views.Toolbar.textTop": "Top: ", "DE.Views.Toolbar.textUnderline": "Subrayado", "DE.Views.Toolbar.tipAlignCenter": "Alinear al centro", "DE.Views.Toolbar.tipAlignJust": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipAlignLeft": "Alinear a la izquierda", "DE.Views.Toolbar.tipAlignRight": "Alinear a la derecha", "DE.Views.Toolbar.tipBack": "Atrás", "DE.Views.Toolbar.tipBlankPage": "Insertar página en blanco", "DE.Views.Toolbar.tipChangeCase": "Cambiar mayús<PERSON>s y minúsculas", "DE.Views.Toolbar.tipChangeChart": "Cambiar tipo de gráfico", "DE.Views.Toolbar.tipClearStyle": "<PERSON><PERSON><PERSON> estilo", "DE.Views.Toolbar.tipColorSchemas": "Cambiar combinación de colores", "DE.Views.Toolbar.tipColumns": "Insertar columnas", "DE.Views.Toolbar.tipControls": "Insertar controles de contenido", "DE.Views.Toolbar.tipCopy": "Copiar", "DE.Views.Toolbar.tipCopyStyle": "<PERSON><PERSON><PERSON> est<PERSON>", "DE.Views.Toolbar.tipCut": "Cortar", "DE.Views.Toolbar.tipDateTime": "Insertar la fecha y hora actuales", "DE.Views.Toolbar.tipDecFont": "<PERSON><PERSON><PERSON> ta<PERSON> de <PERSON>ra", "DE.Views.Toolbar.tipDecPrLeft": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipDropCap": "Insertar letra capital", "DE.Views.Toolbar.tipEditHeader": "Editar encabezado y pie de página", "DE.Views.Toolbar.tipFontColor": "Color de letra", "DE.Views.Toolbar.tipFontName": "<PERSON><PERSON><PERSON> de letra", "DE.Views.Toolbar.tipFontSize": "Tam<PERSON>ño de <PERSON>", "DE.Views.Toolbar.tipHighlightColor": "Color de resaltado", "DE.Views.Toolbar.tipImgAlign": "Alinear objetos", "DE.Views.Toolbar.tipImgGroup": "Agrupar objetos", "DE.Views.Toolbar.tipImgWrapping": "Ajustar texto", "DE.Views.Toolbar.tipIncFont": "Aumentar tamaño de letra", "DE.Views.Toolbar.tipIncPrLeft": "Aumentar <PERSON>", "DE.Views.Toolbar.tipInsertChart": "Insertar gráfico", "DE.Views.Toolbar.tipInsertEquation": "Insertar ecuación", "DE.Views.Toolbar.tipInsertHorizontalText": "Insertar cuadro de texto horizontal", "DE.Views.Toolbar.tipInsertImage": "Insertar imagen", "DE.Views.Toolbar.tipInsertNum": "Insertar número de página", "DE.Views.Toolbar.tipInsertShape": "Insertar autoforma", "DE.Views.Toolbar.tipInsertSmartArt": "Insertar SmartArt", "DE.Views.Toolbar.tipInsertSymbol": "Insertar Symboló", "DE.Views.Toolbar.tipInsertTable": "Insertar tabla", "DE.Views.Toolbar.tipInsertText": "Insertar cuadro de texto", "DE.Views.Toolbar.tipInsertTextArt": "Insertar Galería de Texto", "DE.Views.Toolbar.tipInsertVerticalText": "Insertar cuadro de texto vertical", "DE.Views.Toolbar.tipLineNumbers": "Mostrar números de línea", "DE.Views.Toolbar.tipLineSpace": "Espaciado de línea de párrafo", "DE.Views.Toolbar.tipMailRecepients": "Combinación de Correspondencia", "DE.Views.Toolbar.tipMarkers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipMarkersArrow": "Viñetas de flecha", "DE.Views.Toolbar.tipMarkersCheckmark": "Viñetas de marca de verificación", "DE.Views.Toolbar.tipMarkersDash": "Viñetas g<PERSON>", "DE.Views.Toolbar.tipMarkersFRhombus": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipMarkersFRound": "Viñetas redondas rellenas", "DE.Views.Toolbar.tipMarkersFSquare": "Viñetas cuadradas rellenas", "DE.Views.Toolbar.tipMarkersHRound": "Viñetas redondas huecas", "DE.Views.Toolbar.tipMarkersStar": "Viñetas de estrella", "DE.Views.Toolbar.tipMultiLevelArticl": "Artículos enumerados en varios niveles", "DE.Views.Toolbar.tipMultiLevelChapter": "Capítulos enumerados en varios niveles", "DE.Views.Toolbar.tipMultiLevelHeadings": "Títulos enumerados en varios niveles", "DE.Views.Toolbar.tipMultiLevelHeadVarious": "Títulos enumerados en varios niveles", "DE.Views.Toolbar.tipMultiLevelNumbered": "Viñetas numeradas de varios niveles", "DE.Views.Toolbar.tipMultilevels": "Esquema", "DE.Views.Toolbar.tipMultiLevelSymbols": "Viñetas de símbolos de varios niveles", "DE.Views.Toolbar.tipMultiLevelVarious": "Viñetas numeradas de varios niveles", "DE.Views.Toolbar.tipNumbers": "Numeración", "DE.Views.Toolbar.tipPageBreak": "Insertar salto de página o de sección", "DE.Views.Toolbar.tipPageMargins": "<PERSON><PERSON><PERSON><PERSON>ágina", "DE.Views.Toolbar.tipPageOrient": "Orientación de página", "DE.Views.Toolbar.tipPageSize": "Tamaño de página", "DE.Views.Toolbar.tipParagraphStyle": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipPaste": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipPrColor": "Sombreado", "DE.Views.Toolbar.tipPrint": "Imprimir", "DE.Views.Toolbar.tipPrintQuick": "Impresión rápida", "DE.Views.Toolbar.tipRedo": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipSave": "Guardar", "DE.Views.Toolbar.tipSaveCoauth": "Guarde los cambios para que otros usuarios los puedan ver.", "DE.Views.Toolbar.tipSelectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> todo", "DE.Views.Toolbar.tipSendBackward": "Una capa hacia abajo", "DE.Views.Toolbar.tipSendForward": "Traer al frente", "DE.Views.Toolbar.tipShowHiddenChars": "Caracteres no imprimibles", "DE.Views.Toolbar.tipSynchronize": "El documento ha sido cambiado por otro usuario. Por favor haga clic para guardar sus cambios y recargue las actualizaciones.", "DE.Views.Toolbar.tipUndo": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.tipWatermark": "Editar marca de agua", "DE.Views.Toolbar.txtDistribHor": "Distribuir horizontalmente", "DE.Views.Toolbar.txtDistribVert": "Distribuir verticalmente", "DE.Views.Toolbar.txtMarginAlign": "Alinear al margen", "DE.Views.Toolbar.txtObjectsAlign": "Alinear objetos seleccionados", "DE.Views.Toolbar.txtPageAlign": "Alinear a la página", "DE.Views.Toolbar.txtScheme1": "Oficina", "DE.Views.Toolbar.txtScheme10": "Intermedio", "DE.Views.Toolbar.txtScheme11": "Metro", "DE.Views.Toolbar.txtScheme12": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.txtScheme13": "Opulento", "DE.Views.Toolbar.txtScheme14": "Mirador", "DE.Views.Toolbar.txtScheme15": "Origen", "DE.Views.Toolbar.txtScheme16": "Papel", "DE.Views.Toolbar.txtScheme17": "Solsticio", "DE.Views.Toolbar.txtScheme18": "Técnico", "DE.Views.Toolbar.txtScheme19": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.txtScheme2": "Escala de grises", "DE.Views.Toolbar.txtScheme20": "Urbano", "DE.Views.Toolbar.txtScheme21": "Brío", "DE.Views.Toolbar.txtScheme22": "Nueva oficina", "DE.Views.Toolbar.txtScheme3": "Vértice", "DE.Views.Toolbar.txtScheme4": "Aspect<PERSON>", "DE.Views.Toolbar.txtScheme5": "Civil", "DE.Views.Toolbar.txtScheme6": "Concurrencia", "DE.Views.Toolbar.txtScheme7": "Equidad ", "DE.Views.Toolbar.txtScheme8": "F<PERSON>jo", "DE.Views.Toolbar.txtScheme9": "Fundición", "DE.Views.ViewTab.textAlwaysShowToolbar": "Mostrar siempre la barra de herramientas", "DE.Views.ViewTab.textDarkDocument": "Documento oscuro", "DE.Views.ViewTab.textFitToPage": "Ajustar a la página", "DE.Views.ViewTab.textFitToWidth": "Ajustar al ancho", "DE.Views.ViewTab.textInterfaceTheme": "Tema de interfaz", "DE.Views.ViewTab.textLeftMenu": "Panel izquierdo", "DE.Views.ViewTab.textNavigation": "Navegación", "DE.Views.ViewTab.textOutline": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ViewTab.textRightMenu": "Panel derecho", "DE.Views.ViewTab.textRulers": "Reg<PERSON>", "DE.Views.ViewTab.textStatusBar": "Barra de estado", "DE.Views.ViewTab.textZoom": "Zoom", "DE.Views.ViewTab.tipDarkDocument": "Documento oscuro", "DE.Views.ViewTab.tipFitToPage": "Ajustar a la página", "DE.Views.ViewTab.tipFitToWidth": "Ajustar al ancho", "DE.Views.ViewTab.tipHeadings": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ViewTab.tipInterfaceTheme": "Tema de la interfaz", "DE.Views.WatermarkSettingsDialog.textAuto": "Auto", "DE.Views.WatermarkSettingsDialog.textBold": "Negrita", "DE.Views.WatermarkSettingsDialog.textColor": "Color de texto", "DE.Views.WatermarkSettingsDialog.textDiagonal": "Diagonal", "DE.Views.WatermarkSettingsDialog.textFont": "Fuente", "DE.Views.WatermarkSettingsDialog.textFromFile": "Desde archivo", "DE.Views.WatermarkSettingsDialog.textFromStorage": "Desde almacenamiento", "DE.Views.WatermarkSettingsDialog.textFromUrl": "De URL", "DE.Views.WatermarkSettingsDialog.textHor": "Horizontal ", "DE.Views.WatermarkSettingsDialog.textImageW": "Marca de agua de imagen", "DE.Views.WatermarkSettingsDialog.textItalic": "Cursiva", "DE.Views.WatermarkSettingsDialog.textLanguage": "Idioma", "DE.Views.WatermarkSettingsDialog.textLayout": "Disposición", "DE.Views.WatermarkSettingsDialog.textNone": "<PERSON><PERSON><PERSON>", "DE.Views.WatermarkSettingsDialog.textScale": "Escala", "DE.Views.WatermarkSettingsDialog.textSelect": "Seleccionar imagen", "DE.Views.WatermarkSettingsDialog.textStrikeout": "<PERSON><PERSON><PERSON>", "DE.Views.WatermarkSettingsDialog.textText": "Texto", "DE.Views.WatermarkSettingsDialog.textTextW": "Marca de agua de texto", "DE.Views.WatermarkSettingsDialog.textTitle": "Ajustes de marca de agua", "DE.Views.WatermarkSettingsDialog.textTransparency": "Semitransparente", "DE.Views.WatermarkSettingsDialog.textUnderline": "Subrayado", "DE.Views.WatermarkSettingsDialog.tipFontName": "Nombre del tipo de letra", "DE.Views.WatermarkSettingsDialog.tipFontSize": "Tamaño del tipo de letra"}