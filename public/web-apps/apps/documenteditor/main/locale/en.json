{"Common.Controllers.Chat.notcriticalErrorTitle": "Warning", "Common.Controllers.Chat.textEnterMessage": "Enter your message here", "Common.Controllers.ExternalDiagramEditor.textAnonymous": "Anonymous", "Common.Controllers.ExternalDiagramEditor.textClose": "Close", "Common.Controllers.ExternalDiagramEditor.warningText": "The object is disabled because it is being edited by another user.", "Common.Controllers.ExternalDiagramEditor.warningTitle": "Warning", "Common.Controllers.ExternalMergeEditor.textAnonymous": "Anonymous", "Common.Controllers.ExternalMergeEditor.textClose": "Close", "Common.Controllers.ExternalMergeEditor.warningText": "The object is disabled because it is being edited by another user.", "Common.Controllers.ExternalMergeEditor.warningTitle": "Warning", "Common.Controllers.ExternalOleEditor.textAnonymous": "Anonymous", "Common.Controllers.ExternalOleEditor.textClose": "Close", "Common.Controllers.ExternalOleEditor.warningText": "The object is disabled because it is being edited by another user.", "Common.Controllers.ExternalOleEditor.warningTitle": "Warning", "Common.Controllers.History.notcriticalErrorTitle": "Warning", "Common.Controllers.ReviewChanges.textAcceptBeforeCompare": "In order to compare documents all the tracked changes in them will be considered to have been accepted. Do you want to continue?", "Common.Controllers.ReviewChanges.textAtLeast": "at least", "Common.Controllers.ReviewChanges.textAuto": "auto", "Common.Controllers.ReviewChanges.textBaseline": "Baseline", "Common.Controllers.ReviewChanges.textBold": "Bold", "Common.Controllers.ReviewChanges.textBreakBefore": "Page break before", "Common.Controllers.ReviewChanges.textCaps": "All caps", "Common.Controllers.ReviewChanges.textCenter": "Align center", "Common.Controllers.ReviewChanges.textChar": "Character level", "Common.Controllers.ReviewChanges.textChart": "Chart", "Common.Controllers.ReviewChanges.textColor": "Font color", "Common.Controllers.ReviewChanges.textContextual": "Don't add interval between paragraphs of the same style", "Common.Controllers.ReviewChanges.textDeleted": "<b>Deleted:</b>", "Common.Controllers.ReviewChanges.textDStrikeout": "Double strikethrough", "Common.Controllers.ReviewChanges.textEquation": "Equation", "Common.Controllers.ReviewChanges.textExact": "exactly", "Common.Controllers.ReviewChanges.textFirstLine": "First line", "Common.Controllers.ReviewChanges.textFontSize": "Font size", "Common.Controllers.ReviewChanges.textFormatted": "Formatted", "Common.Controllers.ReviewChanges.textHighlight": "Highlight color", "Common.Controllers.ReviewChanges.textImage": "Image", "Common.Controllers.ReviewChanges.textIndentLeft": "Indent left", "Common.Controllers.ReviewChanges.textIndentRight": "Indent right", "Common.Controllers.ReviewChanges.textInserted": "<b>Inserted:</b>", "Common.Controllers.ReviewChanges.textItalic": "Italic", "Common.Controllers.ReviewChanges.textJustify": "<PERSON><PERSON> justified", "Common.Controllers.ReviewChanges.textKeepLines": "Keep lines together", "Common.Controllers.ReviewChanges.textKeepNext": "Keep with next", "Common.Controllers.ReviewChanges.textLeft": "<PERSON><PERSON> left", "Common.Controllers.ReviewChanges.textLineSpacing": "Line Spacing: ", "Common.Controllers.ReviewChanges.textMultiple": "multiple", "Common.Controllers.ReviewChanges.textNoBreakBefore": "No page break before", "Common.Controllers.ReviewChanges.textNoContextual": "Add interval between paragraphs of the same style", "Common.Controllers.ReviewChanges.textNoKeepLines": "Don't keep lines together", "Common.Controllers.ReviewChanges.textNoKeepNext": "Don't keep with next", "Common.Controllers.ReviewChanges.textNot": "Not ", "Common.Controllers.ReviewChanges.textNoWidow": "No widow control", "Common.Controllers.ReviewChanges.textNum": "Change numbering", "Common.Controllers.ReviewChanges.textOff": "{0} is no longer using Track Changes.", "Common.Controllers.ReviewChanges.textOffGlobal": "{0} disabled Track Changes for everyone.", "Common.Controllers.ReviewChanges.textOn": "{0} is now using Track Changes.", "Common.Controllers.ReviewChanges.textOnGlobal": "{0} enabled Track Changes for everyone.", "Common.Controllers.ReviewChanges.textParaDeleted": "<b>Paragraph Deleted</b>", "Common.Controllers.ReviewChanges.textParaFormatted": "Paragraph Formatted", "Common.Controllers.ReviewChanges.textParaInserted": "<b>Paragraph Inserted</b>", "Common.Controllers.ReviewChanges.textParaMoveFromDown": "<b>Moved Down:</b>", "Common.Controllers.ReviewChanges.textParaMoveFromUp": "<b>Moved Up:</b>", "Common.Controllers.ReviewChanges.textParaMoveTo": "<b>Moved:</b>", "Common.Controllers.ReviewChanges.textPosition": "Position", "Common.Controllers.ReviewChanges.textRight": "Align right", "Common.Controllers.ReviewChanges.textShape": "<PERSON><PERSON><PERSON>", "Common.Controllers.ReviewChanges.textShd": "Background color", "Common.Controllers.ReviewChanges.textShow": "Show changes at", "Common.Controllers.ReviewChanges.textSmallCaps": "Small caps", "Common.Controllers.ReviewChanges.textSpacing": "Spacing", "Common.Controllers.ReviewChanges.textSpacingAfter": "Spacing after", "Common.Controllers.ReviewChanges.textSpacingBefore": "Spacing before", "Common.Controllers.ReviewChanges.textStrikeout": "Strikethrough", "Common.Controllers.ReviewChanges.textSubScript": "Subscript", "Common.Controllers.ReviewChanges.textSuperScript": "Superscript", "Common.Controllers.ReviewChanges.textTableChanged": "<b>Table Settings Changed</b>", "Common.Controllers.ReviewChanges.textTableRowsAdd": "<b>Table Rows Added</b>", "Common.Controllers.ReviewChanges.textTableRowsDel": "<b>Table Rows Deleted</b>", "Common.Controllers.ReviewChanges.textTabs": "Change tabs", "Common.Controllers.ReviewChanges.textTitleComparison": "Comparison Settings", "Common.Controllers.ReviewChanges.textUnderline": "Underline", "Common.Controllers.ReviewChanges.textUrl": "Paste a document URL", "Common.Controllers.ReviewChanges.textWidow": "Widow control", "Common.Controllers.ReviewChanges.textWord": "Word level", "Common.define.chartData.textArea": "Area", "Common.define.chartData.textAreaStacked": "Stacked area", "Common.define.chartData.textAreaStackedPer": "100% Stacked area", "Common.define.chartData.textBar": "Bar", "Common.define.chartData.textBarNormal": "Clustered column", "Common.define.chartData.textBarNormal3d": "3-D Clustered column", "Common.define.chartData.textBarNormal3dPerspective": "3-D column", "Common.define.chartData.textBarStacked": "Stacked column", "Common.define.chartData.textBarStacked3d": "3-<PERSON> Stacked column", "Common.define.chartData.textBarStackedPer": "100% Stacked column", "Common.define.chartData.textBarStackedPer3d": "3-D 100% Stacked column", "Common.define.chartData.textCharts": "Charts", "Common.define.chartData.textColumn": "Column", "Common.define.chartData.textCombo": "Combo", "Common.define.chartData.textComboAreaBar": "Stacked area - clustered column", "Common.define.chartData.textComboBarLine": "Clustered column - line", "Common.define.chartData.textComboBarLineSecondary": "Clustered column - line on secondary axis", "Common.define.chartData.textComboCustom": "Custom combination", "Common.define.chartData.textDoughnut": "Doughnut", "Common.define.chartData.textHBarNormal": "Clustered bar", "Common.define.chartData.textHBarNormal3d": "3-D Clustered bar", "Common.define.chartData.textHBarStacked": "Stacked bar", "Common.define.chartData.textHBarStacked3d": "3-<PERSON> Stacked bar", "Common.define.chartData.textHBarStackedPer": "100% Stacked bar", "Common.define.chartData.textHBarStackedPer3d": "3-D 100% Stacked bar", "Common.define.chartData.textLine": "Line", "Common.define.chartData.textLine3d": "3-D line", "Common.define.chartData.textLineMarker": "Line with markers", "Common.define.chartData.textLineStacked": "Stacked line", "Common.define.chartData.textLineStackedMarker": "Stacked line with markers", "Common.define.chartData.textLineStackedPer": "100% Stacked line", "Common.define.chartData.textLineStackedPerMarker": "100% Stacked line with markers", "Common.define.chartData.textPie": "Pie", "Common.define.chartData.textPie3d": "3-D pie", "Common.define.chartData.textPoint": "XY (<PERSON><PERSON><PERSON>)", "Common.define.chartData.textScatter": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textScatterLine": "Scatter with straight lines", "Common.define.chartData.textScatterLineMarker": "Scatter with straight lines and markers", "Common.define.chartData.textScatterSmooth": "Scatter with smooth lines", "Common.define.chartData.textScatterSmoothMarker": "Scatter with smooth lines and markers", "Common.define.chartData.textStock": "Stock", "Common.define.chartData.textSurface": "Surface", "Common.define.smartArt.textAccentedPicture": "Accented Picture", "Common.define.smartArt.textAccentProcess": "Accent Process", "Common.define.smartArt.textAlternatingFlow": "Alternating Flow", "Common.define.smartArt.textAlternatingHexagons": "Alternating Hexagons", "Common.define.smartArt.textAlternatingPictureBlocks": "Alternating Picture Blocks", "Common.define.smartArt.textAlternatingPictureCircles": "Alternating Picture Circles", "Common.define.smartArt.textArchitectureLayout": "Architecture Layout", "Common.define.smartArt.textArrowRibbon": "Arrow Ribbon", "Common.define.smartArt.textAscendingPictureAccentProcess": "Ascending Picture Accent Process", "Common.define.smartArt.textBalance": "Balance", "Common.define.smartArt.textBasicBendingProcess": "Basic Bending Process", "Common.define.smartArt.textBasicBlockList": "Basic Block List", "Common.define.smartArt.textBasicChevronProcess": "Basic Chevron Process", "Common.define.smartArt.textBasicCycle": "Basic Cycle", "Common.define.smartArt.textBasicMatrix": "Basic Matrix", "Common.define.smartArt.textBasicPie": "Basic Pie", "Common.define.smartArt.textBasicProcess": "Basic Process", "Common.define.smartArt.textBasicPyramid": "Basic Pyramid", "Common.define.smartArt.textBasicRadial": "Basic Radial", "Common.define.smartArt.textBasicTarget": "Basic Target", "Common.define.smartArt.textBasicTimeline": "Basic Timeline", "Common.define.smartArt.textBasicVenn": "Basic Venn", "Common.define.smartArt.textBendingPictureAccentList": "Bending Picture Accent List", "Common.define.smartArt.textBendingPictureBlocks": "Bending Picture Blocks", "Common.define.smartArt.textBendingPictureCaption": "Bending Picture Caption", "Common.define.smartArt.textBendingPictureCaptionList": "Bending Picture Caption List", "Common.define.smartArt.textBendingPictureSemiTranparentText": "Bending Picture Semi-Transparent Text", "Common.define.smartArt.textBlockCycle": "Block Cycle", "Common.define.smartArt.textBubblePictureList": "Bubble Picture List", "Common.define.smartArt.textCaptionedPictures": "Captioned Pictures", "Common.define.smartArt.textChevronAccentProcess": "Chevron Accent Process", "Common.define.smartArt.textChevronList": "Chevron List", "Common.define.smartArt.textCircleAccentTimeline": "Circle Accent Timeline", "Common.define.smartArt.textCircleArrowProcess": "Circle Arrow Process", "Common.define.smartArt.textCirclePictureHierarchy": "Circle Picture Hierarchy", "Common.define.smartArt.textCircleProcess": "Circle Process", "Common.define.smartArt.textCircleRelationship": "Circle Relationship", "Common.define.smartArt.textCircularBendingProcess": "Circular Bending Process", "Common.define.smartArt.textCircularPictureCallout": "Circular Picture Callout", "Common.define.smartArt.textClosedChevronProcess": "Closed Chevron Process", "Common.define.smartArt.textContinuousArrowProcess": "Continuous Arrow Process", "Common.define.smartArt.textContinuousBlockProcess": "Continuous Block Process", "Common.define.smartArt.textContinuousCycle": "Continuous Cycle", "Common.define.smartArt.textContinuousPictureList": "Continuous Picture List", "Common.define.smartArt.textConvergingArrows": "Converging Arrows", "Common.define.smartArt.textConvergingRadial": "Converging Radial", "Common.define.smartArt.textConvergingText": "Converging Text", "Common.define.smartArt.textCounterbalanceArrows": "Counterbalance Arrows", "Common.define.smartArt.textCycle": "Cycle", "Common.define.smartArt.textCycleMatrix": "Cycle Matrix", "Common.define.smartArt.textDescendingBlockList": "Descending Block List", "Common.define.smartArt.textDescendingProcess": "Descending Process", "Common.define.smartArt.textDetailedProcess": "Detailed Process", "Common.define.smartArt.textDivergingArrows": "Diverging Arrows", "Common.define.smartArt.textDivergingRadial": "Diverging Radial", "Common.define.smartArt.textEquation": "Equation", "Common.define.smartArt.textFramedTextPicture": "Framed Text Picture", "Common.define.smartArt.textFunnel": "Funnel", "Common.define.smartArt.textGear": "Gear", "Common.define.smartArt.textGridMatrix": "Grid Matrix", "Common.define.smartArt.textGroupedList": "Grouped List", "Common.define.smartArt.textHalfCircleOrganizationChart": "Half Circle Organization Chart", "Common.define.smartArt.textHexagonCluster": "Hexagon Cluster", "Common.define.smartArt.textHexagonRadial": "Hexagon Radial", "Common.define.smartArt.textHierarchy": "Hierarchy", "Common.define.smartArt.textHierarchyList": "Hierarchy List", "Common.define.smartArt.textHorizontalBulletList": "Horizontal Bullet List", "Common.define.smartArt.textHorizontalHierarchy": "Horizontal Hierarchy", "Common.define.smartArt.textHorizontalLabeledHierarchy": "Horizontal Labeled Hierarchy", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "Horizontal Multi-Level Hierarchy", "Common.define.smartArt.textHorizontalOrganizationChart": "Horizontal Organization Chart", "Common.define.smartArt.textHorizontalPictureList": "Horizontal Picture List", "Common.define.smartArt.textIncreasingArrowProcess": "Increasing Arrow Process", "Common.define.smartArt.textIncreasingCircleProcess": "Increasing Circle Process", "Common.define.smartArt.textInterconnectedBlockProcess": "Interconnected Block Process", "Common.define.smartArt.textInterconnectedRings": "Interconnected Rings", "Common.define.smartArt.textInvertedPyramid": "Inverted Pyramid", "Common.define.smartArt.textLabeledHierarchy": "Labeled Hierarchy", "Common.define.smartArt.textLinearVenn": "Linear Venn", "Common.define.smartArt.textLinedList": "Lined List", "Common.define.smartArt.textList": "List", "Common.define.smartArt.textMatrix": "Matrix", "Common.define.smartArt.textMultidirectionalCycle": "Multidirectional Cycle", "Common.define.smartArt.textNameAndTitleOrganizationChart": "Name and Title Organization Chart", "Common.define.smartArt.textNestedTarget": "Nested Target", "Common.define.smartArt.textNondirectionalCycle": "Nondirectional Cycle", "Common.define.smartArt.textOpposingArrows": "Opposing Arrows", "Common.define.smartArt.textOpposingIdeas": "Opposing Ideas", "Common.define.smartArt.textOrganizationChart": "Organization Chart", "Common.define.smartArt.textOther": "Other", "Common.define.smartArt.textPhasedProcess": "Phased Process", "Common.define.smartArt.textPicture": "Picture", "Common.define.smartArt.textPictureAccentBlocks": "Picture Accent Blocks", "Common.define.smartArt.textPictureAccentList": "Picture Accent List", "Common.define.smartArt.textPictureAccentProcess": "Picture Accent Process", "Common.define.smartArt.textPictureCaptionList": "Picture Caption List", "Common.define.smartArt.textPictureFrame": "PictureFrame", "Common.define.smartArt.textPictureGrid": "Picture Grid", "Common.define.smartArt.textPictureLineup": "Picture Lineup", "Common.define.smartArt.textPictureOrganizationChart": "Picture Organization Chart", "Common.define.smartArt.textPictureStrips": "Picture Strips", "Common.define.smartArt.textPieProcess": "Pie Process", "Common.define.smartArt.textPlusAndMinus": "Plus and Minus", "Common.define.smartArt.textProcess": "Process", "Common.define.smartArt.textProcessArrows": "Process Arrows", "Common.define.smartArt.textProcessList": "Process List", "Common.define.smartArt.textPyramid": "Pyramid", "Common.define.smartArt.textPyramidList": "Pyramid List", "Common.define.smartArt.textRadialCluster": "Radial Cluster", "Common.define.smartArt.textRadialCycle": "Radial Cycle", "Common.define.smartArt.textRadialList": "Radial List", "Common.define.smartArt.textRadialPictureList": "Radial Picture List", "Common.define.smartArt.textRadialVenn": "Radial Venn", "Common.define.smartArt.textRandomToResultProcess": "Random to Result Process", "Common.define.smartArt.textRelationship": "Relationship", "Common.define.smartArt.textRepeatingBendingProcess": "Repeating Bending Process", "Common.define.smartArt.textReverseList": "Reverse List", "Common.define.smartArt.textSegmentedCycle": "Segmented Cycle", "Common.define.smartArt.textSegmentedProcess": "Segmented Process", "Common.define.smartArt.textSegmentedPyramid": "Segmented Pyramid", "Common.define.smartArt.textSnapshotPictureList": "Snapshot Picture List", "Common.define.smartArt.textSpiralPicture": "Spiral Picture", "Common.define.smartArt.textSquareAccentList": "Square Accent List", "Common.define.smartArt.textStackedList": "Stacked List", "Common.define.smartArt.textStackedVenn": "Stacked <PERSON>n", "Common.define.smartArt.textStaggeredProcess": "Staggered Process", "Common.define.smartArt.textStepDownProcess": "Step Down Process", "Common.define.smartArt.textStepUpProcess": "Step Up Process", "Common.define.smartArt.textSubStepProcess": "Sub-Step Process", "Common.define.smartArt.textTabbedArc": "Tabbed <PERSON>", "Common.define.smartArt.textTableHierarchy": "Table Hierarchy", "Common.define.smartArt.textTableList": "Table List", "Common.define.smartArt.textTabList": "Tab List", "Common.define.smartArt.textTargetList": "Target List", "Common.define.smartArt.textTextCycle": "Text Cycle", "Common.define.smartArt.textThemePictureAccent": "Theme Picture Accent", "Common.define.smartArt.textThemePictureAlternatingAccent": "Theme Picture Alternating Accent", "Common.define.smartArt.textThemePictureGrid": "Theme Picture Grid", "Common.define.smartArt.textTitledMatrix": "Titled Matrix", "Common.define.smartArt.textTitledPictureAccentList": "Titled Picture Accent List", "Common.define.smartArt.textTitledPictureBlocks": "Titled Picture Blocks", "Common.define.smartArt.textTitlePictureLineup": "Title Picture Lineup", "Common.define.smartArt.textTrapezoidList": "Trapezoid List", "Common.define.smartArt.textUpwardArrow": "Upward Arrow", "Common.define.smartArt.textVaryingWidthList": "Varying Width List", "Common.define.smartArt.textVerticalAccentList": "Vertical Accent List", "Common.define.smartArt.textVerticalArrowList": "Vertical Arrow List", "Common.define.smartArt.textVerticalBendingProcess": "Vertical Bending Process", "Common.define.smartArt.textVerticalBlockList": "Vertical Block List", "Common.define.smartArt.textVerticalBoxList": "Vertical Box List", "Common.define.smartArt.textVerticalBracketList": "Vertical Bracket List", "Common.define.smartArt.textVerticalBulletList": "Vertical Bullet List", "Common.define.smartArt.textVerticalChevronList": "Vertical Chevron List", "Common.define.smartArt.textVerticalCircleList": "Vertical Circle List", "Common.define.smartArt.textVerticalCurvedList": "Vertical Curved List", "Common.define.smartArt.textVerticalEquation": "Vertical Equation", "Common.define.smartArt.textVerticalPictureAccentList": "Vertical Picture Accent List", "Common.define.smartArt.textVerticalPictureList": "Vertical Picture List", "Common.define.smartArt.textVerticalProcess": "Vertical Process", "Common.Translation.textMoreButton": "More", "Common.Translation.tipFileLocked": "Document is locked for editing. You can make changes and save it as local copy later.", "Common.Translation.tipFileReadOnly": "The file is read-only. To keep your changes, save the file with a new name or in a different location.", "Common.Translation.warnFileLocked": "You can't edit this file because it's being edited in another app.", "Common.Translation.warnFileLockedBtnEdit": "Create a copy", "Common.Translation.warnFileLockedBtnView": "Open for viewing", "Common.UI.ButtonColored.textAutoColor": "Automatic", "Common.UI.ButtonColored.textNewColor": "Add new custom color", "Common.UI.Calendar.textApril": "April", "Common.UI.Calendar.textAugust": "August", "Common.UI.Calendar.textDecember": "December", "Common.UI.Calendar.textFebruary": "February", "Common.UI.Calendar.textJanuary": "January", "Common.UI.Calendar.textJuly": "July", "Common.UI.Calendar.textJune": "June", "Common.UI.Calendar.textMarch": "March", "Common.UI.Calendar.textMay": "May", "Common.UI.Calendar.textMonths": "Months", "Common.UI.Calendar.textNovember": "November", "Common.UI.Calendar.textOctober": "October", "Common.UI.Calendar.textSeptember": "September", "Common.UI.Calendar.textShortApril": "Apr", "Common.UI.Calendar.textShortAugust": "Aug", "Common.UI.Calendar.textShortDecember": "Dec", "Common.UI.Calendar.textShortFebruary": "Feb", "Common.UI.Calendar.textShortFriday": "Fr", "Common.UI.Calendar.textShortJanuary": "Jan", "Common.UI.Calendar.textShortJuly": "Jul", "Common.UI.Calendar.textShortJune": "Jun", "Common.UI.Calendar.textShortMarch": "Mar", "Common.UI.Calendar.textShortMay": "May", "Common.UI.Calendar.textShortMonday": "Mo", "Common.UI.Calendar.textShortNovember": "Nov", "Common.UI.Calendar.textShortOctober": "Oct", "Common.UI.Calendar.textShortSaturday": "Sa", "Common.UI.Calendar.textShortSeptember": "Sep", "Common.UI.Calendar.textShortSunday": "Su", "Common.UI.Calendar.textShortThursday": "Th", "Common.UI.Calendar.textShortTuesday": "Tu", "Common.UI.Calendar.textShortWednesday": "We", "Common.UI.Calendar.textYears": "Years", "Common.UI.ComboBorderSize.txtNoBorders": "No borders", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "No borders", "Common.UI.ComboDataView.emptyComboText": "No styles", "Common.UI.ExtendedColorDialog.addButtonText": "Add", "Common.UI.ExtendedColorDialog.textCurrent": "Current", "Common.UI.ExtendedColorDialog.textHexErr": "The entered value is incorrect.<br>Please enter a value between 000000 and FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "New", "Common.UI.ExtendedColorDialog.textRGBErr": "The entered value is incorrect.<br>Please enter a numeric value between 0 and 255.", "Common.UI.HSBColorPicker.textNoColor": "No Color", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "Hide password", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "Show password", "Common.UI.SearchBar.textFind": "Find", "Common.UI.SearchBar.tipCloseSearch": "Close search", "Common.UI.SearchBar.tipNextResult": "Next result", "Common.UI.SearchBar.tipOpenAdvancedSettings": "Open advanced settings", "Common.UI.SearchBar.tipPreviousResult": "Previous result", "Common.UI.SearchDialog.textHighlight": "Highlight results", "Common.UI.SearchDialog.textMatchCase": "Case sensitive", "Common.UI.SearchDialog.textReplaceDef": "Enter the replacement text", "Common.UI.SearchDialog.textSearchStart": "Enter your text here", "Common.UI.SearchDialog.textTitle": "Find and replace", "Common.UI.SearchDialog.textTitle2": "Find", "Common.UI.SearchDialog.textWholeWords": "Whole words only", "Common.UI.SearchDialog.txtBtnHideReplace": "<PERSON><PERSON>", "Common.UI.SearchDialog.txtBtnReplace": "Replace", "Common.UI.SearchDialog.txtBtnReplaceAll": "Replace all", "Common.UI.SynchronizeTip.textDontShow": "Don't show this message again", "Common.UI.SynchronizeTip.textSynchronize": "The document has been changed by another user.<br>Please click to save your changes and reload the updates.", "Common.UI.ThemeColorPalette.textRecentColors": "Recent colors", "Common.UI.ThemeColorPalette.textStandartColors": "Standard colors", "Common.UI.ThemeColorPalette.textThemeColors": "Theme colors", "Common.UI.Themes.txtThemeClassicLight": "Classic Light", "Common.UI.Themes.txtThemeContrastDark": "Contrast Dark", "Common.UI.Themes.txtThemeDark": "Dark", "Common.UI.Themes.txtThemeLight": "Light", "Common.UI.Themes.txtThemeSystem": "Same as system", "Common.UI.Window.cancelButtonText": "Cancel", "Common.UI.Window.closeButtonText": "Close", "Common.UI.Window.noButtonText": "No", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "Confirmation", "Common.UI.Window.textDontShow": "Don't show this message again", "Common.UI.Window.textError": "Error", "Common.UI.Window.textInformation": "Information", "Common.UI.Window.textWarning": "Warning", "Common.UI.Window.yesButtonText": "Yes", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Shift", "Common.Views.About.txtAddress": "address: ", "Common.Views.About.txtLicensee": "LICENSEE", "Common.Views.About.txtLicensor": "LICENSOR", "Common.Views.About.txtMail": "email: ", "Common.Views.About.txtPoweredBy": "Powered by", "Common.Views.About.txtTel": "tel.: ", "Common.Views.About.txtVersion": "Version ", "Common.Views.AutoCorrectDialog.textAdd": "Add", "Common.Views.AutoCorrectDialog.textApplyText": "Apply as you type", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Text AutoCorrect", "Common.Views.AutoCorrectDialog.textAutoFormat": "AutoFormat as you type", "Common.Views.AutoCorrectDialog.textBulleted": "Automatic bulleted lists", "Common.Views.AutoCorrectDialog.textBy": "By", "Common.Views.AutoCorrectDialog.textDelete": "Delete", "Common.Views.AutoCorrectDialog.textDoubleSpaces": "Add period with double-space", "Common.Views.AutoCorrectDialog.textFLCells": "Capitalize first letter of table cells", "Common.Views.AutoCorrectDialog.textFLSentence": "Capitalize first letter of sentences", "Common.Views.AutoCorrectDialog.textHyperlink": "Internet and network paths with hyperlinks", "Common.Views.AutoCorrectDialog.textHyphens": "Hyphens (--) with dash (—)", "Common.Views.AutoCorrectDialog.textMathCorrect": "Math AutoCorrect", "Common.Views.AutoCorrectDialog.textNumbered": "Automatic numbered lists", "Common.Views.AutoCorrectDialog.textQuotes": "\"Straight quotes\" with \"smart quotes\"", "Common.Views.AutoCorrectDialog.textRecognized": "Recognized functions", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "The following expressions are recognized math expressions. They will not be automatically italicized.", "Common.Views.AutoCorrectDialog.textReplace": "Replace", "Common.Views.AutoCorrectDialog.textReplaceText": "Replace as you type", "Common.Views.AutoCorrectDialog.textReplaceType": "Replace text as you type", "Common.Views.AutoCorrectDialog.textReset": "Reset", "Common.Views.AutoCorrectDialog.textResetAll": "Reset to default", "Common.Views.AutoCorrectDialog.textRestore": "Rest<PERSON>", "Common.Views.AutoCorrectDialog.textTitle": "AutoCorrect", "Common.Views.AutoCorrectDialog.textWarnAddRec": "Recognized functions must contain only the letters A through Z, uppercase or lowercase.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "Any expression you added will be removed and the removed ones will be restored. Do you want to continue?", "Common.Views.AutoCorrectDialog.warnReplace": "The autocorrect entry for %1 already exists. Do you want to replace it?", "Common.Views.AutoCorrectDialog.warnReset": "Any autocorrect you added will be removed and the changed ones will be restored to their original values. Do you want to continue?", "Common.Views.AutoCorrectDialog.warnRestore": "The autocorrect entry for %1 will be reset to its original value. Do you want to continue?", "Common.Views.Chat.textSend": "Send", "Common.Views.Comments.mniAuthorAsc": "Author A to Z", "Common.Views.Comments.mniAuthorDesc": "Author Z to A", "Common.Views.Comments.mniDateAsc": "Oldest", "Common.Views.Comments.mniDateDesc": "Newest", "Common.Views.Comments.mniFilterGroups": "Filter by Group", "Common.Views.Comments.mniPositionAsc": "From top", "Common.Views.Comments.mniPositionDesc": "From bottom", "Common.Views.Comments.textAdd": "Add", "Common.Views.Comments.textAddComment": "Add Comment", "Common.Views.Comments.textAddCommentToDoc": "Add Comment to Document", "Common.Views.Comments.textAddReply": "Add Reply", "Common.Views.Comments.textAll": "All", "Common.Views.Comments.textAnonym": "Guest", "Common.Views.Comments.textCancel": "Cancel", "Common.Views.Comments.textClose": "Close", "Common.Views.Comments.textClosePanel": "Close comments", "Common.Views.Comments.textComments": "Comments", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "Enter your comment here", "Common.Views.Comments.textHintAddComment": "Add comment", "Common.Views.Comments.textOpenAgain": "Open Again", "Common.Views.Comments.textReply": "Reply", "Common.Views.Comments.textResolve": "Resolve", "Common.Views.Comments.textResolved": "Resolved", "Common.Views.Comments.textSort": "Sort comments", "Common.Views.Comments.textViewResolved": "You have no permission to reopen the comment", "Common.Views.Comments.txtEmpty": "There are no comments in the document.", "Common.Views.CopyWarningDialog.textDontShow": "Don't show this message again", "Common.Views.CopyWarningDialog.textMsg": "Copy, cut and paste actions using the editor toolbar buttons and context menu actions will be performed within this editor tab only.<br><br>To copy or paste to or from applications outside the editor tab use the following keyboard combinations:", "Common.Views.CopyWarningDialog.textTitle": "Copy, Cut and Paste actions", "Common.Views.CopyWarningDialog.textToCopy": "for Co<PERSON>", "Common.Views.CopyWarningDialog.textToCut": "for Cut", "Common.Views.CopyWarningDialog.textToPaste": "for Paste", "Common.Views.DocumentAccessDialog.textLoading": "Loading...", "Common.Views.DocumentAccessDialog.textTitle": "Sharing settings", "Common.Views.ExternalDiagramEditor.textTitle": "Chart Editor", "Common.Views.ExternalEditor.textClose": "Close", "Common.Views.ExternalEditor.textSave": "Save & Exit", "Common.Views.ExternalMergeEditor.textTitle": "Mail Merge Recipients", "Common.Views.ExternalOleEditor.textTitle": "Spreadsheet Editor", "Common.Views.Header.labelCoUsersDescr": "Users who are editing the file:", "Common.Views.Header.textAddFavorite": "<PERSON> as favorite", "Common.Views.Header.textAdvSettings": "Advanced settings", "Common.Views.Header.textBack": "Open file location", "Common.Views.Header.textCompactView": "<PERSON><PERSON>", "Common.Views.Header.textHideLines": "Hide Rulers", "Common.Views.Header.textHideStatusBar": "Hide Status Bar", "Common.Views.Header.textReadOnly": "Read only", "Common.Views.Header.textRemoveFavorite": "Remove from Favorites", "Common.Views.Header.textShare": "Share", "Common.Views.Header.textZoom": "Zoom", "Common.Views.Header.tipAccessRights": "Manage document access rights", "Common.Views.Header.tipDownload": "Download file", "Common.Views.Header.tipGoEdit": "Edit current file", "Common.Views.Header.tipPrint": "Print file", "Common.Views.Header.tipPrintQuick": "Quick print", "Common.Views.Header.tipRedo": "Redo", "Common.Views.Header.tipSave": "Save", "Common.Views.Header.tipSearch": "Search", "Common.Views.Header.tipUndo": "Undo", "Common.Views.Header.tipUsers": "View users", "Common.Views.Header.tipViewSettings": "View settings", "Common.Views.Header.tipViewUsers": "View users and manage document access rights", "Common.Views.Header.txtAccessRights": "Change access rights", "Common.Views.Header.txtRename": "<PERSON><PERSON>", "Common.Views.History.textCloseHistory": "Close History", "Common.Views.History.textHide": "Collapse", "Common.Views.History.textHideAll": "Hide detailed changes", "Common.Views.History.textRestore": "Rest<PERSON>", "Common.Views.History.textShow": "Expand", "Common.Views.History.textShowAll": "Show detailed changes", "Common.Views.History.textVer": "ver.", "Common.Views.ImageFromUrlDialog.textUrl": "Paste an image URL:", "Common.Views.ImageFromUrlDialog.txtEmpty": "This field is required", "Common.Views.ImageFromUrlDialog.txtNotUrl": "This field should be a URL in the \"http://www.example.com\" format", "Common.Views.InsertTableDialog.textInvalidRowsCols": "You need to specify valid rows and columns count.", "Common.Views.InsertTableDialog.txtColumns": "Number of columns", "Common.Views.InsertTableDialog.txtMaxText": "The maximum value for this field is {0}.", "Common.Views.InsertTableDialog.txtMinText": "The minimum value for this field is {0}.", "Common.Views.InsertTableDialog.txtRows": "Number of rows", "Common.Views.InsertTableDialog.txtTitle": "Table size", "Common.Views.InsertTableDialog.txtTitleSplit": "Split cell", "Common.Views.LanguageDialog.labelSelect": "Select document language", "Common.Views.OpenDialog.closeButtonText": "Close file", "Common.Views.OpenDialog.txtEncoding": "Encoding ", "Common.Views.OpenDialog.txtIncorrectPwd": "Password is incorrect.", "Common.Views.OpenDialog.txtOpenFile": "Enter a password to open the file", "Common.Views.OpenDialog.txtPassword": "Password", "Common.Views.OpenDialog.txtPreview": "Preview", "Common.Views.OpenDialog.txtProtected": "Once you enter the password and open the file, the current password to the file will be reset.", "Common.Views.OpenDialog.txtTitle": "Choose %1 options", "Common.Views.OpenDialog.txtTitleProtected": "Protected file", "Common.Views.PasswordDialog.txtDescription": "Set a password to protect this document", "Common.Views.PasswordDialog.txtIncorrectPwd": "Confirmation password is not identical", "Common.Views.PasswordDialog.txtPassword": "Password", "Common.Views.PasswordDialog.txtRepeat": "Repeat password", "Common.Views.PasswordDialog.txtTitle": "Set password", "Common.Views.PasswordDialog.txtWarning": "Warning: If you lose or forget the password, it cannot be recovered. Please keep it in a safe place.", "Common.Views.PluginDlg.textLoading": "Loading", "Common.Views.Plugins.groupCaption": "Plugins", "Common.Views.Plugins.strPlugins": "Plugins", "Common.Views.Plugins.textClosePanel": "Close plugin", "Common.Views.Plugins.textLoading": "Loading", "Common.Views.Plugins.textStart": "Start", "Common.Views.Plugins.textStop": "Stop", "Common.Views.Protection.hintAddPwd": "Encrypt with password", "Common.Views.Protection.hintDelPwd": "Delete password", "Common.Views.Protection.hintPwd": "Change or delete password", "Common.Views.Protection.hintSignature": "Add digital signature or signature line", "Common.Views.Protection.txtAddPwd": "Add password", "Common.Views.Protection.txtChangePwd": "Change password", "Common.Views.Protection.txtDeletePwd": "Delete password", "Common.Views.Protection.txtEncrypt": "Encrypt", "Common.Views.Protection.txtInvisibleSignature": "Add digital signature", "Common.Views.Protection.txtSignature": "Signature", "Common.Views.Protection.txtSignatureLine": "Add signature line", "Common.Views.RenameDialog.textName": "File name", "Common.Views.RenameDialog.txtInvalidName": "The file name cannot contain any of the following characters: ", "Common.Views.ReviewChanges.hintNext": "To next change", "Common.Views.ReviewChanges.hintPrev": "To previous change", "Common.Views.ReviewChanges.mniFromFile": "Document from File", "Common.Views.ReviewChanges.mniFromStorage": "Document from Storage", "Common.Views.ReviewChanges.mniFromUrl": "Document from URL", "Common.Views.ReviewChanges.mniSettings": "Comparison Settings", "Common.Views.ReviewChanges.strFast": "Fast", "Common.Views.ReviewChanges.strFastDesc": "Real-time co-editing. All changes are saved automatically.", "Common.Views.ReviewChanges.strStrict": "Strict", "Common.Views.ReviewChanges.strStrictDesc": "Use the 'Save' button to sync the changes you and others make.", "Common.Views.ReviewChanges.textEnable": "Enable", "Common.Views.ReviewChanges.textWarnTrackChanges": "Track Changes will be switched ON for all users with full access. The next time anyone opens the doc, Track Changes will remain enabled.", "Common.Views.ReviewChanges.textWarnTrackChangesTitle": "Enable Track Changes for everyone?", "Common.Views.ReviewChanges.tipAcceptCurrent": "Accept current change", "Common.Views.ReviewChanges.tipCoAuthMode": "Set co-editing mode", "Common.Views.ReviewChanges.tipCommentRem": "<PERSON>mo<PERSON> comments", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Remove current comments", "Common.Views.ReviewChanges.tipCommentResolve": "Resolve comments", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Resolve current comments", "Common.Views.ReviewChanges.tipCompare": "Compare current document with another one", "Common.Views.ReviewChanges.tipHistory": "Show version history", "Common.Views.ReviewChanges.tipRejectCurrent": "Reject current change", "Common.Views.ReviewChanges.tipReview": "Track changes", "Common.Views.ReviewChanges.tipReviewView": "Select the mode you want the changes to be displayed", "Common.Views.ReviewChanges.tipSetDocLang": "Set document language", "Common.Views.ReviewChanges.tipSetSpelling": "Spell checking", "Common.Views.ReviewChanges.tipSharing": "Manage document access rights", "Common.Views.ReviewChanges.txtAccept": "Accept", "Common.Views.ReviewChanges.txtAcceptAll": "Accept All Changes", "Common.Views.ReviewChanges.txtAcceptChanges": "Accept changes", "Common.Views.ReviewChanges.txtAcceptCurrent": "Accept Current Change", "Common.Views.ReviewChanges.txtChat": "Cha<PERSON>", "Common.Views.ReviewChanges.txtClose": "Close", "Common.Views.ReviewChanges.txtCoAuthMode": "Co-editing Mode", "Common.Views.ReviewChanges.txtCommentRemAll": "Remove all comments", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Remove current comments", "Common.Views.ReviewChanges.txtCommentRemMy": "Remove my comments", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Remove My Current Comments", "Common.Views.ReviewChanges.txtCommentRemove": "Remove", "Common.Views.ReviewChanges.txtCommentResolve": "Resolve", "Common.Views.ReviewChanges.txtCommentResolveAll": "Resolve all comments", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Resolve current comments", "Common.Views.ReviewChanges.txtCommentResolveMy": "Resolve my comments", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Resolve My Current Comments", "Common.Views.ReviewChanges.txtCompare": "Compare", "Common.Views.ReviewChanges.txtDocLang": "Language", "Common.Views.ReviewChanges.txtEditing": "Editing", "Common.Views.ReviewChanges.txtFinal": "All changes accepted {0}", "Common.Views.ReviewChanges.txtFinalCap": "Final", "Common.Views.ReviewChanges.txtHistory": "Version History", "Common.Views.ReviewChanges.txtMarkup": "All changes {0}", "Common.Views.ReviewChanges.txtMarkupCap": "Markup and balloons", "Common.Views.ReviewChanges.txtMarkupSimple": "All changes {0}<br>No balloons", "Common.Views.ReviewChanges.txtMarkupSimpleCap": "Only markup", "Common.Views.ReviewChanges.txtNext": "Next", "Common.Views.ReviewChanges.txtOff": "OFF for me", "Common.Views.ReviewChanges.txtOffGlobal": "OFF for me and everyone", "Common.Views.ReviewChanges.txtOn": "ON for me", "Common.Views.ReviewChanges.txtOnGlobal": "ON for me and everyone", "Common.Views.ReviewChanges.txtOriginal": "All changes rejected {0}", "Common.Views.ReviewChanges.txtOriginalCap": "Original", "Common.Views.ReviewChanges.txtPrev": "Previous", "Common.Views.ReviewChanges.txtPreview": "Preview", "Common.Views.ReviewChanges.txtReject": "Reject", "Common.Views.ReviewChanges.txtRejectAll": "Reject All Changes", "Common.Views.ReviewChanges.txtRejectChanges": "Reject changes", "Common.Views.ReviewChanges.txtRejectCurrent": "Reject Current Change", "Common.Views.ReviewChanges.txtSharing": "Sharing", "Common.Views.ReviewChanges.txtSpelling": "Spell Checking", "Common.Views.ReviewChanges.txtTurnon": "Track Changes", "Common.Views.ReviewChanges.txtView": "Display Mode", "Common.Views.ReviewChangesDialog.textTitle": "Review changes", "Common.Views.ReviewChangesDialog.txtAccept": "Accept", "Common.Views.ReviewChangesDialog.txtAcceptAll": "Accept all changes", "Common.Views.ReviewChangesDialog.txtAcceptCurrent": "Accept current change", "Common.Views.ReviewChangesDialog.txtNext": "To next change", "Common.Views.ReviewChangesDialog.txtPrev": "To previous change", "Common.Views.ReviewChangesDialog.txtReject": "Reject", "Common.Views.ReviewChangesDialog.txtRejectAll": "Reject all changes", "Common.Views.ReviewChangesDialog.txtRejectCurrent": "Reject current change", "Common.Views.ReviewPopover.textAdd": "Add", "Common.Views.ReviewPopover.textAddReply": "Add Reply", "Common.Views.ReviewPopover.textCancel": "Cancel", "Common.Views.ReviewPopover.textClose": "Close", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textEnterComment": "Enter your comment here", "Common.Views.ReviewPopover.textFollowMove": "Follow Move", "Common.Views.ReviewPopover.textMention": "+mention will provide access to the document and send an email", "Common.Views.ReviewPopover.textMentionNotify": "+mention will notify the user via email", "Common.Views.ReviewPopover.textOpenAgain": "Open Again", "Common.Views.ReviewPopover.textReply": "Reply", "Common.Views.ReviewPopover.textResolve": "Resolve", "Common.Views.ReviewPopover.textViewResolved": "You have no permission to reopen the comment", "Common.Views.ReviewPopover.txtAccept": "Accept", "Common.Views.ReviewPopover.txtDeleteTip": "Delete", "Common.Views.ReviewPopover.txtEditTip": "Edit", "Common.Views.ReviewPopover.txtReject": "Reject", "Common.Views.SaveAsDlg.textLoading": "Loading", "Common.Views.SaveAsDlg.textTitle": "Folder for save", "Common.Views.SearchPanel.textCaseSensitive": "Case sensitive", "Common.Views.SearchPanel.textCloseSearch": "Close search", "Common.Views.SearchPanel.textContentChanged": "Document changed.", "Common.Views.SearchPanel.textFind": "Find", "Common.Views.SearchPanel.textFindAndReplace": "Find and replace", "Common.Views.SearchPanel.textMatchUsingRegExp": "Match using regular expressions", "Common.Views.SearchPanel.textNoMatches": "No matches", "Common.Views.SearchPanel.textNoSearchResults": "No search results", "Common.Views.SearchPanel.textReplace": "Replace", "Common.Views.SearchPanel.textReplaceAll": "Replace All", "Common.Views.SearchPanel.textReplaceWith": "Replace with", "Common.Views.SearchPanel.textSearchAgain": "{0}Perform new search{1} for accurate results.", "Common.Views.SearchPanel.textSearchHasStopped": "Search has stopped", "Common.Views.SearchPanel.textSearchResults": "Search results: {0}/{1}", "Common.Views.SearchPanel.textTooManyResults": "There are too many results to show here", "Common.Views.SearchPanel.textWholeWords": "Whole words only", "Common.Views.SearchPanel.tipNextResult": "Next result", "Common.Views.SearchPanel.tipPreviousResult": "Previous result", "Common.Views.SelectFileDlg.textLoading": "Loading", "Common.Views.SelectFileDlg.textTitle": "Select data source", "Common.Views.SignDialog.textBold": "Bold", "Common.Views.SignDialog.textCertificate": "Certificate", "Common.Views.SignDialog.textChange": "Change", "Common.Views.SignDialog.textInputName": "Input signer name", "Common.Views.SignDialog.textItalic": "Italic", "Common.Views.SignDialog.textNameError": "Signer name must not be empty.", "Common.Views.SignDialog.textPurpose": "Purpose for signing this document", "Common.Views.SignDialog.textSelect": "Select", "Common.Views.SignDialog.textSelectImage": "Select image", "Common.Views.SignDialog.textSignature": "Signature looks as", "Common.Views.SignDialog.textTitle": "Sign document", "Common.Views.SignDialog.textUseImage": "or click 'Select Image' to use a picture as signature", "Common.Views.SignDialog.textValid": "Valid from %1 to %2", "Common.Views.SignDialog.tipFontName": "Font name", "Common.Views.SignDialog.tipFontSize": "Font size", "Common.Views.SignSettingsDialog.textAllowComment": "Allow signer to add comment in the signature dialog", "Common.Views.SignSettingsDialog.textDefInstruction": "Before signing this document, verify that the content you are signing is correct.", "Common.Views.SignSettingsDialog.textInfoEmail": "Suggested signer's e-mail", "Common.Views.SignSettingsDialog.textInfoName": "Suggested signer", "Common.Views.SignSettingsDialog.textInfoTitle": "Suggested signer's title", "Common.Views.SignSettingsDialog.textInstructions": "Instructions for signer", "Common.Views.SignSettingsDialog.textShowDate": "Show sign date in signature line", "Common.Views.SignSettingsDialog.textTitle": "Signature setup", "Common.Views.SignSettingsDialog.txtEmpty": "This field is required", "Common.Views.SymbolTableDialog.textCharacter": "Character", "Common.Views.SymbolTableDialog.textCode": "Unicode HEX value", "Common.Views.SymbolTableDialog.textCopyright": "Copyright sign", "Common.Views.SymbolTableDialog.textDCQuote": "Closing double quote", "Common.Views.SymbolTableDialog.textDOQuote": "Opening double quote", "Common.Views.SymbolTableDialog.textEllipsis": "Horizontal ellipsis", "Common.Views.SymbolTableDialog.textEmDash": "Em dash", "Common.Views.SymbolTableDialog.textEmSpace": "Em space", "Common.Views.SymbolTableDialog.textEnDash": "En dash", "Common.Views.SymbolTableDialog.textEnSpace": "En space", "Common.Views.SymbolTableDialog.textFont": "Font", "Common.Views.SymbolTableDialog.textNBHyphen": "Non-breaking hyphen", "Common.Views.SymbolTableDialog.textNBSpace": "No-break space", "Common.Views.SymbolTableDialog.textPilcrow": "Pilcrow sign", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 Em space", "Common.Views.SymbolTableDialog.textRange": "Range", "Common.Views.SymbolTableDialog.textRecent": "Recently used symbols", "Common.Views.SymbolTableDialog.textRegistered": "Registered sign", "Common.Views.SymbolTableDialog.textSCQuote": "Closing single quote", "Common.Views.SymbolTableDialog.textSection": "Section sign", "Common.Views.SymbolTableDialog.textShortcut": "Shortcut key", "Common.Views.SymbolTableDialog.textSHyphen": "Soft hyphen", "Common.Views.SymbolTableDialog.textSOQuote": "Opening single quote", "Common.Views.SymbolTableDialog.textSpecial": "Special characters", "Common.Views.SymbolTableDialog.textSymbols": "Symbols", "Common.Views.SymbolTableDialog.textTitle": "Symbol", "Common.Views.SymbolTableDialog.textTradeMark": "Trademark symbol ", "Common.Views.UserNameDialog.textDontShow": "Don't ask me again", "Common.Views.UserNameDialog.textLabel": "Label:", "Common.Views.UserNameDialog.textLabelError": "Label must not be empty.", "DE.Controllers.DocProtection.txtIsProtectedComment": "Document is protected. You may only insert comments to this document.", "DE.Controllers.DocProtection.txtIsProtectedForms": "Document is protected. You may only fill in forms in this document.", "DE.Controllers.DocProtection.txtIsProtectedTrack": "Document is protected. You may edit this document, but all changes will be tracked.", "DE.Controllers.DocProtection.txtIsProtectedView": "Document is protected. You may only view this document.", "DE.Controllers.DocProtection.txtWasProtectedComment": "Document has been protected by another user.\nYou may only insert comments to this document.", "DE.Controllers.DocProtection.txtWasProtectedForms": "Document has been protected by another user.\nYou may only fill in forms in this document.", "DE.Controllers.DocProtection.txtWasProtectedTrack": "Document has been protected by another user.\nYou may edit this document, but all changes will be tracked.", "DE.Controllers.DocProtection.txtWasProtectedView": "Document has been protected by another user.\nYou may only view this document.", "DE.Controllers.DocProtection.txtWasUnprotected": "Document has been unprotected.", "DE.Controllers.LeftMenu.leavePageText": "All unsaved changes in this document will be lost.<br> Click \"Cancel\" then \"Save\" to save them. Click \"OK\" to discard all the unsaved changes.", "DE.Controllers.LeftMenu.newDocumentTitle": "Unnamed document", "DE.Controllers.LeftMenu.notcriticalErrorTitle": "Warning", "DE.Controllers.LeftMenu.requestEditRightsText": "Requesting editing rights...", "DE.Controllers.LeftMenu.textLoadHistory": "Loading version history...", "DE.Controllers.LeftMenu.textNoTextFound": "The data you have been searching for could not be found. Please adjust your search options.", "DE.Controllers.LeftMenu.textReplaceSkipped": "The replacement has been made. {0} occurrences were skipped.", "DE.Controllers.LeftMenu.textReplaceSuccess": "The search has been done. Occurrences replaced: {0}", "DE.Controllers.LeftMenu.txtCompatible": "The document will be saved to the new format. It will allow to use all the editor features, but might affect the document layout.<br>Use the 'Compatibility' option of the advanced settings if you want to make the files compatible with older MS Word versions.", "DE.Controllers.LeftMenu.txtUntitled": "Untitled", "DE.Controllers.LeftMenu.warnDownloadAs": "If you continue saving in this format all features except the text will be lost.<br>Are you sure you want to continue?", "DE.Controllers.LeftMenu.warnDownloadAsPdf": "Your {0} will be converted to an editable format. This may take a while. The resulting document will be optimized to allow you to edit the text, so it might not look exactly like the original {0}, especially if the original file contained lots of graphics.", "DE.Controllers.LeftMenu.warnDownloadAsRTF": "If you continue saving in this format some of the formatting might be lost.<br>Are you sure you want to continue?", "DE.Controllers.LeftMenu.warnReplaceString": "{0} is not a valid special character for the replacement field.", "DE.Controllers.Main.applyChangesTextText": "Loading the changes...", "DE.Controllers.Main.applyChangesTitleText": "Loading the Changes", "DE.Controllers.Main.confirmMaxChangesSize": "The size of actions exceeds the limitation set for your server.<br>Press \"Undo\" to cancel your last action or press \"Continue\" to keep action locally (you need to download the file or copy its content to make sure nothing is lost).", "DE.Controllers.Main.convertationTimeoutText": "Conversion timeout exceeded.", "DE.Controllers.Main.criticalErrorExtText": "Press \"OK\" to return to document list.", "DE.Controllers.Main.criticalErrorTitle": "Error", "DE.Controllers.Main.downloadErrorText": "Download failed.", "DE.Controllers.Main.downloadMergeText": "Downloading...", "DE.Controllers.Main.downloadMergeTitle": "Downloading", "DE.Controllers.Main.downloadTextText": "Downloading document...", "DE.Controllers.Main.downloadTitleText": "Downloading Document", "DE.Controllers.Main.errorAccessDeny": "You are trying to perform an action you do not have rights for.<br>Please contact your Document Server administrator.", "DE.Controllers.Main.errorBadImageUrl": "Image URL is incorrect", "DE.Controllers.Main.errorCannotPasteImg": "We can't paste this image from the Clipboard, but you can save it to your device and \ninsert it from there, or you can copy the image without text and paste it into the document.", "DE.Controllers.Main.errorCoAuthoringDisconnect": "Server connection lost. The document cannot be edited right now.", "DE.Controllers.Main.errorComboSeries": "To create a combination chart, select at least two series of data.", "DE.Controllers.Main.errorCompare": "The Compare Documents feature is not available while co-editing. ", "DE.Controllers.Main.errorConnectToServer": "The document could not be saved. Please check connection settings or contact your administrator.<br>When you click the 'OK' button, you will be prompted to download the document.", "DE.Controllers.Main.errorDatabaseConnection": "External error.<br>Database connection error. Please contact support in case the error persists.", "DE.Controllers.Main.errorDataEncrypted": "Encrypted changes have been received, they cannot be deciphered.", "DE.Controllers.Main.errorDataRange": "Incorrect data range.", "DE.Controllers.Main.errorDefaultMessage": "Error code: %1", "DE.Controllers.Main.errorDirectUrl": "Please verify the link to the document.<br>This link must be a direct link to the file for downloading.", "DE.Controllers.Main.errorEditingDownloadas": "An error occurred during the work with the document.<br>Use the 'Download as' option to save the file backup copy to a drive.", "DE.Controllers.Main.errorEditingSaveas": "An error occurred during the work with the document.<br>Use the 'Save as...' option to save the file backup copy to a drive.", "DE.Controllers.Main.errorEmailClient": "No email client could be found.", "DE.Controllers.Main.errorEmptyTOC": "Start creating a table of contents by applying a heading style from the Styles gallery to the selected text.", "DE.Controllers.Main.errorFilePassProtect": "The file is password protected and cannot be opened.", "DE.Controllers.Main.errorFileSizeExceed": "The file size exceeds the limitation set for your server.<br>Please contact your Document Server administrator for details.", "DE.Controllers.Main.errorForceSave": "An error occurred while saving the file. Please use the 'Download as' option to save the file to a drive or try again later.", "DE.Controllers.Main.errorInconsistentExt": "An error has occurred while opening the file.<br>The file content does not match the file extension.", "DE.Controllers.Main.errorInconsistentExtDocx": "An error has occurred while opening the file.<br>The file content corresponds to text documents (e.g. docx), but the file has the inconsistent extension: %1.", "DE.Controllers.Main.errorInconsistentExtPdf": "An error has occurred while opening the file.<br>The file content corresponds to one of the following formats: pdf/djvu/xps/oxps, but the file has the inconsistent extension: %1.", "DE.Controllers.Main.errorInconsistentExtPptx": "An error has occurred while opening the file.<br>The file content corresponds to presentations (e.g. pptx), but the file has the inconsistent extension: %1.", "DE.Controllers.Main.errorInconsistentExtXlsx": "An error has occurred while opening the file.<br>The file content corresponds to spreadsheets (e.g. xlsx), but the file has the inconsistent extension: %1.", "DE.Controllers.Main.errorKeyEncrypt": "Unknown key descriptor", "DE.Controllers.Main.errorKeyExpire": "Key descriptor expired", "DE.Controllers.Main.errorLoadingFont": "Fonts are not loaded.<br>Please contact your Document Server administrator.", "DE.Controllers.Main.errorMailMergeLoadFile": "Loading the document failed. Please select a different file.", "DE.Controllers.Main.errorMailMergeSaveFile": "Me<PERSON> failed.", "DE.Controllers.Main.errorNoTOC": "There's no table of contents to update. You can insert one from the References tab.", "DE.Controllers.Main.errorPasswordIsNotCorrect": "The password you supplied is not correct.<br>Verify that the CAPS LOCK key is off and be sure to use the correct capitalization.", "DE.Controllers.Main.errorProcessSaveResult": "Saving failed.", "DE.Controllers.Main.errorServerVersion": "The editor version has been updated. The page will be reloaded to apply the changes.", "DE.Controllers.Main.errorSessionAbsolute": "The document editing session has expired. Please reload the page.", "DE.Controllers.Main.errorSessionIdle": "The document has not been edited for quite a long time. Please reload the page.", "DE.Controllers.Main.errorSessionToken": "The connection to the server has been interrupted. Please reload the page.", "DE.Controllers.Main.errorSetPassword": "Password could not be set.", "DE.Controllers.Main.errorStockChart": "Incorrect row order. To build a stock chart place the data on the sheet in the following order:<br> opening price, max price, min price, closing price.", "DE.Controllers.Main.errorSubmit": "Submit failed.", "DE.Controllers.Main.errorTextFormWrongFormat": "The value entered does not match the format of the field.", "DE.Controllers.Main.errorToken": "The document security token is not correctly formed.<br>Please contact your Document Server administrator.", "DE.Controllers.Main.errorTokenExpire": "The document security token has expired.<br>Please contact your Document Server administrator.", "DE.Controllers.Main.errorUpdateVersion": "The file version has been changed. The page will be reloaded.", "DE.Controllers.Main.errorUpdateVersionOnDisconnect": "Connection has been restored, and the file version has been changed.<br>Before you can continue working, you need to download the file or copy its content to make sure nothing is lost, and then reload this page.", "DE.Controllers.Main.errorUserDrop": "The file cannot be accessed right now.", "DE.Controllers.Main.errorUsersExceed": "The number of users allowed by the pricing plan was exceeded", "DE.Controllers.Main.errorViewerDisconnect": "Connection is lost. You can still view the document,<br>but will not be able to download or print it until the connection is restored and page is reloaded.", "DE.Controllers.Main.leavePageText": "You have unsaved changes in this document. Click \"Stay on This Page\", then \"Save\" to save them. Click \"Leave This Page\" to discard all the unsaved changes.", "DE.Controllers.Main.leavePageTextOnClose": "All unsaved changes in this document will be lost.<br> Click \"Cancel\" then \"Save\" to save them. Click \"OK\" to discard all the unsaved changes.", "DE.Controllers.Main.loadFontsTextText": "Loading data...", "DE.Controllers.Main.loadFontsTitleText": "Loading Data", "DE.Controllers.Main.loadFontTextText": "Loading data...", "DE.Controllers.Main.loadFontTitleText": "Loading Data", "DE.Controllers.Main.loadImagesTextText": "Loading images...", "DE.Controllers.Main.loadImagesTitleText": "Loading Images", "DE.Controllers.Main.loadImageTextText": "Loading image...", "DE.Controllers.Main.loadImageTitleText": "Loading Image", "DE.Controllers.Main.loadingDocumentTextText": "Loading document...", "DE.Controllers.Main.loadingDocumentTitleText": "Loading document", "DE.Controllers.Main.mailMergeLoadFileText": "Loading Data Source...", "DE.Controllers.Main.mailMergeLoadFileTitle": "Loading Data Source", "DE.Controllers.Main.notcriticalErrorTitle": "Warning", "DE.Controllers.Main.openErrorText": "An error has occurred while opening the file.", "DE.Controllers.Main.openTextText": "Opening document...", "DE.Controllers.Main.openTitleText": "Opening Document", "DE.Controllers.Main.printTextText": "Printing document...", "DE.Controllers.Main.printTitleText": "Printing Document", "DE.Controllers.Main.reloadButtonText": "Reload Page", "DE.Controllers.Main.requestEditFailedMessageText": "Someone is editing this document right now. Please try again later.", "DE.Controllers.Main.requestEditFailedTitleText": "Access denied", "DE.Controllers.Main.saveErrorText": "An error has occurred while saving the file.", "DE.Controllers.Main.saveErrorTextDesktop": "This file cannot be saved or created.<br>Possible reasons are: <br>1. The file is read-only. <br>2. The file is being edited by other users. <br>3. The disk is full or corrupted.", "DE.Controllers.Main.saveTextText": "Saving document...", "DE.Controllers.Main.saveTitleText": "Saving Document", "DE.Controllers.Main.scriptLoadError": "The connection is too slow, some of the components could not be loaded. Please reload the page.", "DE.Controllers.Main.sendMergeText": "Sending Merge...", "DE.Controllers.Main.sendMergeTitle": "Sending Merge", "DE.Controllers.Main.splitDividerErrorText": "The number of rows must be a divisor of %1.", "DE.Controllers.Main.splitMaxColsErrorText": "The number of columns must be less than %1.", "DE.Controllers.Main.splitMaxRowsErrorText": "The number of rows must be less than %1.", "DE.Controllers.Main.textAnonymous": "Anonymous", "DE.Controllers.Main.textAnyone": "Anyone", "DE.Controllers.Main.textApplyAll": "Apply to all equations", "DE.Controllers.Main.textBuyNow": "Visit website", "DE.Controllers.Main.textChangesSaved": "All changes saved", "DE.Controllers.Main.textClose": "Close", "DE.Controllers.Main.textCloseTip": "Click to close the tip", "DE.Controllers.Main.textContactUs": "Contact sales", "DE.Controllers.Main.textContinue": "Continue", "DE.Controllers.Main.textConvertEquation": "This equation was created with an old version of the equation editor which is no longer supported. To edit it, convert the equation to the Office Math ML format.<br>Convert now?", "DE.Controllers.Main.textCustomLoader": "Please note that according to the terms of the license you are not entitled to change the loader.<br>Please contact our Sales Department to get a quote.", "DE.Controllers.Main.textDisconnect": "Connection is lost", "DE.Controllers.Main.textGuest": "Guest", "DE.Controllers.Main.textHasMacros": "The file contains automatic macros.<br>Do you want to run macros?", "DE.Controllers.Main.textLearnMore": "Learn More", "DE.Controllers.Main.textLoadingDocument": "Loading document", "DE.Controllers.Main.textLongName": "Enter a name that is less than 128 characters.", "DE.Controllers.Main.textNoLicenseTitle": "License limit reached", "DE.Controllers.Main.textPaidFeature": "Paid feature", "DE.Controllers.Main.textReconnect": "Connection is restored", "DE.Controllers.Main.textRemember": "Remember my choice for all files", "DE.Controllers.Main.textRememberMacros": "Remember my choice for all macros", "DE.Controllers.Main.textRenameError": "User name must not be empty.", "DE.Controllers.Main.textRenameLabel": "Enter a name to be used for collaboration", "DE.Controllers.Main.textRequestMacros": "A macro makes a request to URL. Do you want to allow the request to the %1?", "DE.Controllers.Main.textShape": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.textStrict": "Strict mode", "DE.Controllers.Main.textText": "Text", "DE.Controllers.Main.textTryQuickPrint": "You have selected Quick print: the entire document will be printed on the last selected or default printer.<br>Do you want to continue?", "DE.Controllers.Main.textTryUndoRedo": "The Undo/Redo functions are disabled for the Fast co-editing mode.<br>Click the 'Strict mode' button to switch to the Strict co-editing mode to edit the file without other users interference and send your changes only after you save them. You can switch between the co-editing modes using the editor Advanced settings.", "DE.Controllers.Main.textTryUndoRedoWarn": "The Undo/Redo functions are disabled for the Fast co-editing mode.", "DE.Controllers.Main.textUndo": "Undo", "DE.Controllers.Main.titleLicenseExp": "License expired", "DE.Controllers.Main.titleServerVersion": "Editor updated", "DE.Controllers.Main.titleUpdateVersion": "Version changed", "DE.Controllers.Main.txtAbove": "above", "DE.Controllers.Main.txtArt": "Your text here", "DE.Controllers.Main.txtBasicShapes": "Basic shapes", "DE.Controllers.Main.txtBelow": "below", "DE.Controllers.Main.txtBookmarkError": "Error! Bookmark not defined.", "DE.Controllers.Main.txtButtons": "Buttons", "DE.Controllers.Main.txtCallouts": "Callouts", "DE.Controllers.Main.txtCharts": "Charts", "DE.Controllers.Main.txtChoose": "Choose an item", "DE.Controllers.Main.txtClickToLoad": "Click to load image", "DE.Controllers.Main.txtCurrentDocument": "Current Document", "DE.Controllers.Main.txtDiagramTitle": "Chart Title", "DE.Controllers.Main.txtEditingMode": "Set editing mode...", "DE.Controllers.Main.txtEndOfFormula": "Unexpected End of Formula", "DE.Controllers.Main.txtEnterDate": "Enter a date", "DE.Controllers.Main.txtErrorLoadHistory": "History loading failed", "DE.Controllers.Main.txtEvenPage": "<PERSON>", "DE.Controllers.Main.txtFiguredArrows": "Figured arrows", "DE.Controllers.Main.txtFirstPage": "First Page", "DE.Controllers.Main.txtFooter": "Footer", "DE.Controllers.Main.txtFormulaNotInTable": "The Formula Not In Table", "DE.Controllers.Main.txtHeader": "Header", "DE.Controllers.Main.txtHyperlink": "Hyperlink", "DE.Controllers.Main.txtIndTooLarge": "Index Too Large", "DE.Controllers.Main.txtLines": "Lines", "DE.Controllers.Main.txtMainDocOnly": "Error! Main Document Only.", "DE.Controllers.Main.txtMath": "Math", "DE.Controllers.Main.txtMissArg": "Missing Argument", "DE.Controllers.Main.txtMissOperator": "Missing Operator", "DE.Controllers.Main.txtNeedSynchronize": "You have updates", "DE.Controllers.Main.txtNone": "None", "DE.Controllers.Main.txtNoTableOfContents": "There are no headings in the document. Apply a heading style to the text so that it appears in the table of contents.", "DE.Controllers.Main.txtNoTableOfFigures": "No table of figures entries found.", "DE.Controllers.Main.txtNoText": "Error! No text of specified style in document.", "DE.Controllers.Main.txtNotInTable": "Is Not In Table", "DE.Controllers.Main.txtNotValidBookmark": "Error! Not a valid bookmark self-reference.", "DE.Controllers.Main.txtOddPage": "<PERSON>", "DE.Controllers.Main.txtOnPage": "on page", "DE.Controllers.Main.txtRectangles": "Rectangles", "DE.Controllers.Main.txtSameAsPrev": "Same as Previous", "DE.Controllers.Main.txtSection": "-Section", "DE.Controllers.Main.txtSeries": "Series", "DE.Controllers.Main.txtShape_accentBorderCallout1": "Line Callout 1 (Border and Accent Bar)", "DE.Controllers.Main.txtShape_accentBorderCallout2": "Line Callout 2 (Border and Accent Bar)", "DE.Controllers.Main.txtShape_accentBorderCallout3": "Line Callout 3 (Border and Accent Bar)", "DE.Controllers.Main.txtShape_accentCallout1": "Line Callout 1 (Accent Bar)", "DE.Controllers.Main.txtShape_accentCallout2": "Line Callout 2 (Accent Bar)", "DE.Controllers.Main.txtShape_accentCallout3": "Line Callout 3 (Accent Bar)", "DE.Controllers.Main.txtShape_actionButtonBackPrevious": "Back or Previous <PERSON><PERSON>", "DE.Controllers.Main.txtShape_actionButtonBeginning": "Beginning Button", "DE.Controllers.Main.txtShape_actionButtonBlank": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_actionButtonDocument": "Document Button", "DE.Controllers.Main.txtShape_actionButtonEnd": "<PERSON>", "DE.Controllers.Main.txtShape_actionButtonForwardNext": "Forward or Next <PERSON><PERSON>", "DE.Controllers.Main.txtShape_actionButtonHelp": "Help Button", "DE.Controllers.Main.txtShape_actionButtonHome": "Home Button", "DE.Controllers.Main.txtShape_actionButtonInformation": "Information But<PERSON>", "DE.Controllers.Main.txtShape_actionButtonMovie": "Movie Button", "DE.Controllers.Main.txtShape_actionButtonReturn": "Return Button", "DE.Controllers.Main.txtShape_actionButtonSound": "Sound Button", "DE.Controllers.Main.txtShape_arc": "Arc", "DE.Controllers.Main.txtShape_bentArrow": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_bentConnector5": "Elbow Connector", "DE.Controllers.Main.txtShape_bentConnector5WithArrow": "Elbow Arrow Connector", "DE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Elbow Double-Arrow Connector", "DE.Controllers.Main.txtShape_bentUpArrow": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_bevel": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_blockArc": "Block Arc", "DE.Controllers.Main.txtShape_borderCallout1": "Line Callout 1", "DE.Controllers.Main.txtShape_borderCallout2": "Line Callout 2", "DE.Controllers.Main.txtShape_borderCallout3": "Line Callout 3", "DE.Controllers.Main.txtShape_bracePair": "Double Brace", "DE.Controllers.Main.txtShape_callout1": "Line Callout 1 (No Border)", "DE.Controllers.Main.txtShape_callout2": "Line Callout 2 (No Border)", "DE.Controllers.Main.txtShape_callout3": "Line Callout 3 (No Border)", "DE.Controllers.Main.txtShape_can": "Can", "DE.Controllers.Main.txtShape_chevron": "Chevron", "DE.Controllers.Main.txtShape_chord": "Chord", "DE.Controllers.Main.txtShape_circularArrow": "Circular Arrow", "DE.Controllers.Main.txtShape_cloud": "Cloud", "DE.Controllers.Main.txtShape_cloudCallout": "Cloud Callout", "DE.Controllers.Main.txtShape_corner": "Corner", "DE.Controllers.Main.txtShape_cube": "C<PERSON>", "DE.Controllers.Main.txtShape_curvedConnector3": "Curved Connector", "DE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Curved Arrow Connector", "DE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Curved Double-Arrow Connector", "DE.Controllers.Main.txtShape_curvedDownArrow": "Curved Down Arrow", "DE.Controllers.Main.txtShape_curvedLeftArrow": "Curved Left Arrow", "DE.Controllers.Main.txtShape_curvedRightArrow": "Curved Right Arrow", "DE.Controllers.Main.txtShape_curvedUpArrow": "Curved Up Arrow", "DE.Controllers.Main.txtShape_decagon": "Decagon", "DE.Controllers.Main.txtShape_diagStripe": "Diagonal Stripe", "DE.Controllers.Main.txtShape_diamond": "Diamond", "DE.Controllers.Main.txtShape_dodecagon": "Dodecagon", "DE.Controllers.Main.txtShape_donut": "Donut", "DE.Controllers.Main.txtShape_doubleWave": "Double Wave", "DE.Controllers.Main.txtShape_downArrow": "Down Arrow", "DE.Controllers.Main.txtShape_downArrowCallout": "Down Arrow Callout", "DE.Controllers.Main.txtShape_ellipse": "Ellipse", "DE.Controllers.Main.txtShape_ellipseRibbon": "Curved Down Ribbon", "DE.Controllers.Main.txtShape_ellipseRibbon2": "Curved Up Ribbon", "DE.Controllers.Main.txtShape_flowChartAlternateProcess": "Flowchart: Alternate Process", "DE.Controllers.Main.txtShape_flowChartCollate": "Flowchart: Collate", "DE.Controllers.Main.txtShape_flowChartConnector": "Flowchart: Connector", "DE.Controllers.Main.txtShape_flowChartDecision": "Flowchart: Decision", "DE.Controllers.Main.txtShape_flowChartDelay": "Flowchart: Delay", "DE.Controllers.Main.txtShape_flowChartDisplay": "Flowchart: Display", "DE.Controllers.Main.txtShape_flowChartDocument": "Flowchart: Document", "DE.Controllers.Main.txtShape_flowChartExtract": "Flowchart: Extract", "DE.Controllers.Main.txtShape_flowChartInputOutput": "Flowchart: Data", "DE.Controllers.Main.txtShape_flowChartInternalStorage": "Flowchart: Internal Storage", "DE.Controllers.Main.txtShape_flowChartMagneticDisk": "Flowchart: Magnetic Disk", "DE.Controllers.Main.txtShape_flowChartMagneticDrum": "Flowchart: Direct Access Storage", "DE.Controllers.Main.txtShape_flowChartMagneticTape": "Flowchart: Sequential Access Storage", "DE.Controllers.Main.txtShape_flowChartManualInput": "Flowchart: Manual Input", "DE.Controllers.Main.txtShape_flowChartManualOperation": "Flowchart: Manual Operation", "DE.Controllers.Main.txtShape_flowChartMerge": "Flowchart: <PERSON><PERSON>", "DE.Controllers.Main.txtShape_flowChartMultidocument": "Flowchart: Multidocument ", "DE.Controllers.Main.txtShape_flowChartOffpageConnector": "Flowchart: Off-page Connector", "DE.Controllers.Main.txtShape_flowChartOnlineStorage": "Flowchart: Stored Data", "DE.Controllers.Main.txtShape_flowChartOr": "Flowchart: Or", "DE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Flowchart: Predefined Process", "DE.Controllers.Main.txtShape_flowChartPreparation": "Flowchart: Preparation", "DE.Controllers.Main.txtShape_flowChartProcess": "Flowchart: Process", "DE.Controllers.Main.txtShape_flowChartPunchedCard": "Flowchart: Card", "DE.Controllers.Main.txtShape_flowChartPunchedTape": "Flowchart: Punched Ta<PERSON>", "DE.Controllers.Main.txtShape_flowChartSort": "Flowchart: Sort", "DE.Controllers.Main.txtShape_flowChartSummingJunction": "Flowchart: Summing Junction", "DE.Controllers.Main.txtShape_flowChartTerminator": "Flowchart: Terminator", "DE.Controllers.Main.txtShape_foldedCorner": "Folded Corner", "DE.Controllers.Main.txtShape_frame": "<PERSON>ame", "DE.Controllers.Main.txtShape_halfFrame": "Half Frame", "DE.Controllers.Main.txtShape_heart": "Heart", "DE.Controllers.Main.txtShape_heptagon": "Heptagon", "DE.Controllers.Main.txtShape_hexagon": "Hexagon", "DE.Controllers.Main.txtShape_homePlate": "Pentagon", "DE.Controllers.Main.txtShape_horizontalScroll": "<PERSON><PERSON> Scroll", "DE.Controllers.Main.txtShape_irregularSeal1": "Explosion 1", "DE.Controllers.Main.txtShape_irregularSeal2": "Explosion 2", "DE.Controllers.Main.txtShape_leftArrow": "Left Arrow", "DE.Controllers.Main.txtShape_leftArrowCallout": "Left <PERSON> Callout", "DE.Controllers.Main.txtShape_leftBrace": "Left Brace", "DE.Controllers.Main.txtShape_leftBracket": "Left Bracket", "DE.Controllers.Main.txtShape_leftRightArrow": "Left Right Arrow", "DE.Controllers.Main.txtShape_leftRightArrowCallout": "Left Right <PERSON> Callout", "DE.Controllers.Main.txtShape_leftRightUpArrow": "Left Right Up Arrow", "DE.Controllers.Main.txtShape_leftUpArrow": "Left Up Arrow", "DE.Controllers.Main.txtShape_lightningBolt": "Lightning Bolt", "DE.Controllers.Main.txtShape_line": "Line", "DE.Controllers.Main.txtShape_lineWithArrow": "Arrow", "DE.Controllers.Main.txtShape_lineWithTwoArrows": "Double Arrow", "DE.Controllers.Main.txtShape_mathDivide": "Division", "DE.Controllers.Main.txtShape_mathEqual": "Equal", "DE.Controllers.Main.txtShape_mathMinus": "Minus", "DE.Controllers.Main.txtShape_mathMultiply": "Multiply", "DE.Controllers.Main.txtShape_mathNotEqual": "Not Equal", "DE.Controllers.Main.txtShape_mathPlus": "Plus", "DE.Controllers.Main.txtShape_moon": "Moon", "DE.Controllers.Main.txtShape_noSmoking": "\"No\" Symbol", "DE.Controllers.Main.txtShape_notchedRightArrow": "Notched Right Arrow", "DE.Controllers.Main.txtShape_octagon": "Octagon", "DE.Controllers.Main.txtShape_parallelogram": "Parallelogram", "DE.Controllers.Main.txtShape_pentagon": "Pentagon", "DE.Controllers.Main.txtShape_pie": "Pie", "DE.Controllers.Main.txtShape_plaque": "Sign", "DE.Controllers.Main.txtShape_plus": "Plus", "DE.Controllers.Main.txtShape_polyline1": "Scribble", "DE.Controllers.Main.txtShape_polyline2": "Freeform", "DE.Controllers.Main.txtShape_quadArrow": "Quad Arrow", "DE.Controllers.Main.txtShape_quadArrowCallout": "Quad <PERSON> Callout", "DE.Controllers.Main.txtShape_rect": "Rectangle", "DE.Controllers.Main.txtShape_ribbon": "Down Ribbon", "DE.Controllers.Main.txtShape_ribbon2": "Up Ribbon", "DE.Controllers.Main.txtShape_rightArrow": "Right Arrow", "DE.Controllers.Main.txtShape_rightArrowCallout": "Right <PERSON> Callout", "DE.Controllers.Main.txtShape_rightBrace": "Right Brace", "DE.Controllers.Main.txtShape_rightBracket": "Right Bracket", "DE.Controllers.Main.txtShape_round1Rect": "Round Single Corner Rectangle", "DE.Controllers.Main.txtShape_round2DiagRect": "Round Diagonal Corner Rectangle", "DE.Controllers.Main.txtShape_round2SameRect": "Round Same Side Corner Rectangle", "DE.Controllers.Main.txtShape_roundRect": "Round Corner Rectangle", "DE.Controllers.Main.txtShape_rtTriangle": "Right Triangle", "DE.Controllers.Main.txtShape_smileyFace": "<PERSON><PERSON>", "DE.Controllers.Main.txtShape_snip1Rect": "Snip Single Corner Rectangle", "DE.Controllers.Main.txtShape_snip2DiagRect": "Snip Diagonal Corner Rectangle", "DE.Controllers.Main.txtShape_snip2SameRect": "Snip Same Side Corner Rectangle", "DE.Controllers.Main.txtShape_snipRoundRect": "Snip and Round Single Corner Rectangle", "DE.Controllers.Main.txtShape_spline": "Curve", "DE.Controllers.Main.txtShape_star10": "10-Point Star", "DE.Controllers.Main.txtShape_star12": "12-Point Star", "DE.Controllers.Main.txtShape_star16": "16-Point Star", "DE.Controllers.Main.txtShape_star24": "24-Point Star", "DE.Controllers.Main.txtShape_star32": "32-Point Star", "DE.Controllers.Main.txtShape_star4": "4-Point Star", "DE.Controllers.Main.txtShape_star5": "5-Point Star", "DE.Controllers.Main.txtShape_star6": "6-<PERSON> Star", "DE.Controllers.Main.txtShape_star7": "7-Point Star", "DE.Controllers.Main.txtShape_star8": "8-Point Star", "DE.Controllers.Main.txtShape_stripedRightArrow": "Striped Right Arrow", "DE.Controllers.Main.txtShape_sun": "Sun", "DE.Controllers.Main.txtShape_teardrop": "Teardrop", "DE.Controllers.Main.txtShape_textRect": "Text Box", "DE.Controllers.Main.txtShape_trapezoid": "Trapezoid", "DE.Controllers.Main.txtShape_triangle": "Triangle", "DE.Controllers.Main.txtShape_upArrow": "Up Arrow", "DE.Controllers.Main.txtShape_upArrowCallout": "Up Arrow Callout", "DE.Controllers.Main.txtShape_upDownArrow": "Up Down Arrow", "DE.Controllers.Main.txtShape_uturnArrow": "U-Turn Arrow", "DE.Controllers.Main.txtShape_verticalScroll": "Vertical Scroll", "DE.Controllers.Main.txtShape_wave": "Wave", "DE.Controllers.Main.txtShape_wedgeEllipseCallout": "Oval Callout", "DE.Controllers.Main.txtShape_wedgeRectCallout": "Rectangular Callout", "DE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Rounded Rectangular Callout", "DE.Controllers.Main.txtStarsRibbons": "Stars & Ribbons", "DE.Controllers.Main.txtStyle_Caption": "Caption", "DE.Controllers.Main.txtStyle_endnote_text": "Endnote text", "DE.Controllers.Main.txtStyle_footnote_text": "Footnote text", "DE.Controllers.Main.txtStyle_Heading_1": "Heading 1", "DE.Controllers.Main.txtStyle_Heading_2": "Heading 2", "DE.Controllers.Main.txtStyle_Heading_3": "Heading 3", "DE.Controllers.Main.txtStyle_Heading_4": "Heading 4", "DE.Controllers.Main.txtStyle_Heading_5": "Heading 5", "DE.Controllers.Main.txtStyle_Heading_6": "Heading 6", "DE.Controllers.Main.txtStyle_Heading_7": "Heading 7", "DE.Controllers.Main.txtStyle_Heading_8": "Heading 8", "DE.Controllers.Main.txtStyle_Heading_9": "Heading 9", "DE.Controllers.Main.txtStyle_Intense_Quote": "Intense Quote", "DE.Controllers.Main.txtStyle_List_Paragraph": "List Paragraph", "DE.Controllers.Main.txtStyle_No_Spacing": "No Spacing", "DE.Controllers.Main.txtStyle_Normal": "Normal", "DE.Controllers.Main.txtStyle_Quote": "Quote", "DE.Controllers.Main.txtStyle_Subtitle": "Subtitle", "DE.Controllers.Main.txtStyle_Title": "Title", "DE.Controllers.Main.txtSyntaxError": "Syntax Error", "DE.Controllers.Main.txtTableInd": "Table Index Cannot be Zero", "DE.Controllers.Main.txtTableOfContents": "Table of Contents", "DE.Controllers.Main.txtTableOfFigures": "Table of figures", "DE.Controllers.Main.txtTOCHeading": "TOC Heading", "DE.Controllers.Main.txtTooLarge": "Number Too Large To Format", "DE.Controllers.Main.txtTypeEquation": "Type an equation here.", "DE.Controllers.Main.txtUndefBookmark": "Undefined Bookmark", "DE.Controllers.Main.txtXAxis": "X Axis", "DE.Controllers.Main.txtYAxis": "Y Axis", "DE.Controllers.Main.txtZeroDivide": "Zero Divide", "DE.Controllers.Main.unknownErrorText": "Unknown error.", "DE.Controllers.Main.unsupportedBrowserErrorText": "Your browser is not supported.", "DE.Controllers.Main.uploadDocExtMessage": "Unknown document format.", "DE.Controllers.Main.uploadDocFileCountMessage": "No documents uploaded.", "DE.Controllers.Main.uploadDocSizeMessage": "Maximum document size limit exceeded.", "DE.Controllers.Main.uploadImageExtMessage": "Unknown image format.", "DE.Controllers.Main.uploadImageFileCountMessage": "No images uploaded.", "DE.Controllers.Main.uploadImageSizeMessage": "The image is too big. The maximum size is 25 MB.", "DE.Controllers.Main.uploadImageTextText": "Uploading image...", "DE.Controllers.Main.uploadImageTitleText": "Uploading Image", "DE.Controllers.Main.waitText": "Please, wait...", "DE.Controllers.Main.warnBrowserIE9": "The application has low capabilities on IE9. Use IE10 or higher", "DE.Controllers.Main.warnBrowserZoom": "Your browser current zoom setting is not fully supported. Please reset to the default zoom by pressing Ctrl+0.", "DE.Controllers.Main.warnLicenseExceeded": "You've reached the limit for simultaneous connections to %1 editors. This document will be opened for viewing only.<br>Contact your administrator to learn more.", "DE.Controllers.Main.warnLicenseExp": "Your license has expired.<br>Please update your license and refresh the page.", "DE.Controllers.Main.warnLicenseLimitedNoAccess": "License expired.<br>You have no access to document editing functionality.<br>Please contact your administrator.", "DE.Controllers.Main.warnLicenseLimitedRenewed": "License needs to be renewed.<br>You have a limited access to document editing functionality.<br>Please contact your administrator to get full access", "DE.Controllers.Main.warnLicenseUsersExceeded": "You've reached the user limit for %1 editors. Contact your administrator to learn more.", "DE.Controllers.Main.warnNoLicense": "You've reached the limit for simultaneous connections to %1 editors. This document will be opened for viewing only.<br>Contact %1 sales team for personal upgrade terms.", "DE.Controllers.Main.warnNoLicenseUsers": "You've reached the user limit for %1 editors. Contact %1 sales team for personal upgrade terms.", "DE.Controllers.Main.warnProcessRightsChange": "You have been denied the right to edit the file.", "DE.Controllers.Navigation.txtBeginning": "Beginning of document", "DE.Controllers.Navigation.txtGotoBeginning": "Go to the beginning of the document", "DE.Controllers.Print.textMarginsLast": "Last Custom", "DE.Controllers.Print.txtCustom": "Custom", "DE.Controllers.Print.txtPrintRangeInvalid": "Invalid print range", "DE.Controllers.Print.txtPrintRangeSingleRange": "Enter either a single page number or a single page range (for example, 5-12). Or you can Print to PDF.", "DE.Controllers.Search.notcriticalErrorTitle": "Warning", "DE.Controllers.Search.textNoTextFound": "The data you have been searching for could not be found. Please adjust your search options.", "DE.Controllers.Search.textReplaceSkipped": "The replacement has been made. {0} occurrences were skipped.", "DE.Controllers.Search.textReplaceSuccess": "Search has been done. {0} occurrences have been replaced", "DE.Controllers.Search.warnReplaceString": "{0} is not a valid special character for the Replace With box.", "DE.Controllers.Statusbar.textDisconnect": "<b>Connection is lost</b><br>Trying to connect. Please check connection settings.", "DE.Controllers.Statusbar.textHasChanges": "New changes have been tracked", "DE.Controllers.Statusbar.textSetTrackChanges": "You are in Track Changes mode", "DE.Controllers.Statusbar.textTrackChanges": "The document is opened with the Track Changes mode enabled", "DE.Controllers.Statusbar.tipReview": "Track changes", "DE.Controllers.Statusbar.zoomText": "Zoom {0}%", "DE.Controllers.Toolbar.confirmAddFontName": "The font you are going to save is not available on the current device.<br>The text style will be displayed using one of the system fonts, the saved font will be used when it is available.<br>Do you want to continue?", "DE.Controllers.Toolbar.dataUrl": "Paste a data URL", "DE.Controllers.Toolbar.notcriticalErrorTitle": "Warning", "DE.Controllers.Toolbar.textAccent": "Accents", "DE.Controllers.Toolbar.textBracket": "Brackets", "DE.Controllers.Toolbar.textEmptyImgUrl": "You need to specify image URL.", "DE.Controllers.Toolbar.textEmptyMMergeUrl": "You need to specify URL.", "DE.Controllers.Toolbar.textFontSizeErr": "The entered value is incorrect.<br>Please enter a numeric value between 1 and 300", "DE.Controllers.Toolbar.textFraction": "Fractions", "DE.Controllers.Toolbar.textFunction": "Functions", "DE.Controllers.Toolbar.textGroup": "Group", "DE.Controllers.Toolbar.textInsert": "Insert", "DE.Controllers.Toolbar.textIntegral": "Integrals", "DE.Controllers.Toolbar.textLargeOperator": "Large Operators", "DE.Controllers.Toolbar.textLimitAndLog": "Limits and Logarithms", "DE.Controllers.Toolbar.textMatrix": "Matrices", "DE.Controllers.Toolbar.textOperator": "Operators", "DE.Controllers.Toolbar.textRadical": "Radicals", "DE.Controllers.Toolbar.textRecentlyUsed": "Recently Used", "DE.Controllers.Toolbar.textScript": "<PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.textSymbols": "Symbols", "DE.Controllers.Toolbar.textTabForms": "Forms", "DE.Controllers.Toolbar.textWarning": "Warning", "DE.Controllers.Toolbar.txtAccent_Accent": "Acute", "DE.Controllers.Toolbar.txtAccent_ArrowD": "Right-left arrow above", "DE.Controllers.Toolbar.txtAccent_ArrowL": "Leftwards arrow above", "DE.Controllers.Toolbar.txtAccent_ArrowR": "Rightwards arrow above", "DE.Controllers.Toolbar.txtAccent_Bar": "Bar", "DE.Controllers.Toolbar.txtAccent_BarBot": "Underbar", "DE.Controllers.Toolbar.txtAccent_BarTop": "Overbar", "DE.Controllers.Toolbar.txtAccent_BorderBox": "Boxed formula (with placeholder)", "DE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Boxed formula (example)", "DE.Controllers.Toolbar.txtAccent_Check": "Check", "DE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Underbrace", "DE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Overbrace", "DE.Controllers.Toolbar.txtAccent_Custom_1": "Vector A", "DE.Controllers.Toolbar.txtAccent_Custom_2": "ABC with overbar", "DE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y with overbar", "DE.Controllers.Toolbar.txtAccent_DDDot": "Triple dot", "DE.Controllers.Toolbar.txtAccent_DDot": "Double dot", "DE.Controllers.Toolbar.txtAccent_Dot": "Dot", "DE.Controllers.Toolbar.txtAccent_DoubleBar": "Double overbar", "DE.Controllers.Toolbar.txtAccent_Grave": "Grave", "DE.Controllers.Toolbar.txtAccent_GroupBot": "Grouping character below", "DE.Controllers.Toolbar.txtAccent_GroupTop": "Grouping character above", "DE.Controllers.Toolbar.txtAccent_HarpoonL": "Leftwards harpoon above", "DE.Controllers.Toolbar.txtAccent_HarpoonR": "Rightwards harpoon above", "DE.Controllers.Toolbar.txtAccent_Hat": "Hat", "DE.Controllers.Toolbar.txtAccent_Smile": "Breve", "DE.Controllers.Toolbar.txtAccent_Tilde": "<PERSON><PERSON>", "DE.Controllers.Toolbar.txtBracket_Angle": "Angle brackets", "DE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Angle brackets with separator", "DE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Angle brackets with two separators", "DE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Right angle bracket", "DE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Left angle bracket", "DE.Controllers.Toolbar.txtBracket_Curve": "Curly brackets", "DE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Curly brackets with separator", "DE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Right curly bracket", "DE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Left curly bracket", "DE.Controllers.Toolbar.txtBracket_Custom_1": "Cases (two conditions)", "DE.Controllers.Toolbar.txtBracket_Custom_2": "Cases (three conditions)", "DE.Controllers.Toolbar.txtBracket_Custom_3": "Stack object", "DE.Controllers.Toolbar.txtBracket_Custom_4": "Stack object in parentheses", "DE.Controllers.Toolbar.txtBracket_Custom_5": "Cases example", "DE.Controllers.Toolbar.txtBracket_Custom_6": "Binomial coefficient", "DE.Controllers.Toolbar.txtBracket_Custom_7": "Binomial coefficient in angle brackets", "DE.Controllers.Toolbar.txtBracket_Line": "Vertical bars", "DE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Right vertical bar", "DE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Left vertical bar", "DE.Controllers.Toolbar.txtBracket_LineDouble": "Double vertical bars", "DE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Right double vertical bar", "DE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Left double vertical bar", "DE.Controllers.Toolbar.txtBracket_LowLim": "Floor", "DE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Right floor", "DE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Left floor", "DE.Controllers.Toolbar.txtBracket_Round": "Parentheses", "DE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Parentheses with separator", "DE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Right parenthesis", "DE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Left parenthesis", "DE.Controllers.Toolbar.txtBracket_Square": "Square brackets", "DE.Controllers.Toolbar.txtBracket_Square_CloseClose": "Placeholder between two right square brackets", "DE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "Inverted square brackets", "DE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Right square bracket", "DE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Left square bracket", "DE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "Placeholder between two left square brackets", "DE.Controllers.Toolbar.txtBracket_SquareDouble": "Double square brackets", "DE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Right double square bracket", "DE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Left double square bracket", "DE.Controllers.Toolbar.txtBracket_UppLim": "Ceiling", "DE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Right ceiling", "DE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Left ceiling", "DE.Controllers.Toolbar.txtFractionDiagonal": "Skewed fraction", "DE.Controllers.Toolbar.txtFractionDifferential_1": "dx over dy", "DE.Controllers.Toolbar.txtFractionDifferential_2": "cap delta y over cap delta x", "DE.Controllers.Toolbar.txtFractionDifferential_3": "partial y over partial x", "DE.Controllers.Toolbar.txtFractionDifferential_4": "delta y over delta x", "DE.Controllers.Toolbar.txtFractionHorizontal": "Linear fraction", "DE.Controllers.Toolbar.txtFractionPi_2": "Pi over 2", "DE.Controllers.Toolbar.txtFractionSmall": "Small fraction", "DE.Controllers.Toolbar.txtFractionVertical": "Stacked fraction", "DE.Controllers.Toolbar.txtFunction_1_Cos": "Inverse cosine function", "DE.Controllers.Toolbar.txtFunction_1_Cosh": "Hyperbolic inverse cosine function", "DE.Controllers.Toolbar.txtFunction_1_Cot": "Inverse cotangent function", "DE.Controllers.Toolbar.txtFunction_1_Coth": "Hyperbolic inverse cotangent function", "DE.Controllers.Toolbar.txtFunction_1_Csc": "Inverse cosecant function", "DE.Controllers.Toolbar.txtFunction_1_Csch": "Hyperbolic inverse cosecant function", "DE.Controllers.Toolbar.txtFunction_1_Sec": "Inverse secant function", "DE.Controllers.Toolbar.txtFunction_1_Sech": "Hyperbolic inverse secant function", "DE.Controllers.Toolbar.txtFunction_1_Sin": "Inverse sine function", "DE.Controllers.Toolbar.txtFunction_1_Sinh": "Hyperbolic inverse sine function", "DE.Controllers.Toolbar.txtFunction_1_Tan": "Inverse tangent function", "DE.Controllers.Toolbar.txtFunction_1_Tanh": "Hyperbolic inverse tangent function", "DE.Controllers.Toolbar.txtFunction_Cos": "Cosine function", "DE.Controllers.Toolbar.txtFunction_Cosh": "Hyperbolic cosine function", "DE.Controllers.Toolbar.txtFunction_Cot": "Cotangent function", "DE.Controllers.Toolbar.txtFunction_Coth": "Hyperbolic cotangent function", "DE.Controllers.Toolbar.txtFunction_Csc": "Cosecant function", "DE.Controllers.Toolbar.txtFunction_Csch": "Hyperbolic cosecant function", "DE.Controllers.Toolbar.txtFunction_Custom_1": "Sine theta", "DE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "DE.Controllers.Toolbar.txtFunction_Custom_3": "Tangent formula", "DE.Controllers.Toolbar.txtFunction_Sec": "Secant function", "DE.Controllers.Toolbar.txtFunction_Sech": "Hyperbolic secant function", "DE.Controllers.Toolbar.txtFunction_Sin": "Sine function", "DE.Controllers.Toolbar.txtFunction_Sinh": "Hyperbolic sine function", "DE.Controllers.Toolbar.txtFunction_Tan": "Tangent function", "DE.Controllers.Toolbar.txtFunction_Tanh": "Hyperbolic tangent function", "DE.Controllers.Toolbar.txtIntegral": "Integral", "DE.Controllers.Toolbar.txtIntegral_dtheta": "Differential theta", "DE.Controllers.Toolbar.txtIntegral_dx": "Differential x", "DE.Controllers.Toolbar.txtIntegral_dy": "Differential y", "DE.Controllers.Toolbar.txtIntegralCenterSubSup": "Integral with stacked limits", "DE.Controllers.Toolbar.txtIntegralDouble": "Double integral", "DE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Double integral with stacked limits", "DE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Double integral with limits", "DE.Controllers.Toolbar.txtIntegralOriented": "Contour integral", "DE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Contour integral with stacked limits", "DE.Controllers.Toolbar.txtIntegralOrientedDouble": "Surface integral", "DE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Surface integral with stacked limits", "DE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Surface integral with limits", "DE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Contour integral with limits", "DE.Controllers.Toolbar.txtIntegralOrientedTriple": "Volume integral", "DE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Volume integral with stacked limits", "DE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Volume integral with limits", "DE.Controllers.Toolbar.txtIntegralSubSup": "Integral with limits", "DE.Controllers.Toolbar.txtIntegralTriple": "Triple integral", "DE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Triple integral with stacked limits", "DE.Controllers.Toolbar.txtIntegralTripleSubSup": "Triple integral with limits", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Logical And", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Logical And with lower limit", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Logical And with limits", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Logical And with subscript lower limit", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Logical And with subscript/superscript limits", "DE.Controllers.Toolbar.txtLargeOperator_CoProd": "Co-product", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Co-product with lower limit", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Co-product with limits", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Co-product with subscript lower limit", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Co-product with subscript/superscript limits", "DE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Summation over k of n choose k", "DE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Summation from i equal zero to n", "DE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Summation example using two indices", "DE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Product example", "DE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Union example", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction": "Logical Or", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "Logical Or with lower limit", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "Logical Or with limits", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "Logical Or with subscript lower limit", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "Logical Or with subscript/superscript limits", "DE.Controllers.Toolbar.txtLargeOperator_Intersection": "Intersection", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Intersection with lower limit", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Intersection with limits", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Intersection with subscript lower limit", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Intersection with subscript/superscript limits", "DE.Controllers.Toolbar.txtLargeOperator_Prod": "Product", "DE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Product with lower limit", "DE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Product with limits", "DE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Product with subscript lower limit", "DE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Product with subscript/superscript limits", "DE.Controllers.Toolbar.txtLargeOperator_Sum": "Summation", "DE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Summation with lower limit", "DE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Summation with limits", "DE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Summation with subscript lower limit", "DE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Summation with subscript/superscript limits", "DE.Controllers.Toolbar.txtLargeOperator_Union": "Union", "DE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Union with lower limit", "DE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Union with limits", "DE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Union with subscript lower limit", "DE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Union with subscript/superscript limits", "DE.Controllers.Toolbar.txtLimitLog_Custom_1": "Limit example", "DE.Controllers.Toolbar.txtLimitLog_Custom_2": "Maximum example", "DE.Controllers.Toolbar.txtLimitLog_Lim": "Limit", "DE.Controllers.Toolbar.txtLimitLog_Ln": "Natural logarithm", "DE.Controllers.Toolbar.txtLimitLog_Log": "Logarithm", "DE.Controllers.Toolbar.txtLimitLog_LogBase": "Logarithm", "DE.Controllers.Toolbar.txtLimitLog_Max": "Maximum", "DE.Controllers.Toolbar.txtLimitLog_Min": "Minimum", "DE.Controllers.Toolbar.txtMarginsH": "Top and bottom margins are too high for a given page height", "DE.Controllers.Toolbar.txtMarginsW": "Left and right margins are too wide for a given page width", "DE.Controllers.Toolbar.txtMatrix_1_2": "1x2 empty matrix", "DE.Controllers.Toolbar.txtMatrix_1_3": "1x3 empty matrix", "DE.Controllers.Toolbar.txtMatrix_2_1": "2x1 empty matrix", "DE.Controllers.Toolbar.txtMatrix_2_2": "2x2 empty matrix", "DE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "Empty 2 by 2 matrix in double vertical bars", "DE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Empty 2 by 2 determinant", "DE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "Empty 2 by 2 matrix in parentheses", "DE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "Empty 2 by 2 matrix in brackets", "DE.Controllers.Toolbar.txtMatrix_2_3": "2x3 empty matrix", "DE.Controllers.Toolbar.txtMatrix_3_1": "3x1 empty matrix", "DE.Controllers.Toolbar.txtMatrix_3_2": "3x2 empty matrix", "DE.Controllers.Toolbar.txtMatrix_3_3": "3x3 empty matrix", "DE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Baseline dots", "DE.Controllers.Toolbar.txtMatrix_Dots_Center": "Midline dots", "DE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Diagonal dots", "DE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "Vertical dots", "DE.Controllers.Toolbar.txtMatrix_Flat_Round": "Sparse matrix in parentheses", "DE.Controllers.Toolbar.txtMatrix_Flat_Square": "Sparse matrix in brackets", "DE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2 identity matrix with zeros", "DE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "2x2 identity matrix with blank off-diagonal cells", "DE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3 identity matrix with zeros", "DE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 identity matrix with blank off-diagonal cells", "DE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Right-left arrow below", "DE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Right-left arrow above", "DE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Leftwards arrow below", "DE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Leftwards arrow above", "DE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Rightwards arrow below", "DE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Rightwards arrow above", "DE.Controllers.Toolbar.txtOperator_ColonEquals": "Colon equal", "DE.Controllers.Toolbar.txtOperator_Custom_1": "Yields", "DE.Controllers.Toolbar.txtOperator_Custom_2": "Delta yields", "DE.Controllers.Toolbar.txtOperator_Definition": "Equal to by definition", "DE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta equal to", "DE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Right-left double arrow below", "DE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Right-left double arrow above", "DE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Leftwards arrow below", "DE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Leftwards arrow above", "DE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Rightwards arrow below", "DE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Rightwards arrow above", "DE.Controllers.Toolbar.txtOperator_EqualsEquals": "Equal equal", "DE.Controllers.Toolbar.txtOperator_MinusEquals": "Minus equal", "DE.Controllers.Toolbar.txtOperator_PlusEquals": "Plus equal", "DE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "Measured by", "DE.Controllers.Toolbar.txtRadicalCustom_1": "Right hand side of quadratic formula", "DE.Controllers.Toolbar.txtRadicalCustom_2": "Square root of a squared plus b squared", "DE.Controllers.Toolbar.txtRadicalRoot_2": "Square root with degree", "DE.Controllers.Toolbar.txtRadicalRoot_3": "Cubic root", "DE.Controllers.Toolbar.txtRadicalRoot_n": "Radical with degree", "DE.Controllers.Toolbar.txtRadicalSqrt": "Square root", "DE.Controllers.Toolbar.txtScriptCustom_1": "x subscript y squared", "DE.Controllers.Toolbar.txtScriptCustom_2": "e to the minus i omega t", "DE.Controllers.Toolbar.txtScriptCustom_3": "x squared", "DE.Controllers.Toolbar.txtScriptCustom_4": "Y left superscript n left subscript one", "DE.Controllers.Toolbar.txtScriptSub": "Subscript", "DE.Controllers.Toolbar.txtScriptSubSup": "Subscript-superscript", "DE.Controllers.Toolbar.txtScriptSubSupLeft": "Left subscript-superscript", "DE.Controllers.Toolbar.txtScriptSup": "Superscript", "DE.Controllers.Toolbar.txtSymbol_about": "Approximately", "DE.Controllers.Toolbar.txtSymbol_additional": "Complement", "DE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "DE.Controllers.Toolbar.txtSymbol_alpha": "Alpha", "DE.Controllers.Toolbar.txtSymbol_approx": "Almost equal to", "DE.Controllers.Toolbar.txtSymbol_ast": "Asterisk operator", "DE.Controllers.Toolbar.txtSymbol_beta": "Beta", "DE.Controllers.Toolbar.txtSymbol_beth": "Bet", "DE.Controllers.Toolbar.txtSymbol_bullet": "Bullet operator", "DE.Controllers.Toolbar.txtSymbol_cap": "Intersection", "DE.Controllers.Toolbar.txtSymbol_cbrt": "Cube root", "DE.Controllers.Toolbar.txtSymbol_cdots": "Midline horizontal ellipsis", "DE.Controllers.Toolbar.txtSymbol_celsius": "Degrees Celsius", "DE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "DE.Controllers.Toolbar.txtSymbol_cong": "Approximately equal to", "DE.Controllers.Toolbar.txtSymbol_cup": "Union", "DE.Controllers.Toolbar.txtSymbol_ddots": "Down right diagonal ellipsis", "DE.Controllers.Toolbar.txtSymbol_degree": "Degrees", "DE.Controllers.Toolbar.txtSymbol_delta": "Delta", "DE.Controllers.Toolbar.txtSymbol_div": "Division sign", "DE.Controllers.Toolbar.txtSymbol_downarrow": "Down arrow", "DE.Controllers.Toolbar.txtSymbol_emptyset": "Empty set", "DE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "DE.Controllers.Toolbar.txtSymbol_equals": "Equal", "DE.Controllers.Toolbar.txtSymbol_equiv": "Identical to", "DE.Controllers.Toolbar.txtSymbol_eta": "Eta", "DE.Controllers.Toolbar.txtSymbol_exists": "There exist", "DE.Controllers.Toolbar.txtSymbol_factorial": "Factorial", "DE.Controllers.Toolbar.txtSymbol_fahrenheit": "Degrees Fahrenheit", "DE.Controllers.Toolbar.txtSymbol_forall": "For all", "DE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "DE.Controllers.Toolbar.txtSymbol_geq": "Greater than or equal to", "DE.Controllers.Toolbar.txtSymbol_gg": "Much greater than", "DE.Controllers.Toolbar.txtSymbol_greater": "Greater than", "DE.Controllers.Toolbar.txtSymbol_in": "Element of", "DE.Controllers.Toolbar.txtSymbol_inc": "Increment", "DE.Controllers.Toolbar.txtSymbol_infinity": "Infinity", "DE.Controllers.Toolbar.txtSymbol_iota": "Iota", "DE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "DE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "DE.Controllers.Toolbar.txtSymbol_leftarrow": "Left arrow", "DE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Left-right arrow", "DE.Controllers.Toolbar.txtSymbol_leq": "Less than or equal to", "DE.Controllers.Toolbar.txtSymbol_less": "Less than", "DE.Controllers.Toolbar.txtSymbol_ll": "Much less than", "DE.Controllers.Toolbar.txtSymbol_minus": "Minus", "DE.Controllers.Toolbar.txtSymbol_mp": "Minus plus", "DE.Controllers.Toolbar.txtSymbol_mu": "Mu", "DE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "DE.Controllers.Toolbar.txtSymbol_neq": "Not equal to", "DE.Controllers.Toolbar.txtSymbol_ni": "Contains as member", "DE.Controllers.Toolbar.txtSymbol_not": "Not sign", "DE.Controllers.Toolbar.txtSymbol_notexists": "There does not exist", "DE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "DE.Controllers.Toolbar.txtSymbol_o": "Omicron", "DE.Controllers.Toolbar.txtSymbol_omega": "Omega", "DE.Controllers.Toolbar.txtSymbol_partial": "Partial differential", "DE.Controllers.Toolbar.txtSymbol_percent": "Percentage", "DE.Controllers.Toolbar.txtSymbol_phi": "Phi", "DE.Controllers.Toolbar.txtSymbol_pi": "Pi", "DE.Controllers.Toolbar.txtSymbol_plus": "Plus", "DE.Controllers.Toolbar.txtSymbol_pm": "Plus minus", "DE.Controllers.Toolbar.txtSymbol_propto": "Proportional to", "DE.Controllers.Toolbar.txtSymbol_psi": "Psi", "DE.Controllers.Toolbar.txtSymbol_qdrt": "Fourth root", "DE.Controllers.Toolbar.txtSymbol_qed": "End of proof", "DE.Controllers.Toolbar.txtSymbol_rddots": "Up right diagonal ellipsis", "DE.Controllers.Toolbar.txtSymbol_rho": "Rho", "DE.Controllers.Toolbar.txtSymbol_rightarrow": "Right arrow", "DE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "DE.Controllers.Toolbar.txtSymbol_sqrt": "Radical sign", "DE.Controllers.Toolbar.txtSymbol_tau": "Tau", "DE.Controllers.Toolbar.txtSymbol_therefore": "Therefore", "DE.Controllers.Toolbar.txtSymbol_theta": "Theta", "DE.Controllers.Toolbar.txtSymbol_times": "Multiplication sign", "DE.Controllers.Toolbar.txtSymbol_uparrow": "Up arrow", "DE.Controllers.Toolbar.txtSymbol_upsilon": "Upsilon", "DE.Controllers.Toolbar.txtSymbol_varepsilon": "Epsilon variant", "DE.Controllers.Toolbar.txtSymbol_varphi": "Phi variant", "DE.Controllers.Toolbar.txtSymbol_varpi": "Pi variant", "DE.Controllers.Toolbar.txtSymbol_varrho": "Rho variant", "DE.Controllers.Toolbar.txtSymbol_varsigma": "Sigma variant", "DE.Controllers.Toolbar.txtSymbol_vartheta": "Theta variant", "DE.Controllers.Toolbar.txtSymbol_vdots": "Vertical ellipsis", "DE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "DE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "DE.Controllers.Viewport.textFitPage": "Fit to <PERSON>", "DE.Controllers.Viewport.textFitWidth": "Fit to Width", "DE.Controllers.Viewport.txtDarkMode": "Dark mode", "DE.Views.AddNewCaptionLabelDialog.textLabel": "Label:", "DE.Views.AddNewCaptionLabelDialog.textLabelError": "Label must not be empty.", "DE.Views.BookmarksDialog.textAdd": "Add", "DE.Views.BookmarksDialog.textBookmarkName": "Bookmark name", "DE.Views.BookmarksDialog.textClose": "Close", "DE.Views.BookmarksDialog.textCopy": "Copy", "DE.Views.BookmarksDialog.textDelete": "Delete", "DE.Views.BookmarksDialog.textGetLink": "Get link", "DE.Views.BookmarksDialog.textGoto": "Go to", "DE.Views.BookmarksDialog.textHidden": "Hidden bookmarks", "DE.Views.BookmarksDialog.textLocation": "Location", "DE.Views.BookmarksDialog.textName": "Name", "DE.Views.BookmarksDialog.textSort": "Sort by", "DE.Views.BookmarksDialog.textTitle": "Bookmarks", "DE.Views.BookmarksDialog.txtInvalidName": "Bookmark name can only contain letters, digits and underscores, and should begin with the letter", "DE.Views.CaptionDialog.textAdd": "Add label", "DE.Views.CaptionDialog.textAfter": "After", "DE.Views.CaptionDialog.textBefore": "Before", "DE.Views.CaptionDialog.textCaption": "Caption", "DE.Views.CaptionDialog.textChapter": "Chapter starts with style", "DE.Views.CaptionDialog.textChapterInc": "Include chapter number", "DE.Views.CaptionDialog.textColon": "colon", "DE.Views.CaptionDialog.textDash": "dash", "DE.Views.CaptionDialog.textDelete": "Delete label", "DE.Views.CaptionDialog.textEquation": "Equation", "DE.Views.CaptionDialog.textExamples": "Examples: Table 2-A, Image 1.IV", "DE.Views.CaptionDialog.textExclude": "Exclude label from caption", "DE.Views.CaptionDialog.textFigure": "Figure", "DE.Views.CaptionDialog.textHyphen": "hyphen", "DE.Views.CaptionDialog.textInsert": "Insert", "DE.Views.CaptionDialog.textLabel": "Label", "DE.Views.CaptionDialog.textLongDash": "long dash", "DE.Views.CaptionDialog.textNumbering": "Numbering", "DE.Views.CaptionDialog.textPeriod": "period", "DE.Views.CaptionDialog.textSeparator": "Use separator", "DE.Views.CaptionDialog.textTable": "Table", "DE.Views.CaptionDialog.textTitle": "Insert caption", "DE.Views.CellsAddDialog.textCol": "Columns", "DE.Views.CellsAddDialog.textDown": "Below the cursor", "DE.Views.CellsAddDialog.textLeft": "To the left", "DE.Views.CellsAddDialog.textRight": "To the right", "DE.Views.CellsAddDialog.textRow": "Rows", "DE.Views.CellsAddDialog.textTitle": "Insert several", "DE.Views.CellsAddDialog.textUp": "Above the cursor", "DE.Views.ChartSettings.text3dDepth": "Depth (% of base)", "DE.Views.ChartSettings.text3dHeight": "Height (% of base)", "DE.Views.ChartSettings.text3dRotation": "3D Rotation", "DE.Views.ChartSettings.textAdvanced": "Show advanced settings", "DE.Views.ChartSettings.textAutoscale": "Autoscale", "DE.Views.ChartSettings.textChartType": "Change Chart Type", "DE.Views.ChartSettings.textDefault": "Default Rotation", "DE.Views.ChartSettings.textDown": "Down", "DE.Views.ChartSettings.textEditData": "Edit Data", "DE.Views.ChartSettings.textHeight": "Height", "DE.Views.ChartSettings.textLeft": "Left", "DE.Views.ChartSettings.textNarrow": "Narrow field of view", "DE.Views.ChartSettings.textOriginalSize": "Actual Size", "DE.Views.ChartSettings.textPerspective": "Perspective", "DE.Views.ChartSettings.textRight": "Right", "DE.Views.ChartSettings.textRightAngle": "Right Angle Axes", "DE.Views.ChartSettings.textSize": "Size", "DE.Views.ChartSettings.textStyle": "Style", "DE.Views.ChartSettings.textUndock": "Undock from panel", "DE.Views.ChartSettings.textUp": "Up", "DE.Views.ChartSettings.textWiden": "Widen field of view", "DE.Views.ChartSettings.textWidth": "<PERSON><PERSON><PERSON>", "DE.Views.ChartSettings.textWrap": "Wrapping Style", "DE.Views.ChartSettings.textX": "X rotation", "DE.Views.ChartSettings.textY": "Y rotation", "DE.Views.ChartSettings.txtBehind": "Behind Text", "DE.Views.ChartSettings.txtInFront": "In Front of Text", "DE.Views.ChartSettings.txtInline": "In Line with Text", "DE.Views.ChartSettings.txtSquare": "Square", "DE.Views.ChartSettings.txtThrough": "Through", "DE.Views.ChartSettings.txtTight": "Tight", "DE.Views.ChartSettings.txtTitle": "Chart", "DE.Views.ChartSettings.txtTopAndBottom": "Top and bottom", "DE.Views.ControlSettingsDialog.strGeneral": "General", "DE.Views.ControlSettingsDialog.textAdd": "Add", "DE.Views.ControlSettingsDialog.textAppearance": "Appearance", "DE.Views.ControlSettingsDialog.textApplyAll": "Apply to all", "DE.Views.ControlSettingsDialog.textBox": "Bounding box", "DE.Views.ControlSettingsDialog.textChange": "Edit", "DE.Views.ControlSettingsDialog.textCheckbox": "Check box", "DE.Views.ControlSettingsDialog.textChecked": "Checked symbol", "DE.Views.ControlSettingsDialog.textColor": "Color", "DE.Views.ControlSettingsDialog.textCombobox": "Combo box", "DE.Views.ControlSettingsDialog.textDate": "Date format", "DE.Views.ControlSettingsDialog.textDelete": "Delete", "DE.Views.ControlSettingsDialog.textDisplayName": "Display name", "DE.Views.ControlSettingsDialog.textDown": "Down", "DE.Views.ControlSettingsDialog.textDropDown": "Drop-down list", "DE.Views.ControlSettingsDialog.textFormat": "Display the date like this", "DE.Views.ControlSettingsDialog.textLang": "Language", "DE.Views.ControlSettingsDialog.textLock": "Locking", "DE.Views.ControlSettingsDialog.textName": "Title", "DE.Views.ControlSettingsDialog.textNone": "None", "DE.Views.ControlSettingsDialog.textPlaceholder": "Placeholder", "DE.Views.ControlSettingsDialog.textShowAs": "Show as", "DE.Views.ControlSettingsDialog.textSystemColor": "System", "DE.Views.ControlSettingsDialog.textTag": "Tag", "DE.Views.ControlSettingsDialog.textTitle": "Content control settings", "DE.Views.ControlSettingsDialog.textUnchecked": "Unchecked symbol", "DE.Views.ControlSettingsDialog.textUp": "Up", "DE.Views.ControlSettingsDialog.textValue": "Value", "DE.Views.ControlSettingsDialog.tipChange": "Change symbol", "DE.Views.ControlSettingsDialog.txtLockDelete": "Content control cannot be deleted", "DE.Views.ControlSettingsDialog.txtLockEdit": "Contents cannot be edited", "DE.Views.CrossReferenceDialog.textAboveBelow": "Above/below", "DE.Views.CrossReferenceDialog.textBookmark": "Bookmark", "DE.Views.CrossReferenceDialog.textBookmarkText": "Bookmark text", "DE.Views.CrossReferenceDialog.textCaption": "Entire caption", "DE.Views.CrossReferenceDialog.textEmpty": "The request reference is empty.", "DE.Views.CrossReferenceDialog.textEndnote": "Endnote", "DE.Views.CrossReferenceDialog.textEndNoteNum": "Endnote number", "DE.Views.CrossReferenceDialog.textEndNoteNumForm": "Endnote number (formatted)", "DE.Views.CrossReferenceDialog.textEquation": "Equation", "DE.Views.CrossReferenceDialog.textFigure": "Figure", "DE.Views.CrossReferenceDialog.textFootnote": "Footnote", "DE.Views.CrossReferenceDialog.textHeading": "Heading", "DE.Views.CrossReferenceDialog.textHeadingNum": "Heading number", "DE.Views.CrossReferenceDialog.textHeadingNumFull": "Heading number (full context)", "DE.Views.CrossReferenceDialog.textHeadingNumNo": "Heading number (no context)", "DE.Views.CrossReferenceDialog.textHeadingText": "Heading text", "DE.Views.CrossReferenceDialog.textIncludeAbove": "Include above/below", "DE.Views.CrossReferenceDialog.textInsert": "Insert", "DE.Views.CrossReferenceDialog.textInsertAs": "Insert as hyperlink", "DE.Views.CrossReferenceDialog.textLabelNum": "Only label and number", "DE.Views.CrossReferenceDialog.textNoteNum": "Footnote number", "DE.Views.CrossReferenceDialog.textNoteNumForm": "Footnote number (formatted)", "DE.Views.CrossReferenceDialog.textOnlyCaption": "Only caption text", "DE.Views.CrossReferenceDialog.textPageNum": "Page number", "DE.Views.CrossReferenceDialog.textParagraph": "Numbered item", "DE.Views.CrossReferenceDialog.textParaNum": "Paragraph number", "DE.Views.CrossReferenceDialog.textParaNumFull": "Paragraph number (full context)", "DE.Views.CrossReferenceDialog.textParaNumNo": "Paragraph number (no context)", "DE.Views.CrossReferenceDialog.textSeparate": "Separate numbers with", "DE.Views.CrossReferenceDialog.textTable": "Table", "DE.Views.CrossReferenceDialog.textText": "Paragraph text", "DE.Views.CrossReferenceDialog.textWhich": "For which caption", "DE.Views.CrossReferenceDialog.textWhichBookmark": "For which bookmark", "DE.Views.CrossReferenceDialog.textWhichEndnote": "For which endnote", "DE.Views.CrossReferenceDialog.textWhichHeading": "For which heading", "DE.Views.CrossReferenceDialog.textWhichNote": "For which footnote", "DE.Views.CrossReferenceDialog.textWhichPara": "For which numbered item", "DE.Views.CrossReferenceDialog.txtReference": "Insert reference to", "DE.Views.CrossReferenceDialog.txtTitle": "Cross-reference", "DE.Views.CrossReferenceDialog.txtType": "Reference type", "DE.Views.CustomColumnsDialog.textColumns": "Number of columns", "DE.Views.CustomColumnsDialog.textSeparator": "Column divider", "DE.Views.CustomColumnsDialog.textSpacing": "Spacing between columns", "DE.Views.CustomColumnsDialog.textTitle": "Columns", "DE.Views.DateTimeDialog.confirmDefault": "Set default format for {0}: \"{1}\"", "DE.Views.DateTimeDialog.textDefault": "Set as default", "DE.Views.DateTimeDialog.textFormat": "Formats", "DE.Views.DateTimeDialog.textLang": "Language", "DE.Views.DateTimeDialog.textUpdate": "Update automatically", "DE.Views.DateTimeDialog.txtTitle": "Date & Time", "DE.Views.DocProtection.hintProtectDoc": "Protect document", "DE.Views.DocProtection.txtDocProtectedComment": "Document is protected.<br>You may only insert comments to this document.", "DE.Views.DocProtection.txtDocProtectedForms": "Document is protected.<br>You may only fill in forms in this document.", "DE.Views.DocProtection.txtDocProtectedTrack": "Document is protected.<br>You may edit this document, but all changes will be tracked.", "DE.Views.DocProtection.txtDocProtectedView": "Document is protected.<br>You may only view this document.", "DE.Views.DocProtection.txtDocUnlockDescription": "Enter a password to unprotect document", "DE.Views.DocProtection.txtProtectDoc": "Protect Document", "DE.Views.DocProtection.txtUnlockTitle": "Unprotect Document", "DE.Views.DocumentHolder.aboveText": "Above", "DE.Views.DocumentHolder.addCommentText": "Add comment", "DE.Views.DocumentHolder.advancedDropCapText": "Drop Cap Settings", "DE.Views.DocumentHolder.advancedEquationText": "Equation Settings", "DE.Views.DocumentHolder.advancedFrameText": "Frame Advanced Settings", "DE.Views.DocumentHolder.advancedParagraphText": "Paragraph advanced settings", "DE.Views.DocumentHolder.advancedTableText": "Table advanced settings", "DE.Views.DocumentHolder.advancedText": "Advanced Settings", "DE.Views.DocumentHolder.alignmentText": "Alignment", "DE.Views.DocumentHolder.allLinearText": "All - Linear", "DE.Views.DocumentHolder.allProfText": "All - Professional", "DE.Views.DocumentHolder.belowText": "Below", "DE.Views.DocumentHolder.breakBeforeText": "Page break before", "DE.Views.DocumentHolder.bulletsText": "Bullets and Numbering", "DE.Views.DocumentHolder.cellAlignText": "Cell vertical alignment", "DE.Views.DocumentHolder.cellText": "Cell", "DE.Views.DocumentHolder.centerText": "Center", "DE.Views.DocumentHolder.chartText": "Chart advanced settings", "DE.Views.DocumentHolder.columnText": "Column", "DE.Views.DocumentHolder.currLinearText": "Current - Linear", "DE.Views.DocumentHolder.currProfText": "Current - Professional", "DE.Views.DocumentHolder.deleteColumnText": "Delete Column", "DE.Views.DocumentHolder.deleteRowText": "Delete Row", "DE.Views.DocumentHolder.deleteTableText": "Delete Table", "DE.Views.DocumentHolder.deleteText": "Delete", "DE.Views.DocumentHolder.direct270Text": "Rotate Text Up", "DE.Views.DocumentHolder.direct90Text": "Rotate Text Down", "DE.Views.DocumentHolder.directHText": "Horizontal", "DE.Views.DocumentHolder.directionText": "Text direction", "DE.Views.DocumentHolder.editChartText": "Edit data", "DE.Views.DocumentHolder.editFooterText": "Edit footer", "DE.Views.DocumentHolder.editHeaderText": "Edit header", "DE.Views.DocumentHolder.editHyperlinkText": "Edit Hyperlink", "DE.Views.DocumentHolder.eqToInlineText": "Change to Inline", "DE.Views.DocumentHolder.guestText": "Guest", "DE.Views.DocumentHolder.hyperlinkText": "Hyperlink", "DE.Views.DocumentHolder.ignoreAllSpellText": "Ignore all", "DE.Views.DocumentHolder.ignoreSpellText": "Ignore", "DE.Views.DocumentHolder.imageText": "Image advanced settings", "DE.Views.DocumentHolder.insertColumnLeftText": "Column Left", "DE.Views.DocumentHolder.insertColumnRightText": "Column Right", "DE.Views.DocumentHolder.insertColumnText": "Insert Column", "DE.Views.DocumentHolder.insertRowAboveText": "Row Above", "DE.Views.DocumentHolder.insertRowBelowText": "Row Below", "DE.Views.DocumentHolder.insertRowText": "Insert Row", "DE.Views.DocumentHolder.insertText": "Insert", "DE.Views.DocumentHolder.keepLinesText": "Keep lines together", "DE.Views.DocumentHolder.langText": "Select language", "DE.Views.DocumentHolder.latexText": "LaTeX", "DE.Views.DocumentHolder.leftText": "Left", "DE.Views.DocumentHolder.loadSpellText": "Loading variants...", "DE.Views.DocumentHolder.mergeCellsText": "Merge cells", "DE.Views.DocumentHolder.moreText": "More variants...", "DE.Views.DocumentHolder.noSpellVariantsText": "No variants", "DE.Views.DocumentHolder.notcriticalErrorTitle": "Warning", "DE.Views.DocumentHolder.originalSizeText": "Actual size", "DE.Views.DocumentHolder.paragraphText": "Paragraph", "DE.Views.DocumentHolder.removeHyperlinkText": "Remove Hyperlink", "DE.Views.DocumentHolder.rightText": "Right", "DE.Views.DocumentHolder.rowText": "Row", "DE.Views.DocumentHolder.saveStyleText": "Create new style", "DE.Views.DocumentHolder.selectCellText": "Select Cell", "DE.Views.DocumentHolder.selectColumnText": "Select Column", "DE.Views.DocumentHolder.selectRowText": "Select Row", "DE.Views.DocumentHolder.selectTableText": "Select Table", "DE.Views.DocumentHolder.selectText": "Select", "DE.Views.DocumentHolder.shapeText": "Shape advanced settings", "DE.Views.DocumentHolder.spellcheckText": "Spellcheck", "DE.Views.DocumentHolder.splitCellsText": "Split cell...", "DE.Views.DocumentHolder.splitCellTitleText": "Split Cell", "DE.Views.DocumentHolder.strDelete": "Remove Signature", "DE.Views.DocumentHolder.strDetails": "Signature Details", "DE.Views.DocumentHolder.strSetup": "Signature Setup", "DE.Views.DocumentHolder.strSign": "Sign", "DE.Views.DocumentHolder.styleText": "Formatting as Style", "DE.Views.DocumentHolder.tableText": "Table", "DE.Views.DocumentHolder.textAccept": "Accept change", "DE.Views.DocumentHolder.textAlign": "Align", "DE.Views.DocumentHolder.textArrange": "<PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.textArrangeBack": "Send to Background", "DE.Views.DocumentHolder.textArrangeBackward": "Send Backward", "DE.Views.DocumentHolder.textArrangeForward": "Bring Forward", "DE.Views.DocumentHolder.textArrangeFront": "Bring to Foreground", "DE.Views.DocumentHolder.textCells": "Cells", "DE.Views.DocumentHolder.textCol": "Delete entire column", "DE.Views.DocumentHolder.textContentControls": "Content control", "DE.Views.DocumentHolder.textContinueNumbering": "Continue numbering", "DE.Views.DocumentHolder.textCopy": "Copy", "DE.Views.DocumentHolder.textCrop": "Crop", "DE.Views.DocumentHolder.textCropFill": "Fill", "DE.Views.DocumentHolder.textCropFit": "Fit", "DE.Views.DocumentHolder.textCut": "Cut", "DE.Views.DocumentHolder.textDistributeCols": "Distribute columns", "DE.Views.DocumentHolder.textDistributeRows": "Distribute rows", "DE.Views.DocumentHolder.textEditControls": "Content control settings", "DE.Views.DocumentHolder.textEditPoints": "Edit Points", "DE.Views.DocumentHolder.textEditWrapBoundary": "Edit wrap boundary", "DE.Views.DocumentHolder.textFlipH": "Flip Horizontally", "DE.Views.DocumentHolder.textFlipV": "Flip Vertically", "DE.Views.DocumentHolder.textFollow": "Follow move", "DE.Views.DocumentHolder.textFromFile": "From File", "DE.Views.DocumentHolder.textFromStorage": "From Storage", "DE.Views.DocumentHolder.textFromUrl": "From URL", "DE.Views.DocumentHolder.textJoinList": "Join to previous list", "DE.Views.DocumentHolder.textLeft": "Shift cells left", "DE.Views.DocumentHolder.textNest": "Nest table", "DE.Views.DocumentHolder.textNextPage": "Next Page", "DE.Views.DocumentHolder.textNumberingValue": "Numbering Value", "DE.Views.DocumentHolder.textPaste": "Paste", "DE.Views.DocumentHolder.textPrevPage": "Previous Page", "DE.Views.DocumentHolder.textRefreshField": "Update field", "DE.Views.DocumentHolder.textReject": "Reject change", "DE.Views.DocumentHolder.textRemCheckBox": "Remove Checkbox", "DE.Views.DocumentHolder.textRemComboBox": "Remove Combo Box", "DE.Views.DocumentHolder.textRemDropdown": "Remove Dropdown", "DE.Views.DocumentHolder.textRemField": "Remove Text Field", "DE.Views.DocumentHolder.textRemove": "Remove", "DE.Views.DocumentHolder.textRemoveControl": "Remove content control", "DE.Views.DocumentHolder.textRemPicture": "Remove Image", "DE.Views.DocumentHolder.textRemRadioBox": "Remove Radio Button", "DE.Views.DocumentHolder.textReplace": "Replace image", "DE.Views.DocumentHolder.textRotate": "Rotate", "DE.Views.DocumentHolder.textRotate270": "Rotate 90° Counterclockwise", "DE.Views.DocumentHolder.textRotate90": "Rotate 90° Clockwise", "DE.Views.DocumentHolder.textRow": "Delete entire row", "DE.Views.DocumentHolder.textSeparateList": "Separate list", "DE.Views.DocumentHolder.textSettings": "Settings", "DE.Views.DocumentHolder.textSeveral": "Several Rows/Columns", "DE.Views.DocumentHolder.textShapeAlignBottom": "Align Bottom", "DE.Views.DocumentHolder.textShapeAlignCenter": "Align Center", "DE.Views.DocumentHolder.textShapeAlignLeft": "Align Left", "DE.Views.DocumentHolder.textShapeAlignMiddle": "Align Middle", "DE.Views.DocumentHolder.textShapeAlignRight": "Align Right", "DE.Views.DocumentHolder.textShapeAlignTop": "Align Top", "DE.Views.DocumentHolder.textStartNewList": "Start new list", "DE.Views.DocumentHolder.textStartNumberingFrom": "Set numbering value", "DE.Views.DocumentHolder.textTitleCellsRemove": "Delete Cells", "DE.Views.DocumentHolder.textTOC": "Table of contents", "DE.Views.DocumentHolder.textTOCSettings": "Table of contents settings", "DE.Views.DocumentHolder.textUndo": "Undo", "DE.Views.DocumentHolder.textUpdateAll": "Update entire table", "DE.Views.DocumentHolder.textUpdatePages": "Update page numbers only", "DE.Views.DocumentHolder.textUpdateTOC": "Update table of contents", "DE.Views.DocumentHolder.textWrap": "Wrapping style", "DE.Views.DocumentHolder.tipIsLocked": "This element is currently being edited by another user.", "DE.Views.DocumentHolder.toDictionaryText": "Add to dictionary", "DE.Views.DocumentHolder.txtAddBottom": "Add bottom border", "DE.Views.DocumentHolder.txtAddFractionBar": "Add fraction bar", "DE.Views.DocumentHolder.txtAddHor": "Add horizontal line", "DE.Views.DocumentHolder.txtAddLB": "Add left bottom line", "DE.Views.DocumentHolder.txtAddLeft": "Add left border", "DE.Views.DocumentHolder.txtAddLT": "Add left top line", "DE.Views.DocumentHolder.txtAddRight": "Add right border", "DE.Views.DocumentHolder.txtAddTop": "Add top border", "DE.Views.DocumentHolder.txtAddVer": "Add vertical line", "DE.Views.DocumentHolder.txtAlignToChar": "Align to character", "DE.Views.DocumentHolder.txtBehind": "Behind text", "DE.Views.DocumentHolder.txtBorderProps": "Border properties", "DE.Views.DocumentHolder.txtBottom": "Bottom", "DE.Views.DocumentHolder.txtColumnAlign": "Column alignment", "DE.Views.DocumentHolder.txtDecreaseArg": "Decrease argument size", "DE.Views.DocumentHolder.txtDeleteArg": "Delete argument", "DE.Views.DocumentHolder.txtDeleteBreak": "Delete manual break", "DE.Views.DocumentHolder.txtDeleteChars": "Delete enclosing characters", "DE.Views.DocumentHolder.txtDeleteCharsAndSeparators": "Delete enclosing characters and separators", "DE.Views.DocumentHolder.txtDeleteEq": "Delete equation", "DE.Views.DocumentHolder.txtDeleteGroupChar": "Delete char", "DE.Views.DocumentHolder.txtDeleteRadical": "Delete radical", "DE.Views.DocumentHolder.txtDistribHor": "Distribute horizontally", "DE.Views.DocumentHolder.txtDistribVert": "Distribute vertically", "DE.Views.DocumentHolder.txtEmpty": "(Empty)", "DE.Views.DocumentHolder.txtFractionLinear": "Change to linear fraction", "DE.Views.DocumentHolder.txtFractionSkewed": "Change to skewed fraction", "DE.Views.DocumentHolder.txtFractionStacked": "Change to stacked fraction", "DE.Views.DocumentHolder.txtGroup": "Group", "DE.Views.DocumentHolder.txtGroupCharOver": "Char over text", "DE.Views.DocumentHolder.txtGroupCharUnder": "Char under text", "DE.Views.DocumentHolder.txtHideBottom": "Hide bottom border", "DE.Views.DocumentHolder.txtHideBottomLimit": "Hide bottom limit", "DE.Views.DocumentHolder.txtHideCloseBracket": "Hide closing bracket", "DE.Views.DocumentHolder.txtHideDegree": "Hide degree", "DE.Views.DocumentHolder.txtHideHor": "Hide horizontal line", "DE.Views.DocumentHolder.txtHideLB": "<PERSON><PERSON> left bottom line", "DE.Views.DocumentHolder.txtHideLeft": "<PERSON><PERSON> left border", "DE.Views.DocumentHolder.txtHideLT": "<PERSON><PERSON> left top line", "DE.Views.DocumentHolder.txtHideOpenBracket": "Hide opening bracket", "DE.Views.DocumentHolder.txtHidePlaceholder": "Hide placeholder", "DE.Views.DocumentHolder.txtHideRight": "Hide right border", "DE.Views.DocumentHolder.txtHideTop": "Hide top border", "DE.Views.DocumentHolder.txtHideTopLimit": "Hide top limit", "DE.Views.DocumentHolder.txtHideVer": "Hide vertical line", "DE.Views.DocumentHolder.txtIncreaseArg": "Increase argument size", "DE.Views.DocumentHolder.txtInFront": "In front of text", "DE.Views.DocumentHolder.txtInline": "In line with text", "DE.Views.DocumentHolder.txtInsertArgAfter": "Insert argument after", "DE.Views.DocumentHolder.txtInsertArgBefore": "Insert argument before", "DE.Views.DocumentHolder.txtInsertBreak": "Insert manual break", "DE.Views.DocumentHolder.txtInsertCaption": "Insert caption", "DE.Views.DocumentHolder.txtInsertEqAfter": "Insert equation after", "DE.Views.DocumentHolder.txtInsertEqBefore": "Insert equation before", "DE.Views.DocumentHolder.txtKeepTextOnly": "Keep text only", "DE.Views.DocumentHolder.txtLimitChange": "Change limits location", "DE.Views.DocumentHolder.txtLimitOver": "Limit over text", "DE.Views.DocumentHolder.txtLimitUnder": "Limit under text", "DE.Views.DocumentHolder.txtMatchBrackets": "Match brackets to argument height", "DE.Views.DocumentHolder.txtMatrixAlign": "Matrix alignment", "DE.Views.DocumentHolder.txtOverbar": "Bar over text", "DE.Views.DocumentHolder.txtOverwriteCells": "Overwrite cells", "DE.Views.DocumentHolder.txtPasteSourceFormat": "Keep source formatting", "DE.Views.DocumentHolder.txtPressLink": "Press {0} and click link", "DE.Views.DocumentHolder.txtPrintSelection": "Print selection", "DE.Views.DocumentHolder.txtRemFractionBar": "Remove fraction bar", "DE.Views.DocumentHolder.txtRemLimit": "Remove limit", "DE.Views.DocumentHolder.txtRemoveAccentChar": "Remove accent character", "DE.Views.DocumentHolder.txtRemoveBar": "Remove bar", "DE.Views.DocumentHolder.txtRemoveWarning": "Do you want to remove this signature?<br>It can't be undone.", "DE.Views.DocumentHolder.txtRemScripts": "Remove scripts", "DE.Views.DocumentHolder.txtRemSubscript": "Remove subscript", "DE.Views.DocumentHolder.txtRemSuperscript": "Remove superscript", "DE.Views.DocumentHolder.txtScriptsAfter": "Scripts after text", "DE.Views.DocumentHolder.txtScriptsBefore": "Scripts before text", "DE.Views.DocumentHolder.txtShowBottomLimit": "Show bottom limit", "DE.Views.DocumentHolder.txtShowCloseBracket": "Show closing bracket", "DE.Views.DocumentHolder.txtShowDegree": "Show degree", "DE.Views.DocumentHolder.txtShowOpenBracket": "Show opening bracket", "DE.Views.DocumentHolder.txtShowPlaceholder": "Show placeholder", "DE.Views.DocumentHolder.txtShowTopLimit": "Show top limit", "DE.Views.DocumentHolder.txtSquare": "Square", "DE.Views.DocumentHolder.txtStretchBrackets": "Stretch brackets", "DE.Views.DocumentHolder.txtThrough": "Through", "DE.Views.DocumentHolder.txtTight": "Tight", "DE.Views.DocumentHolder.txtTop": "Top", "DE.Views.DocumentHolder.txtTopAndBottom": "Top and bottom", "DE.Views.DocumentHolder.txtUnderbar": "Bar under text", "DE.Views.DocumentHolder.txtUngroup": "Ungroup", "DE.Views.DocumentHolder.txtWarnUrl": "Clicking this link can be harmful to your device and data.<br>Are you sure you want to continue?", "DE.Views.DocumentHolder.unicodeText": "Unicode", "DE.Views.DocumentHolder.updateStyleText": "Update %1 style", "DE.Views.DocumentHolder.vertAlignText": "Vertical alignment", "DE.Views.DropcapSettingsAdvanced.strBorders": "Borders & Fill", "DE.Views.DropcapSettingsAdvanced.strDropcap": "Drop cap", "DE.Views.DropcapSettingsAdvanced.strMargins": "<PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textAlign": "Alignment", "DE.Views.DropcapSettingsAdvanced.textAtLeast": "At least", "DE.Views.DropcapSettingsAdvanced.textAuto": "Auto", "DE.Views.DropcapSettingsAdvanced.textBackColor": "Background color", "DE.Views.DropcapSettingsAdvanced.textBorderColor": "Border color", "DE.Views.DropcapSettingsAdvanced.textBorderDesc": "Click on diagram or use buttons to select borders", "DE.Views.DropcapSettingsAdvanced.textBorderWidth": "Border size", "DE.Views.DropcapSettingsAdvanced.textBottom": "Bottom", "DE.Views.DropcapSettingsAdvanced.textCenter": "Center", "DE.Views.DropcapSettingsAdvanced.textColumn": "Column", "DE.Views.DropcapSettingsAdvanced.textDistance": "Distance from text", "DE.Views.DropcapSettingsAdvanced.textExact": "Exactly", "DE.Views.DropcapSettingsAdvanced.textFlow": "Flow frame", "DE.Views.DropcapSettingsAdvanced.textFont": "Font", "DE.Views.DropcapSettingsAdvanced.textFrame": "<PERSON>ame", "DE.Views.DropcapSettingsAdvanced.textHeight": "Height", "DE.Views.DropcapSettingsAdvanced.textHorizontal": "Horizontal", "DE.Views.DropcapSettingsAdvanced.textInline": "Inline frame", "DE.Views.DropcapSettingsAdvanced.textInMargin": "In margin", "DE.Views.DropcapSettingsAdvanced.textInText": "In text", "DE.Views.DropcapSettingsAdvanced.textLeft": "Left", "DE.Views.DropcapSettingsAdvanced.textMargin": "<PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textMove": "Move with text", "DE.Views.DropcapSettingsAdvanced.textNone": "None", "DE.Views.DropcapSettingsAdvanced.textPage": "Page", "DE.Views.DropcapSettingsAdvanced.textParagraph": "Paragraph", "DE.Views.DropcapSettingsAdvanced.textParameters": "Parameters", "DE.Views.DropcapSettingsAdvanced.textPosition": "Position", "DE.Views.DropcapSettingsAdvanced.textRelative": "Relative to", "DE.Views.DropcapSettingsAdvanced.textRight": "Right", "DE.Views.DropcapSettingsAdvanced.textRowHeight": "Height in rows", "DE.Views.DropcapSettingsAdvanced.textTitle": "Drop cap - Advanced settings", "DE.Views.DropcapSettingsAdvanced.textTitleFrame": "Frame - Advanced settings", "DE.Views.DropcapSettingsAdvanced.textTop": "Top", "DE.Views.DropcapSettingsAdvanced.textVertical": "Vertical", "DE.Views.DropcapSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.tipFontName": "Font", "DE.Views.DropcapSettingsAdvanced.txtNoBorders": "No borders", "DE.Views.EditListItemDialog.textDisplayName": "Display name", "DE.Views.EditListItemDialog.textNameError": "Display name must not be empty.", "DE.Views.EditListItemDialog.textValue": "Value", "DE.Views.EditListItemDialog.textValueError": "An item with the same value already exists.", "DE.Views.FileMenu.btnBackCaption": "Open file location", "DE.Views.FileMenu.btnCloseMenuCaption": "Close Menu", "DE.Views.FileMenu.btnCreateNewCaption": "Create New", "DE.Views.FileMenu.btnDownloadCaption": "Download as", "DE.Views.FileMenu.btnExitCaption": "Close", "DE.Views.FileMenu.btnFileOpenCaption": "Open", "DE.Views.FileMenu.btnHelpCaption": "Help", "DE.Views.FileMenu.btnHistoryCaption": "Version History", "DE.Views.FileMenu.btnInfoCaption": "Document Info", "DE.Views.FileMenu.btnPrintCaption": "Print", "DE.Views.FileMenu.btnProtectCaption": "Protect", "DE.Views.FileMenu.btnRecentFilesCaption": "Open Recent", "DE.Views.FileMenu.btnRenameCaption": "<PERSON><PERSON>", "DE.Views.FileMenu.btnReturnCaption": "Back to Document", "DE.Views.FileMenu.btnRightsCaption": "Access Rights", "DE.Views.FileMenu.btnSaveAsCaption": "Save as", "DE.Views.FileMenu.btnSaveCaption": "Save", "DE.Views.FileMenu.btnSaveCopyAsCaption": "Save Copy as", "DE.Views.FileMenu.btnSettingsCaption": "Advanced Settings", "DE.Views.FileMenu.btnToEditCaption": "Edit Document", "DE.Views.FileMenu.textDownload": "Download", "DE.Views.FileMenuPanels.CreateNew.txtBlank": "Blank Document", "DE.Views.FileMenuPanels.CreateNew.txtCreateNew": "Create New", "DE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Apply", "DE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Add Author", "DE.Views.FileMenuPanels.DocumentInfo.txtAddText": "Add Text", "DE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Application", "DE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "Author", "DE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Change access rights", "DE.Views.FileMenuPanels.DocumentInfo.txtComment": "Comment", "DE.Views.FileMenuPanels.DocumentInfo.txtCreated": "Created", "DE.Views.FileMenuPanels.DocumentInfo.txtFastWV": "Fast Web View", "DE.Views.FileMenuPanels.DocumentInfo.txtLoading": "Loading...", "DE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Last Modified By", "DE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Last Modified", "DE.Views.FileMenuPanels.DocumentInfo.txtNo": "No", "DE.Views.FileMenuPanels.DocumentInfo.txtOwner": "Owner", "DE.Views.FileMenuPanels.DocumentInfo.txtPages": "Pages", "DE.Views.FileMenuPanels.DocumentInfo.txtPageSize": "<PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtParagraphs": "Paragraphs", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfProducer": "PDF Producer", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfTagged": "Tagged PDF", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfVer": "PDF Version", "DE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Location", "DE.Views.FileMenuPanels.DocumentInfo.txtRights": "Persons who have rights", "DE.Views.FileMenuPanels.DocumentInfo.txtSpaces": "Characters with spaces", "DE.Views.FileMenuPanels.DocumentInfo.txtStatistics": "Statistics", "DE.Views.FileMenuPanels.DocumentInfo.txtSubject": "Subject", "DE.Views.FileMenuPanels.DocumentInfo.txtSymbols": "Characters", "DE.Views.FileMenuPanels.DocumentInfo.txtTags": "Tags", "DE.Views.FileMenuPanels.DocumentInfo.txtTitle": "Title", "DE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Uploaded", "DE.Views.FileMenuPanels.DocumentInfo.txtWords": "Words", "DE.Views.FileMenuPanels.DocumentInfo.txtYes": "Yes", "DE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Change access rights", "DE.Views.FileMenuPanels.DocumentRights.txtRights": "Persons who have rights", "DE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Warning", "DE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "With password", "DE.Views.FileMenuPanels.ProtectDoc.strProtect": "Protect Document", "DE.Views.FileMenuPanels.ProtectDoc.strSignature": "With signature", "DE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Edit document", "DE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "Editing will remove signatures from the document.<br>Continue?", "DE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "This document has been protected with password", "DE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "This document needs to be signed.", "DE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Valid signatures have been added to the document. The document is protected from editing.", "DE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Some of the digital signatures in the document are invalid or could not be verified. The document is protected from editing.", "DE.Views.FileMenuPanels.ProtectDoc.txtView": "View signatures", "DE.Views.FileMenuPanels.Settings.okButtonText": "Apply", "DE.Views.FileMenuPanels.Settings.strCoAuthMode": "Co-editing Mode", "DE.Views.FileMenuPanels.Settings.strFast": "Fast", "DE.Views.FileMenuPanels.Settings.strFontRender": "<PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.strIgnoreWordsInUPPERCASE": "Ignore words in UPPERCASE", "DE.Views.FileMenuPanels.Settings.strIgnoreWordsWithNumbers": "Ignore words with numbers", "DE.Views.FileMenuPanels.Settings.strMacrosSettings": "<PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.strPasteButton": "Show the Paste Options button when the content is pasted", "DE.Views.FileMenuPanels.Settings.strShowChanges": "Real-time Collaboration Changes", "DE.Views.FileMenuPanels.Settings.strShowComments": "Show comments in text", "DE.Views.FileMenuPanels.Settings.strShowOthersChanges": "Show changes from other users", "DE.Views.FileMenuPanels.Settings.strShowResolvedComments": "Show resolved comments", "DE.Views.FileMenuPanels.Settings.strStrict": "Strict", "DE.Views.FileMenuPanels.Settings.strTheme": "Interface theme", "DE.Views.FileMenuPanels.Settings.strUnit": "Unit of Measurement", "DE.Views.FileMenuPanels.Settings.strZoom": "Default Zoom Value", "DE.Views.FileMenuPanels.Settings.text10Minutes": "Every 10 Minutes", "DE.Views.FileMenuPanels.Settings.text30Minutes": "Every 30 Minutes", "DE.Views.FileMenuPanels.Settings.text5Minutes": "Every 5 Minutes", "DE.Views.FileMenuPanels.Settings.text60Minutes": "Every Hour", "DE.Views.FileMenuPanels.Settings.textAlignGuides": "Alignment Guides", "DE.Views.FileMenuPanels.Settings.textAutoRecover": "Autorecover", "DE.Views.FileMenuPanels.Settings.textAutoSave": "Autosave", "DE.Views.FileMenuPanels.Settings.textDisabled": "Disabled", "DE.Views.FileMenuPanels.Settings.textForceSave": "Saving intermediate versions", "DE.Views.FileMenuPanels.Settings.textMinute": "Every Minute", "DE.Views.FileMenuPanels.Settings.textOldVersions": "Make the files compatible with older MS Word versions when saved as DOCX", "DE.Views.FileMenuPanels.Settings.txtAll": "View All", "DE.Views.FileMenuPanels.Settings.txtAutoCorrect": "AutoCorrect options...", "DE.Views.FileMenuPanels.Settings.txtCacheMode": "Default cache mode", "DE.Views.FileMenuPanels.Settings.txtChangesBalloons": "Show by click in balloons", "DE.Views.FileMenuPanels.Settings.txtChangesTip": "Show by hover in tooltips", "DE.Views.FileMenuPanels.Settings.txtCm": "Centimeter", "DE.Views.FileMenuPanels.Settings.txtCollaboration": "Collaboration", "DE.Views.FileMenuPanels.Settings.txtDarkMode": "Turn on document dark mode", "DE.Views.FileMenuPanels.Settings.txtEditingSaving": "Editing and saving", "DE.Views.FileMenuPanels.Settings.txtFastTip": "Real-time co-editing. All changes are saved automatically", "DE.Views.FileMenuPanels.Settings.txtFitPage": "Fit to <PERSON>", "DE.Views.FileMenuPanels.Settings.txtFitWidth": "Fit to Width", "DE.Views.FileMenuPanels.Settings.txtHieroglyphs": "Hieroglyphs", "DE.Views.FileMenuPanels.Settings.txtInch": "Inch", "DE.Views.FileMenuPanels.Settings.txtLast": "View Last", "DE.Views.FileMenuPanels.Settings.txtMac": "as OS X", "DE.Views.FileMenuPanels.Settings.txtNative": "Native", "DE.Views.FileMenuPanels.Settings.txtNone": "View None", "DE.Views.FileMenuPanels.Settings.txtProofing": "Proofing", "DE.Views.FileMenuPanels.Settings.txtPt": "Point", "DE.Views.FileMenuPanels.Settings.txtQuickPrint": "Show the Quick Print button in the editor header", "DE.Views.FileMenuPanels.Settings.txtQuickPrintTip": "The document will be printed on the last selected or default printer", "DE.Views.FileMenuPanels.Settings.txtRunMacros": "Enable All", "DE.Views.FileMenuPanels.Settings.txtRunMacrosDesc": "Enable all macros without a notification", "DE.Views.FileMenuPanels.Settings.txtShowTrackChanges": "Show track changes", "DE.Views.FileMenuPanels.Settings.txtSpellCheck": "Spell Checking", "DE.Views.FileMenuPanels.Settings.txtStopMacros": "Disable All", "DE.Views.FileMenuPanels.Settings.txtStopMacrosDesc": "Disable all macros without a notification", "DE.Views.FileMenuPanels.Settings.txtStrictTip": "Use the \"Save\" button to sync the changes you and others make", "DE.Views.FileMenuPanels.Settings.txtUseAltKey": "Use Alt key to navigate the user interface using the keyboard", "DE.Views.FileMenuPanels.Settings.txtUseOptionKey": "Use Option key to navigate the user interface using the keyboard", "DE.Views.FileMenuPanels.Settings.txtWarnMacros": "Show Notification", "DE.Views.FileMenuPanels.Settings.txtWarnMacrosDesc": "Disable all macros with a notification", "DE.Views.FileMenuPanels.Settings.txtWin": "as Windows", "DE.Views.FileMenuPanels.Settings.txtWorkspace": "Workspace", "DE.Views.FormSettings.textAlways": "Always", "DE.Views.FormSettings.textAnyone": "Anyone", "DE.Views.FormSettings.textAspect": "Lock aspect ratio", "DE.Views.FormSettings.textAtLeast": "At least", "DE.Views.FormSettings.textAuto": "Auto", "DE.Views.FormSettings.textAutofit": "AutoFit", "DE.Views.FormSettings.textBackgroundColor": "Background Color", "DE.Views.FormSettings.textCheckbox": "Checkbox", "DE.Views.FormSettings.textColor": "Border color", "DE.Views.FormSettings.textComb": "Comb of characters", "DE.Views.FormSettings.textCombobox": "Combo Box", "DE.Views.FormSettings.textComplex": "Complex Field", "DE.Views.FormSettings.textConnected": "Fields connected", "DE.Views.FormSettings.textCreditCard": "Credit Card Number (e.g 4111-1111-1111-1111)", "DE.Views.FormSettings.textDateField": "Date & Time Field", "DE.Views.FormSettings.textDateFormat": "Display the date like this", "DE.Views.FormSettings.textDelete": "Delete", "DE.Views.FormSettings.textDigits": "Digits", "DE.Views.FormSettings.textDisconnect": "Disconnect", "DE.Views.FormSettings.textDropDown": "Dropdown", "DE.Views.FormSettings.textExact": "Exactly", "DE.Views.FormSettings.textField": "Text Field", "DE.Views.FormSettings.textFillRoles": "Who needs to fill this out?", "DE.Views.FormSettings.textFixed": "Fixed size field", "DE.Views.FormSettings.textFormat": "Format", "DE.Views.FormSettings.textFormatSymbols": "Allowed Symbols", "DE.Views.FormSettings.textFromFile": "From File", "DE.Views.FormSettings.textFromStorage": "From Storage", "DE.Views.FormSettings.textFromUrl": "From URL", "DE.Views.FormSettings.textGroupKey": "Group key", "DE.Views.FormSettings.textImage": "Image", "DE.Views.FormSettings.textKey": "Key", "DE.Views.FormSettings.textLang": "Language", "DE.Views.FormSettings.textLetters": "Letters", "DE.Views.FormSettings.textLock": "Lock", "DE.Views.FormSettings.textMask": "Arbitrary Mask", "DE.Views.FormSettings.textMaxChars": "Characters limit", "DE.Views.FormSettings.textMulti": "Multiline field", "DE.Views.FormSettings.textNever": "Never", "DE.Views.FormSettings.textNoBorder": "No border", "DE.Views.FormSettings.textNone": "None", "DE.Views.FormSettings.textPhone1": "Phone Number (e.g. (*************)", "DE.Views.FormSettings.textPhone2": "Phone Number (e.g. +447911123456)", "DE.Views.FormSettings.textPlaceholder": "Placeholder", "DE.Views.FormSettings.textRadiobox": "Radio Button", "DE.Views.FormSettings.textReg": "Regular Expression", "DE.Views.FormSettings.textRequired": "Required", "DE.Views.FormSettings.textScale": "When to scale", "DE.Views.FormSettings.textSelectImage": "Select Image", "DE.Views.FormSettings.textTag": "Tag", "DE.Views.FormSettings.textTip": "Tip", "DE.Views.FormSettings.textTipAdd": "Add new value", "DE.Views.FormSettings.textTipDelete": "Delete value", "DE.Views.FormSettings.textTipDown": "Move down", "DE.Views.FormSettings.textTipUp": "Move up", "DE.Views.FormSettings.textTooBig": "Image is Too Big", "DE.Views.FormSettings.textTooSmall": "Image is Too Small", "DE.Views.FormSettings.textUKPassport": "UK Passport Number (e.g. *********)", "DE.Views.FormSettings.textUnlock": "Unlock", "DE.Views.FormSettings.textUSSSN": "US SSN (e.g. ***********)", "DE.Views.FormSettings.textValue": "Value Options", "DE.Views.FormSettings.textWidth": "Cell width", "DE.Views.FormSettings.textZipCodeUS": "US Zip Code (e.g. 92663 or 92663-1234)", "DE.Views.FormsTab.capBtnCheckBox": "Checkbox", "DE.Views.FormsTab.capBtnComboBox": "Combo Box", "DE.Views.FormsTab.capBtnComplex": "Complex Field", "DE.Views.FormsTab.capBtnDownloadForm": "Download as oform", "DE.Views.FormsTab.capBtnDropDown": "Dropdown", "DE.Views.FormsTab.capBtnEmail": "Email Address", "DE.Views.FormsTab.capBtnImage": "Image", "DE.Views.FormsTab.capBtnManager": "Manage Roles", "DE.Views.FormsTab.capBtnNext": "Next Field", "DE.Views.FormsTab.capBtnPhone": "Phone Number", "DE.Views.FormsTab.capBtnPrev": "Previous Field", "DE.Views.FormsTab.capBtnRadioBox": "Radio Button", "DE.Views.FormsTab.capBtnSaveForm": "Save as oform", "DE.Views.FormsTab.capBtnSubmit": "Submit", "DE.Views.FormsTab.capBtnText": "Text Field", "DE.Views.FormsTab.capBtnView": "View Form", "DE.Views.FormsTab.capCreditCard": "Credit Card", "DE.Views.FormsTab.capDateTime": "Date & Time", "DE.Views.FormsTab.capZipCode": "Zip Code", "DE.Views.FormsTab.textAnyone": "Anyone", "DE.Views.FormsTab.textClear": "Clear Fields", "DE.Views.FormsTab.textClearFields": "Clear All Fields", "DE.Views.FormsTab.textCreateForm": "Add fields and create a fillable OFORM document", "DE.Views.FormsTab.textGotIt": "Got it", "DE.Views.FormsTab.textHighlight": "Highlight Settings", "DE.Views.FormsTab.textNoHighlight": "No highlighting", "DE.Views.FormsTab.textRequired": "Fill all required fields to send form.", "DE.Views.FormsTab.textSubmited": "Form submitted successfully", "DE.Views.FormsTab.tipCheckBox": "Insert checkbox", "DE.Views.FormsTab.tipComboBox": "Insert combo box", "DE.Views.FormsTab.tipComplexField": "Insert complex field", "DE.Views.FormsTab.tipCreditCard": "Insert credit card number", "DE.Views.FormsTab.tipDateTime": "Insert date and time", "DE.Views.FormsTab.tipDownloadForm": "Download a file as a fillable OFORM document", "DE.Views.FormsTab.tipDropDown": "Insert dropdown list", "DE.Views.FormsTab.tipEmailField": "Insert email address", "DE.Views.FormsTab.tipFixedText": "Insert fixed text field", "DE.Views.FormsTab.tipImageField": "Insert image", "DE.Views.FormsTab.tipInlineText": "Insert inline text field", "DE.Views.FormsTab.tipManager": "Manage Roles", "DE.Views.FormsTab.tipNextForm": "Go to the next field", "DE.Views.FormsTab.tipPhoneField": "Insert phone number", "DE.Views.FormsTab.tipPrevForm": "Go to the previous field", "DE.Views.FormsTab.tipRadioBox": "Insert radio button", "DE.Views.FormsTab.tipSaveForm": "Save a file as a fillable OFORM document", "DE.Views.FormsTab.tipSubmit": "Submit form", "DE.Views.FormsTab.tipTextField": "Insert text field", "DE.Views.FormsTab.tipViewForm": "View form", "DE.Views.FormsTab.tipZipCode": "Insert zip code", "DE.Views.FormsTab.txtFixedDesc": "Insert fixed text field", "DE.Views.FormsTab.txtFixedText": "Fixed", "DE.Views.FormsTab.txtInlineDesc": "Insert inline text field", "DE.Views.FormsTab.txtInlineText": "Inline", "DE.Views.FormsTab.txtUntitled": "Untitled", "DE.Views.HeaderFooterSettings.textBottomCenter": "Bottom center", "DE.Views.HeaderFooterSettings.textBottomLeft": "Bottom left", "DE.Views.HeaderFooterSettings.textBottomPage": "Bottom of Page", "DE.Views.HeaderFooterSettings.textBottomRight": "Bottom right", "DE.Views.HeaderFooterSettings.textDiffFirst": "Different first page", "DE.Views.HeaderFooterSettings.textDiffOdd": "Different odd and even pages", "DE.Views.HeaderFooterSettings.textFrom": "Start at", "DE.Views.HeaderFooterSettings.textHeaderFromBottom": "Footer from Bottom", "DE.Views.HeaderFooterSettings.textHeaderFromTop": "Header from Top", "DE.Views.HeaderFooterSettings.textInsertCurrent": "Insert to Current Position", "DE.Views.HeaderFooterSettings.textOptions": "Options", "DE.Views.HeaderFooterSettings.textPageNum": "Insert Page Number", "DE.Views.HeaderFooterSettings.textPageNumbering": "Page Numbering", "DE.Views.HeaderFooterSettings.textPosition": "Position", "DE.Views.HeaderFooterSettings.textPrev": "Continue from previous section", "DE.Views.HeaderFooterSettings.textSameAs": "Link to Previous", "DE.Views.HeaderFooterSettings.textTopCenter": "Top center", "DE.Views.HeaderFooterSettings.textTopLeft": "Top left", "DE.Views.HeaderFooterSettings.textTopPage": "Top of Page", "DE.Views.HeaderFooterSettings.textTopRight": "Top right", "DE.Views.HyperlinkSettingsDialog.textDefault": "Selected text fragment", "DE.Views.HyperlinkSettingsDialog.textDisplay": "Display", "DE.Views.HyperlinkSettingsDialog.textExternal": "External link", "DE.Views.HyperlinkSettingsDialog.textInternal": "Place in document", "DE.Views.HyperlinkSettingsDialog.textTitle": "Hyperlink settings", "DE.Views.HyperlinkSettingsDialog.textTooltip": "ScreenTip text", "DE.Views.HyperlinkSettingsDialog.textUrl": "Link to", "DE.Views.HyperlinkSettingsDialog.txtBeginning": "Beginning of document", "DE.Views.HyperlinkSettingsDialog.txtBookmarks": "Bookmarks", "DE.Views.HyperlinkSettingsDialog.txtEmpty": "This field is required", "DE.Views.HyperlinkSettingsDialog.txtHeadings": "Headings", "DE.Views.HyperlinkSettingsDialog.txtNotUrl": "This field should be a URL in the \"http://www.example.com\" format", "DE.Views.HyperlinkSettingsDialog.txtSizeLimit": "This field is limited to 2083 characters", "DE.Views.ImageSettings.textAdvanced": "Show advanced settings", "DE.Views.ImageSettings.textCrop": "Crop", "DE.Views.ImageSettings.textCropFill": "Fill", "DE.Views.ImageSettings.textCropFit": "Fit", "DE.Views.ImageSettings.textCropToShape": "Crop to shape", "DE.Views.ImageSettings.textEdit": "Edit", "DE.Views.ImageSettings.textEditObject": "Edit Object", "DE.Views.ImageSettings.textFitMargins": "Fit to Margin", "DE.Views.ImageSettings.textFlip": "Flip", "DE.Views.ImageSettings.textFromFile": "From File", "DE.Views.ImageSettings.textFromStorage": "From Storage", "DE.Views.ImageSettings.textFromUrl": "From URL", "DE.Views.ImageSettings.textHeight": "Height", "DE.Views.ImageSettings.textHint270": "Rotate 90° Counterclockwise", "DE.Views.ImageSettings.textHint90": "Rotate 90° Clockwise", "DE.Views.ImageSettings.textHintFlipH": "Flip Horizontally", "DE.Views.ImageSettings.textHintFlipV": "Flip Vertically", "DE.Views.ImageSettings.textInsert": "Replace Image", "DE.Views.ImageSettings.textOriginalSize": "Actual Size", "DE.Views.ImageSettings.textRecentlyUsed": "Recently Used", "DE.Views.ImageSettings.textRotate90": "Rotate 90°", "DE.Views.ImageSettings.textRotation": "Rotation", "DE.Views.ImageSettings.textSize": "Size", "DE.Views.ImageSettings.textWidth": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettings.textWrap": "Wrapping Style", "DE.Views.ImageSettings.txtBehind": "Behind Text", "DE.Views.ImageSettings.txtInFront": "In Front of Text", "DE.Views.ImageSettings.txtInline": "In Line with Text", "DE.Views.ImageSettings.txtSquare": "Square", "DE.Views.ImageSettings.txtThrough": "Through", "DE.Views.ImageSettings.txtTight": "Tight", "DE.Views.ImageSettings.txtTopAndBottom": "Top and bottom", "DE.Views.ImageSettingsAdvanced.strMargins": "Text padding", "DE.Views.ImageSettingsAdvanced.textAbsoluteWH": "Absolute", "DE.Views.ImageSettingsAdvanced.textAlignment": "Alignment", "DE.Views.ImageSettingsAdvanced.textAlt": "Alternative text", "DE.Views.ImageSettingsAdvanced.textAltDescription": "Description", "DE.Views.ImageSettingsAdvanced.textAltTip": "The alternative text-based representation of the visual object information, which will be read to the people with vision or cognitive impairments to help them better understand what information there is in the image, autoshape, chart, or table.", "DE.Views.ImageSettingsAdvanced.textAltTitle": "Title", "DE.Views.ImageSettingsAdvanced.textAngle": "<PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textArrows": "Arrows", "DE.Views.ImageSettingsAdvanced.textAspectRatio": "Lock aspect ratio", "DE.Views.ImageSettingsAdvanced.textAutofit": "AutoFit", "DE.Views.ImageSettingsAdvanced.textBeginSize": "Begin size", "DE.Views.ImageSettingsAdvanced.textBeginStyle": "Begin style", "DE.Views.ImageSettingsAdvanced.textBelow": "below", "DE.Views.ImageSettingsAdvanced.textBevel": "<PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textBottom": "Bottom", "DE.Views.ImageSettingsAdvanced.textBottomMargin": "Bottom margin", "DE.Views.ImageSettingsAdvanced.textBtnWrap": "Text wrapping", "DE.Views.ImageSettingsAdvanced.textCapType": "Cap type", "DE.Views.ImageSettingsAdvanced.textCenter": "Center", "DE.Views.ImageSettingsAdvanced.textCharacter": "Character", "DE.Views.ImageSettingsAdvanced.textColumn": "Column", "DE.Views.ImageSettingsAdvanced.textDistance": "Distance from text", "DE.Views.ImageSettingsAdvanced.textEndSize": "End size", "DE.Views.ImageSettingsAdvanced.textEndStyle": "End style", "DE.Views.ImageSettingsAdvanced.textFlat": "Flat", "DE.Views.ImageSettingsAdvanced.textFlipped": "Flipped", "DE.Views.ImageSettingsAdvanced.textHeight": "Height", "DE.Views.ImageSettingsAdvanced.textHorizontal": "Horizontal", "DE.Views.ImageSettingsAdvanced.textHorizontally": "Horizontally", "DE.Views.ImageSettingsAdvanced.textJoinType": "Join type", "DE.Views.ImageSettingsAdvanced.textKeepRatio": "Constant proportions", "DE.Views.ImageSettingsAdvanced.textLeft": "Left", "DE.Views.ImageSettingsAdvanced.textLeftMargin": "Left margin", "DE.Views.ImageSettingsAdvanced.textLine": "Line", "DE.Views.ImageSettingsAdvanced.textLineStyle": "Line style", "DE.Views.ImageSettingsAdvanced.textMargin": "<PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textMiter": "<PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textMove": "Move object with text", "DE.Views.ImageSettingsAdvanced.textOptions": "Options", "DE.Views.ImageSettingsAdvanced.textOriginalSize": "Actual size", "DE.Views.ImageSettingsAdvanced.textOverlap": "Allow overlap", "DE.Views.ImageSettingsAdvanced.textPage": "Page", "DE.Views.ImageSettingsAdvanced.textParagraph": "Paragraph", "DE.Views.ImageSettingsAdvanced.textPosition": "Position", "DE.Views.ImageSettingsAdvanced.textPositionPc": "Relative position", "DE.Views.ImageSettingsAdvanced.textRelative": "relative to", "DE.Views.ImageSettingsAdvanced.textRelativeWH": "Relative", "DE.Views.ImageSettingsAdvanced.textResizeFit": "Resize shape to fit text", "DE.Views.ImageSettingsAdvanced.textRight": "Right", "DE.Views.ImageSettingsAdvanced.textRightMargin": "Right margin", "DE.Views.ImageSettingsAdvanced.textRightOf": "to the right of", "DE.Views.ImageSettingsAdvanced.textRotation": "Rotation", "DE.Views.ImageSettingsAdvanced.textRound": "Round", "DE.Views.ImageSettingsAdvanced.textShape": "Shape settings", "DE.Views.ImageSettingsAdvanced.textSize": "Size", "DE.Views.ImageSettingsAdvanced.textSquare": "Square", "DE.Views.ImageSettingsAdvanced.textTextBox": "Text box", "DE.Views.ImageSettingsAdvanced.textTitle": "Image - Advanced settings", "DE.Views.ImageSettingsAdvanced.textTitleChart": "Chart - Advanced settings", "DE.Views.ImageSettingsAdvanced.textTitleShape": "Shape - Advanced settings", "DE.Views.ImageSettingsAdvanced.textTop": "Top", "DE.Views.ImageSettingsAdvanced.textTopMargin": "Top margin", "DE.Views.ImageSettingsAdvanced.textVertical": "Vertical", "DE.Views.ImageSettingsAdvanced.textVertically": "Vertically", "DE.Views.ImageSettingsAdvanced.textWeightArrows": "Weights & Arrows", "DE.Views.ImageSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textWrap": "Wrapping style", "DE.Views.ImageSettingsAdvanced.textWrapBehindTooltip": "Behind text", "DE.Views.ImageSettingsAdvanced.textWrapInFrontTooltip": "In front of text", "DE.Views.ImageSettingsAdvanced.textWrapInlineTooltip": "In line with text", "DE.Views.ImageSettingsAdvanced.textWrapSquareTooltip": "Square", "DE.Views.ImageSettingsAdvanced.textWrapThroughTooltip": "Through", "DE.Views.ImageSettingsAdvanced.textWrapTightTooltip": "Tight", "DE.Views.ImageSettingsAdvanced.textWrapTopbottomTooltip": "Top and bottom", "DE.Views.LeftMenu.tipAbout": "About", "DE.Views.LeftMenu.tipChat": "Cha<PERSON>", "DE.Views.LeftMenu.tipComments": "Comments", "DE.Views.LeftMenu.tipNavigation": "Navigation", "DE.Views.LeftMenu.tipOutline": "Headings", "DE.Views.LeftMenu.tipPageThumbnails": "<PERSON> Thumbnails", "DE.Views.LeftMenu.tipPlugins": "Plugins", "DE.Views.LeftMenu.tipSearch": "Search", "DE.Views.LeftMenu.tipSupport": "Feedback & Support", "DE.Views.LeftMenu.tipTitles": "Titles", "DE.Views.LeftMenu.txtDeveloper": "DEVELOPER MODE", "DE.Views.LeftMenu.txtEditor": "Document Editor", "DE.Views.LeftMenu.txtLimit": "Limit Access", "DE.Views.LeftMenu.txtTrial": "TRIAL MODE", "DE.Views.LeftMenu.txtTrialDev": "Trial Developer Mode", "DE.Views.LineNumbersDialog.textAddLineNumbering": "Add line numbering", "DE.Views.LineNumbersDialog.textApplyTo": "Apply changes to", "DE.Views.LineNumbersDialog.textContinuous": "Continuous", "DE.Views.LineNumbersDialog.textCountBy": "Count by", "DE.Views.LineNumbersDialog.textDocument": "Whole document", "DE.Views.LineNumbersDialog.textForward": "This point forward", "DE.Views.LineNumbersDialog.textFromText": "From text", "DE.Views.LineNumbersDialog.textNumbering": "Numbering", "DE.Views.LineNumbersDialog.textRestartEachPage": "Restart each page", "DE.Views.LineNumbersDialog.textRestartEachSection": "Restart each section", "DE.Views.LineNumbersDialog.textSection": "Current section", "DE.Views.LineNumbersDialog.textStartAt": "Start at", "DE.Views.LineNumbersDialog.textTitle": "Line numbers", "DE.Views.LineNumbersDialog.txtAutoText": "Auto", "DE.Views.Links.capBtnAddText": "Add Text", "DE.Views.Links.capBtnBookmarks": "Bookmark", "DE.Views.Links.capBtnCaption": "Caption", "DE.Views.Links.capBtnContentsUpdate": "Update Table", "DE.Views.Links.capBtnCrossRef": "Cross-reference", "DE.Views.Links.capBtnInsContents": "Table of Contents", "DE.Views.Links.capBtnInsFootnote": "Footnote", "DE.Views.Links.capBtnInsLink": "Hyperlink", "DE.Views.Links.capBtnTOF": "Table of Figures", "DE.Views.Links.confirmDeleteFootnotes": "Do you want to delete all footnotes?", "DE.Views.Links.confirmReplaceTOF": "Do you want to replace the selected table of figures?", "DE.Views.Links.mniConvertNote": "Convert all notes", "DE.Views.Links.mniDelFootnote": "Delete all notes", "DE.Views.Links.mniInsEndnote": "Insert endnote", "DE.Views.Links.mniInsFootnote": "Insert footnote", "DE.Views.Links.mniNoteSettings": "Notes settings", "DE.Views.Links.textContentsRemove": "Remove table of contents", "DE.Views.Links.textContentsSettings": "Settings", "DE.Views.Links.textConvertToEndnotes": "Convert All Footnotes to Endnotes", "DE.Views.Links.textConvertToFootnotes": "Convert All Endnotes to Footnotes", "DE.Views.Links.textGotoEndnote": "Go to endnotes", "DE.Views.Links.textGotoFootnote": "Go to footnotes", "DE.Views.Links.textSwapNotes": "Swap Footnotes and Endnotes", "DE.Views.Links.textUpdateAll": "Update entire table", "DE.Views.Links.textUpdatePages": "Update page numbers only", "DE.Views.Links.tipAddText": "Include heading in the table of contents", "DE.Views.Links.tipBookmarks": "Create a bookmark", "DE.Views.Links.tipCaption": "Insert caption", "DE.Views.Links.tipContents": "Insert table of contents", "DE.Views.Links.tipContentsUpdate": "Update table of contents", "DE.Views.Links.tipCrossRef": "Insert cross-reference", "DE.Views.Links.tipInsertHyperlink": "Add hyperlink", "DE.Views.Links.tipNotes": "Insert or edit footnotes", "DE.Views.Links.tipTableFigures": "Insert table of figures", "DE.Views.Links.tipTableFiguresUpdate": "Update table of figures", "DE.Views.Links.titleUpdateTOF": "Update Table of Figures", "DE.Views.Links.txtDontShowTof": "Do not show in table of contents", "DE.Views.Links.txtLevel": "Level", "DE.Views.ListSettingsDialog.textAuto": "Automatic", "DE.Views.ListSettingsDialog.textCenter": "Center", "DE.Views.ListSettingsDialog.textLeft": "Left", "DE.Views.ListSettingsDialog.textLevel": "Level", "DE.Views.ListSettingsDialog.textPreview": "Preview", "DE.Views.ListSettingsDialog.textRight": "Right", "DE.Views.ListSettingsDialog.txtAlign": "Alignment", "DE.Views.ListSettingsDialog.txtBullet": "Bullet", "DE.Views.ListSettingsDialog.txtColor": "Color", "DE.Views.ListSettingsDialog.txtFont": "Font and symbol", "DE.Views.ListSettingsDialog.txtLikeText": "Like a text", "DE.Views.ListSettingsDialog.txtNewBullet": "New bullet", "DE.Views.ListSettingsDialog.txtNone": "None", "DE.Views.ListSettingsDialog.txtSize": "Size", "DE.Views.ListSettingsDialog.txtSymbol": "Symbol", "DE.Views.ListSettingsDialog.txtTitle": "List settings", "DE.Views.ListSettingsDialog.txtType": "Type", "DE.Views.MailMergeEmailDlg.filePlaceholder": "PDF", "DE.Views.MailMergeEmailDlg.okButtonText": "Send", "DE.Views.MailMergeEmailDlg.subjectPlaceholder": "Theme", "DE.Views.MailMergeEmailDlg.textAttachDocx": "Attach as DOCX", "DE.Views.MailMergeEmailDlg.textAttachPdf": "Attach as PDF", "DE.Views.MailMergeEmailDlg.textFileName": "File name", "DE.Views.MailMergeEmailDlg.textFormat": "Mail format", "DE.Views.MailMergeEmailDlg.textFrom": "From", "DE.Views.MailMergeEmailDlg.textHTML": "HTML", "DE.Views.MailMergeEmailDlg.textMessage": "Message", "DE.Views.MailMergeEmailDlg.textSubject": "Subject line", "DE.Views.MailMergeEmailDlg.textTitle": "Send to email", "DE.Views.MailMergeEmailDlg.textTo": "To", "DE.Views.MailMergeEmailDlg.textWarning": "Warning!", "DE.Views.MailMergeEmailDlg.textWarningMsg": "Please note that mailing cannot be stopped once your click the 'Send' button.", "DE.Views.MailMergeSettings.downloadMergeTitle": "Merging", "DE.Views.MailMergeSettings.errorMailMergeSaveFile": "Me<PERSON> failed.", "DE.Views.MailMergeSettings.notcriticalErrorTitle": "Warning", "DE.Views.MailMergeSettings.textAddRecipients": "Add some recipients to the list first", "DE.Views.MailMergeSettings.textAll": "All records", "DE.Views.MailMergeSettings.textCurrent": "Current record", "DE.Views.MailMergeSettings.textDataSource": "Data Source", "DE.Views.MailMergeSettings.textDocx": "Docx", "DE.Views.MailMergeSettings.textDownload": "Download", "DE.Views.MailMergeSettings.textEditData": "Edit recipient list", "DE.Views.MailMergeSettings.textEmail": "Email", "DE.Views.MailMergeSettings.textFrom": "From", "DE.Views.MailMergeSettings.textGoToMail": "Go to Mail", "DE.Views.MailMergeSettings.textHighlight": "Highlight merge fields", "DE.Views.MailMergeSettings.textInsertField": "Insert Merge Field", "DE.Views.MailMergeSettings.textMaxRecepients": "Max 100 recipients.", "DE.Views.MailMergeSettings.textMerge": "<PERSON><PERSON>", "DE.Views.MailMergeSettings.textMergeFields": "<PERSON><PERSON>", "DE.Views.MailMergeSettings.textMergeTo": "Merge to", "DE.Views.MailMergeSettings.textPdf": "PDF", "DE.Views.MailMergeSettings.textPortal": "Save", "DE.Views.MailMergeSettings.textPreview": "Preview results", "DE.Views.MailMergeSettings.textReadMore": "Read more", "DE.Views.MailMergeSettings.textSendMsg": "All mail messages are ready and will be sent out within some time.<br>The speed of mailing depends on your mail service.<br>You can continue working with document or close it. After the operation is over the notification will be sent to your registration email address.", "DE.Views.MailMergeSettings.textTo": "To", "DE.Views.MailMergeSettings.txtFirst": "To first record", "DE.Views.MailMergeSettings.txtFromToError": "\"From\" value must be less than \"To\" value", "DE.Views.MailMergeSettings.txtLast": "To last record", "DE.Views.MailMergeSettings.txtNext": "To next record", "DE.Views.MailMergeSettings.txtPrev": "To previous record", "DE.Views.MailMergeSettings.txtUntitled": "Untitled", "DE.Views.MailMergeSettings.warnProcessMailMerge": "Starting merge failed", "DE.Views.Navigation.strNavigate": "Headings", "DE.Views.Navigation.txtClosePanel": "Close headings", "DE.Views.Navigation.txtCollapse": "Collapse all", "DE.Views.Navigation.txtDemote": "Demote", "DE.Views.Navigation.txtEmpty": "There are no headings in the document.<br>Apply a heading style to the text so that it appears in the table of contents.", "DE.Views.Navigation.txtEmptyItem": "Empty Heading", "DE.Views.Navigation.txtEmptyViewer": "There are no headings in the document.", "DE.Views.Navigation.txtExpand": "Expand all", "DE.Views.Navigation.txtExpandToLevel": "Expand to level", "DE.Views.Navigation.txtFontSize": "Font size", "DE.Views.Navigation.txtHeadingAfter": "New heading after", "DE.Views.Navigation.txtHeadingBefore": "New heading before", "DE.Views.Navigation.txtLarge": "Large", "DE.Views.Navigation.txtMedium": "Medium", "DE.Views.Navigation.txtNewHeading": "New subheading", "DE.Views.Navigation.txtPromote": "Promote", "DE.Views.Navigation.txtSelect": "Select content", "DE.Views.Navigation.txtSettings": "Headings settings", "DE.Views.Navigation.txtSmall": "Small", "DE.Views.Navigation.txtWrapHeadings": "Wrap long headings", "DE.Views.NoteSettingsDialog.textApply": "Apply", "DE.Views.NoteSettingsDialog.textApplyTo": "Apply changes to", "DE.Views.NoteSettingsDialog.textContinue": "Continuous", "DE.Views.NoteSettingsDialog.textCustom": "Custom mark", "DE.Views.NoteSettingsDialog.textDocEnd": "End of document", "DE.Views.NoteSettingsDialog.textDocument": "Whole document", "DE.Views.NoteSettingsDialog.textEachPage": "Restart each page", "DE.Views.NoteSettingsDialog.textEachSection": "Restart each section", "DE.Views.NoteSettingsDialog.textEndnote": "Endnote", "DE.Views.NoteSettingsDialog.textFootnote": "Footnote", "DE.Views.NoteSettingsDialog.textFormat": "Format", "DE.Views.NoteSettingsDialog.textInsert": "Insert", "DE.Views.NoteSettingsDialog.textLocation": "Location", "DE.Views.NoteSettingsDialog.textNumbering": "Numbering", "DE.Views.NoteSettingsDialog.textNumFormat": "Number format", "DE.Views.NoteSettingsDialog.textPageBottom": "Bottom of page", "DE.Views.NoteSettingsDialog.textSectEnd": "End of section", "DE.Views.NoteSettingsDialog.textSection": "Current section", "DE.Views.NoteSettingsDialog.textStart": "Start at", "DE.Views.NoteSettingsDialog.textTextBottom": "Below text", "DE.Views.NoteSettingsDialog.textTitle": "Notes settings", "DE.Views.NotesRemoveDialog.textEnd": "Delete all endnotes", "DE.Views.NotesRemoveDialog.textFoot": "Delete all footnotes", "DE.Views.NotesRemoveDialog.textTitle": "Delete notes", "DE.Views.PageMarginsDialog.notcriticalErrorTitle": "Warning", "DE.Views.PageMarginsDialog.textBottom": "Bottom", "DE.Views.PageMarginsDialog.textGutter": "<PERSON><PERSON>", "DE.Views.PageMarginsDialog.textGutterPosition": "Gutter position", "DE.Views.PageMarginsDialog.textInside": "Inside", "DE.Views.PageMarginsDialog.textLandscape": "Landscape", "DE.Views.PageMarginsDialog.textLeft": "Left", "DE.Views.PageMarginsDialog.textMirrorMargins": "Mirror margins", "DE.Views.PageMarginsDialog.textMultiplePages": "Multiple pages", "DE.Views.PageMarginsDialog.textNormal": "Normal", "DE.Views.PageMarginsDialog.textOrientation": "Orientation", "DE.Views.PageMarginsDialog.textOutside": "Outside", "DE.Views.PageMarginsDialog.textPortrait": "Portrait", "DE.Views.PageMarginsDialog.textPreview": "Preview", "DE.Views.PageMarginsDialog.textRight": "Right", "DE.Views.PageMarginsDialog.textTitle": "<PERSON><PERSON>", "DE.Views.PageMarginsDialog.textTop": "Top", "DE.Views.PageMarginsDialog.txtMarginsH": "Top and bottom margins are too high for a given page height", "DE.Views.PageMarginsDialog.txtMarginsW": "Left and right margins are too wide for a given page width", "DE.Views.PageSizeDialog.textHeight": "Height", "DE.Views.PageSizeDialog.textPreset": "Preset", "DE.Views.PageSizeDialog.textTitle": "Page size", "DE.Views.PageSizeDialog.textWidth": "<PERSON><PERSON><PERSON>", "DE.Views.PageSizeDialog.txtCustom": "Custom", "DE.Views.PageThumbnails.textClosePanel": "Close page thumbnails", "DE.Views.PageThumbnails.textHighlightVisiblePart": "Highlight visible part of page", "DE.Views.PageThumbnails.textPageThumbnails": "<PERSON> Thumbnails", "DE.Views.PageThumbnails.textThumbnailsSettings": "Thumbnails settings", "DE.Views.PageThumbnails.textThumbnailsSize": "Thumbnails size", "DE.Views.ParagraphSettings.strIndent": "Indents", "DE.Views.ParagraphSettings.strIndentsLeftText": "Left", "DE.Views.ParagraphSettings.strIndentsRightText": "Right", "DE.Views.ParagraphSettings.strIndentsSpecial": "Special", "DE.Views.ParagraphSettings.strLineHeight": "Line Spacing", "DE.Views.ParagraphSettings.strParagraphSpacing": "Paragraph Spacing", "DE.Views.ParagraphSettings.strSomeParagraphSpace": "Don't add interval between paragraphs of the same style", "DE.Views.ParagraphSettings.strSpacingAfter": "After", "DE.Views.ParagraphSettings.strSpacingBefore": "Before", "DE.Views.ParagraphSettings.textAdvanced": "Show advanced settings", "DE.Views.ParagraphSettings.textAt": "At", "DE.Views.ParagraphSettings.textAtLeast": "At least", "DE.Views.ParagraphSettings.textAuto": "Multiple", "DE.Views.ParagraphSettings.textBackColor": "Background color", "DE.Views.ParagraphSettings.textExact": "Exactly", "DE.Views.ParagraphSettings.textFirstLine": "First line", "DE.Views.ParagraphSettings.textHanging": "Hanging", "DE.Views.ParagraphSettings.textNoneSpecial": "(none)", "DE.Views.ParagraphSettings.txtAutoText": "Auto", "DE.Views.ParagraphSettingsAdvanced.noTabs": "The specified tabs will appear in this field", "DE.Views.ParagraphSettingsAdvanced.strAllCaps": "All caps", "DE.Views.ParagraphSettingsAdvanced.strBorders": "Borders & Fill", "DE.Views.ParagraphSettingsAdvanced.strBreakBefore": "Page break before", "DE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "Double strikethrough", "DE.Views.ParagraphSettingsAdvanced.strIndent": "Indents", "DE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "Left", "DE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Line spacing", "DE.Views.ParagraphSettingsAdvanced.strIndentsOutlinelevel": "Outline level", "DE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "Right", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "After", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "Before", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Special", "DE.Views.ParagraphSettingsAdvanced.strKeepLines": "Keep lines together", "DE.Views.ParagraphSettingsAdvanced.strKeepNext": "Keep with next", "DE.Views.ParagraphSettingsAdvanced.strMargins": "Paddings", "DE.Views.ParagraphSettingsAdvanced.strOrphan": "Orphan control", "DE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Font", "DE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Indents & Spacing", "DE.Views.ParagraphSettingsAdvanced.strParagraphLine": "Line & Page breaks", "DE.Views.ParagraphSettingsAdvanced.strParagraphPosition": "Placement", "DE.Views.ParagraphSettingsAdvanced.strSmallCaps": "Small caps", "DE.Views.ParagraphSettingsAdvanced.strSomeParagraphSpace": "Don't add interval between paragraphs of the same style", "DE.Views.ParagraphSettingsAdvanced.strSpacing": "Spacing", "DE.Views.ParagraphSettingsAdvanced.strStrike": "Strikethrough", "DE.Views.ParagraphSettingsAdvanced.strSubscript": "Subscript", "DE.Views.ParagraphSettingsAdvanced.strSuperscript": "Superscript", "DE.Views.ParagraphSettingsAdvanced.strSuppressLineNumbers": "Suppress line numbers", "DE.Views.ParagraphSettingsAdvanced.strTabs": "Tabs", "DE.Views.ParagraphSettingsAdvanced.textAlign": "Alignment", "DE.Views.ParagraphSettingsAdvanced.textAll": "All", "DE.Views.ParagraphSettingsAdvanced.textAtLeast": "At least", "DE.Views.ParagraphSettingsAdvanced.textAuto": "Multiple", "DE.Views.ParagraphSettingsAdvanced.textBackColor": "Background color", "DE.Views.ParagraphSettingsAdvanced.textBodyText": "Basic text", "DE.Views.ParagraphSettingsAdvanced.textBorderColor": "Border color", "DE.Views.ParagraphSettingsAdvanced.textBorderDesc": "Click on diagram or use buttons to select borders and apply chosen style to them", "DE.Views.ParagraphSettingsAdvanced.textBorderWidth": "Border size", "DE.Views.ParagraphSettingsAdvanced.textBottom": "Bottom", "DE.Views.ParagraphSettingsAdvanced.textCentered": "Centered", "DE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Character spacing", "DE.Views.ParagraphSettingsAdvanced.textContext": "Contextual", "DE.Views.ParagraphSettingsAdvanced.textContextDiscret": "Contextual and discretionary", "DE.Views.ParagraphSettingsAdvanced.textContextHistDiscret": "Contextual, historical and discretionary", "DE.Views.ParagraphSettingsAdvanced.textContextHistorical": "Contextual and historical", "DE.Views.ParagraphSettingsAdvanced.textDefault": "Default tab", "DE.Views.ParagraphSettingsAdvanced.textDiscret": "Discretionary", "DE.Views.ParagraphSettingsAdvanced.textEffects": "Effects", "DE.Views.ParagraphSettingsAdvanced.textExact": "Exactly", "DE.Views.ParagraphSettingsAdvanced.textFirstLine": "First line", "DE.Views.ParagraphSettingsAdvanced.textHanging": "Hanging", "DE.Views.ParagraphSettingsAdvanced.textHistorical": "Historical", "DE.Views.ParagraphSettingsAdvanced.textHistoricalDiscret": "Historical and discretionary", "DE.Views.ParagraphSettingsAdvanced.textJustified": "Justified", "DE.Views.ParagraphSettingsAdvanced.textLeader": "Leader", "DE.Views.ParagraphSettingsAdvanced.textLeft": "Left", "DE.Views.ParagraphSettingsAdvanced.textLevel": "Level", "DE.Views.ParagraphSettingsAdvanced.textLigatures": "Ligatures", "DE.Views.ParagraphSettingsAdvanced.textNone": "None", "DE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(none)", "DE.Views.ParagraphSettingsAdvanced.textOpenType": "OpenType features", "DE.Views.ParagraphSettingsAdvanced.textPosition": "Position", "DE.Views.ParagraphSettingsAdvanced.textRemove": "Remove", "DE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Remove all", "DE.Views.ParagraphSettingsAdvanced.textRight": "Right", "DE.Views.ParagraphSettingsAdvanced.textSet": "Specify", "DE.Views.ParagraphSettingsAdvanced.textSpacing": "Spacing", "DE.Views.ParagraphSettingsAdvanced.textStandard": "Standard only", "DE.Views.ParagraphSettingsAdvanced.textStandardContext": "Standard and contextual", "DE.Views.ParagraphSettingsAdvanced.textStandardContextDiscret": "Standard, contextual and discretionary", "DE.Views.ParagraphSettingsAdvanced.textStandardContextHist": "Standard, contextual and historical", "DE.Views.ParagraphSettingsAdvanced.textStandardDiscret": "Standard and discretionary", "DE.Views.ParagraphSettingsAdvanced.textStandardHistDiscret": "Standard, historical and discretionary", "DE.Views.ParagraphSettingsAdvanced.textStandardHistorical": "Standard and historical", "DE.Views.ParagraphSettingsAdvanced.textTabCenter": "Center", "DE.Views.ParagraphSettingsAdvanced.textTabLeft": "Left", "DE.Views.ParagraphSettingsAdvanced.textTabPosition": "Tab position", "DE.Views.ParagraphSettingsAdvanced.textTabRight": "Right", "DE.Views.ParagraphSettingsAdvanced.textTitle": "Paragraph - Advanced settings", "DE.Views.ParagraphSettingsAdvanced.textTop": "Top", "DE.Views.ParagraphSettingsAdvanced.tipAll": "Set outer border and all inner lines", "DE.Views.ParagraphSettingsAdvanced.tipBottom": "Set bottom border only", "DE.Views.ParagraphSettingsAdvanced.tipInner": "Set horizontal inner lines only", "DE.Views.ParagraphSettingsAdvanced.tipLeft": "Set left border only", "DE.Views.ParagraphSettingsAdvanced.tipNone": "Set no borders", "DE.Views.ParagraphSettingsAdvanced.tipOuter": "Set outer border only", "DE.Views.ParagraphSettingsAdvanced.tipRight": "Set right border only", "DE.Views.ParagraphSettingsAdvanced.tipTop": "Set top border only", "DE.Views.ParagraphSettingsAdvanced.txtAutoText": "Auto", "DE.Views.ParagraphSettingsAdvanced.txtNoBorders": "No borders", "DE.Views.PrintWithPreview.textMarginsLast": "Last Custom", "DE.Views.PrintWithPreview.textMarginsModerate": "Moderate", "DE.Views.PrintWithPreview.textMarginsNarrow": "<PERSON>rrow", "DE.Views.PrintWithPreview.textMarginsNormal": "Normal", "DE.Views.PrintWithPreview.textMarginsUsNormal": "US Normal", "DE.Views.PrintWithPreview.textMarginsWide": "Wide", "DE.Views.PrintWithPreview.txtAllPages": "All pages", "DE.Views.PrintWithPreview.txtBottom": "Bottom", "DE.Views.PrintWithPreview.txtCurrentPage": "Current page", "DE.Views.PrintWithPreview.txtCustom": "Custom", "DE.Views.PrintWithPreview.txtCustomPages": "Custom print", "DE.Views.PrintWithPreview.txtLandscape": "Landscape", "DE.Views.PrintWithPreview.txtLeft": "Left", "DE.Views.PrintWithPreview.txtMargins": "<PERSON><PERSON>", "DE.Views.PrintWithPreview.txtOf": "of {0}", "DE.Views.PrintWithPreview.txtPage": "Page", "DE.Views.PrintWithPreview.txtPageNumInvalid": "Page number invalid", "DE.Views.PrintWithPreview.txtPageOrientation": "Page orientation", "DE.Views.PrintWithPreview.txtPages": "Pages", "DE.Views.PrintWithPreview.txtPageSize": "Page size", "DE.Views.PrintWithPreview.txtPortrait": "Portrait", "DE.Views.PrintWithPreview.txtPrint": "Print", "DE.Views.PrintWithPreview.txtPrintPdf": "Print to PDF", "DE.Views.PrintWithPreview.txtPrintRange": "Print range", "DE.Views.PrintWithPreview.txtRight": "Right", "DE.Views.PrintWithPreview.txtSelection": "Selection", "DE.Views.PrintWithPreview.txtTop": "Top", "DE.Views.ProtectDialog.textComments": "Comments", "DE.Views.ProtectDialog.textForms": "Filling forms", "DE.Views.ProtectDialog.textReview": "Tracked changes", "DE.Views.ProtectDialog.textView": "No changes (Read only)", "DE.Views.ProtectDialog.txtAllow": "Allow only this type of editing in the document", "DE.Views.ProtectDialog.txtIncorrectPwd": "Confirmation password is not identical", "DE.Views.ProtectDialog.txtLimit": "Password is limited to 15 characters", "DE.Views.ProtectDialog.txtOptional": "optional", "DE.Views.ProtectDialog.txtPassword": "Password", "DE.Views.ProtectDialog.txtProtect": "Protect", "DE.Views.ProtectDialog.txtRepeat": "Repeat password", "DE.Views.ProtectDialog.txtTitle": "Protect", "DE.Views.ProtectDialog.txtWarning": "Warning: If you lose or forget the password, it cannot be recovered. Please keep it in a safe place.", "DE.Views.RightMenu.txtChartSettings": "Chart settings", "DE.Views.RightMenu.txtFormSettings": "Form Settings", "DE.Views.RightMenu.txtHeaderFooterSettings": "Header and footer settings", "DE.Views.RightMenu.txtImageSettings": "Image settings", "DE.Views.RightMenu.txtMailMergeSettings": "Mail merge settings", "DE.Views.RightMenu.txtParagraphSettings": "Paragraph settings", "DE.Views.RightMenu.txtShapeSettings": "Shape settings", "DE.Views.RightMenu.txtSignatureSettings": "Signature settings", "DE.Views.RightMenu.txtTableSettings": "Table settings", "DE.Views.RightMenu.txtTextArtSettings": "Text Art settings", "DE.Views.RoleDeleteDlg.textLabel": "To delete this role, you  need to move the fields associated with it to another role.", "DE.Views.RoleDeleteDlg.textSelect": "Select for field merger role", "DE.Views.RoleDeleteDlg.textTitle": "Delete Role", "DE.Views.RoleEditDlg.errNameExists": "Role with such a name already exists.", "DE.Views.RoleEditDlg.textEmptyError": "Role name must not be empty.", "DE.Views.RoleEditDlg.textName": "Role name", "DE.Views.RoleEditDlg.textNoHighlight": "No highlighting", "DE.Views.RoleEditDlg.txtTitleEdit": "Edit Role", "DE.Views.RoleEditDlg.txtTitleNew": "Create New Role", "DE.Views.RolesManagerDlg.closeButtonText": "Close", "DE.Views.RolesManagerDlg.textAnyone": "Anyone", "DE.Views.RolesManagerDlg.textDelete": "Delete", "DE.Views.RolesManagerDlg.textDeleteLast": "Are you sure you want to delete the role {0}?<br>Once deleted, the default role will be created.", "DE.Views.RolesManagerDlg.textDescription": "Add roles and set the order in which the fillers receive and sign the document", "DE.Views.RolesManagerDlg.textDown": "Move role down", "DE.Views.RolesManagerDlg.textEdit": "Edit", "DE.Views.RolesManagerDlg.textEmpty": "No roles have been created yet.<br>Create at least one role and it will appear in this field.", "DE.Views.RolesManagerDlg.textNew": "New", "DE.Views.RolesManagerDlg.textUp": "Move role up", "DE.Views.RolesManagerDlg.txtTitle": "Manage Roles", "DE.Views.RolesManagerDlg.warnCantDelete": "You cannot delete this role because it has associated fields.", "DE.Views.RolesManagerDlg.warnDelete": "Are you sure you want to delete the role {0}?", "DE.Views.SaveFormDlg.saveButtonText": "Save", "DE.Views.SaveFormDlg.textAnyone": "Anyone", "DE.Views.SaveFormDlg.textDescription": "When saving to the oform, only roles with fields are added to the filling list", "DE.Views.SaveFormDlg.textEmpty": "There are no roles associated with fields.", "DE.Views.SaveFormDlg.textFill": "Filling list", "DE.Views.SaveFormDlg.txtTitle": "Save as Form", "DE.Views.ShapeSettings.strBackground": "Background color", "DE.Views.ShapeSettings.strChange": "Change Autoshape", "DE.Views.ShapeSettings.strColor": "Color", "DE.Views.ShapeSettings.strFill": "Fill", "DE.Views.ShapeSettings.strForeground": "Foreground color", "DE.Views.ShapeSettings.strPattern": "Pattern", "DE.Views.ShapeSettings.strShadow": "Show shadow", "DE.Views.ShapeSettings.strSize": "Size", "DE.Views.ShapeSettings.strStroke": "Line", "DE.Views.ShapeSettings.strTransparency": "Opacity", "DE.Views.ShapeSettings.strType": "Type", "DE.Views.ShapeSettings.textAdvanced": "Show advanced settings", "DE.Views.ShapeSettings.textAngle": "<PERSON><PERSON>", "DE.Views.ShapeSettings.textBorderSizeErr": "The entered value is incorrect.<br>Please enter a value between 0 pt and 1584 pt.", "DE.Views.ShapeSettings.textColor": "Color Fill", "DE.Views.ShapeSettings.textDirection": "Direction", "DE.Views.ShapeSettings.textEmptyPattern": "No Pattern", "DE.Views.ShapeSettings.textFlip": "Flip", "DE.Views.ShapeSettings.textFromFile": "From File", "DE.Views.ShapeSettings.textFromStorage": "From Storage", "DE.Views.ShapeSettings.textFromUrl": "From URL", "DE.Views.ShapeSettings.textGradient": "Gradient points", "DE.Views.ShapeSettings.textGradientFill": "<PERSON><PERSON><PERSON>ll", "DE.Views.ShapeSettings.textHint270": "Rotate 90° Counterclockwise", "DE.Views.ShapeSettings.textHint90": "Rotate 90° Clockwise", "DE.Views.ShapeSettings.textHintFlipH": "Flip Horizontally", "DE.Views.ShapeSettings.textHintFlipV": "Flip Vertically", "DE.Views.ShapeSettings.textImageTexture": "Picture or Texture", "DE.Views.ShapeSettings.textLinear": "Linear", "DE.Views.ShapeSettings.textNoFill": "No Fill", "DE.Views.ShapeSettings.textPatternFill": "Pattern", "DE.Views.ShapeSettings.textPosition": "Position", "DE.Views.ShapeSettings.textRadial": "Radial", "DE.Views.ShapeSettings.textRecentlyUsed": "Recently used", "DE.Views.ShapeSettings.textRotate90": "Rotate 90°", "DE.Views.ShapeSettings.textRotation": "Rotation", "DE.Views.ShapeSettings.textSelectImage": "Select Picture", "DE.Views.ShapeSettings.textSelectTexture": "Select", "DE.Views.ShapeSettings.textStretch": "<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.textStyle": "Style", "DE.Views.ShapeSettings.textTexture": "From Texture", "DE.Views.ShapeSettings.textTile": "Tile", "DE.Views.ShapeSettings.textWrap": "Wrapping Style", "DE.Views.ShapeSettings.tipAddGradientPoint": "Add gradient point", "DE.Views.ShapeSettings.tipRemoveGradientPoint": "Remove gradient point", "DE.Views.ShapeSettings.txtBehind": "Behind Text", "DE.Views.ShapeSettings.txtBrownPaper": "Brown Paper", "DE.Views.ShapeSettings.txtCanvas": "<PERSON><PERSON>", "DE.Views.ShapeSettings.txtCarton": "<PERSON><PERSON>", "DE.Views.ShapeSettings.txtDarkFabric": "<PERSON> Fab<PERSON>", "DE.Views.ShapeSettings.txtGrain": "Grain", "DE.Views.ShapeSettings.txtGranite": "Granite", "DE.Views.ShapeSettings.txtGreyPaper": "Gray Paper", "DE.Views.ShapeSettings.txtInFront": "In Front of Text", "DE.Views.ShapeSettings.txtInline": "In Line with Text", "DE.Views.ShapeSettings.txtKnit": "K<PERSON><PERSON>", "DE.Views.ShapeSettings.txtLeather": "Leather", "DE.Views.ShapeSettings.txtNoBorders": "No Line", "DE.Views.ShapeSettings.txtPapyrus": "Papyrus", "DE.Views.ShapeSettings.txtSquare": "Square", "DE.Views.ShapeSettings.txtThrough": "Through", "DE.Views.ShapeSettings.txtTight": "Tight", "DE.Views.ShapeSettings.txtTopAndBottom": "Top and bottom", "DE.Views.ShapeSettings.txtWood": "<PERSON>", "DE.Views.SignatureSettings.notcriticalErrorTitle": "Warning", "DE.Views.SignatureSettings.strDelete": "Remove Signature", "DE.Views.SignatureSettings.strDetails": "Signature Details", "DE.Views.SignatureSettings.strInvalid": "Invalid signatures", "DE.Views.SignatureSettings.strRequested": "Requested signatures", "DE.Views.SignatureSettings.strSetup": "Signature Setup", "DE.Views.SignatureSettings.strSign": "Sign", "DE.Views.SignatureSettings.strSignature": "Signature", "DE.Views.SignatureSettings.strSigner": "Signer", "DE.Views.SignatureSettings.strValid": "Valid signatures", "DE.Views.SignatureSettings.txtContinueEditing": "Edit anyway", "DE.Views.SignatureSettings.txtEditWarning": "Editing will remove signatures from the document.<br>Continue?", "DE.Views.SignatureSettings.txtRemoveWarning": "Do you want to remove this signature?<br>It can't be undone.", "DE.Views.SignatureSettings.txtRequestedSignatures": "This document needs to be signed.", "DE.Views.SignatureSettings.txtSigned": "Valid signatures have been added to the document. The document is protected from editing.", "DE.Views.SignatureSettings.txtSignedInvalid": "Some of the digital signatures in the document are invalid or could not be verified. The document is protected from editing.", "DE.Views.Statusbar.goToPageText": "Go to Page", "DE.Views.Statusbar.pageIndexText": "Page {0} of {1}", "DE.Views.Statusbar.tipFitPage": "Fit to page", "DE.Views.Statusbar.tipFitWidth": "Fit to width", "DE.Views.Statusbar.tipHandTool": "Hand tool", "DE.Views.Statusbar.tipSelectTool": "Select tool", "DE.Views.Statusbar.tipSetLang": "Set text language", "DE.Views.Statusbar.tipZoomFactor": "Zoom", "DE.Views.Statusbar.tipZoomIn": "Zoom in", "DE.Views.Statusbar.tipZoomOut": "Zoom out", "DE.Views.Statusbar.txtPageNumInvalid": "Page number invalid", "DE.Views.Statusbar.txtPages": "Pages", "DE.Views.Statusbar.txtParagraphs": "Paragraphs", "DE.Views.Statusbar.txtSpaces": "Symbols with spaces", "DE.Views.Statusbar.txtSymbols": "Symbols", "DE.Views.Statusbar.txtWordCount": "Word count", "DE.Views.Statusbar.txtWords": "Words", "DE.Views.StyleTitleDialog.textHeader": "Create new style", "DE.Views.StyleTitleDialog.textNextStyle": "Next paragraph style", "DE.Views.StyleTitleDialog.textTitle": "Title", "DE.Views.StyleTitleDialog.txtEmpty": "This field is required", "DE.Views.StyleTitleDialog.txtNotEmpty": "Field must not be empty", "DE.Views.StyleTitleDialog.txtSameAs": "Same as created new style", "DE.Views.TableFormulaDialog.textBookmark": "Paste bookmark", "DE.Views.TableFormulaDialog.textFormat": "Number format", "DE.Views.TableFormulaDialog.textFormula": "Formula", "DE.Views.TableFormulaDialog.textInsertFunction": "Paste function", "DE.Views.TableFormulaDialog.textTitle": "Formula settings", "DE.Views.TableOfContentsSettings.strAlign": "Right align page numbers", "DE.Views.TableOfContentsSettings.strFullCaption": "Include label and number", "DE.Views.TableOfContentsSettings.strLinks": "Format table of contents as links", "DE.Views.TableOfContentsSettings.strLinksOF": "Format table of figures as links", "DE.Views.TableOfContentsSettings.strShowPages": "Show page numbers", "DE.Views.TableOfContentsSettings.textBuildTable": "Build table of contents from", "DE.Views.TableOfContentsSettings.textBuildTableOF": "Build table of figures from", "DE.Views.TableOfContentsSettings.textEquation": "Equation", "DE.Views.TableOfContentsSettings.textFigure": "Figure", "DE.Views.TableOfContentsSettings.textLeader": "Leader", "DE.Views.TableOfContentsSettings.textLevel": "Level", "DE.Views.TableOfContentsSettings.textLevels": "Levels", "DE.Views.TableOfContentsSettings.textNone": "None", "DE.Views.TableOfContentsSettings.textRadioCaption": "Caption", "DE.Views.TableOfContentsSettings.textRadioLevels": "Outline levels", "DE.Views.TableOfContentsSettings.textRadioStyle": "Style", "DE.Views.TableOfContentsSettings.textRadioStyles": "Selected styles", "DE.Views.TableOfContentsSettings.textStyle": "Style", "DE.Views.TableOfContentsSettings.textStyles": "Styles", "DE.Views.TableOfContentsSettings.textTable": "Table", "DE.Views.TableOfContentsSettings.textTitle": "Table of contents", "DE.Views.TableOfContentsSettings.textTitleTOF": "Table of figures", "DE.Views.TableOfContentsSettings.txtCentered": "Centered", "DE.Views.TableOfContentsSettings.txtClassic": "Classic", "DE.Views.TableOfContentsSettings.txtCurrent": "Current", "DE.Views.TableOfContentsSettings.txtDistinctive": "Distinctive", "DE.Views.TableOfContentsSettings.txtFormal": "Formal", "DE.Views.TableOfContentsSettings.txtModern": "Modern", "DE.Views.TableOfContentsSettings.txtOnline": "Online", "DE.Views.TableOfContentsSettings.txtSimple": "Simple", "DE.Views.TableOfContentsSettings.txtStandard": "Standard", "DE.Views.TableSettings.deleteColumnText": "Delete Column", "DE.Views.TableSettings.deleteRowText": "Delete Row", "DE.Views.TableSettings.deleteTableText": "Delete Table", "DE.Views.TableSettings.insertColumnLeftText": "Insert Column Left", "DE.Views.TableSettings.insertColumnRightText": "Insert Column Right", "DE.Views.TableSettings.insertRowAboveText": "Insert Row Above", "DE.Views.TableSettings.insertRowBelowText": "Insert Row Below", "DE.Views.TableSettings.mergeCellsText": "Merge Cells", "DE.Views.TableSettings.selectCellText": "Select Cell", "DE.Views.TableSettings.selectColumnText": "Select Column", "DE.Views.TableSettings.selectRowText": "Select Row", "DE.Views.TableSettings.selectTableText": "Select Table", "DE.Views.TableSettings.splitCellsText": "Split Cell...", "DE.Views.TableSettings.splitCellTitleText": "Split Cell", "DE.Views.TableSettings.strRepeatRow": "Repeat as header row at the top of each page", "DE.Views.TableSettings.textAddFormula": "Add formula", "DE.Views.TableSettings.textAdvanced": "Show advanced settings", "DE.Views.TableSettings.textBackColor": "Background Color", "DE.Views.TableSettings.textBanded": "Banded", "DE.Views.TableSettings.textBorderColor": "Color", "DE.Views.TableSettings.textBorders": "Borders Style", "DE.Views.TableSettings.textCellSize": "Rows & Columns Size", "DE.Views.TableSettings.textColumns": "Columns", "DE.Views.TableSettings.textConvert": "Convert Table to Text", "DE.Views.TableSettings.textDistributeCols": "Distribute columns", "DE.Views.TableSettings.textDistributeRows": "Distribute rows", "DE.Views.TableSettings.textEdit": "Rows & Columns", "DE.Views.TableSettings.textEmptyTemplate": "No templates", "DE.Views.TableSettings.textFirst": "First", "DE.Views.TableSettings.textHeader": "Header", "DE.Views.TableSettings.textHeight": "Height", "DE.Views.TableSettings.textLast": "Last", "DE.Views.TableSettings.textRows": "Rows", "DE.Views.TableSettings.textSelectBorders": "Select borders you want to change applying style chosen above", "DE.Views.TableSettings.textTemplate": "Select From Template", "DE.Views.TableSettings.textTotal": "Total", "DE.Views.TableSettings.textWidth": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.tipAll": "Set outer border and all inner lines", "DE.Views.TableSettings.tipBottom": "Set outer bottom border only", "DE.Views.TableSettings.tipInner": "Set inner lines only", "DE.Views.TableSettings.tipInnerHor": "Set horizontal inner lines only", "DE.Views.TableSettings.tipInnerVert": "Set vertical inner lines only", "DE.Views.TableSettings.tipLeft": "Set outer left border only", "DE.Views.TableSettings.tipNone": "Set no borders", "DE.Views.TableSettings.tipOuter": "Set outer border only", "DE.Views.TableSettings.tipRight": "Set outer right border only", "DE.Views.TableSettings.tipTop": "Set outer top border only", "DE.Views.TableSettings.txtGroupTable_BorderedAndLined": "Bordered & Lined Tables", "DE.Views.TableSettings.txtGroupTable_Custom": "Custom", "DE.Views.TableSettings.txtGroupTable_Grid": "Grid Tables", "DE.Views.TableSettings.txtGroupTable_List": "List Tables", "DE.Views.TableSettings.txtGroupTable_Plain": "Plain Tables", "DE.Views.TableSettings.txtNoBorders": "No borders", "DE.Views.TableSettings.txtTable_Accent": "Accent", "DE.Views.TableSettings.txtTable_Bordered": "Bordered", "DE.Views.TableSettings.txtTable_BorderedAndLined": "Bordered & Lined", "DE.Views.TableSettings.txtTable_Colorful": "Colorful", "DE.Views.TableSettings.txtTable_Dark": "Dark", "DE.Views.TableSettings.txtTable_GridTable": "Grid Table", "DE.Views.TableSettings.txtTable_Light": "Light", "DE.Views.TableSettings.txtTable_Lined": "Lined", "DE.Views.TableSettings.txtTable_ListTable": "List Table", "DE.Views.TableSettings.txtTable_PlainTable": "Plain Table", "DE.Views.TableSettings.txtTable_TableGrid": "Table Grid", "DE.Views.TableSettingsAdvanced.textAlign": "Alignment", "DE.Views.TableSettingsAdvanced.textAlignment": "Alignment", "DE.Views.TableSettingsAdvanced.textAllowSpacing": "Spacing between cells", "DE.Views.TableSettingsAdvanced.textAlt": "Alternative text", "DE.Views.TableSettingsAdvanced.textAltDescription": "Description", "DE.Views.TableSettingsAdvanced.textAltTip": "The alternative text-based representation of the visual object information, which will be read to the people with vision or cognitive impairments to help them better understand what information there is in the image, autoshape, chart, or table.", "DE.Views.TableSettingsAdvanced.textAltTitle": "Title", "DE.Views.TableSettingsAdvanced.textAnchorText": "Text", "DE.Views.TableSettingsAdvanced.textAutofit": "Automatically resize to fit contents", "DE.Views.TableSettingsAdvanced.textBackColor": "Cell background", "DE.Views.TableSettingsAdvanced.textBelow": "below", "DE.Views.TableSettingsAdvanced.textBorderColor": "Border color", "DE.Views.TableSettingsAdvanced.textBorderDesc": "Click on diagram or use buttons to select borders and apply chosen style to them", "DE.Views.TableSettingsAdvanced.textBordersBackgroung": "Borders & Background", "DE.Views.TableSettingsAdvanced.textBorderWidth": "Border size", "DE.Views.TableSettingsAdvanced.textBottom": "Bottom", "DE.Views.TableSettingsAdvanced.textCellOptions": "Cell options", "DE.Views.TableSettingsAdvanced.textCellProps": "Cell", "DE.Views.TableSettingsAdvanced.textCellSize": "Cell size", "DE.Views.TableSettingsAdvanced.textCenter": "Center", "DE.Views.TableSettingsAdvanced.textCenterTooltip": "Center", "DE.Views.TableSettingsAdvanced.textCheckMargins": "Use default margins", "DE.Views.TableSettingsAdvanced.textDefaultMargins": "Default cell margins", "DE.Views.TableSettingsAdvanced.textDistance": "Distance from text", "DE.Views.TableSettingsAdvanced.textHorizontal": "Horizontal", "DE.Views.TableSettingsAdvanced.textIndLeft": "Indent from left", "DE.Views.TableSettingsAdvanced.textLeft": "Left", "DE.Views.TableSettingsAdvanced.textLeftTooltip": "Left", "DE.Views.TableSettingsAdvanced.textMargin": "<PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textMargins": "Cell margins", "DE.Views.TableSettingsAdvanced.textMeasure": "Measure in", "DE.Views.TableSettingsAdvanced.textMove": "Move object with text", "DE.Views.TableSettingsAdvanced.textOnlyCells": "For selected cells only", "DE.Views.TableSettingsAdvanced.textOptions": "Options", "DE.Views.TableSettingsAdvanced.textOverlap": "Allow overlap", "DE.Views.TableSettingsAdvanced.textPage": "Page", "DE.Views.TableSettingsAdvanced.textPosition": "Position", "DE.Views.TableSettingsAdvanced.textPrefWidth": "Preferred width", "DE.Views.TableSettingsAdvanced.textPreview": "Preview", "DE.Views.TableSettingsAdvanced.textRelative": "relative to", "DE.Views.TableSettingsAdvanced.textRight": "Right", "DE.Views.TableSettingsAdvanced.textRightOf": "to the right of", "DE.Views.TableSettingsAdvanced.textRightTooltip": "Right", "DE.Views.TableSettingsAdvanced.textTable": "Table", "DE.Views.TableSettingsAdvanced.textTableBackColor": "Table background", "DE.Views.TableSettingsAdvanced.textTablePosition": "Table position", "DE.Views.TableSettingsAdvanced.textTableSize": "Table size", "DE.Views.TableSettingsAdvanced.textTitle": "Table - Advanced settings", "DE.Views.TableSettingsAdvanced.textTop": "Top", "DE.Views.TableSettingsAdvanced.textVertical": "Vertical", "DE.Views.TableSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textWidthSpaces": "Width & Spaces", "DE.Views.TableSettingsAdvanced.textWrap": "Text wrapping", "DE.Views.TableSettingsAdvanced.textWrapNoneTooltip": "Inline table", "DE.Views.TableSettingsAdvanced.textWrapParallelTooltip": "Flow table", "DE.Views.TableSettingsAdvanced.textWrappingStyle": "Wrapping style", "DE.Views.TableSettingsAdvanced.textWrapText": "Wrap text", "DE.Views.TableSettingsAdvanced.tipAll": "Set outer border and all inner lines", "DE.Views.TableSettingsAdvanced.tipCellAll": "Set borders for inner cells only", "DE.Views.TableSettingsAdvanced.tipCellInner": "Set vertical and horizontal lines for inner cells only", "DE.Views.TableSettingsAdvanced.tipCellOuter": "Set outer borders for inner cells only", "DE.Views.TableSettingsAdvanced.tipInner": "Set inner lines only", "DE.Views.TableSettingsAdvanced.tipNone": "Set no borders", "DE.Views.TableSettingsAdvanced.tipOuter": "Set outer border only", "DE.Views.TableSettingsAdvanced.tipTableOuterCellAll": "Set outer border and borders for all inner cells", "DE.Views.TableSettingsAdvanced.tipTableOuterCellInner": "Set outer border and vertical and horizontal lines for inner cells", "DE.Views.TableSettingsAdvanced.tipTableOuterCellOuter": "Set table outer border and outer borders for inner cells", "DE.Views.TableSettingsAdvanced.txtCm": "Centimeter", "DE.Views.TableSettingsAdvanced.txtInch": "Inch", "DE.Views.TableSettingsAdvanced.txtNoBorders": "No borders", "DE.Views.TableSettingsAdvanced.txtPercent": "Percent", "DE.Views.TableSettingsAdvanced.txtPt": "Point", "DE.Views.TableToTextDialog.textEmpty": "You must type a character for the custom separator.", "DE.Views.TableToTextDialog.textNested": "Convert nested tables", "DE.Views.TableToTextDialog.textOther": "Other", "DE.Views.TableToTextDialog.textPara": "Paragraph marks", "DE.Views.TableToTextDialog.textSemicolon": "Semicolons", "DE.Views.TableToTextDialog.textSeparator": "Separate text with", "DE.Views.TableToTextDialog.textTab": "Tabs", "DE.Views.TableToTextDialog.textTitle": "Convert table to text", "DE.Views.TextArtSettings.strColor": "Color", "DE.Views.TextArtSettings.strFill": "Fill", "DE.Views.TextArtSettings.strSize": "Size", "DE.Views.TextArtSettings.strStroke": "Line", "DE.Views.TextArtSettings.strTransparency": "Opacity", "DE.Views.TextArtSettings.strType": "Type", "DE.Views.TextArtSettings.textAngle": "<PERSON><PERSON>", "DE.Views.TextArtSettings.textBorderSizeErr": "The entered value is incorrect.<br>Please enter a value between 0 pt and 1584 pt.", "DE.Views.TextArtSettings.textColor": "Color Fill", "DE.Views.TextArtSettings.textDirection": "Direction", "DE.Views.TextArtSettings.textGradient": "Gradient points", "DE.Views.TextArtSettings.textGradientFill": "<PERSON><PERSON><PERSON>ll", "DE.Views.TextArtSettings.textLinear": "Linear", "DE.Views.TextArtSettings.textNoFill": "No Fill", "DE.Views.TextArtSettings.textPosition": "Position", "DE.Views.TextArtSettings.textRadial": "Radial", "DE.Views.TextArtSettings.textSelectTexture": "Select", "DE.Views.TextArtSettings.textStyle": "Style", "DE.Views.TextArtSettings.textTemplate": "Template", "DE.Views.TextArtSettings.textTransform": "Transform", "DE.Views.TextArtSettings.tipAddGradientPoint": "Add gradient point", "DE.Views.TextArtSettings.tipRemoveGradientPoint": "Remove gradient point", "DE.Views.TextArtSettings.txtNoBorders": "No Line", "DE.Views.TextToTableDialog.textAutofit": "Autofit behavior", "DE.Views.TextToTableDialog.textColumns": "Columns", "DE.Views.TextToTableDialog.textContents": "Autofit to contents", "DE.Views.TextToTableDialog.textEmpty": "You must type a character for the custom separator.", "DE.Views.TextToTableDialog.textFixed": "Fixed column width", "DE.Views.TextToTableDialog.textOther": "Other", "DE.Views.TextToTableDialog.textPara": "Paragraphs", "DE.Views.TextToTableDialog.textRows": "Rows", "DE.Views.TextToTableDialog.textSemicolon": "Semicolons", "DE.Views.TextToTableDialog.textSeparator": "Separate text at", "DE.Views.TextToTableDialog.textTab": "Tabs", "DE.Views.TextToTableDialog.textTableSize": "Table size", "DE.Views.TextToTableDialog.textTitle": "Convert text to table", "DE.Views.TextToTableDialog.textWindow": "Autofit to window", "DE.Views.TextToTableDialog.txtAutoText": "Auto", "DE.Views.Toolbar.capBtnAddComment": "Add Comment", "DE.Views.Toolbar.capBtnBlankPage": "<PERSON><PERSON> <PERSON>", "DE.Views.Toolbar.capBtnColumns": "Columns", "DE.Views.Toolbar.capBtnComment": "Comment", "DE.Views.Toolbar.capBtnDateTime": "Date & Time", "DE.Views.Toolbar.capBtnInsChart": "Chart", "DE.Views.Toolbar.capBtnInsControls": "Content Controls", "DE.Views.Toolbar.capBtnInsDropcap": "Drop Cap", "DE.Views.Toolbar.capBtnInsEquation": "Equation", "DE.Views.Toolbar.capBtnInsHeader": "Header & Footer", "DE.Views.Toolbar.capBtnInsImage": "Image", "DE.Views.Toolbar.capBtnInsPagebreak": "Breaks", "DE.Views.Toolbar.capBtnInsShape": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "DE.Views.Toolbar.capBtnInsSymbol": "Symbol", "DE.Views.Toolbar.capBtnInsTable": "Table", "DE.Views.Toolbar.capBtnInsTextart": "Text Art", "DE.Views.Toolbar.capBtnInsTextbox": "Text Box", "DE.Views.Toolbar.capBtnLineNumbers": "Line Numbers", "DE.Views.Toolbar.capBtnMargins": "<PERSON><PERSON>", "DE.Views.Toolbar.capBtnPageOrient": "Orientation", "DE.Views.Toolbar.capBtnPageSize": "Size", "DE.Views.Toolbar.capBtnWatermark": "Watermark", "DE.Views.Toolbar.capImgAlign": "Align", "DE.Views.Toolbar.capImgBackward": "Send Backward", "DE.Views.Toolbar.capImgForward": "Bring Forward", "DE.Views.Toolbar.capImgGroup": "Group", "DE.Views.Toolbar.capImgWrapping": "Wrapping", "DE.Views.Toolbar.mniCapitalizeWords": "Capitalize Each Word", "DE.Views.Toolbar.mniCustomTable": "Insert custom table", "DE.Views.Toolbar.mniDrawTable": "Draw table", "DE.Views.Toolbar.mniEditControls": "Control Settings", "DE.Views.Toolbar.mniEditDropCap": "Drop Cap Settings", "DE.Views.Toolbar.mniEditFooter": "Edit footer", "DE.Views.Toolbar.mniEditHeader": "Edit header", "DE.Views.Toolbar.mniEraseTable": "Erase table", "DE.Views.Toolbar.mniFromFile": "From File", "DE.Views.Toolbar.mniFromStorage": "From Storage", "DE.Views.Toolbar.mniFromUrl": "From URL", "DE.Views.Toolbar.mniHiddenBorders": "Hidden table borders", "DE.Views.Toolbar.mniHiddenChars": "Nonprinting characters", "DE.Views.Toolbar.mniHighlightControls": "Highlight settings", "DE.Views.Toolbar.mniImageFromFile": "Image from File", "DE.Views.Toolbar.mniImageFromStorage": "Image from Storage", "DE.Views.Toolbar.mniImageFromUrl": "Image from URL", "DE.Views.Toolbar.mniInsertSSE": "Insert Spreadsheet", "DE.Views.Toolbar.mniLowerCase": "lowercase", "DE.Views.Toolbar.mniRemoveFooter": "Remove footer", "DE.Views.Toolbar.mniRemoveHeader": "Remove header", "DE.Views.Toolbar.mniSentenceCase": "Sentence case.", "DE.Views.Toolbar.mniTextToTable": "Convert text to table", "DE.Views.Toolbar.mniToggleCase": "tOGGLE cASE", "DE.Views.Toolbar.mniUpperCase": "UPPERCASE", "DE.Views.Toolbar.strMenuNoFill": "No fill", "DE.Views.Toolbar.textAutoColor": "Automatic", "DE.Views.Toolbar.textBold": "Bold", "DE.Views.Toolbar.textBottom": "Bottom: ", "DE.Views.Toolbar.textChangeLevel": "Change List Level", "DE.Views.Toolbar.textCheckboxControl": "Check box", "DE.Views.Toolbar.textColumnsCustom": "Custom columns", "DE.Views.Toolbar.textColumnsLeft": "Left", "DE.Views.Toolbar.textColumnsOne": "One", "DE.Views.Toolbar.textColumnsRight": "Right", "DE.Views.Toolbar.textColumnsThree": "Three", "DE.Views.Toolbar.textColumnsTwo": "Two", "DE.Views.Toolbar.textComboboxControl": "Combo box", "DE.Views.Toolbar.textContinuous": "Continuous", "DE.Views.Toolbar.textContPage": "Continuous page", "DE.Views.Toolbar.textCustomLineNumbers": "Line numbering options", "DE.Views.Toolbar.textDateControl": "Date", "DE.Views.Toolbar.textDropdownControl": "Drop-down list", "DE.Views.Toolbar.textEditWatermark": "Custom watermark", "DE.Views.Toolbar.textEvenPage": "Even page", "DE.Views.Toolbar.textInMargin": "In Margin", "DE.Views.Toolbar.textInsColumnBreak": "Insert column break", "DE.Views.Toolbar.textInsertPageCount": "Insert number of pages", "DE.Views.Toolbar.textInsertPageNumber": "Insert Page Number", "DE.Views.Toolbar.textInsPageBreak": "Insert page break", "DE.Views.Toolbar.textInsSectionBreak": "Insert section break", "DE.Views.Toolbar.textInText": "In Text", "DE.Views.Toolbar.textItalic": "Italic", "DE.Views.Toolbar.textLandscape": "Landscape", "DE.Views.Toolbar.textLeft": "Left: ", "DE.Views.Toolbar.textListSettings": "List Settings", "DE.Views.Toolbar.textMarginsLast": "Last Custom", "DE.Views.Toolbar.textMarginsModerate": "Moderate", "DE.Views.Toolbar.textMarginsNarrow": "<PERSON>rrow", "DE.Views.Toolbar.textMarginsNormal": "Normal", "DE.Views.Toolbar.textMarginsUsNormal": "US Normal", "DE.Views.Toolbar.textMarginsWide": "Wide", "DE.Views.Toolbar.textNewColor": "Add New Custom Color", "DE.Views.Toolbar.textNextPage": "Next page", "DE.Views.Toolbar.textNoHighlight": "No highlighting", "DE.Views.Toolbar.textNone": "None", "DE.Views.Toolbar.textOddPage": "Odd page", "DE.Views.Toolbar.textPageMarginsCustom": "Custom margins", "DE.Views.Toolbar.textPageSizeCustom": "Custom page size", "DE.Views.Toolbar.textPictureControl": "Picture", "DE.Views.Toolbar.textPlainControl": "Plain text", "DE.Views.Toolbar.textPortrait": "Portrait", "DE.Views.Toolbar.textRemoveControl": "Remove Content Control", "DE.Views.Toolbar.textRemWatermark": "Remove watermark", "DE.Views.Toolbar.textRestartEachPage": "Restart each page", "DE.Views.Toolbar.textRestartEachSection": "Restart each section", "DE.Views.Toolbar.textRichControl": "Rich text", "DE.Views.Toolbar.textRight": "Right: ", "DE.Views.Toolbar.textStrikeout": "Strikethrough", "DE.Views.Toolbar.textStyleMenuDelete": "Delete style", "DE.Views.Toolbar.textStyleMenuDeleteAll": "Delete all custom styles", "DE.Views.Toolbar.textStyleMenuNew": "New style from selection", "DE.Views.Toolbar.textStyleMenuRestore": "Restore to default", "DE.Views.Toolbar.textStyleMenuRestoreAll": "Restore all to default styles", "DE.Views.Toolbar.textStyleMenuUpdate": "Update from selection", "DE.Views.Toolbar.textSubscript": "Subscript", "DE.Views.Toolbar.textSuperscript": "Superscript", "DE.Views.Toolbar.textSuppressForCurrentParagraph": "Suppress for current paragraph", "DE.Views.Toolbar.textTabCollaboration": "Collaboration", "DE.Views.Toolbar.textTabFile": "File", "DE.Views.Toolbar.textTabHome": "Home", "DE.Views.Toolbar.textTabInsert": "Insert", "DE.Views.Toolbar.textTabLayout": "Layout", "DE.Views.Toolbar.textTabLinks": "References", "DE.Views.Toolbar.textTabProtect": "Protection", "DE.Views.Toolbar.textTabReview": "Review", "DE.Views.Toolbar.textTabView": "View", "DE.Views.Toolbar.textTitleError": "Error", "DE.Views.Toolbar.textToCurrent": "To current position", "DE.Views.Toolbar.textTop": "Top: ", "DE.Views.Toolbar.textUnderline": "Underline", "DE.Views.Toolbar.tipAlignCenter": "Align center", "DE.Views.Toolbar.tipAlignJust": "Justified", "DE.Views.Toolbar.tipAlignLeft": "<PERSON><PERSON> left", "DE.Views.Toolbar.tipAlignRight": "Align right", "DE.Views.Toolbar.tipBack": "Back", "DE.Views.Toolbar.tipBlankPage": "Insert blank page", "DE.Views.Toolbar.tipChangeCase": "Change case", "DE.Views.Toolbar.tipChangeChart": "Change chart type", "DE.Views.Toolbar.tipClearStyle": "Clear style", "DE.Views.Toolbar.tipColorSchemas": "Change color scheme", "DE.Views.Toolbar.tipColumns": "Insert columns", "DE.Views.Toolbar.tipControls": "Insert content controls", "DE.Views.Toolbar.tipCopy": "Copy", "DE.Views.Toolbar.tipCopyStyle": "Copy style", "DE.Views.Toolbar.tipCut": "Cut", "DE.Views.Toolbar.tipDateTime": "Insert current date and time", "DE.Views.Toolbar.tipDecFont": "Decrement font size", "DE.Views.Toolbar.tipDecPrLeft": "Decrease indent", "DE.Views.Toolbar.tipDropCap": "Insert drop cap", "DE.Views.Toolbar.tipEditHeader": "Edit header or footer", "DE.Views.Toolbar.tipFontColor": "Font color", "DE.Views.Toolbar.tipFontName": "Font", "DE.Views.Toolbar.tipFontSize": "Font size", "DE.Views.Toolbar.tipHighlightColor": "Highlight color", "DE.Views.Toolbar.tipImgAlign": "Align objects", "DE.Views.Toolbar.tipImgGroup": "Group objects", "DE.Views.Toolbar.tipImgWrapping": "Wrap text", "DE.Views.Toolbar.tipIncFont": "Increment font size", "DE.Views.Toolbar.tipIncPrLeft": "Increase indent", "DE.Views.Toolbar.tipInsertChart": "Insert chart", "DE.Views.Toolbar.tipInsertEquation": "Insert equation", "DE.Views.Toolbar.tipInsertHorizontalText": "Insert horizontal text box", "DE.Views.Toolbar.tipInsertImage": "Insert image", "DE.Views.Toolbar.tipInsertNum": "Insert page number", "DE.Views.Toolbar.tipInsertShape": "Insert autoshape", "DE.Views.Toolbar.tipInsertSmartArt": "Insert SmartArt", "DE.Views.Toolbar.tipInsertSymbol": "Insert symbol", "DE.Views.Toolbar.tipInsertTable": "Insert table", "DE.Views.Toolbar.tipInsertText": "Insert text box", "DE.Views.Toolbar.tipInsertTextArt": "Insert Text Art", "DE.Views.Toolbar.tipInsertVerticalText": "Insert vertical text box", "DE.Views.Toolbar.tipLineNumbers": "Show line numbers", "DE.Views.Toolbar.tipLineSpace": "Paragraph line spacing", "DE.Views.Toolbar.tipMailRecepients": "Mail merge", "DE.Views.Toolbar.tipMarkers": "Bullets", "DE.Views.Toolbar.tipMarkersArrow": "Arrow bullets", "DE.Views.Toolbar.tipMarkersCheckmark": "Checkmark bullets", "DE.Views.Toolbar.tipMarkersDash": "Dash bullets", "DE.Views.Toolbar.tipMarkersFRhombus": "Filled rhombus bullets", "DE.Views.Toolbar.tipMarkersFRound": "Filled round bullets", "DE.Views.Toolbar.tipMarkersFSquare": "Filled square bullets", "DE.Views.Toolbar.tipMarkersHRound": "Hollow round bullets", "DE.Views.Toolbar.tipMarkersStar": "Star bullets", "DE.Views.Toolbar.tipMultiLevelArticl": "Multi-level numbered articles", "DE.Views.Toolbar.tipMultiLevelChapter": "Multi-level numbered chapters", "DE.Views.Toolbar.tipMultiLevelHeadings": "Multi-level numbered headings", "DE.Views.Toolbar.tipMultiLevelHeadVarious": "Multi-level various numbered headings", "DE.Views.Toolbar.tipMultiLevelNumbered": "Multi-level numbered bullets", "DE.Views.Toolbar.tipMultilevels": "Multilevel list", "DE.Views.Toolbar.tipMultiLevelSymbols": "Multi-level symbols bullets", "DE.Views.Toolbar.tipMultiLevelVarious": "Multi-level various numbered bullets", "DE.Views.Toolbar.tipNumbers": "Numbering", "DE.Views.Toolbar.tipPageBreak": "Insert page or section break", "DE.Views.Toolbar.tipPageMargins": "Page margins", "DE.Views.Toolbar.tipPageOrient": "Page orientation", "DE.Views.Toolbar.tipPageSize": "Page size", "DE.Views.Toolbar.tipParagraphStyle": "Paragraph style", "DE.Views.Toolbar.tipPaste": "Paste", "DE.Views.Toolbar.tipPrColor": "Shading", "DE.Views.Toolbar.tipPrint": "Print", "DE.Views.Toolbar.tipPrintQuick": "Quick print", "DE.Views.Toolbar.tipRedo": "Redo", "DE.Views.Toolbar.tipSave": "Save", "DE.Views.Toolbar.tipSaveCoauth": "Save your changes for the other users to see them.", "DE.Views.Toolbar.tipSelectAll": "Select all", "DE.Views.Toolbar.tipSendBackward": "Send backward", "DE.Views.Toolbar.tipSendForward": "Bring forward", "DE.Views.Toolbar.tipShowHiddenChars": "Nonprinting characters", "DE.Views.Toolbar.tipSynchronize": "The document has been changed by another user. Please click to save your changes and reload the updates.", "DE.Views.Toolbar.tipUndo": "Undo", "DE.Views.Toolbar.tipWatermark": "Edit watermark", "DE.Views.Toolbar.txtDistribHor": "Distribute horizontally", "DE.Views.Toolbar.txtDistribVert": "Distribute vertically", "DE.Views.Toolbar.txtMarginAlign": "Align to margin", "DE.Views.Toolbar.txtObjectsAlign": "Align Selected Objects", "DE.Views.Toolbar.txtPageAlign": "Align to page", "DE.Views.Toolbar.txtScheme1": "Office", "DE.Views.Toolbar.txtScheme10": "Median", "DE.Views.Toolbar.txtScheme11": "Metro", "DE.Views.Toolbar.txtScheme12": "<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.txtScheme13": "Opulent", "DE.Views.Toolbar.txtScheme14": "Oriel", "DE.Views.Toolbar.txtScheme15": "Origin", "DE.Views.Toolbar.txtScheme16": "Paper", "DE.Views.Toolbar.txtScheme17": "Solstice", "DE.Views.Toolbar.txtScheme18": "Technic", "DE.Views.Toolbar.txtScheme19": "Trek", "DE.Views.Toolbar.txtScheme2": "Grayscale", "DE.Views.Toolbar.txtScheme20": "Urban", "DE.Views.Toolbar.txtScheme21": "Verve", "DE.Views.Toolbar.txtScheme22": "New Office", "DE.Views.Toolbar.txtScheme3": "Apex", "DE.Views.Toolbar.txtScheme4": "Aspect", "DE.Views.Toolbar.txtScheme5": "Civic", "DE.Views.Toolbar.txtScheme6": "Concourse", "DE.Views.Toolbar.txtScheme7": "Equity", "DE.Views.Toolbar.txtScheme8": "Flow", "DE.Views.Toolbar.txtScheme9": "Foundry", "DE.Views.ViewTab.textAlwaysShowToolbar": "Always Show Toolbar", "DE.Views.ViewTab.textDarkDocument": "Dark Document", "DE.Views.ViewTab.textFitToPage": "<PERSON><PERSON> To <PERSON>", "DE.Views.ViewTab.textFitToWidth": "Fit To <PERSON>th", "DE.Views.ViewTab.textInterfaceTheme": "Interface Theme", "DE.Views.ViewTab.textLeftMenu": "Left Panel", "DE.Views.ViewTab.textNavigation": "Navigation", "DE.Views.ViewTab.textOutline": "Headings", "DE.Views.ViewTab.textRightMenu": "Right Panel", "DE.Views.ViewTab.textRulers": "Rulers", "DE.Views.ViewTab.textStatusBar": "Status Bar", "DE.Views.ViewTab.textZoom": "Zoom", "DE.Views.ViewTab.tipDarkDocument": "Dark document", "DE.Views.ViewTab.tipFitToPage": "Fit to page", "DE.Views.ViewTab.tipFitToWidth": "Fit to width", "DE.Views.ViewTab.tipHeadings": "Headings", "DE.Views.ViewTab.tipInterfaceTheme": "Interface theme", "DE.Views.WatermarkSettingsDialog.textAuto": "Auto", "DE.Views.WatermarkSettingsDialog.textBold": "Bold", "DE.Views.WatermarkSettingsDialog.textColor": "Text color", "DE.Views.WatermarkSettingsDialog.textDiagonal": "Diagonal", "DE.Views.WatermarkSettingsDialog.textFont": "Font", "DE.Views.WatermarkSettingsDialog.textFromFile": "From file", "DE.Views.WatermarkSettingsDialog.textFromStorage": "From storage", "DE.Views.WatermarkSettingsDialog.textFromUrl": "From URL", "DE.Views.WatermarkSettingsDialog.textHor": "Horizontal", "DE.Views.WatermarkSettingsDialog.textImageW": "Image watermark", "DE.Views.WatermarkSettingsDialog.textItalic": "Italic", "DE.Views.WatermarkSettingsDialog.textLanguage": "Language", "DE.Views.WatermarkSettingsDialog.textLayout": "Layout", "DE.Views.WatermarkSettingsDialog.textNone": "None", "DE.Views.WatermarkSettingsDialog.textScale": "Scale", "DE.Views.WatermarkSettingsDialog.textSelect": "Select image", "DE.Views.WatermarkSettingsDialog.textStrikeout": "Strikethrough", "DE.Views.WatermarkSettingsDialog.textText": "Text", "DE.Views.WatermarkSettingsDialog.textTextW": "Text watermark", "DE.Views.WatermarkSettingsDialog.textTitle": "Watermark settings", "DE.Views.WatermarkSettingsDialog.textTransparency": "Semitransparent", "DE.Views.WatermarkSettingsDialog.textUnderline": "Underline", "DE.Views.WatermarkSettingsDialog.tipFontName": "Font name", "DE.Views.WatermarkSettingsDialog.tipFontSize": "Font size"}