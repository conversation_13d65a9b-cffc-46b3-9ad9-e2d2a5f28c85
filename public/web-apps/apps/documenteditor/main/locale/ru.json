{"Common.Controllers.Chat.notcriticalErrorTitle": "Предупреждение", "Common.Controllers.Chat.textEnterMessage": "Введите здесь своё сообщение", "Common.Controllers.ExternalDiagramEditor.textAnonymous": "Аноним", "Common.Controllers.ExternalDiagramEditor.textClose": "Закрыть", "Common.Controllers.ExternalDiagramEditor.warningText": "Объект недоступен, так как редактируется другим пользователем.", "Common.Controllers.ExternalDiagramEditor.warningTitle": "Предупреждение", "Common.Controllers.ExternalMergeEditor.textAnonymous": "Анонимный пользователь", "Common.Controllers.ExternalMergeEditor.textClose": "Закрыть", "Common.Controllers.ExternalMergeEditor.warningText": "Объект недоступен, так как редактируется другим пользователем.", "Common.Controllers.ExternalMergeEditor.warningTitle": "Внимание", "Common.Controllers.ExternalOleEditor.textAnonymous": "Аноним", "Common.Controllers.ExternalOleEditor.textClose": "Закрыть", "Common.Controllers.ExternalOleEditor.warningText": "Объект недоступен, так как редактируется другим пользователем.", "Common.Controllers.ExternalOleEditor.warningTitle": "Внимание", "Common.Controllers.History.notcriticalErrorTitle": "Внимание", "Common.Controllers.ReviewChanges.textAcceptBeforeCompare": "Для сравнения документов все отслеживаемые изменения в них будут считаться принятыми. Вы хотите продолжить?", "Common.Controllers.ReviewChanges.textAtLeast": "Мини<PERSON>ум", "Common.Controllers.ReviewChanges.textAuto": "Авто", "Common.Controllers.ReviewChanges.textBaseline": "Базовая линия", "Common.Controllers.ReviewChanges.textBold": "Полужирный", "Common.Controllers.ReviewChanges.textBreakBefore": "С новой страницы", "Common.Controllers.ReviewChanges.textCaps": "Все прописные", "Common.Controllers.ReviewChanges.textCenter": "Выравнивание по центру", "Common.Controllers.ReviewChanges.textChar": "По знакам", "Common.Controllers.ReviewChanges.textChart": "Диаграмма", "Common.Controllers.ReviewChanges.textColor": "Цвет шрифта", "Common.Controllers.ReviewChanges.textContextual": "Не добавлять интервал между абзацами одного стиля", "Common.Controllers.ReviewChanges.textDeleted": "<b>Удалено:</b>", "Common.Controllers.ReviewChanges.textDStrikeout": "Двойное зачёркивание", "Common.Controllers.ReviewChanges.textEquation": "Уравнение", "Common.Controllers.ReviewChanges.textExact": "Точно", "Common.Controllers.ReviewChanges.textFirstLine": "Первая строка", "Common.Controllers.ReviewChanges.textFontSize": "Размер шрифта", "Common.Controllers.ReviewChanges.textFormatted": "Отформатировано", "Common.Controllers.ReviewChanges.textHighlight": "Цвет выделения", "Common.Controllers.ReviewChanges.textImage": "Изображение", "Common.Controllers.ReviewChanges.textIndentLeft": "Отступ слева", "Common.Controllers.ReviewChanges.textIndentRight": "Отступ справа", "Common.Controllers.ReviewChanges.textInserted": "<b>Добавлено:</b>", "Common.Controllers.ReviewChanges.textItalic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Controllers.ReviewChanges.textJustify": "Выравнивание по ширине", "Common.Controllers.ReviewChanges.textKeepLines": "Не разрывать абзац", "Common.Controllers.ReviewChanges.textKeepNext": "Не отрывать от следующего", "Common.Controllers.ReviewChanges.textLeft": "Выравнивание по левому краю", "Common.Controllers.ReviewChanges.textLineSpacing": "Междустрочный интервал: ", "Common.Controllers.ReviewChanges.textMultiple": "Множитель", "Common.Controllers.ReviewChanges.textNoBreakBefore": "Не с новой страницы", "Common.Controllers.ReviewChanges.textNoContextual": "Добавлять интервал между абзацами одного стиля", "Common.Controllers.ReviewChanges.textNoKeepLines": "Разрешить разрывать абзац", "Common.Controllers.ReviewChanges.textNoKeepNext": "Разрешить отрывать от следующего", "Common.Controllers.ReviewChanges.textNot": "Не", "Common.Controllers.ReviewChanges.textNoWidow": "Без запрета висячих строк", "Common.Controllers.ReviewChanges.textNum": "Изменение нумерации", "Common.Controllers.ReviewChanges.textOff": "{0} больше не использует отслеживание изменений.", "Common.Controllers.ReviewChanges.textOffGlobal": "{0} отключил(а) отслеживание изменений для всех.", "Common.Controllers.ReviewChanges.textOn": "{0} использует отслеживание изменений.", "Common.Controllers.ReviewChanges.textOnGlobal": "{0} вклю<PERSON>ил(а) отслеживание изменений для всех.", "Common.Controllers.ReviewChanges.textParaDeleted": "<b>А<PERSON><PERSON><PERSON><PERSON> удален</b>", "Common.Controllers.ReviewChanges.textParaFormatted": "Абзац отформатирован", "Common.Controllers.ReviewChanges.textParaInserted": "<b>Абза<PERSON> добавлен</b>", "Common.Controllers.ReviewChanges.textParaMoveFromDown": "<b>Перемещено вниз:</b>", "Common.Controllers.ReviewChanges.textParaMoveFromUp": "<b>Перемещено вверх:</b>", "Common.Controllers.ReviewChanges.textParaMoveTo": "<b>Перемещено:</b>", "Common.Controllers.ReviewChanges.textPosition": "Положение", "Common.Controllers.ReviewChanges.textRight": "Выравнивание по правому краю", "Common.Controllers.ReviewChanges.textShape": "Фигура", "Common.Controllers.ReviewChanges.textShd": "Цвет фона", "Common.Controllers.ReviewChanges.textShow": "Показывать изменения:", "Common.Controllers.ReviewChanges.textSmallCaps": "Малые прописные", "Common.Controllers.ReviewChanges.textSpacing": "Интервал", "Common.Controllers.ReviewChanges.textSpacingAfter": "Интервал после абзаца", "Common.Controllers.ReviewChanges.textSpacingBefore": "Интервал перед абзацем", "Common.Controllers.ReviewChanges.textStrikeout": "Зачёркнутый", "Common.Controllers.ReviewChanges.textSubScript": "Подстрочные", "Common.Controllers.ReviewChanges.textSuperScript": "Надстрочные", "Common.Controllers.ReviewChanges.textTableChanged": "<b>Изменены настройки таблицы</b>", "Common.Controllers.ReviewChanges.textTableRowsAdd": "<b>Добавлены строки таблицы</b>", "Common.Controllers.ReviewChanges.textTableRowsDel": "<b>Удалены строки таблицы</b>", "Common.Controllers.ReviewChanges.textTabs": "Изменение табуляции", "Common.Controllers.ReviewChanges.textTitleComparison": "Параметры сравнения", "Common.Controllers.ReviewChanges.textUnderline": "Подчёркнутый", "Common.Controllers.ReviewChanges.textUrl": "Вставьте URL-адрес документа", "Common.Controllers.ReviewChanges.textWidow": "Запрет висячих строк", "Common.Controllers.ReviewChanges.textWord": "По словам", "Common.define.chartData.textArea": "С областями", "Common.define.chartData.textAreaStacked": "Диаграмма с областями с накоплением", "Common.define.chartData.textAreaStackedPer": "Нормированная с областями и накоплением", "Common.define.chartData.textBar": "Лин<PERSON>йчатая", "Common.define.chartData.textBarNormal": "Гистограмма с группировкой", "Common.define.chartData.textBarNormal3d": "Трехмерная гистограмма с группировкой", "Common.define.chartData.textBarNormal3dPerspective": "Трехмерная гистограмма", "Common.define.chartData.textBarStacked": "Гистограмма с накоплением", "Common.define.chartData.textBarStacked3d": "Трехмерная гистограмма с накоплением", "Common.define.chartData.textBarStackedPer": "Нормированная гистограмма с накоплением", "Common.define.chartData.textBarStackedPer3d": "Трехмерная нормированная гистограмма с накоплением", "Common.define.chartData.textCharts": "Диаграммы", "Common.define.chartData.textColumn": "Гистограмма", "Common.define.chartData.textCombo": "Комбинированные", "Common.define.chartData.textComboAreaBar": "С областями с накоплением и гистограмма с группировкой", "Common.define.chartData.textComboBarLine": "Гистограмма с группировкой и график", "Common.define.chartData.textComboBarLineSecondary": "Гистограмма с группировкой и график на вспомогательной оси", "Common.define.chartData.textComboCustom": "Пользовательская комбинация", "Common.define.chartData.textDoughnut": "Кольцевая диаграмма", "Common.define.chartData.textHBarNormal": "Линейчатая с группировкой", "Common.define.chartData.textHBarNormal3d": "Трехмерная линейчатая с группировкой", "Common.define.chartData.textHBarStacked": "Линейчатая с накоплением", "Common.define.chartData.textHBarStacked3d": "Трехмерная линейчатая с накоплением", "Common.define.chartData.textHBarStackedPer": "Нормированная линейчатая с накоплением", "Common.define.chartData.textHBarStackedPer3d": "Трехмерная нормированная линейчатая с накоплением", "Common.define.chartData.textLine": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textLine3d": "Трехмерный график", "Common.define.chartData.textLineMarker": "График с маркерами", "Common.define.chartData.textLineStacked": "График с накоплением", "Common.define.chartData.textLineStackedMarker": "График с накоплением с маркерами", "Common.define.chartData.textLineStackedPer": "Нормированный график с накоплением", "Common.define.chartData.textLineStackedPerMarker": "Нормированный график с маркерами и накоплением", "Common.define.chartData.textPie": "Круговая", "Common.define.chartData.textPie3d": "Трехмерная круговая диаграмма", "Common.define.chartData.textPoint": "Точечная", "Common.define.chartData.textScatter": "Точечная диаграмма", "Common.define.chartData.textScatterLine": "Точечная с прямыми отрезками", "Common.define.chartData.textScatterLineMarker": "Точечная с прямыми отрезками и маркерами", "Common.define.chartData.textScatterSmooth": "Точечная с гладкими кривыми", "Common.define.chartData.textScatterSmoothMarker": "Точечная с гладкими кривыми и маркерами", "Common.define.chartData.textStock": "Биржевая", "Common.define.chartData.textSurface": "Поверхность", "Common.define.smartArt.textAccentedPicture": "Акцентируемый рисунок", "Common.define.smartArt.textAccentProcess": "Процесс со смещением", "Common.define.smartArt.textAlternatingFlow": "Переменный поток", "Common.define.smartArt.textAlternatingHexagons": "Чередующиеся шестиугольники", "Common.define.smartArt.textAlternatingPictureBlocks": "Чередующиеся блоки рисунков", "Common.define.smartArt.textAlternatingPictureCircles": "Чередующиеся круги рисунков", "Common.define.smartArt.textArchitectureLayout": "Архитектурный макет", "Common.define.smartArt.textArrowRibbon": "Лента со стрелками", "Common.define.smartArt.textAscendingPictureAccentProcess": "Процесс со смещенными по возрастанию рисунками", "Common.define.smartArt.textBalance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textBasicBendingProcess": "Простой ломаный процесс", "Common.define.smartArt.textBasicBlockList": "Простой блочный список", "Common.define.smartArt.textBasicChevronProcess": "Простой уголковый процесс", "Common.define.smartArt.textBasicCycle": "Простой цикл", "Common.define.smartArt.textBasicMatrix": "Простая матрица", "Common.define.smartArt.textBasicPie": "Простая круговая", "Common.define.smartArt.textBasicProcess": "Простой процесс", "Common.define.smartArt.textBasicPyramid": "Простая пирамида", "Common.define.smartArt.textBasicRadial": "Простая радиальная", "Common.define.smartArt.textBasicTarget": "Простая целевая", "Common.define.smartArt.textBasicTimeline": "Простая временная шкала", "Common.define.smartArt.textBasicVenn": "Простая Венна", "Common.define.smartArt.textBendingPictureAccentList": "Ломаный список со смещенными рисунками", "Common.define.smartArt.textBendingPictureBlocks": "Нелинейные рисунки с блоками", "Common.define.smartArt.textBendingPictureCaption": "Нелинейные рисунки с подписями", "Common.define.smartArt.textBendingPictureCaptionList": "Ломаный список рисунков с подписями", "Common.define.smartArt.textBendingPictureSemiTranparentText": "Нелинейные рисунки с полупрозрачным текстом", "Common.define.smartArt.textBlockCycle": "Блочный цикл", "Common.define.smartArt.textBubblePictureList": "Список рисунков с выносками", "Common.define.smartArt.textCaptionedPictures": "Подписанные рисунки", "Common.define.smartArt.textChevronAccentProcess": "Уголковый процесс со смещением", "Common.define.smartArt.textChevronList": "Уголковый список", "Common.define.smartArt.textCircleAccentTimeline": "Круглая временная шкала", "Common.define.smartArt.textCircleArrowProcess": "Стрелка процесса с кругами", "Common.define.smartArt.textCirclePictureHierarchy": "Иерархия с круглыми рисунками", "Common.define.smartArt.textCircleProcess": "Процесс с кругами", "Common.define.smartArt.textCircleRelationship": "Круг связей", "Common.define.smartArt.textCircularBendingProcess": "Круглый ломаный процесс", "Common.define.smartArt.textCircularPictureCallout": "Выноска с круглыми рисунками", "Common.define.smartArt.textClosedChevronProcess": "Закрытый уголковый процесс", "Common.define.smartArt.textContinuousArrowProcess": "Стрелка непрерывного процесса", "Common.define.smartArt.textContinuousBlockProcess": "Непрерывный блочный процесс", "Common.define.smartArt.textContinuousCycle": "Непрерывный цикл", "Common.define.smartArt.textContinuousPictureList": "Непрерывный список с рисунками", "Common.define.smartArt.textConvergingArrows": "Сходящиеся стрелки", "Common.define.smartArt.textConvergingRadial": "Сходящаяся радиальная", "Common.define.smartArt.textConvergingText": "Сходящийся текст", "Common.define.smartArt.textCounterbalanceArrows": "Уравновешивающие стрелки", "Common.define.smartArt.textCycle": "<PERSON>и<PERSON><PERSON>", "Common.define.smartArt.textCycleMatrix": "Циклическая матрица", "Common.define.smartArt.textDescendingBlockList": "Нисходящий блочный список", "Common.define.smartArt.textDescendingProcess": "Убывающий процесс", "Common.define.smartArt.textDetailedProcess": "Подробный процесс", "Common.define.smartArt.textDivergingArrows": "Расходящиеся стрелки", "Common.define.smartArt.textDivergingRadial": "Расходящаяся радиальная", "Common.define.smartArt.textEquation": "Уравнение", "Common.define.smartArt.textFramedTextPicture": "Рисунок с текстом в рамке", "Common.define.smartArt.textFunnel": "Воронка", "Common.define.smartArt.textGear": "Шестеренка", "Common.define.smartArt.textGridMatrix": "Сетчатая матрица", "Common.define.smartArt.textGroupedList": "Сгруппированный список", "Common.define.smartArt.textHalfCircleOrganizationChart": "Полукруглая организационная диаграмма", "Common.define.smartArt.textHexagonCluster": "Кластер шестиугольников", "Common.define.smartArt.textHexagonRadial": "Радиальный шестиугольник", "Common.define.smartArt.textHierarchy": "Иерархия", "Common.define.smartArt.textHierarchyList": "Иерархический список", "Common.define.smartArt.textHorizontalBulletList": "Горизонтальный маркированный список", "Common.define.smartArt.textHorizontalHierarchy": "Горизонтальная иерархия", "Common.define.smartArt.textHorizontalLabeledHierarchy": "Горизонтальная иерархия с подписями", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "Горизонтальная многоуровневая иерархия", "Common.define.smartArt.textHorizontalOrganizationChart": "Горизонтальная организационная диаграмма", "Common.define.smartArt.textHorizontalPictureList": "Горизонтальный список рисунков", "Common.define.smartArt.textIncreasingArrowProcess": "Стрелка нарастающего процесса", "Common.define.smartArt.textIncreasingCircleProcess": "Нарастающий процесс с кругами", "Common.define.smartArt.textInterconnectedBlockProcess": "Процесс со взаимосвязанными блоками", "Common.define.smartArt.textInterconnectedRings": "Взаимосвязанные кольца", "Common.define.smartArt.textInvertedPyramid": "Инвертированная пирамида", "Common.define.smartArt.textLabeledHierarchy": "Иерархия с подписями", "Common.define.smartArt.textLinearVenn": "Линейная Венна", "Common.define.smartArt.textLinedList": "Список с линиями", "Common.define.smartArt.textList": "Список", "Common.define.smartArt.textMatrix": "Матрица", "Common.define.smartArt.textMultidirectionalCycle": "Разнонаправленный цикл", "Common.define.smartArt.textNameAndTitleOrganizationChart": "Организационная диаграмма с именами и должностями", "Common.define.smartArt.textNestedTarget": "Вложенная целевая", "Common.define.smartArt.textNondirectionalCycle": "Ненаправленный цикл", "Common.define.smartArt.textOpposingArrows": "Противостоящие стрелки", "Common.define.smartArt.textOpposingIdeas": "Противоположные идеи", "Common.define.smartArt.textOrganizationChart": "Организационная диаграмма", "Common.define.smartArt.textOther": "Другое", "Common.define.smartArt.textPhasedProcess": "Поэтапный процесс", "Common.define.smartArt.textPicture": "Рисунок", "Common.define.smartArt.textPictureAccentBlocks": "Блоки со смещенными рисунками", "Common.define.smartArt.textPictureAccentList": "Список со смещенными рисунками", "Common.define.smartArt.textPictureAccentProcess": "Процесс со смещенными рисунками", "Common.define.smartArt.textPictureCaptionList": "Список названий рисунков", "Common.define.smartArt.textPictureFrame": "Фоторамка", "Common.define.smartArt.textPictureGrid": "Сетка рисунков", "Common.define.smartArt.textPictureLineup": "Линия рисунков", "Common.define.smartArt.textPictureOrganizationChart": "Организационная диаграмма с рисунками", "Common.define.smartArt.textPictureStrips": "Полосы рисунков", "Common.define.smartArt.textPieProcess": "Процесс с круговой диаграммой", "Common.define.smartArt.textPlusAndMinus": "Плюс и минус", "Common.define.smartArt.textProcess": "Процесс", "Common.define.smartArt.textProcessArrows": "Стрелки процесса", "Common.define.smartArt.textProcessList": "Список процессов", "Common.define.smartArt.textPyramid": "Пирамида", "Common.define.smartArt.textPyramidList": "Пирамидальный список", "Common.define.smartArt.textRadialCluster": "Радиал<PERSON>ный кластер", "Common.define.smartArt.textRadialCycle": "Радиальная циклическая", "Common.define.smartArt.textRadialList": "Радиальный список", "Common.define.smartArt.textRadialPictureList": "Радиальный список рисунков", "Common.define.smartArt.textRadialVenn": "Радиальная Венна", "Common.define.smartArt.textRandomToResultProcess": "Процесс от случайности к результату", "Common.define.smartArt.textRelationship": "Связь", "Common.define.smartArt.textRepeatingBendingProcess": "Повторяющийся ломаный процесс", "Common.define.smartArt.textReverseList": "Обратный список", "Common.define.smartArt.textSegmentedCycle": "Сегментированный цикл", "Common.define.smartArt.textSegmentedProcess": "Сегментированный процесс", "Common.define.smartArt.textSegmentedPyramid": "Сегментированная пирамида", "Common.define.smartArt.textSnapshotPictureList": "Список со снимками", "Common.define.smartArt.textSpiralPicture": "Спираль рисунков", "Common.define.smartArt.textSquareAccentList": "Список с квадратиками", "Common.define.smartArt.textStackedList": "Список в столбик", "Common.define.smartArt.textStackedVenn": "Венна в столбик", "Common.define.smartArt.textStaggeredProcess": "Ступенчатый процесс", "Common.define.smartArt.textStepDownProcess": "Нисходящий процесс", "Common.define.smartArt.textStepUpProcess": "Восходящий процесс", "Common.define.smartArt.textSubStepProcess": "Процесс с вложенными шагами", "Common.define.smartArt.textTabbedArc": "Дуга с вкладками", "Common.define.smartArt.textTableHierarchy": "Табличная иерархия", "Common.define.smartArt.textTableList": "Табличный список", "Common.define.smartArt.textTabList": "Список вкладок", "Common.define.smartArt.textTargetList": "Целевой список", "Common.define.smartArt.textTextCycle": "Текстовый цикл", "Common.define.smartArt.textThemePictureAccent": "Смещенные рисунки темы", "Common.define.smartArt.textThemePictureAlternatingAccent": "Чередующиеся смещенные рисунки темы", "Common.define.smartArt.textThemePictureGrid": "Сетка рисунков темы", "Common.define.smartArt.textTitledMatrix": "Матрица с заголовками", "Common.define.smartArt.textTitledPictureAccentList": "Список со смещенными рисунками и заголовком", "Common.define.smartArt.textTitledPictureBlocks": "Блоки рисунков с названиями", "Common.define.smartArt.textTitlePictureLineup": "Линия рисунков с названиями", "Common.define.smartArt.textTrapezoidList": "Трапециевидный список", "Common.define.smartArt.textUpwardArrow": "Восходящая стрелка", "Common.define.smartArt.textVaryingWidthList": "Список переменной ширины", "Common.define.smartArt.textVerticalAccentList": "Вертикальный список со смещением", "Common.define.smartArt.textVerticalArrowList": "Вертикальный список со стрелкой", "Common.define.smartArt.textVerticalBendingProcess": "Вертикальный ломаный процесс", "Common.define.smartArt.textVerticalBlockList": "Вертикальный блочный список", "Common.define.smartArt.textVerticalBoxList": "Вертикальный список", "Common.define.smartArt.textVerticalBracketList": "Вертикальный список со скобками", "Common.define.smartArt.textVerticalBulletList": "Вертикальный маркированный список", "Common.define.smartArt.textVerticalChevronList": "Вертикальный уголковый список", "Common.define.smartArt.textVerticalCircleList": "Вертикальный список с кругами", "Common.define.smartArt.textVerticalCurvedList": "Вертикальный нелинейный список", "Common.define.smartArt.textVerticalEquation": "Вертикальное уравнение", "Common.define.smartArt.textVerticalPictureAccentList": "Вертикальный список со смещенными рисунками", "Common.define.smartArt.textVerticalPictureList": "Вертикальный список рисунков", "Common.define.smartArt.textVerticalProcess": "Вертикальный процесс", "Common.Translation.textMoreButton": "<PERSON><PERSON><PERSON>", "Common.Translation.tipFileLocked": "Документ заблокирован на редактирование. Вы можете внести изменения и сохранить его как локальную копию позже.", "Common.Translation.tipFileReadOnly": "Файл доступен только для чтения. Чтобы сохранить изменения, сохраните файл с новым названием или в другом месте.", "Common.Translation.warnFileLocked": "Вы не можете редактировать этот файл, потому что он уже редактируется в другом приложении.", "Common.Translation.warnFileLockedBtnEdit": "Создать копию", "Common.Translation.warnFileLockedBtnView": "Открыть на просмотр", "Common.UI.ButtonColored.textAutoColor": "Автоматический", "Common.UI.ButtonColored.textNewColor": "Пользовательский цвет", "Common.UI.Calendar.textApril": "Апрель", "Common.UI.Calendar.textAugust": "Август", "Common.UI.Calendar.textDecember": "Декабрь", "Common.UI.Calendar.textFebruary": "Февраль", "Common.UI.Calendar.textJanuary": "Январь", "Common.UI.Calendar.textJuly": "Июль", "Common.UI.Calendar.textJune": "Июнь", "Common.UI.Calendar.textMarch": "Ма<PERSON><PERSON>", "Common.UI.Calendar.textMay": "<PERSON><PERSON><PERSON>", "Common.UI.Calendar.textMonths": "Месяцы", "Common.UI.Calendar.textNovember": "Ноябрь", "Common.UI.Calendar.textOctober": "Октябрь", "Common.UI.Calendar.textSeptember": "Сентябрь", "Common.UI.Calendar.textShortApril": "<PERSON><PERSON><PERSON>", "Common.UI.Calendar.textShortAugust": "Авг", "Common.UI.Calendar.textShortDecember": "<PERSON><PERSON><PERSON>", "Common.UI.Calendar.textShortFebruary": "<PERSON><PERSON><PERSON>", "Common.UI.Calendar.textShortFriday": "Пт", "Common.UI.Calendar.textShortJanuary": "<PERSON><PERSON><PERSON>", "Common.UI.Calendar.textShortJuly": "<PERSON><PERSON><PERSON>", "Common.UI.Calendar.textShortJune": "<PERSON><PERSON><PERSON>", "Common.UI.Calendar.textShortMarch": "<PERSON><PERSON><PERSON>", "Common.UI.Calendar.textShortMay": "<PERSON><PERSON><PERSON>", "Common.UI.Calendar.textShortMonday": "Пн", "Common.UI.Calendar.textShortNovember": "Ноя", "Common.UI.Calendar.textShortOctober": "Окт", "Common.UI.Calendar.textShortSaturday": "Сб", "Common.UI.Calendar.textShortSeptember": "Сен", "Common.UI.Calendar.textShortSunday": "Вс", "Common.UI.Calendar.textShortThursday": "Чт", "Common.UI.Calendar.textShortTuesday": "Вт", "Common.UI.Calendar.textShortWednesday": "Ср", "Common.UI.Calendar.textYears": "Годы", "Common.UI.ComboBorderSize.txtNoBorders": "Без границ", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "Без границ", "Common.UI.ComboDataView.emptyComboText": "Без стилей", "Common.UI.ExtendedColorDialog.addButtonText": "Добавить", "Common.UI.ExtendedColorDialog.textCurrent": "Текущий", "Common.UI.ExtendedColorDialog.textHexErr": "Введено некорректное значение.<br>Пожалуйста, введите значение от 000000 до FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "Новый", "Common.UI.ExtendedColorDialog.textRGBErr": "Введено некорректное значение.<br>Пожалуйста, введите числовое значение от 0 до 255.", "Common.UI.HSBColorPicker.textNoColor": "Без цвета", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "Скрыть пароль", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "Показать пароль", "Common.UI.SearchBar.textFind": "Поиск", "Common.UI.SearchBar.tipCloseSearch": "Закрыть поиск", "Common.UI.SearchBar.tipNextResult": "Следующий результат", "Common.UI.SearchBar.tipOpenAdvancedSettings": "Открыть дополнительные параметры", "Common.UI.SearchBar.tipPreviousResult": "Предыдущий результат", "Common.UI.SearchDialog.textHighlight": "Выделить результаты", "Common.UI.SearchDialog.textMatchCase": "С учетом регистра", "Common.UI.SearchDialog.textReplaceDef": "Введите текст для замены", "Common.UI.SearchDialog.textSearchStart": "Введите здесь текст", "Common.UI.SearchDialog.textTitle": "Поиск и замена", "Common.UI.SearchDialog.textTitle2": "Поиск", "Common.UI.SearchDialog.textWholeWords": "Только слово целиком", "Common.UI.SearchDialog.txtBtnHideReplace": "Скрыть поле замены", "Common.UI.SearchDialog.txtBtnReplace": "Заменить", "Common.UI.SearchDialog.txtBtnReplaceAll": "Заменить все", "Common.UI.SynchronizeTip.textDontShow": "Больше не показывать это сообщение", "Common.UI.SynchronizeTip.textSynchronize": "Документ изменен другим пользователем.<br>Нажмите, чтобы сохранить свои изменения и загрузить обновления.", "Common.UI.ThemeColorPalette.textRecentColors": "Недавние цвета", "Common.UI.ThemeColorPalette.textStandartColors": "Стандартные цвета", "Common.UI.ThemeColorPalette.textThemeColors": "Цвета темы", "Common.UI.Themes.txtThemeClassicLight": "Классическая светлая", "Common.UI.Themes.txtThemeContrastDark": "Контрастная темная", "Common.UI.Themes.txtThemeDark": "Темная", "Common.UI.Themes.txtThemeLight": "Светлая", "Common.UI.Themes.txtThemeSystem": "Системная", "Common.UI.Window.cancelButtonText": "Отмена", "Common.UI.Window.closeButtonText": "Закрыть", "Common.UI.Window.noButtonText": "Нет", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "Подтверждение", "Common.UI.Window.textDontShow": "Больше не показывать это сообщение", "Common.UI.Window.textError": "Ошибка", "Common.UI.Window.textInformation": "Информация", "Common.UI.Window.textWarning": "Предупреждение", "Common.UI.Window.yesButtonText": "Да", "Common.Utils.Metric.txtCm": "см", "Common.Utils.Metric.txtPt": "пт", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Shift", "Common.Views.About.txtAddress": "адрес: ", "Common.Views.About.txtLicensee": "ЛИЦЕНЗИАТ", "Common.Views.About.txtLicensor": "ЛИЦЕНЗИАР", "Common.Views.About.txtMail": "email: ", "Common.Views.About.txtPoweredBy": "Разработано", "Common.Views.About.txtTel": "тел.: ", "Common.Views.About.txtVersion": "Версия ", "Common.Views.AutoCorrectDialog.textAdd": "Добавить", "Common.Views.AutoCorrectDialog.textApplyText": "Применять при вводе", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Автозамена текста", "Common.Views.AutoCorrectDialog.textAutoFormat": "Автоформат при вводе", "Common.Views.AutoCorrectDialog.textBulleted": "Стили маркированных списков", "Common.Views.AutoCorrectDialog.textBy": "На", "Common.Views.AutoCorrectDialog.textDelete": "Удалить", "Common.Views.AutoCorrectDialog.textDoubleSpaces": "Добавлять точку двойным пробелом", "Common.Views.AutoCorrectDialog.textFLCells": "Делать первые буквы ячеек таблиц прописными", "Common.Views.AutoCorrectDialog.textFLSentence": "Делать первые буквы предложений прописными", "Common.Views.AutoCorrectDialog.textHyperlink": "Адреса в Интернете и сетевые пути гиперссылками", "Common.Views.AutoCorrectDialog.textHyphens": "Дефисы (--) на тире (—)", "Common.Views.AutoCorrectDialog.textMathCorrect": "Автозамена математическими символами", "Common.Views.AutoCorrectDialog.textNumbered": "Стили нумерованных списков", "Common.Views.AutoCorrectDialog.textQuotes": "Прямые кавычки парными", "Common.Views.AutoCorrectDialog.textRecognized": "Распознанные функции", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "Следующие выражения являются распознанными математическими функциями. Они не будут автоматически выделяться курсивом.", "Common.Views.AutoCorrectDialog.textReplace": "Заменить", "Common.Views.AutoCorrectDialog.textReplaceText": "Заменять при вводе", "Common.Views.AutoCorrectDialog.textReplaceType": "Заменять текст при вводе", "Common.Views.AutoCorrectDialog.textReset": "Сброс", "Common.Views.AutoCorrectDialog.textResetAll": "Сбросить настройки", "Common.Views.AutoCorrectDialog.textRestore": "Восстановить", "Common.Views.AutoCorrectDialog.textTitle": "Автозамена", "Common.Views.AutoCorrectDialog.textWarnAddRec": "Распознанные функции должны содержать только прописные или строчные буквы от А до Я.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "Все добавленные вами выражения будут удалены, а удаленные восстановлены. Вы хотите продолжить?", "Common.Views.AutoCorrectDialog.warnReplace": "Элемент автозамены для %1 уже существует. Вы хотите заменить его?", "Common.Views.AutoCorrectDialog.warnReset": "Все добавленные вами автозамены будут удалены, а для измененных будут восстановлены исходные значения. Вы хотите продолжить?", "Common.Views.AutoCorrectDialog.warnRestore": "Элемент автозамены для %1 будет сброшен на исходное значение. Вы хотите продолжить?", "Common.Views.Chat.textSend": "Отправить", "Common.Views.Comments.mniAuthorAsc": "По автору от А до Я", "Common.Views.Comments.mniAuthorDesc": "По автору от Я до А", "Common.Views.Comments.mniDateAsc": "От старых к новым", "Common.Views.Comments.mniDateDesc": "От новых к старым", "Common.Views.Comments.mniFilterGroups": "Фильтровать по группе", "Common.Views.Comments.mniPositionAsc": "Сверху вниз", "Common.Views.Comments.mniPositionDesc": "Снизу вверх", "Common.Views.Comments.textAdd": "Добавить", "Common.Views.Comments.textAddComment": "Добавить", "Common.Views.Comments.textAddCommentToDoc": "Добавить комментарий к документу", "Common.Views.Comments.textAddReply": "Добавить ответ", "Common.Views.Comments.textAll": "Все", "Common.Views.Comments.textAnonym": "Гость", "Common.Views.Comments.textCancel": "Отмена", "Common.Views.Comments.textClose": "Закрыть", "Common.Views.Comments.textClosePanel": "Закрыть комментарии", "Common.Views.Comments.textComments": "Комментарии", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "Введите здесь свой комментарий", "Common.Views.Comments.textHintAddComment": "Добавить комментарий", "Common.Views.Comments.textOpenAgain": "Открыть снова", "Common.Views.Comments.textReply": "Ответить", "Common.Views.Comments.textResolve": "Решить", "Common.Views.Comments.textResolved": "Решено", "Common.Views.Comments.textSort": "Сортировать комментарии", "Common.Views.Comments.textViewResolved": "У вас нет прав для повторного открытия комментария", "Common.Views.Comments.txtEmpty": "В документе нет комментариев.", "Common.Views.CopyWarningDialog.textDontShow": "Больше не показывать это сообщение", "Common.Views.CopyWarningDialog.textMsg": "Операции копирования, вырезания и вставки можно выполнить с помощью кнопок на панели инструментов и команд контекстного меню только в этой вкладке редактора.<br><br>Для копирования в другие приложения и вставки из них используйте следующие сочетания клавиш:", "Common.Views.CopyWarningDialog.textTitle": "Операции копирования, вырезания и вставки", "Common.Views.CopyWarningDialog.textToCopy": "для копирования", "Common.Views.CopyWarningDialog.textToCut": "для вырезания", "Common.Views.CopyWarningDialog.textToPaste": "для вставки", "Common.Views.DocumentAccessDialog.textLoading": "Загрузка...", "Common.Views.DocumentAccessDialog.textTitle": "Настройки совместного доступа", "Common.Views.ExternalDiagramEditor.textTitle": "Редактор диаграмм", "Common.Views.ExternalEditor.textClose": "Закрыть", "Common.Views.ExternalEditor.textSave": "Сохранить и выйти", "Common.Views.ExternalMergeEditor.textTitle": "Получатели слияния", "Common.Views.ExternalOleEditor.textTitle": "Редактор таблиц", "Common.Views.Header.labelCoUsersDescr": "Пользователи, редактирующие документ:", "Common.Views.Header.textAddFavorite": "Добавить в избранное", "Common.Views.Header.textAdvSettings": "Дополнительные параметры", "Common.Views.Header.textBack": "Открыть расположение файла", "Common.Views.Header.textCompactView": "Скрыть панель инструментов", "Common.Views.Header.textHideLines": "Скрыть линейки", "Common.Views.Header.textHideStatusBar": "Скрыть строку состояния", "Common.Views.Header.textReadOnly": "Только чтение", "Common.Views.Header.textRemoveFavorite": "Удалить из избранного", "Common.Views.Header.textShare": "Доступ", "Common.Views.Header.textZoom": "Масш<PERSON><PERSON><PERSON>", "Common.Views.Header.tipAccessRights": "Управление правами доступа к документу", "Common.Views.Header.tipDownload": "Скачать файл", "Common.Views.Header.tipGoEdit": "Редактировать текущий файл", "Common.Views.Header.tipPrint": "Напечатать файл", "Common.Views.Header.tipPrintQuick": "Быстрая печать", "Common.Views.Header.tipRedo": "Повторить", "Common.Views.Header.tipSave": "Сохранить", "Common.Views.Header.tipSearch": "Поиск", "Common.Views.Header.tipUndo": "Отменить", "Common.Views.Header.tipUsers": "Просмотр пользователей", "Common.Views.Header.tipViewSettings": "Параметры вида", "Common.Views.Header.tipViewUsers": "Просмотр пользователей и управление правами доступа к документу", "Common.Views.Header.txtAccessRights": "Изменить права доступа", "Common.Views.Header.txtRename": "Переименовать", "Common.Views.History.textCloseHistory": "Закрыть историю", "Common.Views.History.textHide": "Свернуть", "Common.Views.History.textHideAll": "Скрыть подробные изменения", "Common.Views.History.textRestore": "Восстановить", "Common.Views.History.textShow": "Развернуть", "Common.Views.History.textShowAll": "Показать подробные изменения", "Common.Views.History.textVer": "вер.", "Common.Views.ImageFromUrlDialog.textUrl": "Вставьте URL изображения:", "Common.Views.ImageFromUrlDialog.txtEmpty": "Это поле обязательно для заполнения", "Common.Views.ImageFromUrlDialog.txtNotUrl": "Это поле должно быть URL-адресом в формате \"http://www.example.com\"", "Common.Views.InsertTableDialog.textInvalidRowsCols": "Необходимо указать допустимое количество строк и столбцов.", "Common.Views.InsertTableDialog.txtColumns": "Количество столбцов", "Common.Views.InsertTableDialog.txtMaxText": "Максимальное значение для этого поля - {0}.", "Common.Views.InsertTableDialog.txtMinText": "Минимальное значение для этого поля - {0}.", "Common.Views.InsertTableDialog.txtRows": "Количество строк", "Common.Views.InsertTableDialog.txtTitle": "Размер таблицы", "Common.Views.InsertTableDialog.txtTitleSplit": "Разделить ячейку", "Common.Views.LanguageDialog.labelSelect": "Выбрать язык документа", "Common.Views.OpenDialog.closeButtonText": "Закрыть файл", "Common.Views.OpenDialog.txtEncoding": "Кодировка", "Common.Views.OpenDialog.txtIncorrectPwd": "Указан неверный пароль.", "Common.Views.OpenDialog.txtOpenFile": "Введите пароль для открытия файла", "Common.Views.OpenDialog.txtPassword": "Пароль", "Common.Views.OpenDialog.txtPreview": "Просмотр", "Common.Views.OpenDialog.txtProtected": "Как только вы введете пароль и откроете файл, текущий пароль к файлу будет сброшен.", "Common.Views.OpenDialog.txtTitle": "Выбрать параметры %1", "Common.Views.OpenDialog.txtTitleProtected": "Защищенный файл", "Common.Views.PasswordDialog.txtDescription": "Задайте пароль, чтобы защитить этот документ", "Common.Views.PasswordDialog.txtIncorrectPwd": "Пароль и его подтверждение не совпадают", "Common.Views.PasswordDialog.txtPassword": "Пароль", "Common.Views.PasswordDialog.txtRepeat": "Повторить пароль", "Common.Views.PasswordDialog.txtTitle": "Установка пароля", "Common.Views.PasswordDialog.txtWarning": "Внимание: Если пароль забыт или утерян, его нельзя восстановить. Храните его в надежном месте.", "Common.Views.PluginDlg.textLoading": "Загрузка", "Common.Views.Plugins.groupCaption": "Плагины", "Common.Views.Plugins.strPlugins": "Плагины", "Common.Views.Plugins.textClosePanel": "Закрыть плагин", "Common.Views.Plugins.textLoading": "Загрузка", "Common.Views.Plugins.textStart": "Запустить", "Common.Views.Plugins.textStop": "Остановить", "Common.Views.Protection.hintAddPwd": "Зашифровать с помощью пароля", "Common.Views.Protection.hintDelPwd": "Удалить пароль", "Common.Views.Protection.hintPwd": "Изменить или удалить пароль", "Common.Views.Protection.hintSignature": "Добавить цифровую подпись или строку подписи", "Common.Views.Protection.txtAddPwd": "Добавить пароль", "Common.Views.Protection.txtChangePwd": "Изменить пароль", "Common.Views.Protection.txtDeletePwd": "Удалить пароль", "Common.Views.Protection.txtEncrypt": "Шифровать", "Common.Views.Protection.txtInvisibleSignature": "Добавить цифровую подпись", "Common.Views.Protection.txtSignature": "Подпись", "Common.Views.Protection.txtSignatureLine": "Добавить строку подписи", "Common.Views.RenameDialog.textName": "Имя файла", "Common.Views.RenameDialog.txtInvalidName": "Имя файла не должно содержать следующих символов: ", "Common.Views.ReviewChanges.hintNext": "К следующему изменению", "Common.Views.ReviewChanges.hintPrev": "К предыдущему изменению", "Common.Views.ReviewChanges.mniFromFile": "Документ из файла", "Common.Views.ReviewChanges.mniFromStorage": "Документ из хранилища", "Common.Views.ReviewChanges.mniFromUrl": "Документ с URL-адреса", "Common.Views.ReviewChanges.mniSettings": "Параметры сравнения", "Common.Views.ReviewChanges.strFast": "Быстрый", "Common.Views.ReviewChanges.strFastDesc": "Совместное редактирование в режиме реального времени. Все изменения сохраняются автоматически.", "Common.Views.ReviewChanges.strStrict": "Строгий", "Common.Views.ReviewChanges.strStrictDesc": "Используйте кнопку 'Сохранить' для синхронизации изменений, вносимых вами и другими пользователями.", "Common.Views.ReviewChanges.textEnable": "Включить", "Common.Views.ReviewChanges.textWarnTrackChanges": "Отслеживание изменений будет ВКЛЮЧЕНО для всех пользователей с полным доступом. При следующем открытии документа отслеживание изменений останется включенным.", "Common.Views.ReviewChanges.textWarnTrackChangesTitle": "Включить отслеживание изменений для всех?", "Common.Views.ReviewChanges.tipAcceptCurrent": "Принять текущее изменение", "Common.Views.ReviewChanges.tipCoAuthMode": "Задать режим совместного редактирования", "Common.Views.ReviewChanges.tipCommentRem": "Удалить комментарии", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Удалить текущие комментарии", "Common.Views.ReviewChanges.tipCommentResolve": "Решить комментарии", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Решить текущие комментарии", "Common.Views.ReviewChanges.tipCompare": "Сравнить текущий документ с другим", "Common.Views.ReviewChanges.tipHistory": "Показать историю версий", "Common.Views.ReviewChanges.tipRejectCurrent": "Отклонить текущее изменение", "Common.Views.ReviewChanges.tipReview": "Отслеживать изменения", "Common.Views.ReviewChanges.tipReviewView": "Выберите режим, в котором вы хотите отображать изменения", "Common.Views.ReviewChanges.tipSetDocLang": "Задать язык документа", "Common.Views.ReviewChanges.tipSetSpelling": "Проверка орфографии", "Common.Views.ReviewChanges.tipSharing": "Управление правами доступа к документу", "Common.Views.ReviewChanges.txtAccept": "Принять", "Common.Views.ReviewChanges.txtAcceptAll": "Принять все изменения", "Common.Views.ReviewChanges.txtAcceptChanges": "Принять изменения", "Common.Views.ReviewChanges.txtAcceptCurrent": "Принять текущее изменение", "Common.Views.ReviewChanges.txtChat": "Чат", "Common.Views.ReviewChanges.txtClose": "Закрыть", "Common.Views.ReviewChanges.txtCoAuthMode": "Режим совместного редактирования", "Common.Views.ReviewChanges.txtCommentRemAll": "Удалить все комментарии", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Удалить текущие комментарии", "Common.Views.ReviewChanges.txtCommentRemMy": "Удалить мои комментарии", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Удалить мои текущие комментарии", "Common.Views.ReviewChanges.txtCommentRemove": "Удалить", "Common.Views.ReviewChanges.txtCommentResolve": "Решить", "Common.Views.ReviewChanges.txtCommentResolveAll": "Решить все комментарии", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Решить текущие комментарии", "Common.Views.ReviewChanges.txtCommentResolveMy": "Решить мои комментарии", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Решить мои текущие комментарии", "Common.Views.ReviewChanges.txtCompare": "Сравнить", "Common.Views.ReviewChanges.txtDocLang": "Язык", "Common.Views.ReviewChanges.txtEditing": "Редактирование", "Common.Views.ReviewChanges.txtFinal": "Все изменения приняты {0}", "Common.Views.ReviewChanges.txtFinalCap": "Измененный документ", "Common.Views.ReviewChanges.txtHistory": "История версий", "Common.Views.ReviewChanges.txtMarkup": "Все изменения {0}", "Common.Views.ReviewChanges.txtMarkupCap": "Исправления и выноски", "Common.Views.ReviewChanges.txtMarkupSimple": "Все изменения {0}<br>Выноски выключены", "Common.Views.ReviewChanges.txtMarkupSimpleCap": "Только исправления", "Common.Views.ReviewChanges.txtNext": "К следующему", "Common.Views.ReviewChanges.txtOff": "ОТКЛ. для меня", "Common.Views.ReviewChanges.txtOffGlobal": "ОТКЛ. для меня и для всех", "Common.Views.ReviewChanges.txtOn": "ВКЛ. для меня", "Common.Views.ReviewChanges.txtOnGlobal": "ВКЛ. для меня и для всех", "Common.Views.ReviewChanges.txtOriginal": "Все изменения отклонены {0}", "Common.Views.ReviewChanges.txtOriginalCap": "Исходный документ", "Common.Views.ReviewChanges.txtPrev": "К предыдущему", "Common.Views.ReviewChanges.txtPreview": "Просмотр", "Common.Views.ReviewChanges.txtReject": "Отклонить", "Common.Views.ReviewChanges.txtRejectAll": "Отклонить все изменения", "Common.Views.ReviewChanges.txtRejectChanges": "Отклонить изменения", "Common.Views.ReviewChanges.txtRejectCurrent": "Отклонить текущее изменение", "Common.Views.ReviewChanges.txtSharing": "Совместный доступ", "Common.Views.ReviewChanges.txtSpelling": "Проверка орфографии", "Common.Views.ReviewChanges.txtTurnon": "Отслеживание изменений", "Common.Views.ReviewChanges.txtView": "Отображение", "Common.Views.ReviewChangesDialog.textTitle": "Просмотр изменений", "Common.Views.ReviewChangesDialog.txtAccept": "Принять", "Common.Views.ReviewChangesDialog.txtAcceptAll": "Принять все изменения", "Common.Views.ReviewChangesDialog.txtAcceptCurrent": "Принять текущее изменение", "Common.Views.ReviewChangesDialog.txtNext": "К следующему изменению", "Common.Views.ReviewChangesDialog.txtPrev": "К предыдущему изменению", "Common.Views.ReviewChangesDialog.txtReject": "Отклонить", "Common.Views.ReviewChangesDialog.txtRejectAll": "Отклонить все изменения", "Common.Views.ReviewChangesDialog.txtRejectCurrent": "Отклонить текущее изменение", "Common.Views.ReviewPopover.textAdd": "Добавить", "Common.Views.ReviewPopover.textAddReply": "Добавить ответ", "Common.Views.ReviewPopover.textCancel": "Отмена", "Common.Views.ReviewPopover.textClose": "Закрыть", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textEnterComment": "Введите здесь свой комментарий", "Common.Views.ReviewPopover.textFollowMove": "Перейти на прежнее место", "Common.Views.ReviewPopover.textMention": "+упоминание предоставит доступ к документу и отправит оповещение по почте", "Common.Views.ReviewPopover.textMentionNotify": "+упоминание отправит пользователю оповещение по почте", "Common.Views.ReviewPopover.textOpenAgain": "Открыть снова", "Common.Views.ReviewPopover.textReply": "Ответить", "Common.Views.ReviewPopover.textResolve": "Решить", "Common.Views.ReviewPopover.textViewResolved": "У вас нет прав для повторного открытия комментария", "Common.Views.ReviewPopover.txtAccept": "Принять", "Common.Views.ReviewPopover.txtDeleteTip": "Удалить", "Common.Views.ReviewPopover.txtEditTip": "Редактировать", "Common.Views.ReviewPopover.txtReject": "Отклонить", "Common.Views.SaveAsDlg.textLoading": "Загрузка", "Common.Views.SaveAsDlg.textTitle": "Папка для сохранения", "Common.Views.SearchPanel.textCaseSensitive": "С учетом регистра", "Common.Views.SearchPanel.textCloseSearch": "Закрыть поиск", "Common.Views.SearchPanel.textContentChanged": "Документ изменен.", "Common.Views.SearchPanel.textFind": "Поиск", "Common.Views.SearchPanel.textFindAndReplace": "Поиск и замена", "Common.Views.SearchPanel.textMatchUsingRegExp": "Сопоставление с использованием регулярных выражений", "Common.Views.SearchPanel.textNoMatches": "Совпадений нет", "Common.Views.SearchPanel.textNoSearchResults": "Ничего не найдено", "Common.Views.SearchPanel.textReplace": "Заменить", "Common.Views.SearchPanel.textReplaceAll": "Заменить все", "Common.Views.SearchPanel.textReplaceWith": "Заменить на", "Common.Views.SearchPanel.textSearchAgain": "{0}Выполнить новый поиск{1} для получения точных результатов.", "Common.Views.SearchPanel.textSearchHasStopped": "Поиск остановлен", "Common.Views.SearchPanel.textSearchResults": "Результаты поиска: {0}/{1}", "Common.Views.SearchPanel.textTooManyResults": "Слишком много результатов, чтобы отобразить их здесь", "Common.Views.SearchPanel.textWholeWords": "Только слово целиком", "Common.Views.SearchPanel.tipNextResult": "Следующий результат", "Common.Views.SearchPanel.tipPreviousResult": "Предыдущий результат", "Common.Views.SelectFileDlg.textLoading": "Загрузка", "Common.Views.SelectFileDlg.textTitle": "Выбрать источник данных", "Common.Views.SignDialog.textBold": "Полужирный", "Common.Views.SignDialog.textCertificate": "Сертификат", "Common.Views.SignDialog.textChange": "Изменить", "Common.Views.SignDialog.textInputName": "Введите имя подписывающего", "Common.Views.SignDialog.textItalic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textNameError": "Имя подписывающего не должно быть пустым.", "Common.Views.SignDialog.textPurpose": "Цель подписания документа", "Common.Views.SignDialog.textSelect": "Выбрать", "Common.Views.SignDialog.textSelectImage": "Выбрать изображение", "Common.Views.SignDialog.textSignature": "Как выглядит подпись:", "Common.Views.SignDialog.textTitle": "Подписание документа", "Common.Views.SignDialog.textUseImage": "или нажмите 'Выбрать изображение', чтобы использовать изображение в качестве подписи", "Common.Views.SignDialog.textValid": "Действителен с %1 по %2", "Common.Views.SignDialog.tipFontName": "<PERSON>ри<PERSON><PERSON>", "Common.Views.SignDialog.tipFontSize": "Размер шрифта", "Common.Views.SignSettingsDialog.textAllowComment": "Разрешить подписывающему добавлять примечания в окне подписи", "Common.Views.SignSettingsDialog.textDefInstruction": "Перед подписанием документа убедитесь, что подписываемое содержимое является правильным.", "Common.Views.SignSettingsDialog.textInfoEmail": "Адрес электронной почты предложенного подписывающего", "Common.Views.SignSettingsDialog.textInfoName": "Предложенный подписывающий", "Common.Views.SignSettingsDialog.textInfoTitle": "Должность предложенного подписывающего", "Common.Views.SignSettingsDialog.textInstructions": "Инструкции для подписывающего", "Common.Views.SignSettingsDialog.textShowDate": "Показывать дату подписи в строке подписи", "Common.Views.SignSettingsDialog.textTitle": "Настройка подписи", "Common.Views.SignSettingsDialog.txtEmpty": "Это поле необходимо заполнить", "Common.Views.SymbolTableDialog.textCharacter": "Символ", "Common.Views.SymbolTableDialog.textCode": "Код знака из Юникод (шестн.)", "Common.Views.SymbolTableDialog.textCopyright": "Знак авторского права", "Common.Views.SymbolTableDialog.textDCQuote": "Закрывающая двойная кавычка", "Common.Views.SymbolTableDialog.textDOQuote": "Открывающая двойная кавычка", "Common.Views.SymbolTableDialog.textEllipsis": "Горизонтальное многоточие", "Common.Views.SymbolTableDialog.textEmDash": "Длинное тире", "Common.Views.SymbolTableDialog.textEmSpace": "Длинный пробел", "Common.Views.SymbolTableDialog.textEnDash": "Короткое тире", "Common.Views.SymbolTableDialog.textEnSpace": "Короткий пробел", "Common.Views.SymbolTableDialog.textFont": "<PERSON>ри<PERSON><PERSON>", "Common.Views.SymbolTableDialog.textNBHyphen": "Неразрывный дефис", "Common.Views.SymbolTableDialog.textNBSpace": "Неразрывный пробел", "Common.Views.SymbolTableDialog.textPilcrow": "Знак абзаца", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 пробела", "Common.Views.SymbolTableDialog.textRange": "Набор", "Common.Views.SymbolTableDialog.textRecent": "Ранее использовавшиеся символы", "Common.Views.SymbolTableDialog.textRegistered": "Зарегистрированный товарный знак", "Common.Views.SymbolTableDialog.textSCQuote": "Закрывающая одинарная кавычка", "Common.Views.SymbolTableDialog.textSection": "Знак раздела", "Common.Views.SymbolTableDialog.textShortcut": "Сочетание клавиш", "Common.Views.SymbolTableDialog.textSHyphen": "Мягкий дефис", "Common.Views.SymbolTableDialog.textSOQuote": "Открывающая одинарная кавычка", "Common.Views.SymbolTableDialog.textSpecial": "Специальные символы", "Common.Views.SymbolTableDialog.textSymbols": "Символы", "Common.Views.SymbolTableDialog.textTitle": "Символ", "Common.Views.SymbolTableDialog.textTradeMark": "Символ товарного знака", "Common.Views.UserNameDialog.textDontShow": "Больше не спрашивать", "Common.Views.UserNameDialog.textLabel": "Подпись:", "Common.Views.UserNameDialog.textLabelError": "Подпись не должна быть пустой.", "DE.Controllers.DocProtection.txtIsProtectedComment": "Документ защищен. Вы можете только добавлять комментарии к этому документу.", "DE.Controllers.DocProtection.txtIsProtectedForms": "Документ защищен. Вы можете только заполнять формы в этом документе.", "DE.Controllers.DocProtection.txtIsProtectedTrack": "Документ защищен. Вы можете редактировать этот документ, но все изменения будут отслеживаться.", "DE.Controllers.DocProtection.txtIsProtectedView": "Документ защищен. Вы можете только просматривать этот документ.", "DE.Controllers.DocProtection.txtWasProtectedComment": "Документ был защищен другим пользователем.\nВы можете только добавлять комментарии к этому документу.", "DE.Controllers.DocProtection.txtWasProtectedForms": "Документ был защищен другим пользователем.\nВы можете только заполнять формы в этом документе.", "DE.Controllers.DocProtection.txtWasProtectedTrack": "Документ был защищен другим пользователем.\nВы можете редактировать этот документ, но все изменения будут отслеживаться.", "DE.Controllers.DocProtection.txtWasProtectedView": "Документ был защищен другим пользователем.\nВы можете только просматривать этот документ.", "DE.Controllers.DocProtection.txtWasUnprotected": "Защита документа снята.", "DE.Controllers.LeftMenu.leavePageText": "Все несохраненные изменения в этом документе будут потеряны.<br> Нажмите кнопку \"Отмена\", а затем нажмите кнопку \"Сохранить\", чтобы сохранить их. Нажмите кнопку \"OK\", чтобы сбросить все несохраненные изменения.", "DE.Controllers.LeftMenu.newDocumentTitle": "Документ без имени", "DE.Controllers.LeftMenu.notcriticalErrorTitle": "Внимание", "DE.Controllers.LeftMenu.requestEditRightsText": "Запрос прав на редактирование...", "DE.Controllers.LeftMenu.textLoadHistory": "Загрузка истории версий...", "DE.Controllers.LeftMenu.textNoTextFound": "Искомые данные не найдены. Пожалуйста, измените параметры поиска.", "DE.Controllers.LeftMenu.textReplaceSkipped": "Замена выполнена. Пропущено вхождений - {0}.", "DE.Controllers.LeftMenu.textReplaceSuccess": "Поиск выполнен. Заменено вхождений: {0}", "DE.Controllers.LeftMenu.txtCompatible": "Документ будет сохранен в новый формат. Это позволит использовать все функции редактора, но может повлиять на структуру документа.<br>Используйте опцию 'Совместимость' в дополнительных параметрах, если хотите сделать файлы совместимыми с более старыми версиями MS Word.", "DE.Controllers.LeftMenu.txtUntitled": "Без имени", "DE.Controllers.LeftMenu.warnDownloadAs": "Если Вы продолжите сохранение в этот формат, вcя функциональность, кроме текста, будет потеряна.<br>Вы действительно хотите продолжить?", "DE.Controllers.LeftMenu.warnDownloadAsPdf": "{0} будет сконвертирован в редактируемый формат. Это может занять некоторое время. Получившийся в результате документ будет оптимизирован для редактирования текста, поэтому он может отличаться от исходного {0}, особенно если исходный файл содержит много графических элементов.", "DE.Controllers.LeftMenu.warnDownloadAsRTF": "Если вы продолжите сохранение в этот формат, часть форматирования может быть потеряна.<br>Вы действительно хотите продолжить?", "DE.Controllers.LeftMenu.warnReplaceString": "{0} нельзя использовать как специальный символ в поле замены.", "DE.Controllers.Main.applyChangesTextText": "Загрузка изменений...", "DE.Controllers.Main.applyChangesTitleText": "Загрузка изменений", "DE.Controllers.Main.confirmMaxChangesSize": "Размер внесенных изменений превышает ограничение, установленное для вашего сервера.<br>Нажмите \"Отменить\" для отмены последнего действия или нажмите \"Продолжить\", чтобы сохранить действие локально (потребуется скачать файл или скопировать его содержимое чтобы ничего не потерялось).", "DE.Controllers.Main.convertationTimeoutText": "Превышено время ожидания конвертации.", "DE.Controllers.Main.criticalErrorExtText": "Нажмите \"OK\", чтобы вернуться к списку документов.", "DE.Controllers.Main.criticalErrorTitle": "Ошибка", "DE.Controllers.Main.downloadErrorText": "Загрузка не удалась.", "DE.Controllers.Main.downloadMergeText": "Загрузка...", "DE.Controllers.Main.downloadMergeTitle": "Загрузка", "DE.Controllers.Main.downloadTextText": "Загрузка документа...", "DE.Controllers.Main.downloadTitleText": "Загрузка документа", "DE.Controllers.Main.errorAccessDeny": "Вы пытаетесь выполнить действие, на которое у вас нет прав.<br>Пожалуйста, обратитесь к администратору Сервера документов.", "DE.Controllers.Main.errorBadImageUrl": "Неправильный URL-адрес изображения", "DE.Controllers.Main.errorCannotPasteImg": "Не удается вставить это изображение из буфера обмена, но вы можете сохранить его на устройстве и вставить оттуда или вы можете скопировать изображение без текста и вставить его в документ.", "DE.Controllers.Main.errorCoAuthoringDisconnect": "Потеряно соединение с сервером. В данный момент нельзя отредактировать документ.", "DE.Controllers.Main.errorComboSeries": "Для создания комбинированной диаграммы выберите не менее двух рядов данных.", "DE.Controllers.Main.errorCompare": "Функция сравнения документов недоступна в режиме совместного редактирования.", "DE.Controllers.Main.errorConnectToServer": "Не удается сохранить документ. Проверьте параметры подключения или обратитесь к вашему администратору.<br>Когда вы нажмете на кнопку 'OK', вам будет предложено скачать документ.", "DE.Controllers.Main.errorDatabaseConnection": "Внешняя ошибка.<br>Ош<PERSON>бка подключения к базе данных. Если ошибка повторяется, пожалуйста, обратитесь в службу поддержки.", "DE.Controllers.Main.errorDataEncrypted": "Получены зашифрованные изменения, их нельзя расшифровать.", "DE.Controllers.Main.errorDataRange": "Некорректный диапазон данных.", "DE.Controllers.Main.errorDefaultMessage": "Код ошибки: %1", "DE.Controllers.Main.errorDirectUrl": "Проверьте ссылку на документ.<br>Эта ссылка должна быть прямой ссылкой для скачивания файла.", "DE.Controllers.Main.errorEditingDownloadas": "В ходе работы с документом произошла ошибка.<br>Используйте опцию 'Скачать как', чтобы сохранить резервную копию файла на диск.", "DE.Controllers.Main.errorEditingSaveas": "В ходе работы с документом произошла ошибка.<br>Используйте опцию 'Сохранить как...', чтобы сохранить резервную копию файла на диск.", "DE.Controllers.Main.errorEmailClient": "Не найден почтовый клиент", "DE.Controllers.Main.errorEmptyTOC": "Начните создавать оглавление, применив к выделенному тексту стиль заголовка из галереи Стилей.", "DE.Controllers.Main.errorFilePassProtect": "Файл защищен паролем и не может быть открыт.", "DE.Controllers.Main.errorFileSizeExceed": "Размер файла превышает ограничение, установленное для вашего сервера.<br>Обратитесь к администратору Сервера документов для получения дополнительной информации.", "DE.Controllers.Main.errorForceSave": "При сохранении файла произошла ошибка. Используйте опцию 'Скачать как', чтобы сохранить файл на диск или повторите попытку позже.", "DE.Controllers.Main.errorInconsistentExt": "При открытии файла произошла ошибка.<br>Содержимое файла не соответствует расширению файла.", "DE.Controllers.Main.errorInconsistentExtDocx": "При открытии файла произошла ошибка.<br>Содержимое файла соответствует документам (например, docx), но файл имеет несоответствующее расширение: %1.", "DE.Controllers.Main.errorInconsistentExtPdf": "При открытии файла произошла ошибка.<br>Содержимое файла соответствует одному из следующих форматов: pdf/djvu/xps/oxps, но файл имеет несоответствующее расширение: %1.", "DE.Controllers.Main.errorInconsistentExtPptx": "При открытии файла произошла ошибка.<br>Содержимое файла соответствует презентациям (например, pptx), но файл имеет несоответствующее расширение: %1.", "DE.Controllers.Main.errorInconsistentExtXlsx": "При открытии файла произошла ошибка.<br>Содержимое файла соответствует электронным таблицам (например, xlsx), но файл имеет несоответствующее расширение: %1.", "DE.Controllers.Main.errorKeyEncrypt": "Неизвестный дескриптор ключа", "DE.Controllers.Main.errorKeyExpire": "Срок действия дескриптора ключа истек", "DE.Controllers.Main.errorLoadingFont": "Шрифты не загружены.<br>Пож<PERSON><PERSON><PERSON><PERSON>ста, обратитесь к администратору Сервера документов.", "DE.Controllers.Main.errorMailMergeLoadFile": "Загрузка документа не удалась. Выберите другой файл.", "DE.Controllers.Main.errorMailMergeSaveFile": "Не удалось выполнить слияние.", "DE.Controllers.Main.errorNoTOC": "Нет оглавления, которое нужно обновить. Вы можете вставить его на вкладке Ссылки.", "DE.Controllers.Main.errorPasswordIsNotCorrect": "Неверный пароль.<br>Убедите<PERSON><PERSON>, что отключена клавиша CAPS LOCK и используется правильный регистр.", "DE.Controllers.Main.errorProcessSaveResult": "Сбой при сохранении.", "DE.Controllers.Main.errorServerVersion": "Версия редактора была обновлена. Страница будет перезагружена, чтобы применить изменения.", "DE.Controllers.Main.errorSessionAbsolute": "Время сеанса редактирования документа истекло. Пожалуйста, обновите страницу.", "DE.Controllers.Main.errorSessionIdle": "Документ долгое время не редактировался. Пожалуйста, обновите страницу.", "DE.Controllers.Main.errorSessionToken": "Подключение к серверу было прервано. Пожалуйста, обновите страницу.", "DE.Controllers.Main.errorSetPassword": "Не удалось задать пароль.", "DE.Controllers.Main.errorStockChart": "Неверный порядок строк. Чтобы создать биржевую диаграмму, расположите данные на листе в следующем порядке:<br> цена открытия, максимальная цена, минимальная цена, цена закрытия.", "DE.Controllers.Main.errorSubmit": "Не удалось отправить.", "DE.Controllers.Main.errorTextFormWrongFormat": "Введенное значение не соответствует формату поля.", "DE.Controllers.Main.errorToken": "Токен безопасности документа имеет неправильный формат.<br>Пожалуйста, обратитесь к администратору Сервера документов.", "DE.Controllers.Main.errorTokenExpire": "Истек срок действия токена безопасности документа.<br>Пожалуйста, обратитесь к администратору Сервера документов.", "DE.Controllers.Main.errorUpdateVersion": "Версия файла была изменена. Страница будет перезагружена.", "DE.Controllers.Main.errorUpdateVersionOnDisconnect": "Соединение было восстановлено, и версия файла изменилась.<br>Прежде чем продолжить работу, надо скачать файл или скопировать его содержимое, чтобы обеспечить сохранность данных, а затем перезагрузить страницу.", "DE.Controllers.Main.errorUserDrop": "В настоящий момент файл недоступен.", "DE.Controllers.Main.errorUsersExceed": "Превышено количество пользователей, разрешенных согласно тарифному плану", "DE.Controllers.Main.errorViewerDisconnect": "Подключение прервано. Вы по-прежнему можете просматривать документ,<br>но не сможете скачать или напечатать его до восстановления подключения и обновления страницы.", "DE.Controllers.Main.leavePageText": "Документ содержит несохраненные изменения. Чтобы сохранить их, нажмите \"Остаться на этой странице\", затем \"Сохранить\". Нажмите \"Покинуть эту страницу\", чтобы сбросить все несохраненные изменения.", "DE.Controllers.Main.leavePageTextOnClose": "Все несохраненные изменения в этом документе будут потеряны.<br> Нажмите кнопку \"Отмена\", а затем нажмите кнопку \"Сохранить\", чтобы сохранить их. Нажмите кнопку \"OK\", чтобы сбросить все несохраненные изменения.", "DE.Controllers.Main.loadFontsTextText": "Загрузка данных...", "DE.Controllers.Main.loadFontsTitleText": "Загрузка данных", "DE.Controllers.Main.loadFontTextText": "Загрузка данных...", "DE.Controllers.Main.loadFontTitleText": "Загрузка данных", "DE.Controllers.Main.loadImagesTextText": "Загрузка изображений...", "DE.Controllers.Main.loadImagesTitleText": "Загрузка изображений", "DE.Controllers.Main.loadImageTextText": "Загрузка изображения...", "DE.Controllers.Main.loadImageTitleText": "Загрузка изображения", "DE.Controllers.Main.loadingDocumentTextText": "Загрузка документа...", "DE.Controllers.Main.loadingDocumentTitleText": "Загрузка документа", "DE.Controllers.Main.mailMergeLoadFileText": "Загрузка источника данных...", "DE.Controllers.Main.mailMergeLoadFileTitle": "Загрузка источника данных", "DE.Controllers.Main.notcriticalErrorTitle": "Предупреждение", "DE.Controllers.Main.openErrorText": "При открытии файла произошла ошибка.", "DE.Controllers.Main.openTextText": "Открытие документа...", "DE.Controllers.Main.openTitleText": "Открытие документа", "DE.Controllers.Main.printTextText": "Печать документа...", "DE.Controllers.Main.printTitleText": "Печать документа", "DE.Controllers.Main.reloadButtonText": "Обновить страницу", "DE.Controllers.Main.requestEditFailedMessageText": "В настоящее время документ редактируется. Пожалуйста, попробуйте позже.", "DE.Controllers.Main.requestEditFailedTitleText": "Доступ запрещён", "DE.Controllers.Main.saveErrorText": "При сохранении файла произошла ошибка.", "DE.Controllers.Main.saveErrorTextDesktop": "Нельзя сохранить или создать этот файл.<br>Возможные причины: <br>1. Файл доступен только для чтения. <br>2. Файл редактируется другими пользователями. <br>3. Диск заполнен или поврежден.", "DE.Controllers.Main.saveTextText": "Сохранение документа...", "DE.Controllers.Main.saveTitleText": "Сохранение документа", "DE.Controllers.Main.scriptLoadError": "Слишком медленное подключение, некоторые компоненты не удалось загрузить. Пожалуйста, обновите страницу.", "DE.Controllers.Main.sendMergeText": "Отправка результатов слияния...", "DE.Controllers.Main.sendMergeTitle": "Отправка результатов слияния", "DE.Controllers.Main.splitDividerErrorText": "Число строк должно являться делителем для %1.", "DE.Controllers.Main.splitMaxColsErrorText": "Число столбцов должно быть меньше, чем %1.", "DE.Controllers.Main.splitMaxRowsErrorText": "Число строк должно быть меньше, чем %1.", "DE.Controllers.Main.textAnonymous": "Аноним", "DE.Controllers.Main.textAnyone": "Любой", "DE.Controllers.Main.textApplyAll": "Применить ко всем уравнениям", "DE.Controllers.Main.textBuyNow": "Перейти на сайт", "DE.Controllers.Main.textChangesSaved": "Все изменения сохранены", "DE.Controllers.Main.textClose": "Закрыть", "DE.Controllers.Main.textCloseTip": "Щелкните, чтобы закрыть эту подсказку", "DE.Controllers.Main.textContactUs": "Связаться с отделом продаж", "DE.Controllers.Main.textContinue": "Продолжить", "DE.Controllers.Main.textConvertEquation": "Это уравнение создано в старой версии редактора уравнений, которая больше не поддерживается. Чтобы изменить это уравнение, его необходимо преобразовать в формат Office Math ML.<br>Преобразовать сейчас?", "DE.Controllers.Main.textCustomLoader": "Обратите внимание, что по условиям лицензии у вас нет прав изменять экран, отображаемый при загрузке.<br>Пожалуйста, обратитесь в наш отдел продаж, чтобы сделать запрос.", "DE.Controllers.Main.textDisconnect": "Соединение потеряно", "DE.Controllers.Main.textGuest": "Гость", "DE.Controllers.Main.textHasMacros": "Файл содержит автозапускаемые макросы.<br>Хотите запустить макросы?", "DE.Controllers.Main.textLearnMore": "Подробнее", "DE.Controllers.Main.textLoadingDocument": "Загрузка документа", "DE.Controllers.Main.textLongName": "Введите имя длиной менее 128 символов.", "DE.Controllers.Main.textNoLicenseTitle": "Лицензионное ограничение", "DE.Controllers.Main.textPaidFeature": "Платная функция", "DE.Controllers.Main.textReconnect": "Соединение восстановлено", "DE.Controllers.Main.textRemember": "Запомнить мой выбор для всех файлов", "DE.Controllers.Main.textRememberMacros": "Запомнить мой выбор для всех макросов", "DE.Controllers.Main.textRenameError": "Имя пользователя не должно быть пустым.", "DE.Controllers.Main.textRenameLabel": "Введите имя, которое будет использоваться для совместной работы", "DE.Controllers.Main.textRequestMacros": "Макрос делает запрос на URL. Вы хотите разрешить запрос на %1?", "DE.Controllers.Main.textShape": "Фигура", "DE.Controllers.Main.textStrict": "Строгий режим", "DE.Controllers.Main.textText": "Текст", "DE.Controllers.Main.textTryQuickPrint": "Вы выбрали быструю печать: весь документ будет напечатан на последнем выбранном принтере или на принтере по умолчанию.<br>Вы хотите продолжить?", "DE.Controllers.Main.textTryUndoRedo": "Функции отмены и повтора действий отключены в Быстром режиме совместного редактирования.<br>Нажмите на кнопку 'Строгий режим' для переключения в Строгий режим совместного редактирования, чтобы редактировать файл без вмешательства других пользователей и отправлять изменения только после того, как вы их сохраните. Переключаться между режимами совместного редактирования можно с помощью Дополнительных параметров редактора.", "DE.Controllers.Main.textTryUndoRedoWarn": "Функции отмены и повтора действий отключены в Быстром режиме совместного редактирования.", "DE.Controllers.Main.textUndo": "Отменить", "DE.Controllers.Main.titleLicenseExp": "Истек срок действия лицензии", "DE.Controllers.Main.titleServerVersion": "Редактор обновлен", "DE.Controllers.Main.titleUpdateVersion": "Версия изменилась", "DE.Controllers.Main.txtAbove": "выше", "DE.Controllers.Main.txtArt": "Введите ваш текст", "DE.Controllers.Main.txtBasicShapes": "Основные фигуры", "DE.Controllers.Main.txtBelow": "ниже", "DE.Controllers.Main.txtBookmarkError": "Ошибка! Закладка не определена.", "DE.Controllers.Main.txtButtons": "Кнопки", "DE.Controllers.Main.txtCallouts": "Выноски", "DE.Controllers.Main.txtCharts": "Схемы", "DE.Controllers.Main.txtChoose": "Выберите элемент", "DE.Controllers.Main.txtClickToLoad": "Нажмите для загрузки изображения", "DE.Controllers.Main.txtCurrentDocument": "Текущий документ", "DE.Controllers.Main.txtDiagramTitle": "Заголовок диаграммы", "DE.Controllers.Main.txtEditingMode": "Установка режима редактирования...", "DE.Controllers.Main.txtEndOfFormula": "Непредвиденное завершение формулы", "DE.Controllers.Main.txtEnterDate": "Введите дату", "DE.Controllers.Main.txtErrorLoadHistory": "Не удалось загрузить историю", "DE.Controllers.Main.txtEvenPage": "Четная страница", "DE.Controllers.Main.txtFiguredArrows": "Фигурные стрелки", "DE.Controllers.Main.txtFirstPage": "Первая страница", "DE.Controllers.Main.txtFooter": "Нижний колонтитул", "DE.Controllers.Main.txtFormulaNotInTable": "Формула не в таблице", "DE.Controllers.Main.txtHeader": "Верхний колонтитул", "DE.Controllers.Main.txtHyperlink": "Гиперссылка", "DE.Controllers.Main.txtIndTooLarge": "Индекс слишком большой", "DE.Controllers.Main.txtLines": "Линии", "DE.Controllers.Main.txtMainDocOnly": "Ошибка! Только основной документ.", "DE.Controllers.Main.txtMath": "Математические знаки", "DE.Controllers.Main.txtMissArg": "Отсутствует аргумент", "DE.Controllers.Main.txtMissOperator": "Отсутствует оператор", "DE.Controllers.Main.txtNeedSynchronize": "Есть обновления", "DE.Controllers.Main.txtNone": "Нет", "DE.Controllers.Main.txtNoTableOfContents": "В документе нет заголовков. Примените стиль заголовка к тексту, чтобы он появился в оглавлении.", "DE.Controllers.Main.txtNoTableOfFigures": "Элементы списка иллюстраций не найдены.", "DE.Controllers.Main.txtNoText": "Ошибка! В документе отсутствует текст указанного стиля.", "DE.Controllers.Main.txtNotInTable": "Не в таблице", "DE.Controllers.Main.txtNotValidBookmark": "Ошибка! Неверная ссылка закладки.", "DE.Controllers.Main.txtOddPage": "Нечетная страница", "DE.Controllers.Main.txtOnPage": "на странице", "DE.Controllers.Main.txtRectangles": "Прямоугольники", "DE.Controllers.Main.txtSameAsPrev": "Как в предыдущем", "DE.Controllers.Main.txtSection": "-Раздел", "DE.Controllers.Main.txtSeries": "<PERSON>яд", "DE.Controllers.Main.txtShape_accentBorderCallout1": "Выноска 1 (граница и черта)", "DE.Controllers.Main.txtShape_accentBorderCallout2": "Выноска 2 (граница и черта)", "DE.Controllers.Main.txtShape_accentBorderCallout3": "Выноска 3 (граница и черта)", "DE.Controllers.Main.txtShape_accentCallout1": "Выноска 1 (черта)", "DE.Controllers.Main.txtShape_accentCallout2": "Выноска 2 (черта)", "DE.Controllers.Main.txtShape_accentCallout3": "Выноска 3 (черта)", "DE.Controllers.Main.txtShape_actionButtonBackPrevious": "Кнопка \"Назад\"", "DE.Controllers.Main.txtShape_actionButtonBeginning": "Кнопка \"В начало\"", "DE.Controllers.Main.txtShape_actionButtonBlank": "Пустая кнопка", "DE.Controllers.Main.txtShape_actionButtonDocument": "Кнопка документа", "DE.Controllers.Main.txtShape_actionButtonEnd": "Кнопка \"В конец\"", "DE.Controllers.Main.txtShape_actionButtonForwardNext": "Кнопка \"Вперед\"", "DE.Controllers.Main.txtShape_actionButtonHelp": "Кнопка \"Справка\"", "DE.Controllers.Main.txtShape_actionButtonHome": "Кнопка \"Домой\"", "DE.Controllers.Main.txtShape_actionButtonInformation": "Кнопка сведений", "DE.Controllers.Main.txtShape_actionButtonMovie": "Кнопка видео", "DE.Controllers.Main.txtShape_actionButtonReturn": "Кнопка возврата", "DE.Controllers.Main.txtShape_actionButtonSound": "Кнопка звука", "DE.Controllers.Main.txtShape_arc": "Дуга", "DE.Controllers.Main.txtShape_bentArrow": "Угловая стрелка", "DE.Controllers.Main.txtShape_bentConnector5": "Соединительная линия уступом", "DE.Controllers.Main.txtShape_bentConnector5WithArrow": "Уступ со стрелкой", "DE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Уступ с двумя стрелками", "DE.Controllers.Main.txtShape_bentUpArrow": "Угловая стрелка вверх", "DE.Controllers.Main.txtShape_bevel": "Багетная рамка", "DE.Controllers.Main.txtShape_blockArc": "Арка", "DE.Controllers.Main.txtShape_borderCallout1": "Выноска 1", "DE.Controllers.Main.txtShape_borderCallout2": "Выноска 2", "DE.Controllers.Main.txtShape_borderCallout3": "Выноска 3", "DE.Controllers.Main.txtShape_bracePair": "Двойные фигурные скобки", "DE.Controllers.Main.txtShape_callout1": "Выноска 1 (без границы)", "DE.Controllers.Main.txtShape_callout2": "Выноска 2 (без границы)", "DE.Controllers.Main.txtShape_callout3": "Выноска 3 (без границы)", "DE.Controllers.Main.txtShape_can": "Цилиндр", "DE.Controllers.Main.txtShape_chevron": "Шевр<PERSON>н", "DE.Controllers.Main.txtShape_chord": "Сегмент круга", "DE.Controllers.Main.txtShape_circularArrow": "Круговая стрелка", "DE.Controllers.Main.txtShape_cloud": "Облако", "DE.Controllers.Main.txtShape_cloudCallout": "Выноска-облако", "DE.Controllers.Main.txtShape_corner": "Угол", "DE.Controllers.Main.txtShape_cube": "<PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_curvedConnector3": "Скругленная соединительная линия", "DE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Скругленная линия со стрелкой", "DE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Скругленная линия с двумя стрелками", "DE.Controllers.Main.txtShape_curvedDownArrow": "Выгнутая вверх стрелка", "DE.Controllers.Main.txtShape_curvedLeftArrow": "Выгнутая вправо стрелка", "DE.Controllers.Main.txtShape_curvedRightArrow": "Выгнутая влево стрелка", "DE.Controllers.Main.txtShape_curvedUpArrow": "Выгнутая вниз стрелка", "DE.Controllers.Main.txtShape_decagon": "Десятиугольник", "DE.Controllers.Main.txtShape_diagStripe": "Диагональная полоса", "DE.Controllers.Main.txtShape_diamond": "Ромб", "DE.Controllers.Main.txtShape_dodecagon": "Двенадцатиугольник", "DE.Controllers.Main.txtShape_donut": "Кольцо", "DE.Controllers.Main.txtShape_doubleWave": "Двойная волна", "DE.Controllers.Main.txtShape_downArrow": "Стрелка вниз", "DE.Controllers.Main.txtShape_downArrowCallout": "Выноска со стрелкой вниз", "DE.Controllers.Main.txtShape_ellipse": "Элли<PERSON><PERSON>", "DE.Controllers.Main.txtShape_ellipseRibbon": "Круглая лента лицом вниз", "DE.Controllers.Main.txtShape_ellipseRibbon2": "Круглая лента лицом вверх", "DE.Controllers.Main.txtShape_flowChartAlternateProcess": "Блок-схема: альтернативный процесс", "DE.Controllers.Main.txtShape_flowChartCollate": "Блок-схема: сопоставление", "DE.Controllers.Main.txtShape_flowChartConnector": "Блок-схема: соединительная линия", "DE.Controllers.Main.txtShape_flowChartDecision": "Блок-схема: решение", "DE.Controllers.Main.txtShape_flowChartDelay": "Блок-схема: задержка", "DE.Controllers.Main.txtShape_flowChartDisplay": "Блок-схема: дисплей", "DE.Controllers.Main.txtShape_flowChartDocument": "Блок-схема: документ", "DE.Controllers.Main.txtShape_flowChartExtract": "Блок-схема: извлечение", "DE.Controllers.Main.txtShape_flowChartInputOutput": "Блок-схема: данные", "DE.Controllers.Main.txtShape_flowChartInternalStorage": "Блок-схема: внутренняя память", "DE.Controllers.Main.txtShape_flowChartMagneticDisk": "Блок-схема: магнитный диск", "DE.Controllers.Main.txtShape_flowChartMagneticDrum": "Блок-схема: память с прямым доступом", "DE.Controllers.Main.txtShape_flowChartMagneticTape": "Блок-схема: память с посл. доступом", "DE.Controllers.Main.txtShape_flowChartManualInput": "Блок-схема: ручной ввод", "DE.Controllers.Main.txtShape_flowChartManualOperation": "Блок-схема: ручное управление", "DE.Controllers.Main.txtShape_flowChartMerge": "Блок-схема: объединение", "DE.Controllers.Main.txtShape_flowChartMultidocument": "Блок-схема: несколько документов", "DE.Controllers.Main.txtShape_flowChartOffpageConnector": "Блок-схема: ссылка на другую страницу", "DE.Controllers.Main.txtShape_flowChartOnlineStorage": "Блок-схема: сохраненные данные", "DE.Controllers.Main.txtShape_flowChartOr": "Блок-схема: ИЛИ", "DE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Блок-схема: типовой процесс", "DE.Controllers.Main.txtShape_flowChartPreparation": "Блок-схема: подготовка", "DE.Controllers.Main.txtShape_flowChartProcess": "Блок-схема: процесс", "DE.Controllers.Main.txtShape_flowChartPunchedCard": "Блок-схема: карточка", "DE.Controllers.Main.txtShape_flowChartPunchedTape": "Блок-схема: перфолента", "DE.Controllers.Main.txtShape_flowChartSort": "Блок-схема: сортировка", "DE.Controllers.Main.txtShape_flowChartSummingJunction": "Блок-схема: узел суммирования", "DE.Controllers.Main.txtShape_flowChartTerminator": "Блок-схема: знак завершения", "DE.Controllers.Main.txtShape_foldedCorner": "Загнутый угол", "DE.Controllers.Main.txtShape_frame": "Рамка", "DE.Controllers.Main.txtShape_halfFrame": "Половина рамки", "DE.Controllers.Main.txtShape_heart": "Сердце", "DE.Controllers.Main.txtShape_heptagon": "Семиугольник", "DE.Controllers.Main.txtShape_hexagon": "Шестиугольник", "DE.Controllers.Main.txtShape_homePlate": "Пятиугольник", "DE.Controllers.Main.txtShape_horizontalScroll": "Горизонтальный свиток", "DE.Controllers.Main.txtShape_irregularSeal1": "Вспышка 1", "DE.Controllers.Main.txtShape_irregularSeal2": "Вспышка 2", "DE.Controllers.Main.txtShape_leftArrow": "Стрелка влево", "DE.Controllers.Main.txtShape_leftArrowCallout": "Выноска со стрелкой влево", "DE.Controllers.Main.txtShape_leftBrace": "Левая фигурная скобка", "DE.Controllers.Main.txtShape_leftBracket": "Левая круглая скобка", "DE.Controllers.Main.txtShape_leftRightArrow": "Двойная стрелка влево-вправо", "DE.Controllers.Main.txtShape_leftRightArrowCallout": "Выноска со стрелками влево-вправо", "DE.Controllers.Main.txtShape_leftRightUpArrow": "Тройная стрелка влево-вправо-вверх", "DE.Controllers.Main.txtShape_leftUpArrow": "Двойная стрелка влево-вверх", "DE.Controllers.Main.txtShape_lightningBolt": "Молния", "DE.Controllers.Main.txtShape_line": "Линия", "DE.Controllers.Main.txtShape_lineWithArrow": "Стрелка", "DE.Controllers.Main.txtShape_lineWithTwoArrows": "Двойная стрелка", "DE.Controllers.Main.txtShape_mathDivide": "Деление", "DE.Controllers.Main.txtShape_mathEqual": "Равно", "DE.Controllers.Main.txtShape_mathMinus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_mathMultiply": "Умножение", "DE.Controllers.Main.txtShape_mathNotEqual": "Не равно", "DE.Controllers.Main.txtShape_mathPlus": "Плюс", "DE.Controllers.Main.txtShape_moon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Main.txtShape_noSmoking": "Запрещено", "DE.Controllers.Main.txtShape_notchedRightArrow": "Стрелка вправо с вырезом", "DE.Controllers.Main.txtShape_octagon": "Восьмиугольник", "DE.Controllers.Main.txtShape_parallelogram": "Параллелограмм", "DE.Controllers.Main.txtShape_pentagon": "Пятиугольник", "DE.Controllers.Main.txtShape_pie": "Сектор круга", "DE.Controllers.Main.txtShape_plaque": "Табличка", "DE.Controllers.Main.txtShape_plus": "Плюс", "DE.Controllers.Main.txtShape_polyline1": "Рисованная кривая", "DE.Controllers.Main.txtShape_polyline2": "Произвольная форма", "DE.Controllers.Main.txtShape_quadArrow": "Счетверенная стрелка", "DE.Controllers.Main.txtShape_quadArrowCallout": "Выноска с четырьмя стрелками", "DE.Controllers.Main.txtShape_rect": "Прямоугольник", "DE.Controllers.Main.txtShape_ribbon": "Лента лицом вниз", "DE.Controllers.Main.txtShape_ribbon2": "Лента лицом вверх", "DE.Controllers.Main.txtShape_rightArrow": "Стрелка вправо", "DE.Controllers.Main.txtShape_rightArrowCallout": "Выноска со стрелкой вправо", "DE.Controllers.Main.txtShape_rightBrace": "Правая фигурная скобка", "DE.Controllers.Main.txtShape_rightBracket": "Правая круглая скобка", "DE.Controllers.Main.txtShape_round1Rect": "Прямоугольник с одним скругленным углом", "DE.Controllers.Main.txtShape_round2DiagRect": "Прямоугольник с двумя скругленными противолежащими углами", "DE.Controllers.Main.txtShape_round2SameRect": "Прямоугольник с двумя скругленными соседними углами", "DE.Controllers.Main.txtShape_roundRect": "Прямоугольник со скругленными углами", "DE.Controllers.Main.txtShape_rtTriangle": "Прямоугольный треугольник", "DE.Controllers.Main.txtShape_smileyFace": "Улыбающееся лицо", "DE.Controllers.Main.txtShape_snip1Rect": "Прямоугольник с одним вырезанным углом", "DE.Controllers.Main.txtShape_snip2DiagRect": "Прямоугольник с двумя вырезанными противолежащими углами", "DE.Controllers.Main.txtShape_snip2SameRect": "Прямоугольник с двумя вырезанными соседними углами", "DE.Controllers.Main.txtShape_snipRoundRect": "Прямоугольник с одним вырезанным скругленным углом", "DE.Controllers.Main.txtShape_spline": "Кривая", "DE.Controllers.Main.txtShape_star10": "10-конечная звезда", "DE.Controllers.Main.txtShape_star12": "12-конечная звезда", "DE.Controllers.Main.txtShape_star16": "16-конечная звезда", "DE.Controllers.Main.txtShape_star24": "24-конечная звезда", "DE.Controllers.Main.txtShape_star32": "32-конечная звезда", "DE.Controllers.Main.txtShape_star4": "4-конечная звезда", "DE.Controllers.Main.txtShape_star5": "5-конечная звезда", "DE.Controllers.Main.txtShape_star6": "6-конечная звезда", "DE.Controllers.Main.txtShape_star7": "7-конечная звезда", "DE.Controllers.Main.txtShape_star8": "8-конечная звезда", "DE.Controllers.Main.txtShape_stripedRightArrow": "Штриховая стрелка вправо", "DE.Controllers.Main.txtShape_sun": "Солнце", "DE.Controllers.Main.txtShape_teardrop": "Капля", "DE.Controllers.Main.txtShape_textRect": "Надпись", "DE.Controllers.Main.txtShape_trapezoid": "Трапеция", "DE.Controllers.Main.txtShape_triangle": "Треугольник", "DE.Controllers.Main.txtShape_upArrow": "Стрелка вверх", "DE.Controllers.Main.txtShape_upArrowCallout": "Выноска со стрелкой вверх", "DE.Controllers.Main.txtShape_upDownArrow": "Двойная стрелка вверх-вниз", "DE.Controllers.Main.txtShape_uturnArrow": "Развернутая стрелка", "DE.Controllers.Main.txtShape_verticalScroll": "Вертикальный свиток", "DE.Controllers.Main.txtShape_wave": "Волна", "DE.Controllers.Main.txtShape_wedgeEllipseCallout": "Овальная выноска", "DE.Controllers.Main.txtShape_wedgeRectCallout": "Прямоугольная выноска", "DE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Скругленная прямоугольная выноска", "DE.Controllers.Main.txtStarsRibbons": "Звезды и ленты", "DE.Controllers.Main.txtStyle_Caption": "Название", "DE.Controllers.Main.txtStyle_endnote_text": "Текст концевой сноски", "DE.Controllers.Main.txtStyle_footnote_text": "Текст сноски", "DE.Controllers.Main.txtStyle_Heading_1": "Заголовок 1", "DE.Controllers.Main.txtStyle_Heading_2": "Заголовок 2", "DE.Controllers.Main.txtStyle_Heading_3": "Заголовок 3", "DE.Controllers.Main.txtStyle_Heading_4": "Заголовок 4", "DE.Controllers.Main.txtStyle_Heading_5": "Заголовок 5", "DE.Controllers.Main.txtStyle_Heading_6": "Заголовок 6", "DE.Controllers.Main.txtStyle_Heading_7": "Заголовок 7", "DE.Controllers.Main.txtStyle_Heading_8": "Заголовок 8", "DE.Controllers.Main.txtStyle_Heading_9": "Заголовок 9", "DE.Controllers.Main.txtStyle_Intense_Quote": "Выделенная цитата", "DE.Controllers.Main.txtStyle_List_Paragraph": "Абзац списка", "DE.Controllers.Main.txtStyle_No_Spacing": "Без интервала", "DE.Controllers.Main.txtStyle_Normal": "Обычный", "DE.Controllers.Main.txtStyle_Quote": "Цитата", "DE.Controllers.Main.txtStyle_Subtitle": "Подзаголовок", "DE.Controllers.Main.txtStyle_Title": "Название", "DE.Controllers.Main.txtSyntaxError": "Синтаксическая ошибка", "DE.Controllers.Main.txtTableInd": "Индекс таблицы не может быть нулевым", "DE.Controllers.Main.txtTableOfContents": "Оглавление", "DE.Controllers.Main.txtTableOfFigures": "Список иллюстраций", "DE.Controllers.Main.txtTOCHeading": "Заголовок оглавления", "DE.Controllers.Main.txtTooLarge": "Число слишком большое для форматирования", "DE.Controllers.Main.txtTypeEquation": "Место для уравнения.", "DE.Controllers.Main.txtUndefBookmark": "Закладка не определена", "DE.Controllers.Main.txtXAxis": "Ось X", "DE.Controllers.Main.txtYAxis": "Ось Y", "DE.Controllers.Main.txtZeroDivide": "Деление на ноль", "DE.Controllers.Main.unknownErrorText": "Неизвестная ошибка.", "DE.Controllers.Main.unsupportedBrowserErrorText": "Ваш браузер не поддерживается.", "DE.Controllers.Main.uploadDocExtMessage": "Неизвестный формат документа.", "DE.Controllers.Main.uploadDocFileCountMessage": "Ни одного документа не загружено.", "DE.Controllers.Main.uploadDocSizeMessage": "Превышен максимальный размер документа.", "DE.Controllers.Main.uploadImageExtMessage": "Неизвестный формат изображения.", "DE.Controllers.Main.uploadImageFileCountMessage": "Ни одного изображения не загружено.", "DE.Controllers.Main.uploadImageSizeMessage": "Слишком большое изображение. Максимальный размер - 25 MB.", "DE.Controllers.Main.uploadImageTextText": "Загрузка изображения...", "DE.Controllers.Main.uploadImageTitleText": "Загрузка изображения", "DE.Controllers.Main.waitText": "Пожалуйста, подождите...", "DE.Controllers.Main.warnBrowserIE9": "В IE9 приложение имеет низкую производительность. Используйте IE10 или более позднюю версию.", "DE.Controllers.Main.warnBrowserZoom": "Текущее значение масштаба страницы в браузере поддерживается не полностью. Вернитесь к масштабу по умолчанию, нажав Ctrl+0.", "DE.Controllers.Main.warnLicenseExceeded": "Вы достигли лимита на одновременные подключения к редакторам %1. Этот документ будет открыт только на просмотр.<br>Свяжитесь с администратором, чтобы узнать больше.", "DE.Controllers.Main.warnLicenseExp": "Истек срок действия лицензии.<br>Обновите лицензию, а затем обновите страницу.", "DE.Controllers.Main.warnLicenseLimitedNoAccess": "Истек срок действия лицензии.<br>Нет доступа к функциональности редактирования документов.<br>Пожалуйста, обратитесь к администратору.", "DE.Controllers.Main.warnLicenseLimitedRenewed": "Необходимо обновить лицензию.<br>У вас ограниченный доступ к функциональности редактирования документов.<br>Пожалуйста, обратитесь к администратору, чтобы получить полный доступ", "DE.Controllers.Main.warnLicenseUsersExceeded": "Вы достигли лимита на количество пользователей редакторов %1.<br>Свяжитесь с администратором, чтобы узнать больше.", "DE.Controllers.Main.warnNoLicense": "Вы достигли лимита на одновременные подключения к редакторам %1. Этот документ будет открыт на просмотр.<br>Напишите в отдел продаж %1, чтобы обсудить индивидуальные условия лицензирования.", "DE.Controllers.Main.warnNoLicenseUsers": "Вы достигли лимита на одновременные подключения к редакторам %1.<br>Напишите в отдел продаж %1, чтобы обсудить индивидуальные условия лицензирования.", "DE.Controllers.Main.warnProcessRightsChange": "Вам было отказано в праве на редактирование этого файла.", "DE.Controllers.Navigation.txtBeginning": "Начало документа", "DE.Controllers.Navigation.txtGotoBeginning": "Перейти в начало документа", "DE.Controllers.Print.textMarginsLast": "Последние настраиваемые", "DE.Controllers.Print.txtCustom": "Пользовательское", "DE.Controllers.Print.txtPrintRangeInvalid": "Неправильный диапазон печати", "DE.Controllers.Print.txtPrintRangeSingleRange": "Введите или один номер страницы, или один диапазон страниц (например, 5-12). Или вы можете выбрать печать в PDF.", "DE.Controllers.Search.notcriticalErrorTitle": "Внимание", "DE.Controllers.Search.textNoTextFound": "Искомые данные не найдены. Пожалуйста, измените параметры поиска.", "DE.Controllers.Search.textReplaceSkipped": "Замена выполнена. Пропущено вхождений - {0}.", "DE.Controllers.Search.textReplaceSuccess": "Поиск выполнен. Заменено совпадений: {0}", "DE.Controllers.Search.warnReplaceString": "{0} нельзя использовать как специальный символ в поле Заменить на.", "DE.Controllers.Statusbar.textDisconnect": "<b>Соединение потеряно</b><br>Попытка подключения. Проверьте настройки подключения.", "DE.Controllers.Statusbar.textHasChanges": "Отслежены новые изменения", "DE.Controllers.Statusbar.textSetTrackChanges": "Вы находитесь в режиме отслеживания изменений", "DE.Controllers.Statusbar.textTrackChanges": "Документ открыт при включенном режиме отслеживания изменений", "DE.Controllers.Statusbar.tipReview": "Отслеживать изменения", "DE.Controllers.Statusbar.zoomText": "Мас<PERSON>т<PERSON>б {0}%", "DE.Controllers.Toolbar.confirmAddFontName": "Шрифт, который вы хотите сохранить, недоступен на этом устройстве.<br>Стиль текста будет отображаться с помощью одного из системных шрифтов. Сохраненный шрифт будет использоваться, когда он станет доступен.<br>Вы хотите продолжить?", "DE.Controllers.Toolbar.dataUrl": "Вставьте URL-адрес данных", "DE.Controllers.Toolbar.notcriticalErrorTitle": "Внимание", "DE.Controllers.Toolbar.textAccent": "Диакритические знаки", "DE.Controllers.Toolbar.textBracket": "Скобки", "DE.Controllers.Toolbar.textEmptyImgUrl": "Необходимо указать URL изображения.", "DE.Controllers.Toolbar.textEmptyMMergeUrl": "Необходимо указать URL.", "DE.Controllers.Toolbar.textFontSizeErr": "Введенное значение некорректно.<br>Введите числовое значение от 1 до 300", "DE.Controllers.Toolbar.textFraction": "Дроби", "DE.Controllers.Toolbar.textFunction": "Функции", "DE.Controllers.Toolbar.textGroup": "Группа", "DE.Controllers.Toolbar.textInsert": "Вставить", "DE.Controllers.Toolbar.textIntegral": "Интегралы", "DE.Controllers.Toolbar.textLargeOperator": "Крупные операторы", "DE.Controllers.Toolbar.textLimitAndLog": "Пределы и логарифмы", "DE.Controllers.Toolbar.textMatrix": "Матрицы", "DE.Controllers.Toolbar.textOperator": "Операторы", "DE.Controllers.Toolbar.textRadical": "Радикалы", "DE.Controllers.Toolbar.textRecentlyUsed": "Последние использованные", "DE.Controllers.Toolbar.textScript": "Индексы", "DE.Controllers.Toolbar.textSymbols": "Символы", "DE.Controllers.Toolbar.textTabForms": "Формы", "DE.Controllers.Toolbar.textWarning": "Предупреждение", "DE.Controllers.Toolbar.txtAccent_Accent": "Ударение", "DE.Controllers.Toolbar.txtAccent_ArrowD": "Стрелка вправо-влево сверху", "DE.Controllers.Toolbar.txtAccent_ArrowL": "Стрелка влево сверху", "DE.Controllers.Toolbar.txtAccent_ArrowR": "Стрелка вправо сверху", "DE.Controllers.Toolbar.txtAccent_Bar": "Черта", "DE.Controllers.Toolbar.txtAccent_BarBot": "Черта снизу", "DE.Controllers.Toolbar.txtAccent_BarTop": "Черта сверху", "DE.Controllers.Toolbar.txtAccent_BorderBox": "Формула в рамке (с заполнителем)", "DE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Формула в рамке (пример)", "DE.Controllers.Toolbar.txtAccent_Check": "Галочка", "DE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Фигурная скобка снизу", "DE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Фигурная скобка сверху", "DE.Controllers.Toolbar.txtAccent_Custom_1": "Вектор A", "DE.Controllers.Toolbar.txtAccent_Custom_2": "ABC с чертой сверху", "DE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y с чертой сверху", "DE.Controllers.Toolbar.txtAccent_DDDot": "Три точки", "DE.Controllers.Toolbar.txtAccent_DDot": "Две точки", "DE.Controllers.Toolbar.txtAccent_Dot": "Точка", "DE.Controllers.Toolbar.txtAccent_DoubleBar": "Двойная черта сверху", "DE.Controllers.Toolbar.txtAccent_Grave": "Тупое ударение", "DE.Controllers.Toolbar.txtAccent_GroupBot": "Группир<PERSON><PERSON><PERSON>ий знак снизу", "DE.Controllers.Toolbar.txtAccent_GroupTop": "Группир<PERSON>ю<PERSON>ий знак сверху", "DE.Controllers.Toolbar.txtAccent_HarpoonL": "Гар<PERSON>ун влево сверху", "DE.Controllers.Toolbar.txtAccent_HarpoonR": "Гарпун вправо сверху", "DE.Controllers.Toolbar.txtAccent_Hat": "Крышка", "DE.Controllers.Toolbar.txtAccent_Smile": "Значок краткости", "DE.Controllers.Toolbar.txtAccent_Tilde": "Тильда", "DE.Controllers.Toolbar.txtBracket_Angle": "Угловые скобки", "DE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Угловые скобки с разделителем", "DE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Угловые скобки с двумя разделителями", "DE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Правая угловая скобка", "DE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Левая угловая скобка", "DE.Controllers.Toolbar.txtBracket_Curve": "Фигурные скобки", "DE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Фигурные скобки с разделителем", "DE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Правая фигурная скобка", "DE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Левая фигурная скобка", "DE.Controllers.Toolbar.txtBracket_Custom_1": "Наборы условий (два условия)", "DE.Controllers.Toolbar.txtBracket_Custom_2": "Наборы условий (три условия)", "DE.Controllers.Toolbar.txtBracket_Custom_3": "Стопка объектов", "DE.Controllers.Toolbar.txtBracket_Custom_4": "Стопка объектов в круглых скобках", "DE.Controllers.Toolbar.txtBracket_Custom_5": "Наборы условий (пример)", "DE.Controllers.Toolbar.txtBracket_Custom_6": "Биномиальный коэффициент", "DE.Controllers.Toolbar.txtBracket_Custom_7": "Биномиальный коэффициент в угловых скобках", "DE.Controllers.Toolbar.txtBracket_Line": "Вертикальные черты", "DE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Правая вертикальная черта", "DE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Левая вертикальная черта", "DE.Controllers.Toolbar.txtBracket_LineDouble": "Двойные вертикальные черты", "DE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Правая двойная вертикальная черта", "DE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Левая двойная вертикальная черта", "DE.Controllers.Toolbar.txtBracket_LowLim": "Закрытые снизу скобки", "DE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Правый предельный уровень снизу", "DE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Левый предельный уровень снизу", "DE.Controllers.Toolbar.txtBracket_Round": "Круглые скобки", "DE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Круглые скобки с разделителем", "DE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Правая круглая скобка", "DE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Левая круглая скобка", "DE.Controllers.Toolbar.txtBracket_Square": "Квадратные скобки", "DE.Controllers.Toolbar.txtBracket_Square_CloseClose": "Заполнитель между двумя правыми квадратными скобками", "DE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "Перевернутые квадратные скобки", "DE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Правая квадратная скобка", "DE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Левая квадратная скобка", "DE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "Заполнитель между двумя левыми квадратными скобками", "DE.Controllers.Toolbar.txtBracket_SquareDouble": "Двойные квадратные скобки", "DE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Правая двойная квадратная скобка", "DE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Левая двойная квадратная скобка", "DE.Controllers.Toolbar.txtBracket_UppLim": "Закрытые сверху скобки", "DE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Правый предельный уровень сверху", "DE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Левый предельный уровень сверху", "DE.Controllers.Toolbar.txtFractionDiagonal": "Диагональная простая дробь", "DE.Controllers.Toolbar.txtFractionDifferential_1": "dy над dx", "DE.Controllers.Toolbar.txtFractionDifferential_2": "пересечение дельты y над пересечением дельты x", "DE.Controllers.Toolbar.txtFractionDifferential_3": "частичная y по частичной x", "DE.Controllers.Toolbar.txtFractionDifferential_4": "дельта y через дельта x", "DE.Controllers.Toolbar.txtFractionHorizontal": "Горизонтальная простая дробь", "DE.Controllers.Toolbar.txtFractionPi_2": "Пи разделить на два", "DE.Controllers.Toolbar.txtFractionSmall": "Маленькая простая дробь", "DE.Controllers.Toolbar.txtFractionVertical": "Вертикальная простая дробь", "DE.Controllers.Toolbar.txtFunction_1_Cos": "Арккосинус", "DE.Controllers.Toolbar.txtFunction_1_Cosh": "Гиперболический арккосинус", "DE.Controllers.Toolbar.txtFunction_1_Cot": "Арккотангенс", "DE.Controllers.Toolbar.txtFunction_1_Coth": "Гиперболический арккотангенс", "DE.Controllers.Toolbar.txtFunction_1_Csc": "Арккосеканс", "DE.Controllers.Toolbar.txtFunction_1_Csch": "Гиперболический арккосеканс", "DE.Controllers.Toolbar.txtFunction_1_Sec": "Арксеканс", "DE.Controllers.Toolbar.txtFunction_1_Sech": "Гиперболический арксеканс", "DE.Controllers.Toolbar.txtFunction_1_Sin": "Аркси<PERSON><PERSON>с", "DE.Controllers.Toolbar.txtFunction_1_Sinh": "Гиперболический арксинус", "DE.Controllers.Toolbar.txtFunction_1_Tan": "Арк<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtFunction_1_Tanh": "Гиперболический арктангенс", "DE.Controllers.Toolbar.txtFunction_Cos": "Ко<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtFunction_Cosh": "Гиперболический косинус", "DE.Controllers.Toolbar.txtFunction_Cot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtFunction_Coth": "Гиперболический котангенс", "DE.Controllers.Toolbar.txtFunction_Csc": "Косеканс", "DE.Controllers.Toolbar.txtFunction_Csch": "Гиперболический косеканс", "DE.Controllers.Toolbar.txtFunction_Custom_1": "Sin θ", "DE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "DE.Controllers.Toolbar.txtFunction_Custom_3": "Формула тангенса", "DE.Controllers.Toolbar.txtFunction_Sec": "Секанс", "DE.Controllers.Toolbar.txtFunction_Sech": "Гиперболический секанс", "DE.Controllers.Toolbar.txtFunction_Sin": "Син<PERSON>с", "DE.Controllers.Toolbar.txtFunction_Sinh": "Гиперболический синус", "DE.Controllers.Toolbar.txtFunction_Tan": "Та<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtFunction_Tanh": "Гиперболический тангенс", "DE.Controllers.Toolbar.txtIntegral": "Интеграл", "DE.Controllers.Toolbar.txtIntegral_dtheta": "Дифференциал dθ", "DE.Controllers.Toolbar.txtIntegral_dx": "Дифференциал dx", "DE.Controllers.Toolbar.txtIntegral_dy": "Ди<PERSON><PERSON>еренциал dy", "DE.Controllers.Toolbar.txtIntegralCenterSubSup": "Интеграл с пределами с накоплением", "DE.Controllers.Toolbar.txtIntegralDouble": "Двойной интеграл", "DE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Двойной интеграл с пределами с накоплением", "DE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Двойной интеграл с пределами", "DE.Controllers.Toolbar.txtIntegralOriented": "Контурный интеграл", "DE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Контурный интеграл с пределами с накоплением", "DE.Controllers.Toolbar.txtIntegralOrientedDouble": "Интеграл по поверхности", "DE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Интеграл по поверхности с пределами с накоплением", "DE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Интеграл по поверхности с пределами", "DE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Контурный интеграл с пределами", "DE.Controllers.Toolbar.txtIntegralOrientedTriple": "Интеграл по объему", "DE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Интеграл по объему с пределами с накоплением", "DE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Интеграл по объему с пределами", "DE.Controllers.Toolbar.txtIntegralSubSup": "Интеграл с пределами", "DE.Controllers.Toolbar.txtIntegralTriple": "Тройной интеграл", "DE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Тройной интеграл с пределами с накоплением", "DE.Controllers.Toolbar.txtIntegralTripleSubSup": "Тройной интеграл с пределами", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Логическое И", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Логическое И с нижним пределом", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Логическое И с пределами", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Логическое И с нижним пределом подстрочного знака", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Логическое И с пределом подстрочного/надстрочного знака", "DE.Controllers.Toolbar.txtLargeOperator_CoProd": "Сопроизведение", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Сопроизведение с нижним пределом", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Сопроизведение с пределами", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Сопроизведение с нижним пределом подстрочного знака", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Сопроизведение с пределами подстрочного/надстрочного знака", "DE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Суммирование от k от n с выбором k", "DE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Суммирование от i равно ноль до n", "DE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Пример суммирования с использованием двух индексов", "DE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Пример произведения", "DE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Пример объединения", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction": "Логическое Или", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "Логическое ИЛИ с нижним пределом", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "Логическое ИЛИ с пределами", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "Логическое ИЛИ с нижним пределом подстрочного знака", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "Логическое ИЛИ с пределами подстрочного/надстрочного знака", "DE.Controllers.Toolbar.txtLargeOperator_Intersection": "Пересечение", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Пересечение с нижним пределом", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Пересечение с пределами", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Пересечение с нижним пределом в виде подстрочного знака", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Пересечение с пределами подстрочного/надстрочного знака", "DE.Controllers.Toolbar.txtLargeOperator_Prod": "Произведение", "DE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Произведение с нижним пределом", "DE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Произведение с пределами", "DE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Произведение с нижним пределом подстрочного знака", "DE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Произведение с пределами подстрочного/надстрочного знака", "DE.Controllers.Toolbar.txtLargeOperator_Sum": "Сумма", "DE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Суммирование с нижним пределом", "DE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Суммирование с пределами", "DE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Суммирование с нижним пределом подстрочного знака", "DE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Суммирование с пределами подстрочного/надстрочного знака", "DE.Controllers.Toolbar.txtLargeOperator_Union": "Объединение", "DE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Объединение с нижним пределом", "DE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Объединение с пределами", "DE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Объединение с нижним пределом подстрочного знака", "DE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Объединение с пределами подстрочного/надстрочного знака", "DE.Controllers.Toolbar.txtLimitLog_Custom_1": "Пример предела", "DE.Controllers.Toolbar.txtLimitLog_Custom_2": "Пример максимума", "DE.Controllers.Toolbar.txtLimitLog_Lim": "Предел", "DE.Controllers.Toolbar.txtLimitLog_Ln": "Натуральный логарифм", "DE.Controllers.Toolbar.txtLimitLog_Log": "Лог<PERSON><PERSON><PERSON><PERSON>м", "DE.Controllers.Toolbar.txtLimitLog_LogBase": "Лог<PERSON><PERSON><PERSON><PERSON>м", "DE.Controllers.Toolbar.txtLimitLog_Max": "Максимум", "DE.Controllers.Toolbar.txtLimitLog_Min": "Мини<PERSON>ум", "DE.Controllers.Toolbar.txtMarginsH": "Верхнее и нижнее поля слишком высокие для заданной высоты страницы", "DE.Controllers.Toolbar.txtMarginsW": "Левое и правое поля слишком широкие для заданной ширины страницы", "DE.Controllers.Toolbar.txtMatrix_1_2": "Пустая матрица 1 x 2", "DE.Controllers.Toolbar.txtMatrix_1_3": "Пустая матрица 1 x 3", "DE.Controllers.Toolbar.txtMatrix_2_1": "Пустая матрица 2 x 1", "DE.Controllers.Toolbar.txtMatrix_2_2": "Пустая матрица 2 x 2", "DE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "Пустая матрица 2 х 2 в двойных вертикальных чертах", "DE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Пустой определитель 2 x 2", "DE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "Пустая матрица 2 х 2 в круглых скобках", "DE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "Пустая матрица 2 х 2 в скобках", "DE.Controllers.Toolbar.txtMatrix_2_3": "Пустая матрица 2 x 3", "DE.Controllers.Toolbar.txtMatrix_3_1": "Пустая матрица 3 x 1", "DE.Controllers.Toolbar.txtMatrix_3_2": "Пустая матрица 3 x 2", "DE.Controllers.Toolbar.txtMatrix_3_3": "Пустая матрица 3 x 3", "DE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Точки на опорной линии", "DE.Controllers.Toolbar.txtMatrix_Dots_Center": "Точки посередине", "DE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Точки по диагонали", "DE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "Точки по вертикали", "DE.Controllers.Toolbar.txtMatrix_Flat_Round": "Разреженная матрица в круглых скобках", "DE.Controllers.Toolbar.txtMatrix_Flat_Square": "Разреженная матрица в квадратных скобках", "DE.Controllers.Toolbar.txtMatrix_Identity_2": "Единичная матрица 2 x 2 с нулями", "DE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "Единичная матрица 2 x 2 с пустыми ячейками не на диагонали", "DE.Controllers.Toolbar.txtMatrix_Identity_3": "Единичная матрица 3 x 3 с нулями", "DE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "Единичная матрица 3 x 3 с пустыми ячейками не на диагонали", "DE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Стрелка вправо-влево снизу", "DE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Стрелка вправо-влево сверху", "DE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Стрелка влево снизу", "DE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Стрелка влево сверху", "DE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Стрелка вправо снизу", "DE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Стрелка вправо сверху", "DE.Controllers.Toolbar.txtOperator_ColonEquals": "Двоеточие равно", "DE.Controllers.Toolbar.txtOperator_Custom_1": "Выход", "DE.Controllers.Toolbar.txtOperator_Custom_2": "Дельта выхода", "DE.Controllers.Toolbar.txtOperator_Definition": "Равно по определению", "DE.Controllers.Toolbar.txtOperator_DeltaEquals": "Дельта равна", "DE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Двойная стрелка вправо-влево снизу", "DE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Двойная стрелка вправо-влево сверху", "DE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Стрелка влево снизу", "DE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Стрелка влево сверху", "DE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Стрелка вправо снизу", "DE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Стрелка вправо сверху", "DE.Controllers.Toolbar.txtOperator_EqualsEquals": "Равно равно", "DE.Controllers.Toolbar.txtOperator_MinusEquals": "Минус равно", "DE.Controllers.Toolbar.txtOperator_PlusEquals": "Плюс равно", "DE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "Единица измерения", "DE.Controllers.Toolbar.txtRadicalCustom_1": "Правая часть квадратного уравнения", "DE.Controllers.Toolbar.txtRadicalCustom_2": "Квадратный корень из квадрата плюс b квадрат", "DE.Controllers.Toolbar.txtRadicalRoot_2": "Квадратный корень со степенью", "DE.Controllers.Toolbar.txtRadicalRoot_3": "Кубический корень", "DE.Controllers.Toolbar.txtRadicalRoot_n": "Радикал со степенью", "DE.Controllers.Toolbar.txtRadicalSqrt": "Квадратный корень", "DE.Controllers.Toolbar.txtScriptCustom_1": "x в степени квадрата y", "DE.Controllers.Toolbar.txtScriptCustom_2": "e в степени -iωt", "DE.Controllers.Toolbar.txtScriptCustom_3": "квадрат x", "DE.Controllers.Toolbar.txtScriptCustom_4": "Y, надстрочный индекс n слева, подстрочный индекс 1 справа", "DE.Controllers.Toolbar.txtScriptSub": "Нижний индекс", "DE.Controllers.Toolbar.txtScriptSubSup": "Нижний и верхний индексы", "DE.Controllers.Toolbar.txtScriptSubSupLeft": "Нижний и верхний индексы слева", "DE.Controllers.Toolbar.txtScriptSup": "Верхний индекс", "DE.Controllers.Toolbar.txtSymbol_about": "Приблизительно", "DE.Controllers.Toolbar.txtSymbol_additional": "Дополнение", "DE.Controllers.Toolbar.txtSymbol_aleph": "<PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_alpha": "Альфа", "DE.Controllers.Toolbar.txtSymbol_approx": "Почти равно", "DE.Controllers.Toolbar.txtSymbol_ast": "Оператор-звездочка", "DE.Controllers.Toolbar.txtSymbol_beta": "Бета", "DE.Controllers.Toolbar.txtSymbol_beth": "Бет", "DE.Controllers.Toolbar.txtSymbol_bullet": "Оператор-маркер", "DE.Controllers.Toolbar.txtSymbol_cap": "Пересечение", "DE.Controllers.Toolbar.txtSymbol_cbrt": "Кубический корень", "DE.Controllers.Toolbar.txtSymbol_cdots": "Горизонтальное многоточие посередине", "DE.Controllers.Toolbar.txtSymbol_celsius": "Градусы Цельсия", "DE.Controllers.Toolbar.txtSymbol_chi": "Хи", "DE.Controllers.Toolbar.txtSymbol_cong": "Приблизительно равно", "DE.Controllers.Toolbar.txtSymbol_cup": "Объединение", "DE.Controllers.Toolbar.txtSymbol_ddots": "Диагональное многоточие вниз вправо", "DE.Controllers.Toolbar.txtSymbol_degree": "<PERSON>ра<PERSON><PERSON><PERSON>ы", "DE.Controllers.Toolbar.txtSymbol_delta": "Дельта", "DE.Controllers.Toolbar.txtSymbol_div": "Знак деления", "DE.Controllers.Toolbar.txtSymbol_downarrow": "Стрелка вниз", "DE.Controllers.Toolbar.txtSymbol_emptyset": "Пустое множество", "DE.Controllers.Toolbar.txtSymbol_epsilon": "Эп<PERSON>илон", "DE.Controllers.Toolbar.txtSymbol_equals": "Равно", "DE.Controllers.Toolbar.txtSymbol_equiv": "Тождественно", "DE.Controllers.Toolbar.txtSymbol_eta": "Эта", "DE.Controllers.Toolbar.txtSymbol_exists": "Существует", "DE.Controllers.Toolbar.txtSymbol_factorial": "Факториал", "DE.Controllers.Toolbar.txtSymbol_fahrenheit": "Градусы Фаренгейта", "DE.Controllers.Toolbar.txtSymbol_forall": "Для всех", "DE.Controllers.Toolbar.txtSymbol_gamma": "Гамма", "DE.Controllers.Toolbar.txtSymbol_geq": "Больше или равно", "DE.Controllers.Toolbar.txtSymbol_gg": "Много больше", "DE.Controllers.Toolbar.txtSymbol_greater": "Больше", "DE.Controllers.Toolbar.txtSymbol_in": "Является элементом", "DE.Controllers.Toolbar.txtSymbol_inc": "Приращение", "DE.Controllers.Toolbar.txtSymbol_infinity": "Бесконечность", "DE.Controllers.Toolbar.txtSymbol_iota": "Йота", "DE.Controllers.Toolbar.txtSymbol_kappa": "Каппа", "DE.Controllers.Toolbar.txtSymbol_lambda": "Лямбда", "DE.Controllers.Toolbar.txtSymbol_leftarrow": "Стрелка влево", "DE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Стрелка влево и вправо", "DE.Controllers.Toolbar.txtSymbol_leq": "Меньше или равно", "DE.Controllers.Toolbar.txtSymbol_less": "Меньше", "DE.Controllers.Toolbar.txtSymbol_ll": "Много меньше", "DE.Controllers.Toolbar.txtSymbol_minus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Controllers.Toolbar.txtSymbol_mp": "Минус и плюс", "DE.Controllers.Toolbar.txtSymbol_mu": "Мю", "DE.Controllers.Toolbar.txtSymbol_nabla": "Набла", "DE.Controllers.Toolbar.txtSymbol_neq": "Не равно", "DE.Controllers.Toolbar.txtSymbol_ni": "Содер<PERSON><PERSON>т как член", "DE.Controllers.Toolbar.txtSymbol_not": "Знак отрицания", "DE.Controllers.Toolbar.txtSymbol_notexists": "Не существует", "DE.Controllers.Toolbar.txtSymbol_nu": "Ню", "DE.Controllers.Toolbar.txtSymbol_o": "Омикрон", "DE.Controllers.Toolbar.txtSymbol_omega": "Омега", "DE.Controllers.Toolbar.txtSymbol_partial": "Частный дифференциал", "DE.Controllers.Toolbar.txtSymbol_percent": "Процент", "DE.Controllers.Toolbar.txtSymbol_phi": "Фи", "DE.Controllers.Toolbar.txtSymbol_pi": "Пи", "DE.Controllers.Toolbar.txtSymbol_plus": "Плюс", "DE.Controllers.Toolbar.txtSymbol_pm": "Плюс и минус", "DE.Controllers.Toolbar.txtSymbol_propto": "Пропорционально", "DE.Controllers.Toolbar.txtSymbol_psi": "Пси", "DE.Controllers.Toolbar.txtSymbol_qdrt": "Корень четвертой степени", "DE.Controllers.Toolbar.txtSymbol_qed": "Что и требовалось доказать", "DE.Controllers.Toolbar.txtSymbol_rddots": "Диагональное многоточие вверх вправо", "DE.Controllers.Toolbar.txtSymbol_rho": "Ро", "DE.Controllers.Toolbar.txtSymbol_rightarrow": "Стрелка вправо", "DE.Controllers.Toolbar.txtSymbol_sigma": "Сигма", "DE.Controllers.Toolbar.txtSymbol_sqrt": "Знак радикала", "DE.Controllers.Toolbar.txtSymbol_tau": "Тау", "DE.Controllers.Toolbar.txtSymbol_therefore": "Следовательно", "DE.Controllers.Toolbar.txtSymbol_theta": "Тета", "DE.Controllers.Toolbar.txtSymbol_times": "Знак умножения", "DE.Controllers.Toolbar.txtSymbol_uparrow": "Стрелка вверх", "DE.Controllers.Toolbar.txtSymbol_upsilon": "Ипсилон", "DE.Controllers.Toolbar.txtSymbol_varepsilon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (вариант)", "DE.Controllers.Toolbar.txtSymbol_varphi": "Фи (вариант)", "DE.Controllers.Toolbar.txtSymbol_varpi": "Пи (вариант)", "DE.Controllers.Toolbar.txtSymbol_varrho": "Ро (вариант)", "DE.Controllers.Toolbar.txtSymbol_varsigma": "Сигма (вариант)", "DE.Controllers.Toolbar.txtSymbol_vartheta": "Тета (вариант)", "DE.Controllers.Toolbar.txtSymbol_vdots": "Вертикальное многоточие", "DE.Controllers.Toolbar.txtSymbol_xsi": "Кси", "DE.Controllers.Toolbar.txtSymbol_zeta": "Дзета", "DE.Controllers.Viewport.textFitPage": "По размеру страницы", "DE.Controllers.Viewport.textFitWidth": "По ширине", "DE.Controllers.Viewport.txtDarkMode": "Темный режим", "DE.Views.AddNewCaptionLabelDialog.textLabel": "Подпись:", "DE.Views.AddNewCaptionLabelDialog.textLabelError": "Подпись не должна быть пустой.", "DE.Views.BookmarksDialog.textAdd": "Добавить", "DE.Views.BookmarksDialog.textBookmarkName": "Имя закладки", "DE.Views.BookmarksDialog.textClose": "Закрыть", "DE.Views.BookmarksDialog.textCopy": "Копировать", "DE.Views.BookmarksDialog.textDelete": "Удалить", "DE.Views.BookmarksDialog.textGetLink": "Получить ссылку", "DE.Views.BookmarksDialog.textGoto": "Перейти", "DE.Views.BookmarksDialog.textHidden": "Скрытые закладки", "DE.Views.BookmarksDialog.textLocation": "Положение", "DE.Views.BookmarksDialog.textName": "Имя", "DE.Views.BookmarksDialog.textSort": "Порядок", "DE.Views.BookmarksDialog.textTitle": "Закладки", "DE.Views.BookmarksDialog.txtInvalidName": "Имя закладки может содержать только буквы, цифры и знаки подчеркивания и должно начинаться с буквы.", "DE.Views.CaptionDialog.textAdd": "Добавить", "DE.Views.CaptionDialog.textAfter": "После", "DE.Views.CaptionDialog.textBefore": "Перед", "DE.Views.CaptionDialog.textCaption": "Название", "DE.Views.CaptionDialog.textChapter": "Начинается со стиля:", "DE.Views.CaptionDialog.textChapterInc": "Включить номер главы", "DE.Views.CaptionDialog.textColon": "двоеточие", "DE.Views.CaptionDialog.textDash": "тире", "DE.Views.CaptionDialog.textDelete": "Удалить", "DE.Views.CaptionDialog.textEquation": "Уравнение", "DE.Views.CaptionDialog.textExamples": "Примеры: Таблица 2-A, <PERSON>з<PERSON>бражение 1.IV", "DE.Views.CaptionDialog.textExclude": "Исключить подпись из названия", "DE.Views.CaptionDialog.textFigure": "Рисунок", "DE.Views.CaptionDialog.textHyphen": "дефис", "DE.Views.CaptionDialog.textInsert": "Вставить", "DE.Views.CaptionDialog.textLabel": "Подпись", "DE.Views.CaptionDialog.textLongDash": "длинное тире", "DE.Views.CaptionDialog.textNumbering": "Нумерация", "DE.Views.CaptionDialog.textPeriod": "точка", "DE.Views.CaptionDialog.textSeparator": "Использовать разделитель", "DE.Views.CaptionDialog.textTable": "Таблица", "DE.Views.CaptionDialog.textTitle": "Вставить название", "DE.Views.CellsAddDialog.textCol": "Столбцы", "DE.Views.CellsAddDialog.textDown": "Под курсором", "DE.Views.CellsAddDialog.textLeft": "Слева", "DE.Views.CellsAddDialog.textRight": "Справа", "DE.Views.CellsAddDialog.textRow": "Строки", "DE.Views.CellsAddDialog.textTitle": "Вставить несколько", "DE.Views.CellsAddDialog.textUp": "Над курсором", "DE.Views.ChartSettings.text3dDepth": "Глубина (% от базовой)", "DE.Views.ChartSettings.text3dHeight": "Высота (% от базовой)", "DE.Views.ChartSettings.text3dRotation": "Трехмерный поворот", "DE.Views.ChartSettings.textAdvanced": "Дополнительные параметры", "DE.Views.ChartSettings.textAutoscale": "Автомасштабирование", "DE.Views.ChartSettings.textChartType": "Изменить тип диаграммы", "DE.Views.ChartSettings.textDefault": "Поворот по умолчанию", "DE.Views.ChartSettings.textDown": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ChartSettings.textEditData": "Изменить данные", "DE.Views.ChartSettings.textHeight": "Высота", "DE.Views.ChartSettings.textLeft": "Влево", "DE.Views.ChartSettings.textNarrow": "Сузить поле зрения", "DE.Views.ChartSettings.textOriginalSize": "Реальный размер", "DE.Views.ChartSettings.textPerspective": "Перспектива", "DE.Views.ChartSettings.textRight": "Вправо", "DE.Views.ChartSettings.textRightAngle": "Оси под прямым углом", "DE.Views.ChartSettings.textSize": "Размер", "DE.Views.ChartSettings.textStyle": "Стиль", "DE.Views.ChartSettings.textUndock": "Открепить от панели", "DE.Views.ChartSettings.textUp": "Ввер<PERSON>", "DE.Views.ChartSettings.textWiden": "Расширить поле зрения", "DE.Views.ChartSettings.textWidth": "Ши<PERSON><PERSON><PERSON>", "DE.Views.ChartSettings.textWrap": "Стиль обтекания", "DE.Views.ChartSettings.textX": "По оси X", "DE.Views.ChartSettings.textY": "По оси Y", "DE.Views.ChartSettings.txtBehind": "За текстом", "DE.Views.ChartSettings.txtInFront": "Перед текстом", "DE.Views.ChartSettings.txtInline": "В тексте", "DE.Views.ChartSettings.txtSquare": "Вокруг рамки", "DE.Views.ChartSettings.txtThrough": "Сквозное", "DE.Views.ChartSettings.txtTight": "По контуру", "DE.Views.ChartSettings.txtTitle": "Диаграмма", "DE.Views.ChartSettings.txtTopAndBottom": "Сверху и снизу", "DE.Views.ControlSettingsDialog.strGeneral": "Общие", "DE.Views.ControlSettingsDialog.textAdd": "Добавить", "DE.Views.ControlSettingsDialog.textAppearance": "Вид", "DE.Views.ControlSettingsDialog.textApplyAll": "Применить ко всем", "DE.Views.ControlSettingsDialog.textBox": "С ограничивающей рамкой", "DE.Views.ControlSettingsDialog.textChange": "Редактировать", "DE.Views.ControlSettingsDialog.textCheckbox": "Флажок", "DE.Views.ControlSettingsDialog.textChecked": "Символ установленного флажка", "DE.Views.ControlSettingsDialog.textColor": "Цвет", "DE.Views.ControlSettingsDialog.textCombobox": "Поле со списком", "DE.Views.ControlSettingsDialog.textDate": "Формат даты", "DE.Views.ControlSettingsDialog.textDelete": "Удалить", "DE.Views.ControlSettingsDialog.textDisplayName": "Отображаемое имя", "DE.Views.ControlSettingsDialog.textDown": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ControlSettingsDialog.textDropDown": "Выпадающий список", "DE.Views.ControlSettingsDialog.textFormat": "Отображать дату следующим образом", "DE.Views.ControlSettingsDialog.textLang": "Язык", "DE.Views.ControlSettingsDialog.textLock": "Блокировка", "DE.Views.ControlSettingsDialog.textName": "Заголовок", "DE.Views.ControlSettingsDialog.textNone": "Без рамки", "DE.Views.ControlSettingsDialog.textPlaceholder": "Заполнитель", "DE.Views.ControlSettingsDialog.textShowAs": "Отобража<PERSON>ь", "DE.Views.ControlSettingsDialog.textSystemColor": "Системный", "DE.Views.ControlSettingsDialog.textTag": "Тег", "DE.Views.ControlSettingsDialog.textTitle": "Параметры элемента управления содержимым", "DE.Views.ControlSettingsDialog.textUnchecked": "Символ снятого флажка", "DE.Views.ControlSettingsDialog.textUp": "Ввер<PERSON>", "DE.Views.ControlSettingsDialog.textValue": "Значение", "DE.Views.ControlSettingsDialog.tipChange": "Изменить символ", "DE.Views.ControlSettingsDialog.txtLockDelete": "Элемент управления содержимым нельзя удалить", "DE.Views.ControlSettingsDialog.txtLockEdit": "Содержимое нельзя редактировать", "DE.Views.CrossReferenceDialog.textAboveBelow": "Выше/ниже", "DE.Views.CrossReferenceDialog.textBookmark": "Закладка", "DE.Views.CrossReferenceDialog.textBookmarkText": "Текст закладки", "DE.Views.CrossReferenceDialog.textCaption": "Название целиком", "DE.Views.CrossReferenceDialog.textEmpty": "Ссылка запроса пуста.", "DE.Views.CrossReferenceDialog.textEndnote": "Концевая сноска", "DE.Views.CrossReferenceDialog.textEndNoteNum": "Номер концевой сноски", "DE.Views.CrossReferenceDialog.textEndNoteNumForm": "Номер концевой сноски (форм.)", "DE.Views.CrossReferenceDialog.textEquation": "Уравнение", "DE.Views.CrossReferenceDialog.textFigure": "Рисунок", "DE.Views.CrossReferenceDialog.textFootnote": "Сноска", "DE.Views.CrossReferenceDialog.textHeading": "Заголовок", "DE.Views.CrossReferenceDialog.textHeadingNum": "Номер заголовка", "DE.Views.CrossReferenceDialog.textHeadingNumFull": "Номер заголовка (полный)", "DE.Views.CrossReferenceDialog.textHeadingNumNo": "Номер заголовка (краткий)", "DE.Views.CrossReferenceDialog.textHeadingText": "Текст заголовка", "DE.Views.CrossReferenceDialog.textIncludeAbove": "Добавить слово \"выше\" или \"ниже\"", "DE.Views.CrossReferenceDialog.textInsert": "Вставить", "DE.Views.CrossReferenceDialog.textInsertAs": "Вставить как гиперссылку", "DE.Views.CrossReferenceDialog.textLabelNum": "Постоянная часть и номер", "DE.Views.CrossReferenceDialog.textNoteNum": "Номер сноски", "DE.Views.CrossReferenceDialog.textNoteNumForm": "Номер сноски (форм.)", "DE.Views.CrossReferenceDialog.textOnlyCaption": "Только текст названия", "DE.Views.CrossReferenceDialog.textPageNum": "Номер страницы", "DE.Views.CrossReferenceDialog.textParagraph": "Нумерованный список", "DE.Views.CrossReferenceDialog.textParaNum": "Номер абзаца", "DE.Views.CrossReferenceDialog.textParaNumFull": "Номер абзаца (полный)", "DE.Views.CrossReferenceDialog.textParaNumNo": "Номер абзаца (краткий)", "DE.Views.CrossReferenceDialog.textSeparate": "Разделитель номеров", "DE.Views.CrossReferenceDialog.textTable": "Таблица", "DE.Views.CrossReferenceDialog.textText": "Текст абзаца", "DE.Views.CrossReferenceDialog.textWhich": "Для какого названия", "DE.Views.CrossReferenceDialog.textWhichBookmark": "Для какой закладки", "DE.Views.CrossReferenceDialog.textWhichEndnote": "Для какой концевой сноски", "DE.Views.CrossReferenceDialog.textWhichHeading": "Для какого заголовка", "DE.Views.CrossReferenceDialog.textWhichNote": "Для какой сноски", "DE.Views.CrossReferenceDialog.textWhichPara": "Для какого абзаца", "DE.Views.CrossReferenceDialog.txtReference": "Вставить ссылку на", "DE.Views.CrossReferenceDialog.txtTitle": "Перекрестная ссылка", "DE.Views.CrossReferenceDialog.txtType": "Тип ссылки", "DE.Views.CustomColumnsDialog.textColumns": "Количество колонок", "DE.Views.CustomColumnsDialog.textSeparator": "Разделитель", "DE.Views.CustomColumnsDialog.textSpacing": "Интервал между колонками", "DE.Views.CustomColumnsDialog.textTitle": "Колонки", "DE.Views.DateTimeDialog.confirmDefault": "Задать формат по умолчанию для {0}: \"{1}\"", "DE.Views.DateTimeDialog.textDefault": "Установить по умолчанию", "DE.Views.DateTimeDialog.textFormat": "Форматы", "DE.Views.DateTimeDialog.textLang": "Язык", "DE.Views.DateTimeDialog.textUpdate": "Обновлять автоматически", "DE.Views.DateTimeDialog.txtTitle": "Дата и время", "DE.Views.DocProtection.hintProtectDoc": "Защитить документ", "DE.Views.DocProtection.txtDocProtectedComment": "Документ защищен.<br>Вы можете только добавлять комментарии к этому документу.", "DE.Views.DocProtection.txtDocProtectedForms": "Документ защищен.<br>Вы можете только заполнять формы в этом документе.", "DE.Views.DocProtection.txtDocProtectedTrack": "Документ защищен.<br>Вы можете редактировать этот документ, но все изменения будут отслеживаться.", "DE.Views.DocProtection.txtDocProtectedView": "Документ защищен.<br>Вы можете только просматривать этот документ.", "DE.Views.DocProtection.txtDocUnlockDescription": "Введите пароль, чтобы снять защиту документа", "DE.Views.DocProtection.txtProtectDoc": "Защитить документ", "DE.Views.DocProtection.txtUnlockTitle": "Снять защиту документа", "DE.Views.DocumentHolder.aboveText": "Выше", "DE.Views.DocumentHolder.addCommentText": "Добавить комментарий", "DE.Views.DocumentHolder.advancedDropCapText": "Параметры буквицы", "DE.Views.DocumentHolder.advancedEquationText": "Параметры уравнений", "DE.Views.DocumentHolder.advancedFrameText": "Дополнительные параметры рамки", "DE.Views.DocumentHolder.advancedParagraphText": "Дополнительные параметры абзаца", "DE.Views.DocumentHolder.advancedTableText": "Дополнительные параметры таблицы", "DE.Views.DocumentHolder.advancedText": "Дополнительные параметры", "DE.Views.DocumentHolder.alignmentText": "Выравнивание", "DE.Views.DocumentHolder.allLinearText": "Все - линейный", "DE.Views.DocumentHolder.allProfText": "Все - профессиональный", "DE.Views.DocumentHolder.belowText": "Ниже", "DE.Views.DocumentHolder.breakBeforeText": "С новой страницы", "DE.Views.DocumentHolder.bulletsText": "Маркеры и нумерация", "DE.Views.DocumentHolder.cellAlignText": "Вертикальное выравнивание в ячейках", "DE.Views.DocumentHolder.cellText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.centerText": "По центру", "DE.Views.DocumentHolder.chartText": "Дополнительные параметры диаграммы", "DE.Views.DocumentHolder.columnText": "Столбец", "DE.Views.DocumentHolder.currLinearText": "Текущее - линейный", "DE.Views.DocumentHolder.currProfText": "Текущее - профессиональный", "DE.Views.DocumentHolder.deleteColumnText": "Удалить столбец", "DE.Views.DocumentHolder.deleteRowText": "Удалить строку", "DE.Views.DocumentHolder.deleteTableText": "Удалить таблицу", "DE.Views.DocumentHolder.deleteText": "Удалить", "DE.Views.DocumentHolder.direct270Text": "Повернуть текст вверх", "DE.Views.DocumentHolder.direct90Text": "Повернуть текст вниз", "DE.Views.DocumentHolder.directHText": "Горизонтальное", "DE.Views.DocumentHolder.directionText": "Направление текста", "DE.Views.DocumentHolder.editChartText": "Изменить данные", "DE.Views.DocumentHolder.editFooterText": "Изменить нижний колонтитул", "DE.Views.DocumentHolder.editHeaderText": "Изменить верхний колонтитул", "DE.Views.DocumentHolder.editHyperlinkText": "Изменить гиперссылку", "DE.Views.DocumentHolder.eqToInlineText": "Изменить на встроенный", "DE.Views.DocumentHolder.guestText": "Гость", "DE.Views.DocumentHolder.hyperlinkText": "Гиперссылка", "DE.Views.DocumentHolder.ignoreAllSpellText": "Пропустить все", "DE.Views.DocumentHolder.ignoreSpellText": "Пропустить", "DE.Views.DocumentHolder.imageText": "Дополнительные параметры изображения", "DE.Views.DocumentHolder.insertColumnLeftText": "Столбец слева", "DE.Views.DocumentHolder.insertColumnRightText": "Столбец справа", "DE.Views.DocumentHolder.insertColumnText": "Вставить столбец", "DE.Views.DocumentHolder.insertRowAboveText": "Строку выше", "DE.Views.DocumentHolder.insertRowBelowText": "Строку ниже", "DE.Views.DocumentHolder.insertRowText": "Вставить строку", "DE.Views.DocumentHolder.insertText": "Добавить", "DE.Views.DocumentHolder.keepLinesText": "Не разрывать абзац", "DE.Views.DocumentHolder.langText": "Выбрать язык", "DE.Views.DocumentHolder.latexText": "LaTeX", "DE.Views.DocumentHolder.leftText": "По левому краю", "DE.Views.DocumentHolder.loadSpellText": "Загрузка вариантов...", "DE.Views.DocumentHolder.mergeCellsText": "Объединить ячейки", "DE.Views.DocumentHolder.moreText": "Больше вариантов...", "DE.Views.DocumentHolder.noSpellVariantsText": "Нет вариа<PERSON><PERSON>ов", "DE.Views.DocumentHolder.notcriticalErrorTitle": "Внимание", "DE.Views.DocumentHolder.originalSizeText": "Реальный размер", "DE.Views.DocumentHolder.paragraphText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.DocumentHolder.removeHyperlinkText": "Удалить гиперссылку", "DE.Views.DocumentHolder.rightText": "По правому краю", "DE.Views.DocumentHolder.rowText": "Строку", "DE.Views.DocumentHolder.saveStyleText": "Создать новый стиль", "DE.Views.DocumentHolder.selectCellText": "Выделить ячейку", "DE.Views.DocumentHolder.selectColumnText": "Выделить столбец", "DE.Views.DocumentHolder.selectRowText": "Выделить строку", "DE.Views.DocumentHolder.selectTableText": "Выделить таблицу", "DE.Views.DocumentHolder.selectText": "Выделить", "DE.Views.DocumentHolder.shapeText": "Дополнительные параметры фигуры", "DE.Views.DocumentHolder.spellcheckText": "Проверка орфографии", "DE.Views.DocumentHolder.splitCellsText": "Разделить ячейку...", "DE.Views.DocumentHolder.splitCellTitleText": "Разделить ячейку", "DE.Views.DocumentHolder.strDelete": "Удалить подпись", "DE.Views.DocumentHolder.strDetails": "Состав подписи", "DE.Views.DocumentHolder.strSetup": "Настройка подписи", "DE.Views.DocumentHolder.strSign": "Подписать", "DE.Views.DocumentHolder.styleText": "Форматирование как стиль", "DE.Views.DocumentHolder.tableText": "Табли<PERSON>у", "DE.Views.DocumentHolder.textAccept": "Принять изменение", "DE.Views.DocumentHolder.textAlign": "Выравнивание", "DE.Views.DocumentHolder.textArrange": "Порядок", "DE.Views.DocumentHolder.textArrangeBack": "Перенести на задний план", "DE.Views.DocumentHolder.textArrangeBackward": "Перенести назад", "DE.Views.DocumentHolder.textArrangeForward": "Перенести вперед", "DE.Views.DocumentHolder.textArrangeFront": "Перенести на передний план", "DE.Views.DocumentHolder.textCells": "Ячейки", "DE.Views.DocumentHolder.textCol": "Удалить весь столбец", "DE.Views.DocumentHolder.textContentControls": "Элемент управления содержимым", "DE.Views.DocumentHolder.textContinueNumbering": "Продолжить нумерацию", "DE.Views.DocumentHolder.textCopy": "Копировать", "DE.Views.DocumentHolder.textCrop": "Обрезать", "DE.Views.DocumentHolder.textCropFill": "Заливка", "DE.Views.DocumentHolder.textCropFit": "Вписать", "DE.Views.DocumentHolder.textCut": "Вырезать", "DE.Views.DocumentHolder.textDistributeCols": "Выровнять ширину столбцов", "DE.Views.DocumentHolder.textDistributeRows": "Выровнять высоту строк", "DE.Views.DocumentHolder.textEditControls": "Параметры элемента управления содержимым", "DE.Views.DocumentHolder.textEditPoints": "Изменить точки", "DE.Views.DocumentHolder.textEditWrapBoundary": "Изменить границу обтекания", "DE.Views.DocumentHolder.textFlipH": "Отразить слева направо", "DE.Views.DocumentHolder.textFlipV": "Отразить сверху вниз", "DE.Views.DocumentHolder.textFollow": "Перейти на прежнее место", "DE.Views.DocumentHolder.textFromFile": "Из файла", "DE.Views.DocumentHolder.textFromStorage": "Из хранилища", "DE.Views.DocumentHolder.textFromUrl": "По URL", "DE.Views.DocumentHolder.textJoinList": "Объединить с предыдущим списком", "DE.Views.DocumentHolder.textLeft": "Ячейки со сдвигом влево", "DE.Views.DocumentHolder.textNest": "Вставить как вложенную таблицу", "DE.Views.DocumentHolder.textNextPage": "Следующая страница", "DE.Views.DocumentHolder.textNumberingValue": "Начальное значение", "DE.Views.DocumentHolder.textPaste": "Вставить", "DE.Views.DocumentHolder.textPrevPage": "Предыдущая страница", "DE.Views.DocumentHolder.textRefreshField": "Обновить поле", "DE.Views.DocumentHolder.textReject": "Отклонить изменение", "DE.Views.DocumentHolder.textRemCheckBox": "Удалить флажок", "DE.Views.DocumentHolder.textRemComboBox": "Удалить поле со списком", "DE.Views.DocumentHolder.textRemDropdown": "Удалить выпадающий список", "DE.Views.DocumentHolder.textRemField": "Удалить текстовое поле", "DE.Views.DocumentHolder.textRemove": "Удалить", "DE.Views.DocumentHolder.textRemoveControl": "Удалить элемент управления содержимым", "DE.Views.DocumentHolder.textRemPicture": "Удалить изображение", "DE.Views.DocumentHolder.textRemRadioBox": "Удалить переключатель", "DE.Views.DocumentHolder.textReplace": "Заменить изображение", "DE.Views.DocumentHolder.textRotate": "Поворот", "DE.Views.DocumentHolder.textRotate270": "Повернуть на 90° против часовой стрелки", "DE.Views.DocumentHolder.textRotate90": "Повернуть на 90° по часовой стрелке", "DE.Views.DocumentHolder.textRow": "Удалить всю строку", "DE.Views.DocumentHolder.textSeparateList": "Разделить список", "DE.Views.DocumentHolder.textSettings": "Настройки", "DE.Views.DocumentHolder.textSeveral": "Несколько строк/столбцов", "DE.Views.DocumentHolder.textShapeAlignBottom": "Выровнять по нижнему краю", "DE.Views.DocumentHolder.textShapeAlignCenter": "Выровнять по центру", "DE.Views.DocumentHolder.textShapeAlignLeft": "Выровнять по левому краю", "DE.Views.DocumentHolder.textShapeAlignMiddle": "Выровнять по середине", "DE.Views.DocumentHolder.textShapeAlignRight": "Выровнять по правому краю", "DE.Views.DocumentHolder.textShapeAlignTop": "Выровнять по верхнему краю", "DE.Views.DocumentHolder.textStartNewList": "Начать новый список", "DE.Views.DocumentHolder.textStartNumberingFrom": "Задать начальное значение", "DE.Views.DocumentHolder.textTitleCellsRemove": "Удалить ячейки", "DE.Views.DocumentHolder.textTOC": "Оглавление", "DE.Views.DocumentHolder.textTOCSettings": "Параметры оглавления", "DE.Views.DocumentHolder.textUndo": "Отменить", "DE.Views.DocumentHolder.textUpdateAll": "Обновить целиком", "DE.Views.DocumentHolder.textUpdatePages": "Обновить только номера страниц", "DE.Views.DocumentHolder.textUpdateTOC": "Обновить оглавление", "DE.Views.DocumentHolder.textWrap": "Стиль обтекания", "DE.Views.DocumentHolder.tipIsLocked": "Этот элемент редактируется другим пользователем.", "DE.Views.DocumentHolder.toDictionaryText": "Добавить в словарь", "DE.Views.DocumentHolder.txtAddBottom": "Добавить нижнюю границу", "DE.Views.DocumentHolder.txtAddFractionBar": "Добавить дробную черту", "DE.Views.DocumentHolder.txtAddHor": "Добавить горизонтальную линию", "DE.Views.DocumentHolder.txtAddLB": "Добавить линию из левого нижнего угла", "DE.Views.DocumentHolder.txtAddLeft": "Добавить левую границу", "DE.Views.DocumentHolder.txtAddLT": "Добавить линию из левого верхнего угла", "DE.Views.DocumentHolder.txtAddRight": "Добавить правую границу", "DE.Views.DocumentHolder.txtAddTop": "Добавить верхнюю границу", "DE.Views.DocumentHolder.txtAddVer": "Добавить вертикальную линию", "DE.Views.DocumentHolder.txtAlignToChar": "Выравнивание по символу", "DE.Views.DocumentHolder.txtBehind": "За текстом", "DE.Views.DocumentHolder.txtBorderProps": "Свойства границ", "DE.Views.DocumentHolder.txtBottom": "По нижнему краю", "DE.Views.DocumentHolder.txtColumnAlign": "Выравнивание столбца", "DE.Views.DocumentHolder.txtDecreaseArg": "Уменьшить размер аргумента", "DE.Views.DocumentHolder.txtDeleteArg": "Удалить аргумент", "DE.Views.DocumentHolder.txtDeleteBreak": "Удалить принудительный разрыв", "DE.Views.DocumentHolder.txtDeleteChars": "Удалить вложенные знаки", "DE.Views.DocumentHolder.txtDeleteCharsAndSeparators": "Удалить вложенные знаки и разделители", "DE.Views.DocumentHolder.txtDeleteEq": "Удалить уравнение", "DE.Views.DocumentHolder.txtDeleteGroupChar": "Удалить символ", "DE.Views.DocumentHolder.txtDeleteRadical": "Удалить радикал", "DE.Views.DocumentHolder.txtDistribHor": "Распределить по горизонтали", "DE.Views.DocumentHolder.txtDistribVert": "Распределить по вертикали", "DE.Views.DocumentHolder.txtEmpty": "(Пусто)", "DE.Views.DocumentHolder.txtFractionLinear": "Изменить на горизонтальную простую дробь", "DE.Views.DocumentHolder.txtFractionSkewed": "Изменить на диагональную простую дробь", "DE.Views.DocumentHolder.txtFractionStacked": "Изменить на вертикальную простую дробь", "DE.Views.DocumentHolder.txtGroup": "Сгруппировать", "DE.Views.DocumentHolder.txtGroupCharOver": "Символ над текстом", "DE.Views.DocumentHolder.txtGroupCharUnder": "Символ под текстом", "DE.Views.DocumentHolder.txtHideBottom": "Скрыть нижнюю границу", "DE.Views.DocumentHolder.txtHideBottomLimit": "Скрыть нижний предел", "DE.Views.DocumentHolder.txtHideCloseBracket": "Скрыть закрывающую скобку", "DE.Views.DocumentHolder.txtHideDegree": "Скрыть степень", "DE.Views.DocumentHolder.txtHideHor": "Скрыть горизонтальную линию", "DE.Views.DocumentHolder.txtHideLB": "Скрыть линию из левого нижнего угла", "DE.Views.DocumentHolder.txtHideLeft": "Скрыть левую границу", "DE.Views.DocumentHolder.txtHideLT": "Скрыть линию из левого верхнего угла", "DE.Views.DocumentHolder.txtHideOpenBracket": "Скрыть открывающую скобку", "DE.Views.DocumentHolder.txtHidePlaceholder": "Скрыть поля для заполнения", "DE.Views.DocumentHolder.txtHideRight": "Скрыть правую границу", "DE.Views.DocumentHolder.txtHideTop": "Скрыть верхнюю границу", "DE.Views.DocumentHolder.txtHideTopLimit": "Скрыть верхний предел", "DE.Views.DocumentHolder.txtHideVer": "Скрыть вертикальную линию", "DE.Views.DocumentHolder.txtIncreaseArg": "Увеличить размер аргумента", "DE.Views.DocumentHolder.txtInFront": "Перед текстом", "DE.Views.DocumentHolder.txtInline": "В тексте", "DE.Views.DocumentHolder.txtInsertArgAfter": "Вставить аргумент после", "DE.Views.DocumentHolder.txtInsertArgBefore": "Вставить аргумент перед", "DE.Views.DocumentHolder.txtInsertBreak": "Вставить принудительный разрыв", "DE.Views.DocumentHolder.txtInsertCaption": "Вставить название", "DE.Views.DocumentHolder.txtInsertEqAfter": "Вставить уравнение после", "DE.Views.DocumentHolder.txtInsertEqBefore": "Вставить уравнение перед", "DE.Views.DocumentHolder.txtKeepTextOnly": "Сохранить только текст", "DE.Views.DocumentHolder.txtLimitChange": "Изменить положение пределов", "DE.Views.DocumentHolder.txtLimitOver": "Предел над текстом", "DE.Views.DocumentHolder.txtLimitUnder": "Предел под текстом", "DE.Views.DocumentHolder.txtMatchBrackets": "Изменить размер скобок в соответствии с высотой аргумента", "DE.Views.DocumentHolder.txtMatrixAlign": "Выравнивание матрицы", "DE.Views.DocumentHolder.txtOverbar": "Черта над текстом", "DE.Views.DocumentHolder.txtOverwriteCells": "Заменить содержимое ячеек", "DE.Views.DocumentHolder.txtPasteSourceFormat": "Сохранить исходное форматирование", "DE.Views.DocumentHolder.txtPressLink": "Нажмите {0} и щелкните по ссылке", "DE.Views.DocumentHolder.txtPrintSelection": "Напечатать выделенное", "DE.Views.DocumentHolder.txtRemFractionBar": "Удалить дробную черту", "DE.Views.DocumentHolder.txtRemLimit": "Удалить предел", "DE.Views.DocumentHolder.txtRemoveAccentChar": "Удалить диакритический знак", "DE.Views.DocumentHolder.txtRemoveBar": "Удалить черту", "DE.Views.DocumentHolder.txtRemoveWarning": "Вы хотите удалить эту подпись?<br>Это нельзя отменить.", "DE.Views.DocumentHolder.txtRemScripts": "Удалить индексы", "DE.Views.DocumentHolder.txtRemSubscript": "Удалить нижний индекс", "DE.Views.DocumentHolder.txtRemSuperscript": "Удалить верхний индекс", "DE.Views.DocumentHolder.txtScriptsAfter": "Индексы после текста", "DE.Views.DocumentHolder.txtScriptsBefore": "Индексы перед текстом", "DE.Views.DocumentHolder.txtShowBottomLimit": "Показать нижний предел", "DE.Views.DocumentHolder.txtShowCloseBracket": "Показать закрывающую скобку", "DE.Views.DocumentHolder.txtShowDegree": "Показать степень", "DE.Views.DocumentHolder.txtShowOpenBracket": "Показать открывающую скобку", "DE.Views.DocumentHolder.txtShowPlaceholder": "Показать поля для заполнения", "DE.Views.DocumentHolder.txtShowTopLimit": "Показать верхний предел", "DE.Views.DocumentHolder.txtSquare": "Вокруг рамки", "DE.Views.DocumentHolder.txtStretchBrackets": "Растянуть скобки", "DE.Views.DocumentHolder.txtThrough": "Сквозное", "DE.Views.DocumentHolder.txtTight": "По контуру", "DE.Views.DocumentHolder.txtTop": "По верхнему краю", "DE.Views.DocumentHolder.txtTopAndBottom": "Сверху и снизу", "DE.Views.DocumentHolder.txtUnderbar": "Черта под текстом", "DE.Views.DocumentHolder.txtUngroup": "Разгруппировать", "DE.Views.DocumentHolder.txtWarnUrl": "Переход по этой ссылке может нанести вред вашему устройству и данным.<br>Вы действительно хотите продолжить?", "DE.Views.DocumentHolder.unicodeText": "Юникод", "DE.Views.DocumentHolder.updateStyleText": "Обновить стиль %1", "DE.Views.DocumentHolder.vertAlignText": "Вертикальное выравнивание", "DE.Views.DropcapSettingsAdvanced.strBorders": "Границы и заливка", "DE.Views.DropcapSettingsAdvanced.strDropcap": "Буквица", "DE.Views.DropcapSettingsAdvanced.strMargins": "Поля", "DE.Views.DropcapSettingsAdvanced.textAlign": "Выравнивание", "DE.Views.DropcapSettingsAdvanced.textAtLeast": "Мини<PERSON>ум", "DE.Views.DropcapSettingsAdvanced.textAuto": "Авто", "DE.Views.DropcapSettingsAdvanced.textBackColor": "Цвет фона", "DE.Views.DropcapSettingsAdvanced.textBorderColor": "Цвет границ", "DE.Views.DropcapSettingsAdvanced.textBorderDesc": "Щелкайте по схеме или используйте кнопки, чтобы выбрать границы", "DE.Views.DropcapSettingsAdvanced.textBorderWidth": "<PERSON><PERSON><PERSON><PERSON><PERSON> границ", "DE.Views.DropcapSettingsAdvanced.textBottom": "Снизу", "DE.Views.DropcapSettingsAdvanced.textCenter": "По центру", "DE.Views.DropcapSettingsAdvanced.textColumn": "Столбца", "DE.Views.DropcapSettingsAdvanced.textDistance": "Расстояние до текста", "DE.Views.DropcapSettingsAdvanced.textExact": "Точно", "DE.Views.DropcapSettingsAdvanced.textFlow": "Плавающая рамка", "DE.Views.DropcapSettingsAdvanced.textFont": "<PERSON>ри<PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.textFrame": "Рамка", "DE.Views.DropcapSettingsAdvanced.textHeight": "Высота", "DE.Views.DropcapSettingsAdvanced.textHorizontal": "По горизонтали", "DE.Views.DropcapSettingsAdvanced.textInline": "Встроенная рамка", "DE.Views.DropcapSettingsAdvanced.textInMargin": "На поле", "DE.Views.DropcapSettingsAdvanced.textInText": "В тексте", "DE.Views.DropcapSettingsAdvanced.textLeft": "Слева", "DE.Views.DropcapSettingsAdvanced.textMargin": "Поля", "DE.Views.DropcapSettingsAdvanced.textMove": "Перемещать с текстом", "DE.Views.DropcapSettingsAdvanced.textNone": "Нет", "DE.Views.DropcapSettingsAdvanced.textPage": "Страницы", "DE.Views.DropcapSettingsAdvanced.textParagraph": "Абзаца", "DE.Views.DropcapSettingsAdvanced.textParameters": "Параметры", "DE.Views.DropcapSettingsAdvanced.textPosition": "Положение", "DE.Views.DropcapSettingsAdvanced.textRelative": "Относительно", "DE.Views.DropcapSettingsAdvanced.textRight": "Справа", "DE.Views.DropcapSettingsAdvanced.textRowHeight": "Высота в строках", "DE.Views.DropcapSettingsAdvanced.textTitle": "Буквица - дополнительные параметры", "DE.Views.DropcapSettingsAdvanced.textTitleFrame": "Рамка - дополнительные параметры", "DE.Views.DropcapSettingsAdvanced.textTop": "Сверху", "DE.Views.DropcapSettingsAdvanced.textVertical": "По вертикали", "DE.Views.DropcapSettingsAdvanced.textWidth": "Ши<PERSON><PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.tipFontName": "<PERSON>ри<PERSON><PERSON>", "DE.Views.DropcapSettingsAdvanced.txtNoBorders": "Без границ", "DE.Views.EditListItemDialog.textDisplayName": "Отображаемое имя", "DE.Views.EditListItemDialog.textNameError": "Отображаемое имя не должно быть пустым.", "DE.Views.EditListItemDialog.textValue": "Значение", "DE.Views.EditListItemDialog.textValueError": "Элемент с таким значением уже существует.", "DE.Views.FileMenu.btnBackCaption": "Открыть расположение файла", "DE.Views.FileMenu.btnCloseMenuCaption": "Закрыть меню", "DE.Views.FileMenu.btnCreateNewCaption": "Создать новый", "DE.Views.FileMenu.btnDownloadCaption": "Скачать как", "DE.Views.FileMenu.btnExitCaption": "Закрыть", "DE.Views.FileMenu.btnFileOpenCaption": "Открыть", "DE.Views.FileMenu.btnHelpCaption": "Справка", "DE.Views.FileMenu.btnHistoryCaption": "История версий", "DE.Views.FileMenu.btnInfoCaption": "Сведения о документе", "DE.Views.FileMenu.btnPrintCaption": "Печать", "DE.Views.FileMenu.btnProtectCaption": "Защитить", "DE.Views.FileMenu.btnRecentFilesCaption": "Открыть последние", "DE.Views.FileMenu.btnRenameCaption": "Переименовать", "DE.Views.FileMenu.btnReturnCaption": "Вернуться к документу", "DE.Views.FileMenu.btnRightsCaption": "Права доступа", "DE.Views.FileMenu.btnSaveAsCaption": "Сохранить как", "DE.Views.FileMenu.btnSaveCaption": "Сохранить", "DE.Views.FileMenu.btnSaveCopyAsCaption": "Сохранить копию как", "DE.Views.FileMenu.btnSettingsCaption": "Дополнительные параметры", "DE.Views.FileMenu.btnToEditCaption": "Редактировать", "DE.Views.FileMenu.textDownload": "Скачать", "DE.Views.FileMenuPanels.CreateNew.txtBlank": "Пустой документ", "DE.Views.FileMenuPanels.CreateNew.txtCreateNew": "Создать новый", "DE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Применить", "DE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Добавить автора", "DE.Views.FileMenuPanels.DocumentInfo.txtAddText": "Добавить текст", "DE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Приложение", "DE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Изменить права доступа", "DE.Views.FileMenuPanels.DocumentInfo.txtComment": "Комментарий", "DE.Views.FileMenuPanels.DocumentInfo.txtCreated": "Создан", "DE.Views.FileMenuPanels.DocumentInfo.txtFastWV": "Быстрый веб-просмотр", "DE.Views.FileMenuPanels.DocumentInfo.txtLoading": "Загрузка...", "DE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Автор последнего изменения", "DE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Последнее изменение", "DE.Views.FileMenuPanels.DocumentInfo.txtNo": "Нет", "DE.Views.FileMenuPanels.DocumentInfo.txtOwner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtPages": "Страницы", "DE.Views.FileMenuPanels.DocumentInfo.txtPageSize": "Размер страницы", "DE.Views.FileMenuPanels.DocumentInfo.txtParagraphs": "Аб<PERSON>а<PERSON>ы", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfProducer": "Производитель PDF", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfTagged": "PDF с тегами", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfVer": "Версия PDF", "DE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Размещение", "DE.Views.FileMenuPanels.DocumentInfo.txtRights": "Люди, имеющие права", "DE.Views.FileMenuPanels.DocumentInfo.txtSpaces": "Знаков с пробелами", "DE.Views.FileMenuPanels.DocumentInfo.txtStatistics": "Статистика", "DE.Views.FileMenuPanels.DocumentInfo.txtSubject": "Тема", "DE.Views.FileMenuPanels.DocumentInfo.txtSymbols": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.DocumentInfo.txtTags": "Теги", "DE.Views.FileMenuPanels.DocumentInfo.txtTitle": "Название", "DE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Загру<PERSON><PERSON>н", "DE.Views.FileMenuPanels.DocumentInfo.txtWords": "Слова", "DE.Views.FileMenuPanels.DocumentInfo.txtYes": "Да", "DE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Изменить права доступа", "DE.Views.FileMenuPanels.DocumentRights.txtRights": "Люди, имеющие права", "DE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Внимание", "DE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "C помощью пароля", "DE.Views.FileMenuPanels.ProtectDoc.strProtect": "Защитить документ", "DE.Views.FileMenuPanels.ProtectDoc.strSignature": "С помощью подписи", "DE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Редактировать документ", "DE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "При редактировании из документа будут удалены подписи.<br>Продолжить?", "DE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "Этот документ защищен паролем", "DE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "Этот документ требуется подписать.", "DE.Views.FileMenuPanels.ProtectDoc.txtSigned": "В документ добавлены действительные подписи. Документ защищен от редактирования.", "DE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Некоторые из цифровых подписей в документе недействительны или их нельзя проверить. Документ защищен от редактирования.", "DE.Views.FileMenuPanels.ProtectDoc.txtView": "Просмотр подписей", "DE.Views.FileMenuPanels.Settings.okButtonText": "Применить", "DE.Views.FileMenuPanels.Settings.strCoAuthMode": "Режим совместного редактирования", "DE.Views.FileMenuPanels.Settings.strFast": "Быстрый", "DE.Views.FileMenuPanels.Settings.strFontRender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> шрифтов", "DE.Views.FileMenuPanels.Settings.strIgnoreWordsInUPPERCASE": "Пропускать слова из ПРОПИСНЫХ БУКВ", "DE.Views.FileMenuPanels.Settings.strIgnoreWordsWithNumbers": "Пропускать слова с цифрами", "DE.Views.FileMenuPanels.Settings.strMacrosSettings": "Настройки макросов", "DE.Views.FileMenuPanels.Settings.strPasteButton": "Показывать кнопку Параметры вставки при вставке содержимого", "DE.Views.FileMenuPanels.Settings.strShowChanges": "Отображать изменения при совместной работе", "DE.Views.FileMenuPanels.Settings.strShowComments": "Показывать комментарии в тексте", "DE.Views.FileMenuPanels.Settings.strShowOthersChanges": "Показывать изменения других пользователей", "DE.Views.FileMenuPanels.Settings.strShowResolvedComments": "Показывать решенные комментарии", "DE.Views.FileMenuPanels.Settings.strStrict": "Строгий", "DE.Views.FileMenuPanels.Settings.strTheme": "Тема интерфейса", "DE.Views.FileMenuPanels.Settings.strUnit": "Единица измерения", "DE.Views.FileMenuPanels.Settings.strZoom": "Стандартное значение масштаба", "DE.Views.FileMenuPanels.Settings.text10Minutes": "Каждые 10 минут", "DE.Views.FileMenuPanels.Settings.text30Minutes": "Каждые 30 минут", "DE.Views.FileMenuPanels.Settings.text5Minutes": "Каждые 5 минут", "DE.Views.FileMenuPanels.Settings.text60Minutes": "Каждый час", "DE.Views.FileMenuPanels.Settings.textAlignGuides": "Направляющие выравнивания", "DE.Views.FileMenuPanels.Settings.textAutoRecover": "Автовосстановление", "DE.Views.FileMenuPanels.Settings.textAutoSave": "Автосохранение", "DE.Views.FileMenuPanels.Settings.textDisabled": "Отключено", "DE.Views.FileMenuPanels.Settings.textForceSave": "Сохранение промежуточных версий", "DE.Views.FileMenuPanels.Settings.textMinute": "Каждую минуту", "DE.Views.FileMenuPanels.Settings.textOldVersions": "Сделать файлы совместимыми с более старыми версиями MS Word при сохранении как DOCX", "DE.Views.FileMenuPanels.Settings.txtAll": "Все", "DE.Views.FileMenuPanels.Settings.txtAutoCorrect": "Параметры автозамены...", "DE.Views.FileMenuPanels.Settings.txtCacheMode": "Режим кэширования по умолчанию", "DE.Views.FileMenuPanels.Settings.txtChangesBalloons": "Показывать при клике в выносках", "DE.Views.FileMenuPanels.Settings.txtChangesTip": "Показывать при наведении в подсказках", "DE.Views.FileMenuPanels.Settings.txtCm": "Сантиметр", "DE.Views.FileMenuPanels.Settings.txtCollaboration": "Совместная работа", "DE.Views.FileMenuPanels.Settings.txtDarkMode": "Включить темный режим", "DE.Views.FileMenuPanels.Settings.txtEditingSaving": "Редактирование и сохранение", "DE.Views.FileMenuPanels.Settings.txtFastTip": "Совместное редактирование в режиме реального времени. Все изменения сохраняются автоматически", "DE.Views.FileMenuPanels.Settings.txtFitPage": "По размеру страницы", "DE.Views.FileMenuPanels.Settings.txtFitWidth": "По ширине", "DE.Views.FileMenuPanels.Settings.txtHieroglyphs": "Иероглифы", "DE.Views.FileMenuPanels.Settings.txtInch": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.txtLast": "Последние", "DE.Views.FileMenuPanels.Settings.txtMac": "как OS X", "DE.Views.FileMenuPanels.Settings.txtNative": "Собственный", "DE.Views.FileMenuPanels.Settings.txtNone": "Никакие", "DE.Views.FileMenuPanels.Settings.txtProofing": "Правописание", "DE.Views.FileMenuPanels.Settings.txtPt": "<PERSON>у<PERSON><PERSON><PERSON>", "DE.Views.FileMenuPanels.Settings.txtQuickPrint": "Показывать кнопку Быстрая печать в шапке редактора", "DE.Views.FileMenuPanels.Settings.txtQuickPrintTip": "Документ будет напечатан на последнем выбранном принтере или на принтере по умолчанию", "DE.Views.FileMenuPanels.Settings.txtRunMacros": "Включить все", "DE.Views.FileMenuPanels.Settings.txtRunMacrosDesc": "Включить все макросы без уведомления", "DE.Views.FileMenuPanels.Settings.txtShowTrackChanges": "Показывать изменения при рецензировании", "DE.Views.FileMenuPanels.Settings.txtSpellCheck": "Проверка орфографии", "DE.Views.FileMenuPanels.Settings.txtStopMacros": "Отключить все", "DE.Views.FileMenuPanels.Settings.txtStopMacrosDesc": "Отключить все макросы без уведомления", "DE.Views.FileMenuPanels.Settings.txtStrictTip": "Используйте кнопку \"Сохранить\" для синхронизации изменений, внесенных вами и другими пользователями.", "DE.Views.FileMenuPanels.Settings.txtUseAltKey": "Использовать клавишу Alt для навигации по интерфейсу с помощью клавиатуры", "DE.Views.FileMenuPanels.Settings.txtUseOptionKey": "Использовать клавишу Option для навигации по интерфейсу с помощью клавиатуры", "DE.Views.FileMenuPanels.Settings.txtWarnMacros": "Показывать уведомление", "DE.Views.FileMenuPanels.Settings.txtWarnMacrosDesc": "Отключить все макросы с уведомлением", "DE.Views.FileMenuPanels.Settings.txtWin": "как Windows", "DE.Views.FileMenuPanels.Settings.txtWorkspace": "Рабочая область", "DE.Views.FormSettings.textAlways": "Всегда", "DE.Views.FormSettings.textAnyone": "Любой", "DE.Views.FormSettings.textAspect": "Сохранять пропорции", "DE.Views.FormSettings.textAtLeast": "Мини<PERSON>ум", "DE.Views.FormSettings.textAuto": "Авто", "DE.Views.FormSettings.textAutofit": "Автоподбор", "DE.Views.FormSettings.textBackgroundColor": "Цвет фона", "DE.Views.FormSettings.textCheckbox": "Флажок", "DE.Views.FormSettings.textColor": "Цвет границ", "DE.Views.FormSettings.textComb": "Комбинировать символы", "DE.Views.FormSettings.textCombobox": "Поле со списком", "DE.Views.FormSettings.textComplex": "Составное поле", "DE.Views.FormSettings.textConnected": "Подключенные поля", "DE.Views.FormSettings.textCreditCard": "Номер кредитной карты (например, 4111-1111-1111-1111)", "DE.Views.FormSettings.textDateField": "Поле Дата и время", "DE.Views.FormSettings.textDateFormat": "Отображать дату следующим образом", "DE.Views.FormSettings.textDelete": "Удалить", "DE.Views.FormSettings.textDigits": "Цифры", "DE.Views.FormSettings.textDisconnect": "Отключить", "DE.Views.FormSettings.textDropDown": "Выпадающий список", "DE.Views.FormSettings.textExact": "Точно", "DE.Views.FormSettings.textField": "Текстовое поле", "DE.Views.FormSettings.textFillRoles": "Кто должен это заполнять?", "DE.Views.FormSettings.textFixed": "Поле фиксированного размера", "DE.Views.FormSettings.textFormat": "Формат", "DE.Views.FormSettings.textFormatSymbols": "Допустимые символы", "DE.Views.FormSettings.textFromFile": "Из файла", "DE.Views.FormSettings.textFromStorage": "Из хранилища", "DE.Views.FormSettings.textFromUrl": "По URL", "DE.Views.FormSettings.textGroupKey": "Ключ группы", "DE.Views.FormSettings.textImage": "Изображение", "DE.Views.FormSettings.textKey": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.FormSettings.textLang": "Язык", "DE.Views.FormSettings.textLetters": "Буквы", "DE.Views.FormSettings.textLock": "Заблокировать", "DE.Views.FormSettings.textMask": "Произвольная маска", "DE.Views.FormSettings.textMaxChars": "Макс. чис<PERSON><PERSON> знаков", "DE.Views.FormSettings.textMulti": "Многострочное поле", "DE.Views.FormSettings.textNever": "Никогда", "DE.Views.FormSettings.textNoBorder": "Без границ", "DE.Views.FormSettings.textNone": "Нет", "DE.Views.FormSettings.textPhone1": "Номер телефона (например, (*************)", "DE.Views.FormSettings.textPhone2": "Номер телефона (например, +447911123456)", "DE.Views.FormSettings.textPlaceholder": "Заполнитель", "DE.Views.FormSettings.textRadiobox": "Переключатель", "DE.Views.FormSettings.textReg": "Регулярное выражение", "DE.Views.FormSettings.textRequired": "Обязательно", "DE.Views.FormSettings.textScale": "Когда масштабировать", "DE.Views.FormSettings.textSelectImage": "Выбрать изображение", "DE.Views.FormSettings.textTag": "Тег", "DE.Views.FormSettings.textTip": "Подсказка", "DE.Views.FormSettings.textTipAdd": "Добавить новое значение", "DE.Views.FormSettings.textTipDelete": "Удалить значение", "DE.Views.FormSettings.textTipDown": "Переместить вниз", "DE.Views.FormSettings.textTipUp": "Переместить вверх", "DE.Views.FormSettings.textTooBig": "Изображение слишком большое", "DE.Views.FormSettings.textTooSmall": "Изображение слишком маленькое", "DE.Views.FormSettings.textUKPassport": "Номер паспорта Великобритании (например, *********)", "DE.Views.FormSettings.textUnlock": "Разблокировать", "DE.Views.FormSettings.textUSSSN": "SSN США (например, ***********)", "DE.Views.FormSettings.textValue": "Параметры значений", "DE.Views.FormSettings.textWidth": "<PERSON>и<PERSON><PERSON>на ячейки", "DE.Views.FormSettings.textZipCodeUS": "Почтовый индекс США (например, 92663 или 92663-1234)", "DE.Views.FormsTab.capBtnCheckBox": "Флажок", "DE.Views.FormsTab.capBtnComboBox": "Поле со списком", "DE.Views.FormsTab.capBtnComplex": "Составное поле", "DE.Views.FormsTab.capBtnDownloadForm": "Скачать как oform", "DE.Views.FormsTab.capBtnDropDown": "Выпадающий список", "DE.Views.FormsTab.capBtnEmail": "Адрес email", "DE.Views.FormsTab.capBtnImage": "Изображение", "DE.Views.FormsTab.capBtnManager": "Управление ролями", "DE.Views.FormsTab.capBtnNext": "Следующее поле", "DE.Views.FormsTab.capBtnPhone": "Номер телефона", "DE.Views.FormsTab.capBtnPrev": "Предыдущее поле", "DE.Views.FormsTab.capBtnRadioBox": "Переключатель", "DE.Views.FormsTab.capBtnSaveForm": "Сохранить как oform", "DE.Views.FormsTab.capBtnSubmit": "Отправить", "DE.Views.FormsTab.capBtnText": "Текстовое поле", "DE.Views.FormsTab.capBtnView": "Просмотреть форму", "DE.Views.FormsTab.capCreditCard": "Кредитная карта", "DE.Views.FormsTab.capDateTime": "Дата и время", "DE.Views.FormsTab.capZipCode": "Ин<PERSON><PERSON><PERSON>с", "DE.Views.FormsTab.textAnyone": "Любой", "DE.Views.FormsTab.textClear": "Очистить поля", "DE.Views.FormsTab.textClearFields": "Очистить все поля", "DE.Views.FormsTab.textCreateForm": "Добавьте поля и создайте заполняемый документ OFORM", "DE.Views.FormsTab.textGotIt": "ОК", "DE.Views.FormsTab.textHighlight": "Цвет подсветки", "DE.Views.FormsTab.textNoHighlight": "Без подсветки", "DE.Views.FormsTab.textRequired": "Заполните все обязательные поля для отправки формы.", "DE.Views.FormsTab.textSubmited": "Форма успешно отправлена", "DE.Views.FormsTab.tipCheckBox": "Вставить флажок", "DE.Views.FormsTab.tipComboBox": "Вставить поле со списком", "DE.Views.FormsTab.tipComplexField": "Вставить составное поле", "DE.Views.FormsTab.tipCreditCard": "Вставить номер кредитной карты", "DE.Views.FormsTab.tipDateTime": "Вставить дату и время", "DE.Views.FormsTab.tipDownloadForm": "Скачать файл как заполняемый документ OFORM", "DE.Views.FormsTab.tipDropDown": "Вставить выпадающий список", "DE.Views.FormsTab.tipEmailField": "Вставить адрес email", "DE.Views.FormsTab.tipFixedText": "Вставить фиксированное текстовое поле", "DE.Views.FormsTab.tipImageField": "Вставить изображение", "DE.Views.FormsTab.tipInlineText": "Вставить встроенное текстовое поле", "DE.Views.FormsTab.tipManager": "Управление ролями", "DE.Views.FormsTab.tipNextForm": "Перейти к следующему полю", "DE.Views.FormsTab.tipPhoneField": "Вставить номер телефона", "DE.Views.FormsTab.tipPrevForm": "Перейти к предыдущему полю", "DE.Views.FormsTab.tipRadioBox": "Вставить переключатель", "DE.Views.FormsTab.tipSaveForm": "Сохранить файл как заполняемый документ OFORM", "DE.Views.FormsTab.tipSubmit": "Отправить форму", "DE.Views.FormsTab.tipTextField": "Вставить текстовое поле", "DE.Views.FormsTab.tipViewForm": "Просмотреть форму", "DE.Views.FormsTab.tipZipCode": "Вставить индекс", "DE.Views.FormsTab.txtFixedDesc": "Вставить фиксированное текстовое поле", "DE.Views.FormsTab.txtFixedText": "Фиксированное", "DE.Views.FormsTab.txtInlineDesc": "Вставить встроенное текстовое поле", "DE.Views.FormsTab.txtInlineText": "Встроенное", "DE.Views.FormsTab.txtUntitled": "Без имени", "DE.Views.HeaderFooterSettings.textBottomCenter": "Снизу по центру", "DE.Views.HeaderFooterSettings.textBottomLeft": "Снизу слева", "DE.Views.HeaderFooterSettings.textBottomPage": "Внизу страницы", "DE.Views.HeaderFooterSettings.textBottomRight": "Снизу справа", "DE.Views.HeaderFooterSettings.textDiffFirst": "Особый для первой страницы", "DE.Views.HeaderFooterSettings.textDiffOdd": "Разные для четных и нечетных", "DE.Views.HeaderFooterSettings.textFrom": "Начать с", "DE.Views.HeaderFooterSettings.textHeaderFromBottom": "Нижний колонтитул", "DE.Views.HeaderFooterSettings.textHeaderFromTop": "Верхний колонтитул", "DE.Views.HeaderFooterSettings.textInsertCurrent": "Вставить в текущей позиции", "DE.Views.HeaderFooterSettings.textOptions": "Параметры", "DE.Views.HeaderFooterSettings.textPageNum": "Вставка номера страницы", "DE.Views.HeaderFooterSettings.textPageNumbering": "Нумерация страниц", "DE.Views.HeaderFooterSettings.textPosition": "Положение", "DE.Views.HeaderFooterSettings.textPrev": "Продолжить", "DE.Views.HeaderFooterSettings.textSameAs": "Связать с предыдущим", "DE.Views.HeaderFooterSettings.textTopCenter": "Сверху по центру", "DE.Views.HeaderFooterSettings.textTopLeft": "Сверху слева", "DE.Views.HeaderFooterSettings.textTopPage": "Вверху страницы", "DE.Views.HeaderFooterSettings.textTopRight": "Сверху справа", "DE.Views.HyperlinkSettingsDialog.textDefault": "Выделенный фрагмент текста", "DE.Views.HyperlinkSettingsDialog.textDisplay": "Отобража<PERSON>ь", "DE.Views.HyperlinkSettingsDialog.textExternal": "Внешняя ссылка", "DE.Views.HyperlinkSettingsDialog.textInternal": "Место в документе", "DE.Views.HyperlinkSettingsDialog.textTitle": "Параметры гиперссылки", "DE.Views.HyperlinkSettingsDialog.textTooltip": "Текст подсказки", "DE.Views.HyperlinkSettingsDialog.textUrl": "Связать с", "DE.Views.HyperlinkSettingsDialog.txtBeginning": "Начало документа", "DE.Views.HyperlinkSettingsDialog.txtBookmarks": "Закладки", "DE.Views.HyperlinkSettingsDialog.txtEmpty": "Это поле обязательно для заполнения", "DE.Views.HyperlinkSettingsDialog.txtHeadings": "Заголовки", "DE.Views.HyperlinkSettingsDialog.txtNotUrl": "Это поле должно быть URL-адресом в формате \"http://www.example.com\"", "DE.Views.HyperlinkSettingsDialog.txtSizeLimit": "Это поле может содержать не более 2083 символов", "DE.Views.ImageSettings.textAdvanced": "Дополнительные параметры", "DE.Views.ImageSettings.textCrop": "Обрезать", "DE.Views.ImageSettings.textCropFill": "Заливка", "DE.Views.ImageSettings.textCropFit": "Вписать", "DE.Views.ImageSettings.textCropToShape": "Обрезать по фигуре", "DE.Views.ImageSettings.textEdit": "Редактировать", "DE.Views.ImageSettings.textEditObject": "Редактировать объект", "DE.Views.ImageSettings.textFitMargins": "Вписать", "DE.Views.ImageSettings.textFlip": "Отразить", "DE.Views.ImageSettings.textFromFile": "Из файла", "DE.Views.ImageSettings.textFromStorage": "Из хранилища", "DE.Views.ImageSettings.textFromUrl": "По URL", "DE.Views.ImageSettings.textHeight": "Высота", "DE.Views.ImageSettings.textHint270": "Повернуть на 90° против часовой стрелки", "DE.Views.ImageSettings.textHint90": "Повернуть на 90° по часовой стрелке", "DE.Views.ImageSettings.textHintFlipH": "Отразить слева направо", "DE.Views.ImageSettings.textHintFlipV": "Отразить сверху вниз", "DE.Views.ImageSettings.textInsert": "Заменить изображение", "DE.Views.ImageSettings.textOriginalSize": "Реальный размер", "DE.Views.ImageSettings.textRecentlyUsed": "Последние использованные", "DE.Views.ImageSettings.textRotate90": "Повернуть на 90°", "DE.Views.ImageSettings.textRotation": "Поворот", "DE.Views.ImageSettings.textSize": "Размер", "DE.Views.ImageSettings.textWidth": "Ши<PERSON><PERSON><PERSON>", "DE.Views.ImageSettings.textWrap": "Стиль обтекания", "DE.Views.ImageSettings.txtBehind": "За текстом", "DE.Views.ImageSettings.txtInFront": "Перед текстом", "DE.Views.ImageSettings.txtInline": "В тексте", "DE.Views.ImageSettings.txtSquare": "Вокруг рамки", "DE.Views.ImageSettings.txtThrough": "Сквозное", "DE.Views.ImageSettings.txtTight": "По контуру", "DE.Views.ImageSettings.txtTopAndBottom": "Сверху и снизу", "DE.Views.ImageSettingsAdvanced.strMargins": "Поля вокруг текста", "DE.Views.ImageSettingsAdvanced.textAbsoluteWH": "Абсолютная", "DE.Views.ImageSettingsAdvanced.textAlignment": "Выравнивание", "DE.Views.ImageSettingsAdvanced.textAlt": "Альтернативный текст", "DE.Views.ImageSettingsAdvanced.textAltDescription": "Описание", "DE.Views.ImageSettingsAdvanced.textAltTip": "Альтернативное текстовое представление информации о визуальном объекте, которое будет зачитываться для людей с нарушениями зрения или когнитивными нарушениями, чтобы помочь им лучше понять, какую информацию содержит изображение, автофигура, диаграмма или таблица.", "DE.Views.ImageSettingsAdvanced.textAltTitle": "Заголовок", "DE.Views.ImageSettingsAdvanced.textAngle": "Угол", "DE.Views.ImageSettingsAdvanced.textArrows": "Стрелки", "DE.Views.ImageSettingsAdvanced.textAspectRatio": "Сохранять пропорции", "DE.Views.ImageSettingsAdvanced.textAutofit": "Автоподбор", "DE.Views.ImageSettingsAdvanced.textBeginSize": "Начальный размер", "DE.Views.ImageSettingsAdvanced.textBeginStyle": "Начальный стиль", "DE.Views.ImageSettingsAdvanced.textBelow": "ниже", "DE.Views.ImageSettingsAdvanced.textBevel": "Скошенный", "DE.Views.ImageSettingsAdvanced.textBottom": "Снизу", "DE.Views.ImageSettingsAdvanced.textBottomMargin": "Нижнего поля", "DE.Views.ImageSettingsAdvanced.textBtnWrap": "Обтекание текстом", "DE.Views.ImageSettingsAdvanced.textCapType": "Тип окончания", "DE.Views.ImageSettingsAdvanced.textCenter": "По центру", "DE.Views.ImageSettingsAdvanced.textCharacter": "Символа", "DE.Views.ImageSettingsAdvanced.textColumn": "Столбца", "DE.Views.ImageSettingsAdvanced.textDistance": "Расстояние до текста", "DE.Views.ImageSettingsAdvanced.textEndSize": "Конечный размер", "DE.Views.ImageSettingsAdvanced.textEndStyle": "Конечный стиль", "DE.Views.ImageSettingsAdvanced.textFlat": "Плоский", "DE.Views.ImageSettingsAdvanced.textFlipped": "Отраж<PERSON>но", "DE.Views.ImageSettingsAdvanced.textHeight": "Высота", "DE.Views.ImageSettingsAdvanced.textHorizontal": "По горизонтали", "DE.Views.ImageSettingsAdvanced.textHorizontally": "По горизонтали", "DE.Views.ImageSettingsAdvanced.textJoinType": "Тип соединения", "DE.Views.ImageSettingsAdvanced.textKeepRatio": "Сохранять пропорции", "DE.Views.ImageSettingsAdvanced.textLeft": "Слева", "DE.Views.ImageSettingsAdvanced.textLeftMargin": "Левого поля", "DE.Views.ImageSettingsAdvanced.textLine": "Строки", "DE.Views.ImageSettingsAdvanced.textLineStyle": "Стиль линии", "DE.Views.ImageSettingsAdvanced.textMargin": "Поля", "DE.Views.ImageSettingsAdvanced.textMiter": "Прям<PERSON>й", "DE.Views.ImageSettingsAdvanced.textMove": "Перемещать с текстом", "DE.Views.ImageSettingsAdvanced.textOptions": "Параметры", "DE.Views.ImageSettingsAdvanced.textOriginalSize": "Реальный размер", "DE.Views.ImageSettingsAdvanced.textOverlap": "Разрешить перекрытие", "DE.Views.ImageSettingsAdvanced.textPage": "Страницы", "DE.Views.ImageSettingsAdvanced.textParagraph": "Абзаца", "DE.Views.ImageSettingsAdvanced.textPosition": "Положение", "DE.Views.ImageSettingsAdvanced.textPositionPc": "Относительное положение", "DE.Views.ImageSettingsAdvanced.textRelative": "относительно", "DE.Views.ImageSettingsAdvanced.textRelativeWH": "Относительная", "DE.Views.ImageSettingsAdvanced.textResizeFit": "Подгонять размер фигуры под текст", "DE.Views.ImageSettingsAdvanced.textRight": "Справа", "DE.Views.ImageSettingsAdvanced.textRightMargin": "Правого поля", "DE.Views.ImageSettingsAdvanced.textRightOf": "справа от", "DE.Views.ImageSettingsAdvanced.textRotation": "Поворот", "DE.Views.ImageSettingsAdvanced.textRound": "Закругленный", "DE.Views.ImageSettingsAdvanced.textShape": "Параметры фигуры", "DE.Views.ImageSettingsAdvanced.textSize": "Размер", "DE.Views.ImageSettingsAdvanced.textSquare": "Квадр<PERSON>тный", "DE.Views.ImageSettingsAdvanced.textTextBox": "Текстовое поле", "DE.Views.ImageSettingsAdvanced.textTitle": "Изображение - дополнительные параметры", "DE.Views.ImageSettingsAdvanced.textTitleChart": "Диаграмма - дополнительные параметры", "DE.Views.ImageSettingsAdvanced.textTitleShape": "Фигура - дополнительные параметры", "DE.Views.ImageSettingsAdvanced.textTop": "Сверху", "DE.Views.ImageSettingsAdvanced.textTopMargin": "Верхнего поля", "DE.Views.ImageSettingsAdvanced.textVertical": "По вертикали", "DE.Views.ImageSettingsAdvanced.textVertically": "По вертикали", "DE.Views.ImageSettingsAdvanced.textWeightArrows": "Линии и стрелки", "DE.Views.ImageSettingsAdvanced.textWidth": "Ши<PERSON><PERSON><PERSON>", "DE.Views.ImageSettingsAdvanced.textWrap": "Стиль обтекания", "DE.Views.ImageSettingsAdvanced.textWrapBehindTooltip": "За текстом", "DE.Views.ImageSettingsAdvanced.textWrapInFrontTooltip": "Перед текстом", "DE.Views.ImageSettingsAdvanced.textWrapInlineTooltip": "В тексте", "DE.Views.ImageSettingsAdvanced.textWrapSquareTooltip": "Вокруг рамки", "DE.Views.ImageSettingsAdvanced.textWrapThroughTooltip": "Сквозное", "DE.Views.ImageSettingsAdvanced.textWrapTightTooltip": "По контуру", "DE.Views.ImageSettingsAdvanced.textWrapTopbottomTooltip": "Сверху и снизу", "DE.Views.LeftMenu.tipAbout": "О программе", "DE.Views.LeftMenu.tipChat": "Чат", "DE.Views.LeftMenu.tipComments": "Комментарии", "DE.Views.LeftMenu.tipNavigation": "Навигация", "DE.Views.LeftMenu.tipOutline": "Заголовки", "DE.Views.LeftMenu.tipPageThumbnails": "Эскизы страниц", "DE.Views.LeftMenu.tipPlugins": "Плагины", "DE.Views.LeftMenu.tipSearch": "Поиск", "DE.Views.LeftMenu.tipSupport": "Обратная связь и поддержка", "DE.Views.LeftMenu.tipTitles": "Заголовки", "DE.Views.LeftMenu.txtDeveloper": "РЕЖИМ РАЗРАБОТЧИКА", "DE.Views.LeftMenu.txtEditor": "Редактор документов", "DE.Views.LeftMenu.txtLimit": "Ограниченный доступ", "DE.Views.LeftMenu.txtTrial": "ПРОБНЫЙ РЕЖИМ", "DE.Views.LeftMenu.txtTrialDev": "Пробный режим разработчика", "DE.Views.LineNumbersDialog.textAddLineNumbering": "Добавить нумерацию строк", "DE.Views.LineNumbersDialog.textApplyTo": "Применить изменения к", "DE.Views.LineNumbersDialog.textContinuous": "Непрерывная", "DE.Views.LineNumbersDialog.textCountBy": "<PERSON>аг", "DE.Views.LineNumbersDialog.textDocument": "Ко всему документу", "DE.Views.LineNumbersDialog.textForward": "До конца документа", "DE.Views.LineNumbersDialog.textFromText": "От текста", "DE.Views.LineNumbersDialog.textNumbering": "Нумерация", "DE.Views.LineNumbersDialog.textRestartEachPage": "На каждой странице", "DE.Views.LineNumbersDialog.textRestartEachSection": "В каждом разделе", "DE.Views.LineNumbersDialog.textSection": "К текущему разделу", "DE.Views.LineNumbersDialog.textStartAt": "Начать с", "DE.Views.LineNumbersDialog.textTitle": "Нумерация строк", "DE.Views.LineNumbersDialog.txtAutoText": "Авто", "DE.Views.Links.capBtnAddText": "Добавить текст", "DE.Views.Links.capBtnBookmarks": "Закладка", "DE.Views.Links.capBtnCaption": "Название", "DE.Views.Links.capBtnContentsUpdate": "Обновить таблицу", "DE.Views.Links.capBtnCrossRef": "Перекрестная ссылка", "DE.Views.Links.capBtnInsContents": "Оглавление", "DE.Views.Links.capBtnInsFootnote": "Сноска", "DE.Views.Links.capBtnInsLink": "Гиперссылка", "DE.Views.Links.capBtnTOF": "Список иллюстраций", "DE.Views.Links.confirmDeleteFootnotes": "Вы хотите удалить все сноски?", "DE.Views.Links.confirmReplaceTOF": "Заменить выделенный список иллюстраций?", "DE.Views.Links.mniConvertNote": "Преобразовать все сноски", "DE.Views.Links.mniDelFootnote": "Удалить все сноски", "DE.Views.Links.mniInsEndnote": "Вставить концевую сноску", "DE.Views.Links.mniInsFootnote": "Вставить сноску", "DE.Views.Links.mniNoteSettings": "Параметры сносок", "DE.Views.Links.textContentsRemove": "Удалить оглавление", "DE.Views.Links.textContentsSettings": "Настройки", "DE.Views.Links.textConvertToEndnotes": "Преобразовать все обычные сноски в концевые сноски", "DE.Views.Links.textConvertToFootnotes": "Преобразовать все концевые сноски в обычные сноски", "DE.Views.Links.textGotoEndnote": "Перейти к концевым сноскам", "DE.Views.Links.textGotoFootnote": "Перейти к сноскам", "DE.Views.Links.textSwapNotes": "Поменять сноски", "DE.Views.Links.textUpdateAll": "Обновить целиком", "DE.Views.Links.textUpdatePages": "Обновить только номера страниц", "DE.Views.Links.tipAddText": "Включать заголовки в оглавление", "DE.Views.Links.tipBookmarks": "Создать закладку", "DE.Views.Links.tipCaption": "Вставить название", "DE.Views.Links.tipContents": "Вставить оглавление", "DE.Views.Links.tipContentsUpdate": "Обновить оглавление", "DE.Views.Links.tipCrossRef": "Вставить перекрестную ссылку", "DE.Views.Links.tipInsertHyperlink": "Добавить гиперссылку", "DE.Views.Links.tipNotes": "Вставить или редактировать сноски", "DE.Views.Links.tipTableFigures": "Вставить список иллюстраций", "DE.Views.Links.tipTableFiguresUpdate": "Обновить список иллюстраций", "DE.Views.Links.titleUpdateTOF": "Обновить список иллюстраций", "DE.Views.Links.txtDontShowTof": "Не включать в оглавление", "DE.Views.Links.txtLevel": "Уровень", "DE.Views.ListSettingsDialog.textAuto": "Автоматически", "DE.Views.ListSettingsDialog.textCenter": "По центру", "DE.Views.ListSettingsDialog.textLeft": "По левому краю", "DE.Views.ListSettingsDialog.textLevel": "Уровень", "DE.Views.ListSettingsDialog.textPreview": "Просмотр", "DE.Views.ListSettingsDialog.textRight": "По правому краю", "DE.Views.ListSettingsDialog.txtAlign": "Выравнивание", "DE.Views.ListSettingsDialog.txtBullet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.ListSettingsDialog.txtColor": "Цвет", "DE.Views.ListSettingsDialog.txtFont": "Шрифт и символ", "DE.Views.ListSettingsDialog.txtLikeText": "Как текст", "DE.Views.ListSettingsDialog.txtNewBullet": "Новый маркер", "DE.Views.ListSettingsDialog.txtNone": "Нет", "DE.Views.ListSettingsDialog.txtSize": "Размер", "DE.Views.ListSettingsDialog.txtSymbol": "Символ", "DE.Views.ListSettingsDialog.txtTitle": "Параметры списка", "DE.Views.ListSettingsDialog.txtType": "Тип", "DE.Views.MailMergeEmailDlg.filePlaceholder": "PDF", "DE.Views.MailMergeEmailDlg.okButtonText": "Отправить", "DE.Views.MailMergeEmailDlg.subjectPlaceholder": "Тема", "DE.Views.MailMergeEmailDlg.textAttachDocx": "Прикрепить как DOCX", "DE.Views.MailMergeEmailDlg.textAttachPdf": "Прикрепить как PDF", "DE.Views.MailMergeEmailDlg.textFileName": "Имя файла", "DE.Views.MailMergeEmailDlg.textFormat": "Формат", "DE.Views.MailMergeEmailDlg.textFrom": "От кого", "DE.Views.MailMergeEmailDlg.textHTML": "HTML", "DE.Views.MailMergeEmailDlg.textMessage": "Сообщение", "DE.Views.MailMergeEmailDlg.textSubject": "Тема", "DE.Views.MailMergeEmailDlg.textTitle": "Отправить по электронной почте", "DE.Views.MailMergeEmailDlg.textTo": "Кому", "DE.Views.MailMergeEmailDlg.textWarning": "Внимание!", "DE.Views.MailMergeEmailDlg.textWarningMsg": "После нажатия кнопки 'Отправить' отправку нельзя будет остановить.", "DE.Views.MailMergeSettings.downloadMergeTitle": "Слияние", "DE.Views.MailMergeSettings.errorMailMergeSaveFile": "Не удалось выполнить слияние.", "DE.Views.MailMergeSettings.notcriticalErrorTitle": "Внимание", "DE.Views.MailMergeSettings.textAddRecipients": "Сначала добавьте в список получателей", "DE.Views.MailMergeSettings.textAll": "Все записи", "DE.Views.MailMergeSettings.textCurrent": "Текущая запись", "DE.Views.MailMergeSettings.textDataSource": "Источник данных", "DE.Views.MailMergeSettings.textDocx": "Docx", "DE.Views.MailMergeSettings.textDownload": "Скачать", "DE.Views.MailMergeSettings.textEditData": "Изменить список получателей", "DE.Views.MailMergeSettings.textEmail": "Email", "DE.Views.MailMergeSettings.textFrom": "С", "DE.Views.MailMergeSettings.textGoToMail": "Перейти в Почту", "DE.Views.MailMergeSettings.textHighlight": "Выделить поля слияния", "DE.Views.MailMergeSettings.textInsertField": "Вставить поле слияния", "DE.Views.MailMergeSettings.textMaxRecepients": "Макс. 100 получателей.", "DE.Views.MailMergeSettings.textMerge": "Слияние", "DE.Views.MailMergeSettings.textMergeFields": "Поля слияния", "DE.Views.MailMergeSettings.textMergeTo": "Слияние в", "DE.Views.MailMergeSettings.textPdf": "PDF", "DE.Views.MailMergeSettings.textPortal": "Сохранить", "DE.Views.MailMergeSettings.textPreview": "Просмотр результатов", "DE.Views.MailMergeSettings.textReadMore": "Подробнее", "DE.Views.MailMergeSettings.textSendMsg": "Письма сформированы и будут отправлены в течение некоторого времени.<br>Скорость отправки писем зависит от вашего почтового сервиса.<br>Вы можете продолжить работу с документом или закрыть документ. По завершению процесса отправки вы получите уведомление на ваш почтовый ящик, использовавшийся при регистрации.", "DE.Views.MailMergeSettings.textTo": "По", "DE.Views.MailMergeSettings.txtFirst": "Первая запись", "DE.Views.MailMergeSettings.txtFromToError": "Значение \"С\" должно быть меньше значения \"По\"", "DE.Views.MailMergeSettings.txtLast": "Последняя запись", "DE.Views.MailMergeSettings.txtNext": "Следующая запись", "DE.Views.MailMergeSettings.txtPrev": "Предыдущая запись", "DE.Views.MailMergeSettings.txtUntitled": "Без имени", "DE.Views.MailMergeSettings.warnProcessMailMerge": "Не удалось начать слияние", "DE.Views.Navigation.strNavigate": "Заголовки", "DE.Views.Navigation.txtClosePanel": "Закрыть заголовки", "DE.Views.Navigation.txtCollapse": "Свернуть все", "DE.Views.Navigation.txtDemote": "Понизить уровень", "DE.Views.Navigation.txtEmpty": "В документе нет заголовков.<br>Примените стиль заголовка к тексту, чтобы он появился в оглавлении.", "DE.Views.Navigation.txtEmptyItem": "Пустой заголовок", "DE.Views.Navigation.txtEmptyViewer": "В документе нет заголовков.", "DE.Views.Navigation.txtExpand": "Развернуть все", "DE.Views.Navigation.txtExpandToLevel": "Развернуть до уровня", "DE.Views.Navigation.txtFontSize": "Размер шрифта", "DE.Views.Navigation.txtHeadingAfter": "Новый заголовок после", "DE.Views.Navigation.txtHeadingBefore": "Новый заголовок перед", "DE.Views.Navigation.txtLarge": "Больш<PERSON>й", "DE.Views.Navigation.txtMedium": "Средний", "DE.Views.Navigation.txtNewHeading": "Новый подзаголовок", "DE.Views.Navigation.txtPromote": "Повысить уровень", "DE.Views.Navigation.txtSelect": "Выделить содержимое", "DE.Views.Navigation.txtSettings": "Параметры заголовков", "DE.Views.Navigation.txtSmall": "Маленький", "DE.Views.Navigation.txtWrapHeadings": "Переносить длинные заголовки", "DE.Views.NoteSettingsDialog.textApply": "Применить", "DE.Views.NoteSettingsDialog.textApplyTo": "Применить изменения", "DE.Views.NoteSettingsDialog.textContinue": "Непрерывная", "DE.Views.NoteSettingsDialog.textCustom": "Особый символ", "DE.Views.NoteSettingsDialog.textDocEnd": "В конце документа", "DE.Views.NoteSettingsDialog.textDocument": "Ко всему документу", "DE.Views.NoteSettingsDialog.textEachPage": "На каждой странице", "DE.Views.NoteSettingsDialog.textEachSection": "В каждом разделе", "DE.Views.NoteSettingsDialog.textEndnote": "Концевая сноска", "DE.Views.NoteSettingsDialog.textFootnote": "Сноска", "DE.Views.NoteSettingsDialog.textFormat": "Формат", "DE.Views.NoteSettingsDialog.textInsert": "Вставить", "DE.Views.NoteSettingsDialog.textLocation": "Положение", "DE.Views.NoteSettingsDialog.textNumbering": "Нумерация", "DE.Views.NoteSettingsDialog.textNumFormat": "Формат номера", "DE.Views.NoteSettingsDialog.textPageBottom": "Внизу страницы", "DE.Views.NoteSettingsDialog.textSectEnd": "В конце раздела", "DE.Views.NoteSettingsDialog.textSection": "К текущему разделу", "DE.Views.NoteSettingsDialog.textStart": "Начать с", "DE.Views.NoteSettingsDialog.textTextBottom": "Под текстом", "DE.Views.NoteSettingsDialog.textTitle": "Параметры сносок", "DE.Views.NotesRemoveDialog.textEnd": "Удалить все концевые сноски", "DE.Views.NotesRemoveDialog.textFoot": "Удалить все обычные сноски", "DE.Views.NotesRemoveDialog.textTitle": "Удалить сноски", "DE.Views.PageMarginsDialog.notcriticalErrorTitle": "Внимание", "DE.Views.PageMarginsDialog.textBottom": "Нижнее", "DE.Views.PageMarginsDialog.textGutter": "Переплет", "DE.Views.PageMarginsDialog.textGutterPosition": "Положение переплета", "DE.Views.PageMarginsDialog.textInside": "Внутреннее", "DE.Views.PageMarginsDialog.textLandscape": "Альбомная", "DE.Views.PageMarginsDialog.textLeft": "Левое", "DE.Views.PageMarginsDialog.textMirrorMargins": "Зеркальные поля", "DE.Views.PageMarginsDialog.textMultiplePages": "Несколько страниц", "DE.Views.PageMarginsDialog.textNormal": "Обычные", "DE.Views.PageMarginsDialog.textOrientation": "Ориентация", "DE.Views.PageMarginsDialog.textOutside": "Внешнее", "DE.Views.PageMarginsDialog.textPortrait": "Книжная", "DE.Views.PageMarginsDialog.textPreview": "Просмотр", "DE.Views.PageMarginsDialog.textRight": "Правое", "DE.Views.PageMarginsDialog.textTitle": "Поля", "DE.Views.PageMarginsDialog.textTop": "Верхнее", "DE.Views.PageMarginsDialog.txtMarginsH": "Верхнее и нижнее поля слишком высокие для заданной высоты страницы", "DE.Views.PageMarginsDialog.txtMarginsW": "Левое и правое поля слишком широкие для заданной ширины страницы", "DE.Views.PageSizeDialog.textHeight": "Высота", "DE.Views.PageSizeDialog.textPreset": "Предустановка", "DE.Views.PageSizeDialog.textTitle": "Размер страницы", "DE.Views.PageSizeDialog.textWidth": "Ши<PERSON><PERSON><PERSON>", "DE.Views.PageSizeDialog.txtCustom": "Особый", "DE.Views.PageThumbnails.textClosePanel": "Закрыть эскизы страниц", "DE.Views.PageThumbnails.textHighlightVisiblePart": "Выделить видимую часть страницы", "DE.Views.PageThumbnails.textPageThumbnails": "Эскизы страниц", "DE.Views.PageThumbnails.textThumbnailsSettings": "Параметры эскизов", "DE.Views.PageThumbnails.textThumbnailsSize": "Размер эскизов", "DE.Views.ParagraphSettings.strIndent": "Отступы", "DE.Views.ParagraphSettings.strIndentsLeftText": "Слева", "DE.Views.ParagraphSettings.strIndentsRightText": "Справа", "DE.Views.ParagraphSettings.strIndentsSpecial": "Первая строка", "DE.Views.ParagraphSettings.strLineHeight": "Междустрочный интервал", "DE.Views.ParagraphSettings.strParagraphSpacing": "Интервал между абзацами", "DE.Views.ParagraphSettings.strSomeParagraphSpace": "Не добавлять интервал между абзацами одного стиля", "DE.Views.ParagraphSettings.strSpacingAfter": "После", "DE.Views.ParagraphSettings.strSpacingBefore": "Перед", "DE.Views.ParagraphSettings.textAdvanced": "Дополнительные параметры", "DE.Views.ParagraphSettings.textAt": "Значение", "DE.Views.ParagraphSettings.textAtLeast": "Мини<PERSON>ум", "DE.Views.ParagraphSettings.textAuto": "Множитель", "DE.Views.ParagraphSettings.textBackColor": "Цвет фона", "DE.Views.ParagraphSettings.textExact": "Точно", "DE.Views.ParagraphSettings.textFirstLine": "Отступ", "DE.Views.ParagraphSettings.textHanging": "Выступ", "DE.Views.ParagraphSettings.textNoneSpecial": "(нет)", "DE.Views.ParagraphSettings.txtAutoText": "Авто", "DE.Views.ParagraphSettingsAdvanced.noTabs": "В этом поле появятся позиции табуляции, которые вы зададите", "DE.Views.ParagraphSettingsAdvanced.strAllCaps": "Все прописные", "DE.Views.ParagraphSettingsAdvanced.strBorders": "Границы и заливка", "DE.Views.ParagraphSettingsAdvanced.strBreakBefore": "С новой страницы", "DE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "Двойное зачёркивание", "DE.Views.ParagraphSettingsAdvanced.strIndent": "Отступы", "DE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "Слева", "DE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Междустрочный интервал", "DE.Views.ParagraphSettingsAdvanced.strIndentsOutlinelevel": "Уровень структуры", "DE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "Справа", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "После", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "Перед", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Первая строка", "DE.Views.ParagraphSettingsAdvanced.strKeepLines": "Не разрывать абзац", "DE.Views.ParagraphSettingsAdvanced.strKeepNext": "Не отрывать от следующего", "DE.Views.ParagraphSettingsAdvanced.strMargins": "Внутренние поля", "DE.Views.ParagraphSettingsAdvanced.strOrphan": "Запрет висячих строк", "DE.Views.ParagraphSettingsAdvanced.strParagraphFont": "<PERSON>ри<PERSON><PERSON>", "DE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Отступы и интервалы", "DE.Views.ParagraphSettingsAdvanced.strParagraphLine": "Разрывы строк и страницы", "DE.Views.ParagraphSettingsAdvanced.strParagraphPosition": "Положение", "DE.Views.ParagraphSettingsAdvanced.strSmallCaps": "Малые прописные", "DE.Views.ParagraphSettingsAdvanced.strSomeParagraphSpace": "Не добавлять интервал между абзацами одного стиля", "DE.Views.ParagraphSettingsAdvanced.strSpacing": "Интервал между абзацами", "DE.Views.ParagraphSettingsAdvanced.strStrike": "Зачёркивание", "DE.Views.ParagraphSettingsAdvanced.strSubscript": "Подстрочные", "DE.Views.ParagraphSettingsAdvanced.strSuperscript": "Надстрочные", "DE.Views.ParagraphSettingsAdvanced.strSuppressLineNumbers": "Запретить нумерацию строк", "DE.Views.ParagraphSettingsAdvanced.strTabs": "Табуляция", "DE.Views.ParagraphSettingsAdvanced.textAlign": "Выравнивание", "DE.Views.ParagraphSettingsAdvanced.textAll": "Все", "DE.Views.ParagraphSettingsAdvanced.textAtLeast": "Мини<PERSON>ум", "DE.Views.ParagraphSettingsAdvanced.textAuto": "Множитель", "DE.Views.ParagraphSettingsAdvanced.textBackColor": "Цвет фона", "DE.Views.ParagraphSettingsAdvanced.textBodyText": "Основной текст", "DE.Views.ParagraphSettingsAdvanced.textBorderColor": "Цвет границ", "DE.Views.ParagraphSettingsAdvanced.textBorderDesc": "Щелкайте по схеме или используйте кнопки, чтобы выбрать границы и применить к ним выбранный стиль", "DE.Views.ParagraphSettingsAdvanced.textBorderWidth": "<PERSON><PERSON><PERSON><PERSON><PERSON> границ", "DE.Views.ParagraphSettingsAdvanced.textBottom": "Снизу", "DE.Views.ParagraphSettingsAdvanced.textCentered": "По центру", "DE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Межзнаковый интервал", "DE.Views.ParagraphSettingsAdvanced.textContext": "Контекстные", "DE.Views.ParagraphSettingsAdvanced.textContextDiscret": "Контекстные и дискреционные", "DE.Views.ParagraphSettingsAdvanced.textContextHistDiscret": "Контекстные, исторические и дискреционные", "DE.Views.ParagraphSettingsAdvanced.textContextHistorical": "Контекстные и исторические", "DE.Views.ParagraphSettingsAdvanced.textDefault": "По умолчанию", "DE.Views.ParagraphSettingsAdvanced.textDiscret": "Дискреционные", "DE.Views.ParagraphSettingsAdvanced.textEffects": "Эффекты", "DE.Views.ParagraphSettingsAdvanced.textExact": "Точно", "DE.Views.ParagraphSettingsAdvanced.textFirstLine": "Отступ", "DE.Views.ParagraphSettingsAdvanced.textHanging": "Выступ", "DE.Views.ParagraphSettingsAdvanced.textHistorical": "Исторические", "DE.Views.ParagraphSettingsAdvanced.textHistoricalDiscret": "Исторические и дискреционные", "DE.Views.ParagraphSettingsAdvanced.textJustified": "По ширине", "DE.Views.ParagraphSettingsAdvanced.textLeader": "Заполнитель", "DE.Views.ParagraphSettingsAdvanced.textLeft": "Слева", "DE.Views.ParagraphSettingsAdvanced.textLevel": "Уровень", "DE.Views.ParagraphSettingsAdvanced.textLigatures": "Лига<PERSON>уры", "DE.Views.ParagraphSettingsAdvanced.textNone": "Нет", "DE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(нет)", "DE.Views.ParagraphSettingsAdvanced.textOpenType": "Шрифты OpenType", "DE.Views.ParagraphSettingsAdvanced.textPosition": "Положение", "DE.Views.ParagraphSettingsAdvanced.textRemove": "Удалить", "DE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Удалить все", "DE.Views.ParagraphSettingsAdvanced.textRight": "Справа", "DE.Views.ParagraphSettingsAdvanced.textSet": "Задать", "DE.Views.ParagraphSettingsAdvanced.textSpacing": "Интервал", "DE.Views.ParagraphSettingsAdvanced.textStandard": "Только стандартные", "DE.Views.ParagraphSettingsAdvanced.textStandardContext": "Стандартные и контекстные", "DE.Views.ParagraphSettingsAdvanced.textStandardContextDiscret": "Стандартные, контекстные и дискреционные", "DE.Views.ParagraphSettingsAdvanced.textStandardContextHist": "Стандартные, контекстные и исторические", "DE.Views.ParagraphSettingsAdvanced.textStandardDiscret": "Стандартные и дискреционные", "DE.Views.ParagraphSettingsAdvanced.textStandardHistDiscret": "Стандартные, исторические и дискреционные", "DE.Views.ParagraphSettingsAdvanced.textStandardHistorical": "Стандартные и исторические", "DE.Views.ParagraphSettingsAdvanced.textTabCenter": "По центру", "DE.Views.ParagraphSettingsAdvanced.textTabLeft": "По левому краю", "DE.Views.ParagraphSettingsAdvanced.textTabPosition": "Позиция", "DE.Views.ParagraphSettingsAdvanced.textTabRight": "По правому краю", "DE.Views.ParagraphSettingsAdvanced.textTitle": "Абзац - дополнительные параметры", "DE.Views.ParagraphSettingsAdvanced.textTop": "Сверху", "DE.Views.ParagraphSettingsAdvanced.tipAll": "Задать внешнюю границу и все внутренние линии", "DE.Views.ParagraphSettingsAdvanced.tipBottom": "Задать только нижнюю границу", "DE.Views.ParagraphSettingsAdvanced.tipInner": "Задать только горизонтальные внутренние линии", "DE.Views.ParagraphSettingsAdvanced.tipLeft": "Задать только левую границу", "DE.Views.ParagraphSettingsAdvanced.tipNone": "Не задавать границ", "DE.Views.ParagraphSettingsAdvanced.tipOuter": "Задать только внешнюю границу", "DE.Views.ParagraphSettingsAdvanced.tipRight": "Задать только правую границу", "DE.Views.ParagraphSettingsAdvanced.tipTop": "Задать только верхнюю границу", "DE.Views.ParagraphSettingsAdvanced.txtAutoText": "Авто", "DE.Views.ParagraphSettingsAdvanced.txtNoBorders": "Без границ", "DE.Views.PrintWithPreview.textMarginsLast": "Последние настраиваемые", "DE.Views.PrintWithPreview.textMarginsModerate": "Средние", "DE.Views.PrintWithPreview.textMarginsNarrow": "Узкие", "DE.Views.PrintWithPreview.textMarginsNormal": "Обычные", "DE.Views.PrintWithPreview.textMarginsUsNormal": "Обычные (американский стандарт)", "DE.Views.PrintWithPreview.textMarginsWide": "Широкие", "DE.Views.PrintWithPreview.txtAllPages": "Все страницы", "DE.Views.PrintWithPreview.txtBottom": "Нижнее", "DE.Views.PrintWithPreview.txtCurrentPage": "Текущая страница", "DE.Views.PrintWithPreview.txtCustom": "Пользовательское", "DE.Views.PrintWithPreview.txtCustomPages": "Настраиваемая печать", "DE.Views.PrintWithPreview.txtLandscape": "Альбомная", "DE.Views.PrintWithPreview.txtLeft": "Левое", "DE.Views.PrintWithPreview.txtMargins": "Поля", "DE.Views.PrintWithPreview.txtOf": "из {0}", "DE.Views.PrintWithPreview.txtPage": "Страница", "DE.Views.PrintWithPreview.txtPageNumInvalid": "Неправильный номер страницы", "DE.Views.PrintWithPreview.txtPageOrientation": "Ориентация страницы", "DE.Views.PrintWithPreview.txtPages": "Страницы", "DE.Views.PrintWithPreview.txtPageSize": "Размер страницы", "DE.Views.PrintWithPreview.txtPortrait": "Книжная", "DE.Views.PrintWithPreview.txtPrint": "Печать", "DE.Views.PrintWithPreview.txtPrintPdf": "Печать в PDF", "DE.Views.PrintWithPreview.txtPrintRange": "Диапазон печати", "DE.Views.PrintWithPreview.txtRight": "Правое", "DE.Views.PrintWithPreview.txtSelection": "Выделенный фрагмент", "DE.Views.PrintWithPreview.txtTop": "Верхнее", "DE.Views.ProtectDialog.textComments": "Комментарии", "DE.Views.ProtectDialog.textForms": "Заполнение форм", "DE.Views.ProtectDialog.textReview": "Отслеживаемые изменения", "DE.Views.ProtectDialog.textView": "Только чтение", "DE.Views.ProtectDialog.txtAllow": "Разрешить только указанный способ редактирования документа", "DE.Views.ProtectDialog.txtIncorrectPwd": "Пароль и его подтверждение не совпадают", "DE.Views.ProtectDialog.txtLimit": "Пароль ограничен 15 символами", "DE.Views.ProtectDialog.txtOptional": "необязательно", "DE.Views.ProtectDialog.txtPassword": "Пароль", "DE.Views.ProtectDialog.txtProtect": "Защитить", "DE.Views.ProtectDialog.txtRepeat": "Повторить пароль", "DE.Views.ProtectDialog.txtTitle": "Защитить", "DE.Views.ProtectDialog.txtWarning": "Внимание: Если пароль забыт или утерян, его нельзя восстановить. Храните его в надежном месте.", "DE.Views.RightMenu.txtChartSettings": "Параметры диаграммы", "DE.Views.RightMenu.txtFormSettings": "Параметры формы", "DE.Views.RightMenu.txtHeaderFooterSettings": "Параметры верхнего и нижнего колонтитулов", "DE.Views.RightMenu.txtImageSettings": "Параметры изображения", "DE.Views.RightMenu.txtMailMergeSettings": "Параметры слияния", "DE.Views.RightMenu.txtParagraphSettings": "Параметры абзаца", "DE.Views.RightMenu.txtShapeSettings": "Параметры фигуры", "DE.Views.RightMenu.txtSignatureSettings": "Настройка подписи", "DE.Views.RightMenu.txtTableSettings": "Параметры таблицы", "DE.Views.RightMenu.txtTextArtSettings": "Параметры объекта Text Art", "DE.Views.RoleDeleteDlg.textLabel": "Чтобы удалить эту роль, надо перенести связанные с ней поля в другую роль.", "DE.Views.RoleDeleteDlg.textSelect": "Выберите для роли слияния полей", "DE.Views.RoleDeleteDlg.textTitle": "Удалить роль", "DE.Views.RoleEditDlg.errNameExists": "Роль с таким названием уже существует.", "DE.Views.RoleEditDlg.textEmptyError": "Название роли не может быть пустым.", "DE.Views.RoleEditDlg.textName": "Название роли", "DE.Views.RoleEditDlg.textNoHighlight": "Без подсветки", "DE.Views.RoleEditDlg.txtTitleEdit": "Редактировать роль", "DE.Views.RoleEditDlg.txtTitleNew": "Создать новую роль", "DE.Views.RolesManagerDlg.closeButtonText": "Закрыть", "DE.Views.RolesManagerDlg.textAnyone": "Любой", "DE.Views.RolesManagerDlg.textDelete": "Удалить", "DE.Views.RolesManagerDlg.textDeleteLast": "Вы действительно хотите удалить роль {0}?<br>После удаления будет создана роль по умолчанию.", "DE.Views.RolesManagerDlg.textDescription": "Добавьте роли и задайте порядок, в котором заполняющие получают и подписывают документ", "DE.Views.RolesManagerDlg.textDown": "Переместить роль вниз", "DE.Views.RolesManagerDlg.textEdit": "Редактировать", "DE.Views.RolesManagerDlg.textEmpty": "Еще не было создано ни одной роли.<br>Создайте хотя бы одну роль, и она появится в этом поле.", "DE.Views.RolesManagerDlg.textNew": "Создать", "DE.Views.RolesManagerDlg.textUp": "Переместить роль вверх", "DE.Views.RolesManagerDlg.txtTitle": "Управление ролями", "DE.Views.RolesManagerDlg.warnCantDelete": "Нельзя удалить эту роль, так как есть связанные с ней поля.", "DE.Views.RolesManagerDlg.warnDelete": "Вы действительно хотите удалить роль {0}?", "DE.Views.SaveFormDlg.saveButtonText": "Сохранить", "DE.Views.SaveFormDlg.textAnyone": "Любой", "DE.Views.SaveFormDlg.textDescription": "При сохранении в oform в список заполнения добавляются только роли с полями", "DE.Views.SaveFormDlg.textEmpty": "Нет ролей, связанных с этим полем.", "DE.Views.SaveFormDlg.textFill": "Список заполнения", "DE.Views.SaveFormDlg.txtTitle": "Сохранить как форму", "DE.Views.ShapeSettings.strBackground": "Цвет фона", "DE.Views.ShapeSettings.strChange": "Изменить автофигуру", "DE.Views.ShapeSettings.strColor": "Цвет", "DE.Views.ShapeSettings.strFill": "Заливка", "DE.Views.ShapeSettings.strForeground": "Цвет переднего плана", "DE.Views.ShapeSettings.strPattern": "Узор", "DE.Views.ShapeSettings.strShadow": "Отображать тень", "DE.Views.ShapeSettings.strSize": "Толщина", "DE.Views.ShapeSettings.strStroke": "<PERSON>он<PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.strTransparency": "Непрозрачность", "DE.Views.ShapeSettings.strType": "Тип", "DE.Views.ShapeSettings.textAdvanced": "Дополнительные параметры", "DE.Views.ShapeSettings.textAngle": "Угол", "DE.Views.ShapeSettings.textBorderSizeErr": "Введено некорректное значение.<br>Пожалуйста, введите значение от 0 до 1584 пунктов.", "DE.Views.ShapeSettings.textColor": "Заливка цветом", "DE.Views.ShapeSettings.textDirection": "Направление", "DE.Views.ShapeSettings.textEmptyPattern": "Без узора", "DE.Views.ShapeSettings.textFlip": "Отразить", "DE.Views.ShapeSettings.textFromFile": "Из файла", "DE.Views.ShapeSettings.textFromStorage": "Из хранилища", "DE.Views.ShapeSettings.textFromUrl": "По URL", "DE.Views.ShapeSettings.textGradient": "Точки градиента", "DE.Views.ShapeSettings.textGradientFill": "Градиентная заливка", "DE.Views.ShapeSettings.textHint270": "Повернуть на 90° против часовой стрелки", "DE.Views.ShapeSettings.textHint90": "Повернуть на 90° по часовой стрелке", "DE.Views.ShapeSettings.textHintFlipH": "Отразить слева направо", "DE.Views.ShapeSettings.textHintFlipV": "Отразить сверху вниз", "DE.Views.ShapeSettings.textImageTexture": "Изображение или текстура", "DE.Views.ShapeSettings.textLinear": "Линейный", "DE.Views.ShapeSettings.textNoFill": "Без заливки", "DE.Views.ShapeSettings.textPatternFill": "Узор", "DE.Views.ShapeSettings.textPosition": "Положение", "DE.Views.ShapeSettings.textRadial": "Радиальный", "DE.Views.ShapeSettings.textRecentlyUsed": "Последние использованные", "DE.Views.ShapeSettings.textRotate90": "Повернуть на 90°", "DE.Views.ShapeSettings.textRotation": "Поворот", "DE.Views.ShapeSettings.textSelectImage": "Выбрать изображение", "DE.Views.ShapeSettings.textSelectTexture": "Выбрать", "DE.Views.ShapeSettings.textStretch": "Растяжение", "DE.Views.ShapeSettings.textStyle": "Стиль", "DE.Views.ShapeSettings.textTexture": "Из текстуры", "DE.Views.ShapeSettings.textTile": "Плитка", "DE.Views.ShapeSettings.textWrap": "Стиль обтекания", "DE.Views.ShapeSettings.tipAddGradientPoint": "Добавить точку градиента", "DE.Views.ShapeSettings.tipRemoveGradientPoint": "Удалить точку градиента", "DE.Views.ShapeSettings.txtBehind": "За текстом", "DE.Views.ShapeSettings.txtBrownPaper": "Крафт-бумага", "DE.Views.ShapeSettings.txtCanvas": "Хол<PERSON>т", "DE.Views.ShapeSettings.txtCarton": "Карт<PERSON>н", "DE.Views.ShapeSettings.txtDarkFabric": "Темная ткань", "DE.Views.ShapeSettings.txtGrain": "Песок", "DE.Views.ShapeSettings.txtGranite": "<PERSON>р<PERSON><PERSON><PERSON><PERSON>", "DE.Views.ShapeSettings.txtGreyPaper": "Серая бумага", "DE.Views.ShapeSettings.txtInFront": "Перед текстом", "DE.Views.ShapeSettings.txtInline": "В тексте", "DE.Views.ShapeSettings.txtKnit": "Вязание", "DE.Views.ShapeSettings.txtLeather": "Кожа", "DE.Views.ShapeSettings.txtNoBorders": "Без контура", "DE.Views.ShapeSettings.txtPapyrus": "Папир<PERSON>с", "DE.Views.ShapeSettings.txtSquare": "Вокруг рамки", "DE.Views.ShapeSettings.txtThrough": "Сквозное", "DE.Views.ShapeSettings.txtTight": "По контуру", "DE.Views.ShapeSettings.txtTopAndBottom": "Сверху и снизу", "DE.Views.ShapeSettings.txtWood": "Дерево", "DE.Views.SignatureSettings.notcriticalErrorTitle": "Внимание", "DE.Views.SignatureSettings.strDelete": "Удалить подпись", "DE.Views.SignatureSettings.strDetails": "Состав подписи", "DE.Views.SignatureSettings.strInvalid": "Недействительные подписи", "DE.Views.SignatureSettings.strRequested": "Запрошенные подписи", "DE.Views.SignatureSettings.strSetup": "Настройка подписи", "DE.Views.SignatureSettings.strSign": "Подписать", "DE.Views.SignatureSettings.strSignature": "Подпись", "DE.Views.SignatureSettings.strSigner": "Подписывающий", "DE.Views.SignatureSettings.strValid": "Действительные подписи", "DE.Views.SignatureSettings.txtContinueEditing": "Все равно редактировать", "DE.Views.SignatureSettings.txtEditWarning": "При редактировании из документа будут удалены подписи.<br>Продолжить?", "DE.Views.SignatureSettings.txtRemoveWarning": "Вы хотите удалить эту подпись?<br>Это нельзя отменить.", "DE.Views.SignatureSettings.txtRequestedSignatures": "Этот документ требуется подписать.", "DE.Views.SignatureSettings.txtSigned": "В документ добавлены действительные подписи. Документ защищен от редактирования.", "DE.Views.SignatureSettings.txtSignedInvalid": "Некоторые из цифровых подписей в документе недействительны или их нельзя проверить. Документ защищен от редактирования.", "DE.Views.Statusbar.goToPageText": "Перейти на страницу", "DE.Views.Statusbar.pageIndexText": "Страни<PERSON><PERSON> {0} из {1}", "DE.Views.Statusbar.tipFitPage": "По размеру страницы", "DE.Views.Statusbar.tipFitWidth": "По ширине", "DE.Views.Statusbar.tipHandTool": "Инструмент \"Рука\"", "DE.Views.Statusbar.tipSelectTool": "Инструмент выделения", "DE.Views.Statusbar.tipSetLang": "Выбрать язык текста", "DE.Views.Statusbar.tipZoomFactor": "Масш<PERSON><PERSON><PERSON>", "DE.Views.Statusbar.tipZoomIn": "Увеличить", "DE.Views.Statusbar.tipZoomOut": "Уменьшить", "DE.Views.Statusbar.txtPageNumInvalid": "Неправильный номер страницы", "DE.Views.Statusbar.txtPages": "Страницы", "DE.Views.Statusbar.txtParagraphs": "Аб<PERSON>а<PERSON>ы", "DE.Views.Statusbar.txtSpaces": "Символы и пробелы", "DE.Views.Statusbar.txtSymbols": "Символы", "DE.Views.Statusbar.txtWordCount": "Количество слов", "DE.Views.Statusbar.txtWords": "Слова", "DE.Views.StyleTitleDialog.textHeader": "Создание нового стиля", "DE.Views.StyleTitleDialog.textNextStyle": "Стиль следующего абзаца", "DE.Views.StyleTitleDialog.textTitle": "Название", "DE.Views.StyleTitleDialog.txtEmpty": "Это поле необходимо заполнить", "DE.Views.StyleTitleDialog.txtNotEmpty": "Поле не может быть пустым", "DE.Views.StyleTitleDialog.txtSameAs": "Такой же, как создаваемый стиль", "DE.Views.TableFormulaDialog.textBookmark": "Вставить закладку", "DE.Views.TableFormulaDialog.textFormat": "Формат числа", "DE.Views.TableFormulaDialog.textFormula": "Формула", "DE.Views.TableFormulaDialog.textInsertFunction": "Вставить функцию", "DE.Views.TableFormulaDialog.textTitle": "Настройки формулы", "DE.Views.TableOfContentsSettings.strAlign": "Номера страниц по правому краю", "DE.Views.TableOfContentsSettings.strFullCaption": "Полное название", "DE.Views.TableOfContentsSettings.strLinks": "Форматировать оглавление как ссылки", "DE.Views.TableOfContentsSettings.strLinksOF": "Форматировать список иллюстраций как ссылки", "DE.Views.TableOfContentsSettings.strShowPages": "Показать номера страниц", "DE.Views.TableOfContentsSettings.textBuildTable": "Собрать оглавление, используя:", "DE.Views.TableOfContentsSettings.textBuildTableOF": "Собрать список иллюстраций, используя:", "DE.Views.TableOfContentsSettings.textEquation": "Уравнение", "DE.Views.TableOfContentsSettings.textFigure": "Рисунок", "DE.Views.TableOfContentsSettings.textLeader": "Заполнитель", "DE.Views.TableOfContentsSettings.textLevel": "Уровень", "DE.Views.TableOfContentsSettings.textLevels": "Уровни", "DE.Views.TableOfContentsSettings.textNone": "Нет", "DE.Views.TableOfContentsSettings.textRadioCaption": "Название", "DE.Views.TableOfContentsSettings.textRadioLevels": "Уровни структуры", "DE.Views.TableOfContentsSettings.textRadioStyle": "Стиль", "DE.Views.TableOfContentsSettings.textRadioStyles": "Выделенные стили", "DE.Views.TableOfContentsSettings.textStyle": "Стиль", "DE.Views.TableOfContentsSettings.textStyles": "Стили", "DE.Views.TableOfContentsSettings.textTable": "Таблица", "DE.Views.TableOfContentsSettings.textTitle": "Оглавление", "DE.Views.TableOfContentsSettings.textTitleTOF": "Список иллюстраций", "DE.Views.TableOfContentsSettings.txtCentered": "По центру", "DE.Views.TableOfContentsSettings.txtClassic": "Классический", "DE.Views.TableOfContentsSettings.txtCurrent": "Текущий", "DE.Views.TableOfContentsSettings.txtDistinctive": "Изысканный", "DE.Views.TableOfContentsSettings.txtFormal": "Официальный", "DE.Views.TableOfContentsSettings.txtModern": "Современный", "DE.Views.TableOfContentsSettings.txtOnline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.TableOfContentsSettings.txtSimple": "Простой", "DE.Views.TableOfContentsSettings.txtStandard": "Стандартный", "DE.Views.TableSettings.deleteColumnText": "Удалить столбец", "DE.Views.TableSettings.deleteRowText": "Удалить строку", "DE.Views.TableSettings.deleteTableText": "Удалить таблицу", "DE.Views.TableSettings.insertColumnLeftText": "Вставить столбец слева", "DE.Views.TableSettings.insertColumnRightText": "Вставить столбец справа", "DE.Views.TableSettings.insertRowAboveText": "Вставить строку выше", "DE.Views.TableSettings.insertRowBelowText": "Вставить строку ниже", "DE.Views.TableSettings.mergeCellsText": "Объединить ячейки", "DE.Views.TableSettings.selectCellText": "Выбрать ячейку", "DE.Views.TableSettings.selectColumnText": "Выбрать столбец", "DE.Views.TableSettings.selectRowText": "Выбрать строку", "DE.Views.TableSettings.selectTableText": "Выбрать таблицу", "DE.Views.TableSettings.splitCellsText": "Разделить ячейку...", "DE.Views.TableSettings.splitCellTitleText": "Разделить ячейку", "DE.Views.TableSettings.strRepeatRow": "Повторять как заголовок на каждой странице", "DE.Views.TableSettings.textAddFormula": "Добавить формулу", "DE.Views.TableSettings.textAdvanced": "Дополнительные параметры", "DE.Views.TableSettings.textBackColor": "Цвет фона", "DE.Views.TableSettings.textBanded": "Чередовать", "DE.Views.TableSettings.textBorderColor": "Цвет", "DE.Views.TableSettings.textBorders": "Стиль границ", "DE.Views.TableSettings.textCellSize": "Размеры строк и столбцов", "DE.Views.TableSettings.textColumns": "Столбцы", "DE.Views.TableSettings.textConvert": "Преобразовать таблицу в текст", "DE.Views.TableSettings.textDistributeCols": "Выровнять ширину столбцов", "DE.Views.TableSettings.textDistributeRows": "Выровнять высоту строк", "DE.Views.TableSettings.textEdit": "Строки и столбцы", "DE.Views.TableSettings.textEmptyTemplate": "Без ша<PERSON><PERSON><PERSON>нов", "DE.Views.TableSettings.textFirst": "Первый", "DE.Views.TableSettings.textHeader": "Заголовок", "DE.Views.TableSettings.textHeight": "Высота", "DE.Views.TableSettings.textLast": "Последний", "DE.Views.TableSettings.textRows": "Строки", "DE.Views.TableSettings.textSelectBorders": "Выберите границы, к которым надо применить выбранный стиль", "DE.Views.TableSettings.textTemplate": "По шаблону", "DE.Views.TableSettings.textTotal": "Итоговая", "DE.Views.TableSettings.textWidth": "Ши<PERSON><PERSON><PERSON>", "DE.Views.TableSettings.tipAll": "Задать внешнюю границу и все внутренние линии", "DE.Views.TableSettings.tipBottom": "Задать только внешнюю нижнюю границу", "DE.Views.TableSettings.tipInner": "Задать только внутренние линии", "DE.Views.TableSettings.tipInnerHor": "Задать только горизонтальные внутренние линии", "DE.Views.TableSettings.tipInnerVert": "Задать только вертикальные внутренние линии", "DE.Views.TableSettings.tipLeft": "Задать только внешнюю левую границу", "DE.Views.TableSettings.tipNone": "Не задавать границ", "DE.Views.TableSettings.tipOuter": "Задать только внешнюю границу", "DE.Views.TableSettings.tipRight": "Задать только внешнюю правую границу", "DE.Views.TableSettings.tipTop": "Задать только внешнюю верхнюю границу", "DE.Views.TableSettings.txtGroupTable_BorderedAndLined": "Таблицы с границами и с линиями", "DE.Views.TableSettings.txtGroupTable_Custom": "Пользовательские", "DE.Views.TableSettings.txtGroupTable_Grid": "Таблицы-сетки", "DE.Views.TableSettings.txtGroupTable_List": "Таблицы-списки", "DE.Views.TableSettings.txtGroupTable_Plain": "Простые таблицы", "DE.Views.TableSettings.txtNoBorders": "Без границ", "DE.Views.TableSettings.txtTable_Accent": "акцент", "DE.Views.TableSettings.txtTable_Bordered": "С границами", "DE.Views.TableSettings.txtTable_BorderedAndLined": "С границами и с линиями", "DE.Views.TableSettings.txtTable_Colorful": "цветная", "DE.Views.TableSettings.txtTable_Dark": "темная", "DE.Views.TableSettings.txtTable_GridTable": "Таблица-сетка", "DE.Views.TableSettings.txtTable_Light": "светлая", "DE.Views.TableSettings.txtTable_Lined": "С линиями", "DE.Views.TableSettings.txtTable_ListTable": "Список-таблица", "DE.Views.TableSettings.txtTable_PlainTable": "Таблица простая", "DE.Views.TableSettings.txtTable_TableGrid": "Сетка таблицы", "DE.Views.TableSettingsAdvanced.textAlign": "Выравнивание", "DE.Views.TableSettingsAdvanced.textAlignment": "Выравнивание", "DE.Views.TableSettingsAdvanced.textAllowSpacing": "Интервалы между ячейками", "DE.Views.TableSettingsAdvanced.textAlt": "Альтернативный текст", "DE.Views.TableSettingsAdvanced.textAltDescription": "Описание", "DE.Views.TableSettingsAdvanced.textAltTip": "Альтернативное текстовое представление информации о визуальном объекте, которое будет зачитываться для людей с нарушениями зрения или когнитивными нарушениями, чтобы помочь им лучше понять, какую информацию содержит изображение, автофигура, диаграмма или таблица.", "DE.Views.TableSettingsAdvanced.textAltTitle": "Заголовок", "DE.Views.TableSettingsAdvanced.textAnchorText": "Текста", "DE.Views.TableSettingsAdvanced.textAutofit": "Автоподбор размеров по содержимому", "DE.Views.TableSettingsAdvanced.textBackColor": "Фон ячейки", "DE.Views.TableSettingsAdvanced.textBelow": "ниже", "DE.Views.TableSettingsAdvanced.textBorderColor": "Цвет границ", "DE.Views.TableSettingsAdvanced.textBorderDesc": "Щелкайте по схеме или используйте кнопки, чтобы выбрать границы и применить к ним выбранный стиль", "DE.Views.TableSettingsAdvanced.textBordersBackgroung": "Границы и фон", "DE.Views.TableSettingsAdvanced.textBorderWidth": "<PERSON><PERSON><PERSON><PERSON><PERSON> границ", "DE.Views.TableSettingsAdvanced.textBottom": "Снизу", "DE.Views.TableSettingsAdvanced.textCellOptions": "Параметры ячейки", "DE.Views.TableSettingsAdvanced.textCellProps": "Ячейка", "DE.Views.TableSettingsAdvanced.textCellSize": "Размер ячейки", "DE.Views.TableSettingsAdvanced.textCenter": "По центру", "DE.Views.TableSettingsAdvanced.textCenterTooltip": "По центру", "DE.Views.TableSettingsAdvanced.textCheckMargins": "Использовать поля по умолчанию", "DE.Views.TableSettingsAdvanced.textDefaultMargins": "Поля ячейки по умолчанию", "DE.Views.TableSettingsAdvanced.textDistance": "Расстояние до текста", "DE.Views.TableSettingsAdvanced.textHorizontal": "По горизонтали", "DE.Views.TableSettingsAdvanced.textIndLeft": "Отступ слева", "DE.Views.TableSettingsAdvanced.textLeft": "Слева", "DE.Views.TableSettingsAdvanced.textLeftTooltip": "По левому краю", "DE.Views.TableSettingsAdvanced.textMargin": "Поля", "DE.Views.TableSettingsAdvanced.textMargins": "Поля ячейки", "DE.Views.TableSettingsAdvanced.textMeasure": "Единицы", "DE.Views.TableSettingsAdvanced.textMove": "Перемещать с текстом", "DE.Views.TableSettingsAdvanced.textOnlyCells": "Только для выбранных ячеек", "DE.Views.TableSettingsAdvanced.textOptions": "Параметры", "DE.Views.TableSettingsAdvanced.textOverlap": "Разрешить перекрытие", "DE.Views.TableSettingsAdvanced.textPage": "Страницы", "DE.Views.TableSettingsAdvanced.textPosition": "Положение", "DE.Views.TableSettingsAdvanced.textPrefWidth": "Ши<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textPreview": "Просмотр", "DE.Views.TableSettingsAdvanced.textRelative": "относительно", "DE.Views.TableSettingsAdvanced.textRight": "Справа", "DE.Views.TableSettingsAdvanced.textRightOf": "справа от", "DE.Views.TableSettingsAdvanced.textRightTooltip": "По правому краю", "DE.Views.TableSettingsAdvanced.textTable": "Таблица", "DE.Views.TableSettingsAdvanced.textTableBackColor": "Фон таблицы", "DE.Views.TableSettingsAdvanced.textTablePosition": "Положение таблицы", "DE.Views.TableSettingsAdvanced.textTableSize": "Размер таблицы", "DE.Views.TableSettingsAdvanced.textTitle": "Таблица - дополнительные параметры", "DE.Views.TableSettingsAdvanced.textTop": "Сверху", "DE.Views.TableSettingsAdvanced.textVertical": "По вертикали", "DE.Views.TableSettingsAdvanced.textWidth": "Ши<PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.textWidthSpaces": "Ширина и интервал", "DE.Views.TableSettingsAdvanced.textWrap": "Обтекание текстом", "DE.Views.TableSettingsAdvanced.textWrapNoneTooltip": "Встроенная таблица", "DE.Views.TableSettingsAdvanced.textWrapParallelTooltip": "Плавающая таблица", "DE.Views.TableSettingsAdvanced.textWrappingStyle": "Стиль обтекания", "DE.Views.TableSettingsAdvanced.textWrapText": "Перенос текста", "DE.Views.TableSettingsAdvanced.tipAll": "Задать внешнюю границу и все внутренние линии", "DE.Views.TableSettingsAdvanced.tipCellAll": "Задать границы только для внутренних ячеек", "DE.Views.TableSettingsAdvanced.tipCellInner": "Задать вертикальные и горизонтальные линии только для внутренних ячеек", "DE.Views.TableSettingsAdvanced.tipCellOuter": "Задать внешние границы только для внутренних ячеек", "DE.Views.TableSettingsAdvanced.tipInner": "Задать только внутренние линии", "DE.Views.TableSettingsAdvanced.tipNone": "Не задавать границ", "DE.Views.TableSettingsAdvanced.tipOuter": "Задать только внешнюю границу", "DE.Views.TableSettingsAdvanced.tipTableOuterCellAll": "Задать внешнюю границу и границы для всех внутренних ячеек", "DE.Views.TableSettingsAdvanced.tipTableOuterCellInner": "Задать внешнюю границу и вертикальные и горизонтальные линии для внутренних ячеек", "DE.Views.TableSettingsAdvanced.tipTableOuterCellOuter": "Задать внешнюю границу таблицы и внешние границы для внутренних ячеек", "DE.Views.TableSettingsAdvanced.txtCm": "Сантиметр", "DE.Views.TableSettingsAdvanced.txtInch": "<PERSON><PERSON><PERSON><PERSON>", "DE.Views.TableSettingsAdvanced.txtNoBorders": "Без границ", "DE.Views.TableSettingsAdvanced.txtPercent": "Процент", "DE.Views.TableSettingsAdvanced.txtPt": "<PERSON>у<PERSON><PERSON><PERSON>", "DE.Views.TableToTextDialog.textEmpty": "Следует ввести знак, который будет использоваться в качестве разделителя.", "DE.Views.TableToTextDialog.textNested": "Преобразовывать вложенные таблицы", "DE.Views.TableToTextDialog.textOther": "Друг<PERSON>й", "DE.Views.TableToTextDialog.textPara": "Знаки абзаца", "DE.Views.TableToTextDialog.textSemicolon": "Точки с запятыми", "DE.Views.TableToTextDialog.textSeparator": "Разделитель", "DE.Views.TableToTextDialog.textTab": "Табуляция", "DE.Views.TableToTextDialog.textTitle": "Преобразовать таблицу в текст", "DE.Views.TextArtSettings.strColor": "Цвет", "DE.Views.TextArtSettings.strFill": "Заливка", "DE.Views.TextArtSettings.strSize": "Толщина", "DE.Views.TextArtSettings.strStroke": "<PERSON>он<PERSON><PERSON><PERSON>", "DE.Views.TextArtSettings.strTransparency": "Непрозрачность", "DE.Views.TextArtSettings.strType": "Тип", "DE.Views.TextArtSettings.textAngle": "Угол", "DE.Views.TextArtSettings.textBorderSizeErr": "Введено некорректное значение.<br>Пожалуйста, введите значение от 0 до 1584 пунктов.", "DE.Views.TextArtSettings.textColor": "Заливка цветом", "DE.Views.TextArtSettings.textDirection": "Направление", "DE.Views.TextArtSettings.textGradient": "Точки градиента", "DE.Views.TextArtSettings.textGradientFill": "Градиентная заливка", "DE.Views.TextArtSettings.textLinear": "Линейный", "DE.Views.TextArtSettings.textNoFill": "Без заливки", "DE.Views.TextArtSettings.textPosition": "Положение", "DE.Views.TextArtSettings.textRadial": "Радиальный", "DE.Views.TextArtSettings.textSelectTexture": "Выбрать", "DE.Views.TextArtSettings.textStyle": "Стиль", "DE.Views.TextArtSettings.textTemplate": "Шабл<PERSON>н", "DE.Views.TextArtSettings.textTransform": "Трансформация", "DE.Views.TextArtSettings.tipAddGradientPoint": "Добавить точку градиента", "DE.Views.TextArtSettings.tipRemoveGradientPoint": "Удалить точку градиента", "DE.Views.TextArtSettings.txtNoBorders": "Без контура", "DE.Views.TextToTableDialog.textAutofit": "Автоподбор ширины столбцов", "DE.Views.TextToTableDialog.textColumns": "Столбцы", "DE.Views.TextToTableDialog.textContents": "Автоподбор по содержимому", "DE.Views.TextToTableDialog.textEmpty": "Следует ввести знак, который будет использоваться в качестве разделителя.", "DE.Views.TextToTableDialog.textFixed": "Фиксированная ширина столбца", "DE.Views.TextToTableDialog.textOther": "Друг<PERSON>й", "DE.Views.TextToTableDialog.textPara": "Аб<PERSON>а<PERSON>ы", "DE.Views.TextToTableDialog.textRows": "Строки", "DE.Views.TextToTableDialog.textSemicolon": "Точки с запятыми", "DE.Views.TextToTableDialog.textSeparator": "Разделитель текста", "DE.Views.TextToTableDialog.textTab": "Табуляция", "DE.Views.TextToTableDialog.textTableSize": "Размер таблицы", "DE.Views.TextToTableDialog.textTitle": "Преобразовать текст в таблицу", "DE.Views.TextToTableDialog.textWindow": "Автоподбор по ширине окна", "DE.Views.TextToTableDialog.txtAutoText": "Авто", "DE.Views.Toolbar.capBtnAddComment": "Добавить комментарий", "DE.Views.Toolbar.capBtnBlankPage": "Пустая страница", "DE.Views.Toolbar.capBtnColumns": "Колонки", "DE.Views.Toolbar.capBtnComment": "Комментарий", "DE.Views.Toolbar.capBtnDateTime": "Дата и время", "DE.Views.Toolbar.capBtnInsChart": "Диаграмма", "DE.Views.Toolbar.capBtnInsControls": "Элементы управления содержимым", "DE.Views.Toolbar.capBtnInsDropcap": "Буквица", "DE.Views.Toolbar.capBtnInsEquation": "Уравнение", "DE.Views.Toolbar.capBtnInsHeader": "Колонтитулы", "DE.Views.Toolbar.capBtnInsImage": "Изображение", "DE.Views.Toolbar.capBtnInsPagebreak": "Разрывы", "DE.Views.Toolbar.capBtnInsShape": "Фигура", "DE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "DE.Views.Toolbar.capBtnInsSymbol": "Символ", "DE.Views.Toolbar.capBtnInsTable": "Таблица", "DE.Views.Toolbar.capBtnInsTextart": "Text Art", "DE.Views.Toolbar.capBtnInsTextbox": "Надпись", "DE.Views.Toolbar.capBtnLineNumbers": "Нумерация строк", "DE.Views.Toolbar.capBtnMargins": "Поля", "DE.Views.Toolbar.capBtnPageOrient": "Ориентация", "DE.Views.Toolbar.capBtnPageSize": "Размер", "DE.Views.Toolbar.capBtnWatermark": "Подложка", "DE.Views.Toolbar.capImgAlign": "Выравнивание", "DE.Views.Toolbar.capImgBackward": "Перенести назад", "DE.Views.Toolbar.capImgForward": "Перенести вперед", "DE.Views.Toolbar.capImgGroup": "Группировка", "DE.Views.Toolbar.capImgWrapping": "Обтекание", "DE.Views.Toolbar.mniCapitalizeWords": "Каждое Слово С Прописной", "DE.Views.Toolbar.mniCustomTable": "Пользовательская таблица", "DE.Views.Toolbar.mniDrawTable": "Нарисовать таблицу", "DE.Views.Toolbar.mniEditControls": "Параметры элемента управления", "DE.Views.Toolbar.mniEditDropCap": "Параметры буквицы", "DE.Views.Toolbar.mniEditFooter": "Изменить нижний колонтитул", "DE.Views.Toolbar.mniEditHeader": "Изменить верхний колонтитул", "DE.Views.Toolbar.mniEraseTable": "Очистить таблицу", "DE.Views.Toolbar.mniFromFile": "Из файла", "DE.Views.Toolbar.mniFromStorage": "Из хранилища", "DE.Views.Toolbar.mniFromUrl": "По URL", "DE.Views.Toolbar.mniHiddenBorders": "Скрытые границы таблиц", "DE.Views.Toolbar.mniHiddenChars": "Непечатаемые символы", "DE.Views.Toolbar.mniHighlightControls": "Цвет подсветки", "DE.Views.Toolbar.mniImageFromFile": "Изображение из файла", "DE.Views.Toolbar.mniImageFromStorage": "Изображение из хранилища", "DE.Views.Toolbar.mniImageFromUrl": "Изображение по URL", "DE.Views.Toolbar.mniInsertSSE": "Вставить таблицу", "DE.Views.Toolbar.mniLowerCase": "нижний регистр", "DE.Views.Toolbar.mniRemoveFooter": "Удалить нижний колонтитул", "DE.Views.Toolbar.mniRemoveHeader": "Удалить верхний колонтитул", "DE.Views.Toolbar.mniSentenceCase": "Как в предложениях.", "DE.Views.Toolbar.mniTextToTable": "Преобразовать текст в таблицу", "DE.Views.Toolbar.mniToggleCase": "иЗМЕНИТЬ рЕГИСТР", "DE.Views.Toolbar.mniUpperCase": "ВЕРХНИЙ РЕГИСТР", "DE.Views.Toolbar.strMenuNoFill": "Без заливки", "DE.Views.Toolbar.textAutoColor": "Автоматический", "DE.Views.Toolbar.textBold": "Полужирный", "DE.Views.Toolbar.textBottom": "Нижнее: ", "DE.Views.Toolbar.textChangeLevel": "Изменить уровень списка", "DE.Views.Toolbar.textCheckboxControl": "Флажок", "DE.Views.Toolbar.textColumnsCustom": "Настраиваемые колонки", "DE.Views.Toolbar.textColumnsLeft": "Слева", "DE.Views.Toolbar.textColumnsOne": "Одна", "DE.Views.Toolbar.textColumnsRight": "Справа", "DE.Views.Toolbar.textColumnsThree": "Три", "DE.Views.Toolbar.textColumnsTwo": "Две", "DE.Views.Toolbar.textComboboxControl": "Поле со списком", "DE.Views.Toolbar.textContinuous": "Непрерывная", "DE.Views.Toolbar.textContPage": "На текущей странице", "DE.Views.Toolbar.textCustomLineNumbers": "Варианты нумерации строк", "DE.Views.Toolbar.textDateControl": "Дата", "DE.Views.Toolbar.textDropdownControl": "Выпадающий список", "DE.Views.Toolbar.textEditWatermark": "Настраиваемая подложка", "DE.Views.Toolbar.textEvenPage": "С четной страницы", "DE.Views.Toolbar.textInMargin": "На поле", "DE.Views.Toolbar.textInsColumnBreak": "Вставить разрыв колонки", "DE.Views.Toolbar.textInsertPageCount": "Вставить число страниц", "DE.Views.Toolbar.textInsertPageNumber": "Вставить номер страницы", "DE.Views.Toolbar.textInsPageBreak": "Вставить разрыв страницы", "DE.Views.Toolbar.textInsSectionBreak": "Вставить разрыв раздела", "DE.Views.Toolbar.textInText": "В тексте", "DE.Views.Toolbar.textItalic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textLandscape": "Альбомная", "DE.Views.Toolbar.textLeft": "Левое: ", "DE.Views.Toolbar.textListSettings": "Параметры списка", "DE.Views.Toolbar.textMarginsLast": "Последние настраиваемые", "DE.Views.Toolbar.textMarginsModerate": "Средние", "DE.Views.Toolbar.textMarginsNarrow": "Узкие", "DE.Views.Toolbar.textMarginsNormal": "Обычные", "DE.Views.Toolbar.textMarginsUsNormal": "Обычные (американский стандарт)", "DE.Views.Toolbar.textMarginsWide": "Широкие", "DE.Views.Toolbar.textNewColor": "Пользовательский цвет", "DE.Views.Toolbar.textNextPage": "Со следующей страницы", "DE.Views.Toolbar.textNoHighlight": "Без подсветки", "DE.Views.Toolbar.textNone": "Нет", "DE.Views.Toolbar.textOddPage": "С нечетной страницы", "DE.Views.Toolbar.textPageMarginsCustom": "Настраиваемые поля", "DE.Views.Toolbar.textPageSizeCustom": "Особый размер страницы", "DE.Views.Toolbar.textPictureControl": "Рисунок", "DE.Views.Toolbar.textPlainControl": "Обычный текст", "DE.Views.Toolbar.textPortrait": "Книжная", "DE.Views.Toolbar.textRemoveControl": "Удалить элемент управления содержимым", "DE.Views.Toolbar.textRemWatermark": "Удалить подложку", "DE.Views.Toolbar.textRestartEachPage": "На каждой странице", "DE.Views.Toolbar.textRestartEachSection": "В каждом разделе", "DE.Views.Toolbar.textRichControl": "Форматированный текст", "DE.Views.Toolbar.textRight": "Правое: ", "DE.Views.Toolbar.textStrikeout": "Зачёркнутый", "DE.Views.Toolbar.textStyleMenuDelete": "Удалить стиль", "DE.Views.Toolbar.textStyleMenuDeleteAll": "Удалить все пользовательские стили", "DE.Views.Toolbar.textStyleMenuNew": "Новый стиль из выделенного фрагмента", "DE.Views.Toolbar.textStyleMenuRestore": "Восстановить параметры по умолчанию", "DE.Views.Toolbar.textStyleMenuRestoreAll": "Восстановить все стандартные стили", "DE.Views.Toolbar.textStyleMenuUpdate": "Обновить из выделенного фрагмента", "DE.Views.Toolbar.textSubscript": "Подстрочные знаки", "DE.Views.Toolbar.textSuperscript": "Надстрочные знаки", "DE.Views.Toolbar.textSuppressForCurrentParagraph": "Отключить для текущего абзаца", "DE.Views.Toolbar.textTabCollaboration": "Совместная работа", "DE.Views.Toolbar.textTabFile": "<PERSON>а<PERSON><PERSON>", "DE.Views.Toolbar.textTabHome": "Главная", "DE.Views.Toolbar.textTabInsert": "Вставка", "DE.Views.Toolbar.textTabLayout": "<PERSON>а<PERSON><PERSON><PERSON>", "DE.Views.Toolbar.textTabLinks": "Ссылки", "DE.Views.Toolbar.textTabProtect": "Защита", "DE.Views.Toolbar.textTabReview": "Рецензирование", "DE.Views.Toolbar.textTabView": "Вид", "DE.Views.Toolbar.textTitleError": "Ошибка", "DE.Views.Toolbar.textToCurrent": "В текущей позиции", "DE.Views.Toolbar.textTop": "Верхнее: ", "DE.Views.Toolbar.textUnderline": "Подчеркнутый", "DE.Views.Toolbar.tipAlignCenter": "Выравнивание по центру", "DE.Views.Toolbar.tipAlignJust": "Выравнивание по ширине", "DE.Views.Toolbar.tipAlignLeft": "Выравнивание по левому краю", "DE.Views.Toolbar.tipAlignRight": "Выравнивание по правому краю", "DE.Views.Toolbar.tipBack": "Назад", "DE.Views.Toolbar.tipBlankPage": "Вставить пустую страницу", "DE.Views.Toolbar.tipChangeCase": "Изменить регистр", "DE.Views.Toolbar.tipChangeChart": "Изменить тип диаграммы", "DE.Views.Toolbar.tipClearStyle": "Очистить стиль", "DE.Views.Toolbar.tipColorSchemas": "Изменение цветовой схемы", "DE.Views.Toolbar.tipColumns": "Вставить колонки", "DE.Views.Toolbar.tipControls": "Вставить элемент управления содержимым", "DE.Views.Toolbar.tipCopy": "Копировать", "DE.Views.Toolbar.tipCopyStyle": "Копировать стиль", "DE.Views.Toolbar.tipCut": "Вырезать", "DE.Views.Toolbar.tipDateTime": "Вставить текущую дату и время", "DE.Views.Toolbar.tipDecFont": "Уменьшить размер шрифта", "DE.Views.Toolbar.tipDecPrLeft": "Уменьшить отступ", "DE.Views.Toolbar.tipDropCap": "Вставить буквицу", "DE.Views.Toolbar.tipEditHeader": "Изменить колонтитулы", "DE.Views.Toolbar.tipFontColor": "Цвет шрифта", "DE.Views.Toolbar.tipFontName": "<PERSON>ри<PERSON><PERSON>", "DE.Views.Toolbar.tipFontSize": "Размер шрифта", "DE.Views.Toolbar.tipHighlightColor": "Цвет выделения", "DE.Views.Toolbar.tipImgAlign": "Выровнять объекты", "DE.Views.Toolbar.tipImgGroup": "Сгруппировать объекты", "DE.Views.Toolbar.tipImgWrapping": "Обтекание текстом", "DE.Views.Toolbar.tipIncFont": "Увеличить размер шрифта", "DE.Views.Toolbar.tipIncPrLeft": "Увеличить отступ", "DE.Views.Toolbar.tipInsertChart": "Вставить диаграмму", "DE.Views.Toolbar.tipInsertEquation": "Вставить уравнение", "DE.Views.Toolbar.tipInsertHorizontalText": "Вставить горизонтальную надпись", "DE.Views.Toolbar.tipInsertImage": "Вставить изображение", "DE.Views.Toolbar.tipInsertNum": "Вставить номер страницы", "DE.Views.Toolbar.tipInsertShape": "Вставить автофигуру", "DE.Views.Toolbar.tipInsertSmartArt": "Вставить SmartArt", "DE.Views.Toolbar.tipInsertSymbol": "Вставить символ", "DE.Views.Toolbar.tipInsertTable": "Вставить таблицу", "DE.Views.Toolbar.tipInsertText": "Вставить надпись", "DE.Views.Toolbar.tipInsertTextArt": "Вставить объект Text Art", "DE.Views.Toolbar.tipInsertVerticalText": "Вставить вертикальную надпись", "DE.Views.Toolbar.tipLineNumbers": "Показывать номера строк", "DE.Views.Toolbar.tipLineSpace": "Междустрочный интервал в абзацах", "DE.Views.Toolbar.tipMailRecepients": "Слияние", "DE.Views.Toolbar.tipMarkers": "Маркированный список", "DE.Views.Toolbar.tipMarkersArrow": "Маркеры-стрелки", "DE.Views.Toolbar.tipMarkersCheckmark": "Маркеры-галочки", "DE.Views.Toolbar.tipMarkersDash": "Маркеры-тире", "DE.Views.Toolbar.tipMarkersFRhombus": "Заполненные ромбовидные маркеры", "DE.Views.Toolbar.tipMarkersFRound": "Заполненные круглые маркеры", "DE.Views.Toolbar.tipMarkersFSquare": "Заполненные квадратные маркеры", "DE.Views.Toolbar.tipMarkersHRound": "Пустые круглые маркеры", "DE.Views.Toolbar.tipMarkersStar": "Маркеры-звездочки", "DE.Views.Toolbar.tipMultiLevelArticl": "Многоуровневые нумерованные статьи", "DE.Views.Toolbar.tipMultiLevelChapter": "Многоуровневые нумерованные главы", "DE.Views.Toolbar.tipMultiLevelHeadings": "Многоуровневые нумерованные заголовки", "DE.Views.Toolbar.tipMultiLevelHeadVarious": "Многоуровневые разные нумерованные заголовки", "DE.Views.Toolbar.tipMultiLevelNumbered": "Многоуровневые нумерованные маркеры", "DE.Views.Toolbar.tipMultilevels": "Многоуровневый список", "DE.Views.Toolbar.tipMultiLevelSymbols": "Многоуровневые маркеры-символы", "DE.Views.Toolbar.tipMultiLevelVarious": "Многоуровневые разные нумерованные маркеры", "DE.Views.Toolbar.tipNumbers": "Нумерованный список", "DE.Views.Toolbar.tipPageBreak": "Вставить разрыв страницы или раздела", "DE.Views.Toolbar.tipPageMargins": "Поля страницы", "DE.Views.Toolbar.tipPageOrient": "Ориентация страницы", "DE.Views.Toolbar.tipPageSize": "Размер страницы", "DE.Views.Toolbar.tipParagraphStyle": "Стиль абзаца", "DE.Views.Toolbar.tipPaste": "Вставить", "DE.Views.Toolbar.tipPrColor": "Заливка", "DE.Views.Toolbar.tipPrint": "Печать", "DE.Views.Toolbar.tipPrintQuick": "Быстрая печать", "DE.Views.Toolbar.tipRedo": "Повторить", "DE.Views.Toolbar.tipSave": "Сохранить", "DE.Views.Toolbar.tipSaveCoauth": "Сохраните свои изменения, чтобы другие пользователи их увидели.", "DE.Views.Toolbar.tipSelectAll": "Выделить всё", "DE.Views.Toolbar.tipSendBackward": "Перенести назад", "DE.Views.Toolbar.tipSendForward": "Перенести вперед", "DE.Views.Toolbar.tipShowHiddenChars": "Непечатаемые символы", "DE.Views.Toolbar.tipSynchronize": "Документ изменен другим пользователем. Нажмите, чтобы сохранить свои изменения и загрузить обновления.", "DE.Views.Toolbar.tipUndo": "Отменить", "DE.Views.Toolbar.tipWatermark": "Изменить подложку", "DE.Views.Toolbar.txtDistribHor": "Распределить по горизонтали", "DE.Views.Toolbar.txtDistribVert": "Распределить по вертикали", "DE.Views.Toolbar.txtMarginAlign": "Выровнять относительно поля", "DE.Views.Toolbar.txtObjectsAlign": "Выровнять выделенные объекты", "DE.Views.Toolbar.txtPageAlign": "Выровнять относительно страницы", "DE.Views.Toolbar.txtScheme1": "Стандартная", "DE.Views.Toolbar.txtScheme10": "Обычная", "DE.Views.Toolbar.txtScheme11": "Метро", "DE.Views.Toolbar.txtScheme12": "Модульная", "DE.Views.Toolbar.txtScheme13": "Изящная", "DE.Views.Toolbar.txtScheme14": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.txtScheme15": "Начальная", "DE.Views.Toolbar.txtScheme16": "Бумажная", "DE.Views.Toolbar.txtScheme17": "Солнцестояние", "DE.Views.Toolbar.txtScheme18": "Техническая", "DE.Views.Toolbar.txtScheme19": "Трек", "DE.Views.Toolbar.txtScheme2": "Оттенки серого", "DE.Views.Toolbar.txtScheme20": "Городская", "DE.Views.Toolbar.txtScheme21": "Яркая", "DE.Views.Toolbar.txtScheme22": "Новая офисная", "DE.Views.Toolbar.txtScheme3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.Toolbar.txtScheme4": "Аспект", "DE.Views.Toolbar.txtScheme5": "Официальная", "DE.Views.Toolbar.txtScheme6": "Открытая", "DE.Views.Toolbar.txtScheme7": "Справедливость", "DE.Views.Toolbar.txtScheme8": "Поток", "DE.Views.Toolbar.txtScheme9": "Литейная", "DE.Views.ViewTab.textAlwaysShowToolbar": "Всегда показывать панель инструментов", "DE.Views.ViewTab.textDarkDocument": "Темный документ", "DE.Views.ViewTab.textFitToPage": "По размеру страницы", "DE.Views.ViewTab.textFitToWidth": "По ширине", "DE.Views.ViewTab.textInterfaceTheme": "Тема интерфейса", "DE.Views.ViewTab.textLeftMenu": "Левая панель", "DE.Views.ViewTab.textNavigation": "Навигация", "DE.Views.ViewTab.textOutline": "Заголовки", "DE.Views.ViewTab.textRightMenu": "Правая панель", "DE.Views.ViewTab.textRulers": "Линейки", "DE.Views.ViewTab.textStatusBar": "Строка состояния", "DE.Views.ViewTab.textZoom": "Масш<PERSON><PERSON><PERSON>", "DE.Views.ViewTab.tipDarkDocument": "Темный документ", "DE.Views.ViewTab.tipFitToPage": "По размеру страницы", "DE.Views.ViewTab.tipFitToWidth": "По ширине", "DE.Views.ViewTab.tipHeadings": "Заголовки", "DE.Views.ViewTab.tipInterfaceTheme": "Тема интерфейса", "DE.Views.WatermarkSettingsDialog.textAuto": "Авто", "DE.Views.WatermarkSettingsDialog.textBold": "Полужирный", "DE.Views.WatermarkSettingsDialog.textColor": "Цвет текста", "DE.Views.WatermarkSettingsDialog.textDiagonal": "По диагонали", "DE.Views.WatermarkSettingsDialog.textFont": "<PERSON>ри<PERSON><PERSON>", "DE.Views.WatermarkSettingsDialog.textFromFile": "Из файла", "DE.Views.WatermarkSettingsDialog.textFromStorage": "Из хранилища", "DE.Views.WatermarkSettingsDialog.textFromUrl": "По URL", "DE.Views.WatermarkSettingsDialog.textHor": "По горизонтали", "DE.Views.WatermarkSettingsDialog.textImageW": "Графическая подложка", "DE.Views.WatermarkSettingsDialog.textItalic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DE.Views.WatermarkSettingsDialog.textLanguage": "Язык", "DE.Views.WatermarkSettingsDialog.textLayout": "Расположение", "DE.Views.WatermarkSettingsDialog.textNone": "Нет", "DE.Views.WatermarkSettingsDialog.textScale": "Масш<PERSON><PERSON><PERSON>", "DE.Views.WatermarkSettingsDialog.textSelect": "Выбрать изображение", "DE.Views.WatermarkSettingsDialog.textStrikeout": "Зачёркнутый", "DE.Views.WatermarkSettingsDialog.textText": "Текст", "DE.Views.WatermarkSettingsDialog.textTextW": "Текстовая подложка", "DE.Views.WatermarkSettingsDialog.textTitle": "Параметры подложки", "DE.Views.WatermarkSettingsDialog.textTransparency": "Полупрозрачный", "DE.Views.WatermarkSettingsDialog.textUnderline": "Подчёркнутый", "DE.Views.WatermarkSettingsDialog.tipFontName": "<PERSON>ри<PERSON><PERSON>", "DE.Views.WatermarkSettingsDialog.tipFontSize": "Размер шрифта"}