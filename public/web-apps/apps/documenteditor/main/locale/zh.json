{"Common.Controllers.Chat.notcriticalErrorTitle": "警告", "Common.Controllers.Chat.textEnterMessage": "在这里输入你的信息", "Common.Controllers.ExternalDiagramEditor.textAnonymous": "匿名", "Common.Controllers.ExternalDiagramEditor.textClose": "关闭", "Common.Controllers.ExternalDiagramEditor.warningText": "该对象被禁用，因为它被另一个用户编辑。", "Common.Controllers.ExternalDiagramEditor.warningTitle": "警告", "Common.Controllers.ExternalMergeEditor.textAnonymous": "匿名", "Common.Controllers.ExternalMergeEditor.textClose": "关闭", "Common.Controllers.ExternalMergeEditor.warningText": "该对象被禁用，因为它被另一个用户编辑。", "Common.Controllers.ExternalMergeEditor.warningTitle": "警告", "Common.Controllers.ExternalOleEditor.textAnonymous": "匿名用户", "Common.Controllers.ExternalOleEditor.textClose": "关闭", "Common.Controllers.ExternalOleEditor.warningText": "该对象被禁用，因为它在被另一个用户编辑。", "Common.Controllers.ExternalOleEditor.warningTitle": "警告", "Common.Controllers.History.notcriticalErrorTitle": "警告", "Common.Controllers.ReviewChanges.textAcceptBeforeCompare": "为了比较文件，文件中所有跟踪的更改都将被视为已被接受。你想继续吗？", "Common.Controllers.ReviewChanges.textAtLeast": "至少", "Common.Controllers.ReviewChanges.textAuto": "自动", "Common.Controllers.ReviewChanges.textBaseline": "基线", "Common.Controllers.ReviewChanges.textBold": "加粗", "Common.Controllers.ReviewChanges.textBreakBefore": "段前分页", "Common.Controllers.ReviewChanges.textCaps": "全部大写字母", "Common.Controllers.ReviewChanges.textCenter": "居中对齐", "Common.Controllers.ReviewChanges.textChar": "字符级别", "Common.Controllers.ReviewChanges.textChart": "图表", "Common.Controllers.ReviewChanges.textColor": "字体颜色", "Common.Controllers.ReviewChanges.textContextual": "不要在相同样式的段落之间添加间隔", "Common.Controllers.ReviewChanges.textDeleted": "<b>已删除</b>", "Common.Controllers.ReviewChanges.textDStrikeout": "双击", "Common.Controllers.ReviewChanges.textEquation": "方程", "Common.Controllers.ReviewChanges.textExact": "精确地", "Common.Controllers.ReviewChanges.textFirstLine": "第一行", "Common.Controllers.ReviewChanges.textFontSize": "字体大小", "Common.Controllers.ReviewChanges.textFormatted": "格式化", "Common.Controllers.ReviewChanges.textHighlight": "颜色高亮", "Common.Controllers.ReviewChanges.textImage": "图片", "Common.Controllers.ReviewChanges.textIndentLeft": "左缩进", "Common.Controllers.ReviewChanges.textIndentRight": "右缩进", "Common.Controllers.ReviewChanges.textInserted": "<b>插入：</b>", "Common.Controllers.ReviewChanges.textItalic": "斜体", "Common.Controllers.ReviewChanges.textJustify": "两端对齐", "Common.Controllers.ReviewChanges.textKeepLines": "保持同一行", "Common.Controllers.ReviewChanges.textKeepNext": "与下一个保持一致", "Common.Controllers.ReviewChanges.textLeft": "左对齐", "Common.Controllers.ReviewChanges.textLineSpacing": "行间距：", "Common.Controllers.ReviewChanges.textMultiple": "多", "Common.Controllers.ReviewChanges.textNoBreakBefore": "段前无分页", "Common.Controllers.ReviewChanges.textNoContextual": "在相同样式的段落之间添加间隔", "Common.Controllers.ReviewChanges.textNoKeepLines": "不要保持一行", "Common.Controllers.ReviewChanges.textNoKeepNext": "不要跟着下一个", "Common.Controllers.ReviewChanges.textNot": "不", "Common.Controllers.ReviewChanges.textNoWidow": "没有单独控制", "Common.Controllers.ReviewChanges.textNum": "更改编号", "Common.Controllers.ReviewChanges.textOff": "{0} 已停用“跟踪修改”", "Common.Controllers.ReviewChanges.textOffGlobal": "{0} 为所有人关闭“跟踪修改”", "Common.Controllers.ReviewChanges.textOn": "{0} 正在使用“跟踪修改”", "Common.Controllers.ReviewChanges.textOnGlobal": "{0} 为所有人启动“跟踪修改”", "Common.Controllers.ReviewChanges.textParaDeleted": "<b>段落已删除</b>", "Common.Controllers.ReviewChanges.textParaFormatted": "段落格式化", "Common.Controllers.ReviewChanges.textParaInserted": "<b>段落插入</b>", "Common.Controllers.ReviewChanges.textParaMoveFromDown": "<b>已下移</b>", "Common.Controllers.ReviewChanges.textParaMoveFromUp": "<b>已上移</b>", "Common.Controllers.ReviewChanges.textParaMoveTo": "<b>已移动</b>", "Common.Controllers.ReviewChanges.textPosition": "位置", "Common.Controllers.ReviewChanges.textRight": "右对齐", "Common.Controllers.ReviewChanges.textShape": "形状", "Common.Controllers.ReviewChanges.textShd": "背景颜色", "Common.Controllers.ReviewChanges.textShow": "显示更改在", "Common.Controllers.ReviewChanges.textSmallCaps": "小型大写字母", "Common.Controllers.ReviewChanges.textSpacing": "间距", "Common.Controllers.ReviewChanges.textSpacingAfter": "间隔", "Common.Controllers.ReviewChanges.textSpacingBefore": "之前的距离", "Common.Controllers.ReviewChanges.textStrikeout": "加删除线", "Common.Controllers.ReviewChanges.textSubScript": "下标", "Common.Controllers.ReviewChanges.textSuperScript": "上标", "Common.Controllers.ReviewChanges.textTableChanged": "<b>表格设置已更改</b>", "Common.Controllers.ReviewChanges.textTableRowsAdd": "<b>表格行已添加</b>", "Common.Controllers.ReviewChanges.textTableRowsDel": "<b>表格行已删除</b>", "Common.Controllers.ReviewChanges.textTabs": "更改选项卡", "Common.Controllers.ReviewChanges.textTitleComparison": "比较设置", "Common.Controllers.ReviewChanges.textUnderline": "下划线", "Common.Controllers.ReviewChanges.textUrl": "粘贴文档URL", "Common.Controllers.ReviewChanges.textWidow": "视窗控制", "Common.Controllers.ReviewChanges.textWord": "字级", "Common.define.chartData.textArea": "面积图", "Common.define.chartData.textAreaStacked": "堆积面积图", "Common.define.chartData.textAreaStackedPer": "百分比堆积面积图", "Common.define.chartData.textBar": "条形图", "Common.define.chartData.textBarNormal": "簇状柱形图", "Common.define.chartData.textBarNormal3d": "三维簇状柱形图", "Common.define.chartData.textBarNormal3dPerspective": "三维柱形图", "Common.define.chartData.textBarStacked": "堆积柱形图", "Common.define.chartData.textBarStacked3d": "三维堆积柱形图", "Common.define.chartData.textBarStackedPer": "百分比堆积柱形图", "Common.define.chartData.textBarStackedPer3d": "三维百分比堆积柱形图", "Common.define.chartData.textCharts": "流程图", "Common.define.chartData.textColumn": "柱状图", "Common.define.chartData.textCombo": "组合图", "Common.define.chartData.textComboAreaBar": "堆积面积图 - 簇状柱形图", "Common.define.chartData.textComboBarLine": "簇状柱形图 - 折线图", "Common.define.chartData.textComboBarLineSecondary": "簇状柱形图 - 次坐标轴上的折线图", "Common.define.chartData.textComboCustom": "自定义组合", "Common.define.chartData.textDoughnut": "圆环图", "Common.define.chartData.textHBarNormal": "簇状条形图", "Common.define.chartData.textHBarNormal3d": "三维簇状条形图", "Common.define.chartData.textHBarStacked": "堆积条形图", "Common.define.chartData.textHBarStacked3d": "三维堆积条形图", "Common.define.chartData.textHBarStackedPer": "百分比堆积条形图", "Common.define.chartData.textHBarStackedPer3d": "三维百分比堆积条形图", "Common.define.chartData.textLine": "折线图", "Common.define.chartData.textLine3d": "三维折线图", "Common.define.chartData.textLineMarker": "带数据标记的折线图", "Common.define.chartData.textLineStacked": "堆积折线图", "Common.define.chartData.textLineStackedMarker": "带数据标记的堆积折线图", "Common.define.chartData.textLineStackedPer": "百分比堆积折线图", "Common.define.chartData.textLineStackedPerMarker": "带数据标记的百分比堆积折线图", "Common.define.chartData.textPie": "饼图", "Common.define.chartData.textPie3d": "三维饼图", "Common.define.chartData.textPoint": "散点图", "Common.define.chartData.textScatter": "散点图​​", "Common.define.chartData.textScatterLine": "带直线的散点图", "Common.define.chartData.textScatterLineMarker": "带直线和数据标记的散点图", "Common.define.chartData.textScatterSmooth": "带平滑线的散点图", "Common.define.chartData.textScatterSmoothMarker": "带平滑线和数据标记的散点图", "Common.define.chartData.textStock": "股价图", "Common.define.chartData.textSurface": "平面", "Common.define.smartArt.textAccentedPicture": "重音图片", "Common.define.smartArt.textAccentProcess": "重点流程", "Common.define.smartArt.textAlternatingFlow": "交替流", "Common.define.smartArt.textAlternatingHexagons": "交替六边形", "Common.define.smartArt.textAlternatingPictureBlocks": "交替图片块", "Common.define.smartArt.textAlternatingPictureCircles": "交替图片圆形", "Common.define.smartArt.textArchitectureLayout": "结构布局", "Common.define.smartArt.textArrowRibbon": "带形箭头", "Common.define.smartArt.textAscendingPictureAccentProcess": "升序图片重点流程", "Common.define.smartArt.textBalance": "平衡", "Common.define.smartArt.textBasicBendingProcess": "基本蛇形流程", "Common.define.smartArt.textBasicBlockList": "基本列表", "Common.define.smartArt.textBasicChevronProcess": "基本 V 形流程", "Common.define.smartArt.textBasicCycle": "基本循环", "Common.define.smartArt.textBasicMatrix": "基本矩阵", "Common.define.smartArt.textBasicPie": "基本饼图", "Common.define.smartArt.textBasicProcess": "基本流程", "Common.define.smartArt.textBasicPyramid": "基本棱锥图", "Common.define.smartArt.textBasicRadial": "基本射线图", "Common.define.smartArt.textBasicTarget": "基本目标图", "Common.define.smartArt.textBasicTimeline": "基本时间线", "Common.define.smartArt.textBasicVenn": "基本维恩图", "Common.define.smartArt.textBendingPictureAccentList": "蛇形图片重点列表", "Common.define.smartArt.textBendingPictureBlocks": "蛇形图片块", "Common.define.smartArt.textBendingPictureCaption": "蛇形图片题注", "Common.define.smartArt.textBendingPictureCaptionList": "蛇形图片题注列表", "Common.define.smartArt.textBendingPictureSemiTranparentText": "蛇形图片半透明文本", "Common.define.smartArt.textBlockCycle": "块循环", "Common.define.smartArt.textBubblePictureList": "气泡图片列表", "Common.define.smartArt.textCaptionedPictures": "题注图片", "Common.define.smartArt.textChevronAccentProcess": "V 形重点流程", "Common.define.smartArt.textChevronList": "V 型列表", "Common.define.smartArt.textCircleAccentTimeline": "圆形重点日程表", "Common.define.smartArt.textCircleArrowProcess": "圆箭头流程", "Common.define.smartArt.textCirclePictureHierarchy": "圆形图片层次结构", "Common.define.smartArt.textCircleProcess": "循环流程", "Common.define.smartArt.textCircleRelationship": "循环关系", "Common.define.smartArt.textCircularBendingProcess": "环状蛇形流程", "Common.define.smartArt.textCircularPictureCallout": "圆形图片标注", "Common.define.smartArt.textClosedChevronProcess": "闭合 V 形流程", "Common.define.smartArt.textContinuousArrowProcess": "连续箭头流程", "Common.define.smartArt.textContinuousBlockProcess": "连续块状流程", "Common.define.smartArt.textContinuousCycle": "连续循环", "Common.define.smartArt.textContinuousPictureList": "连续图片列表", "Common.define.smartArt.textConvergingArrows": "汇聚箭头", "Common.define.smartArt.textConvergingRadial": "聚合射线", "Common.define.smartArt.textConvergingText": "聚合文本", "Common.define.smartArt.textCounterbalanceArrows": "平衡箭头", "Common.define.smartArt.textCycle": "循环", "Common.define.smartArt.textCycleMatrix": "循环矩阵", "Common.define.smartArt.textDescendingBlockList": "降序块列表", "Common.define.smartArt.textDescendingProcess": "降序流程", "Common.define.smartArt.textDetailedProcess": "详细流程", "Common.define.smartArt.textDivergingArrows": "分叉箭头", "Common.define.smartArt.textDivergingRadial": "分离射线", "Common.define.smartArt.textEquation": "公式", "Common.define.smartArt.textFramedTextPicture": "带框架的文本图片", "Common.define.smartArt.textFunnel": "漏斗", "Common.define.smartArt.textGear": "齿轮", "Common.define.smartArt.textGridMatrix": "网格矩阵", "Common.define.smartArt.textGroupedList": "分组列表", "Common.define.smartArt.textHalfCircleOrganizationChart": "半圆组织结构图", "Common.define.smartArt.textHexagonCluster": "六边形群集", "Common.define.smartArt.textHexagonRadial": "放射状六边形", "Common.define.smartArt.textHierarchy": "层次结构", "Common.define.smartArt.textHierarchyList": "层次结构列表", "Common.define.smartArt.textHorizontalBulletList": "水平项目符号列表", "Common.define.smartArt.textHorizontalHierarchy": "水平层次结构", "Common.define.smartArt.textHorizontalLabeledHierarchy": "水平标记的层次结构", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "水平多层层次结构", "Common.define.smartArt.textHorizontalOrganizationChart": "水平组织结构图", "Common.define.smartArt.textHorizontalPictureList": "水平图片列表", "Common.define.smartArt.textIncreasingArrowProcess": "递增箭头流程", "Common.define.smartArt.textIncreasingCircleProcess": "递增循环流程", "Common.define.smartArt.textInterconnectedBlockProcess": "互联块流程", "Common.define.smartArt.textInterconnectedRings": "互联环", "Common.define.smartArt.textInvertedPyramid": "倒棱锥图", "Common.define.smartArt.textLabeledHierarchy": "标记的层次结构", "Common.define.smartArt.textLinearVenn": "线性维恩图", "Common.define.smartArt.textLinedList": "线型列表", "Common.define.smartArt.textList": "列表", "Common.define.smartArt.textMatrix": "矩阵", "Common.define.smartArt.textMultidirectionalCycle": "多向循环", "Common.define.smartArt.textNameAndTitleOrganizationChart": "姓名和职务组织结构图", "Common.define.smartArt.textNestedTarget": "嵌套目标图", "Common.define.smartArt.textNondirectionalCycle": "不定向循环", "Common.define.smartArt.textOpposingArrows": "反向箭头", "Common.define.smartArt.textOpposingIdeas": "对立观点", "Common.define.smartArt.textOrganizationChart": "组织结构图", "Common.define.smartArt.textOther": "其他", "Common.define.smartArt.textPhasedProcess": "分阶段流程", "Common.define.smartArt.textPicture": "图片", "Common.define.smartArt.textPictureAccentBlocks": "图片重点块", "Common.define.smartArt.textPictureAccentList": "图片重点列表", "Common.define.smartArt.textPictureAccentProcess": "图片重点流程", "Common.define.smartArt.textPictureCaptionList": "图片题注列表", "Common.define.smartArt.textPictureFrame": "图片框", "Common.define.smartArt.textPictureGrid": "图片网格", "Common.define.smartArt.textPictureLineup": "图片排列", "Common.define.smartArt.textPictureOrganizationChart": "图片组织结构图", "Common.define.smartArt.textPictureStrips": "图片条纹", "Common.define.smartArt.textPieProcess": "饼图流程", "Common.define.smartArt.textPlusAndMinus": "加号和减号", "Common.define.smartArt.textProcess": "流程", "Common.define.smartArt.textProcessArrows": "流程箭头", "Common.define.smartArt.textProcessList": "流程列表", "Common.define.smartArt.textPyramid": "棱锥型", "Common.define.smartArt.textPyramidList": "棱锥型列表", "Common.define.smartArt.textRadialCluster": "射线群集", "Common.define.smartArt.textRadialCycle": "射线循环", "Common.define.smartArt.textRadialList": "射线列表", "Common.define.smartArt.textRadialPictureList": "放射状图片列表", "Common.define.smartArt.textRadialVenn": "射线维恩图", "Common.define.smartArt.textRandomToResultProcess": "随机至结果流程", "Common.define.smartArt.textRelationship": "关系", "Common.define.smartArt.textRepeatingBendingProcess": "重复蛇形流程", "Common.define.smartArt.textReverseList": "反转列表", "Common.define.smartArt.textSegmentedCycle": "分段循环", "Common.define.smartArt.textSegmentedProcess": "分段流程", "Common.define.smartArt.textSegmentedPyramid": "分段棱锥图", "Common.define.smartArt.textSnapshotPictureList": "快照图片列表", "Common.define.smartArt.textSpiralPicture": "螺旋图", "Common.define.smartArt.textSquareAccentList": "方形重点列表", "Common.define.smartArt.textStackedList": "堆叠列表", "Common.define.smartArt.textStackedVenn": "堆叠维恩图", "Common.define.smartArt.textStaggeredProcess": "交错流程", "Common.define.smartArt.textStepDownProcess": "步骤下移流程", "Common.define.smartArt.textStepUpProcess": "升级流程", "Common.define.smartArt.textSubStepProcess": "子步骤流程", "Common.define.smartArt.textTabbedArc": "拱状", "Common.define.smartArt.textTableHierarchy": "表层次结构", "Common.define.smartArt.textTableList": "表格列表", "Common.define.smartArt.textTabList": "选项卡列表", "Common.define.smartArt.textTargetList": "目标图列表", "Common.define.smartArt.textTextCycle": "文本循环", "Common.define.smartArt.textThemePictureAccent": "主题图片重点", "Common.define.smartArt.textThemePictureAlternatingAccent": "主题图片交替重点", "Common.define.smartArt.textThemePictureGrid": "主题图片网格", "Common.define.smartArt.textTitledMatrix": "带标题的矩阵", "Common.define.smartArt.textTitledPictureAccentList": "标题图片重点列表", "Common.define.smartArt.textTitledPictureBlocks": "标题图片块", "Common.define.smartArt.textTitlePictureLineup": "标题图片排列", "Common.define.smartArt.textTrapezoidList": "梯形列表", "Common.define.smartArt.textUpwardArrow": "向上箭头", "Common.define.smartArt.textVaryingWidthList": "不同宽度列表", "Common.define.smartArt.textVerticalAccentList": "垂直重点列表", "Common.define.smartArt.textVerticalArrowList": "垂直箭头列表", "Common.define.smartArt.textVerticalBendingProcess": "垂直蛇形流程", "Common.define.smartArt.textVerticalBlockList": "垂直块列表", "Common.define.smartArt.textVerticalBoxList": "垂直框列表", "Common.define.smartArt.textVerticalBracketList": "垂直括弧列表", "Common.define.smartArt.textVerticalBulletList": "垂直项目符号列表", "Common.define.smartArt.textVerticalChevronList": "垂直 V 形列表", "Common.define.smartArt.textVerticalCircleList": "垂直圆形列表", "Common.define.smartArt.textVerticalCurvedList": "垂直曲形列表", "Common.define.smartArt.textVerticalEquation": "垂直公式", "Common.define.smartArt.textVerticalPictureAccentList": "垂直图片重点列表", "Common.define.smartArt.textVerticalPictureList": "垂直图片列表", "Common.define.smartArt.textVerticalProcess": "垂直流程", "Common.Translation.textMoreButton": "更多", "Common.Translation.tipFileLocked": "文件被锁定，无法编辑。您可以进行修改并将其另存为本地副本。", "Common.Translation.tipFileReadOnly": "该文件具有只读访问权限。要保留您的改动，请重新名或在不同的位置保存该文件。", "Common.Translation.warnFileLocked": "您不能编辑此文件，因为它正在另一个应用程序中被编辑。", "Common.Translation.warnFileLockedBtnEdit": "建立副本", "Common.Translation.warnFileLockedBtnView": "以浏览模式打开", "Common.UI.ButtonColored.textAutoColor": "自动", "Common.UI.ButtonColored.textNewColor": "添加新的自定义颜色", "Common.UI.Calendar.textApril": "四月", "Common.UI.Calendar.textAugust": "八月", "Common.UI.Calendar.textDecember": "十二月", "Common.UI.Calendar.textFebruary": "二月", "Common.UI.Calendar.textJanuary": "一月", "Common.UI.Calendar.textJuly": "七月", "Common.UI.Calendar.textJune": "六月", "Common.UI.Calendar.textMarch": "三月", "Common.UI.Calendar.textMay": "五月", "Common.UI.Calendar.textMonths": "月", "Common.UI.Calendar.textNovember": "十一月", "Common.UI.Calendar.textOctober": "十月", "Common.UI.Calendar.textSeptember": "九月", "Common.UI.Calendar.textShortApril": "四月", "Common.UI.Calendar.textShortAugust": "八月", "Common.UI.Calendar.textShortDecember": "十二月", "Common.UI.Calendar.textShortFebruary": "二月", "Common.UI.Calendar.textShortFriday": "星期五", "Common.UI.Calendar.textShortJanuary": "一月", "Common.UI.Calendar.textShortJuly": "七月", "Common.UI.Calendar.textShortJune": "六月", "Common.UI.Calendar.textShortMarch": "三月", "Common.UI.Calendar.textShortMay": "五月", "Common.UI.Calendar.textShortMonday": "星期一", "Common.UI.Calendar.textShortNovember": "十一月", "Common.UI.Calendar.textShortOctober": "十月", "Common.UI.Calendar.textShortSaturday": "星期六", "Common.UI.Calendar.textShortSeptember": "九月", "Common.UI.Calendar.textShortSunday": "星期日", "Common.UI.Calendar.textShortThursday": "星期四", "Common.UI.Calendar.textShortTuesday": "星期二", "Common.UI.Calendar.textShortWednesday": "我们", "Common.UI.Calendar.textYears": "年", "Common.UI.ComboBorderSize.txtNoBorders": "没有边框", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "没有边框", "Common.UI.ComboDataView.emptyComboText": "没有风格", "Common.UI.ExtendedColorDialog.addButtonText": "添加", "Common.UI.ExtendedColorDialog.textCurrent": "当前", "Common.UI.ExtendedColorDialog.textHexErr": "输入的值不正确。<br>请输入000000和FFFFFF之间的值。", "Common.UI.ExtendedColorDialog.textNew": "新", "Common.UI.ExtendedColorDialog.textRGBErr": "输入的值不正确。<br>请输入介于0和255之间的数值。", "Common.UI.HSBColorPicker.textNoColor": "没有颜色", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "隐藏密码", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "显示密码", "Common.UI.SearchBar.textFind": "查找", "Common.UI.SearchBar.tipCloseSearch": "关闭搜索", "Common.UI.SearchBar.tipNextResult": "下一个", "Common.UI.SearchBar.tipOpenAdvancedSettings": "打开高级设置", "Common.UI.SearchBar.tipPreviousResult": "上一个", "Common.UI.SearchDialog.textHighlight": "高亮效果", "Common.UI.SearchDialog.textMatchCase": "区分大小写", "Common.UI.SearchDialog.textReplaceDef": "输入替换文字", "Common.UI.SearchDialog.textSearchStart": "在这里输入你的文字", "Common.UI.SearchDialog.textTitle": "查找和替换", "Common.UI.SearchDialog.textTitle2": "查找", "Common.UI.SearchDialog.textWholeWords": "整词匹配", "Common.UI.SearchDialog.txtBtnHideReplace": "隐藏替换", "Common.UI.SearchDialog.txtBtnReplace": "替换", "Common.UI.SearchDialog.txtBtnReplaceAll": "全部替换", "Common.UI.SynchronizeTip.textDontShow": "不要再显示此消息", "Common.UI.SynchronizeTip.textSynchronize": "该文档已被其他用户更改。<br>请点击保存更改并重新加载更新。", "Common.UI.ThemeColorPalette.textRecentColors": "最近的颜色", "Common.UI.ThemeColorPalette.textStandartColors": "标准颜色", "Common.UI.ThemeColorPalette.textThemeColors": "主题颜色", "Common.UI.Themes.txtThemeClassicLight": "经典浅颜色", "Common.UI.Themes.txtThemeContrastDark": "反差暗色", "Common.UI.Themes.txtThemeDark": "暗色模式", "Common.UI.Themes.txtThemeLight": "浅颜色", "Common.UI.Themes.txtThemeSystem": "和系统一致", "Common.UI.Window.cancelButtonText": "取消", "Common.UI.Window.closeButtonText": "关闭", "Common.UI.Window.noButtonText": "否", "Common.UI.Window.okButtonText": "确定", "Common.UI.Window.textConfirmation": "确认", "Common.UI.Window.textDontShow": "不要再显示此消息", "Common.UI.Window.textError": "错误:", "Common.UI.Window.textInformation": "信息", "Common.UI.Window.textWarning": "警告", "Common.UI.Window.yesButtonText": "是", "Common.Utils.Metric.txtCm": "厘米", "Common.Utils.Metric.txtPt": "像素", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Shift", "Common.Views.About.txtAddress": "地址：", "Common.Views.About.txtLicensee": "被许可人", "Common.Views.About.txtLicensor": "许可", "Common.Views.About.txtMail": "电子邮件：", "Common.Views.About.txtPoweredBy": "技术支持", "Common.Views.About.txtTel": "电话：", "Common.Views.About.txtVersion": "版本", "Common.Views.AutoCorrectDialog.textAdd": "新增", "Common.Views.AutoCorrectDialog.textApplyText": "输入时自动应用", "Common.Views.AutoCorrectDialog.textAutoCorrect": "自动修正", "Common.Views.AutoCorrectDialog.textAutoFormat": "输入时自动调整格式", "Common.Views.AutoCorrectDialog.textBulleted": "自动项目符号列表", "Common.Views.AutoCorrectDialog.textBy": "依据", "Common.Views.AutoCorrectDialog.textDelete": "删除", "Common.Views.AutoCorrectDialog.textDoubleSpaces": "按两下空白键自动增加一个句点(.)符号", "Common.Views.AutoCorrectDialog.textFLCells": "表格单元格的首字母大写", "Common.Views.AutoCorrectDialog.textFLSentence": "将句子首字母大写", "Common.Views.AutoCorrectDialog.textHyperlink": "带超链接的互联网与网络路径", "Common.Views.AutoCorrectDialog.textHyphens": "连带字符（-）改为破折号（-）", "Common.Views.AutoCorrectDialog.textMathCorrect": "数学自动修正", "Common.Views.AutoCorrectDialog.textNumbered": "自动编号列表", "Common.Views.AutoCorrectDialog.textQuotes": "“半角引号”替换为“全角引号”", "Common.Views.AutoCorrectDialog.textRecognized": "可识别的函数", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "以下表达式被识别为数学公式。这些表达式不会被自动设为斜体。", "Common.Views.AutoCorrectDialog.textReplace": "替换", "Common.Views.AutoCorrectDialog.textReplaceText": "输入时自动替换", "Common.Views.AutoCorrectDialog.textReplaceType": "输入时自动替换文字", "Common.Views.AutoCorrectDialog.textReset": "重置", "Common.Views.AutoCorrectDialog.textResetAll": "重置为默认", "Common.Views.AutoCorrectDialog.textRestore": "恢复", "Common.Views.AutoCorrectDialog.textTitle": "自动修正", "Common.Views.AutoCorrectDialog.textWarnAddRec": "识别的函数只能包含字母A到Z，大写或小写。", "Common.Views.AutoCorrectDialog.textWarnResetRec": "由您新增的表达式将被移除，由您移除的将被恢复。是否继续？", "Common.Views.AutoCorrectDialog.warnReplace": "关于％1的自动更正条目已存在。确认替换？", "Common.Views.AutoCorrectDialog.warnReset": "即将移除对自动更正功能所做的自定义设置，并恢复默认值。是否继续？", "Common.Views.AutoCorrectDialog.warnRestore": "关于％1的自动更正条目将恢复默认值。确认继续？", "Common.Views.Chat.textSend": "发送", "Common.Views.Comments.mniAuthorAsc": "作者 (A到Z)", "Common.Views.Comments.mniAuthorDesc": "作者 (Z到A)", "Common.Views.Comments.mniDateAsc": "最老", "Common.Views.Comments.mniDateDesc": "最新", "Common.Views.Comments.mniFilterGroups": "按组筛选", "Common.Views.Comments.mniPositionAsc": "自上而下", "Common.Views.Comments.mniPositionDesc": "自下而上", "Common.Views.Comments.textAdd": "添加", "Common.Views.Comments.textAddComment": "添加批注", "Common.Views.Comments.textAddCommentToDoc": "添加批注到文档", "Common.Views.Comments.textAddReply": "添加回复", "Common.Views.Comments.textAll": "所有", "Common.Views.Comments.textAnonym": "游客", "Common.Views.Comments.textCancel": "取消", "Common.Views.Comments.textClose": "关闭", "Common.Views.Comments.textClosePanel": "关闭注释", "Common.Views.Comments.textComments": "批注", "Common.Views.Comments.textEdit": "确定", "Common.Views.Comments.textEnterCommentHint": "在此输入您的批注", "Common.Views.Comments.textHintAddComment": "添加批注", "Common.Views.Comments.textOpenAgain": "再次打开", "Common.Views.Comments.textReply": "回复", "Common.Views.Comments.textResolve": "解决", "Common.Views.Comments.textResolved": "已解决", "Common.Views.Comments.textSort": "评注排序", "Common.Views.Comments.textViewResolved": "您没有重开评论的权限", "Common.Views.Comments.txtEmpty": "文件中没有任何评论。", "Common.Views.CopyWarningDialog.textDontShow": "不要再显示此消息", "Common.Views.CopyWarningDialog.textMsg": "使用编辑器工具栏按钮和上下文菜单操作复制，剪切和粘贴操作将仅在此编辑器选项卡中执行。<br> <br>要在编辑器选项卡之外复制或粘贴到应用程序，请使用以下键盘组合：", "Common.Views.CopyWarningDialog.textTitle": "复制，剪切和粘贴操作", "Common.Views.CopyWarningDialog.textToCopy": "复制", "Common.Views.CopyWarningDialog.textToCut": "剪切", "Common.Views.CopyWarningDialog.textToPaste": "粘贴", "Common.Views.DocumentAccessDialog.textLoading": "载入中……", "Common.Views.DocumentAccessDialog.textTitle": "共享设置", "Common.Views.ExternalDiagramEditor.textTitle": "图表编辑器", "Common.Views.ExternalEditor.textClose": "关闭", "Common.Views.ExternalEditor.textSave": "保存并退出", "Common.Views.ExternalMergeEditor.textTitle": "邮件合并收件人", "Common.Views.ExternalOleEditor.textTitle": "电子表格编辑器", "Common.Views.Header.labelCoUsersDescr": "正在编辑文件的用户：", "Common.Views.Header.textAddFavorite": "记作喜爱内容", "Common.Views.Header.textAdvSettings": "高级设置", "Common.Views.Header.textBack": "打开文件所在位置", "Common.Views.Header.textCompactView": "查看紧凑工具栏", "Common.Views.Header.textHideLines": "隐藏标尺", "Common.Views.Header.textHideStatusBar": "隐藏状态栏", "Common.Views.Header.textReadOnly": "只读", "Common.Views.Header.textRemoveFavorite": "从收藏夹中删除", "Common.Views.Header.textShare": "共享", "Common.Views.Header.textZoom": "放大", "Common.Views.Header.tipAccessRights": "管理文档访问权限", "Common.Views.Header.tipDownload": "下载文件", "Common.Views.Header.tipGoEdit": "编辑当前文件", "Common.Views.Header.tipPrint": "打印文件", "Common.Views.Header.tipPrintQuick": "快速打印", "Common.Views.Header.tipRedo": "重做", "Common.Views.Header.tipSave": "保存", "Common.Views.Header.tipSearch": "搜索", "Common.Views.Header.tipUndo": "撤消", "Common.Views.Header.tipUsers": "查看用户", "Common.Views.Header.tipViewSettings": "视图设置", "Common.Views.Header.tipViewUsers": "查看用户和管理文档访问权限", "Common.Views.Header.txtAccessRights": "更改访问权限", "Common.Views.Header.txtRename": "重命名", "Common.Views.History.textCloseHistory": "关闭历史记录", "Common.Views.History.textHide": "折叠", "Common.Views.History.textHideAll": "隐藏详细的更改", "Common.Views.History.textRestore": "还原", "Common.Views.History.textShow": "扩大", "Common.Views.History.textShowAll": "显示详细的更改", "Common.Views.History.textVer": "版本", "Common.Views.ImageFromUrlDialog.textUrl": "粘贴图片网址：", "Common.Views.ImageFromUrlDialog.txtEmpty": "这是必填栏", "Common.Views.ImageFromUrlDialog.txtNotUrl": "该字段应该是“http://www.example.com”格式的URL", "Common.Views.InsertTableDialog.textInvalidRowsCols": "您需要指定有效的行数和列数。", "Common.Views.InsertTableDialog.txtColumns": "列数", "Common.Views.InsertTableDialog.txtMaxText": "该字段的最大值为{0}。", "Common.Views.InsertTableDialog.txtMinText": "该字段的最小值为{0}。", "Common.Views.InsertTableDialog.txtRows": "行数", "Common.Views.InsertTableDialog.txtTitle": "表格大小", "Common.Views.InsertTableDialog.txtTitleSplit": "拆分单元格", "Common.Views.LanguageDialog.labelSelect": "选择文档语言", "Common.Views.OpenDialog.closeButtonText": "关闭文件", "Common.Views.OpenDialog.txtEncoding": "编码", "Common.Views.OpenDialog.txtIncorrectPwd": "密码错误", "Common.Views.OpenDialog.txtOpenFile": "输入密码来打开文件", "Common.Views.OpenDialog.txtPassword": "密码", "Common.Views.OpenDialog.txtPreview": "预览", "Common.Views.OpenDialog.txtProtected": "在您输入密码和打开文件后，该文件的当前密码将被重置。", "Common.Views.OpenDialog.txtTitle": "选择%1个选项", "Common.Views.OpenDialog.txtTitleProtected": "受保护的文件", "Common.Views.PasswordDialog.txtDescription": "设置密码以保护此文档", "Common.Views.PasswordDialog.txtIncorrectPwd": "确认密码不一致", "Common.Views.PasswordDialog.txtPassword": "密码", "Common.Views.PasswordDialog.txtRepeat": "重复输入密码", "Common.Views.PasswordDialog.txtTitle": "设置密码", "Common.Views.PasswordDialog.txtWarning": "警告: 如果丢失或忘记密码，则无法将其恢复。请妥善保存。", "Common.Views.PluginDlg.textLoading": "载入中", "Common.Views.Plugins.groupCaption": "插件", "Common.Views.Plugins.strPlugins": "插件", "Common.Views.Plugins.textClosePanel": "关闭插件", "Common.Views.Plugins.textLoading": "载入中", "Common.Views.Plugins.textStart": "开始", "Common.Views.Plugins.textStop": "停止", "Common.Views.Protection.hintAddPwd": "使用密码加密", "Common.Views.Protection.hintDelPwd": "删除密码", "Common.Views.Protection.hintPwd": "修改或删除密码", "Common.Views.Protection.hintSignature": "添加数字签名或签名行", "Common.Views.Protection.txtAddPwd": "添加密码", "Common.Views.Protection.txtChangePwd": "修改密码", "Common.Views.Protection.txtDeletePwd": "删除密码", "Common.Views.Protection.txtEncrypt": "加密", "Common.Views.Protection.txtInvisibleSignature": "添加数字签名", "Common.Views.Protection.txtSignature": "签名", "Common.Views.Protection.txtSignatureLine": "添加签名行", "Common.Views.RenameDialog.textName": "文件名", "Common.Views.RenameDialog.txtInvalidName": "文件名不能包含以下任何字符：", "Common.Views.ReviewChanges.hintNext": "下一个变化", "Common.Views.ReviewChanges.hintPrev": "以前的变化", "Common.Views.ReviewChanges.mniFromFile": "文件中的文档", "Common.Views.ReviewChanges.mniFromStorage": " 存储中的文档", "Common.Views.ReviewChanges.mniFromUrl": "url中的文档", "Common.Views.ReviewChanges.mniSettings": "比较设置", "Common.Views.ReviewChanges.strFast": "自动", "Common.Views.ReviewChanges.strFastDesc": "自动共同编辑模式，自动保存修改痕迹。", "Common.Views.ReviewChanges.strStrict": "手动", "Common.Views.ReviewChanges.strStrictDesc": "使用“保存”按钮同步你和其他人的修改。", "Common.Views.ReviewChanges.textEnable": "启动", "Common.Views.ReviewChanges.textWarnTrackChanges": "对全体有完整控制权的用户，“跟踪修改”功能将会启动。任何人下次打开该文档，“跟踪修改”功能都会保持在启动状态。", "Common.Views.ReviewChanges.textWarnTrackChangesTitle": "是否为所有人启动“跟踪修改”？", "Common.Views.ReviewChanges.tipAcceptCurrent": "接受当前的变化", "Common.Views.ReviewChanges.tipCoAuthMode": "设置共同编辑模式", "Common.Views.ReviewChanges.tipCommentRem": "移除批注", "Common.Views.ReviewChanges.tipCommentRemCurrent": "移除当前批注", "Common.Views.ReviewChanges.tipCommentResolve": "解决评论", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "解决该评论", "Common.Views.ReviewChanges.tipCompare": "将当前文档与另一个文档进行比较", "Common.Views.ReviewChanges.tipHistory": "显示历史版本", "Common.Views.ReviewChanges.tipRejectCurrent": "拒绝当前的变化", "Common.Views.ReviewChanges.tipReview": "跟踪变化", "Common.Views.ReviewChanges.tipReviewView": "选择您希望显示更改的模式", "Common.Views.ReviewChanges.tipSetDocLang": "设置文档语言", "Common.Views.ReviewChanges.tipSetSpelling": "拼写检查", "Common.Views.ReviewChanges.tipSharing": "管理文档访问权限", "Common.Views.ReviewChanges.txtAccept": "接受", "Common.Views.ReviewChanges.txtAcceptAll": "接受所有更改", "Common.Views.ReviewChanges.txtAcceptChanges": "接受更改", "Common.Views.ReviewChanges.txtAcceptCurrent": "接受当前的变化", "Common.Views.ReviewChanges.txtChat": "聊天", "Common.Views.ReviewChanges.txtClose": "关闭", "Common.Views.ReviewChanges.txtCoAuthMode": "共同编辑模式", "Common.Views.ReviewChanges.txtCommentRemAll": "移除所有批注", "Common.Views.ReviewChanges.txtCommentRemCurrent": "移除当前批注", "Common.Views.ReviewChanges.txtCommentRemMy": "移除我的批注", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "移除我的当前批注", "Common.Views.ReviewChanges.txtCommentRemove": "删除", "Common.Views.ReviewChanges.txtCommentResolve": "解决", "Common.Views.ReviewChanges.txtCommentResolveAll": "解决所有评论", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "解决该评论", "Common.Views.ReviewChanges.txtCommentResolveMy": "解决我的评论", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "解决我当前的评论", "Common.Views.ReviewChanges.txtCompare": "比较", "Common.Views.ReviewChanges.txtDocLang": "语言", "Common.Views.ReviewChanges.txtEditing": "编辑", "Common.Views.ReviewChanges.txtFinal": "已接受所有更改{0}", "Common.Views.ReviewChanges.txtFinalCap": "最终版", "Common.Views.ReviewChanges.txtHistory": "版本历史", "Common.Views.ReviewChanges.txtMarkup": "所有更改{0}", "Common.Views.ReviewChanges.txtMarkupCap": "标记批注框", "Common.Views.ReviewChanges.txtMarkupSimple": "所有修改 {0}<br>无通知", "Common.Views.ReviewChanges.txtMarkupSimpleCap": "仅标记", "Common.Views.ReviewChanges.txtNext": "下一个变化", "Common.Views.ReviewChanges.txtOff": "对我自己关闭", "Common.Views.ReviewChanges.txtOffGlobal": "对所有人关闭", "Common.Views.ReviewChanges.txtOn": "对我自己启动", "Common.Views.ReviewChanges.txtOnGlobal": "对所有人启动", "Common.Views.ReviewChanges.txtOriginal": "已拒绝所有更改{0}", "Common.Views.ReviewChanges.txtOriginalCap": "原始版", "Common.Views.ReviewChanges.txtPrev": "以前的变化", "Common.Views.ReviewChanges.txtPreview": "预览", "Common.Views.ReviewChanges.txtReject": "拒绝", "Common.Views.ReviewChanges.txtRejectAll": "拒绝所有更改", "Common.Views.ReviewChanges.txtRejectChanges": "拒绝更改", "Common.Views.ReviewChanges.txtRejectCurrent": "拒绝当前的变化", "Common.Views.ReviewChanges.txtSharing": "分享", "Common.Views.ReviewChanges.txtSpelling": "拼写检查", "Common.Views.ReviewChanges.txtTurnon": "跟踪变化", "Common.Views.ReviewChanges.txtView": "显示模式", "Common.Views.ReviewChangesDialog.textTitle": "查看更改", "Common.Views.ReviewChangesDialog.txtAccept": "接受", "Common.Views.ReviewChangesDialog.txtAcceptAll": "接受所有更改", "Common.Views.ReviewChangesDialog.txtAcceptCurrent": "接受当前的变化", "Common.Views.ReviewChangesDialog.txtNext": "下一个变化", "Common.Views.ReviewChangesDialog.txtPrev": "以前的变化", "Common.Views.ReviewChangesDialog.txtReject": "拒绝", "Common.Views.ReviewChangesDialog.txtRejectAll": "拒绝所有更改", "Common.Views.ReviewChangesDialog.txtRejectCurrent": "拒绝当前的变化", "Common.Views.ReviewPopover.textAdd": "添加", "Common.Views.ReviewPopover.textAddReply": "添加回复", "Common.Views.ReviewPopover.textCancel": "取消", "Common.Views.ReviewPopover.textClose": "关闭", "Common.Views.ReviewPopover.textEdit": "确定", "Common.Views.ReviewPopover.textEnterComment": "在此输入您的批注", "Common.Views.ReviewPopover.textFollowMove": "跟随移动", "Common.Views.ReviewPopover.textMention": "+提供访问文档和发送电子邮件的权限", "Common.Views.ReviewPopover.textMentionNotify": "+通知将通过电子邮件通知用户", "Common.Views.ReviewPopover.textOpenAgain": "再次打开", "Common.Views.ReviewPopover.textReply": "回复", "Common.Views.ReviewPopover.textResolve": "解决", "Common.Views.ReviewPopover.textViewResolved": "您没有重开评论的权限", "Common.Views.ReviewPopover.txtAccept": "接受", "Common.Views.ReviewPopover.txtDeleteTip": "删除", "Common.Views.ReviewPopover.txtEditTip": "编辑", "Common.Views.ReviewPopover.txtReject": "拒绝", "Common.Views.SaveAsDlg.textLoading": "载入中", "Common.Views.SaveAsDlg.textTitle": "保存文件夹", "Common.Views.SearchPanel.textCaseSensitive": "区分大小写", "Common.Views.SearchPanel.textCloseSearch": "关闭搜索", "Common.Views.SearchPanel.textContentChanged": "文件已更改。", "Common.Views.SearchPanel.textFind": "查找", "Common.Views.SearchPanel.textFindAndReplace": "查找和替换", "Common.Views.SearchPanel.textMatchUsingRegExp": "正则匹配", "Common.Views.SearchPanel.textNoMatches": "找不到匹配信息", "Common.Views.SearchPanel.textNoSearchResults": "找不到搜索结果", "Common.Views.SearchPanel.textReplace": "替换", "Common.Views.SearchPanel.textReplaceAll": "全部替换", "Common.Views.SearchPanel.textReplaceWith": "替换为", "Common.Views.SearchPanel.textSearchAgain": "{0}进行新的搜索{1}以获得准确结果。", "Common.Views.SearchPanel.textSearchHasStopped": "搜索已停止", "Common.Views.SearchPanel.textSearchResults": "搜索结果：{0}/{1}", "Common.Views.SearchPanel.textTooManyResults": "这里显示的结果太多了", "Common.Views.SearchPanel.textWholeWords": "整词匹配", "Common.Views.SearchPanel.tipNextResult": "下一个", "Common.Views.SearchPanel.tipPreviousResult": "上一个", "Common.Views.SelectFileDlg.textLoading": "载入中", "Common.Views.SelectFileDlg.textTitle": "选择数据源", "Common.Views.SignDialog.textBold": "加粗", "Common.Views.SignDialog.textCertificate": "证书", "Common.Views.SignDialog.textChange": "修改", "Common.Views.SignDialog.textInputName": "输入签名者姓名", "Common.Views.SignDialog.textItalic": "斜体", "Common.Views.SignDialog.textNameError": "签名者名称不能为空", "Common.Views.SignDialog.textPurpose": "签署本文档的目的", "Common.Views.SignDialog.textSelect": "请选择", "Common.Views.SignDialog.textSelectImage": "选择图像", "Common.Views.SignDialog.textSignature": "签名类似", "Common.Views.SignDialog.textTitle": "签署文件", "Common.Views.SignDialog.textUseImage": "或单击“选择图像”将图片用作签名", "Common.Views.SignDialog.textValid": "从%1到%2有效", "Common.Views.SignDialog.tipFontName": "字体名称", "Common.Views.SignDialog.tipFontSize": "字体大小", "Common.Views.SignSettingsDialog.textAllowComment": "允许签名者在签名对话框中添加批注", "Common.Views.SignSettingsDialog.textDefInstruction": "在签署本文件之前，请确保您所签署的内容是正确。", "Common.Views.SignSettingsDialog.textInfoEmail": "建议签署人电邮", "Common.Views.SignSettingsDialog.textInfoName": "建议签署人", "Common.Views.SignSettingsDialog.textInfoTitle": "建议签署人职务", "Common.Views.SignSettingsDialog.textInstructions": "给签名者的说明", "Common.Views.SignSettingsDialog.textShowDate": "在签名行中显示签名日期", "Common.Views.SignSettingsDialog.textTitle": "签名设置", "Common.Views.SignSettingsDialog.txtEmpty": "这是必填栏", "Common.Views.SymbolTableDialog.textCharacter": "字符", "Common.Views.SymbolTableDialog.textCode": "Unicode十六进制值", "Common.Views.SymbolTableDialog.textCopyright": "版权所有标识", "Common.Views.SymbolTableDialog.textDCQuote": "后双引号", "Common.Views.SymbolTableDialog.textDOQuote": "前双引号", "Common.Views.SymbolTableDialog.textEllipsis": "横向省略号", "Common.Views.SymbolTableDialog.textEmDash": "破折号", "Common.Views.SymbolTableDialog.textEmSpace": "全角空格", "Common.Views.SymbolTableDialog.textEnDash": "半破折号", "Common.Views.SymbolTableDialog.textEnSpace": "半角空格", "Common.Views.SymbolTableDialog.textFont": "字体 ", "Common.Views.SymbolTableDialog.textNBHyphen": "不换行连词符", "Common.Views.SymbolTableDialog.textNBSpace": "不换行空格", "Common.Views.SymbolTableDialog.textPilcrow": "段落标识", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4全角空格", "Common.Views.SymbolTableDialog.textRange": "子集", "Common.Views.SymbolTableDialog.textRecent": "最近使用的符号", "Common.Views.SymbolTableDialog.textRegistered": "注册商标标识", "Common.Views.SymbolTableDialog.textSCQuote": "后单引号", "Common.Views.SymbolTableDialog.textSection": "章节标识", "Common.Views.SymbolTableDialog.textShortcut": "快捷键", "Common.Views.SymbolTableDialog.textSHyphen": "软连词符", "Common.Views.SymbolTableDialog.textSOQuote": "前单引号", "Common.Views.SymbolTableDialog.textSpecial": "特殊字符", "Common.Views.SymbolTableDialog.textSymbols": "符号", "Common.Views.SymbolTableDialog.textTitle": "符号", "Common.Views.SymbolTableDialog.textTradeMark": "商标标识", "Common.Views.UserNameDialog.textDontShow": "不要再问我", "Common.Views.UserNameDialog.textLabel": "标签：", "Common.Views.UserNameDialog.textLabelError": "标签不能空", "DE.Controllers.DocProtection.txtIsProtectedComment": "文件受保护。\n在本文件中您只能发评论。", "DE.Controllers.DocProtection.txtIsProtectedForms": "文件受保护。\n在本文件中您只能填写表单。", "DE.Controllers.DocProtection.txtIsProtectedTrack": "文件受保护。\n您可以编辑这个文件，但所有的更改都会被跟踪。", "DE.Controllers.DocProtection.txtIsProtectedView": "文件受保护。\n本文件您只能查看。", "DE.Controllers.DocProtection.txtWasProtectedComment": "文件已被其他用户保护。\n在本文件中您只能发评论。", "DE.Controllers.DocProtection.txtWasProtectedForms": "文件已被其他用户保护。\n在本文件中您只能填写表单。", "DE.Controllers.DocProtection.txtWasProtectedTrack": "文件已被其他用户保护。\n您可以编辑这个文件，但所有的更改都会被跟踪。", "DE.Controllers.DocProtection.txtWasProtectedView": "文件已被其他用户保护。\n本文件您只能查看。", "DE.Controllers.DocProtection.txtWasUnprotected": "文件已经不受保护了。", "DE.Controllers.LeftMenu.leavePageText": "本文档中的所有未保存的更改都将丢失。<br>单击“取消”，然后单击“保存”保存。单击“确定”以放弃所有未保存的更改。", "DE.Controllers.LeftMenu.newDocumentTitle": "未命名的文档", "DE.Controllers.LeftMenu.notcriticalErrorTitle": "警告", "DE.Controllers.LeftMenu.requestEditRightsText": "请求编辑权限..", "DE.Controllers.LeftMenu.textLoadHistory": "载入版本历史记录中...", "DE.Controllers.LeftMenu.textNoTextFound": "您搜索的数据无法找到。请调整您的搜索选项。", "DE.Controllers.LeftMenu.textReplaceSkipped": "已经更换了。{0}次跳过。", "DE.Controllers.LeftMenu.textReplaceSuccess": "他的搜索已经完成。发生次数：{0}", "DE.Controllers.LeftMenu.txtCompatible": "文档将保存为新格式。它将允许使用所有编辑器功能，但可能会影响文档布局。<br>如果要使文档与旧版微软 Word 兼容，请使用高级设置的“兼容性”选项。", "DE.Controllers.LeftMenu.txtUntitled": "无标题", "DE.Controllers.LeftMenu.warnDownloadAs": "如果您继续以此格式保存，除文本之外的所有功能将丢失。<br>您确定要继续吗？", "DE.Controllers.LeftMenu.warnDownloadAsPdf": "您的 {0} 将转换成一份可修改的文件。系统需要处理一段时间。转换后的文件即可随之修改，但可能不完全跟您的原 {0} 相同，特别是如果原文件有过多的图像将会更有差异。", "DE.Controllers.LeftMenu.warnDownloadAsRTF": "如果您继续以此格式保存，一些格式可能会丢失。<br>您确定要继续吗？", "DE.Controllers.LeftMenu.warnReplaceString": "{0}不是可用于“替换字段”的有效特殊字符。", "DE.Controllers.Main.applyChangesTextText": "载入更改...", "DE.Controllers.Main.applyChangesTitleText": "加载更改", "DE.Controllers.Main.confirmMaxChangesSize": "行动的大小超过了对您服务器设置的限制。<br>按 \"撤消\"取消您的最后一次行动，或按\"继续\"在本地保留该行动（您需要下载文件或复制其内容以确保没有任何损失）。", "DE.Controllers.Main.convertationTimeoutText": "转换超时", "DE.Controllers.Main.criticalErrorExtText": "按“确定”返回该文件列表。", "DE.Controllers.Main.criticalErrorTitle": "错误:", "DE.Controllers.Main.downloadErrorText": "下载失败", "DE.Controllers.Main.downloadMergeText": "下载中…", "DE.Controllers.Main.downloadMergeTitle": "下载中", "DE.Controllers.Main.downloadTextText": "正在下载文件...", "DE.Controllers.Main.downloadTitleText": "下载文件", "DE.Controllers.Main.errorAccessDeny": "您正在尝试执行您没有权限的操作。<br>请联系您的文档服务器管理员。", "DE.Controllers.Main.errorBadImageUrl": "图片地址不正确", "DE.Controllers.Main.errorCannotPasteImg": "我们无法从剪贴板上粘贴这个图片。您可以把它保存到您的设备上，然后从那里进行插入。\n或者您可以不复制文本而只复制图片，并将它粘贴到文档中。", "DE.Controllers.Main.errorCoAuthoringDisconnect": "服务器连接丢失。该文档现在无法编辑", "DE.Controllers.Main.errorComboSeries": "要创建联立图表，请选中至少两组数据。", "DE.Controllers.Main.errorCompare": "协作编辑状态下，无法使用文件比对功能。", "DE.Controllers.Main.errorConnectToServer": "这份文件无法保存。请检查连接设置或联系您的管理员。<br>当您点击“OK”按钮,系统将提示您下载文档。", "DE.Controllers.Main.errorDatabaseConnection": "外部错误。<br>数据库连接错误。如果错误仍然存​​在，请联系支持人员。", "DE.Controllers.Main.errorDataEncrypted": "加密更改已收到，无法对其解密。", "DE.Controllers.Main.errorDataRange": "数据范围不正确", "DE.Controllers.Main.errorDefaultMessage": "错误代码：%1", "DE.Controllers.Main.errorDirectUrl": "请验证指向文档的链接。<br>此链接必须是指向要下载的文件的直接链接。", "DE.Controllers.Main.errorEditingDownloadas": "在处理文档期间发生错误。<br>使用“下载为”选项将文件备份复制到您的计算机硬盘中。", "DE.Controllers.Main.errorEditingSaveas": "在处理文档期间发生错误。<br>使用“另存为…”选项将文件备份复制到计算机硬盘中。", "DE.Controllers.Main.errorEmailClient": "未找到电子邮件客户端。", "DE.Controllers.Main.errorEmptyTOC": "开始创建一个目录并应用样式库中的标题样式到选择的文本。", "DE.Controllers.Main.errorFilePassProtect": "该文档受密码保护，无法被打开。", "DE.Controllers.Main.errorFileSizeExceed": "文件大小超出了为服务器设置的限制.<br>有关详细信息，请与文档服务器管理员联系。", "DE.Controllers.Main.errorForceSave": "保存文件时发生错误请使用“下载为”选项将文件保存到计算机硬盘中或稍后重试。", "DE.Controllers.Main.errorInconsistentExt": "在打开文件时发生了错误。<br> 该文件的扩展名不一致：%1。", "DE.Controllers.Main.errorInconsistentExtDocx": "在打开文件时发生了错误。<br>文件内容对应于文本文档（如 docx），但该文件的扩展名不一致：%1。", "DE.Controllers.Main.errorInconsistentExtPdf": "在打开文件时发生了错误。<br>文件内容对应于以下格式之一：pdf/djvu/xps/oxps，但该文件的扩展名不一致：%1。", "DE.Controllers.Main.errorInconsistentExtPptx": "在打开文件时发生了错误。<br>文件内容对应于演示文稿（如 pptx），但该文件的扩展名不一致：%1。", "DE.Controllers.Main.errorInconsistentExtXlsx": "在打开文件时发生了错误。<br>文件内容对应于电子表格（如 xlsx），但该文件的扩展名不一致：%1。", "DE.Controllers.Main.errorKeyEncrypt": "未知密钥描述", "DE.Controllers.Main.errorKeyExpire": "密钥过期", "DE.Controllers.Main.errorLoadingFont": "字体未加载。<br>请与文档服务器管理员联系。", "DE.Controllers.Main.errorMailMergeLoadFile": "加载文件失败了。请选择另一份文件。", "DE.Controllers.Main.errorMailMergeSaveFile": "合并失败", "DE.Controllers.Main.errorNoTOC": "没有目录要更新。你可以从引用标签插入一个目录。", "DE.Controllers.Main.errorPasswordIsNotCorrect": "输入的密码不正确。<br> 请检查 CAPS LOCK 键的状态，确认输入密码的大小写正确。", "DE.Controllers.Main.errorProcessSaveResult": "保存失败", "DE.Controllers.Main.errorServerVersion": "该编辑版本已经更新。该页面将被重新加载以应用更改。", "DE.Controllers.Main.errorSessionAbsolute": "文档编辑会话已过期。请重新加载页面。", "DE.Controllers.Main.errorSessionIdle": "该文件尚未编辑相当长的时间。请重新加载页面。", "DE.Controllers.Main.errorSessionToken": "与服务器的连接已中断。请重新加载页面。", "DE.Controllers.Main.errorSetPassword": "未能成功设置密码", "DE.Controllers.Main.errorStockChart": "行顺序不正确。建立股票图表将数据按照以下顺序放置在表格上：<br>开盘价，最高价格，最低价格，收盘价。", "DE.Controllers.Main.errorSubmit": "提交失败", "DE.Controllers.Main.errorTextFormWrongFormat": "输入的值与该字段的格式不一致。", "DE.Controllers.Main.errorToken": "文档安全令牌未正确形成。<br>请与您的文件服务器管理员联系。", "DE.Controllers.Main.errorTokenExpire": "文档安全令牌已过期。<br>请与您的文档服务器管理员联系。", "DE.Controllers.Main.errorUpdateVersion": "该文件版本已经改变了。该页面将被重新加载。", "DE.Controllers.Main.errorUpdateVersionOnDisconnect": "网连接已还原文件版本已更改。.<br>在继续工作之前，需要下载文件或复制其内容以确保没有丢失任何内容，然后重新加载此页。", "DE.Controllers.Main.errorUserDrop": "该文件现在无法访问。", "DE.Controllers.Main.errorUsersExceed": "超过了定价计划允许的用户数", "DE.Controllers.Main.errorViewerDisconnect": "连接丢失了。您仍然可以查看文档，<br>但在连接恢复并页面被重新加载之前无法下载或打印。", "DE.Controllers.Main.leavePageText": "您在本文档中有未保存的更改。点击“留在此页面”，然后点击“保存”以保存保存。点击“离开此页面”，以放弃所有未保存的更改。", "DE.Controllers.Main.leavePageTextOnClose": "本文档中的所有未保存的更改都将丢失。<br>单击“取消”，然后单击“保存”保存。单击“确定”以放弃所有未保存的更改。", "DE.Controllers.Main.loadFontsTextText": "数据加载中…", "DE.Controllers.Main.loadFontsTitleText": "数据加载中", "DE.Controllers.Main.loadFontTextText": "数据加载中…", "DE.Controllers.Main.loadFontTitleText": "数据加载中", "DE.Controllers.Main.loadImagesTextText": "图片加载中…", "DE.Controllers.Main.loadImagesTitleText": "图片加载中", "DE.Controllers.Main.loadImageTextText": "图片加载中…", "DE.Controllers.Main.loadImageTitleText": "图片加载中", "DE.Controllers.Main.loadingDocumentTextText": "文件加载中…", "DE.Controllers.Main.loadingDocumentTitleText": "文件加载中…", "DE.Controllers.Main.mailMergeLoadFileText": "原始数据加载中…", "DE.Controllers.Main.mailMergeLoadFileTitle": "原始数据加载中…", "DE.Controllers.Main.notcriticalErrorTitle": "警告", "DE.Controllers.Main.openErrorText": "打开文件时出现错误", "DE.Controllers.Main.openTextText": "打开文件...", "DE.Controllers.Main.openTitleText": "正在打开文件", "DE.Controllers.Main.printTextText": "打印文件", "DE.Controllers.Main.printTitleText": "打印文件", "DE.Controllers.Main.reloadButtonText": "重新加载页面", "DE.Controllers.Main.requestEditFailedMessageText": "有人正在编辑此文档。请稍后再试。", "DE.Controllers.Main.requestEditFailedTitleText": "访问被拒绝", "DE.Controllers.Main.saveErrorText": "保存文件时出现错误", "DE.Controllers.Main.saveErrorTextDesktop": "无法保存或创建此文件。<br>可能的原因是：<br> 1.此文件是只读的。 <br> 2.此文件正在由其他用户编辑。 <br> 3.磁盘已满或损坏。", "DE.Controllers.Main.saveTextText": "保存文件…", "DE.Controllers.Main.saveTitleText": "保存文件", "DE.Controllers.Main.scriptLoadError": "连接速度过慢，部分组件无法被加载。请重新加载页面。", "DE.Controllers.Main.sendMergeText": "任务合并", "DE.Controllers.Main.sendMergeTitle": "任务合并", "DE.Controllers.Main.splitDividerErrorText": "行数必须为%1的除数。", "DE.Controllers.Main.splitMaxColsErrorText": "列数必须小于%1。", "DE.Controllers.Main.splitMaxRowsErrorText": "行数必须小于%1。", "DE.Controllers.Main.textAnonymous": "匿名", "DE.Controllers.Main.textAnyone": "任何人", "DE.Controllers.Main.textApplyAll": "应用到所有公式", "DE.Controllers.Main.textBuyNow": "访问网站", "DE.Controllers.Main.textChangesSaved": "所有更改已保存", "DE.Controllers.Main.textClose": "关闭", "DE.Controllers.Main.textCloseTip": "点击关闭提示", "DE.Controllers.Main.textContactUs": "联系销售", "DE.Controllers.Main.textContinue": "继续", "DE.Controllers.Main.textConvertEquation": "这个公式是由一个早期版本的公式编辑器创建的。这个版本现在不受支持了。要想编辑这个公式，你需要将其转换成 Office Math ML 格式.<br>现在转换吗？", "DE.Controllers.Main.textCustomLoader": "请注意，根据许可条款您无权更改加载程序。<br>请联系我们的销售部门获取报价。", "DE.Controllers.Main.textDisconnect": "网络连接失败", "DE.Controllers.Main.textGuest": "访客", "DE.Controllers.Main.textHasMacros": "这个文件带有自动宏。<br> 是否要运行宏？", "DE.Controllers.Main.textLearnMore": "了解更多", "DE.Controllers.Main.textLoadingDocument": "文件加载中…", "DE.Controllers.Main.textLongName": "输入名称，要求小于128字符。", "DE.Controllers.Main.textNoLicenseTitle": "已达到许可证限制", "DE.Controllers.Main.textPaidFeature": "付费功能", "DE.Controllers.Main.textReconnect": "连接已恢复", "DE.Controllers.Main.textRemember": "记住我为所有文件的选择", "DE.Controllers.Main.textRememberMacros": "记住我的选择", "DE.Controllers.Main.textRenameError": "用户名不能留空。", "DE.Controllers.Main.textRenameLabel": "输入名称，可用于协作。", "DE.Controllers.Main.textRequestMacros": "宏发起一个请求至URL。你是否允许请求到％1？", "DE.Controllers.Main.textShape": "形状", "DE.Controllers.Main.textStrict": "手动模式", "DE.Controllers.Main.textText": "文本", "DE.Controllers.Main.textTryQuickPrint": "您已经选择了快速打印：整个文件将用最新选定的打印机或默认的打印机被打印。<br>您想继续吗？", "DE.Controllers.Main.textTryUndoRedo": "对于自动的协同编辑模式，取消/重做功能是禁用的。< br >单击“手动模式”按钮切换到手动协同编辑模式，这样，编辑该文件时只有您保存修改之后，其他用户才能访问这些修改。您可以使用编辑器高级设置易于切换编辑模式。", "DE.Controllers.Main.textTryUndoRedoWarn": "自动共同编辑模式下，撤销/重做功能被禁用。", "DE.Controllers.Main.textUndo": "撤消", "DE.Controllers.Main.titleLicenseExp": "许可证过期", "DE.Controllers.Main.titleServerVersion": "编辑器已更新", "DE.Controllers.Main.titleUpdateVersion": "版本已变化", "DE.Controllers.Main.txtAbove": "以上", "DE.Controllers.Main.txtArt": "在此输入文字", "DE.Controllers.Main.txtBasicShapes": "基本形状", "DE.Controllers.Main.txtBelow": "下面", "DE.Controllers.Main.txtBookmarkError": "错误！未定义书签。", "DE.Controllers.Main.txtButtons": "按钮", "DE.Controllers.Main.txtCallouts": "标注", "DE.Controllers.Main.txtCharts": "流程图", "DE.Controllers.Main.txtChoose": "选择一项", "DE.Controllers.Main.txtClickToLoad": "点按以加载图像", "DE.Controllers.Main.txtCurrentDocument": "当前文件", "DE.Controllers.Main.txtDiagramTitle": "图表标题", "DE.Controllers.Main.txtEditingMode": "设置编辑模式..", "DE.Controllers.Main.txtEndOfFormula": "意外的公式结尾", "DE.Controllers.Main.txtEnterDate": "输入日期", "DE.Controllers.Main.txtErrorLoadHistory": "历史加载失败", "DE.Controllers.Main.txtEvenPage": "偶数页", "DE.Controllers.Main.txtFiguredArrows": "箭头汇总", "DE.Controllers.Main.txtFirstPage": "首页", "DE.Controllers.Main.txtFooter": "页脚", "DE.Controllers.Main.txtFormulaNotInTable": "公式不在表格中", "DE.Controllers.Main.txtHeader": "页眉", "DE.Controllers.Main.txtHyperlink": "超链接", "DE.Controllers.Main.txtIndTooLarge": "指数过大", "DE.Controllers.Main.txtLines": "线条", "DE.Controllers.Main.txtMainDocOnly": "错误！仅限主文档。", "DE.Controllers.Main.txtMath": "公式形状", "DE.Controllers.Main.txtMissArg": "缺少参数", "DE.Controllers.Main.txtMissOperator": "缺少运算符", "DE.Controllers.Main.txtNeedSynchronize": "您有更新", "DE.Controllers.Main.txtNone": "无", "DE.Controllers.Main.txtNoTableOfContents": "文档中无标题。文本中应用标题样式以便使其出现在目录中。", "DE.Controllers.Main.txtNoTableOfFigures": "未找到图片或表格的元素。", "DE.Controllers.Main.txtNoText": "错误！文档中没有指定样式的文本。", "DE.Controllers.Main.txtNotInTable": "不在表格中", "DE.Controllers.Main.txtNotValidBookmark": "错误！不是有效的书签自引用。", "DE.Controllers.Main.txtOddPage": "奇数页", "DE.Controllers.Main.txtOnPage": "在页面上", "DE.Controllers.Main.txtRectangles": "矩形", "DE.Controllers.Main.txtSameAsPrev": "与上一个相同", "DE.Controllers.Main.txtSection": "-部分", "DE.Controllers.Main.txtSeries": "系列", "DE.Controllers.Main.txtShape_accentBorderCallout1": "线形标注1（边框和强调线）", "DE.Controllers.Main.txtShape_accentBorderCallout2": "线形标注2（边框和强调线）", "DE.Controllers.Main.txtShape_accentBorderCallout3": "线形标注3（边框和强调线）", "DE.Controllers.Main.txtShape_accentCallout1": "线形标注1（强调线）", "DE.Controllers.Main.txtShape_accentCallout2": "线形标注2（强调线）", "DE.Controllers.Main.txtShape_accentCallout3": "线形标注3（强调线）", "DE.Controllers.Main.txtShape_actionButtonBackPrevious": "后退或上一个按钮", "DE.Controllers.Main.txtShape_actionButtonBeginning": "开始按钮", "DE.Controllers.Main.txtShape_actionButtonBlank": "空白按钮", "DE.Controllers.Main.txtShape_actionButtonDocument": "文件按钮", "DE.Controllers.Main.txtShape_actionButtonEnd": "结束按钮", "DE.Controllers.Main.txtShape_actionButtonForwardNext": "转发或下一个按钮", "DE.Controllers.Main.txtShape_actionButtonHelp": "帮助按钮", "DE.Controllers.Main.txtShape_actionButtonHome": "主页按钮", "DE.Controllers.Main.txtShape_actionButtonInformation": "信息按钮", "DE.Controllers.Main.txtShape_actionButtonMovie": "视频按钮", "DE.Controllers.Main.txtShape_actionButtonReturn": "返回按钮", "DE.Controllers.Main.txtShape_actionButtonSound": "声音按钮", "DE.Controllers.Main.txtShape_arc": "弧", "DE.Controllers.Main.txtShape_bentArrow": "圆角右箭头", "DE.Controllers.Main.txtShape_bentConnector5": "肘形连接符", "DE.Controllers.Main.txtShape_bentConnector5WithArrow": "肘形箭头连接符", "DE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "肘形双箭头连接符", "DE.Controllers.Main.txtShape_bentUpArrow": "直角上箭头", "DE.Controllers.Main.txtShape_bevel": "斜角", "DE.Controllers.Main.txtShape_blockArc": "空心弧", "DE.Controllers.Main.txtShape_borderCallout1": "线形标注1", "DE.Controllers.Main.txtShape_borderCallout2": "线形标注2", "DE.Controllers.Main.txtShape_borderCallout3": "线形标注3", "DE.Controllers.Main.txtShape_bracePair": "双大括号", "DE.Controllers.Main.txtShape_callout1": "线形标注1（无边框）", "DE.Controllers.Main.txtShape_callout2": "线形标注2（无边框）", "DE.Controllers.Main.txtShape_callout3": "线形标注3（无边框）", "DE.Controllers.Main.txtShape_can": "圆柱", "DE.Controllers.Main.txtShape_chevron": "V形", "DE.Controllers.Main.txtShape_chord": "弦形", "DE.Controllers.Main.txtShape_circularArrow": "环形箭头", "DE.Controllers.Main.txtShape_cloud": "云形", "DE.Controllers.Main.txtShape_cloudCallout": "云形标注", "DE.Controllers.Main.txtShape_corner": "角", "DE.Controllers.Main.txtShape_cube": "正方体", "DE.Controllers.Main.txtShape_curvedConnector3": "曲线连接符", "DE.Controllers.Main.txtShape_curvedConnector3WithArrow": "曲线箭头连接符", "DE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "曲线双箭头连接符", "DE.Controllers.Main.txtShape_curvedDownArrow": "下弧形箭头", "DE.Controllers.Main.txtShape_curvedLeftArrow": "左弧形箭头", "DE.Controllers.Main.txtShape_curvedRightArrow": "右弧形箭头", "DE.Controllers.Main.txtShape_curvedUpArrow": "上弧形箭头", "DE.Controllers.Main.txtShape_decagon": "十边形", "DE.Controllers.Main.txtShape_diagStripe": "斜纹", "DE.Controllers.Main.txtShape_diamond": "菱形", "DE.Controllers.Main.txtShape_dodecagon": "十二边形", "DE.Controllers.Main.txtShape_donut": "环形", "DE.Controllers.Main.txtShape_doubleWave": "双波形", "DE.Controllers.Main.txtShape_downArrow": "下箭头", "DE.Controllers.Main.txtShape_downArrowCallout": "下箭头标注", "DE.Controllers.Main.txtShape_ellipse": "椭圆", "DE.Controllers.Main.txtShape_ellipseRibbon": "下凸弯带形", "DE.Controllers.Main.txtShape_ellipseRibbon2": "上凸弯带形", "DE.Controllers.Main.txtShape_flowChartAlternateProcess": "流程图：可选流程", "DE.Controllers.Main.txtShape_flowChartCollate": "流程图：对照", "DE.Controllers.Main.txtShape_flowChartConnector": "流程图：接点", "DE.Controllers.Main.txtShape_flowChartDecision": "流程图：决策", "DE.Controllers.Main.txtShape_flowChartDelay": "流程图：延期", "DE.Controllers.Main.txtShape_flowChartDisplay": "流程图：展示", "DE.Controllers.Main.txtShape_flowChartDocument": "流程图：文档", "DE.Controllers.Main.txtShape_flowChartExtract": "流程图：摘录", "DE.Controllers.Main.txtShape_flowChartInputOutput": "流程图：数据", "DE.Controllers.Main.txtShape_flowChartInternalStorage": "流程图：内部贮存", "DE.Controllers.Main.txtShape_flowChartMagneticDisk": "流程图：磁盘", "DE.Controllers.Main.txtShape_flowChartMagneticDrum": "流程图：直接访问存储器", "DE.Controllers.Main.txtShape_flowChartMagneticTape": "流程图：顺序访问存储器", "DE.Controllers.Main.txtShape_flowChartManualInput": "流程图：手动输入", "DE.Controllers.Main.txtShape_flowChartManualOperation": "流程图：手动操作", "DE.Controllers.Main.txtShape_flowChartMerge": "流程图：合并", "DE.Controllers.Main.txtShape_flowChartMultidocument": "流程图：多文档 ", "DE.Controllers.Main.txtShape_flowChartOffpageConnector": "流程图：离页连接符", "DE.Controllers.Main.txtShape_flowChartOnlineStorage": "流程图：存储数据", "DE.Controllers.Main.txtShape_flowChartOr": "流程图：或者", "DE.Controllers.Main.txtShape_flowChartPredefinedProcess": "流程图：预定义过程", "DE.Controllers.Main.txtShape_flowChartPreparation": "流程图：准备", "DE.Controllers.Main.txtShape_flowChartProcess": "流程图：过程", "DE.Controllers.Main.txtShape_flowChartPunchedCard": "流程图：卡片", "DE.Controllers.Main.txtShape_flowChartPunchedTape": "流程图：资料带", "DE.Controllers.Main.txtShape_flowChartSort": "流程图：分类", "DE.Controllers.Main.txtShape_flowChartSummingJunction": "流程图：汇总连接", "DE.Controllers.Main.txtShape_flowChartTerminator": "流程图：终止", "DE.Controllers.Main.txtShape_foldedCorner": "折角形", "DE.Controllers.Main.txtShape_frame": "框架", "DE.Controllers.Main.txtShape_halfFrame": "半闭框", "DE.Controllers.Main.txtShape_heart": "心形", "DE.Controllers.Main.txtShape_heptagon": "七边形", "DE.Controllers.Main.txtShape_hexagon": "六边形", "DE.Controllers.Main.txtShape_homePlate": "五边形", "DE.Controllers.Main.txtShape_horizontalScroll": "横卷形", "DE.Controllers.Main.txtShape_irregularSeal1": "爆炸形1", "DE.Controllers.Main.txtShape_irregularSeal2": "爆炸形2", "DE.Controllers.Main.txtShape_leftArrow": "左箭头", "DE.Controllers.Main.txtShape_leftArrowCallout": "左箭头标注", "DE.Controllers.Main.txtShape_leftBrace": "左括号", "DE.Controllers.Main.txtShape_leftBracket": "左中括号", "DE.Controllers.Main.txtShape_leftRightArrow": "左右箭头", "DE.Controllers.Main.txtShape_leftRightArrowCallout": "左右箭头标注", "DE.Controllers.Main.txtShape_leftRightUpArrow": "丁字箭头", "DE.Controllers.Main.txtShape_leftUpArrow": "左上箭头", "DE.Controllers.Main.txtShape_lightningBolt": "闪电形", "DE.Controllers.Main.txtShape_line": "线", "DE.Controllers.Main.txtShape_lineWithArrow": "箭头", "DE.Controllers.Main.txtShape_lineWithTwoArrows": "双箭头", "DE.Controllers.Main.txtShape_mathDivide": "除", "DE.Controllers.Main.txtShape_mathEqual": "平等", "DE.Controllers.Main.txtShape_mathMinus": "减去", "DE.Controllers.Main.txtShape_mathMultiply": "乘", "DE.Controllers.Main.txtShape_mathNotEqual": "不等于", "DE.Controllers.Main.txtShape_mathPlus": "加", "DE.Controllers.Main.txtShape_moon": "月形", "DE.Controllers.Main.txtShape_noSmoking": "“NO”符号", "DE.Controllers.Main.txtShape_notchedRightArrow": "燕尾形箭头", "DE.Controllers.Main.txtShape_octagon": "八边形", "DE.Controllers.Main.txtShape_parallelogram": "平行四边形", "DE.Controllers.Main.txtShape_pentagon": "五边形", "DE.Controllers.Main.txtShape_pie": "派", "DE.Controllers.Main.txtShape_plaque": "符号", "DE.Controllers.Main.txtShape_plus": "加", "DE.Controllers.Main.txtShape_polyline1": "自由曲线", "DE.Controllers.Main.txtShape_polyline2": "任意多边形", "DE.Controllers.Main.txtShape_quadArrow": "十字箭头", "DE.Controllers.Main.txtShape_quadArrowCallout": "十字箭头标注", "DE.Controllers.Main.txtShape_rect": "矩形", "DE.Controllers.Main.txtShape_ribbon": "下凸带形", "DE.Controllers.Main.txtShape_ribbon2": "上凸带形", "DE.Controllers.Main.txtShape_rightArrow": "右箭头", "DE.Controllers.Main.txtShape_rightArrowCallout": "右箭头标注", "DE.Controllers.Main.txtShape_rightBrace": "右括号", "DE.Controllers.Main.txtShape_rightBracket": "右中括号", "DE.Controllers.Main.txtShape_round1Rect": "单圆角矩形", "DE.Controllers.Main.txtShape_round2DiagRect": "对角圆角矩形", "DE.Controllers.Main.txtShape_round2SameRect": "同侧圆角矩形", "DE.Controllers.Main.txtShape_roundRect": "圆角矩形", "DE.Controllers.Main.txtShape_rtTriangle": "直角三角形", "DE.Controllers.Main.txtShape_smileyFace": "笑脸", "DE.Controllers.Main.txtShape_snip1Rect": "剪去单角的矩形", "DE.Controllers.Main.txtShape_snip2DiagRect": "剪去对角的矩形", "DE.Controllers.Main.txtShape_snip2SameRect": "剪去同侧角的矩形", "DE.Controllers.Main.txtShape_snipRoundRect": "剪去单圆角的矩形", "DE.Controllers.Main.txtShape_spline": "曲线", "DE.Controllers.Main.txtShape_star10": "十角星", "DE.Controllers.Main.txtShape_star12": "十二角星", "DE.Controllers.Main.txtShape_star16": "十六角星", "DE.Controllers.Main.txtShape_star24": "二十四角星", "DE.Controllers.Main.txtShape_star32": "三十二角星", "DE.Controllers.Main.txtShape_star4": "四角星", "DE.Controllers.Main.txtShape_star5": "五角星", "DE.Controllers.Main.txtShape_star6": "六角星", "DE.Controllers.Main.txtShape_star7": "七角星", "DE.Controllers.Main.txtShape_star8": "八角星", "DE.Controllers.Main.txtShape_stripedRightArrow": "虚尾箭头", "DE.Controllers.Main.txtShape_sun": "太阳形", "DE.Controllers.Main.txtShape_teardrop": "泪珠形", "DE.Controllers.Main.txtShape_textRect": "文本框", "DE.Controllers.Main.txtShape_trapezoid": "梯形", "DE.Controllers.Main.txtShape_triangle": "三角形", "DE.Controllers.Main.txtShape_upArrow": "上箭头", "DE.Controllers.Main.txtShape_upArrowCallout": "上箭头标注", "DE.Controllers.Main.txtShape_upDownArrow": "上下箭头", "DE.Controllers.Main.txtShape_uturnArrow": "手杖形箭头", "DE.Controllers.Main.txtShape_verticalScroll": "竖卷形", "DE.Controllers.Main.txtShape_wave": "波浪线", "DE.Controllers.Main.txtShape_wedgeEllipseCallout": "椭圆形标注", "DE.Controllers.Main.txtShape_wedgeRectCallout": "矩形标注", "DE.Controllers.Main.txtShape_wedgeRoundRectCallout": "圆角矩形标注", "DE.Controllers.Main.txtStarsRibbons": "星星和旗帜", "DE.Controllers.Main.txtStyle_Caption": "标题", "DE.Controllers.Main.txtStyle_endnote_text": "结束处文本", "DE.Controllers.Main.txtStyle_footnote_text": "脚注文本", "DE.Controllers.Main.txtStyle_Heading_1": "标题1", "DE.Controllers.Main.txtStyle_Heading_2": "标题2", "DE.Controllers.Main.txtStyle_Heading_3": "标题3", "DE.Controllers.Main.txtStyle_Heading_4": "标题4", "DE.Controllers.Main.txtStyle_Heading_5": "标题5", "DE.Controllers.Main.txtStyle_Heading_6": "标题6", "DE.Controllers.Main.txtStyle_Heading_7": "标题7", "DE.Controllers.Main.txtStyle_Heading_8": "标题8", "DE.Controllers.Main.txtStyle_Heading_9": "标题9", "DE.Controllers.Main.txtStyle_Intense_Quote": "直接引用", "DE.Controllers.Main.txtStyle_List_Paragraph": "列表段落", "DE.Controllers.Main.txtStyle_No_Spacing": "无空格", "DE.Controllers.Main.txtStyle_Normal": "常规", "DE.Controllers.Main.txtStyle_Quote": "引用", "DE.Controllers.Main.txtStyle_Subtitle": "副标题", "DE.Controllers.Main.txtStyle_Title": "标题", "DE.Controllers.Main.txtSyntaxError": "语法错误", "DE.Controllers.Main.txtTableInd": "表格索引不能为零", "DE.Controllers.Main.txtTableOfContents": "目录", "DE.Controllers.Main.txtTableOfFigures": "图表目录", "DE.Controllers.Main.txtTOCHeading": "目录标题", "DE.Controllers.Main.txtTooLarge": "数字太大，无法设定格式", "DE.Controllers.Main.txtTypeEquation": "在此这里输入公式。", "DE.Controllers.Main.txtUndefBookmark": "未定义书签", "DE.Controllers.Main.txtXAxis": "X轴", "DE.Controllers.Main.txtYAxis": "Y轴", "DE.Controllers.Main.txtZeroDivide": "除数为零", "DE.Controllers.Main.unknownErrorText": "未知错误", "DE.Controllers.Main.unsupportedBrowserErrorText": "你的浏览器不支持", "DE.Controllers.Main.uploadDocExtMessage": "未知的文档格式", "DE.Controllers.Main.uploadDocFileCountMessage": "没有文档上载了", "DE.Controllers.Main.uploadDocSizeMessage": "超过最大文档大小限制。", "DE.Controllers.Main.uploadImageExtMessage": "未知图像格式", "DE.Controllers.Main.uploadImageFileCountMessage": "没有图片上传", "DE.Controllers.Main.uploadImageSizeMessage": "图像太大。最大的大小为25MB。", "DE.Controllers.Main.uploadImageTextText": "图片上传中...", "DE.Controllers.Main.uploadImageTitleText": "图片上传中", "DE.Controllers.Main.waitText": "请稍候...", "DE.Controllers.Main.warnBrowserIE9": "该应用程序在IE9上的功能很差。使用IE10或更高版本", "DE.Controllers.Main.warnBrowserZoom": "您的浏览器当前缩放设置不完全支持。请按Ctrl + 0重设为默认缩放。", "DE.Controllers.Main.warnLicenseExceeded": "您已达到同时连接%1编辑器的限制。该文档将被打开仅供查看。<br>请联系您的管理员以了解更多。", "DE.Controllers.Main.warnLicenseExp": "您的许可证已过期。<br>请更新您的许可证并刷新页面。", "DE.Controllers.Main.warnLicenseLimitedNoAccess": "授权过期<br>您不具备文件编辑功能的授权<br>请联系管理员。", "DE.Controllers.Main.warnLicenseLimitedRenewed": "授权需更新<br>您只有文件编辑功能的部分权限<br>请联系管理员以取得完整权限。", "DE.Controllers.Main.warnLicenseUsersExceeded": "您已达到%1编辑器的用户限制。请联系您的管理员以了解更多。", "DE.Controllers.Main.warnNoLicense": "您已达到同时连接%1编辑器的限制。该文档将被打开仅供查看。<br>请联系%1销售团队以了解个人升级条款。", "DE.Controllers.Main.warnNoLicenseUsers": "您已达到%1编辑器的用户限制。请联系%1销售团队以了解个人升级条款。", "DE.Controllers.Main.warnProcessRightsChange": "您被拒绝编辑文件的权限。", "DE.Controllers.Navigation.txtBeginning": "文档开头", "DE.Controllers.Navigation.txtGotoBeginning": "转到文档开头", "DE.Controllers.Print.textMarginsLast": "上次的自定义", "DE.Controllers.Print.txtCustom": "自定义", "DE.Controllers.Print.txtPrintRangeInvalid": "打印范围无效", "DE.Controllers.Print.txtPrintRangeSingleRange": "输入单个页码或单个页面范围（例如，5-12）。或者您可以打印为 PDF。", "DE.Controllers.Search.notcriticalErrorTitle": "警告", "DE.Controllers.Search.textNoTextFound": "您搜索的数据无法找到。请调整您的搜索选项。", "DE.Controllers.Search.textReplaceSkipped": "替换已完成。 {0}个符合结果被跳过。", "DE.Controllers.Search.textReplaceSuccess": "搜寻已完成。 {0}个符合结果已被取代", "DE.Controllers.Search.warnReplaceString": "{0}不是有效的规范", "DE.Controllers.Statusbar.textDisconnect": "<b>连接失败</b><br>正在尝试连接。请检查连接设置。", "DE.Controllers.Statusbar.textHasChanges": "已经跟踪了新的变化", "DE.Controllers.Statusbar.textSetTrackChanges": "你现在处于审阅模式。", "DE.Controllers.Statusbar.textTrackChanges": "打开文档，并启用“跟踪更改”模式", "DE.Controllers.Statusbar.tipReview": "跟踪变化", "DE.Controllers.Statusbar.zoomText": "缩放{0}%", "DE.Controllers.Toolbar.confirmAddFontName": "您要保存的字体在当前设备上不可用。<br>使用其中一种系统字体显示文本样式，当可用时将使用保存的字体。<br>是否要继续？", "DE.Controllers.Toolbar.dataUrl": "粘贴URL数据", "DE.Controllers.Toolbar.notcriticalErrorTitle": "警告", "DE.Controllers.Toolbar.textAccent": "导数符号", "DE.Controllers.Toolbar.textBracket": "括号", "DE.Controllers.Toolbar.textEmptyImgUrl": "您需要指定图像URL。", "DE.Controllers.Toolbar.textEmptyMMergeUrl": "你需要指明 URL。", "DE.Controllers.Toolbar.textFontSizeErr": "输入的值不正确。<br>请输入1到300之间的数值", "DE.Controllers.Toolbar.textFraction": "分数", "DE.Controllers.Toolbar.textFunction": "三角函数", "DE.Controllers.Toolbar.textGroup": "群", "DE.Controllers.Toolbar.textInsert": "插入", "DE.Controllers.Toolbar.textIntegral": "积分", "DE.Controllers.Toolbar.textLargeOperator": "大型运算符", "DE.Controllers.Toolbar.textLimitAndLog": "极限和对数", "DE.Controllers.Toolbar.textMatrix": "矩阵", "DE.Controllers.Toolbar.textOperator": "运算符", "DE.Controllers.Toolbar.textRadical": "根式", "DE.Controllers.Toolbar.textRecentlyUsed": "最近使用的", "DE.Controllers.Toolbar.textScript": "上下标", "DE.Controllers.Toolbar.textSymbols": "符号", "DE.Controllers.Toolbar.textTabForms": "表单", "DE.Controllers.Toolbar.textWarning": "警告", "DE.Controllers.Toolbar.txtAccent_Accent": "急性", "DE.Controllers.Toolbar.txtAccent_ArrowD": "右上方的箭头在上方", "DE.Controllers.Toolbar.txtAccent_ArrowL": "向左箭头", "DE.Controllers.Toolbar.txtAccent_ArrowR": "向右箭头上方", "DE.Controllers.Toolbar.txtAccent_Bar": "条", "DE.Controllers.Toolbar.txtAccent_BarBot": "下划线", "DE.Controllers.Toolbar.txtAccent_BarTop": "划线", "DE.Controllers.Toolbar.txtAccent_BorderBox": "盒装配方（带占位符)", "DE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "盒装配方（示例）", "DE.Controllers.Toolbar.txtAccent_Check": "检查", "DE.Controllers.Toolbar.txtAccent_CurveBracketBot": "下方大括号", "DE.Controllers.Toolbar.txtAccent_CurveBracketTop": "上方大括号", "DE.Controllers.Toolbar.txtAccent_Custom_1": "A的矢量", "DE.Controllers.Toolbar.txtAccent_Custom_2": "带顶线的ABC", "DE.Controllers.Toolbar.txtAccent_Custom_3": "带顶线的x XOR y", "DE.Controllers.Toolbar.txtAccent_DDDot": "三点", "DE.Controllers.Toolbar.txtAccent_DDot": "双点", "DE.Controllers.Toolbar.txtAccent_Dot": "点", "DE.Controllers.Toolbar.txtAccent_DoubleBar": "双顶线", "DE.Controllers.Toolbar.txtAccent_Grave": "抑音符号", "DE.Controllers.Toolbar.txtAccent_GroupBot": "分组字符下面", "DE.Controllers.Toolbar.txtAccent_GroupTop": "分组字符以上", "DE.Controllers.Toolbar.txtAccent_HarpoonL": "向左鱼叉以上", "DE.Controllers.Toolbar.txtAccent_HarpoonR": "向右上方的鱼叉", "DE.Controllers.Toolbar.txtAccent_Hat": "帽状", "DE.Controllers.Toolbar.txtAccent_Smile": "短音符", "DE.Controllers.Toolbar.txtAccent_Tilde": "波浪号", "DE.Controllers.Toolbar.txtBracket_Angle": "括号", "DE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "带分隔符的方括号", "DE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "带分隔符的方括号", "DE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "单括号", "DE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "单括号", "DE.Controllers.Toolbar.txtBracket_Curve": "括号", "DE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "带分隔符的方括号", "DE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "单括号", "DE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "单括号", "DE.Controllers.Toolbar.txtBracket_Custom_1": "案件（两个条件）", "DE.Controllers.Toolbar.txtBracket_Custom_2": "案件（三个条件）", "DE.Controllers.Toolbar.txtBracket_Custom_3": "堆栈对象", "DE.Controllers.Toolbar.txtBracket_Custom_4": "堆栈对象", "DE.Controllers.Toolbar.txtBracket_Custom_5": "案例示例", "DE.Controllers.Toolbar.txtBracket_Custom_6": "二项系数", "DE.Controllers.Toolbar.txtBracket_Custom_7": "二项系数", "DE.Controllers.Toolbar.txtBracket_Line": "括号", "DE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "单括号", "DE.Controllers.Toolbar.txtBracket_Line_OpenNone": "单括号", "DE.Controllers.Toolbar.txtBracket_LineDouble": "括号", "DE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "单括号", "DE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "单括号", "DE.Controllers.Toolbar.txtBracket_LowLim": "括号", "DE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "单括号", "DE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "单括号", "DE.Controllers.Toolbar.txtBracket_Round": "括号", "DE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "带分隔符的方括号", "DE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "单括号", "DE.Controllers.Toolbar.txtBracket_Round_OpenNone": "单括号", "DE.Controllers.Toolbar.txtBracket_Square": "括号", "DE.Controllers.Toolbar.txtBracket_Square_CloseClose": "括号", "DE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "括号", "DE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "单括号", "DE.Controllers.Toolbar.txtBracket_Square_OpenNone": "单括号", "DE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "括号", "DE.Controllers.Toolbar.txtBracket_SquareDouble": "括号", "DE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "单括号", "DE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "单括号", "DE.Controllers.Toolbar.txtBracket_UppLim": "括号", "DE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "单括号", "DE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "单括号", "DE.Controllers.Toolbar.txtFractionDiagonal": "倾斜分数", "DE.Controllers.Toolbar.txtFractionDifferential_1": "微分", "DE.Controllers.Toolbar.txtFractionDifferential_2": "微分", "DE.Controllers.Toolbar.txtFractionDifferential_3": "微分", "DE.Controllers.Toolbar.txtFractionDifferential_4": "微分", "DE.Controllers.Toolbar.txtFractionHorizontal": "线性分数", "DE.Controllers.Toolbar.txtFractionPi_2": "π/ 2", "DE.Controllers.Toolbar.txtFractionSmall": "小分数", "DE.Controllers.Toolbar.txtFractionVertical": "堆积分数", "DE.Controllers.Toolbar.txtFunction_1_Cos": "反余弦", "DE.Controllers.Toolbar.txtFunction_1_Cosh": "双曲反余弦函数", "DE.Controllers.Toolbar.txtFunction_1_Cot": "反余切", "DE.Controllers.Toolbar.txtFunction_1_Coth": "双曲反正弦函数", "DE.Controllers.Toolbar.txtFunction_1_Csc": "反余割", "DE.Controllers.Toolbar.txtFunction_1_Csch": "双曲逆乘功能", "DE.Controllers.Toolbar.txtFunction_1_Sec": "反正割", "DE.Controllers.Toolbar.txtFunction_1_Sech": "双曲线逆分割函数", "DE.Controllers.Toolbar.txtFunction_1_Sin": "反正弦", "DE.Controllers.Toolbar.txtFunction_1_Sinh": "双曲反正弦函数", "DE.Controllers.Toolbar.txtFunction_1_Tan": "反切线", "DE.Controllers.Toolbar.txtFunction_1_Tanh": "双曲反正切函数", "DE.Controllers.Toolbar.txtFunction_Cos": "余弦", "DE.Controllers.Toolbar.txtFunction_Cosh": "双曲余弦函数", "DE.Controllers.Toolbar.txtFunction_Cot": "余切", "DE.Controllers.Toolbar.txtFunction_Coth": "双曲正交函数", "DE.Controllers.Toolbar.txtFunction_Csc": "余割", "DE.Controllers.Toolbar.txtFunction_Csch": "双曲余弦函数", "DE.Controllers.Toolbar.txtFunction_Custom_1": "正弦θ", "DE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "DE.Controllers.Toolbar.txtFunction_Custom_3": "切线公式", "DE.Controllers.Toolbar.txtFunction_Sec": "正割", "DE.Controllers.Toolbar.txtFunction_Sech": "双曲正割函数", "DE.Controllers.Toolbar.txtFunction_Sin": "正弦", "DE.Controllers.Toolbar.txtFunction_Sinh": "双曲正弦函数", "DE.Controllers.Toolbar.txtFunction_Tan": "切线", "DE.Controllers.Toolbar.txtFunction_Tanh": "双曲正切函数", "DE.Controllers.Toolbar.txtIntegral": "积分", "DE.Controllers.Toolbar.txtIntegral_dtheta": "差分θ", "DE.Controllers.Toolbar.txtIntegral_dx": "差分x", "DE.Controllers.Toolbar.txtIntegral_dy": "差分y", "DE.Controllers.Toolbar.txtIntegralCenterSubSup": "积分", "DE.Controllers.Toolbar.txtIntegralDouble": "双积分", "DE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "双积分", "DE.Controllers.Toolbar.txtIntegralDoubleSubSup": "双积分", "DE.Controllers.Toolbar.txtIntegralOriented": "轮廓积分", "DE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "轮廓积分", "DE.Controllers.Toolbar.txtIntegralOrientedDouble": "曲面积分", "DE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "曲面积分", "DE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "曲面积分", "DE.Controllers.Toolbar.txtIntegralOrientedSubSup": "轮廓积分", "DE.Controllers.Toolbar.txtIntegralOrientedTriple": "体积积分", "DE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "体积积分", "DE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "体积积分", "DE.Controllers.Toolbar.txtIntegralSubSup": "积分", "DE.Controllers.Toolbar.txtIntegralTriple": "三重积分", "DE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "三重积分", "DE.Controllers.Toolbar.txtIntegralTripleSubSup": "三重积分", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction": "楔", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "楔", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "楔", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "楔", "DE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "楔", "DE.Controllers.Toolbar.txtLargeOperator_CoProd": "副积", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "副积", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "副积", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "副积", "DE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "副积", "DE.Controllers.Toolbar.txtLargeOperator_Custom_1": "合计", "DE.Controllers.Toolbar.txtLargeOperator_Custom_2": "合计", "DE.Controllers.Toolbar.txtLargeOperator_Custom_3": "合计", "DE.Controllers.Toolbar.txtLargeOperator_Custom_4": "乘积", "DE.Controllers.Toolbar.txtLargeOperator_Custom_5": "并集", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction": "三角", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "三角", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "三角", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "三角", "DE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "三角", "DE.Controllers.Toolbar.txtLargeOperator_Intersection": "交集", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "交集", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "交集", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "交集", "DE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "交集", "DE.Controllers.Toolbar.txtLargeOperator_Prod": "乘积", "DE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "乘积", "DE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "乘积", "DE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "乘积", "DE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "乘积", "DE.Controllers.Toolbar.txtLargeOperator_Sum": "合计", "DE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "合计", "DE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "合计", "DE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "合计", "DE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "合计", "DE.Controllers.Toolbar.txtLargeOperator_Union": "并集", "DE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "并集", "DE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "并集", "DE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "并集", "DE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "并集", "DE.Controllers.Toolbar.txtLimitLog_Custom_1": "极限示例", "DE.Controllers.Toolbar.txtLimitLog_Custom_2": "最大范例", "DE.Controllers.Toolbar.txtLimitLog_Lim": "极限", "DE.Controllers.Toolbar.txtLimitLog_Ln": "自然对数", "DE.Controllers.Toolbar.txtLimitLog_Log": "对数", "DE.Controllers.Toolbar.txtLimitLog_LogBase": "对数", "DE.Controllers.Toolbar.txtLimitLog_Max": "最大值", "DE.Controllers.Toolbar.txtLimitLog_Min": "最小值", "DE.Controllers.Toolbar.txtMarginsH": "顶部和底部边距对于给定的页面高度来说太高", "DE.Controllers.Toolbar.txtMarginsW": "对于给定的页面宽度，左右边距太宽", "DE.Controllers.Toolbar.txtMatrix_1_2": "1x2空矩阵", "DE.Controllers.Toolbar.txtMatrix_1_3": "1x3空矩阵", "DE.Controllers.Toolbar.txtMatrix_2_1": "2x1空矩阵", "DE.Controllers.Toolbar.txtMatrix_2_2": "2x2空矩阵", "DE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "空矩阵与支架", "DE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "空矩阵与支架", "DE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "空矩阵与支架", "DE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "空矩阵与支架", "DE.Controllers.Toolbar.txtMatrix_2_3": "2x3空矩阵", "DE.Controllers.Toolbar.txtMatrix_3_1": "3x1空矩阵", "DE.Controllers.Toolbar.txtMatrix_3_2": "3x2空矩阵", "DE.Controllers.Toolbar.txtMatrix_3_3": "3x3空矩阵", "DE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "基线点", "DE.Controllers.Toolbar.txtMatrix_Dots_Center": "中线点", "DE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "对角点", "DE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "垂直点", "DE.Controllers.Toolbar.txtMatrix_Flat_Round": "稀疏矩阵", "DE.Controllers.Toolbar.txtMatrix_Flat_Square": "稀疏矩阵", "DE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2个性矩阵", "DE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "3x3个性矩阵", "DE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3个性矩阵", "DE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3个性矩阵", "DE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "右左箭头下面", "DE.Controllers.Toolbar.txtOperator_ArrowD_Top": "右上方的箭头在上方", "DE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "向左箭头", "DE.Controllers.Toolbar.txtOperator_ArrowL_Top": "向左箭头", "DE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "下面箭头向右", "DE.Controllers.Toolbar.txtOperator_ArrowR_Top": "向右箭头上方", "DE.Controllers.Toolbar.txtOperator_ColonEquals": "冒号相等", "DE.Controllers.Toolbar.txtOperator_Custom_1": "统一", "DE.Controllers.Toolbar.txtOperator_Custom_2": "三角形区域", "DE.Controllers.Toolbar.txtOperator_Definition": "相等于定义", "DE.Controllers.Toolbar.txtOperator_DeltaEquals": "数据相当于", "DE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "右左箭头下面", "DE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "右上方的箭头在上方", "DE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "向左箭头", "DE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "向左箭头", "DE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "下面箭头向右", "DE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "向右箭头上方", "DE.Controllers.Toolbar.txtOperator_EqualsEquals": "等于等于", "DE.Controllers.Toolbar.txtOperator_MinusEquals": "减号相等", "DE.Controllers.Toolbar.txtOperator_PlusEquals": "加上等于", "DE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "被测量", "DE.Controllers.Toolbar.txtRadicalCustom_1": "部首", "DE.Controllers.Toolbar.txtRadicalCustom_2": "部首", "DE.Controllers.Toolbar.txtRadicalRoot_2": "平方根", "DE.Controllers.Toolbar.txtRadicalRoot_3": "立方根", "DE.Controllers.Toolbar.txtRadicalRoot_n": "带有次数的根式", "DE.Controllers.Toolbar.txtRadicalSqrt": "平方根", "DE.Controllers.Toolbar.txtScriptCustom_1": "脚本", "DE.Controllers.Toolbar.txtScriptCustom_2": "脚本", "DE.Controllers.Toolbar.txtScriptCustom_3": "脚本", "DE.Controllers.Toolbar.txtScriptCustom_4": "脚本", "DE.Controllers.Toolbar.txtScriptSub": "下标", "DE.Controllers.Toolbar.txtScriptSubSup": "下标", "DE.Controllers.Toolbar.txtScriptSubSupLeft": "左标-标注", "DE.Controllers.Toolbar.txtScriptSup": "上标", "DE.Controllers.Toolbar.txtSymbol_about": "大约", "DE.Controllers.Toolbar.txtSymbol_additional": "补集", "DE.Controllers.Toolbar.txtSymbol_aleph": "阿列夫", "DE.Controllers.Toolbar.txtSymbol_alpha": "Alpha", "DE.Controllers.Toolbar.txtSymbol_approx": "几乎相等", "DE.Controllers.Toolbar.txtSymbol_ast": "星号运算符", "DE.Controllers.Toolbar.txtSymbol_beta": "测试版", "DE.Controllers.Toolbar.txtSymbol_beth": "打赌", "DE.Controllers.Toolbar.txtSymbol_bullet": "加重号运算符", "DE.Controllers.Toolbar.txtSymbol_cap": "交集", "DE.Controllers.Toolbar.txtSymbol_cbrt": "立方根", "DE.Controllers.Toolbar.txtSymbol_cdots": "中线水平省略号", "DE.Controllers.Toolbar.txtSymbol_celsius": "摄氏度", "DE.Controllers.Toolbar.txtSymbol_chi": "驰", "DE.Controllers.Toolbar.txtSymbol_cong": "大致相等", "DE.Controllers.Toolbar.txtSymbol_cup": "并集", "DE.Controllers.Toolbar.txtSymbol_ddots": "向右对角线省略号", "DE.Controllers.Toolbar.txtSymbol_degree": "度", "DE.Controllers.Toolbar.txtSymbol_delta": "参加", "DE.Controllers.Toolbar.txtSymbol_div": "除号", "DE.Controllers.Toolbar.txtSymbol_downarrow": "向下箭头", "DE.Controllers.Toolbar.txtSymbol_emptyset": "空集", "DE.Controllers.Toolbar.txtSymbol_epsilon": "小量", "DE.Controllers.Toolbar.txtSymbol_equals": "平等", "DE.Controllers.Toolbar.txtSymbol_equiv": "相同", "DE.Controllers.Toolbar.txtSymbol_eta": "和", "DE.Controllers.Toolbar.txtSymbol_exists": "有存在", "DE.Controllers.Toolbar.txtSymbol_factorial": "阶乘", "DE.Controllers.Toolbar.txtSymbol_fahrenheit": "华氏度", "DE.Controllers.Toolbar.txtSymbol_forall": "全部", "DE.Controllers.Toolbar.txtSymbol_gamma": "伽马", "DE.Controllers.Toolbar.txtSymbol_geq": "大于或等于", "DE.Controllers.Toolbar.txtSymbol_gg": "远大于", "DE.Controllers.Toolbar.txtSymbol_greater": "大于", "DE.Controllers.Toolbar.txtSymbol_in": "元素", "DE.Controllers.Toolbar.txtSymbol_inc": "增量", "DE.Controllers.Toolbar.txtSymbol_infinity": "无限", "DE.Controllers.Toolbar.txtSymbol_iota": "Iota", "DE.Controllers.Toolbar.txtSymbol_kappa": "卡帕", "DE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "DE.Controllers.Toolbar.txtSymbol_leftarrow": "左箭头", "DE.Controllers.Toolbar.txtSymbol_leftrightarrow": "左右箭头", "DE.Controllers.Toolbar.txtSymbol_leq": "小于或等于", "DE.Controllers.Toolbar.txtSymbol_less": "小于", "DE.Controllers.Toolbar.txtSymbol_ll": "远小于", "DE.Controllers.Toolbar.txtSymbol_minus": "减去", "DE.Controllers.Toolbar.txtSymbol_mp": "减号加", "DE.Controllers.Toolbar.txtSymbol_mu": "亩", "DE.Controllers.Toolbar.txtSymbol_nabla": "微分算符", "DE.Controllers.Toolbar.txtSymbol_neq": "不等于", "DE.Controllers.Toolbar.txtSymbol_ni": "包含作为成员", "DE.Controllers.Toolbar.txtSymbol_not": "未签名", "DE.Controllers.Toolbar.txtSymbol_notexists": "没有存在", "DE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "DE.Controllers.Toolbar.txtSymbol_o": "Omicron", "DE.Controllers.Toolbar.txtSymbol_omega": "Omega", "DE.Controllers.Toolbar.txtSymbol_partial": "偏微分", "DE.Controllers.Toolbar.txtSymbol_percent": "百分比", "DE.Controllers.Toolbar.txtSymbol_phi": "Phi", "DE.Controllers.Toolbar.txtSymbol_pi": "Pi", "DE.Controllers.Toolbar.txtSymbol_plus": "加", "DE.Controllers.Toolbar.txtSymbol_pm": "加减", "DE.Controllers.Toolbar.txtSymbol_propto": "成比例", "DE.Controllers.Toolbar.txtSymbol_psi": "PSI", "DE.Controllers.Toolbar.txtSymbol_qdrt": "第四根", "DE.Controllers.Toolbar.txtSymbol_qed": "审校结束", "DE.Controllers.Toolbar.txtSymbol_rddots": "向右对角线省略号", "DE.Controllers.Toolbar.txtSymbol_rho": "Rho", "DE.Controllers.Toolbar.txtSymbol_rightarrow": "右箭头", "DE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "DE.Controllers.Toolbar.txtSymbol_sqrt": "根号", "DE.Controllers.Toolbar.txtSymbol_tau": "战斗", "DE.Controllers.Toolbar.txtSymbol_therefore": "因此", "DE.Controllers.Toolbar.txtSymbol_theta": "西塔", "DE.Controllers.Toolbar.txtSymbol_times": "乘法符号", "DE.Controllers.Toolbar.txtSymbol_uparrow": "向上箭头", "DE.Controllers.Toolbar.txtSymbol_upsilon": "Upsilon", "DE.Controllers.Toolbar.txtSymbol_varepsilon": "Epsilon变体", "DE.Controllers.Toolbar.txtSymbol_varphi": "Phi 变量", "DE.Controllers.Toolbar.txtSymbol_varpi": "π变量", "DE.Controllers.Toolbar.txtSymbol_varrho": "Rho 变量", "DE.Controllers.Toolbar.txtSymbol_varsigma": "Sigma变量", "DE.Controllers.Toolbar.txtSymbol_vartheta": "西塔", "DE.Controllers.Toolbar.txtSymbol_vdots": "垂直省略号", "DE.Controllers.Toolbar.txtSymbol_xsi": "粒子", "DE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "DE.Controllers.Viewport.textFitPage": "适合页面", "DE.Controllers.Viewport.textFitWidth": "适合宽度", "DE.Controllers.Viewport.txtDarkMode": "暗黑模式", "DE.Views.AddNewCaptionLabelDialog.textLabel": "标签：", "DE.Views.AddNewCaptionLabelDialog.textLabelError": "标签不能空", "DE.Views.BookmarksDialog.textAdd": "添加", "DE.Views.BookmarksDialog.textBookmarkName": "书签名称", "DE.Views.BookmarksDialog.textClose": "关闭", "DE.Views.BookmarksDialog.textCopy": "复制", "DE.Views.BookmarksDialog.textDelete": "删除", "DE.Views.BookmarksDialog.textGetLink": "获取链接", "DE.Views.BookmarksDialog.textGoto": "转到", "DE.Views.BookmarksDialog.textHidden": "隐藏的书签", "DE.Views.BookmarksDialog.textLocation": "位置", "DE.Views.BookmarksDialog.textName": "名称", "DE.Views.BookmarksDialog.textSort": "排序方式", "DE.Views.BookmarksDialog.textTitle": "书签", "DE.Views.BookmarksDialog.txtInvalidName": "书签名称只能包含字母、数字和下划线，并且应以字母开头", "DE.Views.CaptionDialog.textAdd": "添加标签", "DE.Views.CaptionDialog.textAfter": "段后", "DE.Views.CaptionDialog.textBefore": "段前", "DE.Views.CaptionDialog.textCaption": "标题", "DE.Views.CaptionDialog.textChapter": "章节以风格开始", "DE.Views.CaptionDialog.textChapterInc": "包括章节号", "DE.Views.CaptionDialog.textColon": "冒号", "DE.Views.CaptionDialog.textDash": "破折号", "DE.Views.CaptionDialog.textDelete": "删除标签", "DE.Views.CaptionDialog.textEquation": "方程", "DE.Views.CaptionDialog.textExamples": "列入：表 2-A，图片 1。IV", "DE.Views.CaptionDialog.textExclude": "从标题中排除标签", "DE.Views.CaptionDialog.textFigure": "图", "DE.Views.CaptionDialog.textHyphen": "连字符", "DE.Views.CaptionDialog.textInsert": "插入", "DE.Views.CaptionDialog.textLabel": "标签", "DE.Views.CaptionDialog.textLongDash": "长破折号", "DE.Views.CaptionDialog.textNumbering": "编号", "DE.Views.CaptionDialog.textPeriod": "时间段", "DE.Views.CaptionDialog.textSeparator": "使用分隔符", "DE.Views.CaptionDialog.textTable": "表格", "DE.Views.CaptionDialog.textTitle": "插入标题", "DE.Views.CellsAddDialog.textCol": "列", "DE.Views.CellsAddDialog.textDown": "光标下方", "DE.Views.CellsAddDialog.textLeft": "向左", "DE.Views.CellsAddDialog.textRight": "向右", "DE.Views.CellsAddDialog.textRow": "行", "DE.Views.CellsAddDialog.textTitle": "插入几个", "DE.Views.CellsAddDialog.textUp": "光标上方", "DE.Views.ChartSettings.text3dDepth": "深度(原始深度百分比)", "DE.Views.ChartSettings.text3dHeight": "高度(原始高度百分比)", "DE.Views.ChartSettings.text3dRotation": "三维旋转", "DE.Views.ChartSettings.textAdvanced": "显示高级设置", "DE.Views.ChartSettings.textAutoscale": "自动缩放", "DE.Views.ChartSettings.textChartType": "更改图表类型", "DE.Views.ChartSettings.textDefault": "默认旋转", "DE.Views.ChartSettings.textDown": "向下", "DE.Views.ChartSettings.textEditData": "编辑数据", "DE.Views.ChartSettings.textHeight": "高度", "DE.Views.ChartSettings.textLeft": "向左", "DE.Views.ChartSettings.textNarrow": "缩小视野", "DE.Views.ChartSettings.textOriginalSize": "实际大小", "DE.Views.ChartSettings.textPerspective": "透视", "DE.Views.ChartSettings.textRight": "向右", "DE.Views.ChartSettings.textRightAngle": "直角坐标轴", "DE.Views.ChartSettings.textSize": "大小", "DE.Views.ChartSettings.textStyle": "类型", "DE.Views.ChartSettings.textUndock": "离开面板", "DE.Views.ChartSettings.textUp": "向上", "DE.Views.ChartSettings.textWiden": "扩大视野", "DE.Views.ChartSettings.textWidth": "宽度", "DE.Views.ChartSettings.textWrap": "包裹风格", "DE.Views.ChartSettings.textX": "X 旋转", "DE.Views.ChartSettings.textY": "Y 旋转", "DE.Views.ChartSettings.txtBehind": "衬于​​文字下方", "DE.Views.ChartSettings.txtInFront": "浮于文字上方", "DE.Views.ChartSettings.txtInline": "嵌入型​​", "DE.Views.ChartSettings.txtSquare": "四周型环绕", "DE.Views.ChartSettings.txtThrough": "穿越型环绕", "DE.Views.ChartSettings.txtTight": "紧密型环绕", "DE.Views.ChartSettings.txtTitle": "图表", "DE.Views.ChartSettings.txtTopAndBottom": "上下型环绕", "DE.Views.ControlSettingsDialog.strGeneral": "常规", "DE.Views.ControlSettingsDialog.textAdd": "添加", "DE.Views.ControlSettingsDialog.textAppearance": "外观", "DE.Views.ControlSettingsDialog.textApplyAll": "全部应用", "DE.Views.ControlSettingsDialog.textBox": "边界框", "DE.Views.ControlSettingsDialog.textChange": "编辑", "DE.Views.ControlSettingsDialog.textCheckbox": "复选框", "DE.Views.ControlSettingsDialog.textChecked": "勾选符号", "DE.Views.ControlSettingsDialog.textColor": "颜色", "DE.Views.ControlSettingsDialog.textCombobox": "\n组合框", "DE.Views.ControlSettingsDialog.textDate": "日期格式", "DE.Views.ControlSettingsDialog.textDelete": "删除", "DE.Views.ControlSettingsDialog.textDisplayName": "显示名称", "DE.Views.ControlSettingsDialog.textDown": "下", "DE.Views.ControlSettingsDialog.textDropDown": "下拉列表", "DE.Views.ControlSettingsDialog.textFormat": "这样显示日期", "DE.Views.ControlSettingsDialog.textLang": "语言", "DE.Views.ControlSettingsDialog.textLock": "锁定", "DE.Views.ControlSettingsDialog.textName": "标题", "DE.Views.ControlSettingsDialog.textNone": "无", "DE.Views.ControlSettingsDialog.textPlaceholder": "占位符", "DE.Views.ControlSettingsDialog.textShowAs": "显示为", "DE.Views.ControlSettingsDialog.textSystemColor": "系统", "DE.Views.ControlSettingsDialog.textTag": "标签", "DE.Views.ControlSettingsDialog.textTitle": "内容控制设置", "DE.Views.ControlSettingsDialog.textUnchecked": "未选中的符号", "DE.Views.ControlSettingsDialog.textUp": "上", "DE.Views.ControlSettingsDialog.textValue": "值", "DE.Views.ControlSettingsDialog.tipChange": "更改符号", "DE.Views.ControlSettingsDialog.txtLockDelete": "无法删除内容控制", "DE.Views.ControlSettingsDialog.txtLockEdit": "无法编辑内容", "DE.Views.CrossReferenceDialog.textAboveBelow": "上/下", "DE.Views.CrossReferenceDialog.textBookmark": "书签", "DE.Views.CrossReferenceDialog.textBookmarkText": "书签文字", "DE.Views.CrossReferenceDialog.textCaption": "整个标题", "DE.Views.CrossReferenceDialog.textEmpty": "请求的引用为空。", "DE.Views.CrossReferenceDialog.textEndnote": "尾注", "DE.Views.CrossReferenceDialog.textEndNoteNum": "尾注编号", "DE.Views.CrossReferenceDialog.textEndNoteNumForm": "尾注编号（带格式）", "DE.Views.CrossReferenceDialog.textEquation": "方程式", "DE.Views.CrossReferenceDialog.textFigure": "图", "DE.Views.CrossReferenceDialog.textFootnote": "脚注", "DE.Views.CrossReferenceDialog.textHeading": "标题", "DE.Views.CrossReferenceDialog.textHeadingNum": "标题号", "DE.Views.CrossReferenceDialog.textHeadingNumFull": "标题号 (完整上下文)", "DE.Views.CrossReferenceDialog.textHeadingNumNo": "标题号 (无上下文)", "DE.Views.CrossReferenceDialog.textHeadingText": "标题文本", "DE.Views.CrossReferenceDialog.textIncludeAbove": "包括上方/下方", "DE.Views.CrossReferenceDialog.textInsert": "插入", "DE.Views.CrossReferenceDialog.textInsertAs": "插入超链接", "DE.Views.CrossReferenceDialog.textLabelNum": "仅标签与标号", "DE.Views.CrossReferenceDialog.textNoteNum": "脚注编号", "DE.Views.CrossReferenceDialog.textNoteNumForm": "脚注编号（带格式）", "DE.Views.CrossReferenceDialog.textOnlyCaption": "仅标注文本", "DE.Views.CrossReferenceDialog.textPageNum": "页码", "DE.Views.CrossReferenceDialog.textParagraph": "标号项目", "DE.Views.CrossReferenceDialog.textParaNum": "段落编号", "DE.Views.CrossReferenceDialog.textParaNumFull": "段落编号（完整上下文）", "DE.Views.CrossReferenceDialog.textParaNumNo": "段落编号（无上下文）", "DE.Views.CrossReferenceDialog.textSeparate": "数字分隔符", "DE.Views.CrossReferenceDialog.textTable": "表格", "DE.Views.CrossReferenceDialog.textText": "段落文字", "DE.Views.CrossReferenceDialog.textWhich": "对哪一个标注", "DE.Views.CrossReferenceDialog.textWhichBookmark": "对哪一个书签", "DE.Views.CrossReferenceDialog.textWhichEndnote": "对哪一个尾注", "DE.Views.CrossReferenceDialog.textWhichHeading": "对哪一个标题", "DE.Views.CrossReferenceDialog.textWhichNote": "对哪一个脚注", "DE.Views.CrossReferenceDialog.textWhichPara": "对哪一个标号项目", "DE.Views.CrossReferenceDialog.txtReference": "插入引用到", "DE.Views.CrossReferenceDialog.txtTitle": "交叉引用", "DE.Views.CrossReferenceDialog.txtType": "引用类型", "DE.Views.CustomColumnsDialog.textColumns": "列数", "DE.Views.CustomColumnsDialog.textSeparator": "列分隔符", "DE.Views.CustomColumnsDialog.textSpacing": "列之间的间距", "DE.Views.CustomColumnsDialog.textTitle": "栏", "DE.Views.DateTimeDialog.confirmDefault": "设置{0}：{1}的默认格式", "DE.Views.DateTimeDialog.textDefault": "设为默认", "DE.Views.DateTimeDialog.textFormat": "格式", "DE.Views.DateTimeDialog.textLang": "语言", "DE.Views.DateTimeDialog.textUpdate": "自动更新", "DE.Views.DateTimeDialog.txtTitle": "日期、时间", "DE.Views.DocProtection.hintProtectDoc": "保护文档", "DE.Views.DocProtection.txtDocProtectedComment": "文件是受保护的。<br>您仅能对这个文件添加评论。", "DE.Views.DocProtection.txtDocProtectedForms": "文件是受保护的。<br>您仅能填写本文件中的字段。", "DE.Views.DocProtection.txtDocProtectedTrack": "文件是受保护的。<br>您可以编辑这个文件，但所做的变化都会被跟踪。", "DE.Views.DocProtection.txtDocProtectedView": "文件是受保护的。<br>您仅能查看这个文件。", "DE.Views.DocProtection.txtDocUnlockDescription": "输入密码以解除文件的保护", "DE.Views.DocProtection.txtProtectDoc": "保护文档", "DE.Views.DocProtection.txtUnlockTitle": "解除文件保护", "DE.Views.DocumentHolder.aboveText": "以上", "DE.Views.DocumentHolder.addCommentText": "添加批注", "DE.Views.DocumentHolder.advancedDropCapText": "首字下沉设置", "DE.Views.DocumentHolder.advancedEquationText": "方程式设置", "DE.Views.DocumentHolder.advancedFrameText": "框架高级设置", "DE.Views.DocumentHolder.advancedParagraphText": "段落高级设置", "DE.Views.DocumentHolder.advancedTableText": "高级表设置", "DE.Views.DocumentHolder.advancedText": "高级设置", "DE.Views.DocumentHolder.alignmentText": "对齐", "DE.Views.DocumentHolder.allLinearText": "全部 - 线性", "DE.Views.DocumentHolder.allProfText": "全部 - 专业", "DE.Views.DocumentHolder.belowText": "下面", "DE.Views.DocumentHolder.breakBeforeText": "段前分页", "DE.Views.DocumentHolder.bulletsText": "符号和编号", "DE.Views.DocumentHolder.cellAlignText": "单元格垂直对齐", "DE.Views.DocumentHolder.cellText": "元件", "DE.Views.DocumentHolder.centerText": "中心", "DE.Views.DocumentHolder.chartText": "图表高级设置", "DE.Views.DocumentHolder.columnText": "列", "DE.Views.DocumentHolder.currLinearText": "当前 - 线性", "DE.Views.DocumentHolder.currProfText": "当前 - 专业", "DE.Views.DocumentHolder.deleteColumnText": "删除列", "DE.Views.DocumentHolder.deleteRowText": "删除行", "DE.Views.DocumentHolder.deleteTableText": "删除表", "DE.Views.DocumentHolder.deleteText": "删除", "DE.Views.DocumentHolder.direct270Text": "向上旋转文字", "DE.Views.DocumentHolder.direct90Text": "向下旋转文字", "DE.Views.DocumentHolder.directHText": "水平的", "DE.Views.DocumentHolder.directionText": "文字方向", "DE.Views.DocumentHolder.editChartText": "编辑数据", "DE.Views.DocumentHolder.editFooterText": "编辑页脚", "DE.Views.DocumentHolder.editHeaderText": "编辑页眉", "DE.Views.DocumentHolder.editHyperlinkText": "编辑超链接", "DE.Views.DocumentHolder.eqToInlineText": "改成内联", "DE.Views.DocumentHolder.guestText": "游客", "DE.Views.DocumentHolder.hyperlinkText": "超链接", "DE.Views.DocumentHolder.ignoreAllSpellText": "忽略所有", "DE.Views.DocumentHolder.ignoreSpellText": "忽视", "DE.Views.DocumentHolder.imageText": "高级图像设置", "DE.Views.DocumentHolder.insertColumnLeftText": "列左", "DE.Views.DocumentHolder.insertColumnRightText": "右列", "DE.Views.DocumentHolder.insertColumnText": "插入列", "DE.Views.DocumentHolder.insertRowAboveText": "上面的行", "DE.Views.DocumentHolder.insertRowBelowText": "下面的行", "DE.Views.DocumentHolder.insertRowText": "插入行", "DE.Views.DocumentHolder.insertText": "插入", "DE.Views.DocumentHolder.keepLinesText": "保持同一行", "DE.Views.DocumentHolder.langText": "选择语言", "DE.Views.DocumentHolder.latexText": "LaTeX", "DE.Views.DocumentHolder.leftText": "左", "DE.Views.DocumentHolder.loadSpellText": "加载变体...", "DE.Views.DocumentHolder.mergeCellsText": "合并单元格", "DE.Views.DocumentHolder.moreText": "更多变体...", "DE.Views.DocumentHolder.noSpellVariantsText": "没有变量", "DE.Views.DocumentHolder.notcriticalErrorTitle": "警告", "DE.Views.DocumentHolder.originalSizeText": "实际大小", "DE.Views.DocumentHolder.paragraphText": "段", "DE.Views.DocumentHolder.removeHyperlinkText": "删除超链接", "DE.Views.DocumentHolder.rightText": "右", "DE.Views.DocumentHolder.rowText": "行", "DE.Views.DocumentHolder.saveStyleText": "新建样式", "DE.Views.DocumentHolder.selectCellText": "选择单元格", "DE.Views.DocumentHolder.selectColumnText": "选择列", "DE.Views.DocumentHolder.selectRowText": "选择行", "DE.Views.DocumentHolder.selectTableText": "选择表", "DE.Views.DocumentHolder.selectText": "选择", "DE.Views.DocumentHolder.shapeText": "形状高级设置", "DE.Views.DocumentHolder.spellcheckText": "拼写检查", "DE.Views.DocumentHolder.splitCellsText": "拆分单元格", "DE.Views.DocumentHolder.splitCellTitleText": "拆分单元格", "DE.Views.DocumentHolder.strDelete": "删除签名", "DE.Views.DocumentHolder.strDetails": "签名详情", "DE.Views.DocumentHolder.strSetup": "签名设置", "DE.Views.DocumentHolder.strSign": "签名", "DE.Views.DocumentHolder.styleText": "格式化为样式", "DE.Views.DocumentHolder.tableText": "表格", "DE.Views.DocumentHolder.textAccept": "接受变化", "DE.Views.DocumentHolder.textAlign": "对齐", "DE.Views.DocumentHolder.textArrange": "安排", "DE.Views.DocumentHolder.textArrangeBack": "发送到背景", "DE.Views.DocumentHolder.textArrangeBackward": "下移一层", "DE.Views.DocumentHolder.textArrangeForward": "上移一层", "DE.Views.DocumentHolder.textArrangeFront": "放到最上面", "DE.Views.DocumentHolder.textCells": "元件", "DE.Views.DocumentHolder.textCol": "删除整列", "DE.Views.DocumentHolder.textContentControls": "内容控制", "DE.Views.DocumentHolder.textContinueNumbering": "继续编号", "DE.Views.DocumentHolder.textCopy": "复制", "DE.Views.DocumentHolder.textCrop": "裁剪", "DE.Views.DocumentHolder.textCropFill": "填满", "DE.Views.DocumentHolder.textCropFit": "最佳", "DE.Views.DocumentHolder.textCut": "剪切", "DE.Views.DocumentHolder.textDistributeCols": "分布列", "DE.Views.DocumentHolder.textDistributeRows": "分布行", "DE.Views.DocumentHolder.textEditControls": "内容控制设置", "DE.Views.DocumentHolder.textEditPoints": "编辑点", "DE.Views.DocumentHolder.textEditWrapBoundary": "编辑环绕边界", "DE.Views.DocumentHolder.textFlipH": "水平翻转", "DE.Views.DocumentHolder.textFlipV": "垂直翻转", "DE.Views.DocumentHolder.textFollow": "跟随移动", "DE.Views.DocumentHolder.textFromFile": "从文件导入", "DE.Views.DocumentHolder.textFromStorage": "来自存储设备", "DE.Views.DocumentHolder.textFromUrl": "从URL", "DE.Views.DocumentHolder.textJoinList": "联接上一个列表", "DE.Views.DocumentHolder.textLeft": "单元格左移", "DE.Views.DocumentHolder.textNest": "下一个表格", "DE.Views.DocumentHolder.textNextPage": "下一页", "DE.Views.DocumentHolder.textNumberingValue": "编号值", "DE.Views.DocumentHolder.textPaste": "粘贴", "DE.Views.DocumentHolder.textPrevPage": "上一页", "DE.Views.DocumentHolder.textRefreshField": "刷新字段", "DE.Views.DocumentHolder.textReject": "拒绝变化", "DE.Views.DocumentHolder.textRemCheckBox": "删除多选框", "DE.Views.DocumentHolder.textRemComboBox": "删除组合框", "DE.Views.DocumentHolder.textRemDropdown": "删除候选列表", "DE.Views.DocumentHolder.textRemField": "删除文本填充框", "DE.Views.DocumentHolder.textRemove": "删除", "DE.Views.DocumentHolder.textRemoveControl": "删除内容控件", "DE.Views.DocumentHolder.textRemPicture": "删除图片", "DE.Views.DocumentHolder.textRemRadioBox": "删除单选框", "DE.Views.DocumentHolder.textReplace": "替换图像", "DE.Views.DocumentHolder.textRotate": "旋转", "DE.Views.DocumentHolder.textRotate270": "逆时针旋转90°", "DE.Views.DocumentHolder.textRotate90": "顺时针旋转90°", "DE.Views.DocumentHolder.textRow": "删除整行", "DE.Views.DocumentHolder.textSeparateList": "分隔列表", "DE.Views.DocumentHolder.textSettings": "设置", "DE.Views.DocumentHolder.textSeveral": "几行/几列", "DE.Views.DocumentHolder.textShapeAlignBottom": "靠下对齐", "DE.Views.DocumentHolder.textShapeAlignCenter": "居中对齐", "DE.Views.DocumentHolder.textShapeAlignLeft": "左对齐", "DE.Views.DocumentHolder.textShapeAlignMiddle": "垂直居中", "DE.Views.DocumentHolder.textShapeAlignRight": "右对齐", "DE.Views.DocumentHolder.textShapeAlignTop": "靠上对齐", "DE.Views.DocumentHolder.textStartNewList": "开始新列表", "DE.Views.DocumentHolder.textStartNumberingFrom": "设置编号值", "DE.Views.DocumentHolder.textTitleCellsRemove": "删除单元格", "DE.Views.DocumentHolder.textTOC": "目录", "DE.Views.DocumentHolder.textTOCSettings": "目录设置", "DE.Views.DocumentHolder.textUndo": "撤消", "DE.Views.DocumentHolder.textUpdateAll": "刷新整个表格", "DE.Views.DocumentHolder.textUpdatePages": "仅刷新页码", "DE.Views.DocumentHolder.textUpdateTOC": "刷新目录", "DE.Views.DocumentHolder.textWrap": "包裹风格", "DE.Views.DocumentHolder.tipIsLocked": "此元素正在由其他用户编辑。", "DE.Views.DocumentHolder.toDictionaryText": "添加到词典", "DE.Views.DocumentHolder.txtAddBottom": "添加底部边框", "DE.Views.DocumentHolder.txtAddFractionBar": "添加分数栏", "DE.Views.DocumentHolder.txtAddHor": "在相同样式的段落之间添加间隔", "DE.Views.DocumentHolder.txtAddLB": "添加左下线", "DE.Views.DocumentHolder.txtAddLeft": "添加左边框", "DE.Views.DocumentHolder.txtAddLT": "添加左上线", "DE.Views.DocumentHolder.txtAddRight": "添加右边框", "DE.Views.DocumentHolder.txtAddTop": "添加顶部边框", "DE.Views.DocumentHolder.txtAddVer": "添加垂直线", "DE.Views.DocumentHolder.txtAlignToChar": "字符对齐", "DE.Views.DocumentHolder.txtBehind": "衬于​​文字下方", "DE.Views.DocumentHolder.txtBorderProps": "边框属性", "DE.Views.DocumentHolder.txtBottom": "底部", "DE.Views.DocumentHolder.txtColumnAlign": "列对齐", "DE.Views.DocumentHolder.txtDecreaseArg": "减少大小参数", "DE.Views.DocumentHolder.txtDeleteArg": "删除参数", "DE.Views.DocumentHolder.txtDeleteBreak": "删除手动的断点", "DE.Views.DocumentHolder.txtDeleteChars": "删除包围字符", "DE.Views.DocumentHolder.txtDeleteCharsAndSeparators": "删除封闭字符和分隔符", "DE.Views.DocumentHolder.txtDeleteEq": "删除方程", "DE.Views.DocumentHolder.txtDeleteGroupChar": "删除字符", "DE.Views.DocumentHolder.txtDeleteRadical": "彻底删除", "DE.Views.DocumentHolder.txtDistribHor": "水平分布", "DE.Views.DocumentHolder.txtDistribVert": "垂直分布", "DE.Views.DocumentHolder.txtEmpty": "（空）", "DE.Views.DocumentHolder.txtFractionLinear": "改为线性分数", "DE.Views.DocumentHolder.txtFractionSkewed": "改为倾斜分数", "DE.Views.DocumentHolder.txtFractionStacked": "改为堆积分数", "DE.Views.DocumentHolder.txtGroup": "分组", "DE.Views.DocumentHolder.txtGroupCharOver": "字符在文字上", "DE.Views.DocumentHolder.txtGroupCharUnder": "文字下的Char", "DE.Views.DocumentHolder.txtHideBottom": "隐藏底部边框", "DE.Views.DocumentHolder.txtHideBottomLimit": "隐藏下限", "DE.Views.DocumentHolder.txtHideCloseBracket": "隐藏右括号", "DE.Views.DocumentHolder.txtHideDegree": "隐藏学位", "DE.Views.DocumentHolder.txtHideHor": "隐藏水平线", "DE.Views.DocumentHolder.txtHideLB": "隐藏左侧底线", "DE.Views.DocumentHolder.txtHideLeft": "隐藏左边界", "DE.Views.DocumentHolder.txtHideLT": "隐藏左顶线", "DE.Views.DocumentHolder.txtHideOpenBracket": "隐藏开放支架", "DE.Views.DocumentHolder.txtHidePlaceholder": "隐藏占位符", "DE.Views.DocumentHolder.txtHideRight": "隐藏右边框", "DE.Views.DocumentHolder.txtHideTop": "隐藏顶部边框", "DE.Views.DocumentHolder.txtHideTopLimit": "隐藏上限", "DE.Views.DocumentHolder.txtHideVer": "隐藏垂直线", "DE.Views.DocumentHolder.txtIncreaseArg": "增加参数大小", "DE.Views.DocumentHolder.txtInFront": "浮于文字上方", "DE.Views.DocumentHolder.txtInline": "嵌入型​​", "DE.Views.DocumentHolder.txtInsertArgAfter": "插入参数", "DE.Views.DocumentHolder.txtInsertArgBefore": "之前插入参数", "DE.Views.DocumentHolder.txtInsertBreak": "插入手动中断", "DE.Views.DocumentHolder.txtInsertCaption": "插入标题", "DE.Views.DocumentHolder.txtInsertEqAfter": "插入方程后", "DE.Views.DocumentHolder.txtInsertEqBefore": "之前插入方程式", "DE.Views.DocumentHolder.txtKeepTextOnly": "仅保留文字", "DE.Views.DocumentHolder.txtLimitChange": "更改限制位置", "DE.Views.DocumentHolder.txtLimitOver": "限制文字", "DE.Views.DocumentHolder.txtLimitUnder": "在文本限制", "DE.Views.DocumentHolder.txtMatchBrackets": "匹配括号到参数高度", "DE.Views.DocumentHolder.txtMatrixAlign": "矩阵对齐", "DE.Views.DocumentHolder.txtOverbar": "文本上一条", "DE.Views.DocumentHolder.txtOverwriteCells": "覆盖单元格", "DE.Views.DocumentHolder.txtPasteSourceFormat": "保持源格式", "DE.Views.DocumentHolder.txtPressLink": "按{0}并单击链接", "DE.Views.DocumentHolder.txtPrintSelection": "打印选择", "DE.Views.DocumentHolder.txtRemFractionBar": "去除分数吧", "DE.Views.DocumentHolder.txtRemLimit": "删除限制", "DE.Views.DocumentHolder.txtRemoveAccentChar": "删除重音字符", "DE.Views.DocumentHolder.txtRemoveBar": "删除栏", "DE.Views.DocumentHolder.txtRemoveWarning": "要删除该签名吗？<br>这一操作不能被撤销。", "DE.Views.DocumentHolder.txtRemScripts": "删除上下标", "DE.Views.DocumentHolder.txtRemSubscript": "删除标", "DE.Views.DocumentHolder.txtRemSuperscript": "除去上标", "DE.Views.DocumentHolder.txtScriptsAfter": "文字后的上下标", "DE.Views.DocumentHolder.txtScriptsBefore": "文字前的上下标", "DE.Views.DocumentHolder.txtShowBottomLimit": "显示下限", "DE.Views.DocumentHolder.txtShowCloseBracket": "显示关闭括号", "DE.Views.DocumentHolder.txtShowDegree": "显示学位", "DE.Views.DocumentHolder.txtShowOpenBracket": "显示开放支架", "DE.Views.DocumentHolder.txtShowPlaceholder": "显示占位符", "DE.Views.DocumentHolder.txtShowTopLimit": "显示上限", "DE.Views.DocumentHolder.txtSquare": "四周型环绕", "DE.Views.DocumentHolder.txtStretchBrackets": "拉伸支架", "DE.Views.DocumentHolder.txtThrough": "穿越型环绕", "DE.Views.DocumentHolder.txtTight": "紧密型环绕", "DE.Views.DocumentHolder.txtTop": "顶部", "DE.Views.DocumentHolder.txtTopAndBottom": "上下型环绕", "DE.Views.DocumentHolder.txtUnderbar": "在文本栏", "DE.Views.DocumentHolder.txtUngroup": "取消组合", "DE.Views.DocumentHolder.txtWarnUrl": "点击该链接可能会损害你的设备或数据。<br>你确定要继续吗？", "DE.Views.DocumentHolder.unicodeText": "Unicode", "DE.Views.DocumentHolder.updateStyleText": "更新%1样式", "DE.Views.DocumentHolder.vertAlignText": "垂直对齐", "DE.Views.DropcapSettingsAdvanced.strBorders": "边框和填充", "DE.Views.DropcapSettingsAdvanced.strDropcap": "首字下沉 ", "DE.Views.DropcapSettingsAdvanced.strMargins": "边距", "DE.Views.DropcapSettingsAdvanced.textAlign": "对齐", "DE.Views.DropcapSettingsAdvanced.textAtLeast": "至少", "DE.Views.DropcapSettingsAdvanced.textAuto": "自动", "DE.Views.DropcapSettingsAdvanced.textBackColor": "背景颜色", "DE.Views.DropcapSettingsAdvanced.textBorderColor": "边框颜色", "DE.Views.DropcapSettingsAdvanced.textBorderDesc": "点击图表或使用按钮选择边框", "DE.Views.DropcapSettingsAdvanced.textBorderWidth": "边框大小", "DE.Views.DropcapSettingsAdvanced.textBottom": "底部", "DE.Views.DropcapSettingsAdvanced.textCenter": "中心", "DE.Views.DropcapSettingsAdvanced.textColumn": "列", "DE.Views.DropcapSettingsAdvanced.textDistance": "文字距离", "DE.Views.DropcapSettingsAdvanced.textExact": "精确地", "DE.Views.DropcapSettingsAdvanced.textFlow": "流程框架", "DE.Views.DropcapSettingsAdvanced.textFont": "字体", "DE.Views.DropcapSettingsAdvanced.textFrame": "框架", "DE.Views.DropcapSettingsAdvanced.textHeight": "高度", "DE.Views.DropcapSettingsAdvanced.textHorizontal": "水平的", "DE.Views.DropcapSettingsAdvanced.textInline": "内联框架", "DE.Views.DropcapSettingsAdvanced.textInMargin": "在边际", "DE.Views.DropcapSettingsAdvanced.textInText": "在文本中", "DE.Views.DropcapSettingsAdvanced.textLeft": "左", "DE.Views.DropcapSettingsAdvanced.textMargin": "边", "DE.Views.DropcapSettingsAdvanced.textMove": "文字移动", "DE.Views.DropcapSettingsAdvanced.textNone": "无", "DE.Views.DropcapSettingsAdvanced.textPage": "页面", "DE.Views.DropcapSettingsAdvanced.textParagraph": "段", "DE.Views.DropcapSettingsAdvanced.textParameters": "参数", "DE.Views.DropcapSettingsAdvanced.textPosition": "位置", "DE.Views.DropcapSettingsAdvanced.textRelative": "关系到", "DE.Views.DropcapSettingsAdvanced.textRight": "对", "DE.Views.DropcapSettingsAdvanced.textRowHeight": "行高", "DE.Views.DropcapSettingsAdvanced.textTitle": "首字下沉 - 高级设置", "DE.Views.DropcapSettingsAdvanced.textTitleFrame": "框架 - 高级设置", "DE.Views.DropcapSettingsAdvanced.textTop": "顶部", "DE.Views.DropcapSettingsAdvanced.textVertical": "垂直", "DE.Views.DropcapSettingsAdvanced.textWidth": "宽度", "DE.Views.DropcapSettingsAdvanced.tipFontName": "字体", "DE.Views.DropcapSettingsAdvanced.txtNoBorders": "没有边框", "DE.Views.EditListItemDialog.textDisplayName": "显示名称", "DE.Views.EditListItemDialog.textNameError": "显示名称不能空", "DE.Views.EditListItemDialog.textValue": "值", "DE.Views.EditListItemDialog.textValueError": "已存在具有相同值的项", "DE.Views.FileMenu.btnBackCaption": "打开文件所在位置", "DE.Views.FileMenu.btnCloseMenuCaption": "关闭菜单", "DE.Views.FileMenu.btnCreateNewCaption": "新建", "DE.Views.FileMenu.btnDownloadCaption": "下载为", "DE.Views.FileMenu.btnExitCaption": "关闭", "DE.Views.FileMenu.btnFileOpenCaption": "打开", "DE.Views.FileMenu.btnHelpCaption": "帮助", "DE.Views.FileMenu.btnHistoryCaption": "版本历史", "DE.Views.FileMenu.btnInfoCaption": "文件信息", "DE.Views.FileMenu.btnPrintCaption": "打印", "DE.Views.FileMenu.btnProtectCaption": "保护", "DE.Views.FileMenu.btnRecentFilesCaption": "打开最近", "DE.Views.FileMenu.btnRenameCaption": "改名", "DE.Views.FileMenu.btnReturnCaption": "返回文件", "DE.Views.FileMenu.btnRightsCaption": "访问权", "DE.Views.FileMenu.btnSaveAsCaption": "另存为", "DE.Views.FileMenu.btnSaveCaption": "保存", "DE.Views.FileMenu.btnSaveCopyAsCaption": "将副本另存为", "DE.Views.FileMenu.btnSettingsCaption": "高级设置", "DE.Views.FileMenu.btnToEditCaption": "编辑文档", "DE.Views.FileMenu.textDownload": "下载", "DE.Views.FileMenuPanels.CreateNew.txtBlank": "空文档", "DE.Views.FileMenuPanels.CreateNew.txtCreateNew": "新建", "DE.Views.FileMenuPanels.DocumentInfo.okButtonText": "应用", "DE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "添加作者", "DE.Views.FileMenuPanels.DocumentInfo.txtAddText": "添加文本", "DE.Views.FileMenuPanels.DocumentInfo.txtAppName": "应用", "DE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "作者", "DE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "更改访问权限", "DE.Views.FileMenuPanels.DocumentInfo.txtComment": "批注", "DE.Views.FileMenuPanels.DocumentInfo.txtCreated": "已创建", "DE.Views.FileMenuPanels.DocumentInfo.txtFastWV": "快速Web视图", "DE.Views.FileMenuPanels.DocumentInfo.txtLoading": "载入中……", "DE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "上次修改时间", "DE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "上次修改时间", "DE.Views.FileMenuPanels.DocumentInfo.txtNo": "否", "DE.Views.FileMenuPanels.DocumentInfo.txtOwner": "创建者", "DE.Views.FileMenuPanels.DocumentInfo.txtPages": "页面", "DE.Views.FileMenuPanels.DocumentInfo.txtPageSize": "页面大小", "DE.Views.FileMenuPanels.DocumentInfo.txtParagraphs": "段落", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfProducer": "PDF制作器", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfTagged": "已标记为PDF", "DE.Views.FileMenuPanels.DocumentInfo.txtPdfVer": "PDF版", "DE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "位置", "DE.Views.FileMenuPanels.DocumentInfo.txtRights": "有权利的人", "DE.Views.FileMenuPanels.DocumentInfo.txtSpaces": "字符数（计空格）", "DE.Views.FileMenuPanels.DocumentInfo.txtStatistics": "统计", "DE.Views.FileMenuPanels.DocumentInfo.txtSubject": "主题", "DE.Views.FileMenuPanels.DocumentInfo.txtSymbols": "字符数（不计空格）", "DE.Views.FileMenuPanels.DocumentInfo.txtTags": "标签", "DE.Views.FileMenuPanels.DocumentInfo.txtTitle": "标题", "DE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "上载", "DE.Views.FileMenuPanels.DocumentInfo.txtWords": "字幕", "DE.Views.FileMenuPanels.DocumentInfo.txtYes": "是", "DE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "更改访问权限", "DE.Views.FileMenuPanels.DocumentRights.txtRights": "有权利的人", "DE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "警告", "DE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "使用密码", "DE.Views.FileMenuPanels.ProtectDoc.strProtect": "保护文档", "DE.Views.FileMenuPanels.ProtectDoc.strSignature": "使用签名", "DE.Views.FileMenuPanels.ProtectDoc.txtEdit": "编辑文档", "DE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "编辑将删除文档中的签名。<br>您确定要继续吗？", "DE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "此文档受密码保护", "DE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "此文档需要签名。", "DE.Views.FileMenuPanels.ProtectDoc.txtSigned": "有效签名已添加到文档中。该文档受到保护，无法编辑。", "DE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "文档中的某些数字签名无效或无法验证。该文档受到保护，无法编辑。", "DE.Views.FileMenuPanels.ProtectDoc.txtView": "查看签名", "DE.Views.FileMenuPanels.Settings.okButtonText": "应用", "DE.Views.FileMenuPanels.Settings.strCoAuthMode": "共同编辑模式", "DE.Views.FileMenuPanels.Settings.strFast": "自动", "DE.Views.FileMenuPanels.Settings.strFontRender": "字体微调方式", "DE.Views.FileMenuPanels.Settings.strIgnoreWordsInUPPERCASE": "忽略大写单词", "DE.Views.FileMenuPanels.Settings.strIgnoreWordsWithNumbers": "忽略带数字的单词", "DE.Views.FileMenuPanels.Settings.strMacrosSettings": "宏设置", "DE.Views.FileMenuPanels.Settings.strPasteButton": "在执行粘贴操作后显示“粘贴选项”按钮", "DE.Views.FileMenuPanels.Settings.strShowChanges": "实时协作变更", "DE.Views.FileMenuPanels.Settings.strShowComments": "在文本中显示注释", "DE.Views.FileMenuPanels.Settings.strShowOthersChanges": "显示其他用户的更改", "DE.Views.FileMenuPanels.Settings.strShowResolvedComments": "显示已解决的注释", "DE.Views.FileMenuPanels.Settings.strStrict": "手动", "DE.Views.FileMenuPanels.Settings.strTheme": "界面主题", "DE.Views.FileMenuPanels.Settings.strUnit": "测量单位", "DE.Views.FileMenuPanels.Settings.strZoom": "默认缩放比率", "DE.Views.FileMenuPanels.Settings.text10Minutes": "每10分钟", "DE.Views.FileMenuPanels.Settings.text30Minutes": "每30分钟", "DE.Views.FileMenuPanels.Settings.text5Minutes": "每5分钟", "DE.Views.FileMenuPanels.Settings.text60Minutes": "每隔一小时", "DE.Views.FileMenuPanels.Settings.textAlignGuides": "对齐辅助线", "DE.Views.FileMenuPanels.Settings.textAutoRecover": "自动恢复", "DE.Views.FileMenuPanels.Settings.textAutoSave": "自动保存", "DE.Views.FileMenuPanels.Settings.textDisabled": "已禁用", "DE.Views.FileMenuPanels.Settings.textForceSave": "在保存中间版本", "DE.Views.FileMenuPanels.Settings.textMinute": "每一分钟", "DE.Views.FileMenuPanels.Settings.textOldVersions": "使用与旧版微软 Word 兼容的方式保存 DOCX 文档", "DE.Views.FileMenuPanels.Settings.txtAll": "查看全部", "DE.Views.FileMenuPanels.Settings.txtAutoCorrect": "自动修正选项...", "DE.Views.FileMenuPanels.Settings.txtCacheMode": "默认缓存模式", "DE.Views.FileMenuPanels.Settings.txtChangesBalloons": "点击通知气球以展示", "DE.Views.FileMenuPanels.Settings.txtChangesTip": "光标移至弹出窗口以展示", "DE.Views.FileMenuPanels.Settings.txtCm": "厘米", "DE.Views.FileMenuPanels.Settings.txtCollaboration": "协作", "DE.Views.FileMenuPanels.Settings.txtDarkMode": "开启文档夜间模式", "DE.Views.FileMenuPanels.Settings.txtEditingSaving": "编辑并保存", "DE.Views.FileMenuPanels.Settings.txtFastTip": "实时协同编辑。所有", "DE.Views.FileMenuPanels.Settings.txtFitPage": "适合页面", "DE.Views.FileMenuPanels.Settings.txtFitWidth": "适合宽度", "DE.Views.FileMenuPanels.Settings.txtHieroglyphs": "特殊符号", "DE.Views.FileMenuPanels.Settings.txtInch": "寸", "DE.Views.FileMenuPanels.Settings.txtLast": "最后查看", "DE.Views.FileMenuPanels.Settings.txtMac": "仿照 OS X", "DE.Views.FileMenuPanels.Settings.txtNative": "本机系统原生", "DE.Views.FileMenuPanels.Settings.txtNone": "无查看", "DE.Views.FileMenuPanels.Settings.txtProofing": "校对", "DE.Views.FileMenuPanels.Settings.txtPt": "点", "DE.Views.FileMenuPanels.Settings.txtQuickPrint": "在编辑器标题中显示快速打印按钮", "DE.Views.FileMenuPanels.Settings.txtQuickPrintTip": "文件将用最新选定的打印机或默认的打印机被打印出来。", "DE.Views.FileMenuPanels.Settings.txtRunMacros": "启动所有项目", "DE.Views.FileMenuPanels.Settings.txtRunMacrosDesc": "启动所有不带通知的宏", "DE.Views.FileMenuPanels.Settings.txtShowTrackChanges": "显示跟踪变化", "DE.Views.FileMenuPanels.Settings.txtSpellCheck": "拼写检查", "DE.Views.FileMenuPanels.Settings.txtStopMacros": "解除所有项目", "DE.Views.FileMenuPanels.Settings.txtStopMacrosDesc": "解除所有不带通知的宏", "DE.Views.FileMenuPanels.Settings.txtStrictTip": "使用“保存\"按钮同步你和其他人的修改", "DE.Views.FileMenuPanels.Settings.txtUseAltKey": "使用键盘上的Alt键导航至用户界面", "DE.Views.FileMenuPanels.Settings.txtUseOptionKey": "使用键盘上的Option键导航至用户界面", "DE.Views.FileMenuPanels.Settings.txtWarnMacros": "显示通知", "DE.Views.FileMenuPanels.Settings.txtWarnMacrosDesc": "解除所有带通知的宏", "DE.Views.FileMenuPanels.Settings.txtWin": "仿照 Windows", "DE.Views.FileMenuPanels.Settings.txtWorkspace": "工作空间", "DE.Views.FormSettings.textAlways": "总是", "DE.Views.FormSettings.textAnyone": "任何人", "DE.Views.FormSettings.textAspect": "锁定宽高比", "DE.Views.FormSettings.textAtLeast": "至少", "DE.Views.FormSettings.textAuto": "自动", "DE.Views.FormSettings.textAutofit": "自动适应", "DE.Views.FormSettings.textBackgroundColor": "背景颜色", "DE.Views.FormSettings.textCheckbox": "多选框", "DE.Views.FormSettings.textColor": "边框颜色", "DE.Views.FormSettings.textComb": "字符组合", "DE.Views.FormSettings.textCombobox": "\n组合框", "DE.Views.FormSettings.textComplex": "复合字段", "DE.Views.FormSettings.textConnected": "关联字段", "DE.Views.FormSettings.textCreditCard": "信用卡号码（如：4111-1111-1111-1111）", "DE.Views.FormSettings.textDateField": "日期与时间字段", "DE.Views.FormSettings.textDateFormat": "这样显示日期", "DE.Views.FormSettings.textDelete": "删除", "DE.Views.FormSettings.textDigits": "数字", "DE.Views.FormSettings.textDisconnect": "断开", "DE.Views.FormSettings.textDropDown": "候选列表", "DE.Views.FormSettings.textExact": "精确", "DE.Views.FormSettings.textField": "文字字段", "DE.Views.FormSettings.textFillRoles": "谁需要填写这个？", "DE.Views.FormSettings.textFixed": "固定填充框大小", "DE.Views.FormSettings.textFormat": "格式", "DE.Views.FormSettings.textFormatSymbols": "可用符号", "DE.Views.FormSettings.textFromFile": "从文件导入", "DE.Views.FormSettings.textFromStorage": "来自存储设备", "DE.Views.FormSettings.textFromUrl": "从URL", "DE.Views.FormSettings.textGroupKey": "群组密钥", "DE.Views.FormSettings.textImage": "图片", "DE.Views.FormSettings.textKey": "密钥", "DE.Views.FormSettings.textLang": "语言", "DE.Views.FormSettings.textLetters": "文字", "DE.Views.FormSettings.textLock": "锁定", "DE.Views.FormSettings.textMask": "任意掩码", "DE.Views.FormSettings.textMaxChars": "字数限制", "DE.Views.FormSettings.textMulti": "多行填充框", "DE.Views.FormSettings.textNever": "从不", "DE.Views.FormSettings.textNoBorder": "无边框", "DE.Views.FormSettings.textNone": "无", "DE.Views.FormSettings.textPhone1": "电话号码（如：(*************）", "DE.Views.FormSettings.textPhone2": "电话号码（如：+447911123456）", "DE.Views.FormSettings.textPlaceholder": "占位符", "DE.Views.FormSettings.textRadiobox": "单选框", "DE.Views.FormSettings.textReg": "正则表达式", "DE.Views.FormSettings.textRequired": "必填", "DE.Views.FormSettings.textScale": "何时按倍缩放？", "DE.Views.FormSettings.textSelectImage": "选择图像", "DE.Views.FormSettings.textTag": "标签", "DE.Views.FormSettings.textTip": "提示", "DE.Views.FormSettings.textTipAdd": "新增值", "DE.Views.FormSettings.textTipDelete": "刪除值", "DE.Views.FormSettings.textTipDown": "下移", "DE.Views.FormSettings.textTipUp": "上移", "DE.Views.FormSettings.textTooBig": "图片过大", "DE.Views.FormSettings.textTooSmall": "图片较小", "DE.Views.FormSettings.textUKPassport": "英国护照号码（如：*********）", "DE.Views.FormSettings.textUnlock": "解锁", "DE.Views.FormSettings.textUSSSN": "美国 SSN (如：***********)", "DE.Views.FormSettings.textValue": "值设置", "DE.Views.FormSettings.textWidth": "单元格宽度", "DE.Views.FormSettings.textZipCodeUS": "美国邮政编码（如：92663 或 92663-1234）", "DE.Views.FormsTab.capBtnCheckBox": "多选框", "DE.Views.FormsTab.capBtnComboBox": "组合框", "DE.Views.FormsTab.capBtnComplex": "复合字段", "DE.Views.FormsTab.capBtnDownloadForm": "下载为oform", "DE.Views.FormsTab.capBtnDropDown": "候选列表", "DE.Views.FormsTab.capBtnEmail": "Email地址", "DE.Views.FormsTab.capBtnImage": "图片", "DE.Views.FormsTab.capBtnManager": "管理角色", "DE.Views.FormsTab.capBtnNext": "下一填充框", "DE.Views.FormsTab.capBtnPhone": "电话号码", "DE.Views.FormsTab.capBtnPrev": "上一填充框", "DE.Views.FormsTab.capBtnRadioBox": "单选框", "DE.Views.FormsTab.capBtnSaveForm": "另存为oform", "DE.Views.FormsTab.capBtnSubmit": "提交", "DE.Views.FormsTab.capBtnText": "文字字段", "DE.Views.FormsTab.capBtnView": "浏览表单", "DE.Views.FormsTab.capCreditCard": "信用卡", "DE.Views.FormsTab.capDateTime": "日期与时间", "DE.Views.FormsTab.capZipCode": "邮政编码", "DE.Views.FormsTab.textAnyone": "任何人", "DE.Views.FormsTab.textClear": "清除填充框", "DE.Views.FormsTab.textClearFields": "清除所有字段", "DE.Views.FormsTab.textCreateForm": "添加字段并创建一个可填充的OFORM文件", "DE.Views.FormsTab.textGotIt": "明白了", "DE.Views.FormsTab.textHighlight": "高亮设置", "DE.Views.FormsTab.textNoHighlight": "无高亮", "DE.Views.FormsTab.textRequired": "要发送表单，请填写所有必填项目。", "DE.Views.FormsTab.textSubmited": "成功提交表单", "DE.Views.FormsTab.tipCheckBox": "插入多选框", "DE.Views.FormsTab.tipComboBox": "插入组合框", "DE.Views.FormsTab.tipComplexField": "插入复合字段", "DE.Views.FormsTab.tipCreditCard": "插入信用卡号码", "DE.Views.FormsTab.tipDateTime": "插入日期与时间", "DE.Views.FormsTab.tipDownloadForm": "下载一个文件填写", "DE.Views.FormsTab.tipDropDown": "插入候选列表", "DE.Views.FormsTab.tipEmailField": "插入电邮地址", "DE.Views.FormsTab.tipFixedText": "插入固定文本字段", "DE.Views.FormsTab.tipImageField": "插入图片", "DE.Views.FormsTab.tipInlineText": "插入内嵌文本字段", "DE.Views.FormsTab.tipManager": "管理角色", "DE.Views.FormsTab.tipNextForm": "前往下一填充框", "DE.Views.FormsTab.tipPhoneField": "插入电话号码", "DE.Views.FormsTab.tipPrevForm": "回到上一填充框", "DE.Views.FormsTab.tipRadioBox": "添加单选框", "DE.Views.FormsTab.tipSaveForm": "将文件保存为可填充的OFORM文件", "DE.Views.FormsTab.tipSubmit": "提交表单", "DE.Views.FormsTab.tipTextField": "插入文本框", "DE.Views.FormsTab.tipViewForm": "视图窗体", "DE.Views.FormsTab.tipZipCode": "插入邮政编码", "DE.Views.FormsTab.txtFixedDesc": "插入固定文本字段", "DE.Views.FormsTab.txtFixedText": "固定", "DE.Views.FormsTab.txtInlineDesc": "插入内嵌文本字段", "DE.Views.FormsTab.txtInlineText": "内嵌", "DE.Views.FormsTab.txtUntitled": "无标题", "DE.Views.HeaderFooterSettings.textBottomCenter": "底部中心", "DE.Views.HeaderFooterSettings.textBottomLeft": "左下", "DE.Views.HeaderFooterSettings.textBottomPage": "页面底部", "DE.Views.HeaderFooterSettings.textBottomRight": "右下", "DE.Views.HeaderFooterSettings.textDiffFirst": "不同的第一页", "DE.Views.HeaderFooterSettings.textDiffOdd": "不同的奇数页和偶数页", "DE.Views.HeaderFooterSettings.textFrom": "开始", "DE.Views.HeaderFooterSettings.textHeaderFromBottom": "页脚从底部", "DE.Views.HeaderFooterSettings.textHeaderFromTop": "头顶", "DE.Views.HeaderFooterSettings.textInsertCurrent": "插入到当前位置", "DE.Views.HeaderFooterSettings.textOptions": "选项", "DE.Views.HeaderFooterSettings.textPageNum": "插入页码", "DE.Views.HeaderFooterSettings.textPageNumbering": "页码编号", "DE.Views.HeaderFooterSettings.textPosition": "位置", "DE.Views.HeaderFooterSettings.textPrev": "从上一节继续", "DE.Views.HeaderFooterSettings.textSameAs": "链接到上一个", "DE.Views.HeaderFooterSettings.textTopCenter": "顶部中心", "DE.Views.HeaderFooterSettings.textTopLeft": "左上", "DE.Views.HeaderFooterSettings.textTopPage": "页面顶部", "DE.Views.HeaderFooterSettings.textTopRight": "右上", "DE.Views.HyperlinkSettingsDialog.textDefault": "所选文本片段", "DE.Views.HyperlinkSettingsDialog.textDisplay": "展示", "DE.Views.HyperlinkSettingsDialog.textExternal": "外部链接", "DE.Views.HyperlinkSettingsDialog.textInternal": "文档中的位置", "DE.Views.HyperlinkSettingsDialog.textTitle": "超链接设置", "DE.Views.HyperlinkSettingsDialog.textTooltip": "屏幕提示文字", "DE.Views.HyperlinkSettingsDialog.textUrl": "链接到", "DE.Views.HyperlinkSettingsDialog.txtBeginning": "文档开头", "DE.Views.HyperlinkSettingsDialog.txtBookmarks": "书签", "DE.Views.HyperlinkSettingsDialog.txtEmpty": "这是必填栏", "DE.Views.HyperlinkSettingsDialog.txtHeadings": "标题", "DE.Views.HyperlinkSettingsDialog.txtNotUrl": "该字段应该是“http://www.example.com”格式的URL", "DE.Views.HyperlinkSettingsDialog.txtSizeLimit": "该域字符限制为2803个。", "DE.Views.ImageSettings.textAdvanced": "显示高级设置", "DE.Views.ImageSettings.textCrop": "裁剪", "DE.Views.ImageSettings.textCropFill": "填满", "DE.Views.ImageSettings.textCropFit": "最佳", "DE.Views.ImageSettings.textCropToShape": "裁剪为形状", "DE.Views.ImageSettings.textEdit": "修改", "DE.Views.ImageSettings.textEditObject": "编辑对象", "DE.Views.ImageSettings.textFitMargins": "适合边距", "DE.Views.ImageSettings.textFlip": "翻转", "DE.Views.ImageSettings.textFromFile": "从文件导入", "DE.Views.ImageSettings.textFromStorage": "来自存储设备", "DE.Views.ImageSettings.textFromUrl": "从URL", "DE.Views.ImageSettings.textHeight": "高度", "DE.Views.ImageSettings.textHint270": "逆时针旋转90°", "DE.Views.ImageSettings.textHint90": "顺时针旋转90°", "DE.Views.ImageSettings.textHintFlipH": "水平翻转", "DE.Views.ImageSettings.textHintFlipV": "垂直翻转", "DE.Views.ImageSettings.textInsert": "替换图像", "DE.Views.ImageSettings.textOriginalSize": "实际大小", "DE.Views.ImageSettings.textRecentlyUsed": "最近使用的", "DE.Views.ImageSettings.textRotate90": "旋转90°", "DE.Views.ImageSettings.textRotation": "旋转", "DE.Views.ImageSettings.textSize": "大小", "DE.Views.ImageSettings.textWidth": "宽度", "DE.Views.ImageSettings.textWrap": "包裹风格", "DE.Views.ImageSettings.txtBehind": "衬于​​文字下方", "DE.Views.ImageSettings.txtInFront": "浮于文字上方", "DE.Views.ImageSettings.txtInline": "嵌入型​​", "DE.Views.ImageSettings.txtSquare": "四周型环绕", "DE.Views.ImageSettings.txtThrough": "穿越型环绕", "DE.Views.ImageSettings.txtTight": "紧密型环绕", "DE.Views.ImageSettings.txtTopAndBottom": "上下型环绕", "DE.Views.ImageSettingsAdvanced.strMargins": "文字填充", "DE.Views.ImageSettingsAdvanced.textAbsoluteWH": "绝对", "DE.Views.ImageSettingsAdvanced.textAlignment": "对齐", "DE.Views.ImageSettingsAdvanced.textAlt": "替代文本", "DE.Views.ImageSettingsAdvanced.textAltDescription": "说明", "DE.Views.ImageSettingsAdvanced.textAltTip": "视觉对象信息的替代基于文本的表示法，将要向有视觉或认知障碍人阅读，以帮助他们更好地了解图像、自选图形、图表或表格中的那些信息。", "DE.Views.ImageSettingsAdvanced.textAltTitle": "标题", "DE.Views.ImageSettingsAdvanced.textAngle": "角度", "DE.Views.ImageSettingsAdvanced.textArrows": "箭头", "DE.Views.ImageSettingsAdvanced.textAspectRatio": "锁定宽高比", "DE.Views.ImageSettingsAdvanced.textAutofit": "自动适应", "DE.Views.ImageSettingsAdvanced.textBeginSize": "初始大小", "DE.Views.ImageSettingsAdvanced.textBeginStyle": "初始风格", "DE.Views.ImageSettingsAdvanced.textBelow": "下面", "DE.Views.ImageSettingsAdvanced.textBevel": "斜角", "DE.Views.ImageSettingsAdvanced.textBottom": "底部", "DE.Views.ImageSettingsAdvanced.textBottomMargin": "底部边缘", "DE.Views.ImageSettingsAdvanced.textBtnWrap": "文字包装", "DE.Views.ImageSettingsAdvanced.textCapType": "帽类型", "DE.Views.ImageSettingsAdvanced.textCenter": "中心", "DE.Views.ImageSettingsAdvanced.textCharacter": "字符", "DE.Views.ImageSettingsAdvanced.textColumn": "列", "DE.Views.ImageSettingsAdvanced.textDistance": "文字距离", "DE.Views.ImageSettingsAdvanced.textEndSize": "结束尺寸", "DE.Views.ImageSettingsAdvanced.textEndStyle": "结束样式", "DE.Views.ImageSettingsAdvanced.textFlat": "平面", "DE.Views.ImageSettingsAdvanced.textFlipped": "已翻转", "DE.Views.ImageSettingsAdvanced.textHeight": "高度", "DE.Views.ImageSettingsAdvanced.textHorizontal": "水平的", "DE.Views.ImageSettingsAdvanced.textHorizontally": "水平", "DE.Views.ImageSettingsAdvanced.textJoinType": "加入类型", "DE.Views.ImageSettingsAdvanced.textKeepRatio": "不变比例", "DE.Views.ImageSettingsAdvanced.textLeft": "左", "DE.Views.ImageSettingsAdvanced.textLeftMargin": "左边距", "DE.Views.ImageSettingsAdvanced.textLine": "线", "DE.Views.ImageSettingsAdvanced.textLineStyle": "线样式", "DE.Views.ImageSettingsAdvanced.textMargin": "边", "DE.Views.ImageSettingsAdvanced.textMiter": "米特", "DE.Views.ImageSettingsAdvanced.textMove": "用文本移动对象", "DE.Views.ImageSettingsAdvanced.textOptions": "选项", "DE.Views.ImageSettingsAdvanced.textOriginalSize": "实际大小", "DE.Views.ImageSettingsAdvanced.textOverlap": "允许重叠", "DE.Views.ImageSettingsAdvanced.textPage": "页面", "DE.Views.ImageSettingsAdvanced.textParagraph": "段", "DE.Views.ImageSettingsAdvanced.textPosition": "位置", "DE.Views.ImageSettingsAdvanced.textPositionPc": "相对位置", "DE.Views.ImageSettingsAdvanced.textRelative": "关系到", "DE.Views.ImageSettingsAdvanced.textRelativeWH": "相对的", "DE.Views.ImageSettingsAdvanced.textResizeFit": "调整形状以适应文本", "DE.Views.ImageSettingsAdvanced.textRight": "对", "DE.Views.ImageSettingsAdvanced.textRightMargin": "右页边距", "DE.Views.ImageSettingsAdvanced.textRightOf": "在 - 的右边", "DE.Views.ImageSettingsAdvanced.textRotation": "旋转", "DE.Views.ImageSettingsAdvanced.textRound": "圆", "DE.Views.ImageSettingsAdvanced.textShape": "形状设置", "DE.Views.ImageSettingsAdvanced.textSize": "大小", "DE.Views.ImageSettingsAdvanced.textSquare": "四周型环绕", "DE.Views.ImageSettingsAdvanced.textTextBox": "文本框", "DE.Views.ImageSettingsAdvanced.textTitle": "图片 - 高级设置", "DE.Views.ImageSettingsAdvanced.textTitleChart": "图 - 高级设置", "DE.Views.ImageSettingsAdvanced.textTitleShape": "形状 - 高级设置", "DE.Views.ImageSettingsAdvanced.textTop": "顶部", "DE.Views.ImageSettingsAdvanced.textTopMargin": "上边距", "DE.Views.ImageSettingsAdvanced.textVertical": "垂直", "DE.Views.ImageSettingsAdvanced.textVertically": "垂直", "DE.Views.ImageSettingsAdvanced.textWeightArrows": "重量和箭头", "DE.Views.ImageSettingsAdvanced.textWidth": "宽度", "DE.Views.ImageSettingsAdvanced.textWrap": "包裹风格", "DE.Views.ImageSettingsAdvanced.textWrapBehindTooltip": "衬于​​文字下方", "DE.Views.ImageSettingsAdvanced.textWrapInFrontTooltip": "浮于文字上方", "DE.Views.ImageSettingsAdvanced.textWrapInlineTooltip": "嵌入型​​", "DE.Views.ImageSettingsAdvanced.textWrapSquareTooltip": "四周型环绕", "DE.Views.ImageSettingsAdvanced.textWrapThroughTooltip": "穿越型环绕", "DE.Views.ImageSettingsAdvanced.textWrapTightTooltip": "紧密型环绕", "DE.Views.ImageSettingsAdvanced.textWrapTopbottomTooltip": "上下型环绕", "DE.Views.LeftMenu.tipAbout": "关于", "DE.Views.LeftMenu.tipChat": "聊天", "DE.Views.LeftMenu.tipComments": "批注", "DE.Views.LeftMenu.tipNavigation": "导航", "DE.Views.LeftMenu.tipOutline": "标题", "DE.Views.LeftMenu.tipPageThumbnails": "页面缩略图", "DE.Views.LeftMenu.tipPlugins": "插件", "DE.Views.LeftMenu.tipSearch": "搜索", "DE.Views.LeftMenu.tipSupport": "反馈和支持", "DE.Views.LeftMenu.tipTitles": "标题", "DE.Views.LeftMenu.txtDeveloper": "开发者模式", "DE.Views.LeftMenu.txtEditor": "文字编辑器", "DE.Views.LeftMenu.txtLimit": "限制访问", "DE.Views.LeftMenu.txtTrial": "试用模式", "DE.Views.LeftMenu.txtTrialDev": "试用开发者模式", "DE.Views.LineNumbersDialog.textAddLineNumbering": "新增行号", "DE.Views.LineNumbersDialog.textApplyTo": "应用更改", "DE.Views.LineNumbersDialog.textContinuous": "连续", "DE.Views.LineNumbersDialog.textCountBy": "计数依据", "DE.Views.LineNumbersDialog.textDocument": "整个文件", "DE.Views.LineNumbersDialog.textForward": "从该处起", "DE.Views.LineNumbersDialog.textFromText": "来自文本", "DE.Views.LineNumbersDialog.textNumbering": "编号", "DE.Views.LineNumbersDialog.textRestartEachPage": "重新启动每一页", "DE.Views.LineNumbersDialog.textRestartEachSection": "重新启动每一节", "DE.Views.LineNumbersDialog.textSection": "当前部分", "DE.Views.LineNumbersDialog.textStartAt": "始于", "DE.Views.LineNumbersDialog.textTitle": "行号", "DE.Views.LineNumbersDialog.txtAutoText": "自动", "DE.Views.Links.capBtnAddText": "添加文本", "DE.Views.Links.capBtnBookmarks": "书签", "DE.Views.Links.capBtnCaption": "标题", "DE.Views.Links.capBtnContentsUpdate": "刷新", "DE.Views.Links.capBtnCrossRef": "交叉引用", "DE.Views.Links.capBtnInsContents": "目录", "DE.Views.Links.capBtnInsFootnote": "脚注", "DE.Views.Links.capBtnInsLink": "超链接", "DE.Views.Links.capBtnTOF": "图表目录", "DE.Views.Links.confirmDeleteFootnotes": "您要删除所有脚注吗？", "DE.Views.Links.confirmReplaceTOF": "确认替换选中的图表目录？", "DE.Views.Links.mniConvertNote": "转换所有注释", "DE.Views.Links.mniDelFootnote": "删除所有注释", "DE.Views.Links.mniInsEndnote": "插入尾注", "DE.Views.Links.mniInsFootnote": "插入脚注", "DE.Views.Links.mniNoteSettings": "笔记设置", "DE.Views.Links.textContentsRemove": "删除目录", "DE.Views.Links.textContentsSettings": "设置", "DE.Views.Links.textConvertToEndnotes": "将所有脚注转换为尾注", "DE.Views.Links.textConvertToFootnotes": "将所有尾注转换为脚注", "DE.Views.Links.textGotoEndnote": "转到尾注", "DE.Views.Links.textGotoFootnote": "转到脚注", "DE.Views.Links.textSwapNotes": "交换脚注和尾注", "DE.Views.Links.textUpdateAll": "刷新整个表格", "DE.Views.Links.textUpdatePages": "仅刷新页码", "DE.Views.Links.tipAddText": "包括标题在", "DE.Views.Links.tipBookmarks": "创建书签", "DE.Views.Links.tipCaption": "插入标题", "DE.Views.Links.tipContents": "插入目录", "DE.Views.Links.tipContentsUpdate": "刷新目录", "DE.Views.Links.tipCrossRef": "插入交叉引用", "DE.Views.Links.tipInsertHyperlink": "添加超链接", "DE.Views.Links.tipNotes": "插入或编辑脚注", "DE.Views.Links.tipTableFigures": "插入图表目录", "DE.Views.Links.tipTableFiguresUpdate": "刷新图表目录", "DE.Views.Links.titleUpdateTOF": "刷新图表目录", "DE.Views.Links.txtDontShowTof": "不要显示在表格", "DE.Views.Links.txtLevel": "级别", "DE.Views.ListSettingsDialog.textAuto": "自动", "DE.Views.ListSettingsDialog.textCenter": "中心", "DE.Views.ListSettingsDialog.textLeft": "左", "DE.Views.ListSettingsDialog.textLevel": "级别", "DE.Views.ListSettingsDialog.textPreview": "预览", "DE.Views.ListSettingsDialog.textRight": "右", "DE.Views.ListSettingsDialog.txtAlign": "对齐", "DE.Views.ListSettingsDialog.txtBullet": "项目符号", "DE.Views.ListSettingsDialog.txtColor": "颜色", "DE.Views.ListSettingsDialog.txtFont": "字体和符号", "DE.Views.ListSettingsDialog.txtLikeText": "像文字一样", "DE.Views.ListSettingsDialog.txtNewBullet": "添加一个新的项目点", "DE.Views.ListSettingsDialog.txtNone": "无", "DE.Views.ListSettingsDialog.txtSize": "大小", "DE.Views.ListSettingsDialog.txtSymbol": "符号", "DE.Views.ListSettingsDialog.txtTitle": "列表设置", "DE.Views.ListSettingsDialog.txtType": "类型", "DE.Views.MailMergeEmailDlg.filePlaceholder": "PDF", "DE.Views.MailMergeEmailDlg.okButtonText": "发送", "DE.Views.MailMergeEmailDlg.subjectPlaceholder": "主题", "DE.Views.MailMergeEmailDlg.textAttachDocx": "作为DOCX附加", "DE.Views.MailMergeEmailDlg.textAttachPdf": "附上PDF", "DE.Views.MailMergeEmailDlg.textFileName": "文件名", "DE.Views.MailMergeEmailDlg.textFormat": "邮件格式", "DE.Views.MailMergeEmailDlg.textFrom": "从", "DE.Views.MailMergeEmailDlg.textHTML": "超文本标记语言", "DE.Views.MailMergeEmailDlg.textMessage": "消息", "DE.Views.MailMergeEmailDlg.textSubject": "标题", "DE.Views.MailMergeEmailDlg.textTitle": "发送到电子邮件", "DE.Views.MailMergeEmailDlg.textTo": "到", "DE.Views.MailMergeEmailDlg.textWarning": "警告！", "DE.Views.MailMergeEmailDlg.textWarningMsg": "请注意，一旦您点击“发送”按钮，邮件无法停止。", "DE.Views.MailMergeSettings.downloadMergeTitle": "合并", "DE.Views.MailMergeSettings.errorMailMergeSaveFile": "合并失败", "DE.Views.MailMergeSettings.notcriticalErrorTitle": "警告", "DE.Views.MailMergeSettings.textAddRecipients": "首先将一些收件人添加到列表中", "DE.Views.MailMergeSettings.textAll": "所有记录", "DE.Views.MailMergeSettings.textCurrent": "当前记录", "DE.Views.MailMergeSettings.textDataSource": "数据来源", "DE.Views.MailMergeSettings.textDocx": "Docx", "DE.Views.MailMergeSettings.textDownload": "下载", "DE.Views.MailMergeSettings.textEditData": "编辑收件人列表", "DE.Views.MailMergeSettings.textEmail": "电子邮件", "DE.Views.MailMergeSettings.textFrom": "从", "DE.Views.MailMergeSettings.textGoToMail": "转到邮件", "DE.Views.MailMergeSettings.textHighlight": "突出显示合并字段", "DE.Views.MailMergeSettings.textInsertField": "插入合并字段", "DE.Views.MailMergeSettings.textMaxRecepients": "最多100位收件人。", "DE.Views.MailMergeSettings.textMerge": "合并", "DE.Views.MailMergeSettings.textMergeFields": "合并字段", "DE.Views.MailMergeSettings.textMergeTo": "合并到", "DE.Views.MailMergeSettings.textPdf": "PDF", "DE.Views.MailMergeSettings.textPortal": "保存", "DE.Views.MailMergeSettings.textPreview": "预览结果", "DE.Views.MailMergeSettings.textReadMore": "了解更多", "DE.Views.MailMergeSettings.textSendMsg": "所有邮件都已准备就绪，并会在一段时间内发出。<br>邮件的速度取决于您的邮件服务,您可以继续使用文档或关闭它。操作结束后，通知将发送到您的注册邮箱地址。", "DE.Views.MailMergeSettings.textTo": "到", "DE.Views.MailMergeSettings.txtFirst": "首先记录", "DE.Views.MailMergeSettings.txtFromToError": "From”值必须小于“To”值", "DE.Views.MailMergeSettings.txtLast": "到下一个记录", "DE.Views.MailMergeSettings.txtNext": "到下一个记录", "DE.Views.MailMergeSettings.txtPrev": "以前的记录", "DE.Views.MailMergeSettings.txtUntitled": "无标题", "DE.Views.MailMergeSettings.warnProcessMailMerge": "启动合并失败", "DE.Views.Navigation.strNavigate": "标题", "DE.Views.Navigation.txtClosePanel": "关闭标题", "DE.Views.Navigation.txtCollapse": "全部收起", "DE.Views.Navigation.txtDemote": "下移", "DE.Views.Navigation.txtEmpty": "文档中无标题。<br>文本中应用标题样式以便使其出现在目录中。", "DE.Views.Navigation.txtEmptyItem": "空标题", "DE.Views.Navigation.txtEmptyViewer": "文档中无标题。", "DE.Views.Navigation.txtExpand": "全部展开", "DE.Views.Navigation.txtExpandToLevel": "展开至特定级别", "DE.Views.Navigation.txtFontSize": "字体大小", "DE.Views.Navigation.txtHeadingAfter": "新标题在后", "DE.Views.Navigation.txtHeadingBefore": "新标题在前", "DE.Views.Navigation.txtLarge": "大", "DE.Views.Navigation.txtMedium": "中", "DE.Views.Navigation.txtNewHeading": "新副标题", "DE.Views.Navigation.txtPromote": "上移", "DE.Views.Navigation.txtSelect": "选择内容", "DE.Views.Navigation.txtSettings": "标题设置", "DE.Views.Navigation.txtSmall": "小", "DE.Views.Navigation.txtWrapHeadings": "长标题换行", "DE.Views.NoteSettingsDialog.textApply": "应用", "DE.Views.NoteSettingsDialog.textApplyTo": "应用更改", "DE.Views.NoteSettingsDialog.textContinue": "连续", "DE.Views.NoteSettingsDialog.textCustom": "自定义标记", "DE.Views.NoteSettingsDialog.textDocEnd": "全文完", "DE.Views.NoteSettingsDialog.textDocument": "整个文件", "DE.Views.NoteSettingsDialog.textEachPage": "重新启动每一页", "DE.Views.NoteSettingsDialog.textEachSection": "重新启动每一节", "DE.Views.NoteSettingsDialog.textEndnote": "尾注", "DE.Views.NoteSettingsDialog.textFootnote": "脚注", "DE.Views.NoteSettingsDialog.textFormat": "格式", "DE.Views.NoteSettingsDialog.textInsert": "插入", "DE.Views.NoteSettingsDialog.textLocation": "位置", "DE.Views.NoteSettingsDialog.textNumbering": "编号", "DE.Views.NoteSettingsDialog.textNumFormat": "数字格式", "DE.Views.NoteSettingsDialog.textPageBottom": "页面底部", "DE.Views.NoteSettingsDialog.textSectEnd": "本节结束", "DE.Views.NoteSettingsDialog.textSection": "当前部分", "DE.Views.NoteSettingsDialog.textStart": "开始", "DE.Views.NoteSettingsDialog.textTextBottom": "文字下方", "DE.Views.NoteSettingsDialog.textTitle": "笔记设置", "DE.Views.NotesRemoveDialog.textEnd": "刪除所有尾注", "DE.Views.NotesRemoveDialog.textFoot": "删除所有脚注", "DE.Views.NotesRemoveDialog.textTitle": "删除注释", "DE.Views.PageMarginsDialog.notcriticalErrorTitle": "警告", "DE.Views.PageMarginsDialog.textBottom": "底部", "DE.Views.PageMarginsDialog.textGutter": " 装订线", "DE.Views.PageMarginsDialog.textGutterPosition": " 装订线的位置", "DE.Views.PageMarginsDialog.textInside": "里面", "DE.Views.PageMarginsDialog.textLandscape": "横向", "DE.Views.PageMarginsDialog.textLeft": "左", "DE.Views.PageMarginsDialog.textMirrorMargins": "镜像边距", "DE.Views.PageMarginsDialog.textMultiplePages": "多页", "DE.Views.PageMarginsDialog.textNormal": "常规", "DE.Views.PageMarginsDialog.textOrientation": "方向", "DE.Views.PageMarginsDialog.textOutside": "外面", "DE.Views.PageMarginsDialog.textPortrait": "纵向", "DE.Views.PageMarginsDialog.textPreview": "预览", "DE.Views.PageMarginsDialog.textRight": "右", "DE.Views.PageMarginsDialog.textTitle": "边距", "DE.Views.PageMarginsDialog.textTop": "顶部", "DE.Views.PageMarginsDialog.txtMarginsH": "顶部和底部边距对于给定的页面高度来说太高", "DE.Views.PageMarginsDialog.txtMarginsW": "对于给定的页面宽度，左右边距太宽", "DE.Views.PageSizeDialog.textHeight": "高度", "DE.Views.PageSizeDialog.textPreset": "预设", "DE.Views.PageSizeDialog.textTitle": "页面大小", "DE.Views.PageSizeDialog.textWidth": "宽度", "DE.Views.PageSizeDialog.txtCustom": "自定义", "DE.Views.PageThumbnails.textClosePanel": "关闭页面缩略图", "DE.Views.PageThumbnails.textHighlightVisiblePart": "突出显示页面的可见部分", "DE.Views.PageThumbnails.textPageThumbnails": "页面缩略图", "DE.Views.PageThumbnails.textThumbnailsSettings": "缩略图设置", "DE.Views.PageThumbnails.textThumbnailsSize": "缩略图大小", "DE.Views.ParagraphSettings.strIndent": "缩进", "DE.Views.ParagraphSettings.strIndentsLeftText": "左", "DE.Views.ParagraphSettings.strIndentsRightText": "右", "DE.Views.ParagraphSettings.strIndentsSpecial": "特殊格式", "DE.Views.ParagraphSettings.strLineHeight": "行间距", "DE.Views.ParagraphSettings.strParagraphSpacing": "段落间距", "DE.Views.ParagraphSettings.strSomeParagraphSpace": "不要在相同样式的段落之间添加间隔", "DE.Views.ParagraphSettings.strSpacingAfter": "段后", "DE.Views.ParagraphSettings.strSpacingBefore": "段前", "DE.Views.ParagraphSettings.textAdvanced": "显示高级设置", "DE.Views.ParagraphSettings.textAt": "在", "DE.Views.ParagraphSettings.textAtLeast": "至少", "DE.Views.ParagraphSettings.textAuto": "多", "DE.Views.ParagraphSettings.textBackColor": "背景颜色", "DE.Views.ParagraphSettings.textExact": "精确地", "DE.Views.ParagraphSettings.textFirstLine": "首行缩进", "DE.Views.ParagraphSettings.textHanging": "悬挂缩进", "DE.Views.ParagraphSettings.textNoneSpecial": "（无）", "DE.Views.ParagraphSettings.txtAutoText": "自动", "DE.Views.ParagraphSettingsAdvanced.noTabs": "指定的选项卡将显示在此字段中", "DE.Views.ParagraphSettingsAdvanced.strAllCaps": "全部大写字母", "DE.Views.ParagraphSettingsAdvanced.strBorders": "边框和填充", "DE.Views.ParagraphSettingsAdvanced.strBreakBefore": "段前分页", "DE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "双删除线", "DE.Views.ParagraphSettingsAdvanced.strIndent": "缩进", "DE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "左", "DE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "行间距", "DE.Views.ParagraphSettingsAdvanced.strIndentsOutlinelevel": "大纲级别", "DE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "右", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "段后", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "段前", "DE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "特殊格式", "DE.Views.ParagraphSettingsAdvanced.strKeepLines": "保持同一行", "DE.Views.ParagraphSettingsAdvanced.strKeepNext": "与下一个保持一致", "DE.Views.ParagraphSettingsAdvanced.strMargins": "填充", "DE.Views.ParagraphSettingsAdvanced.strOrphan": "单独控制", "DE.Views.ParagraphSettingsAdvanced.strParagraphFont": "字体", "DE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "缩进和间距", "DE.Views.ParagraphSettingsAdvanced.strParagraphLine": "换行符和分页符", "DE.Views.ParagraphSettingsAdvanced.strParagraphPosition": "放置", "DE.Views.ParagraphSettingsAdvanced.strSmallCaps": "小型大写字母", "DE.Views.ParagraphSettingsAdvanced.strSomeParagraphSpace": "不要在相同样式的段落之间添加间隔", "DE.Views.ParagraphSettingsAdvanced.strSpacing": "间距", "DE.Views.ParagraphSettingsAdvanced.strStrike": "删除线", "DE.Views.ParagraphSettingsAdvanced.strSubscript": "下标", "DE.Views.ParagraphSettingsAdvanced.strSuperscript": "上标", "DE.Views.ParagraphSettingsAdvanced.strSuppressLineNumbers": "压制行号", "DE.Views.ParagraphSettingsAdvanced.strTabs": "标签", "DE.Views.ParagraphSettingsAdvanced.textAlign": "对齐", "DE.Views.ParagraphSettingsAdvanced.textAll": "所有", "DE.Views.ParagraphSettingsAdvanced.textAtLeast": "至少", "DE.Views.ParagraphSettingsAdvanced.textAuto": "倍数", "DE.Views.ParagraphSettingsAdvanced.textBackColor": "背景颜色", "DE.Views.ParagraphSettingsAdvanced.textBodyText": "基本文本", "DE.Views.ParagraphSettingsAdvanced.textBorderColor": "边框颜色", "DE.Views.ParagraphSettingsAdvanced.textBorderDesc": "点击图表或使用按钮选择边框并将选择的样式应用于它们", "DE.Views.ParagraphSettingsAdvanced.textBorderWidth": "边框大小", "DE.Views.ParagraphSettingsAdvanced.textBottom": "底部", "DE.Views.ParagraphSettingsAdvanced.textCentered": "居中的", "DE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "字符间距", "DE.Views.ParagraphSettingsAdvanced.textContext": "上下文的", "DE.Views.ParagraphSettingsAdvanced.textContextDiscret": "上下文和任意的", "DE.Views.ParagraphSettingsAdvanced.textContextHistDiscret": "上下文、历史和任意的", "DE.Views.ParagraphSettingsAdvanced.textContextHistorical": "上下文和历史的", "DE.Views.ParagraphSettingsAdvanced.textDefault": "默认选项", "DE.Views.ParagraphSettingsAdvanced.textDiscret": "任意的", "DE.Views.ParagraphSettingsAdvanced.textEffects": "效果", "DE.Views.ParagraphSettingsAdvanced.textExact": "精确", "DE.Views.ParagraphSettingsAdvanced.textFirstLine": "首行缩进", "DE.Views.ParagraphSettingsAdvanced.textHanging": "悬挂缩进", "DE.Views.ParagraphSettingsAdvanced.textHistorical": "历史的", "DE.Views.ParagraphSettingsAdvanced.textHistoricalDiscret": "历史和任意的", "DE.Views.ParagraphSettingsAdvanced.textJustified": "正当", "DE.Views.ParagraphSettingsAdvanced.textLeader": "引导线", "DE.Views.ParagraphSettingsAdvanced.textLeft": "左", "DE.Views.ParagraphSettingsAdvanced.textLevel": "级别", "DE.Views.ParagraphSettingsAdvanced.textLigatures": "连字", "DE.Views.ParagraphSettingsAdvanced.textNone": "无", "DE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "（无）", "DE.Views.ParagraphSettingsAdvanced.textOpenType": "OpenType 功能", "DE.Views.ParagraphSettingsAdvanced.textPosition": "位置", "DE.Views.ParagraphSettingsAdvanced.textRemove": "删除", "DE.Views.ParagraphSettingsAdvanced.textRemoveAll": "删除所有", "DE.Views.ParagraphSettingsAdvanced.textRight": "对", "DE.Views.ParagraphSettingsAdvanced.textSet": "指定", "DE.Views.ParagraphSettingsAdvanced.textSpacing": "间距", "DE.Views.ParagraphSettingsAdvanced.textStandard": "仅限标准的", "DE.Views.ParagraphSettingsAdvanced.textStandardContext": "标准和上下文的", "DE.Views.ParagraphSettingsAdvanced.textStandardContextDiscret": "标准、上下文和任意的", "DE.Views.ParagraphSettingsAdvanced.textStandardContextHist": "标准、上下文和历史的", "DE.Views.ParagraphSettingsAdvanced.textStandardDiscret": "标准和任意的", "DE.Views.ParagraphSettingsAdvanced.textStandardHistDiscret": "标准、历史和任意的", "DE.Views.ParagraphSettingsAdvanced.textStandardHistorical": "标准和历史的", "DE.Views.ParagraphSettingsAdvanced.textTabCenter": "中心", "DE.Views.ParagraphSettingsAdvanced.textTabLeft": "左", "DE.Views.ParagraphSettingsAdvanced.textTabPosition": "标签位置", "DE.Views.ParagraphSettingsAdvanced.textTabRight": "右", "DE.Views.ParagraphSettingsAdvanced.textTitle": "段落 - 高级设置", "DE.Views.ParagraphSettingsAdvanced.textTop": "顶部", "DE.Views.ParagraphSettingsAdvanced.tipAll": "设置外边框和所有内线", "DE.Views.ParagraphSettingsAdvanced.tipBottom": "仅设置底部边框", "DE.Views.ParagraphSettingsAdvanced.tipInner": "仅设置水平内线", "DE.Views.ParagraphSettingsAdvanced.tipLeft": "仅设置左边框", "DE.Views.ParagraphSettingsAdvanced.tipNone": "设置无边框", "DE.Views.ParagraphSettingsAdvanced.tipOuter": "仅限外部边框", "DE.Views.ParagraphSettingsAdvanced.tipRight": "设置右边框", "DE.Views.ParagraphSettingsAdvanced.tipTop": "仅限顶部边框", "DE.Views.ParagraphSettingsAdvanced.txtAutoText": "自动", "DE.Views.ParagraphSettingsAdvanced.txtNoBorders": "没有边框", "DE.Views.PrintWithPreview.textMarginsLast": "上次的自定义", "DE.Views.PrintWithPreview.textMarginsModerate": "中等", "DE.Views.PrintWithPreview.textMarginsNarrow": "窄", "DE.Views.PrintWithPreview.textMarginsNormal": "常规", "DE.Views.PrintWithPreview.textMarginsUsNormal": "美国常规", "DE.Views.PrintWithPreview.textMarginsWide": "宽", "DE.Views.PrintWithPreview.txtAllPages": "所有页面", "DE.Views.PrintWithPreview.txtBottom": "底部", "DE.Views.PrintWithPreview.txtCurrentPage": "当前页面", "DE.Views.PrintWithPreview.txtCustom": "自定义", "DE.Views.PrintWithPreview.txtCustomPages": "自定义打印", "DE.Views.PrintWithPreview.txtLandscape": "横向", "DE.Views.PrintWithPreview.txtLeft": "左侧", "DE.Views.PrintWithPreview.txtMargins": "页边距", "DE.Views.PrintWithPreview.txtOf": "/ {0}", "DE.Views.PrintWithPreview.txtPage": "页面", "DE.Views.PrintWithPreview.txtPageNumInvalid": "页码无效", "DE.Views.PrintWithPreview.txtPageOrientation": "页面方向", "DE.Views.PrintWithPreview.txtPages": "页面", "DE.Views.PrintWithPreview.txtPageSize": "页面大小", "DE.Views.PrintWithPreview.txtPortrait": "纵向", "DE.Views.PrintWithPreview.txtPrint": "打印", "DE.Views.PrintWithPreview.txtPrintPdf": "打印为 PDF 格式", "DE.Views.PrintWithPreview.txtPrintRange": "打印范围", "DE.Views.PrintWithPreview.txtRight": "右侧", "DE.Views.PrintWithPreview.txtSelection": "选择", "DE.Views.PrintWithPreview.txtTop": "顶部", "DE.Views.ProtectDialog.textComments": "评论", "DE.Views.ProtectDialog.textForms": "填写表单", "DE.Views.ProtectDialog.textReview": "修订", "DE.Views.ProtectDialog.textView": "不允许任何更改（只读）", "DE.Views.ProtectDialog.txtAllow": "仅允许在文档中进行此类型的编辑", "DE.Views.ProtectDialog.txtIncorrectPwd": "确认的密码与先前输入的不一致。", "DE.Views.ProtectDialog.txtLimit": "密码限制为15个字符", "DE.Views.ProtectDialog.txtOptional": "可选", "DE.Views.ProtectDialog.txtPassword": "密码", "DE.Views.ProtectDialog.txtProtect": "保护", "DE.Views.ProtectDialog.txtRepeat": "重复输入密码", "DE.Views.ProtectDialog.txtTitle": "保护", "DE.Views.ProtectDialog.txtWarning": "警告: 如果丢失或忘记密码，则无法将其恢复。请妥善保存。", "DE.Views.RightMenu.txtChartSettings": "图表设置", "DE.Views.RightMenu.txtFormSettings": "表单设置", "DE.Views.RightMenu.txtHeaderFooterSettings": "页眉和页脚设置", "DE.Views.RightMenu.txtImageSettings": "图像设置", "DE.Views.RightMenu.txtMailMergeSettings": "邮件合并设置", "DE.Views.RightMenu.txtParagraphSettings": "段落设置", "DE.Views.RightMenu.txtShapeSettings": "形状设置", "DE.Views.RightMenu.txtSignatureSettings": "签名设置", "DE.Views.RightMenu.txtTableSettings": "表设置", "DE.Views.RightMenu.txtTextArtSettings": "艺术字设置", "DE.Views.RoleDeleteDlg.textLabel": "要删除这个角色，请把与之相关的字段移到另一个角色领域中。", "DE.Views.RoleDeleteDlg.textSelect": "为外地合并角色选择", "DE.Views.RoleDeleteDlg.textTitle": "删除角色", "DE.Views.RoleEditDlg.errNameExists": "具有同样名称的角色已经存在。", "DE.Views.RoleEditDlg.textEmptyError": "角色名称不得为空。", "DE.Views.RoleEditDlg.textName": "角色名称", "DE.Views.RoleEditDlg.textNoHighlight": "无高亮", "DE.Views.RoleEditDlg.txtTitleEdit": "编辑角色", "DE.Views.RoleEditDlg.txtTitleNew": "创建新角色", "DE.Views.RolesManagerDlg.textAnyone": "任何人", "DE.Views.RolesManagerDlg.textDelete": "删除", "DE.Views.RolesManagerDlg.textDeleteLast": "您确定您要删除{0}角色吗？<br>删除之后，默认角色将自动创建。", "DE.Views.RolesManagerDlg.textDescription": "添加角色并设置填写者接收和签署文件的顺序", "DE.Views.RolesManagerDlg.textDown": "角色下移", "DE.Views.RolesManagerDlg.textEdit": "编辑", "DE.Views.RolesManagerDlg.textEmpty": "还没有创建任何角色。<br>至少创建一个角色，它就会在这个领域出现。", "DE.Views.RolesManagerDlg.textNew": "新建", "DE.Views.RolesManagerDlg.textUp": "角色上移", "DE.Views.RolesManagerDlg.txtTitle": "管理角色", "DE.Views.RolesManagerDlg.warnCantDelete": "您不得删除这个角色，它有相关的字段。", "DE.Views.RolesManagerDlg.warnDelete": "您确定您要删除{0}角色吗？", "DE.Views.SaveFormDlg.saveButtonText": "保存", "DE.Views.SaveFormDlg.textAnyone": "任何人", "DE.Views.SaveFormDlg.textDescription": "当另存为 oform 时，只有带字段的角色被添加到填充列表中。", "DE.Views.SaveFormDlg.textEmpty": "与该字段相关的角色不存在。", "DE.Views.SaveFormDlg.textFill": "填写列表", "DE.Views.SaveFormDlg.txtTitle": "另存为表单", "DE.Views.ShapeSettings.strBackground": "背景颜色", "DE.Views.ShapeSettings.strChange": "更改自动形状", "DE.Views.ShapeSettings.strColor": "颜色", "DE.Views.ShapeSettings.strFill": "填满", "DE.Views.ShapeSettings.strForeground": "前景色", "DE.Views.ShapeSettings.strPattern": "模式", "DE.Views.ShapeSettings.strShadow": "显示阴影", "DE.Views.ShapeSettings.strSize": "大小", "DE.Views.ShapeSettings.strStroke": "线条", "DE.Views.ShapeSettings.strTransparency": "不透明度", "DE.Views.ShapeSettings.strType": "按类型查看", "DE.Views.ShapeSettings.textAdvanced": "显示高级设置", "DE.Views.ShapeSettings.textAngle": "角度", "DE.Views.ShapeSettings.textBorderSizeErr": "输入的值不正确。<br>请输入介于0 pt和1584 pt之间的值。", "DE.Views.ShapeSettings.textColor": "颜色填充", "DE.Views.ShapeSettings.textDirection": "方向", "DE.Views.ShapeSettings.textEmptyPattern": "无图案", "DE.Views.ShapeSettings.textFlip": "翻转", "DE.Views.ShapeSettings.textFromFile": "从文件导入", "DE.Views.ShapeSettings.textFromStorage": "来自存储设备", "DE.Views.ShapeSettings.textFromUrl": "从URL", "DE.Views.ShapeSettings.textGradient": "渐变点", "DE.Views.ShapeSettings.textGradientFill": "渐变填充", "DE.Views.ShapeSettings.textHint270": "逆时针旋转90°", "DE.Views.ShapeSettings.textHint90": "顺时针旋转90°", "DE.Views.ShapeSettings.textHintFlipH": "水平翻转", "DE.Views.ShapeSettings.textHintFlipV": "垂直翻转", "DE.Views.ShapeSettings.textImageTexture": "图片或纹理", "DE.Views.ShapeSettings.textLinear": "线性", "DE.Views.ShapeSettings.textNoFill": "没有填充", "DE.Views.ShapeSettings.textPatternFill": "模式", "DE.Views.ShapeSettings.textPosition": "位置", "DE.Views.ShapeSettings.textRadial": "径向", "DE.Views.ShapeSettings.textRecentlyUsed": "最近使用的", "DE.Views.ShapeSettings.textRotate90": "旋转90°", "DE.Views.ShapeSettings.textRotation": "旋转", "DE.Views.ShapeSettings.textSelectImage": "选取图片", "DE.Views.ShapeSettings.textSelectTexture": "请选择", "DE.Views.ShapeSettings.textStretch": "伸展", "DE.Views.ShapeSettings.textStyle": "类型", "DE.Views.ShapeSettings.textTexture": "从纹理", "DE.Views.ShapeSettings.textTile": "瓦", "DE.Views.ShapeSettings.textWrap": "包裹风格", "DE.Views.ShapeSettings.tipAddGradientPoint": "新增渐变点", "DE.Views.ShapeSettings.tipRemoveGradientPoint": "删除渐变点", "DE.Views.ShapeSettings.txtBehind": "衬于​​文字下方", "DE.Views.ShapeSettings.txtBrownPaper": "牛皮纸", "DE.Views.ShapeSettings.txtCanvas": "画布", "DE.Views.ShapeSettings.txtCarton": "纸板", "DE.Views.ShapeSettings.txtDarkFabric": "深色织物", "DE.Views.ShapeSettings.txtGrain": "颗粒", "DE.Views.ShapeSettings.txtGranite": "花岗岩", "DE.Views.ShapeSettings.txtGreyPaper": "灰纸", "DE.Views.ShapeSettings.txtInFront": "浮于文字上方", "DE.Views.ShapeSettings.txtInline": "嵌入型​​", "DE.Views.ShapeSettings.txtKnit": "针织", "DE.Views.ShapeSettings.txtLeather": "真皮", "DE.Views.ShapeSettings.txtNoBorders": "没有线", "DE.Views.ShapeSettings.txtPapyrus": "纸莎草", "DE.Views.ShapeSettings.txtSquare": "四周型环绕", "DE.Views.ShapeSettings.txtThrough": "穿越型环绕", "DE.Views.ShapeSettings.txtTight": "紧密型环绕", "DE.Views.ShapeSettings.txtTopAndBottom": "上下型环绕", "DE.Views.ShapeSettings.txtWood": "木头", "DE.Views.SignatureSettings.notcriticalErrorTitle": "警告", "DE.Views.SignatureSettings.strDelete": "删除签名", "DE.Views.SignatureSettings.strDetails": "签名详情", "DE.Views.SignatureSettings.strInvalid": "签名无效", "DE.Views.SignatureSettings.strRequested": "请求的签名", "DE.Views.SignatureSettings.strSetup": "签名设置", "DE.Views.SignatureSettings.strSign": "签名", "DE.Views.SignatureSettings.strSignature": "签名", "DE.Views.SignatureSettings.strSigner": "签名者", "DE.Views.SignatureSettings.strValid": "有效签名", "DE.Views.SignatureSettings.txtContinueEditing": "继续编辑", "DE.Views.SignatureSettings.txtEditWarning": "编辑将删除文档中的签名。<br>您确定要继续吗？", "DE.Views.SignatureSettings.txtRemoveWarning": "要删除该签名吗？<br>这一操作不能被撤销。", "DE.Views.SignatureSettings.txtRequestedSignatures": "此文档需要签名。", "DE.Views.SignatureSettings.txtSigned": "有效签名已添加到文档中。该文档受到保护，无法编辑。", "DE.Views.SignatureSettings.txtSignedInvalid": "文档中的某些数字签名无效或无法验证。该文档受到保护，无法编辑。", "DE.Views.Statusbar.goToPageText": "转到页面", "DE.Views.Statusbar.pageIndexText": "第{0}页共{1}页", "DE.Views.Statusbar.tipFitPage": "适合页面", "DE.Views.Statusbar.tipFitWidth": "适合宽度", "DE.Views.Statusbar.tipHandTool": "移动工具", "DE.Views.Statusbar.tipSelectTool": "选取工具", "DE.Views.Statusbar.tipSetLang": "设置文本语言", "DE.Views.Statusbar.tipZoomFactor": "放大", "DE.Views.Statusbar.tipZoomIn": "放大", "DE.Views.Statusbar.tipZoomOut": "缩小", "DE.Views.Statusbar.txtPageNumInvalid": "页码无效", "DE.Views.Statusbar.txtPages": "页面", "DE.Views.Statusbar.txtParagraphs": "段落", "DE.Views.Statusbar.txtSpaces": "字符数（计空格）", "DE.Views.Statusbar.txtSymbols": "符号", "DE.Views.Statusbar.txtWordCount": "字数统计", "DE.Views.Statusbar.txtWords": "单词", "DE.Views.StyleTitleDialog.textHeader": "新建样式", "DE.Views.StyleTitleDialog.textNextStyle": "下一段款式", "DE.Views.StyleTitleDialog.textTitle": "标题", "DE.Views.StyleTitleDialog.txtEmpty": "这是必填栏", "DE.Views.StyleTitleDialog.txtNotEmpty": "区域不能为空", "DE.Views.StyleTitleDialog.txtSameAs": "与创建的新样式相同", "DE.Views.TableFormulaDialog.textBookmark": "粘贴书签", "DE.Views.TableFormulaDialog.textFormat": "数字格式", "DE.Views.TableFormulaDialog.textFormula": "公式", "DE.Views.TableFormulaDialog.textInsertFunction": "粘贴函数", "DE.Views.TableFormulaDialog.textTitle": "公式设置", "DE.Views.TableOfContentsSettings.strAlign": "右对齐页码", "DE.Views.TableOfContentsSettings.strFullCaption": "包括标签和编号", "DE.Views.TableOfContentsSettings.strLinks": "将目录格式化为链接", "DE.Views.TableOfContentsSettings.strLinksOF": "将图片表格格式化为链接", "DE.Views.TableOfContentsSettings.strShowPages": "显示页码", "DE.Views.TableOfContentsSettings.textBuildTable": "构建目录起始", "DE.Views.TableOfContentsSettings.textBuildTableOF": "构建图表目录起始", "DE.Views.TableOfContentsSettings.textEquation": "方程式", "DE.Views.TableOfContentsSettings.textFigure": "图", "DE.Views.TableOfContentsSettings.textLeader": "引导线", "DE.Views.TableOfContentsSettings.textLevel": "级别", "DE.Views.TableOfContentsSettings.textLevels": "级别", "DE.Views.TableOfContentsSettings.textNone": "无", "DE.Views.TableOfContentsSettings.textRadioCaption": "标题", "DE.Views.TableOfContentsSettings.textRadioLevels": "大纲级别", "DE.Views.TableOfContentsSettings.textRadioStyle": "样式", "DE.Views.TableOfContentsSettings.textRadioStyles": "所选样式", "DE.Views.TableOfContentsSettings.textStyle": "样式", "DE.Views.TableOfContentsSettings.textStyles": "样式", "DE.Views.TableOfContentsSettings.textTable": "表格", "DE.Views.TableOfContentsSettings.textTitle": "目录", "DE.Views.TableOfContentsSettings.textTitleTOF": "图表目录", "DE.Views.TableOfContentsSettings.txtCentered": "居中", "DE.Views.TableOfContentsSettings.txtClassic": "古典", "DE.Views.TableOfContentsSettings.txtCurrent": "当前", "DE.Views.TableOfContentsSettings.txtDistinctive": "可区分", "DE.Views.TableOfContentsSettings.txtFormal": "正式", "DE.Views.TableOfContentsSettings.txtModern": "现代", "DE.Views.TableOfContentsSettings.txtOnline": "在线", "DE.Views.TableOfContentsSettings.txtSimple": "简单", "DE.Views.TableOfContentsSettings.txtStandard": "标准", "DE.Views.TableSettings.deleteColumnText": "删除列", "DE.Views.TableSettings.deleteRowText": "删除行", "DE.Views.TableSettings.deleteTableText": "删除表", "DE.Views.TableSettings.insertColumnLeftText": "在左侧插入列", "DE.Views.TableSettings.insertColumnRightText": "向右侧插入列", "DE.Views.TableSettings.insertRowAboveText": "插入行以上", "DE.Views.TableSettings.insertRowBelowText": "在下面插入行", "DE.Views.TableSettings.mergeCellsText": "合并单元格", "DE.Views.TableSettings.selectCellText": "选择单元格", "DE.Views.TableSettings.selectColumnText": "选择列", "DE.Views.TableSettings.selectRowText": "选择行", "DE.Views.TableSettings.selectTableText": "选择表", "DE.Views.TableSettings.splitCellsText": "拆分单元格", "DE.Views.TableSettings.splitCellTitleText": "拆分单元格", "DE.Views.TableSettings.strRepeatRow": "在每页顶部重复标题行", "DE.Views.TableSettings.textAddFormula": "添加公式", "DE.Views.TableSettings.textAdvanced": "显示高级设置", "DE.Views.TableSettings.textBackColor": "背景颜色", "DE.Views.TableSettings.textBanded": "带状", "DE.Views.TableSettings.textBorderColor": "颜色", "DE.Views.TableSettings.textBorders": "边框风格", "DE.Views.TableSettings.textCellSize": "行&列的大小", "DE.Views.TableSettings.textColumns": "列", "DE.Views.TableSettings.textConvert": "将表格转为文本", "DE.Views.TableSettings.textDistributeCols": "分布列", "DE.Views.TableSettings.textDistributeRows": "分布行", "DE.Views.TableSettings.textEdit": "行和列", "DE.Views.TableSettings.textEmptyTemplate": "没有模板", "DE.Views.TableSettings.textFirst": "第一", "DE.Views.TableSettings.textHeader": "头", "DE.Views.TableSettings.textHeight": "高度", "DE.Views.TableSettings.textLast": "最后", "DE.Views.TableSettings.textRows": "行", "DE.Views.TableSettings.textSelectBorders": "选择您要更改应用样式的边框", "DE.Views.TableSettings.textTemplate": "从模板中选择", "DE.Views.TableSettings.textTotal": "总计", "DE.Views.TableSettings.textWidth": "宽度", "DE.Views.TableSettings.tipAll": "设置外边框和所有内线", "DE.Views.TableSettings.tipBottom": "仅设置外底边框", "DE.Views.TableSettings.tipInner": "仅设置内线", "DE.Views.TableSettings.tipInnerHor": "仅设置水平内线", "DE.Views.TableSettings.tipInnerVert": "仅设置垂直内线", "DE.Views.TableSettings.tipLeft": "仅限外部左边框", "DE.Views.TableSettings.tipNone": "设置无边框", "DE.Views.TableSettings.tipOuter": "仅限外部边框", "DE.Views.TableSettings.tipRight": "仅设置外边界", "DE.Views.TableSettings.tipTop": "仅限外部边框", "DE.Views.TableSettings.txtGroupTable_BorderedAndLined": "带边框及线条的表格", "DE.Views.TableSettings.txtGroupTable_Custom": "自定义", "DE.Views.TableSettings.txtGroupTable_Grid": "网格表", "DE.Views.TableSettings.txtGroupTable_List": "清单表", "DE.Views.TableSettings.txtGroupTable_Plain": "普通表格", "DE.Views.TableSettings.txtNoBorders": "没有边框", "DE.Views.TableSettings.txtTable_Accent": "强调", "DE.Views.TableSettings.txtTable_Bordered": "带边框的", "DE.Views.TableSettings.txtTable_BorderedAndLined": "带边框及线条的", "DE.Views.TableSettings.txtTable_Colorful": "多彩", "DE.Views.TableSettings.txtTable_Dark": "黑暗的", "DE.Views.TableSettings.txtTable_GridTable": "表格网格", "DE.Views.TableSettings.txtTable_Light": "光", "DE.Views.TableSettings.txtTable_Lined": "带线条的", "DE.Views.TableSettings.txtTable_ListTable": "列表表", "DE.Views.TableSettings.txtTable_PlainTable": "简单的表", "DE.Views.TableSettings.txtTable_TableGrid": "表格的网格", "DE.Views.TableSettingsAdvanced.textAlign": "对齐", "DE.Views.TableSettingsAdvanced.textAlignment": "对齐", "DE.Views.TableSettingsAdvanced.textAllowSpacing": "细胞之间的距离", "DE.Views.TableSettingsAdvanced.textAlt": "替代文本", "DE.Views.TableSettingsAdvanced.textAltDescription": "说明", "DE.Views.TableSettingsAdvanced.textAltTip": "视觉对象信息的替代基于文本的表示法，将要向有视觉或认知障碍人阅读，以帮助他们更好地了解图像、自选图形、图表或表格中的那些信息。", "DE.Views.TableSettingsAdvanced.textAltTitle": "标题", "DE.Views.TableSettingsAdvanced.textAnchorText": "文本", "DE.Views.TableSettingsAdvanced.textAutofit": "自动调整大小以适应内容", "DE.Views.TableSettingsAdvanced.textBackColor": "元数据背景", "DE.Views.TableSettingsAdvanced.textBelow": "下面", "DE.Views.TableSettingsAdvanced.textBorderColor": "边框颜色", "DE.Views.TableSettingsAdvanced.textBorderDesc": "点击图表或使用按钮选择边框并将选择的样式应用于它们", "DE.Views.TableSettingsAdvanced.textBordersBackgroung": "边界与背景", "DE.Views.TableSettingsAdvanced.textBorderWidth": "边框大小", "DE.Views.TableSettingsAdvanced.textBottom": "底部", "DE.Views.TableSettingsAdvanced.textCellOptions": "单元格选项", "DE.Views.TableSettingsAdvanced.textCellProps": "元件", "DE.Views.TableSettingsAdvanced.textCellSize": "单元格大小", "DE.Views.TableSettingsAdvanced.textCenter": "中心", "DE.Views.TableSettingsAdvanced.textCenterTooltip": "中心", "DE.Views.TableSettingsAdvanced.textCheckMargins": "使用默认页边距", "DE.Views.TableSettingsAdvanced.textDefaultMargins": "默认的单元格边距", "DE.Views.TableSettingsAdvanced.textDistance": "文字距离", "DE.Views.TableSettingsAdvanced.textHorizontal": "水平的", "DE.Views.TableSettingsAdvanced.textIndLeft": "从左缩进", "DE.Views.TableSettingsAdvanced.textLeft": "左", "DE.Views.TableSettingsAdvanced.textLeftTooltip": "左", "DE.Views.TableSettingsAdvanced.textMargin": "边", "DE.Views.TableSettingsAdvanced.textMargins": "元数据边缘", "DE.Views.TableSettingsAdvanced.textMeasure": "测量", "DE.Views.TableSettingsAdvanced.textMove": "用文本移动对象", "DE.Views.TableSettingsAdvanced.textOnlyCells": "仅适用于选定的单元格", "DE.Views.TableSettingsAdvanced.textOptions": "选项", "DE.Views.TableSettingsAdvanced.textOverlap": "允许重叠", "DE.Views.TableSettingsAdvanced.textPage": "页面", "DE.Views.TableSettingsAdvanced.textPosition": "位置", "DE.Views.TableSettingsAdvanced.textPrefWidth": "首选宽度", "DE.Views.TableSettingsAdvanced.textPreview": "预览", "DE.Views.TableSettingsAdvanced.textRelative": "关系到", "DE.Views.TableSettingsAdvanced.textRight": "对", "DE.Views.TableSettingsAdvanced.textRightOf": "在 - 的右边", "DE.Views.TableSettingsAdvanced.textRightTooltip": "对", "DE.Views.TableSettingsAdvanced.textTable": "表格", "DE.Views.TableSettingsAdvanced.textTableBackColor": "表背景", "DE.Views.TableSettingsAdvanced.textTablePosition": "表位置", "DE.Views.TableSettingsAdvanced.textTableSize": "表格大小", "DE.Views.TableSettingsAdvanced.textTitle": "表-高级设置", "DE.Views.TableSettingsAdvanced.textTop": "顶部", "DE.Views.TableSettingsAdvanced.textVertical": "垂直", "DE.Views.TableSettingsAdvanced.textWidth": "宽度", "DE.Views.TableSettingsAdvanced.textWidthSpaces": "宽度和间距", "DE.Views.TableSettingsAdvanced.textWrap": "文字包装", "DE.Views.TableSettingsAdvanced.textWrapNoneTooltip": "内联表", "DE.Views.TableSettingsAdvanced.textWrapParallelTooltip": "流程表", "DE.Views.TableSettingsAdvanced.textWrappingStyle": "包裹风格", "DE.Views.TableSettingsAdvanced.textWrapText": "文字换行", "DE.Views.TableSettingsAdvanced.tipAll": "设置外边框和所有内线", "DE.Views.TableSettingsAdvanced.tipCellAll": "仅为内部单元设置边框", "DE.Views.TableSettingsAdvanced.tipCellInner": "设置内部单元格的垂直和水平线", "DE.Views.TableSettingsAdvanced.tipCellOuter": "仅为内部细胞设定外部边界", "DE.Views.TableSettingsAdvanced.tipInner": "仅设置内线", "DE.Views.TableSettingsAdvanced.tipNone": "设置无边框", "DE.Views.TableSettingsAdvanced.tipOuter": "仅限外部边框", "DE.Views.TableSettingsAdvanced.tipTableOuterCellAll": "设置所有内部细胞的外边界和边界。", "DE.Views.TableSettingsAdvanced.tipTableOuterCellInner": "设置外部边界和内部细胞的垂直和水平线", "DE.Views.TableSettingsAdvanced.tipTableOuterCellOuter": "为内细胞设置表外边界和外边界", "DE.Views.TableSettingsAdvanced.txtCm": "厘米", "DE.Views.TableSettingsAdvanced.txtInch": "寸", "DE.Views.TableSettingsAdvanced.txtNoBorders": "没有边框", "DE.Views.TableSettingsAdvanced.txtPercent": "百分", "DE.Views.TableSettingsAdvanced.txtPt": "点", "DE.Views.TableToTextDialog.textEmpty": "必须为自定义的分隔符键入一个字符", "DE.Views.TableToTextDialog.textNested": "转换嵌套表格", "DE.Views.TableToTextDialog.textOther": "其他", "DE.Views.TableToTextDialog.textPara": "自然段符号", "DE.Views.TableToTextDialog.textSemicolon": "分号", "DE.Views.TableToTextDialog.textSeparator": "以何方法分开文本？", "DE.Views.TableToTextDialog.textTab": "标签", "DE.Views.TableToTextDialog.textTitle": "将表格转为文本", "DE.Views.TextArtSettings.strColor": "颜色", "DE.Views.TextArtSettings.strFill": "填满", "DE.Views.TextArtSettings.strSize": "大小", "DE.Views.TextArtSettings.strStroke": "线条", "DE.Views.TextArtSettings.strTransparency": "不透明度", "DE.Views.TextArtSettings.strType": "按类型查看", "DE.Views.TextArtSettings.textAngle": "角度", "DE.Views.TextArtSettings.textBorderSizeErr": "输入的值不正确。<br>请输入介于0 pt和1584 pt之间的值。", "DE.Views.TextArtSettings.textColor": "颜色填充", "DE.Views.TextArtSettings.textDirection": "方向", "DE.Views.TextArtSettings.textGradient": "渐变点", "DE.Views.TextArtSettings.textGradientFill": "渐变填充", "DE.Views.TextArtSettings.textLinear": "线性", "DE.Views.TextArtSettings.textNoFill": "没有填充", "DE.Views.TextArtSettings.textPosition": "位置", "DE.Views.TextArtSettings.textRadial": "径向", "DE.Views.TextArtSettings.textSelectTexture": "请选择", "DE.Views.TextArtSettings.textStyle": "类型", "DE.Views.TextArtSettings.textTemplate": "模板", "DE.Views.TextArtSettings.textTransform": "跟踪变化", "DE.Views.TextArtSettings.tipAddGradientPoint": "新增渐变点", "DE.Views.TextArtSettings.tipRemoveGradientPoint": "删除渐变点", "DE.Views.TextArtSettings.txtNoBorders": "无线条", "DE.Views.TextToTableDialog.textAutofit": "自动适应行为", "DE.Views.TextToTableDialog.textColumns": "列", "DE.Views.TextToTableDialog.textContents": "自动适应内容", "DE.Views.TextToTableDialog.textEmpty": "必须为自定义的分隔符键入一个字符", "DE.Views.TextToTableDialog.textFixed": "固定列宽", "DE.Views.TextToTableDialog.textOther": "其他", "DE.Views.TextToTableDialog.textPara": "段落", "DE.Views.TextToTableDialog.textRows": "行", "DE.Views.TextToTableDialog.textSemicolon": "分号", "DE.Views.TextToTableDialog.textSeparator": "文本分开位置", "DE.Views.TextToTableDialog.textTab": "标签", "DE.Views.TextToTableDialog.textTableSize": "表格大小", "DE.Views.TextToTableDialog.textTitle": "将文本转为表格", "DE.Views.TextToTableDialog.textWindow": "自动适应窗口", "DE.Views.TextToTableDialog.txtAutoText": "自动", "DE.Views.Toolbar.capBtnAddComment": "添加批注", "DE.Views.Toolbar.capBtnBlankPage": "空白页", "DE.Views.Toolbar.capBtnColumns": "列", "DE.Views.Toolbar.capBtnComment": "批注", "DE.Views.Toolbar.capBtnDateTime": "日期、时间", "DE.Views.Toolbar.capBtnInsChart": "图表", "DE.Views.Toolbar.capBtnInsControls": "内容控件", "DE.Views.Toolbar.capBtnInsDropcap": "首字下沉 ", "DE.Views.Toolbar.capBtnInsEquation": "方程式", "DE.Views.Toolbar.capBtnInsHeader": "页眉&页脚", "DE.Views.Toolbar.capBtnInsImage": "图片", "DE.Views.Toolbar.capBtnInsPagebreak": "分隔符", "DE.Views.Toolbar.capBtnInsShape": "形状", "DE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "DE.Views.Toolbar.capBtnInsSymbol": "符号", "DE.Views.Toolbar.capBtnInsTable": "表格", "DE.Views.Toolbar.capBtnInsTextart": "艺术字", "DE.Views.Toolbar.capBtnInsTextbox": "文本框", "DE.Views.Toolbar.capBtnLineNumbers": "行号", "DE.Views.Toolbar.capBtnMargins": "页边距", "DE.Views.Toolbar.capBtnPageOrient": "方向", "DE.Views.Toolbar.capBtnPageSize": "大小", "DE.Views.Toolbar.capBtnWatermark": "水印", "DE.Views.Toolbar.capImgAlign": "对齐", "DE.Views.Toolbar.capImgBackward": "下移一层", "DE.Views.Toolbar.capImgForward": "上移一层", "DE.Views.Toolbar.capImgGroup": "分组", "DE.Views.Toolbar.capImgWrapping": "环绕", "DE.Views.Toolbar.mniCapitalizeWords": "将每个单词大写", "DE.Views.Toolbar.mniCustomTable": "插入自定义表", "DE.Views.Toolbar.mniDrawTable": "绘制表", "DE.Views.Toolbar.mniEditControls": "控制设置", "DE.Views.Toolbar.mniEditDropCap": "首字下沉设置", "DE.Views.Toolbar.mniEditFooter": "编辑页脚", "DE.Views.Toolbar.mniEditHeader": "编辑页眉", "DE.Views.Toolbar.mniEraseTable": "删除表", "DE.Views.Toolbar.mniFromFile": "从文件导入", "DE.Views.Toolbar.mniFromStorage": "来自存储设备", "DE.Views.Toolbar.mniFromUrl": "从URL", "DE.Views.Toolbar.mniHiddenBorders": "隐藏表边框", "DE.Views.Toolbar.mniHiddenChars": "不打印字符", "DE.Views.Toolbar.mniHighlightControls": "突出显示设置", "DE.Views.Toolbar.mniImageFromFile": "图片文件", "DE.Views.Toolbar.mniImageFromStorage": "图片来自存储", "DE.Views.Toolbar.mniImageFromUrl": "图片来自网络", "DE.Views.Toolbar.mniInsertSSE": "插入表格", "DE.Views.Toolbar.mniLowerCase": "小写", "DE.Views.Toolbar.mniRemoveFooter": "移除页脚", "DE.Views.Toolbar.mniRemoveHeader": "移除页头", "DE.Views.Toolbar.mniSentenceCase": "句子大小写。", "DE.Views.Toolbar.mniTextToTable": "将文本转为表格", "DE.Views.Toolbar.mniToggleCase": "切换大小写", "DE.Views.Toolbar.mniUpperCase": "大写", "DE.Views.Toolbar.strMenuNoFill": "没有填充", "DE.Views.Toolbar.textAutoColor": "自动化的", "DE.Views.Toolbar.textBold": "加粗", "DE.Views.Toolbar.textBottom": "底部： ", "DE.Views.Toolbar.textChangeLevel": "修改列表的层级", "DE.Views.Toolbar.textCheckboxControl": "复选框", "DE.Views.Toolbar.textColumnsCustom": "自定义栏", "DE.Views.Toolbar.textColumnsLeft": "左", "DE.Views.Toolbar.textColumnsOne": "一", "DE.Views.Toolbar.textColumnsRight": "右", "DE.Views.Toolbar.textColumnsThree": "三", "DE.Views.Toolbar.textColumnsTwo": "二", "DE.Views.Toolbar.textComboboxControl": "\n组合框", "DE.Views.Toolbar.textContinuous": "连续", "DE.Views.Toolbar.textContPage": "连续页", "DE.Views.Toolbar.textCustomLineNumbers": "行号选项", "DE.Views.Toolbar.textDateControl": "日期", "DE.Views.Toolbar.textDropdownControl": "下拉列表", "DE.Views.Toolbar.textEditWatermark": "自定义水印", "DE.Views.Toolbar.textEvenPage": "偶数页", "DE.Views.Toolbar.textInMargin": "在边际", "DE.Views.Toolbar.textInsColumnBreak": "插入列中断", "DE.Views.Toolbar.textInsertPageCount": "插入页数", "DE.Views.Toolbar.textInsertPageNumber": "插入页码", "DE.Views.Toolbar.textInsPageBreak": "插入分页符", "DE.Views.Toolbar.textInsSectionBreak": "插入分节符", "DE.Views.Toolbar.textInText": "在文本中", "DE.Views.Toolbar.textItalic": "斜体", "DE.Views.Toolbar.textLandscape": "横向", "DE.Views.Toolbar.textLeft": "左： ", "DE.Views.Toolbar.textListSettings": "列表设置", "DE.Views.Toolbar.textMarginsLast": "最后自定义", "DE.Views.Toolbar.textMarginsModerate": "中等", "DE.Views.Toolbar.textMarginsNarrow": "窄", "DE.Views.Toolbar.textMarginsNormal": "常规", "DE.Views.Toolbar.textMarginsUsNormal": "美国常规", "DE.Views.Toolbar.textMarginsWide": "宽", "DE.Views.Toolbar.textNewColor": "添加新的自定义颜色", "DE.Views.Toolbar.textNextPage": "下一页", "DE.Views.Toolbar.textNoHighlight": "无高亮", "DE.Views.Toolbar.textNone": "无", "DE.Views.Toolbar.textOddPage": "奇数页", "DE.Views.Toolbar.textPageMarginsCustom": "自定义边距", "DE.Views.Toolbar.textPageSizeCustom": "自定义页面大小", "DE.Views.Toolbar.textPictureControl": "图片", "DE.Views.Toolbar.textPlainControl": "纯文本", "DE.Views.Toolbar.textPortrait": "纵向", "DE.Views.Toolbar.textRemoveControl": "删除内容控件", "DE.Views.Toolbar.textRemWatermark": "删除水印", "DE.Views.Toolbar.textRestartEachPage": "重新启动每一页", "DE.Views.Toolbar.textRestartEachSection": "重新启动每一节", "DE.Views.Toolbar.textRichControl": "格式文本", "DE.Views.Toolbar.textRight": "右： ", "DE.Views.Toolbar.textStrikeout": "加删除线", "DE.Views.Toolbar.textStyleMenuDelete": "删除样式", "DE.Views.Toolbar.textStyleMenuDeleteAll": "删除所有自定义样式", "DE.Views.Toolbar.textStyleMenuNew": "新风格从选择", "DE.Views.Toolbar.textStyleMenuRestore": "恢复为默认", "DE.Views.Toolbar.textStyleMenuRestoreAll": "全部恢复为默认样式", "DE.Views.Toolbar.textStyleMenuUpdate": "从选择更新", "DE.Views.Toolbar.textSubscript": "下标", "DE.Views.Toolbar.textSuperscript": "上标", "DE.Views.Toolbar.textSuppressForCurrentParagraph": "压制当前段落", "DE.Views.Toolbar.textTabCollaboration": "协作", "DE.Views.Toolbar.textTabFile": "文件", "DE.Views.Toolbar.textTabHome": "主页", "DE.Views.Toolbar.textTabInsert": "插入", "DE.Views.Toolbar.textTabLayout": "布局", "DE.Views.Toolbar.textTabLinks": "引用", "DE.Views.Toolbar.textTabProtect": "保护", "DE.Views.Toolbar.textTabReview": "审阅", "DE.Views.Toolbar.textTabView": "视图", "DE.Views.Toolbar.textTitleError": "错误", "DE.Views.Toolbar.textToCurrent": "到当前位置", "DE.Views.Toolbar.textTop": "顶边： ", "DE.Views.Toolbar.textUnderline": "下划线", "DE.Views.Toolbar.tipAlignCenter": "居中对齐", "DE.Views.Toolbar.tipAlignJust": "正当", "DE.Views.Toolbar.tipAlignLeft": "左对齐", "DE.Views.Toolbar.tipAlignRight": "右对齐", "DE.Views.Toolbar.tipBack": "返回", "DE.Views.Toolbar.tipBlankPage": "插入空白页面", "DE.Views.Toolbar.tipChangeCase": "修改大小写", "DE.Views.Toolbar.tipChangeChart": "更改图表类型", "DE.Views.Toolbar.tipClearStyle": "清除样式", "DE.Views.Toolbar.tipColorSchemas": "更改配色方案", "DE.Views.Toolbar.tipColumns": "插入列", "DE.Views.Toolbar.tipControls": "插入内容控件", "DE.Views.Toolbar.tipCopy": "复制", "DE.Views.Toolbar.tipCopyStyle": "复制样式", "DE.Views.Toolbar.tipCut": "剪切", "DE.Views.Toolbar.tipDateTime": "插入当前日期和时间", "DE.Views.Toolbar.tipDecFont": "递减字体大小", "DE.Views.Toolbar.tipDecPrLeft": "减少缩进", "DE.Views.Toolbar.tipDropCap": "插入首字下沉 ", "DE.Views.Toolbar.tipEditHeader": "编辑标题或页脚", "DE.Views.Toolbar.tipFontColor": "字体颜色", "DE.Views.Toolbar.tipFontName": "字体", "DE.Views.Toolbar.tipFontSize": "字体大小", "DE.Views.Toolbar.tipHighlightColor": "颜色高亮", "DE.Views.Toolbar.tipImgAlign": "对齐对象", "DE.Views.Toolbar.tipImgGroup": "组合对象", "DE.Views.Toolbar.tipImgWrapping": "文字换行", "DE.Views.Toolbar.tipIncFont": "增加字体大小", "DE.Views.Toolbar.tipIncPrLeft": "增加缩进", "DE.Views.Toolbar.tipInsertChart": "插入图表", "DE.Views.Toolbar.tipInsertEquation": "插入方程", "DE.Views.Toolbar.tipInsertHorizontalText": "插入横排文本框", "DE.Views.Toolbar.tipInsertImage": "插入图片", "DE.Views.Toolbar.tipInsertNum": "插入页码", "DE.Views.Toolbar.tipInsertShape": "自动插入形状", "DE.Views.Toolbar.tipInsertSmartArt": "插入 SmartArt", "DE.Views.Toolbar.tipInsertSymbol": "插入符号", "DE.Views.Toolbar.tipInsertTable": "插入表", "DE.Views.Toolbar.tipInsertText": "插入文字", "DE.Views.Toolbar.tipInsertTextArt": "插入艺术字", "DE.Views.Toolbar.tipInsertVerticalText": "插入竖排文本框", "DE.Views.Toolbar.tipLineNumbers": "显示行号", "DE.Views.Toolbar.tipLineSpace": "段线间距", "DE.Views.Toolbar.tipMailRecepients": "邮件合并", "DE.Views.Toolbar.tipMarkers": "项目符号", "DE.Views.Toolbar.tipMarkersArrow": "箭头项目符号", "DE.Views.Toolbar.tipMarkersCheckmark": "选中标记项目符号", "DE.Views.Toolbar.tipMarkersDash": "划线项目符号", "DE.Views.Toolbar.tipMarkersFRhombus": "实心菱形项目符号", "DE.Views.Toolbar.tipMarkersFRound": "实心圆形项目符号", "DE.Views.Toolbar.tipMarkersFSquare": "实心方形项目符号", "DE.Views.Toolbar.tipMarkersHRound": "空心圆形项目符号", "DE.Views.Toolbar.tipMarkersStar": "星形项目符号", "DE.Views.Toolbar.tipMultiLevelArticl": "多层次编号的文章", "DE.Views.Toolbar.tipMultiLevelChapter": "多层次编号的章节", "DE.Views.Toolbar.tipMultiLevelHeadings": "多层次编号的标题", "DE.Views.Toolbar.tipMultiLevelHeadVarious": "多层次各种编号的标题", "DE.Views.Toolbar.tipMultiLevelNumbered": "多级编号", "DE.Views.Toolbar.tipMultilevels": "多级列表", "DE.Views.Toolbar.tipMultiLevelSymbols": "多级项目符号", "DE.Views.Toolbar.tipMultiLevelVarious": "多级各种编号", "DE.Views.Toolbar.tipNumbers": "编号", "DE.Views.Toolbar.tipPageBreak": "插入页面或分节符", "DE.Views.Toolbar.tipPageMargins": "页边距", "DE.Views.Toolbar.tipPageOrient": "页面方向", "DE.Views.Toolbar.tipPageSize": "页面大小", "DE.Views.Toolbar.tipParagraphStyle": "段落风格", "DE.Views.Toolbar.tipPaste": "粘贴", "DE.Views.Toolbar.tipPrColor": "段落背景颜色", "DE.Views.Toolbar.tipPrint": "打印", "DE.Views.Toolbar.tipPrintQuick": "快速打印", "DE.Views.Toolbar.tipRedo": "重做", "DE.Views.Toolbar.tipSave": "保存", "DE.Views.Toolbar.tipSaveCoauth": "保存您的更改以供其他用户查看", "DE.Views.Toolbar.tipSelectAll": "全选", "DE.Views.Toolbar.tipSendBackward": "下移一层", "DE.Views.Toolbar.tipSendForward": "上移一层", "DE.Views.Toolbar.tipShowHiddenChars": "不打印字符", "DE.Views.Toolbar.tipSynchronize": "该文档已被其他用户更改。请点击保存更改和重新加载更新。", "DE.Views.Toolbar.tipUndo": "撤消", "DE.Views.Toolbar.tipWatermark": "编辑水印", "DE.Views.Toolbar.txtDistribHor": "水平分布", "DE.Views.Toolbar.txtDistribVert": "垂直分布", "DE.Views.Toolbar.txtMarginAlign": "对齐边距", "DE.Views.Toolbar.txtObjectsAlign": "对齐所选对象", "DE.Views.Toolbar.txtPageAlign": "对齐页面", "DE.Views.Toolbar.txtScheme1": "公司地址", "DE.Views.Toolbar.txtScheme10": "中位数", "DE.Views.Toolbar.txtScheme11": "组件", "DE.Views.Toolbar.txtScheme12": "组件", "DE.Views.Toolbar.txtScheme13": "富裕的", "DE.Views.Toolbar.txtScheme14": "奥丽尔", "DE.Views.Toolbar.txtScheme15": "原来的", "DE.Views.Toolbar.txtScheme16": "纸", "DE.Views.Toolbar.txtScheme17": "至点", "DE.Views.Toolbar.txtScheme18": "技术", "DE.Views.Toolbar.txtScheme19": "行进", "DE.Views.Toolbar.txtScheme2": "灰度", "DE.Views.Toolbar.txtScheme20": "城市的", "DE.Views.Toolbar.txtScheme21": "气势", "DE.Views.Toolbar.txtScheme22": "新的 Office", "DE.Views.Toolbar.txtScheme3": "顶点", "DE.Views.Toolbar.txtScheme4": "方面", "DE.Views.Toolbar.txtScheme5": "公民", "DE.Views.Toolbar.txtScheme6": "中央大厅", "DE.Views.Toolbar.txtScheme7": "公平", "DE.Views.Toolbar.txtScheme8": "流动", "DE.Views.Toolbar.txtScheme9": "发现", "DE.Views.ViewTab.textAlwaysShowToolbar": "始终显示工具栏", "DE.Views.ViewTab.textDarkDocument": "黑暗文件", "DE.Views.ViewTab.textFitToPage": "适合页面", "DE.Views.ViewTab.textFitToWidth": "适合宽度", "DE.Views.ViewTab.textInterfaceTheme": "界面主题", "DE.Views.ViewTab.textLeftMenu": "左面板", "DE.Views.ViewTab.textNavigation": "导航", "DE.Views.ViewTab.textOutline": "标题", "DE.Views.ViewTab.textRightMenu": "右面板", "DE.Views.ViewTab.textRulers": "标尺", "DE.Views.ViewTab.textStatusBar": "状态栏", "DE.Views.ViewTab.textZoom": "缩放", "DE.Views.ViewTab.tipDarkDocument": "黑暗文件", "DE.Views.ViewTab.tipFitToPage": "适合页面", "DE.Views.ViewTab.tipFitToWidth": "适合宽度", "DE.Views.ViewTab.tipHeadings": "标题", "DE.Views.ViewTab.tipInterfaceTheme": "界面主题", "DE.Views.WatermarkSettingsDialog.textAuto": "自动", "DE.Views.WatermarkSettingsDialog.textBold": "加粗", "DE.Views.WatermarkSettingsDialog.textColor": "文字颜色", "DE.Views.WatermarkSettingsDialog.textDiagonal": " 斜线的", "DE.Views.WatermarkSettingsDialog.textFont": "字体 ", "DE.Views.WatermarkSettingsDialog.textFromFile": "从文件导入", "DE.Views.WatermarkSettingsDialog.textFromStorage": "来自存储设备", "DE.Views.WatermarkSettingsDialog.textFromUrl": "从URL导入", "DE.Views.WatermarkSettingsDialog.textHor": "水平的", "DE.Views.WatermarkSettingsDialog.textImageW": "图像水印", "DE.Views.WatermarkSettingsDialog.textItalic": "斜体", "DE.Views.WatermarkSettingsDialog.textLanguage": "语言", "DE.Views.WatermarkSettingsDialog.textLayout": "布局", "DE.Views.WatermarkSettingsDialog.textNone": "无", "DE.Views.WatermarkSettingsDialog.textScale": "规模", "DE.Views.WatermarkSettingsDialog.textSelect": "选择图像", "DE.Views.WatermarkSettingsDialog.textStrikeout": "删除线", "DE.Views.WatermarkSettingsDialog.textText": "文本", "DE.Views.WatermarkSettingsDialog.textTextW": "文本水印", "DE.Views.WatermarkSettingsDialog.textTitle": "水印设置", "DE.Views.WatermarkSettingsDialog.textTransparency": "半透明的", "DE.Views.WatermarkSettingsDialog.textUnderline": "下划线", "DE.Views.WatermarkSettingsDialog.tipFontName": "字体名称", "DE.Views.WatermarkSettingsDialog.tipFontSize": "字体大小"}