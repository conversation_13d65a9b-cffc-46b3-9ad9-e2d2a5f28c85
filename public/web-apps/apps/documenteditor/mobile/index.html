<!doctype html><html><head><meta charset="utf-8"><meta http-equiv="Content-Security-Policy" content="default-src * 'self' 'unsafe-inline' 'unsafe-eval' data: blob:"><meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1,user-scalable=no,minimal-ui,viewport-fit=cover"><meta name="theme-color" content="#007aff"><meta name="format-detection" content="telephone=no"><meta name="msapplication-tap-highlight" content="no"><title>Desktop Editor</title><meta name="apple-mobile-web-app-capable" content="yes"><meta name="apple-mobile-web-app-status-bar-style" content="black-translucent"><link rel="apple-touch-icon" href="static/icons/apple-touch-icon.png"><link rel="icon" href="static/icons/favicon.png"><style>body {
    margin: 0;
}

body.theme-type-light {
    --background-navbar-ios: #ffffff;
    --background-navbar-word: #446995;
    --background-navbar-cell: #40865c;
    --background-navbar-slide: #aa5252;
}

body.theme-type-dark {
    --background-navbar-ios: #232323;
    --background-navbar-word: #232323;
    --background-navbar-cell: #232323;
    --background-navbar-slide: #232323;
}

.skl-navbar {
    --box-logo-height: 0;
    --skl-navbar-height: 44px;
    --skl-pixel-ratio: 1;
}

.skl-navbar--md {
    --skl-navbar-height: 56px;
}

.skl-pixel-ratio--2 {
    --skl-pixel-ratio: 2;
}

.skl-navbar {
    height: calc(var(--skl-navbar-height) + var(--box-logo-height));
    width: 100%;
    position: relative;
}

.navbar.main-navbar {
    height: 0;
}

.skl-navbar.skl-navbar--md {
    /*height: calc(56px + 26px);*/
}

.skl-navbar.skl-navbar--ios {
    background-color: var(--background-navbar-ios, #f7f7f8);
}

.skl-navbar.skl-navbar--word.skl-navbar--md {
    background-color: var(--background-navbar-word, #446995);
}

.skl-navbar.skl-navbar--cell.skl-navbar--md {
    background-color: var(--background-navbar-cell, #40865c);
}

.skl-navbar.skl-navbar--slide.skl-navbar--md {
    background-color: var(--background-navbar-slide, #aa5252);
}

.skl-navbar::before {
    content: '';
    position: absolute;
    width: 100%;
}

.skl-navbar:not(.skl-navbar--md)::before {
    background-color: rgba(0,0,0,0.25);
    display: block;
    z-index: 15;
    top: auto;
    right: auto;
    bottom: 0;
    left: 0;
    height: 1px;
    transform-origin: 50% 100%;
    transform: scaleY(calc(1 / var(--skl-pixel-ratio)));
}

.skl-navbar.skl-navbar--md::before {
    right: 0;
    width: 100%;
    top: 100%;
    bottom: auto;
    height: 8px;
    pointer-events: none;
    background-image: linear-gradient(to bottom, rgba(0, 0, 0, 0.25) 0%, rgba(0, 0, 0, 0.08) 40%, rgba(0, 0, 0, 0.04) 50%, rgba(0, 0, 0, 0) 90%, rgba(0, 0, 0, 0) 100%)
}

.skl-lines {
    display: none;
}

.skl-line {
    height: 15px;
    margin: 30px;
    background: #e2e2e2;
    overflow: hidden;
    position: relative;
    -webkit-animation: flickerAnimation 2s infinite ease-in-out;
    animation: flickerAnimation 2s infinite ease-in-out;
}

.framework7-initializing .page-content {
    display: none;
}

@keyframes flickerAnimation {
    0%   { opacity:0.1; }
    50%  { opacity:1; }
    100% { opacity:0.1; }
}
@-o-keyframes flickerAnimation{
    0%   { opacity:0.1; }
    50%  { opacity:1; }
    100% { opacity:0.1; }
}
@-moz-keyframes flickerAnimation{
    0%   { opacity:0.1; }
    50%  { opacity:1; }
    100% { opacity:0.1; }
}
@-webkit-keyframes flickerAnimation{
    0%   { opacity:0.1; }
    50%  { opacity:1; }
    100% { opacity:0.1; }
}

.md .navbar.navbar-with-logo {
    height: 34px;
}

.ios .navbar.navbar-with-logo {
    height: 26px;
}

:root .theme-type-dark {
    --f7-navbar-bg-color: #232323;
    --f7-bars-bg-color-rgb: 35,35,35;
    --f7-subnavbar-bg-color: #232323;
    --f7-bars-translucent-opacity: 1;
}

.md .word-editor {
    --f7-navbar-bg-color: var(--background-navbar-word, #446995);
    --f7-subnavbar-bg-color: var(--background-navbar-word, #446995);
    --f7-subnavbar-height: 56px;
}

.md .cell-editor {
    --f7-navbar-bg-color: var(--background-navbar-word, #40865c);
    --f7-subnavbar-bg-color: var(--background-navbar-word, #40865c);
    --f7-subnavbar-height: 56px;
}

.md .slide-editor {
    --f7-navbar-bg-color: var(--background-navbar-word, #aa5252);
    --f7-subnavbar-bg-color: var(--background-navbar-word, #aa5252);
    --f7-subnavbar-height: 56px;
}</style><script defer="defer" src="dist/js/app.js"></script></head><body><script>window.asceditor = 'word';
        
const load_stylesheet = reflink => {
    let link = document.createElement( "link" );
    link.href = reflink;
    // link.type = "text/css";
    link.rel = "stylesheet";

    document.getElementsByTagName("head")[0].appendChild(link);
};

if ( localStorage && localStorage.getItem('mobile-mode-direction') === 'rtl' ) {
    load_stylesheet('./css/framework7-rtl.css')
    document.body.classList.add('rtl');
} else {
    load_stylesheet('./css/framework7.css')
}

let obj = !localStorage ? {id: 'theme-light', type: 'light'} : JSON.parse(localStorage.getItem("mobile-ui-theme"));
if ( !obj ) {
    obj = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches ?
        {id: 'theme-dark', type: 'dark'} : {id: 'theme-light', type: 'light'};
    localStorage && localStorage.setItem("mobile-ui-theme", JSON.stringify(obj));
}

document.body.classList.add(`theme-type-${obj.type}`, `${window.asceditor}-editor`);</script><section class="skl-container"><div class="skl-navbar skl-navbar--word"></div><div class="skl-lines"><div class="skl-line"></div><div class="skl-line"></div><div class="skl-line"></div></div></section><script>const isAndroid = /Android/.test(navigator.userAgent);
        if ( isAndroid && navigator.platform == 'Win32' )
            // Framework7 doesn't set Device.android flag when navigator.platform == 'Win32', change it for debug
            navigator.__defineGetter__('platform', () => 'Win32Debug');

        const element_add_class = (el, cls) => (el && el.classList.add(cls));

        let navbar = document.querySelector('.skl-navbar');
        if ( window.devicePixelRatio ) {
            element_add_class(navbar, `skl-pixel-ratio--${Math.floor(window.devicePixelRatio)}`);
        }

        if ( !isAndroid ) {
            const ua = navigator.userAgent;
            const iPad = ua.match(/(iPad).*OS\s([\d_]+)/);
            const iPhone = !iPad && ua.match(/(iPhone\sOS|iOS)\s([\d_]+)/);

            if ( !iPad && !iPhone ) {
                Object.defineProperty(navigator, 'userAgent', {
                    get: function () { return `iPad; CPU OS 11_0 ${ua}`; }
                });
            } else {
                element_add_class(navbar, `skl-navbar--ios`);
            }
        } else {
            element_add_class(navbar, `skl-navbar--md`);
        }

        const getUrlParams = () => {
            let e,
                a = /\+/g,  // Regex for replacing addition symbol with a space
                r = /([^&=]+)=?([^&]*)/g,
                d = s => decodeURIComponent(s.replace(a, " ")),
                q = window.location.search.substring(1),
                urlParams = {};

            while (e = r.exec(q))
                urlParams[d(e[1])] = d(e[2]);

            return urlParams;
        };

        window.Common = {Locale: {defaultLang: "en"}};
        let params = getUrlParams(),
            lang = (params["lang"] || window.Common.Locale.defaultLang).split(/[\-\_]/)[0];

        window.Common.Locale.currentLang = lang;
        window.frameEditorId = params["frameEditorId"];
        window.parentOrigin = params["parentOrigin"];</script><script src="../../../vendor/jquery/jquery.min.js"></script><div id="app"></div></body></html>