{"About": {"textAbout": "About", "textAddress": "Address", "textBack": "Back", "textEmail": "Email", "textPoweredBy": "Powered By", "textTel": "Tel", "textVersion": "Version", "textEditor": "Document Editor"}, "Add": {"notcriticalErrorTitle": "Warning", "textAddLink": "Add link", "textAddress": "Address", "textBack": "Back", "textBelowText": "Below text", "textBottomOfPage": "Bottom of page", "textBreak": "Break", "textCancel": "Cancel", "textCenterBottom": "Center Bottom", "textCenterTop": "Center Top", "textColumnBreak": "Column Break", "textColumns": "Columns", "textComment": "Comment", "textContinuousPage": "Continuous Page", "textCurrentPosition": "Current Position", "textDisplay": "Display", "textEmptyImgUrl": "You need to specify image URL.", "textEvenPage": "<PERSON>", "textFootnote": "Footnote", "textFormat": "Format", "textImage": "Image", "textImageURL": "Image URL", "textInsert": "Insert", "textInsertFootnote": "Insert Footnote", "textInsertImage": "Insert Image", "textLeftBottom": "Left Bottom", "textLeftTop": "Left Top", "textLink": "Link", "textLinkSettings": "<PERSON>s", "textLocation": "Location", "textNextPage": "Next Page", "textOddPage": "<PERSON>", "textOther": "Other", "textPageBreak": "Page Break", "textPageNumber": "Page Number", "textPictureFromLibrary": "Picture from Library", "textPictureFromURL": "Picture from URL", "textPosition": "Position", "textRightBottom": "Right Bottom", "textRightTop": "Right Top", "textRows": "Rows", "textScreenTip": "Screen Tip", "textSectionBreak": "Section Break", "textShape": "<PERSON><PERSON><PERSON>", "textStartAt": "Start At", "textTable": "Table", "textTableSize": "Table Size", "txtNotUrl": "This field should be a URL in the format \"http://www.example.com\"", "textOk": "Ok", "textTableContents": "Table of Contents", "textWithPageNumbers": "With Page Numbers", "textWithBlueLinks": "With Blue Links", "textDone": "Done", "textRecommended": "Recommended", "textRequired": "Required"}, "Common": {"Collaboration": {"notcriticalErrorTitle": "Warning", "textAccept": "Accept", "textAcceptAllChanges": "Accept all changes", "textAddComment": "Add comment", "textAddReply": "Add reply", "textAllChangesAcceptedPreview": "All changes accepted (Preview)", "textAllChangesEditing": "All changes (Editing)", "textAllChangesRejectedPreview": "All changes rejected (Preview)", "textAtLeast": "at least", "textAuto": "auto", "textBack": "Back", "textBaseline": "Baseline", "textBold": "Bold", "textBreakBefore": "Page break before", "textCancel": "Cancel", "textCaps": "All caps", "textCenter": "Align center", "textChart": "Chart", "textCollaboration": "Collaboration", "textColor": "Font color", "textComments": "Comments", "textContextual": "Don't add intervals between paragraphs of the same style", "textDelete": "Delete", "textDeleteComment": "Delete Comment", "textDeleted": "Deleted:", "textDeleteReply": "Delete Reply", "textDisplayMode": "Display Mode", "textDone": "Done", "textDStrikeout": "Double strikeout", "textEdit": "Edit", "textEditComment": "Edit Comment", "textEditReply": "Edit Reply", "textEditUser": "Users who are editing the file:", "textEquation": "Equation", "textExact": "exactly", "textFinal": "Final", "textFirstLine": "First line", "textFormatted": "Formatted", "textHighlight": "Highlight color", "textImage": "Image", "textIndentLeft": "Indent left", "textIndentRight": "Indent right", "textInserted": "Inserted:", "textItalic": "Italic", "textJustify": "<PERSON><PERSON> justified ", "textKeepLines": "Keep lines together", "textKeepNext": "Keep with next", "textLeft": "<PERSON><PERSON> left", "textLineSpacing": "Line Spacing: ", "textMarkup": "<PERSON><PERSON>", "textMessageDeleteComment": "Do you really want to delete this comment?", "textMessageDeleteReply": "Do you really want to delete this reply?", "textMultiple": "multiple", "textNoBreakBefore": "No page break before", "textNoChanges": "There are no changes.", "textNoComments": "This document doesn't contain comments", "textNoContextual": "Add interval between paragraphs of the same style", "textNoKeepLines": "Don't keep lines together", "textNoKeepNext": "Don't keep with next", "textNot": "Not ", "textNoWidow": "No widow control", "textNum": "Change numbering", "textOriginal": "Original", "textParaDeleted": "Paragraph Deleted", "textParaFormatted": "Paragraph Formatted", "textParaInserted": "Paragraph Inserted", "textParaMoveFromDown": "Moved Down:", "textParaMoveFromUp": "Moved Up:", "textParaMoveTo": "Moved:", "textPosition": "Position", "textReject": "Reject", "textRejectAllChanges": "Reject All Changes", "textReopen": "Reopen", "textResolve": "Resolve", "textReview": "Review", "textReviewChange": "Review Change", "textRight": "Align right", "textShape": "<PERSON><PERSON><PERSON>", "textShd": "Background color", "textSmallCaps": "Small caps", "textSpacing": "Spacing", "textSpacingAfter": "Spacing after", "textSpacingBefore": "Spacing before", "textStrikeout": "Strikeout", "textSubScript": "Subscript", "textSuperScript": "Superscript", "textTableChanged": "Table Settings Changed", "textTableRowsAdd": "Table Rows Added", "textTableRowsDel": "Table Rows Deleted", "textTabs": "Change tabs", "textTrackChanges": "Track Changes", "textTryUndoRedo": "The Undo/Redo functions are disabled for the Fast co-editing mode.", "textUnderline": "Underline", "textUsers": "Users", "textWidow": "Widow control", "textOk": "Ok", "textSharingSettings": "Sharing Settings"}, "ThemeColorPalette": {"textCustomColors": "Custom Colors", "textStandartColors": "Standard Colors", "textThemeColors": "Theme Colors"}, "HighlightColorPalette": {"textNoFill": "No Fill"}}, "ContextMenu": {"errorCopyCutPaste": "Copy, cut and paste actions using the context menu will be performed within the current file only.", "menuAddComment": "Add comment", "menuAddLink": "Add link", "menuCancel": "Cancel", "menuDelete": "Delete", "menuDeleteTable": "Delete Table", "menuEdit": "Edit", "menuMerge": "<PERSON><PERSON>", "menuMore": "More", "menuOpenLink": "Open Link", "menuReview": "Review", "menuReviewChange": "Review Change", "menuSplit": "Split", "menuViewComment": "View Comment", "textColumns": "Columns", "textCopyCutPasteActions": "Copy, Cut and Paste Actions", "textDoNotShowAgain": "Don't show again", "textRows": "Rows", "menuStartNewList": "Start new list", "menuStartNumberingFrom": "Set numbering value", "menuContinueNumbering": "Continue numbering", "menuSeparateList": "Separate list", "menuJoinList": "Join to previous list", "textOk": "OK", "textCancel": "Cancel", "textNumberingValue": "Numbering Value", "textRefreshEntireTable": "Refresh entire table", "textRefreshPageNumbersOnly": "Refresh page numbers only", "txtWarnUrl": "Clicking this link can be harmful to your device and data.<br>Are you sure you want to continue?", "menuEditLink": "Edit Link"}, "Edit": {"notcriticalErrorTitle": "Warning", "textActualSize": "Actual size", "textAddCustomColor": "Add custom color", "textAdditional": "Additional", "textAdditionalFormatting": "Additional formatting", "textAddress": "Address", "textAdvanced": "Advanced", "textAdvancedSettings": "Advanced settings", "textAfter": "After", "textAlign": "Align", "textAllCaps": "All caps", "textAllowOverlap": "Allow overlap", "textAuto": "Auto", "textAutomatic": "Automatic", "textBack": "Back", "textBackground": "Background", "textBandedColumn": "Banded column", "textBandedRow": "Banded row", "textBefore": "Before", "textBehind": "Behind", "textBorder": "Border", "textBringToForeground": "Bring to foreground", "textBullets": "Bullets", "textBulletsAndNumbers": "Bullets & Numbers", "textCellMargins": "<PERSON>", "textChart": "Chart", "textClose": "Close", "textColor": "Color", "textContinueFromPreviousSection": "Continue from previous section", "textCustomColor": "Custom Color", "textDifferentFirstPage": "Different first page", "textDifferentOddAndEvenPages": "Different odd and even pages", "textDisplay": "Display", "textDistanceFromText": "Distance from text", "textDoubleStrikethrough": "Double Strikethrough", "textEditLink": "Edit Link", "textEffects": "Effects", "textEmptyImgUrl": "You need to specify image URL.", "textFill": "Fill", "textFirstColumn": "First Column", "textFirstLine": "FirstLine", "textFlow": "Flow", "textFontColor": "Font Color", "textFontColors": "Font Colors", "textFonts": "Fonts", "textFooter": "Footer", "textHeader": "Header", "textHeaderRow": "Header Row", "textHighlightColor": "Highlight Color", "textHyperlink": "Hyperlink", "textImage": "Image", "textImageURL": "Image URL", "textInFront": "In Front", "textInline": "Inline", "textKeepLinesTogether": "Keep Lines Together", "textKeepWithNext": "Keep with Next", "textLastColumn": "Last Column", "textLetterSpacing": "Letter Spacing", "textLineSpacing": "Line Spacing", "textLink": "Link", "textLinkSettings": "<PERSON>s", "textLinkToPrevious": "Link to Previous", "textMoveBackward": "Move Backward", "textMoveForward": "Move Forward", "textMoveWithText": "Move with Text", "textNone": "None", "textNoStyles": "No styles for this type of charts.", "textNotUrl": "This field should be a URL in the format \"http://www.example.com\"", "textOpacity": "Opacity", "textOptions": "Options", "textOrphanControl": "Orphan Control", "textPageBreakBefore": "Page Break Before", "textPageNumbering": "Page Numbering", "textParagraph": "Paragraph", "textParagraphStyles": "Paragraph Styles", "textPictureFromLibrary": "Picture from Library", "textPictureFromURL": "Picture from URL", "textPt": "pt", "textRemoveChart": "Remove Chart", "textRemoveImage": "Remove Image", "textRemoveLink": "Remove Link", "textRemoveShape": "Remove <PERSON>", "textRemoveTable": "Remove Table", "textReorder": "Reorder", "textRepeatAsHeaderRow": "Repeat as <PERSON><PERSON>", "textReplace": "Replace", "textReplaceImage": "Replace Image", "textResizeToFitContent": "Resize to Fit Content", "textScreenTip": "Screen Tip", "textSelectObjectToEdit": "Select object to edit", "textSendToBackground": "Send to Background", "textSettings": "Settings", "textShape": "<PERSON><PERSON><PERSON>", "textSize": "Size", "textSmallCaps": "Small Caps", "textSpaceBetweenParagraphs": "Space Between Paragraphs", "textSquare": "Square", "textStartAt": "Start at", "textStrikethrough": "Strikethrough", "textStyle": "Style", "textStyleOptions": "Style Options", "textSubscript": "Subscript", "textSuperscript": "Superscript", "textTable": "Table", "textTableOptions": "Table Options", "textText": "Text", "textThrough": "Through", "textTight": "Tight", "textTopAndBottom": "Top and Bottom", "textTotalRow": "Total Row", "textType": "Type", "textWrap": "Wrap", "textNumbers": "Numbers", "textDesign": "Design", "textSu": "Su", "textMo": "Mo", "textTu": "Tu", "textWe": "We", "textTh": "Th", "textFr": "Fr", "textSa": "Sa", "textJanuary": "January", "textFebruary": "February", "textMarch": "March", "textApril": "April", "textMay": "May", "textJune": "June", "textJuly": "July", "textAugust": "August", "textSeptember": "September", "textOctober": "October", "textNovember": "November", "textDecember": "December", "textEmpty": "Empty", "textOk": "Ok", "textTableOfCont": "TOC", "textPageNumbers": "Page Numbers", "textSimple": "Simple", "textRightAlign": "Right Align", "textLeader": "Leader", "textStructure": "Structure", "textRefresh": "Refresh", "textLevels": "Levels", "textRemoveTableContent": "Remove table of content", "textCurrent": "Current", "textOnline": "Online", "textClassic": "Classic", "textDistinctive": "Distinctive", "textCentered": "Centered", "textFormal": "Formal", "textStandard": "Standard", "textModern": "Modern", "textCancel": "Cancel", "textRefreshEntireTable": "Refresh entire table", "textRefreshPageNumbersOnly": "Refresh page numbers only", "textStyles": "Styles", "textAmountOfLevels": "Amount of Levels", "textCreateTextStyle": "Create new text style", "textDone": "Done", "textTitle": "Title", "textEnterTitleNewStyle": "Enter title of a new style", "textNextParagraphStyle": "Next paragraph style", "textSameCreatedNewStyle": "Same as created new style", "textParagraphStyle": "Paragraph Style", "textChangeShape": "Change Shape", "textDeleteImage": "Delete Image", "textDeleteLink": "Delete Link", "textRecommended": "Recommended", "textRequired": "Required", "textTextWrapping": "Text Wrapping", "textCustomStyle": "Custom Style", "textWrappingStyle": "Wrapping Style", "textArrange": "<PERSON><PERSON><PERSON>"}, "Error": {"convertationTimeoutText": "Conversion timeout exceeded.", "criticalErrorExtText": "Press 'OK' to go back to the document list.", "criticalErrorTitle": "Error", "downloadErrorText": "Download failed.", "errorAccessDeny": "You are trying to perform an action you do not have rights for.<br>Please, contact your admin.", "errorBadImageUrl": "Image url is incorrect", "errorConnectToServer": "Can't save this doc. Check your connection settings or contact the admin.<br>When you click OK, you will be prompted to download the document.", "errorDatabaseConnection": "External error.<br>Database connection error. Please, contact support.", "errorDataEncrypted": "Encrypted changes have been received, they cannot be deciphered.", "errorDataRange": "Incorrect data range.", "errorDefaultMessage": "Error code: %1", "errorEditingDownloadas": "An error occurred during the work with the document.<br>Download document to save the file backup copy locally.", "errorFilePassProtect": "The file is password protected and could not be opened.", "errorFileSizeExceed": "The file size exceeds your server limit.<br>Please, contact your admin.", "errorKeyEncrypt": "Unknown key descriptor", "errorKeyExpire": "Key descriptor expired", "errorMailMergeLoadFile": "Loading failed", "errorMailMergeSaveFile": "Me<PERSON> failed.", "errorSessionAbsolute": "The document editing session has expired. Please, reload the page.", "errorSessionIdle": "The document has not been edited for quite a long time. Please, reload the page.", "errorSessionToken": "The connection to the server has been interrupted. Please, reload the page.", "errorStockChart": "Incorrect row order. To build a stock chart, place the data on the sheet in the following order:<br> opening price, max price, min price, closing price.", "errorUpdateVersionOnDisconnect": "Internet connection has been restored, and the file version has been changed.<br>Before you can continue working, you need to download the file or copy its content to make sure nothing is lost, and then reload this page.", "errorUserDrop": "The file can't be accessed right now.", "errorUsersExceed": "The number of users allowed by the pricing plan was exceeded", "errorViewerDisconnect": "Connection is lost. You can still view the document,<br>but you won't be able to download it until the connection is restored and the page is reloaded.", "notcriticalErrorTitle": "Warning", "openErrorText": "An error has occurred while opening the file", "saveErrorText": "An error has occurred while saving the file", "scriptLoadError": "The connection is too slow, some of the components could not be loaded. Please, reload the page.", "splitDividerErrorText": "The number of rows must be a divisor of %1", "splitMaxColsErrorText": "The number of columns must be less than %1", "splitMaxRowsErrorText": "The number of rows must be less than %1", "unknownErrorText": "Unknown error.", "uploadImageExtMessage": "Unknown image format.", "uploadImageFileCountMessage": "No images uploaded.", "uploadImageSizeMessage": "The image is too big. The maximum size is 25 MB.", "errorLoadingFont": "Fonts are not loaded.<br>Please contact your Document Server administrator.", "errorEmptyTOC": "Start creating a table of contents by applying a heading style from the Styles gallery to the selected text.", "errorNoTOC": "There's no table of contents to update. You can insert one from the References tab.", "errorTextFormWrongFormat": "The value entered does not match the format of the field.", "errorDirectUrl": "Please verify the link to the document.<br>This link must be a direct link to the file for downloading.", "errorInconsistentExtDocx": "An error has occurred while opening the file.<br>The file content corresponds to text documents (e.g. docx), but the file has the inconsistent extension: %1.", "errorInconsistentExtXlsx": "An error has occurred while opening the file.<br>The file content corresponds to spreadsheets (e.g. xlsx), but the file has the inconsistent extension: %1.", "errorInconsistentExtPptx": "An error has occurred while opening the file.<br>The file content corresponds to presentations (e.g. pptx), but the file has the inconsistent extension: %1.", "errorInconsistentExtPdf": "An error has occurred while opening the file.<br>The file content corresponds to one of the following formats: pdf/djvu/xps/oxps, but the file has the inconsistent extension: %1.", "errorInconsistentExt": "An error has occurred while opening the file.<br>The file content does not match the file extension.", "errorToken": "The document security token is not correctly formed.<br>Please contact your Document Server administrator.", "errorTokenExpire": "The document security token has expired.<br>Please contact your Document Server administrator."}, "LongActions": {"applyChangesTextText": "Loading data...", "applyChangesTitleText": "Loading Data", "downloadMergeText": "Downloading...", "downloadMergeTitle": "Downloading", "downloadTextText": "Downloading document...", "downloadTitleText": "Downloading Document", "loadFontsTextText": "Loading data...", "loadFontsTitleText": "Loading Data", "loadFontTextText": "Loading data...", "loadFontTitleText": "Loading Data", "loadImagesTextText": "Loading images...", "loadImagesTitleText": "Loading Images", "loadImageTextText": "Loading image...", "loadImageTitleText": "Loading Image", "loadingDocumentTextText": "Loading document...", "loadingDocumentTitleText": "Loading document", "mailMergeLoadFileText": "Loading Data Source...", "mailMergeLoadFileTitle": "Loading Data Source", "openTextText": "Opening document...", "openTitleText": "Opening Document", "printTextText": "Printing document...", "printTitleText": "Printing Document", "savePreparingText": "Preparing to save", "savePreparingTitle": "Preparing to save. Please wait...", "saveTextText": "Saving document...", "saveTitleText": "Saving Document", "sendMergeText": "Sending Merge...", "sendMergeTitle": "Sending Merge", "textLoadingDocument": "Loading document", "txtEditingMode": "Set editing mode...", "uploadImageTextText": "Uploading image...", "uploadImageTitleText": "Uploading Image", "waitText": "Please, wait...", "confirmMaxChangesSize": "The size of actions exceeds the limitation set for your server.<br>Press \"Undo\" to cancel your last action or press \"Continue\" to keep action locally (you need to download the file or copy its content to make sure nothing is lost).", "textUndo": "Undo", "textContinue": "Continue"}, "Main": {"criticalErrorTitle": "Error", "errorAccessDeny": "You are trying to perform an action you do not have rights for.<br>Please, contact your administrator.", "errorOpensource": "Using the free Community version, you can open documents for viewing only. To access mobile web editors, a commercial license is required.", "errorProcessSaveResult": "Saving failed.", "errorServerVersion": "The editor version has been updated. The page will be reloaded to apply the changes.", "errorUpdateVersion": "The file version has been changed. The page will be reloaded.", "leavePageText": "You have unsaved changes. Click 'Stay on this Page' to wait for autosave. Click 'Leave this Page' to discard all the unsaved changes.", "notcriticalErrorTitle": "Warning", "SDK": {" -Section ": " -Section ", "above": "above", "below": "below", "Caption": "Caption", "Choose an item": "Choose an item", "Click to load image": "Click to load image", "Current Document": "Current Document", "Diagram Title": "Chart Title", "endnote text": "Endnote Text", "Enter a date": "Enter a date", "Error! Bookmark not defined": "Error! Bookmark not defined.", "Error! Main Document Only": "Error! Main Document Only.", "Error! No text of specified style in document": "Error! No text of specified style in document.", "Error! Not a valid bookmark self-reference": "Error! Not a valid bookmark self-reference.", "Even Page ": "<PERSON> ", "First Page ": "First Page ", "Footer": "Footer", "footnote text": "Footnote Text", "Header": "Header", "Heading 1": "Heading 1", "Heading 2": "Heading 2", "Heading 3": "Heading 3", "Heading 4": "Heading 4", "Heading 5": "Heading 5", "Heading 6": "Heading 6", "Heading 7": "Heading 7", "Heading 8": "Heading 8", "Heading 9": "Heading 9", "Hyperlink": "Hyperlink", "Index Too Large": "Index Too Large", "Intense Quote": "Intense Quote", "Is Not In Table": "Is Not In Table", "List Paragraph": "List Paragraph", "Missing Argument": "Missing Argument", "Missing Operator": "Missing Operator", "No Spacing": "No Spacing", "No table of contents entries found": "There are no headings in the document. Apply a heading style to the text so that it appears in the table of contents.", "No table of figures entries found": "No table of figures entries found.", "None": "None", "Normal": "Normal", "Number Too Large To Format": "Number Too Large To Format", "Odd Page ": "<PERSON> ", "Quote": "Quote", "Same as Previous": "Same as Previous", "Series": "Series", "Subtitle": "Subtitle", "Syntax Error": "Syntax Error", "Table Index Cannot be Zero": "Table Index Cannot be Zero", "Table of Contents": "Table of Contents", "table of figures": "Table of figures", "The Formula Not In Table": "The Formula Not In Table", "Title": "Title", "TOC Heading": "TOC Heading", "Type equation here": "Type equation here", "Undefined Bookmark": "Undefined Bookmark", "Unexpected End of Formula": "Unexpected End of Formula", "X Axis": "X Axis XAS", "Y Axis": "Y Axis", "Your text here": "Your text here", "Zero Divide": "Zero Divide"}, "textAnonymous": "Anonymous", "textBuyNow": "Visit website", "textClose": "Close", "textContactUs": "Contact sales", "textCustomLoader": "Sorry, you are not entitled to change the loader. Contact our sales department to get a quote.", "textGuest": "Guest", "textHasMacros": "The file contains automatic macros.<br>Do you want to run macros?", "textNo": "No", "textNoLicenseTitle": "License limit reached", "textPaidFeature": "Paid feature", "textRemember": "Remember my choice", "textYes": "Yes", "titleLicenseExp": "License expired", "titleServerVersion": "Editor updated", "titleUpdateVersion": "Version changed", "warnLicenseExceeded": "You've reached the limit for simultaneous connections to %1 editors. This document will be opened for viewing only. Contact your administrator to learn more.", "warnLicenseExp": "Your license has expired. Please, update your license and refresh the page.", "warnLicenseLimitedNoAccess": "License expired. You have no access to document editing functionality. Please, contact your admin.", "warnLicenseLimitedRenewed": "The license needs to be renewed. You have limited access to document editing functionality.<br>Please contact your administrator to get full access", "warnLicenseUsersExceeded": "You've reached the user limit for %1 editors. Contact your administrator to learn more.", "warnNoLicense": "You've reached the limit for simultaneous connections to %1 editors. This document will be opened for viewing only. Contact %1 sales team for personal upgrade terms.", "warnNoLicenseUsers": "You've reached the user limit for %1 editors. Contact %1 sales team for personal upgrade terms.", "warnProcessRightsChange": "You don't have permission to edit this file.", "textReplaceSuccess": "The search has been done. Occurrences replaced: {0}", "textReplaceSkipped": "The replacement has been made. {0} occurrences were skipped.", "textNoTextFound": "Text not found", "textRequestMacros": "A macro makes a request to URL. Do you want to allow the request to the %1?", "textDocumentProtected": "The document is protected. Edit settings are not available", "textOk": "Ok"}, "Settings": {"advDRMOptions": "Protected File", "advDRMPassword": "Password", "advTxtOptions": "Choose TXT Options", "closeButtonText": "Close File", "notcriticalErrorTitle": "Warning", "textAbout": "About", "textApplication": "Application", "textApplicationSettings": "Application Settings", "textAuthor": "Author", "textBack": "Back", "textBottom": "Bottom", "textCancel": "Cancel", "textCaseSensitive": "Case Sensitive", "textCentimeter": "Centimeter", "textCollaboration": "Collaboration", "textColorSchemes": "Color Schemes", "textComment": "Comment", "textComments": "Comments", "textCommentsDisplay": "Comments Display", "textCreated": "Created", "textCustomSize": "Custom Size", "textDisableAll": "Disable All", "textDisableAllMacrosWithNotification": "Disable all macros with notification", "textDisableAllMacrosWithoutNotification": "Disable all macros without notification", "textDocumentInfo": "Document Info", "textDocumentSettings": "Document Settings", "textDocumentTitle": "Document Title", "textDone": "Done", "textDownload": "Download", "textDownloadAs": "Download As", "textDownloadRtf": "If you continue saving in this format some of the formatting might be lost. Are you sure you want to continue?", "textDownloadTxt": "If you continue saving in this format all features except the text will be lost. Are you sure you want to continue?", "textEnableAll": "Enable All", "textEnableAllMacrosWithoutNotification": "Enable all macros without notification", "textEncoding": "Encoding", "textFind": "Find", "textFindAndReplace": "Find and Replace", "textFindAndReplaceAll": "Find and Replace All", "textFormat": "Format", "textHelp": "Help", "textHiddenTableBorders": "Hidden Table Borders", "textHighlightResults": "Highlight Results", "textInch": "Inch", "textLandscape": "Landscape", "textLastModified": "Last Modified", "textLastModifiedBy": "Last Modified By", "textLeft": "Left", "textLoading": "Loading...", "textLocation": "Location", "textMacrosSettings": "<PERSON><PERSON>", "textMargins": "<PERSON><PERSON>", "textMarginsH": "Top and bottom margins are too high for a given page height", "textMarginsW": "Left and right margins are too wide for a given page width", "textNoCharacters": "Nonprinting Characters", "textNoTextFound": "Text not found", "textOpenFile": "Enter a password to open the file", "textOrientation": "Orientation", "textOwner": "Owner", "textPoint": "Point", "textPortrait": "Portrait", "textPrint": "Print", "textReaderMode": "Reader Mode", "textReplace": "Replace", "textReplaceAll": "Replace All", "textResolvedComments": "Resolved Comments", "textRight": "Right", "textSearch": "Search", "textSettings": "Settings", "textShowNotification": "Show Notification", "textSpellcheck": "Spell Checking", "textStatistic": "Statistic", "textSubject": "Subject", "textTitle": "Title", "textTop": "Top", "textUnitOfMeasurement": "Unit Of Measurement", "textUploaded": "Uploaded", "txtIncorrectPwd": "Password is incorrect", "txtProtected": "Once you enter the password and open the file, the current password will be reset", "txtScheme1": "Office", "txtScheme10": "Median", "txtScheme11": "Metro", "txtScheme12": "<PERSON><PERSON><PERSON>", "txtScheme13": "Opulent", "txtScheme14": "Oriel", "txtScheme15": "Origin", "txtScheme16": "Paper", "txtScheme17": "Solstice", "txtScheme18": "Technic", "txtScheme19": "Trek", "txtScheme2": "Grayscale", "txtScheme20": "Urban", "txtScheme21": "Verve", "txtScheme22": "New Office", "txtScheme3": "Apex", "txtScheme4": "Aspect", "txtScheme5": "Civic", "txtScheme6": "Concourse", "txtScheme7": "Equity", "txtScheme8": "Flow", "txtScheme9": "Foundry", "textOk": "Ok", "textChooseEncoding": "Choose Encoding", "textChooseTxtOptions": "Choose TXT Options", "txtDownloadTxt": "Download TXT", "txtOk": "Ok", "textPages": "Pages", "textParagraphs": "Paragraphs", "textSpaces": "Spaces", "textSymbols": "Symbols", "textWords": "Words", "textFeedback": "Feedback & Support", "textNavigation": "Navigation", "textEmptyScreens": "There are no headings in the document. Apply a headings style to the text so that it appeas in the table of cotents.", "textBeginningDocument": "Beginning of document", "textEmptyHeading": "Empty Heading", "textPageSize": "<PERSON>", "textPdfVer": "PDF Version", "textPdfTagged": "Tagged PDF", "textFastWV": "Fast Web View", "textYes": "Yes", "textNo": "No", "textPdfProducer": "PDF Producer", "textDirection": "Direction", "textLeftToRight": "Left To Right", "textRightToLeft": "Right To Left", "textRestartApplication": "Please restart the application for the changes to take effect", "textMobileView": "Mobile View"}, "Toolbar": {"dlgLeaveMsgText": "You have unsaved changes. Click 'Stay on this Page' to wait for autosave. Click 'Leave this Page' to discard all the unsaved changes.", "dlgLeaveTitleText": "You leave the application", "leaveButtonText": "Leave this Page", "stayButtonText": "Stay on this page", "textOk": "OK", "textSwitchedMobileView": "Switched to Mobile view", "textSwitchedStandardView": "Switched to Standard view"}}