{"About": {"textAbout": "O programu", "textAddress": "<PERSON><PERSON><PERSON>", "textBack": "<PERSON><PERSON><PERSON>", "textEditor": "Document Editor", "textEmail": "Email", "textPoweredBy": "Powered By", "textTel": "Tel", "textVersion": "Version"}, "Add": {"textAddLink": "Do<PERSON>j povezavo", "textAddress": "<PERSON><PERSON><PERSON>", "textBack": "<PERSON><PERSON><PERSON>", "textCancel": "<PERSON><PERSON><PERSON>", "textImage": "Slika", "notcriticalErrorTitle": "Warning", "textBelowText": "Below text", "textBottomOfPage": "Bottom of page", "textBreak": "Break", "textCenterBottom": "Center Bottom", "textCenterTop": "Center Top", "textColumnBreak": "Column Break", "textColumns": "Columns", "textComment": "Comment", "textContinuousPage": "Continuous Page", "textCurrentPosition": "Current Position", "textDisplay": "Display", "textDone": "Done", "textEmptyImgUrl": "You need to specify image URL.", "textEvenPage": "<PERSON>", "textFootnote": "Footnote", "textFormat": "Format", "textImageURL": "Image URL", "textInsert": "Insert", "textInsertFootnote": "Insert Footnote", "textInsertImage": "Insert Image", "textLeftBottom": "Left Bottom", "textLeftTop": "Left Top", "textLink": "Link", "textLinkSettings": "<PERSON>s", "textLocation": "Location", "textNextPage": "Next Page", "textOddPage": "<PERSON>", "textOk": "Ok", "textOther": "Other", "textPageBreak": "Page Break", "textPageNumber": "Page Number", "textPictureFromLibrary": "Picture from Library", "textPictureFromURL": "Picture from URL", "textPosition": "Position", "textRecommended": "Recommended", "textRequired": "Required", "textRightBottom": "Right Bottom", "textRightTop": "Right Top", "textRows": "Rows", "textScreenTip": "Screen Tip", "textSectionBreak": "Section Break", "textShape": "<PERSON><PERSON><PERSON>", "textStartAt": "Start At", "textTable": "Table", "textTableContents": "Table of Contents", "textTableSize": "Table Size", "textWithBlueLinks": "With Blue Links", "textWithPageNumbers": "With Page Numbers", "txtNotUrl": "This field should be a URL in the format \"http://www.example.com\""}, "Common": {"Collaboration": {"textAccept": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "textAcceptAllChanges": "Sprej<PERSON> vse spremembe", "textAddComment": "<PERSON><PERSON><PERSON> k<PERSON>", "textAddReply": "Dodaj odgovor", "textAtLeast": "minimalno", "textAuto": "samodejno", "textBack": "<PERSON><PERSON><PERSON>", "textBold": "<PERSON><PERSON><PERSON><PERSON>", "textCancel": "<PERSON><PERSON><PERSON>", "textCenter": "Poravnava na sredino", "textHighlight": "Barva za označevanje besedila", "textImage": "Slika", "textNoContextual": "Dodajte interval med odstavki istega sloga", "textShd": "<PERSON><PERSON>", "notcriticalErrorTitle": "Warning", "textAllChangesAcceptedPreview": "All changes accepted (Preview)", "textAllChangesEditing": "All changes (Editing)", "textAllChangesRejectedPreview": "All changes rejected (Preview)", "textBaseline": "Baseline", "textBreakBefore": "Page break before", "textCaps": "All caps", "textChart": "Chart", "textCollaboration": "Collaboration", "textColor": "Font color", "textComments": "Comments", "textContextual": "Don't add intervals between paragraphs of the same style", "textDelete": "Delete", "textDeleteComment": "Delete Comment", "textDeleted": "Deleted:", "textDeleteReply": "Delete Reply", "textDisplayMode": "Display Mode", "textDone": "Done", "textDStrikeout": "Double strikeout", "textEdit": "Edit", "textEditComment": "Edit Comment", "textEditReply": "Edit Reply", "textEditUser": "Users who are editing the file:", "textEquation": "Equation", "textExact": "exactly", "textFinal": "Final", "textFirstLine": "First line", "textFormatted": "Formatted", "textIndentLeft": "Indent left", "textIndentRight": "Indent right", "textInserted": "Inserted:", "textItalic": "Italic", "textJustify": "<PERSON><PERSON> justified ", "textKeepLines": "Keep lines together", "textKeepNext": "Keep with next", "textLeft": "<PERSON><PERSON> left", "textLineSpacing": "Line Spacing: ", "textMarkup": "<PERSON><PERSON>", "textMessageDeleteComment": "Do you really want to delete this comment?", "textMessageDeleteReply": "Do you really want to delete this reply?", "textMultiple": "multiple", "textNoBreakBefore": "No page break before", "textNoChanges": "There are no changes.", "textNoComments": "No Comments", "textNoKeepLines": "Don't keep lines together", "textNoKeepNext": "Don't keep with next", "textNot": "Not ", "textNoWidow": "No widow control", "textNum": "Change numbering", "textOk": "Ok", "textOriginal": "Original", "textParaDeleted": "Paragraph Deleted", "textParaFormatted": "Paragraph Formatted", "textParaInserted": "Paragraph Inserted", "textParaMoveFromDown": "Moved Down:", "textParaMoveFromUp": "Moved Up:", "textParaMoveTo": "Moved:", "textPosition": "Position", "textReject": "Reject", "textRejectAllChanges": "Reject All Changes", "textReopen": "Reopen", "textResolve": "Resolve", "textReview": "Review", "textReviewChange": "Review Change", "textRight": "Align right", "textShape": "<PERSON><PERSON><PERSON>", "textSharingSettings": "Sharing Settings", "textSmallCaps": "Small caps", "textSpacing": "Spacing", "textSpacingAfter": "Spacing after", "textSpacingBefore": "Spacing before", "textStrikeout": "Strikeout", "textSubScript": "Subscript", "textSuperScript": "Superscript", "textTableChanged": "Table Settings Changed", "textTableRowsAdd": "Table Rows Added", "textTableRowsDel": "Table Rows Deleted", "textTabs": "Change tabs", "textTrackChanges": "Track Changes", "textTryUndoRedo": "The Undo/Redo functions are disabled for the Fast co-editing mode.", "textUnderline": "Underline", "textUsers": "Users", "textWidow": "Widow control"}, "HighlightColorPalette": {"textNoFill": "No Fill"}, "ThemeColorPalette": {"textCustomColors": "Custom Colors", "textStandartColors": "Standard Colors", "textThemeColors": "Theme Colors"}}, "ContextMenu": {"menuAddComment": "<PERSON><PERSON><PERSON> k<PERSON>", "menuAddLink": "Do<PERSON>j povezavo", "menuCancel": "<PERSON><PERSON><PERSON>", "textCancel": "<PERSON><PERSON><PERSON>", "errorCopyCutPaste": "Copy, cut and paste actions using the context menu will be performed within the current file only.", "menuContinueNumbering": "Continue numbering", "menuDelete": "Delete", "menuDeleteTable": "Delete Table", "menuEdit": "Edit", "menuEditLink": "Edit Link", "menuJoinList": "Join to previous list", "menuMerge": "<PERSON><PERSON>", "menuMore": "More", "menuOpenLink": "Open Link", "menuReview": "Review", "menuReviewChange": "Review Change", "menuSeparateList": "Separate list", "menuSplit": "Split", "menuStartNewList": "Start new list", "menuStartNumberingFrom": "Set numbering value", "menuViewComment": "View Comment", "textColumns": "Columns", "textCopyCutPasteActions": "Copy, Cut and Paste Actions", "textDoNotShowAgain": "Don't show again", "textNumberingValue": "Numbering Value", "textOk": "OK", "textRefreshEntireTable": "Refresh entire table", "textRefreshPageNumbersOnly": "Refresh page numbers only", "textRows": "Rows", "txtWarnUrl": "Clicking this link can be harmful to your device and data.<br>Are you sure you want to continue?"}, "Edit": {"textActualSize": "Dejanska velikost", "textAddCustomColor": "Dodaj barvo po meri", "textAdditional": "Dodatno", "textAdditionalFormatting": "Dodatno oblikovanje", "textAddress": "<PERSON><PERSON><PERSON>", "textAdvanced": "Napredno", "textAdvancedSettings": "Napredne nastavitve", "textAfter": "Po", "textAlign": "Poravnaj", "textApril": "April", "textAugust": "<PERSON><PERSON><PERSON><PERSON>", "textAuto": "Samodejno", "textAutomatic": "Samodejeno", "textBack": "<PERSON><PERSON><PERSON>", "textBackground": "Ozadje", "textCancel": "<PERSON><PERSON><PERSON>", "textHighlightColor": "Barva za označevanje besedila", "textHyperlink": "Hiperpovezava", "textImage": "Slika", "notcriticalErrorTitle": "Warning", "textAllCaps": "All caps", "textAllowOverlap": "Allow overlap", "textAmountOfLevels": "Amount of Levels", "textArrange": "<PERSON><PERSON><PERSON>", "textBandedColumn": "Banded column", "textBandedRow": "Banded row", "textBefore": "Before", "textBehind": "Behind Text", "textBorder": "Border", "textBringToForeground": "Bring to Foreground", "textBullets": "Bullets", "textBulletsAndNumbers": "Bullets & Numbers", "textCellMargins": "<PERSON>", "textCentered": "Centered", "textChangeShape": "Change Shape", "textChart": "Chart", "textClassic": "Classic", "textClose": "Close", "textColor": "Color", "textContinueFromPreviousSection": "Continue from previous section", "textCreateTextStyle": "Create new text style", "textCurrent": "Current", "textCustomColor": "Custom Color", "textCustomStyle": "Custom Style", "textDecember": "December", "textDeleteImage": "Delete Image", "textDeleteLink": "Delete Link", "textDesign": "Design", "textDifferentFirstPage": "Different first page", "textDifferentOddAndEvenPages": "Different odd and even pages", "textDisplay": "Display", "textDistanceFromText": "Distance from text", "textDistinctive": "Distinctive", "textDone": "Done", "textDoubleStrikethrough": "Double Strikethrough", "textEditLink": "Edit Link", "textEffects": "Effects", "textEmpty": "Empty", "textEmptyImgUrl": "You need to specify image URL.", "textEnterTitleNewStyle": "Enter title of a new style", "textFebruary": "February", "textFill": "Fill", "textFirstColumn": "First Column", "textFirstLine": "First Line", "textFlow": "Flow", "textFontColor": "Font Color", "textFontColors": "Font Colors", "textFonts": "Fonts", "textFooter": "Footer", "textFormal": "Formal", "textFr": "Fr", "textHeader": "Header", "textHeaderRow": "Header Row", "textImageURL": "Image URL", "textInFront": "In Front of Text", "textInline": "In Line with Text", "textJanuary": "January", "textJuly": "July", "textJune": "June", "textKeepLinesTogether": "Keep Lines Together", "textKeepWithNext": "Keep with Next", "textLastColumn": "Last Column", "textLeader": "Leader", "textLetterSpacing": "Letter Spacing", "textLevels": "Levels", "textLineSpacing": "Line Spacing", "textLink": "Link", "textLinkSettings": "<PERSON>s", "textLinkToPrevious": "Link to Previous", "textMarch": "March", "textMay": "May", "textMo": "Mo", "textModern": "Modern", "textMoveBackward": "Move Backward", "textMoveForward": "Move Forward", "textMoveWithText": "Move with Text", "textNextParagraphStyle": "Next paragraph style", "textNone": "None", "textNoStyles": "No styles for this type of charts.", "textNotUrl": "This field should be a URL in the format \"http://www.example.com\"", "textNovember": "November", "textNumbers": "Numbers", "textOctober": "October", "textOk": "Ok", "textOnline": "Online", "textOpacity": "Opacity", "textOptions": "Options", "textOrphanControl": "Orphan Control", "textPageBreakBefore": "Page Break Before", "textPageNumbering": "Page Numbering", "textPageNumbers": "Page Numbers", "textParagraph": "Paragraph", "textParagraphStyle": "Paragraph Style", "textPictureFromLibrary": "Picture from Library", "textPictureFromURL": "Picture from URL", "textPt": "pt", "textRecommended": "Recommended", "textRefresh": "Refresh", "textRefreshEntireTable": "Refresh entire table", "textRefreshPageNumbersOnly": "Refresh page numbers only", "textRemoveChart": "Remove Chart", "textRemoveShape": "Remove <PERSON>", "textRemoveTable": "Remove Table", "textRemoveTableContent": "Remove table of contents", "textRepeatAsHeaderRow": "Repeat as <PERSON><PERSON>", "textReplace": "Replace", "textReplaceImage": "Replace Image", "textRequired": "Required", "textResizeToFitContent": "Resize to Fit Content", "textRightAlign": "Right Align", "textSa": "Sa", "textSameCreatedNewStyle": "Same as created new style", "textScreenTip": "Screen Tip", "textSelectObjectToEdit": "Select object to edit", "textSendToBackground": "Send to Background", "textSeptember": "September", "textSettings": "Settings", "textShape": "<PERSON><PERSON><PERSON>", "textSimple": "Simple", "textSize": "Size", "textSmallCaps": "Small Caps", "textSpaceBetweenParagraphs": "Space Between Paragraphs", "textSquare": "Square", "textStandard": "Standard", "textStartAt": "Start at", "textStrikethrough": "Strikethrough", "textStructure": "Structure", "textStyle": "Style", "textStyleOptions": "Style Options", "textStyles": "Styles", "textSu": "Su", "textSubscript": "Subscript", "textSuperscript": "Superscript", "textTable": "Table", "textTableOfCont": "TOC", "textTableOptions": "Table Options", "textText": "Text", "textTextWrapping": "Text Wrapping", "textTh": "Th", "textThrough": "Through", "textTight": "Tight", "textTitle": "Title", "textTopAndBottom": "Top and Bottom", "textTotalRow": "Total Row", "textTu": "Tu", "textType": "Type", "textWe": "We", "textWrap": "Wrap", "textWrappingStyle": "Wrapping Style"}, "Main": {"SDK": {"above": "nad", "Heading 1": "Naslov 1", "Heading 2": "Naslov 2", "Heading 3": "Naslov 3", "Heading 4": "Naslov 4", "Heading 5": "Naslov 5", "Heading 6": "Naslov 6", "Heading 7": "Naslov 7", "Heading 8": "Naslov 8", "Heading 9": "Naslov 9", "Hyperlink": "Hiperpovezava", "Your text here": "<PERSON><PERSON><PERSON><PERSON> besedilo pride tukaj", " -Section ": " -Section ", "below": "below", "Caption": "Caption", "Choose an item": "Choose an item", "Click to load image": "Click to load image", "Current Document": "Current Document", "Diagram Title": "Chart Title", "endnote text": "Endnote Text", "Enter a date": "Enter a date", "Error! Bookmark not defined": "Error! Bookmark not defined.", "Error! Main Document Only": "Error! Main Document Only.", "Error! No text of specified style in document": "Error! No text of specified style in document.", "Error! Not a valid bookmark self-reference": "Error! Not a valid bookmark self-reference.", "Even Page ": "<PERSON> ", "First Page ": "First Page ", "Footer": "Footer", "footnote text": "Footnote Text", "Header": "Header", "Index Too Large": "Index Too Large", "Intense Quote": "Intense Quote", "Is Not In Table": "Is Not In Table", "List Paragraph": "List Paragraph", "Missing Argument": "Missing Argument", "Missing Operator": "Missing Operator", "No Spacing": "No Spacing", "No table of contents entries found": "There are no headings in the document. Apply a heading style to the text so that it appears in the table of contents.", "No table of figures entries found": "No table of figures entries found.", "None": "None", "Normal": "Normal", "Number Too Large To Format": "Number Too Large To Format", "Odd Page ": "<PERSON> ", "Quote": "Quote", "Same as Previous": "Same as Previous", "Series": "Series", "Subtitle": "Subtitle", "Syntax Error": "Syntax Error", "Table Index Cannot be Zero": "Table Index Cannot be Zero", "Table of Contents": "Table of Contents", "table of figures": "Table of figures", "The Formula Not In Table": "The Formula Not In Table", "Title": "Title", "TOC Heading": "TOC Heading", "Type equation here": "Type equation here", "Undefined Bookmark": "Undefined Bookmark", "Unexpected End of Formula": "Unexpected End of Formula", "X Axis": "X Axis XAS", "Y Axis": "Y Axis", "Zero Divide": "Zero Divide"}, "criticalErrorTitle": "Error", "errorAccessDeny": "You are trying to perform an action you do not have rights for.<br>Please, contact your administrator.", "errorOpensource": "Using the free Community version, you can open documents for viewing only. To access mobile web editors, a commercial license is required.", "errorProcessSaveResult": "Saving failed.", "errorServerVersion": "The editor version has been updated. The page will be reloaded to apply the changes.", "errorUpdateVersion": "The file version has been changed. The page will be reloaded.", "leavePageText": "You have unsaved changes. Click 'Stay on this Page' to wait for autosave. Click 'Leave this Page' to discard all the unsaved changes.", "notcriticalErrorTitle": "Warning", "textAnonymous": "Anonymous", "textBuyNow": "Visit website", "textClose": "Close", "textContactUs": "Contact sales", "textCustomLoader": "Sorry, you are not entitled to change the loader. Contact our sales department to get a quote.", "textDocumentProtected": "The document is protected. Edit settings are not available", "textGuest": "Guest", "textHasMacros": "The file contains automatic macros.<br>Do you want to run macros?", "textNo": "No", "textNoLicenseTitle": "License limit reached", "textNoTextFound": "Text not found", "textOk": "Ok", "textPaidFeature": "Paid feature", "textRemember": "Remember my choice", "textReplaceSkipped": "The replacement has been made. {0} occurrences were skipped.", "textReplaceSuccess": "The search has been done. Occurrences replaced: {0}", "textRequestMacros": "A macro makes a request to URL. Do you want to allow the request to the %1?", "textYes": "Yes", "titleLicenseExp": "License expired", "titleServerVersion": "Editor updated", "titleUpdateVersion": "Version changed", "warnLicenseExceeded": "You've reached the limit for simultaneous connections to %1 editors. This document will be opened for viewing only. Contact your administrator to learn more.", "warnLicenseExp": "Your license has expired. Please, update your license and refresh the page.", "warnLicenseLimitedNoAccess": "License expired. You have no access to document editing functionality. Please, contact your admin.", "warnLicenseLimitedRenewed": "License needs to be renewed. You have limited access to document editing functionality.<br>Please contact your administrator to get full access", "warnLicenseUsersExceeded": "You've reached the user limit for %1 editors. Contact your administrator to learn more.", "warnNoLicense": "You've reached the limit for simultaneous connections to %1 editors. This document will be opened for viewing only. Contact %1 sales team for personal upgrade terms.", "warnNoLicenseUsers": "You've reached the user limit for %1 editors. Contact %1 sales team for personal upgrade terms.", "warnProcessRightsChange": "You don't have permission to edit this file."}, "Settings": {"textAbout": "O programu", "textApplication": "Aplikacija", "textApplicationSettings": "Nastavitve aplikacije", "textAuthor": "A<PERSON><PERSON>", "textBack": "<PERSON><PERSON><PERSON>", "textCancel": "<PERSON><PERSON><PERSON>", "textHelp": "<PERSON><PERSON><PERSON>", "txtScheme4": "<PERSON><PERSON><PERSON>", "advDRMOptions": "Protected File", "advDRMPassword": "Password", "advTxtOptions": "Choose TXT Options", "closeButtonText": "Close File", "notcriticalErrorTitle": "Warning", "textBeginningDocument": "Beginning of document", "textBottom": "Bottom", "textCaseSensitive": "Case Sensitive", "textCentimeter": "Centimeter", "textChooseEncoding": "Choose Encoding", "textChooseTxtOptions": "Choose TXT Options", "textCollaboration": "Collaboration", "textColorSchemes": "Color Schemes", "textComment": "Comment", "textComments": "Comments", "textCommentsDisplay": "Comments Display", "textCreated": "Created", "textCustomSize": "Custom Size", "textDirection": "Direction", "textDisableAll": "Disable All", "textDisableAllMacrosWithNotification": "Disable all macros with notification", "textDisableAllMacrosWithoutNotification": "Disable all macros without notification", "textDocumentInfo": "Document Info", "textDocumentSettings": "Document Settings", "textDocumentTitle": "Document Title", "textDone": "Done", "textDownload": "Download", "textDownloadAs": "Download As", "textDownloadRtf": "If you continue saving in this format some of the formatting might be lost. Are you sure you want to continue?", "textDownloadTxt": "If you continue saving in this format all features except the text will be lost. Are you sure you want to continue?", "textEmptyHeading": "Empty Heading", "textEmptyScreens": "There are no headings in the document. Apply a headings style to the text so that it appears in the table of contents.", "textEnableAll": "Enable All", "textEnableAllMacrosWithoutNotification": "Enable all macros without notification", "textEncoding": "Encoding", "textFastWV": "Fast Web View", "textFeedback": "Feedback & Support", "textFind": "Find", "textFindAndReplace": "Find and Replace", "textFindAndReplaceAll": "Find and Replace All", "textFormat": "Format", "textHiddenTableBorders": "Hidden Table Borders", "textHighlightResults": "Highlight Results", "textInch": "Inch", "textLandscape": "Landscape", "textLastModified": "Last Modified", "textLastModifiedBy": "Last Modified By", "textLeft": "Left", "textLeftToRight": "Left To Right", "textLoading": "Loading...", "textLocation": "Location", "textMacrosSettings": "<PERSON><PERSON>", "textMargins": "<PERSON><PERSON>", "textMarginsH": "Top and bottom margins are too high for a given page height", "textMarginsW": "Left and right margins are too wide for a given page width", "textMobileView": "Mobile View", "textNavigation": "Navigation", "textNo": "No", "textNoCharacters": "Nonprinting Characters", "textNoTextFound": "Text not found", "textOk": "Ok", "textOpenFile": "Enter a password to open the file", "textOrientation": "Orientation", "textOwner": "Owner", "textPages": "Pages", "textPageSize": "<PERSON>", "textParagraphs": "Paragraphs", "textPdfProducer": "PDF Producer", "textPdfTagged": "Tagged PDF", "textPdfVer": "PDF Version", "textPoint": "Point", "textPortrait": "Portrait", "textPrint": "Print", "textReaderMode": "Reader Mode", "textReplace": "Replace", "textReplaceAll": "Replace All", "textResolvedComments": "Resolved Comments", "textRestartApplication": "Please restart the application for the changes to take effect", "textRight": "Right", "textRightToLeft": "Right To Left", "textSearch": "Search", "textSettings": "Settings", "textShowNotification": "Show Notification", "textSpaces": "Spaces", "textSpellcheck": "Spell Checking", "textStatistic": "Statistic", "textSubject": "Subject", "textSymbols": "Symbols", "textTitle": "Title", "textTop": "Top", "textUnitOfMeasurement": "Unit Of Measurement", "textUploaded": "Uploaded", "textWords": "Words", "textYes": "Yes", "txtDownloadTxt": "Download TXT", "txtIncorrectPwd": "Password is incorrect", "txtOk": "Ok", "txtProtected": "Once you enter the password and open the file, the current password will be reset", "txtScheme1": "Office", "txtScheme10": "Median", "txtScheme11": "Metro", "txtScheme12": "<PERSON><PERSON><PERSON>", "txtScheme13": "Opulent", "txtScheme14": "Oriel", "txtScheme15": "Origin", "txtScheme16": "Paper", "txtScheme17": "Solstice", "txtScheme18": "Technic", "txtScheme19": "Trek", "txtScheme2": "Grayscale", "txtScheme20": "Urban", "txtScheme21": "Verve", "txtScheme22": "New Office", "txtScheme3": "Apex", "txtScheme5": "Civic", "txtScheme6": "Concourse", "txtScheme7": "Equity", "txtScheme8": "Flow", "txtScheme9": "Foundry"}, "Error": {"convertationTimeoutText": "Conversion timeout exceeded.", "criticalErrorExtText": "Press 'OK' to go back to the document list.", "criticalErrorTitle": "Error", "downloadErrorText": "Download failed.", "errorAccessDeny": "You are trying to perform an action you do not have rights for.<br>Please, contact your admin.", "errorBadImageUrl": "Image URL is incorrect", "errorConnectToServer": "Can't save this doc. Check your connection settings or contact the admin.<br>When you click OK, you will be prompted to download the document.", "errorDatabaseConnection": "External error.<br>Database connection error. Please, contact support.", "errorDataEncrypted": "Encrypted changes have been received, they cannot be deciphered.", "errorDataRange": "Incorrect data range.", "errorDefaultMessage": "Error code: %1", "errorDirectUrl": "Please verify the link to the document.<br>This link must be a direct link to the file for downloading.", "errorEditingDownloadas": "An error occurred during the work with the document.<br>Download document to save the file backup copy locally.", "errorEmptyTOC": "Start creating a table of contents by applying a heading style from the Styles gallery to the selected text.", "errorFilePassProtect": "The file is password protected and could not be opened.", "errorFileSizeExceed": "The file size exceeds your server limit.<br>Please, contact your admin.", "errorInconsistentExt": "An error has occurred while opening the file.<br>The file content does not match the file extension.", "errorInconsistentExtDocx": "An error has occurred while opening the file.<br>The file content corresponds to text documents (e.g. docx), but the file has the inconsistent extension: %1.", "errorInconsistentExtPdf": "An error has occurred while opening the file.<br>The file content corresponds to one of the following formats: pdf/djvu/xps/oxps, but the file has the inconsistent extension: %1.", "errorInconsistentExtPptx": "An error has occurred while opening the file.<br>The file content corresponds to presentations (e.g. pptx), but the file has the inconsistent extension: %1.", "errorInconsistentExtXlsx": "An error has occurred while opening the file.<br>The file content corresponds to spreadsheets (e.g. xlsx), but the file has the inconsistent extension: %1.", "errorKeyEncrypt": "Unknown key descriptor", "errorKeyExpire": "Key descriptor expired", "errorLoadingFont": "Fonts are not loaded.<br>Please contact your Document Server administrator.", "errorMailMergeLoadFile": "Loading failed", "errorMailMergeSaveFile": "Me<PERSON> failed.", "errorNoTOC": "There's no table of contents to update. You can insert one from the References tab.", "errorSessionAbsolute": "The document editing session has expired. Please, reload the page.", "errorSessionIdle": "The document has not been edited for quite a long time. Please, reload the page.", "errorSessionToken": "The connection to the server has been interrupted. Please, reload the page.", "errorStockChart": "Incorrect row order. To build a stock chart, place the data on the sheet in the following order:<br> opening price, max price, min price, closing price.", "errorTextFormWrongFormat": "The value entered does not match the format of the field.", "errorToken": "The document security token is not correctly formed.<br>Please contact your Document Server administrator.", "errorTokenExpire": "The document security token has expired.<br>Please contact your Document Server administrator.", "errorUpdateVersionOnDisconnect": "Connection has been restored, and the file version has been changed.<br>Before you can continue working, you need to download the file or copy its content to make sure nothing is lost, and then reload this page.", "errorUserDrop": "The file can't be accessed right now.", "errorUsersExceed": "The number of users allowed by the pricing plan was exceeded", "errorViewerDisconnect": "Connection is lost. You can still view the document,<br>but you won't be able to download or print it until the connection is restored and the page is reloaded.", "notcriticalErrorTitle": "Warning", "openErrorText": "An error has occurred while opening the file", "saveErrorText": "An error has occurred while saving the file", "scriptLoadError": "The connection is too slow, some of the components could not be loaded. Please, reload the page.", "splitDividerErrorText": "The number of rows must be a divisor of %1", "splitMaxColsErrorText": "The number of columns must be less than %1", "splitMaxRowsErrorText": "The number of rows must be less than %1", "unknownErrorText": "Unknown error.", "uploadImageExtMessage": "Unknown image format.", "uploadImageFileCountMessage": "No images uploaded.", "uploadImageSizeMessage": "The image is too big. The maximum size is 25 MB."}, "LongActions": {"applyChangesTextText": "Loading data...", "applyChangesTitleText": "Loading Data", "confirmMaxChangesSize": "The size of actions exceeds the limitation set for your server.<br>Press \"Undo\" to cancel your last action or press \"Continue\" to keep action locally (you need to download the file or copy its content to make sure nothing is lost).", "downloadMergeText": "Downloading...", "downloadMergeTitle": "Downloading", "downloadTextText": "Downloading document...", "downloadTitleText": "Downloading Document", "loadFontsTextText": "Loading data...", "loadFontsTitleText": "Loading Data", "loadFontTextText": "Loading data...", "loadFontTitleText": "Loading Data", "loadImagesTextText": "Loading images...", "loadImagesTitleText": "Loading Images", "loadImageTextText": "Loading image...", "loadImageTitleText": "Loading Image", "loadingDocumentTextText": "Loading document...", "loadingDocumentTitleText": "Loading document", "mailMergeLoadFileText": "Loading Data Source...", "mailMergeLoadFileTitle": "Loading Data Source", "openTextText": "Opening document...", "openTitleText": "Opening Document", "printTextText": "Printing document...", "printTitleText": "Printing Document", "savePreparingText": "Preparing to save", "savePreparingTitle": "Preparing to save. Please wait...", "saveTextText": "Saving document...", "saveTitleText": "Saving Document", "sendMergeText": "Sending Merge...", "sendMergeTitle": "Sending Merge", "textContinue": "Continue", "textLoadingDocument": "Loading document", "textUndo": "Undo", "txtEditingMode": "Set editing mode...", "uploadImageTextText": "Uploading image...", "uploadImageTitleText": "Uploading Image", "waitText": "Please, wait..."}, "Toolbar": {"dlgLeaveMsgText": "You have unsaved changes. Click 'Stay on this Page' to wait for autosave. Click 'Leave this Page' to discard all the unsaved changes.", "dlgLeaveTitleText": "You leave the application", "leaveButtonText": "Leave this Page", "stayButtonText": "Stay on this page", "textOk": "OK", "textSwitchedMobileView": "Switched to Mobile view", "textSwitchedStandardView": "Switched to Standard view"}}